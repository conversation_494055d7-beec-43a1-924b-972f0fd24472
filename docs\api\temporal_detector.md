# Configuração do TemporalPatternDetector

O `TemporalPatternDetector` analisa séries temporais para extrair padrões que
orientam a metacognição do QUALIA. Os parâmetros são controlados pela classe
[`TemporalPatternConfig`](temporal_pattern_config.md).

```python
from src.qualia.config.temporal_detector_config import TemporalPatternConfig

config = TemporalPatternConfig()
```

Valores padrão são carregados das variáveis de ambiente quando definidas. Um
arquivo YAML pode ser usado como referência:

```yaml
wavelet_depth: 3
use_quantum_transform: false
min_confidence: 0.65
quantum_wavelet_shots: 2048
qft_shots: 4096
```

Salve-o como `config/temporal_detector.yaml` para documentar as opções de
configuração ou como base para ajustes personalizados.

## Métricas StatsD

Quando inicializado com `statsd_client`, o detector envia duas métricas (com a tag opcional `trace_id`):

- `temporal_detector.patterns` – quantidade de padrões identificados a cada análise.
- `temporal_detector.execution_ms` – tempo gasto para processar o histórico, em milissegundos.

Essas métricas auxiliam no monitoramento de desempenho e complexidade dos sinais. Exemplo de envio com tag:

```
statsd.gauge("temporal_detector.patterns", 3, tags=["trace_id:abc123"])
```

Também é possível utilizar JSON com o mesmo conteúdo:

```json
{
  "wavelet_depth": 3,
  "use_quantum_transform": false,
  "min_confidence": 0.65,
  "quantum_wavelet_shots": 2048,
  "qft_shots": 4096
}
```

Nesse caso, armazene o arquivo como `config/temporal_detector.json`.

## Uso do QuantumTemporalPatternDetector

Além do detector clássico, o módulo `src.qualia.temporal.pattern_detector`
fornece o `QuantumTemporalPatternDetector`, capaz de executar análises
harmônicas e fractais avançadas. A classe pode ser instanciada diretamente:

```python
import numpy as np
from src.qualia.temporal.pattern_detector import QuantumTemporalPatternDetector

detector = QuantumTemporalPatternDetector(wavelet_depth=3, quantum_shots=512)

series = np.random.rand(128)
results = detector.detect_quantum_patterns(series)
```

O alias ``TemporalPatternDetector`` permanece disponível para compatibilidade.

## Qual detector utilizar?

* `MarketTemporalPatternDetector` – disponível em
  `src.qualia.market.temporal_pattern_detector`. É utilizado pelos subsistemas
  de mercado para analisar o histórico de decisões e publicar eventos.
* `QuantumTemporalPatternDetector` – disponível em
  `src.qualia.temporal.pattern_detector`. Fornece análises harmônicas e
  fractais avançadas para estudos de sinais quânticos.

Para evitar ambiguidades, prefira esses nomes explícitos nos imports. O alias
``TemporalPatternDetector`` deve ser mantido apenas em código legado.

## Persistência de memória

O ``QuantumTemporalPatternDetector`` pode exportar o conteúdo de
``pattern_cache`` e ``harmonic_memory`` em um arquivo JSON por meio do método
``save_memory``. A leitura é realizada com ``load_memory``.

O arquivo segue o formato:

```json
{
  "pattern_cache": [
    {"timestamp": 1689272130.0, "patterns": {...}, "confidence": 0.8}
  ],
  "harmonic_memory": [
    {"timestamp": 1689272130.0, "resonance_strength": 0.7}
  ]
}
```

Exemplo de uso:

```python
detector.save_memory("memory.json")
new_detector = QuantumTemporalPatternDetector()
new_detector.load_memory("memory.json")
```

