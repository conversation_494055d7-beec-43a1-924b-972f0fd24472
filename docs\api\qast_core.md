# QASTCore e Extração de Características

A classe `QASTCore` coordena cada ciclo do motor QAST, avaliando o estado quântico e
ajustando parâmetros de evolução. A partir da versão atual, o cálculo de entropia
simbólica depende das características extraídas pelo método
`extract_signal_features`.

## `extract_signal_features`

Esta função aplica a Transformada Cosseno Discreta (DCT) à parte real do vetor de
estado e retorna os primeiros coeficientes normalizados. Essas características
servem de base para gerar os polinômios analisados por `SymbolicEntropy`,
permitindo medir padrões simbólicos do mercado mesmo com sinais ruidosos.

Ao invocar `QASTCore.run_cycle`, o vetor de estado atualizado passa primeiro por
`extract_signal_features`. Em seguida os coeficientes resultantes são passados a
`SymbolicEntropy.compute`, que produz o valor de entropia simbólica utilizado nos
mecanismos de controle do QAST.

## Controle Adaptativo e Massa Informacional

O `QASTCore` carrega os parâmetros do controlador PID do arquivo
`pid_coeffs.json` (configurável via `QUALIA_PID_COEFFS_FILE`), quando presente,
permitindo que ajustes prévios sejam reaproveitados. Após cada cálculo de
entropia simbólica, a classe
`MassManager` atualiza a massa informacional com base no valor de entropia e na
coerência quântica do universo.

O erro entre `H_target` e a entropia observada alimenta o `PIDController`, que
gera um sinal de controle. Esse sinal modifica `scr_depth` e `temperature` do
universo, afinando a exploração quântica de forma gradual. O ciclo é repetido
até que `should_terminate` indique convergência, seja por atingir `mass_min` ou
por estabilização da variação de entropia na janela recente definida em
`stop_window`.

## Métricas StatsD do PIDOptimizer

Quando `pid_metrics_enabled` está ativado (via `QUALIA_PID_METRICS_ENABLED`),
o `PIDOptimizer` envia métricas através do DogStatsd:

- `pid.record` – incrementado sempre que `record_pid_performance` é chamado.
- `pid.optimize_ms` – tempo de execução de `optimize_pid` em milissegundos.
- `pid.optimize.success` – contagem de otimizações concluídas sem erro.
- `pid.optimize.error` – contagem de falhas durante a otimização.

## TradingQASTCore

O `TradingQASTCore` estende o `QASTCore` para integrar exchanges e estratégias de
trading. A lista de pares analisados é definida na seção `trading` do arquivo
`config.yaml`. O campo `quote_currencies` determina quais moedas serão consultadas
e não possui valor padrão no código-fonte. Certifique-se de manter esse valor no
arquivo de configuração para que a coleta de mercado funcione corretamente.

