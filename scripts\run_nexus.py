from __future__ import annotations
"""Bootstrap script to run NEXUS (coerência + GA) end-to-end.

Usage:
    python scripts/run_nexus.py
Requires:
    • python-dotenv (pip install python-dotenv)
    • feedparser tweepy yt_dlp soundfile librosa dash plotly requests
Environment:
    .env file at project root containing at least TWITTER_BEARER_TOKEN.
Dashboards:
    http://localhost:8050 – Coerência / Threshold
    http://localhost:8060 – Evolução GA
REST (Flask app already running on port 5000 via ui/app.py).
"""

import asyncio
import threading
from pathlib import Path
import sys
# ensure project root on PATH
sys.path.insert(0, str(Path(__file__).resolve().parents[1]))

from dotenv import load_dotenv

# Load .env at repo root
load_dotenv(dotenv_path=Path(__file__).resolve().parents[1] / ".env")

from qualia.memory.event_bus import SimpleEventBus
from qualia.analysis.coherence_monitor import CoherenceMonitor
from qualia.analysis.threshold_calibrator import AdaptiveThresholdCalibrator
from qualia.analysis.ga_threshold_evolver import GAThresholdEvolver
from qualia.persistence.coherence_store import CoherenceStore
from qualia.sources.rss_ingestor import RSSIngestor
from qualia.sources.twitter_ingestor import TwitterIngestor
from qualia.sources.youtube_audio_ingestor import YouTubeAudioIngestor
from qualia.ui.dashboards.nexus import init_app as dash_coh
from qualia.ui.dashboards.nexus_ga import init_app as dash_ga
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


def main() -> None:
    # Create dedicated asyncio loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    bus = SimpleEventBus()

    # Core components
    CoherenceMonitor(bus)
    AdaptiveThresholdCalibrator(bus, config_path="config/nexus_threshold.yaml")
    GAThresholdEvolver(bus)
    CoherenceStore(bus)

    # Data sources ----------------------------------------------------
    rss_ing = RSSIngestor([
        "https://feeds.bbci.co.uk/news/world/rss.xml",
        "https://www.reuters.com/rssFeed/topNews",
    ], bus, poll_seconds=90)
    loop.create_task(rss_ing._loop())

    try:
        tw_ing = TwitterIngestor(["pandemic", "inflation"], bus)
        tw_ing.start()
    except Exception as exc:
        logger.warning("TwitterIngestor disabled: %s", exc)

    try:
        yt_ing = YouTubeAudioIngestor(["dQw4w9WgXcQ"], bus, seconds=15)
        loop.create_task(yt_ing.ingest_all())
    except Exception as exc:
        logger.warning("YouTubeAudioIngestor disabled: %s", exc)

    # Dash dashboards -------------------------------------------------
    app_coh = dash_coh(bus)
    app_ga = dash_ga(bus)

    threading.Thread(target=lambda: app_coh.run_server(port=8050), daemon=True).start()
    threading.Thread(target=lambda: app_ga.run_server(port=8060), daemon=True).start()

    logger.info("NEXUS running – dashboards on 8050 (coh) and 8060 (GA)")
    loop.run_forever()


if __name__ == "__main__":
    main() 