"""Enhanced retry mechanisms with exponential backoff and jitter.

This module provides sophisticated retry strategies that help prevent
thundering herd problems and optimize recovery patterns for network operations.

Features:
- Exponential backoff with configurable base and multiplier
- Jitter strategies (full, equal, decorrelated)
- Per-operation retry policies
- Circuit breaker integration
- Adaptive retry based on error types
- Comprehensive retry statistics and monitoring
"""

from __future__ import annotations

import asyncio
import random
import time
import math
from typing import Any, Awaitable, Callable, Dict, List, Optional, Type, Union
from dataclasses import dataclass
from enum import Enum
from functools import wraps

from qualia.utils.logger import get_logger
from qualia.utils.network_resilience import EnhancedCircuitBreaker

logger = get_logger(__name__)


class JitterStrategy(Enum):
    """Jitter strategies for backoff calculation."""
    NONE = "none"
    FULL = "full"
    EQUAL = "equal"
    DECORRELATED = "decorrelated"


class RetryDecision(Enum):
    """Retry decision outcomes."""
    RETRY = "retry"
    STOP = "stop"
    CIRCUIT_OPEN = "circuit_open"


@dataclass
class RetryConfig:
    """Configuration for retry behavior."""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    multiplier: float = 2.0
    jitter_strategy: JitterStrategy = JitterStrategy.FULL
    exponential_base: float = 2.0
    timeout_multiplier: float = 1.0
    
    # Error-specific configurations
    retryable_exceptions: tuple = (asyncio.TimeoutError, ConnectionError, OSError)
    non_retryable_exceptions: tuple = (ValueError, TypeError, KeyError)
    
    # Circuit breaker integration
    use_circuit_breaker: bool = True
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: float = 60.0


@dataclass
class RetryAttempt:
    """Information about a retry attempt."""
    attempt_number: int
    delay: float
    exception: Optional[Exception]
    timestamp: float
    operation: str
    context: Dict[str, Any]


class EnhancedRetryManager:
    """Enhanced retry manager with sophisticated backoff and jitter strategies.
    
    This manager provides:
    - Multiple jitter strategies to prevent thundering herd
    - Adaptive retry policies based on error patterns
    - Circuit breaker integration for fail-fast behavior
    - Comprehensive retry statistics and monitoring
    - Per-operation retry configuration
    """
    
    def __init__(
        self,
        default_config: Optional[RetryConfig] = None,
        enable_metrics: bool = True,
        enable_circuit_breaker: bool = True
    ):
        self.default_config = default_config or RetryConfig()
        self.enable_metrics = enable_metrics
        self.enable_circuit_breaker = enable_circuit_breaker
        
        # Per-operation configurations
        self.operation_configs: Dict[str, RetryConfig] = {}
        
        # Circuit breakers per operation
        self.circuit_breakers: Dict[str, EnhancedCircuitBreaker] = {}
        
        # Retry statistics
        self.stats = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'total_retries': 0,
            'circuit_breaker_trips': 0,
            'jitter_calculations': 0
        }
        
        # Retry history for analysis
        self.retry_history: Dict[str, List[RetryAttempt]] = {}
        
        # Decorrelated jitter state
        self._decorrelated_state: Dict[str, float] = {}

    def configure_operation(self, operation: str, config: RetryConfig) -> None:
        """Configure retry behavior for a specific operation."""
        self.operation_configs[operation] = config
        
        if config.use_circuit_breaker and self.enable_circuit_breaker:
            self.circuit_breakers[operation] = EnhancedCircuitBreaker(
                fail_threshold=config.circuit_breaker_threshold,
                recovery_timeout=config.circuit_breaker_timeout,
                name=f"retry_cb_{operation}",
                adaptive_recovery=True,
                symbol_isolation=True
            )
        
        logger.debug(f"Configured retry policy for operation: {operation}")

    async def execute_with_retry(
        self,
        operation: Callable[..., Awaitable[Any]],
        operation_name: str,
        *args,
        context: Optional[Dict[str, Any]] = None,
        config_override: Optional[RetryConfig] = None,
        **kwargs
    ) -> Any:
        """Execute an operation with retry logic.
        
        Parameters
        ----------
        operation : Callable
            Async function to execute
        operation_name : str
            Name of the operation for tracking
        *args, **kwargs
            Arguments to pass to the operation
        context : Dict, optional
            Additional context for retry decisions
        config_override : RetryConfig, optional
            Override configuration for this execution
            
        Returns
        -------
        Any
            Result of the successful operation
            
        Raises
        ------
        Exception
            Last exception if all retries failed
        """
        config = config_override or self.operation_configs.get(operation_name, self.default_config)
        context = context or {}
        
        self.stats['total_operations'] += 1
        
        # Check circuit breaker
        circuit_breaker = self.circuit_breakers.get(operation_name)
        if circuit_breaker and not circuit_breaker.allow_request():
            self.stats['circuit_breaker_trips'] += 1
            raise RuntimeError(f"Circuit breaker open for operation: {operation_name}")
        
        last_exception = None
        retry_attempts = []
        
        for attempt in range(config.max_attempts):
            try:
                # Execute the operation
                start_time = time.time()
                result = await operation(*args, **kwargs)
                
                # Record success
                if circuit_breaker:
                    circuit_breaker.record_success()
                
                self.stats['successful_operations'] += 1
                
                # Log successful retry if this wasn't the first attempt
                if attempt > 0:
                    logger.info(
                        f"Operation {operation_name} succeeded on attempt {attempt + 1}/{config.max_attempts}"
                    )
                
                return result
                
            except Exception as exc:
                last_exception = exc
                
                # Record failure in circuit breaker
                if circuit_breaker:
                    circuit_breaker.record_failure()
                
                # Check if this exception is retryable
                retry_decision = self._should_retry(exc, attempt, config, context)
                
                if retry_decision != RetryDecision.RETRY:
                    if retry_decision == RetryDecision.CIRCUIT_OPEN:
                        self.stats['circuit_breaker_trips'] += 1
                    break
                
                # Calculate delay for next attempt
                if attempt < config.max_attempts - 1:  # Don't delay after last attempt
                    delay = self._calculate_delay(attempt, config, operation_name, context)
                    
                    # Record retry attempt
                    retry_attempt = RetryAttempt(
                        attempt_number=attempt + 1,
                        delay=delay,
                        exception=exc,
                        timestamp=time.time(),
                        operation=operation_name,
                        context=context.copy()
                    )
                    retry_attempts.append(retry_attempt)
                    
                    self.stats['total_retries'] += 1
                    
                    logger.warning(
                        f"Operation {operation_name} failed on attempt {attempt + 1}/{config.max_attempts}, "
                        f"retrying in {delay:.2f}s: {exc.__class__.__name__}: {exc}"
                    )
                    
                    await asyncio.sleep(delay)
        
        # All retries failed
        self.stats['failed_operations'] += 1
        
        # Store retry history
        if operation_name not in self.retry_history:
            self.retry_history[operation_name] = []
        self.retry_history[operation_name].extend(retry_attempts)
        
        # Keep only recent history (last 100 attempts per operation)
        self.retry_history[operation_name] = self.retry_history[operation_name][-100:]
        
        logger.error(
            f"Operation {operation_name} failed after {config.max_attempts} attempts. "
            f"Last error: {last_exception.__class__.__name__}: {last_exception}"
        )
        
        raise last_exception

    def _should_retry(
        self, 
        exception: Exception, 
        attempt: int, 
        config: RetryConfig,
        context: Dict[str, Any]
    ) -> RetryDecision:
        """Determine if an operation should be retried based on the exception and context."""
        
        # Check if we've reached max attempts
        if attempt >= config.max_attempts - 1:
            return RetryDecision.STOP
        
        # Check non-retryable exceptions first
        if isinstance(exception, config.non_retryable_exceptions):
            return RetryDecision.STOP
        
        # Check retryable exceptions
        if isinstance(exception, config.retryable_exceptions):
            return RetryDecision.RETRY
        
        # Default behavior for unknown exceptions
        # Retry for common network-related exceptions
        if isinstance(exception, (
            asyncio.TimeoutError,
            ConnectionError,
            OSError,
            aiohttp.ClientError if 'aiohttp' in str(type(exception)) else type(None)
        )):
            return RetryDecision.RETRY
        
        # Don't retry for other exceptions
        return RetryDecision.STOP

    def _calculate_delay(
        self, 
        attempt: int, 
        config: RetryConfig, 
        operation_name: str,
        context: Dict[str, Any]
    ) -> float:
        """Calculate delay for next retry attempt with jitter."""
        
        # Base exponential backoff
        exponential_delay = config.base_delay * (config.multiplier ** attempt)
        
        # Apply jitter based on strategy
        if config.jitter_strategy == JitterStrategy.NONE:
            delay = exponential_delay
        elif config.jitter_strategy == JitterStrategy.FULL:
            delay = self._full_jitter(exponential_delay)
        elif config.jitter_strategy == JitterStrategy.EQUAL:
            delay = self._equal_jitter(exponential_delay)
        elif config.jitter_strategy == JitterStrategy.DECORRELATED:
            delay = self._decorrelated_jitter(exponential_delay, operation_name)
        else:
            delay = exponential_delay
        
        # Apply maximum delay limit
        delay = min(delay, config.max_delay)
        
        # Apply context-based adjustments
        if 'load_factor' in context:
            delay *= context['load_factor']
        
        if 'priority' in context and context['priority'] == 'high':
            delay *= 0.5  # Reduce delay for high priority operations
        
        self.stats['jitter_calculations'] += 1
        
        return max(0.1, delay)  # Minimum delay of 100ms

    def _full_jitter(self, base_delay: float) -> float:
        """Apply full jitter: random value between 0 and base_delay."""
        return random.uniform(0, base_delay)

    def _equal_jitter(self, base_delay: float) -> float:
        """Apply equal jitter: base_delay/2 + random(0, base_delay/2)."""
        half_delay = base_delay / 2
        return half_delay + random.uniform(0, half_delay)

    def _decorrelated_jitter(self, base_delay: float, operation_name: str) -> float:
        """Apply decorrelated jitter to prevent synchronized retries."""
        if operation_name not in self._decorrelated_state:
            self._decorrelated_state[operation_name] = base_delay
        
        previous_delay = self._decorrelated_state[operation_name]
        
        # Decorrelated jitter formula: random(base_delay, previous_delay * 3)
        min_delay = base_delay
        max_delay = previous_delay * 3
        
        if max_delay <= min_delay:
            delay = min_delay
        else:
            delay = random.uniform(min_delay, max_delay)
        
        self._decorrelated_state[operation_name] = delay
        return delay

    def get_operation_stats(self, operation_name: str) -> Dict[str, Any]:
        """Get statistics for a specific operation."""
        circuit_breaker = self.circuit_breakers.get(operation_name)
        retry_history = self.retry_history.get(operation_name, [])
        
        stats = {
            'operation_name': operation_name,
            'total_retry_attempts': len(retry_history),
            'circuit_breaker_stats': circuit_breaker.get_stats() if circuit_breaker else None,
            'recent_failures': len([a for a in retry_history[-20:] if a.exception]),
            'average_delay': sum(a.delay for a in retry_history) / max(1, len(retry_history)),
            'most_common_exceptions': self._get_common_exceptions(retry_history)
        }
        
        return stats

    def _get_common_exceptions(self, retry_history: List[RetryAttempt]) -> Dict[str, int]:
        """Get most common exception types from retry history."""
        exception_counts = {}
        for attempt in retry_history:
            if attempt.exception:
                exc_type = attempt.exception.__class__.__name__
                exception_counts[exc_type] = exception_counts.get(exc_type, 0) + 1
        
        # Return top 5 most common exceptions
        return dict(sorted(exception_counts.items(), key=lambda x: x[1], reverse=True)[:5])

    def get_global_stats(self) -> Dict[str, Any]:
        """Get global retry manager statistics."""
        return {
            **self.stats,
            'success_rate': self.stats['successful_operations'] / max(1, self.stats['total_operations']),
            'average_retries_per_operation': self.stats['total_retries'] / max(1, self.stats['total_operations']),
            'configured_operations': list(self.operation_configs.keys()),
            'active_circuit_breakers': len(self.circuit_breakers),
            'operations_with_history': len(self.retry_history)
        }

    def reset_stats(self) -> None:
        """Reset all statistics and history."""
        self.stats = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'total_retries': 0,
            'circuit_breaker_trips': 0,
            'jitter_calculations': 0
        }
        self.retry_history.clear()
        self._decorrelated_state.clear()
        
        # Reset circuit breakers
        for cb in self.circuit_breakers.values():
            cb.reset()
        
        logger.info("Reset retry manager statistics and history")


def retry_with_backoff(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    multiplier: float = 2.0,
    jitter_strategy: JitterStrategy = JitterStrategy.FULL,
    retryable_exceptions: tuple = (asyncio.TimeoutError, ConnectionError, OSError)
):
    """Decorator for adding retry logic to async functions.
    
    Parameters
    ----------
    max_attempts : int
        Maximum number of retry attempts
    base_delay : float
        Base delay between retries in seconds
    max_delay : float
        Maximum delay between retries in seconds
    multiplier : float
        Exponential backoff multiplier
    jitter_strategy : JitterStrategy
        Jitter strategy to use
    retryable_exceptions : tuple
        Tuple of exception types that should trigger retries
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            config = RetryConfig(
                max_attempts=max_attempts,
                base_delay=base_delay,
                max_delay=max_delay,
                multiplier=multiplier,
                jitter_strategy=jitter_strategy,
                retryable_exceptions=retryable_exceptions
            )
            
            retry_manager = EnhancedRetryManager()
            operation_name = f"{func.__module__}.{func.__name__}"
            
            return await retry_manager.execute_with_retry(
                func, operation_name, *args, config_override=config, **kwargs
            )
        
        return wrapper
    return decorator
