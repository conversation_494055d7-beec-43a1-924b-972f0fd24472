#!/usr/bin/env python3
"""
QUALIA D-07.4 Test Configuration Manager - Teste Específico

Teste detalhado do gerenciador de configurações de teste.
"""

import sys
import asyncio
from pathlib import Path
from datetime import datetime
import tempfile
import os

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.ab_testing.test_config_manager import (
    TestConfigManager,
    TestConfiguration,
    StrategyConfig,
    RiskManagementConfig,
    ExecutionConfig
)

async def test_config_creation():
    """Testa criação de configurações de teste."""
    print("🧪 Testando criação de configurações...")
    
    manager = TestConfigManager()
    
    # Criar configuração básica
    config = await manager.create_test_configuration(
        symbols=["BTC/USDT", "ETH/USDT"],
        strategy_config={
            "name": "Test Strategy",
            "type": "momentum",
            "parameters": {"rsi_period": 14}
        },
        risk_params={
            "initial_capital": 10000.0,
            "max_capital_risk_pct": 2.0
        },
        config_name="Basic Test Config"
    )
    
    # Verificações
    assert config is not None
    assert isinstance(config, TestConfiguration)
    assert config.config_name == "Basic Test Config"
    assert config.symbols == ["BTC/USDT", "ETH/USDT"]
    assert config.strategy_config.name == "Test Strategy"
    assert config.strategy_config.type == "momentum"
    assert config.risk_config.initial_capital == 10000.0
    assert config.risk_config.max_capital_risk_pct == 2.0
    
    print(f"✅ Config Creation:")
    print(f"   Config ID: {config.config_id}")
    print(f"   Name: {config.config_name}")
    print(f"   Symbols: {config.symbols}")
    print(f"   Strategy: {config.strategy_config.name} ({config.strategy_config.type})")
    print(f"   Capital: ${config.risk_config.initial_capital:,.2f}")
    
    return True

async def test_strategy_templates():
    """Testa templates de estratégias."""
    print("🧪 Testando templates de estratégias...")
    
    manager = TestConfigManager()
    
    # Testar template momentum_rsi
    config_momentum = await manager.create_test_configuration(
        symbols=["BTC/USDT"],
        strategy_config={"template": "momentum_rsi"},
        risk_params={"initial_capital": 5000.0},
        config_name="Momentum RSI Test"
    )
    
    assert config_momentum.strategy_config.type == "momentum"
    assert "rsi_period" in config_momentum.strategy_config.parameters
    assert config_momentum.strategy_config.parameters["rsi_period"] == 14
    assert "rsi" in config_momentum.strategy_config.indicators
    
    # Testar template mean_reversion
    config_mean_rev = await manager.create_test_configuration(
        symbols=["ETH/USDT"],
        strategy_config={"template": "mean_reversion"},
        risk_params={"initial_capital": 5000.0},
        config_name="Mean Reversion Test"
    )
    
    assert config_mean_rev.strategy_config.type == "mean_reversion"
    assert "bollinger_period" in config_mean_rev.strategy_config.parameters
    assert "bollinger" in config_mean_rev.strategy_config.indicators
    
    print(f"✅ Strategy Templates:")
    print(f"   Momentum RSI: {config_momentum.strategy_config.parameters}")
    print(f"   Mean Reversion: {config_mean_rev.strategy_config.parameters}")
    
    return True

async def test_config_persistence():
    """Testa persistência de configurações."""
    print("🧪 Testando persistência de configurações...")
    
    # Usar diretório temporário
    with tempfile.TemporaryDirectory() as temp_dir:
        config_dir = Path(temp_dir) / "test_configs"
        manager = TestConfigManager(config_dir=config_dir)
        
        # Criar configuração
        original_config = await manager.create_test_configuration(
            symbols=["BTC/USDT"],
            strategy_config={
                "name": "Persistence Test",
                "type": "momentum",
                "parameters": {"test_param": 42}
            },
            risk_params={"initial_capital": 15000.0},
            config_name="Persistence Test Config"
        )
        
        # Salvar configuração
        saved_path = await manager.save_configuration(original_config)
        assert saved_path.exists()
        
        # Carregar configuração
        loaded_config = await manager.load_configuration(saved_path)
        
        # Verificar se são idênticas
        assert loaded_config.config_id == original_config.config_id
        assert loaded_config.config_name == original_config.config_name
        assert loaded_config.symbols == original_config.symbols
        assert loaded_config.strategy_config.name == original_config.strategy_config.name
        assert loaded_config.strategy_config.parameters == original_config.strategy_config.parameters
        assert loaded_config.risk_config.initial_capital == original_config.risk_config.initial_capital
        
        print(f"✅ Config Persistence:")
        print(f"   Saved to: {saved_path}")
        print(f"   Config ID match: {loaded_config.config_id == original_config.config_id}")
        print(f"   Parameters match: {loaded_config.strategy_config.parameters == original_config.strategy_config.parameters}")
    
    return True

async def test_config_validation():
    """Testa validação de configurações."""
    print("🧪 Testando validação de configurações...")
    
    manager = TestConfigManager()
    
    # Criar configuração válida
    valid_config = await manager.create_test_configuration(
        symbols=["BTC/USDT"],
        strategy_config={"name": "Valid", "type": "momentum"},
        risk_params={"initial_capital": 10000.0},
        config_name="Valid Config"
    )
    
    # Validar configuração válida
    is_valid, issues = await manager.validate_configuration(valid_config)
    assert is_valid == True
    assert len(issues) == 0
    
    # Criar configuração inválida (capital muito baixo)
    invalid_config = await manager.create_test_configuration(
        symbols=["BTC/USDT"],
        strategy_config={"name": "Invalid", "type": "momentum"},
        risk_params={"initial_capital": 10.0},  # Muito baixo
        config_name="Invalid Config"
    )
    
    # Validar configuração inválida
    is_invalid, issues = await manager.validate_configuration(invalid_config)
    assert is_invalid == False
    assert len(issues) > 0
    
    print(f"✅ Config Validation:")
    print(f"   Valid config: {is_valid} (issues: {len(issues)})")
    print(f"   Invalid config: {is_invalid} (issues: {len(issues)})")
    if issues:
        for issue in issues:
            print(f"     - {issue}")
    
    return True

async def test_multi_symbol_support():
    """Testa suporte a múltiplos símbolos."""
    print("🧪 Testando suporte a múltiplos símbolos...")
    
    manager = TestConfigManager()
    
    # Criar configuração com múltiplos símbolos
    multi_config = await manager.create_test_configuration(
        symbols=["BTC/USDT", "ETH/USDT", "ADA/USDT", "DOT/USDT"],
        strategy_config={
            "name": "Multi Symbol Strategy",
            "type": "momentum",
            "parameters": {"rsi_period": 14}
        },
        risk_params={
            "initial_capital": 20000.0,
            "max_capital_risk_pct": 1.5,  # Mais conservador para múltiplos símbolos
            "position_sizing_method": "volatility_adjusted"
        },
        config_name="Multi Symbol Test"
    )
    
    # Verificações
    assert len(multi_config.symbols) == 4
    assert "BTC/USDT" in multi_config.symbols
    assert "ETH/USDT" in multi_config.symbols
    assert "ADA/USDT" in multi_config.symbols
    assert "DOT/USDT" in multi_config.symbols
    assert multi_config.risk_config.position_sizing_method == "volatility_adjusted"
    
    # Validar configuração
    is_valid, issues = await manager.validate_configuration(multi_config)
    assert is_valid == True
    
    print(f"✅ Multi Symbol Support:")
    print(f"   Symbols: {multi_config.symbols}")
    print(f"   Position sizing: {multi_config.risk_config.position_sizing_method}")
    print(f"   Risk per trade: {multi_config.risk_config.max_capital_risk_pct}%")
    print(f"   Valid: {is_valid}")
    
    return True

async def test_execution_settings():
    """Testa configurações de execução."""
    print("🧪 Testando configurações de execução...")
    
    manager = TestConfigManager()
    
    # Criar configuração com settings específicos de execução
    config = await manager.create_test_configuration(
        symbols=["BTC/USDT"],
        strategy_config={"name": "Execution Test", "type": "momentum"},
        risk_params={"initial_capital": 10000.0},
        execution_settings={
            "paper_trading": True,
            "max_slippage_pct": 0.1,
            "order_timeout_seconds": 30,
            "max_orders_per_minute": 5
        },
        config_name="Execution Settings Test"
    )
    
    # Verificações
    assert config.execution_config.paper_trading == True
    assert config.execution_config.max_slippage_pct == 0.1
    assert config.execution_config.order_timeout_seconds == 30
    assert config.execution_config.max_orders_per_minute == 5
    
    print(f"✅ Execution Settings:")
    print(f"   Paper trading: {config.execution_config.paper_trading}")
    print(f"   Max slippage: {config.execution_config.max_slippage_pct}%")
    print(f"   Order timeout: {config.execution_config.order_timeout_seconds}s")
    print(f"   Max orders/min: {config.execution_config.max_orders_per_minute}")
    
    return True

async def main():
    """Função principal de teste."""
    print("🚀 Testando D-07.4 Test Configuration Manager")
    
    tests = [
        ("Config Creation", test_config_creation),
        ("Strategy Templates", test_strategy_templates),
        ("Config Persistence", test_config_persistence),
        ("Config Validation", test_config_validation),
        ("Multi Symbol Support", test_multi_symbol_support),
        ("Execution Settings", test_execution_settings),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Executando: {test_name}")
            result = await test_func()
            results.append((test_name, result, None))
            print(f"✅ {test_name}: PASSOU")
        except Exception as e:
            results.append((test_name, False, str(e)))
            print(f"❌ {test_name}: FALHOU - {e}")
    
    # Resumo
    print("\n" + "="*60)
    print("📋 RESUMO DOS TESTES D-07.4")
    print("="*60)
    
    passed = sum(1 for _, result, _ in results if result)
    total = len(results)
    
    for test_name, result, error in results:
        status = "✅ PASSOU" if result else "❌ FALHOU"
        print(f"{status}: {test_name}")
        if error:
            print(f"    Erro: {error}")
    
    print(f"\nTaxa de Sucesso: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 D-07.4 Test Configuration Manager implementado com sucesso!")
        return True
    else:
        print(f"❌ {total-passed} testes falharam.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
