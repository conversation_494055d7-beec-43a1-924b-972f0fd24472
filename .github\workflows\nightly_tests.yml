name: Nightly Tests and Coverage

on:
  schedule:
    - cron: '0 2 * * *'  # Executa diariamente às 02:00 UTC
  workflow_dispatch: # Permite execução manual

jobs:
  test_and_coverage:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"] # Pode adicionar mais versões se necessário

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        ./scripts/install_test_deps.sh
        pip install -e .[dev]

    - name: Validate tests in isolation
      run: |
        for test in $(find tests -name 'test_*.py'); do
          echo "Running $test"
          pytest "$test"
        done

    - name: Run unit tests (excluding integration)
      run: |
        pytest -m "not integration" --cov=src --cov-report=xml --cov-append -o console_output_style=progress

    - name: Run integration tests
      run: |
        pytest -m "integration" tests/integration/ --cov=src --cov-report=xml --cov-append -o console_output_style=progress
        # Assumindo que testes de integração estão em tests/integration/
        # Se não houver testes de integração ainda, esta etapa pode falhar ou não fazer nada.
        # Considere adicionar ` || true` se quiser que o workflow continue mesmo se não houver testes de integração.

    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v3
      with:
        token: ${{ secrets.CODECOV_TOKEN }} # CODECOV_TOKEN é um secret do repositório
        files: ./coverage.xml # Arquivo de cobertura gerado
        fail_ci_if_error: true
        verbose: true

# Nota sobre Codecov: Se for usar, o ideal é fazer upload do coverage.xml final após todos os jobs de teste terem (potencialmente) contribuído para ele.
# Isso pode envolver download de artefatos de cobertura parciais, merge (pytest-cov pode ter utilitários para isso ou via config do coverage.py),
# e então um upload final em um job dedicado. 