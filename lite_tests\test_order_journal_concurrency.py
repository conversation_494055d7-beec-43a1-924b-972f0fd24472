import json
import threading
from qualia.persistence.order_journal import <PERSON><PERSON>our<PERSON>


def test_order_journal_concurrent_appends(tmp_path):
    journal_path = tmp_path / "journal.jsonl"
    journal = OrderJournal(str(journal_path))

    def writer(i: int) -> None:
        journal.append({"order_id": str(i)})

    threads = [threading.Thread(target=writer, args=(i,)) for i in range(50)]
    for t in threads:
        t.start()
    for t in threads:
        t.join()

    lines = journal_path.read_text(encoding="utf-8").splitlines()
    assert len(lines) == 50
    ids = {json.loads(line)["order_id"] for line in lines}
    assert ids == {str(i) for i in range(50)}
