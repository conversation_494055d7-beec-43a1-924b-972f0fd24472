"""
QUALIA Enhanced Data Collector - Versão avançada com encoders quânticos.

Este módulo implementa um coletor de dados que aproveita TODOS os recursos
disponíveis na codebase QUALIA:
- Encoders quânticos para RSI, Volume, MFCC, etc.
- Dados OHLCV completos com múltiplos timeframes
- Integração com QUALIARealTimeTrader
- Análise técnica avançada
"""

from __future__ import annotations

import asyncio
import time
import numpy as np
import pandas as pd
from typing import Any, Dict, List, Optional, TYPE_CHECKING
from datetime import datetime
from dataclasses import dataclass
import hashlib
import feedparser
import random
import inspect
from datadog import DogStatsd
import aiohttp

from .holographic_universe import HolographicEvent
from ..core.encoders import RSIPhaseEncoder, VolumeRatioAmplitudeEncoder
from ..core.rss_encoder import RSSTextSentimentEncoder
from .amplification_calibrator import AmplificationCalibrator
from ..market.base_integration import CryptoDataFetcher
from ..indicators import rsi
from ..utils.logging_config import get_qualia_logger
from ..utils.statsd_client import get_statsd_client
from ..utils.data_helpers import is_data_empty
from ..config.settings import get_env
from ..config.utils_defaults import load_utils_defaults
from ..event_bus import MarketDataUpdated, SimpleEventBus

try:
    from ..utils.cache import CacheManager, get_cache_manager
except Exception:  # pragma: no cover - optional during tests
    from typing import Any as CacheManager  # type: ignore

    def get_cache_manager(*_args: Any, **_kwargs: Any) -> Any:
        return None


from ..common.specs import MarketSpec

# YAA TASK 2: Usar logger QUALIA padronizado com formato UTC
logger = get_qualia_logger("consciousness.enhanced_data_collector")

_DEFAULTS = load_utils_defaults()
DEFAULT_GATHER_TIMEOUT: float = float(
    get_env(
        "ENHANCED_GATHER_TIMEOUT",
        str(
            _DEFAULTS.get("enhanced_collector_gather_timeout", 120.0)
        ),  # NETWORK FIX: Aumentado para 120s
        warn=False,
    )
)

if TYPE_CHECKING:  # pragma: no cover - hints only
    import aiohttp


@dataclass
class EnhancedMarketData:
    """Dados de mercado enriquecidos com indicadores técnicos."""

    symbol: str
    timeframe: str = "realtime"
    timestamp: float = 0.0

    # OHLCV completo
    open: float = 0.0
    high: float = 0.0
    low: float = 0.0
    close: float = 0.0
    volume: float = 0.0

    # Indicadores técnicos
    rsi: Optional[float] = None
    volume_ratio: Optional[float] = None
    price_change_pct: Optional[float] = None
    volatility: Optional[float] = None

    # Estados quânticos
    rsi_quantum_state: Optional[np.ndarray] = None
    volume_quantum_state: Optional[np.ndarray] = None

    source: str = "enhanced"


@dataclass
class EnhancedNewsEvent:
    """Evento de notícia com encoding quântico."""

    title: str
    content: str
    timestamp: float
    sentiment_score: float
    quantum_sentiment_state: Optional[np.ndarray] = None
    source: str = "rss"
    url: str = ""
    guid: str = ""


class EnhancedDataCollector:
    """
    Coletor de dados aprimorado que utiliza todos os recursos QUALIA.

    Características:
    - Dados OHLCV completos de múltiplas exchanges
    - Encoders quânticos para indicadores técnicos
    - Análise de sentiment com encoding quântico
    - Integração com sistema de trading existente
    - Calibração adaptativa de amplificação
    """

    def __init__(
        self,
        symbols: Optional[List[str]] = None,
        timeframes: Optional[List[str]] = None,
        exchange_config: Optional[Dict[str, Any]] = None,
        cache_ttl_minutes: int = 5,
        cache_manager: Optional[CacheManager] = None,
        exchange_client: Optional[CryptoDataFetcher] = None,
        gather_timeout: float = DEFAULT_GATHER_TIMEOUT,
        statsd_client: Optional[DogStatsd] = None,
        event_bus: Optional[SimpleEventBus] = None,
    ) -> None:
        """Inicializa o coletor de dados.

        Parameters
        ----------
        symbols
            Lista de símbolos para monitorar.
        timeframes
            Lista de timeframes para consulta.
        exchange_config
            Configuração da integração com a exchange.
        cache_ttl_minutes
            Tempo de vida do cache de OHLCV em minutos.
        exchange_client
            Instância de cliente de exchange já inicializada para reuso.
        gather_timeout
            Timeout para ``asyncio.gather`` em segundos. Valor padrão
            ``DEFAULT_GATHER_TIMEOUT`` ou lido de configuração.
        statsd_client
            Instância opcional de ``DogStatsd`` para emissão de métricas.
        event_bus
            Instância opcional do SimpleEventBus para publicação de eventos.
        """
        # YAA FIX-DATA-PIPELINE: Garantir compatibilidade de formato de símbolos
        if symbols:
            self.symbols = [s.replace("/", "") if "/" in s else s for s in symbols]
        else:
            self.symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "SOLUSDT"]

        logger.info(f"🔧 DATA-PIPELINE: Símbolos processados: {self.symbols}")
        self.timeframes = timeframes or ["5m", "15m", "1h"]
        self.gather_timeout = float(gather_timeout)

        # YAA-FIX: KuCoin pode levar >60s para retornar OHLCV de múltiplos timeframes.
        # Se o exchange_client for KuCoin e gather_timeout for menor que 240s,
        # ajustamos automaticamente para 240s para evitar cancelamentos prematuros.
        if (
            exchange_client is not None
            and getattr(exchange_client, "exchange_id", "").lower() == "kucoin"
            and self.gather_timeout < 240.0
        ):
            logger.debug(
                "🔧 Ajustando gather_timeout para KuCoin: %.1fs → 240.0s",
                self.gather_timeout,
            )
            self.gather_timeout = 240.0

        # Encoders quânticos
        self.rsi_encoder = RSIPhaseEncoder(
            name="market_rsi", data_keys=["rsi"], target_qubits=[0]
        )
        self.volume_encoder = VolumeRatioAmplitudeEncoder(
            name="market_volume", data_keys=["volume_ratio"], target_qubits=[1]
        )
        self.sentiment_encoder = RSSTextSentimentEncoder(name="news_sentiment")

        # Calibrador de amplificação adaptativo - usando hiperparâmetros consolidados
        self.amplification_calibrator = AmplificationCalibrator()

        self.statsd = statsd_client or get_statsd_client()

        # YAA-FIX: Injeção de dependência do EventBus
        self.event_bus = event_bus

        # YAA-FIX: Sistema de quarentena mais flexível para KuCoin
        self._symbol_quarantine: Dict[str, float] = (
            {}
        )  # symbol@timeframe -> timestamp_until
        self._quarantine_duration = 180.0  # YAA-FIX: Reduzir para 3 minutos
        self._max_consecutive_failures = 5  # YAA-FIX: Aumentar tolerância para 5 falhas

        # YAA TASK 1.1: Contador de falhas por símbolo
        self._symbol_failure_count: Dict[str, int] = {}
        # Contador de falhas consecutivas para alertas de conectividade
        self._consecutive_failures: Dict[str, int] = {}
        # Backoff dinâmico por símbolo/timeframe
        self._retry_backoff: Dict[str, float] = {}
        self._initial_backoff = 1.0
        self._max_backoff = 60.0

        # Data fetcher para OHLCV
        self.data_fetcher = exchange_client
        if self.data_fetcher is None and exchange_config:
            self.data_fetcher = CryptoDataFetcher(
                api_key=exchange_config.get("api_key", ""),
                api_secret=exchange_config.get("api_secret", ""),
                exchange_id=exchange_config.get("exchange_id", "binance"),
            )

        # Cache de dados históricos - permitir injeção de instância única
        self.ohlcv_cache = cache_manager or get_cache_manager(
            cache_type="memory", expiry_seconds=cache_ttl_minutes * 60
        )

        # URLs de feeds RSS para notícias de crypto
        self.news_feeds = [
            "https://cointelegraph.com/rss",
            "https://decrypt.co/feed",
            "https://cryptonews.com/news/feed/",
            "https://www.coindesk.com/arc/outboundfeeds/rss/",
        ]

        # Controle de avisos de feed RSS
        self.feed_warning_timestamps: Dict[str, float] = {}

        logger.info(
            f"EnhancedDataCollector inicializado: {len(self.symbols)} símbolos, "
            f"{len(self.timeframes)} timeframes, com calibração adaptativa"
        )

    async def __aenter__(self):
        """Context manager entry."""
        if self.data_fetcher:
            await self.data_fetcher.initialize_connection()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        if self.data_fetcher:
            await self.data_fetcher.close()

    async def collect_enhanced_market_data(self) -> None:
        """
        Coleta dados de mercado enriquecidos e publica eventos individuais
        para cada par símbolo/timeframe em tempo real.
        """
        if not self.event_bus:
            logger.error(
                "EventBus não configurado no EnhancedDataCollector. Eventos não serão publicados."
            )
            # Continua a coleta mesmo sem publicar eventos

        tasks_info = [
            (symbol, timeframe)
            for symbol in self.symbols
            for timeframe in self.timeframes
        ]

        # YAA: Aumentar batch para 8 requisições conforme especificado
        batch_size = 8

        # YAA: Métricas de performance da paralelização
        start_time = time.time()
        if self.statsd:
            self.statsd.increment("collector.parallel_collection.started")
            self.statsd.gauge("collector.parallel_collection.batch_size", batch_size)
            self.statsd.gauge(
                "collector.parallel_collection.total_tasks", len(tasks_info)
            )

        logger.info(
            f"🚀 Iniciando coleta paralela com {batch_size} requisições concorrentes (total: {len(tasks_info)} tarefas)"
        )

        # NETWORK FIX: Timeout mais tolerante para DNS issues
        timeout = aiohttp.ClientTimeout(total=180.0, connect=60.0, sock_read=120.0)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            # YAA: Semáforo para rate-limit baseado em KuCoin (90 req / 3s)
            semaphore = asyncio.Semaphore(batch_size)

            async def rate_limited_task(symbol: str, timeframe: str) -> Any:
                """Tarefa com rate-limit otimizado para KuCoin."""
                async with semaphore:
                    symbol_tf = f"{symbol}@{timeframe}"
                    backoff = self._retry_backoff.get(symbol_tf, 0.0)
                    if backoff > 0:
                        await asyncio.sleep(backoff)
                    try:
                        result = await self._collect_symbol_timeframe(
                            symbol, timeframe, session
                        )
                        # YAA: Reduzir pausa para 0s em ambientes de teste
                        await asyncio.sleep(0)
                        return result
                    except Exception as e:
                        # NETWORK FIX: Melhor logging para DNS timeouts
                        if "Timeout while contacting DNS servers" in str(e):
                            logger.error(
                                f"❌ DNS timeout coletando {symbol}@{timeframe}: {e}"
                            )
                        else:
                            logger.error(f"❌ Erro coletando {symbol}@{timeframe}: {e}")
                        return e

            # YAA: Usar asyncio.gather() diretamente (mais eficiente que asyncio.wait)
            gather_tasks = [
                asyncio.create_task(rate_limited_task(sym, tf))
                for sym, tf in tasks_info
            ]

            try:
                results_raw = await asyncio.wait_for(
                    asyncio.gather(*gather_tasks, return_exceptions=True),
                    timeout=self.gather_timeout,
                )
            except asyncio.TimeoutError:
                logger.error(f"⏰ Timeout na coleta paralela ({self.gather_timeout}s)")
                if self.statsd:
                    self.statsd.increment("collector.parallel_collection.timeout")
                for task in gather_tasks:
                    if not task.done():
                        task.cancel()
                results_raw = await asyncio.gather(
                    *gather_tasks,
                    return_exceptions=True,
                )

            results_raw = [
                (
                    r
                    if not isinstance(r, asyncio.CancelledError)
                    else asyncio.CancelledError()
                )
                for r in results_raw
            ]

            results: List[EnhancedMarketData] = []
            failed: List[str] = []
            for i, result in enumerate(results_raw):
                sym, tf = tasks_info[i]
                key = f"{sym}@{tf}"
                if isinstance(result, EnhancedMarketData):
                    results.append(result)
                    self._consecutive_failures[key] = 0
                    self._retry_backoff[key] = 0.0
                else:
                    if isinstance(result, Exception):
                        logger.error("❌ exceÃ§Ãµes em %s@%s: %s", sym, tf, result)
                    else:
                        logger.error("❌ Falha em %s@%s", sym, tf)
                    self._consecutive_failures[key] = (
                        self._consecutive_failures.get(key, 0) + 1
                    )
                    failed.append(key)

                    if self._consecutive_failures[key] > self._max_consecutive_failures:
                        new_backoff = min(
                            self._retry_backoff.get(key, self._initial_backoff) * 2,
                            self._max_backoff,
                        )
                        self._retry_backoff[key] = new_backoff
                        logger.warning(
                            "🔌 Alerta de conectividade em %s. Backoff aumentado para %.0fs",
                            key,
                            new_backoff,
                        )

            if failed:
                await self._update_symbol_quarantine(failed)

            # YAA: Métricas finais de performance
            collection_time = time.time() - start_time
            success_rate = len(results) / len(tasks_info) if tasks_info else 0.0

            if self.statsd:
                self.statsd.timing(
                    "collector.parallel_collection.duration_ms", collection_time * 1000
                )
                self.statsd.gauge(
                    "collector.parallel_collection.success_rate", success_rate
                )
                self.statsd.gauge(
                    "collector.parallel_collection.results_count", len(results)
                )

            logger.info(
                f"✅ Coleta paralela concluída: {len(results)}/{len(tasks_info)} sucessos "
                f"({success_rate:.1%}) em {collection_time:.2f}s"
            )

            # YAA: Processamento otimizado de eventos (batch em vez de individual)
            if results and self.event_bus:
                from ..consciousness.real_data_collectors import MarketDataPoint

                # Processa todos os resultados em batch
                market_data_points = []
                for result in results:
                    market_data_point = MarketDataPoint(
                        symbol=result.symbol,
                        price=result.close,
                        volume=result.volume,
                        timestamp=result.timestamp,
                        change_24h=result.price_change_pct or 0.0,
                        source="enhanced",
                    )
                    market_data_points.append(market_data_point)

                # YAA: Publica evento único com todos os dados (mais eficiente)
                if market_data_points:
                    batch_event = MarketDataUpdated(
                        market_data=market_data_points, news_events=[]
                    )
                    self.event_bus.publish(batch_event)
                    logger.debug(
                        f"📬 Evento batch publicado com {len(market_data_points)} pontos de dados"
                    )

            elif not results:
                logger.warning(
                    "⚠️ Nenhuma amostra coletada - verificar conectividade e quarentena"
                )

        return results

    async def _update_symbol_quarantine(self, failed_symbols: List[str]) -> None:
        """
        YAA TASK 1.3: Atualiza o sistema de quarentena para símbolos problemáticos.

        Args:
            failed_symbols: Lista de símbolos que falharam (formato: "SYMBOL@TIMEFRAME")
        """
        current_time = time.time()

        for symbol_tf in failed_symbols:
            # Incrementar contador de falhas
            self._symbol_failure_count[symbol_tf] = (
                self._symbol_failure_count.get(symbol_tf, 0) + 1
            )

            # Se excedeu o limite, colocar em quarentena
            if self._symbol_failure_count[symbol_tf] >= self._max_consecutive_failures:
                quarantine_until = current_time + self._quarantine_duration
                self._symbol_quarantine[symbol_tf] = quarantine_until

                backoff = min(
                    self._retry_backoff.get(symbol_tf, self._initial_backoff) * 2,
                    self._max_backoff,
                )
                self._retry_backoff[symbol_tf] = backoff

                logger.warning(
                    "🚫 QUARENTENA: %s colocado em quarentena por %.0f segundos após %d falhas consecutivas",
                    symbol_tf,
                    self._quarantine_duration,
                    self._symbol_failure_count[symbol_tf],
                )
                logger.warning(
                    "🔌 Conectividade instável em %s. Backoff atual %.0fs",
                    symbol_tf,
                    backoff,
                )

                # Reset contador após quarentena
                self._symbol_failure_count[symbol_tf] = 0

                # Emitir métrica se disponível
                if self.statsd:
                    self.statsd.increment(
                        "collector.symbol_quarantine",
                        tags=[f"symbol_timeframe:{symbol_tf}"],
                    )

    def _is_symbol_quarantined(self, symbol: str, timeframe: str) -> bool:
        """
        YAA TASK 1.3: Verifica se um símbolo está em quarentena.

        Args:
            symbol: Símbolo a verificar
            timeframe: Timeframe a verificar

        Returns:
            True se o símbolo estiver em quarentena
        """
        symbol_tf = f"{symbol}@{timeframe}"
        current_time = time.time()

        if symbol_tf in self._symbol_quarantine:
            if current_time < self._symbol_quarantine[symbol_tf]:
                return True
            else:
                # Quarentena expirou, remover
                del self._symbol_quarantine[symbol_tf]
                logger.info(
                    "✅ QUARENTENA EXPIRADA: %s liberado da quarentena",
                    symbol_tf,
                )
                self._consecutive_failures[symbol_tf] = 0
                self._retry_backoff[symbol_tf] = 0.0

        return False

    def _validate_data_completeness(
        self, df: pd.DataFrame, expected_count: int, symbol: str, timeframe: str
    ) -> bool:
        """
        YAA TASK 2: Valida se os dados retornados são suficientes para operação segura.

        Args:
            df: DataFrame com dados OHLCV
            expected_count: Número esperado de candles
            symbol: Símbolo dos dados
            timeframe: Timeframe dos dados

        Returns:
            True se os dados são válidos para uso
        """
        if is_data_empty(df):
            logger.warning(
                "❌ DADOS INVÁLIDOS: DataFrame vazio para %s@%s", symbol, timeframe
            )
            return False

        actual_count = len(df)
        if expected_count > 0:
            completeness_ratio = actual_count / expected_count

            # YAA-FIX: Política adaptativa - mais flexível para KuCoin em tempo real
            # Para KuCoin, aceitar até 30% dos dados (devido às limitações da exchange)
            # Para outras exchanges, manter 80%
            min_ratio = (
                0.3
                if (
                    hasattr(self, "data_fetcher")
                    and self.data_fetcher
                    and hasattr(self.data_fetcher, "exchange_id")
                    and self.data_fetcher.exchange_id.lower() == "kucoin"
                )
                else 0.8
            )

            if completeness_ratio < min_ratio:
                logger.error(
                    "❌ DADOS INSUFICIENTES: %s@%s retornou apenas %d/%d candles (%.1f%%). Rejeitando dados (mín: %.0f%%).",
                    symbol,
                    timeframe,
                    actual_count,
                    expected_count,
                    completeness_ratio * 100,
                    min_ratio * 100,
                )

                # Emitir métrica
                if self.statsd:
                    self.statsd.gauge(
                        "collector.data_completeness_ratio",
                        completeness_ratio,
                        tags=[f"symbol:{symbol}", f"timeframe:{timeframe}"],
                    )

                return False

        # Verificar se os dados não estão muito antigos
        if not is_data_empty(df):
            # Verificar se df é DataFrame e tem a coluna timestamp
            if hasattr(df, "iloc") and "timestamp" in df.columns:
                latest_timestamp = df["timestamp"].iloc[-1]
                current_time = time.time()
                # Converter timestamp para float se for pd.Timestamp
                if isinstance(latest_timestamp, pd.Timestamp):
                    latest_timestamp_float = latest_timestamp.timestamp()
                else:
                    latest_timestamp_float = float(latest_timestamp)
                age_seconds = current_time - latest_timestamp_float

                # YAA TASK 2: Rejeitar dados com mais de 1 hora de atraso
                max_age_seconds = 3600  # 1 hora
                if age_seconds > max_age_seconds:
                    logger.error(
                        "❌ DADOS DEFASADOS: %s@%s com dados de %.1f minutos atrás. Rejeitando.",
                        symbol,
                        timeframe,
                        age_seconds / 60,
                    )
                    return False

        return True

    async def _collect_symbol_timeframe(
        self,
        symbol: str,
        timeframe: str,
        session: "aiohttp.ClientSession",
    ) -> Optional[EnhancedMarketData]:
        """
        Coleta e processa dados para um par símbolo/timeframe.
        Retorna EnhancedMarketData em caso de sucesso ou None em caso de falha ou dados insuficientes.
        """
        # YAA TASK 1.3: Verificar quarentena antes de tentar coletar
        if self._is_symbol_quarantined(symbol, timeframe):
            logger.debug(
                "⏸️ QUARENTENA: Pulando %s@%s (em quarentena)", symbol, timeframe
            )
            return None

        if not hasattr(self, "_semaphore"):
            self._semaphore = asyncio.Semaphore(8)

        async with self._semaphore:
            try:
                ohlcv_df = await self._fetch_ohlcv_data(symbol, timeframe, session)

                # YAA TASK 2: Validação rigorosa de dados
                # YAA-FIX: Ajustar expectativa baseada no exchange
                if (
                    self.data_fetcher
                    and hasattr(self.data_fetcher, "exchange_id")
                    and self.data_fetcher.exchange_id.lower() == "kucoin"
                ):
                    expected_candles = 20  # KuCoin tem limite menor
                else:
                    expected_candles = 50  # Expectativa padrão para outros exchanges

                if not self._validate_data_completeness(
                    ohlcv_df, expected_candles, symbol, timeframe
                ):
                    symbol_tf = f"{symbol}@{timeframe}"
                    self._consecutive_failures[symbol_tf] = (
                        self._consecutive_failures.get(symbol_tf, 0) + 1
                    )
                    return None

                if ohlcv_df is None or len(ohlcv_df) < 20:
                    logger.debug(
                        "Dados insuficientes ou ausentes para %s@%s. Pulando.",
                        symbol,
                        timeframe,
                    )
                    symbol_tf = f"{symbol}@{timeframe}"
                    self._consecutive_failures[symbol_tf] = (
                        self._consecutive_failures.get(symbol_tf, 0) + 1
                    )
                    return None

                # YAA TASK 1.1: Reset contador de falhas em caso de sucesso
                symbol_tf = f"{symbol}@{timeframe}"
                if symbol_tf in self._symbol_failure_count:
                    self._symbol_failure_count[symbol_tf] = 0
                if symbol_tf in self._consecutive_failures:
                    self._consecutive_failures[symbol_tf] = 0
                if symbol_tf in self._retry_backoff:
                    self._retry_backoff[symbol_tf] = 0.0

            except Exception as exc:
                logger.warning(
                    "Erro ao coletar dados para %s@%s: %s",
                    symbol,
                    timeframe,
                    exc,
                    exc_info=False,
                )
                symbol_tf = f"{symbol}@{timeframe}"
                self._consecutive_failures[symbol_tf] = (
                    self._consecutive_failures.get(symbol_tf, 0) + 1
                )
                return None

            latest_data = self._calculate_technical_indicators(ohlcv_df)
            quantum_states = await self._encode_quantum_states(latest_data)

            return EnhancedMarketData(
                symbol=symbol,
                timeframe=timeframe,
                timestamp=time.time(),
                open=latest_data["open"],
                high=latest_data["high"],
                low=latest_data["low"],
                close=latest_data["close"],
                volume=latest_data["volume"],
                rsi=latest_data.get("rsi"),
                volume_ratio=latest_data.get("volume_ratio"),
                price_change_pct=latest_data.get("price_change_pct"),
                volatility=latest_data.get("volatility"),
                rsi_quantum_state=quantum_states.get("rsi"),
                volume_quantum_state=quantum_states.get("volume"),
            )

    async def _call_fetch_ohlcv(
        self,
        spec: MarketSpec,
        *,
        limit: int,
        ohlcv_timeout: float = 15.0,
    ) -> Any:
        """Invoca ``fetch_ohlcv`` apenas com argumentos suportados.

        Parameters
        ----------
        spec
            Especificação do mercado a ser consultado.
        limit
            Valor a ser usado quando ``fetch_ohlcv`` aceitar ``limit``.
        ohlcv_timeout
            Valor do argumento ``ohlcv_timeout`` quando disponível.
        """

        try:
            sig = inspect.signature(self.data_fetcher.fetch_ohlcv)
        except (TypeError, ValueError) as exc:
            logger.warning("Falha ao inspecionar assinatura fetch_ohlcv: %s", exc)
            if self.statsd:
                self.statsd.increment("collector.fetch_ohlcv.signature_error")
            return await self.data_fetcher.fetch_ohlcv(spec=spec)

        kwargs: Dict[str, Any] = {}
        if "limit" in sig.parameters:
            kwargs["limit"] = limit
        if "ohlcv_timeout" in sig.parameters:
            kwargs["ohlcv_timeout"] = ohlcv_timeout
        try:
            return await self.data_fetcher.fetch_ohlcv(spec=spec, **kwargs)
        except TypeError as exc:
            logger.error("Erro de assinatura em fetch_ohlcv: %s", exc)
            if self.statsd:
                self.statsd.increment("collector.fetch_ohlcv.signature_error")
            return await self.data_fetcher.fetch_ohlcv(spec=spec)

    async def _fetch_ohlcv_data(
        self,
        symbol: str,
        timeframe: str,
        session: "aiohttp.ClientSession",
        limit: int = 500,
    ) -> Optional[Any]:
        """Busca dados OHLCV utilizando cache e data fetcher.

        Parameters
        ----------
        symbol
            Par de negociação a ser consultado.
        timeframe
            Timeframe dos candles.
        session
            Sessão HTTP já aberta.
        limit
            Quantidade de candles a retornar.
        """

        cache_key = f"{symbol}_{timeframe}"
        cached = self.ohlcv_cache.get(cache_key) if self.ohlcv_cache else None
        if cached is not None:
            return cached

        if not self.data_fetcher:
            df = await self._fetch_simple_ohlcv(
                symbol,
                timeframe,
                session,
                limit=limit,
            )
            if df is not None and self.ohlcv_cache:
                self.ohlcv_cache.set(cache_key, df)
            return df

        try:
            # YAA: Normalizar símbolo para o formato esperado pela exchange
            # Converter BTCUSDT -> BTC/USDT para KuCoin
            normalized_symbol = symbol
            if "/" not in symbol and len(symbol) >= 6:
                # Assume formato BASEQUOTE e converte para BASE/QUOTE
                if symbol.endswith("USDT"):
                    base = symbol[:-4]  # Remove USDT
                    normalized_symbol = f"{base}/USDT"
                elif symbol.endswith("BTC"):
                    base = symbol[:-3]  # Remove BTC
                    normalized_symbol = f"{base}/BTC"
                elif symbol.endswith("ETH"):
                    base = symbol[:-3]  # Remove ETH
                    normalized_symbol = f"{base}/ETH"

            logger.debug(f"Normalizando símbolo: {symbol} -> {normalized_symbol}")

            # YAA: Busca dados com timeout agressivo para coleta em tempo real
            logger.debug(
                f"Buscando dados OHLCV para {normalized_symbol}@{timeframe}..."
            )

            # YAA-FIX: Ajustar limite para KuCoin - usar limite muito menor para tempo real
            # KuCoin está retornando apenas 5-6 candles de 50-100 solicitados
            if (
                self.data_fetcher
                and hasattr(self.data_fetcher, "exchange_id")
                and self.data_fetcher.exchange_id.lower() == "kucoin"
            ):
                real_time_limit = min(
                    limit, 20
                )  # Máximo 20 candles para KuCoin tempo real
                ohlcv_timeout = 30.0  # Timeout maior para KuCoin
            else:
                real_time_limit = min(
                    limit, 100
                )  # Máximo 100 candles para outras exchanges
                ohlcv_timeout = 15.0

            df = await self._call_fetch_ohlcv(
                MarketSpec(symbol=normalized_symbol, timeframe=timeframe),
                limit=real_time_limit,
                ohlcv_timeout=ohlcv_timeout,
            )

            # Se a chamada principal falhar ou retornar DataFrame vazio, tenta o fallback
            if is_data_empty(df):
                logger.warning(
                    "Dados insuficientes para %s@%s. Usando fallback Binance.",
                    normalized_symbol,
                    timeframe,
                )
                df = await self._fetch_simple_ohlcv(
                    symbol, timeframe, session, limit=limit
                )

            # Se ainda assim não houver dados, retorna None
            if is_data_empty(df):
                return None

            # Armazena no cache se disponível
            if self.ohlcv_cache:
                self.ohlcv_cache.set(cache_key, df)
            return df

        except Exception as e:
            logger.error(
                "Erro no data_fetcher para %s@%s: %s", normalized_symbol, timeframe, e
            )
            df = await self._fetch_simple_ohlcv(symbol, timeframe, session, limit=limit)
            if df is not None and self.ohlcv_cache:
                self.ohlcv_cache.set(cache_key, df)
            return df

    async def _fetch_simple_ohlcv(
        self,
        symbol: str,
        timeframe: str,
        session: "aiohttp.ClientSession",
        limit: int = 50,  # YAA-FIX: Reduzir limite padrão para fallback Binance
    ) -> Optional[pd.DataFrame]:
        """Fallback simples para OHLCV via API Binance.

        A função consulta diretamente a API pública da Binance quando o
        ``CryptoDataFetcher`` não está configurado ou falha na coleta.

        Parameters
        ----------
        symbol
            Par de negociação a ser consultado.
        timeframe
            Timeframe dos candles.
        session
            Sessão HTTP já aberta.
        limit
            Número de candles a retornar. O padrão é ``100`` devido às
            limitações da API pública.
        """

        try:
            # Mapeia timeframe
            tf_map = {"5m": "5m", "15m": "15m", "1h": "1h", "4h": "4h", "1d": "1d"}
            interval = tf_map.get(timeframe, "1h")

            url = "https://api.binance.com/api/v3/klines"
            params = {"symbol": symbol, "interval": interval, "limit": limit}

            # NETWORK FIX: Timeout específico para fallback Binance com DNS resilience
            timeout = aiohttp.ClientTimeout(total=120.0, connect=30.0, sock_read=60.0)
            async with session.get(url, params=params, timeout=timeout) as response:
                if response.status == 200:
                    data = await response.json()

                    # Converte para formato padrão
                    df = pd.DataFrame(
                        data,
                        columns=[
                            "timestamp",
                            "open",
                            "high",
                            "low",
                            "close",
                            "volume",
                            "close_time",
                            "quote_asset_volume",
                            "number_of_trades",
                            "taker_buy_base_asset_volume",
                            "taker_buy_quote_asset_volume",
                            "ignore",
                        ],
                    )

                    # Converte tipos
                    for col in ["open", "high", "low", "close", "volume"]:
                        df[col] = pd.to_numeric(df[col], errors="coerce")

                    df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")

                    return df[["timestamp", "open", "high", "low", "close", "volume"]]
                logger.error(
                    "Falha no fallback OHLCV para %s@%s - HTTP %s",
                    symbol,
                    timeframe,
                    response.status,
                )
                return None

        except asyncio.TimeoutError as e:
            logger.error("Erro no fallback OHLCV para %s@%s: %s", symbol, timeframe, e)
            logger.warning(
                "[ERROR] DADOS INVALIDOS: DataFrame vazio para %s@%s", symbol, timeframe
            )
            return None
        except Exception as e:
            # NETWORK FIX: Melhor tratamento de DNS timeout
            if "Timeout while contacting DNS servers" in str(
                e
            ) or "Cannot connect to host" in str(e):
                logger.error(
                    "Erro no fallback OHLCV para %s@%s: %s", symbol, timeframe, e
                )
                logger.warning(
                    "[ERROR] DADOS INVALIDOS: DataFrame vazio para %s@%s",
                    symbol,
                    timeframe,
                )
            else:
                logger.error(
                    "Erro no fallback OHLCV para %s@%s: %s", symbol, timeframe, e
                )
            return None

    def _calculate_technical_indicators(
        self, ohlcv_df: pd.DataFrame
    ) -> Dict[str, float]:
        """Calcula indicadores técnicos a partir dos dados OHLCV."""

        try:
            # Dados mais recentes
            latest = ohlcv_df.iloc[-1]

            # RSI (14 períodos)
            closes = ohlcv_df["close"].values
            rsi_values = rsi(closes, period=14)
            current_rsi = (
                rsi_values[-1]
                if len(rsi_values) > 0 and not np.isnan(rsi_values[-1])
                else None
            )

            # Volume ratio (volume atual vs média)
            volumes = ohlcv_df["volume"].values
            volume_ratio = (
                volumes[-1] / np.mean(volumes[-20:]) if len(volumes) >= 20 else 1.0
            )

            # Mudança percentual
            price_change_pct = (
                (latest["close"] - latest["open"]) / latest["open"]
            ) * 100

            # Volatilidade (desvio padrão dos retornos)
            returns = np.diff(closes) / closes[:-1]
            volatility = np.std(returns[-20:]) * 100 if len(returns) >= 20 else 0.0

            return {
                "open": float(latest["open"]),
                "high": float(latest["high"]),
                "low": float(latest["low"]),
                "close": float(latest["close"]),
                "volume": float(latest["volume"]),
                "rsi": float(current_rsi) if current_rsi is not None else None,
                "volume_ratio": float(volume_ratio),
                "price_change_pct": float(price_change_pct),
                "volatility": float(volatility),
            }

        except Exception as e:
            logger.error(f"Erro calculando indicadores: {e}")
            return {}

    async def _encode_quantum_states(
        self, market_data: Dict[str, Any]
    ) -> Dict[str, np.ndarray]:
        """Codifica indicadores em estados quânticos."""

        quantum_states = {}
        current_timestamp = time.time()

        try:
            # Encode RSI
            if market_data.get("rsi") is not None:
                rsi_snapshot = {
                    "rsi": market_data["rsi"],
                    "timestamp": current_timestamp,
                }
                quantum_states["rsi"] = self.rsi_encoder.encode(rsi_snapshot)

            # Encode Volume Ratio
            if market_data.get("volume_ratio") is not None:
                volume_snapshot = {
                    "volume_ratio": market_data["volume_ratio"],
                    "timestamp": current_timestamp,
                }
                quantum_states["volume"] = self.volume_encoder.encode(volume_snapshot)

        except Exception as e:
            logger.error(f"Erro no encoding quântico: {e}")

        return quantum_states

    def convert_to_holographic_events(
        self,
        enhanced_data: List[EnhancedMarketData],
        news_events: List[EnhancedNewsEvent],
        universe_field_size: tuple,
    ) -> List[HolographicEvent]:
        """
        Converte dados enriquecidos em eventos holográficos avançados.
        """
        events = []
        current_time = time.time()

        news_events = self._deduplicate_news_events(news_events)

        # Obtém amplificações calibradas
        price_amp = self.amplification_calibrator.get_calibrated_amplification("price")
        news_amp = self.amplification_calibrator.get_calibrated_amplification("news")
        pattern_threshold = self.amplification_calibrator.get_pattern_threshold()

        logger.debug(
            f"Usando amplificações calibradas: price={price_amp:.2f}, "
            f"news={news_amp:.2f}, threshold={pattern_threshold:.2f}"
        )

        # Processa dados de mercado enriquecidos
        for data in enhanced_data:
            # Posição baseada no símbolo e timeframe
            symbol_hash = hash(data.symbol) % (
                universe_field_size[0] * universe_field_size[1]
            )
            x = symbol_hash % universe_field_size[0]
            y = symbol_hash // universe_field_size[0]

            # Amplitude baseada em múltiplos fatores COM CALIBRAÇÃO
            base_amplitude = abs(data.price_change_pct or 0) / 2.0

            # Amplifica com RSI (sobrevenda/sobrecompra)
            if data.rsi is not None:
                if data.rsi > 70:  # Sobrecompra
                    base_amplitude *= 1.5
                elif data.rsi < 30:  # Sobrevenda
                    base_amplitude *= 1.8

            # Amplifica com volume
            if data.volume_ratio is not None and data.volume_ratio > 1.5:
                base_amplitude *= 1.3

            # Amplifica com volatilidade
            if data.volatility is not None and data.volatility > 3.0:
                base_amplitude *= 1.2

            # Aplica amplificação calibrada
            amplitude = min(base_amplitude * price_amp, 8.0)  # Cap em 8.0
            if data.price_change_pct and data.price_change_pct < 0:
                amplitude = -amplitude

            # Cria evento holográfico enriquecido
            events.append(
                HolographicEvent(
                    position=(x, y),
                    time=current_time,
                    amplitude=amplitude,
                    spatial_sigma=10.0 + (data.volatility or 0) * 0.5,
                    temporal_sigma=5.0,
                    event_type=f"enhanced_market_{data.symbol.lower()}",
                    source_data={
                        "symbol": data.symbol,
                        "ohlcv": {
                            "open": data.open,
                            "high": data.high,
                            "low": data.low,
                            "close": data.close,
                            "volume": data.volume,
                        },
                        "indicators": {
                            "rsi": data.rsi,
                            "volume_ratio": data.volume_ratio,
                            "volatility": data.volatility,
                            "price_change_pct": data.price_change_pct,
                        },
                        "quantum_states": {
                            "rsi_encoded": data.rsi_quantum_state is not None,
                            "volume_encoded": data.volume_quantum_state is not None,
                        },
                    },
                    confidence=0.95,  # Maior confiança por ser dados enriquecidos
                )
            )

        # Processa eventos de notícias com sentiment quântico
        for news in news_events:
            x = 150 + int(np.random.normal(0, 15))
            y = 150 + int(np.random.normal(0, 15))
            x = max(0, min(universe_field_size[0] - 1, x))
            y = max(0, min(universe_field_size[1] - 1, y))

            # Amplitude baseada no sentiment quântico COM CALIBRAÇÃO
            amplitude = (news.sentiment_score * 4.0 + 1.0) * news_amp

            events.append(
                HolographicEvent(
                    position=(x, y),
                    time=current_time,
                    amplitude=amplitude,
                    spatial_sigma=15.0,
                    temporal_sigma=8.0,
                    event_type="enhanced_news_event",
                    source_data={
                        "title": news.title,
                        "sentiment": news.sentiment_score,
                        "quantum_encoded": news.quantum_sentiment_state is not None,
                        "source": news.source,
                    },
                    confidence=0.8,
                )
            )

        logger.info(
            f"Convertidos {len(events)} eventos holográficos enriquecidos "
            f"(calibração: price_amp={price_amp:.2f}, news_amp={news_amp:.2f})"
        )
        return events

    def _deduplicate_news_events(
        self, news_events: List[EnhancedNewsEvent]
    ) -> List[EnhancedNewsEvent]:
        """Remove eventos duplicados e aplica limiar de score."""

        unique: Dict[str, EnhancedNewsEvent] = {}
        for event in news_events:
            if abs(event.sentiment_score) < 0.05:
                continue
            guid = (
                event.guid
                or hashlib.sha1(f"{event.url}|{event.title}".encode()).hexdigest()
            )
            if guid not in unique:
                unique[guid] = event
        return list(unique.values())

    def get_calibration_report(self) -> Dict[str, Any]:
        """Retorna relatório de calibração do amplificador."""
        return self.amplification_calibrator.get_calibration_report()

    def record_pattern_feedback(
        self,
        patterns_detected: int,
        signals_generated: int,
        signals_executed: int,
        execution_results: List[bool],
    ):
        """Registra feedback para calibração adaptativa."""
        self.amplification_calibrator.record_pattern_detection(
            patterns_detected=patterns_detected,
            signals_generated=signals_generated,
            signals_executed=signals_executed,
            execution_results=execution_results,
        )

    async def process_raw_data(
        self, raw_data: List[Dict[str, Any]]
    ) -> List[EnhancedMarketData]:
        """Processa dados crus e calcula indicadores técnicos.

        Parameters
        ----------
        raw_data : list of dict
            Lista de dicionários contendo registros de mercado. Cada
            elemento deve possuir ao menos as chaves ``symbol`` e
            ``price`` e pode incluir ``volume`` e ``timestamp``.

        Returns
        -------
        list of EnhancedMarketData
            Coleção de :class:`EnhancedMarketData` gerada a partir dos
            registros fornecidos. Quando valores estiverem ausentes,
            os campos de indicadores receberão ``None``.
        """
        if not raw_data:
            return []

        df = pd.DataFrame(raw_data)
        df = df[df["symbol"].notna()].copy()
        if is_data_empty(df):
            return []

        df["open"] = df["price"] * 0.999
        df["high"] = df["price"] * 1.001
        df["low"] = df["price"] * 0.998
        df["close"] = df["price"]
        df["volume"] = pd.to_numeric(df.get("volume", 0), errors="coerce").fillna(0)

        closes = df["close"].astype(float)
        volumes = df["volume"].astype(float)

        df["rsi"] = rsi(closes, period=14)
        df["volume_ratio"] = volumes / volumes.rolling(window=20).mean()
        df["price_change_pct"] = (df["close"] - df["open"]) / df["open"] * 100
        returns = closes.pct_change()
        df["volatility"] = returns.rolling(window=20).std() * 100

        enhanced_points: List[EnhancedMarketData] = []
        for row in df.itertuples(index=False):
            # YAA: MarketDataPoint não tem timeframe, usar padrão "realtime" para dados em tempo real
            timeframe = getattr(row, "timeframe", "realtime")

            enhanced_points.append(
                EnhancedMarketData(
                    symbol=row.symbol,
                    timeframe=timeframe,
                    timestamp=(
                        float(row.timestamp)
                        if hasattr(row, "timestamp")
                        else time.time()
                    ),
                    open=float(row.open),
                    high=float(row.high),
                    low=float(row.low),
                    close=float(row.close),
                    volume=float(row.volume),
                    rsi=None if pd.isna(row.rsi) else float(row.rsi),
                    volume_ratio=(
                        None if pd.isna(row.volume_ratio) else float(row.volume_ratio)
                    ),
                    price_change_pct=(
                        None
                        if pd.isna(row.price_change_pct)
                        else float(row.price_change_pct)
                    ),
                    volatility=(
                        None if pd.isna(row.volatility) else float(row.volatility)
                    ),
                )
            )

        return enhanced_points

    async def collect_news_events(self) -> List[EnhancedNewsEvent]:
        """
        Coleta eventos de notícias de feeds RSS com encoding quântico.

        Implementa coleta RSS diretamente no EnhancedDataCollector para
        garantir compatibilidade com o pipeline holográfico.

        Returns:
            Lista de eventos de notícias enriquecidos
        """
        news_events = []
        logger.info(
            f"🔍 EnhancedDataCollector: Iniciando coleta RSS de {len(self.news_feeds)} feeds"
        )

        # Cria sessão temporária se não existir
        session = None
        need_close = False

        try:
            if not hasattr(self, "session") or self.session is None:
                session = aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=10),
                    headers={"User-Agent": "QUALIA-Enhanced-System/1.0"},
                )
                need_close = True
            else:
                session = self.session

            async def process_feed(feed_url: str) -> None:
                """Coleta e processa um único feed de notícias."""
                logger.info(f"🔍 EnhancedDataCollector: Buscando feed: {feed_url}")
                content = await self._fetch_news_feed(feed_url, session)
                if not content:
                    logger.warning(
                        f"🔍 EnhancedDataCollector: Falha ao buscar conteúdo de {feed_url}"
                    )
                    return

                logger.info(
                    f"🔍 EnhancedDataCollector: Conteúdo obtido de {feed_url}, analisando..."
                )

                try:
                    feed = feedparser.parse(content)
                    logger.info(
                        f"🔍 EnhancedDataCollector: Feed parseado - {len(feed.entries)} entradas encontradas"
                    )

                    for i, entry in enumerate(feed.entries[:3]):
                        text_content = entry.title + " " + entry.get("summary", "")
                        sentiment_snap = {"text": text_content}

                        logger.debug(
                            f"🔍 EnhancedDataCollector: Processando entrada {i+1}: {entry.title[:50]}..."
                        )

                        sentiment_score = self.sentiment_encoder._get_sentiment(
                            sentiment_snap
                        )
                        quantum_state = self.sentiment_encoder._encode_single(
                            sentiment_snap
                        )

                        unique_str = f"{entry.link}|{entry.title}"
                        guid = hashlib.sha1(unique_str.encode()).hexdigest()
                        news_events.append(
                            EnhancedNewsEvent(
                                title=entry.title,
                                content=entry.get("summary", "")[:300],
                                timestamp=time.time(),
                                sentiment_score=sentiment_score,
                                quantum_sentiment_state=quantum_state,
                                source=getattr(feed, "title", feed_url),
                                url=entry.link,
                                guid=guid,
                            )
                        )

                        logger.info(
                            f"✅ EnhancedDataCollector RSS: '{entry.title[:40]}...' "
                            f"score={sentiment_score:.3f} "
                            f"quantum_state=[{quantum_state[0]:.3f}, {quantum_state[1]:.3f}]"
                        )

                    await asyncio.sleep(0.5)

                except Exception as exc:
                    logger.error(
                        f"❌ EnhancedDataCollector: Erro processando feed {feed_url}: {exc}",
                        exc_info=True,
                    )

            tasks = [asyncio.create_task(process_feed(url)) for url in self.news_feeds]

            await asyncio.gather(*tasks)

        except Exception as e:
            logger.error(
                f"❌ EnhancedDataCollector: Erro geral na coleta de notícias: {e}",
                exc_info=True,
            )
        finally:
            if need_close and session:
                await session.close()

        logger.info(
            f"✅ EnhancedDataCollector: Coletados {len(news_events)} eventos de notícias RSS"
        )
        return news_events

    async def _fetch_news_feed(
        self,
        feed_url: str,
        session: "aiohttp.ClientSession",
        attempts: int = 3,
        initial_backoff: float = 1.0,
    ) -> Optional[str]:
        """Busca RSS com retentativa exponencial e jitter aleatório."""

        backoff = initial_backoff
        for attempt in range(1, attempts + 1):
            try:
                async with session.get(feed_url) as response:
                    if response.status == 200:
                        return await response.text()
                    raise RuntimeError(f"HTTP {response.status}")
            except Exception as exc:
                reason = str(exc)
                if attempt == attempts:
                    self._log_feed_failure(feed_url, reason)
                    return None

                jitter = random.uniform(0.8, 1.2)
                await asyncio.sleep(backoff * jitter)
                backoff *= 2

        return None

    def _log_feed_failure(self, feed_url: str, reason: str) -> None:
        """Registra aviso limitado para feed com falha."""

        now = time.time()
        last_time = self.feed_warning_timestamps.get(feed_url, 0.0)
        if now - last_time > 3600:  # Limita avisos por feed a cada hora
            logger.warning(
                f"EnhancedDataCollector: Erro coletando feed {feed_url}: {reason}"
            )
            self.feed_warning_timestamps[feed_url] = now
        else:
            logger.debug(
                f"EnhancedDataCollector: Erro repetido para feed {feed_url}: {reason}"
            )

    async def fetch_historical_data_for_warmup(
        self,
        *,
        start_timestamp_ms: int,
        end_timestamp_ms: int,
    ) -> List[EnhancedMarketData]:
        """Obtém dados históricos para o Warm-up holográfico.

        Parameters
        ----------
        start_timestamp_ms
            Timestamp inicial em milissegundos.
        end_timestamp_ms
            Timestamp final em milissegundos.

        Returns
        -------
        list of EnhancedMarketData
            Lista de registros enriquecidos de mercado.
        """

        if not self.data_fetcher:
            logger.warning(
                "DataFetcher ausente no EnhancedDataCollector. Sem dados de histórico."
            )
            return []

        start_dt = datetime.utcfromtimestamp(start_timestamp_ms / 1000)
        end_dt = datetime.utcfromtimestamp(end_timestamp_ms / 1000)

        all_data: List[EnhancedMarketData] = []

        for symbol in self.symbols:
            for timeframe in self.timeframes:
                spec = MarketSpec(symbol=symbol, timeframe=timeframe)
                try:
                    df = await self.data_fetcher.fetch_historical_data(
                        spec=spec,
                        start_date=start_dt,
                        end_date=end_dt,
                        use_cache=False,
                    )
                except Exception as exc:  # pragma: no cover - defensive
                    logger.error(
                        "Erro buscando historico para %s@%s: %s",
                        symbol,
                        timeframe,
                        exc,
                    )
                    continue

                if df is None or df.empty:
                    logger.debug(
                        "Dados historicos vazios para %s@%s no warm-up",
                        symbol,
                        timeframe,
                    )
                    continue

                df = df.sort_values("timestamp")

                closes = df["close"].astype(float)
                volumes = df["volume"].astype(float)

                df["rsi"] = rsi(closes, period=14)
                df["volume_ratio"] = volumes / volumes.rolling(window=20).mean()
                df["price_change_pct"] = (df["close"] - df["open"]) / df["open"] * 100
                returns = closes.pct_change()
                df["volatility"] = returns.rolling(window=20).std() * 100

                for row in df.itertuples(index=False):
                    timestamp = (
                        row.timestamp.timestamp()
                        if isinstance(row.timestamp, pd.Timestamp)
                        else row.timestamp
                    )
                    all_data.append(
                        EnhancedMarketData(
                            symbol=symbol,
                            timeframe=timeframe,
                            timestamp=float(timestamp),
                            open=float(row.open),
                            high=float(row.high),
                            low=float(row.low),
                            close=float(row.close),
                            volume=float(row.volume),
                            rsi=None if pd.isna(row.rsi) else float(row.rsi),
                            volume_ratio=(
                                None
                                if pd.isna(row.volume_ratio)
                                else float(row.volume_ratio)
                            ),
                            price_change_pct=(
                                None
                                if pd.isna(row.price_change_pct)
                                else float(row.price_change_pct)
                            ),
                            volatility=(
                                None
                                if pd.isna(row.volatility)
                                else float(row.volatility)
                            ),
                        )
                    )

        all_data.sort(key=lambda x: x.timestamp)
        logger.info(
            "Dados históricos processados para warm-up: %d registros", len(all_data)
        )
        return all_data


# Factory function
def create_enhanced_data_collector(
    symbols: Optional[List[str]] = None,
    timeframes: Optional[List[str]] = None,
    exchange_config: Optional[Dict[str, Any]] = None,
    cache_ttl_minutes: int = 5,
) -> EnhancedDataCollector:
    """
    Cria instância do coletor de dados enriquecido.

    Args:
        symbols: Lista de símbolos para monitorar
        timeframes: Lista de timeframes para análise
        exchange_config: Configuração da exchange
        cache_ttl_minutes: TTL do cache em minutos

    Returns:
        Instância configurada do EnhancedDataCollector
    """
    return EnhancedDataCollector(
        symbols,
        timeframes,
        exchange_config,
        cache_ttl_minutes,
    )
