#!/usr/bin/env python3
"""
QUALIA Parameter Tuner Worker - Etapa D
YAA IMPLEMENTATION: Worker que executa tuning contínuo de parâmetros em background.
"""

import sys
import asyncio
import time
import json
import logging
import signal
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import threading
from concurrent.futures import ThreadPoolExecutor

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from src.bayesian_optimizer import BayesianOptimizer, OptimizationConfig
except ImportError:
    # Fallback se não conseguir importar
    logging.warning("Não foi possível importar BayesianOptimizer, usando modo standalone")
    BayesianOptimizer = None
    OptimizationConfig = None

class ParameterTunerWorker:
    """Worker para tuning contínuo de parâmetros."""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config/tuner_config.json"
        self.optimizer = None
        self.is_running = False
        self.worker_thread = None
        self.stats = {
            'start_time': None,
            'cycles_completed': 0,
            'best_pnl_ever': -999.0,
            'last_update': None,
            'total_trials': 0
        }
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - TUNER - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/parameter_tuner.log'),
                logging.StreamHandler()
            ]
        )
        
        # Cria diretórios
        Path("logs").mkdir(exist_ok=True)
        Path("config").mkdir(exist_ok=True)
        Path("results/parameter_tuner").mkdir(parents=True, exist_ok=True)
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handler para sinais de parada."""
        logging.info(f"Recebido sinal {signum}, parando worker...")
        self.stop()
    
    def load_config(self) -> OptimizationConfig:
        """Carrega configuração do worker."""
        
        default_config = {
            "study_name": "qualia_parameter_tuner",
            "n_trials_per_cycle": 10,
            "optimization_cycles": 1000,
            "pnl_window_hours": 24,
            "symbols": ["BTCUSDT", "ETHUSDT"],
            "base_params": {
                "price_amplification": 2.0,
                "news_amplification": 7.0,
                "min_confidence": 0.4
            },
            "worker_settings": {
                "cycle_interval_minutes": 10,
                "max_runtime_hours": 24,
                "auto_restart": True,
                "performance_threshold": -5.0
            }
        }
        
        try:
            if Path(self.config_file).exists():
                with open(self.config_file, 'r') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
                    logging.info(f"Configuração carregada de: {self.config_file}")
            else:
                # Salva configuração padrão
                with open(self.config_file, 'w') as f:
                    json.dump(default_config, f, indent=2)
                logging.info(f"Configuração padrão criada em: {self.config_file}")
        
        except Exception as e:
            logging.error(f"Erro ao carregar configuração: {e}")
            logging.info("Usando configuração padrão")
        
        # Converte para OptimizationConfig
        config = OptimizationConfig(
            study_name=default_config["study_name"],
            n_trials_per_cycle=default_config["n_trials_per_cycle"],
            optimization_cycles=default_config["optimization_cycles"],
            pnl_window_hours=default_config["pnl_window_hours"],
            symbols=default_config["symbols"],
            base_params=default_config["base_params"]
        )
        
        self.worker_settings = default_config["worker_settings"]
        
        return config
    
    def update_stats(self, cycle_result: Dict[str, Any]):
        """Atualiza estatísticas do worker."""
        
        self.stats['cycles_completed'] += 1
        self.stats['last_update'] = datetime.now().isoformat()
        self.stats['total_trials'] = cycle_result.get('n_trials', 0)
        
        current_pnl = cycle_result.get('best_pnl_24h', -999.0)
        if current_pnl > self.stats['best_pnl_ever']:
            self.stats['best_pnl_ever'] = current_pnl
            logging.info(f"🎉 Novo recorde de PnL: {current_pnl:.4f}%")
        
        # Salva estatísticas
        stats_file = Path("results/parameter_tuner/worker_stats.json")
        with open(stats_file, 'w') as f:
            json.dump(self.stats, f, indent=2)
    
    def check_performance(self) -> bool:
        """Verifica se a performance está aceitável."""
        
        threshold = self.worker_settings.get('performance_threshold', -5.0)
        current_best = self.stats['best_pnl_ever']
        
        if current_best < threshold:
            logging.warning(f"Performance abaixo do threshold: {current_best:.4f}% < {threshold}%")
            return False
        
        return True
    
    async def run_worker_cycle(self):
        """Executa um ciclo do worker."""
        
        try:
            # Executa ciclo de otimização
            cycle_result = self.optimizer.run_optimization_cycle()
            
            # Atualiza estatísticas
            self.update_stats(cycle_result)
            
            # Log do progresso
            logging.info(f"Ciclo {self.stats['cycles_completed']} concluído")
            logging.info(f"Melhor PnL atual: {cycle_result.get('best_pnl_24h', 0):.4f}%")
            logging.info(f"Parâmetros: {cycle_result.get('best_params', {})}")
            
            # Verifica performance
            if not self.check_performance():
                logging.warning("Performance baixa detectada")
            
            return cycle_result
            
        except Exception as e:
            logging.error(f"Erro no ciclo do worker: {e}")
            return None
    
    async def worker_loop(self):
        """Loop principal do worker."""
        
        self.stats['start_time'] = datetime.now().isoformat()
        cycle_interval = self.worker_settings.get('cycle_interval_minutes', 10) * 60
        max_runtime = self.worker_settings.get('max_runtime_hours', 24) * 3600
        
        start_time = time.time()
        
        logging.info(f"Worker iniciado - Intervalo: {cycle_interval/60:.1f}min, Runtime máximo: {max_runtime/3600:.1f}h")
        
        while self.is_running:
            try:
                # Verifica tempo máximo de execução
                if time.time() - start_time > max_runtime:
                    logging.info("Tempo máximo de execução atingido")
                    break
                
                # Executa ciclo
                cycle_result = await self.run_worker_cycle()
                
                if cycle_result is None:
                    logging.error("Falha no ciclo, aguardando antes de tentar novamente...")
                    await asyncio.sleep(60)  # Aguarda 1 minuto em caso de erro
                    continue
                
                # Aguarda próximo ciclo
                if self.is_running:
                    logging.info(f"Aguardando próximo ciclo ({cycle_interval/60:.1f} minutos)...")
                    await asyncio.sleep(cycle_interval)
                
            except asyncio.CancelledError:
                logging.info("Worker cancelado")
                break
            except Exception as e:
                logging.error(f"Erro no loop do worker: {e}")
                await asyncio.sleep(60)  # Aguarda antes de tentar novamente
        
        logging.info("Worker loop finalizado")
    
    def start(self):
        """Inicia o worker."""
        
        if self.is_running:
            logging.warning("Worker já está rodando")
            return
        
        logging.info("🚀 Iniciando Parameter Tuner Worker")
        
        try:
            # Carrega configuração
            config = self.load_config()
            
            # Inicializa otimizador
            self.optimizer = BayesianOptimizer(config)
            self.optimizer.initialize_study()
            
            # Inicia worker
            self.is_running = True
            
            # Executa loop em thread separada
            def run_async_loop():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(self.worker_loop())
                finally:
                    loop.close()
            
            self.worker_thread = threading.Thread(target=run_async_loop, daemon=True)
            self.worker_thread.start()
            
            logging.info("✅ Parameter Tuner Worker iniciado com sucesso")
            
        except Exception as e:
            logging.error(f"Erro ao iniciar worker: {e}")
            self.is_running = False
            raise
    
    def stop(self):
        """Para o worker."""
        
        if not self.is_running:
            logging.info("Worker já está parado")
            return
        
        logging.info("Parando Parameter Tuner Worker...")
        
        self.is_running = False
        
        if self.optimizer:
            self.optimizer.stop_optimization()
        
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=10)
        
        # Salva estatísticas finais
        self.stats['stop_time'] = datetime.now().isoformat()
        stats_file = Path("results/parameter_tuner/final_worker_stats.json")
        with open(stats_file, 'w') as f:
            json.dump(self.stats, f, indent=2)
        
        logging.info("✅ Parameter Tuner Worker parado")
    
    def get_status(self) -> Dict[str, Any]:
        """Retorna status atual do worker."""
        
        status = {
            'is_running': self.is_running,
            'stats': self.stats.copy(),
            'current_best_params': self.optimizer.get_current_best_params() if self.optimizer else None,
            'worker_settings': self.worker_settings
        }
        
        return status
    
    def get_current_best_params(self) -> Optional[Dict[str, float]]:
        """Retorna os melhores parâmetros atuais."""
        
        if self.optimizer:
            return self.optimizer.get_current_best_params()
        
        return None


def main():
    """Função principal do worker."""
    
    print("🔧 QUALIA PARAMETER TUNER WORKER")
    print("=" * 50)
    
    # Inicializa worker
    worker = ParameterTunerWorker()
    
    try:
        # Inicia worker
        worker.start()
        
        print("✅ Worker iniciado! Pressione Ctrl+C para parar.")
        print("📊 Logs disponíveis em: logs/parameter_tuner.log")
        print("📈 Resultados em: results/parameter_tuner/")
        
        # Mantém o processo vivo
        while worker.is_running:
            time.sleep(10)
            
            # Mostra status periodicamente
            status = worker.get_status()
            if status['stats']['cycles_completed'] > 0:
                print(f"📊 Status: {status['stats']['cycles_completed']} ciclos, "
                      f"Melhor PnL: {status['stats']['best_pnl_ever']:.4f}%")
    
    except KeyboardInterrupt:
        print("\n🛑 Interrompido pelo usuário")
    except Exception as e:
        print(f"❌ Erro: {e}")
    finally:
        worker.stop()
        print("👋 Worker finalizado")


if __name__ == "__main__":
    main()
