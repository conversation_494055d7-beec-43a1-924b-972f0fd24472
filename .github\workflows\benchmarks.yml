name: Benchmark Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

jobs:
  benchmarks:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"]
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          ./scripts/install_test_deps.sh
          pip install pytest-benchmark

      - name: Prepare results directory
        run: mkdir -p benchmarks/results

      - name: Run benchmarks without GPU
        env:
          QUALIA_USE_GPU: "false"
          QUALIA_NO_DISPLAY: "true"
        run: |
          pytest benchmarks --benchmark-save=cpu --benchmark-json=benchmarks/results/benchmark_cpu.json -vv

      - name: Run benchmarks with GPU
        env:
          QUALIA_USE_GPU: "true"
          QUALIA_NO_DISPLAY: "true"
        run: |
          pytest benchmarks --benchmark-save=gpu --benchmark-json=benchmarks/results/benchmark_gpu.json -vv

      - name: Compare CPU vs GPU
        run: |
          pytest-benchmark compare cpu gpu --csv benchmarks/results/benchmark_compare.csv > benchmarks/results/benchmark_compare.txt

      - name: Upload benchmark artifacts
        uses: actions/upload-artifact@v4
        with:
          name: pytest-benchmark-results
          path: benchmarks/results/
