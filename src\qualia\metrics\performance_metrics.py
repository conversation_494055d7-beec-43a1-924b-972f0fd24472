import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional

# YAA: Importações de QUALIA devem usar src.qualia
from ..utils.logger import get_logger

# Métricas de performance não dependem mais de utils.common_utils.
# Funções utilitárias são definidas localmente para reduzir acoplamento.


def calculate_sharpe_ratio(
    returns: List[float] | pd.Series,
    risk_free_rate: float = 0.0,
    periods_per_year: int = 252,
) -> float:
    """Calculate the annualized Sharpe ratio.

    Parameters
    ----------
    returns
        Sequence of periodic returns expressed in decimal form.
    risk_free_rate
        Return of a risk free asset for the same period as ``returns``.
    periods_per_year
        Number of return periods in one year (252 for daily returns).

    Returns
    -------
    float
        Annualized Sharpe ratio. ``0.0`` if the standard deviation is zero.
    """

    series = pd.Series(returns).dropna()
    excess_returns = series - risk_free_rate
    std_dev = excess_returns.std(ddof=0)
    if std_dev == 0 or series.empty:
        return 0.0
    mean_return = excess_returns.mean()
    return float((mean_return / std_dev) * np.sqrt(periods_per_year))


def calculate_sortino_ratio(
    returns: List[float] | pd.Series,
    risk_free_rate: float = 0.0,
    periods_per_year: int = 252,
) -> float:
    """Calculate the annualized Sortino ratio.

    Parameters
    ----------
    returns
        Sequence of periodic returns expressed in decimal form.
    risk_free_rate
        Return of a risk free asset for the same period as ``returns``.
    periods_per_year
        Number of return periods in one year (252 for daily returns).

    Returns
    -------
    float
        Annualized Sortino ratio. ``0.0`` if downside deviation is zero.
    """

    series = pd.Series(returns).dropna()
    excess_returns = series - risk_free_rate
    downside_returns = excess_returns[excess_returns < 0]
    downside_std = downside_returns.std(ddof=0)
    if downside_std == 0 or series.empty:
        return 0.0
    mean_return = excess_returns.mean()
    return float((mean_return / downside_std) * np.sqrt(periods_per_year))


def calculate_max_drawdown(values: List[float] | pd.Series) -> float:
    """Return the maximum drawdown for a series of portfolio values.

    Parameters
    ----------
    values
        Historical portfolio values.

    Returns
    -------
    float
        Maximum drawdown expressed as a positive proportion (e.g. ``0.1`` for
        a 10% drawdown).
    """

    series = pd.Series(values).dropna()
    if series.empty:
        return 0.0
    running_max = series.cummax()
    drawdowns = (running_max - series) / running_max
    return float(drawdowns.max())


def calculate_cagr(
    values: List[float] | pd.Series,
    periods_per_year: int = 252,
) -> float:
    """Compute the Compound Annual Growth Rate (CAGR).

    Parameters
    ----------
    values
        Sequence of portfolio values.
    periods_per_year
        Number of periods in a year corresponding to ``values``.

    Returns
    -------
    float
        CAGR as a decimal value. Returns ``0.0`` if ``values`` is empty or the
        initial value is zero.
    """

    series = pd.Series(values).dropna()
    if len(series) < 2 or series.iloc[0] == 0:
        return 0.0
    total_periods = len(series) - 1
    final_value = series.iloc[-1] / series.iloc[0]
    return float(final_value ** (periods_per_year / total_periods) - 1)


def calculate_quantum_advantage(
    tsvf_returns: List[float] | pd.Series,
    classical_returns: List[float] | pd.Series,
    risk_free_rate: float = 0.0,
    periods_per_year: int = 252,
) -> float:
    """Return the Sharpe ratio advantage of TSVF over a classical baseline.

    Parameters
    ----------
    tsvf_returns
        Returns generated by the TSVF strategy.
    classical_returns
        Returns from a classical reference strategy.
    risk_free_rate
        Risk free rate used in the Sharpe calculation.
    periods_per_year
        Number of return periods in one year.

    Returns
    -------
    float
        ``calculate_sharpe_ratio(tsvf_returns)`` minus
        ``calculate_sharpe_ratio(classical_returns)``.
    """

    tsvf_sr = calculate_sharpe_ratio(tsvf_returns, risk_free_rate, periods_per_year)
    classical_sr = calculate_sharpe_ratio(
        classical_returns, risk_free_rate, periods_per_year
    )
    return float(tsvf_sr - classical_sr)


def cross_modal_coherence(vectors: List[np.ndarray]) -> float:
    """Return average absolute cosine similarity between modality vectors.

    Parameters
    ----------
    vectors
        List of encoded modality vectors (already L2-normalized). Each element
        must be a 1-D ``numpy.ndarray`` of equal length, but lengths may differ
        across modalities – in that case, the shorter vector is zero-padded to
        the longest size.

    Returns
    -------
    float
        Coerência média no intervalo ``[0, 1]``; retorna ``0.0`` se menos de
        dois vetores forem fornecidos.
    """

    if len(vectors) < 2:
        return 0.0

    # Encontrar dimensão máxima e pad os demais
    max_dim = max(v.size for v in vectors)
    if max_dim == 0:
        return 0.0

    padded = np.stack([np.pad(vec, (0, max_dim - vec.size)) for vec in vectors])
    norms = np.linalg.norm(padded, axis=1, keepdims=True)
    norms[norms < 1e-9] = 1.0
    normalized = padded / norms

    # Produto escalar → matriz de similaridade coseno
    sim = np.abs(normalized @ normalized.T)
    n = sim.shape[0]
    # média do triângulo superior sem diagonal
    upper_tri_indices = np.triu_indices(n, k=1)
    mean_sim = sim[upper_tri_indices].mean() if upper_tri_indices[0].size else 0.0
    return float(np.clip(mean_sim, 0.0, 1.0))


def aggregate_trade_performance(
    trade_history: List[Dict[str, Any]],
    initial_capital: float,
    total_fees: float = 0.0,
) -> Dict[str, Any]:
    """Return aggregated performance metrics from trade history.

    Parameters
    ----------
    trade_history
        Sequence of trades with at least a ``pnl`` key.
    initial_capital
        Amount of capital at the start of trading.
    total_fees
        Total fees paid across all trades.

    Returns
    -------
    dict
        Aggregated metrics including drawdown and Sharpe ratio.
    """

    if not trade_history:
        return {
            "total_pnl": 0.0,
            "total_pnl_pct": 0.0,
            "win_rate": 0.0,
            "loss_rate": 0.0,
            "total_trades": 0,
            "winning_trades": 0,
            "losing_trades": 0,
            "max_drawdown": 0.0,
            "max_drawdown_pct": 0.0,
            "sharpe_ratio": 0.0,
            "average_trade_pnl": 0.0,
            "average_winning_trade": 0.0,
            "average_losing_trade": 0.0,
            "profit_factor": 0.0,
            "final_capital": initial_capital,
            "total_fees": total_fees,
            "portfolio_values": [initial_capital],
        }

    pnl_series = pd.Series(
        [
            trade.get("pnl", trade.get("realized_pnl_net", 0.0))
            for trade in trade_history
        ],
        dtype=float,
    )
    total_pnl = float(pnl_series.sum())
    total_pnl_pct = (total_pnl / initial_capital) * 100 if initial_capital > 0 else 0.0

    total_trades = len(pnl_series)
    winning_mask = pnl_series > 0
    losing_mask = pnl_series < 0
    winning_trades = int(winning_mask.sum())
    losing_trades = int(losing_mask.sum())

    win_rate = (winning_trades / total_trades) * 100 if total_trades else 0.0
    loss_rate = (losing_trades / total_trades) * 100 if total_trades else 0.0

    average_trade_pnl = total_pnl / total_trades if total_trades else 0.0
    average_winning_trade = (
        float(pnl_series[winning_mask].mean()) if winning_trades else 0.0
    )
    average_losing_trade = (
        float(pnl_series[losing_mask].mean()) if losing_trades else 0.0
    )

    gross_profit = float(pnl_series[winning_mask].sum())
    gross_loss = float(-pnl_series[losing_mask].sum())
    profit_factor = gross_profit / gross_loss if gross_loss > 0 else float("inf")

    portfolio_values = pd.Series(initial_capital + pnl_series.cumsum())
    portfolio_values = pd.concat(
        [pd.Series([initial_capital]), portfolio_values], ignore_index=True
    )
    max_drawdown_value = calculate_max_drawdown(portfolio_values)
    max_drawdown_pct = float(max_drawdown_value * 100)
    max_drawdown = float(max_drawdown_value * initial_capital)
    final_capital = float(portfolio_values.iloc[-1])

    if total_trades > 1:
        trade_returns_pct = pnl_series / initial_capital
        sharpe_ratio = calculate_sharpe_ratio(trade_returns_pct, periods_per_year=252)
    else:
        sharpe_ratio = 0.0

    return {
        "total_pnl": total_pnl,
        "total_pnl_pct": total_pnl_pct,
        "win_rate": win_rate,
        "loss_rate": loss_rate,
        "total_trades": total_trades,
        "winning_trades": winning_trades,
        "losing_trades": losing_trades,
        "max_drawdown": max_drawdown,
        "max_drawdown_pct": max_drawdown_pct,
        "sharpe_ratio": sharpe_ratio,
        "average_trade_pnl": average_trade_pnl,
        "average_winning_trade": average_winning_trade,
        "average_losing_trade": average_losing_trade,
        "profit_factor": profit_factor,
        "final_capital": final_capital,
        "total_fees": total_fees,
        "portfolio_values": portfolio_values.tolist(),
    }


logger = get_logger(__name__)

__all__ = [  # type: ignore[var-annotated]
    "calculate_sharpe_ratio",
    "calculate_sortino_ratio",
    "calculate_max_drawdown",
    "calculate_cagr",
    "calculate_quantum_advantage",
    "cross_modal_coherence",
    "aggregate_trade_performance",
]
