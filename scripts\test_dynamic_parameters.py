#!/usr/bin/env python3
"""
Test script for Dynamic Parameter Optimization in Enhanced Quantum Momentum Strategy.

This script validates the dynamic parameter manager and enhanced confidence calculator
to ensure they improve signal quality and confidence scores.
"""

import sys
import os
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.strategies.enhanced_quantum_momentum.core import EnhancedQuantumMomentumStrategy
from qualia.strategies.enhanced_quantum_momentum.dynamic_parameter_manager import DynamicParameterManager
from qualia.strategies.enhanced_quantum_momentum.enhanced_confidence_calculator import EnhancedConfidenceCalculator
from qualia.config.config_manager import ConfigManager
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


def create_sample_market_data(length: int = 200) -> pd.DataFrame:
    """Create sample market data for testing."""
    dates = pd.date_range(start=datetime.now() - timedelta(days=length), periods=length, freq='1H')
    
    # Generate realistic price data with trend and volatility
    np.random.seed(42)
    base_price = 50000
    returns = np.random.normal(0.0001, 0.02, length)  # Small positive drift with volatility
    
    # Add some trend periods
    trend_periods = [
        (50, 80, 0.001),   # Uptrend
        (120, 150, -0.0008), # Downtrend
        (170, 190, 0.0005)   # Mild uptrend
    ]
    
    for start, end, trend in trend_periods:
        if start < length and end <= length:
            returns[start:end] += trend
    
    prices = base_price * np.cumprod(1 + returns)
    
    # Generate OHLCV data
    data = []
    for i, price in enumerate(prices):
        high = price * (1 + abs(np.random.normal(0, 0.01)))
        low = price * (1 - abs(np.random.normal(0, 0.01)))
        volume = np.random.uniform(1000, 5000)
        
        data.append({
            'open': price,
            'high': high,
            'low': low,
            'close': price,
            'volume': volume
        })
    
    df = pd.DataFrame(data, index=dates)
    return df


def test_dynamic_parameter_manager():
    """Test the Dynamic Parameter Manager."""
    print("🧪 Testing Dynamic Parameter Manager...")
    
    manager = DynamicParameterManager(symbol="BTCUSDT")
    
    # Test different market regimes
    test_scenarios = [
        ("uptrend", 0.02, 0.8, 0.7),
        ("downtrend", 0.03, 0.6, 0.5),
        ("range", 0.015, 0.3, 0.4),
        ("neutral", 0.025, 0.5, 0.6)
    ]
    
    print("\n📊 Dynamic Parameter Adjustments:")
    for regime, volatility, trend_strength, quantum_coherence in test_scenarios:
        params = manager.get_dynamic_parameters(
            market_regime=regime,
            volatility=volatility,
            trend_strength=trend_strength,
            quantum_coherence=quantum_coherence
        )
        
        print(f"  {regime:>10}: threshold={params['signal_threshold']:.3f}, "
              f"quantum_weight={params['quantum_weight']:.3f}, "
              f"confidence_mult={params['confidence_multiplier']:.3f}")
    
    # Test performance feedback
    print("\n📈 Testing Performance Feedback:")
    for i in range(10):
        pnl = np.random.normal(0.5, 1.0)  # Random PnL
        success = pnl > 0
        manager.update_performance_feedback(pnl, success)
    
    state = manager.get_current_state()
    print(f"  Recent Performance: {state['recent_performance']}")
    print(f"  Recent Confidence: {state['recent_confidence']}")
    
    return True


def test_enhanced_confidence_calculator():
    """Test the Enhanced Confidence Calculator."""
    print("\n🧪 Testing Enhanced Confidence Calculator...")
    
    calculator = EnhancedConfidenceCalculator()
    
    # Create sample analysis result
    market_data = create_sample_market_data(50)
    
    analysis_result = {
        "rsi": pd.Series(np.random.uniform(30, 70, 50)),
        "ema_short": pd.Series(np.random.uniform(49000, 51000, 50)),
        "ema_long": pd.Series(np.random.uniform(48000, 52000, 50)),
        "trend_ema": pd.Series(np.random.uniform(47000, 53000, 50)),
        "macd": pd.Series(np.random.uniform(-100, 100, 50)),
        "macd_signal": pd.Series(np.random.uniform(-80, 80, 50)),
        "macd_histogram": pd.Series(np.random.uniform(-50, 50, 50)),
        "vol_short": 0.02,
        "vol_medium": 0.025,
        "directional_strength": 0.6
    }
    
    quantum_metrics = [
        {"coherence": 0.7, "entropy": 0.3, "ground_state_probability": 0.6},
        {"coherence": 0.6, "entropy": 0.4, "ground_state_probability": 0.5},
        {"coherence": 0.8, "entropy": 0.2, "ground_state_probability": 0.7}
    ]
    
    # Test different scenarios
    test_scenarios = [
        ("uptrend", 0.8),
        ("downtrend", -0.7),
        ("range", 0.4),
        ("neutral", 0.2)
    ]
    
    print("\n📊 Enhanced Confidence Calculations:")
    for regime, combined_score in test_scenarios:
        metrics = calculator.calculate_confidence(
            combined_score=combined_score,
            market_regime=regime,
            analysis_result=analysis_result,
            quantum_metrics=quantum_metrics,
            price_data=market_data
        )
        
        print(f"  {regime:>10}: confidence={metrics.final_confidence:.4f}, "
              f"base_score={metrics.factors.base_score:.3f}, "
              f"quantum_coherence={metrics.factors.quantum_coherence:.3f}")
        print(f"             {metrics.explanation}")
    
    # Test statistics
    stats = calculator.get_confidence_statistics()
    print(f"\n📈 Confidence Statistics:")
    print(f"  Recent Average: {stats.get('recent_avg_confidence', 0):.4f}")
    print(f"  Recent Max: {stats.get('recent_max_confidence', 0):.4f}")
    print(f"  Recent Min: {stats.get('recent_min_confidence', 0):.4f}")
    
    return True


def test_strategy_integration():
    """Test the integration with Enhanced Quantum Momentum Strategy."""
    print("\n🧪 Testing Strategy Integration...")
    
    try:
        # Initialize strategy with dynamic parameters enabled
        strategy = EnhancedQuantumMomentumStrategy(
            symbol="BTCUSDT",
            timeframe="1h",
            use_dynamic_parameters=True,
            signal_threshold=0.45,  # Base threshold
            quantum_weight=0.55,    # Base weight
            min_volatility=0.007,
            max_volatility=0.035
        )
        
        # Initialize strategy
        strategy.initialize()
        
        # Create sample market data
        market_data = create_sample_market_data(200)
        
        print(f"  ✅ Strategy initialized with dynamic parameters")
        print(f"  📊 Market data created: {len(market_data)} candles")
        
        # Test analysis
        analysis_result = strategy.analyze_market(market_data)
        
        if analysis_result:
            print(f"  ✅ Market analysis completed")
            print(f"  📈 Market regime: {analysis_result.get('market_regime', 'unknown')}")
            print(f"  📊 Volatility: {analysis_result.get('vol_short', 0):.4f}")
            print(f"  🎯 Directional strength: {analysis_result.get('directional_strength', 0):.3f}")
            
            # Test signal generation
            signals = strategy.generate_signals(analysis_result)
            
            if not signals.empty:
                print(f"  ✅ Signals generated: {len(signals)} signals")
                for _, signal in signals.iterrows():
                    print(f"    🎯 Signal: {signal.get('signal', 'none')} "
                          f"with confidence {signal.get('confidence', 0):.4f}")
            else:
                print(f"  ℹ️  No signals generated (normal for test data)")
            
            # Test dynamic state
            dynamic_state = strategy.get_dynamic_state()
            if dynamic_state:
                print(f"  ✅ Dynamic state retrieved")
                if 'dynamic_parameters' in dynamic_state:
                    dp = dynamic_state['dynamic_parameters']
                    print(f"    📊 Current regime: {dp.get('current_regime', 'unknown')}")
                    print(f"    🔧 Optimized params: {dp.get('optimized_parameters', {})}")
                
                if 'confidence_stats' in dynamic_state:
                    cs = dynamic_state['confidence_stats']
                    print(f"    📈 Confidence stats: {cs}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Strategy integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    print("🚀 TESTING ENHANCED QUANTUM MOMENTUM DYNAMIC PARAMETERS")
    print("=" * 70)
    
    tests = [
        ("Dynamic Parameter Manager", test_dynamic_parameter_manager),
        ("Enhanced Confidence Calculator", test_enhanced_confidence_calculator),
        ("Strategy Integration", test_strategy_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        print("-" * 50)
        
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"\n{status}: {test_name}")
        except Exception as e:
            print(f"\n❌ ERROR in {test_name}: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {status}: {test_name}")
    
    print(f"\n🎯 OVERALL: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Dynamic parameters are working correctly.")
        print("\n🚀 NEXT STEPS:")
        print("   1. ✅ Deploy to production with dynamic parameters enabled")
        print("   2. ✅ Monitor confidence improvements in real trading")
        print("   3. ✅ Validate performance against static parameters")
    else:
        print("⚠️  Some tests failed. Please review and fix issues before deployment.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
