#!/usr/bin/env python3
"""
Diagnóstico Profundo da Quantum Momentum
Identifica exatamente onde está o gargalo de performance.
"""

import pandas as pd
import numpy as np
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any


def fetch_current_data(symbol: str = "BTCUSDT", days: int = 30) -> pd.DataFrame:
    """Busca dados atuais do mercado."""
    try:
        end_time = int(datetime.now().timestamp() * 1000)
        start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
        
        url = "https://api.binance.com/api/v3/klines"
        params = {
            'symbol': symbol,
            'interval': '1h',
            'startTime': start_time,
            'endTime': end_time,
            'limit': 1000
        }
        
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        data = response.json()
        df = pd.DataFrame(data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])
        
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df.set_index('timestamp', inplace=True)
        df = df.sort_index().dropna()
        
        # Indicadores básicos
        df['returns'] = df['close'].pct_change()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['sma_50'] = df['close'].rolling(50).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        df['volatility'] = df['returns'].rolling(20).std()
        
        return df
        
    except Exception as e:
        print(f"❌ Erro ao buscar dados: {e}")
        return pd.DataFrame()


def diagnose_filter_impact(df: pd.DataFrame) -> Dict[str, Any]:
    """Diagnóstica o impacto de cada filtro na geração de sinais."""
    
    total_periods = len(df) - 50
    filter_stats = {
        'total_periods': total_periods,
        'vol_filter_pass': 0,
        'trend_filter_pass': 0,
        'rsi_filter_pass': 0,
        'all_filters_pass': 0,
        'signal_threshold_pass': 0,
        'final_signals': 0
    }
    
    signal_details = []
    
    for i in range(50, len(df)):
        # Testa cada filtro individualmente
        vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-20:i].quantile(0.7)
        trend_filter = abs(df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i] > 0.02
        rsi_filter = 32 < df['rsi'].iloc[i] < 68
        
        if vol_filter:
            filter_stats['vol_filter_pass'] += 1
        if trend_filter:
            filter_stats['trend_filter_pass'] += 1
        if rsi_filter:
            filter_stats['rsi_filter_pass'] += 1
        
        all_filters = vol_filter and trend_filter and rsi_filter
        if all_filters:
            filter_stats['all_filters_pass'] += 1
            
            # Calcula sinal
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
            long_momentum = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            
            signal = (
                price_momentum * 0.4 +
                vol_momentum * 0.2 +
                rsi_momentum * 0.2 +
                long_momentum * 0.2
            )
            
            signal_details.append({
                'timestamp': df.index[i],
                'signal_strength': abs(signal),
                'price_momentum': price_momentum,
                'vol_momentum': vol_momentum,
                'rsi_momentum': rsi_momentum,
                'long_momentum': long_momentum,
                'rsi': df['rsi'].iloc[i],
                'volatility': df['volatility'].iloc[i]
            })
            
            # Testa threshold
            if abs(signal) > 0.027:
                filter_stats['signal_threshold_pass'] += 1
                filter_stats['final_signals'] += 1
    
    # Calcula percentuais
    filter_percentages = {}
    for key, value in filter_stats.items():
        if key != 'total_periods':
            filter_percentages[f'{key}_pct'] = (value / total_periods * 100) if total_periods > 0 else 0
    
    return {
        'filter_stats': filter_stats,
        'filter_percentages': filter_percentages,
        'signal_details': signal_details
    }


def test_different_configurations(df: pd.DataFrame) -> Dict[str, Any]:
    """Testa diferentes configurações para identificar gargalos."""
    
    configurations = {
        'CURRENT_OPTIMIZED': {
            'rsi_range': (32, 68),
            'vol_quantile': 0.7,
            'trend_threshold': 0.02,
            'signal_threshold': 0.027,
            'stop_loss': -0.0048,
            'take_profit': 0.0095
        },
        'MORE_AGGRESSIVE': {
            'rsi_range': (25, 75),
            'vol_quantile': 0.8,
            'trend_threshold': 0.015,
            'signal_threshold': 0.02,
            'stop_loss': -0.006,
            'take_profit': 0.012
        },
        'LESS_RESTRICTIVE': {
            'rsi_range': (20, 80),
            'vol_quantile': 0.85,
            'trend_threshold': 0.01,
            'signal_threshold': 0.015,
            'stop_loss': -0.004,
            'take_profit': 0.008
        },
        'ORIGINAL_STYLE': {
            'rsi_range': (35, 65),
            'vol_quantile': 0.7,
            'trend_threshold': 0.02,
            'signal_threshold': 0.03,
            'stop_loss': -0.005,
            'take_profit': 0.008
        }
    }
    
    results = {}
    
    for config_name, config in configurations.items():
        signals = []
        
        for i in range(50, len(df)):
            # Aplica filtros da configuração
            vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-20:i].quantile(config['vol_quantile'])
            trend_filter = abs(df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i] > config['trend_threshold']
            rsi_filter = config['rsi_range'][0] < df['rsi'].iloc[i] < config['rsi_range'][1]
            
            if not (vol_filter and trend_filter and rsi_filter):
                signals.append(0)
                continue
            
            # Calcula sinal
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
            long_momentum = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            
            signal = (
                price_momentum * 0.4 +
                vol_momentum * 0.2 +
                rsi_momentum * 0.2 +
                long_momentum * 0.2
            )
            
            if abs(signal) > config['signal_threshold']:
                signals.append(np.clip(signal * 6, -1, 1))
            else:
                signals.append(0)
        
        # Calcula performance
        performance = calculate_performance(df.iloc[50:], signals, config)
        results[config_name] = performance
    
    return results


def calculate_performance(df: pd.DataFrame, signals: List[float], config: Dict) -> Dict[str, Any]:
    """Calcula performance com configuração específica."""
    
    returns = []
    trades = 0
    winning_trades = 0
    total_wins = 0
    total_losses = 0
    
    for i in range(1, len(signals)):
        if abs(signals[i-1]) > 0.1:
            price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
            raw_return = signals[i-1] * price_return
            
            # Aplica gestão de risco da configuração
            if raw_return < config['stop_loss']:
                final_return = config['stop_loss']
            elif raw_return > config['take_profit']:
                final_return = config['take_profit']
            else:
                final_return = raw_return
            
            returns.append(final_return)
            trades += 1
            
            if final_return > 0:
                winning_trades += 1
                total_wins += final_return
            else:
                total_losses += abs(final_return)
    
    if not returns:
        return {'error': 'Nenhum trade executado'}
    
    returns_series = pd.Series(returns)
    
    total_return = returns_series.sum()
    volatility = returns_series.std() * np.sqrt(252)
    sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
    
    cumulative = (1 + returns_series).cumprod()
    rolling_max = cumulative.expanding().max()
    drawdowns = (cumulative - rolling_max) / rolling_max
    max_drawdown = abs(drawdowns.min())
    
    win_rate = winning_trades / trades if trades > 0 else 0
    profit_factor = total_wins / total_losses if total_losses > 0 else 0
    
    return {
        'total_return_pct': total_return * 100,
        'sharpe_ratio': sharpe_ratio,
        'win_rate': win_rate * 100,
        'max_drawdown_pct': max_drawdown * 100,
        'total_trades': trades,
        'profit_factor': profit_factor
    }


def run_deep_diagnosis():
    """Executa diagnóstico profundo da Quantum Momentum."""
    
    print("🔬 DIAGNÓSTICO PROFUNDO: QUANTUM MOMENTUM")
    print("=" * 60)
    print("🎯 Identificando gargalos de performance")
    print("=" * 60)
    
    symbols = ["BTCUSDT", "ETHUSDT"]
    
    for symbol in symbols:
        print(f"\n📈 Diagnosticando {symbol}...")
        
        df = fetch_current_data(symbol, days=30)
        if df.empty:
            print(f"❌ Dados insuficientes para {symbol}")
            continue
        
        # 1. Diagnóstico de filtros
        filter_diagnosis = diagnose_filter_impact(df)
        
        print(f"\n🔍 ANÁLISE DE FILTROS:")
        print(f"   Total de períodos analisados: {filter_diagnosis['filter_stats']['total_periods']}")
        print(f"   Volatilidade filter pass: {filter_diagnosis['filter_percentages']['vol_filter_pass_pct']:.1f}%")
        print(f"   Trend filter pass: {filter_diagnosis['filter_percentages']['trend_filter_pass_pct']:.1f}%")
        print(f"   RSI filter pass: {filter_diagnosis['filter_percentages']['rsi_filter_pass_pct']:.1f}%")
        print(f"   TODOS os filtros pass: {filter_diagnosis['filter_percentages']['all_filters_pass_pct']:.1f}%")
        print(f"   Signal threshold pass: {filter_diagnosis['filter_percentages']['signal_threshold_pass_pct']:.1f}%")
        print(f"   Sinais finais gerados: {filter_diagnosis['filter_stats']['final_signals']}")
        
        # Identifica gargalo principal
        bottlenecks = []
        if filter_diagnosis['filter_percentages']['vol_filter_pass_pct'] < 50:
            bottlenecks.append("Filtro de volatilidade muito restritivo")
        if filter_diagnosis['filter_percentages']['trend_filter_pass_pct'] < 50:
            bottlenecks.append("Filtro de trend muito restritivo")
        if filter_diagnosis['filter_percentages']['rsi_filter_pass_pct'] < 50:
            bottlenecks.append("Filtro RSI muito restritivo")
        if filter_diagnosis['filter_percentages']['signal_threshold_pass_pct'] < 20:
            bottlenecks.append("Signal threshold muito alto")
        
        if bottlenecks:
            print(f"\n⚠️  GARGALOS IDENTIFICADOS:")
            for bottleneck in bottlenecks:
                print(f"   • {bottleneck}")
        
        # 2. Teste de configurações
        config_results = test_different_configurations(df)
        
        print(f"\n📊 TESTE DE CONFIGURAÇÕES:")
        print(f"{'Config':<18} {'Return':<8} {'Sharpe':<8} {'Win%':<6} {'Trades':<7}")
        print("-" * 50)
        
        best_config = None
        best_score = -999
        
        for config_name, result in config_results.items():
            if 'error' not in result:
                score = result['sharpe_ratio'] * 0.6 + result['total_return_pct'] * 0.4
                
                print(f"{config_name:<18} "
                      f"{result['total_return_pct']:>6.2f}% "
                      f"{result['sharpe_ratio']:>6.3f} "
                      f"{result['win_rate']:>5.1f}% "
                      f"{result['total_trades']:>6}")
                
                if score > best_score:
                    best_score = score
                    best_config = (config_name, result)
        
        if best_config:
            config_name, result = best_config
            print(f"\n🏆 MELHOR CONFIGURAÇÃO: {config_name}")
            print(f"   Return: {result['total_return_pct']:.2f}%")
            print(f"   Sharpe: {result['sharpe_ratio']:.3f}")
            print(f"   Win Rate: {result['win_rate']:.1f}%")
            print(f"   Trades: {result['total_trades']}")
        
        # 3. Análise de sinais
        if filter_diagnosis['signal_details']:
            signals = filter_diagnosis['signal_details']
            avg_signal_strength = np.mean([s['signal_strength'] for s in signals])
            strong_signals = [s for s in signals if s['signal_strength'] > 0.027]
            
            print(f"\n📊 ANÁLISE DE SINAIS:")
            print(f"   Sinais candidatos gerados: {len(signals)}")
            print(f"   Força média dos sinais: {avg_signal_strength:.4f}")
            print(f"   Sinais acima do threshold: {len(strong_signals)}")
            print(f"   Taxa de conversão: {len(strong_signals)/len(signals)*100:.1f}%")
            
            if signals:
                print(f"   RSI médio nos sinais: {np.mean([s['rsi'] for s in signals]):.1f}")
                print(f"   Volatilidade média: {np.mean([s['volatility'] for s in signals]):.4f}")
    
    print(f"\n💡 RECOMENDAÇÕES BASEADAS NO DIAGNÓSTICO:")
    print(f"   1. Se poucos sinais: Reduzir thresholds ou expandir filtros")
    print(f"   2. Se muitos trades perdedores: Melhorar gestão de risco")
    print(f"   3. Se baixo Sharpe: Ajustar stop-loss e take-profit")
    print(f"   4. Se baixo return: Considerar configuração mais agressiva")


if __name__ == "__main__":
    run_deep_diagnosis()
