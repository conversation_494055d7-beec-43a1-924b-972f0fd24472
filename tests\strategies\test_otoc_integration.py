"""
Testes para integração OTOC no sistema QUALIA.

YAA-TESTING: Cobertura dos edge-cases identificados no feedback.
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from qualia.utils.otoc_calculator import (
    calculate_otoc, 
    calculate_adaptive_threshold,
    OTOCCalculationError,
    get_otoc_diagnostics
)
from qualia.strategies.fibonacci_wave_hype.multi_timeframe_consolidator import (
    MultiTimeframeSignalConsolidator,
    TimeframeSignal,
    ConsolidatedSignal
)


class TestOTOCCalculator:
    """Testes para o calculador OTOC."""
    
    def test_high_chaos_blocked(self):
        """YAA-TEST: Pure Gaussian returns devem resultar em OTOC alto."""
        # Gerar retornos puramente aleatórios (Gaussian noise)
        np.random.seed(42)
        gaussian_returns = np.random.normal(0, 0.02, 200)

        # YAA-FIX: Usar índice específico que garante dados suficientes
        otoc_value = calculate_otoc(
            series=gaussian_returns,
            idx=100,  # Índice no meio da série
            window=50,
            method="correlation"
        )

        # Verificar se não é NaN
        assert not np.isnan(otoc_value), f"OTOC retornou NaN para dados válidos"

        # Retornos Gaussianos devem ter OTOC alto (baixa correlação temporal)
        assert otoc_value > 0.3, f"OTOC muito baixo para ruído Gaussiano: {otoc_value}"
        assert otoc_value <= 1.0, f"OTOC fora do range: {otoc_value}"
    
    def test_low_chaos_passes(self):
        """YAA-TEST: Série com tendência deve ter OTOC baixo."""
        # Gerar série com tendência + ruído pequeno
        np.random.seed(123)
        trend = np.linspace(100, 200, 200)
        noise = np.random.normal(0, 0.5, 200)
        trending_series = trend + noise

        # YAA-FIX: Usar índice específico que garante dados suficientes
        otoc_value = calculate_otoc(
            series=trending_series,
            idx=100,  # Índice no meio da série
            window=50,
            method="correlation"
        )

        # Verificar se não é NaN
        assert not np.isnan(otoc_value), f"OTOC retornou NaN para dados válidos"

        # Série com tendência deve ter OTOC baixo (alta correlação temporal)
        assert otoc_value < 0.5, f"OTOC muito alto para série com tendência: {otoc_value}"
        assert otoc_value >= 0.0, f"OTOC fora do range: {otoc_value}"
    
    def test_window_validation(self):
        """YAA-TEST: Validação de window mínimo."""
        series = np.random.normal(0, 1, 100)
        
        # Window muito pequeno deve gerar erro
        with pytest.raises(OTOCCalculationError, match="window must be >= 20"):
            calculate_otoc(series, window=10)
    
    def test_delta_validation(self):
        """YAA-TEST: Validação de delta mínimo."""
        series = np.random.normal(0, 1, 100)
        
        # Delta inválido deve gerar erro
        with pytest.raises(OTOCCalculationError, match="delta must be >= 1"):
            calculate_otoc(series, delta=0, method="financial")
    
    def test_insufficient_data_returns_nan(self):
        """YAA-TEST: Dados insuficientes devem retornar NaN."""
        short_series = np.array([1, 2, 3])
        
        otoc_value = calculate_otoc(series=short_series, window=50)
        assert np.isnan(otoc_value), "Dados insuficientes devem retornar NaN"
    
    def test_adaptive_threshold(self):
        """YAA-TEST: Threshold adaptativo baseado em volatilidade."""
        base_threshold = 0.35
        
        # Baixa volatilidade - threshold deve ficar próximo do base
        low_vol_threshold = calculate_adaptive_threshold(
            base_threshold=base_threshold,
            volatility=0.01,
            beta=1.0
        )
        assert low_vol_threshold >= base_threshold * 0.5
        assert low_vol_threshold <= base_threshold * 1.2
        
        # Alta volatilidade - threshold deve aumentar
        high_vol_threshold = calculate_adaptive_threshold(
            base_threshold=base_threshold,
            volatility=0.05,
            beta=1.0
        )
        assert high_vol_threshold > low_vol_threshold
        assert high_vol_threshold <= base_threshold * 3.0
    
    def test_diagnostics(self):
        """YAA-TEST: Função de diagnóstico."""
        series = np.random.normal(0, 1, 100)
        
        diagnostics = get_otoc_diagnostics(series, window=50)
        
        assert "otoc_correlation" in diagnostics
        assert "otoc_financial" in diagnostics
        assert "series_length" in diagnostics
        assert diagnostics["series_length"] == 100
        assert "window_coverage" in diagnostics


class TestOTOCIntegration:
    """Testes para integração OTOC no consolidador."""
    
    def setup_method(self):
        """Setup para cada teste."""
        self.config = {
            "timeframe_weights": {
                "1m": 0.3,
                "5m": 0.4,
                "15m": 0.6,
                "1h": 0.8
            },
            "otoc_config": {
                "enabled": True,
                "max_threshold": 0.35,
                "window": 50,
                "method": "correlation",
                "adaptive_threshold": {
                    "enabled": True,
                    "beta": 1.0,
                    "vol_window": 20
                }
            }
        }
        self.consolidator = MultiTimeframeSignalConsolidator(self.config)
    
    def create_test_signal(
        self, 
        timeframe: str = "1m", 
        signal: str = "buy", 
        confidence: float = 0.8,
        otoc_value: float = 0.2
    ) -> TimeframeSignal:
        """Cria sinal de teste."""
        return TimeframeSignal(
            timeframe=timeframe,
            signal=signal,
            confidence=confidence,
            signal_strength=0.7,
            hype_momentum=0.6,
            holographic_boost=1.1,
            tsvf_validation=0.5,
            timestamp=datetime.now(),
            otoc_value=otoc_value
        )
    
    def test_otoc_filter_blocks_chaos(self):
        """YAA-TEST: Filtro OTOC deve bloquear sinais caóticos."""
        # Sinal com OTOC alto (caótico)
        chaotic_signal = self.create_test_signal(
            signal="buy",
            confidence=0.8,
            otoc_value=0.8  # Acima do threshold 0.35
        )
        
        filtered_signals = self.consolidator.apply_otoc_filter([chaotic_signal])
        
        assert len(filtered_signals) == 1
        filtered_signal = filtered_signals[0]
        
        # Sinal deve ser convertido para HOLD
        assert filtered_signal.signal == "hold"
        assert filtered_signal.confidence == 0.0
        assert filtered_signal.otoc_value == 0.8  # OTOC preservado
    
    def test_otoc_filter_passes_ordered(self):
        """YAA-TEST: Filtro OTOC deve passar sinais ordenados."""
        # Sinal com OTOC baixo (ordenado)
        ordered_signal = self.create_test_signal(
            signal="buy",
            confidence=0.8,
            otoc_value=0.1  # Abaixo do threshold 0.35
        )
        
        filtered_signals = self.consolidator.apply_otoc_filter([ordered_signal])
        
        assert len(filtered_signals) == 1
        filtered_signal = filtered_signals[0]
        
        # Sinal deve permanecer inalterado
        assert filtered_signal.signal == "buy"
        assert filtered_signal.confidence == 0.8
        assert filtered_signal.otoc_value == 0.1
    
    def test_nan_otoc_handling(self):
        """YAA-TEST: NaN OTOC deve manter sinal original (warm-up period)."""
        # Sinal com OTOC NaN (período de warm-up)
        warmup_signal = self.create_test_signal(
            signal="buy",
            confidence=0.8,
            otoc_value=np.nan
        )
        
        filtered_signals = self.consolidator.apply_otoc_filter([warmup_signal])
        
        assert len(filtered_signals) == 1
        filtered_signal = filtered_signals[0]
        
        # Sinal deve permanecer inalterado
        assert filtered_signal.signal == "buy"
        assert filtered_signal.confidence == 0.8
        assert np.isnan(filtered_signal.otoc_value)
    
    def test_otoc_disabled(self):
        """YAA-TEST: OTOC desabilitado deve passar todos os sinais."""
        # Configuração com OTOC desabilitado
        config_disabled = self.config.copy()
        config_disabled["otoc_config"]["enabled"] = False
        
        consolidator = MultiTimeframeSignalConsolidator(config_disabled)
        
        # Sinal com OTOC alto
        chaotic_signal = self.create_test_signal(
            signal="buy",
            confidence=0.8,
            otoc_value=0.9  # Muito acima do threshold
        )
        
        filtered_signals = consolidator.apply_otoc_filter([chaotic_signal])
        
        assert len(filtered_signals) == 1
        filtered_signal = filtered_signals[0]
        
        # Sinal deve permanecer inalterado (filtro desabilitado)
        assert filtered_signal.signal == "buy"
        assert filtered_signal.confidence == 0.8
    
    def test_backwards_compatibility(self):
        """YAA-TEST: Compatibilidade com sinais sem campo otoc_value."""
        # Configuração sem OTOC
        config_legacy = {
            "timeframe_weights": {
                "1m": 0.3,
                "5m": 0.4,
                "15m": 0.6,
                "1h": 0.8
            }
            # Sem otoc_config
        }
        
        consolidator = MultiTimeframeSignalConsolidator(config_legacy)
        
        # Sinal legado (otoc_value usa default 0.0)
        legacy_signal = TimeframeSignal(
            timeframe="1m",
            signal="buy",
            confidence=0.8,
            signal_strength=0.7,
            hype_momentum=0.6,
            holographic_boost=1.1,
            tsvf_validation=0.5,
            timestamp=datetime.now()
            # otoc_value usa default 0.0
        )
        
        # Não deve gerar erro
        filtered_signals = consolidator.apply_otoc_filter([legacy_signal])
        assert len(filtered_signals) == 1
        assert filtered_signals[0].signal == "buy"  # OTOC 0.0 < threshold


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
