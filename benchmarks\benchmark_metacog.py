import csv
import timeit
from pathlib import Path

from qualia.metacognition.metacognition_trading import (
    DecisionContext,
    QUALIAMetacognitionTrading,
)
from qualia.memory.quantum_pattern_memory import QuantumPatternMemory
from qualia.common_types import QuantumSignaturePacket
from qualia.risk_management.risk_manager_base import QUALIARiskManagerBase


class DummyUniverse:
    def __init__(self) -> None:
        self.n_qubits = 1

    def get_current_statevector(self):
        return None

    def calculate_otoc(self, *_, **__):
        return 0.5


class DummyRiskManager(QUALIARiskManagerBase):
    """Implementacao minima para uso em benchmarks."""

    def __init__(self) -> None:
        super().__init__(initial_capital=1_000.0)

    def calculate_position_size(self, *_, **__) -> dict:
        return {"position_allowed": True, "position_size": 1.0}

    def update_capital(self, new_capital: float) -> dict:
        self.current_capital = new_capital
        return {"current_capital": new_capital}

    def can_open_new_position(self, current_positions: int) -> tuple[bool, str]:
        return True, ""

    def process_trade_result(self, trade_info: dict) -> dict:
        return {"current_capital": self.current_capital}


def _setup_metacog() -> tuple[QUALIAMetacognitionTrading, DecisionContext]:
    risk_manager = DummyRiskManager()
    qpm = QuantumPatternMemory(
        max_memory_size_per_dimension=10,
        enable_warmstart=False,
        risk_manager=risk_manager,
    )
    universe = DummyUniverse()
    metacog = QUALIAMetacognitionTrading(None, qpm, universe=universe)
    packet = QuantumSignaturePacket(vector=[0.2] * 8, metrics={})
    ctx = DecisionContext(quantum_signature_packet=packet)
    return metacog, ctx


def benchmark_cycle(cycles: int) -> float:
    metacog, ctx = _setup_metacog()
    timer = timeit.Timer(lambda: metacog.run_with_qast_feedback(ctx, None))
    times = timer.repeat(repeat=3, number=cycles)
    best = min(times)
    return (best / cycles) * 1000  # milliseconds per cycle


def main() -> None:
    max_cycles = 5000
    results_path = Path("benchmarks/results/metacog_benchmark.csv")
    results_path.parent.mkdir(parents=True, exist_ok=True)

    with results_path.open("w", newline="") as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(["cycles", "ms_per_cycle"])
        for cycles in range(1000, max_cycles + 1, 1000):
            ms_per_cycle = benchmark_cycle(cycles)
            writer.writerow([cycles, f"{ms_per_cycle:.3f}"])


if __name__ == "__main__":
    main()
