Referências Científicas para Analogia Informacional com Buracos Negros e Radiação de Hawking

Analogia entre Sistemas Quântico-Simbióticos e Fenômenos de Buraco Negro
Resumo: Diversos avanços recentes em computação quântica e sistemas análogos sustentam a plausibilidade física da metáfora de um “buraco negro informacional” aplicada a sistemas adaptativos como o QUALIA. Pesquisas demonstram que circuitos quânticos podem simular características de buracos negros – radiação de Hawking, scrambling (mistura caótica da informação), curva de Page (entropia da radiação) – e investigar o paradoxo da informação de forma controlada. A seguir, são apresentadas referências técnico-científicas e experimentais que fundamentam essa analogia, cobrindo (1) simulações de radiação de Hawking em qubits, (2) estudos de quantum scrambling e entropia de Page via circuitos aleatórios, (3) discussões sobre preservação da informação e emaranhamento em modelos discretos, e (4) evidências experimentais de circuitos que mimetizam perda de informação e emissão adaptativa (evaporação informacional).
1. Simulação da Radiação de Hawking com Qubits e Circuitos Quânticos
Experimentos recentes mostram que processadores quânticos conseguem simular a radiação de Hawking em sistemas de qubits. Shi et al. (2023) construíram um “buraco negro on-chip” usando 10 qubits supercondutores com acopladores ajustáveis, criando um análogo de horizonte de eventos em 1D
quantumzeitgeist.com
. Ao excitar um qubit no interior deste “buraco negro” simulado, observaram que há sempre uma certa probabilidade de uma quase-partícula escapar através do horizonte – uma emissão que segue a distribuição térmica prevista por Hawking
quantumzeitgeist.com
. Em outras palavras, o qubit excitado no interior tinha uma chance mensurável de radiar para fora, satisfazendo a relação teórica da radiação de Hawking
quantumzeitgeist.com
quantumzeitgeist.com
. Esse “sopro Hawking” foi detectado realizando tomografia de estado em todos os qubits fora do horizonte, confirmando o aparecimento de emaranhamento e partículas na região externa conforme esperado
pmc.ncbi.nlm.nih.gov
pmc.ncbi.nlm.nih.gov
. Vale notar que se tratou de radiação de Hawking estimulada (iniciada por uma excitação controlada), já que a radiação espontânea em análogos de buracos negros é muito fraca para ser detectada diretamente com a tecnologia atual
pmc.ncbi.nlm.nih.gov
. Ainda assim, o experimento representa um avanço pioneiro ao testar a teoria de Hawking em hardware quântico, conforme relatado pelos autores na Nature Communications em 2023
quantumzeitgeist.com
quantumzeitgeist.com
. Outro exemplo de simulação envolve abordagens digitais em computadores quânticos universais. Dhaulakhandi et al. (2024) utilizaram dispositivos IBM Quantum para simular um modelo de buraco negro evaporando como forma de avaliar algoritmos variacionais
citedrive.com
. Eles empregaram o algoritmo Variational Quantum Eigensolver (VQE) para encontrar o estado fundamental de um Hamiltoniano efetivo de buraco negro evaporante, comparando ansätze quânticos personalizados e padrão
citedrive.com
. Embora o foco desse trabalho seja o método computacional, ele evidencia que modelos de buracos negros podem servir de benchmark para testar o poder de simulação de processadores quânticos atuais. De forma geral, esses estudos confirmam que plataformas de qubits supercondutores ou circuitos em simuladores (ex: Qiskit) conseguem emular a dinâmica de horizontes e radiação. Isso abre caminho para sistemas quânticos programáveis com propriedades análogas às de buracos negros, validando a metáfora física por trás de conceitos como o QUALIA
quantumzeitgeist.com
quantumzeitgeist.com
.
2. Quantum Scrambling, Entropia de Page e Circuitos Aleatórios como Análogos de Buracos Negros
Uma característica fundamental de buracos negros é serem “scramblers” extremamente rápidos, ou seja, sistemas que misturam (dispersam) informações quânticas de forma caótica em um curto tempo. Quando um objeto cai em um buraco negro, todos os dados quânticos sobre sua composição ficam espalhados e aparentemente irreconhecíveis no emaranhado complexo do interior
news.berkeley.edu
. Esse processo de quantum scrambling está no centro do paradoxo da informação – a informação do material que atravessou o horizonte parece perdida, pois sai na radiação Hawking como ruído térmico
news.berkeley.edu
. Entretanto, teoricamente a mecânica quântica exige que a informação não seja destruída, apenas ofuscada. Modelos de circuitos quânticos aleatórios têm servido como análogos de buracos negros por exibirem esse comportamento de mistura máxima de informação (fast scrambling). Experimentalmente, scrambling quântico já foi demonstrado e verificado em computadores quânticos. Landsman et al. (Nature 567, 2019) implementaram em um sistema de 7 qubits um protocolo proposto por Yoshida e Yao para diagnosticar scrambling via correlatores fora da ordem de tempo (OTOCs)
news.berkeley.edu
news.berkeley.edu
. Nesse experimento, eles simularam um pequeno “buraco negro” de 3 qubits que interagia com outros qubits e mostraram que a informação lançada no interior pode ser recuperada através de um procedimento de teleporte quântico – análogo a “resgatar” informação de dentro de um buraco negro
news.berkeley.edu
. A capacidade de teleportar o estado de um qubit caído no buraco negro usando apenas qubits externos (que representam a radiação Hawking) é evidência de que o circuito realizou um scrambling completo seguido de liberação controlada de informação
news.berkeley.edu
. De fato, quanto mais efetivo o resgate via teleporte, mais se pode inferir que o circuito se comportou como um scrambler caótico, espalhando totalmente a informação inicial
news.berkeley.edu
. Esse resultado, com acurácia de cerca de 80%, foi reportado como a primeira verificação experimental de scrambling quântico em laboratório (usando íons aprisionados)
scholars.duke.edu
semanticscholar.org
. Ele demonstra empiricamente o conceito de que buracos negros misturam, mas não destroem informação, pois um protocolo apropriado consegue recuperá-la – em acordo com a conjectura de que buracos negros são os scramblers mais rápidos possíveis
pnas.org
link.aps.org
. Do ponto de vista computacional, circuitos quânticos aleatórios têm sido empregados como modelos simples de buracos negros. Chowdhury et al. (2025) propuseram um toy model de buraco negro composto por qubits, no qual um circuito aleatório unitário representa a dinâmica caótica interna (scrambling rápido) seguida pela emissão gradual de qubits que simulam a radiação
arxiv.org
arxiv.org
. Usando computadores quânticos da IBM, eles implementaram esse modelo de forma eficiente e mediram a entropia de entrelaçamento dos qubits de radiação ao longo do tempo
arxiv.org
. Para quantificar a perda e recuperação de informação, aplicaram dois métodos de medida de entropia (protocolo de interferência swap de muitos corpos, e medida randômica), incorporando técnicas de mitigação de erro para garantir fidelidade nos resultados
arxiv.org
arxiv.org
. Os autores conseguiram capturar a curva de Page – ou seja, observar a entropia dos “qubits-radiação Hawking” primeiro crescer e depois decrescer, conforme previsto teoricamente para um buraco negro evaporando
arxiv.org
arxiv.org
. Esse estudo indica que os computadores quânticos atuais já podem explorar a dinâmica de entropia de buracos negros em modelos simplificados, validando a utilidade de circuitos aleatórios como proxies de buracos negros
arxiv.org
arxiv.org
. Além disso, conceitos da correspondência holográfica também estão sendo testados em circuitos quânticos. Um exemplo notável foi a simulação de uma dinâmica de buraco de minhoca atravessável em 2022: Jafferis et al. implementaram um pequeno modelo SYK esparsificado em 9 qubits do processador Google Sycamore, reproduzindo propriedades-chave de um buraco de minhoca gravitacional dentro de um circuito quântico
phys.org
. Embora o experimento não crie um buraco de minhoca físico, ele explora a conexão ER = EPR (Einstein-Rosen = Entrelaçamento) ao mostrar que um processo de teleporte quântico entre subsistemas corresponde, no modelo dual, à passagem de informação através de um túnel espaço-temporal
phys.org
phys.org
. Maria Spiropulu, líder do projeto, destacou que encontraram um sistema quântico mínimo exibindo propriedades de um buraco de minhoca gravitacional, viável em hardware atual, o que representa um passo inicial em direção a testar ideias de gravidade quântica em computadores quânticos
phys.org
. Esse resultado – ainda debatido – reforça que mesmo fenômenos análogos a buracos negros (como wormholes) podem ser explorados em circuitos quânticos, solidificando a ponte conceitual entre qubits e gravidade
phys.org
phys.org
.
3. Preservação da Informação e Paradoxo da Radiação de Hawking em Modelos Discretos
O paradoxo da informação em buracos negros surge da aparente contradição entre a mecânica quântica e a relatividade geral: a teoria de Hawking prevê que a radiação emitida por um buraco negro não carrega impressões distinguíveis do material que caiu, parecendo térmica e destruindo informação específica. Contudo, pela mecânica quântica, a evolução completa (buraco negro + radiação) deve ser unitária, i.e., reversível e conservando a informação total
news.berkeley.edu
. Isso leva à pergunta: para onde vai a informação engolida pelo buraco negro?. Alguns físicos propuseram que a informação seria perdida para sempre (violando unitariedade), enquanto outros argumentam que a informação retorna codificada na radiação emitida, mas somente após um longo tempo – aproximadamente quando o buraco negro tiver evaporado metade de sua massa
news.berkeley.edu
. Esse ponto temporal é conhecido como o tempo de Page, e marca o pico da entropia da radiação. Após esse ponto, a radiação carrega cada vez mais informação do buraco negro, de modo que a entropia (incerteza) do que saiu começa a cair, desenhando a “curva de Page”
news.berkeley.edu
arxiv.org
. Dois conceitos relacionados são a entropia de Bekenstein-Hawking (que quantifica a informação máxima que poderia estar dentro do buraco negro, proporcional à área do horizonte) e a entropia de Page (que é a entropia da radiação Hawking considerando a unitariedade). Page mostrou em 1993, por argumentos estatísticos, que se um buraco negro evapora de forma unitária, a entropia von Neumann da radiação inicialmente cresce (como radiação praticamente térmica) até atingir aproximadamente o valor da entropia do buraco negro inicial, então decai de volta a zero conforme o buraco negro desaparece
arxiv.org
. Em termos informacionais, isso significa que nenhuma informação é permanentemente perdida: ela fica inicialmente oculta no emaranhamento entre o buraco negro e a radiação, mas acaba toda devolvida na fase final da evaporação
arxiv.org
. O estado final da radiação seria puro (entropia zero) contendo toda a informação que antes estava atrás do horizonte
arxiv.org
. Modelos quânticos discretos têm sido usados para explorar e ilustrar essa preservação da informação. Por exemplo, no modelo de transporte de qubits de Chowdhury et al. (2025), assume-se que o buraco negro consiste em $N$ qubits inicialmente emaranhados (estado próximo do aleatório máximo) e que a radiação consiste em qubits emitidos sequencialmente
arxiv.org
. A cada emissão de um “qubit Hawking”, considera-se que um par emaranhado partícula–antipartícula foi criado no horizonte (como no cenário de Hawking) – o qubit que sai compõe a radiação e o qubit parceiro fica dentro do buraco negro e eventualmente se torna parte do estado interno
arxiv.org
arxiv.org
. Durante metade inicial da evaporação, cada qubit de radiação emitido está quase maximamente emaranhado com o buraco negro restante, aumentando a entropia mútua. Mas depois do tempo de Page (quando restam ~$N/2$ qubits no buraco negro), as emissões posteriores reduzem o emaranhamento restante, pois começam a levar embora informação específica do interior
arxiv.org
. No final, com $N$ qubits emitidos, o buraco negro efetivamente desaparece e a radiação fica em estado puro, contendo todos os $N$ qubits de informação inicial
arxiv.org
. Essa dinâmica da entropia da radiação (a curva de Page) é um forte indicador de resolução do paradoxo da informação via emaranhamento quântico
arxiv.org
. Notavelmente, experimentos em plataformas quânticas já capturaram aspectos dessa curva de Page, corroborando a ideia de preservação da informação. Como citado, o trabalho de Chowdhury et al. mediu entropias de Rényi de radiação simulada em um IBM Q, observando tendências consistentes com a curva de Page esperada
arxiv.org
arxiv.org
. Outro estudo (Brown et al., 2019) inferiu indiretamente a transferência de informação em um circuito embaralhador ao conseguir decodificar um qubit injetado após metade do sistema ter evoluído – análogo a esperar até meia evaporação do buraco negro para tentar recuperar a informação
news.berkeley.edu
. Esses resultados dão sustentação experimental à noção de que buracos negros não violam a conservação de informação, mas sim a devolvem de forma altamente cifrada na radiação. Em suma, “informação não some, apenas fica temporariamente inacessível”, retornando ao universo exterior codificada nos correlações quânticas de partículas Hawking
news.berkeley.edu
arxiv.org
.
4. Circuitos Quânticos Mimetizando Perda de Informação e Emissão Adaptativa (Evaporação Informacional)
Para que a metáfora de um sistema adaptativo funcionar como um “buraco negro informacional” seja plausível, é preciso demonstrar mecanismos de circuito que imitem a perda aparente de informação e sua liberação gradual – similar à evaporação de massa/informação de um buraco negro. Os trabalhos acima indicam precisamente isso: é possível programar circuitos quânticos que absorvem, escondem e depois liberam informação de maneira controlada. No modelo de transporte de qubits
arxiv.org
, cada passo de evaporação pode ser implementado como uma operação de SWAP ou movimentação de um qubit do registro do “buraco negro” para o da “radiação”
arxiv.org
. Internamente, aplica-se um circuito aleatório (conjunto de portas quânticas) que scramble a informação nos qubits internos, de modo que quando um qubit sai, ele carrega apenas informação térmica, não revelando imediatamente detalhes do interior. No entanto, conforme mais qubits são emitidos, os padrões de correlação carregados pela radiação contêm cada vez mais informação do interior – ou seja, a emissão é adaptativa: vai progressivamente revelando o conteúdo informacional oculto no “massivo” estado interno do sistema. Essa adaptação ocorre naturalmente no quadro teórico – à medida que o buraco negro perde massa informacional (qubits internos), a radiação sai menos misturada e mais correlacionada com estados específicos
arxiv.org
. Experimentalmente, o mimetismo da perda de informação é observado quando se considera apenas uma parte do sistema. Por exemplo, no experimento de scrambling de 7 qubits, se analisarmos somente os 3 qubits internos (buraco negro simulado), seu estado reduzido parece praticamente maximamente misturado após o scrambling – indicando que qualquer informação inicial foi perdida localmente, espalhada nas correlações com os demais qubits
news.berkeley.edu
. Essa “perda” é reversível apenas ao incluir o resto do sistema (os qubits da radiação) e aplicar o protocolo de decodificação adequado
news.berkeley.edu
. Assim, do ponto de vista subsistêmico, o circuito se comporta como se destruísse informação, embora no nível global ela esteja conservada – exatamente o que se espera de um buraco negro real em relação a um observador externo. Além disso, a emissão de quanta de informação de forma gradual já foi implementada em etapas em simuladores. Shi et al. (2023) verificaram, em seu análogo de buraco negro supercondutor, que a probabilidade de emissão de uma partícula através do horizonte seguia o comportamento previsto por Hawking e dependia da configuração inicial (eles induziram emissões controladas)
quantumzeitgeist.com
. Enquanto esse experimento mediu essencialmente uma emissão única estimulada, trabalhos teóricos sugerem estender o circuito para múltiplas emissões em sequência – cada qual removendo um qubit de dentro e acrescentando à radiação – para simular a evaporação completa. De fato, Chowdhury et al. em seu modelo discreto reduzem iterativamente o número de qubits interiores para representar a evaporação gradual
arxiv.org
. Após cada emissão, o estado residual do “buraco negro” permanece emaranhado com a radiação já emitida, preservando a unitariedade
arxiv.org
. Esse tipo de circuito adaptativo, onde parâmetros ou configurações mudam conforme qubits são transferidos, é análogo a um buraco negro perdendo massa (qubits) e emitindo radiação de forma progressiva. Em resumo, há suporte experimental e teórico para a ideia de que um sistema quântico simbiótico pode agir como um buraco negro informacional: ele absorve entradas (informação) e as scramble internamente (tornando-as inacessíveis temporariamente), mas ao evoluir/”evaporar” vai liberando saídas informacionais correlacionadas que gradualmente refletem o que foi absorvido. A metáfora de “evaporação de massa informacional” no QUALIA encontra eco nesses estudos – a massa de informação interna diminui conforme “qualia” (blocos de informação fundamental, no jargão do sistema) são processados e radiados para fora em forma de conhecimento ou adaptações. Os experimentos citados, envolvendo empresas e instituições de ponta (IBM Q, Google Quantum AI, IonQ/JQI, CAS/RIKEN, etc.), legitimam a aplicação de conceitos de buracos negros a sistemas de informação. Em essência, tudo aponta que a física e a computação quântica atuais suportam a plausibilidade da analogia: um sistema adaptativo quântico pode sim imitar um buraco negro, emaranhando e liberando informação tal como um buraco negro verdadeiro emite radiação de Hawking com o conteúdo informacional do que engoliu
news.berkeley.edu
arxiv.org
. As referências destacadas fornecem a base científico-experimental para essa fascinante ponte entre gravidade, informação e computação. Referências Selecionadas:
Yun-Hao Shi et al. (2023). “Quantum simulation of Hawking radiation and curved spacetime with a superconducting on-chip black hole.” Nature Communications 14, 3263 (2023). Demonstra a radiação de Hawking análoga em uma cadeia de 10 qubits supercondutores
quantumzeitgeist.com
pmc.ncbi.nlm.nih.gov
.
Talal A. Chowdhury et al. (2025). “Capturing the Page Curve and Entanglement Dynamics of Black Holes in Quantum Computers.” Pré-print arXiv:2412.15180
arxiv.org
arxiv.org
. Simula evaporação de buraco negro com circuitos aleatórios em computadores IBM, medindo entropia da radiação (curva de Page).
Kevin Landsman et al. (2019). “Verified Quantum Information Scrambling.” Nature 567, 61–65 (2019)
news.berkeley.edu
news.berkeley.edu
. Verifica experimentalmente o scrambling quântico e recuperação de informação em um sistema de 7 qubits (protocolos de OTOC/teleporte).
Ritu Dhaulakhandi et al. (2024). “Studying evaporating black hole using quantum computation algorithms on IBM quantum processor.” AIP Advances 14, 125121 (2024)
citedrive.com
. Usa um modelo de buraco negro evaporando como caso de teste para algoritmos VQE em hardware IBM, indicando viabilidade de simular buracos negros em circuitos variacionais.
Daniel Jafferis et al. (2022). “Traversable wormhole dynamics on a quantum processor.” Nature 612, 51–55 (2022). Implementa um modelo dual de buraco de minhoca (SYK) em 9 qubits do Google Sycamore, observando teleporte análogo a um ER = EPR (entrelaçamento-gravidade)
phys.org
phys.org
.
Norman Y. Yao et al. (2020). “Scrambling in quantum networks and partially scrambling channels.” (exemplo de discussão teórica sobre scrambling e recuperação de informação).
(Outras referências nos trechos citados): Berkeley News sobre experimento de scrambling
news.berkeley.edu
news.berkeley.edu
; Quantum Zeitgeist sobre simulação de buraco negro
quantumzeitgeist.com
quantumzeitgeist.com
; Phys.org/Caltech News sobre wormhole quântico
phys.org
phys.org
; Seção teórica de Chowdhury et al. descrevendo dinâmica de entropia
arxiv.org
arxiv.org
, etc. Estas fontes complementam a fundamentação da analogia QUALIA–buraco negro.