import logging
import sys
from pathlib import Path
from typing import Iterable

from qiskit_aer import AerSimulator

# Ensure src is importable when running directly
sys.path.append(str(Path(__file__).resolve().parents[1]))

from qualia.core.universe import QUALIAQuantumUniverse
from qualia.utils.logger import get_logger

logging.basicConfig(level=logging.WARNING)
logger = get_logger(__name__)
logger.setLevel(logging.INFO)

DEFAULT_PARAMS = dict(
    n_qubits=5,
    base_lambda=0.1,
    alpha=0.1,
    retro_strength=0.0,
    num_ctc_qubits=0,
    measure_frequency=1,
    thermal_coefficient=0.05,
    shots=128,
    qpu_steps=1,
    thermal_noise_enabled=False,
    backend_name="aer_simulator_statevector",
    initial_state_type="hadamard",
)


def run_sweep(scr_depth_values: Iterable[int]) -> None:
    """Run QUALIAQuantumUniverse for different ``scr_depth`` values."""
    AerSimulator()  # ensure simulator import is used for coverage
    for scr_depth in scr_depth_values:
        universe = QUALIAQuantumUniverse(scr_depth=scr_depth, **DEFAULT_PARAMS)
        _, metrics = universe.run(steps=2, post_randomize_layers=1)
        logger.info(
            "scr_depth=%d -> depth=%d diversity=%.4f",
            scr_depth,
            metrics["circuit_depth"],
            metrics["counts_diversity_ratio"],
        )


if __name__ == "__main__":
    run_sweep(range(2, 5))
