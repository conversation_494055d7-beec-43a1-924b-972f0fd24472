"""
QUALIA Production Monitoring System
24/7 monitoring with real-time alerts, performance tracking, and anomaly detection
"""

import asyncio
import smtplib
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
import logging
import threading
from collections import deque, defaultdict
import statistics
import requests
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class Alert:
    """Alert information"""
    id: str
    timestamp: datetime
    level: str  # CRITICAL, WARNING, INFO
    component: str
    message: str
    details: Dict[str, Any]
    resolved: bool = False
    resolved_at: Optional[datetime] = None

@dataclass
class MetricThreshold:
    """Metric threshold configuration"""
    name: str
    warning_threshold: float
    critical_threshold: float
    comparison: str  # 'gt', 'lt', 'eq'
    window_minutes: int = 5
    consecutive_violations: int = 3

@dataclass
class SystemHealth:
    """System health status"""
    timestamp: datetime
    overall_status: str  # HEALTHY, WARNING, CRITICAL
    components: Dict[str, str]
    metrics: Dict[str, float]
    active_alerts: List[Alert]
    uptime_seconds: float

class ProductionMonitor:
    """
    Production monitoring system with real-time alerting
    Features:
    - Performance metrics tracking
    - Threshold-based alerting
    - Email/SMS notifications
    - System health dashboard
    - Anomaly detection
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.monitoring_config = config.get('monitoring', {})
        self.alert_config = self.monitoring_config.get('alerts', {})
        
        # Monitoring state
        self.is_running = False
        self.start_time = time.time()
        self.metrics_history = defaultdict(lambda: deque(maxlen=1000))
        self.active_alerts = {}
        self.alert_history = deque(maxlen=10000)
        
        # Thresholds
        self.thresholds = self._setup_thresholds()
        
        # Alert channels
        self.alert_channels = self._setup_alert_channels()
        
        # Monitoring tasks
        self.monitoring_tasks = []
        
        # Health status
        self.last_health_check = None
        
    def _setup_thresholds(self) -> List[MetricThreshold]:
        """Setup monitoring thresholds from configuration"""
        thresholds_config = self.monitoring_config.get('thresholds', {})
        
        return [
            MetricThreshold(
                name='sharpe_ratio',
                warning_threshold=thresholds_config.get('min_sharpe_ratio', 2.0),
                critical_threshold=thresholds_config.get('min_sharpe_ratio', 2.0) * 0.5,
                comparison='lt',
                window_minutes=60,
                consecutive_violations=3
            ),
            MetricThreshold(
                name='drawdown',
                warning_threshold=thresholds_config.get('max_drawdown_alert', 0.08),
                critical_threshold=0.12,
                comparison='gt',
                window_minutes=5,
                consecutive_violations=1
            ),
            MetricThreshold(
                name='win_rate',
                warning_threshold=thresholds_config.get('min_win_rate', 0.55),
                critical_threshold=0.45,
                comparison='lt',
                window_minutes=120,
                consecutive_violations=5
            ),
            MetricThreshold(
                name='consecutive_losses',
                warning_threshold=thresholds_config.get('max_consecutive_losses', 5),
                critical_threshold=8,
                comparison='gt',
                window_minutes=1,
                consecutive_violations=1
            ),
            MetricThreshold(
                name='latency_ms',
                warning_threshold=100,
                critical_threshold=500,
                comparison='gt',
                window_minutes=5,
                consecutive_violations=3
            ),
            MetricThreshold(
                name='error_rate',
                warning_threshold=0.05,
                critical_threshold=0.15,
                comparison='gt',
                window_minutes=10,
                consecutive_violations=2
            )
        ]
    
    def _setup_alert_channels(self) -> Dict[str, Any]:
        """Setup alert notification channels"""
        channels = {}
        
        # Email alerts
        email_config = self.alert_config.get('email', {})
        if email_config.get('enabled', False):
            channels['email'] = {
                'enabled': True,
                'smtp_server': email_config.get('smtp_server'),
                'smtp_port': email_config.get('smtp_port', 587),
                'username': email_config.get('username'),
                'password': email_config.get('password'),
                'recipients': email_config.get('recipients', [])
            }
        
        # Slack alerts
        slack_config = self.alert_config.get('slack', {})
        if slack_config.get('enabled', False):
            channels['slack'] = {
                'enabled': True,
                'webhook_url': slack_config.get('webhook_url')
            }
        
        # SMS alerts (placeholder)
        sms_config = self.alert_config.get('sms', {})
        if sms_config.get('enabled', False):
            channels['sms'] = {
                'enabled': True,
                'provider': sms_config.get('provider'),
                'api_key': sms_config.get('api_key'),
                'recipients': sms_config.get('recipients', [])
            }
        
        return channels
    
    async def start_monitoring(self):
        """Start the monitoring system"""
        if self.is_running:
            logger.warning("Monitoring system is already running")
            return
        
        self.is_running = True
        self.start_time = time.time()
        
        logger.info("Starting production monitoring system")
        
        # Start monitoring tasks
        self.monitoring_tasks = [
            asyncio.create_task(self._performance_monitor()),
            asyncio.create_task(self._health_checker()),
            asyncio.create_task(self._alert_processor()),
            asyncio.create_task(self._anomaly_detector())
        ]
        
        # Wait for all tasks
        try:
            await asyncio.gather(*self.monitoring_tasks)
        except asyncio.CancelledError:
            logger.info("Monitoring tasks cancelled")
        except Exception as e:
            logger.error(f"Monitoring system error: {e}")
            await self.send_alert("CRITICAL", "monitoring", 
                                f"Monitoring system failure: {e}", 
                                {"error": str(e)})
    
    async def stop_monitoring(self):
        """Stop the monitoring system"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Cancel monitoring tasks
        for task in self.monitoring_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)
        
        logger.info("Production monitoring system stopped")
    
    async def _performance_monitor(self):
        """Monitor system performance metrics"""
        while self.is_running:
            try:
                # Collect performance metrics
                metrics = await self._collect_performance_metrics()
                
                # Store metrics
                timestamp = datetime.utcnow()
                for metric_name, value in metrics.items():
                    self.metrics_history[metric_name].append((timestamp, value))
                
                # Check thresholds
                await self._check_thresholds(metrics)
                
                # Wait for next collection
                await asyncio.sleep(self.monitoring_config.get('frequency', 60))
                
            except Exception as e:
                logger.error(f"Performance monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def _collect_performance_metrics(self) -> Dict[str, float]:
        """Collect current performance metrics"""
        metrics = {}
        
        try:
            # System metrics
            metrics['uptime_seconds'] = time.time() - self.start_time
            metrics['timestamp'] = time.time()
            
            # Trading metrics (placeholder - would integrate with actual trading system)
            metrics['sharpe_ratio'] = 5.34  # From last known good value
            metrics['drawdown'] = 0.05      # 5% current drawdown
            metrics['win_rate'] = 0.68      # 68% win rate
            metrics['consecutive_losses'] = 0
            metrics['total_trades'] = 150
            metrics['profitable_trades'] = 102
            
            # System performance metrics
            metrics['latency_ms'] = 45.0
            metrics['error_rate'] = 0.01
            metrics['memory_usage_mb'] = 1024.0
            metrics['cpu_usage_percent'] = 25.0
            
            # Market data metrics
            metrics['data_feed_latency'] = 23.0
            metrics['data_quality_score'] = 0.97
            metrics['websocket_connections'] = 1
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to collect metrics: {e}")
            return {}
    
    async def _check_thresholds(self, current_metrics: Dict[str, float]):
        """Check metrics against configured thresholds"""
        for threshold in self.thresholds:
            if threshold.name not in current_metrics:
                continue
            
            current_value = current_metrics[threshold.name]
            
            # Check if threshold is violated
            violation_level = self._check_threshold_violation(threshold, current_value)
            
            if violation_level:
                await self._handle_threshold_violation(threshold, current_value, violation_level)
    
    def _check_threshold_violation(self, threshold: MetricThreshold, value: float) -> Optional[str]:
        """Check if a threshold is violated"""
        if threshold.comparison == 'gt':
            if value > threshold.critical_threshold:
                return 'CRITICAL'
            elif value > threshold.warning_threshold:
                return 'WARNING'
        elif threshold.comparison == 'lt':
            if value < threshold.critical_threshold:
                return 'CRITICAL'
            elif value < threshold.warning_threshold:
                return 'WARNING'
        elif threshold.comparison == 'eq':
            if abs(value - threshold.critical_threshold) < 0.001:
                return 'CRITICAL'
            elif abs(value - threshold.warning_threshold) < 0.001:
                return 'WARNING'
        
        return None
    
    async def _handle_threshold_violation(self, threshold: MetricThreshold, 
                                        value: float, level: str):
        """Handle threshold violation"""
        alert_id = f"{threshold.name}_{level}_{int(time.time())}"
        
        # Check if we already have an active alert for this threshold
        existing_alert_key = f"{threshold.name}_{level}"
        if existing_alert_key in self.active_alerts:
            return  # Don't spam alerts
        
        # Create alert
        alert = Alert(
            id=alert_id,
            timestamp=datetime.utcnow(),
            level=level,
            component=threshold.name,
            message=f"{threshold.name} threshold violated: {value}",
            details={
                'metric': threshold.name,
                'current_value': value,
                'threshold': threshold.critical_threshold if level == 'CRITICAL' else threshold.warning_threshold,
                'comparison': threshold.comparison
            }
        )
        
        # Store alert
        self.active_alerts[existing_alert_key] = alert
        self.alert_history.append(alert)
        
        # Send alert
        await self.send_alert(level, threshold.name, alert.message, alert.details)
    
    async def _health_checker(self):
        """Perform periodic health checks"""
        while self.is_running:
            try:
                health = await self._check_system_health()
                self.last_health_check = health
                
                # Log health status
                logger.info(f"System health check: {health.overall_status}")
                
                # Send critical health alerts
                if health.overall_status == 'CRITICAL':
                    await self.send_alert('CRITICAL', 'system_health',
                                        'System health is CRITICAL',
                                        {'health_status': asdict(health)})
                
                await asyncio.sleep(300)  # 5 minutes
                
            except Exception as e:
                logger.error(f"Health check error: {e}")
                await asyncio.sleep(300)
    
    async def _check_system_health(self) -> SystemHealth:
        """Check overall system health"""
        components = {}
        metrics = {}
        
        try:
            # Check trading system
            components['trading_system'] = 'HEALTHY'  # Would check actual system
            
            # Check data feeds
            components['data_feeds'] = 'HEALTHY'
            
            # Check optimization system
            components['optimization'] = 'HEALTHY'
            
            # Check monitoring system
            components['monitoring'] = 'HEALTHY'
            
            # Collect key metrics
            current_metrics = await self._collect_performance_metrics()
            metrics.update(current_metrics)
            
            # Determine overall status
            if any(status == 'CRITICAL' for status in components.values()):
                overall_status = 'CRITICAL'
            elif any(status == 'WARNING' for status in components.values()):
                overall_status = 'WARNING'
            else:
                overall_status = 'HEALTHY'
            
            return SystemHealth(
                timestamp=datetime.utcnow(),
                overall_status=overall_status,
                components=components,
                metrics=metrics,
                active_alerts=list(self.active_alerts.values()),
                uptime_seconds=time.time() - self.start_time
            )
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return SystemHealth(
                timestamp=datetime.utcnow(),
                overall_status='CRITICAL',
                components={'system': 'ERROR'},
                metrics={},
                active_alerts=[],
                uptime_seconds=time.time() - self.start_time
            )
    
    async def _alert_processor(self):
        """Process and manage alerts"""
        while self.is_running:
            try:
                # Clean up resolved alerts
                current_time = datetime.utcnow()
                expired_alerts = []
                
                for key, alert in self.active_alerts.items():
                    # Auto-resolve old alerts (24 hours)
                    if current_time - alert.timestamp > timedelta(hours=24):
                        expired_alerts.append(key)
                
                for key in expired_alerts:
                    del self.active_alerts[key]
                
                await asyncio.sleep(300)  # 5 minutes
                
            except Exception as e:
                logger.error(f"Alert processor error: {e}")
                await asyncio.sleep(300)
    
    async def _anomaly_detector(self):
        """Detect anomalies in metrics"""
        while self.is_running:
            try:
                # Simple anomaly detection based on statistical deviation
                for metric_name, history in self.metrics_history.items():
                    if len(history) < 10:
                        continue
                    
                    # Get recent values
                    recent_values = [value for _, value in list(history)[-10:]]
                    
                    if len(recent_values) < 5:
                        continue
                    
                    # Calculate statistics
                    mean_val = statistics.mean(recent_values)
                    stdev_val = statistics.stdev(recent_values) if len(recent_values) > 1 else 0
                    
                    # Check for anomalies (3 standard deviations)
                    latest_value = recent_values[-1]
                    if stdev_val > 0 and abs(latest_value - mean_val) > 3 * stdev_val:
                        await self.send_alert('WARNING', 'anomaly_detection',
                                            f'Anomaly detected in {metric_name}',
                                            {
                                                'metric': metric_name,
                                                'current_value': latest_value,
                                                'mean': mean_val,
                                                'stdev': stdev_val,
                                                'deviation': abs(latest_value - mean_val) / stdev_val
                                            })
                
                await asyncio.sleep(600)  # 10 minutes
                
            except Exception as e:
                logger.error(f"Anomaly detection error: {e}")
                await asyncio.sleep(600)
    
    async def send_alert(self, level: str, component: str, message: str, 
                        details: Dict[str, Any]):
        """Send alert through configured channels"""
        try:
            # Email alerts
            if self.alert_channels.get('email', {}).get('enabled'):
                await self._send_email_alert(level, component, message, details)
            
            # Slack alerts
            if self.alert_channels.get('slack', {}).get('enabled'):
                await self._send_slack_alert(level, component, message, details)
            
            # SMS alerts (for critical only)
            if level == 'CRITICAL' and self.alert_channels.get('sms', {}).get('enabled'):
                await self._send_sms_alert(level, component, message, details)
            
            logger.info(f"Alert sent: {level} - {component} - {message}")
            
        except Exception as e:
            logger.error(f"Failed to send alert: {e}")
    
    async def _send_email_alert(self, level: str, component: str, 
                               message: str, details: Dict[str, Any]):
        """Send email alert"""
        try:
            email_config = self.alert_channels['email']
            
            # Create message
            msg = MimeMultipart()
            msg['From'] = email_config['username']
            msg['To'] = ', '.join(email_config['recipients'])
            msg['Subject'] = f"QUALIA {level} Alert: {component}"
            
            # Email body
            body = f"""
QUALIA Production Alert

Level: {level}
Component: {component}
Message: {message}
Timestamp: {datetime.utcnow().isoformat()}

Details:
{json.dumps(details, indent=2, default=str)}

System Status: {self.last_health_check.overall_status if self.last_health_check else 'Unknown'}
Uptime: {time.time() - self.start_time:.0f} seconds

This is an automated alert from the QUALIA production monitoring system.
"""
            
            msg.attach(MimeText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            server.starttls()
            server.login(email_config['username'], email_config['password'])
            server.send_message(msg)
            server.quit()
            
        except Exception as e:
            logger.error(f"Failed to send email alert: {e}")
    
    async def _send_slack_alert(self, level: str, component: str, 
                               message: str, details: Dict[str, Any]):
        """Send Slack alert"""
        try:
            slack_config = self.alert_channels['slack']
            
            # Create Slack message
            color = {'CRITICAL': 'danger', 'WARNING': 'warning', 'INFO': 'good'}.get(level, 'good')
            
            payload = {
                'text': f'QUALIA {level} Alert',
                'attachments': [{
                    'color': color,
                    'fields': [
                        {'title': 'Component', 'value': component, 'short': True},
                        {'title': 'Level', 'value': level, 'short': True},
                        {'title': 'Message', 'value': message, 'short': False},
                        {'title': 'Timestamp', 'value': datetime.utcnow().isoformat(), 'short': True}
                    ]
                }]
            }
            
            # Send to Slack
            response = requests.post(slack_config['webhook_url'], json=payload)
            response.raise_for_status()
            
        except Exception as e:
            logger.error(f"Failed to send Slack alert: {e}")
    
    async def _send_sms_alert(self, level: str, component: str, 
                             message: str, details: Dict[str, Any]):
        """Send SMS alert (placeholder)"""
        # This would integrate with SMS provider like Twilio
        logger.info(f"SMS alert would be sent: {level} - {component} - {message}")
    
    def get_system_health(self) -> Optional[SystemHealth]:
        """Get current system health"""
        return self.last_health_check
    
    def get_active_alerts(self) -> List[Alert]:
        """Get active alerts"""
        return list(self.active_alerts.values())
    
    def get_metrics_history(self, metric_name: str, hours: int = 24) -> List[tuple]:
        """Get metrics history for specified time period"""
        if metric_name not in self.metrics_history:
            return []
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        return [(ts, value) for ts, value in self.metrics_history[metric_name] 
                if ts >= cutoff_time]

# Global production monitor instance
_production_monitor = None

async def initialize_production_monitoring(config: Dict[str, Any]) -> ProductionMonitor:
    """Initialize global production monitor"""
    global _production_monitor
    _production_monitor = ProductionMonitor(config)
    await _production_monitor.start_monitoring()
    return _production_monitor

def get_production_monitor() -> Optional[ProductionMonitor]:
    """Get global production monitor instance"""
    return _production_monitor
