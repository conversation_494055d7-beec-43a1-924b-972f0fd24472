#!/usr/bin/env python3
"""
Exemplo Prático: Sistema Integrado de Trading Holográfico

Este exemplo demonstra como usar o sistema completo de trading holográfico
integrando todos os componentes QUALIA para execução real de trades.

Componentes Demonstrados:
- Enhanced Data Collector (OHLCV + Quantum Encoders)
- Holographic Universe (Simulação de campo holográfico)
- HolographicTradingAdapter (Conversão de sinais para trades)
- QUALIARealTimeTrader (Execução real via KuCoin)
"""

import os
import sys
import asyncio
import yaml
from pathlib import Path

# Adiciona o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from qualia.consciousness.enhanced_data_collector import EnhancedDataCollector
from qualia.consciousness.holographic_universe import HolographicMarketUniverse
from qualia.consciousness.holographic_trading_adapter import (
    HolographicTradingAdapter,
    HolographicSignal,
    create_holographic_trading_adapter,
)
from qualia.qualia_trading_system import QUALIARealTimeTrader
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class HolographicTradingExample:
    """Exemplo de uso do sistema integrado de trading holográfico."""

    def __init__(self, config_path: str = None):
        # Carrega configuração
        if config_path is None:
            config_path = (
                Path(__file__).parent.parent
                / "config"
                / "integrated_holographic_trading_config.yaml"
            )

        with open(config_path, "r") as f:
            self.config = yaml.safe_load(f)

        # Componentes do sistema
        self.data_collector = None
        self.holographic_universe = None
        self.trader = None
        self.trading_adapter = None

    async def example_1_basic_setup(self):
        """Exemplo 1: Setup básico do sistema."""

        print("\n" + "=" * 60)
        print("EXEMPLO 1: Setup Básico do Sistema Integrado")
        print("=" * 60)

        try:
            # 1. Enhanced Data Collector
            print("📊 Inicializando Enhanced Data Collector...")
            self.data_collector = EnhancedDataCollector()
            await self.data_collector.__aenter__()
            print("✅ Enhanced Data Collector inicializado")

            # 2. Holographic Universe
            print("🌌 Inicializando Holographic Universe...")
            universe_config = self.config["holographic_universe"]
            self.holographic_universe = HolographicMarketUniverse(
                field_size=tuple(universe_config["field_size"]),
                diffusion_rate=universe_config["diffusion_rate"],
                feedback_strength=universe_config["feedback_strength"],
            )
            print("✅ Holographic Universe inicializado")

            # 3. QUALIARealTimeTrader
            print("🤖 Inicializando QUALIARealTimeTrader...")
            trader_config = self.config["trader"]

            self.trader = QUALIARealTimeTrader(
                symbols=trader_config["symbols"][:2],  # Apenas 2 símbolos para exemplo
                timeframes=trader_config["timeframes"],
                capital=1000.0,  # Capital menor para exemplo
                risk_profile=trader_config["risk_profile"],
                mode="paper_trading",  # Sempre paper trading para exemplo
                data_source=trader_config["data_source"],
                risk_per_trade_pct=trader_config["risk_per_trade_pct"],
            )

            await self.trader.initialize()
            print("✅ QUALIARealTimeTrader inicializado")

            # 4. Holographic Trading Adapter
            print("🌀 Inicializando Holographic Trading Adapter...")
            adapter_config = self.config["adapter"]

            self.trading_adapter = create_holographic_trading_adapter(
                trader=self.trader, config=adapter_config
            )
            print("✅ Holographic Trading Adapter inicializado")

            print("\n🎉 Sistema básico configurado com sucesso!")

        except Exception as e:
            print(f"❌ Erro no setup básico: {e}")
            raise

    async def example_2_data_collection_demo(self):
        """Exemplo 2: Demonstração da coleta de dados enhanced."""

        print("\n" + "=" * 60)
        print("EXEMPLO 2: Coleta de Dados Enhanced")
        print("=" * 60)

        try:
            # Coleta dados de mercado
            print("📊 Coletando dados de mercado...")
            market_data = await self.data_collector.collect_market_data()

            print(f"📈 Dados coletados para {len(market_data)} símbolos:")
            for symbol, data in market_data.items():
                if data:
                    print(
                        f"   {symbol}: preço={data.get('close', 'N/A'):.4f} "
                        f"RSI={data.get('rsi', 'N/A'):.1f} "
                        f"volatilidade={data.get('volatility', 'N/A'):.2f}%"
                    )

            # Coleta notícias
            print("\n📰 Coletando eventos de notícias...")
            news_events = await self.data_collector.collect_news_events()
            print(f"📰 {len(news_events)} eventos de notícias coletados")

            for event in news_events[:3]:  # Mostra apenas os primeiros 3
                print(f"   - {event.get('title', 'N/A')[:50]}...")
                print(f"     Sentiment: {event.get('sentiment_score', 'N/A'):.2f}")

            # Fear & Greed Index
            print("\n😨 Coletando Fear & Greed Index...")
            sentiment_data = await self.data_collector.collect_fear_greed_index()
            if sentiment_data:
                print(
                    f"😨 Fear & Greed: {sentiment_data.get('value', 'N/A')} "
                    f"({sentiment_data.get('value_classification', 'N/A')})"
                )

            return market_data, news_events, sentiment_data

        except Exception as e:
            print(f"❌ Erro na coleta de dados: {e}")
            raise

    async def example_3_holographic_simulation(self, market_data, news_events):
        """Exemplo 3: Simulação holográfica e detecção de padrões."""

        print("\n" + "=" * 60)
        print("EXEMPLO 3: Simulação Holográfica")
        print("=" * 60)

        try:
            # Injeta eventos de mercado
            print("🌌 Injetando eventos de mercado no universo holográfico...")
            events_injected = 0

            for symbol, data in market_data.items():
                if data and "close" in data:
                    volatility = data.get("volatility", 1.0)
                    price_change = data.get("price_change_pct", 0.0)
                    amplitude = min(abs(price_change) * 3.0 + volatility * 0.5, 8.0)

                    self.holographic_universe.inject_market_event(
                        symbol=symbol,
                        event_type="price_movement",
                        amplitude=amplitude,
                        metadata=data,
                    )
                    events_injected += 1

            print(f"✅ {events_injected} eventos de mercado injetados")

            # Injeta eventos de notícias
            print("📰 Injetando eventos de notícias...")
            news_injected = 0

            for event in news_events:
                sentiment_score = event.get("sentiment_score", 0.0)
                amplitude = min(abs(sentiment_score) * 4.0 + 1.0, 6.0)

                if amplitude > 0.5:
                    self.holographic_universe.inject_market_event(
                        symbol="NEWS",
                        event_type="news",
                        amplitude=amplitude,
                        metadata=event,
                    )
                    news_injected += 1

            print(f"✅ {news_injected} eventos de notícias injetados")

            # Evolui o universo
            print("🔄 Evoluindo universo holográfico...")
            for step in range(5):
                self.holographic_universe.evolve()
                print(f"   Passo {step + 1}/5 concluído")

            # Detecta padrões
            print("🔍 Detectando padrões...")
            patterns = self.holographic_universe.detect_patterns()

            print(f"🎯 {len(patterns)} padrões detectados:")
            for i, pattern in enumerate(patterns[:3]):  # Mostra apenas os primeiros 3
                print(
                    f"   Padrão {i+1}: strength={pattern.get('strength', 0):.2f} "
                    f"confidence={pattern.get('confidence', 0):.2f} "
                    f"symbol={pattern.get('symbol', 'N/A')}"
                )

            return patterns

        except Exception as e:
            print(f"❌ Erro na simulação holográfica: {e}")
            raise

    async def example_4_signal_generation_and_trading(self, patterns, market_data):
        """Exemplo 4: Geração de sinais e execução de trades."""

        print("\n" + "=" * 60)
        print("EXEMPLO 4: Geração de Sinais e Trading")
        print("=" * 60)

        try:
            # Converte padrões em sinais holográficos
            print("⚡ Convertendo padrões em sinais de trading...")
            signals = []

            for pattern in patterns:
                if (
                    pattern.get("strength", 0) > 0.3
                    and pattern.get("confidence", 0) > 0.4
                ):

                    # Determina ação baseada no padrão
                    field_energy = pattern.get("field_energy", 0.0)
                    entropy = pattern.get("entropy", 1.0)

                    if field_energy > 0.6 and entropy < 0.7:
                        action = "BUY"
                    elif field_energy < -0.6 and entropy < 0.7:
                        action = "SELL"
                    else:
                        continue  # HOLD

                    # Obtém dados do símbolo
                    symbol = pattern.get("symbol", "BTC/USDT")
                    symbol_data = market_data.get(symbol, {})

                    if symbol_data:
                        signal = HolographicSignal(
                            symbol=symbol,
                            action=action,
                            confidence=pattern["confidence"],
                            timeframe="5m",
                            timestamp=asyncio.get_event_loop().time(),
                            rsi=symbol_data.get("rsi"),
                            volume_ratio=symbol_data.get("volume_ratio"),
                            volatility=symbol_data.get("volatility"),
                            quantum_rsi_encoded=symbol_data.get(
                                "quantum_rsi_encoded", False
                            ),
                            pattern_strength=pattern["strength"],
                            field_energy=field_energy,
                            entropy=entropy,
                        )

                        signals.append(signal)

            print(f"📡 {len(signals)} sinais holográficos gerados")

            # Exibe sinais
            for i, signal in enumerate(signals):
                print(
                    f"   Sinal {i+1}: {signal.symbol} {signal.action} "
                    f"confidence={signal.confidence:.2f} "
                    f"RSI={f'{signal.rsi:.1f}' if signal.rsi is not None else 'N/A'}"
                )

            # Executa trades
            if signals:
                print("\n🚀 Executando trades através do adapter...")

                for signal in signals:
                    print(f"\n⚡ Processando sinal: {signal.symbol} {signal.action}")

                    try:
                        trade_result = (
                            await self.trading_adapter.process_holographic_signal(
                                signal
                            )
                        )

                        if trade_result.trade_executed:
                            print(
                                f"   ✅ Trade executado @ {trade_result.execution_price:.4f}"
                            )
                        else:
                            print(
                                f"   ❌ Trade rejeitado: {trade_result.rejection_reason}"
                            )

                    except Exception as e:
                        print(f"   ❌ Erro executando trade: {e}")

                # Relatório de performance
                print("\n📊 Relatório de Performance do Adapter:")
                report = self.trading_adapter.get_holographic_performance_report()

                print(f"   - Sinais processados: {report['signals_processed']}")
                print(f"   - Trades executados: {report['trades_executed']}")
                print(f"   - Taxa de execução: {report['execution_rate']:.1f}%")
                print(
                    f"   - Quantum encoding rate: {report['quantum_encoding_rate']:.1f}%"
                )
                print(f"   - Posições abertas: {report['positions']['open']}")

            else:
                print("⚠️  Nenhum sinal válido gerado neste ciclo")

        except Exception as e:
            print(f"❌ Erro na geração de sinais e trading: {e}")
            raise

    async def example_5_complete_cycle(self):
        """Exemplo 5: Ciclo completo integrado."""

        print("\n" + "=" * 60)
        print("EXEMPLO 5: Ciclo Completo Integrado")
        print("=" * 60)

        try:
            print("🔄 Executando ciclo completo...")

            # 1. Coleta dados
            market_data, news_events, sentiment_data = (
                await self.example_2_data_collection_demo()
            )

            # 2. Simulação holográfica
            patterns = await self.example_3_holographic_simulation(
                market_data, news_events
            )

            # 3. Trading
            await self.example_4_signal_generation_and_trading(patterns, market_data)

            print("\n🎉 Ciclo completo executado com sucesso!")

        except Exception as e:
            print(f"❌ Erro no ciclo completo: {e}")
            raise

    async def cleanup(self):
        """Limpa recursos."""

        print("\n🧹 Limpando recursos...")

        try:
            if self.trading_adapter:
                await self.trading_adapter.close_all_holographic_positions(
                    "Example cleanup"
                )

            if self.trader:
                await self.trader.close_exchange()

            if self.data_collector:
                await self.data_collector.__aexit__(None, None, None)

            print("✅ Recursos limpos")

        except Exception as e:
            print(f"❌ Erro na limpeza: {e}")


async def main():
    """Função principal dos exemplos."""

    print("🌀 QUALIA Integrated Holographic Trading - Exemplos Práticos")
    print("=" * 60)

    example = HolographicTradingExample()

    try:
        # Executa exemplos em sequência
        await example.example_1_basic_setup()

        # Coleta dados e executa simulação
        market_data, news_events, sentiment_data = (
            await example.example_2_data_collection_demo()
        )
        patterns = await example.example_3_holographic_simulation(
            market_data, news_events
        )
        await example.example_4_signal_generation_and_trading(patterns, market_data)

        # Ciclo completo adicional
        await example.example_5_complete_cycle()

        print("\n" + "=" * 60)
        print("🎉 TODOS OS EXEMPLOS EXECUTADOS COM SUCESSO!")
        print("=" * 60)
        print("\nPróximos passos:")
        print(
            "1. Execute o sistema completo: python scripts/deploy_integrated_holographic_trading.py"
        )
        print("2. Configure credenciais KuCoin no .env para trading real")
        print(
            "3. Ajuste parâmetros no config/integrated_holographic_trading_config.yaml"
        )
        print("4. Monitore performance nos logs e relatórios")

    except Exception as e:
        print(f"\n❌ Erro nos exemplos: {e}")
        import traceback

        traceback.print_exc()

    finally:
        await example.cleanup()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Exemplos interrompidos pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro fatal: {e}")
        sys.exit(1)
