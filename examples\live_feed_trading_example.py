#!/usr/bin/env python3
"""
QUALIA Live Feed Trading Example - D-03.2 Integration

Exemplo prático de como usar o sistema de live feed integrado com o
sistema de trading QUALIA em modo paper trading.

Este exemplo demonstra:
1. Configuração da integração do live feed
2. Inicialização com parâmetros otimizados
3. Execução em modo paper trading
4. Monitoramento de dados em tempo real
5. Integração com sistema holográfico
"""

import asyncio
import time
import yaml
from pathlib import Path
from typing import Dict, Any, List
import sys
import signal

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.consciousness.live_feed_integration import LiveFeedIntegration
from qualia.consciousness.enhanced_data_collector import EnhancedDataCollector
from qualia.consciousness.amplification_calibrator import AmplificationCalibrator
from qualia.utils.logging_config import get_qualia_logger
from qualia.event_bus import SimpleEventBus

logger = get_qualia_logger(__name__)


class LiveFeedTradingExample:
    """Exemplo de trading com live feed integrado."""
    
    def __init__(self):
        self.integration: LiveFeedIntegration = None
        self.is_running = False
        self.stats = {
            "start_time": 0,
            "data_points_received": 0,
            "signals_generated": 0,
            "trades_executed": 0
        }
        
        # Configurar handler para interrupção
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handler para sinais de interrupção."""
        logger.info("🛑 Sinal de interrupção recebido, parando sistema...")
        self.is_running = False
    
    async def run_example(self):
        """Executa o exemplo de trading com live feed."""
        logger.info("🚀 Iniciando exemplo de trading com Live Feed")
        
        try:
            # 1. Configurar integração
            await self._setup_integration()
            
            # 2. Inicializar sistema
            await self._initialize_system()
            
            # 3. Iniciar trading
            await self._start_trading()
            
            # 4. Executar loop principal
            await self._main_trading_loop()
            
        except Exception as e:
            logger.error(f"❌ Erro durante execução do exemplo: {e}")
        
        finally:
            await self._cleanup()
    
    async def _setup_integration(self):
        """Configura a integração do live feed."""
        logger.info("⚙️ Configurando integração do live feed...")
        
        # Configuração otimizada para paper trading
        integration_config = {
            "mode": "hybrid",
            "enable_live_feed": True,
            "paper_trading_mode": True,
            "enable_fallback": True,
            "enable_data_validation": True,
            
            # Parâmetros otimizados conforme D-03.2
            "news_amp": 11.3,
            "price_amp": 1.0,
            "min_conf": 0.37,
            
            # Configuração de performance
            "update_frequency": 1.0,
            "batch_processing": True
        }
        
        # Criar integração
        self.integration = LiveFeedIntegration(config=integration_config)
        
        logger.info("✅ Integração configurada")
    
    async def _initialize_system(self):
        """Inicializa o sistema de trading."""
        logger.info("🔧 Inicializando sistema de trading...")
        
        # Símbolos para paper trading
        symbols = ["BTC/USDT", "ETH/USDT", "ADA/USDT"]
        timeframes = ["5m", "15m", "1h"]
        
        # Inicializar integração
        success = await self.integration.initialize(symbols=symbols, timeframes=timeframes)
        if not success:
            raise Exception("Falha na inicialização da integração")
        
        logger.info("✅ Sistema inicializado")
    
    async def _start_trading(self):
        """Inicia o sistema de trading."""
        logger.info("📈 Iniciando sistema de trading...")
        
        # Iniciar integração
        success = await self.integration.start()
        if not success:
            raise Exception("Falha ao iniciar integração")
        
        self.is_running = True
        self.stats["start_time"] = time.time()
        
        logger.info("✅ Sistema de trading iniciado")
    
    async def _main_trading_loop(self):
        """Loop principal de trading."""
        logger.info("🔄 Iniciando loop principal de trading...")
        
        last_status_time = 0
        status_interval = 30  # Log status a cada 30 segundos
        
        while self.is_running:
            try:
                current_time = time.time()
                
                # 1. Verificar dados do live feed
                await self._check_live_feed_data()
                
                # 2. Processar sinais de trading (simulado)
                await self._process_trading_signals()
                
                # 3. Executar trades em paper trading (simulado)
                await self._execute_paper_trades()
                
                # 4. Log de status periódico
                if current_time - last_status_time >= status_interval:
                    await self._log_status()
                    last_status_time = current_time
                
                # Aguardar próximo ciclo
                await asyncio.sleep(1.0)
                
            except Exception as e:
                logger.error(f"❌ Erro no loop principal: {e}")
                await asyncio.sleep(5.0)
        
        logger.info("🛑 Loop principal finalizado")
    
    async def _check_live_feed_data(self):
        """Verifica dados do live feed."""
        try:
            if not self.integration or not self.integration.live_feed_collector:
                return
            
            # Obter dados mais recentes
            latest_data = self.integration.live_feed_collector.get_latest_data()
            
            for symbol, ticker in latest_data.items():
                if ticker is not None:
                    self.stats["data_points_received"] += 1
                    
                    # Log dados recebidos (apenas ocasionalmente para não poluir logs)
                    if self.stats["data_points_received"] % 100 == 0:
                        logger.debug(f"📊 {symbol}: ${ticker.price:.4f} "
                                   f"(Vol: {ticker.volume_24h:.0f}, "
                                   f"Change: {ticker.change_24h_percent:.2f}%)")
            
        except Exception as e:
            logger.error(f"❌ Erro ao verificar dados do live feed: {e}")
    
    async def _process_trading_signals(self):
        """Processa sinais de trading (simulado)."""
        try:
            # Simular processamento de sinais baseado em dados holográficos
            # Em uma implementação real, isso seria feito pelo sistema holográfico
            
            # Verificar se há novos dados para processar
            if self.integration and self.integration.amplification_calibrator:
                # Simular geração de sinal ocasional
                if self.stats["data_points_received"] % 50 == 0 and self.stats["data_points_received"] > 0:
                    self.stats["signals_generated"] += 1
                    logger.info(f"📡 Sinal de trading gerado (#{self.stats['signals_generated']})")
            
        except Exception as e:
            logger.error(f"❌ Erro ao processar sinais de trading: {e}")
    
    async def _execute_paper_trades(self):
        """Executa trades em modo paper trading (simulado)."""
        try:
            # Simular execução de trades baseado em sinais
            # Em uma implementação real, isso seria feito pelo sistema de trading
            
            # Simular execução ocasional de trade
            if self.stats["signals_generated"] > self.stats["trades_executed"]:
                self.stats["trades_executed"] += 1
                logger.info(f"💰 Paper trade executado (#{self.stats['trades_executed']})")
            
        except Exception as e:
            logger.error(f"❌ Erro ao executar paper trades: {e}")
    
    async def _log_status(self):
        """Log do status atual do sistema."""
        try:
            uptime = time.time() - self.stats["start_time"]
            
            # Status da integração
            integration_status = self.integration.get_integration_status()
            
            # Log status resumido
            logger.info(
                f"📊 Status do Sistema (Uptime: {uptime:.0f}s):\n"
                f"   • Live Feed: {'✅' if integration_status.get('live_feed_active') else '❌'}\n"
                f"   • Fallback: {'✅' if integration_status.get('fallback_active') else '❌'}\n"
                f"   • Dados Recebidos: {self.stats['data_points_received']}\n"
                f"   • Sinais Gerados: {self.stats['signals_generated']}\n"
                f"   • Paper Trades: {self.stats['trades_executed']}\n"
                f"   • Parâmetros: news_amp={integration_status.get('config', {}).get('news_amp')}, "
                f"price_amp={integration_status.get('config', {}).get('price_amp')}, "
                f"min_conf={integration_status.get('config', {}).get('min_conf')}"
            )
            
            # Status detalhado dos componentes
            components = integration_status.get("components", {})
            if components.get("live_feed_collector"):
                feed_status = components["live_feed_collector"]
                logger.debug(f"🔍 Live Feed Status: {feed_status}")
            
            if components.get("amplification_calibrator"):
                calib_status = components["amplification_calibrator"]
                logger.debug(f"🎛️ Calibrator Status: {calib_status}")
            
        except Exception as e:
            logger.error(f"❌ Erro ao fazer log de status: {e}")
    
    async def _cleanup(self):
        """Limpeza e finalização."""
        logger.info("🧹 Executando limpeza...")
        
        try:
            if self.integration:
                await self.integration.stop()
            
            # Log estatísticas finais
            uptime = time.time() - self.stats["start_time"] if self.stats["start_time"] > 0 else 0
            
            logger.info(
                f"📈 Estatísticas Finais:\n"
                f"   • Tempo de Execução: {uptime:.0f} segundos\n"
                f"   • Dados Processados: {self.stats['data_points_received']}\n"
                f"   • Sinais Gerados: {self.stats['signals_generated']}\n"
                f"   • Paper Trades: {self.stats['trades_executed']}\n"
                f"   • Taxa de Dados: {self.stats['data_points_received']/max(uptime, 1):.2f} dados/s"
            )
            
        except Exception as e:
            logger.error(f"❌ Erro durante limpeza: {e}")
        
        logger.info("✅ Limpeza concluída")


async def main():
    """Função principal."""
    logger.info("🌟 QUALIA Live Feed Trading Example - D-03.2")
    logger.info("=" * 60)
    
    # Verificar se estamos no diretório correto
    config_path = Path("config/holographic_trading_config.yaml")
    if not config_path.exists():
        logger.error(f"❌ Arquivo de configuração não encontrado: {config_path}")
        logger.info("💡 Execute este script a partir do diretório raiz do projeto QUALIA")
        return
    
    # Criar e executar exemplo
    example = LiveFeedTradingExample()
    
    try:
        await example.run_example()
        
    except KeyboardInterrupt:
        logger.info("🛑 Exemplo interrompido pelo usuário")
    except Exception as e:
        logger.error(f"❌ Erro durante execução do exemplo: {e}")
    
    logger.info("🏁 Exemplo finalizado")


if __name__ == "__main__":
    # Configurar logging para exemplo
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Executar exemplo
    asyncio.run(main())
