#!/usr/bin/env python3
"""
Test script for structured logging system.
Validates sensitive data redaction, structured formatting, and logging functionality.
"""

import sys
import os
import logging
import tempfile
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.qualia.utils.structured_logging import (
    configure_structured_logging,
    get_qualia_logger,
    LogContext,
    SensitiveDataRedactor,
    StructuredFormatter,
    ConsoleFormatter
)

def test_sensitive_data_redaction():
    """Test sensitive data redaction functionality"""
    
    print("🧪 Testing Sensitive Data Redaction")
    print("=" * 50)
    
    redactor = SensitiveDataRedactor()
    
    test_cases = [
        # API Keys
        ('api_key: "sk-1234567890abcdef"', 'api_key: "***REDACTED***"'),
        ('API_KEY=abc123def456', 'API_KEY=***REDACTED***'),
        ("'secret': 'my-secret-key'", "'secret': '***REDACTED***'"),
        
        # Environment variables
        ('${KUCOIN_API_KEY}', '***ENV_VAR***'),
        ('${KUCOIN_SECRET_KEY}', '***ENV_VAR***'),
        ('${KUCOIN_PASSPHRASE}', '***ENV_VAR***'),
        
        # Long hex strings (potential keys)
        ('token: a1b2c3d4e5f6789012345678901234567890abcdef', 'token: ***46CHARS***'),
        
        # Email addresses
        ('<EMAIL> contacted us', '***@example.com contacted us'),
        
        # Normal text should remain unchanged
        ('This is normal log message', 'This is normal log message'),
        ('Processing BTC/USDT symbol', 'Processing BTC/USDT symbol'),
    ]
    
    passed = 0
    failed = 0
    
    for input_text, expected in test_cases:
        result = redactor.redact(input_text)
        if result == expected:
            print(f"✅ PASS: '{input_text}' -> '{result}'")
            passed += 1
        else:
            print(f"❌ FAIL: '{input_text}' -> '{result}' (expected: '{expected}')")
            failed += 1
    
    print(f"\nRedaction Tests: {passed} passed, {failed} failed")
    return failed == 0


def test_structured_formatter():
    """Test structured JSON formatter"""
    
    print("\n🧪 Testing Structured Formatter")
    print("=" * 50)
    
    formatter = StructuredFormatter(redact_sensitive=True, compact=True)
    
    # Create test log record
    logger = logging.getLogger('test.logger')
    record = logger.makeRecord(
        name='test.logger',
        level=logging.INFO,
        fn='test_file.py',
        lno=42,
        msg='Processing API key: %s',
        args=('sk-1234567890abcdef',),
        exc_info=None
    )
    
    # Add extra context
    record.symbol = 'BTC/USDT'
    record.component = 'oracle_engine'
    record.api_key = 'sk-secret-key-123'
    
    # Format the record
    formatted = formatter.format(record)
    
    try:
        log_data = json.loads(formatted)
        
        # Check basic structure
        required_fields = ['timestamp', 'level', 'logger', 'message', 'module', 'function', 'line']
        missing_fields = [field for field in required_fields if field not in log_data]
        
        if missing_fields:
            print(f"❌ FAIL: Missing required fields: {missing_fields}")
            return False
        
        # Check sensitive data redaction
        if 'sk-1234567890abcdef' in formatted:
            print("❌ FAIL: Sensitive data not redacted in message")
            return False
        
        if 'sk-secret-key-123' in formatted:
            print("❌ FAIL: Sensitive data not redacted in extra fields")
            return False
        
        # Check extra fields
        if 'extra' in log_data:
            extra = log_data['extra']
            if extra.get('symbol') != 'BTC/USDT':
                print("❌ FAIL: Extra field 'symbol' not preserved correctly")
                return False
            
            if extra.get('component') != 'oracle_engine':
                print("❌ FAIL: Extra field 'component' not preserved correctly")
                return False
        
        print("✅ PASS: Structured formatter working correctly")
        print(f"Sample output: {formatted[:100]}...")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ FAIL: Invalid JSON output: {e}")
        return False


def test_console_formatter():
    """Test console formatter"""
    
    print("\n🧪 Testing Console Formatter")
    print("=" * 50)
    
    formatter = ConsoleFormatter(use_colors=False, redact_sensitive=True, compact=False)
    
    # Create test log record
    logger = logging.getLogger('test.console')
    record = logger.makeRecord(
        name='test.console',
        level=logging.INFO,
        fn='test_file.py',
        lno=42,
        msg='API key: %s, processing symbol: %s',
        args=('sk-1234567890abcdef', 'BTC/USDT'),
        exc_info=None
    )
    
    # Add context
    record.symbol = 'BTC/USDT'
    record.component = 'trading_engine'
    
    # Format the record
    formatted = formatter.format(record)
    
    # Check sensitive data redaction
    if 'sk-1234567890abcdef' in formatted:
        print("❌ FAIL: Sensitive data not redacted")
        return False
    
    # Check context inclusion
    if 'symbol=BTC/USDT' not in formatted:
        print("❌ FAIL: Context not included")
        return False
    
    if 'component=trading_engine' not in formatted:
        print("❌ FAIL: Component context not included")
        return False
    
    print("✅ PASS: Console formatter working correctly")
    print(f"Sample output: {formatted}")
    return True


def test_qualia_logger():
    """Test QualiaLogger with context"""
    
    print("\n🧪 Testing QualiaLogger")
    print("=" * 50)
    
    # Create temporary log file
    import uuid
    temp_dir = Path(tempfile.gettempdir())
    log_file = temp_dir / f"qualia_test_{uuid.uuid4().hex[:8]}.log"
    
    try:
        # Configure structured logging
        configure_structured_logging(
            level="DEBUG",
            console_enabled=False,  # Disable console for this test
            file_enabled=True,
            file_path=log_file,
            structured_format=True,
            redact_sensitive=True
        )
        
        # Create logger with context
        context = LogContext(
            correlation_id="test-123",
            component="test_component",
            symbol="BTC/USDT",
            exchange="kucoin"
        )
        
        logger = get_qualia_logger("test.qualia", context)
        
        # Log various messages
        logger.info("Starting test operation")
        logger.debug("Debug message with API key: %s", "sk-secret-123")
        logger.warning("Warning message", operation="test_op")
        logger.error("Error with sensitive data", extra={'password': 'secret123'})
        
        # Read log file and validate
        log_content = log_file.read_text(encoding='utf-8')
        log_lines = [line.strip() for line in log_content.split('\n') if line.strip()]
        
        if len(log_lines) < 4:
            print(f"❌ FAIL: Expected at least 4 log lines, got {len(log_lines)}")
            return False
        
        # Check each log line
        for i, line in enumerate(log_lines):
            try:
                log_data = json.loads(line)
                
                # Check context fields
                extra = log_data.get('extra', {})
                if extra.get('correlation_id') != 'test-123':
                    print(f"❌ FAIL: Line {i+1} missing correlation_id")
                    return False
                
                if extra.get('component') != 'test_component':
                    print(f"❌ FAIL: Line {i+1} missing component")
                    return False
                
                # Check sensitive data redaction
                if 'sk-secret-123' in line or 'secret123' in line:
                    print(f"❌ FAIL: Line {i+1} contains sensitive data")
                    return False
                
            except json.JSONDecodeError:
                print(f"❌ FAIL: Line {i+1} is not valid JSON")
                return False
        
        print("✅ PASS: QualiaLogger working correctly")
        print(f"Generated {len(log_lines)} structured log entries")
        return True
        
    finally:
        # Clean up
        if log_file.exists():
            log_file.unlink()


def test_logging_levels():
    """Test different logging levels"""
    
    print("\n🧪 Testing Logging Levels")
    print("=" * 50)
    
    # Test with different levels
    levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
    
    for level in levels:
        import uuid
        temp_dir = Path(tempfile.gettempdir())
        log_file = temp_dir / f"qualia_level_test_{uuid.uuid4().hex[:8]}.log"
        
        try:
            configure_structured_logging(
                level=level,
                console_enabled=False,
                file_enabled=True,
                file_path=log_file,
                structured_format=True
            )
            
            logger = get_qualia_logger(f"test.{level.lower()}")
            
            # Log at all levels
            logger.debug("Debug message")
            logger.info("Info message")
            logger.warning("Warning message")
            logger.error("Error message")
            logger.critical("Critical message")
            
            # Count log entries
            log_content = log_file.read_text(encoding='utf-8')
            log_lines = [line.strip() for line in log_content.split('\n') if line.strip()]
            
            # Expected counts based on level
            expected_counts = {
                'DEBUG': 5,
                'INFO': 4,
                'WARNING': 3,
                'ERROR': 2,
                'CRITICAL': 1
            }
            
            expected = expected_counts[level]
            actual = len(log_lines)
            
            if actual == expected:
                print(f"✅ PASS: Level {level} - {actual} entries (expected {expected})")
            else:
                print(f"❌ FAIL: Level {level} - {actual} entries (expected {expected})")
                return False
                
        finally:
            # Clean up log file
            try:
                if log_file.exists():
                    log_file.unlink()
            except (OSError, PermissionError):
                pass  # File might be locked, ignore cleanup error
    
    return True


def main():
    """Run all tests"""
    
    print("🚀 Starting Structured Logging Tests")
    print("=" * 80)
    
    tests = [
        test_sensitive_data_redaction,
        test_structured_formatter,
        test_console_formatter,
        test_qualia_logger,
        test_logging_levels
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ FAIL: {test.__name__} raised exception: {e}")
            failed += 1
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 TEST SUMMARY")
    print("=" * 80)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 All structured logging tests passed!")
        return True
    else:
        print(f"\n💥 {failed} test(s) failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
