#!/usr/bin/env python
"""Demo: Synesthetic Quantum Fusion

Funde áudio (.wav) + v<PERSON><PERSON><PERSON> (.mp4) em um circuito QUALIA usando encoders
multimodais.  Requer dependências extras **opcionais** que não fazem parte do
core trading system:

    pip install librosa opencv-python qiskit-aer tqdm

Uso (CLI):
    python scripts/demo_synesthetic_fusion.py path/to/sample.wav path/to/sample.mp4

Gera um arquivo ``fusion_brightness.npy`` com uma série temporal de valores de
coerência (0-1) – que podem ser mapeados em cor depois – e imprime feedback a
cada quadro.
"""
from __future__ import annotations

import sys
from pathlib import Path

inserted_path = False
try:
    from qualia.core.multimodal_encoders import (
        SpectrogramQuantumEncoder,
        QuantumPatchEncoder,
        TemporalDifferenceEncoder,
    )
except ModuleNotFoundError:
    ROOT = Path(__file__).resolve().parents[1] / "src"
    sys.path.insert(0, str(ROOT))
    inserted_path = True
    from qualia.core.multimodal_encoders import (
        SpectrogramQuantumEncoder,
        QuantumPatchEncoder,
        TemporalDifferenceEncoder,
    )

from qualia.utils.logger import get_logger

logger = get_logger(__name__)
if inserted_path:
    logger.info(
        "Pacote 'qualia' nao encontrado; '%s' adicionado ao sys.path. Considere instalar com `pip install -e .`." % ROOT
    )

from typing import List

import numpy as np
from tqdm import tqdm

# Dependências opcionais (libs pesadas só usadas no demo)
try:
    import librosa
except ImportError as exc:  # pragma: no cover
    raise SystemExit(
        "librosa nao encontrado. Instale com `pip install librosa`. "
    ) from exc

try:
    import cv2  # type: ignore
except ImportError as exc:  # pragma: no cover
    raise SystemExit(
        "opencv-python nao encontrado. Instale com `pip install opencv-python`. "
    ) from exc

try:
    from qiskit_aer import AerSimulator
except ImportError as exc:  # pragma: no cover
    raise SystemExit(
        "qiskit-aer nao encontrado. Instale com `pip install qiskit-aer`. "
    ) from exc

from qualia.core.encoders import QuantumEncodingInterface


def main(wav_path: Path, mp4_path: Path, max_steps: int = 120):
    # ------------------------- ÁUDIO ------------------------- #
    y, sr = librosa.load(str(wav_path), sr=None, mono=True)
    # STFT básico
    S = np.abs(librosa.stft(y, n_fft=1024, hop_length=512))
    S_norm = (S - S.min()) / (np.ptp(S) + 1e-9)  # escala 0-1

    # ------------------------- VÍDEO ------------------------- #
    cap = cv2.VideoCapture(str(mp4_path))
    if not cap.isOpened():
        raise RuntimeError(f"Falha ao abrir {mp4_path}")

    ret, prev_frame = cap.read()
    if not ret:
        raise RuntimeError("Sem frames no vídeo.")
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY).astype(np.float32) / 255.0

    # ------------------------- Encoders ---------------------- #
    enc_audio = SpectrogramQuantumEncoder()
    enc_patch = QuantumPatchEncoder()
    enc_diff = TemporalDifferenceEncoder()
    q_interface = QuantumEncodingInterface([enc_audio, enc_patch, enc_diff])

    backend = AerSimulator()

    brightness_curve: List[float] = []

    # número de iterações limitado por áudio, vídeo e max_steps
    total_steps = min(
        S_norm.shape[1], int(cap.get(cv2.CAP_PROP_FRAME_COUNT)), max_steps
    )

    for t in tqdm(range(total_steps), desc="Processing"):
        # ----- snapshot áudio -----
        # média simples sobre todas as bandas (poderíamos usar bandas múltiplas)
        audio_power = float(S_norm[:, t].mean())
        audio_snapshot = {"band_power": audio_power, "timestamp": float(t)}

        # ----- snapshot vídeo -----
        ret, frame = cap.read()
        if not ret:
            break
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY).astype(np.float32) / 255.0
        diff_val = float(np.mean(np.abs(gray - prev_gray)))
        prev_gray = gray
        diff_snapshot = {"diff_value": diff_val, "frame_idx": t}

        # patch central 8×8
        h, w = gray.shape
        patch = gray[h // 2 - 4 : h // 2 + 4, w // 2 - 4 : w // 2 + 4].flatten()
        patch_snapshot = {"patch": patch, "position": (h // 2, w // 2)}

        # O batch circuit espera um dict, não uma lista!
        perception_data = {
            'audio': audio_snapshot,
            'patch': patch_snapshot,
            'diff': diff_snapshot,
        }

        qc = q_interface.build_batch_circuit(  # type: ignore[attr-defined]
            perception_data=perception_data, shots=256
        )[0]
        if qc is None:
            brightness_curve.append(0.0)
            continue

        job = backend.run(qc, shots=256)
        counts = job.result().get_counts()
        zero_state = "0" * qc.num_qubits
        brightness = counts.get(zero_state, 0) / 256.0
        brightness_curve.append(brightness)

    cap.release()

    out_file = wav_path.with_name("fusion_brightness.npy")
    np.save(out_file, np.asarray(brightness_curve))
    print(f"\nSalvo curva de brilho em {out_file}")


if __name__ == "__main__":
    if len(sys.argv) < 3:
        script = Path(sys.argv[0]).name
        print(f"Uso: python {script} <audio.wav> <video.mp4>")
        sys.exit(1)

    wav_path = Path(sys.argv[1]).expanduser().resolve()
    mp4_path = Path(sys.argv[2]).expanduser().resolve()
    main(wav_path, mp4_path)
