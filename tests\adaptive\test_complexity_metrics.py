from unittest.mock import MagicMock

import numpy as np
import pytest

from tests.stub_utils import install_stubs

install_stubs()

from qualia.adaptive_evolution import AdaptiveConsciousnessEvolution


@pytest.mark.parametrize(
    "market_data,expected",
    [
        ({"volatility": 0.8, "non_linearity": 0.6}, 0.7),
        ({"volatility": 0.2, "non_linearity": 0.4}, 0.3),
    ],
)
def test_calculate_market_complexity_valid(market_data, expected):
    ace = AdaptiveConsciousnessEvolution(qualia_universe=MagicMock())
    result = ace._calculate_market_complexity(market_data)
    assert pytest.approx(expected) == result


def test_calculate_market_complexity_missing_keys():
    ace = AdaptiveConsciousnessEvolution(qualia_universe=MagicMock())
    result = ace._calculate_market_complexity({"volatility": 0.1})
    assert result == 0.5


@pytest.mark.parametrize(
    "prices,expected",
    [([], 0.0), ([1.0], 0.0), ([1.0, 2.0, 3.0], 0.25)],
)
def test_calculate_volatility(prices, expected):
    ace = AdaptiveConsciousnessEvolution(qualia_universe=MagicMock())
    result = ace._calculate_volatility(prices)
    assert pytest.approx(expected, rel=1e-6) == result


@pytest.mark.parametrize(
    "prices,expected",
    [
        ([1, 1, 1, 1, 1], 0.0),
        ([1, 2, 3, 4, 5], 0.0),
        ([1, 2, 1, 2, 1, 2], pytest.approx(0.38685280723454163)),
        ([1, 5, 2, 4, 3, 7], pytest.approx(0.5802792108518124)),
    ],
)
def test_measure_non_linearity(prices, expected):
    ace = AdaptiveConsciousnessEvolution(qualia_universe=MagicMock())
    result = ace._measure_non_linearity(prices)
    assert result == expected


def test_measure_non_linearity_random_series():
    ace = AdaptiveConsciousnessEvolution(qualia_universe=MagicMock())
    np.random.seed(42)
    prices = list(100 + np.random.normal(0, 1, 50).cumsum())
    result = ace._measure_non_linearity(prices)
    assert 0.9 < result <= 1.0
