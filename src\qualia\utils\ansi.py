"""ANSI color codes and helper for CLI output."""

from __future__ import annotations

__all__ = ["BOLD", "RED", "GREEN", "<PERSON><PERSON><PERSON><PERSON>", "RESET", "colorize"]

BOLD = "\033[1m"
RED = "\033[91m"
GREEN = "\033[92m"
YELLOW = "\033[93m"
RESET = "\033[0m"


def colorize(text: str, color: str, *, bold: bool = False) -> str:
    """Return ``text`` wrapped in ANSI color codes.

    Parameters
    ----------
    text : str
        Texto a ser colorido.
    color : str
        Um dos códigos de cor definidos neste módulo.
    bold : bool, optional
        Se ``True``, aplica :data:`BOLD` antes da cor.

    Returns
    -------
    str
        Texto formatado com a cor escolhida.
    """

    prefix = f"{BOLD}{color}" if bold else color
    return f"{prefix}{text}{RESET}"
