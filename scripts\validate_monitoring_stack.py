#!/usr/bin/env python3
"""
Script de validação completa do stack de monitoramento QUALIA.

YAA REFINEMENT: Valida funcionamento de todos os componentes do sistema
de monitoramento: Grafana, Prometheus, AlertManager, Exportador.
"""

import sys
import time
import requests
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class MonitoringStackValidator:
    """Validador completo do stack de monitoramento."""
    
    def __init__(self):
        self.services = {
            "Prometheus": {
                "url": "http://localhost:9090",
                "health_endpoint": "/-/ready",
                "port": 9090
            },
            "Grafana": {
                "url": "http://localhost:3000",
                "health_endpoint": "/api/health",
                "port": 3000
            },
            "AlertManager": {
                "url": "http://localhost:9093",
                "health_endpoint": "/-/ready",
                "port": 9093
            },
            "QUALIA Exporter": {
                "url": "http://localhost:8080",
                "health_endpoint": "/health",
                "port": 8080
            }
        }
        
        self.validation_results: Dict[str, bool] = {}
        self.errors: List[str] = []
    
    def validate_service_health(self, service_name: str, config: Dict) -> bool:
        """Valida saúde de um serviço específico."""
        try:
            health_url = f"{config['url']}{config['health_endpoint']}"
            response = requests.get(health_url, timeout=10)
            
            if response.status_code == 200:
                logger.info(f"✅ {service_name}: Saudável")
                return True
            else:
                logger.error(f"❌ {service_name}: Status {response.status_code}")
                self.errors.append(f"{service_name} retornou status {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            logger.error(f"❌ {service_name}: Não conseguiu conectar")
            self.errors.append(f"{service_name} não está acessível")
            return False
        except requests.exceptions.Timeout:
            logger.error(f"❌ {service_name}: Timeout")
            self.errors.append(f"{service_name} timeout na resposta")
            return False
        except Exception as e:
            logger.error(f"❌ {service_name}: Erro {e}")
            self.errors.append(f"{service_name} erro: {str(e)}")
            return False
    
    def validate_prometheus_targets(self) -> bool:
        """Valida se Prometheus está coletando targets."""
        try:
            response = requests.get("http://localhost:9090/api/v1/targets", timeout=10)
            if response.status_code != 200:
                self.errors.append("Prometheus targets API não acessível")
                return False
            
            targets_data = response.json()
            active_targets = targets_data.get("data", {}).get("activeTargets", [])
            
            if not active_targets:
                self.errors.append("Nenhum target ativo no Prometheus")
                return False
            
            # Verificar targets específicos
            expected_jobs = ["prometheus", "node-exporter", "statsd-exporter"]
            found_jobs = set()
            
            for target in active_targets:
                job = target.get("labels", {}).get("job", "")
                if job in expected_jobs:
                    found_jobs.add(job)
                
                if target.get("health") != "up":
                    logger.warning(f"⚠️ Target {job} não está UP: {target.get('lastError', 'Unknown')}")
            
            missing_jobs = set(expected_jobs) - found_jobs
            if missing_jobs:
                logger.warning(f"⚠️ Jobs não encontrados: {missing_jobs}")
            
            logger.info(f"✅ Prometheus: {len(active_targets)} targets ativos")
            return True
            
        except Exception as e:
            self.errors.append(f"Erro validando targets Prometheus: {str(e)}")
            return False
    
    def validate_qualia_metrics(self) -> bool:
        """Valida se métricas QUALIA estão sendo exportadas."""
        try:
            response = requests.get("http://localhost:8080/metrics", timeout=10)
            if response.status_code != 200:
                self.errors.append("Endpoint de métricas QUALIA não acessível")
                return False
            
            metrics_text = response.text
            
            # Verificar métricas essenciais
            expected_metrics = [
                "qualia_hyperparams_total_return_pct",
                "qualia_hyperparams_sharpe_ratio",
                "qualia_hyperparams_max_drawdown_pct",
                "qualia_hyperparams_decision_rate",
                "qualia_system_uptime_seconds"
            ]
            
            found_metrics = []
            for metric in expected_metrics:
                if metric in metrics_text:
                    found_metrics.append(metric)
            
            if len(found_metrics) < len(expected_metrics) * 0.8:  # 80% das métricas
                missing = set(expected_metrics) - set(found_metrics)
                self.errors.append(f"Métricas QUALIA faltando: {missing}")
                return False
            
            logger.info(f"✅ QUALIA Metrics: {len(found_metrics)}/{len(expected_metrics)} métricas encontradas")
            return True
            
        except Exception as e:
            self.errors.append(f"Erro validando métricas QUALIA: {str(e)}")
            return False
    
    def validate_grafana_datasource(self) -> bool:
        """Valida se Grafana tem datasource Prometheus configurado."""
        try:
            # Login no Grafana
            login_data = {"user": "admin", "password": "qualia2024"}
            session = requests.Session()
            
            login_response = session.post(
                "http://localhost:3000/login",
                json=login_data,
                timeout=10
            )
            
            if login_response.status_code not in [200, 302]:
                self.errors.append("Não conseguiu fazer login no Grafana")
                return False
            
            # Verificar datasources
            ds_response = session.get("http://localhost:3000/api/datasources", timeout=10)
            if ds_response.status_code != 200:
                self.errors.append("Não conseguiu acessar datasources do Grafana")
                return False
            
            datasources = ds_response.json()
            prometheus_ds = None
            
            for ds in datasources:
                if ds.get("type") == "prometheus":
                    prometheus_ds = ds
                    break
            
            if not prometheus_ds:
                self.errors.append("Datasource Prometheus não encontrado no Grafana")
                return False
            
            # Testar datasource
            test_response = session.post(
                f"http://localhost:3000/api/datasources/{prometheus_ds['id']}/proxy/api/v1/query",
                params={"query": "up"},
                timeout=10
            )
            
            if test_response.status_code != 200:
                self.errors.append("Datasource Prometheus não está funcionando")
                return False
            
            logger.info("✅ Grafana: Datasource Prometheus configurado e funcionando")
            return True
            
        except Exception as e:
            self.errors.append(f"Erro validando Grafana datasource: {str(e)}")
            return False
    
    def validate_alert_rules(self) -> bool:
        """Valida se regras de alerta estão carregadas."""
        try:
            response = requests.get("http://localhost:9090/api/v1/rules", timeout=10)
            if response.status_code != 200:
                self.errors.append("API de regras Prometheus não acessível")
                return False
            
            rules_data = response.json()
            rule_groups = rules_data.get("data", {}).get("groups", [])
            
            if not rule_groups:
                self.errors.append("Nenhuma regra de alerta carregada")
                return False
            
            # Procurar regras QUALIA
            qualia_rules = 0
            for group in rule_groups:
                if "qualia" in group.get("name", "").lower():
                    qualia_rules += len(group.get("rules", []))
            
            if qualia_rules == 0:
                self.errors.append("Regras de alerta QUALIA não encontradas")
                return False
            
            logger.info(f"✅ Alert Rules: {qualia_rules} regras QUALIA carregadas")
            return True
            
        except Exception as e:
            self.errors.append(f"Erro validando regras de alerta: {str(e)}")
            return False
    
    def validate_docker_containers(self) -> bool:
        """Valida se containers Docker estão rodando."""
        try:
            result = subprocess.run([
                "docker", "ps", "--format", "table {{.Names}}\t{{.Status}}"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                self.errors.append("Não conseguiu listar containers Docker")
                return False
            
            containers_output = result.stdout
            expected_containers = [
                "qualia-prometheus",
                "qualia-grafana", 
                "qualia-alertmanager",
                "qualia-node-exporter",
                "qualia-statsd-exporter"
            ]
            
            running_containers = []
            for container in expected_containers:
                if container in containers_output and "Up" in containers_output:
                    running_containers.append(container)
            
            if len(running_containers) < len(expected_containers):
                missing = set(expected_containers) - set(running_containers)
                self.errors.append(f"Containers não rodando: {missing}")
                return False
            
            logger.info(f"✅ Docker: {len(running_containers)} containers rodando")
            return True
            
        except Exception as e:
            self.errors.append(f"Erro validando containers Docker: {str(e)}")
            return False
    
    def run_full_validation(self) -> bool:
        """Executa validação completa do stack."""
        logger.info("🔍 Iniciando validação completa do stack de monitoramento...")
        
        validations = [
            ("Docker Containers", self.validate_docker_containers),
            ("Service Health", lambda: all(
                self.validate_service_health(name, config) 
                for name, config in self.services.items()
            )),
            ("Prometheus Targets", self.validate_prometheus_targets),
            ("QUALIA Metrics", self.validate_qualia_metrics),
            ("Grafana Datasource", self.validate_grafana_datasource),
            ("Alert Rules", self.validate_alert_rules)
        ]
        
        all_passed = True
        
        for validation_name, validation_func in validations:
            logger.info(f"🔍 Validando: {validation_name}")
            try:
                result = validation_func()
                self.validation_results[validation_name] = result
                if not result:
                    all_passed = False
            except Exception as e:
                logger.error(f"❌ Erro na validação {validation_name}: {e}")
                self.validation_results[validation_name] = False
                self.errors.append(f"Erro na validação {validation_name}: {str(e)}")
                all_passed = False
        
        return all_passed
    
    def print_summary(self):
        """Imprime resumo da validação."""
        print("\n" + "="*60)
        print("📊 RESUMO DA VALIDAÇÃO - QUALIA MONITORING STACK")
        print("="*60)
        
        for validation_name, result in self.validation_results.items():
            status = "✅ PASSOU" if result else "❌ FALHOU"
            print(f"{validation_name}: {status}")
        
        if self.errors:
            print("\n🚨 ERROS ENCONTRADOS:")
            for i, error in enumerate(self.errors, 1):
                print(f"{i}. {error}")
        
        overall_status = "✅ SUCESSO" if all(self.validation_results.values()) else "❌ FALHAS DETECTADAS"
        print(f"\nSTATUS GERAL: {overall_status}")
        print("="*60 + "\n")


def main():
    """Função principal."""
    validator = MonitoringStackValidator()
    
    try:
        success = validator.run_full_validation()
        validator.print_summary()
        
        if success:
            logger.info("🎉 Validação completa: Stack de monitoramento funcionando perfeitamente!")
            sys.exit(0)
        else:
            logger.error("💥 Validação falhou: Problemas detectados no stack")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("🛑 Validação interrompida pelo usuário")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Erro durante validação: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
