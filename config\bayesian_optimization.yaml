# QUALIA Bayesian Optimization Configuration
# Configuração para o sistema de otimização Bayesiana online (Etapa D)
# Baseado nos resultados do benchmark offline da Etapa C

# Configuração do Otimizador Bayesiano
optimization:
  # Configuração de trials
  n_trials_per_cycle: 25              # Número de trials por ciclo de otimização
  optimization_interval_cycles: 500   # Executar otimização a cada N ciclos
  max_concurrent_optimizations: 4     # Máximo de otimizações simultâneas

  # Configuração de performance
  lookback_hours: 24                  # Janela de análise de performance (horas)
  min_trades_for_optimization: 10     # Mínimo de trades para considerar otimização

  # Métrica objetivo
  objective_metric: "sharpe_pnl_combined"  # sharpe_ratio, pnl_24h, sharpe_pnl_combined

  # Pesos para objetivo combinado
  weights:
    sharpe_weight: 0.6                # Peso do Sharpe ratio
    pnl_weight: 0.4                   # Peso do PnL 24h

  # D-04: Advanced Pruning & Multi-fidelity
  pruning_strategy: "adaptive"        # none, median, successive_halving, adaptive, trading_aware, multi_fidelity
  enable_multi_fidelity: true         # Habilitar otimização multi-fidelidade
  multi_fidelity_levels: [1, 6, 24]  # Níveis de fidelidade em horas

# D-04: Configuração de Pruning Avançado
pruning:
  # Estratégia principal (NONE, MEDIAN, SUCCESSIVE_HALVING, ADAPTIVE, TRADING_AWARE)
  strategy: "ADAPTIVE"

  # MedianPruner settings
  median_n_startup_trials: 10         # Trials antes de começar pruning
  median_n_warmup_steps: 5            # Steps de warmup
  median_interval_steps: 1            # Intervalo entre avaliações
  median_percentile: 50.0             # Percentil para pruning (50% = mediana)

  # SuccessiveHalving settings
  sh_min_resource: 1                  # Mínimo de recursos (horas)
  sh_reduction_factor: 3              # Fator de redução
  sh_min_early_stopping_rate: 0      # Taxa mínima de early stopping

  # Trading-aware pruning thresholds
  min_sharpe_threshold: 0.3           # Sharpe mínimo para continuar
  max_drawdown_threshold: 0.20        # Drawdown máximo (20%)
  min_trades_threshold: 10            # Mínimo de trades para avaliar

  # Presets por regime de mercado
  regime_presets:
    BULL:
      median_n_startup_trials: 8      # Menos trials iniciais em bull market
      median_n_warmup_steps: 4        # Menos warmup
      min_sharpe_threshold: 0.2       # Threshold mais baixo
      max_drawdown_threshold: 0.25    # Mais tolerante a drawdown

    BEAR:
      median_n_startup_trials: 15     # Mais trials iniciais em bear market
      median_n_warmup_steps: 8        # Mais warmup
      min_sharpe_threshold: 0.5       # Threshold mais alto
      max_drawdown_threshold: 0.15    # Menos tolerante a drawdown

    VOLATILE:
      median_n_startup_trials: 12     # Trials moderados
      median_n_warmup_steps: 6        # Warmup moderado
      sh_min_resource: 2              # Mais recursos iniciais
      sh_reduction_factor: 2          # Redução mais suave

    STABLE:
      median_n_startup_trials: 6      # Menos trials em mercado estável
      median_n_warmup_steps: 3        # Menos warmup
      sh_reduction_factor: 4          # Redução mais agressiva

# D-04: Configuração Multi-fidelidade
multi_fidelity:
  enabled: true                       # Habilitar otimização multi-fidelidade

  # Níveis de fidelidade (horas de backtesting)
  fidelity_levels: [1, 6, 24]        # 1h, 6h, 24h

  # Orçamento de trials por nível
  budget_ratios: [0.5, 0.3, 0.2]     # 50% no nível 1h, 30% no 6h, 20% no 24h

  # Critérios de promoção
  promotion_percentile: 0.3           # Top 30% promovidos para próximo nível
  min_trials_for_promotion: 5         # Mínimo de trials para avaliar promoção

  # Pesos das métricas por nível de fidelidade
  metrics_weights:
    sharpe_ratio: [0.4, 0.5, 0.6]    # Mais peso em fidelidade alta
    pnl: [0.3, 0.3, 0.3]             # Peso constante
    drawdown: [0.3, 0.2, 0.1]        # Menos peso em fidelidade alta

  # Presets por regime de mercado
  regime_adjustments:
    VOLATILE:
      promotion_percentile: 0.4       # Mais seletivo em mercado volátil
      min_trials_for_promotion: 8     # Mais trials para decidir

    STABLE:
      promotion_percentile: 0.2       # Menos seletivo em mercado estável
      min_trials_for_promotion: 3     # Menos trials para decidir

  # Trading-aware pruning thresholds
  min_sharpe_threshold: 0.5           # Sharpe mínimo para continuar
  max_drawdown_threshold: 0.15        # Drawdown máximo (15%)
  min_trades_threshold: 10            # Mínimo de trades para avaliar

  # Adaptive settings
  adaptive_percentile_range: [25.0, 75.0]  # Range de percentil adaptativo
  adaptive_adjustment_factor: 0.1     # Fator de ajuste

# D-04: Configuração Multi-fidelidade
multi_fidelity:
  # Distribuição de orçamento por nível
  budget_allocation:
    low: 0.6      # 60% dos trials em baixa fidelidade (1h)
    medium: 0.3   # 30% em média fidelidade (6h)
    high: 0.1     # 10% em alta fidelidade (24h)

  # Critérios de promoção
  promotion_thresholds:
    low:          # Promoção de 1h para 6h
      min_sharpe: 0.8
      max_drawdown: 0.12
      min_trades: 5
      percentile_rank: 0.7    # Top 30%
    medium:       # Promoção de 6h para 24h
      min_sharpe: 1.2
      max_drawdown: 0.10
      min_trades: 15
      percentile_rank: 0.8    # Top 20%

  # Configurações de execução
  max_concurrent_evaluations: 4       # Máximo de avaliações simultâneas
  evaluation_timeout_multiplier: 1.5  # Timeout = fidelity_hours * multiplier
  early_stopping_enabled: true       # Habilitar early stopping
  early_stopping_patience: 3         # Níveis sem melhoria para parar
  
  # Configuração do sampler Optuna
  sampler:
    type: "TPE"                       # TPE, Random, CmaEs
    n_startup_trials: 10              # Trials iniciais para warm-up
    seed: 42                          # Seed para reprodutibilidade
  
  # Configuração de pruning
  pruning:
    enabled: true                     # Habilitar pruning de trials ruins
    type: "MedianPruner"              # MedianPruner, SuccessiveHalvingPruner
    n_startup_trials: 5               # Trials antes de iniciar pruning

# Espaço de busca dos parâmetros
# Baseado nos resultados do benchmark offline
parameter_space:
  price_amplification:
    min: 1.0                          # Valor mínimo
    max: 10.0                         # Valor máximo (benchmark mostrou 10.0 como ótimo)
    type: "float"                     # Tipo do parâmetro
    
  news_amplification:
    min: 1.0                          # Valor mínimo
    max: 15.0                         # Valor máximo (expandido para incluir 11.3)
    type: "float"                     # Tipo do parâmetro
    
  min_confidence:
    min: 0.20                         # Valor mínimo
    max: 0.80                         # Valor máximo
    type: "float"                     # Tipo do parâmetro

# Configuração de símbolos
symbols:
  # Símbolos principais baseados no benchmark
  active:
    - "BTCUSDT"
    - "ETHUSDT"
    - "BNBUSDT"
    - "ADAUSDT"
    - "SOLUSDT"
    - "DOTUSDT"
    - "LINKUSDT"
    - "POLUSDT"
  
  # Configurações específicas por símbolo (se necessário)
  specific_configs:
    ETHUSDT:
      # ETHUSDT teve melhor performance no benchmark
      initial_params:
        price_amplification: 10.0     # Melhor resultado do benchmark
        news_amplification: 1.0       # Melhor resultado do benchmark
        min_confidence: 0.30          # Melhor resultado do benchmark
    
    BTCUSDT:
      initial_params:
        price_amplification: 1.0      # Baseado em descobertas anteriores
        news_amplification: 11.3      # Descoberta de produção
        min_confidence: 0.37          # Descoberta de produção

# Configuração do serviço gRPC
grpc:
  enabled: true                       # Habilitar comunicação gRPC
  port: 50051                         # Porta do serviço
  host: "localhost"                   # Host do serviço
  max_workers: 4                      # Máximo de workers
  
  # Configuração de timeout
  timeouts:
    request_timeout: 30               # Timeout para requisições (segundos)
    connection_timeout: 10            # Timeout para conexão (segundos)

# Configuração de persistência
persistence:
  # Salvamento de estado
  state_save_interval_minutes: 30     # Intervalo para salvar estado
  state_file: "data/bayesian_optimizer_state.json"
  
  # Log de otimizações
  optimization_log_file: "data/optimization_log.jsonl"
  optimization_log_max_size_mb: 100   # Tamanho máximo do log
  
  # Backup de configurações
  backup_best_configs: true           # Fazer backup das melhores configurações
  backup_interval_hours: 6            # Intervalo para backup

# Configuração de monitoramento
monitoring:
  # Logging
  log_level: "INFO"                   # DEBUG, INFO, WARNING, ERROR
  verbose_logging: false              # Logging detalhado
  
  # Métricas
  enable_metrics: true                # Habilitar coleta de métricas
  metrics_interval_seconds: 60        # Intervalo de coleta de métricas
  
  # Alertas
  alerts:
    enabled: true                     # Habilitar alertas
    performance_threshold: -0.20      # Alerta se performance cair mais que 20%
    optimization_failure_threshold: 3 # Alerta após N falhas consecutivas

# Configuração de segurança
safety:
  # Limites de parâmetros (fail-safe)
  parameter_bounds:
    price_amplification:
      absolute_min: 0.1               # Limite absoluto mínimo
      absolute_max: 20.0              # Limite absoluto máximo
    news_amplification:
      absolute_min: 0.1               # Limite absoluto mínimo
      absolute_max: 20.0              # Limite absoluto máximo
    min_confidence:
      absolute_min: 0.05              # Limite absoluto mínimo
      absolute_max: 1.0               # Limite absoluto máximo
  
  # Validação de performance
  performance_validation:
    enabled: true                     # Habilitar validação
    min_sharpe_ratio: -5.0            # Sharpe mínimo aceitável
    max_drawdown_threshold: 0.50      # Drawdown máximo aceitável (50%)
    
  # Rollback automático
  auto_rollback:
    enabled: true                     # Habilitar rollback automático
    performance_degradation_threshold: 0.30  # Rollback se performance cair 30%
    rollback_to_previous_best: true   # Voltar para melhor configuração anterior

# Configuração de integração
integration:
  # Integração com sistema QUALIA
  qualia_integration:
    enabled: true                     # Habilitar integração
    config_update_method: "grpc"      # grpc, file, api
    
  # Integração com sistema de trading
  trading_system:
    parameter_update_delay_seconds: 5 # Delay para aplicar novos parâmetros
    validation_period_minutes: 10     # Período de validação após atualização
    
  # Integração com monitoramento
  monitoring_integration:
    prometheus_enabled: false         # Integração com Prometheus
    grafana_dashboard: false          # Dashboard Grafana
    slack_notifications: false        # Notificações Slack

# Configuração experimental
experimental:
  # Recursos experimentais
  multi_objective_optimization: false # Otimização multi-objetivo
  ensemble_methods: false             # Métodos ensemble
  adaptive_search_space: false       # Espaço de busca adaptativo
  
  # A/B Testing
  ab_testing:
    enabled: false                    # Habilitar A/B testing
    test_ratio: 0.1                   # Proporção para teste (10%)
    
# Configuração de desenvolvimento
development:
  # Modo de desenvolvimento
  debug_mode: false                   # Modo debug
  simulation_mode: false              # Modo simulação (sem aplicar parâmetros reais)
  
  # Testes
  enable_unit_tests: true             # Habilitar testes unitários
  enable_integration_tests: true     # Habilitar testes de integração
  
  # Profiling
  enable_profiling: false             # Habilitar profiling de performance
  profiling_output_dir: "data/profiling"
