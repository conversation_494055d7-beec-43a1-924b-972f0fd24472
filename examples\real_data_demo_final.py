#!/usr/bin/env python3
"""
Demo Final - Grid Search QUALIA com Dados Reais
YAA IMPLEMENTATION: Demonstração completa da Etapa C com dados históricos reais.
"""

import asyncio
import json
import sys
from datetime import datetime, timedelta
from pathlib import Path
import time

import pandas as pd
import numpy as np
import requests

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))


class BinanceDataFetcher:
    """Fetcher de dados reais da Binance usando API REST."""
    
    BASE_URL = "https://api.binance.com/api/v3"
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'QUALIA-GridSearch/1.0'
        })
    
    def fetch_historical_data(self, symbol: str, interval: str = "1h", days: int = 90) -> pd.DataFrame:
        """Busca dados históricos da Binance."""
        try:
            # Calcula timestamps
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            print(f"🔄 Buscando dados reais da Binance para {symbol} ({days} dias)...")
            
            # Parâmetros da API
            params = {
                'symbol': symbol.replace('/', ''),  # Remove '/' para formato Binance
                'interval': interval,
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            # Faz requisição
            response = self.session.get(f"{self.BASE_URL}/klines", params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                print(f"⚠️ Nenhum dado retornado para {symbol}")
                return pd.DataFrame()
            
            # Converte para DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Processa dados
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']].copy()
            
            # Converte para float
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index()
            
            # Calcula retornos
            df['returns'] = df['close'].pct_change().fillna(0)
            
            print(f"✅ Dados reais obtidos para {symbol}: {len(df)} candles de {df.index[0]} a {df.index[-1]}")
            return df
            
        except Exception as e:
            print(f"❌ Erro ao buscar dados reais para {symbol}: {e}")
            return pd.DataFrame()


def enhanced_backtest(df: pd.DataFrame, price_amp: float, news_amp: float, min_conf: float):
    """Backtest aprimorado baseado nos princípios QUALIA."""
    
    if df.empty or len(df) < 100:
        return {
            'total_return_pct': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown_pct': 0.0,
            'win_rate': 0.0,
            'total_trades': 0,
            'volatility': 0.0,
            'calmar_ratio': 0.0,
            'sortino_ratio': 0.0
        }
    
    # Copia e limpa dados
    df = df.copy().dropna()
    
    # Indicadores técnicos avançados
    df['sma_fast'] = df['close'].rolling(8).mean()
    df['sma_slow'] = df['close'].rolling(21).mean()
    df['ema_fast'] = df['close'].ewm(span=12).mean()
    df['ema_slow'] = df['close'].ewm(span=26).mean()
    
    # RSI
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # Bollinger Bands
    bb_period = 20
    df['bb_middle'] = df['close'].rolling(bb_period).mean()
    bb_std = df['close'].rolling(bb_period).std()
    df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
    df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
    
    # Volume indicators
    df['volume_sma'] = df['volume'].rolling(20).mean()
    df['volume_ratio'] = df['volume'] / df['volume_sma']
    
    # Volatilidade
    df['volatility'] = df['returns'].rolling(24).std()
    df['atr'] = df[['high', 'low', 'close']].apply(
        lambda x: max(x['high'] - x['low'], 
                     abs(x['high'] - x['close']), 
                     abs(x['low'] - x['close'])), axis=1
    ).rolling(14).mean()
    
    # Simula confiança QUALIA baseada em múltiplos fatores
    df['market_confidence'] = np.clip(
        0.5 + 
        0.2 * (df['volume_ratio'] - 1) +  # Volume anômalo aumenta confiança
        0.1 * (1 - df['volatility'] / df['volatility'].mean()) +  # Baixa volatilidade aumenta confiança
        0.1 * np.sin(np.arange(len(df)) * 2 * np.pi / 168),  # Ciclo semanal simulado
        0.2, 0.9
    )
    
    # Gera sinais QUALIA
    df['signal_strength'] = 0.0
    
    # Sinal de momentum
    momentum_signal = (
        (df['ema_fast'] > df['ema_slow']) & 
        (df['ema_fast'].shift(1) <= df['ema_slow'].shift(1))
    ).astype(int) - (
        (df['ema_fast'] < df['ema_slow']) & 
        (df['ema_fast'].shift(1) >= df['ema_slow'].shift(1))
    ).astype(int)
    
    # Sinal de mean reversion
    mean_reversion_signal = (
        (df['close'] < df['bb_lower']) & (df['rsi'] < 30)
    ).astype(int) - (
        (df['close'] > df['bb_upper']) & (df['rsi'] > 70)
    ).astype(int)
    
    # Combina sinais com pesos baseados nos parâmetros QUALIA
    price_weight = np.clip(price_amp / 10.0, 0.1, 1.0)
    news_weight = np.clip(news_amp / 10.0, 0.1, 1.0)
    
    df['combined_signal'] = (
        price_weight * momentum_signal + 
        news_weight * mean_reversion_signal * df['volume_ratio']
    )
    
    # Aplica filtro de confiança
    df['filtered_signal'] = df['combined_signal'] * (df['market_confidence'] >= min_conf)
    
    # Normaliza posição
    df['position'] = np.clip(df['filtered_signal'], -1.0, 1.0)
    
    # Calcula retornos com custos
    transaction_cost = 0.001  # 0.1%
    df['position_change'] = df['position'].diff().abs()
    df['costs'] = df['position_change'] * transaction_cost
    
    df['strategy_returns'] = df['position'].shift(1) * df['returns'] - df['costs']
    df['cumulative_returns'] = (1 + df['strategy_returns']).cumprod()
    
    # Remove NaN
    df = df.dropna()
    
    if len(df) == 0:
        return {
            'total_return_pct': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown_pct': 0.0,
            'win_rate': 0.0,
            'total_trades': 0,
            'volatility': 0.0,
            'calmar_ratio': 0.0,
            'sortino_ratio': 0.0
        }
    
    # Métricas de performance
    total_return = df['cumulative_returns'].iloc[-1] - 1
    
    # Anualizadas
    periods_per_year = 8760  # Horas por ano
    mean_return = df['strategy_returns'].mean() * periods_per_year
    volatility = df['strategy_returns'].std() * np.sqrt(periods_per_year)
    
    # Sharpe Ratio
    sharpe_ratio = mean_return / volatility if volatility > 0 else 0
    
    # Max Drawdown
    running_max = df['cumulative_returns'].expanding().max()
    drawdown = (df['cumulative_returns'] - running_max) / running_max
    max_drawdown = abs(drawdown.min())
    
    # Calmar Ratio
    calmar_ratio = mean_return / max_drawdown if max_drawdown > 0 else 0
    
    # Sortino Ratio
    negative_returns = df['strategy_returns'][df['strategy_returns'] < 0]
    downside_deviation = negative_returns.std() * np.sqrt(periods_per_year) if len(negative_returns) > 0 else 0
    sortino_ratio = mean_return / downside_deviation if downside_deviation > 0 else 0
    
    # Trades
    total_trades = int(df['position_change'].sum())
    winning_periods = (df['strategy_returns'] > 0).sum()
    win_rate = winning_periods / len(df) if len(df) > 0 else 0
    
    return {
        'total_return_pct': total_return,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown_pct': max_drawdown,
        'win_rate': win_rate,
        'total_trades': total_trades,
        'volatility': volatility,
        'calmar_ratio': calmar_ratio,
        'sortino_ratio': sortino_ratio
    }


def run_real_data_grid_search():
    """Executa grid search com dados reais."""
    print("🚀 QUALIA GRID SEARCH COM DADOS REAIS - ETAPA C")
    print("=" * 60)
    
    # Inicializa fetcher
    fetcher = BinanceDataFetcher()
    
    # Configuração do grid search
    symbols = ["BTCUSDT", "ETHUSDT"]
    
    # Grid de parâmetros (versão compacta para demonstração)
    price_amps = [2.0, 5.0, 8.0]
    news_amps = [1.5, 4.0, 7.0]
    min_confs = [0.4, 0.6, 0.8]
    
    total_combinations = len(price_amps) * len(news_amps) * len(min_confs) * len(symbols)
    print(f"📊 Total de combinações: {total_combinations}")
    
    results = []
    
    for symbol in symbols:
        print(f"\n📈 Processando {symbol}...")
        
        # Busca dados reais
        df = fetcher.fetch_historical_data(symbol, days=90)
        
        if df.empty:
            print(f"❌ Falha ao obter dados para {symbol}")
            continue
        
        for price_amp in price_amps:
            for news_amp in news_amps:
                for min_conf in min_confs:
                    try:
                        result = enhanced_backtest(df, price_amp, news_amp, min_conf)
                        result.update({
                            'symbol': symbol,
                            'price_amplification': price_amp,
                            'news_amplification': news_amp,
                            'min_confidence': min_conf,
                            'data_points': len(df)
                        })
                        results.append(result)
                        
                        print(f"   ✅ ({price_amp:.1f}, {news_amp:.1f}, {min_conf:.1f}): "
                              f"Return {result['total_return_pct']:.2%}, "
                              f"Sharpe {result['sharpe_ratio']:.3f}")
                        
                    except Exception as e:
                        print(f"   ❌ Erro: {e}")
    
    # Análise dos resultados
    if results:
        print(f"\n" + "="*60)
        print(f"📊 RESULTADOS DO GRID SEARCH COM DADOS REAIS")
        print(f"="*60)
        
        df_results = pd.DataFrame(results)
        
        print(f"\n📈 Estatísticas Gerais:")
        print(f"   • Total de testes: {len(results)}")
        print(f"   • Sharpe médio: {df_results['sharpe_ratio'].mean():.3f}")
        print(f"   • Return médio: {df_results['total_return_pct'].mean():.2%}")
        print(f"   • Drawdown médio: {df_results['max_drawdown_pct'].mean():.2%}")
        print(f"   • Win Rate médio: {df_results['win_rate'].mean():.2%}")
        
        # Top 3 configurações
        top_configs = df_results.nlargest(3, 'sharpe_ratio')
        print(f"\n🏆 TOP 3 CONFIGURAÇÕES (por Sharpe Ratio):")
        for i, (_, row) in enumerate(top_configs.iterrows(), 1):
            print(f"   {i}. {row['symbol']}: ({row['price_amplification']:.1f}, "
                  f"{row['news_amplification']:.1f}, {row['min_confidence']:.1f})")
            print(f"      Return: {row['total_return_pct']:.2%}, "
                  f"Sharpe: {row['sharpe_ratio']:.3f}, "
                  f"Drawdown: {row['max_drawdown_pct']:.2%}")
        
        # Salva resultados
        output_dir = Path("results/real_data_grid_search")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = int(time.time())
        output_file = output_dir / f"real_data_results_{timestamp}.json"
        
        # Salva como JSON
        with open(output_file, 'w') as f:
            json.dump({
                'metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'total_combinations': len(results),
                    'symbols': symbols,
                    'data_source': 'Binance API'
                },
                'results': results
            }, f, indent=2)
        
        print(f"\n💾 Resultados salvos em: {output_file}")
        
        # Salva CSV para análise
        csv_file = output_dir / f"real_data_results_{timestamp}.csv"
        df_results.to_csv(csv_file, index=False)
        print(f"📊 CSV salvo em: {csv_file}")
        
        print(f"\n✅ Grid Search com dados reais concluído com sucesso!")
        
    else:
        print("❌ Nenhum resultado obtido")


if __name__ == "__main__":
    run_real_data_grid_search()
