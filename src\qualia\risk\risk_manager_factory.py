"""
Risk Manager Factory for QUALIA System
Centralizes risk manager creation and prevents duplicate instances.
"""

import logging
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass
import threading

from .manager import QUALIARiskManager, create_risk_manager
from ..config.risk_profiles import RiskProfileSettings

logger = logging.getLogger(__name__)


@dataclass
class RiskManagerConfig:
    """Configuration for risk manager creation"""
    initial_capital: float
    risk_profile: str
    custom_risk_per_trade_pct: Optional[float] = None
    custom_max_position_capital_pct: Optional[float] = None
    profile_specific_config: Optional[Union[Dict[str, Any], RiskProfileSettings]] = None
    min_lot_size: Optional[float] = None
    exchange_id: Optional[str] = None
    symbol: Optional[str] = None


class RiskManagerFactory:
    """
    Factory for creating and managing risk manager instances.
    Ensures single instance per configuration to prevent conflicts.
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not getattr(self, '_initialized', False):
            self._risk_managers: Dict[str, QUALIARiskManager] = {}
            self._configs: Dict[str, RiskManagerConfig] = {}
            self._initialized = True
            logger.info("🏭 RiskManagerFactory initialized")
    
    def create_or_get_risk_manager(
        self,
        config: RiskManagerConfig,
        force_recreate: bool = False
    ) -> QUALIARiskManager:
        """
        Create or retrieve existing risk manager instance.
        
        Args:
            config: Risk manager configuration
            force_recreate: Force creation of new instance even if one exists
            
        Returns:
            QUALIARiskManager instance
        """
        # Create unique key for this configuration
        key = self._create_config_key(config)
        
        # Check if we already have a risk manager for this config
        if key in self._risk_managers and not force_recreate:
            existing_manager = self._risk_managers[key]
            logger.info(f"♻️  Reusing existing risk manager for key: {key}")
            return existing_manager
        
        # Validate configuration before creating
        validation_result = self._validate_config(config)
        if not validation_result.is_valid:
            raise ValueError(f"Invalid risk manager configuration: {validation_result.error_message}")
        
        # Create new risk manager
        try:
            logger.info(f"🔨 Creating new risk manager for key: {key}")
            logger.info(f"   Profile: {config.risk_profile}")
            logger.info(f"   Capital: ${config.initial_capital:,.2f}")
            logger.info(f"   Symbol: {config.symbol or 'ALL'}")
            
            risk_manager = create_risk_manager(
                initial_capital=config.initial_capital,
                risk_profile=config.risk_profile,
                custom_risk_per_trade_pct=config.custom_risk_per_trade_pct,
                custom_max_position_capital_pct=config.custom_max_position_capital_pct,
                profile_specific_config=config.profile_specific_config,
                min_lot_size=config.min_lot_size,
                exchange_id=config.exchange_id,
                symbol=config.symbol
            )
            
            # Store the manager and config
            self._risk_managers[key] = risk_manager
            self._configs[key] = config
            
            logger.info(f"✅ Risk manager created successfully for key: {key}")
            return risk_manager
            
        except Exception as e:
            logger.error(f"❌ Failed to create risk manager for key {key}: {e}")
            raise
    
    def get_risk_manager(self, key: str) -> Optional[QUALIARiskManager]:
        """Get existing risk manager by key"""
        return self._risk_managers.get(key)
    
    def list_risk_managers(self) -> Dict[str, Dict[str, Any]]:
        """List all active risk managers with their configurations"""
        result = {}
        for key, manager in self._risk_managers.items():
            config = self._configs[key]
            result[key] = {
                'risk_profile': config.risk_profile,
                'initial_capital': config.initial_capital,
                'symbol': config.symbol,
                'exchange_id': config.exchange_id,
                'current_capital': getattr(manager, 'current_capital', None),
                'active': True
            }
        return result
    
    def remove_risk_manager(self, key: str) -> bool:
        """Remove risk manager instance"""
        if key in self._risk_managers:
            del self._risk_managers[key]
            del self._configs[key]
            logger.info(f"🗑️  Removed risk manager for key: {key}")
            return True
        return False
    
    def clear_all(self):
        """Clear all risk manager instances"""
        count = len(self._risk_managers)
        self._risk_managers.clear()
        self._configs.clear()
        logger.info(f"🧹 Cleared {count} risk manager instances")
    
    def _create_config_key(self, config: RiskManagerConfig) -> str:
        """Create unique key for risk manager configuration"""
        # Use symbol and exchange as primary identifiers
        # If no symbol specified, use 'global'
        symbol_part = config.symbol or 'global'
        exchange_part = config.exchange_id or 'default'
        profile_part = config.risk_profile
        
        return f"{exchange_part}_{symbol_part}_{profile_part}"
    
    def _validate_config(self, config: RiskManagerConfig) -> 'ValidationResult':
        """Validate risk manager configuration"""
        from ..config.exchange_validator import ValidationResult
        
        # Check required fields
        if config.initial_capital <= 0:
            return ValidationResult(
                is_valid=False,
                exchange_name='risk_manager',
                error_message="Initial capital must be greater than 0"
            )
        
        # Check risk profile
        valid_profiles = ['conservative', 'moderate', 'balanced', 'aggressive', 'custom', 'custom_base']
        if config.risk_profile not in valid_profiles:
            return ValidationResult(
                is_valid=False,
                exchange_name='risk_manager',
                error_message=f"Invalid risk profile: {config.risk_profile}. Valid profiles: {valid_profiles}"
            )
        
        # Check custom percentages
        if config.custom_risk_per_trade_pct is not None:
            if not (0 < config.custom_risk_per_trade_pct <= 10):
                return ValidationResult(
                    is_valid=False,
                    exchange_name='risk_manager',
                    error_message="custom_risk_per_trade_pct must be between 0 and 10"
                )
        
        if config.custom_max_position_capital_pct is not None:
            if not (0 < config.custom_max_position_capital_pct <= 100):
                return ValidationResult(
                    is_valid=False,
                    exchange_name='risk_manager',
                    error_message="custom_max_position_capital_pct must be between 0 and 100"
                )
        
        return ValidationResult(
            is_valid=True,
            exchange_name='risk_manager'
        )


# Global factory instance
_factory_instance = None
_factory_lock = threading.Lock()


def get_risk_manager_factory() -> RiskManagerFactory:
    """Get global risk manager factory instance"""
    global _factory_instance
    if _factory_instance is None:
        with _factory_lock:
            if _factory_instance is None:
                _factory_instance = RiskManagerFactory()
    return _factory_instance


def create_unified_risk_manager(
    initial_capital: float,
    risk_profile: str = "moderate",
    symbol: Optional[str] = None,
    exchange_id: Optional[str] = None,
    custom_risk_per_trade_pct: Optional[float] = None,
    custom_max_position_capital_pct: Optional[float] = None,
    profile_specific_config: Optional[Union[Dict[str, Any], RiskProfileSettings]] = None,
    min_lot_size: Optional[float] = None,
    force_recreate: bool = False
) -> QUALIARiskManager:
    """
    Convenience function to create unified risk manager.
    
    This function ensures that only one risk manager instance exists per configuration,
    preventing the duplicate instances issue identified in the logs.
    
    Args:
        initial_capital: Initial capital amount
        risk_profile: Risk profile name
        symbol: Trading symbol (optional)
        exchange_id: Exchange identifier (optional)
        custom_risk_per_trade_pct: Custom risk per trade percentage
        custom_max_position_capital_pct: Custom max position capital percentage
        profile_specific_config: Profile-specific configuration
        min_lot_size: Minimum lot size
        force_recreate: Force creation of new instance
        
    Returns:
        QUALIARiskManager instance
    """
    factory = get_risk_manager_factory()
    
    config = RiskManagerConfig(
        initial_capital=initial_capital,
        risk_profile=risk_profile,
        custom_risk_per_trade_pct=custom_risk_per_trade_pct,
        custom_max_position_capital_pct=custom_max_position_capital_pct,
        profile_specific_config=profile_specific_config,
        min_lot_size=min_lot_size,
        exchange_id=exchange_id,
        symbol=symbol
    )
    
    return factory.create_or_get_risk_manager(config, force_recreate)


def get_risk_manager_status() -> Dict[str, Any]:
    """Get status of all risk managers"""
    factory = get_risk_manager_factory()
    managers = factory.list_risk_managers()
    
    return {
        'total_managers': len(managers),
        'managers': managers,
        'factory_initialized': factory._initialized
    }
