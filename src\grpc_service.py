#!/usr/bin/env python3
"""
QUALIA gRPC Service - Etapa D
YAA IMPLEMENTATION: Endpoint gRPC para comunicação com o Bayesian Optimizer.
"""

import sys
import asyncio
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import threading
from concurrent.futures import ThreadPoolExecutor
import grpc
from grpc import aio
import grpc_reflection.v1alpha.reflection as reflection

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from src.parameter_tuner import ParameterTunerWorker
except ImportError:
    logging.warning("Não foi possível importar ParameterTunerWorker")
    ParameterTunerWorker = None

# Proto definitions (inline para simplicidade)
PROTO_DEFINITION = """
syntax = "proto3";

package qualia;

service QualiaOptimizer {
    rpc GetCurrentParams(Empty) returns (ParamsResponse);
    rpc UpdateParams(UpdateParamsRequest) returns (StatusResponse);
    rpc GetStatus(Empty) returns (StatusResponse);
    rpc StartOptimization(Empty) returns (StatusResponse);
    rpc StopOptimization(Empty) returns (StatusResponse);
    rpc GetOptimizationHistory(Empty) returns (HistoryResponse);
}

message Empty {}

message ParamsResponse {
    bool success = 1;
    string message = 2;
    double price_amplification = 3;
    double news_amplification = 4;
    double min_confidence = 5;
    string timestamp = 6;
}

message UpdateParamsRequest {
    double price_amplification = 1;
    double news_amplification = 2;
    double min_confidence = 3;
}

message StatusResponse {
    bool success = 1;
    string message = 2;
    bool is_running = 3;
    int32 cycles_completed = 4;
    double best_pnl_ever = 5;
    int32 total_trials = 6;
    string last_update = 7;
    string timestamp = 8;
}

message HistoryResponse {
    bool success = 1;
    string message = 2;
    repeated OptimizationCycle cycles = 3;
}

message OptimizationCycle {
    int32 cycle = 1;
    double best_pnl_24h = 2;
    double price_amplification = 3;
    double news_amplification = 4;
    double min_confidence = 5;
    int32 n_trials = 6;
    double cycle_time_seconds = 7;
    string timestamp = 8;
}
"""

class QualiaOptimizerService:
    """Serviço gRPC para o QUALIA Optimizer."""
    
    def __init__(self):
        self.worker = None
        self.is_initialized = False
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - gRPC - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/grpc_service.log'),
                logging.StreamHandler()
            ]
        )
        
        # Cria diretórios
        Path("logs").mkdir(exist_ok=True)
        Path("results/grpc_service").mkdir(parents=True, exist_ok=True)
        
        self.initialize_worker()
    
    def initialize_worker(self):
        """Inicializa o worker se disponível."""
        
        try:
            if ParameterTunerWorker is not None:
                self.worker = ParameterTunerWorker()
                self.is_initialized = True
                logging.info("Worker inicializado com sucesso")
            else:
                logging.warning("ParameterTunerWorker não disponível")
        except Exception as e:
            logging.error(f"Erro ao inicializar worker: {e}")
    
    async def GetCurrentParams(self, request, context):
        """Retorna os parâmetros atuais."""
        
        try:
            if not self.is_initialized or not self.worker:
                return self._create_params_response(
                    success=False,
                    message="Worker não inicializado"
                )
            
            params = self.worker.get_current_best_params()
            
            if params is None:
                # Retorna parâmetros padrão
                params = {
                    'price_amplification': 2.0,
                    'news_amplification': 7.0,
                    'min_confidence': 0.4
                }
                message = "Usando parâmetros padrão"
            else:
                message = "Parâmetros otimizados atuais"
            
            return self._create_params_response(
                success=True,
                message=message,
                params=params
            )
            
        except Exception as e:
            logging.error(f"Erro em GetCurrentParams: {e}")
            return self._create_params_response(
                success=False,
                message=f"Erro interno: {str(e)}"
            )
    
    async def UpdateParams(self, request, context):
        """Atualiza parâmetros manualmente."""
        
        try:
            params = {
                'price_amplification': request.price_amplification,
                'news_amplification': request.news_amplification,
                'min_confidence': request.min_confidence
            }
            
            # Validação básica
            if not (0.1 <= params['price_amplification'] <= 20.0):
                return self._create_status_response(
                    success=False,
                    message="price_amplification deve estar entre 0.1 e 20.0"
                )
            
            if not (0.1 <= params['news_amplification'] <= 20.0):
                return self._create_status_response(
                    success=False,
                    message="news_amplification deve estar entre 0.1 e 20.0"
                )
            
            if not (0.1 <= params['min_confidence'] <= 0.9):
                return self._create_status_response(
                    success=False,
                    message="min_confidence deve estar entre 0.1 e 0.9"
                )
            
            # Salva parâmetros
            params_file = Path("results/grpc_service/manual_params.json")
            with open(params_file, 'w') as f:
                json.dump({
                    'params': params,
                    'timestamp': datetime.now().isoformat(),
                    'source': 'manual_update'
                }, f, indent=2)
            
            logging.info(f"Parâmetros atualizados manualmente: {params}")
            
            return self._create_status_response(
                success=True,
                message="Parâmetros atualizados com sucesso"
            )
            
        except Exception as e:
            logging.error(f"Erro em UpdateParams: {e}")
            return self._create_status_response(
                success=False,
                message=f"Erro interno: {str(e)}"
            )
    
    async def GetStatus(self, request, context):
        """Retorna status do otimizador."""
        
        try:
            if not self.is_initialized or not self.worker:
                return self._create_status_response(
                    success=False,
                    message="Worker não inicializado",
                    is_running=False
                )
            
            status = self.worker.get_status()
            
            return self._create_status_response(
                success=True,
                message="Status obtido com sucesso",
                is_running=status['is_running'],
                cycles_completed=status['stats']['cycles_completed'],
                best_pnl_ever=status['stats']['best_pnl_ever'],
                total_trials=status['stats']['total_trials'],
                last_update=status['stats']['last_update']
            )
            
        except Exception as e:
            logging.error(f"Erro em GetStatus: {e}")
            return self._create_status_response(
                success=False,
                message=f"Erro interno: {str(e)}"
            )
    
    async def StartOptimization(self, request, context):
        """Inicia otimização."""
        
        try:
            if not self.is_initialized or not self.worker:
                return self._create_status_response(
                    success=False,
                    message="Worker não inicializado"
                )
            
            if self.worker.is_running:
                return self._create_status_response(
                    success=False,
                    message="Otimização já está rodando"
                )
            
            self.worker.start()
            
            return self._create_status_response(
                success=True,
                message="Otimização iniciada com sucesso",
                is_running=True
            )
            
        except Exception as e:
            logging.error(f"Erro em StartOptimization: {e}")
            return self._create_status_response(
                success=False,
                message=f"Erro ao iniciar otimização: {str(e)}"
            )
    
    async def StopOptimization(self, request, context):
        """Para otimização."""
        
        try:
            if not self.is_initialized or not self.worker:
                return self._create_status_response(
                    success=False,
                    message="Worker não inicializado"
                )
            
            if not self.worker.is_running:
                return self._create_status_response(
                    success=False,
                    message="Otimização não está rodando"
                )
            
            self.worker.stop()
            
            return self._create_status_response(
                success=True,
                message="Otimização parada com sucesso",
                is_running=False
            )
            
        except Exception as e:
            logging.error(f"Erro em StopOptimization: {e}")
            return self._create_status_response(
                success=False,
                message=f"Erro ao parar otimização: {str(e)}"
            )
    
    async def GetOptimizationHistory(self, request, context):
        """Retorna histórico de otimização."""
        
        try:
            # Busca arquivos de resultados
            results_dir = Path("results/bayesian_optimization")
            history_files = list(results_dir.glob("optimization_results_*.json"))
            
            if not history_files:
                return self._create_history_response(
                    success=False,
                    message="Nenhum histórico encontrado"
                )
            
            # Carrega o arquivo mais recente
            latest_file = max(history_files, key=lambda x: x.stat().st_mtime)
            
            with open(latest_file, 'r') as f:
                data = json.load(f)
            
            history = data.get('optimization_history', [])
            
            return self._create_history_response(
                success=True,
                message=f"Histórico carregado: {len(history)} ciclos",
                cycles=history
            )
            
        except Exception as e:
            logging.error(f"Erro em GetOptimizationHistory: {e}")
            return self._create_history_response(
                success=False,
                message=f"Erro interno: {str(e)}"
            )
    
    def _create_params_response(self, success: bool, message: str, params: Optional[Dict] = None):
        """Cria resposta de parâmetros."""
        
        if params is None:
            params = {'price_amplification': 0, 'news_amplification': 0, 'min_confidence': 0}
        
        # Simula resposta gRPC (sem protobuf real)
        return {
            'success': success,
            'message': message,
            'price_amplification': params.get('price_amplification', 0),
            'news_amplification': params.get('news_amplification', 0),
            'min_confidence': params.get('min_confidence', 0),
            'timestamp': datetime.now().isoformat()
        }
    
    def _create_status_response(self, success: bool, message: str, **kwargs):
        """Cria resposta de status."""
        
        response = {
            'success': success,
            'message': message,
            'is_running': kwargs.get('is_running', False),
            'cycles_completed': kwargs.get('cycles_completed', 0),
            'best_pnl_ever': kwargs.get('best_pnl_ever', 0.0),
            'total_trials': kwargs.get('total_trials', 0),
            'last_update': kwargs.get('last_update', ''),
            'timestamp': datetime.now().isoformat()
        }
        
        return response
    
    def _create_history_response(self, success: bool, message: str, cycles: Optional[list] = None):
        """Cria resposta de histórico."""
        
        if cycles is None:
            cycles = []
        
        return {
            'success': success,
            'message': message,
            'cycles': cycles
        }


class QualiaGRPCServer:
    """Servidor gRPC para QUALIA."""
    
    def __init__(self, port: int = 50051):
        self.port = port
        self.server = None
        self.service = QualiaOptimizerService()
    
    async def start_server(self):
        """Inicia o servidor gRPC."""
        
        try:
            # Cria servidor
            self.server = aio.server(ThreadPoolExecutor(max_workers=10))
            
            # Adiciona serviço (simulado - sem protobuf real)
            # Em implementação real, usaria: add_QualiaOptimizerServicer_to_server
            
            # Adiciona reflexão
            listen_addr = f'[::]:{self.port}'
            self.server.add_insecure_port(listen_addr)
            
            logging.info(f"Iniciando servidor gRPC na porta {self.port}")
            
            await self.server.start()
            
            logging.info(f"✅ Servidor gRPC iniciado: {listen_addr}")
            
            # Mantém servidor rodando
            await self.server.wait_for_termination()
            
        except Exception as e:
            logging.error(f"Erro no servidor gRPC: {e}")
            raise
    
    async def stop_server(self):
        """Para o servidor gRPC."""
        
        if self.server:
            logging.info("Parando servidor gRPC...")
            await self.server.stop(grace=5)
            logging.info("✅ Servidor gRPC parado")


async def main():
    """Função principal do servidor gRPC."""
    
    print("🌐 QUALIA gRPC SERVICE - ETAPA D")
    print("=" * 50)
    
    server = QualiaGRPCServer(port=50051)
    
    try:
        print("🚀 Iniciando servidor gRPC...")
        print("📡 Porta: 50051")
        print("🔧 Serviços disponíveis:")
        print("   • GetCurrentParams")
        print("   • UpdateParams")
        print("   • GetStatus")
        print("   • StartOptimization")
        print("   • StopOptimization")
        print("   • GetOptimizationHistory")
        
        await server.start_server()
        
    except KeyboardInterrupt:
        print("\n🛑 Interrompido pelo usuário")
    except Exception as e:
        print(f"❌ Erro: {e}")
    finally:
        await server.stop_server()
        print("👋 Servidor finalizado")


if __name__ == "__main__":
    asyncio.run(main())
