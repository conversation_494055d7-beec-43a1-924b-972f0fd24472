# Critical Loops Benchmark

Este documento resume resultados de benchmarks focados em trechos de código com alto custo computacional.

```
pytest benchmarks/critical_loops.py --benchmark-only -k "critical-loops" -q
```

Resultados de referência obtidos em ambiente local:

| Teste | Tempo médio |
|-------|-------------|
| `test_liquidity_backtest_loop` | ~6.5 ms |
| `test_liquidity_backtest_vectorized` | ~1.2 ms |
| `test_renyi_naive` | ~2.8 µs |
| `test_renyi_vectorized` | ~0.6 µs |
| `test_position_size_batch_loop` | ~4.5 ms |
| `test_position_size_batch_vectorized` | ~0.7 ms |

A versão vetorizada reduziu o tempo em aproximadamente 80% ou mais, dependendo do caso.
