import timeit

from qualia.utils.simulator import simulate_returns


def benchmark(num_paths: int = 1000, horizon: int = 1000, repeats: int = 3) -> None:
    """Run benchmark comparing serial and parallel Monte Carlo simulation."""
    serial = min(
        timeit.repeat(
            lambda: simulate_returns(
                mu=0.0, sigma=1.0, horizon=horizon, num_paths=num_paths
            ),
            repeat=repeats,
            number=1,
        )
    )

    parallel = min(
        timeit.repeat(
            lambda: simulate_returns(
                mu=0.0,
                sigma=1.0,
                horizon=horizon,
                num_paths=num_paths,
                parallel=True,
            ),
            repeat=repeats,
            number=1,
        )
    )

    print(f"Serial: {serial:.6f}s, Parallel: {parallel:.6f}s")


if __name__ == "__main__":
    benchmark()
