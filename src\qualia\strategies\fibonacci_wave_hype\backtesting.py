"""
Módulo de backtesting para a estratégia Fibonacci Wave Hype (FWH).

Implementa backtests comparativos com outras estratégias do QUALIA,
análise de performance e métricas quântico-holográficas.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import asyncio

from ...utils.logger import get_logger
from ...config.config_manager import ConfigManager
from ...strategies.strategy_factory import StrategyFactory
from ...strategies.nova_estrategia_qualia import QualiaTSVFStrategy
from ...strategies.enhanced_quantum_momentum import EnhancedQuantumMomentumStrategy
from ...farsight.holographic_extension import HolographicFarsightEngine
from .core import FibonacciWaveHypeStrategy

logger = get_logger(__name__)


class FWHBacktestEngine:
    """Engine de backtesting para estratégia FWH."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        self.config_manager = config_manager or ConfigManager()
        self.config_manager.load()
        
        # Configurações FWH
        self.fwh_config = self.config_manager.data.get("fibonacci_wave_hype_config", {})
        self.backtest_config = self.fwh_config.get("backtesting", {})
        
        # Estratégias para comparação
        self.benchmark_strategies = self.backtest_config.get(
            "benchmark_strategies", 
            ["QualiaTSVFStrategy", "EnhancedQuantumMomentumStrategy"]
        )
        
        # Métricas de performance
        self.performance_metrics = self.backtest_config.get(
            "performance_metrics",
            ["sharpe_ratio", "max_drawdown", "win_rate", "profit_factor"]
        )
        
        logger.info(f"[FWH-Backtest] Inicializado com {len(self.benchmark_strategies)} benchmarks")
    
    def run_comparative_backtest(
        self,
        market_data: pd.DataFrame,
        symbol: str = "BTC/USDT",
        timeframe: str = "1h",
        initial_capital: float = 10000.0
    ) -> Dict[str, Any]:
        """
        Executa backtest comparativo entre FWH e estratégias benchmark.
        
        Args:
            market_data: Dados históricos de mercado
            symbol: Símbolo do ativo
            timeframe: Timeframe dos dados
            initial_capital: Capital inicial
            
        Returns:
            Resultados comparativos do backtest
        """
        logger.info(f"[FWH-Backtest] Iniciando backtest comparativo para {symbol}")
        
        results = {}
        
        # 1. Backtest da estratégia FWH
        fwh_results = self._backtest_fwh_strategy(
            market_data, symbol, timeframe, initial_capital
        )
        results["FibonacciWaveHypeStrategy"] = fwh_results
        
        # 2. Backtest das estratégias benchmark
        for strategy_name in self.benchmark_strategies:
            try:
                benchmark_results = self._backtest_benchmark_strategy(
                    strategy_name, market_data, symbol, timeframe, initial_capital
                )
                results[strategy_name] = benchmark_results
                
            except Exception as e:
                logger.error(f"[FWH-Backtest] Erro no benchmark {strategy_name}: {e}")
                results[strategy_name] = {"error": str(e)}
        
        # 3. Análise comparativa
        comparative_analysis = self._analyze_comparative_results(results)
        results["comparative_analysis"] = comparative_analysis
        
        logger.info(f"[FWH-Backtest] Backtest concluído para {len(results)} estratégias")
        return results
    
    def _backtest_fwh_strategy(
        self,
        market_data: pd.DataFrame,
        symbol: str,
        timeframe: str,
        initial_capital: float
    ) -> Dict[str, Any]:
        """Executa backtest específico da estratégia FWH."""
        
        try:
            # Inicializa estratégia FWH
            fwh_params = self.fwh_config.get("params", {})
            strategy = FibonacciWaveHypeStrategy(
                symbol=symbol,
                timeframe=timeframe,
                parameters=fwh_params
            )
            
            # Inicializa com componentes holográficos (mock para backtest)
            holographic_engine = self._create_mock_holographic_engine()
            strategy.initialize({"holographic_engine": holographic_engine})
            
            # Executa simulação
            portfolio_results = self._simulate_trading(
                strategy, market_data, initial_capital
            )
            
            # Calcula métricas
            metrics = self._calculate_performance_metrics(
                portfolio_results, market_data
            )
            
            return {
                "portfolio_results": portfolio_results,
                "performance_metrics": metrics,
                "strategy_params": fwh_params,
                "total_trades": len(portfolio_results.get("trades", [])),
                "final_capital": portfolio_results.get("final_capital", initial_capital)
            }
            
        except Exception as e:
            logger.error(f"[FWH-Backtest] Erro no backtest FWH: {e}")
            return {"error": str(e)}
    
    def _backtest_benchmark_strategy(
        self,
        strategy_name: str,
        market_data: pd.DataFrame,
        symbol: str,
        timeframe: str,
        initial_capital: float
    ) -> Dict[str, Any]:
        """Executa backtest de estratégia benchmark."""
        
        try:
            # Cria estratégia via factory
            context = {
                "symbol": symbol,
                "timeframe": timeframe,
                "qpm_instance": None  # Mock para backtest
            }
            
            strategy = StrategyFactory.create_strategy(
                alias=strategy_name,
                context=context,
                config_manager=self.config_manager
            )
            
            # Executa simulação
            portfolio_results = self._simulate_trading(
                strategy, market_data, initial_capital
            )
            
            # Calcula métricas
            metrics = self._calculate_performance_metrics(
                portfolio_results, market_data
            )
            
            return {
                "portfolio_results": portfolio_results,
                "performance_metrics": metrics,
                "total_trades": len(portfolio_results.get("trades", [])),
                "final_capital": portfolio_results.get("final_capital", initial_capital)
            }
            
        except Exception as e:
            logger.error(f"[FWH-Backtest] Erro no benchmark {strategy_name}: {e}")
            return {"error": str(e)}
    
    def _simulate_trading(
        self,
        strategy: Any,
        market_data: pd.DataFrame,
        initial_capital: float
    ) -> Dict[str, Any]:
        """Simula execução de trades baseada nos sinais da estratégia."""
        
        capital = initial_capital
        position = 0.0
        trades = []
        equity_curve = []
        
        for i in range(len(market_data)):
            current_data = market_data.iloc[:i+1]
            
            if len(current_data) < 20:  # Dados insuficientes
                equity_curve.append(capital)
                continue
            
            try:
                # Gera sinal da estratégia
                if hasattr(strategy, 'generate_signal'):
                    from ...strategies.strategy_interface import TradingContext
                    context = TradingContext(
                        data=current_data,
                        symbol=strategy.symbol if hasattr(strategy, 'symbol') else "BTC/USDT",
                        timeframe=strategy.timeframe if hasattr(strategy, 'timeframe') else "1h"
                    )
                    signals = strategy.generate_signal(context)
                else:
                    # Fallback para estratégias antigas
                    signals = pd.DataFrame()
                
                # Processa sinais
                if not signals.empty and len(signals) > 0:
                    latest_signal = signals.iloc[-1]
                    signal_type = latest_signal.get("signal", "HOLD")
                    confidence = latest_signal.get("confidence", 0.0)
                    
                    current_price = current_data["close"].iloc[-1]
                    
                    # Executa trade baseado no sinal
                    if signal_type == "BUY" and position <= 0 and confidence > 0.5:
                        # Compra
                        position_size = capital * 0.1 * confidence  # 10% max com ajuste por confiança
                        shares = position_size / current_price
                        position += shares
                        capital -= position_size
                        
                        trades.append({
                            "timestamp": current_data.index[-1],
                            "type": "BUY",
                            "price": current_price,
                            "shares": shares,
                            "confidence": confidence
                        })
                        
                    elif signal_type == "SELL" and position > 0:
                        # Venda
                        sell_value = position * current_price
                        capital += sell_value
                        
                        trades.append({
                            "timestamp": current_data.index[-1],
                            "type": "SELL",
                            "price": current_price,
                            "shares": position,
                            "confidence": confidence
                        })
                        
                        position = 0.0
                
                # Atualiza equity curve
                current_equity = capital + (position * current_data["close"].iloc[-1])
                equity_curve.append(current_equity)
                
            except Exception as e:
                logger.debug(f"[FWH-Backtest] Erro na simulação step {i}: {e}")
                equity_curve.append(capital)
        
        return {
            "trades": trades,
            "equity_curve": equity_curve,
            "final_capital": equity_curve[-1] if equity_curve else initial_capital,
            "final_position": position
        }
    
    def _calculate_performance_metrics(
        self,
        portfolio_results: Dict[str, Any],
        market_data: pd.DataFrame
    ) -> Dict[str, float]:
        """Calcula métricas de performance do portfolio."""
        
        equity_curve = portfolio_results.get("equity_curve", [])
        trades = portfolio_results.get("trades", [])
        
        if not equity_curve or len(equity_curve) < 2:
            return {"error": "Dados insuficientes para métricas"}
        
        # Converte para numpy array
        equity = np.array(equity_curve)
        returns = np.diff(equity) / equity[:-1]
        
        # Benchmark (buy and hold)
        market_returns = market_data["close"].pct_change().dropna()
        
        metrics = {}
        
        try:
            # Sharpe Ratio
            if len(returns) > 0 and np.std(returns) > 0:
                metrics["sharpe_ratio"] = np.mean(returns) / np.std(returns) * np.sqrt(252)
            else:
                metrics["sharpe_ratio"] = 0.0
            
            # Maximum Drawdown
            peak = np.maximum.accumulate(equity)
            drawdown = (equity - peak) / peak
            metrics["max_drawdown"] = abs(np.min(drawdown))
            
            # Win Rate
            if trades:
                winning_trades = sum(1 for trade in trades if trade.get("type") == "SELL")
                metrics["win_rate"] = winning_trades / len(trades) if len(trades) > 0 else 0.0
            else:
                metrics["win_rate"] = 0.0
            
            # Total Return
            metrics["total_return"] = (equity[-1] - equity[0]) / equity[0]
            
            # Volatility
            metrics["volatility"] = np.std(returns) * np.sqrt(252) if len(returns) > 0 else 0.0
            
            # Alpha vs Market
            if len(market_returns) > 0 and len(returns) > 0:
                market_return = np.mean(market_returns) * 252
                strategy_return = np.mean(returns) * 252
                metrics["alpha"] = strategy_return - market_return
            else:
                metrics["alpha"] = 0.0
            
        except Exception as e:
            logger.error(f"[FWH-Backtest] Erro no cálculo de métricas: {e}")
            metrics["error"] = str(e)
        
        return metrics
    
    def _analyze_comparative_results(
        self, 
        results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analisa resultados comparativos entre estratégias."""
        
        analysis = {
            "ranking": {},
            "best_strategy": None,
            "performance_comparison": {},
            "statistical_significance": {}
        }
        
        # Extrai métricas de cada estratégia
        strategy_metrics = {}
        for strategy_name, result in results.items():
            if "performance_metrics" in result:
                strategy_metrics[strategy_name] = result["performance_metrics"]
        
        if not strategy_metrics:
            return {"error": "Nenhuma métrica válida encontrada"}
        
        # Ranking por Sharpe Ratio
        sharpe_ranking = sorted(
            strategy_metrics.items(),
            key=lambda x: x[1].get("sharpe_ratio", 0),
            reverse=True
        )
        
        analysis["ranking"]["sharpe_ratio"] = [name for name, _ in sharpe_ranking]
        analysis["best_strategy"] = sharpe_ranking[0][0] if sharpe_ranking else None
        
        # Comparação de performance
        for metric in self.performance_metrics:
            analysis["performance_comparison"][metric] = {
                name: metrics.get(metric, 0)
                for name, metrics in strategy_metrics.items()
            }
        
        return analysis
    
    def _create_mock_holographic_engine(self) -> Any:
        """Cria mock do HolographicFarsightEngine para backtesting."""
        from unittest.mock import Mock

        # Tenta importar CollectiveMindState
        try:
            from ...custom_types import CollectiveMindState
        except ImportError:
            # Fallback se não conseguir importar
            from dataclasses import dataclass
            from typing import Dict, Any

            @dataclass
            class CollectiveMindState:
                timestamp: float
                dominant_narrative: str
                persona_impact: Dict[str, Any]
        
        engine = Mock()
        
        # Mock CollectiveMindState com sentiment neutro
        collective_state = CollectiveMindState(
            timestamp=datetime.now().timestamp(),
            dominant_narrative="BACKTEST_NEUTRAL",
            persona_impact={
                "RetailCluster": {"sentiment": 0.0, "confidence_boost": 0.5},
                "InstitutionalCluster": {"sentiment": 0.0, "confidence_boost": 0.5},
                "MomentumQuant": {"sentiment": 0.0, "confidence_boost": 0.5}
            }
        )
        
        engine.generate_collective_mind_state.return_value = collective_state
        engine.run_holographic_analysis.return_value = []
        
        return engine


def run_fwh_backtest(
    market_data: pd.DataFrame,
    symbol: str = "BTC/USDT",
    timeframe: str = "1h",
    config_path: Optional[str] = None
) -> Dict[str, Any]:
    """
    Função utilitária para executar backtest FWH.
    
    Args:
        market_data: Dados históricos de mercado
        symbol: Símbolo do ativo
        timeframe: Timeframe dos dados
        config_path: Caminho para arquivo de configuração
        
    Returns:
        Resultados do backtest
    """
    config_manager = ConfigManager(config_path) if config_path else None
    engine = FWHBacktestEngine(config_manager)
    
    return engine.run_comparative_backtest(
        market_data=market_data,
        symbol=symbol,
        timeframe=timeframe
    )
