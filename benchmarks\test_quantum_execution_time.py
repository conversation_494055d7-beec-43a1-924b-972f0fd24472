import pytest

pytest.importorskip("pytest_benchmark")
from qiskit import QuantumCircuit, transpile
from qiskit_aer import AerSimulator


def execute_simple_circuit(n_qubits: int = 3):
    qc = QuantumCircuit(n_qubits)
    qc.h(0)
    for i in range(1, n_qubits):
        qc.cx(0, i)
    qc.measure_all()
    simulator = AerSimulator()
    compiled = transpile(qc, simulator)
    result = simulator.run(compiled, shots=1).result()
    return result


@pytest.mark.benchmark(group="quantum-execution")
def test_simple_circuit_execution_time(benchmark):
    result = benchmark(execute_simple_circuit, n_qubits=3)
    assert result.success
