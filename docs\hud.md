# HUD do QUALIA

A HUD (Heads-Up Display) exibe em tempo real o estado do sistema QUALIA. O
`QUALIARealTimeTrader` utiliza `HUDManager.on_tick` a cada ciclo de mercado,
coletando métricas e repassando-as ao `DynamicLogoEngine` para atualizar o logotipo
dinâmico. A invocação ocorre dentro do loop principal
(`trading_loops._main_loop`) logo após a atualização de mercado, garantindo
uma única emissão por ciclo. Chamadas duplicadas em
`_run_metacognition_cycle` foram removidas. O estado enviado ao frontend segue
a estrutura resumida abaixo:

```json
{
  "timestamp": "2024-06-17T10:00:00Z",
  "hue": 151.2,
  "brightness": 0.91,
  "embedding_norm": 0.87,
  "liquidity_buckets": [0.2, 0.3, 0.5],
  "trend_strength": 0.8,
  "delta_entropy": 0.05
}
```

Esses campos permitem que a interface represente a MISSÃO do trader e suas métricas atuais de forma clara. 

## Execução rápida

Para iniciar o sistema em modo ao vivo com a visualização ativa:

```bash
python -m qualia.qualia_trading_system --mode live --enable_hud True
```

O comando inicia o loop principal e habilita o painel HUD em `http://localhost:5001/visualization/`.

O estado da visualização agora é gerenciado pela dataclass ``VisualizationState``.
Ela possui os campos ``active``, ``data_source``, ``last_data`` e ``error`` e pode
ser acessada via ``QUALIAConsciousnessVisualization.state``.

Para executar sem a interface holográfica basta desativar a flag:

```bash
python -m qualia.qualia_trading_system --mode live --enable_hud False
```

Essa opção é útil em ambientes sem suporte a OpenCV ou quando já existe a variável de ambiente `QUALIA_NO_DISPLAY`.

## Suporte WebGPU

Quando disponível, a HUD pode utilizar WebGPU para renderização mais eficiente. Ative a opção com a flag ``--use_webgpu``:

```bash
python -m qualia.qualia_trading_system --mode live --enable_hud True --use_webgpu True
```

O frontend detecta se o navegador suporta WebGPU. Caso contrário, o sistema recai automaticamente para WebGL sem intervenção adicional.

## Estratégia de Frustum Culling

Desde a versão 0.1.47 a HUD passou a utilizar uma hierarquia espacial baseada em **Octree** para filtrar objetos visíveis. O frustum da câmera é testado contra a árvore e somente os nós intersectados retornam seus objetos para renderização.

Um benchmark simples com 1000 objetos demonstrou redução no tempo médio de culling de **0,388&nbsp;ms** para **0,158&nbsp;ms** por quadro. O script utilizado encontra-se em `benchmarks/ui/frustum_culling_benchmark.js`.

Essa otimização reduz a carga de processamento em cenas complexas e mantém a taxa de quadros mais estável.

## Métricas de FPS e latência

O frontend envia periodicamente para o endpoint ``/api/hud/metrics`` a média de
quadros por segundo (`avgFPS`) e a latência de renderização (`latency`). Esses
valores são armazenados no backend pelo módulo
``qualia.monitor.performance``. Cada entrada é representada por um
``PerformanceRecord`` contendo ``timestamp``, ``avg_fps`` e ``latency_ms``.

As amostras ficam disponíveis em um buffer circular com capacidade padrão de
10&nbsp;000 registros. O tamanho do buffer pode ser ajustado chamando
``set_buffer_maxlen``. Para registrar novas métricas utilize
``record_performance(avg_fps, latency_ms)`` ou envie um ``POST`` ao endpoint
citado. A leitura dos dados pode ser feita com ``get_records()`` ou via
``GET /api/hud/metrics``.

Essas métricas também são encaminhadas ao ``MetricsCollector`` do sistema,
permitindo integração com serviços como StatsD ou Prometheus.
