#!/usr/bin/env python3
"""
Script para testar a correção dos thresholds específicos por timeframe
na estratégia FibonacciWaveHypeStrategy.
"""

import sys
import os
import yaml
import logging
from pathlib import Path

# Adicionar o diretório raiz ao path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configurar logging simples (sem emojis para evitar problemas de encoding)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_threshold_fix.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def test_config_file_loading():
    """Testa se o arquivo de configuração está sendo carregado corretamente"""

    logger.info("TESTANDO CARREGAMENTO DO ARQUIVO DE CONFIGURACAO")
    logger.info("=" * 60)

    try:
        # Verificar se o arquivo existe
        config_path = Path(__file__).parent.parent / "config" / "fwh_scalp_config.yaml"

        if not config_path.exists():
            logger.error(f"ERRO: Arquivo de configuracao nao encontrado: {config_path}")
            return False

        logger.info(f"Arquivo encontrado: {config_path}")

        # Carregar o arquivo YAML
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)

        logger.info("Arquivo YAML carregado com sucesso")

        # Verificar estrutura esperada
        if 'fibonacci_wave_hype_config' not in config_data:
            logger.error("ERRO: Chave 'fibonacci_wave_hype_config' nao encontrada")
            return False

        fwh_config = config_data['fibonacci_wave_hype_config']

        if 'params' not in fwh_config:
            logger.error("ERRO: Chave 'params' nao encontrada em fibonacci_wave_hype_config")
            return False

        params = fwh_config['params']

        if 'timeframe_specific' not in params:
            logger.error("ERRO: Chave 'timeframe_specific' nao encontrada em params")
            return False

        timeframe_specific = params['timeframe_specific']
        logger.info(f"Configuracoes timeframe_specific encontradas: {list(timeframe_specific.keys())}")

        # Verificar thresholds esperados
        expected_thresholds = {
            "1m": 0.42,
            "5m": 0.36,
            "15m": 0.28,
            "1h": 0.22
        }

        results = {}

        for timeframe, expected_threshold in expected_thresholds.items():
            if timeframe in timeframe_specific:
                tf_config = timeframe_specific[timeframe]
                if 'hype_threshold' in tf_config:
                    actual_threshold = tf_config['hype_threshold']
                    logger.info(f"{timeframe}: threshold = {actual_threshold} (esperado: {expected_threshold})")

                    if abs(actual_threshold - expected_threshold) < 0.001:
                        results[timeframe] = "PASS"
                    else:
                        results[timeframe] = "FAIL"
                        logger.error(f"ERRO: Threshold incorreto para {timeframe}")
                else:
                    results[timeframe] = "MISSING_THRESHOLD"
                    logger.error(f"ERRO: hype_threshold nao encontrado para {timeframe}")
            else:
                results[timeframe] = "MISSING_TIMEFRAME"
                logger.error(f"ERRO: Configuracao para {timeframe} nao encontrada")

        # Resumo
        logger.info("\nRESUMO DOS TESTES:")
        passed = sum(1 for result in results.values() if result == "PASS")
        total = len(results)

        for timeframe, result in results.items():
            status = "OK" if result == "PASS" else "ERRO"
            logger.info(f"  {timeframe}: {status} ({result})")

        logger.info(f"\nResultado: {passed}/{total} testes passaram")

        return passed == total

    except Exception as e:
        logger.error(f"ERRO ao carregar configuracao: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False


def test_script_integration():
    """Testa se o script de scalping está usando os thresholds corretos"""

    logger.info("\nTESTANDO INTEGRACAO COM SCRIPT DE SCALPING")
    logger.info("=" * 50)

    try:
        # Verificar se o script existe
        script_path = Path(__file__).parent / "run_fwh_scalp_paper_trading.py"

        if not script_path.exists():
            logger.error(f"ERRO: Script nao encontrado: {script_path}")
            return False

        logger.info(f"Script encontrado: {script_path}")

        # Ler o script e verificar se está usando o config_manager correto
        with open(script_path, 'r', encoding='utf-8') as f:
            script_content = f.read()

        # Verificar se as correções estão presentes
        corrections_found = []

        if "from qualia.config.config_manager import ConfigManager" in script_content:
            corrections_found.append("Import ConfigManager")

        if "config_manager = ConfigManager(config_path=self.config_path)" in script_content:
            corrections_found.append("ConfigManager initialization")

        if "config_manager=config_manager" in script_content:
            corrections_found.append("ConfigManager passed to strategy")

        logger.info(f"Correcoes encontradas no script: {len(corrections_found)}/3")
        for correction in corrections_found:
            logger.info(f"  - {correction}")

        return len(corrections_found) == 3

    except Exception as e:
        logger.error(f"ERRO ao verificar script: {e}")
        return False


def main():
    """Função principal de teste"""
    logger.info("Iniciando teste de correcao dos thresholds...")
    logger.info("=" * 60)

    # Teste 1: Verificar carregamento do arquivo de configuração
    test1_success = test_config_file_loading()

    # Teste 2: Verificar integração com script
    test2_success = test_script_integration()

    # Resumo final
    logger.info("\n" + "=" * 60)
    logger.info("RESUMO FINAL DOS TESTES")
    logger.info("=" * 60)

    logger.info(f"Teste 1 - Configuracao YAML: {'PASSOU' if test1_success else 'FALHOU'}")
    logger.info(f"Teste 2 - Integracao Script: {'PASSOU' if test2_success else 'FALHOU'}")

    overall_success = test1_success and test2_success

    logger.info(f"\nLog detalhado salvo em: test_threshold_fix.log")

    if overall_success:
        logger.info("TESTE CONCLUIDO COM SUCESSO!")
        logger.info("Os thresholds especificos por timeframe devem estar funcionando corretamente.")
        return 0
    else:
        logger.error("TESTE FALHOU!")
        logger.error("Verifique os erros acima e corrija os problemas identificados.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
