#!/usr/bin/env python3
"""
Teste simples para verificar se os imports estão funcionando
"""

import sys
import os

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def test_basic_imports():
    """Testa imports básicos do sistema"""
    
    print("🧪 TESTE DE IMPORTS BÁSICOS")
    print("=" * 50)
    
    try:
        print("1️⃣ Testando import do config...")
        from qualia import config
        print("✅ Config importado")
        
        print("2️⃣ Testando import do KucoinIntegration...")
        from qualia.market.kucoin_integration import KucoinIntegration
        print("✅ KucoinIntegration importado")
        
        print("3️⃣ Testando import do ccxt...")
        import ccxt.async_support as ccxt
        kucoin_class = getattr(ccxt, 'kucoin', None)
        print(f"✅ CCXT KuCoin: {kucoin_class}")
        
        if 'Dummy' in str(kucoin_class):
            print("🚨 PROBLEMA: Ainda usando DummyExchange!")
        else:
            print("✅ Usando classe real")
        
        print("4️⃣ Testando criação de integração...")
        kucoin = KucoinIntegration(
            api_key=None,
            api_secret=None,
            password=None,
            use_websocket=False
        )
        print("✅ Integração criada")
        
        print("\n🎉 TODOS OS IMPORTS FUNCIONANDO!")
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste de imports: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_basic_imports()
    if success:
        print("\n✅ Sistema pronto para uso!")
    else:
        print("\n❌ Sistema ainda tem problemas!") 