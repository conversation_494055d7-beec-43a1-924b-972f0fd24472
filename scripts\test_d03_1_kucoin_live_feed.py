#!/usr/bin/env python3
"""
D-03.1: Test KuCoin Live Feed with Real Credentials

Script abrangente para testar todos os componentes do sistema de live feed KuCoin.
Valida arquitetura, componentes, integração e documenta requisitos para credenciais reais.

YAA REFINEMENT: Teste completo do sistema de live feed com validação de todos os componentes.
"""

import sys
import os
import asyncio
import time
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class TestResult:
    """Resultado de um teste individual."""
    name: str
    success: bool
    duration: float
    details: str
    error: Optional[str] = None


@dataclass
class ComponentValidation:
    """Validação de um componente."""
    component: str
    available: bool
    version: Optional[str] = None
    details: str = ""


class D031LiveFeedTester:
    """Testador completo do sistema de live feed KuCoin."""
    
    def __init__(self):
        self.test_results: List[TestResult] = []
        self.component_validations: List[ComponentValidation] = []
        self.has_real_credentials = False
        self.credentials_status = {}
        
    def check_credentials(self) -> Dict[str, bool]:
        """Verifica status das credenciais."""
        credentials = {
            'KUCOIN_API_KEY': bool(os.getenv('KUCOIN_API_KEY')),
            'KUCOIN_API_SECRET': bool(os.getenv('KUCOIN_API_SECRET')),
            'KUCOIN_SECRET_KEY': bool(os.getenv('KUCOIN_SECRET_KEY')),
            'KUCOIN_PASSPHRASE': bool(os.getenv('KUCOIN_PASSPHRASE')),
        }
        
        self.credentials_status = credentials
        self.has_real_credentials = any([
            credentials['KUCOIN_API_KEY'] and credentials['KUCOIN_PASSPHRASE'] and 
            (credentials['KUCOIN_API_SECRET'] or credentials['KUCOIN_SECRET_KEY'])
        ])
        
        return credentials
    
    def validate_component_imports(self) -> bool:
        """Valida se todos os componentes podem ser importados."""
        components = [
            ("KuCoinFeed", "qualia.live_feed.kucoin_feed", "KuCoinFeed"),
            ("DataNormalizer", "qualia.live_feed.data_normalizer", "DataNormalizer"),
            ("FeedManager", "qualia.live_feed.feed_manager", "FeedManager"),
            ("FeedAggregator", "qualia.live_feed.feed_aggregator", "FeedAggregator"),
            ("TestSuite", "qualia.live_feed.test_suite", "LiveFeedTestSuite"),
            ("KucoinIntegration", "qualia.market.kucoin_integration", "KucoinIntegration"),
        ]
        
        all_success = True
        
        for comp_name, module_path, class_name in components:
            try:
                module = __import__(module_path, fromlist=[class_name])
                component_class = getattr(module, class_name)
                
                # Tentar instanciar com parâmetros mínimos
                if comp_name == "KuCoinFeed":
                    # Teste básico de instanciação
                    instance = component_class(
                        api_key="test", api_secret="test", password="test",
                        symbols=["BTC-USDT"], enable_websocket=False
                    )
                elif comp_name == "DataNormalizer":
                    instance = component_class()
                elif comp_name == "FeedAggregator":
                    instance = component_class()
                elif comp_name == "TestSuite":
                    instance = component_class(use_sandbox=True)
                elif comp_name == "KucoinIntegration":
                    instance = component_class(
                        api_key="test", api_secret="test", password="test"
                    )
                else:
                    instance = component_class()
                
                self.component_validations.append(ComponentValidation(
                    component=comp_name,
                    available=True,
                    details=f"✅ Importação e instanciação bem-sucedida"
                ))
                
            except Exception as e:
                logger.error(f"❌ Erro ao validar {comp_name}: {e}")
                self.component_validations.append(ComponentValidation(
                    component=comp_name,
                    available=False,
                    details=f"❌ Erro: {str(e)}"
                ))
                all_success = False
        
        return all_success
    
    async def test_data_normalizer(self) -> TestResult:
        """Testa o normalizador de dados."""
        start_time = time.time()
        
        try:
            from qualia.live_feed.data_normalizer import DataNormalizer
            
            normalizer = DataNormalizer()
            
            # Teste de normalização de ticker KuCoin
            mock_ticker_data = {
                'symbol': 'BTC-USDT',
                'price': '45000.50',
                'bestBid': '44999.00',
                'bestAsk': '45001.00',
                'vol': '1234.56',
                'changePrice': '500.50',
                'changeRate': '0.0112',
                'high': '45500.00',
                'low': '44000.00',
                'time': str(int(time.time() * 1000))
            }
            
            normalized = normalizer.normalize_kucoin_ticker(mock_ticker_data)
            
            # Validações
            assert normalized.symbol == 'BTCUSDT'
            assert normalized.price == 45000.50
            assert normalized.bid == 44999.00
            assert normalized.ask == 45001.00
            assert normalized.source == 'kucoin'
            
            duration = time.time() - start_time
            return TestResult(
                name="Data Normalizer",
                success=True,
                duration=duration,
                details=f"✅ Normalização funcionando: {normalized.symbol} @ ${normalized.price}"
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                name="Data Normalizer",
                success=False,
                duration=duration,
                details="❌ Falha na normalização",
                error=str(e)
            )
    
    async def test_feed_aggregator(self) -> TestResult:
        """Testa o agregador de feeds."""
        start_time = time.time()
        
        try:
            from qualia.live_feed.feed_aggregator import FeedAggregator
            from qualia.live_feed.data_normalizer import NormalizedTicker

            aggregator = FeedAggregator()
            
            # Simular dados de ticker
            ticker1 = NormalizedTicker(
                symbol="BTCUSDT",
                price=45000.0,
                bid=44999.0,
                ask=45001.0,
                volume_24h=1000.0,
                change_24h=500.0,
                change_24h_percent=1.12,
                high_24h=45500.0,
                low_24h=44000.0,
                timestamp=time.time(),
                source="kucoin"
            )
            
            # Processar ticker
            aggregator.add_ticker(ticker1)

            # Verificar agregação
            aggregated = aggregator.get_aggregated_ticker("BTCUSDT")
            assert aggregated is not None
            assert aggregated.symbol == "BTCUSDT"
            assert aggregated.price == 45000.0
            
            duration = time.time() - start_time
            return TestResult(
                name="Feed Aggregator",
                success=True,
                duration=duration,
                details=f"✅ Agregação funcionando: {aggregated.symbol} @ ${aggregated.price}"
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                name="Feed Aggregator",
                success=False,
                duration=duration,
                details="❌ Falha na agregação",
                error=str(e)
            )
    
    async def test_kucoin_feed_architecture(self) -> TestResult:
        """Testa a arquitetura do KuCoin Feed."""
        start_time = time.time()
        
        try:
            from qualia.live_feed.kucoin_feed import KuCoinFeed
            
            # Criar feed com configuração de teste
            feed = KuCoinFeed(
                api_key="test_key",
                api_secret="test_secret", 
                password="test_password",
                symbols=["BTC-USDT", "ETH-USDT"],
                enable_websocket=False,  # Desabilitar WebSocket para teste
                enable_rest_fallback=False,  # Desabilitar REST para teste
                timeout=30.0
            )
            
            # Verificar inicialização
            assert feed.symbols == ["BTC-USDT", "ETH-USDT"]
            assert not feed.enable_websocket
            assert not feed.is_running
            assert not feed.is_connected
            
            # Verificar métodos essenciais existem
            assert hasattr(feed, 'start')
            assert hasattr(feed, 'stop')
            assert hasattr(feed, 'get_feed_status')
            assert hasattr(feed, 'get_latest_ticker')
            
            duration = time.time() - start_time
            return TestResult(
                name="KuCoin Feed Architecture",
                success=True,
                duration=duration,
                details="✅ Arquitetura validada: métodos, configuração, estado inicial"
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                name="KuCoin Feed Architecture",
                success=False,
                duration=duration,
                details="❌ Falha na arquitetura",
                error=str(e)
            )
    
    async def test_feed_manager_integration(self) -> TestResult:
        """Testa integração do Feed Manager."""
        start_time = time.time()

        try:
            from qualia.live_feed.feed_manager import FeedManager

            # Configuração de teste
            config = {
                'exchanges': {
                    'kucoin': {
                        'api_key': 'test_key',
                        'api_secret': 'test_secret',
                        'password': 'test_password',
                        'sandbox': True,
                        'timeout': 30.0,
                        'rate_limit': 4.0,
                    }
                }
            }

            # Criar manager
            manager = FeedManager(
                config=config,
                symbols=['BTC-USDT'],
                enable_kucoin=True,
                aggregation_enabled=True
            )

            # Verificar inicialização
            assert manager.symbols == ['BTC-USDT'], f"Expected ['BTC-USDT'], got {manager.symbols}"
            assert manager.enable_kucoin, f"Expected True, got {manager.enable_kucoin}"
            assert manager.aggregation_enabled, f"Expected True, got {manager.aggregation_enabled}"

            # Verificar métodos essenciais
            assert hasattr(manager, 'start'), "Manager missing 'start' method"
            assert hasattr(manager, 'stop'), "Manager missing 'stop' method"
            assert hasattr(manager, 'get_system_status'), "Manager missing 'get_system_status' method"
            assert hasattr(manager, 'initialize'), "Manager missing 'initialize' method"
            assert hasattr(manager, 'get_latest_ticker'), "Manager missing 'get_latest_ticker' method"

            duration = time.time() - start_time
            return TestResult(
                name="Feed Manager Integration",
                success=True,
                duration=duration,
                details="✅ Integração validada: configuração, símbolos, métodos"
            )

        except Exception as e:
            duration = time.time() - start_time
            import traceback
            error_details = f"{str(e)}\n{traceback.format_exc()}"
            return TestResult(
                name="Feed Manager Integration",
                success=False,
                duration=duration,
                details="❌ Falha na integração",
                error=error_details
            )
    
    async def test_credentials_validation_system(self) -> TestResult:
        """Testa sistema de validação de credenciais."""
        start_time = time.time()
        
        try:
            from qualia.live_feed.setup_credentials import CredentialsSetup
            
            setup = CredentialsSetup()
            
            # Testar verificação de variáveis de ambiente
            env_status = setup.check_environment_variables()
            
            # Verificar estrutura de retorno
            expected_vars = ['KUCOIN_API_KEY', 'KUCOIN_API_SECRET', 'KUCOIN_PASSPHRASE']
            for var in expected_vars:
                assert var in env_status
                assert isinstance(env_status[var], bool)
            
            duration = time.time() - start_time
            return TestResult(
                name="Credentials Validation System",
                success=True,
                duration=duration,
                details=f"✅ Sistema funcionando: {len(env_status)} variáveis verificadas"
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                name="Credentials Validation System",
                success=False,
                duration=duration,
                details="❌ Falha no sistema de credenciais",
                error=str(e)
            )
    
    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Executa todos os testes abrangentes."""
        logger.info("🧪 Iniciando testes abrangentes do sistema de live feed...")
        
        # 1. Verificar credenciais
        credentials = self.check_credentials()
        logger.info(f"🔑 Status das credenciais: {credentials}")
        
        # 2. Validar componentes
        logger.info("📦 Validando componentes...")
        components_ok = self.validate_component_imports()
        
        # 3. Executar testes individuais
        tests = [
            self.test_data_normalizer(),
            self.test_feed_aggregator(),
            self.test_kucoin_feed_architecture(),
            self.test_feed_manager_integration(),
            self.test_credentials_validation_system(),
        ]
        
        logger.info("🔬 Executando testes individuais...")
        for test_coro in tests:
            result = await test_coro
            self.test_results.append(result)
            
            status = "✅" if result.success else "❌"
            logger.info(f"{status} {result.name}: {result.details}")
            
            if result.error:
                logger.error(f"   Erro: {result.error}")
        
        # 4. Compilar resultados
        total_tests = len(self.test_results)
        successful_tests = sum(1 for r in self.test_results if r.success)
        total_components = len(self.component_validations)
        available_components = sum(1 for c in self.component_validations if c.available)
        
        summary = {
            'timestamp': datetime.now().isoformat(),
            'credentials': {
                'has_real_credentials': self.has_real_credentials,
                'status': self.credentials_status
            },
            'components': {
                'total': total_components,
                'available': available_components,
                'validations': [asdict(c) for c in self.component_validations]
            },
            'tests': {
                'total': total_tests,
                'successful': successful_tests,
                'success_rate': successful_tests / total_tests if total_tests > 0 else 0,
                'results': [asdict(r) for r in self.test_results]
            },
            'overall_status': {
                'components_ready': components_ok,
                'tests_passing': successful_tests == total_tests,
                'ready_for_real_credentials': components_ok and successful_tests == total_tests
            }
        }
        
        return summary
    
    def print_detailed_report(self, summary: Dict[str, Any]):
        """Imprime relatório detalhado."""
        print("\n" + "="*80)
        print("📊 RELATÓRIO COMPLETO - D-03.1: TEST KUCOIN LIVE FEED")
        print("="*80)
        
        # Status das credenciais
        print("\n🔑 STATUS DAS CREDENCIAIS:")
        if summary['credentials']['has_real_credentials']:
            print("   ✅ Credenciais reais configuradas")
        else:
            print("   ⚠️ Credenciais reais NÃO configuradas")
            print("   📝 Para configurar credenciais reais:")
            print("      export KUCOIN_API_KEY='sua_api_key'")
            print("      export KUCOIN_API_SECRET='seu_api_secret'")
            print("      export KUCOIN_PASSPHRASE='sua_passphrase'")
        
        # Status dos componentes
        print(f"\n📦 COMPONENTES ({summary['components']['available']}/{summary['components']['total']}):")
        for comp in summary['components']['validations']:
            status = "✅" if comp['available'] else "❌"
            print(f"   {status} {comp['component']}: {comp['details']}")
        
        # Resultados dos testes
        print(f"\n🧪 TESTES ({summary['tests']['successful']}/{summary['tests']['total']}):")
        for test in summary['tests']['results']:
            status = "✅" if test['success'] else "❌"
            duration = f"{test['duration']:.2f}s"
            print(f"   {status} {test['name']} ({duration}): {test['details']}")
            if test['error']:
                print(f"      ❌ Erro: {test['error']}")
        
        # Status geral
        print(f"\n📈 STATUS GERAL:")
        overall = summary['overall_status']
        print(f"   Componentes prontos: {'✅' if overall['components_ready'] else '❌'}")
        print(f"   Testes passando: {'✅' if overall['tests_passing'] else '❌'}")
        print(f"   Pronto para credenciais reais: {'✅' if overall['ready_for_real_credentials'] else '❌'}")
        
        success_rate = summary['tests']['success_rate'] * 100
        print(f"   Taxa de sucesso: {success_rate:.1f}%")
        
        print("\n" + "="*80)


async def main():
    """Função principal."""
    tester = D031LiveFeedTester()
    
    try:
        # Executar testes abrangentes
        summary = await tester.run_comprehensive_tests()
        
        # Salvar resultados
        results_file = f"data/d03_1_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs("data", exist_ok=True)
        
        with open(results_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        # Imprimir relatório
        tester.print_detailed_report(summary)
        
        # Status final
        if summary['overall_status']['ready_for_real_credentials']:
            logger.info("🎉 Sistema pronto para credenciais reais!")
            logger.info(f"📄 Resultados salvos em: {results_file}")
            return 0
        else:
            logger.warning("⚠️ Sistema precisa de ajustes antes de usar credenciais reais")
            return 1
            
    except Exception as e:
        logger.error(f"💥 Erro durante testes: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
