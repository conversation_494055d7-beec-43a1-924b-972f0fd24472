#!/usr/bin/env python
"""Update Quantum Pattern Memory with new pattern files.

This script loads the QPM persistence file specified in the strategy
configuration and imports any JSON files found in a source directory. It
automatically saves the updated memory back to disk. Use this to refresh the
pattern database when new backtest results are available.
"""

from __future__ import annotations

import argparse
import json
from datetime import datetime, timedelta, timezone

from qualia.memory.quantum_pattern_memory import QuantumPatternMemory
from qualia.common_types import QuantumSignaturePacket
from pathlib import Path
import requests

DEFAULT_PERSISTENCE_PATH = Path("data") / "cache" / "qpm_memory.json"
DEFAULT_SOURCE_DIR = Path("data") / "pattern_updates"


def memory_is_outdated(path: str, days: int = 30) -> bool:
    """Return ``True`` when ``path`` is missing or older than ``days``.

    Parameters
    ----------
    path : str
        Path to the persistence file to inspect.
    days : int, default=30
        Maximum age allowed in days.

    Returns
    -------
    bool
        ``True`` if the file does not exist or is outdated.
    """

    p = Path(path)
    if not p.exists():
        return True
    mtime = datetime.fromtimestamp(p.stat().st_mtime)
    return datetime.now(timezone.utc) - mtime > timedelta(days=days)


def main() -> None:
    parser = argparse.ArgumentParser(description="Update QPM memory from pattern files")
    parser.add_argument(
        "--path", default=DEFAULT_PERSISTENCE_PATH, help="Path to QPM persistence JSON"
    )
    parser.add_argument(
        "--source-dir",
        default=DEFAULT_SOURCE_DIR,
        help="Directory containing pattern JSON files",
    )
    parser.add_argument(
        "--history-file",
        help="Optional JSONLines file with historical patterns",
    )
    parser.add_argument(
        "--min-size",
        type=int,
        default=100,
        help="Minimum number of patterns before update is skipped",
    )
    parser.add_argument(
        "--api-url",
        help="Optional base URL to fetch new patterns via API",
    )
    parser.add_argument(
        "--since",
        type=float,
        help="Timestamp to start fetching when using --api-url",
    )
    args = parser.parse_args()

    qpm = QuantumPatternMemory(persistence_path=args.path, enable_warmstart=False)
    total_patterns = sum(len(lst) for lst in qpm.memory.values())

    if not memory_is_outdated(args.path) and total_patterns >= args.min_size:
        print("Memory is up to date - no update needed.")
        return

    imported = 0
    pattern_files = list(Path(args.source_dir).glob("*.json"))
    for pf in pattern_files:
        imported += qpm.import_patterns_from_backtest(str(pf))

    if args.history_file:
        hist_path = Path(args.history_file)
        if hist_path.exists():
            with open(hist_path, "r", encoding="utf-8") as fh:
                for line in fh:
                    if not line.strip():
                        continue
                    try:
                        rec = json.loads(line)
                    except json.JSONDecodeError:
                        continue
                    qsp_data = rec.get("quantum_signature_packet")
                    if not isinstance(qsp_data, dict):
                        continue
                    packet = QuantumSignaturePacket(**qsp_data)
                    qpm.store_pattern(
                        packet,
                        rec.get("market_snapshot", {}),
                        rec.get("outcome", {}),
                        rec.get("decision_context"),
                        timestamp=rec.get("timestamp"),
                        market_scenario=rec.get("market_scenario"),
                        extra_metadata=rec.get("extra_metadata"),
                    )
                    imported += 1

    if args.api_url:
        since = args.since
        if since is None:
            current_ts = 0.0
            for lst in qpm.memory.values():
                for p in lst:
                    ts = p.get("timestamp", 0.0)
                    current_ts = max(current_ts, float(ts))
            since = current_ts
        try:
            resp = requests.get(
                f"{args.api_url.rstrip('/')}/memory/snapshot",
                params={"since": since},
                timeout=10,
            )
            resp.raise_for_status()
            data = resp.json()
            records = data.get("items", []) if isinstance(data, dict) else []
        except Exception as exc:
            print(f"Failed to fetch patterns from API: {exc}")
            records = []
        for rec in records:
            qsp_data = rec.get("quantum_signature_packet")
            if not isinstance(qsp_data, dict):
                continue
            try:
                packet = QuantumSignaturePacket(**qsp_data)
            except Exception:
                continue
            qpm.store_pattern(
                packet,
                rec.get("market_snapshot", {}),
                rec.get("outcome", {}),
                rec.get("decision_context"),
                timestamp=rec.get("timestamp"),
                market_scenario=rec.get("market_scenario"),
                extra_metadata=rec.get("extra_metadata"),
                metadata=rec.get("metadata"),
            )
            imported += 1

    if imported > 0:
        qpm.save_to_file()
        print(f"Imported {imported} patterns and saved to {args.path}.")
    else:
        print("No patterns imported.")


if __name__ == "__main__":
    main()
