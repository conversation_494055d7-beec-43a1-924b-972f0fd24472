#!/usr/bin/env python3
"""
Teste Simples do Sistema Holográfico de Trading

Script simplificado para testar os componentes principais:
- Enhanced Data Collector
- Holographic Universe 
- Signal Generation

Evita dependências complexas do QUALIARealTimeTrader para validação rápida.
"""

import os
import sys
import asyncio
import time
from typing import Dict, Any

# Adiciona o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from qualia.consciousness.enhanced_data_collector import EnhancedDataCollector
from qualia.consciousness.holographic_universe import HolographicMarketUniverse
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

class SimpleHolographicTradingTest:
    """Teste simples do sistema holográfico."""
    
    def __init__(self):
        self.data_collector = None
        self.holographic_universe = None
        
    async def test_data_collection(self):
        """Testa a coleta de dados enhanced."""
        
        print("\n" + "="*50)
        print("🧪 TESTE 1: Coleta de Dados Enhanced")
        print("="*50)
        
        try:
            # Inicializa data collector
            print("📊 Inicializando Enhanced Data Collector...")
            self.data_collector = EnhancedDataCollector()
            await self.data_collector.__aenter__()
            print("✅ Enhanced Data Collector inicializado")
            
            # Testa coleta de dados de mercado
            print("\n📈 Coletando dados de mercado enhanced...")
            enhanced_market_data = await self.data_collector.collect_enhanced_market_data()
            
            print(f"📊 Dados coletados para {len(enhanced_market_data)} pontos de dados:")
            
            # Converte para formato dict para compatibilidade
            market_data = {}
            for data in enhanced_market_data[:5]:  # Mostra apenas os primeiros 5
                symbol = data.symbol
                if symbol not in market_data:
                    market_data[symbol] = {
                        'close': data.close,
                        'volume': data.volume,
                        'rsi': data.rsi,
                        'volatility': data.volatility,
                        'volume_ratio': data.volume_ratio,
                        'price_change_pct': data.price_change_pct,
                        'quantum_rsi_encoded': data.rsi_quantum_state is not None,
                        'quantum_volume_encoded': data.volume_quantum_state is not None
                    }
                
                print(f"   {symbol}:")
                print(f"     Preço: {data.close:.4f}")
                print(f"     Volume: {data.volume:.0f}")
                print(f"     RSI: {data.rsi:.1f}" if data.rsi else "     RSI: N/A")
                print(f"     Volatilidade: {data.volatility:.2f}%" if data.volatility else "     Volatilidade: N/A")
                print(f"     Volume Ratio: {data.volume_ratio:.2f}" if data.volume_ratio else "     Volume Ratio: N/A")
                print(f"     Quantum RSI Encoded: {data.rsi_quantum_state is not None}")
                print(f"     Quantum Volume Encoded: {data.volume_quantum_state is not None}")
            
            # Simula eventos de notícias para teste
            print("\n📰 Simulando eventos de notícias...")
            news_events = [
                {
                    'title': 'Bitcoin reaches new highs amid institutional adoption',
                    'sentiment_score': 0.8,
                    'timestamp': time.time()
                },
                {
                    'title': 'Ethereum network upgrade shows promising results',
                    'sentiment_score': 0.6,
                    'timestamp': time.time()
                },
                {
                    'title': 'Market volatility increases due to regulatory concerns',
                    'sentiment_score': -0.4,
                    'timestamp': time.time()
                }
            ]
            print(f"📰 {len(news_events)} eventos de notícias simulados")
            
            for event in news_events[:2]:  # Mostra apenas os primeiros 2
                print(f"   - {event['title'][:60]}...")
                print(f"     Sentiment Score: {event['sentiment_score']:.2f}")
            
            # Simula Fear & Greed Index
            print("\n😨 Simulando Fear & Greed Index...")
            sentiment_data = {
                'value': 65,
                'value_classification': 'Greed'
            }
            print(f"😨 Fear & Greed Index: {sentiment_data['value']} - {sentiment_data['value_classification']}")
            
            print("\n✅ Teste de coleta de dados concluído com sucesso!")
            return market_data, news_events, sentiment_data
            
        except Exception as e:
            print(f"❌ Erro na coleta de dados: {e}")
            raise
    
    async def test_holographic_simulation(self, market_data, news_events):
        """Testa a simulação holográfica."""
        
        print("\n" + "="*50)
        print("🧪 TESTE 2: Simulação Holográfica")
        print("="*50)
        
        try:
            # Inicializa universo holográfico
            print("🌌 Inicializando Holographic Universe...")
            self.holographic_universe = HolographicMarketUniverse(
                field_size=(200, 200),
                diffusion_rate=0.35,
                feedback_strength=0.08
            )
            print("✅ Holographic Universe inicializado")
            
            # Injeta eventos de mercado usando HolographicEvent
            print("\n🌌 Injetando eventos de mercado...")
            events_injected = 0
            current_time = time.time()
            
            for symbol, data in market_data.items():
                if data and 'close' in data:
                    volatility = data.get('volatility', 1.0)
                    price_change = data.get('price_change_pct', 0.0)
                    amplitude = min(abs(price_change) * 3.0 + volatility * 0.5, 8.0)
                    
                    # Cria evento holográfico
                    from qualia.consciousness.holographic_universe import HolographicEvent
                    
                    # Obtém posição do símbolo (usa centro se não encontrar)
                    symbol_key = symbol.replace('USDT', '').replace('/', '')
                    position = self.holographic_universe.symbol_positions.get(
                        symbol_key, 
                        (100, 100)  # Centro como fallback
                    )
                    
                    event = HolographicEvent(
                        position=position,
                        time=current_time,
                        amplitude=amplitude,
                        spatial_sigma=15.0,
                        temporal_sigma=5.0,
                        event_type='price_movement',
                        source_data={
                            'symbol': symbol,
                            'price': data['close'],
                            'volume': data.get('volume', 0),
                            'volatility': volatility,
                            'rsi': data.get('rsi', 50.0),
                            'volume_ratio': data.get('volume_ratio', 1.0)
                        }
                    )
                    
                    await self.holographic_universe.inject_holographic_event(event)
                    events_injected += 1
                    
                    if events_injected >= 5:  # Limita para teste
                        break
            
            print(f"✅ {events_injected} eventos de mercado injetados")
            
            # Injeta eventos de notícias
            print("📰 Injetando eventos de notícias...")
            news_injected = 0
            
            for news_event in news_events[:3]:  # Limita para teste
                sentiment_score = news_event.get('sentiment_score', 0.0)
                amplitude = min(abs(sentiment_score) * 4.0 + 1.0, 6.0)
                
                if amplitude > 0.5:
                    # Usa posição da região de notícias
                    news_position = self.holographic_universe.modality_regions.get('news', (150, 150))
                    
                    event = HolographicEvent(
                        position=news_position,
                        time=current_time,
                        amplitude=amplitude,
                        spatial_sigma=20.0,
                        temporal_sigma=3.0,
                        event_type='news',
                        source_data=news_event
                    )
                    
                    await self.holographic_universe.inject_holographic_event(event)
                    news_injected += 1
            
            print(f"✅ {news_injected} eventos de notícias injetados")
            
            # Evolui o universo
            print("\n🔄 Evoluindo universo holográfico...")
            for step in range(5):
                await self.holographic_universe.step_evolution(current_time + step)
                print(f"   Passo {step + 1}/5 concluído")
            
            # Detecta padrões
            print("\n🔍 Detectando padrões...")
            patterns = self.holographic_universe.analyze_holographic_patterns()
            
            print(f"🎯 {len(patterns)} padrões detectados:")
            for i, pattern in enumerate(patterns[:5]):  # Mostra apenas os primeiros 5
                print(f"   Padrão {i+1}:")
                print(f"     Position: {pattern.position}")
                print(f"     Strength: {pattern.strength:.3f}")
                print(f"     Confidence: {pattern.confidence:.3f}")
                print(f"     Pattern Type: {pattern.pattern_type}")
                print(f"     Timeframe: {pattern.timeframe}")
                print(f"     Dominant Frequency: {pattern.dominant_frequency:.3f}")
            
            print("\n✅ Teste de simulação holográfica concluído com sucesso!")
            return patterns
            
        except Exception as e:
            print(f"❌ Erro na simulação holográfica: {e}")
            raise
    
    async def test_signal_generation(self, patterns, market_data):
        """Testa a geração de sinais de trading."""
        
        print("\n" + "="*50)
        print("🧪 TESTE 3: Geração de Sinais de Trading")
        print("="*50)
        
        try:
            # Converte padrões em sinais
            signals = []
            
            for pattern in patterns:
                if pattern.strength > 0.3 and pattern.confidence > 0.4:
                    
                    # Determina ação baseada no padrão
                    if pattern.pattern_type in ['bullish', 'ascending']:
                        action = 'BUY'
                    elif pattern.pattern_type in ['bearish', 'descending']:
                        action = 'SELL'
                    else:
                        action = 'HOLD'
                    
                    # Encontra símbolo mais próximo da posição do padrão
                    closest_symbol = 'BTC/USDT'
                    min_distance = float('inf')
                    
                    for symbol, pos in self.holographic_universe.symbol_positions.items():
                        distance = ((pattern.position[0] - pos[0])**2 + (pattern.position[1] - pos[1])**2)**0.5
                        if distance < min_distance:
                            min_distance = distance
                            closest_symbol = f"{symbol}/USDT"
                    
                    symbol_data = market_data.get(closest_symbol.replace('/', ''), {})
                    
                    signal = {
                        'symbol': closest_symbol,
                        'action': action,
                        'confidence': pattern.confidence,
                        'timestamp': time.time(),
                        'rsi': symbol_data.get('rsi'),
                        'volume_ratio': symbol_data.get('volume_ratio'),
                        'volatility': symbol_data.get('volatility'),
                        'quantum_rsi_encoded': symbol_data.get('quantum_rsi_encoded', False),
                        'pattern_strength': pattern.strength,
                        'pattern_type': pattern.pattern_type,
                        'timeframe': pattern.timeframe,
                        'dominant_frequency': pattern.dominant_frequency
                    }
                    
                    signals.append(signal)
            
            print(f"📡 {len(signals)} sinais de trading gerados:")
            
            for i, signal in enumerate(signals):
                print(f"   Sinal {i+1}:")
                print(f"     Symbol: {signal['symbol']}")
                print(f"     Action: {signal['action']}")
                print(f"     Confidence: {signal['confidence']:.3f}")
                print(f"     RSI: {signal['rsi']:.1f}" if signal['rsi'] else "     RSI: N/A")
                print(f"     Volume Ratio: {signal['volume_ratio']:.2f}" if signal['volume_ratio'] else "     Volume Ratio: N/A")
                print(f"     Volatility: {signal['volatility']:.2f}%" if signal['volatility'] else "     Volatility: N/A")
                print(f"     Quantum Encoded: {signal['quantum_rsi_encoded']}")
                print(f"     Pattern Strength: {signal['pattern_strength']:.3f}")
                print(f"     Pattern Type: {signal['pattern_type']}")
                print(f"     Timeframe: {signal['timeframe']}")
                print(f"     Dominant Frequency: {signal['dominant_frequency']:.3f}")
                print()
            
            # Análise dos sinais
            buy_signals = [s for s in signals if s['action'] == 'BUY']
            sell_signals = [s for s in signals if s['action'] == 'SELL']
            hold_signals = [s for s in signals if s['action'] == 'HOLD']
            
            print("📊 Análise dos Sinais:")
            print(f"   🟢 BUY signals: {len(buy_signals)}")
            print(f"   🔴 SELL signals: {len(sell_signals)}")
            print(f"   ⚪ HOLD signals: {len(hold_signals)}")
            
            if signals:
                avg_confidence = sum(s['confidence'] for s in signals) / len(signals)
                quantum_encoded_count = sum(1 for s in signals if s['quantum_rsi_encoded'])
                
                print(f"   📈 Confidence média: {avg_confidence:.3f}")
                print(f"   🌀 Sinais com quantum encoding: {quantum_encoded_count}/{len(signals)}")
            
            print("\n✅ Teste de geração de sinais concluído com sucesso!")
            return signals
            
        except Exception as e:
            print(f"❌ Erro na geração de sinais: {e}")
            raise
    
    async def cleanup(self):
        """Limpa recursos."""
        
        print("\n🧹 Limpando recursos...")
        
        try:
            if self.data_collector:
                await self.data_collector.__aexit__(None, None, None)
            
            print("✅ Recursos limpos")
            
        except Exception as e:
            print(f"❌ Erro na limpeza: {e}")

async def main():
    """Função principal do teste."""
    
    print("🌀 QUALIA Holographic Trading - Teste Simples")
    print("=" * 60)
    print("Testando componentes principais:")
    print("• Enhanced Data Collection")
    print("• Holographic Universe Simulation")
    print("• Signal Generation")
    print("=" * 60)
    
    test = SimpleHolographicTradingTest()
    
    try:
        # Executa testes em sequência
        market_data, news_events, sentiment_data = await test.test_data_collection()
        patterns = await test.test_holographic_simulation(market_data, news_events)
        signals = await test.test_signal_generation(patterns, market_data)
        
        # Relatório final
        print("\n" + "="*60)
        print("🎉 TODOS OS TESTES CONCLUÍDOS COM SUCESSO!")
        print("="*60)
        print("\n📊 Resumo dos Resultados:")
        print(f"   📈 Dados de mercado coletados: {len(market_data)} símbolos")
        print(f"   📰 Eventos de notícias: {len(news_events)}")
        print(f"   🎯 Padrões detectados: {len(patterns)}")
        print(f"   📡 Sinais gerados: {len(signals)}")
        
        if signals:
            buy_count = sum(1 for s in signals if s['action'] == 'BUY')
            sell_count = sum(1 for s in signals if s['action'] == 'SELL')
            print(f"   🟢 BUY signals: {buy_count}")
            print(f"   🔴 SELL signals: {sell_count}")
        
        print("\n✅ Sistema holográfico validado com sucesso!")
        print("✅ Dados reais coletados e processados")
        print("✅ Simulação holográfica funcionando")
        print("✅ Sinais de trading gerados")
        print("\n🚀 Sistema pronto para integração com execução real!")
        
    except Exception as e:
        print(f"\n❌ Erro nos testes: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await test.cleanup()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Testes interrompidos pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro fatal: {e}")
        sys.exit(1)