#!/usr/bin/env python3
"""
ROBUST_NORMALIZED Strategy - Versão Otimizada
Melhorias baseadas na análise de performance detalhada.
"""

import pandas as pd
import numpy as np
import requests
from datetime import datetime, timedelta


def fetch_data(symbol: str, days: int = 90) -> pd.DataFrame:
    """Busca dados históricos."""
    try:
        end_time = int(datetime.now().timestamp() * 1000)
        start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
        
        url = "https://api.binance.com/api/v3/klines"
        params = {
            'symbol': symbol.replace('/', ''),
            'interval': '1h',
            'startTime': start_time,
            'endTime': end_time,
            'limit': 1000
        }
        
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        data = response.json()
        df = pd.DataFrame(data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])
        
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df.set_index('timestamp', inplace=True)
        df = df.sort_index().dropna()
        
        # Indicadores
        df['returns'] = df['close'].pct_change()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['sma_50'] = df['close'].rolling(50).mean()
        df['rsi'] = calculate_rsi(df['close'], 14)
        df['volatility'] = df['returns'].rolling(20).std()
        df['atr'] = calculate_atr(df, 14)
        
        return df
        
    except Exception as e:
        print(f"❌ Erro ao buscar dados para {symbol}: {e}")
        return pd.DataFrame()


def calculate_rsi(prices: pd.Series, period: int = 14) -> pd.Series:
    """Calcula RSI."""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))


def calculate_atr(df: pd.DataFrame, period: int = 14) -> pd.Series:
    """Calcula Average True Range."""
    high_low = df['high'] - df['low']
    high_close = np.abs(df['high'] - df['close'].shift())
    low_close = np.abs(df['low'] - df['close'].shift())
    
    true_range = np.maximum(high_low, np.maximum(high_close, low_close))
    return true_range.rolling(period).mean()


def robust_normalized_original(df: pd.DataFrame) -> dict:
    """Versão ORIGINAL da estratégia."""
    signals = []
    
    lookback = 200
    start_idx = max(50, lookback)
    
    # Coleta histórico para normalização
    trend_history = []
    mean_rev_history = []
    momentum_history = []
    rsi_history = []
    
    for j in range(start_idx-lookback, start_idx):
        if j >= 50:
            trend_raw = (df['sma_20'].iloc[j] - df['sma_50'].iloc[j]) / df['sma_50'].iloc[j]
            mean_rev_raw = -(df['close'].iloc[j] - df['sma_20'].iloc[j]) / df['sma_20'].iloc[j]
            momentum_raw = df['close'].iloc[j-10:j].pct_change().mean()
            rsi_raw = (df['rsi'].iloc[j] - 50) / 50
            
            trend_history.append(trend_raw)
            mean_rev_history.append(mean_rev_raw)
            momentum_history.append(momentum_raw)
            rsi_history.append(rsi_raw)
    
    # Estatísticas para normalização
    trend_mean, trend_std = np.mean(trend_history), np.std(trend_history)
    mean_rev_mean, mean_rev_std = np.mean(mean_rev_history), np.std(mean_rev_history)
    momentum_mean, momentum_std = np.mean(momentum_history), np.std(momentum_history)
    rsi_mean, rsi_std = np.mean(rsi_history), np.std(rsi_history)
    
    for i in range(start_idx, len(df)):
        # Componentes RAW
        trend_raw = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
        mean_rev_raw = -(df['close'].iloc[i] - df['sma_20'].iloc[i]) / df['sma_20'].iloc[i]
        momentum_raw = df['close'].iloc[i-10:i].pct_change().mean()
        rsi_raw = (df['rsi'].iloc[i] - 50) / 50
        
        # NORMALIZAÇÃO Z-SCORE
        trend_signal = (trend_raw - trend_mean) / (trend_std + 1e-8)
        mean_rev_signal = (mean_rev_raw - mean_rev_mean) / (mean_rev_std + 1e-8)
        momentum_signal = (momentum_raw - momentum_mean) / (momentum_std + 1e-8)
        rsi_signal = (rsi_raw - rsi_mean) / (rsi_std + 1e-8)
        
        # Clipping
        trend_signal = np.clip(trend_signal, -3, 3)
        mean_rev_signal = np.clip(mean_rev_signal, -3, 3)
        momentum_signal = np.clip(momentum_signal, -3, 3)
        rsi_signal = np.clip(rsi_signal, -3, 3)
        
        # Combinação
        composite_signal = (
            trend_signal * 0.25 +
            mean_rev_signal * 0.25 +
            momentum_signal * 0.25 +
            rsi_signal * 0.25
        )
        
        # Threshold
        if abs(composite_signal) > 0.5:
            signals.append(np.clip(composite_signal * 0.3, -1, 1))
        else:
            signals.append(0)
    
    return calculate_performance(df.iloc[start_idx:], signals, "ORIGINAL")


def robust_normalized_optimized(df: pd.DataFrame) -> dict:
    """Versão OTIMIZADA da estratégia."""
    signals = []
    
    lookback = 200
    start_idx = max(50, lookback)
    
    # Coleta histórico para normalização (mesmo processo)
    trend_history = []
    mean_rev_history = []
    momentum_history = []
    rsi_history = []
    
    for j in range(start_idx-lookback, start_idx):
        if j >= 50:
            trend_raw = (df['sma_20'].iloc[j] - df['sma_50'].iloc[j]) / df['sma_50'].iloc[j]
            mean_rev_raw = -(df['close'].iloc[j] - df['sma_20'].iloc[j]) / df['sma_20'].iloc[j]
            momentum_raw = df['close'].iloc[j-10:j].pct_change().mean()
            rsi_raw = (df['rsi'].iloc[j] - 50) / 50
            
            trend_history.append(trend_raw)
            mean_rev_history.append(mean_rev_raw)
            momentum_history.append(momentum_raw)
            rsi_history.append(rsi_raw)
    
    # Estatísticas para normalização
    trend_mean, trend_std = np.mean(trend_history), np.std(trend_history)
    mean_rev_mean, mean_rev_std = np.mean(mean_rev_history), np.std(mean_rev_history)
    momentum_mean, momentum_std = np.mean(momentum_history), np.std(momentum_history)
    rsi_mean, rsi_std = np.mean(rsi_history), np.std(rsi_history)
    
    for i in range(start_idx, len(df)):
        # 🔧 OTIMIZAÇÃO 1: Filtros de qualidade de mercado
        
        # Filtro de volatilidade (evita mercados muito voláteis)
        vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-50:i].quantile(0.75)
        
        # Filtro de trend strength (só opera com trend claro)
        trend_strength = abs(df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
        trend_filter = trend_strength > 0.005  # Trend mínimo de 0.5%
        
        # Filtro RSI (evita extremos)
        rsi_filter = 30 < df['rsi'].iloc[i] < 70
        
        # Filtro de volume (confirma movimento)
        volume_filter = df['volume'].iloc[i] > df['volume'].iloc[i-20:i].mean() * 0.8
        
        if not (vol_filter and trend_filter and rsi_filter and volume_filter):
            signals.append(0)
            continue
        
        # Componentes RAW
        trend_raw = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
        mean_rev_raw = -(df['close'].iloc[i] - df['sma_20'].iloc[i]) / df['sma_20'].iloc[i]
        momentum_raw = df['close'].iloc[i-10:i].pct_change().mean()
        rsi_raw = (df['rsi'].iloc[i] - 50) / 50
        
        # NORMALIZAÇÃO Z-SCORE (mantida)
        trend_signal = (trend_raw - trend_mean) / (trend_std + 1e-8)
        mean_rev_signal = (mean_rev_raw - mean_rev_mean) / (mean_rev_std + 1e-8)
        momentum_signal = (momentum_raw - momentum_mean) / (momentum_std + 1e-8)
        rsi_signal = (rsi_raw - rsi_mean) / (rsi_std + 1e-8)
        
        # 🔧 OTIMIZAÇÃO 2: Clipping mais conservador
        trend_signal = np.clip(trend_signal, -2.5, 2.5)  # Reduzido de -3,3
        mean_rev_signal = np.clip(mean_rev_signal, -2.5, 2.5)
        momentum_signal = np.clip(momentum_signal, -2.5, 2.5)
        rsi_signal = np.clip(rsi_signal, -2.5, 2.5)
        
        # 🔧 OTIMIZAÇÃO 3: Pesos adaptativos baseados em volatilidade
        vol_adj = 1 / (1 + df['volatility'].iloc[i] * 100)  # Reduz sinal em alta volatilidade
        
        # 🔧 OTIMIZAÇÃO 4: Pesos otimizados (mais peso em trend e momentum)
        composite_signal = (
            trend_signal * 0.35 +      # Aumentado de 0.25
            momentum_signal * 0.35 +   # Aumentado de 0.25
            mean_rev_signal * 0.15 +   # Reduzido de 0.25
            rsi_signal * 0.15          # Reduzido de 0.25
        ) * vol_adj
        
        # 🔧 OTIMIZAÇÃO 5: Threshold adaptativo baseado em volatilidade
        base_threshold = 0.6  # Aumentado de 0.5
        vol_adjustment = df['volatility'].iloc[i] * 5  # Aumenta threshold em alta volatilidade
        adaptive_threshold = base_threshold + vol_adjustment
        
        if abs(composite_signal) > adaptive_threshold:
            # 🔧 OTIMIZAÇÃO 6: Multiplicador baseado na força do sinal
            signal_strength = abs(composite_signal) / adaptive_threshold
            multiplier = 0.4 * min(signal_strength, 2.0)  # Máximo 0.8
            signals.append(np.clip(composite_signal * multiplier, -1, 1))
        else:
            signals.append(0)
    
    return calculate_performance_with_risk_mgmt(df.iloc[start_idx:], signals, "OPTIMIZED")


def calculate_performance(df: pd.DataFrame, signals: list, strategy_name: str) -> dict:
    """Performance básica."""
    returns = []
    trades = 0
    
    for i in range(1, len(signals)):
        if abs(signals[i-1]) > 0.1:
            price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
            position_return = signals[i-1] * price_return
            returns.append(position_return)
            trades += 1
        else:
            returns.append(0)
    
    if not returns:
        return {'error': 'Nenhum trade'}
    
    returns_series = pd.Series(returns)
    
    total_return = returns_series.sum()
    volatility = returns_series.std() * np.sqrt(252)
    sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
    
    cumulative = (1 + returns_series).cumprod()
    rolling_max = cumulative.expanding().max()
    drawdowns = (cumulative - rolling_max) / rolling_max
    max_drawdown = abs(drawdowns.min())
    
    win_rate = (returns_series > 0).mean()
    
    return {
        'strategy': strategy_name,
        'total_return_pct': total_return * 100,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown_pct': max_drawdown * 100,
        'win_rate': win_rate * 100,
        'total_trades': trades,
        'volatility': volatility * 100
    }


def calculate_performance_with_risk_mgmt(df: pd.DataFrame, signals: list, strategy_name: str) -> dict:
    """Performance com gestão de risco."""
    returns = []
    trades = 0
    winning_trades = 0
    total_wins = 0
    total_losses = 0
    
    for i in range(1, len(signals)):
        if abs(signals[i-1]) > 0.1:
            price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
            raw_return = signals[i-1] * price_return
            
            # 🔧 GESTÃO DE RISCO: Stop-loss e take-profit adaptativos
            atr_pct = df['atr'].iloc[i] / df['close'].iloc[i]
            
            # Stop-loss baseado em ATR (mais inteligente)
            stop_loss = -max(0.008, atr_pct * 2)  # Mínimo -0.8% ou 2x ATR
            
            # Take-profit baseado em ATR
            take_profit = max(0.015, atr_pct * 3)  # Mínimo ****% ou 3x ATR
            
            if raw_return < stop_loss:
                final_return = stop_loss
            elif raw_return > take_profit:
                final_return = take_profit
            else:
                final_return = raw_return
            
            returns.append(final_return)
            trades += 1
            
            if final_return > 0:
                winning_trades += 1
                total_wins += final_return
            else:
                total_losses += abs(final_return)
        else:
            returns.append(0)
    
    if not returns:
        return {'error': 'Nenhum trade'}
    
    returns_series = pd.Series(returns)
    
    total_return = returns_series.sum()
    volatility = returns_series.std() * np.sqrt(252)
    sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
    
    cumulative = (1 + returns_series).cumprod()
    rolling_max = cumulative.expanding().max()
    drawdowns = (cumulative - rolling_max) / rolling_max
    max_drawdown = abs(drawdowns.min())
    
    win_rate = winning_trades / trades if trades > 0 else 0
    profit_factor = total_wins / total_losses if total_losses > 0 else 0
    
    return {
        'strategy': strategy_name,
        'total_return_pct': total_return * 100,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown_pct': max_drawdown * 100,
        'win_rate': win_rate * 100,
        'total_trades': trades,
        'volatility': volatility * 100,
        'profit_factor': profit_factor
    }


def run_optimization_comparison():
    """Compara versão original vs otimizada."""
    print("🔧 ROBUST_NORMALIZED: Original vs Otimizada")
    print("=" * 50)
    
    symbols = ["BTCUSDT", "ETHUSDT"]
    
    for symbol in symbols:
        print(f"\n📈 Testando {symbol}...")
        
        df = fetch_data(symbol, days=90)
        if df.empty or len(df) < 300:
            print(f"❌ Dados insuficientes para {symbol}")
            continue
        
        # Testa ambas as versões
        original = robust_normalized_original(df)
        optimized = robust_normalized_optimized(df)
        
        print(f"\n📊 ORIGINAL:")
        if 'error' not in original:
            print(f"   Return: {original['total_return_pct']:.2f}%")
            print(f"   Sharpe: {original['sharpe_ratio']:.3f}")
            print(f"   Win Rate: {original['win_rate']:.1f}%")
            print(f"   Trades: {original['total_trades']}")
            print(f"   Max DD: {original['max_drawdown_pct']:.2f}%")
        
        print(f"\n🔧 OTIMIZADA:")
        if 'error' not in optimized:
            print(f"   Return: {optimized['total_return_pct']:.2f}%")
            print(f"   Sharpe: {optimized['sharpe_ratio']:.3f}")
            print(f"   Win Rate: {optimized['win_rate']:.1f}%")
            print(f"   Trades: {optimized['total_trades']}")
            print(f"   Max DD: {optimized['max_drawdown_pct']:.2f}%")
            print(f"   Profit Factor: {optimized['profit_factor']:.2f}")
        
        # Comparação
        if 'error' not in original and 'error' not in optimized:
            sharpe_improvement = optimized['sharpe_ratio'] - original['sharpe_ratio']
            return_improvement = optimized['total_return_pct'] - original['total_return_pct']
            
            print(f"\n📈 MELHORIA:")
            print(f"   Sharpe: {sharpe_improvement:+.3f}")
            print(f"   Return: {return_improvement:+.2f}%")
            
            if sharpe_improvement > 0.1:
                print(f"   ✅ Otimização FUNCIONOU!")
            else:
                print(f"   ⚠️  Melhoria marginal")


if __name__ == "__main__":
    run_optimization_comparison()
