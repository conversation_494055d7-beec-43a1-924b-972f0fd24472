# QUALIASystemManager

O `QUALIASystemManager` coordena a inicialização e o monitoramento de todos os componentes do sistema QUALIA. Ele integra o mecanismo de health check e provê métodos para iniciar, verificar e encerrar o sistema de forma segura.

## Principais Funções
- `initialize_system()` &ndash; Inicializa coletores de dados, camada quântica, estratégias e outros módulos configurados.
- `get_current_health_report()` &ndash; Recupera o relatório de saúde mais recente gerado pelo `SystemHealthChecker`.
- `is_ready_for_trading()` &ndash; Verifica se todas as partes essenciais estão prontas para operar.
- `shutdown_system()` &ndash; Executa um desligamento gracioso dos componentes.

## Exemplo de Uso
```python
import asyncio
from qualia.core.system_manager import QUALIASystemManager

async def main():
    config = {
        "health_check_interval": 30,
        "require_all_healthy_for_trading": False,
    }
    manager = QUALIASystemManager(config)

    if await manager.initialize_system():
        if manager.is_ready_for_trading():
            print("🚀 Sistema pronto para trading!")
        report = await manager.get_current_health_report()
        print(report.readiness_score)
    await manager.shutdown_system()

asyncio.run(main())
```
