# Métricas de Loops de Trading

Os principais ciclos do ``QUALIARealTimeTrader`` emitem métricas StatsD quando
``statsd_client`` é fornecido ou ``QUALIA_MARKET_METRICS_ENABLED`` está ativo.

## Métricas

- ``trading.data_collection_ms`` – duração da etapa de coleta de dados.
- ``trading.data_collection_count`` – contador de execuções da coleta.
- ``trading.decision_ms`` – tempo gasto analisando símbolos.
- ``trading.decision_count`` – contador de ciclos de decisão.
- ``trading.execution_ms`` – tempo total executando ordens aprovadas.
- ``trading.execution_count`` – contador de execuções de ordens.

Todas aceitam a tag opcional ``trace_id`` para correlação.

```python
from datadog import DogStatsd
from qualia.utils import get_statsd_client

statsd = DogStatsd()
client = get_statsd_client() or statsd
trader = QUALIARealTimeTrader(..., statsd_client=client)
```
