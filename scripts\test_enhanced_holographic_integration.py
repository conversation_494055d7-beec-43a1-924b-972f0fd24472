#!/usr/bin/env python3
"""
Script de teste para verificar integração do EnhancedDataCollector
com calibração adaptativa e configuração holográfica.
"""

import asyncio
import sys
import os
import yaml
from pathlib import Path

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from qualia.consciousness.enhanced_data_collector import EnhancedDataCollector
from qualia.consciousness.holographic_universe import HolographicMarketUniverse
from qualia.utils.logger import get_logger, setup_logging

logger = get_logger(__name__)


async def test_enhanced_integration():
    """Testa a integração completa do sistema enhanced."""
    
    setup_logging()
    
    print("\n" + "="*60)
    print("🧪 TESTE DE INTEGRAÇÃO ENHANCED HOLOGRAPHIC")
    print("="*60)
    
    try:
        # 1. Carregar configuração holográfica
        print("\n1️⃣ Carregando configuração holográfica...")
        config_path = Path("config/holographic_universe.yaml")
        
        if not config_path.exists():
            print(f"❌ Arquivo não encontrado: {config_path}")
            return False
            
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
            
        holographic_config = config.get('holographic_universe', {})
        print(f"✅ Configuração carregada: field_size={holographic_config.get('field_size')}")
        
        # 2. Inicializar EnhancedDataCollector
        print("\n2️⃣ Inicializando EnhancedDataCollector...")
        symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
        timeframes = ["5m", "15m"]
        
        async with EnhancedDataCollector(symbols=symbols, timeframes=timeframes) as collector:
            print("✅ EnhancedDataCollector inicializado")
            
            # Verificar calibrador
            calibration = collector.get_calibration_report()
            print(f"📊 Calibração inicial: price_amp={calibration['price_amplification']:.2f}, "
                  f"news_amp={calibration['news_amplification']:.2f}")
            
            # 3. Coletar dados enhanced
            print("\n3️⃣ Coletando dados enhanced...")
            enhanced_data = await collector.collect_enhanced_market_data()
            print(f"✅ Coletados {len(enhanced_data)} pontos de dados")
            
            # Mostrar alguns dados
            for i, data in enumerate(enhanced_data[:3]):
                print(f"\n   📊 {data.symbol}:")
                print(f"      Close: ${data.close:.2f}")
                print(f"      RSI: {data.rsi:.1f}" if data.rsi else "      RSI: N/A")
                print(f"      Volume Ratio: {data.volume_ratio:.2f}" if data.volume_ratio else "      Volume Ratio: N/A")
                print(f"      Quantum RSI: {'✅' if data.rsi_quantum_state is not None else '❌'}")
                print(f"      Quantum Volume: {'✅' if data.volume_quantum_state is not None else '❌'}")
            
            # 4. Converter para eventos holográficos
            print("\n4️⃣ Convertendo para eventos holográficos...")
            field_size = tuple(holographic_config.get('field_size', [200, 200]))
            events = collector.convert_to_holographic_events(
                enhanced_data=enhanced_data,
                news_events=[],  # Sem notícias neste teste
                universe_field_size=field_size
            )
            print(f"✅ Convertidos {len(events)} eventos holográficos")
            
            # 5. Criar universo holográfico
            print("\n5️⃣ Criando universo holográfico...")
            universe = HolographicMarketUniverse(
                field_size=field_size,
                time_steps=holographic_config.get('time_steps', 500),
                diffusion_rate=holographic_config.get('diffusion_rate', 0.25),
                feedback_strength=holographic_config.get('feedback_strength', 0.06)
            )
            print("✅ Universo holográfico criado")
            
            # 6. Injetar eventos
            print("\n6️⃣ Injetando eventos no universo...")
            for event in events:
                await universe.inject_holographic_event(event)
            print(f"✅ {len(events)} eventos injetados")
            
            # 7. Evoluir e detectar padrões
            print("\n7️⃣ Evoluindo universo e detectando padrões...")
            await universe.step_evolution(time.time())
            patterns = universe.analyze_holographic_patterns()
            print(f"✅ Detectados {len(patterns)} padrões")
            
            # 8. Gerar sinais
            print("\n8️⃣ Gerando sinais de trading...")
            signals = universe.generate_trading_signals(patterns)
            print(f"✅ Gerados {len(signals)} sinais")
            
            # 9. Simular feedback para calibração
            print("\n9️⃣ Simulando feedback de execução...")
            if len(signals) > 0:
                execution_results = [True, False, True] * (len(signals) // 3 + 1)
                execution_results = execution_results[:len(signals)]
            else:
                execution_results = []
            
            collector.record_pattern_feedback(
                patterns_detected=len(patterns),
                signals_generated=len(signals),
                signals_executed=len(signals),
                execution_results=execution_results
            )
            
            # Verificar se calibração mudou
            new_calibration = collector.get_calibration_report()
            print(f"\n📊 Nova calibração: price_amp={new_calibration['price_amplification']:.2f}, "
                  f"news_amp={new_calibration['news_amplification']:.2f}")
            
            # 10. Resumo final
            print("\n" + "="*60)
            print("📊 RESUMO DO TESTE")
            print("="*60)
            print(f"✅ Dados coletados: {len(enhanced_data)}")
            print(f"✅ Eventos criados: {len(events)}")
            print(f"✅ Padrões detectados: {len(patterns)}")
            print(f"✅ Sinais gerados: {len(signals)}")
            if execution_results:
                print(f"✅ Taxa de sucesso simulada: {sum(execution_results)/len(execution_results)*100:.1f}%")
            else:
                print("⚠️  Sem sinais para executar - sem taxa de sucesso")
            print(f"✅ Calibração funcionando: {'Sim' if new_calibration != calibration else 'Aguardando mais dados'}")
            
            return True
            
    except Exception as e:
        print(f"\n❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Função principal."""
    success = await test_enhanced_integration()
    
    if success:
        print("\n✅ TESTE CONCLUÍDO COM SUCESSO!")
    else:
        print("\n❌ TESTE FALHOU!")
        
    return 0 if success else 1


if __name__ == "__main__":
    import time
    exit_code = asyncio.run(main()) 
