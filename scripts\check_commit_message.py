import re
import sys

TYPES = {"Fix", "Feature", "Refactor", "Docs", "Test", "Perf"}


def main(path: str) -> int:
    with open(path, "r", encoding="utf-8") as f:
        content = f.read().strip()

    lines = content.splitlines()
    summary = lines[0] if lines else ""
    if not re.match(r"^\[(?:" + "|".join(TYPES) + r")\] .+", summary):
        print("Mensagem de commit deve iniciar com '[Tipo] Descrição'. Exemplo: '[Feature] Async persist option'.")
        print("Tipos válidos:", ", ".join(sorted(TYPES)))
        print("Mensagem recebida:", summary)
        return 1

    # Warn if body too short
    body = "\n".join(lines[1:]).strip()
    if body and len(body) < 15:
        print("Aviso: corpo da mensagem de commit parece muito curto ou genérico.")
    elif not body:
        print("Aviso: inclua um corpo de mensagem explicando suas mudanças.")

    return 0


if __name__ == "__main__":
    sys.exit(main(sys.argv[1]))
