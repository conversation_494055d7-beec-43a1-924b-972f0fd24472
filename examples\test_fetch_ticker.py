"""Exemplo manual de uso de ``fetch_ticker`` com a integração da Kraken."""

import os
import asyncio

# Carrega variáveis do .env, se disponível
try:
    from dotenv import load_dotenv

    load_dotenv()
    print("Arquivo .env carregado com sucesso.")
except ImportError:
    print(
        "python-dotenv não está instalado. Se as variáveis não forem reconhecidas, instale com: pip install python-dotenv"
    )

from qualia.market.kraken_integration import KrakenIntegration


async def main():
    k = KrakenIntegration()
    await k.initialize_connection()
    print("\n--- Pares disponíveis na Kraken ---")
    markets = k.exchange.markets
    for pair in [
        "XBT/USD",
        "BTC/USD",
        "XBT/USDT",
        "BTC/USDT",
        "ETH/USD",
        "XETHZUSD",
        "ETH/USDT",
        "XETHUSDT",
    ]:
        exists = pair in markets
        market_id = markets[pair]["id"] if exists else "N/A"
        print(
            f"{pair}: {'DISPONÍVEL' if exists else 'NÃO ENCONTRADO'} | market['id']: {market_id}"
        )
    print("----------------------------------\n")

    # Buscar ticker usando o market['id'] real, se disponível
    for pair in ["BTC/USD", "BTC/USDT", "ETH/USD", "ETH/USDT"]:
        if pair in markets:
            real_id = markets[pair]["id"]
            print(
                f"\nTestando fetch_ticker para market id real: {real_id} (alias: {pair})"
            )
            ticker = await k.fetch_ticker(real_id)
            print(f"Ticker para {real_id}:", ticker)
        else:
            print(f"Par {pair} não encontrado nos mercados carregados.")
    await k.exchange.close()


if __name__ == "__main__":
    asyncio.run(main())
