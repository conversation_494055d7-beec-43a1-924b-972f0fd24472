# Double-Check de Integração do módulo `src/qualia/market`

Este relatório avalia como o pacote `market` se encaixa no ecossistema QUALIA e indica melhorias de curto e longo prazo.

## Integração Sistêmica
- **Event-driven**: o módulo publica `market.ticker_update` e `exchange.lifecycle` no `qualia.events` durante a inicialização e o encerramento das exchanges.
- **Configuração**: `dynamic_risk_controller` e `self_evolving_trading_system` carregam defaults de `config/*.yaml`, mas outras partes do módulo dependem de constantes internas. Não existem feature flags para rollout progressivo.
- **Observabilidade**: métricas DogStatsd estão presentes (`QUALIAMarketPatternDetector`, `SelfEvolvingTradingSystem`), mas falta tracing OpenTelemetry e propagação de `trace_id` até os dashboards.
- **Segurança**: as integrações com exchanges leem API keys via variáveis de ambiente. Há parâmetros de `conn_retries` e `ticker_timeout`, porém não existe verificação explícita de políticas de rate limit.

## Performance & Escalabilidade
- **Benchmark**: não foram identificados testes `pytest-benchmark` para medir latência das operações de mercado.
- **Paralelismo**: há uso de `asyncio` em `kraken_integration`, porém o código não emprega `asyncio.TaskGroup` nem pools de execução para fan-outs.
- **GPU/QPU Hooks**: não há mecanismos para off‑loading de cálculos, mesmo em partes CPU-bound como `qualia_market_pattern_detector`.

## Riscos Identificados
| Gravidade | Esforço | Descrição |
|-----------|--------|-----------|
| Alta | Médio | Falta de integração com o Event Bus reduz a reatividade do pipeline e dificulta observabilidade. |
| Média | Baixo | Ausência de feature flags e configuração unificada em YAML limita o rollout controlado. |
| Média | Médio | Sem tracing OpenTelemetry, torna-se difícil rastrear latência ponta a ponta. |
| Baixa | Baixo | Benchmarks ausentes impedem detectar regressões de throughput. |

## Quick Wins ⚡
- [x] #19 Publicar evento `market.ticker_update` a cada nova cotação no `Qualia Event Bus`.
- [x] #19 Publicar eventos `exchange.lifecycle` em `initialize` e `shutdown`.
- [x] #19 Adicionar `qualia.config.feature_toggle("market_module")` para habilitar novas funcionalidades.
- [x] #19 Criar benchmark simples de `fetch_ticker` usando `pytest-benchmark`.

## Features de Valor
1. **Integração direta com `QuantumPatternMemory`**
   - *User Story*: Como estrategista, desejo que padrões detectados no mercado sejam gravados no `QuantumPatternMemory` via eventos, permitindo correlações mais ricas.
   - *Estimativa*: 4 dias.
2. **Extensão do `DynamicRiskController` via eventos**
   - *User Story*: Como operador, quero ajustar parâmetros de risco em tempo real com mensagens assíncronas, reagindo a regimes de mercado.
   - *Estimativa*: 5 dias.
3. **Tracing completo OpenTelemetry**
   - *User Story*: Como analista, preciso acompanhar o caminho `symbol → trade_decision` no Grafana, identificando gargalos.
   - *Estimativa*: 3 dias.

Todos os riscos classificados como Alta Gravidade devem gerar tickets com responsável e prazo definido.
