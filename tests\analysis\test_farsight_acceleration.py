import numpy as np
from qualia.analysis.farsight_engine import FarsightEngine
from qualia.memory.event_bus import SimpleEventBus


def test_cognitive_acceleration_event_emitted(monkeypatch):
    clusters1 = [
        {
            "topic": "A",
            "curvature": 0.1,
            "velocity": 0.1,
            "prediction_window": "na",
            "sources": [],
        },
        {
            "topic": "B",
            "curvature": 0.1,
            "velocity": 0.2,
            "prediction_window": "na",
            "sources": [],
        },
    ]
    clusters2 = [
        {
            "topic": "A",
            "curvature": 0.1,
            "velocity": 0.5,
            "prediction_window": "na",
            "sources": [],
        },
        {
            "topic": "B",
            "curvature": 0.1,
            "velocity": 0.6,
            "prediction_window": "na",
            "sources": [],
        },
    ]

    call_count = {"n": 0}

    def fake_fetch(self):
        self._papers = [object()] * 10

    def fake_cluster(self):
        if call_count["n"] == 0:
            self._clusters = clusters1
        else:
            self._clusters = clusters2
        call_count["n"] += 1

    bus = SimpleEventBus()
    received = {}

    def handler(payload):
        received.update(payload)

    bus.subscribe("farsight.cognitive_acceleration", handler)
    monkeypatch.setattr(FarsightEngine, "_fetch_papers", fake_fetch)
    monkeypatch.setattr(FarsightEngine, "_cluster_papers", fake_cluster)

    engine = FarsightEngine(event_bus=bus, cognitive_acceleration_threshold=0.2)
    engine.run()  # first run, establishes baseline
    engine.run()  # second run, should trigger acceleration event

    assert "acceleration" in received
    expected_acc = float(np.mean([0.5, 0.6]) - np.mean([0.1, 0.2]))
    assert received["acceleration"] == expected_acc
