#!/usr/bin/env python3
"""
Script para corrigir imports incorretos do tipo 'config.settings'
"""

import os
import re
from pathlib import Path

def fix_imports_in_file(file_path):
    """Corrige imports incorretos de config.settings em um arquivo"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Padrão 1: from qualia.config import settings as settings_module
        # Correção: from qualia.config import settings as settings_module
        pattern1 = r'from qualia\.config\.settings import config\.settings as (\w+)'
        replacement1 = r'from qualia.config import settings as \1'
        content = re.sub(pattern1, replacement1, content)
        
        # Padrão 2: from qualia.config import settings as settings_module
        # Correção: from qualia.config import settings as settings_module
        pattern2 = r'import qualia\.config\.settings as (\w+)'
        replacement2 = r'from qualia.config import settings as \1'
        content = re.sub(pattern2, replacement2, content)
        
        # Padrão 3: from qualia.core import observer as observer_module
        # Correção: from qualia.core import observer as observer_module
        pattern3 = r'from qualia\.core\.observer import core\.observer as (\w+)'
        replacement3 = r'from qualia.core import observer as \1'
        content = re.sub(pattern3, replacement3, content)
        
        # Padrão 4: from qualia.config import config as config_pkg followed by config = config_pkg.config
        # Correção: from qualia.config import config
        pattern4 = r'from qualia\.config import config as config_pkg\s*\n\s*config = config_pkg\.config'
        replacement4 = r'from qualia.config import config'
        content = re.sub(pattern4, replacement4, content, flags=re.MULTILINE)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print("  ✅ Corrigido")
            return True
        else:
            print("  ⚠️ Nenhuma mudança")
            return False
            
    except Exception as e:
        print(f"  ❌ Erro: {e}")
        return False

def main():
    print("🔧 CORRIGINDO IMPORTS INCORRETOS DE CONFIG.SETTINGS")
    
    # Buscar todos os arquivos Python
    python_files = []
    for root, dirs, files in os.walk('.'):
        # Pular diretórios desnecessários
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    # Buscar arquivos com imports incorretos
    problematic_files = []
    patterns = [
        r'from qualia\.config\.settings import config\.settings',
        r'import qualia\.config\.settings',
        r'from qualia\.core\.observer import core\.observer',
        r'config = config_pkg\.config'
    ]
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                for pattern in patterns:
                    if re.search(pattern, content):
                        problematic_files.append(file_path)
                        break
        except:
            continue
    
    print(f"Encontrados {len(problematic_files)} arquivos com imports incorretos")
    
    # Corrigir cada arquivo
    fixed_count = 0
    for file_path in problematic_files:
        print(f"🔄 Corrigindo: {file_path}")
        if fix_imports_in_file(file_path):
            fixed_count += 1
    
    print(f"\n✅ Concluído! {fixed_count}/{len(problematic_files)} arquivos corrigidos")

if __name__ == "__main__":
    main() 
