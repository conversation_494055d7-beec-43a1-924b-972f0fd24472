import numpy as np
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src')))
from qualia.visuals.dynamic_logo import DynamicLogoEngine
import librosa
import sys
import os

# Caminho do áudio
AUDIO_PATH = r"C:\Users\<USER>\Desktop\Audio_for_conversation_ECYFgzf7K5KJoEHVcu4Z.mp3"

# Carrega o áudio e extrai curva de energia (ou brilho)
y, sr = librosa.load(AUDIO_PATH, sr=None, mono=True)
S = np.abs(librosa.stft(y, n_fft=1024, hop_length=512))
energy_curve = S.mean(axis=0)
energy_curve = (energy_curve - energy_curve.min()) / (np.ptp(energy_curve) + 1e-9)

# Inicializa o DynamicLogoEngine
engine = DynamicLogoEngine(fusion_mode="audio→color")

# Renderiza e mostra cada frame (pressione ESC para sair)
for idx, value in enumerate(energy_curve):
    engine.update_audio_color(hue=280, brightness=float(value))
    frame = engine.render()
    engine.show()
    # Opcional: para salvar cada frame como PNG
    # engine.save_png(frame, f"dynamic_logo_frame_{idx:03d}.png")

print("Visualização dinâmica concluída!")
