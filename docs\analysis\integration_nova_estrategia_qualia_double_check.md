# Double-Check de Integração do módulo `src/qualia/strategies/nova_estrategia_qualia`

Este documento avalia como o pacote `nova_estrategia_qualia` se integra ao ecossistema QUALIA e aponta melhorias de curto e longo prazo.

## Integração Sistêmica
- **Contratos**: a estratégia utiliza `QualiaTSVFParams` e expõe `QualiaTSVFStrategy` via registro automático. Não há publicação ou consumo de eventos no *Qualia Event Bus*.
- **Configuração**: parâmetros são definidos em `config/strategy_parameters.json` e podem ser convertidos para `QualiaTSVFParams`. Entretanto não existe feature flag dedicada para ativar a estratégia gradualmente.
- **Histórico mínimo**: a janela `s3_tsvf_window` foi ajustada para `6`, exigindo pelo menos `7` períodos de `s3_resample_period` (28h em `4h`) para que a estratégia inicie.
- **Requisito final armazenado**: o valor calculado após aplicar o limite da exchange passa a ser salvo em `_final_required_initial_data_length`. O aviso "Requisito de dados TSVF excede limite" é emitido apenas quando esse valor se altera.
- **Observabilidade**: `get_logger` é usado e há mensagens estruturadas, porém não há tracing OpenTelemetry.
- **Segurança**: a estratégia depende do `DynamicRiskController` que lida com limites e perfis de risco. Não manipula diretamente chaves de API.

## Performance & Escalabilidade
- **Benchmark**: não foram encontrados testes `pytest-benchmark` específicos para medir latência do método `analyze_market`.
- **Paralelismo**: o código é síncrono e CPU-bound. Há oportunidades para vetorização adicional no cálculo de indicadores.
- **GPU/QPU Hooks**: não há caminhos para off‑load de computação pesada para GPU ou QPU.

## Riscos Identificados
| Gravidade | Esforço | Descrição |
|-----------|--------|-----------|
| Alta | Médio | Ausência de integração com o Event Bus limita a observabilidade e a reação a eventos externos. |
| Média | Baixo | Falta de feature flag específica dificulta rollout controlado da estratégia. |
| Média | Médio | Sem benchmarks, regressões de latência podem passar despercebidas. |
| Baixa | Baixo | Ausência de tracing detalhado reduz visibilidade ponta a ponta. |

## Quick Wins ⚡
- [x] #22 Publicar evento `strategy.signal` a cada chamada de `analyze_market`.
- [x] #22 Adicionar flag `qualia.config.feature_toggle("nova_estrategia")` para ativação gradual.
- [x] #22 Criar caso de benchmark simples para `analyze_market` usando `pytest-benchmark`.

## Features de Valor
1. **Extensão do DynamicRiskController**
   - *User Story*: Como operador, desejo ajustar parâmetros de risco via eventos para reagir a regimes de mercado em tempo real.
   - *Estimativa*: 5 dias.
2. **Suporte opcional a GPU/QPU**
   - *User Story*: Como desenvolvedor, quero que cálculos pesados do TSVF possam rodar em hardware dedicado quando disponível.
   - *Estimativa*: 8 dias.
3. **Integração de métricas no Event Bus**
   - *User Story*: Como analista, desejo receber métricas de força e entropia em tópicos assíncronos para compor dashboards de metacognição.
   - *Estimativa*: 4 dias.

Todos os riscos classificados como Alta Gravidade devem gerar tickets com responsável e prazo definido.
