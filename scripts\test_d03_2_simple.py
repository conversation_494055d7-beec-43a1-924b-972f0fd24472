#!/usr/bin/env python3
"""
YAA: D-03.2 - Teste Simples da Integração Live Feed

Testa se a integração do live feed foi implementada corretamente no QUALIATradingSystem.
"""

import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_live_feed_integration_code():
    """Testa se o código da integração foi adicionado corretamente."""
    logger.info("🧪 Testando código da integração Live Feed...")
    
    results = {
        "timestamp": datetime.now().isoformat(),
        "test_name": "D-03.2 Live Feed Integration Code Test",
        "tests": []
    }
    
    # Teste 1: Verificar se a importação foi adicionada
    trading_system_file = Path("src/qualia/qualia_trading_system.py")
    if not trading_system_file.exists():
        logger.error("❌ Arquivo qualia_trading_system.py não encontrado")
        return False
    
    with open(trading_system_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Verificar importação
    has_import = "from .consciousness.live_feed_integration import LiveFeedIntegration" in content
    results["tests"].append({
        "test": "live_feed_integration_import",
        "status": "PASS" if has_import else "FAIL",
        "details": f"Importação encontrada: {has_import}"
    })
    
    # Verificar atributo no construtor
    has_attribute = "self.live_feed_integration: Optional[LiveFeedIntegration] = None" in content
    results["tests"].append({
        "test": "live_feed_integration_attribute",
        "status": "PASS" if has_attribute else "FAIL",
        "details": f"Atributo encontrado: {has_attribute}"
    })
    
    # Verificar chamada de inicialização
    has_init_call = "await self._init_live_feed_integration()" in content
    results["tests"].append({
        "test": "live_feed_integration_init_call",
        "status": "PASS" if has_init_call else "FAIL",
        "details": f"Chamada de inicialização encontrada: {has_init_call}"
    })
    
    # Verificar método de inicialização
    has_init_method = "async def _init_live_feed_integration(self)" in content
    results["tests"].append({
        "test": "live_feed_integration_init_method",
        "status": "PASS" if has_init_method else "FAIL",
        "details": f"Método de inicialização encontrado: {has_init_method}"
    })
    
    # Calcular resultado geral
    passed_tests = sum(1 for test in results["tests"] if test["status"] == "PASS")
    total_tests = len(results["tests"])
    success_rate = (passed_tests / total_tests) * 100
    
    results.update({
        "tests_passed": passed_tests,
        "tests_total": total_tests,
        "success_rate": success_rate,
        "overall_status": "PASS" if success_rate == 100 else "PARTIAL" if success_rate > 0 else "FAIL"
    })
    
    # Salvar resultados
    os.makedirs("data", exist_ok=True)
    results_file = f"data/d03_2_code_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    logger.info(f"📊 Resultados salvos em: {results_file}")
    
    # Log dos resultados
    for test in results["tests"]:
        status_emoji = "✅" if test["status"] == "PASS" else "❌"
        logger.info(f"{status_emoji} {test['test']}: {test['status']}")
    
    logger.info(f"🎯 Taxa de Sucesso: {success_rate:.1f}% ({passed_tests}/{total_tests})")
    
    return success_rate == 100

def test_configuration_file():
    """Testa se a configuração do live feed está correta."""
    logger.info("🧪 Testando configuração do Live Feed...")
    
    config_file = Path("config/holographic_trading_config.yaml")
    if not config_file.exists():
        logger.error("❌ Arquivo de configuração não encontrado")
        return False
    
    try:
        import yaml
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        # A configuração está aninhada dentro de holographic_trading
        holographic_config = config_data.get("holographic_trading", {})
        live_feed_config = holographic_config.get("live_feed", {})

        # Debug da configuração
        logger.info(f"🔍 Live feed config encontrada: {live_feed_config}")

        # Verificações
        checks = [
            ("enabled", live_feed_config.get("enabled", False) == True),
            ("mode", live_feed_config.get("mode") == "hybrid"),
            ("optimized_params", "optimized_params" in live_feed_config),
            ("data_collector", "data_collector" in live_feed_config)
        ]
        
        all_passed = all(check[1] for check in checks)
        
        for check_name, passed in checks:
            status_emoji = "✅" if passed else "❌"
            logger.info(f"{status_emoji} Configuração {check_name}: {'OK' if passed else 'FALHOU'}")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ Erro ao verificar configuração: {e}")
        return False

def test_component_files():
    """Testa se os arquivos dos componentes existem."""
    logger.info("🧪 Testando existência dos arquivos de componentes...")
    
    required_files = [
        "src/qualia/consciousness/live_feed_integration.py",
        "src/qualia/consciousness/live_feed_data_collector.py",
        "src/qualia/live_feed/kucoin_feed.py",
        "src/qualia/live_feed/data_normalizer.py",
        "src/qualia/live_feed/feed_manager.py",
        "src/qualia/live_feed/feed_aggregator.py"
    ]
    
    all_exist = True
    for file_path in required_files:
        exists = Path(file_path).exists()
        status_emoji = "✅" if exists else "❌"
        logger.info(f"{status_emoji} {file_path}: {'EXISTE' if exists else 'NÃO ENCONTRADO'}")
        if not exists:
            all_exist = False
    
    return all_exist

def main():
    """Função principal do teste."""
    logger.info("🚀 Iniciando testes D-03.2: Live Feed Integration")
    
    tests = [
        ("Código da Integração", test_live_feed_integration_code),
        ("Arquivo de Configuração", test_configuration_file),
        ("Arquivos de Componentes", test_component_files)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Executando: {test_name}")
        try:
            result = test_func()
            if result:
                logger.info(f"✅ {test_name}: PASSOU")
                passed_tests += 1
            else:
                logger.warning(f"❌ {test_name}: FALHOU")
        except Exception as e:
            logger.error(f"💥 {test_name}: ERRO - {e}")
    
    # Resultado final
    success_rate = (passed_tests / total_tests) * 100
    
    print("\n" + "="*60)
    print("📋 RESUMO DOS TESTES D-03.2")
    print("="*60)
    print(f"Taxa de Sucesso: {success_rate:.1f}%")
    print(f"Testes Passaram: {passed_tests}/{total_tests}")
    
    if success_rate == 100:
        print("\n✅ D-03.2: Live Feed Integration implementada com sucesso!")
        print("🚀 Código integrado corretamente no QUALIATradingSystem")
        print("📋 Configuração está correta")
        print("📁 Todos os arquivos de componentes estão presentes")
    else:
        print(f"\n⚠️ D-03.2: Alguns testes falharam ({success_rate:.1f}% sucesso)")
        print("🔧 Verifique os logs para detalhes dos problemas")
    
    return success_rate == 100

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
