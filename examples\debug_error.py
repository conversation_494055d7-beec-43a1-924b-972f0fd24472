import traceback

try:
    # Simular o que está acontecendo no warmup
    print("Testando erro de timeframe...")

    # Simular log com variável não definida
    def test_error():
        # Não definir timeframe aqui
        symbol = "BTCUSDT"
        # timeframe não está definido

        # Tentar usar em uma string formatada
        try:
            msg = f"Erro para {symbol}@{timeframe}"  # Erro aqui!
        except Exception as e:
            print(f"ERRO CAPTURADO: {type(e).__name__}: {e}")
            traceback.print_exc()

    test_error()

except Exception as e:
    print(f"Erro geral: {e}")
    traceback.print_exc()
