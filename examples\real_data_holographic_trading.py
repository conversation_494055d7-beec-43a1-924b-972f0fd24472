#!/usr/bin/env python3
"""
QUALIA Holographic Trading com DADOS REAIS.

Este exemplo demonstra o sistema holográfico usando dados reais de:
- API Binance (preços, volume, mudanças)
- RSS feeds de notícias crypto
- Fear & Greed Index
- Análise de sentiment em tempo real
"""

import asyncio
import time
from typing import List

from src.qualia.consciousness.holographic_universe import (
    HolographicMarketUniverse,
    TradingSignal
)
from src.qualia.consciousness.real_data_collectors import (
    RealDataCollector,
    create_real_data_collector
)
from src.qualia.utils.logger import get_logger

logger = get_logger(__name__)

async def run_real_data_holographic_analysis():
    """
    Executa análise holográfica com dados reais.
    """
    print("🌌 QUALIA Holographic Trading - DADOS REAIS")
    print("=" * 60)
    
    # Inicializa componentes
    universe = HolographicMarketUniverse(
        field_size=(200, 200),
        diffusion_rate=0.3,
        feedback_strength=0.08
    )
    
    print(f"✅ Universo holográfico inicializado: {universe.field_size}")
    
    # Inicializa coletor de dados reais
    async with create_real_data_collector() as collector:
        print("✅ Coletor de dados reais inicializado")
        
        # Coleta dados reais
        print("\n📊 Coletando dados reais...")
        
        # 1. Dados de mercado (Binance)
        print("  • Coletando preços da Binance...")
        market_data = await collector.collect_market_data()
        print(f"    ✅ {len(market_data)} símbolos coletados")
        
        if market_data:
            for data in market_data[:3]:  # Mostra primeiros 3
                print(f"    • {data.symbol}: ${data.price:.4f} ({data.change_24h:+.2f}%)")
        
        # 2. Notícias RSS
        print("  • Coletando notícias RSS...")
        news_events = await collector.collect_news_events()
        print(f"    ✅ {len(news_events)} notícias coletadas")
        
        if news_events:
            for news in news_events[:2]:  # Mostra primeiras 2
                print(f"    • {news.title[:50]}... (sentiment: {news.sentiment_score:.2f})")
        
        # 3. Fear & Greed Index
        print("  • Coletando Fear & Greed Index...")
        fear_greed = await collector.collect_fear_greed_index()
        if fear_greed:
            print(f"    ✅ Fear & Greed: {fear_greed:.0f}/100")
        else:
            print("    ⚠️  Fear & Greed não disponível")
        
        # Converte dados reais para eventos holográficos
        print("\n🔄 Convertendo dados reais para eventos holográficos...")
        holographic_events = collector.convert_to_holographic_events(
            market_data, news_events, universe.field_size
        )
        print(f"✅ {len(holographic_events)} eventos holográficos criados")
        
        # Injeta eventos no universo
        print("\n⚡ Injetando eventos no universo holográfico...")
        for event in holographic_events:
            await universe.inject_holographic_event(event)
            print(f"  • {event.event_type}: amplitude={event.amplitude:.2f} "
                  f"pos={event.position} conf={event.confidence:.2f}")
        
        # Executa simulação holográfica
        print("\n🌊 Executando simulação holográfica...")
        patterns = []
        
        for step in range(60):  # 60 passos de simulação
            current_time = time.time() + step * 0.1
            await universe.step_evolution(current_time)
            
            # Analisa padrões a cada 15 passos
            if step % 15 == 0:
                step_patterns = universe.analyze_holographic_patterns()
                patterns.extend(step_patterns)
                print(f"  • Passo {step}: {len(step_patterns)} padrões detectados")
        
        print(f"✅ Simulação concluída: {len(patterns)} padrões totais")
        
        # Gera sinais de trading baseados em dados reais
        print("\n📈 Gerando sinais de trading...")
        trading_signals = universe.generate_trading_signals(patterns)
        
        # Exibe resultados
        print("\n🎯 RESULTADOS COM DADOS REAIS:")
        print(f"  • Dados de mercado: {len(market_data)} símbolos")
        print(f"  • Notícias analisadas: {len(news_events)}")
        print(f"  • Eventos holográficos: {len(holographic_events)}")
        print(f"  • Padrões detectados: {len(patterns)}")
        print(f"  • Sinais de trading: {len(trading_signals)}")
        
        if trading_signals:
            print("\n📊 SINAIS DE TRADING GERADOS:")
            for signal in trading_signals:
                print(f"  🎯 {signal.symbol}: {signal.action}")
                print(f"     Confiança: {signal.confidence:.2f}")
                print(f"     Força: {signal.strength:.2f}")
                print(f"     Timeframe: {signal.timeframe}")
                print(f"     Razão: {signal.rationale}")
                
                # Mostra dados de origem
                if signal.metadata and 'symbol_position' in signal.metadata:
                    pos = signal.metadata['symbol_position']
                    print(f"     Posição no campo: {pos}")
                print()
        else:
            print("\n⚠️  Nenhum sinal forte o suficiente foi gerado")
            print("    (Isso é normal - o sistema é conservador)")
        
        # Resumo do campo holográfico
        field_summary = universe.get_field_summary()
        print("\n📊 RESUMO DO CAMPO HOLOGRÁFICO:")
        print(f"  • Energia total: {field_summary.get('field_energy', 0):.3f}")
        print(f"  • Entropia: {field_summary.get('field_entropy', 0):.3f}")
        print(f"  • Amplitude máxima: {field_summary.get('max_amplitude', 0):.3f}")
        print(f"  • Eventos ativos: {field_summary.get('active_events', 0)}")
        
        # Correlação com dados de mercado
        if market_data and trading_signals:
            print("\n🔗 CORRELAÇÃO COM MERCADO:")
            for signal in trading_signals:
                # Encontra dados de mercado correspondentes
                market_symbol = signal.symbol + "USDT"
                market_info = next((d for d in market_data if d.symbol == market_symbol), None)
                
                if market_info:
                    print(f"  • {signal.symbol}:")
                    print(f"    Preço atual: ${market_info.price:.4f}")
                    print(f"    Mudança 24h: {market_info.change_24h:+.2f}%")
                    print(f"    Sinal holográfico: {signal.action}")
                    
                    # Verifica concordância
                    market_bullish = market_info.change_24h > 0
                    signal_bullish = signal.action == "BUY"
                    
                    if market_bullish == signal_bullish:
                        print("    ✅ Concordância com tendência atual")
                    else:
                        print("    🔄 Sinal contrário à tendência (possível reversão)")
        
        await universe.shutdown()
        print("\n✅ Análise com dados reais concluída!")

async def run_real_time_monitoring():
    """
    Executa monitoramento em tempo real.
    """
    print("\n🔄 MONITORAMENTO EM TEMPO REAL")
    print("=" * 40)
    print("⚠️  Esta demo coleta dados a cada 60 segundos")
    print("⚠️  Pressione Ctrl+C para parar")
    
    universe = HolographicMarketUniverse()
    
    async with create_real_data_collector() as collector:
        try:
            iteration = 0
            async for events in collector.start_real_time_collection(universe, 60.0):
                iteration += 1
                print(f"\n📊 Iteração {iteration} - {time.strftime('%H:%M:%S')}")
                print(f"  • Eventos coletados: {len(events)}")
                
                # Analisa padrões
                patterns = universe.analyze_holographic_patterns()
                signals = universe.generate_trading_signals(patterns)
                
                print(f"  • Padrões detectados: {len(patterns)}")
                print(f"  • Sinais gerados: {len(signals)}")
                
                if signals:
                    for signal in signals:
                        print(f"    🎯 {signal.symbol}: {signal.action} "
                              f"(conf: {signal.confidence:.2f})")
                
                if iteration >= 3:  # Para depois de 3 iterações na demo
                    print("\n✅ Demo de monitoramento concluída")
                    break
                    
        except KeyboardInterrupt:
            print("\n⏹️  Monitoramento interrompido pelo usuário")
        finally:
            collector.stop_collection()
            await universe.shutdown()

def main():
    """Função principal."""
    print("🚀 QUALIA Holographic Trading - Sistema com DADOS REAIS")
    print("🌐 Conectando com APIs reais...")
    
    try:
        # Demo principal com dados reais
        asyncio.run(run_real_data_holographic_analysis())
        
        # Pergunta se quer monitoramento em tempo real
        response = input("\n❓ Executar monitoramento em tempo real? (y/N): ")
        if response.lower() in ['y', 'yes', 's', 'sim']:
            asyncio.run(run_real_time_monitoring())
        
    except KeyboardInterrupt:
        print("\n⏹️  Programa interrompido pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro: {e}")
        print("💡 Verifique sua conexão com a internet")
    
    print("\n🎉 Programa concluído!")
    print("\n💡 Próximos passos:")
    print("  1. Integrar com sistema de trading automático")
    print("  2. Adicionar mais fontes de dados")
    print("  3. Implementar backtesting com dados históricos")
    print("  4. Configurar alertas em tempo real")

if __name__ == "__main__":
    main() 
