#!/usr/bin/env python3
"""
Análise F<PERSON>ada - Por que Sharpe ainda é negativo?
YAA IMPLEMENTATION: Identifica problemas específicos no Sharpe ratio.
"""

import pandas as pd
import numpy as np
import json
from pathlib import Path

def analyze_sharpe_problems():
    """Analisa os resultados da estratégia corrigida para identificar problemas no Sharpe."""
    
    print("🔍 ANÁLISE FOCADA - PROBLEMAS DO SHARPE NEGATIVO")
    print("=" * 60)
    
    # Carrega resultados da estratégia corrigida
    results_file = Path("results/qualia_corrected_strategy").glob("corrected_results_*.json")
    latest_file = max(results_file, key=lambda x: x.stat().st_mtime)
    
    with open(latest_file, 'r') as f:
        data = json.load(f)
    
    results = data['results']
    valid_results = [r for r in results if 'error' not in r]
    
    print(f"📊 Analisando {len(valid_results)} resultados válidos...")
    
    # Converte para DataFrame
    df = pd.DataFrame(valid_results)
    
    # Análise por símbolo
    print(f"\n📈 ANÁLISE POR SÍMBOLO:")
    for symbol in df['symbol'].unique():
        symbol_data = df[df['symbol'] == symbol]
        
        print(f"\n   🪙 {symbol}:")
        print(f"      • Configurações: {len(symbol_data)}")
        print(f"      • Sharpe médio: {symbol_data['sharpe_ratio'].mean():.3f}")
        print(f"      • Return médio: {symbol_data['total_return_pct'].mean():.2%}")
        print(f"      • Positivas: {(symbol_data['total_return_pct'] > 0).sum()}/{len(symbol_data)}")
        print(f"      • Win rate médio: {symbol_data['win_rate'].mean():.2%}")
        print(f"      • Trades médios: {symbol_data['total_trades'].mean():.0f}")
        
        # Identifica padrões problemáticos
        negative_sharpe = symbol_data[symbol_data['sharpe_ratio'] < 0]
        if len(negative_sharpe) > 0:
            print(f"      • Configurações negativas: {len(negative_sharpe)}")
            print(f"      • Pior Sharpe: {negative_sharpe['sharpe_ratio'].min():.3f}")
            
            # Analisa configurações mais problemáticas
            worst_configs = negative_sharpe.nsmallest(3, 'sharpe_ratio')
            print(f"      • Piores configurações:")
            for _, row in worst_configs.iterrows():
                print(f"        - ({row['price_amplification']:.1f}, {row['news_amplification']:.1f}, {row['min_confidence']:.1f}): "
                      f"Sharpe {row['sharpe_ratio']:.3f}, Return {row['total_return_pct']:.2%}")
    
    # Análise por parâmetros
    print(f"\n🎛️ ANÁLISE POR PARÂMETROS:")
    
    # Price Amplification
    print(f"\n   📊 Price Amplification:")
    for price_amp in sorted(df['price_amplification'].unique()):
        price_data = df[df['price_amplification'] == price_amp]
        print(f"      • {price_amp}: Sharpe médio {price_data['sharpe_ratio'].mean():.3f}, "
              f"Positivas {(price_data['total_return_pct'] > 0).sum()}/{len(price_data)}")
    
    # News Amplification
    print(f"\n   📰 News Amplification:")
    for news_amp in sorted(df['news_amplification'].unique()):
        news_data = df[df['news_amplification'] == news_amp]
        print(f"      • {news_amp}: Sharpe médio {news_data['sharpe_ratio'].mean():.3f}, "
              f"Positivas {(news_data['total_return_pct'] > 0).sum()}/{len(news_data)}")
    
    # Min Confidence
    print(f"\n   🎯 Min Confidence:")
    for min_conf in sorted(df['min_confidence'].unique()):
        conf_data = df[df['min_confidence'] == min_conf]
        print(f"      • {min_conf}: Sharpe médio {conf_data['sharpe_ratio'].mean():.3f}, "
              f"Positivas {(conf_data['total_return_pct'] > 0).sum()}/{len(conf_data)}")
    
    # Análise de correlações
    print(f"\n🔗 CORRELAÇÕES:")
    correlations = df[['price_amplification', 'news_amplification', 'min_confidence', 
                      'sharpe_ratio', 'total_return_pct', 'win_rate', 'total_trades']].corr()
    
    print(f"   • Price Amp vs Sharpe: {correlations.loc['price_amplification', 'sharpe_ratio']:.3f}")
    print(f"   • News Amp vs Sharpe: {correlations.loc['news_amplification', 'sharpe_ratio']:.3f}")
    print(f"   • Min Conf vs Sharpe: {correlations.loc['min_confidence', 'sharpe_ratio']:.3f}")
    print(f"   • Trades vs Sharpe: {correlations.loc['total_trades', 'sharpe_ratio']:.3f}")
    
    # Identifica melhores configurações
    print(f"\n🏆 MELHORES CONFIGURAÇÕES (Sharpe > 2.0):")
    best_configs = df[df['sharpe_ratio'] > 2.0].sort_values('sharpe_ratio', ascending=False)
    
    if len(best_configs) > 0:
        for _, row in best_configs.iterrows():
            print(f"   • {row['symbol']}: ({row['price_amplification']:.1f}, {row['news_amplification']:.1f}, {row['min_confidence']:.1f})")
            print(f"     Sharpe: {row['sharpe_ratio']:.3f}, Return: {row['total_return_pct']:.2%}, "
                  f"Long: {row['long_pct']:.0f}%, Short: {row['short_pct']:.0f}%")
    
    # Recomendações
    print(f"\n💡 RECOMENDAÇÕES PARA CORRIGIR SHARPE:")
    
    # 1. Análise de amplificação
    best_price_amp = df.groupby('price_amplification')['sharpe_ratio'].mean().idxmax()
    best_news_amp = df.groupby('news_amplification')['sharpe_ratio'].mean().idxmax()
    best_min_conf = df.groupby('min_confidence')['sharpe_ratio'].mean().idxmax()
    
    print(f"   1. 🎛️ Melhor Price Amplification: {best_price_amp}")
    print(f"   2. 📰 Melhor News Amplification: {best_news_amp}")
    print(f"   3. 🎯 Melhor Min Confidence: {best_min_conf}")
    
    # 2. Análise de trading frequency
    high_trades = df[df['total_trades'] > df['total_trades'].median()]
    low_trades = df[df['total_trades'] <= df['total_trades'].median()]
    
    print(f"   4. 📊 Trading Frequency:")
    print(f"      • Alto volume (>{df['total_trades'].median():.0f} trades): Sharpe médio {high_trades['sharpe_ratio'].mean():.3f}")
    print(f"      • Baixo volume (<={df['total_trades'].median():.0f} trades): Sharpe médio {low_trades['sharpe_ratio'].mean():.3f}")
    
    # 3. Análise de balance long/short
    balanced_configs = df[
        (df['long_pct'] >= 20) & (df['long_pct'] <= 80) & 
        (df['short_pct'] >= 20) & (df['short_pct'] <= 80)
    ]
    
    if len(balanced_configs) > 0:
        print(f"   5. ⚖️ Configurações balanceadas (20-80% long/short): Sharpe médio {balanced_configs['sharpe_ratio'].mean():.3f}")
    
    # 4. Problema específico identificado
    eth_data = df[df['symbol'] == 'ETH/USDT']
    btc_data = df[df['symbol'] == 'BTC/USDT']
    
    print(f"\n🚨 PROBLEMA IDENTIFICADO:")
    print(f"   • BTC Sharpe médio: {btc_data['sharpe_ratio'].mean():.3f}")
    print(f"   • ETH Sharpe médio: {eth_data['sharpe_ratio'].mean():.3f}")
    
    if eth_data['sharpe_ratio'].mean() < -2.0:
        print(f"   ❌ ETH está puxando a média para baixo!")
        print(f"   💡 SOLUÇÃO: Ajustar parâmetros específicos para ETH ou focar apenas em BTC")
    
    # Configuração recomendada
    print(f"\n🎯 CONFIGURAÇÃO RECOMENDADA:")
    print(f"   • Price Amplification: {best_price_amp}")
    print(f"   • News Amplification: {best_news_amp}")
    print(f"   • Min Confidence: {best_min_conf}")
    print(f"   • Foco: BTC/USDT (melhor performance)")
    
    return {
        'best_price_amp': best_price_amp,
        'best_news_amp': best_news_amp,
        'best_min_conf': best_min_conf,
        'btc_sharpe': btc_data['sharpe_ratio'].mean(),
        'eth_sharpe': eth_data['sharpe_ratio'].mean(),
        'recommendations': [
            f"Usar Price Amp {best_price_amp}",
            f"Usar News Amp {best_news_amp}",
            f"Usar Min Conf {best_min_conf}",
            "Focar em BTC/USDT",
            "Reduzir custos de transação",
            "Ajustar timing dos sinais"
        ]
    }


if __name__ == "__main__":
    recommendations = analyze_sharpe_problems()
    
    # Salva recomendações
    output_file = Path("results/sharpe_fix_recommendations.json")
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'w') as f:
        json.dump(recommendations, f, indent=2)
    
    print(f"\n💾 Recomendações salvas em: {output_file}")
    print(f"\n✅ Análise de Sharpe concluída!")
