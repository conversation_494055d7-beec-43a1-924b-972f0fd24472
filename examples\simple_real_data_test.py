#!/usr/bin/env python3
"""
Teste Simples com Dados Reais - Etapa C
YAA IMPLEMENTATION: Teste básico de integração com dados reais sem dependências complexas.
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

import pandas as pd
import numpy as np

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

try:
    import ccxt.async_support as ccxt
    CCXT_AVAILABLE = True
except ImportError:
    CCXT_AVAILABLE = False
    print("⚠️ CCXT não disponível - usando dados simulados")


class SimpleRealDataFetcher:
    """Fetcher simples de dados reais."""
    
    def __init__(self):
        self.exchange = None
        
    async def initialize(self):
        """Inicializa conexão com exchange."""
        if not CCXT_AVAILABLE:
            print("⚠️ CCXT não disponível - modo simulação")
            return False

        try:
            # Tenta Binance primeiro (mais estável)
            self.exchange = ccxt.binance({
                'sandbox': False,
                'rateLimit': 1200,
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'spot',
                }
            })

            # Testa conexão
            markets = await self.exchange.load_markets()
            print(f"✅ Conectado à Binance - {len(markets)} mercados disponíveis")
            return True

        except Exception as e:
            print(f"⚠️ Falha ao conectar Binance: {e}")

            try:
                # Fallback para KuCoin
                self.exchange = ccxt.kucoin({
                    'sandbox': False,
                    'rateLimit': 1200,
                    'enableRateLimit': True,
                })
                markets = await self.exchange.load_markets()
                print(f"✅ Conectado à KuCoin (fallback) - {len(markets)} mercados disponíveis")
                return True

            except Exception as e2:
                print(f"❌ Falha ao conectar exchanges: {e2}")
                self.exchange = None
                return False
    
    async def fetch_historical_data(self, symbol: str, timeframe: str = "1h", days: int = 90):
        """Busca dados históricos."""
        if not self.exchange:
            return self._generate_mock_data(symbol, days)

        try:
            # Calcula timestamps
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            since = int(start_time.timestamp() * 1000)

            print(f"🔄 Buscando dados reais para {symbol} ({days} dias)...")

            # Verifica se o símbolo existe
            if symbol not in self.exchange.markets:
                print(f"⚠️ Símbolo {symbol} não encontrado. Símbolos disponíveis similares:")
                similar = [s for s in self.exchange.markets.keys() if 'BTC' in s or 'ETH' in s][:5]
                for s in similar:
                    print(f"   - {s}")
                return self._generate_mock_data(symbol, days)

            # Busca dados em chunks para evitar limites
            all_ohlcv = []
            current_since = since
            limit = 500  # Limite conservador

            while current_since < int(end_time.timestamp() * 1000):
                try:
                    ohlcv = await self.exchange.fetch_ohlcv(
                        symbol,
                        timeframe,
                        since=current_since,
                        limit=limit
                    )

                    if not ohlcv:
                        break

                    all_ohlcv.extend(ohlcv)
                    current_since = ohlcv[-1][0] + 1  # Próximo timestamp

                    # Evita rate limiting
                    await asyncio.sleep(0.1)

                except Exception as e:
                    print(f"⚠️ Erro no chunk: {e}")
                    break

            if not all_ohlcv:
                print(f"⚠️ Nenhum dado retornado para {symbol}")
                return self._generate_mock_data(symbol, days)

            # Converte para DataFrame
            df = pd.DataFrame(all_ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)

            # Remove duplicatas e ordena
            df = df[~df.index.duplicated(keep='first')].sort_index()

            # Calcula retornos
            df['returns'] = df['close'].pct_change().fillna(0)

            print(f"✅ Dados reais obtidos para {symbol}: {len(df)} candles de {df.index[0]} a {df.index[-1]}")
            return df

        except Exception as e:
            print(f"❌ Erro ao buscar dados reais para {symbol}: {e}")
            return self._generate_mock_data(symbol, days)
    
    def _generate_mock_data(self, symbol: str, days: int):
        """Gera dados simulados como fallback."""
        print(f"🔄 Gerando dados simulados para {symbol}")
        
        # Parâmetros baseados no símbolo
        if 'BTC' in symbol:
            base_price = 45000
            volatility = 0.04
        elif 'ETH' in symbol:
            base_price = 2800
            volatility = 0.05
        else:
            base_price = 100
            volatility = 0.03
        
        # Gera série temporal
        periods = days * 24  # 1h timeframe
        dates = pd.date_range(
            start=datetime.now() - timedelta(days=days),
            periods=periods,
            freq='1H'
        )
        
        # Simulação de preços
        np.random.seed(42)
        returns = np.random.normal(0, volatility, periods)
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # Cria DataFrame
        df = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.uniform(1000, 10000, periods),
            'returns': [0] + [prices[i]/prices[i-1] - 1 for i in range(1, len(prices))]
        })
        
        df.set_index('timestamp', inplace=True)
        return df


def simple_backtest(df: pd.DataFrame, price_amp: float, news_amp: float, min_conf: float):
    """Backtest simplificado mas mais realista para demonstração."""

    # Copia DataFrame para evitar modificar original
    df = df.copy()

    # Remove linhas com NaN
    df = df.dropna()

    if len(df) < 50:
        # Dados insuficientes
        return {
            'total_return_pct': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown_pct': 0.0,
            'win_rate': 0.0,
            'total_trades': 0,
            'volatility': 0.0
        }

    # Indicadores técnicos
    df['sma_short'] = df['close'].rolling(12).mean()
    df['sma_long'] = df['close'].rolling(24).mean()
    df['rsi'] = calculate_rsi(df['close'], 14)
    df['bb_upper'], df['bb_lower'] = calculate_bollinger_bands(df['close'], 20)

    # Simula confiança baseada na volatilidade e volume
    df['volatility'] = df['returns'].rolling(24).std()
    df['volume_ma'] = df['volume'].rolling(24).mean()
    df['confidence'] = np.clip(
        0.5 + 0.3 * (df['volume'] / df['volume_ma'] - 1) - 0.2 * df['volatility'] / df['volatility'].mean(),
        0.2, 0.9
    )

    # Gera sinais mais sofisticados
    df['signal'] = 0

    # Sinal de compra: SMA cruzamento + RSI oversold + preço próximo à banda inferior
    buy_condition = (
        (df['sma_short'] > df['sma_long']) &
        (df['sma_short'].shift(1) <= df['sma_long'].shift(1)) &  # Cruzamento
        (df['rsi'] < 50) &
        (df['close'] < df['bb_upper'])
    )

    # Sinal de venda: SMA cruzamento + RSI overbought + preço próximo à banda superior
    sell_condition = (
        (df['sma_short'] < df['sma_long']) &
        (df['sma_short'].shift(1) >= df['sma_long'].shift(1)) &  # Cruzamento
        (df['rsi'] > 50) &
        (df['close'] > df['bb_lower'])
    )

    df.loc[buy_condition, 'signal'] = 1
    df.loc[sell_condition, 'signal'] = -1

    # Aplica amplificação baseada nos parâmetros QUALIA
    # price_amp afeta a força do sinal baseado no preço
    # news_amp simula impacto de notícias (aqui usamos volume como proxy)
    # min_conf filtra sinais com baixa confiança

    price_factor = np.clip(price_amp / 5.0, 0.1, 2.0)  # Normaliza price_amp
    news_factor = np.clip(news_amp / 5.0, 0.1, 2.0)    # Normaliza news_amp

    df['volume_factor'] = np.clip(df['volume'] / df['volume_ma'], 0.5, 2.0)
    df['amplified_signal'] = (
        df['signal'] *
        price_factor *
        (news_factor * df['volume_factor']) *
        (df['confidence'] >= min_conf).astype(int)
    )

    # Limita posição máxima
    df['position'] = np.clip(df['amplified_signal'], -1.0, 1.0)

    # Calcula retornos da estratégia com custos de transação
    transaction_cost = 0.001  # 0.1% por trade
    df['position_change'] = df['position'].diff().abs()
    df['transaction_costs'] = df['position_change'] * transaction_cost

    df['strategy_returns'] = (
        df['position'].shift(1) * df['returns'] -
        df['transaction_costs']
    )

    # Remove NaN
    df = df.dropna()

    if len(df) == 0:
        return {
            'total_return_pct': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown_pct': 0.0,
            'win_rate': 0.0,
            'total_trades': 0,
            'volatility': 0.0
        }

    # Calcula métricas
    df['cumulative_returns'] = (1 + df['strategy_returns']).cumprod()

    total_return = df['cumulative_returns'].iloc[-1] - 1
    volatility = df['strategy_returns'].std() * np.sqrt(8760)  # Anualizado
    mean_return = df['strategy_returns'].mean() * 8760  # Anualizado
    sharpe_ratio = mean_return / volatility if volatility > 0 else 0

    # Max drawdown
    running_max = df['cumulative_returns'].expanding().max()
    drawdown = (df['cumulative_returns'] - running_max) / running_max
    max_drawdown = abs(drawdown.min()) if len(drawdown) > 0 else 0

    # Trades e win rate
    trades = int(df['position_change'].sum())
    winning_trades = (df['strategy_returns'] > 0).sum()
    win_rate = winning_trades / len(df) if len(df) > 0 else 0

    return {
        'total_return_pct': total_return,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown_pct': max_drawdown,
        'win_rate': win_rate,
        'total_trades': trades,
        'volatility': volatility
    }


def calculate_rsi(prices, window=14):
    """Calcula RSI."""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi.fillna(50)


def calculate_bollinger_bands(prices, window=20, num_std=2):
    """Calcula Bollinger Bands."""
    sma = prices.rolling(window=window).mean()
    std = prices.rolling(window=window).std()
    upper_band = sma + (std * num_std)
    lower_band = sma - (std * num_std)
    return upper_band.fillna(prices), lower_band.fillna(prices)


async def test_real_data_integration():
    """Testa integração com dados reais."""
    print("🚀 TESTE DE INTEGRAÇÃO COM DADOS REAIS")
    print("=" * 50)
    
    # Inicializa fetcher
    fetcher = SimpleRealDataFetcher()
    connected = await fetcher.initialize()
    
    if not connected:
        print("⚠️ Usando dados simulados para demonstração")
    
    # Símbolos para teste
    symbols = ["BTC/USDT", "ETH/USDT"]
    
    # Parâmetros para teste
    test_params = [
        (2.0, 1.5, 0.7),  # Conservative
        (5.0, 4.0, 0.6),  # Moderate
        (8.0, 7.0, 0.4),  # Aggressive
    ]
    
    results = []
    
    for symbol in symbols:
        print(f"\n📊 Testando {symbol}...")
        
        # Busca dados
        df = await fetcher.fetch_historical_data(symbol, days=60)
        
        if df.empty:
            print(f"❌ Falha ao obter dados para {symbol}")
            continue
        
        print(f"✅ Dados obtidos: {len(df)} candles de {df.index[0]} a {df.index[-1]}")
        
        # Testa diferentes parâmetros
        for price_amp, news_amp, min_conf in test_params:
            try:
                result = simple_backtest(df, price_amp, news_amp, min_conf)
                result.update({
                    'symbol': symbol,
                    'price_amp': price_amp,
                    'news_amp': news_amp,
                    'min_conf': min_conf
                })
                results.append(result)
                
                print(f"   Config ({price_amp:.1f}, {news_amp:.1f}, {min_conf:.1f}): "
                      f"Return {result['total_return_pct']:.2%}, "
                      f"Sharpe {result['sharpe_ratio']:.3f}, "
                      f"Drawdown {result['max_drawdown_pct']:.2%}")
                
            except Exception as e:
                print(f"   ❌ Erro no backtest: {e}")
    
    # Resumo dos resultados
    if results:
        print(f"\n📈 RESUMO DOS RESULTADOS")
        print("=" * 50)
        
        df_results = pd.DataFrame(results)
        
        print(f"Total de testes: {len(results)}")
        print(f"Sharpe médio: {df_results['sharpe_ratio'].mean():.3f}")
        print(f"Return médio: {df_results['total_return_pct'].mean():.2%}")
        print(f"Drawdown médio: {df_results['max_drawdown_pct'].mean():.2%}")
        
        # Melhor resultado
        best = df_results.loc[df_results['sharpe_ratio'].idxmax()]
        print(f"\n🏆 Melhor configuração:")
        print(f"   Symbol: {best['symbol']}")
        print(f"   Parâmetros: ({best['price_amp']:.1f}, {best['news_amp']:.1f}, {best['min_conf']:.1f})")
        print(f"   Performance: Return {best['total_return_pct']:.2%}, Sharpe {best['sharpe_ratio']:.3f}")
        
        # Salva resultados
        output_dir = Path("results/simple_real_data_test")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = int(datetime.now().timestamp())
        output_file = output_dir / f"simple_test_{timestamp}.csv"
        df_results.to_csv(output_file, index=False)
        
        print(f"\n💾 Resultados salvos em: {output_file}")
        
    else:
        print("❌ Nenhum resultado obtido")
    
    # Cleanup
    if fetcher.exchange:
        await fetcher.exchange.close()
    
    print(f"\n✅ Teste concluído!")


if __name__ == "__main__":
    asyncio.run(test_real_data_integration())
