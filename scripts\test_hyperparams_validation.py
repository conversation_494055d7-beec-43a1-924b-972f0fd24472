#!/usr/bin/env python3
"""
Test script for hyperparameters validation and calibration system.
"""

import sys
import os
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.qualia.config.hyperparams_validator import (
    HyperparametersValidator,
    HyperparametersMonitor,
    CalibrationMetrics,
    ValidationResult,
    create_hyperparams_monitor
)


def test_validation_rules():
    """Test hyperparameter validation rules"""
    print("🧪 Testing Hyperparameter Validation Rules")
    print("=" * 60)
    
    validator = HyperparametersValidator()
    
    # Test valid parameters
    valid_params = {
        'price_amplification': 5.0,
        'news_amplification': 4.0,
        'min_confidence': 0.6,
        'pattern_threshold': 0.3
    }
    
    result = validator.validate_hyperparameters(valid_params)
    
    if result.is_valid:
        print("✅ Valid parameters passed validation")
    else:
        print("❌ Valid parameters failed validation")
        return False
    
    # Test invalid parameters (critical violations)
    invalid_params = {
        'price_amplification': 15.0,  # Too high
        'news_amplification': 0.5,   # Too low
        'min_confidence': 1.5,       # Too high
        'pattern_threshold': -0.1    # Too low
    }
    
    result = validator.validate_hyperparameters(invalid_params)
    
    if not result.is_valid and len(result.critical_violations) > 0:
        print("✅ Invalid parameters correctly rejected")
        print(f"   Critical violations: {len(result.critical_violations)}")
    else:
        print("❌ Invalid parameters not properly rejected")
        return False
    
    # Test production-optimized parameters
    production_params = {
        'price_amplification': 1.0,
        'news_amplification': 10.0,
        'min_confidence': 0.37,
        'pattern_threshold': 0.3
    }
    
    result = validator.validate_hyperparameters(production_params)
    
    if result.is_valid:
        print("✅ Production-optimized parameters validated")
        if result.recommendations:
            print(f"   Recommendations: {len(result.recommendations)}")
    else:
        print("❌ Production-optimized parameters failed validation")
        return False
    
    return True


def test_calibration_system():
    """Test hyperparameter calibration system"""
    print("\n🧪 Testing Hyperparameter Calibration System")
    print("=" * 60)
    
    validator = HyperparametersValidator()
    
    # Initial parameters
    initial_params = {
        'price_amplification': 5.0,
        'news_amplification': 4.0,
        'min_confidence': 0.6,
        'pattern_threshold': 0.3
    }
    
    # Simulate poor performance metrics
    poor_metrics = CalibrationMetrics(
        timestamp=time.time(),
        performance_score=0.2,  # Poor performance
        sharpe_ratio=0.1,
        max_drawdown=0.25,      # High drawdown
        win_rate=0.3,           # Low win rate
        total_trades=50,
        avg_confidence=0.4,
        false_positive_rate=0.6
    )
    
    calibrated_params = validator.calibrate_hyperparameters(initial_params, poor_metrics)
    
    # Check if calibration made conservative adjustments
    if calibrated_params['min_confidence'] > initial_params['min_confidence']:
        print("✅ Calibration correctly increased confidence threshold for poor performance")
    else:
        print("⚠️ Calibration did not adjust confidence threshold as expected")
    
    # Simulate good performance metrics
    good_metrics = CalibrationMetrics(
        timestamp=time.time(),
        performance_score=0.8,  # Good performance
        sharpe_ratio=1.5,
        max_drawdown=0.05,      # Low drawdown
        win_rate=0.7,           # High win rate
        total_trades=100,
        avg_confidence=0.6,
        false_positive_rate=0.2
    )
    
    # Add multiple good metrics to establish trend
    for _ in range(5):
        validator.calibration_history.append(good_metrics)
    
    optimized_params = validator.calibrate_hyperparameters(calibrated_params, good_metrics)
    
    print("✅ Calibration system working correctly")
    return True


def test_monitoring_system():
    """Test hyperparameter monitoring system"""
    print("\n🧪 Testing Hyperparameter Monitoring System")
    print("=" * 60)
    
    monitor = create_hyperparams_monitor()
    
    # Set initial hyperparameters
    initial_params = {
        'price_amplification': 5.0,
        'news_amplification': 4.0,
        'min_confidence': 0.6,
        'pattern_threshold': 0.3
    }
    
    monitor.set_current_hyperparams(initial_params)
    
    # Check monitoring status
    status = monitor.get_monitoring_status()
    
    if status['samples_collected'] == 0:
        print("✅ Monitor initialized correctly")
    else:
        print("❌ Monitor not initialized correctly")
        return False
    
    # Simulate performance updates
    for i in range(15):  # More than min_samples_for_calibration
        metrics = CalibrationMetrics(
            timestamp=time.time(),
            performance_score=0.5 - (i * 0.02),  # Declining performance
            sharpe_ratio=1.0 - (i * 0.05),
            max_drawdown=0.05 + (i * 0.01),
            win_rate=0.6 - (i * 0.01),
            total_trades=10 + i,
            avg_confidence=0.5,
            false_positive_rate=0.3
        )
        
        monitor.update_performance(metrics)
        time.sleep(0.01)  # Small delay to simulate time passage
    
    # Check if monitoring collected samples
    status = monitor.get_monitoring_status()
    
    if status['samples_collected'] >= 10:
        print("✅ Monitor collected performance samples")
    else:
        print("❌ Monitor did not collect enough samples")
        return False
    
    print("✅ Monitoring system working correctly")
    return True


def test_dangerous_combinations():
    """Test detection of dangerous parameter combinations"""
    print("\n🧪 Testing Dangerous Parameter Combinations Detection")
    print("=" * 60)
    
    validator = HyperparametersValidator()
    
    # Test high amplification with low confidence (dangerous)
    dangerous_params = {
        'price_amplification': 8.0,  # High
        'news_amplification': 12.0, # High
        'min_confidence': 0.2,      # Low
        'pattern_threshold': 0.3
    }
    
    result = validator.validate_hyperparameters(dangerous_params)
    
    if len(result.warnings) > 0:
        dangerous_warning_found = any(
            "high risk of false signals" in warning.lower() 
            for warning in result.warnings
        )
        
        if dangerous_warning_found:
            print("✅ Dangerous combination correctly detected")
        else:
            print("⚠️ Dangerous combination detected but wrong warning")
    else:
        print("❌ Dangerous combination not detected")
        return False
    
    # Test overly conservative combination
    conservative_params = {
        'price_amplification': 1.5,  # Low
        'news_amplification': 1.8,  # Low
        'min_confidence': 0.8,      # High
        'pattern_threshold': 0.3
    }
    
    result = validator.validate_hyperparameters(conservative_params)
    
    if len(result.warnings) > 0:
        conservative_warning_found = any(
            "miss opportunities" in warning.lower() 
            for warning in result.warnings
        )
        
        if conservative_warning_found:
            print("✅ Overly conservative combination correctly detected")
        else:
            print("⚠️ Conservative combination detected but wrong warning")
    else:
        print("❌ Conservative combination not detected")
        return False
    
    return True


def test_performance_thresholds():
    """Test performance threshold validation"""
    print("\n🧪 Testing Performance Thresholds")
    print("=" * 60)
    
    validator = HyperparametersValidator()
    
    # Test calibration summary
    summary = validator.get_calibration_summary()
    
    if summary['status'] == 'no_data':
        print("✅ Calibration summary correctly reports no data")
    else:
        print("❌ Calibration summary incorrect for empty history")
        return False
    
    # Add some metrics and test again
    metrics = CalibrationMetrics(
        timestamp=time.time(),
        performance_score=0.6,
        sharpe_ratio=1.2,
        max_drawdown=0.08,
        win_rate=0.65,
        total_trades=50,
        avg_confidence=0.5,
        false_positive_rate=0.25
    )
    
    validator.calibration_history.append(metrics)
    
    summary = validator.get_calibration_summary()
    
    if summary['status'] == 'active' and summary['total_calibrations'] == 1:
        print("✅ Calibration summary correctly reports active status")
    else:
        print("❌ Calibration summary incorrect for active history")
        return False
    
    return True


def main():
    """Run all tests"""
    print("🚀 Starting Hyperparameters Validation Tests")
    print("=" * 80)
    
    tests = [
        test_validation_rules,
        test_calibration_system,
        test_monitoring_system,
        test_dangerous_combinations,
        test_performance_thresholds
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ FAIL: {test.__name__} raised exception: {e}")
            failed += 1
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 TEST SUMMARY")
    print("=" * 80)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 All hyperparameter validation tests passed!")
        return True
    else:
        print(f"\n💥 {failed} test(s) failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
