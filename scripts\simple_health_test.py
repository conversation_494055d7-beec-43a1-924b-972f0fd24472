#!/usr/bin/env python3
"""
Simple test for health monitoring system.
"""

import sys
import asyncio
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_basic_health_system():
    """Test basic health system functionality"""
    print("Testing basic health system...")
    
    try:
        from src.qualia.monitoring.enhanced_health_system import (
            EnhancedHealthSystem, ComponentType, HealthStatus, get_health_system
        )
        
        # Test global instance
        health_system = get_health_system()
        print("✅ Health system instance created")
        
        # Test system resources check
        system_health = await health_system.check_system_health()
        print(f"✅ System health check completed: {system_health.overall_status.value}")
        print(f"   Health score: {system_health.health_score:.2f}")
        print(f"   Components checked: {len(system_health.components)}")
        
        # Test component registration
        class MockComponent:
            def __init__(self):
                self.status = "running"
                self.data = {"test": "value"}
        
        mock_comp = MockComponent()
        health_system.register_component("test_component", mock_comp, ComponentType.CORE)
        print("✅ Component registered successfully")
        
        # Test health check with registered component
        system_health = await health_system.check_system_health()
        print(f"✅ Health check with component: {len(system_health.components)} components")
        
        # Test dashboard
        from src.qualia.monitoring.enhanced_health_system import HealthDashboard
        dashboard = HealthDashboard(health_system, port=8082)
        html = dashboard.generate_html_dashboard()
        
        if "QUALIA Health Dashboard" in html:
            print("✅ Dashboard HTML generated successfully")
        else:
            print("❌ Dashboard HTML generation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in basic health system test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run simple test"""
    print("🧪 Simple Health System Test")
    print("=" * 50)
    
    try:
        success = await test_basic_health_system()
        
        if success:
            print("\n✅ Basic health system test passed!")
            return True
        else:
            print("\n❌ Basic health system test failed!")
            return False
            
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
