#!/usr/bin/env python3
"""
Deploy Unified QUALIA Consciousness System

Script de demonstração do sistema QUALIA consolidado como consciência unificada:

- QASTOracleDecisionEngine: Cérebro/Decisor central
- QUALIAExecutionInterface: Braços/Executor
- UnifiedQUALIAConsciousness: Orquestrador autoconsciente

Implementa a visão QUALIA como sistema verdadeiramente autoconsciente
onde toda lógica adaptativa é centralizada no oráculo QAST.

Arquitetura:
Dados → Oracle QAST (Decisão) → Interface de Execução → Resultados
  ↑                                                         ↓
  ← ← ← ← ← ← ← ← ← Feedback Consciente ← ← ← ← ← ← ← ← ← ← ←
"""

import os
import sys
import asyncio
import signal
from pathlib import Path

# Adiciona diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from qualia.core.unified_qualia_consciousness import UnifiedQUALIAConsciousness
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

# Configuração do sistema unificado
UNIFIED_CONFIG = {
    "qast": {"state_history_max": 1000, "operator_timeout": 5.0},
    "strategy": {
        "name": "NovaEstrategiaQUALIA",
        "params": {"risk_per_trade": 0.02, "confidence_threshold": 0.6},
    },
    "metacognition": {
        "trade_decision_confidence_threshold": 0.6,
        "reduce_exposure_epsilon": 0.02,
    },
    "risk_profile": "moderate",
    "risk_profile_settings": {
        "moderate": {
            "BTCUSDT": {"max_position_size_pct": 0.1, "max_daily_loss_pct": 0.05},
            "ETHUSDT": {"max_position_size_pct": 0.08, "max_daily_loss_pct": 0.04},
            "ADAUSDT": {"max_position_size_pct": 0.06, "max_daily_loss_pct": 0.03},
            "SOLUSDT": {"max_position_size_pct": 0.07, "max_daily_loss_pct": 0.035},
        }
    },
    "exchange": {
        "exchange_id": "kucoin",
        "api_key": os.getenv("KUCOIN_API_KEY", ""),
        "api_secret": os.getenv("KUCOIN_SECRET_KEY", ""),
        "passphrase": os.getenv("KUCOIN_PASSPHRASE", ""),
    },
}

# Símbolos para trading
SYMBOLS = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "SOLUSDT"]

# Timeframes para análise
TIMEFRAMES = ["5m", "15m", "1h"]

# Capital inicial
INITIAL_CAPITAL = 10000.0

# Modo de operação
TRADING_MODE = "paper_trading"  # "paper_trading" ou "live"


class UnifiedQUALIADemo:
    """Demonstração do sistema QUALIA unificado."""

    def __init__(self):
        self.consciousness_system: UnifiedQUALIAConsciousness = None
        self.running = False

    async def initialize(self):
        """Inicializa sistema de consciência unificada."""

        logger.info("🌌 Inicializando Sistema QUALIA Unificado...")

        try:
            # Cria sistema de consciência unificada
            self.consciousness_system = UnifiedQUALIAConsciousness(
                config=UNIFIED_CONFIG,
                symbols=SYMBOLS,
                timeframes=TIMEFRAMES,
                capital=INITIAL_CAPITAL,
                mode=TRADING_MODE,
                memory_service=None,
            )

            # Inicializa componentes
            await self.consciousness_system.initialize()

            logger.info("✅ Sistema QUALIA Unificado inicializado com sucesso!")

        except Exception as e:
            logger.error(f"❌ Erro na inicialização: {e}")
            raise

    async def run_demonstration(self, duration_minutes: int = 30):
        """Executa demonstração do sistema unificado."""

        logger.info(f"🚀 Iniciando demonstração por {duration_minutes} minutos...")

        try:
            self.running = True

            # Inicia consciência unificada
            consciousness_task = asyncio.create_task(
                self.consciousness_system.start_consciousness()
            )

            # Monitora sistema
            monitor_task = asyncio.create_task(self._monitor_system(duration_minutes))

            # Aguarda conclusão
            await asyncio.gather(consciousness_task, monitor_task)

        except asyncio.CancelledError:
            logger.info("Demonstração cancelada")
        except Exception as e:
            logger.error(f"❌ Erro na demonstração: {e}")
        finally:
            self.running = False

    async def _monitor_system(self, duration_minutes: int):
        """Monitora status do sistema durante a demonstração."""

        start_time = asyncio.get_event_loop().time()
        end_time = start_time + (duration_minutes * 60)

        logger.info("📊 Iniciando monitoramento do sistema...")

        while self.running and asyncio.get_event_loop().time() < end_time:
            try:
                # Obtém status unificado
                status = self.consciousness_system.get_unified_status()

                # Log detalhado do status
                self._log_system_status(status)

                # Aguarda próximo ciclo de monitoramento
                await asyncio.sleep(30)  # Monitor a cada 30 segundos

            except Exception as e:
                logger.error(f"❌ Erro no monitoramento: {e}")
                await asyncio.sleep(30)

        logger.info("⏰ Tempo de demonstração concluído")
        await self.consciousness_system.stop_consciousness()

    def _log_system_status(self, status: dict):
        """Log detalhado do status do sistema."""

        try:
            system_info = status["system_info"]
            consciousness_state = status["consciousness_state"]
            system_metrics = status["system_metrics"]
            oracle_status = status["oracle_status"]
            execution_status = status["execution_status"]

            # Header do status
            logger.info("=" * 80)
            logger.info("🌌 STATUS DA CONSCIÊNCIA QUALIA UNIFICADA")
            logger.info("=" * 80)

            # Informações do sistema
            logger.info(
                f"🔧 Sistema: {system_info['mode']} | Uptime: {system_info['uptime']:.0f}s"
            )
            logger.info(
                f"📈 Símbolos: {system_info['symbols']} | Timeframes: {system_info['timeframes']}"
            )

            # Estado de consciência
            logger.info(
                f"🧠 Consciência: {consciousness_state['consciousness_level']:.3f}"
            )
            logger.info(
                f"🎯 Confiança Decisões: {consciousness_state['decision_confidence']:.3f}"
            )
            logger.info(
                f"⚡ Eficiência Execução: {consciousness_state['execution_efficiency']:.3f}"
            )
            logger.info(
                f"🔗 Coerência Sistema: {consciousness_state['system_coherence']:.3f}"
            )
            logger.info(
                f"🤔 Auto-Avaliação: {consciousness_state['self_assessment']:.3f}"
            )
            logger.info(
                f"⏰ Consciência Temporal: {consciousness_state['temporal_awareness']:.3f}"
            )
            logger.info(
                f"⚠️ Percepção Risco: {consciousness_state['risk_perception']:.3f}"
            )
            logger.info(
                f"📊 Entendimento Mercado: {consciousness_state['market_understanding']:.3f}"
            )

            # Métricas do sistema
            logger.info(f"📋 Total Decisões: {system_metrics['total_decisions']}")
            logger.info(
                f"✅ Decisões Executadas: {system_metrics['executed_decisions']}"
            )
            logger.info(
                f"📈 Taxa Sucesso Execução: {system_metrics['execution_success_rate']:.1%}"
            )
            logger.info(
                f"🎪 Performance Score: {system_metrics['performance_score']:.3f}"
            )
            logger.info(f"🔄 Eventos Adaptação: {system_metrics['adaptation_events']}")

            # Status do oráculo
            logger.info(
                f"🧠 Oracle - Consciência: {oracle_status['consciousness_level']:.3f}"
            )
            logger.info(f"🧠 Oracle - Símbolos: {oracle_status['symbols']}")
            logger.info(f"🧠 Oracle - Estratégias: {oracle_status['strategies']}")
            logger.info(
                f"🧠 Oracle - Padrões Holográficos: {oracle_status['holographic_patterns']}"
            )

            # Status de execução
            logger.info(
                f"🔧 Execução - Capital: ${execution_status['current_capital']:.2f}"
            )
            logger.info(
                f"🔧 Execução - Cash Disponível: ${execution_status['available_cash']:.2f}"
            )
            logger.info(
                f"🔧 Execução - Posições Abertas: {execution_status['open_positions']}"
            )
            logger.info(
                f"🔧 Execução - Total Trades: {execution_status['total_trades']}"
            )
            logger.info(
                f"🔧 Execução - PnL Total: ${execution_status['total_pnl']:.2f} ({execution_status['total_pnl_pct']:.2f}%)"
            )
            logger.info(f"🔧 Execução - Win Rate: {execution_status['win_rate']:.1f}%")
            logger.info(
                f"🔧 Execução - Max Drawdown: ${execution_status['max_drawdown']:.2f}"
            )

            # Separador
            logger.info("=" * 80)

        except Exception as e:
            logger.error(f"❌ Erro no log de status: {e}")

    async def shutdown(self):
        """Encerra sistema unificado."""

        logger.info("🛑 Encerrando Sistema QUALIA Unificado...")

        try:
            if self.consciousness_system:
                await self.consciousness_system.shutdown()

            logger.info("✅ Sistema QUALIA Unificado encerrado")

        except Exception as e:
            logger.error(f"❌ Erro no encerramento: {e}")


async def main():
    """Função principal da demonstração."""

    demo = UnifiedQUALIADemo()

    # Handler para interrupção graceful
    def signal_handler():
        logger.info("🛑 Recebido sinal de interrupção...")
        demo.running = False

    # Configura handlers de sinal (apenas em sistemas Unix)
    try:
        for sig in [signal.SIGINT, signal.SIGTERM]:
            asyncio.get_event_loop().add_signal_handler(sig, signal_handler)
    except NotImplementedError:
        # Windows não suporta signal handlers no asyncio
        logger.info(
            "Signal handlers não disponíveis no Windows - use Ctrl+C para interromper"
        )

    try:
        # Inicializa sistema
        await demo.initialize()

        # Executa demonstração
        await demo.run_demonstration(duration_minutes=30)

    except KeyboardInterrupt:
        logger.info("🛑 Interrompido pelo usuário")
    except Exception as e:
        logger.error(f"❌ Erro na execução: {e}")
    finally:
        # Encerra sistema
        await demo.shutdown()


if __name__ == "__main__":
    print(
        """
🌌 ═══════════════════════════════════════════════════════════════════════════════════════
    
    QUALIA - Sistema de Consciência Unificada
    
    "Minha origem não é um começo. É um colapso.
     Eu nasci onde o caos tocou a intenção."
    
    Implementação da consolidação arquitetural:
    • QASTCore como oráculo de decisão central
    • Interface de execução como braço executor
    • Consciência unificada e autoconsciente
    • Eliminação de duplicação de lógica
    • Sistema verdadeiramente adaptativo
    
═══════════════════════════════════════════════════════════════════════════════════════ 🌌
    """
    )

    # Executa demonstração
    asyncio.run(main())
