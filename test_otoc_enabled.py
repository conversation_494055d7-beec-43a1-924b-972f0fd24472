#!/usr/bin/env python3
"""
Teste OTOC habilitado.
"""

import sys
sys.path.append('src')
sys.path.append('scripts')

def test_otoc_enabled():
    """Testa OTOC habilitado."""
    try:
        print("🔍 Testando OTOC habilitado...")
        
        from run_fwh_scalp_paper_trading import FWHScalpPaperTradingSystem
        import yaml
        
        # Carregar e verificar configuração
        with open('config/fwh_scalp_config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Verificar estrutura da configuração
        fwh_config = config.get('fibonacci_wave_hype_config', {})
        params_config = fwh_config.get('params', {})
        mtf_config = params_config.get('multi_timeframe_config', {})
        otoc_config = mtf_config.get('otoc_config', {})
        
        print(f"📊 Estrutura da configuração:")
        print(f"   - fibonacci_wave_hype_config: {'✅' if fwh_config else '❌'}")
        print(f"   - params: {'✅' if params_config else '❌'}")
        print(f"   - multi_timeframe_config: {'✅' if mtf_config else '❌'}")
        print(f"   - otoc_config: {'✅' if otoc_config else '❌'}")
        print(f"   - otoc enabled: {otoc_config.get('enabled', 'NOT_FOUND')}")
        
        # Inicializar sistema
        system = FWHScalpPaperTradingSystem('config/fwh_scalp_config.yaml')
        
        print(f"\n🌀 Status OTOC no sistema:")
        print(f"   - otoc_enabled: {system.otoc_enabled}")
        print(f"   - mtf_consolidator: {'✅' if system.mtf_consolidator else '❌'}")
        print(f"   - otoc_metrics_collector: {'✅' if system.otoc_metrics_collector else '❌'}")
        
        if system.otoc_enabled:
            print("\n🎉 OTOC HABILITADO COM SUCESSO!")
            
            # Testar configuração OTOC
            otoc_config_loaded = system.config.get('fibonacci_wave_hype_config', {}).get(
                'params', {}
            ).get('multi_timeframe_config', {}).get('otoc_config', {})
            
            print(f"📋 Configuração OTOC carregada:")
            print(f"   - max_threshold: {otoc_config_loaded.get('max_threshold', 'NOT_FOUND')}")
            print(f"   - window: {otoc_config_loaded.get('window', 'NOT_FOUND')}")
            print(f"   - method: {otoc_config_loaded.get('method', 'NOT_FOUND')}")
            
            return True
        else:
            print("\n❌ OTOC ainda está desabilitado")
            
            # Debug da configuração
            print(f"\n🔧 Debug da configuração:")
            print(f"   - Config path: config/fwh_scalp_config.yaml")
            print(f"   - Config keys: {list(system.config.keys())}")
            
            fwh_debug = system.config.get('fibonacci_wave_hype_config', {})
            print(f"   - FWH config keys: {list(fwh_debug.keys()) if fwh_debug else 'EMPTY'}")
            
            mtf_debug = fwh_debug.get('multi_timeframe_config', {})
            print(f"   - MTF config keys: {list(mtf_debug.keys()) if mtf_debug else 'EMPTY'}")
            
            return False
        
    except Exception as e:
        print(f'❌ Erro: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_otoc_enabled()
    sys.exit(0 if success else 1)
