# Scripts de Depuração

Alguns erros encontrados durante a evolução do QUALIA geraram pequenos scripts de depuração. Eles permanecem no diretório `examples/` para referência manual e não fazem parte da suíte automatizada de testes.

## debug_error.py
Simula um `UnboundLocalError` causado por variável de timeframe não definida. O script mostra como capturar a exceção e imprimir o stack trace.

## debug_timeframe_error.py
Executa `CryptoDataFetcher.fetch_historical_data` com saídas detalhadas para investigar problemas de timeframe em chamadas assíncronas.

## tmp_check.py
Verifica a herança das classes de codificadores quânticos registradas em `encoder_registry`, útil para confirmar a estrutura dos encoders.

Esses utilitários podem ajudar na investigação de comportamentos inesperados, mas não são necessários para a operação normal do sistema.
