from __future__ import annotations

import asyncio
import time
import uuid
from dataclasses import asdict
from datetime import datetime, timezone
from typing import Any, Dict, Optional
import os

from ..common_types import QuantumSignaturePacket
from ..core.position import OpenPosition
from ..fee_management import (
    calculate_entry_fee,
    calculate_exit_fee,
    net_pnl,
)
from ..utils.logger import get_logger
from ..utils.ansi import BOLD, GREEN, RED, RESET, YELLOW, colorize

logger = get_logger(__name__)


async def process_trading_decision(
    trader: Any,
    symbol: str,
    decision: Any,
    current_price: float,
    current_q_signature_packet_arg: Optional[QuantumSignaturePacket],
    market_snapshot_at_decision: Optional[Dict[str, Any]],
    decision_context_details: Optional[Dict[str, Any]],
) -> None:
    """Process the trading decision for a symbol.

    Se a variável de ambiente `QUALIA_FORCE_SIGNAL` estiver definida para "BUY" ou "SELL",
    e o sinal original for "HOLD", o sinal será substituído para facilitar testes end-to-end.
    """

    # ---------------- FORÇAR SINAL PARA TESTES --------------------------
    forced_signal = os.getenv("QUALIA_FORCE_SIGNAL")
    if (
        forced_signal in {"BUY", "SELL", "CLOSE"}
        and getattr(decision, "signal", None) == "HOLD"
    ):
        logger.warning(
            "QUALIA_FORCE_SIGNAL=%s ativo: convertendo sinal HOLD para %s para fins de teste.",
            forced_signal,
            forced_signal,
        )
        try:
            # tentativa 1: mutável
            decision.signal = forced_signal  # type: ignore[attr-defined]
        except Exception:
            # tentativa 2: recriar dataclass/namedtuple imutável
            try:
                decision = type(decision)(**{**decision.__dict__, "signal": forced_signal})  # type: ignore[arg-type]
            except Exception:
                logger.error(
                    "Não foi possível sobrescrever sinal em objeto Decision para testes."
                )
    # --------------------------------------------------------------------

    async with trader._pos_lock:
        active_positions = list(trader.open_positions.get(symbol, []))

    for pos in list(active_positions):
        closed_by_sl_tp = False
        if pos.stop_loss and (
            (pos.side == "buy" and current_price <= pos.stop_loss)
            or (pos.side == "sell" and current_price >= pos.stop_loss)
        ):
            msg = colorize(
                "STOP LOSS ATINGIDO (baseado no trigger_price)", RED, bold=True
            )
            logger.info(f"{msg} para {symbol} em {current_price:.2f}")
            await trader._close_position(
                symbol, pos.order_id, current_price, "stop_loss"
            )
            closed_by_sl_tp = True
        elif pos.take_profit and (
            (pos.side == "buy" and current_price >= pos.take_profit)
            or (pos.side == "sell" and current_price <= pos.take_profit)
        ):
            msg = colorize(
                "TAKE PROFIT ATINGIDO (baseado no trigger_price)", GREEN, bold=True
            )
            logger.info(f"{msg} para {symbol} em {current_price:.2f}")
            await trader._close_position(
                symbol, pos.order_id, current_price, "take_profit"
            )
            closed_by_sl_tp = True

        if closed_by_sl_tp:
            async with trader._pos_lock:
                active_positions = list(trader.open_positions.get(symbol, []))

    if decision.signal == "CLOSE":
        async with trader._pos_lock:
            active_positions = list(trader.open_positions.get(symbol, []))
        if active_positions:
            logger.info("Sinal de FECHAMENTO para %s em %.2f", symbol, current_price)
            for pos in list(active_positions):
                await trader._close_position(
                    symbol, pos.order_id, current_price, "strategy_close"
                )
        else:
            logger.debug(
                "Sinal de FECHAMENTO para %s ignorado, nenhuma posição aberta.", symbol
            )

    elif decision.signal == "BUY":
        async with trader._pos_lock:
            active_positions = list(trader.open_positions.get(symbol, []))
        if active_positions:
            for pos in list(active_positions):
                if pos.side == "sell":
                    logger.info(
                        "Sinal de COMPRA para %s, fechando posição de VENDA %s existente.",
                        symbol,
                        pos.order_id,
                    )
                    await trader._close_position(
                        symbol, pos.order_id, current_price, "close_for_reverse_buy"
                    )
        async with trader._pos_lock:
            active_positions = list(trader.open_positions.get(symbol, []))

        if trader.system_health.get("status") != "ok":
            logger.warning(
                "System health %s; skipping new BUY trades for %s",
                trader.system_health.get("status"),
                symbol,
            )
            return

        if trader.stale_tickers.get(symbol):
            logger.warning(
                "Dados de ticker obsoletos; ignorando nova compra para %s", symbol
            )
            return

        if (
            trader._last_metacognitive_ctx
            and getattr(trader._last_metacognitive_ctx, "trade_directive", None)
            == "REDUCE_EXPOSURE"
        ):
            directive_age = datetime.now(timezone.utc) - getattr(
                trader._last_metacognitive_ctx,
                "timestamp",
                datetime.now(timezone.utc),
            )
            if directive_age.total_seconds() < trader.METACOG_DIRECTIVE_TTL_SECONDS:
                logger.info(
                    "Entrada em %s bloqueada por diretiva metacognitiva REDUCE_EXPOSURE",
                    symbol,
                )
                return
            logger.info(
                "Diretiva REDUCE_EXPOSURE expirada há %s. Liberando entradas para %s.",
                directive_age,
                symbol,
            )
            trader._last_metacognitive_ctx.trade_directive = None

        if symbol not in trader.risk_managers:
            message = (
                f"RiskManager para {symbol} não foi inicializado. "
                "Execute _setup_risk_managers ou configure 'risk_profile_configs' "
                "corretamente para criar o RiskManager."
            )
            logger.error(message)
            raise ValueError(message)

        can_open, reason = trader.risk_managers[symbol].can_open_new_position(
            len(active_positions)
        )
        if not can_open:
            logger.warning("OrderSkipped reason=%s", reason)
            return

        if trader.stale_tickers.get(symbol) or trader._consecutive_ticker_failures > 1:
            logger.warning(
                "Ticker inválido ou falhas consecutivas (%s); abortando nova compra para %s",
                trader._consecutive_ticker_failures,
                symbol,
            )
            return

        trade_size_details = trader.risk_managers[symbol].calculate_position_size(
            current_price=current_price,
            stop_loss_price=decision.stop_loss,
            confidence=decision.confidence,
            symbol=symbol,
            lambda_factor=getattr(
                trader.qualia_universe, "lambda_factor_multiplier", 1.0
            ),
        )
        trade_size = trade_size_details.get("quantity", 0.0)

        if trade_size > 0:
            logger.info("Sinal de COMPRA para %s em %.2f", symbol, current_price)
            await trader._open_position(
                symbol,
                "buy",
                current_price,
                trade_size,
                decision.stop_loss,
                decision.take_profit,
                q_signature_packet_arg=current_q_signature_packet_arg,
                market_snapshot=market_snapshot_at_decision,
                decision_context=decision_context_details,
            )
        else:
            logger.info(
                "OrderSkipped trade_size=%s reason=%s",
                trade_size,
                trade_size_details.get("reason", "N/A"),
            )

    elif decision.signal == "SELL":
        async with trader._pos_lock:
            active_positions = list(trader.open_positions.get(symbol, []))
        if active_positions:
            for pos in list(active_positions):
                if pos.side == "buy":
                    logger.info(
                        "Sinal de VENDA para %s, fechando posição de COMPRA %s existente.",
                        symbol,
                        pos.order_id,
                    )
                    await trader._close_position(
                        symbol, pos.order_id, current_price, "close_for_reverse_sell"
                    )
        async with trader._pos_lock:
            active_positions = list(trader.open_positions.get(symbol, []))

        if trader.system_health.get("status") != "ok":
            logger.warning(
                "System health %s; skipping new SELL trades for %s",
                trader.system_health.get("status"),
                symbol,
            )
            return

        if trader.stale_tickers.get(symbol):
            logger.warning(
                "Dados de ticker obsoletos; ignorando nova venda para %s", symbol
            )
            return

        if (
            trader._last_metacognitive_ctx
            and getattr(trader._last_metacognitive_ctx, "trade_directive", None)
            == "REDUCE_EXPOSURE"
        ):
            directive_age = datetime.now(timezone.utc) - getattr(
                trader._last_metacognitive_ctx,
                "timestamp",
                datetime.now(timezone.utc),
            )
            if directive_age.total_seconds() < trader.METACOG_DIRECTIVE_TTL_SECONDS:
                logger.info(
                    "Entrada em %s bloqueada por diretiva metacognitiva REDUCE_EXPOSURE",
                    symbol,
                )
                return
            logger.info(
                "Diretiva REDUCE_EXPOSURE expirada há %s. Liberando entradas para %s.",
                directive_age,
                symbol,
            )
            trader._last_metacognitive_ctx.trade_directive = None

        if symbol not in trader.risk_managers:
            message = (
                f"RiskManager para {symbol} não foi inicializado. "
                "Execute _setup_risk_managers ou configure 'risk_profile_configs' "
                "corretamente para criar o RiskManager."
            )
            logger.error(message)
            raise ValueError(message)

        can_open, reason = trader.risk_managers[symbol].can_open_new_position(
            len(active_positions)
        )
        if not can_open:
            logger.debug(
                "RiskManager bloqueou nova posição para %s: %s", symbol, reason
            )
            return

        if trader.stale_tickers.get(symbol) or trader._consecutive_ticker_failures > 1:
            logger.warning(
                "Ticker inválido ou falhas consecutivas (%s); abortando nova venda para %s",
                trader._consecutive_ticker_failures,
                symbol,
            )
            return

        trade_size_details = trader.risk_managers[symbol].calculate_position_size(
            current_price=current_price,
            stop_loss_price=decision.stop_loss,
            confidence=decision.confidence,
            symbol=symbol,
            side="sell",
            lambda_factor=getattr(
                trader.qualia_universe, "lambda_factor_multiplier", 1.0
            ),
        )
        trade_size = trade_size_details.get("quantity", 0.0)

        if trade_size > 0:
            logger.info("Sinal de VENDA para %s em %.2f", symbol, current_price)
            await trader._open_position(
                symbol,
                "sell",
                current_price,
                trade_size,
                decision.stop_loss,
                decision.take_profit,
                q_signature_packet_arg=current_q_signature_packet_arg,
                market_snapshot=market_snapshot_at_decision,
                decision_context=decision_context_details,
            )
        else:
            logger.info(
                "OrderSkipped trade_size=%s reason=%s",
                trade_size,
                trade_size_details.get("reason", "N/A"),
            )

    elif decision.signal == "HOLD":
        logger.debug("Sinal de HOLD para %s processado.", symbol)


async def _place_live_order(
    trader: Any,
    symbol: str,
    side: str,
    size: float,
    entry_price: float,
    limit_price: Optional[float] = None,
) -> tuple[float, Optional[str], bool]:
    """Place a live order on the exchange.

    Parameters
    ----------
    trader
        Trader instance with ``exchange`` configured.
    symbol
        Market symbol to trade.
    side
        ``buy`` or ``sell``.
    size
        Order size.
    entry_price
        Context price prior to submission.

    Returns
    -------
    tuple
        ``(actual_price, order_id, success)``
    """

    final_order_id: Optional[str] = None
    actual_entry_price = entry_price
    try:
        order_type = "limit" if limit_price is not None else "market"
        logger.info(
            "Attempting to open LIVE position for %s: Side=%s, Size=%s %s at %.2f (context %.2f)",
            symbol,
            side,
            size,
            order_type,
            limit_price if limit_price is not None else entry_price,
            entry_price,
        )
        if limit_price is not None:
            order_result = await trader.exchange.create_order(
                symbol, order_type, side, size, price=limit_price
            )
        else:
            order_result = await trader.exchange.create_order(
                symbol, order_type, side, size
            )
        if order_result and "id" in order_result:
            final_order_id = order_result["id"]
            if order_result.get("price"):
                actual_entry_price = float(order_result["price"])
            logger.info(
                "Ordem LIVE %s para %s %s %s @ %.2f (context price %.2f) enviada e confirmada.",
                final_order_id,
                side,
                size,
                symbol,
                actual_entry_price,
                entry_price,
            )
            return actual_entry_price, final_order_id, True
        logger.error(
            "Falha ao executar ordem LIVE para %s. Resultado inesperado: %s. Nenhuma posição será aberta.",
            symbol,
            order_result,
        )
    except Exception as exc:
        logger.error(
            "Erro CRÍTICO ao criar ordem LIVE para %s: %s", symbol, exc, exc_info=True
        )
    return actual_entry_price, final_order_id, False


async def _simulate_paper_order(
    trader: Any,
    symbol: str,
    side: str,
    size: float,
    entry_price: float,
    limit_price: Optional[float] = None,
) -> tuple[float, str, bool]:
    """Simulate an order when in paper trading mode."""

    actual_entry_price = entry_price
    try:
        current_ticker = trader.current_tickers.get(symbol, {})
        if side == "buy" and current_ticker.get("ask"):
            ask_value = current_ticker.get("ask")
            if hasattr(ask_value, "__await__"):
                ask_value = await ask_value
            actual_entry_price = float(ask_value)
        elif side == "sell" and current_ticker.get("bid"):
            bid_value = current_ticker.get("bid")
            if hasattr(bid_value, "__await__"):
                bid_value = await bid_value
            actual_entry_price = float(bid_value)
        if limit_price is not None:
            actual_entry_price = limit_price
            final_order_id = f"paper_limit_{int(time.time() * 1000)}"
        else:
            final_order_id = f"paper_ord_{int(time.time() * 1000)}"
        logger.info(
            "[PAPER TRADING] Ordem simulada %s para %s %s %s @ %.2f",
            final_order_id,
            side,
            size,
            symbol,
            actual_entry_price,
        )
        return actual_entry_price, final_order_id, True
    except Exception as exc:
        logger.error(
            "Erro ao simular ordem PAPER para %s: %s", symbol, exc, exc_info=True
        )
    return actual_entry_price, f"paper_fail_{int(time.time() * 1000)}", False


def _update_wallet_after_open(
    trader: Any, price: float, size: float, entry_fee: float
) -> None:
    """Update wallet state after opening a position."""

    if hasattr(trader, "wallet_state") and trader.wallet_state:
        trader.wallet_state["available_cash"] -= price * size + entry_fee
        trader.wallet_state["positions_value"] += price * size
        trader.wallet_state["total_fees_paid"] = (
            trader.wallet_state.get("total_fees_paid", 0.0) + entry_fee
        )
        trader.wallet_state["current_capital"] = (
            trader.wallet_state["available_cash"]
            + trader.wallet_state["positions_value"]
        )


async def open_position(
    trader: Any,
    symbol: str,
    side: str,
    entry_price: float,
    size: float,
    stop_loss: Optional[float],
    take_profit: Optional[float],
    q_signature_packet_arg: Optional[QuantumSignaturePacket],
    market_snapshot: Optional[Dict[str, Any]],
    decision_context: Optional[Dict[str, Any]],
    limit_price: Optional[float] = None,
) -> Optional[OpenPosition]:
    """Open a new position.

    Parameters
    ----------
    trader
        Trader instance.
    symbol
        Market symbol to trade.
    side
        ``buy`` or ``sell``.
    entry_price
        Contextual price used for calculations.
    size
        Order size.
    stop_loss
        Price for stop loss adjustment.
    take_profit
        Price for take profit adjustment.
    q_signature_packet_arg
        Optional quantum signature packet.
    market_snapshot
        Snapshot of market data at decision time.
    decision_context
        Additional context details.
    limit_price
        Optional price to submit a limit order. When ``None`` a market order is
        used.
    """

    last_close = trader.last_trade_close_time.get(symbol, 0.0)
    trade_interval = getattr(trader, "trade_interval", 10)

    if time.time() - last_close < trade_interval:
        logger.info(
            "Ignorando abertura para %s: último trade fechado há %.2fs",
            symbol,
            time.time() - last_close,
        )
        return None

    # A locking context here served no functional purpose and only declared a
    # placeholder variable. It was removed to streamline the open position flow.
    now_millisecs = int(time.time() * 1000)
    uuid_short = str(uuid.uuid4().hex)[:8]
    generated_order_id = f"ord_{now_millisecs}_{uuid_short}"

    timestamp_entry = datetime.now(timezone.utc)
    actual_entry_price = entry_price
    final_order_id: Optional[str] = None
    live_order_succeeded = False

    if trader.mode == "live" and trader.exchange:
        (
            actual_entry_price,
            final_order_id,
            live_order_succeeded,
        ) = await _place_live_order(
            trader, symbol, side, size, entry_price, limit_price
        )
    elif trader.mode == "paper_trading" and trader.exchange:
        (
            actual_entry_price,
            final_order_id,
            live_order_succeeded,
        ) = await _simulate_paper_order(
            trader, symbol, side, size, entry_price, limit_price
        )
    else:
        final_order_id = generated_order_id
        live_order_succeeded = True

    if not live_order_succeeded and trader.mode == "live":
        return None

    if stop_loss is not None:
        stop_loss = actual_entry_price + (stop_loss - entry_price)
    if take_profit is not None:
        take_profit = actual_entry_price + (take_profit - entry_price)

    async with trader._pos_lock:
        if trader.mode != "live" or not final_order_id:
            current_generated_id = f"{symbol}-QS-{uuid.uuid4().hex[:12]}"
            while current_generated_id in trader.live_order_ids:
                logger.warning(
                    "Colisão de ID gerado %s detectada (simulação). Gerando novo.",
                    current_generated_id,
                )
                current_generated_id = f"{symbol}-QS-{uuid.uuid4().hex[:12]}"
            final_order_id = current_generated_id
        elif final_order_id in trader.live_order_ids:
            logger.warning(
                "Order ID da exchange %s para %s já existia em live_order_ids. Fluxo inesperado.",
                final_order_id,
                symbol,
            )
        trader.live_order_ids.add(final_order_id)
        entry_fee = calculate_entry_fee(
            actual_entry_price, size, trader.trading_fee_pct
        )

        new_position = OpenPosition(
            symbol=symbol,
            order_id=final_order_id,
            entry_price=actual_entry_price,
            size=size,
            side=side,
            timestamp=timestamp_entry,
            stop_loss=stop_loss,
            take_profit=take_profit,
            captured_quantum_signature_packet=q_signature_packet_arg,
            market_snapshot_at_decision=market_snapshot,
            decision_context_details=decision_context,
            total_fees=entry_fee,
        )
        if symbol not in trader.open_positions:
            trader.open_positions[symbol] = []
        trader.open_positions[symbol].append(new_position)

        if getattr(trader, "order_journal", None):
            from ..utils.persistence import convert_to_serializable

            trader.order_journal.append(
                {"event": "open", **convert_to_serializable(asdict(new_position))}
            )

        _update_wallet_after_open(trader, actual_entry_price, size, entry_fee)

        log_color = GREEN if side == "buy" else RED
        status_msg = colorize("POSIÇÃO ABERTA (INTERNA)", log_color, bold=True)
        logger.info(
            f"{status_msg}: {side.upper()} {size:.4f} {symbol} @ {actual_entry_price:.2f} (Order ID: {final_order_id}), SL: {stop_loss}, TP: {take_profit}"
        )
        logger.debug(
            "Detalhes da Posição Aberta (%s): %s", final_order_id, new_position
        )

    return new_position


async def execute_close_order(
    trader: Any,
    symbol: str,
    position: Dict[str, Any],
    trigger_price: float,
    trigger_price_current: Optional[float] = None,
) -> tuple[float, bool, str]:
    """Execute the close order on the exchange or simulation."""

    actual_exit_price = trigger_price_current or trigger_price
    success = False
    close_order_id = None

    if trader.mode == "live" and trader.exchange:
        close_side = "sell" if position["side"] == "buy" else "buy"
        logger.info(
            "Attempting to close LIVE position for %s (%s): Side=%s, Size=%s at Market (trigger price: %.2f)",
            symbol,
            position["order_id"],
            close_side,
            position["size"],
            trigger_price,
        )
        try:
            if trigger_price_current is None:
                try:
                    ticker = await trader._fetch_ticker(symbol)
                    if close_side == "sell" and ticker.get("bid"):
                        actual_exit_price = float(ticker.get("bid"))
                    elif close_side == "buy" and ticker.get("ask"):
                        actual_exit_price = float(ticker.get("ask"))
                except Exception:
                    pass
            order_result = await asyncio.wait_for(
                trader.exchange.create_order(
                    symbol,
                    "market",
                    close_side,
                    position["size"],
                ),
                timeout=trader.ORDER_EXEC_TIMEOUT,
            )
            if order_result and order_result.get("id"):
                if order_result.get("price") is not None:
                    actual_exit_price = float(order_result["price"])
                close_order_id = str(order_result["id"])
                success = True
                logger.info(
                    "LIVE position %s for %s closed on exchange. Exchange Order ID: %s, Fill Price: %.2f",
                    position["order_id"],
                    symbol,
                    order_result["id"],
                    actual_exit_price,
                )
            else:
                logger.error(
                    "Failed to close LIVE position for %s on exchange. Order result: %s. Position state with broker is uncertain.",
                    symbol,
                    order_result,
                )
                close_order_id = f"live_close_{int(time.time() * 1000)}"
        except asyncio.TimeoutError:
            logger.warning(
                "Tempo limite de %ss excedido ao fechar posição LIVE para %s (%s)",
                trader.ORDER_EXEC_TIMEOUT,
                symbol,
                position["order_id"],
            )
        except asyncio.CancelledError:
            logger.error(
                "OPERAÇÃO DE FECHAMENTO CANCELADA para %s (%s) durante chamada à exchange. INCERTO.",
                symbol,
                position["order_id"],
            )
            raise
        except Exception as exc:
            logger.error(
                "Erro CRÍTICO ao tentar fechar posição LIVE para %s (%s): %s",
                symbol,
                position["order_id"],
                exc,
                exc_info=True,
            )
    elif trader.mode == "paper_trading" and trader.exchange:
        close_side = "sell" if position["side"] == "buy" else "buy"
        try:
            current_ticker = trader.current_tickers.get(symbol, {})
            if not current_ticker or trader.stale_tickers.get(symbol, False):
                try:
                    current_ticker = await trader._fetch_ticker(symbol)
                    trader.current_tickers[symbol] = current_ticker
                    trader.stale_tickers[symbol] = False
                except Exception as exc:
                    logger.warning(
                        "Falha ao buscar ticker fresco para %s no fechamento: %s",
                        symbol,
                        exc,
                    )
            if trigger_price_current is None:
                if close_side == "sell" and current_ticker.get("bid"):
                    actual_exit_price = float(current_ticker.get("bid"))
                elif close_side == "buy" and current_ticker.get("ask"):
                    actual_exit_price = float(current_ticker.get("ask"))
            close_order_id = f"paper_close_{int(time.time() * 1000)}"
            success = True
            logger.info(
                "[PAPER TRADING] Fechando posição simulada %s para %s: %s %s @ %.2f",
                position["order_id"],
                symbol,
                close_side,
                position["size"],
                actual_exit_price,
            )
        except Exception as exc:
            logger.error(
                "Erro ao simular fechamento PAPER para %s: %s",
                symbol,
                exc,
                exc_info=True,
            )
            success = True
            close_order_id = f"paper_close_{int(time.time() * 1000)}"
    else:
        success = True
        close_order_id = f"sim_close_{int(time.time() * 1000)}"

    return actual_exit_price, success, str(close_order_id)


async def close_position(
    trader: Any,
    symbol: str,
    order_id: str,
    trigger_price: float,
    reason: str,
) -> Optional[Dict[str, Any]]:
    """Orchestrates closing of a position."""

    details = await trader._fetch_position_details(symbol, order_id)
    if details is None:
        return None

    exit_price, success, close_order_id = await execute_close_order(
        trader,
        symbol,
        details,
        trigger_price,
        trigger_price,
    )

    if not success and trader.mode == "live":
        logger.warning(
            "Fechamento da posição para %s (%s) não foi bem-sucedido na exchange. Estado interno não será alterado.",
            symbol,
            order_id,
        )
        return None

    try:
        return await finalize_close(
            trader, symbol, order_id, exit_price, reason, details, close_order_id
        )
    except RuntimeError:
        return None


def _compute_trade_result(
    trader: Any, details: Dict[str, Any], exit_price: float, entry_fee: float
) -> Dict[str, float]:
    """Compute PnL and fee metrics for a closed trade."""

    if details["side"] == "buy":
        raw_pnl = (exit_price - details["entry_price"]) * details["size"]
    else:
        raw_pnl = (details["entry_price"] - exit_price) * details["size"]

    exit_fee = calculate_exit_fee(exit_price, details["size"], trader.trading_fee_pct)
    pnl_value_wallet = raw_pnl - exit_fee
    pnl_value_net = net_pnl(raw_pnl, entry_fee, exit_fee)
    entry_value = details["entry_price"] * details["size"]
    pnl_pct_value = (pnl_value_net / entry_value) * 100 if entry_value != 0 else 0.0
    total_fees = entry_fee + exit_fee
    fees_pct_value = (total_fees / entry_value) * 100 if entry_value != 0 else 0.0

    return {
        "raw_pnl": raw_pnl,
        "exit_fee": exit_fee,
        "pnl_value_wallet": pnl_value_wallet,
        "pnl_value_net": pnl_value_net,
        "pnl_pct_value": pnl_pct_value,
        "total_fees": total_fees,
        "fees_pct_value": fees_pct_value,
        "entry_value": entry_value,
    }


def _update_wallet_after_close(trader: Any, result: Dict[str, float]) -> None:
    """Update wallet statistics after closing a position."""

    if hasattr(trader, "wallet_state") and trader.wallet_state:
        trader.wallet_state["current_capital"] += result["pnl_value_wallet"]
        trader.wallet_state["available_cash"] += (
            result["entry_value"] + result["pnl_value_wallet"]
        )
        trader.wallet_state["positions_value"] -= result["entry_value"]
        trader.wallet_state["total_pnl"] += result["pnl_value_net"]
        trader.wallet_state["total_fees_paid"] = (
            trader.wallet_state.get("total_fees_paid", 0.0) + result["exit_fee"]
        )
        trader.wallet_state["total_trades"] += 1
        if result["pnl_value_net"] > 0:
            trader.wallet_state["winning_trades"] += 1
        else:
            trader.wallet_state["losing_trades"] += 1
        if trader.wallet_state["total_trades"] > 0:
            trader.wallet_state["win_rate"] = (
                trader.wallet_state["winning_trades"]
                / trader.wallet_state["total_trades"]
            ) * 100
            trader.wallet_state["loss_rate"] = (
                trader.wallet_state["losing_trades"]
                / trader.wallet_state["total_trades"]
            ) * 100
        trader.wallet_state["total_pnl_pct"] = (
            (trader.wallet_state["total_pnl"] / trader.wallet_state["initial_capital"])
            * 100
            if trader.wallet_state["initial_capital"] > 0
            else 0
        )
        trader.wallet_state["current_capital"] = (
            trader.wallet_state["available_cash"]
            + trader.wallet_state["positions_value"]
        )
        trader._save_open_positions()


async def finalize_close(
    trader: Any,
    symbol: str,
    order_id: str,
    exit_price: float,
    reason: str,
    details: Dict[str, Any],
    close_order_id: str,
) -> Dict[str, Any]:
    """Finalize the closing process updating internal state and wallet."""

    async with trader._pos_lock:
        positions = trader.open_positions.get(symbol, [])
        if not any(p.order_id == order_id for p in positions):
            logger.warning(
                "Inconsistência ao fechar %s: posição %s não encontrada. Possível race condition ou fechamento externo.",
                symbol,
                order_id,
            )
            raise RuntimeError("position-not-found")

        closed_position_obj = next(p for p in positions if p.order_id == order_id)
        trader.open_positions[symbol] = [p for p in positions if p.order_id != order_id]
        if not trader.open_positions[symbol]:
            trader.open_positions.pop(symbol, None)
        timestamp_exit = datetime.now(timezone.utc)
        entry_time = details["timestamp"]
        if entry_time.tzinfo is None:
            entry_time = entry_time.replace(tzinfo=timezone.utc)

        if details["order_id"] in trader.live_order_ids:
            trader.live_order_ids.remove(details["order_id"])
        else:
            logger.warning(
                "Order ID %s para %s não encontrado em live_order_ids ao fechar.",
                details["order_id"],
                symbol,
            )

        entry_fee = getattr(closed_position_obj, "total_fees", 0.0)
        result = _compute_trade_result(trader, details, exit_price, entry_fee)
        closed_position_obj.total_fees = result["total_fees"]
        pnl_value_wallet = result["pnl_value_wallet"]
        pnl_value_net = result["pnl_value_net"]
        pnl_pct_value = result["pnl_pct_value"]
        entry_value = result["entry_value"]
        exit_fee = result["exit_fee"]
        total_fees = result["total_fees"]
        fees_pct_value = result["fees_pct_value"]

        closed_position_obj.pnl = pnl_value_net
        closed_position_obj.pnl_pct = pnl_pct_value
        closed_position_obj.status = f"closed_{reason}"

        if trader.qpm_memory and details.get("captured_quantum_signature_packet"):
            outcome_data_for_qpm = {
                "pnl": pnl_value_net,
                "pnl_percentage": pnl_pct_value,
                "duration_seconds": (timestamp_exit - entry_time).total_seconds(),
                "reason_for_close": reason,
                "exit_price": exit_price,
                "entry_price": details["entry_price"],
                "symbol": symbol,
                "side": details["side"],
                "size": details["size"],
            }
            try:
                await asyncio.to_thread(
                    trader.qpm_memory.store_pattern,
                    quantum_signature_packet=details[
                        "captured_quantum_signature_packet"
                    ],
                    market_snapshot=details["market_snapshot_at_decision"],
                    decision_context=details["decision_context_details"],
                    outcome=outcome_data_for_qpm,
                )
                logger.info(
                    "Padrão adicionado à QPM para trade %s", details["order_id"]
                )
            except Exception as e_qpm:
                logger.error(
                    "Erro ao adicionar padrão à QPM para trade %s: %s",
                    details["order_id"],
                    e_qpm,
                    exc_info=True,
                )

        _update_wallet_after_close(trader, result)

        trade_info = {
            "realized_pnl": pnl_value_net,
            "entry_price": details["entry_price"],
            "exit_price": exit_price,
            "quantity": details["size"],
            "symbol": symbol,
            "side": details["side"],
        }

        risk_manager = trader.risk_managers.get(symbol)
        if risk_manager:
            risk_manager.process_trade_result(trade_info)
            trader._save_risk_metrics(symbol)
        else:
            logger.warning(
                "Risk manager ausente para %s ao processar resultado do trade", symbol
            )

        trade_log_entry = {
            "order_id": details["order_id"],
            "symbol": symbol,
            "side": details["side"],
            "entry_price": details["entry_price"],
            "exit_price": exit_price,
            "size": details["size"],
            "pnl": pnl_value_net,
            "pnl_pct": pnl_pct_value,
            "entry_time": entry_time.isoformat(),
            "exit_time": timestamp_exit.isoformat(),
            "exit_time_ms": int(timestamp_exit.timestamp() * 1000),
            "duration_seconds": (timestamp_exit - entry_time).total_seconds(),
            "reason": reason,
            "close_order_id": close_order_id,
            "status": f"closed_{reason}",
            "q_signature_on_entry": (
                details["captured_quantum_signature_packet"].model_dump()
                if details.get("captured_quantum_signature_packet")
                else None
            ),
            "market_snapshot_at_decision": details["market_snapshot_at_decision"],
            "decision_context_details": details["decision_context_details"],
            "fees": total_fees,
            "fees_pct": fees_pct_value,
        }
        trader.trade_history.append(trade_log_entry)
        if getattr(trader, "order_journal", None):
            trader.order_journal.append({"event": "close", **trade_log_entry})
        trader.last_trade_close_time[symbol] = time.time()

        logger.info(
            "Registro de ordem fechada: %s -> %s", details["order_id"], close_order_id
        )
        color = GREEN if pnl_value_net > 0 else RED if pnl_value_net < 0 else YELLOW
        status_msg = colorize("POSIÇÃO FECHADA (INTERNA)", color, bold=True)
        logger.info(
            f"{status_msg}: {details['side'].upper()} {details['size']:.4f} {symbol} @ {exit_price:.2f} (Entrada: {details['entry_price']:.2f}), PnL: {pnl_value_net:.2f} ({pnl_pct_value:.2f}%), Fees: {total_fees:.4f} ({fees_pct_value:.2f}%), Razão: {reason}"
        )
        logger.debug(
            "Detalhes da Posição Fechada (%s): %s", details["order_id"], trade_log_entry
        )
        await trader.hud_manager.display_wallet_status(trader, force_update=True)

        if (
            not trader.disable_metacognition
            and hasattr(trader, "metacognition_executive")
            and trader.metacognition_executive
        ):
            metacog_input = asdict(closed_position_obj)
            await trader._run_metacognition_cycle(symbol, metacog_input)
            if not any(trader.open_positions.values()):
                logger.info("Exposure reduced to zero. Clearing metacognitive context.")
                trader._last_metacognitive_ctx = None

    return trade_log_entry
