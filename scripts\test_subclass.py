import importlib, sys
from qualia.config.encoder_registry import register_encoder
QE = importlib.import_module('src.qualia.core.encoders').QuantumEncoder
class TmpEncoder(QE):
    def _encode_single(self, snapshot):
        return [1,0]
    def get_quantum_operation(self, snapshot):
        return None
try:
    register_encoder('tmp_encoder', TmpEncoder)
    print('Success')
except Exception as exc:
    print('Register error', exc) 