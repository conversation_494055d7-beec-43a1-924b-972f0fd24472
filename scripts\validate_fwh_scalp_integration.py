#!/usr/bin/env python3
"""
Script de Validação da Integração FWH Scalp Trading

Este script valida a integração da estratégia Fibonacci Wave Hype (FWH)
com o sistema de paper trading para scalping, verificando:

- Carregamento correto da configuração
- Inicialização da estratégia FWH
- Integração com o sistema QUALIA
- Geração de sinais de teste
- Simulação de trades
- Cálculo de métricas

Autor: QUALIA System
Data: 2024
"""

import asyncio
import sys
import os
import yaml
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

# Adicionar o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

class FWHScalpValidation:
    """
    Classe para validação da integração FWH Scalp Trading
    """
    
    def __init__(self):
        self.config_path = Path(__file__).parent.parent / "config" / "fwh_scalp_config.yaml"
        self.config = None
        self.validation_results = []
        
    def log_result(self, test_name: str, success: bool, message: str = ""):
        """Registra resultado de um teste"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        self.validation_results.append(result)
        print(f"{status} {test_name}: {message}")
        
    def validate_config_file(self) -> bool:
        """Valida se o arquivo de configuração existe e é válido"""
        try:
            if not self.config_path.exists():
                self.log_result("Config File Exists", False, f"Arquivo não encontrado: {self.config_path}")
                return False
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            
            self.log_result("Config File Load", True, "Configuração carregada com sucesso")
            return True
            
        except Exception as e:
            self.log_result("Config File Load", False, f"Erro ao carregar: {e}")
            return False
    
    def validate_config_structure(self) -> bool:
        """Valida a estrutura da configuração"""
        required_sections = [
            'fibonacci_wave_hype_config',
            'trading_system',
            'monitoring',
            'market_data',
            'backtesting'
        ]
        
        try:
            for section in required_sections:
                if section not in self.config:
                    self.log_result("Config Structure", False, f"Seção ausente: {section}")
                    return False
            
            # Validar parâmetros específicos da FWH
            fwh_config = self.config['fibonacci_wave_hype_config']
            required_params = ['name', 'enabled', 'params']
            
            for param in required_params:
                if param not in fwh_config:
                    self.log_result("FWH Config", False, f"Parâmetro ausente: {param}")
                    return False
            
            self.log_result("Config Structure", True, "Estrutura válida")
            return True
            
        except Exception as e:
            self.log_result("Config Structure", False, f"Erro na validação: {e}")
            return False
    
    def validate_fwh_parameters(self) -> bool:
        """Valida os parâmetros da estratégia FWH"""
        try:
            fwh_params = self.config['fibonacci_wave_hype_config']['params']
            
            # Parâmetros obrigatórios
            required_params = [
                'fib_lookback',
                'hype_threshold',
                'profit_target_pct',
                'stop_loss_pct'
            ]
            
            for param in required_params:
                if param not in fwh_params:
                    self.log_result("FWH Parameters", False, f"Parâmetro ausente: {param}")
                    return False
            
            # Validar valores
            if fwh_params['fib_lookback'] <= 0:
                self.log_result("FWH Parameters", False, "fib_lookback deve ser > 0")
                return False
            
            if not (0 < fwh_params['hype_threshold'] < 1):
                self.log_result("FWH Parameters", False, "hype_threshold deve estar entre 0 e 1")
                return False
            
            if fwh_params['profit_target_pct'] <= 0:
                self.log_result("FWH Parameters", False, "profit_target_pct deve ser > 0")
                return False
            
            if fwh_params['stop_loss_pct'] <= 0:
                self.log_result("FWH Parameters", False, "stop_loss_pct deve ser > 0")
                return False
            
            self.log_result("FWH Parameters", True, "Parâmetros válidos")
            return True
            
        except Exception as e:
            self.log_result("FWH Parameters", False, f"Erro na validação: {e}")
            return False
    
    def validate_trading_limits(self) -> bool:
        """Valida os limites de trading"""
        try:
            limits = self.config['trading_system']['limits']
            
            # Verificar limites de posição
            if limits['max_position_size_usd'] <= limits['min_position_size_usd']:
                self.log_result("Trading Limits", False, "max_position_size deve ser > min_position_size")
                return False
            
            # Verificar limites de perda
            if limits['max_daily_loss'] <= 0:
                self.log_result("Trading Limits", False, "max_daily_loss deve ser > 0")
                return False
            
            # Verificar número máximo de trades
            if limits['max_daily_trades'] <= 0:
                self.log_result("Trading Limits", False, "max_daily_trades deve ser > 0")
                return False
            
            self.log_result("Trading Limits", True, "Limites válidos")
            return True
            
        except Exception as e:
            self.log_result("Trading Limits", False, f"Erro na validação: {e}")
            return False
    
    async def validate_fwh_strategy_import(self) -> bool:
        """Valida se a estratégia FWH pode ser importada"""
        try:
            from qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy
            self.log_result("FWH Import", True, "Estratégia importada com sucesso")
            return True
        except ImportError as e:
            self.log_result("FWH Import", False, f"Erro de importação: {e}")
            return False
        except Exception as e:
            self.log_result("FWH Import", False, f"Erro inesperado: {e}")
            return False
    
    async def validate_fwh_strategy_initialization(self) -> bool:
        """Valida se a estratégia FWH pode ser inicializada"""
        try:
            from qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy
            
            fwh_params = self.config['fibonacci_wave_hype_config']['params']
            
            # Tentar inicializar a estratégia
            params = {
                'fib_lookback': fwh_params.get('fib_lookback', 20),
                'hype_threshold': fwh_params.get('hype_threshold', 0.5),
                'profit_target_pct': fwh_params.get('profit_target_pct', 0.5),
                'stop_loss_pct': fwh_params.get('stop_loss_pct', 0.3)
            }
            context = {'symbol': 'BTCUSDT', 'timeframe': '1m'}
            strategy = FibonacciWaveHypeStrategy(params=params, context=context)
            
            if strategy:
                self.log_result("FWH Initialization", True, "Estratégia inicializada com sucesso")
                return True
            else:
                self.log_result("FWH Initialization", False, "Falha na inicialização")
                return False
                
        except Exception as e:
            self.log_result("FWH Initialization", False, f"Erro na inicialização: {e}")
            return False
    
    async def validate_qualia_components(self) -> bool:
        """Valida se os componentes QUALIA podem ser importados"""
        import sys
        import os
        
        components = [
            ('QUALIATradingSystem', 'scripts.start_real_trading'),
            ('QUALIAExecutionInterface', 'qualia.core.qualia_execution_interface'),
            ('TradingContext', 'qualia.strategies.strategy_interface'),
            ('aggregate_trade_performance', 'qualia.metrics.performance_metrics'),
            ('QUALIARiskManager', 'qualia.risk.manager')
        ]
        
        all_success = True
        
        for component_name, module_path in components:
            try:
                if component_name == 'QUALIATradingSystem':
                    # Adicionar o diretório scripts ao path
                    scripts_path = os.path.join(os.getcwd(), 'scripts')
                    if scripts_path not in sys.path:
                        sys.path.insert(0, scripts_path)
                    from start_real_trading import QUALIATradingSystem
                else:
                    module = __import__(module_path, fromlist=[component_name])
                    getattr(module, component_name)
                self.log_result(f"Import {component_name}", True, "Componente disponível")
            except (ImportError, AttributeError) as e:
                self.log_result(f"Import {component_name}", False, f"Erro: {e}")
                all_success = False
        
        return all_success
    
    def validate_directories(self) -> bool:
        """Valida se os diretórios necessários existem ou podem ser criados"""
        try:
            # Diretório de logs
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)
            
            # Verificar se pode escrever
            test_file = log_dir / "test_write.tmp"
            test_file.write_text("test")
            test_file.unlink()
            
            self.log_result("Directories", True, "Diretórios criados/verificados")
            return True
            
        except Exception as e:
            self.log_result("Directories", False, f"Erro: {e}")
            return False
    
    def simulate_market_data(self) -> Dict[str, Any]:
        """Simula dados de mercado para teste"""
        import random
        
        return {
            'symbol': 'BTC/USDT',
            'price': 50000 + random.uniform(-1000, 1000),
            'volume': random.uniform(1000000, 5000000),
            'timestamp': datetime.now(),
            'bid': 49950,
            'ask': 50050,
            'high': 51000,
            'low': 49000,
            'open': 50200,
            'close': 50000
        }
    
    async def validate_signal_generation(self) -> bool:
        """Valida se a estratégia pode gerar sinais"""
        try:
            from qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy
            from qualia.strategies.strategy_interface import TradingContext
            
            # Inicializar estratégia
            fwh_params = self.config['fibonacci_wave_hype_config']['params']
            params = {
                'fib_lookback': fwh_params.get('fib_lookback', 20),
                'hype_threshold': fwh_params.get('hype_threshold', 0.5)
            }
            context = {'symbol': 'BTCUSDT', 'timeframe': '1m'}
            strategy = FibonacciWaveHypeStrategy(params=params, context=context)
            
            # Simular dados de mercado completos
            import time
            import pandas as pd
            
            # Criar DataFrame para OHLCV
            ohlcv_data = pd.DataFrame({
                'open': [50000],
                'high': [50200],
                'low': [49800],
                'close': [50000],
                'volume': [1000]
            })
            
            market_data = {
                'symbol': 'BTC/USDT',
                'timeframe': '1m',
                'current_price': 50000.0,
                'timestamp': pd.Timestamp.now(),
                'ohlcv': ohlcv_data,
                'wallet_state': {'USDT': 10000.0, 'BTC': 0.0}
            }
            
            # Criar contexto de trading
            context = TradingContext(**market_data)
            
            # Tentar gerar sinal
            signal_df = strategy.generate_signal(context)
            
            if signal_df is not None and not signal_df.empty:
                signal_action = signal_df.iloc[0].get('signal', 'HOLD') if len(signal_df) > 0 else 'HOLD'
                self.log_result("Signal Generation", True, f"Sinal gerado: {signal_action}")
                return True
            else:
                self.log_result("Signal Generation", True, "Estratégia funcionando - nenhum sinal no momento")
                return True
                
        except Exception as e:
            self.log_result("Signal Generation", False, f"Erro: {e}")
            return False
    
    def validate_risk_parameters(self) -> bool:
        """Valida os parâmetros de gestão de risco"""
        try:
            risk_config = self.config['trading_system']['risk_management']
            
            # Verificar stop loss e take profit
            if risk_config['stop_loss_pct'] >= risk_config['take_profit_pct']:
                self.log_result("Risk Parameters", False, "take_profit deve ser > stop_loss")
                return False
            
            # Verificar valores positivos
            if risk_config['stop_loss_pct'] <= 0 or risk_config['take_profit_pct'] <= 0:
                self.log_result("Risk Parameters", False, "Stop loss e take profit devem ser > 0")
                return False
            
            # Verificar risk per trade
            if risk_config['risk_per_trade_pct'] <= 0 or risk_config['risk_per_trade_pct'] > 10:
                self.log_result("Risk Parameters", False, "risk_per_trade_pct deve estar entre 0 e 10")
                return False
            
            self.log_result("Risk Parameters", True, "Parâmetros de risco válidos")
            return True
            
        except Exception as e:
            self.log_result("Risk Parameters", False, f"Erro: {e}")
            return False
    
    def save_validation_report(self):
        """Salva relatório de validação"""
        report = {
            'validation_timestamp': datetime.now().isoformat(),
            'total_tests': len(self.validation_results),
            'passed_tests': sum(1 for r in self.validation_results if r['success']),
            'failed_tests': sum(1 for r in self.validation_results if not r['success']),
            'success_rate': sum(1 for r in self.validation_results if r['success']) / len(self.validation_results) * 100,
            'results': self.validation_results
        }
        
        # Salvar relatório
        report_file = Path("logs") / "fwh_scalp_validation_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📊 Relatório salvo em: {report_file}")
        return report
    
    async def run_all_validations(self):
        """Executa todas as validações"""
        print("🔍 Iniciando validação da integração FWH Scalp Trading")
        print("=" * 60)
        
        # Lista de validações
        validations = [
            ("Arquivo de Configuração", self.validate_config_file),
            ("Estrutura da Configuração", self.validate_config_structure),
            ("Parâmetros FWH", self.validate_fwh_parameters),
            ("Limites de Trading", self.validate_trading_limits),
            ("Parâmetros de Risco", self.validate_risk_parameters),
            ("Diretórios", self.validate_directories),
            ("Importação FWH", self.validate_fwh_strategy_import),
            ("Inicialização FWH", self.validate_fwh_strategy_initialization),
            ("Componentes QUALIA", self.validate_qualia_components),
            ("Geração de Sinais", self.validate_signal_generation)
        ]
        
        # Executar validações
        for name, validation_func in validations:
            print(f"\n🔍 Validando: {name}")
            try:
                if asyncio.iscoroutinefunction(validation_func):
                    await validation_func()
                else:
                    validation_func()
            except Exception as e:
                self.log_result(name, False, f"Erro inesperado: {e}")
        
        # Gerar relatório
        print("\n" + "=" * 60)
        report = self.save_validation_report()
        
        # Resumo
        print(f"\n📈 RESUMO DA VALIDAÇÃO:")
        print(f"   Total de testes: {report['total_tests']}")
        print(f"   Testes aprovados: {report['passed_tests']}")
        print(f"   Testes falharam: {report['failed_tests']}")
        print(f"   Taxa de sucesso: {report['success_rate']:.1f}%")
        
        if report['success_rate'] == 100:
            print("\n✅ VALIDAÇÃO COMPLETA: Sistema pronto para execução!")
        elif report['success_rate'] >= 80:
            print("\n⚠️  VALIDAÇÃO PARCIAL: Sistema pode funcionar com limitações")
        else:
            print("\n❌ VALIDAÇÃO FALHOU: Corrija os erros antes de executar")
        
        return report['success_rate'] >= 80

async def main():
    """Função principal"""
    print("🧪 FWH Scalp Trading - Validação de Integração")
    print("=" * 50)
    
    validator = FWHScalpValidation()
    success = await validator.run_all_validations()
    
    if success:
        print("\n🚀 Sistema validado! Execute o trading com:")
        print("   python scripts/run_fwh_scalp_paper_trading.py")
    else:
        print("\n🔧 Corrija os erros encontrados antes de executar o sistema")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())