# QUALIA Live Feed - Guia de Testes D-03.1

Este documento descreve como executar os testes do sistema de live feed do QUALIA para validar a integração com KuCoin usando credenciais reais.

## 📋 Visão Geral

O sistema de testes D-03.1 valida todos os componentes do live feed:

- ✅ **Validação de Credenciais**: Testa autenticação com KuCoin
- ✅ **Normalização de Dados**: Valida conversão de formatos de dados
- ✅ **Agregação de Feeds**: Testa combinação de múltiplas fontes
- ✅ **REST API**: Valida coleta de dados via REST
- ✅ **WebSocket**: Testa streaming em tempo real
- ✅ **Feed Manager**: Valida coordenação de todos os componentes
- ✅ **Métricas de Performance**: Mede latência e throughput
- ✅ **Tratamento de Erros**: Testa robustez do sistema
- ✅ **Reconexão**: Valida mecanismos de recuperação
- ✅ **Sistema de Alertas**: Testa notificações de anomalias

## 🚀 <PERSON><PERSON><PERSON>

### 1. Configurar Credenciais

```bash
# Configurar credenciais interativamente
python -m qualia.live_feed setup

# Ou definir variáveis de ambiente
export KUCOIN_API_KEY="sua_api_key"
export KUCOIN_API_SECRET="sua_api_secret"
export KUCOIN_PASSPHRASE="sua_passphrase"
```

### 2. Executar Todos os Testes

```bash
# Executar suite completa (sandbox + produção)
python -m qualia.live_feed

# Apenas sandbox
python -m qualia.live_feed --sandbox-only

# Apenas produção
python -m qualia.live_feed --production-only
```

### 3. Testes Específicos

```bash
# Testes rápidos (básicos)
python -m qualia.live_feed --quick

# Testes específicos
python -m qualia.live_feed --tests credentials rest websocket

# Com relatório detalhado
python -m qualia.live_feed --report results.json --verbose
```

## 🔧 Opções de Execução

### Comandos Disponíveis

```bash
# Setup de credenciais
python -m qualia.live_feed setup

# Executar exemplo básico
python -m qualia.live_feed example

# Executar testes (padrão)
python -m qualia.live_feed [opções]
```

### Opções de Teste

| Opção | Descrição |
|-------|-----------|
| `--tests LISTA` | Executar testes específicos |
| `--sandbox-only` | Apenas testes em sandbox |
| `--production-only` | Apenas testes em produção |
| `--quick` | Testes rápidos (credentials, normalizer, aggregator) |
| `--report ARQUIVO` | Salvar relatório JSON |
| `--verbose` | Logs detalhados |
| `--no-color` | Desabilitar cores |

### Testes Disponíveis

| Nome | Descrição | Duração |
|------|-----------|---------|
| `credentials` | Validação de credenciais | ~5s |
| `normalizer` | Normalização de dados | ~1s |
| `aggregator` | Agregação de feeds | ~1s |
| `rest` | API REST KuCoin | ~10s |
| `websocket` | WebSocket KuCoin | ~15s |
| `manager` | Feed Manager completo | ~20s |
| `performance` | Métricas de performance | ~2s |
| `errors` | Tratamento de erros | ~5s |
| `reconnection` | Mecanismos de reconexão | ~3s |
| `alerts` | Sistema de alertas | ~2s |

## 📊 Critérios de Sucesso

### Métricas de Performance

- **Latência**: < 100ms (média)
- **Throughput**: > 1000 updates/segundo
- **Disponibilidade**: > 99% uptime
- **Precisão**: < 1% variância entre fontes

### Validações Funcionais

- ✅ Autenticação bem-sucedida (sandbox + produção)
- ✅ Dados normalizados corretamente
- ✅ Agregação com confidence score > 0.8
- ✅ WebSocket conecta e recebe dados
- ✅ REST API funciona como fallback
- ✅ Reconexão automática em falhas
- ✅ Alertas disparados em anomalias

## 🐛 Solução de Problemas

### Credenciais Inválidas

```
❌ Erro: Authentication failed
```

**Solução**: Verifique suas credenciais KuCoin:
1. Acesse https://www.kucoin.com/account/api
2. Verifique se API Key, Secret e Passphrase estão corretos
3. Confirme se a API tem permissões de leitura
4. Execute `python -m qualia.live_feed setup` novamente

### Problemas de Conectividade

```
❌ Erro: Connection timeout
```

**Solução**:
1. Verifique sua conexão com internet
2. Teste conectividade: `ping api.kucoin.com`
3. Verifique se não há firewall bloqueando
4. Tente usar sandbox primeiro: `--sandbox-only`

### Performance Baixa

```
⚠️ Performance: Score 45/100
```

**Solução**:
1. Verifique latência da rede
2. Execute em horários de menor tráfego
3. Use conexão com internet estável
4. Considere usar VPS próximo aos servidores KuCoin

### WebSocket Falha

```
❌ WebSocket: Connection failed
```

**Solução**:
1. Verifique se WebSocket não está bloqueado
2. Teste apenas REST: `--tests rest`
3. Verifique logs detalhados: `--verbose`
4. Considere usar proxy se necessário

## 📈 Interpretando Resultados

### Taxa de Sucesso

- **90-100%**: Excelente - Sistema pronto para produção
- **80-89%**: Bom - Pequenos ajustes necessários
- **70-79%**: Aceitável - Revisar componentes com falha
- **< 70%**: Problemático - Investigar e corrigir

### Relatório JSON

O relatório contém:

```json
{
  "timestamp": "2024-01-15T10:30:00",
  "mode": "SANDBOX",
  "summary": {
    "total_tests": 10,
    "passed_tests": 9,
    "success_rate": 90.0
  },
  "test_results": [...],
  "performance_metrics": {
    "data_points_received": 1250,
    "latency_measurements": [45.2, 67.1, ...],
    "error_count": 1,
    "alert_count": 3
  }
}
```

## 🔄 Próximos Passos

Após os testes passarem com sucesso:

1. **D-03.2**: Integrar live feed com sistema de trading
2. **D-04**: Implementar pruning avançado
3. **D-05**: Dashboard de monitoramento
4. **D-06**: Sistema de hot-reload
5. **D-07**: Testes A/B
6. **D-08**: Documentação final

## 📞 Suporte

Se encontrar problemas:

1. Execute com `--verbose` para logs detalhados
2. Verifique o arquivo de log em `logs/qualia_live_feed.log`
3. Consulte a documentação em `src/qualia/live_feed/README.md`
4. Verifique issues conhecidos no repositório

---

**Nota**: Este sistema foi projetado para ser robusto e tolerante a falhas. Pequenas falhas em testes individuais são esperadas devido à natureza dinâmica dos mercados financeiros.
