"""
BayesOpt Client - Interface para comunicação com BayesOpt Microservice.
Permite integração distribuída com fallback para modo local.
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

import httpx
import optuna
from optuna.samplers import TPESampler
from optuna.pruners import MedianPruner

from .models import (
    SuggestRequest, SuggestResponse, ReportRequest, ReportResponse,
    StudyInfo, StudyListResponse, HealthResponse
)
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class BayesClientConfig:
    """Configuração do cliente BayesOpt."""
    service_url: str = "http://localhost:8080"
    timeout_seconds: float = 30.0
    max_retries: int = 3
    retry_delay: float = 1.0
    fallback_to_local: bool = True
    
    # Local fallback config
    local_storage_url: str = "sqlite:///data/local_bayes_fallback.db"
    
    # Request defaults
    default_sampler: str = "TPE"
    default_pruning: bool = True
    default_n_startup_trials: int = 10


class BayesOptClient:
    """
    Cliente para BayesOpt Microservice com fallback local.
    Permite otimização distribuída com alta disponibilidade.
    """
    
    def __init__(self, config: BayesClientConfig = None):
        self.config = config or BayesClientConfig()
        self.client = httpx.AsyncClient(timeout=self.config.timeout_seconds)
        self.local_studies: Dict[str, optuna.Study] = {}
        self.service_available = None  # None = unknown, True/False = known state
        
        logger.info("🔗 BayesOptClient inicializado")
        logger.info(f"   🌐 Service URL: {self.config.service_url}")
        logger.info(f"   🔄 Fallback local: {self.config.fallback_to_local}")
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def close(self):
        """Close HTTP client."""
        await self.client.aclose()
    
    async def check_service_health(self) -> bool:
        """Check if BayesOpt service is available."""
        try:
            response = await self.client.get(f"{self.config.service_url}/health")
            if response.status_code == 200:
                self.service_available = True
                logger.debug("✅ BayesOpt service disponível")
                return True
            else:
                self.service_available = False
                return False
        except Exception as e:
            logger.debug(f"❌ BayesOpt service indisponível: {e}")
            self.service_available = False
            return False
    
    async def suggest_parameters(
        self,
        study_name: str,
        symbol: str,
        price_amp_range: tuple[float, float] = (1.0, 10.0),
        news_amp_range: tuple[float, float] = (1.0, 15.0),
        min_conf_range: tuple[float, float] = (0.20, 0.80),
        sampler_type: str = None,
        pruning_enabled: bool = None
    ) -> Dict[str, Any]:
        """
        Solicita novos parâmetros para otimização.
        Retorna dict com parâmetros e metadados.
        """
        # Use defaults if not specified
        sampler_type = sampler_type or self.config.default_sampler
        pruning_enabled = pruning_enabled if pruning_enabled is not None else self.config.default_pruning
        
        # Try service first
        if self.service_available is not False:
            try:
                return await self._suggest_via_service(
                    study_name, symbol, price_amp_range, news_amp_range, 
                    min_conf_range, sampler_type, pruning_enabled
                )
            except Exception as e:
                logger.warning(f"⚠️  Erro no service, tentando fallback: {e}")
                self.service_available = False
        
        # Fallback to local
        if self.config.fallback_to_local:
            return await self._suggest_via_local(
                study_name, symbol, price_amp_range, news_amp_range,
                min_conf_range, sampler_type, pruning_enabled
            )
        else:
            raise Exception("BayesOpt service indisponível e fallback desabilitado")
    
    async def _suggest_via_service(
        self, study_name: str, symbol: str, price_amp_range: tuple, 
        news_amp_range: tuple, min_conf_range: tuple, sampler_type: str, 
        pruning_enabled: bool
    ) -> Dict[str, Any]:
        """Suggest parameters via microservice."""
        request = SuggestRequest(
            study_name=study_name,
            symbol=symbol,
            price_amp_range=price_amp_range,
            news_amp_range=news_amp_range,
            min_conf_range=min_conf_range,
            sampler_type=sampler_type,
            pruning_enabled=pruning_enabled,
            n_startup_trials=self.config.default_n_startup_trials
        )
        
        response = await self.client.post(
            f"{self.config.service_url}/suggest",
            json=request.model_dump()
        )
        response.raise_for_status()
        
        result = response.json()
        self.service_available = True
        
        logger.info(f"💡 Parâmetros via service para {symbol}: {result['parameters']}")
        
        return {
            "parameters": result["parameters"],
            "trial_id": result["trial_id"],
            "trial_number": result["trial_number"],
            "source": "service",
            "timestamp": result["timestamp"]
        }
    
    async def _suggest_via_local(
        self, study_name: str, symbol: str, price_amp_range: tuple,
        news_amp_range: tuple, min_conf_range: tuple, sampler_type: str,
        pruning_enabled: bool
    ) -> Dict[str, Any]:
        """Suggest parameters via local Optuna study."""
        study_key = f"{study_name}_{symbol}"
        
        # Get or create local study
        if study_key not in self.local_studies:
            sampler = TPESampler(seed=42, n_startup_trials=self.config.default_n_startup_trials)
            pruner = MedianPruner(n_startup_trials=5) if pruning_enabled else optuna.pruners.NopPruner()
            
            study = optuna.create_study(
                study_name=study_key,
                direction="maximize",
                sampler=sampler,
                pruner=pruner,
                storage=self.config.local_storage_url,
                load_if_exists=True
            )
            self.local_studies[study_key] = study
        
        study = self.local_studies[study_key]
        trial = study.ask()
        
        # Suggest parameters
        parameters = {
            "price_amplification": trial.suggest_float("price_amplification", *price_amp_range),
            "news_amplification": trial.suggest_float("news_amplification", *news_amp_range),
            "min_confidence": trial.suggest_float("min_confidence", *min_conf_range)
        }
        
        logger.info(f"💡 Parâmetros via local para {symbol}: {parameters}")
        
        return {
            "parameters": parameters,
            "trial_id": str(trial._trial_id),
            "trial_number": trial.number,
            "source": "local",
            "timestamp": datetime.now().isoformat(),
            "_trial": trial  # Keep reference for reporting
        }
    
    async def report_result(
        self,
        study_name: str,
        symbol: str,
        trial_id: str,
        objective_value: float,
        metrics: Dict[str, float],
        duration_seconds: float,
        success: bool = True,
        error_message: str = None,
        trial_metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Reporta resultado de um trial.
        Retorna informações sobre o estudo atualizado.
        """
        # Try service first
        if self.service_available is not False:
            try:
                return await self._report_via_service(
                    study_name, symbol, trial_id, objective_value, metrics,
                    duration_seconds, success, error_message
                )
            except Exception as e:
                logger.warning(f"⚠️  Erro ao reportar via service: {e}")
                self.service_available = False
        
        # Fallback to local
        if self.config.fallback_to_local and trial_metadata:
            return await self._report_via_local(
                study_name, symbol, trial_metadata, objective_value, success
            )
        else:
            raise Exception("Não foi possível reportar resultado")
    
    async def _report_via_service(
        self, study_name: str, symbol: str, trial_id: str, objective_value: float,
        metrics: Dict[str, float], duration_seconds: float, success: bool, error_message: str
    ) -> Dict[str, Any]:
        """Report result via microservice."""
        request = ReportRequest(
            study_name=study_name,
            symbol=symbol,
            trial_id=trial_id,
            objective_value=objective_value,
            metrics=metrics,
            duration_seconds=duration_seconds,
            success=success,
            error_message=error_message,
            evaluation_type="realistic",
            cost_ratio_pct=metrics.get("cost_ratio_pct")
        )
        
        response = await self.client.post(
            f"{self.config.service_url}/report",
            json=request.model_dump()
        )
        response.raise_for_status()
        
        result = response.json()
        self.service_available = True
        
        logger.info(f"📈 Resultado reportado via service para {symbol}: {objective_value:.4f}")
        
        return {
            "n_trials": result["n_trials"],
            "best_value": result["best_value"],
            "best_parameters": result["best_parameters"],
            "source": "service"
        }
    
    async def _report_via_local(
        self, study_name: str, symbol: str, trial_metadata: Dict[str, Any],
        objective_value: float, success: bool
    ) -> Dict[str, Any]:
        """Report result via local study."""
        study_key = f"{study_name}_{symbol}"
        
        if study_key not in self.local_studies:
            raise Exception(f"Local study not found: {study_key}")
        
        study = self.local_studies[study_key]
        trial = trial_metadata.get("_trial")
        
        if not trial:
            raise Exception("Trial reference not found in metadata")
        
        # Report result
        if success:
            study.tell(trial, objective_value)
        else:
            study.tell(trial, float('nan'))
        
        logger.info(f"📈 Resultado reportado via local para {symbol}: {objective_value:.4f}")
        
        return {
            "n_trials": len(study.trials),
            "best_value": study.best_value if study.best_trial else None,
            "best_parameters": study.best_params if study.best_trial else None,
            "source": "local"
        }
    
    async def get_study_info(self, study_name: str, symbol: str) -> Optional[Dict[str, Any]]:
        """Get information about a study."""
        # Try service first
        if self.service_available is not False:
            try:
                response = await self.client.get(
                    f"{self.config.service_url}/studies/{study_name}/{symbol}"
                )
                if response.status_code == 200:
                    self.service_available = True
                    return response.json()
            except Exception as e:
                logger.debug(f"Erro ao obter info via service: {e}")
                self.service_available = False
        
        # Try local fallback
        study_key = f"{study_name}_{symbol}"
        if study_key in self.local_studies:
            study = self.local_studies[study_key]
            return {
                "study_name": study_name,
                "symbol": symbol,
                "n_trials": len(study.trials),
                "best_value": study.best_value if study.best_trial else None,
                "best_parameters": study.best_params if study.best_trial else None,
                "source": "local"
            }
        
        return None
    
    async def list_studies(self) -> List[Dict[str, Any]]:
        """List all studies."""
        studies = []
        
        # Try service first
        if self.service_available is not False:
            try:
                response = await self.client.get(f"{self.config.service_url}/studies")
                if response.status_code == 200:
                    self.service_available = True
                    result = response.json()
                    return result.get("studies", [])
            except Exception as e:
                logger.debug(f"Erro ao listar via service: {e}")
                self.service_available = False
        
        # Add local studies
        for study_key, study in self.local_studies.items():
            study_name, symbol = study_key.rsplit("_", 1)
            studies.append({
                "study_name": study_name,
                "symbol": symbol,
                "n_trials": len(study.trials),
                "best_value": study.best_value if study.best_trial else None,
                "best_parameters": study.best_params if study.best_trial else None,
                "source": "local"
            })
        
        return studies
