#!/usr/bin/env python3
"""
QUALIA Holographic Trading Deployment Script

Script de deployment para sistema de trading holográfico.
Configura ambiente, valida dependências e inicia sistema.
"""

import asyncio
import logging
import os
import sys
import yaml
from pathlib import Path
from typing import Dict, Any
import time

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Arquivo .env carregado")
except ImportError:
    # Fallback manual loading if python-dotenv not available
    env_file = Path('.env')
    if env_file.exists():
        with open(env_file) as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        print("✅ Arquivo .env carregado manualmente")
    else:
        print("⚠️ Arquivo .env não encontrado")

from qualia.consciousness.holographic_universe import HolographicMarketUniverse
from qualia.consciousness.real_data_collectors import RealDataCollector
from qualia.risk_management.advanced_risk_manager import AdvancedRiskManager
from qualia.exchanges.kucoin_client import KuCoinClient
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

class HolographicTradingOrchestrator:
    """
    Orquestrador principal do sistema de trading holográfico.
    
    Coordena:
    1. Coleta de dados em tempo real
    2. Simulação holográfica
    3. Geração de sinais
    4. Execução de trades
    5. Monitoramento de performance
    """
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = self._load_config()
        
        # Core components
        self.data_collector = None
        self.holographic_universe = None
        self.risk_manager = None
        self.kucoin_client = None
        self.trading_bridge = None
        
        # State
        self.running = False
        self.performance_stats = {
            'start_time': None,
            'signals_generated': 0,
            'trades_executed': 0,
            'active_positions': 0
        }
        
    def _load_config(self) -> Dict[str, Any]:
        """Carrega configuração do arquivo YAML."""
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # Override with environment variables
            kucoin_config = config['holographic_trading']['exchanges']['kucoin']
            kucoin_config['api_key'] = os.getenv('KUCOIN_API_KEY', '')
            kucoin_config['api_secret'] = os.getenv('KUCOIN_SECRET_KEY', '')
            kucoin_config['password'] = os.getenv('KUCOIN_PASSPHRASE', '')
            
            return config['holographic_trading']
            
        except Exception as e:
            logger.error(f"Erro ao carregar configuração: {e}")
            raise
    
    async def initialize(self):
        """Inicialização completa do sistema."""
        
        logger.info("🌌 Inicializando Holographic Trading System...")
        
        try:
            # 1. Data Collection
            logger.info("📡 Inicializando coleta de dados...")
            self.data_collector = RealDataCollector()
            # Configure symbols based on config
            if 'binance' in self.config['data_sources']:
                self.data_collector.symbols = self.config['data_sources']['binance'].get('symbols', self.data_collector.symbols)
            # Initialize session
            await self.data_collector.__aenter__()
            
            # 2. Holographic Universe
            logger.info("🌀 Inicializando universo holográfico...")
            holographic_config = self.config['holographic']
            self.holographic_universe = HolographicMarketUniverse(
                field_size=tuple(holographic_config['field_size']),
                time_steps=holographic_config['time_steps'],
                diffusion_rate=holographic_config['diffusion_rate'],
                feedback_strength=holographic_config['feedback_strength']
            )
            
            # 3. Risk Manager
            logger.info("🛡️ Inicializando risk manager...")
            risk_config = self.config['risk']
            self.risk_manager = AdvancedRiskManager(
                initial_capital=self.config['capital']['initial'],
                risk_per_trade_pct=risk_config['per_trade_pct'],
                max_drawdown_pct=risk_config['max_drawdown_pct'],
                max_volatility=risk_config['max_volatility']
            )
            
            # 4. KuCoin Client
            logger.info("🏦 Conectando com KuCoin...")
            kucoin_config = self.config['exchanges']['kucoin']
            
            # Validate credentials
            if not all([kucoin_config['api_key'], kucoin_config['api_secret'], kucoin_config['password']]):
                if self.config['mode'] == 'live':
                    raise ValueError("Credenciais KuCoin são obrigatórias para modo live")
                else:
                    logger.warning("⚠️ Credenciais KuCoin não configuradas - modo demo sem exchange")
                    self.kucoin_client = None
            else:
                self.kucoin_client = KuCoinClient(kucoin_config)
                await self.kucoin_client.initialize()
                logger.info("✅ KuCoin client conectado")
            
            # 5. Trading Bridge (implementação simplificada para este exemplo)
            logger.info("🌉 Inicializando trading bridge...")
            # Aqui integraria o HolographicTradingBridge quando implementado
            if self.kucoin_client:
                logger.info("✅ Trading bridge com KuCoin ativo")
            else:
                logger.info("⚠️ Trading bridge em modo demo (sem execução real)")
            
            # 6. Performance tracking
            self.performance_stats['start_time'] = asyncio.get_event_loop().time()
            
            logger.info("✅ Sistema inicializado com sucesso!")
            
        except Exception as e:
            logger.error(f"❌ Erro na inicialização: {e}", exc_info=True)
            raise
    
    async def run_trading_loop(self):
        """Loop principal de trading em tempo real."""
        
        self.running = True
        logger.info("🚀 Iniciando loop de trading holográfico...")
        
        # Timing configuration
        timing = self.config['timing']
        last_data_collection = 0
        last_universe_evolution = 0
        last_signal_generation = 0
        last_position_monitoring = 0
        last_performance_update = 0
        
        while self.running:
            try:
                current_time = asyncio.get_event_loop().time()
                
                # 1. Coleta de dados
                if current_time - last_data_collection >= timing['data_collection_interval']:
                    await self._collect_and_inject_data()
                    last_data_collection = current_time
                
                # 2. Evolução do universo holográfico
                if current_time - last_universe_evolution >= timing['universe_evolution_interval']:
                    await self._evolve_holographic_universe()
                    last_universe_evolution = current_time
                
                # 3. Análise de padrões e geração de sinais
                if current_time - last_signal_generation >= timing['signal_generation_interval']:
                    await self._analyze_and_generate_signals()
                    last_signal_generation = current_time
                
                # 4. Monitoramento de posições
                if current_time - last_position_monitoring >= timing['position_monitoring_interval']:
                    await self._monitor_positions()
                    last_position_monitoring = current_time
                
                # 5. Performance tracking
                if current_time - last_performance_update >= timing['performance_update_interval']:
                    await self._update_performance_metrics()
                    last_performance_update = current_time
                
                await asyncio.sleep(1)  # 1 segundo entre iterações
                
            except KeyboardInterrupt:
                logger.info("⏹️ Interrupção detectada, parando sistema...")
                self.running = False
                break
                
            except Exception as e:
                logger.error(f"Erro no loop principal: {e}", exc_info=True)
                await asyncio.sleep(5)  # Pausa em caso de erro
    
    async def _collect_and_inject_data(self):
        """Coleta dados reais e injeta no universo holográfico."""
        
        try:
            # Coleta dados de mercado
            market_data = await self.data_collector.collect_market_data()
            
            # Coleta eventos de notícias
            news_events = await self.data_collector.collect_news_events()
            
            # Converte para eventos holográficos
            holographic_events = self.data_collector.convert_to_holographic_events(
                market_data, news_events, self.holographic_universe.field_size
            )
            
            # DEBUG: Log detalhado dos eventos
            logger.info(f"🔍 DEBUG: {len(market_data)} dados de mercado, {len(news_events)} notícias")
            for event in holographic_events[:3]:  # Primeiros 3 eventos
                logger.info(f"🔍 Evento: pos={event.position}, amp={event.amplitude:.3f}, tipo={event.event_type}")
            
            # Injeta eventos no universo
            for event in holographic_events:
                await self.holographic_universe.inject_holographic_event(event)
                
        except Exception as e:
            logger.error(f"Erro na coleta de dados: {e}", exc_info=True)
    
    async def _evolve_holographic_universe(self):
        """Evolui o estado do universo holográfico."""
        
        try:
            current_time = time.time()
            await self.holographic_universe.step_evolution(current_time)
            
            # DEBUG: Log estado do campo
            summary = self.holographic_universe.get_field_summary()
            logger.info(f"🔍 Campo: energia={summary.get('field_energy', 0):.3f}, entropia={summary.get('field_entropy', 0):.3f}")
            
        except Exception as e:
            logger.error(f"Erro na evolução do universo: {e}", exc_info=True)
    
    async def _analyze_and_generate_signals(self):
        """Analisa padrões holográficos e gera sinais de trading."""
        
        try:
            # Detecta padrões
            patterns = self.holographic_universe.analyze_holographic_patterns()
            
            # DEBUG: Log padrões detectados
            logger.info(f"🔍 Padrões detectados: {len(patterns)}")
            for pattern in patterns[:2]:  # Primeiros 2 padrões
                logger.info(f"🔍 Padrão: pos={pattern.position}, força={pattern.strength:.3f}, tipo={pattern.pattern_type}")
            
            # Gera sinais
            signals = self.holographic_universe.generate_trading_signals(patterns)
            
            # DEBUG: Log sinais gerados
            logger.info(f"🔍 Sinais gerados: {len(signals)}")
            for signal in signals:
                logger.info(f"🔍 Sinal: {signal.symbol} {signal.action} força={signal.strength:.3f}")
            
            self.performance_stats['signals_generated'] += len(signals)
            
            # Processa sinais (validação de risco + execução)
            for signal in signals:
                await self._process_trading_signal(signal)
                
        except Exception as e:
            logger.error(f"Erro na análise de padrões: {e}", exc_info=True)
    
    async def _process_trading_signal(self, signal):
        """Processa um sinal de trading (validação + execução)."""
        
        try:
            logger.info(
                f"🎯 SIGNAL: {signal.symbol} {signal.action} "
                f"confidence={signal.confidence:.2f} "
                f"timeframe={signal.timeframe}"
            )
            
            # Em produção, aqui executaria via trading bridge
            if self.config['mode'] == 'live' and self.kucoin_client:
                # await self.trading_bridge.process_holographic_signal(signal)
                logger.info("🚀 Executaria trade em modo live")
            else:
                logger.info("📝 Modo paper trading - sinal registrado")
                
        except Exception as e:
            logger.error(f"Erro processando sinal: {e}", exc_info=True)
    
    async def _monitor_positions(self):
        """Monitora posições ativas."""
        
        try:
            # Em produção, monitoraria via trading bridge
            # if self.trading_bridge:
            #     await self.trading_bridge.monitor_active_positions()
            pass
            
        except Exception as e:
            logger.error(f"Erro no monitoramento de posições: {e}", exc_info=True)
    
    async def _update_performance_metrics(self):
        """Atualiza métricas de performance."""
        
        try:
            current_time = asyncio.get_event_loop().time()
            uptime = current_time - self.performance_stats['start_time']
            
            # Get field summary
            field_summary = self.holographic_universe.get_field_summary()
            
            logger.info(
                f"📊 PERFORMANCE: uptime={uptime:.0f}s "
                f"signals={self.performance_stats['signals_generated']} "
                f"field_energy={field_summary.get('total_energy', 0):.2f} "
                f"field_entropy={field_summary.get('entropy', 0):.2f}"
            )
            
            # Save metrics if configured
            if self.config['monitoring']['save_results']:
                await self._save_performance_metrics()
                
        except Exception as e:
            logger.error(f"Erro na atualização de métricas: {e}", exc_info=True)
    
    async def _save_performance_metrics(self):
        """Salva métricas em arquivo."""
        
        try:
            results_path = Path(self.config['monitoring']['results_path'])
            results_path.mkdir(parents=True, exist_ok=True)
            
            # Save basic metrics (implementação simplificada)
            metrics_file = results_path / f"metrics_{int(asyncio.get_event_loop().time())}.json"
            
            import json
            metrics = {
                'timestamp': asyncio.get_event_loop().time(),
                'performance_stats': self.performance_stats,
                'field_summary': self.holographic_universe.get_field_summary()
            }
            
            with open(metrics_file, 'w') as f:
                json.dump(metrics, f, indent=2)
                
        except Exception as e:
            logger.error(f"Erro ao salvar métricas: {e}", exc_info=True)
    
    async def shutdown(self):
        """Shutdown graceful do sistema."""
        
        logger.info("🔄 Iniciando shutdown do sistema...")
        
        try:
            self.running = False
            
            # Close trading bridge
            # if self.trading_bridge:
            #     await self.trading_bridge.shutdown()
            
            # Close data collector
            if self.data_collector:
                self.data_collector.stop_collection()
                await self.data_collector.__aexit__(None, None, None)
            
            # Close holographic universe
            if self.holographic_universe:
                await self.holographic_universe.shutdown()
            
            # Close KuCoin client
            if self.kucoin_client:
                # await self.kucoin_client.close()
                logger.info("KuCoin client fechado")
            
            logger.info("✅ Shutdown completo")
            
        except Exception as e:
            logger.error(f"Erro no shutdown: {e}", exc_info=True)

async def validate_environment():
    """Valida ambiente e dependências."""
    
    logger.info("🔍 Validando ambiente...")
    
    # Check required environment variables
    required_env_vars = [
        "KUCOIN_API_KEY",
        "KUCOIN_API_SECRET", 
        "KUCOIN_PASSPHRASE"
    ]
    
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    if missing_vars:
        logger.warning(f"⚠️ Environment variables não configuradas: {missing_vars}")
        logger.warning("Sistema rodará em modo paper trading")
    else:
        logger.info("✅ Credenciais KuCoin configuradas")
    
    # Check configuration file
    config_path = "config/holographic_trading_config.yaml"
    if not Path(config_path).exists():
        logger.error(f"❌ Arquivo de configuração não encontrado: {config_path}")
        return False
    
    logger.info("✅ Configuração encontrada")
    return True

async def main():
    """Deploy e executa sistema de trading holográfico."""
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("🌌 QUALIA Holographic Trading System")
    logger.info("=" * 50)
    
    # Validate environment
    if not await validate_environment():
        return
    
    # Configuration
    config_path = "config/holographic_trading_config.yaml"
    
    # Initialize orchestrator
    orchestrator = HolographicTradingOrchestrator(config_path)
    
    try:
        # Initialize system
        await orchestrator.initialize()
        
        # Run trading loop
        await orchestrator.run_trading_loop()
        
    except KeyboardInterrupt:
        logger.info("⏹️ Interrupção pelo usuário")
        
    except Exception as e:
        logger.error(f"❌ Erro crítico: {e}", exc_info=True)
        
    finally:
        # Graceful shutdown
        await orchestrator.shutdown()

if __name__ == "__main__":
    asyncio.run(main()) 