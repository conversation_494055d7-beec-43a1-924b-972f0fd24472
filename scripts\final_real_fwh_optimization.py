#!/usr/bin/env python3
"""
Otimização FINAL da estratégia FWH com dados reais da API Binance.
"""

import sys
import os
import asyncio
import pandas as pd
import numpy as np
from dotenv import load_dotenv
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging
from itertools import product

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Carregar variáveis de ambiente
load_dotenv(os.path.join(os.path.dirname(__file__), '..', '.env'))

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class OptimizationResult:
    """Resultado de otimização."""
    parameters: Dict[str, Any]
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    signals_generated: int
    score: float
    is_profitable: bool

class FinalFWHOptimizer:
    """Otimizador final da estratégia FWH com dados reais."""
    
    def __init__(self):
        self.results: List[OptimizationResult] = []
        
        # Ranges otimizados baseados na simulação anterior
        self.param_ranges = {
            'hype_threshold': [0.01, 0.05, 0.10, 0.15],
            'wave_min_strength': [0.005, 0.01, 0.05, 0.10],
            'quantum_boost_factor': [1.01, 1.05, 1.08, 1.10],
            'fib_lookback': [20, 30, 50]
        }
        
        logger.info("🎯 Final FWH Optimizer initialized")
        logger.info(f"   Parameter combinations: {len(list(product(*self.param_ranges.values())))}")
    
    async def get_real_data(self) -> pd.DataFrame:
        """Obtém dados reais da API Binance."""
        try:
            from qualia.market.binance_integration import BinanceIntegration
            from qualia.common.specs import MarketSpec
            
            # Credenciais
            api_key = os.getenv('BINANCE_API_KEY')
            api_secret = os.getenv('BINANCE_API_SECRET')
            
            if not api_key or not api_secret:
                raise ValueError("Binance credentials not found")
            
            # Conectar
            binance = BinanceIntegration(api_key=api_key, api_secret=api_secret)
            await binance.initialize_connection()
            
            # Período (últimos 7 dias)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            
            logger.info(f"Fetching real data: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            
            # Obter dados 1h
            spec = MarketSpec(symbol="BTC/USDT", timeframe="1h")
            df = await binance.fetch_historical_data(
                spec=spec,
                start_date=start_date,
                end_date=end_date,
                use_cache=True
            )
            
            await binance.close()
            
            if df.empty:
                raise ValueError("No data received")
            
            logger.info(f"✅ Real data loaded: {len(df)} candles")
            return df
            
        except Exception as e:
            logger.error(f"Error getting real data: {e}")
            raise
    
    async def test_parameters(self, params: Dict[str, Any], data: pd.DataFrame) -> OptimizationResult:
        """Testa parâmetros específicos."""
        try:
            from qualia.strategies.fibonacci_wave_hype import FibonacciWaveHypeStrategy
            from qualia.strategies.strategy_interface import TradingContext
            
            # Criar estratégia
            strategy = FibonacciWaveHypeStrategy(
                symbol="BTC/USDT",
                timeframe="1h",
                parameters=params
            )
            
            # Configurações de backtest
            initial_capital = 10000.0
            commission = 0.001
            current_capital = initial_capital
            position = 0.0
            trades = []
            signals_count = 0
            
            # Testar com últimas 40 velas
            test_data = data.tail(40).copy()
            
            for i in range(params['fib_lookback'], len(test_data)):
                try:
                    # Dados até momento atual
                    current_data = test_data.iloc[:i+1]
                    current_price = float(current_data['close'].iloc[-1])
                    
                    # Criar contexto
                    context = TradingContext(
                        symbol="BTC/USDT",
                        timeframe="1h",
                        ohlcv=current_data,
                        current_price=current_price,
                        timestamp=pd.Timestamp.now(),
                        wallet_state={"BTC": position, "USDT": current_capital},
                        liquidity=0.5,
                        volatility=0.02,
                        strategy_metrics={},
                        quantum_metrics={},
                        market_state="trend",
                        risk_mode="normal"
                    )
                    
                    # Gerar sinal
                    signal_df = strategy.generate_signal(context)
                    
                    if not signal_df.empty:
                        signals_count += 1
                        signal = signal_df.iloc[0]['signal']
                        confidence = signal_df.iloc[0]['confidence']
                        
                        # Executar trade se confidence > 0.2
                        if confidence > 0.2:
                            if signal == 'buy' and position == 0:
                                # Comprar
                                amount = (current_capital * 0.9) / current_price
                                position = amount
                                current_capital = current_capital * 0.1
                                trades.append({
                                    'type': 'buy',
                                    'price': current_price,
                                    'amount': amount,
                                    'confidence': confidence
                                })
                                
                            elif signal == 'sell' and position > 0:
                                # Vender
                                current_capital = position * current_price * (1 - commission)
                                trades.append({
                                    'type': 'sell',
                                    'price': current_price,
                                    'amount': position,
                                    'confidence': confidence
                                })
                                position = 0.0
                
                except Exception:
                    continue
            
            # Calcular métricas
            final_value = current_capital + (position * test_data['close'].iloc[-1] if position > 0 else 0)
            total_return = (final_value - initial_capital) / initial_capital
            
            # Win rate
            wins = 0
            total_pairs = 0
            for i in range(0, len(trades) - 1, 2):
                if i + 1 < len(trades) and trades[i]['type'] == 'buy' and trades[i+1]['type'] == 'sell':
                    total_pairs += 1
                    if trades[i+1]['price'] > trades[i]['price']:
                        wins += 1
            
            win_rate = wins / total_pairs if total_pairs > 0 else 0
            
            # Sharpe ratio simples
            if total_pairs > 1:
                returns = []
                for i in range(0, len(trades) - 1, 2):
                    if i + 1 < len(trades):
                        ret = (trades[i+1]['price'] - trades[i]['price']) / trades[i]['price']
                        returns.append(ret)
                sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
            else:
                sharpe_ratio = 0
            
            # Outras métricas
            max_drawdown = 0.05  # Placeholder
            profit_factor = 1.0 + total_return if total_return > 0 else 0.5
            
            # Score composto
            score = (
                total_return * 0.4 +
                sharpe_ratio * 0.3 +
                win_rate * 0.2 +
                (1 - max_drawdown) * 0.1
            )
            
            # Critérios de sucesso
            is_profitable = (
                total_return > 0.01 and  # > 1%
                win_rate > 0.3 and      # > 30%
                total_pairs >= 3 and    # Min 3 trades
                signals_count >= 5      # Min 5 sinais
            )
            
            return OptimizationResult(
                parameters=params,
                total_return=total_return,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=profit_factor,
                total_trades=total_pairs,
                signals_generated=signals_count,
                score=score,
                is_profitable=is_profitable
            )
            
        except Exception as e:
            logger.error(f"Error testing parameters {params}: {e}")
            return OptimizationResult(
                parameters=params,
                total_return=-1.0,
                sharpe_ratio=0.0,
                max_drawdown=1.0,
                win_rate=0.0,
                profit_factor=0.0,
                total_trades=0,
                signals_generated=0,
                score=-1.0,
                is_profitable=False
            )
    
    async def run_optimization(self, max_combinations: int = 20) -> List[OptimizationResult]:
        """Executa otimização completa."""
        logger.info(f"🚀 Starting FINAL FWH optimization with REAL API data")
        
        # Obter dados reais
        data = await self.get_real_data()
        
        # Gerar combinações
        param_names = list(self.param_ranges.keys())
        param_values = list(self.param_ranges.values())
        all_combinations = list(product(*param_values))
        
        # Limitar combinações
        if len(all_combinations) > max_combinations:
            step = len(all_combinations) // max_combinations
            combinations = all_combinations[::step][:max_combinations]
        else:
            combinations = all_combinations
        
        logger.info(f"   Testing {len(combinations)} parameter combinations")
        
        # Testar cada combinação
        for i, combination in enumerate(combinations):
            params = dict(zip(param_names, combination))
            
            logger.info(f"   Testing {i+1}/{len(combinations)}: {params}")
            
            result = await self.test_parameters(params, data)
            self.results.append(result)
            
            status = "✅ PROFITABLE" if result.is_profitable else "❌ UNPROFITABLE"
            logger.info(f"   Result: {status} - Return: {result.total_return:.2%}, Trades: {result.total_trades}")
        
        # Ordenar por score
        self.results.sort(key=lambda x: x.score, reverse=True)
        
        logger.info(f"✅ Optimization completed: {len(self.results)} results")
        return self.results
    
    def generate_report(self) -> str:
        """Gera relatório final."""
        if not self.results:
            return "❌ No results available"
        
        profitable = [r for r in self.results if r.is_profitable]
        
        report = f"""🎯 FINAL FWH OPTIMIZATION REPORT (REAL API DATA)
============================================================

📊 OVERVIEW:
   Total Combinations: {len(self.results)}
   Profitable Configs: {len(profitable)}
   Success Rate: {len(profitable)/len(self.results)*100:.1f}%
   Data Source: Binance API (Real BTC/USDT)

🏆 TOP 5 CONFIGURATIONS:
"""
        
        for i, result in enumerate(self.results[:5]):
            status = "✅ PROFITABLE" if result.is_profitable else "❌ UNPROFITABLE"
            report += f"""
   #{i+1} {status}
      Score: {result.score:.4f}
      Return: {result.total_return:.2%} | Win Rate: {result.win_rate:.1%}
      Trades: {result.total_trades} | Signals: {result.signals_generated}
      Parameters: {result.parameters}"""
        
        if profitable:
            best = profitable[0]
            report += f"""

🎯 BEST PROFITABLE CONFIGURATION:
   Return: {best.total_return:.2%}
   Win Rate: {best.win_rate:.1%}
   Trades: {best.total_trades}
   Signals: {best.signals_generated}
   Parameters: {best.parameters}
   
✅ {len(profitable)} PROFITABLE CONFIGURATIONS FOUND WITH REAL DATA!"""
        else:
            report += "\n\n❌ NO PROFITABLE CONFIGURATIONS FOUND"
        
        return report

async def main():
    """Executa otimização final."""
    print("🎯 FINAL FWH OPTIMIZATION WITH REAL API DATA")
    print("=" * 70)
    
    optimizer = FinalFWHOptimizer()
    
    try:
        # Executar otimização
        results = await optimizer.run_optimization(max_combinations=16)
        
        # Gerar relatório
        report = optimizer.generate_report()
        print(report)
        
        # Salvar resultados
        os.makedirs('scripts/logs', exist_ok=True)
        
        import json
        with open('scripts/logs/final_fwh_optimization_results.json', 'w') as f:
            results_data = []
            for result in results:
                results_data.append({
                    'parameters': result.parameters,
                    'total_return': result.total_return,
                    'sharpe_ratio': result.sharpe_ratio,
                    'max_drawdown': result.max_drawdown,
                    'win_rate': result.win_rate,
                    'profit_factor': result.profit_factor,
                    'total_trades': result.total_trades,
                    'signals_generated': result.signals_generated,
                    'score': result.score,
                    'is_profitable': result.is_profitable
                })
            json.dump(results_data, f, indent=2)
        
        print(f"\n💾 Results saved to: scripts/logs/final_fwh_optimization_results.json")
        
        profitable_count = len([r for r in results if r.is_profitable])
        return profitable_count > 0
        
    except Exception as e:
        logger.error(f"❌ Optimization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print(f"\n🎉 FINAL OPTIMIZATION SUCCESSFUL WITH REAL DATA!")
    else:
        print(f"\n🔄 OPTIMIZATION COMPLETE - REVIEW PARAMETERS")
