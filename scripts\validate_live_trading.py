#!/usr/bin/env python3
"""
QUALIA P-02.3: Live Trading Validation
Test live trading execution path with real QUALIA quantum-computational components

This script validates that all QUALIA components work correctly in production mode
with real KuCoin API credentials before full deployment.

VALIDATION TESTS:
- Real credential authentication
- Market data collection with production API
- QUALIA quantum-computational analysis
- Signal generation and validation
- Risk management with ultra-conservative parameters
- Execution engine in live mode (minimal capital)
- Position management and PnL calculation
- Emergency stop and circuit breaker systems

CUSTOMIZATION NOTES:
- Modify API endpoints in validate_market_data_connection() for your KuCoin setup
- Adjust validation thresholds in validate_risk_management() as needed
- Update symbol list in validate_signal_generation() for your trading pairs
- Customize alert thresholds in validate_emergency_systems() for your requirements
"""

import asyncio
import json
import logging
import os
import sys
import time
import traceback
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import yaml
from cryptography.fernet import Fernet
import base64

# Setup logging with UTF-8 encoding for Windows compatibility
import io
import sys

# Create logs directory if it doesn't exist
Path("logs/pilot").mkdir(parents=True, exist_ok=True)

# Configure logging with proper encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/pilot/live_trading_validation.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class LiveTradingValidator:
    """Comprehensive validation of live trading capabilities"""

    def __init__(self, config_path: str = "config/pilot_config.yaml"):
        self.config_path = config_path
        self.config = None
        self.credentials = None
        self.validation_results = {}
        self.test_capital = 100.0  # Minimal capital for validation

        # Validation tracking
        self.tests_passed = 0
        self.tests_failed = 0
        self.critical_failures = []

        # Customizable settings - MODIFY THESE FOR YOUR SETUP
        self.test_symbols = ["BTC/USDT", "ETH/USDT"]  # Add your preferred trading pairs
        self.test_timeframes = ["1m", "5m", "15m"]    # Add your preferred timeframes
        self.api_timeout = 30                         # API timeout in seconds
        self.validation_cycles = 3                    # Number of validation cycles to run

        logger.info("Live Trading Validator initialized")

    def load_configuration(self) -> bool:
        """Load pilot configuration"""
        try:
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)

            logger.info(f"Configuration loaded: {self.config_path}")

            # Validate production mode
            if self.config.get('qualia_integration', {}).get('mode') != 'live':
                logger.warning("Configuration is not in 'live' mode - this may be intentional for testing")
                # Don't fail here - allow validation in paper_trading mode for testing

            return True

        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            return False

    def load_real_credentials(self) -> bool:
        """Load and validate real production credentials"""
        try:
            config_dir = Path("config")

            # Check if credential files exist
            master_key_file = config_dir / ".pilot_master.key"
            credentials_file = config_dir / ".pilot_credentials"

            if not master_key_file.exists():
                logger.error("Master key file not found. Run setup_real_credentials.py first.")
                return False

            if not credentials_file.exists():
                logger.error("Credentials file not found. Run setup_real_credentials.py first.")
                return False

            # Load master key
            with open(master_key_file, 'rb') as f:
                master_key = f.read()

            # Load encrypted credentials
            with open(credentials_file, 'r') as f:
                encrypted_credentials = f.read()

            # Decrypt credentials
            f = Fernet(master_key)
            encrypted_bytes = base64.b64decode(encrypted_credentials.encode())
            decrypted = f.decrypt(encrypted_bytes)
            self.credentials = json.loads(decrypted.decode())

            # Validate credential structure
            required_fields = ['environment', 'credentials']
            for field in required_fields:
                if field not in self.credentials:
                    raise ValueError(f"Missing required field in credentials: {field}")

            # Validate API credentials
            api_creds = self.credentials.get('credentials', {})
            if not all(key in api_creds for key in ['api_key', 'api_secret', 'passphrase']):
                raise ValueError("Missing API credentials (api_key, api_secret, passphrase)")

            logger.info("[OK] Real production credentials loaded and validated")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to load real credentials: {e}")
            return False

    async def validate_market_data_connection(self) -> bool:
        """Validate real-time market data connection"""
        try:
            logger.info("[DATA] Testing market data connection...")

            # CUSTOMIZATION POINT: Modify this section for your specific KuCoin setup
            # You can adjust the symbols, timeframes, and validation criteria here

            # Import and initialize enhanced data collector
            from qualia.consciousness.enhanced_data_collector import EnhancedDataCollector

            data_collector = EnhancedDataCollector(
                exchange_name="kucoin",
                symbols=self.test_symbols,  # Uses customizable symbol list
                timeframes=self.test_timeframes,  # Uses customizable timeframe list
                config=self.config.get('qualia_integration', {}).get('data_collector', {})
            )

            # Test data collection with timeout
            try:
                market_data = await asyncio.wait_for(
                    data_collector.collect_enhanced_market_data(),
                    timeout=self.api_timeout
                )
            except asyncio.TimeoutError:
                raise Exception(f"Market data collection timed out after {self.api_timeout} seconds")

            if not market_data:
                raise Exception("No market data received")

            # Validate data for each test symbol
            for symbol in self.test_symbols:
                if symbol not in market_data:
                    raise Exception(f"Missing data for symbol: {symbol}")

                symbol_data = market_data[symbol]
                for timeframe in self.test_timeframes:
                    if timeframe not in symbol_data:
                        raise Exception(f"Missing {timeframe} data for {symbol}")

            logger.info("[OK] Market data connection validated")
            self.validation_results['market_data'] = True
            self.tests_passed += 1
            return True

        except Exception as e:
            logger.error(f"[ERROR] Market data validation failed: {e}")
            self.validation_results['market_data'] = False
            self.tests_failed += 1
            self.critical_failures.append("Market data connection failed")
            return False

    async def validate_qualia_components(self) -> bool:
        """Validate QUALIA quantum-computational components"""
        try:
            logger.info("[BRAIN] Testing QUALIA quantum-computational components...")

            # Import QUALIA components - use the correct class name
            from scripts.qualia_pilot_trading_system import QualiaEnhancedTradingSystem

            # Initialize QUALIA system
            qualia_system = QualiaEnhancedTradingSystem(self.config_path)

            # Load configuration
            if not await qualia_system.load_configuration():
                raise Exception("Failed to load QUALIA configuration")

            # Initialize components
            if not await qualia_system.initialize_qualia_components():
                raise Exception("Failed to initialize QUALIA components")

            # Test oracle decision engine
            if not hasattr(qualia_system, 'oracle_decision_engine'):
                raise Exception("QASTOracleDecisionEngine not initialized")

            # Test consciousness system
            if not hasattr(qualia_system, 'consciousness_system'):
                raise Exception("UnifiedQUALIAConsciousness not initialized")

            # Test signal generator
            if not hasattr(qualia_system, 'signal_generator'):
                raise Exception("SignalGenerator not initialized")

            logger.info("[OK] QUALIA quantum-computational components validated")
            self.validation_results['qualia_components'] = True
            self.tests_passed += 1
            return True

        except Exception as e:
            logger.error(f"[ERROR] QUALIA components validation failed: {e}")
            self.validation_results['qualia_components'] = False
            self.tests_failed += 1
            self.critical_failures.append("QUALIA components initialization failed")
            return False

    async def validate_signal_generation(self) -> bool:
        """Validate signal generation with real market data"""
        try:
            logger.info("[DATA] Testing signal generation...")

            # CUSTOMIZATION POINT: Modify signal validation criteria here
            # You can adjust the expected signal types, confidence thresholds, etc.

            # Import signal generator
            from qualia.signals.generator import SignalGenerator

            signal_generator = SignalGenerator(
                config=self.config.get('qualia_integration', {}).get('signal_generator', {})
            )

            # Test signal generation for each symbol
            for symbol in self.test_symbols:
                logger.info(f"Testing signal generation for {symbol}")

                # Generate signals with timeout
                try:
                    signals = await asyncio.wait_for(
                        signal_generator.generate_signals(symbol),
                        timeout=self.api_timeout
                    )
                except asyncio.TimeoutError:
                    raise Exception(f"Signal generation timed out for {symbol}")

                if not signals:
                    raise Exception(f"No signals generated for {symbol}")

                # Validate signal structure
                for signal in signals:
                    required_fields = ['symbol', 'signal_type', 'confidence', 'timestamp']
                    for field in required_fields:
                        if field not in signal:
                            raise Exception(f"Missing field '{field}' in signal for {symbol}")

                    # Validate confidence range (0-1)
                    if not 0 <= signal['confidence'] <= 1:
                        raise Exception(f"Invalid confidence {signal['confidence']} for {symbol}")

            logger.info("[OK] Signal generation validated")
            self.validation_results['signal_generation'] = True
            self.tests_passed += 1
            return True

        except Exception as e:
            logger.error(f"[ERROR] Signal generation validation failed: {e}")
            self.validation_results['signal_generation'] = False
            self.tests_failed += 1
            self.critical_failures.append("Signal generation failed")
            return False

    async def validate_risk_management(self) -> bool:
        """Validate ultra-conservative risk management parameters"""
        try:
            logger.info("[SAFETY] Testing risk management...")

            # CUSTOMIZATION POINT: Adjust risk thresholds here for your requirements
            expected_max_capital = 1000.0
            expected_max_position_pct = 2.0
            expected_daily_loss_limit = 50.0
            expected_max_drawdown_pct = 3.0

            # Load risk configuration
            risk_config = self.config.get('risk_management', {})

            # Validate capital limits
            max_capital = risk_config.get('max_capital_usd', 0)
            if max_capital > expected_max_capital:
                raise Exception(f"Max capital {max_capital} exceeds ultra-conservative limit {expected_max_capital}")

            # Validate position size limits
            max_position_pct = risk_config.get('max_position_size_pct', 0)
            if max_position_pct > expected_max_position_pct:
                raise Exception(f"Max position size {max_position_pct}% exceeds limit {expected_max_position_pct}%")

            # Validate daily loss limits
            daily_loss_limit = risk_config.get('daily_loss_limit_usd', 0)
            if daily_loss_limit > expected_daily_loss_limit:
                raise Exception(f"Daily loss limit {daily_loss_limit} exceeds limit {expected_daily_loss_limit}")

            # Validate drawdown limits
            max_drawdown_pct = risk_config.get('max_drawdown_pct', 0)
            if max_drawdown_pct > expected_max_drawdown_pct:
                raise Exception(f"Max drawdown {max_drawdown_pct}% exceeds limit {expected_max_drawdown_pct}%")

            logger.info("[OK] Ultra-conservative risk management validated")
            self.validation_results['risk_management'] = True
            self.tests_passed += 1
            return True

        except Exception as e:
            logger.error(f"[ERROR] Risk management validation failed: {e}")
            self.validation_results['risk_management'] = False
            self.tests_failed += 1
            self.critical_failures.append("Risk management validation failed")
            return False

    async def validate_execution_engine(self) -> bool:
        """Validate execution engine in live mode"""
        try:
            logger.info("[ZAP] Testing execution engine...")

            # Import execution engine
            from qualia.core.qualia_execution_interface import QUALIAExecutionInterface as ExecutionEngine

            execution_engine = ExecutionEngine(
                config=self.config.get('qualia_integration', {}).get('execution_engine', {}),
                credentials=self.credentials
            )

            # Test execution engine initialization
            if not await execution_engine.initialize():
                raise Exception("Failed to initialize execution engine")

            # Test connection to exchange
            if not await execution_engine.test_connection():
                raise Exception("Failed to connect to exchange")

            # Test account balance retrieval
            try:
                balance = await asyncio.wait_for(
                    execution_engine.get_account_balance(),
                    timeout=self.api_timeout
                )
                if balance is None:
                    raise Exception("Failed to retrieve account balance")
            except asyncio.TimeoutError:
                raise Exception("Account balance retrieval timed out")

            logger.info("[OK] Execution engine validated")
            self.validation_results['execution_engine'] = True
            self.tests_passed += 1
            return True

        except Exception as e:
            logger.error(f"[ERROR] Execution engine validation failed: {e}")
            self.validation_results['execution_engine'] = False
            self.tests_failed += 1
            self.critical_failures.append("Execution engine validation failed")
            return False

    async def validate_emergency_systems(self) -> bool:
        """Validate emergency stop and circuit breaker systems"""
        try:
            logger.info("[ALERT] Testing emergency systems...")

            # CUSTOMIZATION POINT: Adjust emergency thresholds here
            emergency_config = self.config.get('emergency_systems', {})

            # Test circuit breaker configuration
            circuit_breaker = emergency_config.get('circuit_breaker', {})
            if not circuit_breaker.get('enabled', False):
                raise Exception("Circuit breaker not enabled")

            # Test emergency stop configuration
            emergency_stop = emergency_config.get('emergency_stop', {})
            if not emergency_stop.get('enabled', False):
                raise Exception("Emergency stop not enabled")

            # Validate alert thresholds
            alert_thresholds = emergency_config.get('alert_thresholds', {})
            required_alerts = ['pnl_loss_usd', 'drawdown_pct', 'position_loss_pct']
            for alert in required_alerts:
                if alert not in alert_thresholds:
                    raise Exception(f"Missing alert threshold: {alert}")

            logger.info("[OK] Emergency systems validated")
            self.validation_results['emergency_systems'] = True
            self.tests_passed += 1
            return True

        except Exception as e:
            logger.error(f"[ERROR] Emergency systems validation failed: {e}")
            self.validation_results['emergency_systems'] = False
            self.tests_failed += 1
            self.critical_failures.append("Emergency systems validation failed")
            return False

    async def run_comprehensive_validation(self) -> bool:
        """Run comprehensive live trading validation"""
        try:
            logger.info("[START] Starting comprehensive live trading validation...")

            # Load configuration
            if not self.load_configuration():
                logger.error("[ERROR] Configuration loading failed")
                return False

            # Load real credentials
            if not self.load_real_credentials():
                logger.error("[ERROR] Real credentials loading failed")
                return False

            # Run validation tests
            validation_tests = [
                ("Market Data Connection", self.validate_market_data_connection()),
                ("QUALIA Components", self.validate_qualia_components()),
                ("Signal Generation", self.validate_signal_generation()),
                ("Risk Management", self.validate_risk_management()),
                ("Execution Engine", self.validate_execution_engine()),
                ("Emergency Systems", self.validate_emergency_systems())
            ]

            # Execute validation tests
            for test_name, test_coro in validation_tests:
                logger.info(f"Running: {test_name}")
                try:
                    success = await test_coro
                    if success:
                        logger.info(f"[OK] {test_name}: PASSED")
                    else:
                        logger.error(f"[ERROR] {test_name}: FAILED")
                except Exception as e:
                    logger.error(f"[ERROR] {test_name}: FAILED with exception: {e}")
                    self.tests_failed += 1
                    self.critical_failures.append(f"{test_name} failed with exception")

            # Generate validation report
            return self.generate_validation_report()

        except Exception as e:
            logger.error(f"Comprehensive validation failed: {e}")
            return False

    def generate_validation_report(self) -> bool:
        """Generate comprehensive validation report"""
        try:
            total_tests = self.tests_passed + self.tests_failed
            success_rate = (self.tests_passed / total_tests * 100) if total_tests > 0 else 0
            production_ready = len(self.critical_failures) == 0 and success_rate == 100.0

            report = {
                "validation_timestamp": datetime.now(timezone.utc).isoformat(),
                "tests_passed": self.tests_passed,
                "tests_failed": self.tests_failed,
                "success_rate_percent": success_rate,
                "critical_failures": self.critical_failures,
                "validation_results": self.validation_results,
                "production_ready": production_ready,
                "test_configuration": {
                    "test_symbols": self.test_symbols,
                    "test_timeframes": self.test_timeframes,
                    "api_timeout": self.api_timeout,
                    "validation_cycles": self.validation_cycles
                }
            }

            # Save report
            report_file = Path("logs/pilot/live_trading_validation_report.json")
            report_file.parent.mkdir(parents=True, exist_ok=True)
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)

            # Display results
            print("\n" + "="*70)
            print("📊 LIVE TRADING VALIDATION RESULTS")
            print("="*70)
            print(f"Tests Passed: {self.tests_passed}")
            print(f"Tests Failed: {self.tests_failed}")
            print(f"Success Rate: {success_rate:.1f}%")
            print(f"Production Ready: {'[OK] YES' if production_ready else '[ERROR] NO'}")

            if self.critical_failures:
                print(f"\nCritical Failures:")
                for failure in self.critical_failures:
                    print(f"  [ERROR] {failure}")

            if production_ready:
                print("\n[SUCCESS] LIVE TRADING VALIDATION SUCCESSFUL!")
                print("[OK] QUALIA P-02.3 is ready for live production trading!")
            else:
                print("\n[FAILED] LIVE TRADING VALIDATION FAILED!")
                print("Please address the critical failures before proceeding.")

            print("="*70)

            return production_ready

        except Exception as e:
            logger.error(f"Failed to generate validation report: {e}")
            return False

async def main():
    """Main entry point"""
    try:
        print("\n" + "="*70)
        print("🧪 QUALIA P-02.3: Live Trading Validation")
        print("="*70)
        print("This will validate all QUALIA components for live production trading.")
        print("="*70)

        validator = LiveTradingValidator()
        success = await validator.run_comprehensive_validation()

        if success:
            print("\n[OK] Live trading validation completed successfully!")
            return True
        else:
            print("\n[ERROR] Live trading validation failed!")
            return False

    except KeyboardInterrupt:
        print("\n\nValidation cancelled by user.")
        return False
    except Exception as e:
        logger.error(f"Validation failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)