#!/usr/bin/env python3
"""
Valida qual estratégia está sendo usada atualmente no sistema QUALIA.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

import json
import yaml
from pathlib import Path

try:
    from qualia.config.config_manager import ConfigManager
    from qualia.strategies.strategy_factory import StrategyFactory
    from qualia.strategies.instance_registry import get_strategy_registry
    IMPORTS_OK = True
except ImportError as e:
    print(f"⚠️ Erro de importação: {e}")
    IMPORTS_OK = False


def check_strategy_configuration():
    """Verifica qual estratégia está configurada no sistema."""
    
    print("🔍 VALIDAÇÃO DA ESTRATÉGIA ATUAL DO SISTEMA QUALIA")
    print("=" * 60)
    
    # 1. Verificar configuração principal
    print("\n📋 1. VERIFICANDO CONFIGURAÇÃO PRINCIPAL...")
    
    config_files = [
        "config.yaml",
        "config/strategy_parameters.yaml", 
        "config/strategy_parameters.json",
        "config/production_config.yaml",
        "config/pilot_config.yaml"
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"   ✅ {config_file} encontrado")
            
            try:
                if config_file.endswith('.yaml'):
                    with open(config_file, 'r') as f:
                        config = yaml.safe_load(f)
                else:
                    with open(config_file, 'r') as f:
                        config = json.load(f)
                
                # Procurar por configuração de estratégia
                strategy_name = None
                if 'strategy_config' in config:
                    strategy_name = config['strategy_config'].get('name')
                elif 'strategy' in config:
                    strategy_name = config.get('strategy')
                elif 'strategies' in config:
                    strategy_name = list(config['strategies'].keys())[0] if config['strategies'] else None
                
                if strategy_name:
                    print(f"      🎯 Estratégia encontrada: {strategy_name}")
                else:
                    print(f"      ⚠️ Nenhuma estratégia específica encontrada")
                    
            except Exception as e:
                print(f"      ❌ Erro ao ler {config_file}: {e}")
        else:
            print(f"   ❌ {config_file} não encontrado")
    
    # 2. Verificar ConfigManager
    print("\n📋 2. VERIFICANDO CONFIG MANAGER...")

    if not IMPORTS_OK:
        print("   ❌ Imports falharam, pulando ConfigManager")
        config_manager = None
    else:
        try:
            config_manager = ConfigManager("config/strategy_parameters.yaml")
            config_manager.load()

            strategy_config = config_manager.get_strategy_config()
            print(f"   ✅ ConfigManager carregado com sucesso")
            print(f"   📊 Estratégia configurada: {strategy_config.get('name', 'N/A')}")

            # Verificar parâmetros da estratégia
            params = strategy_config.get('params', {})
            print(f"   📋 Parâmetros disponíveis: {len(params)} configurados")

            if params:
                print(f"   🔧 Alguns parâmetros:")
                for key, value in list(params.items())[:5]:
                    print(f"      - {key}: {value}")

        except Exception as e:
            print(f"   ❌ Erro ao carregar ConfigManager: {e}")
            config_manager = None
    
    # 3. Verificar StrategyFactory
    print("\n📋 3. VERIFICANDO STRATEGY FACTORY...")

    if not IMPORTS_OK:
        print("   ❌ Imports falharam, pulando StrategyFactory")
    else:
        try:
            # Tentar criar estratégia com alias padrão
            strategy_aliases = ["NovaEstrategiaQUALIA", "enhanced_quantum_momentum", "EnhancedQuantumMomentumStrategy"]

            for alias in strategy_aliases:
                try:
                    print(f"   🧪 Testando alias: {alias}")

                    strategy = StrategyFactory.create_strategy(
                        alias=alias,
                        context={"symbol": "BTC/USDT", "timeframe": "1h"},
                        config_manager=config_manager
                    )

                    print(f"   ✅ Estratégia criada com sucesso: {type(strategy).__name__}")
                    print(f"      📍 Módulo: {type(strategy).__module__}")
                    print(f"      🏷️ Classe: {type(strategy).__qualname__}")

                    # Verificar se é a estratégia corrigida
                    if hasattr(strategy, 'signal_threshold'):
                        print(f"      🎯 Signal threshold: {strategy.signal_threshold}")
                    if hasattr(strategy, 'rsi_overbought'):
                        print(f"      📊 RSI overbought: {strategy.rsi_overbought}")
                    if hasattr(strategy, 'rsi_oversold'):
                        print(f"      📊 RSI oversold: {strategy.rsi_oversold}")

                    break

                except Exception as e:
                    print(f"      ❌ Falha com alias {alias}: {e}")

        except Exception as e:
            print(f"   ❌ Erro geral no StrategyFactory: {e}")
    
    # 4. Verificar Registry de Instâncias
    print("\n📋 4. VERIFICANDO REGISTRY DE INSTÂNCIAS...")

    if not IMPORTS_OK:
        print("   ❌ Imports falharam, pulando Registry")
    else:
        try:
            registry = get_strategy_registry()
            instances = registry.list_instances()

            print(f"   📊 Instâncias ativas no registry: {len(instances)}")

            for instance_info in instances:
                print(f"   🔧 {instance_info['key']}")
                print(f"      Tipo: {instance_info['type']}")
                print(f"      Ativa: {instance_info['is_active']}")
                print(f"      Criada: {instance_info['created_at']}")

        except Exception as e:
            print(f"   ❌ Erro ao acessar registry: {e}")
    
    # 5. Verificar estratégias disponíveis
    print("\n📋 5. VERIFICANDO ESTRATÉGIAS DISPONÍVEIS...")

    if not IMPORTS_OK:
        print("   ❌ Imports falharam, tentando importação direta...")

    try:
        from qualia.strategies import (
            EnhancedQuantumMomentumStrategy,
            QualiaTSVFStrategy
        )
        
        print(f"   ✅ EnhancedQuantumMomentumStrategy disponível")
        print(f"      📍 Módulo: {EnhancedQuantumMomentumStrategy.__module__}")
        
        print(f"   ✅ QualiaTSVFStrategy (Nova) disponível") 
        print(f"      📍 Módulo: {QualiaTSVFStrategy.__module__}")
        
        # Verificar parâmetros padrão da Enhanced Quantum Momentum
        try:
            test_strategy = EnhancedQuantumMomentumStrategy(
                symbol="BTC/USDT",
                timeframe="1h"
            )
            
            print(f"\n   🔍 PARÂMETROS DA ENHANCED QUANTUM MOMENTUM:")
            print(f"      Signal threshold: {test_strategy.signal_threshold}")
            print(f"      RSI overbought: {test_strategy.rsi_overbought}")
            print(f"      RSI oversold: {test_strategy.rsi_oversold}")
            print(f"      Take profit R: {test_strategy._take_profit_r_multiple_value}")
            print(f"      Stop loss R: {test_strategy._stop_loss_r_multiple_value}")
            
            # Verificar se as correções foram aplicadas
            corrections_applied = []
            if test_strategy.signal_threshold <= 0.60:
                corrections_applied.append("✅ Signal threshold otimizado")
            else:
                corrections_applied.append("❌ Signal threshold não otimizado")
                
            if test_strategy.rsi_overbought <= 68:
                corrections_applied.append("✅ RSI range expandido")
            else:
                corrections_applied.append("❌ RSI range não expandido")
                
            if test_strategy._take_profit_r_multiple_value >= 2.3:
                corrections_applied.append("✅ Take profit otimizado")
            else:
                corrections_applied.append("❌ Take profit não otimizado")
            
            print(f"\n   📊 STATUS DAS CORREÇÕES:")
            for correction in corrections_applied:
                print(f"      {correction}")
                
        except Exception as e:
            print(f"   ❌ Erro ao testar Enhanced Quantum Momentum: {e}")
        
    except ImportError as e:
        print(f"   ❌ Erro ao importar estratégias: {e}")
    
    # 6. Recomendações
    print(f"\n💡 RECOMENDAÇÕES:")
    print(f"   1. Verificar se Enhanced Quantum Momentum está sendo usada")
    print(f"   2. Confirmar que correções foram aplicadas")
    print(f"   3. Testar execução do sistema principal")
    print(f"   4. Monitorar performance em tempo real")


if __name__ == "__main__":
    check_strategy_configuration()
