# Retrocausality Operator

O operador de *Retrocausalidade* investiga possíveis influências de eventos futuros no presente. Analisa correlações temporais e gera eventos retrocausais que são registrados para estimar a força do campo temporal.

Principais características
-------------------------
- Janela temporal configurável e previsão simples de estado futuro.
- Detecção de correlações entre mudanças passadas e indicadores derivados de previsões.
- Cálculo de loops causais e confiança nas previsões com base em consistência histórica.
- Canal TSVF para mesclar janelas de preço e gerar sinais utilizados no ajuste de envelopes de intenção.
- `causal_loop_ratio` em `qualia.core.retrocausality` estima a proporção de loops
  causais entre os eventos ativos.
- Função `apply_retrocausality` que realiza mistura ponderada entre presente e futuro.

## Exemplo de Uso
```python
import numpy as np
from qualia.core.retrocausality import RetrocausalityOperator

config = {"window_size": 50}
operator = RetrocausalityOperator(config)

series = np.random.randn(100)
state = asyncio.run(operator.analyze_retrocausality(series, timestamp=0.0))
print(state.prediction_confidence)
```
