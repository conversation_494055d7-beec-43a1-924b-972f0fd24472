"""Maintain a directed graph of symbolic-token transitions.

This lightweight helper uses *networkx* when available; otherwise it falls back
on a minimal in-memory representation so that the rest of QUALIA does not crash
if the dependency is missing. The graph can be queried as a JSON-serialisable
``{"nodes": [...], "links": [...]}`` structure that the UI understands.

Nodes represent individual symbolic tokens (e.g. ``S1A3S0F0``).
Edges represent an observed transition *token_i → token_{i+1}* inside the same
OperatorSIM modulation batch.

Keeping this module isolated avoids polluting core logic with optional
visualisation concerns.
"""
from __future__ import annotations

from typing import Any, Dict, List, Tuple

import os

try:
    import redis  # type: ignore
except ModuleNotFoundError:  # pragma: no cover - optional
    redis = None

try:
    import rocksdb  # type: ignore
except ModuleNotFoundError:  # pragma: no cover - optional
    rocksdb = None

try:
    import networkx as nx  # type: ignore
except ModuleNotFoundError:  # pragma: no cover – optional dependency
    nx = None  # type: ignore

__all__ = [
    "add_path",
    "get_snapshot",
    "get_stats",
]

if nx is not None:
    _G = nx.DiGraph()
else:
    # Very small substitute: dict of dicts with weights
    _edges: Dict[Tuple[str, str], int] = {}

# ---------------------------------------------------------------------------
# Persistence and statistics
# ---------------------------------------------------------------------------
_cycles: Dict[Tuple[str, ...], int] = {}
_features: Dict[str, int] = {}

_redis_client: Any | None = None
_rocks: Any | None = None
_loaded = False


def _add_edge_memory(u: str, v: str, weight: int = 1) -> None:
    """Store edge in-memory, regardless of backend."""
    if nx is not None:
        if not _G.has_edge(u, v):
            _G.add_edge(u, v, weight=weight)
        else:
            _G[u][v]["weight"] = _G[u][v].get("weight", 0) + weight
    else:
        _edges[(u, v)] = _edges.get((u, v), 0) + weight


def _persist_edge(u: str, v: str) -> None:
    if _redis_client is not None:
        try:
            key = f"sim_graph:{u}:{v}"
            _redis_client.incr(key)
            ttl = int(os.getenv("QUALIA_SIM_GRAPH_TTL", "0"))
            if ttl > 0:
                _redis_client.expire(key, ttl)
        except Exception:
            pass
    if _rocks is not None:
        try:
            key = f"{u}->{v}".encode()
            val = _rocks.get(key)
            current = int(val.decode()) if val else 0
            _rocks.put(key, str(current + 1).encode())
        except Exception:
            pass


def _load_persistence() -> None:
    global _loaded, _redis_client, _rocks
    if _loaded:
        return
    redis_url = os.getenv("QUALIA_SIM_GRAPH_REDIS_URL")
    rocks_path = os.getenv("QUALIA_SIM_GRAPH_ROCKSDB_PATH")
    if redis_url and redis is not None:
        try:
            _redis_client = redis.from_url(redis_url)
            for key in _redis_client.scan_iter("sim_graph:*"):
                parts = key.decode().split(":", 2)
                if len(parts) == 3:
                    u, v = parts[1:]
                    w = int(_redis_client.get(key) or 0)
                    _add_edge_memory(u, v, w)
        except Exception:
            _redis_client = None
    if rocks_path and rocksdb is not None:
        try:
            opts = rocksdb.Options(create_if_missing=True)
            _rocks = rocksdb.DB(rocks_path, opts)
            it = _rocks.iterkeys()
            it.seek_to_first()
            while it.valid():
                key = it.key().decode()
                if "->" in key:
                    u, v = key.split("->")
                    w = int(_rocks.get(it.key()).decode())
                    _add_edge_memory(u, v, w)
                it.next()
        except Exception:
            _rocks = None
    _loaded = True


def add_path(tokens: List[str]) -> None:
    """Add a sequence of *tokens* as a directed path in the graph."""
    if len(tokens) < 2:
        return

    _load_persistence()

    for u, v in zip(tokens, tokens[1:]):
        _add_edge_memory(u, v)
        _persist_edge(u, v)
        _features[f"{u}->{v}"] = _features.get(f"{u}->{v}", 0) + 1

    for a, b, c in zip(tokens, tokens[1:], tokens[2:]):
        feat = f"{a}->{b}->{c}"
        _features[feat] = _features.get(feat, 0) + 1

    seen: Dict[str, int] = {}
    for idx, tok in enumerate(tokens):
        if tok in seen:
            cycle = tuple(tokens[seen[tok] : idx + 1])
            _cycles[cycle] = _cycles.get(cycle, 0) + 1
        else:
            seen[tok] = idx


def _build_snapshot() -> Dict[str, List[Dict]]:
    """Return the graph as a ``{nodes, links}`` JSON-ready dict."""
    if nx is not None:
        nodes = [{"id": n, "weight": _G.degree(n)} for n in _G.nodes]  # type: ignore[arg-type]
        links = [
            {"source": u, "target": v, "weight": d.get("weight", 1)}
            for u, v, d in _G.edges(data=True)
        ]
    else:
        node_set = {n for edge in _edges for n in edge}
        nodes = [{"id": n, "weight": 1} for n in node_set]
        links = [
            {"source": u, "target": v, "weight": w} for (u, v), w in _edges.items()
        ]
    return {"nodes": nodes, "links": links}


def get_snapshot() -> Dict[str, List[Dict]]:  # noqa: D401 simple
    """Return a copy of the current graph for external consumption."""
    return _build_snapshot().copy()


def get_stats(top_n: int = 5) -> Dict[str, Any]:  # noqa: D401 simple
    """Return cycle and feature statistics used by the holographic dashboard."""
    cycles = sorted(_cycles.items(), key=lambda x: -x[1])[:top_n]
    cycle_list = [{"cycle": list(c), "count": cnt} for c, cnt in cycles]
    feat_pairs = sorted(_features.items(), key=lambda x: -x[1])[:top_n]
    features = {name: count for name, count in feat_pairs}
    return {
        "cycle_count": len(_cycles),
        "cycles": cycle_list,
        "features": features,
    }
