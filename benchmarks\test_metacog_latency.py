import pytest

pytest.importorskip("pytest_benchmark")

from qualia.metacognition.layer import QuantumMetacognitionLayer
from qualia.metacognition.metacognition_trading import (
    QUALIAMetacognitionTrading,
    DecisionContext,
)
from qualia.memory.quantum_pattern_memory import Quantum<PERSON><PERSON>ernMemory
from qualia.common_types import QuantumSignaturePacket


class DummyUniverse:
    def __init__(self):
        self.n_qubits = 1

    def get_current_statevector(self):
        return None

    def calculate_otoc(self, *_, **__):
        return 0.5


def test_layer_evaluate_latency(benchmark):
    qpm = QuantumPatternMemory(max_memory_size_per_dimension=10, enable_warmstart=False)
    universe = DummyUniverse()
    layer = QuantumMetacognitionLayer(universe=universe, pattern_memory=qpm)
    packet = QuantumSignaturePacket(vector=[0.1] * 8, metrics={})
    benchmark(lambda: layer.evaluate_pattern(packet))


def test_trading_cycle_latency(benchmark):
    qpm = QuantumPatternMemory(max_memory_size_per_dimension=10, enable_warmstart=False)
    universe = DummyUniverse()
    metacog = QUALIAMetacognitionTrading(None, qpm, universe=universe)
    packet = QuantumSignaturePacket(vector=[0.2] * 8, metrics={})
    ctx = DecisionContext(quantum_signature_packet=packet)
    benchmark(lambda: metacog.run_with_qast_feedback(ctx, None))
