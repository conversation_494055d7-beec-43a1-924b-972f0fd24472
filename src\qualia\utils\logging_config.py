#!/usr/bin/env python3
"""Configuração centralizada de logging para o sistema QUALIA.

Implementa filtros e handlers únicos para evitar duplicação de logs.
Quando ``python-json-logger`` não está disponível, uma implementação mínima de
``JsonFormatter`` baseada em :func:`json.dumps` é usada como substituta para
manter a funcionalidade básica de logging.
"""

import json
import logging
import sys
import time
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional

from colorama import init as colorama_init

try:  # pragma: no cover - optional dependency
    from pythonjsonlogger.json import JsonFormatter as _JsonFormatter

    class _JsonLoggerModule:
        JsonFormatter = _JsonFormatter

    jsonlogger = _JsonLoggerModule()
except ImportError:  # pragma: no cover - fallback when package is missing

    class _FallbackJsonFormatter(logging.Formatter):
        """Minimal ``JsonFormatter`` replacement."""

        def add_fields(
            self, log_record: dict, record: logging.LogRecord, message_dict: dict
        ) -> None:
            log_record.update(record.__dict__)
            log_record.update(message_dict)

        def process_log_record(self, log_record: dict) -> dict:
            return log_record

        def format(self, record: logging.LogRecord) -> str:  # type: ignore[override]
            log_record: dict = {}
            message_dict = {"message": record.getMessage()}
            self.add_fields(log_record, record, message_dict)
            log_record = self.process_log_record(log_record)
            return json.dumps(log_record, default=str)

    class _JsonLoggerModule:
        JsonFormatter = _FallbackJsonFormatter

    jsonlogger = _JsonLoggerModule()  # type: ignore

from .correlation_id import get_correlation_id
from .tracing import get_current_trace_id

from ..utils.custom_logging import ColoredJSONFormatter, _utc_now_iso_ms

# Inicializa colorama para suportar cores no Windows
colorama_init(autoreset=True)

# Logger interno do módulo para mensagens de diagnóstico
logger = logging.getLogger(__name__)

# Níveis recomendados para loggers externos usados pelo sistema
EXTERNAL_LOGGERS: dict[str, int] = {
    "qiskit": logging.WARNING,
    "qiskit.compiler": logging.ERROR,
    "qiskit.passmanager": logging.ERROR,
    "matplotlib": logging.WARNING,
    "urllib3": logging.WARNING,
    "requests": logging.WARNING,
    "ccxt": logging.WARNING,
    "asyncio": logging.WARNING,
    "PIL.PngImagePlugin": logging.WARNING,
}


class StructuredFormatter(jsonlogger.JsonFormatter):
    """Formatter for QUALIA logs in JSON with UTC timestamps."""

    def add_fields(
        self, log_record: dict, record: logging.LogRecord, message_dict: dict
    ) -> None:
        super().add_fields(log_record, record, message_dict)
        log_record.setdefault("timestamp", _utc_now_iso_ms())
        log_record.setdefault("level", record.levelname)
        log_record.setdefault("name", record.name)
        log_record.setdefault("message", record.getMessage())
        log_record.setdefault(
            "module", getattr(record, "module", record.name.split(".")[-1])
        )
        log_record.setdefault("function", record.funcName)
        log_record.setdefault("line", record.lineno)
        cid = get_correlation_id()
        if cid:
            log_record.setdefault("correlation_id", cid)
        if record.exc_info:
            log_record["exception"] = self.formatException(record.exc_info)


class QualiaLoggingFilter(logging.Filter):
    """Filtro personalizado para deduplicação e controle de módulos.

    Parameters
    ----------
    name
        Nome opcional do filtro.
    suppress_patterns
        Lista de substrings para suprimir mensagens quando presentes.
    dedup_interval
        Intervalo em segundos para supressão de mensagens idênticas.
        O padrão é ``5.0`` segundos e pode ser alterado pela variável de
        ambiente ``QUALIA_LOG_DEDUP_INTERVAL``.
    allowed_modules
        Conjunto de módulos permitidos para emissão de logs.
    module_levels
        Mapeamento entre prefixos de módulo e níveis mínimos exigidos.
    """

    def __init__(
        self,
        name: str = "",
        suppress_patterns: Optional[list] = None,
        dedup_interval: float = 5.0,
        allowed_modules: Optional[List[str]] = None,
        module_levels: Optional[Dict[str, str]] = None,
    ):
        super().__init__(name)
        self.suppress_patterns = suppress_patterns or []
        self.dedup_interval = dedup_interval
        self.last_message: Optional[str] = None
        self.last_logger: Optional[str] = None
        self.last_time: float = 0.0
        self.allowed_modules = allowed_modules or []
        self.module_levels = {k: v.upper() for k, v in (module_levels or {}).items()}

    def filter(self, record: logging.LogRecord) -> bool:
        """Determina se o ``record`` deve ser emitido.

        Este método suprime mensagens duplicadas dentro de ``dedup_interval`` e
        filtra logs com base em ``suppress_patterns`` ou ``allowed_modules``.
        """
        # Suprimir logs desnecessários baseado em padrões ou duplicados
        try:
            message = record.getMessage()
        except Exception as exc:  # pragma: no cover - defensive
            logging.getLogger("qualia_trading").error("Erro ao formatar log: %s", exc)
            return False

        trace_id = getattr(record, "trace_id", None) or get_current_trace_id()
        if trace_id:
            record.trace_id = trace_id
            if f"trace_id={trace_id}" not in message:
                message = f"[trace_id={trace_id}] {message}"
                record.msg = message
                record.args = ()

        now = time.monotonic()
        if (
            message == self.last_message
            and record.name == self.last_logger
            and now - self.last_time < self.dedup_interval
        ):
            return False

        for pattern in self.suppress_patterns:
            if pattern in message:
                return False

        if self.allowed_modules and not any(
            record.name.startswith(mod) for mod in self.allowed_modules
        ):
            return False

        for mod, level_name in self.module_levels.items():
            if record.name.startswith(mod):
                min_level = getattr(logging, level_name, logging.INFO)
                return record.levelno >= min_level

        self.last_message = message
        self.last_logger = record.name
        self.last_time = now

        # Filtrar logs muito verbosos do Qiskit
        if record.name.startswith("qiskit"):
            # Manter apenas logs WARNING e acima do Qiskit
            return record.levelno >= logging.WARNING

        return True


def resolve_log_level(
    cli_level: str,
    config_level: Optional[str] = None,
    default: str = "INFO",
) -> tuple[str, int]:
    """Resolve o nível de log a ser utilizado.

    A prioridade é dada ao nível fornecido via linha de comando quando
    diferente do ``default``. Caso contrário, utiliza-se ``config_level``
    se disponível. O retorno inclui o nome do nível escolhido e seu valor
    numérico correspondente em ``logging``.
    """

    level_name = default.upper()
    if config_level:
        level_name = config_level.upper()
    if cli_level and cli_level.upper() != default.upper():
        level_name = cli_level.upper()

    numeric = getattr(logging, level_name, logging.INFO)
    return level_name, numeric


def apply_log_level(level: int, loggers: list[logging.Logger]) -> None:
    """Aplica ``level`` a cada logger e seus handlers sem repetições."""

    updated: set[logging.Handler] = set()
    for log in loggers:
        log.setLevel(level)
        for handler in log.handlers:
            if handler not in updated:
                handler.setLevel(level)
                updated.add(handler)


def logger_throttle(
    log_method: Callable[..., None], interval: float = 30.0
) -> Callable[..., None]:
    """Return a throttled logging function.

    Parameters
    ----------
    log_method
        Logging method like ``logger.info``.
    interval
        Minimum seconds between two log emissions.
    """

    last_call = 0.0

    def wrapper(*args: Any, **kwargs: Any) -> None:
        nonlocal last_call
        now = time.monotonic()
        if now - last_call >= interval:
            last_call = now
            log_method(*args, **kwargs)

    return wrapper


def configure_qualia_logging(
    log_level: str = "DEBUG",
    log_file: Optional[Path] = None,
    debug2_file: Optional[Path] = None,
    circuits_file: Optional[Path] = None,
    enable_console: bool = True,
    enable_structured: bool = True,
    dedup_interval: float = 5.0,
    module_levels: Optional[Dict[str, str]] = None,
    universe_debug: bool = False,
    compact_console: bool = False,
    allowed_modules: Optional[List[str]] = None,
    config_file: Optional[Path] = None,
) -> None:
    """Configura o sistema de logging para QUALIA.

    A função deve ser chamada uma única vez no início da aplicação. Caso o
    logger raiz já possua handlers configurados, novas chamadas são
    silenciosamente ignoradas.

    O nível padrão é ``INFO`` e mensagens duplicadas no intervalo definido por
    ``dedup_interval`` (padrão ``5.0`` segundos) são suprimidas pelo filtro
    ``QualiaLoggingFilter``.

    Args:
        log_level: Nível de logging (DEBUG, INFO, WARNING, ERROR)
        log_file: Caminho para arquivo de log (opcional)
        debug2_file: Arquivo específico para logs em ``DEBUG2``
        circuits_file: Arquivo opcional para logs detalhados de circuitos
        enable_console: Se deve habilitar saída no console
        enable_structured: Se deve usar formatação estruturada
        dedup_interval: Janela em segundos para supressão de mensagens idênticas
            (pode ser definido via ``QUALIA_LOG_DEDUP_INTERVAL``)
        module_levels: Dicionário com níveis específicos por módulo
        universe_debug: Se deve forçar DEBUG para o módulo universe
        compact_console: Se deve usar formato reduzido no console
        allowed_modules: Lista de módulos permitidos para emissão de logs
        config_file: Caminho para arquivo JSON com configuração de logging
    """
    from ..utils.logger import UTCFormatter

    root_logger = logging.getLogger()

    # Se já houver handlers configurados, assume que o logging já foi inicializado
    if root_logger.handlers:
        return

    # Marcar configuração básica no root logger para evitar duplicação futura
    root_logger.addHandler(logging.NullHandler())

    if config_file:
        try:
            with open(config_file) as f:
                cfg_data = json.load(f)
                log_level = cfg_data.get("log_level", log_level)
                module_levels = {
                    **(cfg_data.get("module_levels", {})),
                    **(module_levels or {}),
                }
                allowed_modules = cfg_data.get("allowed_modules", allowed_modules)
                dedup_interval = float(cfg_data.get("dedup_interval", dedup_interval))
        except (OSError, json.JSONDecodeError, ValueError):
            logger.error("Falha ao carregar configuração de logging de %s", config_file)

    # Configurar nível base
    log_level_numeric = getattr(logging, log_level.upper(), logging.INFO)

    module_levels = module_levels or {}

    def _level_value(name: str) -> int:
        return getattr(logging, name.upper(), logging.INFO)

    from ..config.settings import get_env

    env_levels = get_env("QUALIA_MODULE_LEVELS", None)
    if env_levels:
        try:
            env_dict = json.loads(env_levels)
            if isinstance(env_dict, dict):
                module_levels = {**env_dict, **module_levels}
        except json.JSONDecodeError:
            logger.error("QUALIA_MODULE_LEVELS inválido. Esperado JSON.")

    dedup_env = get_env("QUALIA_LOG_DEDUP_INTERVAL", None, warn=False)
    if dedup_env is not None:
        try:
            dedup_interval = float(dedup_env)
        except ValueError:
            logger.error("QUALIA_LOG_DEDUP_INTERVAL inválido. Esperado número.")

    module_levels.setdefault("src.qualia.core.encoders", "INFO")
    if universe_debug:
        module_levels.setdefault("src.qualia.core.universe", "DEBUG")

    min_handler_level = min(
        [log_level_numeric] + [_level_value(lv) for lv in module_levels.values()]
    )

    # Configurar loggers QUALIA
    qualia_loggers = [
        "qualia_trading",
        "qualia.core",
        "qualia.memory",
        "qualia.strategies",
        "qualia.market",
        "qualia.metacognition",
        "qualia.adaptive_evolution",
        "qualia.risk_management",
        "trading_system",
        "src.qualia",
    ]

    # Padrões de supressão para logs desnecessários
    suppress_patterns = [
        "Pass:",  # Logs detalhados do Qiskit transpiler
        "Total Transpile Time",  # Já temos logs próprios de timing
        "base_tasks.execute",  # Logs muito verbosos
    ]

    # Criar filtro personalizado
    qualia_filter = QualiaLoggingFilter(
        suppress_patterns=suppress_patterns,
        dedup_interval=dedup_interval,
        allowed_modules=allowed_modules,
        module_levels=module_levels,
    )

    # Configurar handler de console se habilitado
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(min_handler_level)
        console_handler.addFilter(qualia_filter)

        if enable_structured:
            console_handler.setFormatter(ColoredJSONFormatter(compact_console))
        else:
            fmt = (
                "%(message)s"
                if compact_console
                else "%(asctime)s - %(name)s - [%(levelname)s] - %(funcName)s:%(lineno)d - %(message)s"
            )
            console_handler.setFormatter(UTCFormatter(fmt))

    # Configurar handler de arquivo se especificado
    if log_file:
        log_file.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=10 * 1024 * 1024, backupCount=5, encoding="utf-8"
        )
        file_handler.setLevel(min_handler_level)
        file_handler.addFilter(qualia_filter)
        # Manter o formatador JSON estruturado e limpo para arquivos
        file_handler.setFormatter(StructuredFormatter())

    if debug2_file:
        debug2_file.parent.mkdir(parents=True, exist_ok=True)
        debug2_handler = logging.FileHandler(debug2_file)
        debug2_handler.setLevel(logging.DEBUG)
        debug2_handler.addFilter(qualia_filter)
        debug2_handler.setFormatter(StructuredFormatter())

    if circuits_file:
        circuits_file.parent.mkdir(parents=True, exist_ok=True)
        circuits_handler = logging.FileHandler(circuits_file)
        circuits_handler.setLevel(logging.DEBUG)
        circuits_handler.addFilter(qualia_filter)
        circuits_handler.setFormatter(StructuredFormatter())

    # Configurar cada logger QUALIA
    for logger_name in set(qualia_loggers + list(module_levels.keys())):
        log = logging.getLogger(logger_name)
        level_name = module_levels.get(logger_name, log_level).upper()
        level = getattr(logging, level_name, log_level_numeric)
        log.setLevel(level)
        log.propagate = False  # Evitar propagação para root logger

        # Limpar handlers existentes
        for handler in log.handlers[:]:
            log.removeHandler(handler)

        # Adicionar handlers configurados
        if enable_console:
            log.addHandler(console_handler)
        if log_file:
            log.addHandler(file_handler)
        if debug2_file:
            log.addHandler(debug2_handler)

    if circuits_file:
        circuits_logger = logging.getLogger("qualia.circuits")
        circuits_logger.setLevel(logging.DEBUG)
        circuits_logger.propagate = False
        for handler in circuits_logger.handlers[:]:
            circuits_logger.removeHandler(handler)
        circuits_logger.addHandler(circuits_handler)

    # Configurar loggers externos para serem menos verbosos

    for logger_name, level in EXTERNAL_LOGGERS.items():
        ext_logger = logging.getLogger(logger_name)
        # ext_logger.setLevel(level)
        ext_logger.propagate = False

    # Log de confirmação
    test_logger = logging.getLogger("qualia_trading")
    test_logger.info("Sistema de logging QUALIA configurado com sucesso")
    test_logger.info(
        f"Nível: {log_level}, Console: {enable_console}, Estruturado: {enable_structured}"
    )


def init_logging(
    log_level: str = "DEBUG",
    log_file: Optional[Path] = None,
    debug2_file: Optional[Path] = None,
    circuits_file: Optional[Path] = None,
    enable_console: bool = True,
    enable_structured: bool = True,
    module_levels: Optional[Dict[str, str]] = None,
    universe_debug: bool = False,
    compact_console: bool = False,
    allowed_modules: Optional[List[str]] = None,
    config_file: Optional[Path] = None,
) -> None:
    """Initialize QUALIA logging only if not already configured.

    Emits a warning when called multiple times without clearing existing
    handlers. This preserves idempotent behavior while informing the caller
    that the configuration step has been skipped.

    Parameters
    ----------
    log_level
        Nível base do logging.
    log_file
        Caminho opcional para o arquivo de log.
    debug2_file
        Arquivo específico para mensagens ``DEBUG2``.
    circuits_file
        Arquivo opcional para logs detalhados de circuitos.
    enable_console
        Se ``True`` habilita saída no console.
    enable_structured
        Define uso do ``StructuredFormatter``.
    module_levels
        Níveis específicos por módulo/prefixo.
    universe_debug
        Força DEBUG para o módulo universe.
    compact_console
        Usa formato de console reduzido.
    allowed_modules
        Lista de módulos permitidos para saída de log.
    config_file
        Caminho para arquivo JSON de configuração de logging.
    """

    root_logger = logging.getLogger()
    if root_logger.handlers:
        root_logger.warning(
            "init_logging chamado, mas os handlers já estão configurados; "
            "ignorando nova chamada"
        )
        return

    configure_qualia_logging(
        log_level=log_level,
        log_file=log_file,
        debug2_file=debug2_file,
        circuits_file=circuits_file,
        enable_console=enable_console,
        enable_structured=enable_structured,
        module_levels=module_levels,
        universe_debug=universe_debug,
        compact_console=compact_console,
        allowed_modules=allowed_modules,
        config_file=config_file,
    )


def get_qualia_logger(name: str) -> logging.Logger:
    """
    Obtém um logger QUALIA pré-configurado.

    Args:
        name: Nome do logger

    Returns:
        Logger configurado
    """
    return logging.getLogger(f"qualia.{name}")


# Configuração padrão para importação
def setup_default_logging() -> None:
    """Configura logging padrão para QUALIA."""
    configure_qualia_logging(
        log_level="DEBUG", enable_console=True, enable_structured=True
    )


# A configuração deve ser chamada explicitamente apenas uma vez na inicialização

__all__ = [
    "StructuredFormatter",
    "QualiaLoggingFilter",
    "resolve_log_level",
    "apply_log_level",
    "logger_throttle",
    "configure_qualia_logging",
    "init_logging",
    "get_qualia_logger",
    "setup_default_logging",
    "EXTERNAL_LOGGERS",
]
