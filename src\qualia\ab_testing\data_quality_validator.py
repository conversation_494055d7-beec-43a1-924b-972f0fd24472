"""
QUALIA Data Quality Validator - D-07.3 Implementation

Módulo para validar qualidade de dados comparando feeds de preço, timing de execução,
fill rates e impacto de latência entre simulador e trading ao vivo.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple, Any
import logging
import asyncio

from ..utils.logging_config import get_qualia_logger

logger = get_qualia_logger(__name__)


@dataclass
class PriceFeedComparison:
    """Comparação entre feeds de preço."""
    
    symbol: str
    timestamp: datetime
    
    # Preços
    simulator_price: float
    live_price: float
    price_difference: float
    price_difference_pct: float
    
    # Timing
    simulator_timestamp: datetime
    live_timestamp: datetime
    timestamp_lag_ms: float
    
    # Volume
    simulator_volume: Optional[float] = None
    live_volume: Optional[float] = None
    volume_difference_pct: Optional[float] = None


@dataclass
class ExecutionComparison:
    """Comparação de execução de ordens."""

    order_id: str
    symbol: str
    side: str
    quantity: float
    expected_price: float
    order_timestamp: datetime

    # Timing (com valores padrão)
    simulator_fill_timestamp: Optional[datetime] = None
    live_fill_timestamp: Optional[datetime] = None

    # Preços (com valores padrão)
    simulator_fill_price: Optional[float] = None
    live_fill_price: Optional[float] = None

    # Slippage (com valores padrão)
    simulator_slippage: Optional[float] = None
    live_slippage: Optional[float] = None
    slippage_difference: Optional[float] = None

    # Fill rates (com valores padrão)
    simulator_filled: bool = False
    live_filled: bool = False
    simulator_partial_fill_pct: float = 0.0
    live_partial_fill_pct: float = 0.0

    # Latência (com valores padrão)
    simulator_latency_ms: Optional[float] = None
    live_latency_ms: Optional[float] = None
    latency_difference_ms: Optional[float] = None


@dataclass
class DataQualityMetrics:
    """Métricas de qualidade de dados."""
    
    # Identificação
    session_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    
    # Price feed quality
    total_price_comparisons: int = 0
    avg_price_difference_pct: float = 0.0
    max_price_difference_pct: float = 0.0
    price_correlation: float = 0.0
    avg_timestamp_lag_ms: float = 0.0
    
    # Execution quality
    total_executions: int = 0
    simulator_fill_rate: float = 0.0
    live_fill_rate: float = 0.0
    avg_slippage_difference: float = 0.0
    avg_latency_difference_ms: float = 0.0
    
    # Volume quality
    volume_correlation: float = 0.0
    avg_volume_difference_pct: float = 0.0
    
    # Overall scores (0-1, onde 1 = perfeito)
    price_accuracy_score: float = 0.0
    execution_accuracy_score: float = 0.0
    timing_accuracy_score: float = 0.0
    overall_quality_score: float = 0.0
    
    # Issues identificados
    quality_issues: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


@dataclass
class DataQualityReport:
    """Relatório completo de qualidade de dados."""
    
    metrics: DataQualityMetrics
    price_comparisons: List[PriceFeedComparison] = field(default_factory=list)
    execution_comparisons: List[ExecutionComparison] = field(default_factory=list)
    
    # Análise temporal
    quality_over_time: List[Tuple[datetime, float]] = field(default_factory=list)
    
    # Recomendações
    recommendations: List[str] = field(default_factory=list)
    calibration_adjustments: Dict[str, Any] = field(default_factory=dict)


class DataQualityValidator:
    """Validador de qualidade de dados para A/B testing."""
    
    def __init__(self):
        self.metrics: Optional[DataQualityMetrics] = None
        self.price_comparisons: List[PriceFeedComparison] = []
        self.execution_comparisons: List[ExecutionComparison] = []
        
        # Thresholds de qualidade
        self.price_difference_threshold_pct = 0.1  # 0.1%
        self.timestamp_lag_threshold_ms = 1000  # 1 segundo
        self.slippage_difference_threshold = 0.05  # 0.05%
        self.latency_threshold_ms = 500  # 500ms
        
        # Configurações
        self.validation_interval_seconds = 30
        self.max_comparisons_stored = 10000
        
        logger.info("🔍 DataQualityValidator inicializado")
    
    def initialize_validation(self, session_id: str) -> DataQualityMetrics:
        """Inicializa validação de qualidade de dados."""
        self.metrics = DataQualityMetrics(
            session_id=session_id,
            start_time=datetime.now()
        )
        
        self.price_comparisons.clear()
        self.execution_comparisons.clear()
        
        logger.info(f"🔍 Validação de qualidade inicializada: {session_id}")
        return self.metrics
    
    def add_price_comparison(self, comparison: PriceFeedComparison) -> None:
        """Adiciona comparação de preço."""
        if not self.metrics:
            logger.warning("⚠️ Métricas não inicializadas")
            return
        
        self.price_comparisons.append(comparison)
        
        # Limitar armazenamento
        if len(self.price_comparisons) > self.max_comparisons_stored:
            self.price_comparisons = self.price_comparisons[-self.max_comparisons_stored:]
        
        # Atualizar métricas
        self._update_price_metrics()
        
        # Verificar qualidade
        self._check_price_quality(comparison)
        
        logger.debug(f"📊 Comparação de preço adicionada: {comparison.symbol}")
    
    def add_execution_comparison(self, comparison: ExecutionComparison) -> None:
        """Adiciona comparação de execução."""
        if not self.metrics:
            logger.warning("⚠️ Métricas não inicializadas")
            return
        
        self.execution_comparisons.append(comparison)
        
        # Limitar armazenamento
        if len(self.execution_comparisons) > self.max_comparisons_stored:
            self.execution_comparisons = self.execution_comparisons[-self.max_comparisons_stored:]
        
        # Atualizar métricas
        self._update_execution_metrics()
        
        # Verificar qualidade
        self._check_execution_quality(comparison)
        
        logger.debug(f"⚡ Comparação de execução adicionada: {comparison.order_id}")
    
    def calculate_quality_scores(self) -> Dict[str, float]:
        """Calcula scores de qualidade."""
        if not self.metrics:
            return {}
        
        # Price accuracy score
        price_score = self._calculate_price_accuracy_score()
        
        # Execution accuracy score
        execution_score = self._calculate_execution_accuracy_score()
        
        # Timing accuracy score
        timing_score = self._calculate_timing_accuracy_score()
        
        # Overall score (média ponderada)
        overall_score = (price_score * 0.4 + execution_score * 0.4 + timing_score * 0.2)
        
        # Atualizar métricas
        self.metrics.price_accuracy_score = price_score
        self.metrics.execution_accuracy_score = execution_score
        self.metrics.timing_accuracy_score = timing_score
        self.metrics.overall_quality_score = overall_score
        
        return {
            "price_accuracy": price_score,
            "execution_accuracy": execution_score,
            "timing_accuracy": timing_score,
            "overall_quality": overall_score
        }
    
    def generate_report(self) -> Optional[DataQualityReport]:
        """Gera relatório completo de qualidade."""
        if not self.metrics:
            return None
        
        logger.info("📋 Gerando relatório de qualidade de dados...")
        
        # Calcular scores finais
        scores = self.calculate_quality_scores()
        
        # Gerar recomendações
        recommendations = self._generate_recommendations()
        
        # Calcular qualidade ao longo do tempo
        quality_over_time = self._calculate_quality_over_time()
        
        # Sugestões de calibração
        calibration_adjustments = self._generate_calibration_adjustments()
        
        self.metrics.end_time = datetime.now()
        
        report = DataQualityReport(
            metrics=self.metrics,
            price_comparisons=self.price_comparisons.copy(),
            execution_comparisons=self.execution_comparisons.copy(),
            quality_over_time=quality_over_time,
            recommendations=recommendations,
            calibration_adjustments=calibration_adjustments
        )
        
        logger.info(f"✅ Relatório gerado - Qualidade geral: {scores.get('overall_quality', 0):.3f}")
        return report
    
    def _update_price_metrics(self) -> None:
        """Atualiza métricas de preço."""
        if not self.price_comparisons or not self.metrics:
            return
        
        # Diferenças de preço
        price_diffs = [abs(c.price_difference_pct) for c in self.price_comparisons]
        self.metrics.avg_price_difference_pct = np.mean(price_diffs)
        self.metrics.max_price_difference_pct = np.max(price_diffs)
        
        # Correlação de preços
        sim_prices = [c.simulator_price for c in self.price_comparisons]
        live_prices = [c.live_price for c in self.price_comparisons]
        if len(sim_prices) > 1:
            try:
                correlation_matrix = np.corrcoef(sim_prices, live_prices)
                correlation = correlation_matrix[0, 1]
                # Verificar se é NaN (pode acontecer com dados constantes)
                if np.isnan(correlation):
                    # Se os dados são idênticos, correlação é 1.0
                    if np.allclose(sim_prices, live_prices):
                        correlation = 1.0
                    else:
                        correlation = 0.0
                self.metrics.price_correlation = correlation
            except (ValueError, RuntimeWarning):
                # Em caso de erro, assumir correlação baixa
                self.metrics.price_correlation = 0.0
        
        # Lag de timestamp
        timestamp_lags = [c.timestamp_lag_ms for c in self.price_comparisons]
        self.metrics.avg_timestamp_lag_ms = np.mean(timestamp_lags)
        
        # Volume correlation (se disponível)
        volume_comparisons = [c for c in self.price_comparisons if c.simulator_volume and c.live_volume]
        if volume_comparisons:
            sim_volumes = [c.simulator_volume for c in volume_comparisons]
            live_volumes = [c.live_volume for c in volume_comparisons]
            if len(sim_volumes) > 1:
                try:
                    correlation_matrix = np.corrcoef(sim_volumes, live_volumes)
                    correlation = correlation_matrix[0, 1]
                    # Verificar se é NaN
                    if np.isnan(correlation):
                        if np.allclose(sim_volumes, live_volumes):
                            correlation = 1.0
                        else:
                            correlation = 0.0
                    self.metrics.volume_correlation = correlation
                except (ValueError, RuntimeWarning):
                    self.metrics.volume_correlation = 0.0
            
            volume_diffs = [abs(c.volume_difference_pct) for c in volume_comparisons if c.volume_difference_pct]
            if volume_diffs:
                self.metrics.avg_volume_difference_pct = np.mean(volume_diffs)
        
        self.metrics.total_price_comparisons = len(self.price_comparisons)
    
    def _update_execution_metrics(self) -> None:
        """Atualiza métricas de execução."""
        if not self.execution_comparisons or not self.metrics:
            return
        
        # Fill rates
        sim_fills = sum(1 for c in self.execution_comparisons if c.simulator_filled)
        live_fills = sum(1 for c in self.execution_comparisons if c.live_filled)
        total = len(self.execution_comparisons)
        
        self.metrics.simulator_fill_rate = sim_fills / total if total > 0 else 0
        self.metrics.live_fill_rate = live_fills / total if total > 0 else 0
        
        # Slippage differences
        slippage_diffs = [abs(c.slippage_difference) for c in self.execution_comparisons 
                         if c.slippage_difference is not None]
        if slippage_diffs:
            self.metrics.avg_slippage_difference = np.mean(slippage_diffs)
        
        # Latency differences
        latency_diffs = [abs(c.latency_difference_ms) for c in self.execution_comparisons 
                        if c.latency_difference_ms is not None]
        if latency_diffs:
            self.metrics.avg_latency_difference_ms = np.mean(latency_diffs)
        
        self.metrics.total_executions = total
    
    def _check_price_quality(self, comparison: PriceFeedComparison) -> None:
        """Verifica qualidade de uma comparação de preço."""
        if not self.metrics:
            return
        
        # Verificar diferença de preço
        if abs(comparison.price_difference_pct) > self.price_difference_threshold_pct:
            issue = f"Grande diferença de preço em {comparison.symbol}: {comparison.price_difference_pct:.3f}%"
            if issue not in self.metrics.quality_issues:
                self.metrics.quality_issues.append(issue)
                logger.warning(f"⚠️ {issue}")
        
        # Verificar lag de timestamp
        if comparison.timestamp_lag_ms > self.timestamp_lag_threshold_ms:
            warning = f"Alto lag de timestamp em {comparison.symbol}: {comparison.timestamp_lag_ms:.0f}ms"
            if warning not in self.metrics.warnings:
                self.metrics.warnings.append(warning)
                logger.warning(f"⚠️ {warning}")
    
    def _check_execution_quality(self, comparison: ExecutionComparison) -> None:
        """Verifica qualidade de uma comparação de execução."""
        if not self.metrics:
            return
        
        # Verificar diferença de slippage
        if (comparison.slippage_difference is not None and 
            abs(comparison.slippage_difference) > self.slippage_difference_threshold):
            issue = f"Grande diferença de slippage em {comparison.symbol}: {comparison.slippage_difference:.3f}%"
            if issue not in self.metrics.quality_issues:
                self.metrics.quality_issues.append(issue)
                logger.warning(f"⚠️ {issue}")
        
        # Verificar diferença de latência
        if (comparison.latency_difference_ms is not None and 
            abs(comparison.latency_difference_ms) > self.latency_threshold_ms):
            warning = f"Alta diferença de latência em {comparison.symbol}: {comparison.latency_difference_ms:.0f}ms"
            if warning not in self.metrics.warnings:
                self.metrics.warnings.append(warning)
                logger.warning(f"⚠️ {warning}")
    
    def _calculate_price_accuracy_score(self) -> float:
        """Calcula score de precisão de preços (0-1)."""
        if not self.price_comparisons:
            return 0.0
        
        # Score baseado na diferença média de preços
        avg_diff = self.metrics.avg_price_difference_pct if self.metrics else 0
        price_score = max(0, 1 - (avg_diff / self.price_difference_threshold_pct))
        
        # Score baseado na correlação
        correlation_score = max(0, self.metrics.price_correlation if self.metrics else 0)
        
        # Score combinado
        return (price_score * 0.7 + correlation_score * 0.3)
    
    def _calculate_execution_accuracy_score(self) -> float:
        """Calcula score de precisão de execução (0-1)."""
        if not self.execution_comparisons or not self.metrics:
            return 0.0
        
        # Score baseado na diferença de fill rates
        fill_rate_diff = abs(self.metrics.simulator_fill_rate - self.metrics.live_fill_rate)
        fill_rate_score = max(0, 1 - fill_rate_diff)
        
        # Score baseado na diferença de slippage
        slippage_score = max(0, 1 - (self.metrics.avg_slippage_difference / self.slippage_difference_threshold))
        
        # Score combinado
        return (fill_rate_score * 0.6 + slippage_score * 0.4)
    
    def _calculate_timing_accuracy_score(self) -> float:
        """Calcula score de precisão de timing (0-1)."""
        if not self.metrics:
            return 0.0
        
        # Score baseado no lag de timestamp
        timestamp_score = max(0, 1 - (self.metrics.avg_timestamp_lag_ms / self.timestamp_lag_threshold_ms))
        
        # Score baseado na diferença de latência
        latency_score = 1.0
        if self.metrics.avg_latency_difference_ms > 0:
            latency_score = max(0, 1 - (self.metrics.avg_latency_difference_ms / self.latency_threshold_ms))
        
        # Score combinado
        return (timestamp_score * 0.6 + latency_score * 0.4)
    
    def _calculate_quality_over_time(self) -> List[Tuple[datetime, float]]:
        """Calcula qualidade ao longo do tempo."""
        # Implementação simplificada - pode ser expandida
        if not self.metrics:
            return []
        
        # Retornar pontos de qualidade ao longo do tempo
        return [(self.metrics.start_time, self.metrics.overall_quality_score)]
    
    def _generate_recommendations(self) -> List[str]:
        """Gera recomendações baseadas na análise."""
        recommendations = []
        
        if not self.metrics:
            return recommendations
        
        # Recomendações baseadas em price accuracy
        if self.metrics.price_accuracy_score < 0.8:
            recommendations.append("Calibrar feeds de preço - diferenças significativas detectadas")
        
        # Recomendações baseadas em execution accuracy
        if self.metrics.execution_accuracy_score < 0.8:
            recommendations.append("Ajustar modelo de slippage e fill rates no simulador")
        
        # Recomendações baseadas em timing
        if self.metrics.timing_accuracy_score < 0.8:
            recommendations.append("Otimizar sincronização de timestamps e reduzir latência")
        
        # Recomendações específicas
        if self.metrics.avg_price_difference_pct > 0.05:
            recommendations.append("Investigar fonte de diferenças de preço - possível problema de feed")
        
        if abs(self.metrics.simulator_fill_rate - self.metrics.live_fill_rate) > 0.1:
            recommendations.append("Ajustar modelo de execução - fill rates muito diferentes")
        
        return recommendations
    
    def _generate_calibration_adjustments(self) -> Dict[str, Any]:
        """Gera sugestões de ajustes de calibração."""
        adjustments = {}
        
        if not self.metrics:
            return adjustments
        
        # Ajustes de slippage
        if self.metrics.avg_slippage_difference > 0.01:
            adjustments["slippage_adjustment"] = {
                "current_avg_difference": self.metrics.avg_slippage_difference,
                "suggested_multiplier": 1 + self.metrics.avg_slippage_difference
            }
        
        # Ajustes de latência
        if self.metrics.avg_latency_difference_ms > 50:
            adjustments["latency_adjustment"] = {
                "current_avg_difference_ms": self.metrics.avg_latency_difference_ms,
                "suggested_additional_delay_ms": self.metrics.avg_latency_difference_ms * 0.5
            }
        
        # Ajustes de fill rate
        fill_rate_diff = self.metrics.simulator_fill_rate - self.metrics.live_fill_rate
        if abs(fill_rate_diff) > 0.05:
            adjustments["fill_rate_adjustment"] = {
                "current_difference": fill_rate_diff,
                "suggested_fill_probability_adjustment": -fill_rate_diff * 0.5
            }
        
        return adjustments
