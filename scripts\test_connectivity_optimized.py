#!/usr/bin/env python3
"""
Script para testar conectividade otimizada com KuCoin
"""

import sys
import os
import asyncio
import time
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, 'src')

from qualia.market.kucoin_integration import KucoinIntegration
from qualia.config.settings import get_env

async def test_optimized_connectivity():
    """Testa conectividade com configurações otimizadas"""
    print("🔧 TESTANDO CONECTIVIDADE OTIMIZADA KUCOIN")
    print("=" * 60)
    
    # Configurar variáveis de ambiente otimizadas
    os.environ['TICKER_TIMEOUT'] = '30'
    os.environ['OHLCV_TIMEOUT'] = '90'
    os.environ['RATE_LIMIT'] = '4.0'
    os.environ['TICKER_CACHE_TTL'] = '15'
    os.environ['OHLCV_CACHE_TTL'] = '120'
    
    # Criar integração com configurações otimizadas
    integration = KucoinIntegration(
        api_key=get_env("KUCOIN_API_KEY"),
        api_secret=get_env("KUCOIN_SECRET_KEY"),
        password=get_env("KUCOIN_PASSPHRASE"),
        ticker_timeout=30.0,  # 30 segundos timeout
        ohlcv_timeout=90.0,  # 90 segundos timeout
        fail_threshold=2,  # Circuit breaker após 2 falhas
        recovery_timeout=30.0,  # 30 segundos recovery
    )
    
    try:
        print("📡 Inicializando conexão...")
        await integration.initialize_connection()
        print("✅ Conexão estabelecida")
        
        # Teste 1: Ticker
        print("\n🎯 Teste 1: Fetch Ticker")
        start_time = time.time()
        ticker = await integration.fetch_ticker('BTC/USDT')
        duration = time.time() - start_time
        
        if ticker:
            print(f"✅ Ticker obtido em {duration:.2f}s")
            print(f"   Preço: ${ticker['last']}")
        else:
            print(f"❌ Ticker falhou após {duration:.2f}s")
        
        # Aguardar rate limit
        print("⏳ Aguardando rate limit (4s)...")
        await asyncio.sleep(4.0)
        
        # Teste 2: OHLCV pequeno
        print("\n📊 Teste 2: OHLCV (10 candles)")
        start_time = time.time()
        try:
            ohlcv = await integration.fetch_ohlcv('BTC/USDT', '5m', limit=10)
            duration = time.time() - start_time
            
            if not ohlcv.empty:
                print(f"✅ OHLCV obtido em {duration:.2f}s")
                print(f"   Candles: {len(ohlcv)}")
                print(f"   Último preço: ${ohlcv.iloc[-1]['close']}")
            else:
                print(f"❌ OHLCV vazio após {duration:.2f}s")
        except Exception as e:
            duration = time.time() - start_time
            print(f"❌ OHLCV falhou após {duration:.2f}s: {e}")
        
        # Aguardar rate limit
        print("⏳ Aguardando rate limit (4s)...")
        await asyncio.sleep(4.0)
        
        # Teste 3: Cache
        print("\n💾 Teste 3: Cache de Ticker")
        start_time = time.time()
        ticker_cached = await integration.fetch_ticker('BTC/USDT')
        duration = time.time() - start_time
        
        if ticker_cached and duration < 1.0:
            print(f"✅ Cache funcionando ({duration:.3f}s)")
        elif ticker_cached:
            print(f"⚠️ Sem cache, nova requisição ({duration:.2f}s)")
        else:
            print("❌ Cache falhou")
            
    except Exception as e:
        print(f"❌ Erro na conexão: {e}")
        return False
    finally:
        await integration.close()
        print("\n🔌 Conexão fechada")
    
    print("\n" + "=" * 60)
    print("✅ TESTE DE CONECTIVIDADE CONCLUÍDO")
    return True

def main():
    """Função principal"""
    return asyncio.run(test_optimized_connectivity())

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 
