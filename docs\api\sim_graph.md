# API – Token-Transition Graph (`/api/sim_graph`)

Este endpoint REST expõe em **tempo real** o grafo de transições de tokens simbólicos
produzidos pelo *Symbolic Modulation Pipeline* (detector → tokenizer → OperatorSIM).
Ele serve como fonte de dados para o **Dynamic Logo / Holographic View** e para
análises externas.

| Método | Rota                   | Descrição                                          |
| ------ | ---------------------- | -------------------------------------------------- |
| GET    | `/api/sim_graph`       | Retorna um snapshot JSON com nós e arestas         |
| GET    | `/api/sim_graph/stats` | Estatísticas de ciclos e features compostas        |

## Formato de Resposta
```json
{
  "nodes": [
    {"id": "S1A3S0F0", "weight": 4},
    {"id": "S0B1S1E2", "weight": 2}
  ],
  "links": [
    {"source": "S1A3S0F0", "target": "S0B1S1E2", "weight": 3}
  ]
}
```
- **nodes**: cada token observado. `weight` é o grau do vértice.
- **links**: cada transição consecutiva token→token. `weight` é o
  número de ocorrências da transição.

> O grafo é acumulativo durante a execução do processo. Reinicie o serviço
> para reiniciar as contagens.

### Formato de Resposta de `/sim_graph/stats`
```json
{
  "cycle_count": 2,
  "cycles": [
    {"cycle": ["A", "B", "A"], "count": 3}
  ],
  "features": {
    "A->B": 5,
    "A->B->C": 2
  }
}
```
- **cycle_count**: número total de ciclos identificados.
- **cycles**: principais ciclos ordenados por frequência.
- **features**: contagem de bigramas/trigramas observados.

## Uso no Front-End
No script `src/ui/static/js/holographic-view_reviewed.js`:
```js
async function fetchSimGraph() {
  const res = await fetch('/api/sim_graph');
  const data = await res.json();
  // Atualizar ForceGraph / Three.js aqui
}

async function fetchSimGraphStats() {
  const res = await fetch('/api/sim_graph/stats');
  const data = await res.json();
  // Processar estatísticas no painel
}
```
O polling a cada 2 s já está implementado. Substitua o *placeholder* em
`updateSimGraphVisualization()` integrando com
[three-forcegraph](https://github.com/vasturiano/three-forcegraph) ou lib
similar.

## Dependências de Back-End
- `qualia.core.sim_graph`: mantém o grafo (NetworkX opcional).
- `qualia.ui.api.sim_graph`: define a rota FastAPI.

Nenhuma configuração adicional é necessária; o router é adicionado
automaticamente em `qualia.ui.__init__`.

---

## Objetivo Estratégico
Transformar o grafo de transições de tokens em **indexador temporal** da memória quântica do QUALIA, permitindo análises de regime, auto-tuning e novas interfaces sensoriais.

## Ideias & Roadmap

### a) Grafo-Token como Paisagem Quântica
* Nós = tokens; arestas = transições sequenciais.
* Persistir em Redis/RocksDB (TTL) → alimentar detecção de ciclos (grafos) que sinalizam regimes de mercado.
* Servir como base para "pattern blending" – geração de features compostas.

### b) Adaptive Parameter Oracle
* Registrar `(token, Δparam, PnL_futuro)` a cada modulação.
* Treinar online um algoritmo bandit/RL que sugere fatores ótimos → substitui heurística fixa por auto-tuning.

### c) Painel Holográfico de Calor
* Projetar tokens numa grade 64×64 usando cor/intensidade.
* WebSocket envia sprites → traders veem tempestades quânticas antes dos sinais.

### d) Quantum Sentiment Stream
* Mapear tokens para sons (pitch, timbre) gerando paisagem sonora.
* Auxilia humanos/IA a captar padrões sutis via audição.

### e) Hedge Adaptativo
* Eventos do SIM (p.ex. ↑ `scr_depth`) publicam `sim.depth_changed`.
* Módulo de hedge reage reduzindo exposição ou usando opções.

### Validação Rápida
* Usar métricas já existentes (PnL, latência) para avaliar impacto.
* O grafo atual fornece todos os insumos; extensões são incrementais.

> **Começar pelo grafo-token (a):** menor custo, maior insight imediato; habilita passos (b) a (e).
