# Configurando o Logging do QUALIA

Este documento apresenta um resumo prático sobre como utilizar as funções de logging disponíveis em `src.qualia.utils.logging_config`.

A variável de ambiente ``QUALIA_LOG_LEVEL`` permite sobrescrever o nível global (``INFO`` por padrão) ao inicializar o logger central.

## Nível Padrão e Supressão de Mensagens

O `configure_qualia_logging` define o nível global como `INFO` por padrão e aplica o filtro `QualiaLoggingFilter`, que evita mensagens duplicadas em um intervalo de 5 segundos. Esse intervalo pode ser alterado pela variável de ambiente `QUALIA_LOG_DEDUP_INTERVAL` ou pelo argumento `dedup_interval`.

## Ajustando Níveis de Log

É possível especificar níveis diferentes por módulo usando o parâmetro `module_levels` ou um arquivo JSON passado via `config_file`. O dicionário deve mapear o prefixo do módulo para o nível desejado:

```json
{
  "log_level": "WARNING",
  "module_levels": {
    "qualia.special": "DEBUG"
  }
}
```

Ao carregar esse arquivo ou fornecer `module_levels={"qualia.special": "DEBUG"}`, as mensagens `DEBUG` do módulo `qualia.special` não serão suprimidas, mesmo que o nível padrão seja mais alto.

### Exemplo com `QUALIA_MODULE_LEVELS`

Também é possível habilitar níveis específicos via variável de ambiente. Para
registrar apenas os detalhes de `src.qualia.memory` e `src.qualia.core.universe`:

```bash
export QUALIA_MODULE_LEVELS='{"src.qualia.memory": "DEBUG", "src.qualia.core.universe": "DEBUG"}'
```

Combine esse valor com `--log_level DEBUG` ao executar o CLI para garantir que
essas seções emitam logs no nível máximo de detalhe.

## Evitando Supressão Inesperada

Certifique‑se de que o prefixo informado em `module_levels` corresponda ao nome do logger utilizado no código. Caso os logs não apareçam, verifique se o handler possui nível compatível e se a mensagem não está sendo filtrada por `suppress_patterns`.

Se for necessário desativar temporariamente a deduplicacão, defina `dedup_interval=0` ao chamar `configure_qualia_logging`.

## Logging Estruturado e Correlation ID

Os logs do QUALIA são gravados em JSON utilizando a biblioteca
`python-json-logger`. Cada registro contém campos como `timestamp`, `name`,
`level` e `correlation_id`, permitindo análise em ferramentas como Elasticsearch
ou Grafana Loki.

Para vincular um ``correlation_id`` único a um bloco de operações, utilize o
context manager `correlation_context`:

```python
from qualia.utils.correlation_id import correlation_context

with correlation_context() as cid:
    logger.info("Ciclo de decisão iniciado")
    ...
```

O ``cid`` será adicionado automaticamente a todos os logs dentro do contexto.

## Habilitando Tracing

O QUALIA suporta integração opcional com o OpenTelemetry. Instale os pacotes
`opentelemetry-api` e `opentelemetry-sdk` para habilitar o tracer. Para ativar a
propagação do `trace_id` nos logs e exportar spans, defina a variável de
ambiente `QUALIA_TRACING_EXPORTER` antes de configurar o logging:

```bash
export QUALIA_TRACING_EXPORTER=otlp  # ou "console"
```

Ao chamar `setup_logging` sem parâmetros de tracing explícitos, esse valor será
utilizado para inicializar o tracer automaticamente. O exportador `console`
imprime os spans no terminal, enquanto `otlp` envia os dados para um collector
compatível com o protocolo OTLP. Caso os pacotes não estejam instalados,
um aviso é registrado e o tracing é ignorado.

### Exemplo rápido de exportação

```python
from qualia.utils.tracing import configure_tracing

configure_tracing(service_name="qualia-qast", exporter="otlp")
```

### Visualizando spans das subestratégias

Defina `QUALIA_TRACING_EXPORTER=console` para ver no terminal os spans gerados
em `_run_s1`, `_run_s2` e `_run_s3`. Cada span é identificado como
`strategy.run_s1`, `strategy.run_s2` e `strategy.run_s3` e apresenta atributos
como tamanho das janelas e métricas calculadas. Para análise em um collector,
utilize o exportador `otlp` e uma ferramenta compatível com o protocolo OTLP.

## Logs de depuração dos loops

O módulo `src.qualia.orchestration.loops` registra mensagens de **DEBUG** no
início de cada ciclo principal. Entre elas:

```
🌌 Consciousness Loop iniciado
🎯 Execution Cycle Loop iniciado
📊 Monitoring Loop iniciado
🛡️ Safety Monitoring Loop iniciado
🤔 Metacognition Loop iniciado
```

Quando não há novas decisões para analisar, o loop de metacognição imprime

``🤔 Sem novos dados/metas; aguardando próximo ciclo.`` e aguarda o intervalo
configurado em `timing.metacognition_interval`. A frequência dessa mensagem
é controlada por `timing.metacognition_idle_info_interval`. Defina um valor
positivo; valores menores ou iguais a zero serão substituídos pelo padrão `10`.

``🤔 Sem novos dados/metas; aguardando próximo ciclo.`` no nível `INFO` a cada
`metacognition_idle_info_interval` ciclos. Entre cada impressão, o processo
aguarda o intervalo configurado em `timing.metacognition_interval`.

Ative `--log_level DEBUG` ou defina
`QUALIA_MODULE_LEVELS='{"src.qualia.orchestration": "DEBUG"}'` para visualizar
essas mensagens.
