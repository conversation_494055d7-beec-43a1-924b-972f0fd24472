"""
QUALIA - Quantum-Aware Learning Intelligence Architecture
A quantum-inspired algorithmic trading system
"""

__version__ = "1.0.0"
__author__ = "QUALIA_QAST Team"
__description__ = "Quantum-Aware Algorithmic Trading System"

__all__ = ["QASTCore", "setup_logging", "get_trading_system", "market"]


def __getattr__(name: str):
    """Lazily import top-level objects to avoid heavy side effects during
    package import in tests."""

    if name == "QASTCore":
        from .core import QASTCore as _QASTCore

        return _QASTCore
    if name == "setup_logging":
        from .monitoring.logger import setup_logging as _setup_logging

        return _setup_logging
    if name == "get_trading_system":
        return get_trading_system
    if name == "qualia_trading_system":
        import importlib

        return importlib.import_module(".qualia_trading_system", __name__)
    if name == "market":
        import importlib

        return importlib.import_module(".market", __name__)
    raise AttributeError(f"module 'qualia' has no attribute {name}")


def get_trading_system():
    """Return a minimal QuantumTradingSystem implementation used in tests."""

    import numpy as np

    from .core.folding import apply_folding
    from .core.resonance import apply_resonance
    from .core.emergence import apply_emergence
    from .core.retrocausality import apply_retrocausality
    from .core.observer import observe_system
    from .core.noise import inject_noise

    class QuantumTradingSystem:
        def __init__(
            self,
            dimension: int = 2,
            alpha: float = 0.1,
            strength: float = 0.05,
            beta: float = 0.2,
            gamma: float = 0.3,
            noise_sigma: float = 0.0,
            noise_mode: str = "white",
        ) -> None:
            self.dimension = dimension
            self.alpha = alpha
            self.strength = strength
            self.beta = beta
            self.gamma = gamma
            self.noise_sigma = noise_sigma
            self.noise_mode = noise_mode
            self.state = np.eye(dimension)
            self.observer_matrix = np.eye(dimension)
            self._step = 0

        def forecast_future_state(self) -> np.ndarray:
            return self.state.copy()

        def evolve_system(self) -> np.ndarray:
            noisy_state = inject_noise(
                self.state,
                sigma=self.noise_sigma,
                mode=self.noise_mode,
                step=self._step,
            )
            future = self.forecast_future_state()
            self.state = apply_folding(noisy_state, alpha=self.alpha)
            self.state = apply_resonance(self.state, strength=self.strength)
            self.state = apply_emergence(self.state, beta=self.beta)
            self.state = apply_retrocausality(self.state, future, gamma=self.gamma)
            self.state = observe_system(self.state, self.observer_matrix)
            self._step += 1
            return self.state

    return QuantumTradingSystem
