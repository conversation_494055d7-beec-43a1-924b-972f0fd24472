# Alinhamento de Timeframe entre Trader e Encoders

Ao configurar o `QuantumMetricsCalculator` é importante que cada encoder utilize o mesmo timeframe ativo do trader. Encoders que recebem dados de outro timeframe podem gerar métricas inconsistentes.

Se um encoder possui `data_keys` ou parâmetro `timeframe` diferente do timeframe principal definido em `trading_primary_timeframe`, o QMC registra um aviso e ignora o encoder. Habilite `convert_mismatched_timeframes` caso queira que os `data_keys` sejam convertidos automaticamente para o timeframe principal (por exemplo, de `5m` para `1m`). Cada aviso é emitido apenas uma vez por encoder para evitar poluição de logs.

Exemplo de configuração correta:

```json
{
  "trading_primary_timeframe": "1m",
  "convert_mismatched_timeframes": true,
  "encoders": [
    {
      "id": "group1",
      "encoders": [
        {
          "name": "PM",
          "class": "PriceMomentumEncoder",
          "params": {"data_keys": ["BTC/USDT_1m_price_change"], "target_qubits": [0]}
        }
      ]
    }
  ]
}
```

Todos os encoders devem referenciar `1m` em seus `data_keys` ou habilitar `convert_mismatched_timeframes` para que a conversão ocorra de forma automática. Caso contrário, o QMC emitirá o aviso de desalinhamento e ignorará o encoder.
