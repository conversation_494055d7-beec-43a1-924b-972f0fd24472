#!/usr/bin/env python3
"""
Monitor QUALIA Signal Generation
Monitora o progresso do sistema e aguarda geração de sinais
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from pathlib import Path

async def monitor_system():
    """Monitora o sistema QUALIA em busca de sinais"""
    print("🔍 QUALIA Signal Generation Monitor")
    print("=" * 50)
    
    cache_dir = Path("data/cache")
    logs_dir = Path("logs")
    
    start_time = datetime.now()
    last_signal_check = datetime.now()
    
    while True:
        try:
            current_time = datetime.now()
            uptime = current_time - start_time
            
            print(f"\n⏰ {current_time.strftime('%H:%M:%S')} | Uptime: {uptime}")
            
            # 1. Verificar dados em cache
            cache_files = list(cache_dir.glob("*.json"))
            print(f"📊 Cache files: {len(cache_files)}")
            
            # Verificar qualidade dos dados
            total_candles = 0
            for cache_file in cache_files:
                try:
                    with open(cache_file, 'r') as f:
                        data = json.load(f)
                        if 'data' in data and isinstance(data['data'], list):
                            candles = len(data['data'])
                            total_candles += candles
                            if candles < 50:
                                print(f"⚠️ {cache_file.name}: apenas {candles} candles")
                except:
                    pass
            
            print(f"📈 Total candles em cache: {total_candles}")
            
            # 2. Verificar logs recentes
            recent_logs = []
            if logs_dir.exists():
                for log_file in logs_dir.glob("*.log"):
                    try:
                        stat = log_file.stat()
                        if datetime.fromtimestamp(stat.st_mtime) > current_time - timedelta(minutes=5):
                            recent_logs.append(log_file.name)
                    except:
                        pass
            
            if recent_logs:
                print(f"📝 Logs ativos: {', '.join(recent_logs)}")
            
            # 3. Verificar sinais (procurar por arquivos de sinal ou logs específicos)
            signal_indicators = [
                "SIGNAL GENERATED",
                "TRADE EXECUTED", 
                "POSITION OPENED",
                "Oracle Decision",
                "Holographic Signal"
            ]
            
            # Procurar por indicadores de sinal nos logs mais recentes
            signals_found = []
            if logs_dir.exists():
                for log_file in logs_dir.glob("*.log"):
                    try:
                        if log_file.stat().st_mtime > (current_time - timedelta(minutes=10)).timestamp():
                            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                                content = f.read()
                                for indicator in signal_indicators:
                                    if indicator.lower() in content.lower():
                                        signals_found.append(f"{indicator} in {log_file.name}")
                    except:
                        pass
            
            if signals_found:
                print("🚨 SINAIS DETECTADOS:")
                for signal in signals_found:
                    print(f"   ✅ {signal}")
            else:
                print("⏳ Aguardando geração de sinais...")
            
            # 4. Status do sistema
            if uptime.total_seconds() < 300:  # Primeiros 5 minutos
                print("🔥 Sistema em warm-up holográfico...")
            elif uptime.total_seconds() < 600:  # Primeiros 10 minutos
                print("🌀 Processando dados históricos...")
            elif total_candles > 1000:
                print("✅ Dados suficientes - Sistema deveria estar gerando sinais")
            else:
                print("⚠️ Dados insuficientes - Aguardando coleta")
            
            # 5. Recomendações
            if uptime.total_seconds() > 600 and not signals_found:
                print("\n💡 RECOMENDAÇÕES:")
                print("   - Verifique se o sistema ainda está rodando")
                print("   - Considere reduzir ainda mais os thresholds")
                print("   - Verifique logs para erros específicos")
            
            print("-" * 50)
            
            # Aguardar próxima verificação
            await asyncio.sleep(30)  # Verificar a cada 30 segundos
            
        except KeyboardInterrupt:
            print("\n👋 Monitor interrompido pelo usuário")
            break
        except Exception as e:
            print(f"❌ Erro no monitor: {e}")
            await asyncio.sleep(10)

if __name__ == "__main__":
    asyncio.run(monitor_system())
