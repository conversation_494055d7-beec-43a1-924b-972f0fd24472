"""
QUALIA D-05: Hot-reload & Rollback Mechanisms - Test Suite
==========================================================

Testes abrangentes para validar o sistema de hot-reload e rollback.

YAA IMPLEMENTATION: Testes unitários e de integração para garantir
funcionamento correto do sistema D-05.
"""

import asyncio
import json
import os
import tempfile
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any
import shutil

import yaml
import pytest

# Adicionar src ao path para imports
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.config.hot_reload import (
    ConfigurationHotReloader,
    ReloadStatus,
    ValidationResult,
    get_global_hot_reloader,
    start_hot_reload_system,
    stop_hot_reload_system
)
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

class TestConfigurationHotReloader:
    """Testes para o sistema de hot-reload."""
    
    def setup_method(self):
        """Setup para cada teste."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.config_file = self.temp_dir / "test_config.yaml"
        self.backup_dir = self.temp_dir / "backups"
        
        # Criar configuração inicial
        self.initial_config = {
            "optimization": {
                "n_trials_per_cycle": 25,
                "lookback_hours": 24
            },
            "pruning": {
                "strategy": "ADAPTIVE"
            },
            "multi_fidelity": {
                "fidelity_levels": [1, 6, 24]
            }
        }
        
        with open(self.config_file, 'w') as f:
            yaml.dump(self.initial_config, f)
            
        self.hot_reloader = ConfigurationHotReloader(
            watched_paths=[str(self.config_file)],
            backup_dir=str(self.backup_dir)
        )
        
    def teardown_method(self):
        """Cleanup após cada teste."""
        if hasattr(self, 'hot_reloader'):
            self.hot_reloader.stop()
        if hasattr(self, 'temp_dir') and self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
            
    def test_hot_reloader_initialization(self):
        """Testa inicialização do hot-reloader."""
        assert self.hot_reloader is not None
        assert len(self.hot_reloader.watched_paths) == 1
        assert str(self.config_file) in self.hot_reloader.watched_paths
        assert self.hot_reloader.backup_dir.exists()
        
    def test_create_initial_versions(self):
        """Testa criação de versões iniciais."""
        self.hot_reloader._create_initial_versions()
        
        versions = self.hot_reloader.get_version_history(str(self.config_file))
        assert len(versions) == 1
        assert versions[0].description == "Initial version"
        assert versions[0].config_data == self.initial_config
        
    async def test_config_validation_success(self):
        """Testa validação bem-sucedida de configuração."""
        valid_config = {
            "optimization": {
                "n_trials_per_cycle": 30,
                "parameter_ranges": {
                    "price_amplification": {"min": 1.0, "max": 10.0},
                    "news_amplification": {"min": 1.0, "max": 15.0},
                    "min_confidence": {"min": 0.2, "max": 0.8}
                }
            },
            "pruning": {"strategy": "MEDIAN"},
            "multi_fidelity": {"fidelity_levels": [1, 12, 24]}
        }
        
        result = await self.hot_reloader._validate_config(str(self.config_file), valid_config)
        assert result.is_valid
        assert "válida" in result.error_message
        
    async def test_config_validation_failure(self):
        """Testa falha na validação de configuração."""
        invalid_config = {
            "optimization": {
                "parameter_ranges": {
                    "price_amplification": {"min": 50.0, "max": 100.0}  # Range inválido
                }
            },
            "pruning": {"strategy": "INVALID"},
            "multi_fidelity": {}
        }
        
        result = await self.hot_reloader._validate_config(str(self.config_file), invalid_config)
        assert not result.is_valid
        assert "inválido" in result.error_message
        
    async def test_successful_reload(self):
        """Testa reload bem-sucedido de configuração."""
        # Criar versão inicial
        self.hot_reloader._create_initial_versions()
        
        # Modificar configuração
        new_config = self.initial_config.copy()
        new_config["optimization"]["n_trials_per_cycle"] = 50
        
        with open(self.config_file, 'w') as f:
            yaml.dump(new_config, f)
            
        # Executar reload
        event = await self.hot_reloader.reload_config(str(self.config_file))
        
        assert event.status == ReloadStatus.SUCCESS
        assert event.version_after is not None
        
        # Verificar nova versão foi criada
        versions = self.hot_reloader.get_version_history(str(self.config_file))
        assert len(versions) == 2
        assert versions[-1].config_data["optimization"]["n_trials_per_cycle"] == 50
        
    async def test_reload_with_validation_error(self):
        """Testa reload com erro de validação."""
        # Criar versão inicial
        self.hot_reloader._create_initial_versions()
        
        # Criar configuração inválida
        invalid_config = {"invalid": "config"}
        
        with open(self.config_file, 'w') as f:
            yaml.dump(invalid_config, f)
            
        # Executar reload
        event = await self.hot_reloader.reload_config(str(self.config_file))
        
        assert event.status == ReloadStatus.VALIDATION_ERROR
        assert event.error_message is not None
        
    async def test_manual_rollback(self):
        """Testa rollback manual para versão específica."""
        # Criar múltiplas versões
        self.hot_reloader._create_initial_versions()
        
        # Versão 2
        config_v2 = self.initial_config.copy()
        config_v2["optimization"]["n_trials_per_cycle"] = 30
        version_2 = self.hot_reloader._create_version(str(self.config_file), config_v2)
        
        # Versão 3
        config_v3 = self.initial_config.copy()
        config_v3["optimization"]["n_trials_per_cycle"] = 40
        self.hot_reloader._create_version(str(self.config_file), config_v3)
        
        # Rollback para versão 2
        event = await self.hot_reloader.manual_rollback(str(self.config_file), version_2.version_id)
        
        assert event.status == ReloadStatus.ROLLBACK
        assert event.version_after == version_2.version_id
        
        # Verificar arquivo foi restaurado
        with open(self.config_file, 'r') as f:
            restored_config = yaml.safe_load(f)
        assert restored_config["optimization"]["n_trials_per_cycle"] == 30
        
    def test_version_history_management(self):
        """Testa gerenciamento de histórico de versões."""
        # Criar mais versões que o limite
        max_versions = 3
        self.hot_reloader.max_versions = max_versions
        
        for i in range(5):
            config = self.initial_config.copy()
            config["test_version"] = i
            self.hot_reloader._create_version(str(self.config_file), config)
            
        versions = self.hot_reloader.get_version_history(str(self.config_file))
        assert len(versions) <= max_versions
        
        # Verificar que as versões mais recentes foram mantidas
        assert versions[-1].config_data["test_version"] == 4
        
    def test_callback_registration(self):
        """Testa registro e execução de callbacks."""
        callback_called = False
        callback_config = None
        
        def test_callback(config_path: str, config_data: Dict[str, Any]):
            nonlocal callback_called, callback_config
            callback_called = True
            callback_config = config_data
            
        # Registrar callback
        self.hot_reloader.register_callback(str(self.config_file), test_callback)
        
        # Verificar callback foi registrado
        assert str(self.config_file) in self.hot_reloader.reload_callbacks
        assert test_callback in self.hot_reloader.reload_callbacks[str(self.config_file)]
        
    async def test_callback_execution(self):
        """Testa execução de callbacks durante reload."""
        callback_called = False
        callback_config = None
        
        async def async_callback(config_path: str, config_data: Dict[str, Any]):
            nonlocal callback_called, callback_config
            callback_called = True
            callback_config = config_data
            
        # Registrar callback
        self.hot_reloader.register_callback(str(self.config_file), async_callback)
        
        # Executar notificação
        test_config = {"test": "data"}
        await self.hot_reloader._notify_components(str(self.config_file), test_config)
        
        assert callback_called
        assert callback_config == test_config

async def test_integration_with_bayesian_optimizer():
    """Testa integração com BayesianOptimizer."""
    try:
        from qualia.optimization.bayesian_optimizer import BayesianOptimizer, OptimizationConfig
        from qualia.config.hot_reload_integration import BayesianOptimizerHotReload
        
        # Criar otimizador
        config = OptimizationConfig()
        optimizer = BayesianOptimizer(config, use_realistic_evaluation=False)
        
        # Criar integração
        integration = BayesianOptimizerHotReload(optimizer)
        
        # Testar atualização de configuração
        new_config = {
            "optimization": {
                "n_trials_per_cycle": 50,
                "lookback_hours": 48,
                "parameter_ranges": {
                    "price_amplification": {"min": 2.0, "max": 8.0}
                }
            }
        }
        
        await integration._on_config_reload("config/bayesian_optimization.yaml", new_config)
        
        # Verificar configuração foi atualizada
        assert optimizer.config.n_trials_per_cycle == 50
        assert optimizer.config.lookback_hours == 48
        assert optimizer.config.price_amp_range == (2.0, 8.0)
        
        logger.info("✅ Integração com BayesianOptimizer testada com sucesso")
        
    except ImportError as e:
        logger.warning(f"⚠️ Não foi possível testar integração com BayesianOptimizer: {e}")

def test_global_hot_reloader():
    """Testa instância global do hot-reloader."""
    # Obter instância global
    reloader1 = get_global_hot_reloader()
    reloader2 = get_global_hot_reloader()
    
    # Verificar que é a mesma instância
    assert reloader1 is reloader2
    
    # Testar start/stop do sistema global
    start_hot_reload_system()
    assert reloader1.is_running
    
    stop_hot_reload_system()
    assert not reloader1.is_running

async def run_all_tests():
    """Executa todos os testes do sistema D-05."""
    print("🧪 QUALIA D-05: Hot-reload & Rollback - Test Suite")
    print("=" * 60)
    
    test_results = []
    
    # Testes da classe principal
    test_class = TestConfigurationHotReloader()
    
    tests = [
        ("Inicialização do Hot-reloader", test_class.test_hot_reloader_initialization),
        ("Criação de Versões Iniciais", test_class.test_create_initial_versions),
        ("Validação Bem-sucedida", test_class.test_config_validation_success),
        ("Validação com Falha", test_class.test_config_validation_failure),
        ("Reload Bem-sucedido", test_class.test_successful_reload),
        ("Reload com Erro de Validação", test_class.test_reload_with_validation_error),
        ("Rollback Manual", test_class.test_manual_rollback),
        ("Gerenciamento de Histórico", test_class.test_version_history_management),
        ("Registro de Callbacks", test_class.test_callback_registration),
        ("Execução de Callbacks", test_class.test_callback_execution),
        ("Integração com BayesianOptimizer", test_integration_with_bayesian_optimizer),
        ("Hot-reloader Global", test_global_hot_reloader)
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 Executando: {test_name}")
            
            # Setup se necessário
            if hasattr(test_func, '__self__'):
                test_func.__self__.setup_method()
            
            # Executar teste
            if asyncio.iscoroutinefunction(test_func):
                await test_func()
            else:
                test_func()
                
            # Cleanup se necessário
            if hasattr(test_func, '__self__'):
                test_func.__self__.teardown_method()
                
            print(f"✅ {test_name}: PASSOU")
            test_results.append((test_name, True, None))
            
        except Exception as e:
            print(f"❌ {test_name}: FALHOU - {e}")
            test_results.append((test_name, False, str(e)))
            
    # Resumo dos resultados
    print("\n" + "=" * 60)
    print("📊 RESUMO DOS TESTES D-05")
    print("=" * 60)
    
    passed = sum(1 for _, success, _ in test_results if success)
    total = len(test_results)
    
    for test_name, success, error in test_results:
        status = "✅ PASSOU" if success else f"❌ FALHOU: {error}"
        print(f"{test_name:<40} {status}")
        
    print(f"\n🎯 Resultado Final: {passed}/{total} testes passaram")
    
    if passed == total:
        print("🎉 TODOS OS TESTES D-05 PASSARAM COM SUCESSO!")
        return True
    else:
        print(f"⚠️ {total - passed} testes falharam")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    exit(0 if success else 1)
