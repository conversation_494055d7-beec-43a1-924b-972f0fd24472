#!/usr/bin/env python3
"""
Script para iniciar o BayesOpt Microservice - D-02 Implementation.
Inicia o serviço FastAPI com configuração otimizada para produção.
"""

import os
import sys
import argparse
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import uvicorn
from qualia.services.models import ServiceConfig
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="QUALIA BayesOpt Microservice")
    
    # Server settings
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8080, help="Port to bind to")
    parser.add_argument("--workers", type=int, default=1, help="Number of workers")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload for development")
    
    # Database settings
    parser.add_argument("--database-url", default="sqlite:///data/bayes_service.db", 
                       help="Database URL for Optuna studies")
    
    # Service settings
    parser.add_argument("--max-studies", type=int, default=100, 
                       help="Maximum concurrent studies")
    parser.add_argument("--study-timeout", type=int, default=24, 
                       help="Study timeout in hours")
    parser.add_argument("--cleanup", action="store_true", default=True,
                       help="Enable automatic cleanup")
    
    # Performance settings
    parser.add_argument("--max-trials", type=int, default=10000,
                       help="Maximum trials per study")
    parser.add_argument("--trial-timeout", type=int, default=300,
                       help="Trial timeout in seconds")
    
    # Logging settings
    parser.add_argument("--log-level", default="INFO", 
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="Logging level")
    parser.add_argument("--log-file", help="Log file path")
    
    # Security settings
    parser.add_argument("--api-key", help="API key for authentication")
    
    return parser.parse_args()


def create_config(args) -> ServiceConfig:
    """Create service configuration from arguments."""
    return ServiceConfig(
        host=args.host,
        port=args.port,
        workers=args.workers,
        database_url=args.database_url,
        max_concurrent_studies=args.max_studies,
        study_timeout_hours=args.study_timeout,
        auto_cleanup_enabled=args.cleanup,
        max_trials_per_study=args.max_trials,
        trial_timeout_seconds=args.trial_timeout,
        log_level=args.log_level,
        log_file=args.log_file,
        api_key_required=bool(args.api_key),
        api_key=args.api_key
    )


def setup_directories(config: ServiceConfig):
    """Setup required directories."""
    # Create data directory for database
    db_path = config.database_url.replace("sqlite:///", "")
    db_dir = os.path.dirname(db_path)
    if db_dir:
        os.makedirs(db_dir, exist_ok=True)
        logger.info(f"📁 Diretório criado: {db_dir}")
    
    # Create log directory if needed
    if config.log_file:
        log_dir = os.path.dirname(config.log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
            logger.info(f"📁 Diretório de logs criado: {log_dir}")


def main():
    """Main function."""
    print("🚀 QUALIA BAYESOPT MICROSERVICE - D-02")
    print("=" * 60)
    
    # Parse arguments
    args = parse_args()
    config = create_config(args)
    
    # Setup directories
    setup_directories(config)
    
    # Display configuration
    print(f"🔧 Configuração:")
    print(f"   • Host: {config.host}:{config.port}")
    print(f"   • Workers: {config.workers}")
    print(f"   • Database: {config.database_url}")
    print(f"   • Max studies: {config.max_concurrent_studies}")
    print(f"   • Study timeout: {config.study_timeout_hours}h")
    print(f"   • Auto cleanup: {config.auto_cleanup_enabled}")
    print(f"   • Log level: {config.log_level}")
    if config.log_file:
        print(f"   • Log file: {config.log_file}")
    if config.api_key_required:
        print(f"   • API key: {'*' * len(config.api_key) if config.api_key else 'Not set'}")
    print()
    
    # Set environment variables for service config
    os.environ["BAYES_SERVICE_CONFIG"] = config.model_dump_json()
    
    # Start server
    try:
        logger.info("🎯 Iniciando BayesOpt Microservice...")
        
        uvicorn.run(
            "qualia.services.bayes_service:app",
            host=config.host,
            port=config.port,
            workers=config.workers if not args.reload else 1,
            reload=args.reload,
            log_level=config.log_level.lower(),
            access_log=True,
            server_header=False,
            date_header=False
        )
        
    except KeyboardInterrupt:
        logger.info("🛑 Serviço interrompido pelo usuário")
    except Exception as e:
        logger.error(f"❌ Erro ao iniciar serviço: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
