"""Intelligent fallback system with quality scoring and graceful degradation.

This module provides sophisticated fallback mechanisms that maintain system
operation even when primary data sources fail, with intelligent quality
assessment and gradual degradation strategies.

Features:
- Multi-tier fallback hierarchy
- Quality scoring for data sources
- Graceful degradation with confidence levels
- Predictive fallback based on patterns
- Comprehensive fallback analytics
- Automatic recovery detection
"""

from __future__ import annotations

import asyncio
import time
import statistics
from typing import Any, Dict, List, Optional, Callable, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
from collections import deque, defaultdict
import random

from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class FallbackTier(Enum):
    """Fallback tier levels."""
    PRIMARY = "primary"
    SECONDARY = "secondary"
    TERTIARY = "tertiary"
    EMERGENCY = "emergency"
    SYNTHETIC = "synthetic"


class DataQuality(Enum):
    """Data quality levels for fallback assessment."""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    UNRELIABLE = "unreliable"


@dataclass
class FallbackSource:
    """Configuration for a fallback data source."""
    name: str
    tier: FallbackTier
    provider_func: Callable[..., Any]
    quality_weight: float = 1.0
    latency_weight: float = 1.0
    reliability_weight: float = 1.0
    max_age_seconds: float = 300.0
    enabled: bool = True
    
    # Performance tracking
    success_count: int = 0
    failure_count: int = 0
    total_latency: float = 0.0
    last_success: float = 0.0
    last_failure: float = 0.0
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        total = self.success_count + self.failure_count
        return self.success_count / max(1, total)
    
    @property
    def average_latency(self) -> float:
        """Calculate average latency."""
        return self.total_latency / max(1, self.success_count)
    
    @property
    def quality_score(self) -> float:
        """Calculate overall quality score."""
        # Base score from success rate
        reliability_score = self.success_rate * self.reliability_weight
        
        # Latency penalty (lower latency = higher score)
        latency_score = max(0, 1.0 - (self.average_latency / 10.0)) * self.latency_weight
        
        # Recency bonus
        recency_score = 0.0
        if self.last_success > 0:
            age = time.time() - self.last_success
            recency_score = max(0, 1.0 - (age / 3600.0)) * 0.3  # Decay over 1 hour
        
        # Combine scores
        total_score = (reliability_score + latency_score + recency_score) * self.quality_weight
        return min(1.0, total_score)


@dataclass
class FallbackResult:
    """Result from fallback operation."""
    data: Any
    source: str
    tier: FallbackTier
    quality: DataQuality
    confidence: float
    latency: float
    timestamp: float
    is_synthetic: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)


class IntelligentFallbackManager:
    """Advanced fallback manager with quality scoring and graceful degradation.
    
    This manager provides:
    - Multi-tier fallback hierarchy
    - Quality-based source selection
    - Predictive fallback activation
    - Synthetic data generation
    - Comprehensive analytics and monitoring
    """
    
    def __init__(
        self,
        name: str = "fallback_manager",
        min_confidence_threshold: float = 0.3,
        quality_decay_rate: float = 0.1,
        enable_predictive_fallback: bool = True,
        enable_synthetic_data: bool = True,
        max_fallback_attempts: int = 5,
        fallback_timeout: float = 30.0,
    ):
        self.name = name
        self.min_confidence_threshold = min_confidence_threshold
        self.quality_decay_rate = quality_decay_rate
        self.enable_predictive_fallback = enable_predictive_fallback
        self.enable_synthetic_data = enable_synthetic_data
        self.max_fallback_attempts = max_fallback_attempts
        self.fallback_timeout = fallback_timeout
        
        # Fallback sources organized by tier
        self.sources: Dict[FallbackTier, List[FallbackSource]] = {
            tier: [] for tier in FallbackTier
        }
        
        # Historical data for pattern analysis
        self.failure_history: deque = deque(maxlen=1000)
        self.success_history: deque = deque(maxlen=1000)
        self.quality_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Synthetic data generators
        self.synthetic_generators: Dict[str, Callable] = {}
        
        # Statistics
        self.stats = {
            'total_requests': 0,
            'primary_successes': 0,
            'fallback_activations': 0,
            'synthetic_generations': 0,
            'quality_degradations': 0,
            'recovery_detections': 0,
            'predictive_activations': 0
        }
        
        # State tracking
        self.current_primary_health = 1.0
        self.degradation_level = 0
        self.last_health_check = 0.0

    def register_source(
        self,
        name: str,
        tier: FallbackTier,
        provider_func: Callable,
        quality_weight: float = 1.0,
        latency_weight: float = 1.0,
        reliability_weight: float = 1.0,
        max_age_seconds: float = 300.0
    ) -> None:
        """Register a fallback data source.
        
        Parameters
        ----------
        name : str
            Unique name for the source
        tier : FallbackTier
            Fallback tier level
        provider_func : Callable
            Function that provides the data
        quality_weight : float
            Weight for quality scoring
        latency_weight : float
            Weight for latency in scoring
        reliability_weight : float
            Weight for reliability in scoring
        max_age_seconds : float
            Maximum age for cached data from this source
        """
        source = FallbackSource(
            name=name,
            tier=tier,
            provider_func=provider_func,
            quality_weight=quality_weight,
            latency_weight=latency_weight,
            reliability_weight=reliability_weight,
            max_age_seconds=max_age_seconds
        )
        
        self.sources[tier].append(source)
        logger.info(f"Registered fallback source '{name}' at tier {tier.value}")

    def register_synthetic_generator(self, data_type: str, generator_func: Callable) -> None:
        """Register a synthetic data generator.
        
        Parameters
        ----------
        data_type : str
            Type of data this generator produces
        generator_func : Callable
            Function that generates synthetic data
        """
        self.synthetic_generators[data_type] = generator_func
        logger.info(f"Registered synthetic generator for '{data_type}'")

    async def get_data(
        self,
        data_type: str,
        context: Optional[Dict[str, Any]] = None,
        min_quality: DataQuality = DataQuality.ACCEPTABLE,
        timeout: Optional[float] = None
    ) -> Optional[FallbackResult]:
        """Get data with intelligent fallback.
        
        Parameters
        ----------
        data_type : str
            Type of data requested
        context : Dict, optional
            Additional context for data retrieval
        min_quality : DataQuality
            Minimum acceptable quality level
        timeout : float, optional
            Timeout for the operation
            
        Returns
        -------
        FallbackResult or None
            Data with quality information, or None if all sources failed
        """
        self.stats['total_requests'] += 1
        context = context or {}
        timeout = timeout or self.fallback_timeout
        
        start_time = time.time()
        
        # Check if predictive fallback should be activated
        if self.enable_predictive_fallback:
            if await self._should_activate_predictive_fallback(data_type, context):
                self.stats['predictive_activations'] += 1
                logger.info(f"Activating predictive fallback for {data_type}")
        
        # Try sources in tier order
        for tier in FallbackTier:
            if time.time() - start_time > timeout:
                break
            
            sources = self._get_available_sources(tier, data_type)
            if not sources:
                continue
            
            # Sort sources by quality score
            sources.sort(key=lambda s: s.quality_score, reverse=True)
            
            for source in sources:
                if time.time() - start_time > timeout:
                    break
                
                try:
                    result = await self._try_source(source, data_type, context)
                    if result and self._meets_quality_threshold(result, min_quality):
                        
                        # Record success
                        if tier == FallbackTier.PRIMARY:
                            self.stats['primary_successes'] += 1
                        else:
                            self.stats['fallback_activations'] += 1
                        
                        self._record_success(source, result)
                        return result
                        
                except Exception as e:
                    logger.warning(f"Source '{source.name}' failed: {e}")
                    self._record_failure(source, str(e))
        
        # All sources failed, try synthetic data if enabled
        if self.enable_synthetic_data and data_type in self.synthetic_generators:
            try:
                synthetic_result = await self._generate_synthetic_data(data_type, context)
                if synthetic_result:
                    self.stats['synthetic_generations'] += 1
                    return synthetic_result
            except Exception as e:
                logger.error(f"Synthetic data generation failed for {data_type}: {e}")
        
        logger.error(f"All fallback sources failed for {data_type}")
        return None

    async def _try_source(
        self,
        source: FallbackSource,
        data_type: str,
        context: Dict[str, Any]
    ) -> Optional[FallbackResult]:
        """Try to get data from a specific source."""
        start_time = time.time()
        
        try:
            # Call the provider function
            if asyncio.iscoroutinefunction(source.provider_func):
                data = await source.provider_func(data_type, context)
            else:
                data = source.provider_func(data_type, context)
            
            if data is None:
                return None
            
            latency = time.time() - start_time
            
            # Assess data quality
            quality = self._assess_data_quality(data, source, latency)
            confidence = self._calculate_confidence(source, quality, latency)
            
            result = FallbackResult(
                data=data,
                source=source.name,
                tier=source.tier,
                quality=quality,
                confidence=confidence,
                latency=latency,
                timestamp=time.time(),
                metadata={
                    'source_quality_score': source.quality_score,
                    'data_type': data_type
                }
            )
            
            logger.debug(
                f"Source '{source.name}' provided data with quality {quality.value} "
                f"and confidence {confidence:.2f}"
            )
            
            return result
            
        except Exception as e:
            logger.warning(f"Source '{source.name}' error: {e}")
            return None

    def _get_available_sources(self, tier: FallbackTier, data_type: str) -> List[FallbackSource]:
        """Get available sources for a tier and data type."""
        return [
            source for source in self.sources[tier]
            if source.enabled and self._is_source_healthy(source)
        ]

    def _is_source_healthy(self, source: FallbackSource) -> bool:
        """Check if a source is healthy enough to use."""
        # Check recent failure rate
        if source.failure_count > 0:
            recent_failure_rate = source.failure_count / (source.success_count + source.failure_count)
            if recent_failure_rate > 0.8:  # More than 80% failures
                return False
        
        # Check if source has been failing recently
        if source.last_failure > 0 and source.last_success > 0:
            if source.last_failure > source.last_success:
                # Failed more recently than succeeded
                time_since_failure = time.time() - source.last_failure
                if time_since_failure < 60.0:  # Failed within last minute
                    return False
        
        return True

    def _assess_data_quality(self, data: Any, source: FallbackSource, latency: float) -> DataQuality:
        """Assess the quality of data from a source."""
        # Base quality from source tier
        tier_quality_map = {
            FallbackTier.PRIMARY: DataQuality.EXCELLENT,
            FallbackTier.SECONDARY: DataQuality.GOOD,
            FallbackTier.TERTIARY: DataQuality.ACCEPTABLE,
            FallbackTier.EMERGENCY: DataQuality.POOR,
            FallbackTier.SYNTHETIC: DataQuality.UNRELIABLE
        }
        
        base_quality = tier_quality_map.get(source.tier, DataQuality.ACCEPTABLE)
        
        # Adjust based on source performance
        if source.success_rate < 0.5:
            # Downgrade quality for unreliable sources
            quality_levels = list(DataQuality)
            current_index = quality_levels.index(base_quality)
            downgrade_index = min(current_index + 1, len(quality_levels) - 1)
            base_quality = quality_levels[downgrade_index]
        
        # Adjust based on latency
        if latency > 10.0:  # High latency
            quality_levels = list(DataQuality)
            current_index = quality_levels.index(base_quality)
            downgrade_index = min(current_index + 1, len(quality_levels) - 1)
            base_quality = quality_levels[downgrade_index]
        
        return base_quality

    def _calculate_confidence(self, source: FallbackSource, quality: DataQuality, latency: float) -> float:
        """Calculate confidence level for the data."""
        # Base confidence from quality
        quality_confidence_map = {
            DataQuality.EXCELLENT: 0.95,
            DataQuality.GOOD: 0.85,
            DataQuality.ACCEPTABLE: 0.70,
            DataQuality.POOR: 0.50,
            DataQuality.UNRELIABLE: 0.30
        }
        
        base_confidence = quality_confidence_map.get(quality, 0.50)
        
        # Adjust based on source reliability
        reliability_factor = source.success_rate
        
        # Adjust based on latency (lower latency = higher confidence)
        latency_factor = max(0.1, 1.0 - (latency / 30.0))  # Decay over 30 seconds
        
        # Combine factors
        final_confidence = base_confidence * reliability_factor * latency_factor
        
        return max(0.0, min(1.0, final_confidence))

    def _meets_quality_threshold(self, result: FallbackResult, min_quality: DataQuality) -> bool:
        """Check if result meets minimum quality threshold."""
        quality_levels = list(DataQuality)
        result_level = quality_levels.index(result.quality)
        min_level = quality_levels.index(min_quality)
        
        return result_level <= min_level and result.confidence >= self.min_confidence_threshold

    async def _should_activate_predictive_fallback(self, data_type: str, context: Dict[str, Any]) -> bool:
        """Determine if predictive fallback should be activated."""
        # Check primary source health trend
        if self.current_primary_health < 0.7:
            return True
        
        # Check for patterns in failure history
        recent_failures = [
            f for f in self.failure_history
            if time.time() - f['timestamp'] < 300  # Last 5 minutes
        ]
        
        if len(recent_failures) > 3:  # Multiple recent failures
            return True
        
        # Check time-based patterns (e.g., known maintenance windows)
        current_hour = time.localtime().tm_hour
        if current_hour in [2, 3, 4]:  # Common maintenance hours
            return True
        
        return False

    async def _generate_synthetic_data(self, data_type: str, context: Dict[str, Any]) -> Optional[FallbackResult]:
        """Generate synthetic data as last resort."""
        if data_type not in self.synthetic_generators:
            return None
        
        try:
            generator = self.synthetic_generators[data_type]
            
            start_time = time.time()
            if asyncio.iscoroutinefunction(generator):
                synthetic_data = await generator(context)
            else:
                synthetic_data = generator(context)
            
            latency = time.time() - start_time
            
            result = FallbackResult(
                data=synthetic_data,
                source="synthetic_generator",
                tier=FallbackTier.SYNTHETIC,
                quality=DataQuality.UNRELIABLE,
                confidence=0.2,  # Low confidence for synthetic data
                latency=latency,
                timestamp=time.time(),
                is_synthetic=True,
                metadata={
                    'generator': data_type,
                    'context': context
                }
            )
            
            logger.info(f"Generated synthetic data for {data_type}")
            return result
            
        except Exception as e:
            logger.error(f"Synthetic data generation failed: {e}")
            return None

    def _record_success(self, source: FallbackSource, result: FallbackResult) -> None:
        """Record successful data retrieval."""
        source.success_count += 1
        source.total_latency += result.latency
        source.last_success = time.time()
        
        self.success_history.append({
            'timestamp': time.time(),
            'source': source.name,
            'tier': source.tier.value,
            'quality': result.quality.value,
            'confidence': result.confidence,
            'latency': result.latency
        })
        
        # Update quality history
        self.quality_history[source.name].append(result.quality)

    def _record_failure(self, source: FallbackSource, error: str) -> None:
        """Record failed data retrieval."""
        source.failure_count += 1
        source.last_failure = time.time()
        
        self.failure_history.append({
            'timestamp': time.time(),
            'source': source.name,
            'tier': source.tier.value,
            'error': error
        })

    def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status of all sources."""
        status = {
            'overall_health': self.current_primary_health,
            'degradation_level': self.degradation_level,
            'sources': {}
        }
        
        for tier, sources in self.sources.items():
            for source in sources:
                status['sources'][source.name] = {
                    'tier': tier.value,
                    'enabled': source.enabled,
                    'healthy': self._is_source_healthy(source),
                    'success_rate': source.success_rate,
                    'average_latency': source.average_latency,
                    'quality_score': source.quality_score,
                    'last_success': source.last_success,
                    'last_failure': source.last_failure
                }
        
        return status

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive fallback statistics."""
        total_requests = self.stats['total_requests']
        primary_rate = self.stats['primary_successes'] / max(1, total_requests)
        fallback_rate = self.stats['fallback_activations'] / max(1, total_requests)
        
        return {
            'name': self.name,
            'primary_success_rate': primary_rate,
            'fallback_activation_rate': fallback_rate,
            'stats': self.stats,
            'registered_sources': sum(len(sources) for sources in self.sources.values()),
            'synthetic_generators': len(self.synthetic_generators),
            'current_health': self.current_primary_health,
            'recent_failures': len([
                f for f in self.failure_history
                if time.time() - f['timestamp'] < 300
            ])
        }

    async def reset_source_stats(self, source_name: str) -> bool:
        """Reset statistics for a specific source."""
        for sources in self.sources.values():
            for source in sources:
                if source.name == source_name:
                    source.success_count = 0
                    source.failure_count = 0
                    source.total_latency = 0.0
                    source.last_success = 0.0
                    source.last_failure = 0.0
                    logger.info(f"Reset statistics for source '{source_name}'")
                    return True
        
        return False
