#!/usr/bin/env python3
"""
Script simplificado para testar o Warmup Manager
"""

import asyncio
import sys
import os
from pathlib import Path

# Adicionar o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent))


async def test_warmup():
    """Testa o sistema de warmup com configuração mínima."""
    
    print("🚀 Iniciando teste simplificado do Warmup Manager...")
    
    # Importar após adicionar ao path
    from src.qualia.core.warmup_manager import WarmupManager
    from src.qualia.exchanges.kucoin_client import KuCoinClient
    
    # Configurar variáveis de ambiente temporárias para teste
    os.environ["KUCOIN_API_KEY"] = "test_key"
    os.environ["KUCOIN_API_SECRET"] = "test_secret"
    os.environ["KUCOIN_API_PASSPHRASE"] = "test_passphrase"
    
    # Criar cliente KuCoin com configuração de teste
    kucoin_config = {
        "api_key": os.environ["KUCOIN_API_KEY"],
        "api_secret": os.environ["KUCOIN_API_SECRET"],
        "api_passphrase": os.environ["KUCOIN_API_PASSPHRASE"],
        "sandbox": True  # Usar sandbox para teste
    }
    kucoin_client = KuCoinClient(kucoin_config)
    
    # Configurar requisitos mínimos para teste rápido
    min_candles_test = {
        "5m": 10,   # Apenas 10 candles para teste rápido
    }
    
    # Criar warmup manager
    warmup = WarmupManager(
        kucoin_client=kucoin_client,
        min_candles_required=min_candles_test,
        cache_dir="data/warmup_cache_test"
    )
    
    # Testar com apenas um símbolo
    symbols = ["BTC-USDT"]
    timeframes = ["5m"]
    
    try:
        print(f"\n📊 Carregando dados para {symbols} em {timeframes}...")
        print("   ⚠️ Usando modo sandbox para teste")
        
        # Carregar dados históricos
        success = await warmup.load_historical_data(
            symbols=symbols,
            timeframes=timeframes,
            force_reload=False  # Usar cache se disponível
        )
        
        if success:
            print("\n✅ Warmup concluído com sucesso!")
            
            # Mostrar dados carregados
            df = warmup.get_data("BTC-USDT", "5m")
            if df is not None and not df.empty:
                print("\n📈 Dados carregados:")
                print(f"   Total de candles: {len(df)}")
                print(f"   Período: {df.index[0]} até {df.index[-1]}")
                print(f"   Último preço: ${df['close'].iloc[-1]:.2f}")
                print("\n📊 Últimas 5 velas:")
                print(df[['open', 'high', 'low', 'close', 'volume']].tail())
        else:
            print("❌ Falha no warmup")
            
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Fechar cliente usando shutdown
        if hasattr(kucoin_client, 'shutdown'):
            await kucoin_client.shutdown()
        elif hasattr(kucoin_client, 'integration') and kucoin_client.integration:
            await kucoin_client.integration.close()
        
        # Limpar variáveis de ambiente
        for key in ["KUCOIN_API_KEY", "KUCOIN_API_SECRET", "KUCOIN_API_PASSPHRASE"]:
            os.environ.pop(key, None)


if __name__ == "__main__":
    # Executar teste
    asyncio.run(test_warmup()) 
