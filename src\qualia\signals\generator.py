"""
Signal Generator

Generates trading signals by combining quantum operator results,
technical analysis, and market sentiment analysis.
"""

import numpy as np
from typing import Any, Dict, List, Optional
import pandas as pd
from dataclasses import dataclass, asdict
from datetime import datetime, timezone
import logging
import uuid

from .. import indicators as shared_indicators
from .quantum_analyzer import QuantumSignalAnalyzer
from .technical_analyzer import TechnicalSignalAnalyzer
from .sentiment_analyzer import SentimentSignalAnalyzer


@dataclass
class SignalConfig:
    """Configuration for signal generation"""

    min_confidence: float = 0.6
    max_signals_per_symbol: int = 3
    signal_decay_time: int = 300  # seconds
    quantum_weight: float = 0.4
    technical_weight: float = 0.3
    sentiment_weight: float = 0.3


@dataclass
class TradingSignal:
    """Represents a trading signal"""

    id: str
    symbol: str
    action: str  # 'buy' or 'sell'
    confidence: float
    strength: float
    quantity: float
    target_price: Optional[float]
    signal_type: str
    source_components: List[str]
    metadata: Dict[str, Any]
    timestamp: datetime
    expiry_time: Optional[datetime] = None


class SignalGenerator:
    """
    Advanced trading signal generator

    Combines quantum operator results, technical analysis, and market
    sentiment to generate high-quality trading signals.
    """

    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Signal generation configuration
        self.signal_config = SignalConfig(
            min_confidence=config.get("min_confidence", 0.6),
            max_signals_per_symbol=config.get("max_signals_per_symbol", 3),
            signal_decay_time=config.get("signal_decay_time", 300),
            quantum_weight=config.get("quantum_weight", 0.4),
            technical_weight=config.get("technical_weight", 0.3),
            sentiment_weight=config.get("sentiment_weight", 0.3),
        )

        # Signal tracking
        self.active_signals: Dict[str, List[TradingSignal]] = {}
        self.signal_history: List[TradingSignal] = []
        self.signal_performance: Dict[str, Dict] = {}

        # Component analyzers
        self.quantum_analyzer = QuantumSignalAnalyzer()
        self.technical_analyzer = TechnicalSignalAnalyzer()
        self.sentiment_analyzer = SentimentSignalAnalyzer()

        # Signal validation
        self.signal_validators = [
            self._validate_signal_strength,
            self._validate_signal_consistency,
            self._validate_signal_timing,
        ]

    async def generate_signals(self, symbol: str, analysis_input: Dict) -> List[Dict]:
        """
        Generate trading signals for a symbol

        Args:
            symbol: Trading pair symbol
            analysis_input: Combined analysis results from quantum operators and market data

        Returns:
            List of trading signal dictionaries
        """
        try:
            # Extract analysis components
            market_data = analysis_input.get("market_data", {})
            quantum_results = analysis_input.get("quantum_results", {})
            temporal_results = analysis_input.get("temporal_results", {})

            # Generate component signals
            quantum_signals = await self._generate_quantum_signals(
                symbol, quantum_results, market_data
            )
            technical_signals = await self._generate_technical_signals(
                symbol, market_data
            )
            sentiment_signals = await self._generate_sentiment_signals(
                symbol, market_data, temporal_results
            )

            # Combine and weight signals
            combined_signals = self._combine_signals(
                quantum_signals, technical_signals, sentiment_signals
            )

            # Filter and validate signals
            validated_signals = self._validate_signals(combined_signals)

            # Apply position sizing
            sized_signals = self._apply_signal_sizing(validated_signals, market_data)

            # Update signal tracking
            self._update_signal_tracking(symbol, sized_signals)

            # Convert to standardized DataFrame output
            output_signals = [self._signal_to_dict(signal) for signal in sized_signals]
            df = pd.DataFrame(output_signals)
            if not df.empty:
                df.rename(columns={"action": "signal"}, inplace=True)

            self.logger.debug(
                "Generated %s signals for %s", len(output_signals), symbol
            )

            return df

        except Exception as e:
            self.logger.error(f"Error generating signals for {symbol}: {e}")
            return []

    async def _generate_quantum_signals(
        self, symbol: str, quantum_results: Dict, market_data: Dict
    ) -> List[TradingSignal]:
        """Generate signals from quantum operator results"""
        signals = []

        try:
            # Support dataclass or object-like inputs from QAST core
            if hasattr(quantum_results, "__dataclass_fields__"):
                quantum_results = asdict(quantum_results)
            elif hasattr(quantum_results, "to_dict"):
                quantum_results = quantum_results.to_dict()
            elif hasattr(quantum_results, "__dict__"):
                quantum_results = dict(vars(quantum_results))

            # Analyze quantum operator outputs
            quantum_analysis = await self.quantum_analyzer.analyze(
                quantum_results, market_data
            )

            for analysis in quantum_analysis:
                if analysis["confidence"] >= self.signal_config.min_confidence:
                    signal = TradingSignal(
                        id=str(uuid.uuid4()),
                        symbol=symbol,
                        action=analysis["action"],
                        confidence=analysis["confidence"],
                        strength=analysis["strength"],
                        quantity=0.0,  # Will be calculated later
                        target_price=analysis.get("target_price"),
                        signal_type="quantum",
                        source_components=analysis["components"],
                        metadata=analysis.get("metadata", {}),
                        timestamp=datetime.now(timezone.utc),
                    )
                    signals.append(signal)

            return signals

        except Exception as e:
            self.logger.error(f"Error generating quantum signals: {e}")
            return []

    async def _generate_technical_signals(
        self, symbol: str, market_data: Dict
    ) -> List[TradingSignal]:
        """Generate signals from technical analysis"""
        signals = []

        try:
            # Analyze technical indicators
            technical_analysis = await self.technical_analyzer.analyze(market_data)

            for analysis in technical_analysis:
                if analysis["confidence"] >= self.signal_config.min_confidence:
                    signal = TradingSignal(
                        id=str(uuid.uuid4()),
                        symbol=symbol,
                        action=analysis["action"],
                        confidence=analysis["confidence"],
                        strength=analysis["strength"],
                        quantity=0.0,
                        target_price=analysis.get("target_price"),
                        signal_type="technical",
                        source_components=analysis["indicators"],
                        metadata=analysis.get("metadata", {}),
                        timestamp=datetime.now(timezone.utc),
                    )
                    signals.append(signal)

            return signals

        except Exception as e:
            self.logger.error(f"Error generating technical signals: {e}")
            return []

    async def _generate_sentiment_signals(
        self, symbol: str, market_data: Dict, temporal_results: Dict
    ) -> List[TradingSignal]:
        """Generate signals from market sentiment analysis"""
        signals = []

        try:
            # Analyze market sentiment and patterns
            sentiment_analysis = await self.sentiment_analyzer.analyze(
                market_data, temporal_results
            )

            for analysis in sentiment_analysis:
                if analysis["confidence"] >= self.signal_config.min_confidence:
                    signal = TradingSignal(
                        id=str(uuid.uuid4()),
                        symbol=symbol,
                        action=analysis["action"],
                        confidence=analysis["confidence"],
                        strength=analysis["strength"],
                        quantity=0.0,
                        target_price=analysis.get("target_price"),
                        signal_type="sentiment",
                        source_components=analysis["factors"],
                        metadata=analysis.get("metadata", {}),
                        timestamp=datetime.now(timezone.utc),
                    )
                    signals.append(signal)

            return signals

        except Exception as e:
            self.logger.error(f"Error generating sentiment signals: {e}")
            return []

    def _combine_signals(
        self,
        quantum_signals: List[TradingSignal],
        technical_signals: List[TradingSignal],
        sentiment_signals: List[TradingSignal],
    ) -> List[TradingSignal]:
        """Combine signals from different sources with weighted averaging"""
        try:
            # Group signals by action (buy/sell)
            signal_groups = {"buy": [], "sell": []}

            # Add weighted signals to groups
            for signal in quantum_signals:
                weighted_signal = self._apply_weight(
                    signal, self.signal_config.quantum_weight
                )
                signal_groups[signal.action].append(weighted_signal)

            for signal in technical_signals:
                weighted_signal = self._apply_weight(
                    signal, self.signal_config.technical_weight
                )
                signal_groups[signal.action].append(weighted_signal)

            for signal in sentiment_signals:
                weighted_signal = self._apply_weight(
                    signal, self.signal_config.sentiment_weight
                )
                signal_groups[signal.action].append(weighted_signal)

            # Combine signals within each group
            combined_signals = []

            for action, signals in signal_groups.items():
                if signals:
                    combined_signal = self._merge_signal_group(action, signals)
                    if combined_signal:
                        combined_signals.append(combined_signal)

            return combined_signals

        except Exception as e:
            self.logger.error(f"Error combining signals: {e}")
            return []

    def _apply_weight(self, signal: TradingSignal, weight: float) -> TradingSignal:
        """Apply weight to signal confidence and strength"""
        weighted_signal = signal
        weighted_signal.confidence *= weight
        weighted_signal.strength *= weight
        return weighted_signal

    def _merge_signal_group(
        self, action: str, signals: List[TradingSignal]
    ) -> Optional[TradingSignal]:
        """Merge multiple signals of the same action into one combined signal"""
        try:
            if not signals:
                return None

            # Calculate weighted averages
            total_weight = sum(s.strength for s in signals)
            if total_weight == 0:
                return None

            weighted_confidence = (
                sum(s.confidence * s.strength for s in signals) / total_weight
            )
            combined_strength = np.mean([s.strength for s in signals])

            # Combine source components
            all_components = []
            for signal in signals:
                all_components.extend(signal.source_components)

            # Calculate target price (weighted average)
            prices = [s.target_price for s in signals if s.target_price is not None]
            target_price = np.mean(prices) if prices else None

            # Combine metadata
            combined_metadata = {}
            for signal in signals:
                combined_metadata.update(signal.metadata)

            combined_signal = TradingSignal(
                id=str(uuid.uuid4()),
                symbol=signals[0].symbol,
                action=action,
                confidence=weighted_confidence,
                strength=combined_strength,
                quantity=0.0,
                target_price=target_price,
                signal_type="combined",
                source_components=list(set(all_components)),
                metadata=combined_metadata,
                timestamp=datetime.now(timezone.utc),
            )

            return combined_signal

        except Exception as e:
            self.logger.error(f"Error merging signal group: {e}")
            return None

    def _validate_signals(self, signals: List[TradingSignal]) -> List[TradingSignal]:
        """Validate signals using multiple validation criteria"""
        validated_signals = []

        for signal in signals:
            is_valid = True

            # Apply all validators
            for validator in self.signal_validators:
                if not validator(signal):
                    is_valid = False
                    break

            if is_valid:
                validated_signals.append(signal)
            else:
                self.logger.debug(f"Signal {signal.id} failed validation")

        return validated_signals

    def _validate_signal_strength(self, signal: TradingSignal) -> bool:
        """Validate signal strength and confidence"""
        return (
            signal.confidence >= self.signal_config.min_confidence
            and signal.strength > 0.0
            and signal.confidence <= 1.0
            and signal.strength <= 1.0
        )

    def _validate_signal_consistency(self, signal: TradingSignal) -> bool:
        """Validate signal consistency with recent signals"""
        try:
            # Check for conflicting signals
            recent_signals = self.active_signals.get(signal.symbol, [])

            for recent_signal in recent_signals:
                # Check if signals conflict
                if (
                    recent_signal.action != signal.action
                    and recent_signal.confidence > 0.7
                    and signal.confidence > 0.7
                ):

                    # High confidence conflicting signals
                    time_diff = (
                        signal.timestamp - recent_signal.timestamp
                    ).total_seconds()
                    if time_diff < 300:  # Within 5 minutes
                        return False

            return True

        except Exception as e:
            self.logger.error("Error validating signal group: %s", e)
            return True  # Default to valid if validation fails

    def _validate_signal_timing(self, signal: TradingSignal) -> bool:
        """Validate signal timing and market conditions"""
        try:
            # Check if too many signals for this symbol
            active_count = len(self.active_signals.get(signal.symbol, []))
            if active_count >= self.signal_config.max_signals_per_symbol:
                return False

            # Check signal freshness
            age = (datetime.now(timezone.utc) - signal.timestamp).total_seconds()
            if age > 60:  # Signals older than 1 minute are stale
                return False

            return True

        except Exception as e:
            self.logger.error("Error validating signal timing: %s", e)
            return True

    def _apply_signal_sizing(
        self, signals: List[TradingSignal], market_data: Dict
    ) -> List[TradingSignal]:
        """Apply position sizing to signals"""
        try:
            sized_signals = []

            for signal in signals:
                # Calculate base position size
                base_size = self._calculate_base_position_size(signal, market_data)

                # Adjust for confidence
                confidence_adjustment = signal.confidence**0.5  # Square root scaling

                # Adjust for market volatility
                volatility_adjustment = self._calculate_volatility_adjustment(
                    signal.symbol, market_data
                )

                # Final position size
                final_size = base_size * confidence_adjustment * volatility_adjustment

                # Set minimum position size
                min_size = 0.001
                final_size = max(min_size, final_size)

                signal.quantity = final_size
                sized_signals.append(signal)

            return sized_signals

        except Exception as e:
            self.logger.error(f"Error applying signal sizing: {e}")
            return signals

    def _calculate_base_position_size(
        self, signal: TradingSignal, market_data: Dict
    ) -> float:
        """Calculate base position size for signal"""
        try:
            # Default base size based on signal strength
            base_size = (
                0.01 * signal.strength
            )  # 1% of portfolio per full strength signal

            # Adjust based on signal type
            type_multipliers = {
                "quantum": 1.2,  # Higher weight for quantum signals
                "technical": 1.0,  # Standard weight for technical signals
                "sentiment": 0.8,  # Lower weight for sentiment signals
                "combined": 1.5,  # Higher weight for combined signals
            }

            multiplier = type_multipliers.get(signal.signal_type, 1.0)
            return base_size * multiplier

        except Exception as e:
            self.logger.error("Error calculating base position size: %s", e)
            return 0.01

    def _calculate_volatility_adjustment(self, symbol: str, market_data: Dict) -> float:
        """Calculate volatility-based position size adjustment"""
        try:
            if symbol not in market_data:
                return 1.0

            prices = market_data[symbol].get("prices", [])
            if len(prices) < 10:
                return 1.0

            # Calculate recent volatility
            returns = np.diff(prices[-20:]) / prices[-21:-1]
            volatility = np.std(returns)

            # Higher volatility = smaller position size
            # Volatility of 0.05 (5%) = 1.0 adjustment
            # Higher volatility = lower adjustment
            if volatility > 0:
                adjustment = min(1.0, 0.05 / volatility)
                return max(0.1, adjustment)  # Minimum 10% of base size

            return 1.0

        except Exception as e:
            self.logger.error("Error calculating volatility adjustment: %s", e)
            return 1.0

    def _update_signal_tracking(self, symbol: str, signals: List[TradingSignal]):
        """Update signal tracking and cleanup expired signals"""
        try:
            # Initialize symbol tracking if needed
            if symbol not in self.active_signals:
                self.active_signals[symbol] = []

            # Add new signals
            self.active_signals[symbol].extend(signals)

            # Add to history
            self.signal_history.extend(signals)

            # Cleanup expired signals
            self._cleanup_expired_signals()

            # Limit history size
            if len(self.signal_history) > 1000:
                self.signal_history = self.signal_history[-1000:]

        except Exception as e:
            self.logger.error(f"Error updating signal tracking: {e}")

    def _cleanup_expired_signals(self):
        """Remove expired signals from active tracking"""
        try:
            current_time = datetime.now(timezone.utc)

            for symbol in list(self.active_signals.keys()):
                active_signals = []

                for signal in self.active_signals[symbol]:
                    # Check if signal has expired
                    age = (current_time - signal.timestamp).total_seconds()

                    if age < self.signal_config.signal_decay_time:
                        active_signals.append(signal)

                self.active_signals[symbol] = active_signals

                # Remove symbol if no active signals
                if not active_signals:
                    del self.active_signals[symbol]

        except Exception as e:
            self.logger.error(f"Error cleaning up expired signals: {e}")

    def _signal_to_dict(self, signal: TradingSignal) -> Dict:
        """Convert TradingSignal to dictionary format."""

        return {
            "id": signal.id,
            "symbol": signal.symbol,
            "action": signal.action,
            "confidence": signal.confidence,
            "strength": signal.strength,
            "quantity": signal.quantity,
            "target_price": signal.target_price,
            "signal_type": signal.signal_type,
            "source_components": signal.source_components,
            "metadata": signal.metadata,
            "timestamp": signal.timestamp.isoformat(),
            "expiry_time": (
                signal.expiry_time.isoformat() if signal.expiry_time else None
            ),
        }

    def get_active_signals(self, symbol: Optional[str] = None) -> Dict:
        """Get currently active signals"""
        if symbol:
            return {symbol: self.active_signals.get(symbol, [])}
        else:
            return self.active_signals.copy()

    def get_signal_statistics(self) -> Dict:
        """Get signal generation statistics"""
        return {
            "total_signals_generated": len(self.signal_history),
            "active_signals_count": sum(
                len(signals) for signals in self.active_signals.values()
            ),
            "active_symbols": list(self.active_signals.keys()),
            "signal_types_distribution": self._get_signal_type_distribution(),
            "average_confidence": self._calculate_average_confidence(),
            "signal_performance": self.signal_performance.copy(),
        }

    def _get_signal_type_distribution(self) -> Dict[str, int]:
        """Get distribution of signal types"""
        distribution = {}

        for signal in self.signal_history[-100:]:  # Last 100 signals
            signal_type = signal.signal_type
            distribution[signal_type] = distribution.get(signal_type, 0) + 1

        return distribution

    def _calculate_average_confidence(self) -> float:
        """Calculate average confidence of recent signals"""
        if not self.signal_history:
            return 0.0

        recent_signals = self.signal_history[-50:]  # Last 50 signals
        confidences = [signal.confidence for signal in recent_signals]

        return np.mean(confidences) if confidences else 0.0

    async def analyze(self, market_data: Dict) -> List[Dict]:
        """Analyze technical indicators to generate signals"""
        signals = []

        try:
            prices = market_data.get("prices", [])
            volumes = market_data.get("volumes", [])

            if len(prices) < 20:
                return signals

            # Moving average crossover signals
            ma_signals = self._analyze_moving_averages(prices)
            signals.extend(ma_signals)

            # RSI signals
            rsi_signals = self._analyze_rsi(prices)
            signals.extend(rsi_signals)

            # Volume signals
            if len(volumes) > 0:
                volume_signals = self._analyze_volume(prices, volumes)
                signals.extend(volume_signals)

            # Support/resistance signals
            sr_signals = self._analyze_support_resistance(prices)
            signals.extend(sr_signals)

            return signals

        except Exception as e:
            self.logger.error("Error analyzing technical data: %s", e)
            return []

    def _analyze_moving_averages(self, prices: np.ndarray) -> List[Dict]:
        """Analyze moving average crossovers"""
        signals = []

        try:
            if len(prices) < 50:
                return signals

            # Calculate EMAs
            ema_12 = self._calculate_ema(prices, 12)
            ema_26 = self._calculate_ema(prices, 26)

            # Check for crossover
            if len(ema_12) >= 2 and len(ema_26) >= 2:
                current_diff = ema_12[-1] - ema_26[-1]
                previous_diff = ema_12[-2] - ema_26[-2]

                # Bullish crossover
                if previous_diff <= 0 and current_diff > 0:
                    signals.append(
                        {
                            "action": "buy",
                            "confidence": 0.7,
                            "strength": min(1.0, abs(current_diff) / prices[-1] * 100),
                            "indicators": ["ema_crossover"],
                            "metadata": {
                                "crossover_type": "bullish",
                                "ema_diff": current_diff,
                            },
                        }
                    )

                # Bearish crossover
                elif previous_diff >= 0 and current_diff < 0:
                    signals.append(
                        {
                            "action": "sell",
                            "confidence": 0.7,
                            "strength": min(1.0, abs(current_diff) / prices[-1] * 100),
                            "indicators": ["ema_crossover"],
                            "metadata": {
                                "crossover_type": "bearish",
                                "ema_diff": current_diff,
                            },
                        }
                    )

            return signals

        except Exception as e:
            self.logger.error("Error analyzing moving averages: %s", e)
            return []

    def _analyze_rsi(self, prices: np.ndarray) -> List[Dict]:
        """Analyze RSI for overbought/oversold conditions"""
        signals = []

        try:
            if len(prices) < 15:
                return signals

            rsi = self._calculate_rsi(prices, 14)

            if len(rsi) > 0:
                current_rsi = rsi[-1]

                # Oversold condition
                if current_rsi < 30:
                    signals.append(
                        {
                            "action": "buy",
                            "confidence": min(1.0, (30 - current_rsi) / 10),
                            "strength": (30 - current_rsi) / 30,
                            "indicators": ["rsi_oversold"],
                            "metadata": {"rsi_value": current_rsi},
                        }
                    )

                # Overbought condition
                elif current_rsi > 70:
                    signals.append(
                        {
                            "action": "sell",
                            "confidence": min(1.0, (current_rsi - 70) / 10),
                            "strength": (current_rsi - 70) / 30,
                            "indicators": ["rsi_overbought"],
                            "metadata": {"rsi_value": current_rsi},
                        }
                    )

            return signals

        except Exception as e:
            self.logger.error("Error analyzing RSI: %s", e)
            return []

    def _analyze_volume(self, prices: np.ndarray, volumes: np.ndarray) -> List[Dict]:
        """Analyze volume patterns"""
        signals = []

        try:
            if len(volumes) < 20:
                return signals

            # Volume spike analysis
            volume_ma = np.mean(volumes[-20:])
            current_volume = volumes[-1]

            if current_volume > volume_ma * 2:  # Volume spike
                # Determine direction from price movement
                if len(prices) >= 2:
                    price_change = (prices[-1] - prices[-2]) / prices[-2]

                    if abs(price_change) > 0.01:  # Significant price movement
                        action = "buy" if price_change > 0 else "sell"

                        signals.append(
                            {
                                "action": action,
                                "confidence": 0.6,
                                "strength": min(1.0, current_volume / volume_ma / 5),
                                "indicators": ["volume_spike"],
                                "metadata": {
                                    "volume_ratio": current_volume / volume_ma,
                                    "price_change": price_change,
                                },
                            }
                        )

            return signals

        except Exception as e:
            self.logger.error("Error analyzing volume patterns: %s", e)
            return []

    def _analyze_support_resistance(self, prices: np.ndarray) -> List[Dict]:
        """Analyze support and resistance levels"""
        signals = []

        try:
            if len(prices) < 50:
                return signals

            current_price = prices[-1]

            # Find recent highs and lows
            highs = []
            lows = []

            for i in range(5, len(prices) - 5):
                if all(prices[i] >= prices[i - j] for j in range(1, 6)) and all(
                    prices[i] >= prices[i + j] for j in range(1, 6)
                ):
                    highs.append(prices[i])

                if all(prices[i] <= prices[i - j] for j in range(1, 6)) and all(
                    prices[i] <= prices[i + j] for j in range(1, 6)
                ):
                    lows.append(prices[i])

            # Check if current price is near support/resistance
            if lows:
                nearest_support = min(lows, key=lambda x: abs(x - current_price))
                support_distance = abs(current_price - nearest_support) / current_price

                if (
                    support_distance < 0.02 and current_price > nearest_support
                ):  # Near support
                    signals.append(
                        {
                            "action": "buy",
                            "confidence": 0.65,
                            "strength": 1.0 - support_distance * 50,
                            "indicators": ["support_bounce"],
                            "target_price": nearest_support * 1.05,
                            "metadata": {"support_level": nearest_support},
                        }
                    )

            if highs:
                nearest_resistance = min(highs, key=lambda x: abs(x - current_price))
                resistance_distance = (
                    abs(current_price - nearest_resistance) / current_price
                )

                if (
                    resistance_distance < 0.02 and current_price < nearest_resistance
                ):  # Near resistance
                    signals.append(
                        {
                            "action": "sell",
                            "confidence": 0.65,
                            "strength": 1.0 - resistance_distance * 50,
                            "indicators": ["resistance_rejection"],
                            "target_price": nearest_resistance * 0.95,
                            "metadata": {"resistance_level": nearest_resistance},
                        }
                    )

            return signals

        except Exception as e:
            self.logger.error("Error analyzing support/resistance: %s", e)
            return []

    def _calculate_ema(self, prices: np.ndarray, period: int) -> np.ndarray:
        """Calculate Exponential Moving Average"""
        return np.asarray(shared_indicators.ema(prices, period), dtype=float)

    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> np.ndarray:
        """Calculate Relative Strength Index"""
        return np.asarray(shared_indicators.rsi(prices, period), dtype=float)
