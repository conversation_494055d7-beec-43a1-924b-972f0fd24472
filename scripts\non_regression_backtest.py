#!/usr/bin/env python3
"""
QUALIA Non-Regression Backtest

Executa backtest de 24h com parâmetros de produção para detectar regressões de performance.
Falha se PnL ou decision-rate cair mais de 20% em relação ao baseline.

Usage:
    python scripts/non_regression_backtest.py [--threshold 20] [--output-dir results/non_regression]
"""

import sys
import os
import json
import argparse
import asyncio
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import traceback

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.backtest.hyperparams_grid_search import (
    GridSearchParams,
    HyperParamsGridSearch,
    BacktestResult
)
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class NonRegressionTester:
    """
    Testa regressões de performance executando backtest de 24h
    e comparando com métricas baseline estabelecidas.
    """
    
    def __init__(self, output_dir: str = "results/non_regression", threshold: float = 20.0):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.threshold = threshold / 100.0  # Convert percentage to decimal
        
        # Parâmetros de produção otimizados (do sistema atual)
        self.production_params = {
            "price_amplification": 1.0,
            "news_amplification": 11.3,
            "min_confidence": 0.37
        }
        
        # Configuração do backtest de 24h
        self.backtest_params = GridSearchParams(
            price_amp_range=(self.production_params["price_amplification"], 
                           self.production_params["price_amplification"], 1),
            news_amp_range=(self.production_params["news_amplification"], 
                          self.production_params["news_amplification"], 1),
            min_conf_range=(self.production_params["min_confidence"], 
                          self.production_params["min_confidence"], 1),
            backtest_days=1,  # 24 horas
            initial_capital=10000.0,
            symbols=["BTC/USDT", "ETH/USDT"],  # Símbolos principais para teste rápido
            timeframe="1h",
            max_workers=1,
            cache_results=False,  # Sempre dados frescos para CI
            save_detailed_results=True
        )
        
        self.baseline_file = Path(__file__).parent / "baseline_metrics.json"
        
        logger.info(f"🎯 NonRegressionTester inicializado")
        logger.info(f"📊 Threshold: {threshold}%")
        logger.info(f"⚙️ Parâmetros: {self.production_params}")
    
    async def run_test(self) -> Dict[str, Any]:
        """Executa o teste de não-regressão completo."""
        
        logger.info("🚀 Iniciando teste de não-regressão QUALIA...")
        start_time = datetime.now()
        
        try:
            # 1. Executar backtest atual
            current_metrics = await self._run_current_backtest()
            
            # 2. Carregar baseline
            baseline_metrics = self._load_baseline()
            
            # 3. Comparar métricas
            regression_detected = self._compare_metrics(current_metrics, baseline_metrics)
            
            # 4. Gerar relatórios
            await self._generate_reports(current_metrics, baseline_metrics, regression_detected)
            
            # 5. Criar flag de regressão se necessário
            if regression_detected:
                self._create_regression_flag()
            
            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"✅ Teste concluído em {duration:.1f}s")
            
            return {
                "regression_detected": regression_detected,
                "current_metrics": current_metrics,
                "baseline_metrics": baseline_metrics,
                "duration_seconds": duration
            }
            
        except Exception as e:
            logger.error(f"❌ Erro no teste de não-regressão: {e}")
            logger.error(traceback.format_exc())
            raise
    
    async def _run_current_backtest(self) -> Dict[str, Any]:
        """Executa backtest com parâmetros atuais."""
        
        logger.info("📊 Executando backtest de 24h...")
        
        grid_search = HyperParamsGridSearch(self.backtest_params)
        results = await grid_search.run_grid_search()
        
        if not results.results:
            raise ValueError("Nenhum resultado de backtest obtido")
        
        # Pegar o único resultado (já que é um teste específico)
        result = results.results[0]
        
        # Extrair métricas principais
        metrics = {
            "total_return_pct": result.total_return_pct,
            "sharpe_ratio": result.sharpe_ratio,
            "max_drawdown_pct": result.max_drawdown_pct,
            "total_trades": result.total_trades,
            "win_rate": result.win_rate,
            "profit_factor": result.profit_factor,
            "decision_rate": result.total_trades / 24.0,  # Trades por hora
            "volatility": result.volatility,
            "calmar_ratio": result.calmar_ratio,
            "execution_time": result.execution_time_seconds,
            "timestamp": datetime.now().isoformat(),
            "parameters": self.production_params
        }
        
        logger.info(f"📈 Métricas atuais:")
        logger.info(f"   - Return: {metrics['total_return_pct']:.2%}")
        logger.info(f"   - Sharpe: {metrics['sharpe_ratio']:.3f}")
        logger.info(f"   - Drawdown: {metrics['max_drawdown_pct']:.2%}")
        logger.info(f"   - Trades: {metrics['total_trades']}")
        logger.info(f"   - Decision Rate: {metrics['decision_rate']:.2f} trades/h")
        
        return metrics
    
    def _load_baseline(self) -> Optional[Dict[str, Any]]:
        """Carrega métricas baseline do arquivo."""
        
        if not self.baseline_file.exists():
            logger.warning("⚠️ Arquivo baseline não encontrado - primeira execução?")
            return None
        
        try:
            with open(self.baseline_file, 'r') as f:
                baseline = json.load(f)
            
            logger.info(f"📋 Baseline carregado:")
            logger.info(f"   - Return: {baseline.get('total_return_pct', 0):.2%}")
            logger.info(f"   - Sharpe: {baseline.get('sharpe_ratio', 0):.3f}")
            logger.info(f"   - Decision Rate: {baseline.get('decision_rate', 0):.2f} trades/h")
            
            return baseline
            
        except Exception as e:
            logger.error(f"❌ Erro ao carregar baseline: {e}")
            return None
    
    def _compare_metrics(self, current: Dict[str, Any], baseline: Optional[Dict[str, Any]]) -> bool:
        """Compara métricas atuais com baseline e detecta regressões."""
        
        if baseline is None:
            logger.info("ℹ️ Sem baseline para comparação - estabelecendo novo baseline")
            self._save_baseline(current)
            return False
        
        regression_detected = False
        issues = []
        
        # Métricas críticas para comparação
        critical_metrics = {
            "total_return_pct": "Return Total",
            "sharpe_ratio": "Sharpe Ratio", 
            "decision_rate": "Decision Rate"
        }
        
        for metric_key, metric_name in critical_metrics.items():
            current_value = current.get(metric_key, 0)
            baseline_value = baseline.get(metric_key, 0)
            
            if baseline_value == 0:
                continue  # Skip division by zero
            
            # Calcular degradação percentual
            degradation = (baseline_value - current_value) / abs(baseline_value)
            
            if degradation > self.threshold:
                regression_detected = True
                issues.append(f"{metric_name}: {degradation:.1%} degradação (limite: {self.threshold:.1%})")
                logger.error(f"❌ Regressão detectada em {metric_name}: {degradation:.1%}")
            else:
                logger.info(f"✅ {metric_name}: {degradation:.1%} (OK)")
        
        # Verificar drawdown (inverso - aumento é ruim)
        current_dd = abs(current.get("max_drawdown_pct", 0))
        baseline_dd = abs(baseline.get("max_drawdown_pct", 0))
        
        if baseline_dd > 0:
            dd_increase = (current_dd - baseline_dd) / baseline_dd
            if dd_increase > self.threshold:
                regression_detected = True
                issues.append(f"Max Drawdown: {dd_increase:.1%} aumento (limite: {self.threshold:.1%})")
                logger.error(f"❌ Regressão detectada em Max Drawdown: {dd_increase:.1%}")
            else:
                logger.info(f"✅ Max Drawdown: {dd_increase:.1%} (OK)")
        
        if regression_detected:
            logger.error(f"🚨 REGRESSÃO DETECTADA! Issues: {'; '.join(issues)}")
        else:
            logger.info("✅ Nenhuma regressão detectada")
        
        return regression_detected
    
    def _save_baseline(self, metrics: Dict[str, Any]):
        """Salva métricas como novo baseline."""
        
        try:
            with open(self.baseline_file, 'w', encoding='utf-8') as f:
                json.dump(metrics, f, indent=2)
            logger.info(f"💾 Novo baseline salvo em {self.baseline_file}")
        except Exception as e:
            logger.error(f"❌ Erro ao salvar baseline: {e}")
    
    def _create_regression_flag(self):
        """Cria flag indicando regressão detectada."""
        flag_file = self.output_dir / "regression_detected.flag"
        flag_file.write_text(f"Regression detected at {datetime.now().isoformat()}", encoding='utf-8')
        logger.info(f"🚩 Flag de regressão criada: {flag_file}")
    
    async def _generate_reports(self, current: Dict[str, Any], baseline: Optional[Dict[str, Any]], regression: bool):
        """Gera relatórios detalhados."""
        
        # Salvar métricas atuais
        current_file = self.output_dir / "current_metrics.json"
        with open(current_file, 'w', encoding='utf-8') as f:
            json.dump(current, f, indent=2)
        
        # Gerar relatório markdown
        report_file = self.output_dir / "performance_report.md"
        
        status_emoji = "❌" if regression else "✅"
        status_text = "REGRESSÃO DETECTADA" if regression else "SEM REGRESSÃO"
        
        report_content = f"""# QUALIA Non-Regression Test Report

## Status: {status_emoji} {status_text}

**Data:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}
**Threshold:** {self.threshold:.1%}

## Métricas Atuais

| Métrica | Valor |
|---------|-------|
| Return Total | {current.get('total_return_pct', 0):.2%} |
| Sharpe Ratio | {current.get('sharpe_ratio', 0):.3f} |
| Max Drawdown | {current.get('max_drawdown_pct', 0):.2%} |
| Total Trades | {current.get('total_trades', 0)} |
| Decision Rate | {current.get('decision_rate', 0):.2f} trades/h |
| Win Rate | {current.get('win_rate', 0):.1%} |
| Profit Factor | {current.get('profit_factor', 0):.2f} |

## Parâmetros Utilizados

- **Price Amplification:** {current.get('parameters', {}).get('price_amplification', 'N/A')}
- **News Amplification:** {current.get('parameters', {}).get('news_amplification', 'N/A')}
- **Min Confidence:** {current.get('parameters', {}).get('min_confidence', 'N/A')}

"""
        
        if baseline:
            report_content += f"""## Comparação com Baseline

| Métrica | Atual | Baseline | Diferença |
|---------|-------|----------|-----------|
| Return Total | {current.get('total_return_pct', 0):.2%} | {baseline.get('total_return_pct', 0):.2%} | {((current.get('total_return_pct', 0) - baseline.get('total_return_pct', 0)) / abs(baseline.get('total_return_pct', 1)) * 100):.1f}% |
| Sharpe Ratio | {current.get('sharpe_ratio', 0):.3f} | {baseline.get('sharpe_ratio', 0):.3f} | {((current.get('sharpe_ratio', 0) - baseline.get('sharpe_ratio', 0)) / abs(baseline.get('sharpe_ratio', 1)) * 100):.1f}% |
| Decision Rate | {current.get('decision_rate', 0):.2f} | {baseline.get('decision_rate', 0):.2f} | {((current.get('decision_rate', 0) - baseline.get('decision_rate', 0)) / abs(baseline.get('decision_rate', 1)) * 100):.1f}% |

"""
        else:
            report_content += "\n## Baseline\n\nNenhum baseline disponível - primeira execução.\n"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"📋 Relatório gerado: {report_file}")


async def main():
    """Função principal."""
    
    parser = argparse.ArgumentParser(description="QUALIA Non-Regression Test")
    parser.add_argument("--threshold", type=float, default=20.0,
                       help="Threshold de regressão em % (default: 20)")
    parser.add_argument("--output-dir", default="results/non_regression",
                       help="Diretório de saída (default: results/non_regression)")
    parser.add_argument("--verbose", action="store_true",
                       help="Logging verboso")
    
    args = parser.parse_args()
    
    # Configurar logging
    level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("🎯 Iniciando QUALIA Non-Regression Test")
    
    try:
        tester = NonRegressionTester(
            output_dir=args.output_dir,
            threshold=args.threshold
        )
        
        results = await tester.run_test()
        
        if results["regression_detected"]:
            logger.error("🚨 TESTE FALHOU: Regressão de performance detectada!")
            sys.exit(1)
        else:
            logger.info("✅ TESTE PASSOU: Nenhuma regressão detectada")
            sys.exit(0)
            
    except Exception as e:
        logger.error(f"❌ Erro crítico: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
