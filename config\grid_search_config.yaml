# Configuração do Grid Search - Etapa C
# YAA IMPLEMENTATION: Configurações para backtest offline de hiperparâmetros

# Parâmetros do Grid Search
grid_search:
  # Ranges dos hiperparâmetros principais
  hyperparams:
    price_amplification:
      min: 1.0
      max: 10.0
      steps: 10
      description: "Multiplicador para eventos de preço (1.0 = conservador, 10.0 = agressivo)"
    
    news_amplification:
      min: 1.0
      max: 10.0
      steps: 10
      description: "Multiplicador para eventos de notícias (1.0 = conservador, 10.0 = agressivo)"
    
    min_confidence:
      min: 0.3
      max: 0.8
      steps: 6
      description: "Confiança mínima para executar sinais (0.3 = permissivo, 0.8 = rigoroso)"
  
  # Configurações de backtest
  backtest:
    duration_days: 90
    initial_capital: 10000.0
    symbols:
      - "BTC/USDT"
      - "ETH/USDT"
    timeframe: "1h"
    
    # Filtros de qualidade
    min_trades_required: 10
    max_drawdown_limit: 0.5  # 50%
    min_win_rate: 0.2        # 20%
    
  # Configurações de execução
  execution:
    max_workers: 4
    cache_results: true
    save_detailed_results: true
    chunk_size_multiplier: 2  # chunks = workers * multiplier
    
    # Timeouts e limites
    single_backtest_timeout: 300  # 5 minutos por backtest
    total_timeout: 7200           # 2 horas total
    
  # Configurações de saída
  output:
    base_directory: "results/grid_search"
    save_json: true
    save_csv: true
    save_plots: true
    
    # Formato dos arquivos
    filename_template: "grid_search_{timestamp}"
    include_metadata: true
    compress_results: false

# Configurações específicas por perfil
profiles:
  # Perfil rápido para testes
  quick_test:
    hyperparams:
      price_amplification:
        min: 2.0
        max: 8.0
        steps: 4
      news_amplification:
        min: 2.0
        max: 8.0
        steps: 4
      min_confidence:
        min: 0.4
        max: 0.7
        steps: 4
    backtest:
      duration_days: 30
      symbols: ["BTC/USDT"]
    execution:
      max_workers: 2
  
  # Perfil completo (Etapa C)
  full_etapa_c:
    hyperparams:
      price_amplification:
        min: 1.0
        max: 10.0
        steps: 10
      news_amplification:
        min: 1.0
        max: 10.0
        steps: 10
      min_confidence:
        min: 0.3
        max: 0.8
        steps: 6
    backtest:
      duration_days: 90
      symbols: ["BTC/USDT", "ETH/USDT", "ADA/USDT"]
    execution:
      max_workers: 8
  
  # Perfil de produção (mais rigoroso)
  production:
    hyperparams:
      price_amplification:
        min: 2.0
        max: 8.0
        steps: 7
      news_amplification:
        min: 2.0
        max: 8.0
        steps: 7
      min_confidence:
        min: 0.4
        max: 0.8
        steps: 5
    backtest:
      duration_days: 180  # 6 meses
      symbols: ["BTC/USDT", "ETH/USDT", "ADA/USDT", "SOL/USDT"]
      min_trades_required: 20
      max_drawdown_limit: 0.3  # 30%
      min_win_rate: 0.3        # 30%
    execution:
      max_workers: 12

# Métricas de avaliação
metrics:
  # Pesos para score composto
  weights:
    sharpe_ratio: 0.4
    total_return: 0.3
    max_drawdown: 0.2  # Peso negativo (menor é melhor)
    win_rate: 0.1
  
  # Thresholds para classificação
  thresholds:
    excellent:
      sharpe_ratio: 2.0
      total_return: 0.3
      max_drawdown: 0.1
      win_rate: 0.6
    
    good:
      sharpe_ratio: 1.0
      total_return: 0.15
      max_drawdown: 0.2
      win_rate: 0.5
    
    acceptable:
      sharpe_ratio: 0.5
      total_return: 0.05
      max_drawdown: 0.3
      win_rate: 0.4

# Configurações de análise
analysis:
  # Correlações a investigar
  correlations:
    - ["price_amplification", "sharpe_ratio"]
    - ["news_amplification", "total_return"]
    - ["min_confidence", "win_rate"]
    - ["min_confidence", "max_drawdown"]
  
  # Visualizações a gerar
  plots:
    - type: "heatmap"
      x_axis: "price_amplification"
      y_axis: "news_amplification"
      color: "sharpe_ratio"
      title: "Sharpe Ratio por Amplificação"
    
    - type: "scatter"
      x_axis: "total_return"
      y_axis: "max_drawdown"
      color: "min_confidence"
      title: "Retorno vs Drawdown"
    
    - type: "box"
      group_by: "min_confidence"
      metric: "sharpe_ratio"
      title: "Distribuição Sharpe por Confiança"
  
  # Análises estatísticas
  statistics:
    - "descriptive"      # Estatísticas descritivas
    - "correlation"      # Matriz de correlação
    - "regression"       # Regressão múltipla
    - "clustering"       # Agrupamento de resultados

# Configurações de cache
cache:
  enabled: true
  directory: "cache/grid_search"
  ttl_hours: 24
  
  # Estratégias de cache
  strategies:
    market_data: "persistent"    # Cache persistente para dados de mercado
    backtest_results: "memory"   # Cache em memória para resultados
    
  # Limpeza automática
  auto_cleanup:
    enabled: true
    max_size_gb: 5.0
    cleanup_interval_hours: 6

# Configurações de logging
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # Logs específicos
  grid_search_log: "logs/grid_search.log"
  backtest_log: "logs/backtest.log"
  performance_log: "logs/performance.log"
  
  # Rotação de logs
  rotation:
    max_size_mb: 100
    backup_count: 5

# Configurações de monitoramento
monitoring:
  # Métricas em tempo real
  real_time_metrics:
    - "progress_percentage"
    - "backtests_per_second"
    - "estimated_time_remaining"
    - "memory_usage"
    - "cpu_usage"
  
  # Alertas
  alerts:
    high_failure_rate: 0.2      # 20% de falhas
    slow_execution: 10.0        # Mais de 10s por backtest
    high_memory_usage: 0.8      # 80% da memória
  
  # Relatórios
  reports:
    progress_interval: 60       # A cada minuto
    summary_interval: 300       # A cada 5 minutos
