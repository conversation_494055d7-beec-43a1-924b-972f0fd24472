/* Revisado em 2025-06-13 por Codex */
.circuit-container {
padding: 30px;
background: rgba(10, 15, 25, 0.8);
border-radius: 8px;
margin-top: 20px;
}

.circuit-controls {
display: flex;
flex-wrap: wrap;
gap: 20px;
margin-bottom: 20px;
align-items: center;
}

.control-group {
display: flex;
flex-direction: column;
gap: 5px;
}

.control-group label {
font-size: 0.9em;
color: rgba(220, 230, 255, 0.8);
}

.circuit-visualization-area {
min-height: 400px;
width: 100%;
background: rgba(0, 5, 15, 0.5);
border-radius: 4px;
padding: 15px;
overflow: auto;
font-family: monospace;
white-space: pre;
color: #e0e0ff;
}

.circuit-text {
white-space: pre;
overflow-x: auto;
}

.loading-indicator {
display: flex;
align-items: center;
justify-content: center;
height: 100px;
color: rgba(200, 210, 255, 0.8);
}

.error-message {
color: #ff6b6b;
padding: 20px;
}

.success-message {
color: #6bff6b;
padding: 20px;
}

.circuit-info {
margin-top: 20px;
padding: 15px;
background: rgba(20, 30, 50, 0.6);
border-radius: 4px;
}

.real-circuit-checkbox {
display: flex;
align-items: center;
gap: 8px;
margin-left: 20px;
}

.real-circuit-checkbox input[type="checkbox"] {
width: 18px;
height: 18px;
}

.trading-analysis-result {
margin-top: 20px;
padding: 15px;
background: rgba(30, 40, 60, 0.6);
border-radius: 4px;
font-family: monospace;
}

.trading-analysis-result h4 {
margin-top: 0;
margin-bottom: 10px;
color: #88aaff;
}

.back-button {
margin-bottom: 20px;
display: inline-block;
}
