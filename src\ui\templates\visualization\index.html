<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>QUALIA Consciousness Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: sans-serif; margin: 20px; background-color: #f4f4f4; color: #333; }
        .container { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .metric-card { background-color: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h2 { color: #555; }
    </style>
</head>
<body>
    <h1>QUALIA Consciousness Dashboard</h1>
    <div class="container">
        <div class="metric-card">
            <h2>Universe Metrics</h2>
            <pre id="universe-metrics">Loading...</pre>
        </div>
        <div class="metric-card">
            <h2>Statevector Probabilities (Top States)</h2>
            <canvas id="statevector-chart"></canvas>
        </div>
        <div class="metric-card">
            <h2>Consciousness Metrics (if applicable)</h2>
            <pre id="consciousness-metrics">Loading...</pre>
        </div>
    </div>

    <script>
        let statevectorChart;
        async function fetchData() {
            try {
                const response = await fetch('/visualization/api/dashboard_data');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();

                document.getElementById('universe-metrics').textContent = JSON.stringify(data.universe_metrics || {}, null, 2);
                document.getElementById('consciousness-metrics').textContent = JSON.stringify(data.consciousness_metrics || data.last_qast_cycle || {}, null, 2);

                if (data.statevector_probabilities && data.statevector_basis_states) {
                    const chartData = {
                        labels: data.statevector_basis_states,
                        datasets: [{
                            label: 'Probability',
                            data: data.statevector_probabilities,
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 1
                        }]
                    };
                    if (statevectorChart) {
                        statevectorChart.data = chartData;
                        statevectorChart.update();
                    } else {
                        const ctx = document.getElementById('statevector-chart').getContext('2d');
                        statevectorChart = new Chart(ctx, {
                            type: 'bar',
                            data: chartData,
                            options: {
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        title: { display: true, text: 'Probability' }
                                    },
                                    x: {
                                        title: { display: true, text: 'Basis State' }
                                    }
                                }
                            }
                        });
                    }
                }
            } catch (error) {
                console.error('Error fetching dashboard data:', error);
                document.getElementById('universe-metrics').textContent = `Error: ${error.message}`;
            }
        }
        setInterval(fetchData, 5000);
        fetchData();
    </script>
</body>
</html>
