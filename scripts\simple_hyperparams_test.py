#!/usr/bin/env python3
"""
Simple test for hyperparameters validation system.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_basic_validation():
    """Test basic hyperparameter validation"""
    print("Testing basic hyperparameter validation...")
    
    try:
        from src.qualia.config.hyperparams_validator import HyperparametersValidator
        
        validator = HyperparametersValidator()
        
        # Test valid parameters
        valid_params = {
            'price_amplification': 5.0,
            'news_amplification': 4.0,
            'min_confidence': 0.6,
            'pattern_threshold': 0.3
        }
        
        result = validator.validate_hyperparameters(valid_params)
        
        print(f"Validation result: {result.is_valid}")
        print(f"Errors: {len(result.errors)}")
        print(f"Warnings: {len(result.warnings)}")
        print(f"Critical violations: {len(result.critical_violations)}")
        
        if result.is_valid:
            print("✅ Basic validation working")
            return True
        else:
            print("❌ Basic validation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error in basic validation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_production_params():
    """Test production-optimized parameters"""
    print("\nTesting production-optimized parameters...")
    
    try:
        from src.qualia.config.hyperparams_validator import HyperparametersValidator
        
        validator = HyperparametersValidator()
        
        # Production parameters from logs
        production_params = {
            'price_amplification': 1.0,
            'news_amplification': 10.0,
            'min_confidence': 0.37,
            'pattern_threshold': 0.3
        }
        
        result = validator.validate_hyperparameters(production_params)
        
        print(f"Production validation result: {result.is_valid}")
        print(f"Warnings: {len(result.warnings)}")
        print(f"Recommendations: {len(result.recommendations)}")
        
        # Print warnings and recommendations
        for warning in result.warnings:
            print(f"  Warning: {warning}")
        
        for rec in result.recommendations:
            print(f"  Recommendation: {rec}")
        
        if result.is_valid:
            print("✅ Production parameters validated")
            return True
        else:
            print("❌ Production parameters failed validation")
            return False
            
    except Exception as e:
        print(f"❌ Error in production validation: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run simple tests"""
    print("🧪 Simple Hyperparameters Validation Test")
    print("=" * 50)
    
    try:
        # Test basic validation
        basic_ok = test_basic_validation()
        
        # Test production parameters
        production_ok = test_production_params()
        
        if basic_ok and production_ok:
            print("\n✅ All basic tests passed!")
            return True
        else:
            print("\n❌ Some tests failed!")
            return False
            
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
