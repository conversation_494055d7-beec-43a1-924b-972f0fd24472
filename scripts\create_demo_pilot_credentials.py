#!/usr/bin/env python3
"""
Create Demo Pilot Credentials
P-02.3: Deploy Piloto com Capital Limitado

Create demo credentials for pilot validation (NOT for real trading)
"""

import os
import json
from pathlib import Path
from cryptography.fernet import Fernet
import base64

def create_demo_credentials():
    """Create demo credentials for validation purposes"""
    print("🔐 CREATING DEMO PILOT CREDENTIALS")
    print("=" * 50)
    print("⚠️  WARNING: These are DEMO credentials only")
    print("⚠️  NOT for real trading - validation purposes only")
    print("=" * 50)
    
    # Create demo credentials structure
    demo_credentials = {
        "environment": "pilot",
        "exchange": "kucoin",
        "api_url": "https://api.kucoin.com",
        "ws_url": "wss://ws-api.kucoin.com/endpoint",
        "credentials": {
            "api_key": "DEMO_API_KEY_12345678901234567890",
            "api_secret": "DEMO_API_SECRET_12345678901234567890ABCDEF",
            "api_passphrase": "DEMO_PASSPHRASE_123"
        },
        "limits": {
            "max_capital_usd": 1000.0,
            "max_position_usd": 20.0,
            "max_daily_trades": 10
        },
        "created_at": "2025-07-07T14:00:00Z",
        "version": "P-02.3",
        "demo": True,
        "warning": "DEMO CREDENTIALS - NOT FOR REAL TRADING"
    }
    
    # Generate master key
    master_key = Fernet.generate_key()
    
    # Encrypt credentials
    f = Fernet(master_key)
    credentials_json = json.dumps(demo_credentials, indent=2)
    encrypted = f.encrypt(credentials_json.encode())
    encrypted_credentials = base64.b64encode(encrypted).decode()
    
    # Save files
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    # Save master key
    master_key_file = config_dir / ".pilot_master.key"
    with open(master_key_file, 'wb') as f:
        f.write(master_key)
    
    # Save encrypted credentials
    credentials_file = config_dir / ".pilot_credentials"
    with open(credentials_file, 'w') as f:
        f.write(encrypted_credentials)
    
    # Set restrictive permissions
    os.chmod(master_key_file, 0o600)
    os.chmod(credentials_file, 0o600)
    
    # Create environment file
    env_file = config_dir / ".pilot_env"
    env_content = f"""# QUALIA Demo Pilot Environment Variables
# P-02.3: Deploy Piloto com Capital Limitado
# WARNING: DEMO CREDENTIALS ONLY

QUALIA_ENV=pilot
QUALIA_CONFIG=config/pilot_config.yaml
QUALIA_CREDENTIALS=config/.pilot_credentials
QUALIA_MASTER_KEY=config/.pilot_master.key
QUALIA_LOG_LEVEL=INFO
QUALIA_CAPITAL_LIMIT=1000
QUALIA_RISK_LEVEL=ULTRA_CONSERVATIVE
QUALIA_DEMO_MODE=true

# Trading Limits
QUALIA_MAX_POSITION_USD=20
QUALIA_MAX_DAILY_TRADES=10
QUALIA_EMERGENCY_STOP_USD=50

# Monitoring
QUALIA_MONITORING_LEVEL=INTENSIVE
QUALIA_ALERT_LEVEL=HIGH
"""
    
    with open(env_file, 'w') as f:
        f.write(env_content)
    
    os.chmod(env_file, 0o600)
    
    print("✅ Demo pilot credentials created successfully!")
    print(f"📁 Master key: {master_key_file}")
    print(f"📁 Credentials: {credentials_file}")
    print(f"📁 Environment: {env_file}")
    print("🔒 Files secured with 600 permissions")
    print()
    print("⚠️  IMPORTANT: These are DEMO credentials")
    print("⚠️  Replace with real credentials for actual trading")
    
    return True

if __name__ == '__main__':
    create_demo_credentials()
