"""Adaptive cache system with dynamic TTL based on market volatility and data patterns.

This module provides sophisticated caching mechanisms that adapt to market conditions:
- Dynamic TTL based on market volatility
- Frequency-based cache invalidation
- Multi-layer cache architecture
- Intelligent prefetching and warming
- Quality scoring for cached data
- Comprehensive cache analytics and monitoring
"""

from __future__ import annotations

import asyncio
import time
import hashlib
import json
import statistics
from typing import Any, Dict, List, Optional, Callable, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import weakref

from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class CacheLayer(Enum):
    """Cache layer types for multi-layer architecture."""
    L1_MEMORY = "l1_memory"
    L2_REDIS = "l2_redis"
    L3_DISK = "l3_disk"


class DataQuality(Enum):
    """Data quality levels for cached entries."""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    STALE = "stale"


@dataclass
class CacheEntry:
    """Enhanced cache entry with metadata and quality tracking."""
    key: str
    value: Any
    created_at: float
    last_accessed: float
    access_count: int = 0
    ttl: float = 300.0
    quality: DataQuality = DataQuality.GOOD
    volatility_factor: float = 1.0
    update_frequency: float = 0.0
    source: str = "unknown"
    size_bytes: int = 0
    
    @property
    def age(self) -> float:
        """Get age of cache entry in seconds."""
        return time.time() - self.created_at
    
    @property
    def time_since_access(self) -> float:
        """Get time since last access in seconds."""
        return time.time() - self.last_accessed
    
    @property
    def is_expired(self) -> bool:
        """Check if cache entry is expired."""
        return self.age > self.ttl
    
    @property
    def freshness_score(self) -> float:
        """Calculate freshness score (0-1, higher is fresher)."""
        if self.ttl <= 0:
            return 0.0
        return max(0.0, 1.0 - (self.age / self.ttl))


@dataclass
class VolatilityMetrics:
    """Market volatility metrics for adaptive TTL calculation."""
    symbol: str
    price_changes: deque = field(default_factory=lambda: deque(maxlen=100))
    volume_changes: deque = field(default_factory=lambda: deque(maxlen=100))
    update_intervals: deque = field(default_factory=lambda: deque(maxlen=50))
    last_update: float = 0.0
    
    @property
    def price_volatility(self) -> float:
        """Calculate price volatility (standard deviation of price changes)."""
        if len(self.price_changes) < 2:
            return 0.0
        return statistics.stdev(self.price_changes)
    
    @property
    def volume_volatility(self) -> float:
        """Calculate volume volatility."""
        if len(self.volume_changes) < 2:
            return 0.0
        return statistics.stdev(self.volume_changes)
    
    @property
    def update_frequency(self) -> float:
        """Calculate average update frequency (updates per second)."""
        if len(self.update_intervals) < 2:
            return 0.0
        avg_interval = statistics.mean(self.update_intervals)
        return 1.0 / max(0.1, avg_interval)
    
    @property
    def combined_volatility(self) -> float:
        """Calculate combined volatility score."""
        price_vol = min(self.price_volatility * 100, 10.0)  # Normalize to 0-10
        volume_vol = min(self.volume_volatility * 100, 10.0)
        freq_factor = min(self.update_frequency, 5.0)  # Cap at 5 updates/sec
        
        return (price_vol * 0.4 + volume_vol * 0.3 + freq_factor * 0.3) / 10.0


class AdaptiveCacheManager:
    """Advanced cache manager with dynamic TTL and multi-layer architecture.
    
    Features:
    - Dynamic TTL based on market volatility
    - Multi-layer cache (L1: memory, L2: Redis, L3: disk)
    - Intelligent prefetching and cache warming
    - Quality-based cache invalidation
    - Comprehensive analytics and monitoring
    """
    
    def __init__(
        self,
        name: str = "adaptive_cache",
        base_ttl: float = 300.0,
        min_ttl: float = 5.0,
        max_ttl: float = 3600.0,
        max_memory_entries: int = 10000,
        enable_l2_cache: bool = False,
        enable_prefetching: bool = True,
        volatility_sensitivity: float = 1.0,
        quality_threshold: float = 0.7,
    ):
        self.name = name
        self.base_ttl = base_ttl
        self.min_ttl = min_ttl
        self.max_ttl = max_ttl
        self.max_memory_entries = max_memory_entries
        self.enable_l2_cache = enable_l2_cache
        self.enable_prefetching = enable_prefetching
        self.volatility_sensitivity = volatility_sensitivity
        self.quality_threshold = quality_threshold
        
        # Cache storage layers
        self.l1_cache: Dict[str, CacheEntry] = {}
        self.l2_cache: Optional[Any] = None  # Redis client would go here
        
        # Volatility tracking
        self.volatility_metrics: Dict[str, VolatilityMetrics] = {}
        
        # Access patterns and analytics
        self.access_patterns: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.hit_rates: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Background tasks
        self.cleanup_task: Optional[asyncio.Task] = None
        self.prefetch_task: Optional[asyncio.Task] = None
        self.analytics_task: Optional[asyncio.Task] = None
        self.warming_task: Optional[asyncio.Task] = None
        
        # Statistics
        self.stats = {
            'l1_hits': 0,
            'l1_misses': 0,
            'l2_hits': 0,
            'l2_misses': 0,
            'entries_created': 0,
            'entries_expired': 0,
            'entries_evicted': 0,
            'ttl_adaptations': 0,
            'prefetch_operations': 0,
            'quality_downgrades': 0
        }
        
        # Locks for thread safety
        self._lock = asyncio.Lock()
        self._is_running = False

    async def start(self) -> None:
        """Start the cache manager and background tasks."""
        if self._is_running:
            return
        
        self._is_running = True
        
        # Start background tasks
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        if self.enable_prefetching:
            self.prefetch_task = asyncio.create_task(self._prefetch_loop())

        self.analytics_task = asyncio.create_task(self._analytics_loop())
        self.warming_task = asyncio.create_task(self._cache_warming_loop())
        
        logger.info(f"Adaptive cache manager '{self.name}' started")

    async def stop(self) -> None:
        """Stop the cache manager and background tasks."""
        if not self._is_running:
            return
        
        self._is_running = False
        
        # Cancel background tasks
        for task in [self.cleanup_task, self.prefetch_task, self.analytics_task, self.warming_task]:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        logger.info(f"Adaptive cache manager '{self.name}' stopped")

    async def get(
        self, 
        key: str, 
        symbol: Optional[str] = None,
        quality_threshold: Optional[float] = None
    ) -> Optional[Any]:
        """Get value from cache with quality checking.
        
        Parameters
        ----------
        key : str
            Cache key
        symbol : str, optional
            Trading symbol for volatility-based decisions
        quality_threshold : float, optional
            Minimum quality threshold for returned data
            
        Returns
        -------
        Any or None
            Cached value if found and meets quality requirements
        """
        async with self._lock:
            # Try L1 cache first
            if key in self.l1_cache:
                entry = self.l1_cache[key]
                
                # Check if expired
                if entry.is_expired:
                    await self._remove_entry(key, CacheLayer.L1_MEMORY)
                    self.stats['l1_misses'] += 1
                else:
                    # Check quality threshold
                    threshold = quality_threshold or self.quality_threshold
                    if entry.freshness_score >= threshold:
                        # Update access metrics
                        entry.last_accessed = time.time()
                        entry.access_count += 1
                        
                        self.stats['l1_hits'] += 1
                        self._record_access_pattern(key, True)
                        
                        logger.debug(f"L1 cache hit for key '{key}' (quality: {entry.freshness_score:.2f})")
                        return entry.value
                    else:
                        logger.debug(f"L1 cache entry for '{key}' below quality threshold ({entry.freshness_score:.2f} < {threshold:.2f})")
            
            # Try L2 cache if enabled
            if self.enable_l2_cache and self.l2_cache:
                l2_value = await self._get_from_l2(key)
                if l2_value is not None:
                    self.stats['l2_hits'] += 1
                    self._record_access_pattern(key, True)
                    
                    # Promote to L1 cache
                    await self._promote_to_l1(key, l2_value, symbol)
                    return l2_value
                else:
                    self.stats['l2_misses'] += 1
            else:
                self.stats['l1_misses'] += 1
            
            self._record_access_pattern(key, False)
            return None

    async def set(
        self,
        key: str,
        value: Any,
        symbol: Optional[str] = None,
        ttl: Optional[float] = None,
        quality: DataQuality = DataQuality.GOOD,
        source: str = "unknown"
    ) -> None:
        """Set value in cache with adaptive TTL.
        
        Parameters
        ----------
        key : str
            Cache key
        value : Any
            Value to cache
        symbol : str, optional
            Trading symbol for volatility-based TTL calculation
        ttl : float, optional
            Custom TTL (overrides adaptive calculation)
        quality : DataQuality
            Quality level of the data
        source : str
            Source of the data
        """
        async with self._lock:
            # Calculate adaptive TTL
            if ttl is None:
                ttl = await self._calculate_adaptive_ttl(key, symbol, quality)
            
            # Create cache entry
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=time.time(),
                last_accessed=time.time(),
                ttl=ttl,
                quality=quality,
                source=source,
                size_bytes=self._estimate_size(value)
            )
            
            # Update volatility metrics if symbol provided
            if symbol:
                await self._update_volatility_metrics(symbol, value)
                entry.volatility_factor = self.volatility_metrics[symbol].combined_volatility
            
            # Store in L1 cache
            await self._set_in_l1(key, entry)
            
            # Store in L2 cache if enabled
            if self.enable_l2_cache and self.l2_cache:
                await self._set_in_l2(key, entry)
            
            self.stats['entries_created'] += 1
            
            logger.debug(f"Cached '{key}' with TTL {ttl:.1f}s (quality: {quality.value}, source: {source})")

    async def invalidate(self, key: str) -> bool:
        """Invalidate a cache entry.
        
        Parameters
        ----------
        key : str
            Cache key to invalidate
            
        Returns
        -------
        bool
            True if entry was found and invalidated
        """
        async with self._lock:
            found = False
            
            # Remove from L1
            if key in self.l1_cache:
                await self._remove_entry(key, CacheLayer.L1_MEMORY)
                found = True
            
            # Remove from L2 if enabled
            if self.enable_l2_cache and self.l2_cache:
                if await self._remove_from_l2(key):
                    found = True
            
            if found:
                logger.debug(f"Invalidated cache entry '{key}'")
            
            return found

    async def _calculate_adaptive_ttl(
        self, 
        key: str, 
        symbol: Optional[str], 
        quality: DataQuality
    ) -> float:
        """Calculate adaptive TTL based on volatility and other factors."""
        base_ttl = self.base_ttl
        
        # Quality-based adjustment
        quality_multipliers = {
            DataQuality.EXCELLENT: 1.5,
            DataQuality.GOOD: 1.0,
            DataQuality.FAIR: 0.7,
            DataQuality.POOR: 0.4,
            DataQuality.STALE: 0.1
        }
        ttl = base_ttl * quality_multipliers.get(quality, 1.0)
        
        # Volatility-based adjustment
        if symbol and symbol in self.volatility_metrics:
            volatility = self.volatility_metrics[symbol].combined_volatility
            
            # Higher volatility = shorter TTL
            volatility_multiplier = max(0.1, 1.0 - (volatility * self.volatility_sensitivity))
            ttl *= volatility_multiplier
            
            self.stats['ttl_adaptations'] += 1
            
            logger.debug(
                f"Adaptive TTL for '{key}' (symbol: {symbol}): "
                f"{ttl:.1f}s (volatility: {volatility:.3f}, multiplier: {volatility_multiplier:.3f})"
            )
        
        # Access pattern adjustment
        if key in self.access_patterns:
            recent_accesses = len([
                t for t in self.access_patterns[key] 
                if time.time() - t < 300  # Last 5 minutes
            ])
            
            if recent_accesses > 10:  # Frequently accessed
                ttl *= 1.5
            elif recent_accesses < 2:  # Rarely accessed
                ttl *= 0.7
        
        # Ensure bounds
        return max(self.min_ttl, min(ttl, self.max_ttl))

    async def _update_volatility_metrics(self, symbol: str, value: Any) -> None:
        """Update volatility metrics for a symbol."""
        if symbol not in self.volatility_metrics:
            self.volatility_metrics[symbol] = VolatilityMetrics(symbol=symbol)
        
        metrics = self.volatility_metrics[symbol]
        current_time = time.time()
        
        # Extract price and volume if available
        if isinstance(value, dict):
            if 'price' in value or 'last' in value:
                current_price = value.get('price') or value.get('last')
                if current_price and len(metrics.price_changes) > 0:
                    # Calculate price change percentage
                    last_entry = list(self.l1_cache.values())
                    for entry in reversed(last_entry):
                        if (entry.key.endswith(symbol) and 
                            isinstance(entry.value, dict) and 
                            ('price' in entry.value or 'last' in entry.value)):
                            
                            last_price = entry.value.get('price') or entry.value.get('last')
                            if last_price and last_price > 0:
                                price_change = abs(current_price - last_price) / last_price
                                metrics.price_changes.append(price_change)
                                break
            
            if 'volume' in value:
                current_volume = value.get('volume')
                if current_volume and len(metrics.volume_changes) > 0:
                    # Similar logic for volume changes
                    pass  # Simplified for brevity
        
        # Update timing metrics
        if metrics.last_update > 0:
            interval = current_time - metrics.last_update
            metrics.update_intervals.append(interval)
        
        metrics.last_update = current_time

    async def _set_in_l1(self, key: str, entry: CacheEntry) -> None:
        """Set entry in L1 cache with eviction if needed."""
        # Check if we need to evict entries
        if len(self.l1_cache) >= self.max_memory_entries:
            await self._evict_lru_entries(1)
        
        self.l1_cache[key] = entry

    async def _evict_lru_entries(self, count: int) -> None:
        """Evict least recently used entries from L1 cache."""
        if not self.l1_cache:
            return
        
        # Sort by last accessed time
        sorted_entries = sorted(
            self.l1_cache.items(),
            key=lambda x: x[1].last_accessed
        )
        
        for i in range(min(count, len(sorted_entries))):
            key, entry = sorted_entries[i]
            await self._remove_entry(key, CacheLayer.L1_MEMORY)
            self.stats['entries_evicted'] += 1

    async def _remove_entry(self, key: str, layer: CacheLayer) -> None:
        """Remove entry from specified cache layer."""
        if layer == CacheLayer.L1_MEMORY and key in self.l1_cache:
            del self.l1_cache[key]

    async def _cleanup_loop(self) -> None:
        """Background task for cleaning up expired entries."""
        while self._is_running:
            try:
                await asyncio.sleep(60.0)  # Run every minute
                await self._cleanup_expired_entries()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")

    async def _cleanup_expired_entries(self) -> None:
        """Clean up expired entries from all cache layers."""
        async with self._lock:
            expired_keys = []
            
            for key, entry in self.l1_cache.items():
                if entry.is_expired:
                    expired_keys.append(key)
            
            for key in expired_keys:
                await self._remove_entry(key, CacheLayer.L1_MEMORY)
                self.stats['entries_expired'] += 1
            
            if expired_keys:
                logger.debug(f"Cleaned up {len(expired_keys)} expired entries")

    async def _prefetch_loop(self) -> None:
        """Background task for intelligent prefetching."""
        while self._is_running:
            try:
                await asyncio.sleep(30.0)  # Run every 30 seconds
                await self._perform_prefetching()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in prefetch loop: {e}")

    async def _perform_prefetching(self) -> None:
        """Perform intelligent prefetching based on access patterns."""
        async with self._lock:
            prefetch_candidates = await self._identify_prefetch_candidates()

            for candidate in prefetch_candidates:
                try:
                    await self._execute_prefetch(candidate)
                    self.stats['prefetch_operations'] += 1
                except Exception as e:
                    logger.warning(f"Prefetch failed for {candidate['key']}: {e}")

            if prefetch_candidates:
                logger.debug(f"Prefetching completed: {len(prefetch_candidates)} candidates processed")

    async def _identify_prefetch_candidates(self) -> List[Dict[str, Any]]:
        """Identify cache entries that should be prefetched."""
        candidates = []
        current_time = time.time()

        # Analyze access patterns to predict future needs
        for key, access_times in self.access_patterns.items():
            if not access_times:
                continue

            # Check if this key has regular access patterns
            recent_accesses = [t for t in access_times if current_time - t < 3600]  # Last hour

            if len(recent_accesses) >= 3:
                # Calculate access frequency
                time_span = max(recent_accesses) - min(recent_accesses)
                if time_span > 0:
                    frequency = len(recent_accesses) / time_span  # accesses per second

                    # Check if entry is about to expire
                    if key in self.l1_cache:
                        entry = self.l1_cache[key]
                        time_to_expiry = entry.ttl - entry.age

                        # Prefetch if expiring soon and frequently accessed
                        if (time_to_expiry < entry.ttl * 0.2 and  # Less than 20% TTL remaining
                            frequency > 0.001):  # At least 1 access per 1000 seconds

                            candidates.append({
                                'key': key,
                                'frequency': frequency,
                                'time_to_expiry': time_to_expiry,
                                'priority': frequency * (1.0 / max(time_to_expiry, 1.0))
                            })

        # Sort by priority (higher is more important)
        candidates.sort(key=lambda x: x['priority'], reverse=True)

        # Return top candidates (limit to prevent overload)
        return candidates[:10]

    async def _execute_prefetch(self, candidate: Dict[str, Any]) -> None:
        """Execute prefetch operation for a candidate."""
        key = candidate['key']

        # This is where you would implement the actual data fetching logic
        # For now, we'll simulate by extending the TTL of frequently accessed items
        if key in self.l1_cache:
            entry = self.l1_cache[key]

            # Extend TTL for frequently accessed items
            if candidate['frequency'] > 0.01:  # More than 1 access per 100 seconds
                extension_factor = min(2.0, 1.0 + candidate['frequency'] * 100)
                new_ttl = entry.ttl * extension_factor
                new_ttl = min(new_ttl, self.max_ttl)

                # Create refreshed entry
                refreshed_entry = CacheEntry(
                    key=entry.key,
                    value=entry.value,
                    created_at=time.time(),  # Reset creation time
                    last_accessed=entry.last_accessed,
                    access_count=entry.access_count,
                    ttl=new_ttl,
                    quality=entry.quality,
                    volatility_factor=entry.volatility_factor,
                    update_frequency=candidate['frequency'],
                    source=f"prefetch_refresh_{entry.source}",
                    size_bytes=entry.size_bytes
                )

                self.l1_cache[key] = refreshed_entry

                logger.debug(
                    f"Prefetch refresh for '{key}': TTL extended to {new_ttl:.1f}s "
                    f"(frequency: {candidate['frequency']:.4f})"
                )

    async def _cache_warming_loop(self) -> None:
        """Background task for proactive cache warming."""
        while self._is_running:
            try:
                await asyncio.sleep(120.0)  # Run every 2 minutes
                await self._perform_cache_warming()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cache warming loop: {e}")

    async def _perform_cache_warming(self) -> None:
        """Perform proactive cache warming for critical data."""
        async with self._lock:
            warming_candidates = await self._identify_warming_candidates()

            for candidate in warming_candidates:
                try:
                    await self._execute_cache_warming(candidate)
                except Exception as e:
                    logger.warning(f"Cache warming failed for {candidate['key']}: {e}")

            if warming_candidates:
                logger.debug(f"Cache warming completed: {len(warming_candidates)} entries warmed")

    async def _identify_warming_candidates(self) -> List[Dict[str, Any]]:
        """Identify entries that should be warmed before expiration."""
        candidates = []
        current_time = time.time()
        warming_threshold = 0.15  # Warm when 15% of TTL remains

        for key, entry in self.l1_cache.items():
            time_remaining = entry.ttl - entry.age
            warming_point = entry.ttl * warming_threshold

            # Check if entry needs warming
            if (time_remaining <= warming_point and
                time_remaining > 0 and
                entry.access_count > 0):  # Only warm accessed entries

                # Calculate warming priority
                access_frequency = entry.access_count / max(entry.age, 1.0)
                recency_factor = 1.0 / max(entry.time_since_access, 1.0)
                urgency_factor = warming_point / max(time_remaining, 0.1)

                priority = access_frequency * recency_factor * urgency_factor

                candidates.append({
                    'key': key,
                    'entry': entry,
                    'time_remaining': time_remaining,
                    'priority': priority,
                    'access_frequency': access_frequency
                })

        # Sort by priority (higher is more urgent)
        candidates.sort(key=lambda x: x['priority'], reverse=True)

        # Return top candidates (limit to prevent system overload)
        return candidates[:5]

    async def _execute_cache_warming(self, candidate: Dict[str, Any]) -> None:
        """Execute cache warming for a candidate entry."""
        key = candidate['key']
        entry = candidate['entry']

        # Strategy 1: Extend TTL for high-value entries
        if candidate['access_frequency'] > 0.1:  # Frequently accessed
            # Calculate new TTL based on access patterns
            access_boost = min(2.0, 1.0 + candidate['access_frequency'] * 10)
            new_ttl = entry.ttl * access_boost
            new_ttl = min(new_ttl, self.max_ttl)

            # Create warmed entry
            warmed_entry = CacheEntry(
                key=entry.key,
                value=entry.value,
                created_at=time.time(),  # Reset creation time
                last_accessed=entry.last_accessed,
                access_count=entry.access_count,
                ttl=new_ttl,
                quality=entry.quality,
                volatility_factor=entry.volatility_factor,
                update_frequency=candidate['access_frequency'],
                source=f"warmed_{entry.source}",
                size_bytes=entry.size_bytes
            )

            self.l1_cache[key] = warmed_entry

            logger.debug(
                f"Cache warming for '{key}': TTL extended to {new_ttl:.1f}s "
                f"(access_freq: {candidate['access_frequency']:.4f}, priority: {candidate['priority']:.2f})"
            )

        # Strategy 2: Quality upgrade for stable entries
        elif entry.quality in [DataQuality.FAIR, DataQuality.GOOD]:
            if entry.access_count > 5 and entry.age > 60:  # Stable and accessed
                upgraded_quality = DataQuality.GOOD if entry.quality == DataQuality.FAIR else DataQuality.EXCELLENT

                entry.quality = upgraded_quality
                # Recalculate TTL with new quality
                entry.ttl = await self._calculate_adaptive_ttl(key, None, upgraded_quality)
                entry.created_at = time.time()  # Reset creation time

                logger.debug(f"Cache warming: upgraded quality for '{key}' to {upgraded_quality.value}")

    async def warm_specific_keys(self, keys: List[str], extend_ttl: bool = True) -> Dict[str, bool]:
        """Manually warm specific cache keys.

        Parameters
        ----------
        keys : List[str]
            List of cache keys to warm
        extend_ttl : bool
            Whether to extend TTL during warming

        Returns
        -------
        Dict[str, bool]
            Results of warming operations (key -> success)
        """
        results = {}

        async with self._lock:
            for key in keys:
                try:
                    if key in self.l1_cache:
                        entry = self.l1_cache[key]

                        if extend_ttl:
                            # Extend TTL by 50%
                            new_ttl = min(entry.ttl * 1.5, self.max_ttl)
                            entry.ttl = new_ttl
                            entry.created_at = time.time()

                        # Mark as warmed
                        entry.source = f"manual_warm_{entry.source}"

                        results[key] = True
                        logger.debug(f"Manually warmed cache key '{key}'")
                    else:
                        results[key] = False
                        logger.warning(f"Cannot warm non-existent key '{key}'")

                except Exception as e:
                    results[key] = False
                    logger.error(f"Failed to warm key '{key}': {e}")

        return results

    async def get_warming_stats(self) -> Dict[str, Any]:
        """Get statistics about cache warming operations."""
        async with self._lock:
            warming_eligible = 0
            warming_needed = 0
            current_time = time.time()

            for entry in self.l1_cache.values():
                if entry.access_count > 0:
                    warming_eligible += 1

                    time_remaining = entry.ttl - entry.age
                    warming_point = entry.ttl * 0.15

                    if time_remaining <= warming_point and time_remaining > 0:
                        warming_needed += 1

            return {
                'warming_eligible_entries': warming_eligible,
                'warming_needed_entries': warming_needed,
                'warming_coverage': warming_eligible / max(len(self.l1_cache), 1),
                'warming_urgency': warming_needed / max(warming_eligible, 1),
                'total_entries': len(self.l1_cache)
            }

    async def _analytics_loop(self) -> None:
        """Background task for analytics and optimization."""
        while self._is_running:
            try:
                await asyncio.sleep(300.0)  # Run every 5 minutes
                await self._analyze_performance()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in analytics loop: {e}")

    async def _analyze_performance(self) -> None:
        """Analyze cache performance and optimize parameters."""
        total_requests = self.stats['l1_hits'] + self.stats['l1_misses']
        if total_requests > 0:
            hit_rate = self.stats['l1_hits'] / total_requests
            logger.info(f"Cache '{self.name}' hit rate: {hit_rate:.2%} ({total_requests} total requests)")

    def _record_access_pattern(self, key: str, hit: bool) -> None:
        """Record access pattern for analytics."""
        current_time = time.time()
        self.access_patterns[key].append(current_time)
        self.hit_rates[key].append(1.0 if hit else 0.0)

    def _estimate_size(self, value: Any) -> int:
        """Estimate size of cached value in bytes."""
        try:
            return len(json.dumps(value, default=str).encode('utf-8'))
        except:
            return 1024  # Default estimate

    async def _get_from_l2(self, key: str) -> Optional[Any]:
        """Get value from L2 cache (Redis)."""
        if not self.l2_cache:
            return None

        try:
            entry = await self.l2_cache.get(key)
            return entry.value if entry else None
        except Exception as e:
            logger.warning(f"L2 cache get failed for key '{key}': {e}")
            return None

    async def _set_in_l2(self, key: str, entry: CacheEntry) -> None:
        """Set value in L2 cache (Redis)."""
        if not self.l2_cache:
            return

        try:
            await self.l2_cache.set(key, entry)
        except Exception as e:
            logger.warning(f"L2 cache set failed for key '{key}': {e}")

    async def _remove_from_l2(self, key: str) -> bool:
        """Remove value from L2 cache (Redis)."""
        if not self.l2_cache:
            return False

        try:
            return await self.l2_cache.delete(key)
        except Exception as e:
            logger.warning(f"L2 cache delete failed for key '{key}': {e}")
            return False

    async def setup_l2_cache(self, redis_url: str = "redis://localhost:6379") -> bool:
        """Setup L2 Redis cache layer.

        Parameters
        ----------
        redis_url : str
            Redis connection URL

        Returns
        -------
        bool
            True if L2 cache was successfully setup
        """
        try:
            from qualia.utils.redis_cache_layer import RedisL2Cache

            self.l2_cache = RedisL2Cache(
                redis_url=redis_url,
                key_prefix=f"{self.name}_l2:",
                default_ttl=int(self.base_ttl),
                enable_pubsub=True
            )

            # Setup invalidation callback
            self.l2_cache.add_invalidation_callback(self._handle_l2_invalidation)

            # Connect to Redis
            if await self.l2_cache.connect():
                self.enable_l2_cache = True
                logger.info(f"L2 Redis cache setup completed for '{self.name}'")
                return True
            else:
                self.l2_cache = None
                return False

        except ImportError:
            logger.warning("Redis not available, L2 cache disabled")
            return False
        except Exception as e:
            logger.error(f"Failed to setup L2 cache: {e}")
            self.l2_cache = None
            return False

    def _handle_l2_invalidation(self, key: str) -> None:
        """Handle L2 cache invalidation messages."""
        # Remove from L1 cache if present
        if key in self.l1_cache:
            asyncio.create_task(self._remove_entry(key, CacheLayer.L1_MEMORY))
            logger.debug(f"Invalidated L1 cache entry '{key}' due to L2 invalidation")

    async def _promote_to_l1(self, key: str, value: Any, symbol: Optional[str]) -> None:
        """Promote L2 cache hit to L1 cache."""
        await self.set(key, value, symbol=symbol, source="l2_promotion")

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        total_requests = self.stats['l1_hits'] + self.stats['l1_misses']
        hit_rate = self.stats['l1_hits'] / max(1, total_requests)
        
        return {
            'name': self.name,
            'l1_entries': len(self.l1_cache),
            'hit_rate': hit_rate,
            'stats': self.stats,
            'volatility_symbols': len(self.volatility_metrics),
            'memory_usage_mb': sum(entry.size_bytes for entry in self.l1_cache.values()) / (1024 * 1024),
            'average_ttl': statistics.mean([entry.ttl for entry in self.l1_cache.values()]) if self.l1_cache else 0,
            'quality_distribution': {
                quality.value: len([e for e in self.l1_cache.values() if e.quality == quality])
                for quality in DataQuality
            }
        }

    async def clear(self) -> None:
        """Clear all cache entries."""
        async with self._lock:
            self.l1_cache.clear()
            self.volatility_metrics.clear()
            self.access_patterns.clear()
            self.hit_rates.clear()
            
            logger.info(f"Cleared all entries from cache '{self.name}'")
