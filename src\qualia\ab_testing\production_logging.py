"""
QUALIA A/B Testing Framework - D-07.6 Production Error Handling & Logging

Sistema robusto de error handling e logging para ambiente de produção.
Inclui:
- Logging estruturado com diferentes níveis
- Error handling com recovery automático
- Métricas de sistema e alertas
- Monitoramento de saúde
- Fallback mechanisms
"""

import logging
import logging.handlers
import traceback
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import json
from pathlib import Path
import sys
import os


class LogLevel(Enum):
    """Níveis de log customizados."""
    TRACE = 5
    DEBUG = 10
    INFO = 20
    WARNING = 30
    ERROR = 40
    CRITICAL = 50


class ErrorSeverity(Enum):
    """Severidade de erros."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorContext:
    """Contexto de erro para debugging."""
    component: str
    operation: str
    timestamp: datetime
    severity: ErrorSeverity
    error_type: str
    error_message: str
    stack_trace: str
    system_state: Dict[str, Any]
    recovery_attempted: bool = False
    recovery_successful: bool = False
    additional_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SystemHealthMetrics:
    """Métricas de saúde do sistema."""
    timestamp: datetime
    cpu_usage_pct: float
    memory_usage_pct: float
    active_tests: int
    error_rate_per_hour: float
    avg_response_time_ms: float
    uptime_hours: float
    component_status: Dict[str, bool]


class ProductionLogger:
    """
    Sistema de logging para produção com funcionalidades avançadas.
    
    Funcionalidades:
    - Logging estruturado em JSON
    - Rotação automática de logs
    - Diferentes níveis de verbosidade
    - Correlação de logs por contexto
    - Métricas integradas
    """
    
    def __init__(self,
                 log_dir: str = "logs/ab_testing",
                 max_file_size_mb: int = 100,
                 backup_count: int = 10,
                 enable_console: bool = True,
                 log_level = logging.INFO):
        
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        self.max_file_size_mb = max_file_size_mb
        self.backup_count = backup_count
        self.enable_console = enable_console
        self.log_level = log_level
        
        # Configurar loggers
        self._setup_loggers()
        
        # Contexto de correlação
        self.correlation_context: Dict[str, str] = {}
        
        # Métricas
        self.error_counts: Dict[str, int] = {}
        self.performance_metrics: List[float] = []
        
        self.logger.info("🔧 ProductionLogger inicializado", extra={
            "log_dir": str(self.log_dir),
            "log_level": logging.getLevelName(self.log_level),
            "max_file_size_mb": self.max_file_size_mb
        })
    
    def _setup_loggers(self):
        """Configura sistema de logging."""
        
        # Logger principal
        self.logger = logging.getLogger("qualia.ab_testing")
        self.logger.setLevel(self.log_level)
        
        # Remover handlers existentes
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # Formatter estruturado
        formatter = logging.Formatter(
            '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "component": "%(name)s", '
            '"message": "%(message)s", "correlation_id": "%(correlation_id)s", '
            '"extra": %(extra)s}',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # File handler com rotação
        file_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "ab_testing.log",
            maxBytes=self.max_file_size_mb * 1024 * 1024,
            backupCount=self.backup_count
        )
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        # Error file handler
        error_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "ab_testing_errors.log",
            maxBytes=self.max_file_size_mb * 1024 * 1024,
            backupCount=self.backup_count
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        self.logger.addHandler(error_handler)
        
        # Console handler
        if self.enable_console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_formatter = logging.Formatter(
                '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
                datefmt='%H:%M:%S'
            )
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)
    
    def set_correlation_context(self, **context):
        """Define contexto de correlação para logs."""
        self.correlation_context.update(context)
    
    def clear_correlation_context(self):
        """Limpa contexto de correlação."""
        self.correlation_context.clear()
    
    def _prepare_extra(self, extra: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Prepara dados extras para logging."""
        result = {
            "correlation_id": self.correlation_context.get("test_id", ""),
            "extra": json.dumps(extra or {})
        }
        return result
    
    def trace(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log de trace (mais detalhado que debug)."""
        if self.log_level <= LogLevel.TRACE.value:
            self.logger.log(LogLevel.TRACE.value, message, extra=self._prepare_extra(extra))
    
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log de debug."""
        self.logger.debug(message, extra=self._prepare_extra(extra))
    
    def info(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log de informação."""
        self.logger.info(message, extra=self._prepare_extra(extra))
    
    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log de warning."""
        self.logger.warning(message, extra=self._prepare_extra(extra))
    
    def error(self, message: str, extra: Optional[Dict[str, Any]] = None, exc_info: bool = True):
        """Log de erro."""
        self.logger.error(message, extra=self._prepare_extra(extra), exc_info=exc_info)
        
        # Contar erros para métricas
        error_type = extra.get("error_type", "unknown") if extra else "unknown"
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
    
    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None, exc_info: bool = True):
        """Log crítico."""
        self.logger.critical(message, extra=self._prepare_extra(extra), exc_info=exc_info)


class ProductionErrorHandler:
    """
    Sistema de tratamento de erros para produção.
    
    Funcionalidades:
    - Categorização automática de erros
    - Recovery automático quando possível
    - Alertas baseados em severidade
    - Fallback mechanisms
    - Circuit breaker pattern
    """
    
    def __init__(self, logger: ProductionLogger):
        self.logger = logger
        self.error_history: List[ErrorContext] = []
        self.recovery_strategies: Dict[str, Callable] = {}
        self.circuit_breakers: Dict[str, Dict[str, Any]] = {}
        self.alert_callbacks: List[Callable] = []
        
        # Configurações
        self.max_error_history = 1000
        self.circuit_breaker_threshold = 5  # Erros consecutivos
        self.circuit_breaker_timeout_minutes = 10
        
        self.logger.info("🛡️ ProductionErrorHandler inicializado")
    
    def register_recovery_strategy(self, error_type: str, strategy: Callable):
        """Registra estratégia de recovery para tipo de erro."""
        self.recovery_strategies[error_type] = strategy
        self.logger.info(f"📋 Estratégia de recovery registrada: {error_type}")
    
    def add_alert_callback(self, callback: Callable):
        """Adiciona callback para alertas."""
        self.alert_callbacks.append(callback)
    
    async def handle_error(self, 
                          component: str,
                          operation: str,
                          error: Exception,
                          severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                          system_state: Optional[Dict[str, Any]] = None) -> ErrorContext:
        """Trata erro de forma abrangente."""
        
        error_context = ErrorContext(
            component=component,
            operation=operation,
            timestamp=datetime.now(),
            severity=severity,
            error_type=type(error).__name__,
            error_message=str(error),
            stack_trace=traceback.format_exc(),
            system_state=system_state or {}
        )
        
        # Log do erro
        self.logger.error(
            f"❌ Erro em {component}.{operation}: {error}",
            extra={
                "component": component,
                "operation": operation,
                "error_type": error_context.error_type,
                "severity": severity.value,
                "system_state": system_state
            }
        )
        
        # Verificar circuit breaker
        if self._is_circuit_open(component):
            self.logger.warning(f"⚡ Circuit breaker aberto para {component}")
            error_context.additional_data["circuit_breaker"] = "open"
            return error_context
        
        # Tentar recovery
        if error_context.error_type in self.recovery_strategies:
            try:
                self.logger.info(f"🔄 Tentando recovery para {error_context.error_type}")
                recovery_result = await self.recovery_strategies[error_context.error_type](error_context)
                
                error_context.recovery_attempted = True
                error_context.recovery_successful = recovery_result
                
                if recovery_result:
                    self.logger.info(f"✅ Recovery bem-sucedido para {error_context.error_type}")
                    self._reset_circuit_breaker(component)
                else:
                    self.logger.warning(f"❌ Recovery falhou para {error_context.error_type}")
                    self._increment_circuit_breaker(component)
                    
            except Exception as recovery_error:
                self.logger.error(f"💥 Erro durante recovery: {recovery_error}")
                error_context.recovery_attempted = True
                error_context.recovery_successful = False
                self._increment_circuit_breaker(component)
        else:
            self._increment_circuit_breaker(component)
        
        # Adicionar ao histórico
        self.error_history.append(error_context)
        if len(self.error_history) > self.max_error_history:
            self.error_history.pop(0)
        
        # Disparar alertas se necessário
        if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            await self._trigger_alerts(error_context)
        
        return error_context
    
    def _is_circuit_open(self, component: str) -> bool:
        """Verifica se circuit breaker está aberto."""
        if component not in self.circuit_breakers:
            return False
        
        breaker = self.circuit_breakers[component]
        
        # Verificar se timeout expirou
        if datetime.now() > breaker["timeout"]:
            self._reset_circuit_breaker(component)
            return False
        
        return breaker["is_open"]
    
    def _increment_circuit_breaker(self, component: str):
        """Incrementa contador do circuit breaker."""
        if component not in self.circuit_breakers:
            self.circuit_breakers[component] = {
                "error_count": 0,
                "is_open": False,
                "timeout": datetime.now()
            }
        
        breaker = self.circuit_breakers[component]
        breaker["error_count"] += 1
        
        if breaker["error_count"] >= self.circuit_breaker_threshold:
            breaker["is_open"] = True
            breaker["timeout"] = datetime.now() + timedelta(minutes=self.circuit_breaker_timeout_minutes)
            
            self.logger.warning(
                f"⚡ Circuit breaker ativado para {component}",
                extra={"component": component, "error_count": breaker["error_count"]}
            )
    
    def _reset_circuit_breaker(self, component: str):
        """Reseta circuit breaker."""
        if component in self.circuit_breakers:
            self.circuit_breakers[component] = {
                "error_count": 0,
                "is_open": False,
                "timeout": datetime.now()
            }
            
            self.logger.info(f"✅ Circuit breaker resetado para {component}")
    
    async def _trigger_alerts(self, error_context: ErrorContext):
        """Dispara alertas para erros críticos."""
        alert_data = {
            "component": error_context.component,
            "operation": error_context.operation,
            "error_type": error_context.error_type,
            "severity": error_context.severity.value,
            "message": error_context.error_message,
            "timestamp": error_context.timestamp.isoformat(),
            "recovery_attempted": error_context.recovery_attempted,
            "recovery_successful": error_context.recovery_successful
        }
        
        for callback in self.alert_callbacks:
            try:
                await callback("error_alert", alert_data)
            except Exception as e:
                self.logger.error(f"❌ Erro ao disparar alerta: {e}")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Retorna estatísticas de erros."""
        now = datetime.now()
        last_hour = now - timedelta(hours=1)
        last_day = now - timedelta(days=1)
        
        recent_errors = [e for e in self.error_history if e.timestamp >= last_hour]
        daily_errors = [e for e in self.error_history if e.timestamp >= last_day]
        
        return {
            "total_errors": len(self.error_history),
            "errors_last_hour": len(recent_errors),
            "errors_last_day": len(daily_errors),
            "error_rate_per_hour": len(recent_errors),
            "most_common_errors": self._get_most_common_errors(),
            "components_with_errors": list(set(e.component for e in self.error_history)),
            "circuit_breakers": {
                comp: {"is_open": breaker["is_open"], "error_count": breaker["error_count"]}
                for comp, breaker in self.circuit_breakers.items()
            },
            "recovery_success_rate": self._calculate_recovery_success_rate()
        }
    
    def _get_most_common_errors(self) -> List[Dict[str, Any]]:
        """Retorna erros mais comuns."""
        error_counts = {}
        for error in self.error_history:
            key = f"{error.component}.{error.error_type}"
            error_counts[key] = error_counts.get(key, 0) + 1
        
        return [
            {"error": error, "count": count}
            for error, count in sorted(error_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        ]
    
    def _calculate_recovery_success_rate(self) -> float:
        """Calcula taxa de sucesso de recovery."""
        recovery_attempts = [e for e in self.error_history if e.recovery_attempted]
        if not recovery_attempts:
            return 0.0
        
        successful_recoveries = [e for e in recovery_attempts if e.recovery_successful]
        return len(successful_recoveries) / len(recovery_attempts) * 100
