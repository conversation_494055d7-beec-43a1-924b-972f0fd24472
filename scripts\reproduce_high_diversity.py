import logging

import argparse
import random
from pathlib import Path

import numpy as np
from qiskit import qasm3

# ``algorithm_globals`` mudou de local entre versões do Qiskit. Tenta importar
# da biblioteca principal e, caso indisponível, define uma implementação mínima
# para evitar dependência de ``qiskit_algorithms``.
try:  # pragma: no cover - dependente da versão instalada
    from qiskit.utils import algorithm_globals
except Exception:  # pragma: no cover - dependente da versão instalada

    class _AlgorithmGlobals:
        """Fallback mínimo para ``algorithm_globals``."""

        def __init__(self) -> None:
            self.random_seed: int | None = None

    algorithm_globals = _AlgorithmGlobals()

from qualia.utils.logger import get_logger
from qualia.core.universe import QUALIAQuantumUniverse


logging.basicConfig(level=logging.INFO)
logger = get_logger(__name__)
logger.setLevel(logging.INFO)


SEED_QASM_PATH = Path("seed_circuit.qasm")


def run_high_diversity_example(
    fixed_seed: bool = False,
    gate_budget_per_qubit: int | None = None,
    post_randomize_layers: int = 1,
    *,
    shots: int = 128,
    backend_name: str = "aer_simulator_statevector",
) -> tuple[dict[str, int], dict[str, float], "QUALIAQuantumUniverse"]:
    """Run ``QUALIAQuantumUniverse`` with parameters that yield high diversity.

    Parameters
    ----------
    fixed_seed
        If ``True``, use a deterministic seed and save the QASM circuit.
    gate_budget_per_qubit
        Optional hard cap on the number of gates per qubit.
    post_randomize_layers
        Number of randomization layers applied after trimming.
    shots
        Number of circuit repetitions when running on the backend.
    backend_name
        Identifier of the backend used by :class:`QUALIAQuantumUniverse`.
    """

    if fixed_seed:
        seed = 42
        random.seed(seed)
        np.random.seed(seed)
        algorithm_globals.random_seed = seed

    u = QUALIAQuantumUniverse(
        n_qubits=5,
        scr_depth=6,
        base_lambda=0.1,
        alpha=0.1,
        retro_strength=0.0,
        num_ctc_qubits=0,
        measure_frequency=1,
        thermal_coefficient=0.05,
        shots=shots,
        qpu_steps=8,
        thermal_noise_enabled=True,
        backend_name=backend_name,
        initial_state_type="hadamard",
        entanglement_style="full",
    )

    counts, metrics = u.run(
        steps=4,
        gate_budget_per_qubit=gate_budget_per_qubit,
        post_randomize_layers=post_randomize_layers,
    )

    if fixed_seed:
        try:
            SEED_QASM_PATH.write_text(qasm3.dumps(u.qc))
        except Exception as exc:  # pragma: no cover - may fail on restricted env
            logger.warning("Falha ao salvar QASM seed: %s", exc)
    logger.info("Counts diversity ratio: %.4f", metrics["counts_diversity_ratio"])
    logger.info("Circuit depth: %d", u.qc.depth())
    try:
        qasm_snippet = qasm3.dumps(u.qc).splitlines()[:10]
        logger.info("QASM snippet:\n%s", "\n".join(qasm_snippet))
    except Exception as exc:  # pragma: no cover - qasm export may fail
        logger.warning("Failed to export QASM: %s", exc)
    return counts, metrics, u


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Reproduz exemplo de alta diversidade")
    parser.add_argument(
        "--fixed-seed", action="store_true", help="Usar seed fixo e salvar QASM"
    )
    parser.add_argument(
        "--gate-budget-per-qubit",
        type=int,
        default=None,
        help="Limite opcional de portas por qubit",
    )
    parser.add_argument(
        "--shots",
        type=int,
        default=128,
        help="Número de execuções do circuito (shots)",
    )
    parser.add_argument(
        "--backend-name",
        type=str,
        default="aer_simulator_statevector",
        help="Nome do backend a ser utilizado",
    )
    parser.add_argument(
        "--post-randomize-layers",
        type=int,
        default=1,
        help="Número de camadas de randomização pós-trim",
    )
    args = parser.parse_args()

    counts, metrics, universe = run_high_diversity_example(
        args.fixed_seed,
        gate_budget_per_qubit=args.gate_budget_per_qubit,
        post_randomize_layers=args.post_randomize_layers,
        shots=args.shots,
        backend_name=args.backend_name,
    )
    print("\n=== Resultados da simulação ===")
    print(
        f"Razão de diversidade dos contadores: {metrics['counts_diversity_ratio']:.4f}"
    )
    print(f"Profundidade do circuito: {universe.qc.depth()}")
    print("\n=== Fim da simulação ===")
