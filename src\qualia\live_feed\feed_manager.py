"""
QUALIA Feed Manager - Coordenador principal do sistema de feeds.

Este módulo coordena múltiplos feeds de dados, gerencia conexões, monitora saúde
e fornece uma interface unificada para o sistema QUALIA.
"""

from __future__ import annotations

import asyncio
import time
from typing import Dict, Any, Optional, List, Callable, Set
from datetime import datetime
from dataclasses import dataclass, asdict

from ..utils.logger import get_logger
from ..config.data_sources import load_data_sources_defaults
from .kucoin_feed import KuCoinFeed
from .feed_aggregator import FeedAggregator, AggregatedTicker, AggregatedOrderBook
from .data_normalizer import NormalizedTicker, NormalizedOrderBook, NormalizedTrade

logger = get_logger(__name__)


@dataclass
class FeedStatus:
    """Status de um feed."""
    
    name: str
    is_running: bool
    is_connected: bool
    last_data_time: float
    error_count: int
    reconnect_count: int
    symbols_count: int
    data_points_received: int


@dataclass
class SystemStatus:
    """Status geral do sistema de feeds."""
    
    is_running: bool
    active_feeds: int
    total_symbols: int
    aggregated_symbols: int
    total_data_points: int
    uptime_seconds: float
    feeds_status: List[FeedStatus]


class FeedManager:
    """Gerenciador principal do sistema de feeds."""
    
    def __init__(
        self,
        config: Optional[Dict[str, Any]] = None,
        symbols: Optional[List[str]] = None,
        enable_kucoin: bool = True,
        aggregation_enabled: bool = True,
    ):
        """
        Inicializa o gerenciador de feeds.
        
        Args:
            config: Configuração do sistema
            symbols: Lista de símbolos para monitorar
            enable_kucoin: Habilitar feed KuCoin
            aggregation_enabled: Habilitar agregação de dados
        """
        self.config = config or {}
        self.symbols = symbols or ['BTC-USDT', 'ETH-USDT', 'ADA-USDT', 'SOL-USDT']
        self.enable_kucoin = enable_kucoin
        self.aggregation_enabled = aggregation_enabled
        
        # Estado do sistema
        self.is_running = False
        self.start_time = 0.0
        self.total_data_points = 0
        
        # Feeds ativos
        self.feeds: Dict[str, Any] = {}
        self.feed_tasks: Dict[str, asyncio.Task] = {}
        self.feed_metrics: Dict[str, Dict[str, int]] = {}

        # Agregador
        self.aggregator: Optional[FeedAggregator] = None
        if aggregation_enabled:
            self.aggregator = FeedAggregator()
        
        # Callbacks do sistema
        self.on_ticker_callback: Optional[Callable[[AggregatedTicker], None]] = None
        self.on_orderbook_callback: Optional[Callable[[AggregatedOrderBook], None]] = None
        self.on_system_alert_callback: Optional[Callable[[str, Dict[str, Any]], None]] = None
        
        # Monitoramento
        self.monitor_task: Optional[asyncio.Task] = None
        self.health_check_interval = 30.0  # segundos
        
        # Estatísticas
        self.stats = {
            'feeds_started': 0,
            'feeds_stopped': 0,
            'reconnections': 0,
            'errors': 0,
            'alerts': 0,
        }
    
    async def initialize(self) -> bool:
        """Inicializa o sistema de feeds."""
        try:
            logger.info("🚀 Inicializando QUALIA Feed Manager...")
            
            # Carregar configuração padrão se necessário
            if not self.config:
                try:
                    self.config = load_data_sources_defaults()
                except Exception as e:
                    logger.warning(f"Erro ao carregar configuração padrão: {e}")
                    self.config = {}
            
            # Inicializar agregador
            if self.aggregator:
                self.aggregator.set_aggregated_ticker_callback(self._on_aggregated_ticker)
                self.aggregator.set_aggregated_orderbook_callback(self._on_aggregated_orderbook)
                self.aggregator.set_alert_callback(self._on_aggregator_alert)
            
            # Inicializar feeds
            await self._initialize_feeds()
            
            logger.info("✅ Feed Manager inicializado com sucesso")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar Feed Manager: {e}")
            return False
    
    async def start(self) -> bool:
        """Inicia o sistema de feeds."""
        if self.is_running:
            logger.warning("Feed Manager já está rodando")
            return True
        
        if not await self.initialize():
            return False
        
        self.is_running = True
        self.start_time = time.time()
        
        logger.info("🚀 Iniciando sistema de feeds...")
        
        # Iniciar feeds
        for name, feed in self.feeds.items():
            try:
                if hasattr(feed, 'start'):
                    success = await feed.start()
                    if success:
                        logger.info(f"✅ Feed {name} iniciado")
                        self.stats['feeds_started'] += 1
                    else:
                        logger.error(f"❌ Falha ao iniciar feed {name}")
                        self.stats['errors'] += 1
            except Exception as e:
                logger.error(f"Erro ao iniciar feed {name}: {e}")
                self.stats['errors'] += 1
        
        # Iniciar monitoramento
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        
        logger.info("✅ Sistema de feeds iniciado")
        return True
    
    async def stop(self):
        """Para o sistema de feeds."""
        if not self.is_running:
            return
        
        logger.info("🛑 Parando sistema de feeds...")
        self.is_running = False
        
        # Parar monitoramento
        if self.monitor_task and not self.monitor_task.done():
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        # Parar feeds
        for name, feed in self.feeds.items():
            try:
                if hasattr(feed, 'stop'):
                    await feed.stop()
                    logger.info(f"✅ Feed {name} parado")
                    self.stats['feeds_stopped'] += 1
            except Exception as e:
                logger.error(f"Erro ao parar feed {name}: {e}")
        
        # Parar tasks
        for name, task in self.feed_tasks.items():
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        logger.info("✅ Sistema de feeds parado")
    
    async def _initialize_feeds(self):
        """Inicializa feeds individuais."""
        # KuCoin Feed
        if self.enable_kucoin:
            try:
                kucoin_config = self.config.get('exchanges', {}).get('kucoin', {})
                
                kucoin_feed = KuCoinFeed(
                    api_key=kucoin_config.get('api_key'),
                    api_secret=kucoin_config.get('api_secret'),
                    password=kucoin_config.get('password'),
                    symbols=self.symbols,
                    enable_websocket=True,
                    enable_rest_fallback=True,
                    timeout=kucoin_config.get('timeout', 60.0),
                    sandbox=kucoin_config.get('sandbox', False),
                    rate_limit=kucoin_config.get('rate_limit', 2.0),
                )
                
                # Configurar callbacks
                kucoin_feed.set_ticker_callback(self._on_ticker_data)
                kucoin_feed.set_orderbook_callback(self._on_orderbook_data)
                kucoin_feed.set_trade_callback(self._on_trade_data)
                kucoin_feed.set_error_callback(lambda e: self._on_feed_error('kucoin', e))

                self.feeds['kucoin'] = kucoin_feed
                self.feed_metrics['kucoin'] = {'error_count': 0, 'data_points_received': 0}
                logger.info("KuCoin feed configurado")
                
            except Exception as e:
                logger.error(f"Erro ao configurar KuCoin feed: {e}")
    
    def _on_ticker_data(self, ticker: NormalizedTicker):
        """Callback para dados de ticker."""
        try:
            self.total_data_points += 1
            if ticker.source in self.feed_metrics:
                self.feed_metrics[ticker.source]['data_points_received'] += 1
            
            # Enviar para agregador se habilitado
            if self.aggregator:
                self.aggregator.add_ticker(ticker)
            else:
                # Se não há agregador, criar ticker "agregado" simples
                from .feed_aggregator import AggregatedTicker
                aggregated = AggregatedTicker(
                    symbol=ticker.symbol,
                    price=ticker.price,
                    price_weighted=ticker.price,
                    bid=ticker.bid,
                    ask=ticker.ask,
                    spread=ticker.ask - ticker.bid if ticker.ask > ticker.bid else 0.0,
                    volume_24h=ticker.volume_24h,
                    change_24h=ticker.change_24h,
                    change_24h_percent=ticker.change_24h_percent,
                    high_24h=ticker.high_24h,
                    low_24h=ticker.low_24h,
                    timestamp=ticker.timestamp,
                    sources=[ticker.source],
                    source_count=1,
                    price_variance=0.0,
                    confidence_score=1.0,
                    raw_tickers=[ticker]
                )
                
                if self.on_ticker_callback:
                    self.on_ticker_callback(aggregated)
            
        except Exception as e:
            logger.error(f"Erro ao processar ticker: {e}")
    
    def _on_orderbook_data(self, orderbook: NormalizedOrderBook):
        """Callback para dados de orderbook."""
        try:
            self.total_data_points += 1
            if orderbook.source in self.feed_metrics:
                self.feed_metrics[orderbook.source]['data_points_received'] += 1
            
            if self.aggregator:
                self.aggregator.add_orderbook(orderbook)
            
        except Exception as e:
            logger.error(f"Erro ao processar orderbook: {e}")
    
    def _on_trade_data(self, trade: NormalizedTrade):
        """Callback para dados de trade."""
        try:
            self.total_data_points += 1
            if trade.source in self.feed_metrics:
                self.feed_metrics[trade.source]['data_points_received'] += 1
            
            if self.aggregator:
                self.aggregator.add_trade(trade)
            
        except Exception as e:
            logger.error(f"Erro ao processar trade: {e}")
    
    def _on_feed_error(self, feed_name: str, error: Exception):
        """Callback para erros de feed."""
        logger.error(f"Erro em feed: {error}")
        if feed_name in self.feed_metrics:
            self.feed_metrics[feed_name]['error_count'] += 1
        self.stats['errors'] += 1
        
        if self.on_system_alert_callback:
            alert = {
                'type': 'feed_error',
                'error': str(error),
                'timestamp': time.time()
            }
            self.on_system_alert_callback('feed_error', alert)
    
    def _on_aggregated_ticker(self, ticker: AggregatedTicker):
        """Callback para ticker agregado."""
        if self.on_ticker_callback:
            self.on_ticker_callback(ticker)
    
    def _on_aggregated_orderbook(self, orderbook: AggregatedOrderBook):
        """Callback para orderbook agregado."""
        if self.on_orderbook_callback:
            self.on_orderbook_callback(orderbook)
    
    def _on_aggregator_alert(self, alert_type: str, alert_data: Dict[str, Any]):
        """Callback para alertas do agregador."""
        logger.warning(f"Alerta do agregador: {alert_type}")
        self.stats['alerts'] += 1
        
        if self.on_system_alert_callback:
            self.on_system_alert_callback(alert_type, alert_data)
    
    async def _monitor_loop(self):
        """Loop de monitoramento do sistema."""
        while self.is_running:
            try:
                await self._health_check()
                await asyncio.sleep(self.health_check_interval)
            except Exception as e:
                logger.error(f"Erro no monitoramento: {e}")
                await asyncio.sleep(self.health_check_interval)
    
    async def _health_check(self):
        """Verifica saúde do sistema."""
        try:
            current_time = time.time()
            
            # Verificar feeds
            for name, feed in self.feeds.items():
                if hasattr(feed, 'get_feed_status'):
                    status = feed.get_feed_status()
                    
                    # Verificar se feed está recebendo dados
                    if (status.get('last_data_time', 0) > 0 and 
                        current_time - status.get('last_data_time', 0) > 60.0):
                        
                        logger.warning(f"Feed {name} sem dados há mais de 60 segundos")
                        
                        if self.on_system_alert_callback:
                            alert = {
                                'type': 'feed_stale',
                                'feed': name,
                                'last_data_age': current_time - status.get('last_data_time', 0),
                                'timestamp': current_time
                            }
                            self.on_system_alert_callback('feed_stale', alert)
            
        except Exception as e:
            logger.error(f"Erro no health check: {e}")
    
    def get_system_status(self) -> SystemStatus:
        """Retorna status do sistema."""
        feeds_status = []
        
        for name, feed in self.feeds.items():
            if hasattr(feed, 'get_feed_status'):
                status = feed.get_feed_status()
                metrics = self.feed_metrics.get(name, {'error_count': 0, 'data_points_received': 0})
                feeds_status.append(FeedStatus(
                    name=name,
                    is_running=status.get('is_running', False),
                    is_connected=status.get('is_connected', False),
                    last_data_time=status.get('last_data_time', 0),
                    error_count=metrics['error_count'],
                    reconnect_count=status.get('reconnect_count', 0),
                    symbols_count=status.get('symbols_count', 0),
                    data_points_received=metrics['data_points_received'],
                ))
        
        return SystemStatus(
            is_running=self.is_running,
            active_feeds=len([f for f in feeds_status if f.is_running]),
            total_symbols=len(self.symbols),
            aggregated_symbols=len(self.aggregator.aggregated_tickers) if self.aggregator else 0,
            total_data_points=self.total_data_points,
            uptime_seconds=time.time() - self.start_time if self.start_time > 0 else 0,
            feeds_status=feeds_status
        )
    
    def get_latest_ticker(self, symbol: str) -> Optional[AggregatedTicker]:
        """Retorna último ticker agregado para um símbolo."""
        if self.aggregator:
            return self.aggregator.get_aggregated_ticker(symbol)
        return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """Retorna estatísticas do sistema."""
        system_stats = {
            **self.stats,
            'total_data_points': self.total_data_points,
            'uptime_seconds': time.time() - self.start_time if self.start_time > 0 else 0,
            'active_feeds': len([f for f in self.feeds.values() if hasattr(f, 'is_running') and f.is_running]),
            'feeds_metrics': self.feed_metrics,
        }
        
        if self.aggregator:
            system_stats.update(self.aggregator.get_statistics())
        
        return system_stats
    
    def set_ticker_callback(self, callback: Callable[[AggregatedTicker], None]):
        """Define callback para tickers."""
        self.on_ticker_callback = callback
    
    def set_orderbook_callback(self, callback: Callable[[AggregatedOrderBook], None]):
        """Define callback para orderbooks."""
        self.on_orderbook_callback = callback
    
    def set_system_alert_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """Define callback para alertas do sistema."""
        self.on_system_alert_callback = callback
