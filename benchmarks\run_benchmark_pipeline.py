import argparse
import csv
import importlib.util
import json
import os
import pathlib
import subprocess
import sys

from qualia.analysis import log_experiment_results


def _run_pytest(accelerated: bool, results_dir: pathlib.Path, label: str) -> None:
    """Run pytest benchmarks with optional acceleration.

    Parameters
    ----------
    accelerated
        If ``True``, enable Numba acceleration by setting ``QUALIA_USE_GPU``.
    results_dir
        Directory where benchmark results will be stored.
    label
        Name used to tag benchmark run and output files.
    """
    env = os.environ.copy()
    env["QUALIA_USE_GPU"] = "true" if accelerated else "false"

    json_path = results_dir / f"benchmark_{label}.json"

    cmd = [
        sys.executable,
        "-m",
        "pytest",
        "benchmarks",
        "--benchmark-only",
        f"--benchmark-json={json_path}",
        f"--benchmark-save={label}",
        "-vv",
    ]
    subprocess.run(cmd, check=True, env=env)

    _export_json_to_csv(json_path, results_dir / f"benchmark_{label}.csv")

    with json_path.open() as fh:
        data = json.load(fh)
    for bench in data.get("benchmarks", []):
        stats = bench.get("stats", {})
        record = {
            "benchmark": bench.get("fullname", bench.get("name")),
            "mean": stats.get("mean"),
            "stddev": stats.get("stddev"),
            "min": stats.get("min"),
            "max": stats.get("max"),
            "rounds": stats.get("rounds"),
            "accelerated": accelerated,
        }
        log_experiment_results(record, str(results_dir / "benchmark_results.jsonl"))


def _export_json_to_csv(json_path: pathlib.Path, csv_path: pathlib.Path) -> None:
    """Convert pytest-benchmark JSON results to CSV.

    Parameters
    ----------
    json_path
        Path to the JSON file generated by ``pytest-benchmark``.
    csv_path
        Path where the converted CSV will be stored.
    """
    with json_path.open() as fh:
        data = json.load(fh)

    rows = []
    for bench in data.get("benchmarks", []):
        stats = bench.get("stats", {})
        rows.append(
            {
                "name": bench.get("fullname", bench.get("name")),
                "mean": stats.get("mean"),
                "stddev": stats.get("stddev"),
                "min": stats.get("min"),
                "max": stats.get("max"),
                "rounds": stats.get("rounds"),
            }
        )

    if rows:
        with csv_path.open("w", newline="") as fh:
            writer = csv.DictWriter(fh, fieldnames=rows[0].keys())
            writer.writeheader()
            writer.writerows(rows)


def _compare_results(results_dir: pathlib.Path) -> None:
    """Generate CSV comparison between baseline and accelerated runs."""
    compare_csv = results_dir / "benchmark_compare.csv"
    compare_txt = results_dir / "benchmark_compare.txt"
    with compare_txt.open("w") as fh:
        subprocess.run(
            [
                "pytest-benchmark",
                "compare",
                "baseline",
                "accelerated",
                f"--csv={compare_csv}",
            ],
            check=True,
            stdout=fh,
        )


def main() -> None:
    """Execute benchmark pipeline for QUALIA."""
    parser = argparse.ArgumentParser(description="Run QUALIA benchmark suite")
    parser.add_argument(
        "--compare",
        action="store_true",
        help="Run with and without Numba acceleration when available",
    )
    args = parser.parse_args()

    results_dir = pathlib.Path("benchmarks/results")
    results_dir.mkdir(parents=True, exist_ok=True)

    _run_pytest(accelerated=False, results_dir=results_dir, label="baseline")

    if args.compare and importlib.util.find_spec("numba") is not None:
        _run_pytest(accelerated=True, results_dir=results_dir, label="accelerated")
        _compare_results(results_dir)


if __name__ == "__main__":
    main()
