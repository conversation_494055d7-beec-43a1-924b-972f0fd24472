#!/usr/bin/env python3
"""
Run staging validation
"""

import os
import sys
import asyncio
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def main():
    """Main validation execution"""
    print("="*60)
    print("QUALIA STAGING VALIDATION")
    print("="*60)
    
    try:
        import yaml
        from src.qualia.validation.production_validator import ProductionValidator
        
        # Load staging config
        with open("config/staging_config.yaml", 'r') as f:
            config = yaml.safe_load(f)
        
        print(f"Environment: {config.get('environment', 'unknown')}")
        print("Starting validation...")
        
        # Create validator
        validator = ProductionValidator(config)
        
        # Run validation
        report = await validator.validate_production_environment()
        
        print("\n" + "="*60)
        print("VALIDATION RESULTS")
        print("="*60)
        print(f"Overall Status: {report.overall_status}")
        print(f"Total Tests: {report.total_tests}")
        print(f"Passed: {report.passed_tests}")
        print(f"Failed: {report.failed_tests}")
        print(f"Warnings: {report.warning_tests}")
        print(f"Skipped: {report.skipped_tests}")
        print(f"Execution Time: {report.execution_time_ms:.1f}ms")
        
        # Show failed tests
        if report.failed_tests > 0:
            print("\nFAILED TESTS:")
            for result in report.results:
                if result.status == 'FAIL':
                    print(f"  ✗ {result.test_name}: {result.message}")
        
        # Show warning tests
        if report.warning_tests > 0:
            print("\nWARNING TESTS:")
            for result in report.results:
                if result.status == 'WARNING':
                    print(f"  ⚠ {result.test_name}: {result.message}")
        
        print("="*60)
        
        # Return success if no failures
        return report.failed_tests == 0
        
    except Exception as e:
        print(f"Validation failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
