# Gerenciamento de Risco

Este documento descreve os perfis de risco disponíveis no QUALIA e os parâmetros que podem ser configurados para cada perfil. Os perfis definem limites de exposição e servem como base para o módulo de controle dinâmico de risco.

## Perfis Disponíveis

- **conservative**
- **moderate**
- **aggressive** (padrão: 3.0% do capital por trade)
- **custom_base**
- **default**

Cada perfil define valores padrão para todos os parâmetros listados abaixo. Utilize o perfil que melhor se adapta ao apetite de risco da estratégia.

### Filosofia dos Perfis

- **conservative**: preservação de capital acima de tudo. Limites reduzidos de tamanho de posição e tolerância a perdas mínimas.
- **moderate**: busca equilíbrio entre crescimento e segurança. Aceita riscos moderados e tamanho de posição intermediário.
- **aggressive**: prioriza retornos elevados, tolerando volatilidade e posições maiores.
  Desde a versão 0.1.52, utiliza 3.0% de risco por trade mantendo o limite de posição em 25% do capital.
- **custom_base**: ponto de partida flexível para configurações personalizadas.
- **balanced**: mescla parâmetros de risco moderado e conservador para estabilidade de longo prazo.

## Parâmetros

- `max_drawdown_pct`: perda máxima acumulada permitida.
- `risk_per_trade_pct`: parcela do capital arriscada em cada trade (valor em porcentagem; `1.2` representa `1.2%`).
- Valores menores ou iguais a `0.05` são interpretados como frações (ex.: `0.008` é tratado como `0.8%`).
- `max_position_size_pct`: tamanho máximo da posição em relação ao capital total.
- `max_daily_loss_pct`: limite de perda diária antes do bloqueio de novas operações.
- `max_open_positions`: quantidade máxima de posições simultâneas.
- `cooling_period_minutes`: intervalo mínimo entre perdas consecutivas.
- `stop_loss_adjustment`: fator para ajustar o stop-loss ao longo do trade.
- `min_lot_size`: tamanho mínimo de ordem aceito pela estratégia.
- `min_lot_check_threshold`: abaixo desse valor de `risk_per_trade_pct` a
  estratégia verifica o lote mínimo (padrão `5.0`).

Caso `risk_per_trade_pct` multiplicado pelo capital seja inferior ao custo de
compra do `min_lot_size`, a ordem é recusada com o motivo
`size_below_min_lot`. Por exemplo, com capital de **185 USD** e
`risk_per_trade_pct` de **0.1** (10%), o risco permitido é **18,50 USD**. Se o
`min_lot_size` for **0.001 BTC** a **30.000 USD**, seriam necessários **30 USD**
para abrir a posição. Como o valor de risco não cobre o custo mínimo, o trade
é negado. Ajuste o capital disponível ou aumente `risk_per_trade_pct` para que
o montante arriscado seja compatível com o lote mínimo.

### Pré-carregamento para S3

A subestratégia S3 depende de séries de 4h derivadas das velas de 1h. Para evitar
períodos de aquecimento prolongados em execuções ao vivo, é recomendado
pré‑carregar esses dados históricos em um bucket S3. O
`QUALIARealTimeTrader` verifica a quantidade de velas disponíveis e, caso o
histórico seja insuficiente, solicitará blocos adicionais antes de iniciar a
análise.

## Exemplo de Configuração

O trecho abaixo mostra como definir o perfil `moderate` no arquivo `config/strategy_parameters.json`:

```json
{
  "risk_profile_settings": {
    "moderate": {
      "max_drawdown_pct": 6.0,
      "risk_per_trade_pct": 0.8,
      "max_position_size_pct": 15.0,
      "max_daily_loss_pct": 5.0,
      "max_open_positions": 3,
      "cooling_period_minutes": 30,
      "stop_loss_adjustment": 0.8,
      "min_lot_size": 0.0001
    }
  }
}
```

Altere o parâmetro `risk_profile` em sua configuração para selecionar qualquer um dos perfis disponíveis.

### Timeout de ticker

Para controlar quanto tempo o sistema aguarda uma resposta de preço antes de aplicar novo backoff,
utilize a opção de linha de comando `--ticker_fetch_timeout`. Esse valor (em segundos)
se sobrepõe à variável de ambiente `TICKER_TIMEOUT`.

## Definição do Capital Inicial

O `initial_capital` usado nos testes e na operação em tempo real não faz parte da
seção `risk_profile_settings` do arquivo `strategy_parameters.json`.

- **Perfil `custom`**: todo o conjunto de parâmetros, incluindo o capital, é
  fornecido pela linha de comando. O JSON serve apenas como referência.
- **Demais perfis**: os limites de risco são carregados do JSON, mas o valor de
  `initial_capital` continua sendo aquele informado via CLI. Mesmo que o JSON
  possua um campo com esse nome no futuro, a CLI tem precedência.

## Fator de Lucro Mínimo

O arquivo `config/strategy_parameters.json` define, na seção `constraints`, o campo `min_profit_factor`. Esse limite controla o lucro mínimo aceitável nos backtests e atualmente está configurado em 1.12.

Manter `min_profit_factor` acima de 1 é crucial porque valores iguais ou inferiores indicam que a estratégia gera prejuízo ou, no máximo, empata após custos operacionais. Um fator de lucro maior que 1 assegura que as operações compensam taxas e slippage, mantendo o sistema financeiramente saudável.

## JSON corrompido

Caso o arquivo `config/strategy_parameters.json` esteja ilegível ou apresente erros de sintaxe, o sistema utiliza o perfil de risco `default` para garantir limites seguros.

Execute `scripts/check_json.py` antes de iniciar o robô para validar a estrutura do JSON.

## Registro de Gerenciadores Personalizados

Para adicionar suas próprias implementações de controle de risco,
utilize as funções `register_risk_manager` e `create_risk_manager`
do módulo `risk_manager_builder`. A partir da versão atual, essas
operações são protegidas por um `threading.Lock`, garantindo que
múltiplas threads possam registrar ou instanciar gerenciadores
simultaneamente sem condições de corrida.

## Parâmetros Dinâmicos Padrão

O arquivo `config/dynamic_risk_defaults.yaml` contém os valores iniciais utilizados
pelo `DynamicRiskController`. Cada campo da dataclass `DynamicRiskParameters`
aparece mapeado nesse YAML para facilitar ajustes finos. Defina a variável de
ambiente `QUALIA_DYNAMIC_RISK_DEFAULTS` para apontar para outro caminho. Valores
especificados na configuração JSON ou por variáveis `QUALIA_DYN_RISK_*` têm
precedência sobre o YAML. Consulte também
[environment_variables.md](environment_variables.md) para a lista completa de
variáveis configuráveis.
