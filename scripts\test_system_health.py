#!/usr/bin/env python3
"""
Script para testar a saúde geral do sistema QUALIA
"""

import sys
import os
import importlib
import traceback

# Adicionar src ao path
sys.path.insert(0, 'src')

def test_import(module_name, description):
    """Testa um import específico"""
    try:
        importlib.import_module(module_name)
        print(f"✅ {description}")
        return True
    except Exception as e:
        print(f"❌ {description}: {e}")
        return False

def main():
    """Função principal de teste"""
    print("🔍 TESTANDO SAÚDE DO SISTEMA QUALIA")
    print("=" * 50)
    
    success_count = 0
    total_tests = 0
    
    # Testes de imports críticos
    tests = [
        ("qualia.config", "Configuração base"),
        ("qualia.config.settings", "Settings de configuração"),
        ("qualia.core.qast_core", "QAST Core"),
        ("qualia.core.emergence", "Emergence Operator"),
        ("qualia.core.observer", "Observer Operator"),
        ("qualia.core.resonance", "Resonance Operator"),
        ("qualia.memory.system", "Sistema de memória"),
        ("qualia.market.base_integration", "Integração de mercado"),
        ("qualia.qualia_trading_system", "Sistema principal de trading"),
    ]
    
    for module, description in tests:
        total_tests += 1
        if test_import(module, description):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 RESULTADO: {success_count}/{total_tests} testes passaram")
    
    if success_count == total_tests:
        print("🎉 SISTEMA QUALIA TOTALMENTE FUNCIONAL!")
        
        # Teste adicional: instanciar o trader
        try:
            from qualia.qualia_trading_system import QUALIARealTimeTrader
            print("✅ Classe principal QUALIARealTimeTrader importável")
            
            # Testar configuração básica
            import qualia.config as config
            print(f"✅ Configuração carregada: {type(config.config).__name__}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erro ao testar classe principal: {e}")
            return False
    else:
        print("⚠️ SISTEMA COM PROBLEMAS - verifique os erros acima")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 