{"log_level": "DEBUG", "verbose_ticker_logging": true, "dedup_interval": 5.0, "handlers": {"console": {"level": "DEBUG"}, "rotating_file": {"level": "DEBUG", "filename": "logs/qualia.log", "maxBytes": 10485760, "backupCount": 5}}, "tracing": {"service_name": "qualia-market", "exporter": "console"}, "metrics": {"filter_hold_positions": true, "log_directory": "logs/metrics", "max_queue_size": 1000, "flush_interval": 1.0, "enable_compression": true}}