"""
Sistema de Monitoramento Quântico QUALIA
Monitora métricas de sensibilidade, coerência e performance do sistema quântico
"""

import asyncio
import time
from collections import deque
from dataclasses import dataclass
from typing import Any, Deque, Dict, List, Optional
import numpy as np
from datetime import datetime, timezone

from ..utils.logger import get_logger
from ..core.universe import QUALIAQuantumUniverse
from ..memory.quantum_pattern_memory import QuantumPatternMemory
from ..metacognition.analyzer import MetacognitionAnalyzer

logger = get_logger(__name__)


@dataclass
class QuantumMetrics:
    """Métricas instantâneas do sistema quântico"""
    timestamp: datetime
    sensitivity_level: float
    coherence_score: float
    entanglement_depth: float
    otoc_magnitude: float
    pattern_recognition_rate: float
    quantum_advantage_factor: float
    regime_classification: str
    harmonic_resonance: float
    temporal_correlation: float


class QuantumPerformanceMonitor:
    """Monitor de performance do sistema quântico QUALIA"""

    def __init__(
        self,
        universe: Optional[QUALIAQuantumUniverse] = None,
        pattern_memory: Optional[QuantumPatternMemory] = None,
        metacognition: Optional[MetacognitionAnalyzer] = None,
        history_size: int = 1000,
        alert_thresholds: Optional[Dict[str, float]] = None
    ):
        self.universe = universe
        self.pattern_memory = pattern_memory
        self.metacognition = metacognition

        # Histórico de métricas
        self.metrics_history: Deque[QuantumMetrics] = deque(maxlen=history_size)
        self.performance_history: Deque[Dict[str, Any]] = deque(maxlen=history_size)

        # Thresholds para alertas
        self.alert_thresholds = alert_thresholds or {
            'min_coherence': 0.3,
            'min_sensitivity': 0.2,
            'max_noise_ratio': 0.8,
            'min_pattern_rate': 0.1,
            'min_quantum_advantage': 1.1
        }

        # Estado interno
        self.is_monitoring = False
        self.last_alert_time = {}
        self.alert_cooldown = 300  # 5 minutos

        # Estatísticas agregadas
        self.daily_stats = {}
        self.session_start = datetime.now(timezone.utc)

        logger.info("🔬 QuantumPerformanceMonitor inicializado")

    async def start_monitoring(self, interval: float = 30.0):
        """Inicia o monitoramento contínuo"""
        self.is_monitoring = True
        logger.info(f"🔄 Iniciando monitoramento quântico (intervalo: {interval}s)")

        while self.is_monitoring:
            try:
                await self._collect_metrics()
                await self._analyze_performance()
                await self._check_alerts()
                await asyncio.sleep(interval)
            except Exception as e:
                logger.error(f"❌ Erro no monitoramento quântico: {e}")
                await asyncio.sleep(interval)

    def stop_monitoring(self):
        """Para o monitoramento"""
        self.is_monitoring = False
        logger.info("⏹️ Monitoramento quântico parado")

    async def _collect_metrics(self):
        """Coleta métricas do sistema quântico"""
        try:
            now = datetime.now(timezone.utc)

            # Métricas do universo quântico
            sensitivity = await self._get_sensitivity_level()
            coherence = await self._get_coherence_score()
            entanglement = await self._get_entanglement_depth()
            otoc = await self._get_otoc_magnitude()

            # Métricas de padrões
            pattern_rate = await self._get_pattern_recognition_rate()

            # Métricas de performance
            quantum_advantage = await self._calculate_quantum_advantage()
            regime = await self._classify_market_regime()

            # Métricas harmônicas
            harmonic_resonance = await self._get_harmonic_resonance()
            temporal_correlation = await self._get_temporal_correlation()

            metrics = QuantumMetrics(
                timestamp=now,
                sensitivity_level=sensitivity,
                coherence_score=coherence,
                entanglement_depth=entanglement,
                otoc_magnitude=otoc,
                pattern_recognition_rate=pattern_rate,
                quantum_advantage_factor=quantum_advantage,
                regime_classification=regime,
                harmonic_resonance=harmonic_resonance,
                temporal_correlation=temporal_correlation
            )

            self.metrics_history.append(metrics)

            logger.debug(f"📊 Métricas coletadas: Sensibilidade={sensitivity:.3f}, "
                        f"Coerência={coherence:.3f}, Vantagem Quântica={quantum_advantage:.3f}")

        except Exception as e:
            logger.error(f"❌ Erro coletando métricas: {e}")

    async def _get_sensitivity_level(self) -> float:
        """
        YAA REFINEMENT: Calcula nível de sensibilidade ultra-adaptativo.

        Implementa:
        - Análise multi-modal de sensibilidade quântica
        - Detecção de padrões emergentes de sensibilidade
        - Calibração adaptativa baseada em performance histórica
        - Ressonância harmônica de sensibilidade
        """
        if not self.universe:
            return 0.5

        try:
            # === Análise Multi-Modal de Sensibilidade ===

            # 1. Sensibilidade baseada em OTOC
            otoc_sensitivity = await self._calculate_otoc_sensitivity()

            # 2. Sensibilidade baseada em coerência quântica
            coherence_sensitivity = await self._calculate_coherence_sensitivity()

            # 3. Sensibilidade baseada em entropia quântica
            entropy_sensitivity = await self._calculate_entropy_sensitivity()

            # 4. Sensibilidade baseada em variabilidade de medições
            measurement_sensitivity = await self._calculate_measurement_sensitivity()

            # 5. Sensibilidade harmônica emergente
            harmonic_sensitivity = await self._calculate_harmonic_sensitivity()

            # === Composição Adaptativa de Sensibilidades ===

            # Análise de contexto para pesos dinâmicos
            context_weights = self._calculate_context_weights()

            composite_sensitivity = (
                otoc_sensitivity * context_weights['otoc'] +
                coherence_sensitivity * context_weights['coherence'] +
                entropy_sensitivity * context_weights['entropy'] +
                measurement_sensitivity * context_weights['measurement'] +
                harmonic_sensitivity * context_weights['harmonic']
            )

            # === Calibração Histórica ===

            # Aplicar calibração baseada na performance histórica
            calibrated_sensitivity = self._apply_historical_calibration(composite_sensitivity)

            # Suavização temporal para estabilidade
            smoothed_sensitivity = self._apply_temporal_smoothing(calibrated_sensitivity)

            logger.debug(
                f"🔬 Sensitivity Analysis - OTOC: {otoc_sensitivity:.3f}, "
                f"Coherence: {coherence_sensitivity:.3f}, Entropy: {entropy_sensitivity:.3f}, "
                f"Measurement: {measurement_sensitivity:.3f}, Harmonic: {harmonic_sensitivity:.3f}, "
                f"Final: {smoothed_sensitivity:.3f}"
            )

            return smoothed_sensitivity

        except Exception as e:
            logger.warning(f"⚠️ Erro calculando sensibilidade ultra-adaptativa: {e}")
            return 0.5

    async def _calculate_otoc_sensitivity(self) -> float:
        """Calcular sensibilidade baseada em OTOC."""
        if not hasattr(self.universe, 'calculate_otoc'):
            return 0.5

        try:
            # Analisar histórico de OTOC para detectar variações
            if hasattr(self.universe, '_last_otoc_value'):
                current_otoc = self.universe._last_otoc_value or 0.5

                # OTOC próximo de 1.0 = baixo scrambling = alta sensibilidade
                # OTOC próximo de 0.0 = alto scrambling = baixa sensibilidade
                base_sensitivity = current_otoc

                # Analisar estabilidade do OTOC
                if hasattr(self.universe, '_otoc_constant_count'):
                    otoc_stability = max(0, 1.0 - self.universe._otoc_constant_count / 10.0)
                    base_sensitivity *= otoc_stability

                return float(np.clip(base_sensitivity, 0.0, 1.0))

            return 0.5

        except Exception as e:
            logger.debug(f"Erro no cálculo de sensibilidade OTOC: {e}")
            return 0.5

    async def _calculate_coherence_sensitivity(self) -> float:
        """Calcular sensibilidade baseada em coerência quântica."""
        try:
            # Verificar se há método de coerência
            if hasattr(self.universe, 'get_latest_metrics'):
                metrics = self.universe.get_latest_metrics()
                coherence_values = []

                if 'quantum_coherence' in metrics:
                    coherence_data = metrics['quantum_coherence']
                    if isinstance(coherence_data, list):
                        coherence_values = coherence_data[-5:]  # Últimos 5 valores
                    else:
                        coherence_values = [coherence_data]

                if coherence_values:
                    avg_coherence = np.mean(coherence_values)
                    coherence_stability = 1.0 - np.std(coherence_values) if len(coherence_values) > 1 else 1.0

                    # Alta coerência + estabilidade = alta sensibilidade
                    coherence_sensitivity = avg_coherence * coherence_stability
                    return float(np.clip(coherence_sensitivity, 0.0, 1.0))

            return 0.5

        except Exception as e:
            logger.debug(f"Erro no cálculo de sensibilidade de coerência: {e}")
            return 0.5

    async def _calculate_entropy_sensitivity(self) -> float:
        """Calcular sensibilidade baseada em entropia quântica."""
        try:
            if hasattr(self.universe, 'get_latest_metrics'):
                metrics = self.universe.get_latest_metrics()
                entropy_values = []

                if 'quantum_entropy' in metrics:
                    entropy_data = metrics['quantum_entropy']
                    if isinstance(entropy_data, list):
                        entropy_values = entropy_data[-5:]
                    else:
                        entropy_values = [entropy_data]

                if entropy_values:
                    avg_entropy = np.mean(entropy_values)

                    # Baixa entropia = alta ordem = alta sensibilidade
                    entropy_sensitivity = 1.0 - avg_entropy

                    # Considerar variabilidade da entropia
                    if len(entropy_values) > 1:
                        entropy_variability = np.std(entropy_values)
                        # Baixa variabilidade = alta estabilidade = alta sensibilidade
                        stability_factor = 1.0 - entropy_variability
                        entropy_sensitivity *= stability_factor

                    return float(np.clip(entropy_sensitivity, 0.0, 1.0))

            return 0.5

        except Exception as e:
            logger.debug(f"Erro no cálculo de sensibilidade de entropia: {e}")
            return 0.5

    async def _calculate_measurement_sensitivity(self) -> float:
        """Calcular sensibilidade baseada na variabilidade de medições."""
        try:
            if hasattr(self.universe, 'get_latest_metrics'):
                metrics = self.universe.get_latest_metrics()

                if 'measurement_counts' in metrics:
                    counts = metrics['measurement_counts']
                    if isinstance(counts, dict):
                        count_values = list(counts.values())
                        if count_values:
                            # Analisar distribuição das medições
                            count_std = np.std(count_values)
                            count_mean = np.mean(count_values)

                            # Baixa variabilidade relativa = medições consistentes = alta sensibilidade
                            if count_mean > 0:
                                relative_std = count_std / count_mean
                                measurement_sensitivity = 1.0 - min(relative_std, 1.0)
                                return float(np.clip(measurement_sensitivity, 0.0, 1.0))

            return 0.5

        except Exception as e:
            logger.debug(f"Erro no cálculo de sensibilidade de medições: {e}")
            return 0.5

    async def _calculate_harmonic_sensitivity(self) -> float:
        """Calcular sensibilidade baseada em ressonância harmônica."""
        try:
            # Analisar padrões harmônicos na história de métricas
            if len(self.metrics_history) >= 10:
                # Extrair séries temporais de diferentes métricas
                sensitivity_series = [m.sensitivity_level for m in self.metrics_history[-10:]]
                coherence_series = [m.coherence_score for m in self.metrics_history[-10:]]

                # Calcular correlação cruzada para detectar harmônicos
                from scipy.stats import pearsonr

                # Análise de autocorrelação (padrões repetitivos)
                autocorr_sensitivity = self._calculate_autocorrelation(sensitivity_series)
                autocorr_coherence = self._calculate_autocorrelation(coherence_series)

                # Análise de correlação cruzada (sincronização entre métricas)
                if len(sensitivity_series) == len(coherence_series):
                    cross_corr, _ = pearsonr(sensitivity_series, coherence_series)
                    cross_corr = abs(cross_corr) if not np.isnan(cross_corr) else 0
                else:
                    cross_corr = 0

                # Combinar análises harmônicas
                harmonic_score = (
                    autocorr_sensitivity * 0.4 +
                    autocorr_coherence * 0.4 +
                    cross_corr * 0.2
                )

                return float(np.clip(harmonic_score, 0.0, 1.0))

            return 0.0

        except Exception as e:
            logger.debug(f"Erro no cálculo de sensibilidade harmônica: {e}")
            return 0.0

    def _calculate_autocorrelation(self, series: List[float]) -> float:
        """Calcular autocorrelação para detectar padrões harmônicos."""
        if len(series) < 4:
            return 0.0

        try:
            # Calcular autocorrelação com lag 1
            autocorr = np.corrcoef(series[:-1], series[1:])[0, 1]
            return abs(autocorr) if not np.isnan(autocorr) else 0.0
        except Exception:
            return 0.0

    def _calculate_context_weights(self) -> Dict[str, float]:
        """Calcular pesos contextuais para composição de sensibilidade."""
        # Determinar regime atual baseado no histórico
        if len(self.metrics_history) >= 3:
            recent_metrics = self.metrics_history[-3:]
            avg_sensitivity = np.mean([m.sensitivity_level for m in recent_metrics])
            avg_coherence = np.mean([m.coherence_score for m in recent_metrics])

            # Ajustar pesos baseado no regime
            if avg_sensitivity > 0.7 and avg_coherence > 0.6:
                # Regime de alta sensibilidade - enfatizar estabilidade
                return {
                    'otoc': 0.25,
                    'coherence': 0.30,
                    'entropy': 0.25,
                    'measurement': 0.15,
                    'harmonic': 0.05
                }
            elif avg_sensitivity < 0.3 or avg_coherence < 0.3:
                # Regime de baixa sensibilidade - enfatizar OTOC e harmônicos
                return {
                    'otoc': 0.35,
                    'coherence': 0.20,
                    'entropy': 0.20,
                    'measurement': 0.10,
                    'harmonic': 0.15
                }

        # Pesos padrão balanceados
        return {
            'otoc': 0.30,
            'coherence': 0.25,
            'entropy': 0.25,
            'measurement': 0.15,
            'harmonic': 0.05
        }

    def _apply_historical_calibration(self, sensitivity: float) -> float:
        """Aplicar calibração baseada na performance histórica."""
        if len(self.performance_history) < 5:
            return sensitivity

        try:
            # Analisar performance recente
            recent_performance = self.performance_history[-5:]
            avg_quantum_advantage = np.mean([p.get('quantum_advantage', 1.0) for p in recent_performance])

            # Calibrar baseado na vantagem quântica observada
            if avg_quantum_advantage > 1.5:
                # Performance boa - manter/aumentar sensibilidade
                calibration_factor = 1.1
            elif avg_quantum_advantage < 1.1:
                # Performance ruim - reduzir sensibilidade
                calibration_factor = 0.9
            else:
                # Performance normal
                calibration_factor = 1.0

            return sensitivity * calibration_factor

        except Exception as e:
            logger.debug(f"Erro na calibração histórica: {e}")
            return sensitivity

    def _apply_temporal_smoothing(self, sensitivity: float) -> float:
        """Aplicar suavização temporal para estabilidade."""
        if not hasattr(self, '_last_sensitivity'):
            self._last_sensitivity = sensitivity
            return sensitivity

        # Suavização exponencial
        smoothing_factor = 0.3
        smoothed = (1 - smoothing_factor) * self._last_sensitivity + smoothing_factor * sensitivity
        self._last_sensitivity = smoothed

        return smoothed

    async def _get_coherence_score(self) -> float:
        """Calcula score de coerência quântica"""
        if not self.universe:
            return 0.5

        try:
            # Verificar se há método de coerência
            if hasattr(self.universe, 'calculate_coherence'):
                return await self.universe.calculate_coherence()

            # Fallback: usar estabilidade das medições
            if hasattr(self.universe, 'measurement_history'):
                history = self.universe.measurement_history[-20:]
                if len(history) > 1:
                    stability = 1.0 - np.std(history) / (np.mean(history) + 1e-8)
                    return max(0.0, min(1.0, stability))

            return 0.5
        except Exception as e:
            logger.warning(f"⚠️ Erro calculando coerência: {e}")
            return 0.5

    async def _get_entanglement_depth(self) -> float:
        """Calcula profundidade de emaranhamento"""
        if not self.universe:
            return 0.0

        try:
            # Verificar se há qubits emaranhados
            if hasattr(self.universe, 'entangled_qubits'):
                total_qubits = getattr(self.universe, 'num_qubits', 8)
                entangled = len(self.universe.entangled_qubits)
                return entangled / total_qubits if total_qubits > 0 else 0.0

            # Fallback: usar número de operadores ativos
            if hasattr(self.universe, 'active_operators'):
                return len(self.universe.active_operators) / 10.0

            return 0.3  # Valor padrão moderado
        except Exception as e:
            logger.warning(f"⚠️ Erro calculando emaranhamento: {e}")
            return 0.0

    async def _get_otoc_magnitude(self) -> float:
        """Calcula magnitude média do OTOC"""
        if not self.universe:
            return 0.0

        try:
            if hasattr(self.universe, 'last_otoc_values'):
                values = self.universe.last_otoc_values[-10:]
                if values:
                    return np.mean([abs(v) for v in values])

            return 0.0
        except Exception as e:
            logger.warning(f"⚠️ Erro calculando OTOC: {e}")
            return 0.0

    async def _get_pattern_recognition_rate(self) -> float:
        """Calcula taxa de reconhecimento de padrões"""
        if not self.pattern_memory:
            return 0.0

        try:
            # Verificar padrões reconhecidos recentemente
            if hasattr(self.pattern_memory, 'recent_recognitions'):
                recent = self.pattern_memory.recent_recognitions[-60:]  # Último minuto
                return len(recent) / 60.0  # Taxa por segundo

            # Fallback: usar total de padrões
            if hasattr(self.pattern_memory, 'patterns'):
                return min(1.0, len(self.pattern_memory.patterns) / 100.0)

            return 0.0
        except Exception as e:
            logger.warning(f"⚠️ Erro calculando taxa de padrões: {e}")
            return 0.0

    async def _calculate_quantum_advantage(self) -> float:
        """Calcula fator de vantagem quântica"""
        try:
            # Se temos histórico, comparar performance quântica vs clássica
            if len(self.performance_history) > 10:
                recent_performance = list(self.performance_history)[-10:]
                quantum_scores = [p.get('quantum_score', 1.0) for p in recent_performance]
                classical_scores = [p.get('classical_score', 1.0) for p in recent_performance]

                if classical_scores and all(s > 0 for s in classical_scores):
                    quantum_avg = np.mean(quantum_scores)
                    classical_avg = np.mean(classical_scores)
                    return quantum_avg / classical_avg

            # Fallback: calcular baseado em métricas atuais
            if self.metrics_history:
                last_metrics = self.metrics_history[-1]
                base_advantage = 1.0
                base_advantage += last_metrics.sensitivity_level * 0.5
                base_advantage += last_metrics.coherence_score * 0.3
                base_advantage += last_metrics.entanglement_depth * 0.2
                return base_advantage

            return 1.0  # Sem vantagem
        except Exception as e:
            logger.warning(f"⚠️ Erro calculando vantagem quântica: {e}")
            return 1.0

    async def _classify_market_regime(self) -> str:
        """Classifica regime atual do mercado"""
        try:
            if not self.metrics_history:
                return "unknown"

            last_metrics = self.metrics_history[-1]

            # Classificar baseado em sensibilidade e coerência
            if last_metrics.sensitivity_level > 0.7 and last_metrics.coherence_score > 0.6:
                return "high_sensitivity"
            elif last_metrics.sensitivity_level < 0.3 or last_metrics.coherence_score < 0.3:
                return "low_sensitivity"
            elif last_metrics.otoc_magnitude > 0.5:
                return "high_volatility"
            else:
                return "normal"
        except Exception as e:
            logger.warning(f"⚠️ Erro classificando regime: {e}")
            return "unknown"

    async def _get_harmonic_resonance(self) -> float:
        """Calcula ressonância harmônica"""
        try:
            if not self.universe or not hasattr(self.universe, 'harmonic_analyzer'):
                return 0.0

            return getattr(self.universe.harmonic_analyzer, 'current_resonance', 0.0)
        except Exception as e:
            logger.warning(f"⚠️ Erro calculando ressonância: {e}")
            return 0.0

    async def _get_temporal_correlation(self) -> float:
        """Calcula correlação temporal"""
        try:
            if len(self.metrics_history) < 5:
                return 0.0

            # Calcular correlação entre métricas temporais
            sensitivity_series = [m.sensitivity_level for m in list(self.metrics_history)[-10:]]
            coherence_series = [m.coherence_score for m in list(self.metrics_history)[-10:]]

            if len(sensitivity_series) > 2:
                correlation = np.corrcoef(sensitivity_series, coherence_series)[0, 1]
                return abs(correlation) if not np.isnan(correlation) else 0.0

            return 0.0
        except Exception as e:
            logger.warning(f"⚠️ Erro calculando correlação temporal: {e}")
            return 0.0

    async def _analyze_performance(self):
        """Analisa performance e atualiza estatísticas"""
        try:
            if not self.metrics_history:
                return

            current_metrics = self.metrics_history[-1]

            # Calcular scores de performance
            quantum_score = self._calculate_quantum_performance_score(current_metrics)
            classical_score = self._calculate_classical_baseline_score()

            performance_data = {
                'timestamp': current_metrics.timestamp,
                'quantum_score': quantum_score,
                'classical_score': classical_score,
                'advantage_ratio': quantum_score / classical_score if classical_score > 0 else 1.0,
                'regime': current_metrics.regime_classification,
                'alerts_triggered': []
            }

            self.performance_history.append(performance_data)

            # Atualizar estatísticas diárias
            await self._update_daily_stats(current_metrics, performance_data)

        except Exception as e:
            logger.error(f"❌ Erro analisando performance: {e}")

    def _calculate_quantum_performance_score(self, metrics: QuantumMetrics) -> float:
        """Calcula score de performance quântica"""
        try:
            # Pesos para diferentes métricas
            weights = {
                'sensitivity': 0.25,
                'coherence': 0.20,
                'entanglement': 0.15,
                'pattern_rate': 0.20,
                'resonance': 0.10,
                'correlation': 0.10
            }

            score = (
                metrics.sensitivity_level * weights['sensitivity'] +
                metrics.coherence_score * weights['coherence'] +
                metrics.entanglement_depth * weights['entanglement'] +
                metrics.pattern_recognition_rate * weights['pattern_rate'] +
                metrics.harmonic_resonance * weights['resonance'] +
                metrics.temporal_correlation * weights['correlation']
            )

            return max(0.0, min(1.0, score))
        except Exception as e:
            logger.warning(f"⚠️ Erro calculando score quântico: {e}")
            return 0.5

    def _calculate_classical_baseline_score(self) -> float:
        """Calcula score baseline clássico para comparação"""
        try:
            # Score baseline fixo para comparação
            # Em implementação real, seria baseado em métricas clássicas
            return 0.6  # Baseline conservador
        except Exception as e:
            logger.warning(f"⚠️ Erro calculando baseline clássico: {e}")
            return 0.6

    async def _update_daily_stats(self, metrics: QuantumMetrics, performance: Dict[str, Any]):
        """Atualiza estatísticas diárias"""
        try:
            today = metrics.timestamp.date().isoformat()

            if today not in self.daily_stats:
                self.daily_stats[today] = {
                    'measurements': 0,
                    'avg_sensitivity': 0.0,
                    'avg_coherence': 0.0,
                    'avg_quantum_advantage': 0.0,
                    'regime_distribution': {},
                    'alerts_count': 0,
                    'peak_performance': 0.0
                }

            stats = self.daily_stats[today]
            stats['measurements'] += 1

            # Atualizar médias (média móvel simples)
            n = stats['measurements']
            stats['avg_sensitivity'] = ((n-1) * stats['avg_sensitivity'] + metrics.sensitivity_level) / n
            stats['avg_coherence'] = ((n-1) * stats['avg_coherence'] + metrics.coherence_score) / n
            stats['avg_quantum_advantage'] = ((n-1) * stats['avg_quantum_advantage'] + performance['advantage_ratio']) / n

            # Atualizar distribuição de regimes
            regime = metrics.regime_classification
            stats['regime_distribution'][regime] = stats['regime_distribution'].get(regime, 0) + 1

            # Atualizar pico de performance
            stats['peak_performance'] = max(stats['peak_performance'], performance['quantum_score'])

        except Exception as e:
            logger.warning(f"⚠️ Erro atualizando estatísticas diárias: {e}")

    async def _check_alerts(self):
        """Verifica condições de alerta"""
        try:
            if not self.metrics_history:
                return

            current_metrics = self.metrics_history[-1]
            now = time.time()
            alerts = []

            # Verificar coerência baixa
            if current_metrics.coherence_score < self.alert_thresholds['min_coherence']:
                if self._should_trigger_alert('low_coherence', now):
                    alerts.append(f"⚠️ Coerência baixa: {current_metrics.coherence_score:.3f}")

            # Verificar sensibilidade baixa
            if current_metrics.sensitivity_level < self.alert_thresholds['min_sensitivity']:
                if self._should_trigger_alert('low_sensitivity', now):
                    alerts.append(f"⚠️ Sensibilidade baixa: {current_metrics.sensitivity_level:.3f}")

            # Verificar taxa de padrões baixa
            if current_metrics.pattern_recognition_rate < self.alert_thresholds['min_pattern_rate']:
                if self._should_trigger_alert('low_pattern_rate', now):
                    alerts.append(f"⚠️ Taxa de padrões baixa: {current_metrics.pattern_recognition_rate:.3f}")

            # Verificar vantagem quântica baixa
            if current_metrics.quantum_advantage_factor < self.alert_thresholds['min_quantum_advantage']:
                if self._should_trigger_alert('low_quantum_advantage', now):
                    alerts.append(f"⚠️ Vantagem quântica baixa: {current_metrics.quantum_advantage_factor:.3f}")

            # Log dos alertas
            for alert in alerts:
                logger.warning(alert)

            # Atualizar histórico de performance com alertas
            if self.performance_history:
                self.performance_history[-1]['alerts_triggered'] = alerts

        except Exception as e:
            logger.error(f"❌ Erro verificando alertas: {e}")

    def _should_trigger_alert(self, alert_type: str, current_time: float) -> bool:
        """Verifica se deve disparar alerta (cooldown)"""
        last_time = self.last_alert_time.get(alert_type, 0)
        if current_time - last_time > self.alert_cooldown:
            self.last_alert_time[alert_type] = current_time
            return True
        return False

    def get_current_status(self) -> Dict[str, Any]:
        """Retorna status atual do sistema"""
        if not self.metrics_history:
            return {"status": "no_data", "message": "Nenhuma métrica coletada ainda"}

        current = self.metrics_history[-1]
        recent_performance = list(self.performance_history)[-5:] if self.performance_history else []

        return {
            "status": "active" if self.is_monitoring else "inactive",
            "timestamp": current.timestamp.isoformat(),
            "current_metrics": {
                "sensitivity": current.sensitivity_level,
                "coherence": current.coherence_score,
                "entanglement": current.entanglement_depth,
                "pattern_rate": current.pattern_recognition_rate,
                "quantum_advantage": current.quantum_advantage_factor,
                "regime": current.regime_classification
            },
            "recent_performance": recent_performance,
            "session_duration": (datetime.now(timezone.utc) - self.session_start).total_seconds(),
            "total_measurements": len(self.metrics_history),
            "daily_stats": self.daily_stats
        }

    def get_performance_report(self, hours: int = 24) -> Dict[str, Any]:
        """Gera relatório de performance"""
        try:
            cutoff_time = datetime.now(timezone.utc).timestamp() - (hours * 3600)

            # Filtrar métricas recentes
            recent_metrics = [
                m for m in self.metrics_history
                if m.timestamp.timestamp() > cutoff_time
            ]

            if not recent_metrics:
                return {"error": "Nenhuma métrica no período especificado"}

            # Calcular estatísticas
            sensitivities = [m.sensitivity_level for m in recent_metrics]
            coherences = [m.coherence_score for m in recent_metrics]
            advantages = [m.quantum_advantage_factor for m in recent_metrics]

            return {
                "period_hours": hours,
                "total_measurements": len(recent_metrics),
                "sensitivity": {
                    "avg": np.mean(sensitivities),
                    "min": np.min(sensitivities),
                    "max": np.max(sensitivities),
                    "std": np.std(sensitivities)
                },
                "coherence": {
                    "avg": np.mean(coherences),
                    "min": np.min(coherences),
                    "max": np.max(coherences),
                    "std": np.std(coherences)
                },
                "quantum_advantage": {
                    "avg": np.mean(advantages),
                    "min": np.min(advantages),
                    "max": np.max(advantages),
                    "std": np.std(advantages)
                },
                "regime_distribution": self._calculate_regime_distribution(recent_metrics),
                "performance_trend": self._calculate_performance_trend(recent_metrics)
            }
        except Exception as e:
            logger.error(f"❌ Erro gerando relatório: {e}")
            return {"error": str(e)}

    def _calculate_regime_distribution(self, metrics: List[QuantumMetrics]) -> Dict[str, float]:
        """Calcula distribuição de regimes"""
        try:
            regimes = [m.regime_classification for m in metrics]
            total = len(regimes)

            distribution = {}
            for regime in set(regimes):
                distribution[regime] = regimes.count(regime) / total

            return distribution
        except Exception as e:
            logger.warning(f"⚠️ Erro calculando distribuição de regimes: {e}")
            return {}

    def _calculate_performance_trend(self, metrics: List[QuantumMetrics]) -> str:
        """Calcula tendência de performance"""
        try:
            if len(metrics) < 5:
                return "insufficient_data"

            # Usar vantagem quântica como métrica principal
            advantages = [m.quantum_advantage_factor for m in metrics[-10:]]

            # Calcular tendência linear simples
            x = np.arange(len(advantages))
            slope = np.polyfit(x, advantages, 1)[0]

            if slope > 0.01:
                return "improving"
            elif slope < -0.01:
                return "declining"
            else:
                return "stable"
        except Exception as e:
            logger.warning(f"⚠️ Erro calculando tendência: {e}")
            return "unknown"


# Instância global para facilitar acesso
quantum_monitor: Optional[QuantumPerformanceMonitor] = None


def get_quantum_monitor() -> Optional[QuantumPerformanceMonitor]:
    """Retorna instância global do monitor quântico"""
    return quantum_monitor


def initialize_quantum_monitor(
    universe: Optional[QUALIAQuantumUniverse] = None,
    pattern_memory: Optional[QuantumPatternMemory] = None,
    metacognition: Optional[MetacognitionAnalyzer] = None,
    **kwargs
) -> QuantumPerformanceMonitor:
    """Inicializa monitor quântico global"""
    global quantum_monitor
    quantum_monitor = QuantumPerformanceMonitor(
        universe=universe,
        pattern_memory=pattern_memory,
        metacognition=metacognition,
        **kwargs
    )
    return quantum_monitor
