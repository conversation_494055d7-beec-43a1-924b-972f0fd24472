# QUALIA Holographic Universe Configuration
# Configurações para o sistema de trading holográfico

holographic_universe:
  # Parâmetros do campo holográfico
  field_size: [200, 200]
  time_steps: 500
  diffusion_rate: 0.25
  feedback_strength: 0.06
  
  # Configurações de análise wavelet
  wavelet_scales:
    min: 1
    max: 40
    step: 1
  
  # Thresholds para detecção de padrões
  pattern_detection:
    significance_threshold: 0.5
    confidence_threshold: 0.6
    min_history_length: 20
    analysis_window: 50
  
  # Configurações de sinais de trading
  trading_signals:
    min_strength: 0.7
    proximity_threshold: 30
    confidence_threshold: 0.6
    
  # Mapeamento de símbolos no campo
  symbol_positions:
    BTC: "auto"  # Calculado automaticamente
    ETH: "auto"
    ADA: "auto"
    SOL: "auto"
    MATIC: "auto"
    DOT: "auto"
    LINK: "auto"
    UNI: "auto"
    
  # Regiões de modalidades
  modality_regions:
    audio: [50, 50]
    visual: [150, 50]
    sentiment: [50, 150]
    news: [150, 150]
    market: [100, 100]

# Configurações da extensão Farsight
farsight_extension:
  # Parâmetros de conversão insight -> evento
  event_conversion:
    amplitude_multiplier: 1.5
    spatial_base: 5.0
    spatial_velocity_factor: 20.0
    temporal_base: 2.0
    temporal_curvature_factor: 8.0
    
  # Configurações de simulação
  simulation:
    steps: 100
    step_duration: 0.1  # segundos
    analysis_interval: 10  # a cada N passos
    sleep_between_steps: 0.001  # segundos
    
  # Configurações de combinação de insights
  insight_combination:
    high_confidence_threshold: 0.8
    max_signals_summary: 5
    
# Configurações de monitoramento
monitoring:
  metrics:
    enabled: true
    field_energy: true
    field_entropy: true
    active_events: true
    patterns_detected: true
    trading_signals: true
    
  logging:
    level: "INFO"
    holographic_events: true
    pattern_detection: true
    signal_generation: true

# Configurações de performance
performance:
  # Limites de memória
  max_field_history: 500
  max_patterns_history: 1000
  max_events_queue: 1000
  
  # Otimizações
  pattern_deduplication: true
  event_time_threshold: 1e-6
  
# Configurações experimentais
experimental:
  # Features em desenvolvimento
  retrocausal_feedback: false
  quantum_entanglement: false
  consciousness_integration: false
  
  # Parâmetros experimentais
  retrocausal_strength: 0.1
  entanglement_range: 50
  consciousness_influence: 0.05 