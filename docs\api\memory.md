# Mó<PERSON>los de Memória

O framework QUALIA oferece três armazenamentos principais em memória:
`ShortTermMemory`, `QuantumPatternMemory` e `ExperienceReplay`.
Todos suportam segurança opcional para threads por meio do parâmetro `thread_safe`
nos construtores. Quando habilitado, um `threading.Lock` serializa mutações como
`store`, `delete` e `clear` (ou `sample` no caso do `ExperienceReplay`) para evitar
condições de corrida em ambientes multi-thread.


`QuantumPatternMemory` utiliza o valor definido em `similarity_threshold`,
lido de `config/strategy_parameters.json`, como limiar padrão para
`retrieve_similar_patterns`. Caso seja preciso ajustar esse limite
dinamicamente, basta informar `adaptive=True` na chamada.

`QuantumPatternMemory` também reduz vetores com 4096 valores para uma dimensão
menor ao restaurar memórias persistidas. O parâmetro `qpm_config.pca_target_dim`
define a dimensionalidade final após a aplicação do PCA e preenche o campo
`vector_dim` de cada padrão carregado com esse valor. Cada `vector_dim` forma
uma partição distinta da memória, de forma que a quantidade de padrões em cada
dimensão influencia o ajuste dinâmico do `similarity_threshold`.

## Warm-start com Backtests

Quando `enable_warmstart` está habilitado, o construtor de
`QuantumPatternMemory` aceita o parâmetro `backtest_import_paths`, uma lista de
arquivos JSON produzidos por backtests vencedores. Esses padrões são importados
logo na inicialização e complementam a memória antes da geração de padrões
sintéticos, caso necessária.

Para verificar a efetividade da técnica, monitore a métrica `win_rate` das
estratégias comparando execuções com e sem o QPM habilitado.


### Ajuste do warm-start

Os cenários sintéticos utilizados na fase de warm-start são definidos em
`config/warmstart.yaml`. Você pode substituir esse arquivo definindo a variável
de ambiente `QPM_WARMSTART_CONFIG` ou passando uma lista de cenários pelo
parâmetro `warmstart_scenarios` ao instanciar `QuantumPatternMemory`.
Cada cenário pode opcionalmente informar `num_variations` para controlar o
número de variações geradas.

### Atualização automática dos cenários

`WarmStartManager` registra o `quantum_score` e o PnL obtido a cada padrão
gerado. O método `update_scenarios` consolida essas métricas e reescreve
`warmstart.yaml` com as médias calculadas.

```python
qpm.warmstart_manager.update_scenarios()
# ou para atualizações periódicas
qpm.warmstart_manager.start_auto_update(interval_seconds=3600)
```

Também é possível acionar manualmente via endpoint HTTP
`POST /memory/warmstart/update` quando a API Flask está ativa. A rota exige que
a instância do `QuantumPatternMemory` esteja disponível em `QualiaState.qpm_memory`.


### `GET /memory/snapshot`

Este endpoint retorna os padrões persistidos após a data informada.
O parâmetro opcional `since` pode ser um timestamp em segundos
ou uma data no formato ISO 8601 (`YYYY-MM-DDTHH:MM:SS`).
Se omitido, todos os registros são considerados.

#### Estrutura de resposta

```json
{
  "success": true,
  "items": [
    {
      "id": "abc",
      "timestamp": 1689700000.0
    }
  ]
}
```

Os itens são ordenados pelo campo `timestamp` e correspondem ao conteúdo do
arquivo definido em `QUALIA_QPM_MEMORY_FILE` (ou `settings.qpm_memory_file`).

### `GET /memory/report`

Retorna um relatório detalhado do estado da `QuantumPatternMemory`.
Quando o módulo de metacognição está ativo, o relatório inclui
o nível de foco atual e as métricas coletadas.

#### Exemplo de resposta

```json
{
  "success": true,
  "report": {
    "total_patterns": 100,
    "metacognition": {
      "focus": 0.8,
      "metrics": []
    }
  }
}
```


## Recuperação Adaptativa

`QuantumPatternMemory.retrieve_similar_patterns` reduz automaticamente o
limite de similaridade quando há pouca memória disponível. Caso o número de
padrões armazenados para uma dimensão seja menor que `warmstart_min_patterns`, o
método diminui o limite ativo em `0.05` (configurável via `step`) até que pelo
menos um padrão seja retornado ou o limite mínimo configurado seja atingido.

Quando mais padrões se acumulam, o limite de similaridade base cresce
automaticamente. O valor inicial é lido do parâmetro
`qpm_config.similarity_threshold` em `config/strategy_parameters.json` e serve
como limiar de recuperação padrão para `retrieve_similar_patterns`. Com até
`300` padrões o valor permanece em `0.25`. A partir daí cresce de forma
logarítmica conforme `0.25 + 0.3*log10(n/300)`, onde `n` é o total de padrões
armazenados, limitado a `0.95`.

## Uso Síncrono vs Assíncrono

Ao instanciar `QuantumPatternMemory` com `async_safe=True` os métodos
decorados com `with_lock` retornam *coroutines*. É necessário utilizá-los com
`await` em um loop assíncrono. Caso contrário um `TypeError` será levantado.

Exemplo síncrono:

```python
qpm = QuantumPatternMemory(enable_warmstart=False)
packet = QuantumSignaturePacket(vector=[1.0] * 4, metrics={})
qpm.store_pattern(packet, market_snapshot={}, outcome={})
```

Exemplo assíncrono:

```python
qpm = QuantumPatternMemory(enable_warmstart=False, async_safe=True)
packet = QuantumSignaturePacket(vector=[1.0] * 4, metrics={})
await qpm.store_pattern(packet, market_snapshot={}, outcome={})
```

### Integração com MemorySystem e HolographicMemory

Antes de iniciar o `MemorySystem`, crie um arquivo `.env` contendo a variável
`QUALIA_SECRET_KEY`. O valor precisa ter no mínimo 32 bytes; do contrário a
instância aborta a inicialização.

Para utilizar o `HolographicMemory` dentro do `MemorySystem`, informe os parâmetros `holo_max_items` e `holo_half_life` na configuração inicial:

```python
from src.qualia.memory.system import MemorySystem

config = {
    "holo_max_items": 5000,
    "holo_half_life": 3600.0,
}
memory = MemorySystem(config)
```

A partir dessa instância você pode armazenar vetores de padrões e consultá-los por similaridade:

```python
vector = [0.1, 0.7, 0.2]
metadata = {"pattern_id": "abc"}
memory.store_market_pattern(vector, metadata)

results = memory.query_market_patterns(vector, top_n=3)
for item in results:
    print(item["metadata"], item["similarity"])
```

### Recomendações de Tuning

- **`half_life`** controla o ritmo de decaimento temporal dos itens. Use valores
  menores (por exemplo, `600.0`) para priorizar padrões recentes e maiores
  para históricos mais longos.
- **`max_items`** define a capacidade total da memória. Ajuste de acordo com
  a quantidade de padrões gerados e a memória disponível na máquina.
  Evite valores muito altos se o ambiente possuir pouca RAM.

### Modo Experimental

Defina a variável de ambiente `QUALIA_EXPERIMENTAL=1` para ativar a
`HolographicMemory` e o **Retrocausal Operator Z** dentro do `MemorySystem`.
Esses componentes ainda estão em estágio experimental e devem ser habilitados
apenas quando a estabilidade não for crítica.

O sucesso da ativação pode ser verificado executando ao menos um benchmark de
`docs/benchmarks`. Por exemplo, o arquivo
[`benchmarks/qpm_retrieval_perf.md`](../../benchmarks/qpm_retrieval_perf.md)
apresenta ganhos de mais de 10x na latência de recuperação após habilitar as
otimizações relacionadas à memória holográfica.
