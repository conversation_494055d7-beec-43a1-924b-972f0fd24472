/* Revisado em 2025-06-13 por Codex */
/* 
 * QUALIA Trading System - Estilos CSS
 * Complementa o estilo quantum.css com elementos específicos para trading
 */

/* Estrutura da página de trading */
.trading-header {
    border-bottom: 1px solid rgba(72, 91, 196, 0.3);
}

.control-panel, .market-panel, .positions-section, .metrics-section {
    margin-bottom: 1.5rem;
}

.panel-row {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.panel-row .panel {
    flex: 1;
    min-width: 300px;
}

/* Status do sistema */
.system-status {
    display: flex;
    align-items: center;
    margin-left: auto;
    padding-right: 1rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    margin-right: 1rem;
}

.indicator-led {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #999;
    margin-right: 8px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.indicator-led.active {
    background-color: #4CAF50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.8);
}

.indicator-led.warning {
    background-color: #FFC107;
    box-shadow: 0 0 8px rgba(255, 193, 7, 0.8);
}

.indicator-led.error {
    background-color: #F44336;
    box-shadow: 0 0 8px rgba(244, 67, 54, 0.8);
}

/* Configuração do sistema */
.configuration-panel .panel-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.config-section {
    border-bottom: 1px solid rgba(72, 91, 196, 0.2);
    padding-bottom: 1.2rem;
}

.config-section:last-child {
    border-bottom: none;
}

.config-section h3 {
    font-size: 1.05rem;
    margin-bottom: 1rem;
    color: #485bc4;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #555;
}

.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-row .form-group {
    flex: 1;
}

.quantum-input, .quantum-select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: rgba(240, 240, 255, 0.8);
    font-family: 'Space Grotesk', sans-serif;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.quantum-input:focus, .quantum-select:focus {
    border-color: #485bc4;
    box-shadow: 0 0 0 2px rgba(72, 91, 196, 0.2);
    outline: none;
}

.toggle-container {
    display: flex;
    align-items: center;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin-right: 10px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #485bc4;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.toggle-label {
    font-size: 0.9rem;
}

.trading-pairs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.8rem;
}

.pair-checkbox {
    display: flex;
    align-items: center;
}

.pair-checkbox input[type="checkbox"] {
    margin-right: 8px;
}

.system-controls {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

/* Botões */
.quantum-button {
    padding: 10px 16px;
    border: none;
    border-radius: 4px;
    background-color: #333;
    color: white;
    font-family: 'Space Grotesk', sans-serif;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.1s;
}

.quantum-button:hover {
    background-color: #444;
}

.quantum-button:active {
    transform: translateY(1px);
}

.quantum-button.primary {
    background-color: #485bc4;
}

.quantum-button.primary:hover {
    background-color: #3a4bb8;
}

.quantum-button.success {
    background-color: #4CAF50;
}

.quantum-button.success:hover {
    background-color: #3d9140;
}

.quantum-button.danger {
    background-color: #F44336;
}

.quantum-button.danger:hover {
    background-color: #e53935;
}

.quantum-button.small {
    padding: 6px 12px;
    font-size: 0.85rem;
}

.quantum-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* Dados de mercado */
.market-tabs {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.tab-headers {
    display: flex;
    border-bottom: 1px solid rgba(72, 91, 196, 0.3);
}

.tab-header {
    padding: 10px 16px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-size: 0.9rem;
}

.tab-header:hover {
    background-color: rgba(72, 91, 196, 0.1);
}

.tab-header.active {
    border-bottom: 2px solid #485bc4;
    color: #485bc4;
}

.tab-content {
    flex: 1;
    overflow: hidden;
}

.tab-pane {
    display: none;
    height: 100%;
}

.tab-pane.active {
    display: flex;
    flex-direction: column;
}

.market-chart-container {
    flex: 1;
    min-height: 300px;
    margin-bottom: 1rem;
}

.market-chart {
    width: 100%;
    height: 100%;
    min-height: 300px;
}

.market-data-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-top: 0.5rem;
}

.market-data-item {
    padding: 0.8rem;
    background-color: rgba(240, 240, 255, 0.5);
    border-radius: 4px;
    text-align: center;
}

.item-label {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.3rem;
}

.item-value {
    font-size: 1.1rem;
    font-weight: 500;
}

.item-value.positive {
    color: #4CAF50;
}

.item-value.negative {
    color: #F44336;
}

.item-value.neutral {
    color: #FF9800;
}

/* Tabelas */
.positions-table-container, .history-table-container {
    overflow-x: auto;
    margin-bottom: 1rem;
}

.quantum-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.quantum-table th, .quantum-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid rgba(72, 91, 196, 0.2);
}

.quantum-table th {
    background-color: rgba(72, 91, 196, 0.1);
    font-weight: 500;
    color: #333;
}

.quantum-table tr:hover {
    background-color: rgba(72, 91, 196, 0.05);
}

.quantum-table .empty-row td {
    text-align: center;
    padding: 20px;
    color: #999;
}

.table-action-btn {
    padding: 4px 8px;
    font-size: 0.8rem;
    margin-right: 4px;
}

/* Métricas */
.metrics-grid, .quantum-metrics-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
}

.metric-card {
    padding: 1rem;
    background-color: rgba(240, 240, 255, 0.5);
    border-radius: 4px;
    text-align: center;
}

.metric-title {
    font-size: 0.9rem;
    color: #555;
    margin-bottom: 0.5rem;
}

.metric-value {
    font-size: 1.4rem;
    font-weight: 500;
    margin-bottom: 0.2rem;
}

.metric-value.positive {
    color: #4CAF50;
}

.metric-value.negative {
    color: #F44336;
}

.metric-subvalue {
    font-size: 0.8rem;
    color: #777;
}

.chart-container, .quantum-chart-container {
    height: 200px;
}

.performance-chart, .quantum-chart {
    width: 100%;
    height: 100%;
}

/* Notificações */
.notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 350px;
}

.notification {
    padding: 12px 16px;
    border-radius: 4px;
    background-color: #333;
    color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.3s ease-out forwards;
    display: flex;
    align-items: flex-start;
}

.notification.success {
    background-color: #4CAF50;
}

.notification.warning {
    background-color: #FFC107;
    color: #333;
}

.notification.error {
    background-color: #F44336;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 500;
    margin-bottom: 4px;
}

.notification-message {
    font-size: 0.9rem;
}

.notification-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    margin-left: 8px;
    opacity: 0.7;
}

.notification.warning .notification-close {
    color: #333;
}

.notification-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s, visibility 0.3s;
}

.modal-overlay.active {
    visibility: visible;
    opacity: 1;
}

.modal-container {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transform: translateY(-20px);
    transition: transform 0.3s;
}

.modal-overlay.active .modal-container {
    transform: translateY(0);
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    margin: 0;
    font-size: 1.2rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    opacity: 0.7;
}

.modal-close:hover {
    opacity: 1;
}

.modal-content {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Responsividade */
@media (max-width: 768px) {
    .market-data-grid, .metrics-grid, .quantum-metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .panel-row {
        flex-direction: column;
    }
    
    .system-controls {
        flex-direction: column;
    }
    
    .quantum-button {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .market-data-grid, .metrics-grid, .quantum-metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        flex-direction: column;
    }
    
    .trading-pairs-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}