#!/usr/bin/env python3
"""
Script de teste para o sistema de logging detalhado da estratégia FWH
Demonstra os novos diagnósticos específicos para decisões de HOLD
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime, timedelta

# Adicionar o diretório raiz ao path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configurar logging para capturar os detalhes
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_detailed_logging.log')
    ]
)
logger = logging.getLogger(__name__)

try:
    from src.qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy
    from src.qualia.config.config_manager import ConfigManager
    from src.qualia.strategies.strategy_interface import TradingContext
    QUALIA_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ QUALIA não disponível: {e}")
    QUALIA_AVAILABLE = False


def create_test_data(periods=100, trend="sideways"):
    """Cria dados de teste com diferentes características"""
    dates = pd.date_range(start=datetime.now() - timedelta(hours=periods), periods=periods, freq='H')
    
    if trend == "uptrend":
        # Tendência de alta com ruído
        base_price = 50000
        trend_component = np.linspace(0, 2000, periods)
        noise = np.random.normal(0, 100, periods)
        prices = base_price + trend_component + noise
        
    elif trend == "downtrend":
        # Tendência de baixa com ruído
        base_price = 52000
        trend_component = np.linspace(0, -2000, periods)
        noise = np.random.normal(0, 100, periods)
        prices = base_price + trend_component + noise
        
    elif trend == "sideways":
        # Movimento lateral com ruído
        base_price = 51000
        noise = np.random.normal(0, 200, periods)
        prices = base_price + noise
        
    elif trend == "low_volume":
        # Dados com muito pouco movimento (deve gerar HOLD)
        base_price = 50000
        noise = np.random.normal(0, 10, periods)  # Muito pouco ruído
        prices = base_price + noise
        
    else:  # insufficient_data
        # Poucos dados (deve gerar HOLD por dados insuficientes)
        periods = 5  # Menos que fib_lookback
        dates = pd.date_range(start=datetime.now() - timedelta(hours=periods), periods=periods, freq='H')
        prices = np.full(periods, 50000)
    
    # Criar OHLCV
    highs = prices * (1 + np.random.uniform(0, 0.02, periods))
    lows = prices * (1 - np.random.uniform(0, 0.02, periods))
    opens = np.roll(prices, 1)
    opens[0] = prices[0]
    closes = prices
    volumes = np.random.uniform(1000, 10000, periods)
    
    df = pd.DataFrame({
        'timestamp': dates,
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    })
    
    return df


def test_scenario(scenario_name, market_data, symbol="BTC/USDT"):
    """Testa um cenário específico e captura os logs detalhados"""
    logger.info(f"\n{'='*60}")
    logger.info(f"🧪 TESTANDO CENÁRIO: {scenario_name}")
    logger.info(f"{'='*60}")
    
    try:
        # Criar estratégia
        config_manager = ConfigManager()
        config_manager.load()
        
        strategy = FibonacciWaveHypeStrategy(
            symbol=symbol,
            timeframe="5m",
            context={"timeframe": "5m"},
            config_manager=config_manager
        )
        
        # Criar contexto de trading
        context = TradingContext(
            symbol=symbol,
            timeframe="5m",
            ohlcv=market_data,
            timestamp=datetime.now()
        )
        
        logger.info(f"📊 Dados de entrada: {len(market_data)} períodos")
        logger.info(f"📊 Preço atual: ${market_data['close'].iloc[-1]:.2f}")
        logger.info(f"📊 Variação: {((market_data['close'].iloc[-1] / market_data['close'].iloc[0]) - 1) * 100:.2f}%")
        
        # Testar análise de mercado simples
        logger.info(f"\n🔍 ANÁLISE DE MERCADO SIMPLES:")
        result = strategy.analyze_market(market_data)
        
        logger.info(f"📈 Resultado: {result['signal']} (confiança: {result['confidence']:.3f})")
        if 'diagnostic' in result:
            logger.info(f"📋 Diagnóstico: {result['diagnostic']['reason']}")
            logger.info(f"📋 Detalhes: {result['diagnostic']['specific_reason']}")
            logger.info(f"📋 Componentes: {result['diagnostic']['details']['components']}")
        
        # Testar análise multi-timeframe
        logger.info(f"\n🔍 ANÁLISE MULTI-TIMEFRAME:")
        signal_df = strategy.generate_multi_timeframe_signal(context)
        
        if not signal_df.empty:
            logger.info(f"📈 Sinal gerado: {signal_df.iloc[0]['signal']} (confiança: {signal_df.iloc[0]['confidence']:.3f})")
            logger.info(f"📈 Preço: ${signal_df.iloc[0]['price']:.2f}")
            logger.info(f"📈 Stop Loss: ${signal_df.iloc[0]['stop_loss']:.2f}")
            logger.info(f"📈 Take Profit: ${signal_df.iloc[0]['take_profit']:.2f}")
        else:
            logger.info(f"📈 Nenhum sinal gerado (DataFrame vazio)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro no teste {scenario_name}: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False


def main():
    """Função principal de teste"""
    if not QUALIA_AVAILABLE:
        print("❌ QUALIA não disponível para teste")
        return False
    
    logger.info("🚀 Iniciando testes de logging detalhado da estratégia FWH...")
    
    # Cenários de teste
    test_scenarios = [
        ("📈 TENDÊNCIA DE ALTA", create_test_data(periods=50, trend="uptrend")),
        ("📉 TENDÊNCIA DE BAIXA", create_test_data(periods=50, trend="downtrend")),
        ("↔️ MOVIMENTO LATERAL", create_test_data(periods=50, trend="sideways")),
        ("🔇 BAIXO VOLUME/MOVIMENTO", create_test_data(periods=50, trend="low_volume")),
        ("📊 DADOS INSUFICIENTES", create_test_data(periods=5, trend="insufficient_data")),
    ]
    
    results = []
    
    for scenario_name, market_data in test_scenarios:
        success = test_scenario(scenario_name, market_data)
        results.append(success)
        
        # Pausa entre cenários
        import time
        time.sleep(1)
    
    # Resumo final
    logger.info(f"\n{'='*60}")
    logger.info(f"📋 RESUMO DOS TESTES")
    logger.info(f"{'='*60}")
    
    successful_tests = sum(results)
    total_tests = len(results)
    
    logger.info(f"✅ Testes bem-sucedidos: {successful_tests}/{total_tests}")
    
    if successful_tests == total_tests:
        logger.info("🎉 TODOS OS TESTES PASSARAM!")
        logger.info("✅ Sistema de logging detalhado funcionando corretamente")
        logger.info("✅ Diagnósticos específicos sendo gerados para cada cenário")
        return True
    else:
        logger.error("💥 ALGUNS TESTES FALHARAM!")
        return False


if __name__ == "__main__":
    success = main()
    
    print(f"\n📄 Log detalhado salvo em: test_detailed_logging.log")
    print(f"📄 Execute: tail -f test_detailed_logging.log para acompanhar em tempo real")
    
    sys.exit(0 if success else 1)
