import time
from qiskit import QuantumCircuit
from qualia.utils.quantum_utils import enforce_circuit_limits
import numpy as np


def create_test_circuit(n_qubits: int, depth: int) -> QuantumCircuit:
    """Create a test circuit with specified depth and qubits."""
    qc = QuantumCircuit(n_qubits)

    for _ in range(depth):
        for q in range(n_qubits):
            qc.h(q)
        for q in range(n_qubits - 1):
            qc.cx(q, q + 1)

    return qc


def benchmark_trim():
    """Benchmark the circuit trimming performance."""
    print("Circuit Trim Benchmark")
    print("=====================")

    # Test parameters
    n_qubits = 5
    depth = 100  # Create a deep circuit
    max_depth = 32
    max_ops = 64
    min_cx_ratio = 0.8

    # Create test circuit
    qc = create_test_circuit(n_qubits, depth)
    print("\nTest circuit stats:")
    print(f"- Qubits: {n_qubits}")
    print(f"- Initial depth: {qc.depth()}")
    print(f"- Initial ops: {sum(qc.count_ops().values())}")
    print(f"- Initial CX: {qc.count_ops().get('cx', 0)}")

    # Benchmark
    times = []
    for _ in range(100):  # Run 100 iterations
        start = time.time()
        trimmed_qc = enforce_circuit_limits(
            qc.copy(), max_depth=max_depth, max_ops=max_ops, min_cx_ratio=min_cx_ratio
        )
        times.append(time.time() - start)

    # Statistics
    times = np.array(times)
    avg_time = np.mean(times) * 1000  # Convert to milliseconds
    std_dev = np.std(times) * 1000

    print("\nTrimming performance:")
    print(f"- Average time: {avg_time:.2f} ms")
    print(f"- Standard deviation: {std_dev:.2f} ms")
    print(
        f"- 95% confidence interval: {np.percentile(times, 2.5)*1000:.2f} ms to {np.percentile(times, 97.5)*1000:.2f} ms"
    )

    # Trimmed circuit stats
    print("\nTrimmed circuit stats:")
    print(f"- Final depth: {trimmed_qc.depth()}")
    print(f"- Final ops: {sum(trimmed_qc.count_ops().values())}")
    print(f"- Final CX: {trimmed_qc.count_ops().get('cx', 0)}")


if __name__ == "__main__":
    benchmark_trim()
