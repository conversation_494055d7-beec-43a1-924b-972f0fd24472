"""Composite strategy orchestration."""

from __future__ import annotations

from typing import Any, Dict, List, Optional
from ...config.config_manager import ConfigManager
from ...config.composite_defaults import load_composite_defaults

import pandas as pd

from ...strategies.strategy_interface import TradingStrategy, register_strategy
from ...strategies.strategy_factory import StrategyFactory
from ...utils.logger import get_logger

from .analyzer import analyze_market as _analyze_market
from .signal_generator import generate_signals as _generate_signals
from .backtester import backtest as _backtest

logger = get_logger("strategies.composite")

_DEFAULTS = load_composite_defaults()


@register_strategy(
    name="CompositeStrategy",
    category="composite",
    description="Estratégia composta adaptativa unindo Clássica, Momentum Quântico e Reversão Quântica",
    version="1.1.0",
    tags=["composite", "ensemble", "adaptive", "qast"],
    legacy=True,
)
class CompositeStrategy(TradingStrategy):
    """Strategy that orchestrates multiple sub strategies."""

    strategy_alias = "composite_v1"

    def __init__(
        self,
        signal_threshold: float = 0.5,
        common_params: Optional[Dict[str, Any]] = None,
        sub: Optional[Dict[str, Dict[str, Any]]] = None,
        default_weights: Optional[List[float]] = None,
        config_manager: Optional[ConfigManager] = None,
        **kwargs: Any,
    ) -> None:
        super().__init__(config_manager=config_manager)
        self.name = "CompositeStrategy"

        self.common_params: Dict[str, Any] = _DEFAULTS.copy()
        if isinstance(common_params, dict):
            self.common_params.update(common_params)
        elif common_params is not None:
            logger.warning(
                f"{self.name} __init__: 'common_params' do JSON não é um dicionário, usando defaults. Recebido: {common_params}"
            )

        self.signal_threshold = signal_threshold
        self.risk_per_trade_pct = self.common_params.get("risk_per_trade_pct", 0.02)

        self.sub_strategy_configs: List[Dict[str, Any]] = []
        if isinstance(sub, dict):
            for alias_name, params_config in sub.items():
                self.sub_strategy_configs.append(
                    {"alias": alias_name, "params": params_config}
                )
        elif sub is not None:
            logger.warning(
                f"{self.name} __init__: 'sub' não é um dicionário. Nenhuma sub-estratégia será carregada. Recebido: {sub}"
            )

        if default_weights and len(default_weights) == len(self.sub_strategy_configs):
            self.weights = default_weights
        elif self.sub_strategy_configs:
            self.weights = [1.0 / len(self.sub_strategy_configs)] * len(
                self.sub_strategy_configs
            )
        else:
            self.weights = []

        self.qast_quantum_weights_config = kwargs.pop("quantum_weight", None)
        if kwargs:
            logger.warning(
                f"{self.name} __init__: Parâmetros JSON inesperados recebidos e ignorados: {list(kwargs.keys())}"
            )

        self.strategies: List[TradingStrategy] = []
        self.previous_weights = self.weights.copy()
        self.qast_controller = None
        self.metacognition = None
        self.last_market_data_hash = None
        self.indicators_cache: Dict[str, Any] = {}

        logger.info(
            f"CompositeStrategy __init__ concluído. Sub-estratégias configuradas: {len(self.sub_strategy_configs)}"
        )
        logger.debug(
            f"CompositeStrategy __init__ - Signal Threshold: {self.signal_threshold}, Risk/Trade: {self.risk_per_trade_pct}"
        )
        logger.debug(
            f"CompositeStrategy __init__ - Common Params: {self.common_params}"
        )
        logger.debug(f"CompositeStrategy __init__ - Pesos Iniciais: {self.weights}")

    def initialize(self, context: Dict[str, Any]) -> None:
        if self.is_initialized:
            logger.info(f"{self.name} já inicializada.")
            return

        logger.info(f"Inicializando {self.name} com contexto.")
        super().initialize(context)

        self.weights = context.get("weights", self.weights)
        self.signal_threshold = context.get("signal_threshold", self.signal_threshold)
        self.risk_per_trade_pct = context.get(
            "risk_per_trade_pct", self.risk_per_trade_pct
        )

        context_common_params = context.get("common_params", {})
        self.common_params.update(context_common_params)
        self.common_params["risk_per_trade_pct"] = self.risk_per_trade_pct

        self.qast_controller = context.get("qast_controller")
        self.metacognition = context.get("metacognition")

        self.sub_strategy_configs = context.get(
            "sub_strategy_configs", self.sub_strategy_configs
        )

        self.initialize_sub_strategies(context)

        logger.info(
            f"{self.name} inicializada com sucesso com {len(self.strategies)} sub-estratégias."
        )
        logger.debug(f"Pesos Finais da Composite: {self.weights}")

    def initialize_sub_strategies(self, global_context: Dict[str, Any]) -> None:
        self.strategies = []
        new_weights = []
        factory = StrategyFactory()

        if not self.sub_strategy_configs:
            logger.warning(
                f"{self.name}: Nenhuma configuração de sub-estratégia fornecida. A Estratégia Composta não terá sub-estratégias."
            )
            self.weights = []
            self.previous_weights = []
            return

        logger.info(
            f"Inicializando {len(self.sub_strategy_configs)} sub-estratégias..."
        )
        for i, sub_config in enumerate(self.sub_strategy_configs):
            alias = sub_config.get("alias")
            specific_params_for_sub = sub_config.get("params", {})

            if not alias:
                logger.error(
                    f"Configuração de sub-estratégia {i} não possui 'alias'. Pulando."
                )
                continue

            try:
                final_params_for_sub_init = {
                    **self.common_params,
                    **specific_params_for_sub,
                }

                sub_context = global_context.copy()
                sub_context.update(final_params_for_sub_init)

                logger.debug(
                    f"Criando sub-estratégia '{alias}' com params para __init__: {list(final_params_for_sub_init.keys())}"
                )
                instance = factory.create_strategy(
                    alias, params=final_params_for_sub_init, context=sub_context
                )
                self.strategies.append(instance)
                logger.info(
                    f"Sub-estratégia '{alias}' ({instance.name}) criada e inicializada."
                )
                new_weights.append(
                    self.weights[i]
                    if i < len(self.weights)
                    else (1.0 / len(self.sub_strategy_configs))
                )
            except Exception as e:  # pragma: no cover - defensive
                logger.error(
                    f"Falha ao criar ou inicializar sub-estratégia '{alias}': {e}",
                    exc_info=True,
                )

        if len(self.strategies) != len(self.sub_strategy_configs):
            logger.warning(
                "Algumas sub-estratégias não puderam ser carregadas. Ajustando pesos."
            )

        if self.strategies:
            if sum(new_weights) == 0 and new_weights:
                self.weights = [1.0 / len(new_weights)] * len(new_weights)
            elif new_weights:
                s = sum(new_weights)
                self.weights = [w / s for w in new_weights]
            else:
                self.weights = []
        else:
            self.weights = []

        self.previous_weights = self.weights.copy()
        logger.debug(
            f"Sub-estratégias inicializadas ({len(self.strategies)}). Pesos atuais: {self.weights}"
        )

    # Delegated methods -----------------------------------------------------
    def analyze_market(
        self,
        market_data: pd.DataFrame,
        common_indicators: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        return _analyze_market(self, market_data, common_indicators)

    def generate_signals(self, analysis_result: Dict[str, Any]) -> pd.DataFrame:
        return _generate_signals(self, analysis_result)

    def backtest(
        self,
        market_data_map: Dict[str, pd.DataFrame],
        initial_capital: float,
        risk_per_trade_pct: float,
    ) -> Dict[str, Any]:
        return _backtest(self, market_data_map, initial_capital, risk_per_trade_pct)

    # ----------------------------------------------------------------------
    def get_parameters(self) -> Dict[str, Any]:
        params = {
            "strategy_alias": self.strategy_alias,
            "sub_strategy_configs": self.sub_strategy_configs,
            "weights": self.weights,
            "signal_threshold": self.signal_threshold,
            "risk_per_trade_pct": self.risk_per_trade_pct,
            "common_params": self.common_params.copy(),
            "sub_strategies_current_params": {},
        }
        for strat_instance in self.strategies:
            sub_alias = strat_instance.metadata.get("alias", strat_instance.name)
            params["sub_strategies_current_params"][
                sub_alias
            ] = strat_instance.get_parameters()
        return params

    def get_params(self) -> Dict[str, Any]:
        """Alias compatível para ``get_parameters``."""

        return self.get_parameters()

    def set_parameters(self, params: Dict[str, Any]) -> None:
        logger.info(
            f"{self.name}: Recebendo novos parâmetros via set_parameters: {list(params.keys())}"
        )

        if (
            "weights" in params
            and isinstance(params["weights"], list)
            and len(params["weights"]) == len(self.strategies)
        ):
            self.weights = params["weights"]
            self.previous_weights = self.weights.copy()
            logger.debug(f"{self.name}: Pesos atualizados para {self.weights}")

        if "signal_threshold" in params:
            self.signal_threshold = params["signal_threshold"]
            logger.debug(
                f"{self.name}: Signal threshold atualizado para {self.signal_threshold}"
            )

        if "risk_per_trade_pct" in params:
            self.risk_per_trade_pct = params["risk_per_trade_pct"]
            self.common_params["risk_per_trade_pct"] = self.risk_per_trade_pct
            logger.debug(
                f"{self.name}: Risco por trade atualizado para {self.risk_per_trade_pct}"
            )

        if "common_params" in params and isinstance(params["common_params"], dict):
            self.common_params.update(params["common_params"])
            self.common_params.setdefault("risk_per_trade_pct", self.risk_per_trade_pct)
            logger.debug(
                f"{self.name}: Common params atualizados: {self.common_params}"
            )

        if "sub_strategy_configs" in params and isinstance(
            params["sub_strategy_configs"], list
        ):
            self.sub_strategy_configs = params["sub_strategy_configs"]
            logger.info(
                f"{self.name}: Configurações de sub-estratégia atualizadas. {len(self.sub_strategy_configs)} configs."
            )

        evolved_sub_params_map = params.get("sub_strategies_current_params", {})

        if evolved_sub_params_map or "sub_strategy_configs" in params:
            logger.info(
                f"{self.name}: Re-inicializando sub-estratégias. Params evoluídos fornecidos para: {list(evolved_sub_params_map.keys())}"
            )

            current_composite_context = {
                "qast_controller": self.qast_controller,
                "metacognition": self.metacognition,
                "common_params": self.common_params.copy(),
            }
            if evolved_sub_params_map:
                current_composite_context["sub_strategies_evolved_params"] = (
                    evolved_sub_params_map
                )

            self.initialize_sub_strategies(current_composite_context)
        else:
            logger.debug(
                f"{self.name}: set_parameters chamado sem 'sub_strategies_current_params' ou 'sub_strategy_configs' evoluídos. Sub-estratégias não foram re-inicializadas/atualizadas."
            )

    def set_params(self, params: Dict[str, Any]) -> None:
        """Alias compatível para ``set_parameters``."""

        self.set_parameters(params)

    # ----------------------------------------------------------------------
    def analyze(
        self,
        data: pd.DataFrame,
        quantum_metrics: Optional[Dict[str, Any]] = None,
        context: Optional[Any] = None,
        similar_past_patterns: Optional[List[Any]] = None,
    ) -> Dict[str, Any]:
        if not self.is_initialized:
            logger.warning(f"{self.name} não inicializada. Retornando HOLD.")
            return {
                "signal": "HOLD",
                "confidence": 0.0,
                "stop_loss": None,
                "take_profit": None,
                "reasons": ["Strategy not initialized"],
            }

        logger.debug(
            f"{self.name}.analyze: Chamando analyze_market com dados de {len(data) if data is not None else 0} barras."
        )
        market_analysis_result = self.analyze_market(market_data=data)

        if "error" in market_analysis_result:
            logger.warning(
                f"{self.name}.analyze: Erro retornado por analyze_market: {market_analysis_result['error']}. Retornando HOLD."
            )
            return {
                "signal": "HOLD",
                "confidence": 0.0,
                "stop_loss": None,
                "take_profit": None,
                "reasons": [
                    f"Market analysis error: {market_analysis_result['error']}"
                ],
            }

        logger.debug(f"{self.name}.analyze: Chamando generate_signals.")
        signals_df = self.generate_signals(analysis_result=market_analysis_result)

        if signals_df is not None and not signals_df.empty:
            latest_signal_series = signals_df.iloc[-1]
            decision = {
                "signal": latest_signal_series.get("signal", "HOLD"),
                "confidence": latest_signal_series.get("confidence", 0.0),
                "stop_loss": latest_signal_series.get("stop_loss"),
                "take_profit": latest_signal_series.get("take_profit"),
                "price_at_signal": latest_signal_series.get("price"),
                "reasons": latest_signal_series.get("reasons", []),
            }
            if not isinstance(decision["stop_loss"], (int, float)):
                decision["stop_loss"] = None
            if not isinstance(decision["take_profit"], (int, float)):
                decision["take_profit"] = None

            logger.info(
                f"{self.name}.analyze: Decisão gerada: {decision['signal']} com confiança {decision['confidence']:.2f}"
            )
        else:
            logger.info(
                f"{self.name}.analyze: Nenhum sinal gerado por generate_signals. Retornando HOLD."
            )
            decision = {
                "signal": "HOLD",
                "confidence": 0.0,
                "stop_loss": None,
                "take_profit": None,
                "reasons": ["No signal generated by composite strategy"],
            }

        return decision
