import copy
from unittest.mock import MagicMock

from tests.stub_utils import install_stubs

install_stubs()

from qualia.core.universe import QUALIAQuantumUniverse  # noqa: F401
from qualia.adaptive_evolution import AdaptiveConsciousnessEvolution


def test_get_all_dynamic_risk_levels_returns_deep_copy():
    ace = AdaptiveConsciousnessEvolution(qualia_universe=MagicMock())
    ace.risk_calibration_results = {"BTC": {"stop_loss_price": 1.0}}

    results = ace.get_all_dynamic_risk_levels()
    results["BTC"]["stop_loss_price"] = 2.0

    assert ace.risk_calibration_results["BTC"]["stop_loss_price"] == 1.0
