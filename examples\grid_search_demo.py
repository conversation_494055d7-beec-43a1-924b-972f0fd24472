#!/usr/bin/env python3
"""
Demonstração do Sistema de Grid Search - Etapa C

YAA IMPLEMENTATION: Script de demonstração para o sistema de backtest offline
que avalia sistematicamente combinações de hiperparâmetros QUALIA.

Este script demonstra:
- Configuração e execução do grid search
- Análise dos resultados
- Visualização das métricas
- Identificação dos melhores parâmetros
"""

import asyncio
import sys
from pathlib import Path

# Adiciona o diretório src ao path para importações
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.backtest.hyperparams_grid_search import (
    GridSearchParams,
    HyperParamsGridSearch,
    run_hyperparams_grid_search,
)
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


async def demo_grid_search_small():
    """Demonstração com grid search pequeno para testes rápidos."""
    print("🔬 DEMO: Grid Search Pequeno (Teste Rápido)")
    print("=" * 60)
    
    # Configurações reduzidas para demo rápida
    params = GridSearchParams(
        price_amp_range=(2.0, 8.0, 4),      # 4 valores: 2.0, 4.0, 6.0, 8.0
        news_amp_range=(2.0, 8.0, 4),       # 4 valores: 2.0, 4.0, 6.0, 8.0
        min_conf_range=(0.4, 0.7, 4),       # 4 valores: 0.4, 0.5, 0.6, 0.7
        backtest_days=30,                    # 30 dias para teste rápido
        initial_capital=10000.0,
        symbols=["BTC/USDT"],                # Apenas 1 símbolo
        max_workers=1,                       # 1 worker para demo (evita problemas de serialização)
        cache_results=True,
        save_detailed_results=True
    )
    
    print(f"📊 Configurações:")
    print(f"   Price Amplification: {params.price_amp_range[0]}-{params.price_amp_range[1]} ({params.price_amp_range[2]} valores)")
    print(f"   News Amplification: {params.news_amp_range[0]}-{params.news_amp_range[1]} ({params.news_amp_range[2]} valores)")
    print(f"   Min Confidence: {params.min_conf_range[0]}-{params.min_conf_range[1]} ({params.min_conf_range[2]} valores)")
    print(f"   Total combinações: {params.price_amp_range[2] * params.news_amp_range[2] * params.min_conf_range[2]}")
    print(f"   Período de backtest: {params.backtest_days} dias")
    print(f"   Workers: {params.max_workers}")
    print()
    
    # Executa grid search
    grid_search = HyperParamsGridSearch(params)
    results = await grid_search.run_grid_search()
    
    # Exibe resultados
    print_results_summary(results)
    
    # Salva resultados
    output_path = "results/demo/grid_search_small_demo.json"
    grid_search.save_results(results, output_path)
    
    return results


async def demo_grid_search_full():
    """Demonstração com grid search completo (Etapa C)."""
    print("🚀 DEMO: Grid Search Completo - Etapa C")
    print("=" * 60)
    
    # Configurações completas da Etapa C (versão reduzida para demonstração)
    params = GridSearchParams(
        price_amp_range=(1.0, 10.0, 5),     # 5 valores: 1.0, 3.25, 5.5, 7.75, 10.0
        news_amp_range=(1.0, 10.0, 5),      # 5 valores: 1.0, 3.25, 5.5, 7.75, 10.0
        min_conf_range=(0.3, 0.8, 6),       # 6 valores: 0.3 a 0.8
        backtest_days=90,                    # 90 dias conforme especificação
        initial_capital=10000.0,
        symbols=["BTC/USDT", "ETH/USDT"],    # 2 símbolos principais
        max_workers=1,                       # 1 worker (execução sequencial)
        cache_results=True,
        save_detailed_results=True,
        min_trades_required=10,              # Mínimo 10 trades para validar resultado
        max_drawdown_limit=0.5               # Máximo 50% drawdown
    )
    
    total_combinations = params.price_amp_range[2] * params.news_amp_range[2] * params.min_conf_range[2]
    
    print(f"📊 Configurações da Etapa C:")
    print(f"   Price Amplification: {params.price_amp_range[0]}-{params.price_amp_range[1]} ({params.price_amp_range[2]} valores)")
    print(f"   News Amplification: {params.news_amp_range[0]}-{params.news_amp_range[1]} ({params.news_amp_range[2]} valores)")
    print(f"   Min Confidence: {params.min_conf_range[0]}-{params.min_conf_range[1]} ({params.min_conf_range[2]} valores)")
    print(f"   Total combinações: {total_combinations}")
    print(f"   Período de backtest: {params.backtest_days} dias")
    print(f"   Capital inicial: ${params.initial_capital:,.2f}")
    print(f"   Símbolos: {', '.join(params.symbols)}")
    print(f"   Workers: {params.max_workers} ({'sequencial' if params.max_workers == 1 else 'paralelo'})")
    print()
    
    # Confirma execução (pode demorar)
    estimated_time = total_combinations * 0.5 / params.max_workers  # Estimativa: 0.5s por backtest
    print(f"⏱️  Tempo estimado: {estimated_time:.1f} segundos ({estimated_time/60:.1f} minutos)")
    
    response = input("Continuar com o grid search completo? (y/N): ")
    if response.lower() != 'y':
        print("Grid search cancelado.")
        return None
    
    # Executa grid search
    grid_search = HyperParamsGridSearch(params)
    results = await grid_search.run_grid_search()
    
    # Exibe resultados
    print_results_summary(results)
    
    # Salva resultados
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"results/etapa_c/grid_search_full_{timestamp}.json"
    grid_search.save_results(results, output_path)
    
    return results


def print_results_summary(results):
    """Imprime resumo dos resultados do grid search."""
    print("\n" + "="*60)
    print("📈 RESULTADOS DO GRID SEARCH")
    print("="*60)
    
    print(f"⏱️  Tempo de execução: {results.total_execution_time:.1f} segundos")
    print(f"📊 Total de combinações: {results.total_combinations}")
    print(f"✅ Backtests bem-sucedidos: {results.successful_backtests}")
    print(f"❌ Backtests falharam: {results.failed_backtests}")
    print(f"📈 Taxa de sucesso: {results.successful_backtests/results.total_combinations*100:.1f}%")
    print()
    
    if not results.results:
        print("❌ Nenhum resultado válido encontrado.")
        return
    
    # Melhores resultados
    print("🏆 MELHORES RESULTADOS:")
    print("-" * 40)
    
    if results.best_sharpe:
        print(f"🎯 MELHOR SHARPE RATIO: {results.best_sharpe.sharpe_ratio:.3f}")
        print(f"   Parâmetros: price_amp={results.best_sharpe.price_amplification:.1f}, "
              f"news_amp={results.best_sharpe.news_amplification:.1f}, "
              f"min_conf={results.best_sharpe.min_confidence:.2f}")
        print(f"   Return: {results.best_sharpe.total_return_pct:.2%}")
        print(f"   Max Drawdown: {results.best_sharpe.max_drawdown_pct:.2%}")
        print(f"   Win Rate: {results.best_sharpe.win_rate:.2%}")
        print(f"   Trades: {results.best_sharpe.total_trades}")
        print()
    
    if results.best_return:
        print(f"💰 MELHOR RETORNO: {results.best_return.total_return_pct:.2%}")
        print(f"   Parâmetros: price_amp={results.best_return.price_amplification:.1f}, "
              f"news_amp={results.best_return.news_amplification:.1f}, "
              f"min_conf={results.best_return.min_confidence:.2f}")
        print(f"   Sharpe: {results.best_return.sharpe_ratio:.3f}")
        print(f"   Max Drawdown: {results.best_return.max_drawdown_pct:.2%}")
        print()
    
    if results.best_drawdown:
        print(f"🛡️  MENOR DRAWDOWN: {results.best_drawdown.max_drawdown_pct:.2%}")
        print(f"   Parâmetros: price_amp={results.best_drawdown.price_amplification:.1f}, "
              f"news_amp={results.best_drawdown.news_amplification:.1f}, "
              f"min_conf={results.best_drawdown.min_confidence:.2f}")
        print(f"   Return: {results.best_drawdown.total_return_pct:.2%}")
        print(f"   Sharpe: {results.best_drawdown.sharpe_ratio:.3f}")
        print()
    
    if results.best_calmar:
        print(f"⚖️  MELHOR CALMAR RATIO: {results.best_calmar.calmar_ratio:.3f}")
        print(f"   Parâmetros: price_amp={results.best_calmar.price_amplification:.1f}, "
              f"news_amp={results.best_calmar.news_amplification:.1f}, "
              f"min_conf={results.best_calmar.min_confidence:.2f}")
        print(f"   Return: {results.best_calmar.total_return_pct:.2%}")
        print(f"   Max Drawdown: {results.best_calmar.max_drawdown_pct:.2%}")
        print()
    
    # Estatísticas gerais
    print("📊 ESTATÍSTICAS GERAIS:")
    print("-" * 40)
    
    sharpe_ratios = [r.sharpe_ratio for r in results.results]
    returns = [r.total_return_pct for r in results.results]
    drawdowns = [r.max_drawdown_pct for r in results.results]
    
    print(f"Sharpe Ratio - Média: {sum(sharpe_ratios)/len(sharpe_ratios):.3f}, "
          f"Min: {min(sharpe_ratios):.3f}, Max: {max(sharpe_ratios):.3f}")
    print(f"Retorno - Média: {sum(returns)/len(returns):.2%}, "
          f"Min: {min(returns):.2%}, Max: {max(returns):.2%}")
    print(f"Max Drawdown - Média: {sum(drawdowns)/len(drawdowns):.2%}, "
          f"Min: {min(drawdowns):.2%}, Max: {max(drawdowns):.2%}")


async def main():
    """Função principal de demonstração."""
    print("🎯 SISTEMA DE GRID SEARCH QUALIA - ETAPA C")
    print("Backtest offline para otimização de hiperparâmetros")
    print("=" * 60)
    print()
    
    print("Escolha o tipo de demonstração:")
    print("1. Grid Search Pequeno (teste rápido - ~64 combinações)")
    print("2. Grid Search Completo (Etapa C - ~600 combinações)")
    print("3. Executar ambos")
    print()
    
    choice = input("Digite sua escolha (1/2/3): ").strip()
    
    if choice == "1":
        await demo_grid_search_small()
    elif choice == "2":
        await demo_grid_search_full()
    elif choice == "3":
        print("Executando demonstração completa...\n")
        await demo_grid_search_small()
        print("\n" + "="*80 + "\n")
        await demo_grid_search_full()
    else:
        print("Escolha inválida. Executando grid search pequeno...")
        await demo_grid_search_small()
    
    print("\n🎉 Demonstração concluída!")
    print("📁 Resultados salvos na pasta 'results/'")
    print("📊 Arquivos .json e .csv disponíveis para análise")


if __name__ == "__main__":
    # Executa demonstração
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  Demonstração interrompida pelo usuário.")
    except Exception as e:
        logger.error(f"Erro na demonstração: {e}")
        print(f"\n❌ Erro: {e}")
