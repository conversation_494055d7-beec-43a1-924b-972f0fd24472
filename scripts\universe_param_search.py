"""quick parameter sweep for QUALIAQuantumUniverse

Itera combinacoes de parametros (scr_depth, qpu_steps, enrichment_cycles, measure_frequency)
para 8 qubits, mede diversidade (entropia de Shannon do statevector) e grava combinacao
ideal de volta ao config.yaml.

Uso:
    python scripts/universe_param_search.py --max-combos 100 --out config.yaml
"""
import itertools
import json
import math
import random
import sys
from pathlib import Path
from typing import Dict, Tuple

# Garante que src/ está no sys.path para import absoluto
sys.path.insert(0, str(Path(__file__).parent.parent))

import numpy as np
import yaml
from tqdm import tqdm  # type: ignore

# Importar universo QUALIA
try:
    from qualia.core.universe import QUALIAQuantumUniverse  # noqa
except ImportError as e:
    print("Não foi possível importar QUALIAQuantumUniverse. Ajuste PYTHONPATH.")
    raise e


SEARCH_SPACE = {
    "scr_depth": [2, 3, 4, 5, 6],
    "qpu_steps": [1, 2, 3],
    "enrichment_cycles": [1, 3, 5, 7, 9],
    "measure_frequency": [1, 2],
}

N_QUBITS = 8
BACKEND = "aer_simulator_statevector"
SHOTS = 256


def shannon_entropy(probabilities: np.ndarray) -> float:
    """Compute base-2 Shannon entropy of a probability vector."""
    p = probabilities[probabilities > 0]
    return -float(np.sum(p * np.log2(p))) / len(probabilities)


def evaluate_config(cfg: Dict[str, int]) -> float:
    """Cria universo com parametros e mede diversidade via entropia normalizada."""
    universe = QUALIAQuantumUniverse(
        n_qubits=N_QUBITS,
        scr_depth=cfg["scr_depth"],
        qpu_steps=cfg["qpu_steps"],
        enrichment_cycles=cfg["enrichment_cycles"],
        measure_frequency=cfg["measure_frequency"],
        backend_name=BACKEND,
        shots=SHOTS,
        # parâmetros adicionais obrigatórios
        base_lambda=0.5,
        alpha=1.0,
        retro_strength=0.3,
        num_ctc_qubits=0,
    )

    # Executa circuito inicial e obtém statevector
    # Rodar com steps maiores e randomização extra para evitar over-trimming
    steps = max(cfg["qpu_steps"] * 2, 4)
    universe.run(steps=steps, post_randomize_layers=2)
    statevector_obj = getattr(universe, "current_sv", None)
    if statevector_obj is None:
        raise ValueError("Statevector não disponível após execução.")
    statevector = np.array(statevector_obj)
    probabilities = np.abs(np.array(statevector)) ** 2
    diversity = shannon_entropy(probabilities)
    # Descarta combinações com diversidade insignificante
    if diversity < 0.1:
        raise ValueError(f"diversidade insuficiente ({diversity:.4f}), pulando cfg")
    return diversity


def grid_iterator(max_combos: int):
    keys = list(SEARCH_SPACE.keys())
    all_values = [SEARCH_SPACE[k] for k in keys]
    combos = list(itertools.product(*all_values))
    random.shuffle(combos)
    if max_combos:
        combos = combos[:max_combos]
    for combo in combos:
        yield dict(zip(keys, combo))


def main(max_combos: int = 0, out_path: str = "config.yaml"):
    best_cfg: Dict[str, int] | None = None
    best_score = -1.0

    for cfg in tqdm(grid_iterator(max_combos)):
        try:
            score = evaluate_config(cfg)
        except Exception as exc:
            print(f"Falha ao avaliar {cfg}: {exc}")
            continue
        if score > best_score:
            best_score = score
            best_cfg = cfg
            print(f"Novo melhor {best_cfg} -> diversity={best_score:.4f}")

    if best_cfg is None:
        print("Nenhuma configuração válida foi avaliada.")
        return

    print("Melhor configuração encontrada:", json.dumps(best_cfg, indent=2))

    # Atualiza config.yaml
    path = Path(out_path)
    if not path.exists():
        print(f"Arquivo {out_path} não existe; salvando yaml novo.")
        data = {}
    else:
        with path.open("r", encoding="utf-8") as fh:
            data = yaml.safe_load(fh) or {}

    data.setdefault("universe_config", {}).update(best_cfg)
    data["universe_config"].update({
        "n_qubits": N_QUBITS,
        "backend": BACKEND,
        "shots": SHOTS,
    })

    with path.open("w", encoding="utf-8") as fh:
        yaml.dump(data, fh, allow_unicode=True, sort_keys=False)

    print(f"config.yaml atualizado com melhor configuração (diversity={best_score:.4f}).")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="QUALIA universe parameter search")
    parser.add_argument("--max-combos", type=int, default=50, help="Número máximo de combinações a testar (0 = todas)")
    parser.add_argument("--out", type=str, default="config.yaml", help="Arquivo de configuração a atualizar")
    args = parser.parse_args()

    main(max_combos=args.max_combos, out_path=args.out)
