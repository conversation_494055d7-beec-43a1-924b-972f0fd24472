# Configuração do Sistema QUALIA Unificado
# 
# Sistema de consciência unificada que centraliza toda lógica adaptativa
# no QASTCore como oráculo de decisão, com interface de execução pura.

# ============================================================================
# CONFIGURAÇÃO GERAL DO SISTEMA
# ============================================================================

system:
  name: "QUALIA Unified Consciousness"
  version: "1.0.0"
  mode: "paper_trading"  # "paper_trading" ou "live"
  capital: 10000.0
  
  # Símbolos para trading
  symbols:
    - "BTCUSDT"
    - "ETHUSDT"
    - "ADAUSDT"
    - "SOLUSDT"
  
  # Timeframes para análise
  timeframes:
    - "5m"
    - "15m"
    - "1h"

trading:
  base_currency: "USDT"
  quote_currencies: ["BTC", "ETH", "ADA", "DOT"]

timing:
  data_collection_interval: 30.0
  holographic_evolution_interval: 15.0
  decision_cycle_interval: 60.0
  execution_cycle_interval: 5.0
  monitoring_interval: 10.0
  safety_check_interval: 5.0
  metacognition_interval: 30.0
  metacognition_idle_info_interval: 10  # deve ser > 0

# ============================================================================
# QAST ORACLE DECISION ENGINE - CÉREBRO CENTRAL
# ============================================================================

qast_oracle:
  # Configuração do núcleo quântico
  qast_core:
    state_history_max: 1000
    operator_timeout: 5.0
    quantum_coherence_threshold: 0.6
    entanglement_strength: 0.8
  
  # Parâmetros de consciência
  consciousness:
    base_level: 0.5
    coherence_weight: 0.4
    entanglement_weight: 0.2
    pattern_weight: 0.3
    risk_clarity_weight: 0.1
  
  # Universo holográfico integrado
  holographic_universe:
    field_size: [200, 200]
    diffusion_rate: 0.35
    feedback_strength: 0.08
    event_amplitude_multiplier: 50.0
    spatial_sigma: 15.0
    temporal_sigma: 5.0
  
  # Coletor de dados enhanced
  data_collector:
    collection_interval: 1.0
    quantum_encoders:
      - "RSIPhaseEncoder"
      - "VolumeRatioAmplitudeEncoder"
      - "RSSTextSentimentEncoder"
    
    # Configuração OHLCV
    ohlcv_config:
      indicators:
        rsi_period: 14
        volume_ratio_window: 20
        volatility_window: 20
  
  # Detecção de padrões
  pattern_detection:
    strength_threshold: 0.3
    confidence_threshold: 0.4
    signal_threshold: 0.5
    minimum_history: 5
    pattern_types:
      - "bullish"
      - "bearish"
      - "ascending"
      - "descending"
      - "neutral"

# ============================================================================
# ESTRATÉGIAS CENTRALIZADAS
# ============================================================================

strategy:
  name: "NovaEstrategiaQUALIA"
  
  # Parâmetros da estratégia (compatíveis com QualiaTSVFParams)
  params:
    # Parâmetros TSVF
    tsvf_vector_size: 100
    tsvf_alpha: 0.3
    tsvf_gamma: 0.1
    cE: 0.1
    cH: 0.05
    otoc_delta: 1
    otoc_window: 168
    
    # S1 parameters
    s1_tsvf_window: 24
    s1_strength_threshold: 0.02
    
    # S2 parameters
    s2_sma_short_period: 50
    s2_sma_long_period: 100
    s2_rsi_period: 14
    s2_rsi_oversold: 30
    s2_rsi_overbought: 70
    
    # S3 parameters
    s3_resample_period: "4h"
    s3_tsvf_window: 6
    s3_strength_threshold: 0.015
    
    # Meta parameters
    meta_sharpe_window_hours: 168
    meta_transaction_cost: 0.0005
    meta_decision_threshold: 0.1
    
    # Entropy and strength parameters
    entropy_history_window: 20
    h4_window: 20
    strength_percentile: 80
    
    # Risk parameters
    profit_target: 5.0
    max_drawdown: 1.0
    horizon: 24
    experience_replay_capacity: 2500

# ============================================================================
# METACOGNIÇÃO QUÂNTICA
# ============================================================================

metacognition:
  # Thresholds de decisão
  trade_decision_confidence_threshold: 0.6
  reduce_exposure_epsilon: 0.02
  metacognitive_override_threshold: 0.8
  
  # Parâmetros de reflexão
  reflection:
    history_window: 100
    performance_threshold: 0.05
    adaptation_sensitivity: 0.3
  
  # Contexto de mercado
  market_context:
    volatility_threshold: 0.05
    trend_strength_min: 0.4
    regime_detection: true

# ============================================================================
# GESTÃO DE RISCO CENTRALIZADA
# ============================================================================

risk_management:
  profile: "moderate"

  # Controles de segurança gerais
  safety:
    max_daily_loss_pct: 0.05  # stop diário em 5%
    max_drawdown_pct: 0.15    # drawdown máximo de 15%
    max_open_positions: 10    # limite de posições simultâneas
  
  # Configurações por perfil
  profiles:
    conservative:
      max_portfolio_risk: 0.02
      max_position_size_pct: 0.05
      max_daily_loss_pct: 0.02
      max_drawdown_pct: 0.10
    
    moderate:
      max_portfolio_risk: 0.05
      max_position_size_pct: 0.10
      max_daily_loss_pct: 0.05
      max_drawdown_pct: 0.15
    
    aggressive:
      max_portfolio_risk: 0.10
      max_position_size_pct: 0.20
      max_daily_loss_pct: 0.10
      max_drawdown_pct: 0.25
  
  # Configurações específicas por símbolo
  symbol_specific:
    BTCUSDT:
      max_position_size_pct: 0.12
      max_daily_loss_pct: 0.06
      volatility_multiplier: 1.0
    
    ETHUSDT:
      max_position_size_pct: 0.10
      max_daily_loss_pct: 0.05
      volatility_multiplier: 1.1
    
    ADAUSDT:
      max_position_size_pct: 0.08
      max_daily_loss_pct: 0.04
      volatility_multiplier: 1.3
    
    SOLUSDT:
      max_position_size_pct: 0.09
      max_daily_loss_pct: 0.045
      volatility_multiplier: 1.2

# ============================================================================
# QPM CONFIGURAÇÃO
# ============================================================================

qpm_config:
  risk_manager:
    alias: "advanced"
    initial_capital: 10000.0
    risk_profile: "moderate"

# ============================================================================
# INTERFACE DE EXECUÇÃO
# ============================================================================

execution_interface:
  # Configuração de execução
  execution_interval: 1.0
  order_timeout: 30.0
  retry_attempts: 3
  retry_delay: 1.0
  
  # Monitoramento de posições
  position_monitoring:
    check_interval: 5.0
    stop_loss_slippage: 0.001
    take_profit_slippage: 0.001
  
  # Métricas de performance
  performance_metrics:
    update_interval: 10.0
    history_window: 1000
    sharpe_calculation_period: 252

# ============================================================================
# CONFIGURAÇÃO DE EXCHANGE
# ============================================================================

exchange:
  # KuCoin configuração
  kucoin:
    exchange_id: "kucoin"
    api_key: "${KUCOIN_API_KEY}"
    api_secret: "${KUCOIN_SECRET_KEY}"
    passphrase: "${KUCOIN_PASSPHRASE}"
    sandbox: false
    
    # Rate limiting
    rate_limit:
      requests_per_second: 10
      burst_limit: 20
      cooldown_period: 1.0
    
    # Configuração de trading
    trading:
      order_type: "market"
      time_in_force: "IOC"
      min_order_size: 0.0001

# ============================================================================
# CONSCIÊNCIA UNIFICADA
# ============================================================================

unified_consciousness:
  # Parâmetros de consciência
  consciousness_update_interval: 5.0
  performance_window: 100
  adaptation_threshold: 0.3
  
  # Componentes da consciência
  consciousness_components:
    oracle_weight: 0.4
    execution_weight: 0.3
    coherence_weight: 0.2
    adaptation_weight: 0.1
  
  # Adaptação do sistema
  adaptation:
    learning_rate_base: 0.1
    learning_rate_range: [0.01, 0.5]
    adaptation_sensitivity: 0.3
    performance_threshold: 0.4
  
  # Auto-avaliação
  self_assessment:
    evaluation_interval: 10.0
    performance_components:
      consciousness: 0.2
      execution_efficiency: 0.3
      system_coherence: 0.2
      self_assessment: 0.15
      pnl_performance: 0.15

# ============================================================================
# LOGGING E MONITORAMENTO
# ============================================================================

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # Logs específicos
  loggers:
    qualia.core.qast_oracle_decision_engine: "DEBUG"
    qualia.core.qualia_execution_interface: "INFO"
    qualia.core.unified_qualia_consciousness: "INFO"
  
  # Arquivos de log
  files:
    main_log: "logs/qualia_unified.log"
    oracle_log: "logs/oracle_decisions.log"
    execution_log: "logs/execution_trades.log"
    consciousness_log: "logs/consciousness_state.log"

# Overrides para reduzir verbosidade em módulos específicos
logging_overrides:
  qualia.consciousness.holographic_universe: "INFO"  # Reduz DEBUG para INFO
  qualia.consciousness.real_data_collectors: "INFO"  # Reduz DEBUG para INFO
  qualia.consciousness.enhanced_data_collector: "INFO"  # Reduz verbosidade
  # qualia.market.base_integration: "INFO"  # COMENTADO: permite DEBUG quando solicitado
  ccxt: "WARNING"  # Suprimir logs verbosos do ccxt
  ccxt.base.exchange: "ERROR"  # Apenas erros críticos
  urllib3: "WARNING"  # Reduzir verbosidade HTTP
  aiohttp: "WARNING"  # Reduzir verbosidade HTTP
  asyncio: "WARNING"  # Reduzir verbosidade async

# ============================================================================
# MÉTRICAS E MONITORAMENTO
# ============================================================================

monitoring:
  # Status reporting
  status_report_interval: 30.0
  detailed_report_interval: 300.0
  
  # Métricas para tracking
  metrics:
    consciousness_level: true
    decision_confidence: true
    execution_efficiency: true
    system_coherence: true
    performance_score: true
    adaptation_events: true
    
  # Alertas
  alerts:
    low_consciousness_threshold: 0.3
    low_performance_threshold: 0.2
    high_drawdown_threshold: 0.15
    
  # Exportação de dados
  export:
    enabled: true
    format: "json"
    interval: 60.0
    directory: "data/exports/"

# Configuração de monitoramento
monitoring:
  metrics_interval: 60
  performance_tracking: true
  adaptive_logging: true

# Configuração holográfica (detalhes em holographic_universe.yaml)
holographic:
  config_file: "config/holographic_universe.yaml"
  enabled: true
  enable_warmup: true       # executa o aquecimento holográfico na inicialização
  warmup_hours: 72          # período histórico simulado
  # Parâmetros básicos (serão sobrescritos pelo arquivo específico)
  field_size: [200, 200]
  diffusion_rate: 0.25
  feedback_strength: 0.06

# Pesos para unificação da decisão final
unification_weights:
  strategy: 0.5      # A estratégia principal tem o maior peso
  holographic: 0.3   # O universo holográfico tem um peso significativo
  metacognition: 0.2 # A metacognição atua como um refinamento
  decision_threshold: 0.3 # Escore ponderado mínimo para gerar um sinal BUY/SELL

# Adaptive Coherence Engine (ACE) - Configuração para controle dinâmico
ace_config:
  enable_dynamic_risk_control: true  # Habilita ou desabilita o controle de risco dinâmico globalmente
  dynamic_risk_config:
    base_risk_per_trade: 0.01  # Risco base quando a volatilidade está na média
    volatility_adjustment_factor: 0.5 # Quão fortemente a volatilidade impacta o risco (0 a 1)
    max_risk_multiplier: 2.0  # Multiplicador máximo do risco (ex: 2x o risco base)
    min_risk_multiplier: 0.5  # Multiplicador mínimo
    volatility_lookback_period: 20 # Período para calcular a volatilidade
    risk_profile:
      conservative:
        volatility_adjustment_factor: 0.3
        max_risk_multiplier: 1.5
      balanced:
        volatility_adjustment_factor: 0.5
        max_risk_multiplier: 2.0
      aggressive:
        volatility_adjustment_factor: 0.7
        max_risk_multiplier: 3.0

# Configuração de exchanges
exchanges:
  default: "kucoin"
  timeout: 30.0
  kucoin:
    api_key: "${KUCOIN_API_KEY}"
    api_secret: "${KUCOIN_SECRET_KEY}"
    api_passphrase: "${KUCOIN_PASSPHRASE}"
    sandbox: false
    timeout: 20.0
    rate_limit: 4.0
    retry_attempts: 3
    retry_delay: 1.0

# YAA-AGENT: Adicionado para permitir tuning fino do fetch de dados históricos
# da Kucoin, que tem um limite máximo de 1500. Um valor entre 1000-1400
# costuma ser um bom equilíbrio entre velocidade e risco de timeout.
market:
  kucoin_batch_size: 1200

# Configurações do sistema de consciência unificada
unified_consciousness:
  enabled: true

# Configurações específicas de acesso ao mercado
market:
  # Tamanho do lote para buscas de histórico na KuCoin.
  # Valores maiores podem ser mais rápidos, mas aumentam o risco de timeout.
  # O limite máximo da API é 1500.
  kucoin_batch_size: 1500

# Configuração da conexão com a OpenAI (se aplicável)
openai_config:
  api_key: "your_openai_api_key_here" # Substitua pela sua chave 