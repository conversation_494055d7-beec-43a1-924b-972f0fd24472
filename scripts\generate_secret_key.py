#!/usr/bin/env python
"""Generate a random secret key for QUALIA's Flask app.

This script prints a new ``Fernet`` key that can be assigned to
``QUALIA_SECRET_KEY``. Use it whenever you need a fresh secret key.
"""

from __future__ import annotations

from cryptography.fernet import Fernet


def main() -> None:
    """Print a newly generated Fernet key."""
    print(Fernet.generate_key().decode())


if __name__ == "__main__":
    main()
