"""
QUALIA Live Feed - Exemplo de uso do sistema de feeds.

Este script demonstra como usar o sistema de feeds do QUALIA para coletar
dados de mercado em tempo real do KuCoin.
"""

import asyncio
import os
import time
from typing import Dict, Any

from .feed_manager import FeedManager
from .feed_aggregator import AggregatedTicker, AggregatedOrderBook
from ..utils.logger import get_logger

logger = get_logger(__name__)


class LiveFeedExample:
    """Exemplo de uso do sistema de live feed."""
    
    def __init__(self):
        self.feed_manager: Optional[FeedManager] = None
        self.data_received = 0
        self.start_time = 0.0
        
    async def run_example(self):
        """Executa exemplo completo do sistema de feeds."""
        try:
            logger.info("🚀 Iniciando exemplo do QUALIA Live Feed System")
            
            # Configuração de exemplo
            config = {
                'exchanges': {
                    'kucoin': {
                        'api_key': os.getenv('KUCOIN_API_KEY', ''),
                        'api_secret': os.getenv('KUCOIN_API_SECRET', ''),
                        'password': os.getenv('KUCOIN_PASSPHRASE', ''),
                        'sandbox': False,
                        'timeout': 30.0,
                        'rate_limit': 4.0,
                    }
                }
            }
            
            # Símbolos para monitorar
            symbols = ['BTC-USDT', 'ETH-USDT', 'ADA-USDT', 'SOL-USDT']
            
            # Criar feed manager
            self.feed_manager = FeedManager(
                config=config,
                symbols=symbols,
                enable_kucoin=True,
                aggregation_enabled=True
            )
            
            # Configurar callbacks
            self.feed_manager.set_ticker_callback(self.on_ticker_update)
            self.feed_manager.set_orderbook_callback(self.on_orderbook_update)
            self.feed_manager.set_system_alert_callback(self.on_system_alert)
            
            # Inicializar e iniciar
            if await self.feed_manager.start():
                logger.info("✅ Feed Manager iniciado com sucesso")
                self.start_time = time.time()
                
                # Executar por 60 segundos
                await self.run_monitoring_loop(duration=60.0)
                
            else:
                logger.error("❌ Falha ao iniciar Feed Manager")
            
        except Exception as e:
            logger.error(f"Erro no exemplo: {e}")
        finally:
            if self.feed_manager:
                await self.feed_manager.stop()
    
    async def run_monitoring_loop(self, duration: float = 60.0):
        """Executa loop de monitoramento por um período."""
        end_time = time.time() + duration
        
        logger.info(f"📊 Monitorando feeds por {duration} segundos...")
        
        while time.time() < end_time:
            try:
                # Mostrar status a cada 10 segundos
                if int(time.time()) % 10 == 0:
                    await self.show_status()
                
                await asyncio.sleep(1.0)
                
            except KeyboardInterrupt:
                logger.info("Interrompido pelo usuário")
                break
            except Exception as e:
                logger.error(f"Erro no loop de monitoramento: {e}")
        
        # Status final
        await self.show_final_summary()
    
    def on_ticker_update(self, ticker: AggregatedTicker):
        """Callback para atualizações de ticker."""
        self.data_received += 1
        
        logger.info(
            f"📈 {ticker.symbol}: ${ticker.price:.4f} "
            f"({ticker.change_24h_percent:+.2f}%) "
            f"Vol: {ticker.volume_24h:,.0f} "
            f"Sources: {ticker.source_count} "
            f"Confidence: {ticker.confidence_score:.2f}"
        )
        
        # Mostrar alertas de variância
        if ticker.price_variance > 0.02:  # 2%
            logger.warning(
                f"⚠️  Alta variância de preço para {ticker.symbol}: {ticker.price_variance:.3f}"
            )
    
    def on_orderbook_update(self, orderbook: AggregatedOrderBook):
        """Callback para atualizações de orderbook."""
        logger.debug(
            f"📊 OrderBook {orderbook.symbol}: "
            f"Bid: ${orderbook.best_bid:.4f} "
            f"Ask: ${orderbook.best_ask:.4f} "
            f"Spread: ${orderbook.spread:.4f} "
            f"Sources: {orderbook.source_count}"
        )
    
    def on_system_alert(self, alert_type: str, alert_data: Dict[str, Any]):
        """Callback para alertas do sistema."""
        logger.warning(f"🚨 Alerta do sistema: {alert_type}")
        logger.warning(f"   Dados: {alert_data}")
    
    async def show_status(self):
        """Mostra status atual do sistema."""
        if not self.feed_manager:
            return
        
        try:
            status = self.feed_manager.get_system_status()
            stats = self.feed_manager.get_statistics()
            
            uptime = time.time() - self.start_time
            data_rate = self.data_received / uptime if uptime > 0 else 0
            
            logger.info("=" * 60)
            logger.info(f"📊 STATUS DO SISTEMA (Uptime: {uptime:.1f}s)")
            logger.info(f"   Feeds ativos: {status.active_feeds}")
            logger.info(f"   Símbolos agregados: {status.aggregated_symbols}")
            logger.info(f"   Total de dados: {status.total_data_points}")
            logger.info(f"   Taxa de dados: {data_rate:.1f} updates/s")
            logger.info(f"   Erros: {stats.get('errors', 0)}")
            logger.info(f"   Alertas: {stats.get('alerts', 0)}")
            
            # Status dos feeds
            for feed_status in status.feeds_status:
                status_icon = "✅" if feed_status.is_connected else "❌"
                logger.info(
                    f"   {status_icon} {feed_status.name}: "
                    f"Running: {feed_status.is_running}, "
                    f"Connected: {feed_status.is_connected}, "
                    f"Symbols: {feed_status.symbols_count}"
                )
            
            # Mostrar últimos tickers
            logger.info("📈 ÚLTIMOS TICKERS:")
            for symbol in ['BTC-USDT', 'ETH-USDT']:
                ticker = self.feed_manager.get_latest_ticker(symbol)
                if ticker:
                    age = time.time() - ticker.timestamp
                    logger.info(
                        f"   {symbol}: ${ticker.price:.4f} "
                        f"({ticker.change_24h_percent:+.2f}%) "
                        f"[{age:.1f}s ago]"
                    )
            
            logger.info("=" * 60)
            
        except Exception as e:
            logger.error(f"Erro ao mostrar status: {e}")
    
    async def show_final_summary(self):
        """Mostra resumo final da execução."""
        if not self.feed_manager:
            return
        
        try:
            uptime = time.time() - self.start_time
            stats = self.feed_manager.get_statistics()
            
            logger.info("🏁 RESUMO FINAL")
            logger.info("=" * 50)
            logger.info(f"⏱️  Tempo de execução: {uptime:.1f} segundos")
            logger.info(f"📊 Total de dados recebidos: {self.data_received}")
            logger.info(f"📈 Taxa média: {self.data_received / uptime:.2f} updates/s")
            logger.info(f"🔄 Feeds iniciados: {stats.get('feeds_started', 0)}")
            logger.info(f"❌ Erros: {stats.get('errors', 0)}")
            logger.info(f"🚨 Alertas: {stats.get('alerts', 0)}")
            logger.info(f"🔌 Reconexões: {stats.get('reconnections', 0)}")
            
            # Estatísticas do agregador
            if 'symbols_tracked' in stats:
                logger.info(f"📊 Símbolos rastreados: {stats['symbols_tracked']}")
                logger.info(f"🔗 Fontes ativas: {stats.get('active_sources', 0)}")
                logger.info(f"📈 Tickers processados: {stats.get('tickers_processed', 0)}")
                logger.info(f"📊 OrderBooks processados: {stats.get('orderbooks_processed', 0)}")
                logger.info(f"💱 Trades processados: {stats.get('trades_processed', 0)}")
            
            logger.info("=" * 50)
            
        except Exception as e:
            logger.error(f"Erro ao mostrar resumo final: {e}")


async def main():
    """Função principal do exemplo."""
    example = LiveFeedExample()
    await example.run_example()


if __name__ == "__main__":
    # Configurar logging
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Executar exemplo
    asyncio.run(main())
