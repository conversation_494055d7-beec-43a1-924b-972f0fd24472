#!/usr/bin/env python3
"""
QUALIA Pilot Setup Validation
P-02.3: Deploy Piloto com Capital Limitado

Comprehensive validation of pilot setup before real trading
"""

import os
import sys
import json
import time
import asyncio
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import yaml
import requests
from cryptography.fernet import Fernet
import base64

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PilotSetupValidator:
    """Comprehensive pilot setup validator"""
    
    def __init__(self):
        self.config_dir = Path("config")
        self.validation_results = []
        self.critical_failures = []
        
    def decrypt_credentials(self, encrypted_data: str, key: bytes) -> dict:
        """Decrypt credentials"""
        try:
            f = Fernet(key)
            encrypted_bytes = base64.b64decode(encrypted_data.encode())
            decrypted = f.decrypt(encrypted_bytes)
            return json.loads(decrypted.decode())
        except Exception as e:
            raise Exception(f"Failed to decrypt credentials: {e}")
    
    async def validate_files(self) -> Dict[str, Any]:
        """Validate required files exist"""
        logger.info("Validating required files...")
        
        required_files = {
            "pilot_config.yaml": "Pilot configuration",
            ".pilot_credentials": "Encrypted credentials",
            ".pilot_master.key": "Master encryption key",
            ".pilot_env": "Environment variables"
        }
        
        results = {
            'test_name': 'File Validation',
            'status': 'PASS',
            'details': {},
            'missing_files': []
        }
        
        for file_name, description in required_files.items():
            file_path = self.config_dir / file_name
            if file_path.exists():
                # Check file permissions
                stat = file_path.stat()
                permissions = oct(stat.st_mode)[-3:]
                
                results['details'][file_name] = {
                    'exists': True,
                    'size_bytes': stat.st_size,
                    'permissions': permissions,
                    'description': description
                }
                
                # Warn if permissions too open
                if permissions != '600':
                    results['status'] = 'WARNING'
                    results['details'][file_name]['warning'] = f'Permissions {permissions} should be 600'
                
            else:
                results['missing_files'].append(file_name)
                results['status'] = 'FAIL'
        
        if results['missing_files']:
            results['message'] = f"Missing files: {results['missing_files']}"
        else:
            results['message'] = "All required files present"
        
        return results
    
    async def validate_configuration(self) -> Dict[str, Any]:
        """Validate pilot configuration"""
        logger.info("Validating pilot configuration...")
        
        try:
            with open(self.config_dir / "pilot_config.yaml", 'r') as f:
                config = yaml.safe_load(f)
            
            results = {
                'test_name': 'Configuration Validation',
                'status': 'PASS',
                'message': 'Configuration is valid',
                'details': {},
                'issues': []
            }
            
            # Critical configuration checks
            critical_checks = [
                ("environment", "pilot", "Must be pilot environment"),
                ("capital.total_capital_usd", 1000.0, "Capital must be $1,000"),
                ("capital.max_position_size_pct", 2.0, "Max position must be 2%"),
                ("risk_management.max_drawdown_pct", 3.0, "Max drawdown must be 3%"),
                ("circuit_breakers.enabled", True, "Circuit breakers must be enabled"),
                ("exchange.environment", "production", "Must use production exchange"),
                ("monitoring.enabled", True, "Monitoring must be enabled"),
                ("alerts.enabled", True, "Alerts must be enabled")
            ]
            
            for key_path, expected_value, description in critical_checks:
                keys = key_path.split('.')
                value = config
                
                try:
                    for key in keys:
                        value = value[key]
                    
                    if value == expected_value:
                        results['details'][key_path] = {
                            'value': value,
                            'expected': expected_value,
                            'status': 'PASS',
                            'description': description
                        }
                    else:
                        results['details'][key_path] = {
                            'value': value,
                            'expected': expected_value,
                            'status': 'FAIL',
                            'description': description
                        }
                        results['issues'].append(f"{key_path}: {value} (expected {expected_value})")
                        results['status'] = 'FAIL'
                        
                except KeyError:
                    results['details'][key_path] = {
                        'value': None,
                        'expected': expected_value,
                        'status': 'FAIL',
                        'description': f"Missing configuration: {description}"
                    }
                    results['issues'].append(f"Missing: {key_path}")
                    results['status'] = 'FAIL'
            
            # Safety checks
            safety_checks = [
                ("capital.emergency_stop_loss_pct", 5.0, "<=", "Emergency stop loss too high"),
                ("risk_management.daily_loss_limit_usd", 50.0, "<=", "Daily loss limit too high"),
                ("circuit_breakers.total_loss_threshold_usd", 50.0, "<=", "Total loss threshold too high"),
                ("trading.limits.max_positions", 2, "<=", "Too many positions allowed"),
                ("trading.limits.max_daily_trades", 10, "<=", "Too many daily trades allowed")
            ]
            
            for key_path, threshold, operator, warning in safety_checks:
                keys = key_path.split('.')
                value = config
                
                try:
                    for key in keys:
                        value = value[key]
                    
                    if operator == "<=" and value <= threshold:
                        results['details'][f"safety_{key_path}"] = {
                            'value': value,
                            'threshold': threshold,
                            'status': 'PASS',
                            'description': f"Safety check: {warning}"
                        }
                    else:
                        results['details'][f"safety_{key_path}"] = {
                            'value': value,
                            'threshold': threshold,
                            'status': 'WARNING',
                            'description': warning
                        }
                        if results['status'] == 'PASS':
                            results['status'] = 'WARNING'
                        
                except KeyError:
                    pass  # Optional safety checks
            
            if results['issues']:
                results['message'] = f"Configuration issues: {len(results['issues'])}"
            
            return results
            
        except Exception as e:
            return {
                'test_name': 'Configuration Validation',
                'status': 'FAIL',
                'message': f'Failed to validate configuration: {e}',
                'error': str(e)
            }
    
    async def validate_credentials(self) -> Dict[str, Any]:
        """Validate encrypted credentials"""
        logger.info("Validating credentials...")
        
        try:
            # Load master key
            with open(self.config_dir / ".pilot_master.key", 'rb') as f:
                master_key = f.read()
            
            # Load encrypted credentials
            with open(self.config_dir / ".pilot_credentials", 'r') as f:
                encrypted_credentials = f.read()
            
            # Decrypt credentials
            credentials = self.decrypt_credentials(encrypted_credentials, master_key)
            
            results = {
                'test_name': 'Credentials Validation',
                'status': 'PASS',
                'message': 'Credentials are valid',
                'details': {
                    'environment': credentials.get('environment'),
                    'exchange': credentials.get('exchange'),
                    'api_url': credentials.get('api_url'),
                    'has_api_key': bool(credentials.get('credentials', {}).get('api_key')),
                    'has_api_secret': bool(credentials.get('credentials', {}).get('api_secret')),
                    'has_passphrase': bool(credentials.get('credentials', {}).get('api_passphrase')),
                    'limits': credentials.get('limits', {})
                }
            }
            
            # Validate credential structure
            required_fields = ['environment', 'exchange', 'api_url', 'credentials']
            missing_fields = [field for field in required_fields if field not in credentials]
            
            if missing_fields:
                results['status'] = 'FAIL'
                results['message'] = f'Missing credential fields: {missing_fields}'
                results['missing_fields'] = missing_fields
            
            # Validate credential values
            creds = credentials.get('credentials', {})
            required_creds = ['api_key', 'api_secret', 'api_passphrase']
            missing_creds = [cred for cred in required_creds if not creds.get(cred)]
            
            if missing_creds:
                results['status'] = 'FAIL'
                results['message'] = f'Missing credentials: {missing_creds}'
                results['missing_credentials'] = missing_creds
            
            return results
            
        except Exception as e:
            return {
                'test_name': 'Credentials Validation',
                'status': 'FAIL',
                'message': f'Failed to validate credentials: {e}',
                'error': str(e)
            }
    
    async def validate_api_connectivity(self) -> Dict[str, Any]:
        """Validate KuCoin API connectivity"""
        logger.info("Validating API connectivity...")
        
        try:
            # Test public endpoint first
            start_time = time.time()
            try:
                response = requests.get('https://api.kucoin.com/api/v1/timestamp', timeout=10)
                latency = (time.time() - start_time) * 1000
            except requests.exceptions.RequestException as e:
                return {
                    'test_name': 'API Connectivity',
                    'status': 'FAIL',
                    'message': f'Network connectivity failed: {e}',
                    'error': str(e)
                }
            
            results = {
                'test_name': 'API Connectivity',
                'status': 'PASS',
                'message': 'API connectivity is good',
                'details': {
                    'public_api_status': response.status_code,
                    'latency_ms': latency,
                    'api_url': 'https://api.kucoin.com'
                }
            }
            
            if response.status_code != 200:
                results['status'] = 'FAIL'
                results['message'] = f'API connectivity failed: {response.status_code}'
                return results
            
            if latency > 2000:
                results['status'] = 'WARNING'
                results['message'] = f'High API latency: {latency:.1f}ms'
            
            # Test server time sync
            try:
                server_data = response.json()
                server_time = int(server_data['data']) / 1000
                local_time = time.time()
                time_diff = abs(server_time - local_time)
                
                results['details']['time_sync'] = {
                    'server_time': server_time,
                    'local_time': local_time,
                    'difference_seconds': time_diff
                }
                
                if time_diff > 30:  # 30 seconds tolerance
                    results['status'] = 'WARNING'
                    results['message'] = f'Time sync issue: {time_diff:.1f}s difference'
                
            except Exception as e:
                results['details']['time_sync_error'] = str(e)
            
            return results
            
        except Exception as e:
            return {
                'test_name': 'API Connectivity',
                'status': 'FAIL',
                'message': f'API connectivity test failed: {e}',
                'error': str(e)
            }
    
    async def validate_system_resources(self) -> Dict[str, Any]:
        """Validate system resources"""
        logger.info("Validating system resources...")
        
        try:
            try:
                import psutil
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('.')
            except ImportError:
                # Fallback without psutil
                return {
                    'test_name': 'System Resources',
                    'status': 'WARNING',
                    'message': 'psutil not available - system resource monitoring disabled',
                    'details': {
                        'psutil_available': False,
                        'note': 'Install psutil for system resource monitoring'
                    }
                }
            
            results = {
                'test_name': 'System Resources',
                'status': 'PASS',
                'message': 'System resources are adequate',
                'details': {
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent,
                    'memory_available_gb': memory.available / (1024**3),
                    'disk_percent': disk.percent,
                    'disk_free_gb': disk.free / (1024**3)
                }
            }
            
            issues = []
            if cpu_percent > 80:
                issues.append(f"High CPU: {cpu_percent}%")
            if memory.percent > 90:
                issues.append(f"High memory: {memory.percent}%")
            if disk.percent > 90:
                issues.append(f"High disk usage: {disk.percent}%")
            if memory.available < 1024**3:  # Less than 1GB
                issues.append("Low available memory")
            
            if issues:
                results['status'] = 'WARNING'
                results['message'] = f'Resource issues: {"; ".join(issues)}'
                results['issues'] = issues
            
            return results
            
        except Exception as e:
            return {
                'test_name': 'System Resources',
                'status': 'FAIL',
                'message': f'System resource validation failed: {e}',
                'error': str(e)
            }
    
    async def validate_directories(self) -> Dict[str, Any]:
        """Validate required directories"""
        logger.info("Validating directories...")
        
        required_dirs = [
            'logs/pilot',
            'data/pilot', 
            'backups/pilot',
            'reports/pilot',
            'tmp/pilot'
        ]
        
        results = {
            'test_name': 'Directory Structure',
            'status': 'PASS',
            'message': 'All directories present',
            'details': {},
            'missing_dirs': []
        }
        
        for dir_path in required_dirs:
            path = Path(dir_path)
            if path.exists():
                results['details'][dir_path] = {
                    'exists': True,
                    'is_directory': path.is_dir(),
                    'writable': os.access(path, os.W_OK)
                }
                
                if not path.is_dir() or not os.access(path, os.W_OK):
                    results['status'] = 'WARNING'
            else:
                results['missing_dirs'].append(dir_path)
                # Create missing directories
                try:
                    path.mkdir(parents=True, exist_ok=True)
                    results['details'][dir_path] = {
                        'exists': True,
                        'created': True,
                        'is_directory': True,
                        'writable': True
                    }
                except Exception as e:
                    results['details'][dir_path] = {
                        'exists': False,
                        'error': str(e)
                    }
                    results['status'] = 'FAIL'
        
        if results['missing_dirs']:
            results['message'] = f'Created missing directories: {len(results["missing_dirs"])}'
        
        return results
    
    async def run_all_validations(self) -> Dict[str, Any]:
        """Run all validation tests"""
        logger.info("Starting comprehensive pilot validation...")
        
        validation_report = {
            'validation_id': f"pilot_validation_{int(time.time())}",
            'start_time': datetime.utcnow().isoformat(),
            'environment': 'pilot',
            'tests': [],
            'summary': {},
            'status': 'running'
        }
        
        # Define validation test suite
        validation_tests = [
            ("File Validation", self.validate_files),
            ("Configuration Validation", self.validate_configuration),
            ("Credentials Validation", self.validate_credentials),
            ("API Connectivity", self.validate_api_connectivity),
            ("System Resources", self.validate_system_resources),
            ("Directory Structure", self.validate_directories)
        ]
        
        passed_tests = 0
        failed_tests = 0
        warning_tests = 0
        
        try:
            for test_name, test_func in validation_tests:
                logger.info(f"Running validation: {test_name}")
                
                test_start = time.time()
                try:
                    result = await test_func()
                    result['execution_time_ms'] = (time.time() - test_start) * 1000
                    result['timestamp'] = datetime.utcnow().isoformat()
                    
                    validation_report['tests'].append(result)
                    
                    if result['status'] == 'PASS':
                        passed_tests += 1
                        logger.info(f"✓ {test_name} PASSED")
                    elif result['status'] == 'WARNING':
                        warning_tests += 1
                        logger.warning(f"⚠ {test_name} WARNING: {result['message']}")
                    else:
                        failed_tests += 1
                        self.critical_failures.append(test_name)
                        logger.error(f"✗ {test_name} FAILED: {result['message']}")
                
                except Exception as e:
                    failed_tests += 1
                    self.critical_failures.append(test_name)
                    result = {
                        'test_name': test_name,
                        'status': 'FAIL',
                        'message': f'Validation failed with exception: {str(e)}',
                        'execution_time_ms': (time.time() - test_start) * 1000,
                        'timestamp': datetime.utcnow().isoformat(),
                        'error': str(e)
                    }
                    validation_report['tests'].append(result)
                    logger.error(f"✗ {test_name} EXCEPTION: {e}")
            
            # Calculate summary
            total_tests = len(validation_tests)
            validation_report['summary'] = {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'warning_tests': warning_tests,
                'success_rate': (passed_tests / total_tests) * 100 if total_tests > 0 else 0,
                'critical_failures': self.critical_failures
            }
            
            # Determine overall status
            if failed_tests == 0:
                validation_report['status'] = 'PASS' if warning_tests == 0 else 'WARNING'
            else:
                validation_report['status'] = 'FAIL'
            
            validation_report['end_time'] = datetime.utcnow().isoformat()
            
            logger.info(f"Pilot validation completed: {validation_report['status']}")
            
        except Exception as e:
            validation_report['status'] = 'FAIL'
            validation_report['error'] = str(e)
            validation_report['end_time'] = datetime.utcnow().isoformat()
            logger.error(f"Validation suite failed: {e}")
        
        # Save validation report
        await self._save_validation_report(validation_report)
        
        return validation_report
    
    async def _save_validation_report(self, report: Dict[str, Any]):
        """Save validation report"""
        try:
            report_dir = Path("reports/pilot")
            report_dir.mkdir(parents=True, exist_ok=True)
            
            report_file = report_dir / f"pilot_validation_{report['validation_id']}.json"
            
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"Pilot validation report saved: {report_file}")
            
        except Exception as e:
            logger.error(f"Failed to save validation report: {e}")

async def main():
    """Main validation function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='QUALIA Pilot Setup Validation')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    print("🔍 QUALIA P-02.3: PILOT SETUP VALIDATION")
    print("=" * 60)
    print("⚠️  Validating PRODUCTION pilot setup with $1,000 capital")
    print("⚠️  This validation must PASS before real trading")
    print("=" * 60)
    
    # Execute validation
    validator = PilotSetupValidator()
    report = await validator.run_all_validations()
    
    # Print summary
    print("\n" + "="*80)
    print("QUALIA PILOT VALIDATION RESULTS")
    print("="*80)
    print(f"Validation ID: {report['validation_id']}")
    print(f"Environment: pilot")
    print(f"Overall Status: {report['status']}")
    
    summary = report['summary']
    print(f"Total Tests: {summary['total_tests']}")
    print(f"Passed: {summary['passed_tests']}")
    print(f"Failed: {summary['failed_tests']}")
    print(f"Warnings: {summary['warning_tests']}")
    print(f"Success Rate: {summary['success_rate']:.1f}%")
    
    if report['status'] == 'PASS':
        print("🎉 PILOT SETUP VALIDATION PASSED!")
        print("✅ Ready for pilot trading deployment")
    elif report['status'] == 'WARNING':
        print("⚠️ PILOT SETUP VALIDATION PASSED WITH WARNINGS")
        print("⚠️ Review warnings before proceeding")
    else:
        print("❌ PILOT SETUP VALIDATION FAILED")
        print("❌ DO NOT PROCEED WITH REAL TRADING")
    
    # Show failed tests
    if summary['failed_tests'] > 0:
        print("\nCRITICAL FAILURES:")
        for test in report['tests']:
            if test['status'] == 'FAIL':
                print(f"  ✗ {test['test_name']}: {test['message']}")
    
    # Show warning tests
    if summary['warning_tests'] > 0:
        print("\nWARNINGS:")
        for test in report['tests']:
            if test['status'] == 'WARNING':
                print(f"  ⚠ {test['test_name']}: {test['message']}")
    
    print("="*80)
    
    # Exit with appropriate code
    if report['status'] == 'FAIL':
        print("\n🚨 VALIDATION FAILED - DO NOT PROCEED WITH REAL TRADING")
        sys.exit(1)
    elif report['status'] == 'WARNING':
        print("\n⚠️ VALIDATION PASSED WITH WARNINGS - REVIEW BEFORE PROCEEDING")
        sys.exit(0)
    else:
        print("\n✅ VALIDATION PASSED - READY FOR PILOT TRADING")
        sys.exit(0)

if __name__ == '__main__':
    asyncio.run(main())
