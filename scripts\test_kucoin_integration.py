#!/usr/bin/env python3
"""
Teste específico da KucoinIntegration para diagnosticar problemas.
"""

import asyncio
import os
import sys
from pathlib import Path

# Adicionar o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from dotenv import load_dotenv
from qualia.market.kucoin_integration import KucoinIntegration

async def test_kucoin_integration():
    """Teste da KucoinIntegration com configurações específicas."""
    
    # Carregar variáveis de ambiente
    env_path = Path(__file__).parent.parent / ".env"
    load_dotenv(env_path)
    
    print(f"✅ Arquivo .env carregado: {env_path}")
    
    # Verificar credenciais
    api_key = os.getenv('KUCOIN_API_KEY', '')
    api_secret = os.getenv('KUCOIN_SECRET_KEY', '')
    passphrase = os.getenv('KUCOIN_PASSPHRASE', '')
    
    print(f"🔍 Verificando credenciais:")
    print(f"   KUCOIN_API_KEY: {'✅ Presente' if api_key else '❌ Ausente'}")
    print(f"   KUCOIN_SECRET_KEY: {'✅ Presente' if api_secret else '❌ Ausente'}")
    print(f"   KUCOIN_PASSPHRASE: {'✅ Presente' if passphrase else '❌ Ausente'}")
    
    if not all([api_key, api_secret, passphrase]):
        print("❌ Credenciais incompletas!")
        return False
    
    # Teste com configurações específicas
    print("\n🔧 TESTE: KucoinIntegration com timeouts aumentados")
    try:
        kucoin = KucoinIntegration(
            api_key=api_key,
            api_secret=api_secret,
            password=passphrase,
            conn_timeout=60.0,  # 60 segundos para conexão
            ticker_timeout=60.0,  # 60 segundos para ticker
            ohlcv_timeout=60.0,  # 60 segundos para OHLCV
            use_websocket=False,  # Desabilitar WebSocket para simplificar
        )
        
        print("   Inicializando conexão...")
        await kucoin.initialize_connection()
        print("   ✅ Conexão inicializada!")
        
        print("   Testando ticker BTC/USDT...")
        ticker = await kucoin.fetch_ticker('BTC/USDT')
        if ticker:
            print(f"   ✅ Ticker recebido: ${ticker.get('last', 'N/A'):,.2f}")
        else:
            print("   ❌ Ticker vazio")
            
        print("   Testando ticker ETH/USDT...")
        ticker2 = await kucoin.fetch_ticker('ETH/USDT')
        if ticker2:
            print(f"   ✅ Ticker recebido: ${ticker2.get('last', 'N/A'):,.2f}")
        else:
            print("   ❌ Ticker vazio")
        
        await kucoin.close()
        print("   ✅ KUCOIN INTEGRATION FUNCIONANDO!")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro na KucoinIntegration: {e}")
        import traceback
        traceback.print_exc()
        try:
            await kucoin.close()
        except:
            pass
        return False

async def test_live_feed_components():
    """Teste dos componentes do live feed."""
    
    print("\n🧪 TESTE: Componentes Live Feed")
    
    # Carregar variáveis de ambiente
    env_path = Path(__file__).parent.parent / ".env"
    load_dotenv(env_path)
    
    api_key = os.getenv('KUCOIN_API_KEY', '')
    api_secret = os.getenv('KUCOIN_SECRET_KEY', '')
    passphrase = os.getenv('KUCOIN_PASSPHRASE', '')
    
    try:
        from qualia.live_feed.kucoin_feed import KuCoinFeed
        
        print("   Criando KuCoinFeed...")
        feed = KuCoinFeed(
            api_key=api_key,
            api_secret=api_secret,
            password=passphrase,
            symbols=['BTC-USDT', 'ETH-USDT'],
            enable_websocket=False,  # Desabilitar WebSocket
            enable_rest_fallback=True,
            timeout=60.0,
        )
        
        print("   Inicializando feed...")
        await feed.start()
        
        print("   Aguardando dados por 30 segundos...")
        await asyncio.sleep(30)
        
        # Verificar se recebeu dados
        status = feed.get_feed_status()
        print(f"   Status: {status}")
        
        await feed.stop()
        print("   ✅ LIVE FEED COMPONENTS FUNCIONANDO!")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro nos componentes live feed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Função principal."""
    print("🚀 Teste Específico KucoinIntegration")
    print("=" * 60)
    
    # Teste 1: KucoinIntegration direta
    success1 = await test_kucoin_integration()
    
    # Teste 2: Componentes Live Feed
    success2 = await test_live_feed_components()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("✅ SUCESSO: Todos os testes passaram!")
        print("   O sistema está funcionando corretamente.")
    elif success1:
        print("⚠️ PARCIAL: KucoinIntegration funciona, mas Live Feed tem problemas.")
        print("   O problema está na camada Live Feed.")
    else:
        print("❌ FALHA: KucoinIntegration não está funcionando.")
        print("   O problema está na integração base.")

if __name__ == "__main__":
    asyncio.run(main())
