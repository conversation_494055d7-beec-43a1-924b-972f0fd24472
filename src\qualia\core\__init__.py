"""QUALIA Core - Quantum operators and core trading engine."""

__all__ = [
    "QASTCore",
    "FoldingOperator",
    "apply_folding",
    "ResonanceOperator",
    "apply_resonance",
    "EmergenceOperator",
    "apply_emergence",
    "RetrocausalityOperator",
    "apply_retrocausality",
    "ObserverOperator",
    "observe_system",
    "apply_collapse",
    "apply_decoherence",
    "apply_transcendence",
    "apply_retardo",
    "apply_acceleration",
    "apply_anacronism",
    "apply_CCC",
    "apply_DDD",
    "apply_OOO",
    "apply_TTT",
    "apply_RRR",
    "apply_AAA",
    "apply_ZZZ",
    "apply_NNN",
    "apply_XXX",
    "inject_noise",
    "apply_zne",
    "apply_pem",
    "apply_ml_mitigation",
    "qobj_to_qiskit",
    "qobj_to_cirq",
    "QualiaSelfObserver",
    "QuantumObserver",
    "QUALIAConsciousness",
    "QualiaSymbolicProcessor",
    "SymbolicIntentionOperator",
    "OperatorSIM",
    "QuantumSymbolicTokenizer",
    "Base64PatternEncoder",
    "HotspotDetector",
    "IntentioWaveletEngine",
    "MicrotubuleOperator",
    # Interfaces conceituais (ainda sem implementação)
    "FoldingOperatorInterface",
    "ResonanceOperatorInterface",
    "EmergenceOperatorInterface",
    "RetrocausalityOperatorInterface",
    "ObserverOperatorInterface",
    "ReductionOperator",
    "IntegrationOperator",
    "EntanglementOperator",
    "ReductionOperatorInterface",
    "IntegrationOperatorInterface",
]

LAZY_MAP: dict[str, tuple[str, str]] = {
    "QASTCore": (".qast_core", "QASTCore"),
    "FoldingOperator": (".folding", "FoldingOperator"),
    "apply_folding": (".folding", "apply_folding"),
    "ResonanceOperator": (".resonance", "ResonanceOperator"),
    "apply_resonance": (".resonance", "apply_resonance"),
    "EmergenceOperator": (".emergence", "EmergenceOperator"),
    "apply_emergence": (".emergence", "apply_emergence"),
    "RetrocausalityOperator": (".retrocausality", "RetrocausalityOperator"),
    "apply_retrocausality": (".retrocausality", "apply_retrocausality"),
    "ObserverOperator": (".observer", "ObserverOperator"),
    "observe_system": (".observer", "observe_system"),
    "ReductionOperator": (".reduction_operator", "ReductionOperator"),
    "IntegrationOperator": (".integration_operator", "IntegrationOperator"),
    "EntanglementOperator": (".entanglement_operator", "EntanglementOperator"),
    "apply_collapse": (".meta_ops", "apply_collapse"),
    "apply_decoherence": (".meta_ops", "apply_decoherence"),
    "apply_transcendence": (".meta_ops", "apply_transcendence"),
    "apply_retardo": (".meta_ops", "apply_retardo"),
    "apply_acceleration": (".meta_ops", "apply_acceleration"),
    "apply_anacronism": (".meta_ops", "apply_anacronism"),
    "apply_CCC": (".meta_ops_qutip", "apply_CCC"),
    "apply_DDD": (".meta_ops_qutip", "apply_DDD"),
    "apply_OOO": (".meta_ops_qutip", "apply_OOO"),
    "apply_TTT": (".meta_ops_qutip", "apply_TTT"),
    "apply_RRR": (".meta_ops_qutip", "apply_RRR"),
    "apply_AAA": (".meta_ops_qutip", "apply_AAA"),
    "apply_ZZZ": (".meta_ops_qutip", "apply_ZZZ"),
    "apply_NNN": (".meta_ops_qutip", "apply_NNN"),
    "apply_XXX": (".meta_ops_qutip", "apply_XXX"),
    "inject_noise": (".noise", "inject_noise"),
    "apply_zne": (".error_mitigation", "apply_zne"),
    "apply_pem": (".error_mitigation", "apply_pem"),
    "apply_ml_mitigation": (".error_mitigation", "apply_ml_mitigation"),
    "qobj_to_qiskit": (".meta_ops_qutip", "qobj_to_qiskit"),
    "qobj_to_cirq": (".meta_ops_qutip", "qobj_to_cirq"),
    "QualiaSelfObserver": (".observer", "QualiaSelfObserver"),
    "QuantumObserver": (".observer", "QuantumObserver"),
    "QUALIAConsciousness": (".consciousness", "QUALIAConsciousness"),
    "QualiaSymbolicProcessor": (".consciousness", "QualiaSymbolicProcessor"),
    "SymbolicIntentionOperator": (
        ".symbolic_intention_operator",
        "SymbolicIntentionOperator",
    ),
    "OperatorSIM": (".operator_sim", "OperatorSIM"),
    "QuantumSymbolicTokenizer": (".tokenizer", "QuantumSymbolicTokenizer"),
    "Base64PatternEncoder": (".intention", "Base64PatternEncoder"),
    "HotspotDetector": (".intention", "HotspotDetector"),
    "IntentioWaveletEngine": (".intention", "IntentioWaveletEngine"),
    "MicrotubuleOperator": (".microtubule_operator", "MicrotubuleOperator"),
    "FoldingOperatorInterface": (".operator_interfaces", "FoldingOperatorInterface"),
    "ResonanceOperatorInterface": (
        ".operator_interfaces",
        "ResonanceOperatorInterface",
    ),
    "EmergenceOperatorInterface": (
        ".operator_interfaces",
        "EmergenceOperatorInterface",
    ),
    "RetrocausalityOperatorInterface": (
        ".operator_interfaces",
        "RetrocausalityOperatorInterface",
    ),
    "ObserverOperatorInterface": (".operator_interfaces", "ObserverOperatorInterface"),
    "ReductionOperatorInterface": (
        ".operator_interfaces",
        "ReductionOperatorInterface",
    ),
    "IntegrationOperatorInterface": (
        ".operator_interfaces",
        "IntegrationOperatorInterface",
    ),
}


from typing import Any


def __getattr__(name: str) -> Any:
    """Return attributes lazily using :data:`LAZY_MAP`.

    Parameters
    ----------
    name
        Name of the attribute being accessed.

    Returns
    -------
    Any
        The imported object.
    """

    from importlib import import_module

    try:
        module_name, attr = LAZY_MAP[name]
    except KeyError as exc:  # pragma: no cover - invalid attribute access
        raise AttributeError(f"module '{__name__}' has no attribute {name}") from exc

    module = import_module(module_name, __name__)
    return getattr(module, attr)


# Nota de integração futura
# ------------------------------
# As interfaces adicionadas acima descrevem comportamentos esperados dos
# operadores conceituais F, M, E, Z e O. Quando suas implementações forem
# concluídas deverão substituir os stubs atuais em ``folding.py``,
# ``resonance.py``, ``emergence.py``, ``retrocausality.py`` e ``observer.py``.
