"""
QUALIA Hyperparameters Telemetry System

YAA REFINEMENT: Sistema de telemetria especializado para monitoramento de
hiperparâmetros críticos (price_amp, news_amp, min_confidence, decision).
Integra com Prometheus/StatsD para observabilidade completa.
"""

from __future__ import annotations

import time
import logging
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from collections import deque
from contextlib import contextmanager

try:
    from datadog import DogStatsd
except ImportError:
    DogStatsd = None

from ..config.hyperparams_loader import HyperParams
from ..monitoring.metrics import get_collector, MetricsCollector
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class HyperParamsTelemetryEvent:
    """Evento de telemetria para hiperparâmetros."""
    
    timestamp: float = field(default_factory=time.time)
    
    # Hiperparâmetros atuais
    price_amplification: float = 0.0
    news_amplification: float = 0.0
    min_confidence: float = 0.0
    pattern_threshold: float = 0.0
    
    # Contexto da decisão
    raw_score: Optional[float] = None
    confidence: Optional[float] = None
    decision: Optional[str] = None  # "buy", "sell", "hold"
    
    # Métricas de performance
    patterns_detected: int = 0
    signals_generated: int = 0
    signals_executed: int = 0
    execution_success_rate: float = 0.0
    false_positive_rate: float = 0.0
    
    # Contexto do mercado
    symbol: Optional[str] = None
    market_volatility: Optional[float] = None
    market_trend: Optional[str] = None  # "bull", "bear", "sideways"
    
    # Metadados
    component: str = "unknown"  # "AmplificationCalibrator", "TemporalPatternDetector", etc.
    source: str = "unknown"     # Fonte dos hiperparâmetros
    trace_id: Optional[str] = None

    # Contexto adicional (para dados extras)
    extra_context: Dict[str, Any] = field(default_factory=dict)


class HyperParamsTelemetryCollector:
    """
    Coletor de telemetria especializado para hiperparâmetros.
    
    Coleta métricas detalhadas sobre:
    - Valores atuais dos hiperparâmetros
    - Decisões tomadas com base nos parâmetros
    - Performance das decisões
    - Contexto do mercado
    """
    
    def __init__(
        self,
        metrics_collector: Optional[MetricsCollector] = None,
        statsd_client: Optional[DogStatsd] = None,
        max_events: int = 10000
    ):
        self.metrics_collector = metrics_collector or get_collector()
        self.statsd_client = statsd_client
        self.max_events = max_events
        
        # Buffer de eventos para análise
        self.events: deque[HyperParamsTelemetryEvent] = deque(maxlen=max_events)
        
        # Contadores para métricas agregadas
        self.decision_counts: Dict[str, int] = {"buy": 0, "sell": 0, "hold": 0}
        self.component_counts: Dict[str, int] = {}
        
        logger.info("✅ HyperParamsTelemetryCollector inicializado")
    
    def record_decision_event(
        self,
        hyperparams: HyperParams,
        raw_score: float,
        confidence: float,
        decision: str,
        component: str,
        **kwargs
    ) -> None:
        """
        Registra evento de decisão com hiperparâmetros.
        
        Args:
            hyperparams: Hiperparâmetros utilizados na decisão
            raw_score: Score bruto antes da aplicação de thresholds
            confidence: Confiança final da decisão
            decision: Decisão tomada ("buy", "sell", "hold")
            component: Componente que tomou a decisão
            **kwargs: Contexto adicional (symbol, market_volatility, etc.)
        """
        # Separa kwargs conhecidos dos extras
        known_fields = {
            'symbol', 'market_volatility', 'market_trend', 'trace_id',
            'patterns_detected', 'signals_generated', 'signals_executed',
            'execution_success_rate', 'false_positive_rate'
        }

        known_kwargs = {k: v for k, v in kwargs.items() if k in known_fields}
        extra_kwargs = {k: v for k, v in kwargs.items() if k not in known_fields}

        event = HyperParamsTelemetryEvent(
            price_amplification=hyperparams.price_amplification,
            news_amplification=hyperparams.news_amplification,
            min_confidence=hyperparams.min_confidence,
            pattern_threshold=hyperparams.pattern_threshold,
            raw_score=raw_score,
            confidence=confidence,
            decision=decision,
            component=component,
            source=hyperparams.source,
            extra_context=extra_kwargs,
            **known_kwargs
        )
        
        self._record_event(event)
        self._emit_decision_metrics(event)
    
    def record_calibration_event(
        self,
        hyperparams: HyperParams,
        patterns_detected: int,
        signals_generated: int,
        signals_executed: int,
        execution_success_rate: float,
        false_positive_rate: float,
        component: str = "AmplificationCalibrator",
        **kwargs
    ) -> None:
        """
        Registra evento de calibração de hiperparâmetros.
        
        Args:
            hyperparams: Hiperparâmetros após calibração
            patterns_detected: Número de padrões detectados
            signals_generated: Número de sinais gerados
            signals_executed: Número de sinais executados
            execution_success_rate: Taxa de sucesso das execuções
            false_positive_rate: Taxa de falsos positivos
            component: Componente que fez a calibração
            **kwargs: Contexto adicional
        """
        # Separa kwargs conhecidos dos extras
        known_fields = {
            'symbol', 'market_volatility', 'market_trend', 'trace_id',
            'raw_score', 'confidence', 'decision'
        }

        known_kwargs = {k: v for k, v in kwargs.items() if k in known_fields}
        extra_kwargs = {k: v for k, v in kwargs.items() if k not in known_fields}

        event = HyperParamsTelemetryEvent(
            price_amplification=hyperparams.price_amplification,
            news_amplification=hyperparams.news_amplification,
            min_confidence=hyperparams.min_confidence,
            pattern_threshold=hyperparams.pattern_threshold,
            patterns_detected=patterns_detected,
            signals_generated=signals_generated,
            signals_executed=signals_executed,
            execution_success_rate=execution_success_rate,
            false_positive_rate=false_positive_rate,
            component=component,
            source=hyperparams.source,
            extra_context=extra_kwargs,
            **known_kwargs
        )
        
        self._record_event(event)
        self._emit_calibration_metrics(event)
    
    def _record_event(self, event: HyperParamsTelemetryEvent) -> None:
        """Registra evento no buffer interno."""
        self.events.append(event)
        
        # Atualiza contadores
        if event.decision:
            self.decision_counts[event.decision] = self.decision_counts.get(event.decision, 0) + 1
        
        self.component_counts[event.component] = self.component_counts.get(event.component, 0) + 1
    
    def _emit_decision_metrics(self, event: HyperParamsTelemetryEvent) -> None:
        """Emite métricas de decisão para sistemas externos."""
        base_tags = {
            "component": event.component,
            "decision": event.decision or "unknown",
            "symbol": event.symbol or "unknown",
            "source": event.source
        }
        
        # Métricas para MetricsCollector (interno)
        self.metrics_collector.record_metric(
            "hyperparams.price_amplification",
            event.price_amplification,
            tags=base_tags
        )
        self.metrics_collector.record_metric(
            "hyperparams.news_amplification", 
            event.news_amplification,
            tags=base_tags
        )
        self.metrics_collector.record_metric(
            "hyperparams.min_confidence",
            event.min_confidence,
            tags=base_tags
        )
        self.metrics_collector.record_metric(
            "hyperparams.pattern_threshold",
            event.pattern_threshold,
            tags=base_tags
        )
        
        if event.raw_score is not None:
            self.metrics_collector.record_metric(
                "hyperparams.raw_score",
                event.raw_score,
                tags=base_tags
            )
        
        if event.confidence is not None:
            self.metrics_collector.record_metric(
                "hyperparams.final_confidence",
                event.confidence,
                tags=base_tags
            )
        
        # Métricas para StatsD/Prometheus (externo)
        if self.statsd_client:
            tag_list = [f"{k}:{v}" for k, v in base_tags.items()]
            
            try:
                self.statsd_client.gauge(
                    "qualia.hyperparams.price_amplification",
                    event.price_amplification,
                    tags=tag_list
                )
                self.statsd_client.gauge(
                    "qualia.hyperparams.news_amplification",
                    event.news_amplification,
                    tags=tag_list
                )
                self.statsd_client.gauge(
                    "qualia.hyperparams.min_confidence",
                    event.min_confidence,
                    tags=tag_list
                )
                self.statsd_client.gauge(
                    "qualia.hyperparams.pattern_threshold",
                    event.pattern_threshold,
                    tags=tag_list
                )
                
                if event.raw_score is not None:
                    self.statsd_client.gauge(
                        "qualia.hyperparams.raw_score",
                        event.raw_score,
                        tags=tag_list
                    )
                
                if event.confidence is not None:
                    self.statsd_client.gauge(
                        "qualia.hyperparams.final_confidence",
                        event.confidence,
                        tags=tag_list
                    )
                
                # Contador de decisões
                self.statsd_client.increment(
                    "qualia.hyperparams.decisions_total",
                    tags=tag_list
                )
                
            except Exception as e:
                logger.debug(f"Erro ao emitir métricas StatsD: {e}")
    
    def _emit_calibration_metrics(self, event: HyperParamsTelemetryEvent) -> None:
        """Emite métricas de calibração para sistemas externos."""
        base_tags = {
            "component": event.component,
            "source": event.source
        }
        
        # Métricas de performance da calibração
        self.metrics_collector.record_metric(
            "hyperparams.patterns_detected",
            event.patterns_detected,
            tags=base_tags
        )
        self.metrics_collector.record_metric(
            "hyperparams.signals_generated",
            event.signals_generated,
            tags=base_tags
        )
        self.metrics_collector.record_metric(
            "hyperparams.signals_executed",
            event.signals_executed,
            tags=base_tags
        )
        self.metrics_collector.record_metric(
            "hyperparams.execution_success_rate",
            event.execution_success_rate,
            tags=base_tags
        )
        self.metrics_collector.record_metric(
            "hyperparams.false_positive_rate",
            event.false_positive_rate,
            tags=base_tags
        )
        
        # StatsD/Prometheus
        if self.statsd_client:
            tag_list = [f"{k}:{v}" for k, v in base_tags.items()]
            
            try:
                self.statsd_client.gauge(
                    "qualia.hyperparams.patterns_detected",
                    event.patterns_detected,
                    tags=tag_list
                )
                self.statsd_client.gauge(
                    "qualia.hyperparams.signals_generated",
                    event.signals_generated,
                    tags=tag_list
                )
                self.statsd_client.gauge(
                    "qualia.hyperparams.execution_success_rate",
                    event.execution_success_rate,
                    tags=tag_list
                )
                self.statsd_client.gauge(
                    "qualia.hyperparams.false_positive_rate",
                    event.false_positive_rate,
                    tags=tag_list
                )
                
                # Contador de calibrações
                self.statsd_client.increment(
                    "qualia.hyperparams.calibrations_total",
                    tags=tag_list
                )
                
            except Exception as e:
                logger.debug(f"Erro ao emitir métricas de calibração StatsD: {e}")
    
    @contextmanager
    def decision_context(
        self,
        hyperparams: HyperParams,
        component: str,
        **context_kwargs
    ):
        """
        Context manager para capturar decisões automaticamente.
        
        Usage:
            with telemetry.decision_context(hyperparams, "AmplificationCalibrator", symbol="BTC/USDT"):
                raw_score = calculate_score()
                confidence = apply_threshold(raw_score)
                decision = make_decision(confidence)
                # Métricas são capturadas automaticamente
        """
        start_time = time.time()
        context = {"start_time": start_time, **context_kwargs}
        
        try:
            yield context
        finally:
            # Captura métricas do contexto se disponíveis
            if "raw_score" in context and "confidence" in context and "decision" in context:
                self.record_decision_event(
                    hyperparams=hyperparams,
                    raw_score=context["raw_score"],
                    confidence=context["confidence"],
                    decision=context["decision"],
                    component=component,
                    **{k: v for k, v in context.items() if k not in ["raw_score", "confidence", "decision", "start_time"]}
                )
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas resumidas dos eventos coletados."""
        if not self.events:
            return {"status": "no_data"}
        
        recent_events = list(self.events)[-100:]  # Últimos 100 eventos
        
        # Estatísticas dos hiperparâmetros
        price_amps = [e.price_amplification for e in recent_events]
        news_amps = [e.news_amplification for e in recent_events]
        confidences = [e.min_confidence for e in recent_events]
        
        return {
            "total_events": len(self.events),
            "recent_events": len(recent_events),
            "decision_counts": self.decision_counts.copy(),
            "component_counts": self.component_counts.copy(),
            "hyperparams_stats": {
                "price_amplification": {
                    "min": min(price_amps) if price_amps else 0,
                    "max": max(price_amps) if price_amps else 0,
                    "avg": sum(price_amps) / len(price_amps) if price_amps else 0
                },
                "news_amplification": {
                    "min": min(news_amps) if news_amps else 0,
                    "max": max(news_amps) if news_amps else 0,
                    "avg": sum(news_amps) / len(news_amps) if news_amps else 0
                },
                "min_confidence": {
                    "min": min(confidences) if confidences else 0,
                    "max": max(confidences) if confidences else 0,
                    "avg": sum(confidences) / len(confidences) if confidences else 0
                }
            }
        }


# Instância global para uso em todo o sistema
_global_hyperparams_telemetry: Optional[HyperParamsTelemetryCollector] = None


def get_global_hyperparams_telemetry() -> HyperParamsTelemetryCollector:
    """Retorna instância global do coletor de telemetria de hiperparâmetros."""
    global _global_hyperparams_telemetry
    if _global_hyperparams_telemetry is None:
        _global_hyperparams_telemetry = HyperParamsTelemetryCollector()
    return _global_hyperparams_telemetry


__all__ = [
    "HyperParamsTelemetryEvent",
    "HyperParamsTelemetryCollector", 
    "get_global_hyperparams_telemetry"
]
