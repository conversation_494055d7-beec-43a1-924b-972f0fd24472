"""Complete monitoring system demonstration for QUALIA.

This script demonstrates the full integration of all monitoring components:
- Integrated monitoring system
- Health monitoring dashboard
- Performance metrics collection
- Automated parameter tuning
- Multi-exchange failover
- Signal pipeline monitoring
- Bootstrap progress tracking

This serves as both a demonstration and a template for production deployment.
"""

import asyncio
import time
import random
from typing import Dict, Any

from qualia.utils.logger import get_logger
from qualia.monitoring.integrated_monitoring_system import IntegratedMonitoringSystem, MonitoringConfig
from qualia.monitoring.health_dashboard import HealthStatus
from qualia.monitoring.performance_metrics import MetricType
from qualia.monitoring.parameter_tuner import ParameterDefinition, ParameterType, OptimizationObjective
from qualia.utils.signal_pipeline_monitor import SignalStage, RejectionReason
from qualia.utils.bootstrap_tracker import BootstrapPhase

logger = get_logger(__name__)


class QualiaMonitoringDemo:
    """Comprehensive demonstration of QUALIA monitoring system."""
    
    def __init__(self):
        # Configure monitoring system
        self.config = MonitoringConfig(
            enable_health_monitoring=True,
            enable_performance_metrics=True,
            enable_parameter_tuning=True,
            enable_failover_management=True,
            enable_signal_monitoring=True,
            enable_bootstrap_tracking=True,
            health_check_interval=10.0,
            metrics_collection_interval=5.0,
            parameter_tuning_interval=300.0,  # 5 minutes for demo
            enable_alerting=True
        )
        
        # Initialize integrated monitoring system
        self.monitoring_system = IntegratedMonitoringSystem(
            config=self.config,
            name="qualia_demo_monitoring"
        )
        
        # Demo state
        self.demo_running = False
        self.demo_tasks = []

    async def run_complete_demo(self) -> None:
        """Run complete monitoring system demonstration."""
        logger.info("🚀 Starting QUALIA Complete Monitoring System Demo")
        
        try:
            # Initialize monitoring system
            await self.monitoring_system.start()
            
            # Setup demo components
            await self._setup_demo_components()
            
            # Start demo scenarios
            self.demo_running = True
            self.demo_tasks = [
                asyncio.create_task(self._demo_health_monitoring()),
                asyncio.create_task(self._demo_performance_metrics()),
                asyncio.create_task(self._demo_signal_processing()),
                asyncio.create_task(self._demo_bootstrap_tracking()),
                asyncio.create_task(self._demo_failover_scenarios()),
                asyncio.create_task(self._demo_parameter_optimization()),
                asyncio.create_task(self._demo_system_integration())
            ]
            
            # Run demo for specified duration
            demo_duration = 300.0  # 5 minutes
            logger.info(f"🎯 Running demo for {demo_duration/60:.1f} minutes...")
            
            await asyncio.sleep(demo_duration)
            
            # Generate final report
            await self._generate_demo_report()
            
        except Exception as e:
            logger.error(f"Demo failed: {e}")
            raise
        finally:
            await self._cleanup_demo()

    async def _setup_demo_components(self) -> None:
        """Setup demo components and configurations."""
        logger.info("🔧 Setting up demo components...")
        
        # Register health monitoring components
        self.monitoring_system.register_health_component(
            "qualia_core",
            self._mock_qualia_core_health_check,
            metric_definitions={
                'consciousness_level': {'threshold_warning': 0.6, 'threshold_critical': 0.4},
                'quantum_coherence': {'threshold_warning': 0.5, 'threshold_critical': 0.3},
                'decision_accuracy': {'threshold_warning': 0.7, 'threshold_critical': 0.5}
            }
        )
        
        self.monitoring_system.register_health_component(
            "trading_engine",
            self._mock_trading_engine_health_check,
            metric_definitions={
                'signal_processing_rate': {'threshold_warning': 0.8, 'threshold_critical': 0.6},
                'execution_latency': {'threshold_warning': 1.0, 'threshold_critical': 2.0},
                'risk_exposure': {'threshold_warning': 0.7, 'threshold_critical': 0.9}
            }
        )
        
        # Setup exchanges for failover demo
        if self.monitoring_system.failover_manager:
            await self.monitoring_system.failover_manager.setup_qualia_exchanges()
            await self.monitoring_system.failover_manager.configure_qualia_trading_pairs([
                'BTC/USD', 'ETH/USD', 'ADA/USD', 'SOL/USD'
            ])
        
        # Setup parameter optimization studies
        if self.monitoring_system.parameter_tuner:
            await self.monitoring_system.parameter_tuner.setup_qualia_optimization_studies()
        
        logger.info("✅ Demo components setup completed")

    async def _demo_health_monitoring(self) -> None:
        """Demonstrate health monitoring capabilities."""
        logger.info("💊 Starting health monitoring demo...")
        
        while self.demo_running:
            try:
                # Simulate varying health conditions
                consciousness_level = random.uniform(0.3, 0.9)
                quantum_coherence = random.uniform(0.2, 0.8)
                decision_accuracy = random.uniform(0.4, 0.95)
                
                # Update health metrics
                if self.monitoring_system.health_dashboard:
                    self.monitoring_system.health_dashboard.update_metric(
                        "qualia_core", "consciousness_level", consciousness_level, 0.6, 0.4
                    )
                    self.monitoring_system.health_dashboard.update_metric(
                        "qualia_core", "quantum_coherence", quantum_coherence, 0.5, 0.3
                    )
                    self.monitoring_system.health_dashboard.update_metric(
                        "qualia_core", "decision_accuracy", decision_accuracy, 0.7, 0.5
                    )
                
                await asyncio.sleep(15.0)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health monitoring demo error: {e}")
                await asyncio.sleep(15.0)

    async def _demo_performance_metrics(self) -> None:
        """Demonstrate performance metrics collection."""
        logger.info("📊 Starting performance metrics demo...")
        
        while self.demo_running:
            try:
                # Record QUALIA-specific metrics
                self.monitoring_system.record_performance_metric(
                    "qualia.trading.signals_generated", random.randint(10, 50), 
                    MetricType.COUNTER, "count"
                )
                
                self.monitoring_system.record_performance_metric(
                    "qualia.quantum.consciousness_level", random.uniform(0.4, 0.9),
                    MetricType.GAUGE, "level"
                )
                
                self.monitoring_system.record_performance_metric(
                    "qualia.decision.processing_time", random.uniform(0.1, 2.0),
                    MetricType.TIMER, "seconds"
                )
                
                self.monitoring_system.record_performance_metric(
                    "qualia.data.cache_hit_rate", random.uniform(0.6, 0.95),
                    MetricType.GAUGE, "percentage"
                )
                
                await asyncio.sleep(10.0)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Performance metrics demo error: {e}")
                await asyncio.sleep(10.0)

    async def _demo_signal_processing(self) -> None:
        """Demonstrate signal pipeline monitoring."""
        logger.info("🔄 Starting signal processing demo...")
        
        signal_counter = 0
        
        while self.demo_running:
            try:
                signal_counter += 1
                signal_id = f"demo_signal_{signal_counter}"
                
                # Start signal tracking
                tracking_id = self.monitoring_system.start_signal_tracking(
                    signal_id,
                    symbol=random.choice(['BTC/USD', 'ETH/USD', 'ADA/USD']),
                    signal_type=random.choice(['buy', 'sell', 'hold']),
                    initial_confidence=random.uniform(0.3, 0.9),
                    initial_strength=random.uniform(0.2, 0.8),
                    source_components=['quantum_engine', 'decision_oracle', 'market_analyzer']
                )
                
                # Simulate signal processing stages
                if self.monitoring_system.signal_monitor:
                    await asyncio.sleep(0.1)
                    self.monitoring_system.signal_monitor.update_signal_stage(
                        tracking_id, SignalStage.VALIDATION
                    )
                    
                    await asyncio.sleep(0.1)
                    self.monitoring_system.signal_monitor.update_signal_stage(
                        tracking_id, SignalStage.FILTERING
                    )
                    
                    # Simulate occasional rejections
                    if random.random() < 0.3:  # 30% rejection rate
                        self.monitoring_system.signal_monitor.reject_signal(
                            tracking_id,
                            random.choice(list(RejectionReason)),
                            "Demo rejection for testing"
                        )
                    
                    # Complete signal processing
                    self.monitoring_system.signal_monitor.complete_signal_processing(
                        tracking_id,
                        final_confidence=random.uniform(0.4, 0.95),
                        passed=random.random() > 0.2  # 80% pass rate
                    )
                
                await asyncio.sleep(5.0)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Signal processing demo error: {e}")
                await asyncio.sleep(5.0)

    async def _demo_bootstrap_tracking(self) -> None:
        """Demonstrate bootstrap progress tracking."""
        logger.info("🚀 Starting bootstrap tracking demo...")
        
        if not self.monitoring_system.bootstrap_tracker:
            return
        
        try:
            # Start bootstrap tracking
            self.monitoring_system.start_bootstrap_tracking()
            
            # Simulate bootstrap phases
            phases = list(BootstrapPhase)
            
            for i, phase in enumerate(phases[:-1]):  # Exclude COMPLETION
                if not self.demo_running:
                    break
                
                self.monitoring_system.bootstrap_tracker.update_phase(phase, 0.0)
                
                # Simulate phase progress
                for progress in [0.2, 0.4, 0.6, 0.8, 1.0]:
                    if not self.demo_running:
                        break
                    
                    self.monitoring_system.bootstrap_tracker.update_phase(phase, progress)
                    
                    # Record metrics
                    consciousness = min(0.9, 0.3 + (i * 0.1) + (progress * 0.1))
                    coherence = min(0.8, 0.2 + (i * 0.08) + (progress * 0.08))
                    
                    self.monitoring_system.bootstrap_tracker.record_metrics(
                        consciousness_level=consciousness,
                        quantum_coherence=coherence,
                        temporal_coherence=coherence * 0.9,
                        decision_quality=consciousness * 0.8
                    )
                    
                    await asyncio.sleep(3.0)
                
                # Increment cycle
                if not self.monitoring_system.bootstrap_tracker.increment_cycle():
                    break
            
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Bootstrap tracking demo error: {e}")

    async def _demo_failover_scenarios(self) -> None:
        """Demonstrate multi-exchange failover."""
        logger.info("🔄 Starting failover scenarios demo...")
        
        if not self.monitoring_system.failover_manager:
            return
        
        while self.demo_running:
            try:
                # Validate connectivity
                connectivity = await self.monitoring_system.failover_manager.validate_exchange_connectivity()
                logger.info(f"Exchange connectivity: {connectivity}")
                
                # Simulate trading operations with failover
                for _ in range(3):
                    if not self.demo_running:
                        break
                    
                    try:
                        result = await self.monitoring_system.execute_with_failover(
                            "fetch_status"
                        )
                        logger.debug(f"Failover operation successful: {result}")
                    except Exception as e:
                        logger.warning(f"Failover operation failed: {e}")
                    
                    await asyncio.sleep(10.0)
                
                # Run failover simulation
                simulation_results = await self.monitoring_system.failover_manager.simulate_failover_scenarios()
                logger.info(f"Failover simulation completed: {len(simulation_results['scenarios_tested'])} scenarios")
                
                await asyncio.sleep(60.0)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Failover demo error: {e}")
                await asyncio.sleep(60.0)

    async def _demo_parameter_optimization(self) -> None:
        """Demonstrate automated parameter optimization."""
        logger.info("🎯 Starting parameter optimization demo...")
        
        if not self.monitoring_system.parameter_tuner:
            return
        
        try:
            # Setup and start optimization
            objective_functions = await self.monitoring_system.parameter_tuner.create_qualia_objective_functions()
            
            # Run a few optimization trials for demo
            for study_name, objective_func in list(objective_functions.items())[:2]:  # Limit for demo
                if not self.demo_running:
                    break
                
                study_id = f"qualia_{study_name}_optimization"
                if study_id in self.monitoring_system.parameter_tuner.studies:
                    logger.info(f"Running optimization for {study_name}...")
                    
                    result = await self.monitoring_system.parameter_tuner.optimize_parameters(
                        study_id,
                        objective_func,
                        n_trials=5,  # Limited for demo
                        timeout=30.0
                    )
                    
                    if result:
                        logger.info(f"Optimization completed for {study_name}: {result.objective_value:.4f}")
                        await self.monitoring_system.parameter_tuner.apply_best_parameters(study_id)
                
                await asyncio.sleep(30.0)
                
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Parameter optimization demo error: {e}")

    async def _demo_system_integration(self) -> None:
        """Demonstrate cross-component integration."""
        logger.info("🔗 Starting system integration demo...")
        
        while self.demo_running:
            try:
                # Get comprehensive system status
                system_status = await self.monitoring_system.get_system_status()
                
                logger.info(f"System Health Score: {system_status['overall_health_score']:.3f}")
                logger.info(f"Active Components: {len(system_status['components'])}")
                logger.info(f"Recent Alerts: {len(system_status['recent_alerts'])}")
                
                # Log component-specific status
                for component_name, component_status in system_status['components'].items():
                    if isinstance(component_status, dict) and 'system_metrics' in component_status:
                        health_score = component_status['system_metrics'].get('overall_health_score', 0.0)
                        logger.debug(f"{component_name}: {health_score:.3f}")
                
                await asyncio.sleep(30.0)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"System integration demo error: {e}")
                await asyncio.sleep(30.0)

    async def _generate_demo_report(self) -> None:
        """Generate comprehensive demo report."""
        logger.info("📋 Generating demo report...")
        
        try:
            # Get final system status
            system_status = await self.monitoring_system.get_system_status()
            
            # Get component-specific reports
            reports = {}
            
            if self.monitoring_system.health_dashboard:
                reports['health'] = self.monitoring_system.health_dashboard.get_dashboard_status()
            
            if self.monitoring_system.performance_collector:
                reports['performance'] = self.monitoring_system.performance_collector.get_performance_summary()
            
            if self.monitoring_system.signal_monitor:
                reports['signals'] = self.monitoring_system.signal_monitor.get_pipeline_status()
            
            if self.monitoring_system.failover_manager:
                reports['failover'] = self.monitoring_system.failover_manager.get_failover_status()
            
            if self.monitoring_system.parameter_tuner:
                reports['optimization'] = await self.monitoring_system.parameter_tuner.get_qualia_optimization_status()
            
            # Generate summary
            logger.info("=" * 80)
            logger.info("🎉 QUALIA MONITORING SYSTEM DEMO REPORT")
            logger.info("=" * 80)
            logger.info(f"Overall Health Score: {system_status['overall_health_score']:.3f}")
            logger.info(f"Demo Duration: {system_status['uptime']:.1f} seconds")
            logger.info(f"Components Monitored: {len(system_status['components'])}")
            logger.info(f"Total Alerts Generated: {len(system_status['recent_alerts'])}")
            
            # Component summaries
            for component_name, report in reports.items():
                logger.info(f"\n{component_name.upper()} SUMMARY:")
                if component_name == 'health':
                    logger.info(f"  - Total Components: {report.get('system_metrics', {}).get('total_components', 0)}")
                    logger.info(f"  - Healthy Components: {report.get('system_metrics', {}).get('healthy_components', 0)}")
                elif component_name == 'performance':
                    logger.info(f"  - Performance Level: {report.get('current_performance', {}).get('performance_level', 'unknown')}")
                    logger.info(f"  - CPU Usage: {report.get('current_performance', {}).get('cpu_usage', 0):.1f}%")
                elif component_name == 'signals':
                    logger.info(f"  - Total Processed: {report.get('total_processed', 0)}")
                    logger.info(f"  - Pass Rate: {report.get('pass_rate', 0):.1%}")
                elif component_name == 'failover':
                    logger.info(f"  - Active Exchanges: {len(report.get('active_exchanges', []))}")
                    logger.info(f"  - Total Failovers: {report.get('statistics', {}).get('total_failovers', 0)}")
                elif component_name == 'optimization':
                    logger.info(f"  - Studies Active: {len(report.get('studies', {}))}")
            
            logger.info("=" * 80)
            logger.info("✅ Demo completed successfully!")
            
        except Exception as e:
            logger.error(f"Failed to generate demo report: {e}")

    async def _cleanup_demo(self) -> None:
        """Cleanup demo resources."""
        logger.info("🧹 Cleaning up demo...")
        
        self.demo_running = False
        
        # Cancel demo tasks
        for task in self.demo_tasks:
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        # Stop monitoring system
        await self.monitoring_system.stop()
        
        logger.info("✅ Demo cleanup completed")

    async def _mock_qualia_core_health_check(self) -> Dict[str, Any]:
        """Mock health check for QUALIA core."""
        return {
            'consciousness_level': {'value': random.uniform(0.3, 0.9)},
            'quantum_coherence': {'value': random.uniform(0.2, 0.8)},
            'decision_accuracy': {'value': random.uniform(0.4, 0.95)}
        }

    async def _mock_trading_engine_health_check(self) -> Dict[str, Any]:
        """Mock health check for trading engine."""
        return {
            'signal_processing_rate': {'value': random.uniform(0.5, 1.0)},
            'execution_latency': {'value': random.uniform(0.1, 3.0)},
            'risk_exposure': {'value': random.uniform(0.2, 0.9)}
        }


async def main():
    """Run the complete monitoring demo."""
    demo = QualiaMonitoringDemo()
    await demo.run_complete_demo()


if __name__ == "__main__":
    asyncio.run(main())
