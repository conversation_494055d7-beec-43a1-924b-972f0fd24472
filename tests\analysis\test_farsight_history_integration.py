import json
import importlib.util
import sys
from pathlib import Path

spec = importlib.util.spec_from_file_location(
    "qualia.farsight.analyzer_real",
    str(
        Path(__file__).resolve().parents[2]
        / "src"
        / "qualia"
        / "farsight"
        / "analyzer.py"
    ),
)
real_analyzer = importlib.util.module_from_spec(spec)
assert spec.loader is not None
spec.loader.exec_module(real_analyzer)
from qualia.analysis.farsight_engine import FarsightEngine
from qualia.persistence.farsight_history import FarsightHistory


def test_engine_appends_history(monkeypatch, tmp_path):
    clusters = [
        {
            "topic": "A",
            "curvature": 0.3,
            "velocity": 0.2,
            "prediction_window": "na",
            "sources": [],
        }
    ]

    def fake_fetch(self):
        self._papers = [object()]
        self._repos = []
        self._tweets = []
        self._items = [{"title": "t", "summary": "s", "link": "l"}]

    def fake_cluster(self):
        self._clusters = clusters

    # Ensure real Analyzer module is available despite stubs
    monkeypatch.setitem(sys.modules, "qualia.farsight.analyzer", real_analyzer)
    monkeypatch.setattr(
        "qualia.analysis.farsight_engine.Analyzer",
        real_analyzer.Analyzer,
        raising=False,
    )
    monkeypatch.setattr("qualia.analysis.farsight_engine.ENABLE_V2", True)
    monkeypatch.setattr(FarsightEngine, "_fetch_papers_v2", fake_fetch)
    monkeypatch.setattr(FarsightEngine, "_cluster_papers_v2", fake_cluster)

    path = tmp_path / "hist.jsonl"
    history = FarsightHistory(path=str(path))

    engine = FarsightEngine(history_store=history)
    engine.run()

    lines = path.read_text(encoding="utf-8").splitlines()
    assert len(lines) == 1
    rec = json.loads(lines[0])
    assert rec["topic"] == "A"
    assert "timestamp" in rec
