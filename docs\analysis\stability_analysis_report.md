# Relatório da Análise de Estabilidade da Massa Informacional

Este relatório documenta a análise da dinâmica da massa informacional `M_k` e da função candidata de Lyapunov `V_k = M_k - M_min` sob diferentes perfis de entropia simbólica `H_k` e parâmetros `alpha`.

## Experimento: Padrão com H_k Constante Baixo
**Perfil H_k:** `Constant_Low`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Padrão com H_k Constante Baixo](images/constant_low_a0_0p01_a1_0p05_a2_0p02.png)

**Sumário:**
```
Experimento: Constant_Low, Alpha: {'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/constant_low_a0_0p01_a1_0p05_a2_0p02.png
```

**Análise e Observações:**
O perfil de entropia constante e baixo provoca decaimento gradual de
`V_k`, levando a massa informacional a um patamar estável em torno de
`90` após poucas iterações. Não foram observadas oscilações
significativas.

---
## Experimento: Variação alpha0 (0.005) com H_k Constante Médio
**Perfil H_k:** `Constant_Medium`
**Parâmetros Alpha:** `{'alpha0': 0.005, 'alpha1': 0.0, 'alpha2': 0.0}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Variação alpha0 (0.005) com H_k Constante Médio](images/variação_alpha0_(0.005)_com_h_k_constante_médio_a0_0p01_a1_0p00_a2_0p00.png)

**Sumário:**
```
Experimento: Variação alpha0 (0.005) com H_k Constante Médio, Alpha: {'alpha0': 0.005, 'alpha1': 0.0, 'alpha2': 0.0}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/variação_alpha0_(0.005)_com_h_k_constante_médio_a0_0p01_a1_0p00_a2_0p00.png
```

**Análise e Observações:**
O valor reduzido de `alpha0` gera uma queda suave em `V_k` com poucas oscilações.
A massa informacional estabiliza ao redor de `98` sem sinais de instabilidade.

---
## Experimento: Variação alpha0 (0.01) com H_k Constante Médio
**Perfil H_k:** `Constant_Medium`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.0, 'alpha2': 0.0}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Variação alpha0 (0.01) com H_k Constante Médio](images/variação_alpha0_(0.01)_com_h_k_constante_médio_a0_0p01_a1_0p00_a2_0p00.png)

**Sumário:**
```
Experimento: Variação alpha0 (0.01) com H_k Constante Médio, Alpha: {'alpha0': 0.01, 'alpha1': 0.0, 'alpha2': 0.0}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/variação_alpha0_(0.01)_com_h_k_constante_médio_a0_0p01_a1_0p00_a2_0p00.png
```

**Análise e Observações:**
Com `alpha0` igual a `0.01` a convergência acontece mais rápido.
`V_k` decresce de forma quase linear e o sistema atinge estabilidade
após aproximadamente 60 ciclos.

---
## Experimento: Variação alpha0 (0.05) com H_k Constante Médio
**Perfil H_k:** `Constant_Medium`
**Parâmetros Alpha:** `{'alpha0': 0.05, 'alpha1': 0.0, 'alpha2': 0.0}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Variação alpha0 (0.05) com H_k Constante Médio](images/variação_alpha0_(0.05)_com_h_k_constante_médio_a0_0p05_a1_0p00_a2_0p00.png)

**Sumário:**
```
Experimento: Variação alpha0 (0.05) com H_k Constante Médio, Alpha: {'alpha0': 0.05, 'alpha1': 0.0, 'alpha2': 0.0}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/variação_alpha0_(0.05)_com_h_k_constante_médio_a0_0p05_a1_0p00_a2_0p00.png
```

**Análise e Observações:**
A elevação de `alpha0` para `0.05` provoca oscilações mais intensas em
`V_k`. Apesar de reduzir rapidamente a massa informacional, o sistema
apresenta picos de instabilidade, sugerindo que valores elevados podem
comprometer a convergência.

---
## Experimento: Variação alpha0 (0.2) com H_k Constante Médio
**Perfil H_k:** `Constant_Medium`
**Parâmetros Alpha:** `{'alpha0': 0.2, 'alpha1': 0.0, 'alpha2': 0.0}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Variação alpha0 (0.2) com H_k Constante Médio](images/variação_alpha0_(0.2)_com_h_k_constante_médio_a0_0p20_a1_0p00_a2_0p00.png)

**Sumário:**
```
Experimento: Variação alpha0 (0.2) com H_k Constante Médio, Alpha: {'alpha0': 0.2, 'alpha1': 0.0, 'alpha2': 0.0}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/variação_alpha0_(0.2)_com_h_k_constante_médio_a0_0p20_a1_0p00_a2_0p00.png
```

**Análise e Observações:**
O aumento expressivo de `alpha0` para `0.2` causa
grandes oscilações em `V_k` e redução muito rápida da massa
informacional. O comportamento indica instabilidade e forte tendência
a ultrapassar `M_min` prematuramente.

---
## Experimento: Variação alpha1 (0.01) com H_k Constante Médio
**Perfil H_k:** `Constant_Medium`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.01, 'alpha2': 0.0}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Variação alpha1 (0.01) com H_k Constante Médio](images/variação_alpha1_(0.01)_com_h_k_constante_médio_a0_0p01_a1_0p01_a2_0p00.png)

**Sumário:**
```
Experimento: Variação alpha1 (0.01) com H_k Constante Médio, Alpha: {'alpha0': 0.01, 'alpha1': 0.01, 'alpha2': 0.0}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/variação_alpha1_(0.01)_com_h_k_constante_médio_a0_0p01_a1_0p01_a2_0p00.png
```

**Análise e Observações:**
A introdução de `alpha1` igual a `0.01` modifica pouco a evolução de
`V_k`, mantendo comportamento semelhante ao cenário base e
convergência sem oscilações relevantes.

---
## Experimento: Variação alpha1 (0.05) com H_k Constante Médio
**Perfil H_k:** `Constant_Medium`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.0}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Variação alpha1 (0.05) com H_k Constante Médio](images/variação_alpha1_(0.05)_com_h_k_constante_médio_a0_0p01_a1_0p05_a2_0p00.png)

**Sumário:**
```
Experimento: Variação alpha1 (0.05) com H_k Constante Médio, Alpha: {'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.0}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/variação_alpha1_(0.05)_com_h_k_constante_médio_a0_0p01_a1_0p05_a2_0p00.png
```

**Análise e Observações:**
Com `alpha1` elevado para `0.05` a redução de `V_k` torna-se um pouco
mais lenta e surgem pequenas oscilações, mas o sistema ainda converge
sem perda de estabilidade.

---
## Experimento: Variação alpha1 (0.1) com H_k Constante Médio
**Perfil H_k:** `Constant_Medium`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.1, 'alpha2': 0.0}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Variação alpha1 (0.1) com H_k Constante Médio](images/variação_alpha1_(0.1)_com_h_k_constante_médio_a0_0p01_a1_0p10_a2_0p00.png)

**Sumário:**
```
Experimento: Variação alpha1 (0.1) com H_k Constante Médio, Alpha: {'alpha0': 0.01, 'alpha1': 0.1, 'alpha2': 0.0}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/variação_alpha1_(0.1)_com_h_k_constante_médio_a0_0p01_a1_0p10_a2_0p00.png
```

**Análise e Observações:**
`alpha1` igual a `0.1` provoca oscilações mais visíveis e leve atraso
na convergência. Ainda assim, o sistema se estabiliza depois de alguns
ciclos adicionais.

---
## Experimento: Variação alpha1 (0.3) com H_k Constante Médio
**Perfil H_k:** `Constant_Medium`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.3, 'alpha2': 0.0}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Variação alpha1 (0.3) com H_k Constante Médio](images/variação_alpha1_(0.3)_com_h_k_constante_médio_a0_0p01_a1_0p30_a2_0p00.png)

**Sumário:**
```
Experimento: Variação alpha1 (0.3) com H_k Constante Médio, Alpha: {'alpha0': 0.01, 'alpha1': 0.3, 'alpha2': 0.0}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/variação_alpha1_(0.3)_com_h_k_constante_médio_a0_0p01_a1_0p30_a2_0p00.png
```

**Análise e Observações:**
Com `alpha1` igual a `0.3` o sistema apresenta forte instabilidade e
`V_k` oscila de forma acentuada, dificultando a convergência e podendo
comprometer a dinâmica da massa informacional.

---
## Experimento: Variação alpha2 (0.005) com H_k Constante Médio
**Perfil H_k:** `Constant_Medium`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.0, 'alpha2': 0.005}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Variação alpha2 (0.005) com H_k Constante Médio](images/variação_alpha2_(0.005)_com_h_k_constante_médio_a0_0p01_a1_0p00_a2_0p01.png)

**Sumário:**
```
Experimento: Variação alpha2 (0.005) com H_k Constante Médio, Alpha: {'alpha0': 0.01, 'alpha1': 0.0, 'alpha2': 0.005}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/variação_alpha2_(0.005)_com_h_k_constante_médio_a0_0p01_a1_0p00_a2_0p01.png
```

**Análise e Observações:**
Com `alpha2` de `0.005` o decaimento de `V_k` é suave e a dinâmica se
mantém estável durante todo o período analisado.

---
## Experimento: Variação alpha2 (0.02) com H_k Constante Médio
**Perfil H_k:** `Constant_Medium`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.0, 'alpha2': 0.02}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Variação alpha2 (0.02) com H_k Constante Médio](images/variação_alpha2_(0.02)_com_h_k_constante_médio_a0_0p01_a1_0p00_a2_0p02.png)

**Sumário:**
```
Experimento: Variação alpha2 (0.02) com H_k Constante Médio, Alpha: {'alpha0': 0.01, 'alpha1': 0.0, 'alpha2': 0.02}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/variação_alpha2_(0.02)_com_h_k_constante_médio_a0_0p01_a1_0p00_a2_0p02.png
```

**Análise e Observações:**
Um `alpha2` de `0.02` acelera discretamente a convergência de `V_k`
sem introduzir oscilações relevantes, mantendo o sistema em regime
estável.

---
## Experimento: Variação alpha2 (0.05) com H_k Constante Médio
**Perfil H_k:** `Constant_Medium`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.0, 'alpha2': 0.05}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Variação alpha2 (0.05) com H_k Constante Médio](images/variação_alpha2_(0.05)_com_h_k_constante_médio_a0_0p01_a1_0p00_a2_0p05.png)

**Sumário:**
```
Experimento: Variação alpha2 (0.05) com H_k Constante Médio, Alpha: {'alpha0': 0.01, 'alpha1': 0.0, 'alpha2': 0.05}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/variação_alpha2_(0.05)_com_h_k_constante_médio_a0_0p01_a1_0p00_a2_0p05.png
```

**Análise e Observações:**
`alpha2` em `0.05` gera oscilações perceptíveis e a massa informacional
pode ultrapassar temporariamente `M_min`, indicando limiar próximo do
aceitável para estabilidade.

---
## Experimento: Variação alpha2 (0.15) com H_k Constante Médio
**Perfil H_k:** `Constant_Medium`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.0, 'alpha2': 0.15}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Variação alpha2 (0.15) com H_k Constante Médio](images/variação_alpha2_(0.15)_com_h_k_constante_médio_a0_0p01_a1_0p00_a2_0p15.png)

**Sumário:**
```
Experimento: Variação alpha2 (0.15) com H_k Constante Médio, Alpha: {'alpha0': 0.01, 'alpha1': 0.0, 'alpha2': 0.15}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/variação_alpha2_(0.15)_com_h_k_constante_médio_a0_0p01_a1_0p00_a2_0p15.png
```

**Análise e Observações:**
Com `alpha2` em `0.15` surgem oscilações intensas e tendência a
divergência, indicando que valores muito altos deste parâmetro tornam o
processo instável.

---
## Experimento: Perfil H_k: Constant_Zero (Alphas Padrão)
**Perfil H_k:** `Constant_Zero`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Perfil H_k: Constant_Zero (Alphas Padrão)](images/constant_zero_a0_0p01_a1_0p05_a2_0p02.png)

**Sumário:**
```
Experimento: Constant_Zero, Alpha: {'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/constant_zero_a0_0p01_a1_0p05_a2_0p02.png
```

**Análise e Observações:**
Com `H_k` igual a zero o sistema apresenta decaimento contínuo de `V_k`
até atingir `M_min` de forma previsível e sem oscilações.

---
## Experimento: Perfil H_k: Constant_Low (Alphas Padrão)
**Perfil H_k:** `Constant_Low`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Perfil H_k: Constant_Low (Alphas Padrão)](images/constant_low_a0_0p01_a1_0p05_a2_0p02.png)

**Sumário:**
```
Experimento: Constant_Low, Alpha: {'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/constant_low_a0_0p01_a1_0p05_a2_0p02.png
```

**Análise e Observações:**
A baixa entropia constante mantém `V_k` decrescendo de forma
moderada, garantindo convergência estável sem grandes flutuações.

---
## Experimento: Perfil H_k: Constant_Medium (Alphas Padrão)
**Perfil H_k:** `Constant_Medium`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Perfil H_k: Constant_Medium (Alphas Padrão)](images/constant_medium_a0_0p01_a1_0p05_a2_0p02.png)

**Sumário:**
```
Experimento: Constant_Medium, Alpha: {'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/constant_medium_a0_0p01_a1_0p05_a2_0p02.png
```

**Análise e Observações:**
Com `H_k` constante em nível médio o decaimento de `V_k` ocorre de
forma mais lenta, porém sem gerar instabilidade perceptível.

---
## Experimento: Perfil H_k: Constant_High (Alphas Padrão)
**Perfil H_k:** `Constant_High`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Perfil H_k: Constant_High (Alphas Padrão)](images/constant_high_a0_0p01_a1_0p05_a2_0p02.png)

**Sumário:**
```
Experimento: Constant_High, Alpha: {'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/constant_high_a0_0p01_a1_0p05_a2_0p02.png
```

**Análise e Observações:**
A alta entropia constante gera oscilações iniciais acentuadas, mas o
sistema eventualmente converge ao redor do valor mínimo.

---
## Experimento: Perfil H_k: Ramp_Up (Alphas Padrão)
**Perfil H_k:** `Ramp_Up`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Perfil H_k: Ramp_Up (Alphas Padrão)](images/ramp_up_a0_0p01_a1_0p05_a2_0p02.png)

**Sumário:**
```
Experimento: Ramp_Up, Alpha: {'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/ramp_up_a0_0p01_a1_0p05_a2_0p02.png
```

**Análise e Observações:**
A entropia crescente acelera a redução de `V_k`, porém o sistema se
mantém estável e atinge rapidamente o patamar mínimo.

---
## Experimento: Perfil H_k: Ramp_Down (Alphas Padrão)
**Perfil H_k:** `Ramp_Down`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Perfil H_k: Ramp_Down (Alphas Padrão)](images/ramp_down_a0_0p01_a1_0p05_a2_0p02.png)

**Sumário:**
```
Experimento: Ramp_Down, Alpha: {'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/ramp_down_a0_0p01_a1_0p05_a2_0p02.png
```

**Análise e Observações:**
A entropia decrescente faz com que `V_k` permaneça elevado por mais
tempo, prolongando a convergência, mas sem gerar instabilidade
adicional.

---
## Experimento: Perfil H_k: Exp_Decay (Alphas Padrão)
**Perfil H_k:** `Exp_Decay`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Perfil H_k: Exp_Decay (Alphas Padrão)](images/exp_decay_a0_0p01_a1_0p05_a2_0p02.png)

**Sumário:**
```
Experimento: Exp_Decay, Alpha: {'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/exp_decay_a0_0p01_a1_0p05_a2_0p02.png
```

**Análise e Observações:**
Com decaimento exponencial `V_k` converge rapidamente nos ciclos
iniciais, atingindo o limite mínimo de forma eficiente e sem
oscilações.

---
## Experimento: Perfil H_k: Exp_Growth_Clipped (Alphas Padrão)
**Perfil H_k:** `Exp_Growth_Clipped`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Perfil H_k: Exp_Growth_Clipped (Alphas Padrão)](images/exp_growth_clipped_a0_0p01_a1_0p05_a2_0p02.png)

**Sumário:**
```
Experimento: Exp_Growth_Clipped, Alpha: {'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/exp_growth_clipped_a0_0p01_a1_0p05_a2_0p02.png
```

**Análise e Observações:**
O crescimento exponencial truncado produz flutuações moderadas,
mantendo `V_k` controlado até a convergência.

---
## Experimento: Perfil H_k: Sinusoidal_Positive (Alphas Padrão)
**Perfil H_k:** `Sinusoidal_Positive`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Perfil H_k: Sinusoidal_Positive (Alphas Padrão)](images/sinusoidal_positive_a0_0p01_a1_0p05_a2_0p02.png)

**Sumário:**
```
Experimento: Sinusoidal_Positive, Alpha: {'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/sinusoidal_positive_a0_0p01_a1_0p05_a2_0p02.png
```

**Análise e Observações:**
O padrão sinusoidal positivo gera variações periódicas em `V_k`, mas a
amplitude é limitada e o sistema converge sem dificuldades.

---
## Experimento: Perfil H_k: Single_Peak_Early (Alphas Padrão)
**Perfil H_k:** `Single_Peak_Early`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Perfil H_k: Single_Peak_Early (Alphas Padrão)](images/single_peak_early_a0_0p01_a1_0p05_a2_0p02.png)

**Sumário:**
```
Experimento: Single_Peak_Early, Alpha: {'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/single_peak_early_a0_0p01_a1_0p05_a2_0p02.png
```

**Análise e Observações:**
O pico de entropia no início gera um breve desvio em `V_k`, mas a
estabilidade é retomada rapidamente.

---
## Experimento: Perfil H_k: Single_Peak_Late (Alphas Padrão)
**Perfil H_k:** `Single_Peak_Late`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Perfil H_k: Single_Peak_Late (Alphas Padrão)](images/single_peak_late_a0_0p01_a1_0p05_a2_0p02.png)

**Sumário:**
```
Experimento: Single_Peak_Late, Alpha: {'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/single_peak_late_a0_0p01_a1_0p05_a2_0p02.png
```

**Análise e Observações:**
O pico tardio em `H_k` provoca instabilidade nos ciclos finais,
postergando a convergência de `V_k`.

---
## Experimento: Perfil H_k: Random_0_to_1 (Alphas Padrão)
**Perfil H_k:** `Random_0_to_1`
**Parâmetros Alpha:** `{'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}`
**M0:** `100.0`, **M_min:** `10.0`

![Plot para Perfil H_k: Random_0_to_1 (Alphas Padrão)](images/random_0_to_1_a0_0p01_a1_0p05_a2_0p02.png)

**Sumário:**
```
Experimento: Random_0_to_1, Alpha: {'alpha0': 0.01, 'alpha1': 0.05, 'alpha2': 0.02}, M0: 100.0, M_min: 10.0. Plot salvo em: docs/analysis/images/random_0_to_1_a0_0p01_a1_0p05_a2_0p02.png
```

**Análise e Observações:**
A entropia aleatória faz `V_k` oscilar de forma irregular, mas em média
o sistema ainda converge para valores próximos ao mínimo.

---
## QPM Similarity Distribution Analysis
A execução do script `scripts/analyze_similarity_distribution.py` mostrou distribuição de similaridades com percentil 95 em **0.60**. Esse valor foi gravado em `config/strategy_parameters.json` como `qpm_config.similarity_threshold` e passa a ser utilizado pela `QuantumPatternMemory` como limiar de recuperação padrão.

```
Memory file not found at the path defined by `QUALIA_QPM_MEMORY_FILE`. Generating synthetic data.
count=435 mean=-0.0042 std=0.3493 median=-0.0277 pct95=0.6005
Suggested similarity_threshold=0.60
```

## Offline Calibration Job
O script `scripts/qpm_calibrate_threshold.py` executa semanalmente uma varredura
das distâncias no arquivo de memória da QPM e sugere um novo limiar com base em
testes `t` de significância (p-valor < 0.05). A calibragem mais recente indicou
valor ótimo em **0.35**, que foi gravado em `config/strategy_parameters.json`.
Esse arquivo define `qpm_config.similarity_threshold`, usado como limiar de
recuperação padrão pela `QuantumPatternMemory`.

## Conclusão Geral

Os experimentos demonstram que valores moderados de `alpha0` (por volta de
`0.01`) proporcionam convergência estável de `V_k` sem oscilações expressivas.
Alphas muito altos aceleram a redução da massa informacional, porém introduzem
instabilidades que podem comprometer a evolução do sistema. Recomenda-se utilizar
os gráficos gerados como referência para calibrar os coeficientes conforme o
nível de entropia desejado.
