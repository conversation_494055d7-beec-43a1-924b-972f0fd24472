# Métricas da QASTEvolutionaryStrategy

Quando inicializada com `metrics_client`, a `QASTEvolutionaryStrategy` emite métricas StatsD a cada ciclo de evolução.

## Métricas

- `qast.generation_time_ms` – tempo gasto em cada geração, em milissegundos.
- `qast.num_crossovers` – quantidade de descendentes criados via crossover por geração.
- `qast.best_fitness` – fitness da melhor estratégia da geração atual.

Todas as métricas aceitam a tag opcional `trace_id` para correlação com outros módulos.

```python
from datadog import DogStatsd

statsd = DogStatsd()
qast = QASTEvolutionaryStrategy(template, consciousness, metrics_client=statsd)
qast.evolve_population(data, trace_id="abc123")
```
