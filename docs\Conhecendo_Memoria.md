# Conhecendo o Sistema de Memória do QUALIA

Para que o QUALIA aprenda e evolua, ele precisa reter experiências passadas. A camada de memória reúne diferentes formas de armazenamento e oferece interfaces simples para consultá-las durante a análise de mercado.

## Como a memória se organiza?

- **ShortTermMemory**: guarda eventos recentes e é limpo com frequência, como se fosse um bloco de notas rápido.
- **QuantumPatternMemory**: armazena padrões de mercado extraídos, permitindo buscar ocorrências semelhantes no futuro.
- **ExperienceReplay**: registra pares estado-ação para treinar estratégias em cenários variados.
- **HolographicMemory**: componente integrado ao `MemorySystem` que guarda vetores de padrões com decaimento temporal, usado para consultas por similaridade.

Esses componentes podem operar com segurança para threads e possuem métodos para gravação, leitura e amostragem. Mais detalhes estão em [docs/api/memory.md](api/memory.md).

## Personalização

- Ajuste o `similarity_threshold` do `QuantumPatternMemory` para controlar o quão parecidos os padrões devem ser para retornar uma correspondência.
- Defina o tamanho máximo do `ExperienceReplay` conforme a quantidade de dados disponível e a memória da máquina.
- Ative o parâmetro `thread_safe` caso o QUALIA execute múltiplas tarefas simultaneamente.

### Controle de expiração de padrões

O parâmetro `pattern_ttl_seconds` define por quanto tempo cada padrão fica
disponível na `QuantumPatternMemory`. Ao inicializar o componente, passe o
valor desejado em segundos:

```python
qpm = QuantumPatternMemory(pattern_ttl_seconds=86_400)  # um dia de TTL
```

Durante chamadas de armazenamento ou consulta, entradas com *timestamp* mais
antigo que o limite são descartadas automaticamente. Para manter o banco
enxuto, execute periodicamente o script
[`scripts/qpm_purge_expired.py`](../scripts/qpm_purge_expired.py), que carrega a
memória, remove os padrões vencidos e grava o arquivo atualizado.

Além disso, recomenda-se calibrar semanalmente o limiar de similaridade com o
script [`scripts/qpm_calibrate_threshold.py`](../scripts/qpm_calibrate_threshold.py),
evitando vieses conforme a memória cresce.

### Portabilidade do caminho `qpm_memory_file`

O arquivo que armazena os padrões do `QuantumPatternMemory` é salvo por padrão em
`data/cache/qpm_memory.json`. O caminho é resolvido com `pathlib.Path` e
`PureWindowsPath` em [`src/qualia/config/settings.py`](../src/qualia/config/settings.py),
garantindo funcionamento tanto no Windows quanto no Unix. Para alterar a
localização, defina a variável de ambiente `QUALIA_QPM_MEMORY_FILE` ou ajuste
`QUALIA_CACHE_DIR` antes de iniciar o sistema.

### Gravação contínua e inspeção de padrões

O `MemoryService` e o `MemorySystem` inicializam o `QuantumPatternMemory` com
`auto_persist=True`, salvando automaticamente o arquivo na pasta definida por
`QUALIA_CACHE_DIR` sempre que um novo padrão é registrado. Para verificar os
registros atuais utilize o script [`scripts/qpm_inspect.py`](../scripts/qpm_inspect.py):

```bash
python scripts/qpm_inspect.py --path /caminho/para/qpm_memory.json
```

O script lista cada partição de dimensão e exibe o `id`, *timestamp* e cenário
associado a cada padrão, facilitando auditoria e depuração.

Agora o método `retrieve_similar_patterns` considera o nível de foco retornado
por `qualia.metacognition.service.get_focus` para priorizar resultados mais
relevantes conforme o estado metacognitivo do sistema.

## Próximos Passos

- **Persistência em disco** para manter padrões relevantes entre execuções.
- **Ferramentas de visualização** para examinar o conteúdo de cada memória.
- **Integração profunda** com o módulo de metacognição, reforçando quais experiências trazem mais benefício.
