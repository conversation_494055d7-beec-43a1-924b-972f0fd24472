#!/usr/bin/env python3
"""
Teste do Sistema Bayesian Optimizer - Etapa D
YAA IMPLEMENTATION: Testa todos os componentes do sistema de otimização.
"""

import sys
import asyncio
import time
import json
import logging
from datetime import datetime
from pathlib import Path

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from src.bayesian_optimizer import BayesianOptimizer, OptimizationConfig
    from src.parameter_tuner import ParameterTunerWorker
    from src.grpc_service import QualiaOptimizerService
    IMPORTS_OK = True
except ImportError as e:
    print(f"⚠️ Erro de importação: {e}")
    IMPORTS_OK = False

def test_optimization_config():
    """Testa configuração de otimização."""
    
    print("🧪 Testando OptimizationConfig...")
    
    try:
        config = OptimizationConfig(
            study_name="test_study",
            n_trials_per_cycle=5,
            optimization_cycles=2,
            symbols=["BTCUSDT"]
        )
        
        assert config.study_name == "test_study"
        assert config.n_trials_per_cycle == 5
        assert config.optimization_cycles == 2
        assert "BTCUSDT" in config.symbols
        assert config.base_params is not None
        
        print("   ✅ OptimizationConfig OK")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro em OptimizationConfig: {e}")
        return False

def test_data_provider():
    """Testa provedor de dados."""
    
    print("🧪 Testando QualiaDataProvider...")
    
    try:
        if not IMPORTS_OK:
            print("   ⚠️ Imports não disponíveis, pulando teste")
            return True
        
        from src.bayesian_optimizer import QualiaDataProvider
        
        provider = QualiaDataProvider()
        
        # Testa busca de dados
        df = provider.fetch_market_data("BTCUSDT", hours=24)
        
        if df.empty:
            print("   ⚠️ Dados não obtidos (pode ser problema de rede)")
            return True
        
        # Verifica colunas esperadas
        expected_cols = ['open', 'high', 'low', 'close', 'volume', 'rsi', 'macd']
        for col in expected_cols:
            if col not in df.columns:
                print(f"   ❌ Coluna {col} não encontrada")
                return False
        
        print(f"   ✅ Dados obtidos: {len(df)} candles")
        print(f"   ✅ Indicadores adicionados: {list(df.columns)}")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro em QualiaDataProvider: {e}")
        return False

def test_strategy():
    """Testa estratégia QUALIA."""
    
    print("🧪 Testando QualiaStrategy...")
    
    try:
        if not IMPORTS_OK:
            print("   ⚠️ Imports não disponíveis, pulando teste")
            return True
        
        from src.bayesian_optimizer import QualiaStrategy
        import pandas as pd
        import numpy as np
        
        # Parâmetros vencedores
        params = {
            'price_amplification': 2.0,
            'news_amplification': 7.0,
            'min_confidence': 0.4
        }
        
        strategy = QualiaStrategy(params)
        
        # Dados simulados para teste
        test_data = pd.Series({
            'rsi': 30.0,
            'macd': 0.01,
            'macd_signal': 0.005,
            'macd_histogram': 0.005,
            'bb_position': 0.2,
            'ema_8': 100.0,
            'ema_21': 99.0,
            'momentum': 0.02,
            'volatility': 0.01,
            'volume_ratio': 1.5
        })
        
        # Gera sinal
        signal = strategy.generate_signal(test_data, "BTCUSDT")
        
        # Verifica estrutura do sinal
        expected_keys = ['position', 'confidence', 'rsi_signal', 'macd_signal', 'bb_signal', 'trend_signal']
        for key in expected_keys:
            if key not in signal:
                print(f"   ❌ Chave {key} não encontrada no sinal")
                return False
        
        # Verifica valores
        assert -1.0 <= signal['position'] <= 1.0, "Position fora do range"
        assert 0.0 <= signal['confidence'] <= 1.0, "Confidence fora do range"
        
        print(f"   ✅ Sinal gerado: {signal}")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro em QualiaStrategy: {e}")
        return False

def test_bayesian_optimizer():
    """Testa otimizador Bayesiano."""
    
    print("🧪 Testando BayesianOptimizer...")
    
    try:
        if not IMPORTS_OK:
            print("   ⚠️ Imports não disponíveis, pulando teste")
            return True
        
        # Configuração de teste
        config = OptimizationConfig(
            study_name="test_bayesian",
            n_trials_per_cycle=3,
            optimization_cycles=1,
            symbols=["BTCUSDT"]
        )
        
        optimizer = BayesianOptimizer(config)
        
        # Inicializa estudo
        optimizer.initialize_study()
        
        assert optimizer.study is not None, "Study não inicializado"
        assert optimizer.best_params is not None, "Best params não inicializado"
        
        print("   ✅ BayesianOptimizer inicializado")
        
        # Testa função objetivo (simulada)
        try:
            import optuna
            
            # Cria trial simulado
            study = optuna.create_study(direction='maximize')
            trial = study.ask()
            
            # Testa função objetivo
            result = optimizer.objective(trial)
            
            print(f"   ✅ Função objetivo testada: {result}")
            
        except Exception as e:
            print(f"   ⚠️ Erro na função objetivo: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erro em BayesianOptimizer: {e}")
        return False

def test_parameter_tuner():
    """Testa worker de tuning."""
    
    print("🧪 Testando ParameterTunerWorker...")
    
    try:
        if not IMPORTS_OK:
            print("   ⚠️ Imports não disponíveis, pulando teste")
            return True
        
        # Cria configuração de teste
        config_file = "config/test_tuner_config.json"
        Path("config").mkdir(exist_ok=True)
        
        test_config = {
            "study_name": "test_tuner",
            "n_trials_per_cycle": 2,
            "optimization_cycles": 1,
            "symbols": ["BTCUSDT"],
            "worker_settings": {
                "cycle_interval_minutes": 1,
                "max_runtime_hours": 1
            }
        }
        
        with open(config_file, 'w') as f:
            json.dump(test_config, f, indent=2)
        
        # Inicializa worker
        worker = ParameterTunerWorker(config_file)
        
        assert worker.config_file == config_file, "Config file não definido"
        
        # Testa carregamento de configuração
        config = worker.load_config()
        assert config.study_name == "test_tuner", "Configuração não carregada"
        
        # Testa status
        status = worker.get_status()
        assert 'is_running' in status, "Status não contém is_running"
        assert 'stats' in status, "Status não contém stats"
        
        print("   ✅ ParameterTunerWorker OK")
        
        # Limpa arquivo de teste
        Path(config_file).unlink(missing_ok=True)
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erro em ParameterTunerWorker: {e}")
        return False

def test_grpc_service():
    """Testa serviço gRPC."""
    
    print("🧪 Testando QualiaOptimizerService...")
    
    try:
        if not IMPORTS_OK:
            print("   ⚠️ Imports não disponíveis, pulando teste")
            return True
        
        service = QualiaOptimizerService()
        
        assert hasattr(service, 'worker'), "Service não tem worker"
        assert hasattr(service, 'is_initialized'), "Service não tem is_initialized"
        
        print("   ✅ QualiaOptimizerService inicializado")
        
        # Testa métodos básicos
        try:
            # Simula request vazio
            class MockRequest:
                pass
            
            class MockContext:
                pass
            
            request = MockRequest()
            context = MockContext()
            
            # Testa GetCurrentParams
            response = asyncio.run(service.GetCurrentParams(request, context))
            assert 'success' in response, "Response não contém success"
            assert 'message' in response, "Response não contém message"
            
            print("   ✅ GetCurrentParams OK")
            
        except Exception as e:
            print(f"   ⚠️ Erro nos métodos gRPC: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erro em QualiaOptimizerService: {e}")
        return False

def test_integration():
    """Teste de integração completo."""
    
    print("🧪 Testando Integração Completa...")
    
    try:
        if not IMPORTS_OK:
            print("   ⚠️ Imports não disponíveis, pulando teste")
            return True
        
        # 1. Cria configuração
        config = OptimizationConfig(
            study_name="integration_test",
            n_trials_per_cycle=2,
            optimization_cycles=1,
            symbols=["BTCUSDT"]
        )
        
        # 2. Inicializa otimizador
        optimizer = BayesianOptimizer(config)
        optimizer.initialize_study()
        
        # 3. Testa um ciclo rápido
        print("   🔄 Executando ciclo de teste...")
        
        # Simula ciclo (sem executar realmente para economizar tempo)
        cycle_result = {
            'cycle': 1,
            'best_params': config.base_params,
            'best_pnl_24h': 1.5,
            'n_trials': 2,
            'cycle_time_seconds': 10.0,
            'timestamp': datetime.now().isoformat()
        }
        
        # 4. Salva resultados
        optimizer.optimization_history.append(cycle_result)
        optimizer.save_results()
        
        # 5. Verifica arquivos salvos
        results_dir = Path("results/bayesian_optimization")
        result_files = list(results_dir.glob("optimization_results_*.json"))
        
        if result_files:
            print(f"   ✅ Resultados salvos: {len(result_files)} arquivos")
        else:
            print("   ⚠️ Nenhum arquivo de resultado encontrado")
        
        print("   ✅ Integração completa OK")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro na integração: {e}")
        return False

def main():
    """Executa todos os testes."""
    
    print("🧪 TESTE COMPLETO DO SISTEMA BAYESIAN OPTIMIZER")
    print("=" * 60)
    
    tests = [
        ("Configuração", test_optimization_config),
        ("Provedor de Dados", test_data_provider),
        ("Estratégia", test_strategy),
        ("Otimizador Bayesiano", test_bayesian_optimizer),
        ("Parameter Tuner", test_parameter_tuner),
        ("Serviço gRPC", test_grpc_service),
        ("Integração", test_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        start_time = time.time()
        success = test_func()
        test_time = time.time() - start_time
        
        results.append((test_name, success, test_time))
        
        if success:
            print(f"✅ {test_name}: PASSOU ({test_time:.2f}s)")
        else:
            print(f"❌ {test_name}: FALHOU ({test_time:.2f}s)")
    
    # Resumo final
    print("\n" + "=" * 60)
    print("📊 RESUMO DOS TESTES")
    print("=" * 60)
    
    passed = sum(1 for _, success, _ in results if success)
    total = len(results)
    total_time = sum(test_time for _, _, test_time in results)
    
    for test_name, success, test_time in results:
        status = "✅ PASSOU" if success else "❌ FALHOU"
        print(f"   {test_name:<25} {status} ({test_time:.2f}s)")
    
    print(f"\n🎯 RESULTADO FINAL: {passed}/{total} testes passaram")
    print(f"⏱️ Tempo total: {total_time:.2f}s")
    
    if passed == total:
        print("🎉 TODOS OS TESTES PASSARAM!")
        print("✅ Sistema Bayesian Optimizer está funcional!")
    else:
        print("⚠️ Alguns testes falharam - verificar logs")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
