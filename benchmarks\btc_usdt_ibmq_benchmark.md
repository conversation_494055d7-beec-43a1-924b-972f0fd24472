# Benchmark BTC/USDT com IBM Q

Este benchmark usa o notebook `btc_usdt_ibmq_benchmark.ipynb` para executar um circuito de 5 qubits no backend `ibm_iken` (127 qubits). O objetivo é medir latência real da execução, diversidade de resultados, entropia e um PnL simulado. Os valores resultantes são gravados em `config/btc_usdt_ibmq_metrics.json`.

```json
{
  "latency_ms": 0.0,
  "diversity": 0.0,
  "entropy": 0.0,
  "pnl_sim": 0.0,
  "credit_cost": null
}
```

Substitua os valores após executar o notebook com sua própria conta IBM Quantum.
