"""
Asynchronous Initialization System for QUALIA
Provides optimized parallel initialization with dependency injection and circuit breaker patterns.
"""

import asyncio
import time
import threading
from typing import Dict, Any, List, Optional, Callable, Set, Union, Type
from dataclasses import dataclass, field
from enum import Enum
import inspect
from pathlib import Path

from src.qualia.utils.logging_integration import get_component_logger

logger = get_component_logger("async_initialization_system")


class ComponentState(Enum):
    """Component initialization states"""
    PENDING = "pending"
    INITIALIZING = "initializing"
    INITIALIZED = "initialized"
    FAILED = "failed"
    SKIPPED = "skipped"


class InitializationStrategy(Enum):
    """Initialization strategies"""
    PARALLEL = "parallel"
    SEQUENTIAL = "sequential"
    LAZY = "lazy"
    CRITICAL = "critical"


@dataclass
class ComponentDefinition:
    """Definition of a component for initialization"""
    name: str
    factory: Union[Callable, Type]
    dependencies: List[str] = field(default_factory=list)
    strategy: InitializationStrategy = InitializationStrategy.PARALLEL
    timeout: float = 30.0
    retry_count: int = 3
    retry_delay: float = 1.0
    required: bool = True
    config_key: Optional[str] = None
    init_args: Dict[str, Any] = field(default_factory=dict)
    init_kwargs: Dict[str, Any] = field(default_factory=dict)
    health_check: Optional[Callable] = None
    cleanup: Optional[Callable] = None


@dataclass
class InitializationResult:
    """Result of component initialization"""
    component_name: str
    state: ComponentState
    instance: Optional[Any] = None
    error: Optional[Exception] = None
    initialization_time: float = 0.0
    retry_count: int = 0
    dependencies_resolved: List[str] = field(default_factory=list)


@dataclass
class InitializationReport:
    """Complete initialization report"""
    total_components: int
    successful: int
    failed: int
    skipped: int
    total_time: float
    results: Dict[str, InitializationResult] = field(default_factory=dict)
    dependency_graph: Dict[str, List[str]] = field(default_factory=dict)
    initialization_order: List[str] = field(default_factory=list)
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        if self.total_components == 0:
            return 0.0
        return self.successful / self.total_components
    
    @property
    def critical_failures(self) -> List[str]:
        """Get list of critical component failures"""
        return [
            name for name, result in self.results.items()
            if result.state == ComponentState.FAILED and 
            name in self.dependency_graph and
            len(self.dependency_graph[name]) > 0  # Has dependents
        ]


class CircuitBreaker:
    """Circuit breaker for component initialization"""
    
    def __init__(self, failure_threshold: int = 3, recovery_timeout: float = 60.0):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = 0.0
        self.state = "closed"  # closed, open, half-open
        self._lock = threading.Lock()
    
    def can_execute(self) -> bool:
        """Check if execution is allowed"""
        with self._lock:
            if self.state == "closed":
                return True
            elif self.state == "open":
                if time.time() - self.last_failure_time > self.recovery_timeout:
                    self.state = "half-open"
                    return True
                return False
            else:  # half-open
                return True
    
    def record_success(self):
        """Record successful execution"""
        with self._lock:
            self.failure_count = 0
            self.state = "closed"
    
    def record_failure(self):
        """Record failed execution"""
        with self._lock:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = "open"


class DependencyInjectionContainer:
    """Dependency injection container for components"""
    
    def __init__(self):
        self.instances: Dict[str, Any] = {}
        self.definitions: Dict[str, ComponentDefinition] = {}
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self._lock = threading.RLock()
    
    def register(self, definition: ComponentDefinition):
        """Register a component definition"""
        with self._lock:
            self.definitions[definition.name] = definition
            self.circuit_breakers[definition.name] = CircuitBreaker()
            logger.debug(f"📋 Registered component: {definition.name}")
    
    def get_instance(self, name: str) -> Optional[Any]:
        """Get component instance"""
        with self._lock:
            return self.instances.get(name)
    
    def set_instance(self, name: str, instance: Any):
        """Set component instance"""
        with self._lock:
            self.instances[name] = instance
    
    def resolve_dependencies(self, component_name: str) -> Dict[str, Any]:
        """Resolve dependencies for a component"""
        definition = self.definitions.get(component_name)
        if not definition:
            return {}
        
        resolved = {}
        for dep_name in definition.dependencies:
            dep_instance = self.get_instance(dep_name)
            if dep_instance is not None:
                resolved[dep_name] = dep_instance
        
        return resolved
    
    def get_circuit_breaker(self, name: str) -> Optional[CircuitBreaker]:
        """Get circuit breaker for component"""
        return self.circuit_breakers.get(name)


class AsyncInitializationSystem:
    """
    Asynchronous initialization system with dependency injection and parallel execution.
    
    Features:
    - Parallel initialization of independent components
    - Dependency resolution and ordering
    - Circuit breaker pattern for fault tolerance
    - Retry mechanisms with exponential backoff
    - Health checks and validation
    - Comprehensive reporting and monitoring
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.container = DependencyInjectionContainer()
        self.initialization_tasks: Dict[str, asyncio.Task] = {}
        self.initialization_results: Dict[str, InitializationResult] = {}
        
        # Configuration
        self.max_parallel_initializations = self.config.get('max_parallel_initializations', 5)
        self.global_timeout = self.config.get('global_timeout', 300.0)  # 5 minutes
        self.enable_health_checks = self.config.get('enable_health_checks', True)
        
        # State tracking
        self.is_initializing = False
        self.initialization_start_time = 0.0
        
        logger.info("🏗️ Async Initialization System created")
    
    def register_component(self, definition: ComponentDefinition):
        """Register a component for initialization"""
        self.container.register(definition)
        logger.info(f"📦 Component registered: {definition.name} (strategy: {definition.strategy.value})")
    
    def register_components(self, definitions: List[ComponentDefinition]):
        """Register multiple components"""
        for definition in definitions:
            self.register_component(definition)
    
    async def initialize_all(self, 
                           required_only: bool = False,
                           fail_fast: bool = False) -> InitializationReport:
        """
        Initialize all registered components
        
        Args:
            required_only: Only initialize required components
            fail_fast: Stop on first critical failure
            
        Returns:
            InitializationReport with results
        """
        if self.is_initializing:
            raise RuntimeError("Initialization already in progress")
        
        self.is_initializing = True
        self.initialization_start_time = time.time()
        
        try:
            logger.info("🚀 Starting parallel component initialization...")
            
            # Filter components
            components_to_init = self._filter_components(required_only)
            
            # Build dependency graph
            dependency_graph = self._build_dependency_graph(components_to_init)
            
            # Determine initialization order
            init_order = self._topological_sort(dependency_graph)
            
            # Initialize components in parallel where possible
            report = await self._initialize_components_parallel(
                init_order, dependency_graph, fail_fast
            )
            
            # Perform health checks
            if self.enable_health_checks:
                await self._perform_health_checks(report)
            
            # Log summary
            self._log_initialization_summary(report)
            
            return report
            
        finally:
            self.is_initializing = False
    
    def _filter_components(self, required_only: bool) -> List[str]:
        """Filter components based on requirements"""
        components = []
        for name, definition in self.container.definitions.items():
            if required_only and not definition.required:
                continue
            components.append(name)
        return components
    
    def _build_dependency_graph(self, components: List[str]) -> Dict[str, List[str]]:
        """Build dependency graph"""
        graph = {}
        for component in components:
            definition = self.container.definitions[component]
            # Only include dependencies that are also being initialized
            deps = [dep for dep in definition.dependencies if dep in components]
            graph[component] = deps
        return graph
    
    def _topological_sort(self, graph: Dict[str, List[str]]) -> List[str]:
        """Topological sort for dependency ordering"""
        # Kahn's algorithm - corrected implementation
        in_degree = {node: 0 for node in graph}

        # Calculate in-degrees correctly
        for node in graph:
            for dep in graph[node]:
                if dep in in_degree:
                    in_degree[node] += 1

        # Start with nodes that have no dependencies
        queue = [node for node in in_degree if in_degree[node] == 0]
        result = []

        while queue:
            node = queue.pop(0)
            result.append(node)

            # For each node that depends on the current node
            for dependent in graph:
                if node in graph[dependent]:
                    in_degree[dependent] -= 1
                    if in_degree[dependent] == 0:
                        queue.append(dependent)

        if len(result) != len(graph):
            logger.error(f"Dependency graph: {graph}")
            logger.error(f"In-degrees: {in_degree}")
            logger.error(f"Sorted result: {result}")
            raise ValueError("Circular dependency detected in component graph")

        return result
    
    async def _initialize_components_parallel(self, 
                                            init_order: List[str],
                                            dependency_graph: Dict[str, List[str]],
                                            fail_fast: bool) -> InitializationReport:
        """Initialize components in parallel respecting dependencies"""
        
        semaphore = asyncio.Semaphore(self.max_parallel_initializations)
        completed = set()
        failed = set()
        
        async def init_component_when_ready(component_name: str):
            """Initialize component when dependencies are ready"""
            definition = self.container.definitions[component_name]
            
            # Wait for dependencies
            while True:
                deps_ready = all(dep in completed for dep in dependency_graph[component_name])
                if deps_ready or any(dep in failed for dep in dependency_graph[component_name]):
                    break
                await asyncio.sleep(0.1)
            
            # Check if any dependency failed
            failed_deps = [dep for dep in dependency_graph[component_name] if dep in failed]
            if failed_deps:
                # Skip component if any dependency failed (regardless of required status)
                logger.warning(f"⚠️ Skipping {component_name} due to failed dependencies: {failed_deps}")
                self.initialization_results[component_name] = InitializationResult(
                    component_name=component_name,
                    state=ComponentState.SKIPPED,
                    dependencies_resolved=[]
                )
                return
            
            # Initialize component
            async with semaphore:
                result = await self._initialize_single_component(component_name)
                self.initialization_results[component_name] = result
                
                if result.state == ComponentState.INITIALIZED:
                    completed.add(component_name)
                else:
                    failed.add(component_name)
                    if fail_fast and definition.required:
                        logger.error(f"❌ Critical component {component_name} failed, stopping initialization")
                        # Cancel remaining tasks
                        for task in self.initialization_tasks.values():
                            if not task.done():
                                task.cancel()
        
        # Start initialization tasks
        tasks = []
        for component_name in init_order:
            task = asyncio.create_task(init_component_when_ready(component_name))
            self.initialization_tasks[component_name] = task
            tasks.append(task)
        
        # Wait for all tasks with global timeout
        try:
            await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=self.global_timeout
            )
        except asyncio.TimeoutError:
            logger.error(f"❌ Global initialization timeout ({self.global_timeout}s) exceeded")
            # Cancel remaining tasks
            for task in tasks:
                if not task.done():
                    task.cancel()
        
        # Build report
        total_time = time.time() - self.initialization_start_time
        successful = len([r for r in self.initialization_results.values() if r.state == ComponentState.INITIALIZED])
        failed_count = len([r for r in self.initialization_results.values() if r.state == ComponentState.FAILED])
        skipped = len([r for r in self.initialization_results.values() if r.state == ComponentState.SKIPPED])
        
        return InitializationReport(
            total_components=len(init_order),
            successful=successful,
            failed=failed_count,
            skipped=skipped,
            total_time=total_time,
            results=self.initialization_results.copy(),
            dependency_graph=dependency_graph,
            initialization_order=init_order
        )
    
    async def _initialize_single_component(self, component_name: str) -> InitializationResult:
        """Initialize a single component with retry and circuit breaker"""
        definition = self.container.definitions[component_name]
        circuit_breaker = self.container.get_circuit_breaker(component_name)
        
        start_time = time.time()
        retry_count = 0
        
        logger.info(f"🔧 Initializing component: {component_name}")
        
        while retry_count <= definition.retry_count:
            if not circuit_breaker.can_execute():
                logger.error(f"❌ Circuit breaker open for {component_name}")
                return InitializationResult(
                    component_name=component_name,
                    state=ComponentState.FAILED,
                    error=Exception("Circuit breaker open"),
                    initialization_time=time.time() - start_time,
                    retry_count=retry_count
                )
            
            try:
                # Resolve dependencies
                dependencies = self.container.resolve_dependencies(component_name)
                
                # Create instance
                instance = await self._create_component_instance(definition, dependencies)
                
                # Store instance
                self.container.set_instance(component_name, instance)
                
                # Record success
                circuit_breaker.record_success()
                
                initialization_time = time.time() - start_time
                logger.info(f"✅ Component {component_name} initialized in {initialization_time:.2f}s")
                
                return InitializationResult(
                    component_name=component_name,
                    state=ComponentState.INITIALIZED,
                    instance=instance,
                    initialization_time=initialization_time,
                    retry_count=retry_count,
                    dependencies_resolved=list(dependencies.keys())
                )
                
            except Exception as e:
                retry_count += 1
                circuit_breaker.record_failure()
                
                logger.warning(f"⚠️ Component {component_name} initialization failed (attempt {retry_count}): {e}")
                
                if retry_count <= definition.retry_count:
                    await asyncio.sleep(definition.retry_delay * retry_count)  # Exponential backoff
                else:
                    logger.error(f"❌ Component {component_name} failed after {retry_count} attempts")
                    return InitializationResult(
                        component_name=component_name,
                        state=ComponentState.FAILED,
                        error=e,
                        initialization_time=time.time() - start_time,
                        retry_count=retry_count
                    )
        
        # Should not reach here
        return InitializationResult(
            component_name=component_name,
            state=ComponentState.FAILED,
            error=Exception("Unexpected initialization failure"),
            initialization_time=time.time() - start_time,
            retry_count=retry_count
        )
    
    async def _create_component_instance(self, 
                                       definition: ComponentDefinition,
                                       dependencies: Dict[str, Any]) -> Any:
        """Create component instance with dependency injection"""
        
        # Prepare arguments
        args = list(definition.init_args.values())
        kwargs = definition.init_kwargs.copy()
        
        # Inject dependencies based on factory signature
        if inspect.iscoroutinefunction(definition.factory):
            # Async factory
            if dependencies:
                # Try to inject dependencies as keyword arguments
                sig = inspect.signature(definition.factory)
                for param_name, param in sig.parameters.items():
                    if param_name in dependencies:
                        kwargs[param_name] = dependencies[param_name]
            
            instance = await definition.factory(*args, **kwargs)
        else:
            # Sync factory or class
            if dependencies:
                sig = inspect.signature(definition.factory)
                for param_name, param in sig.parameters.items():
                    if param_name in dependencies:
                        kwargs[param_name] = dependencies[param_name]
            
            instance = definition.factory(*args, **kwargs)
        
        # Call async initialize method if available
        if hasattr(instance, 'initialize') and inspect.iscoroutinefunction(instance.initialize):
            await instance.initialize()
        
        return instance
    
    async def _perform_health_checks(self, report: InitializationReport):
        """Perform health checks on initialized components"""
        logger.info("🏥 Performing component health checks...")
        
        for component_name, result in report.results.items():
            if result.state != ComponentState.INITIALIZED:
                continue
            
            definition = self.container.definitions[component_name]
            if definition.health_check:
                try:
                    health_ok = await definition.health_check(result.instance)
                    if not health_ok:
                        logger.warning(f"⚠️ Health check failed for {component_name}")
                except Exception as e:
                    logger.error(f"❌ Health check error for {component_name}: {e}")
    
    def _log_initialization_summary(self, report: InitializationReport):
        """Log initialization summary"""
        logger.info("📊 Initialization Summary:")
        logger.info(f"   Total components: {report.total_components}")
        logger.info(f"   Successful: {report.successful}")
        logger.info(f"   Failed: {report.failed}")
        logger.info(f"   Skipped: {report.skipped}")
        logger.info(f"   Success rate: {report.success_rate:.1%}")
        logger.info(f"   Total time: {report.total_time:.2f}s")
        
        if report.critical_failures:
            logger.error(f"❌ Critical failures: {', '.join(report.critical_failures)}")
    
    def get_component(self, name: str) -> Optional[Any]:
        """Get initialized component instance"""
        return self.container.get_instance(name)
    
    def get_initialization_report(self) -> Optional[InitializationReport]:
        """Get latest initialization report"""
        if not self.initialization_results:
            return None
        
        # Reconstruct report from current state
        successful = len([r for r in self.initialization_results.values() if r.state == ComponentState.INITIALIZED])
        failed = len([r for r in self.initialization_results.values() if r.state == ComponentState.FAILED])
        skipped = len([r for r in self.initialization_results.values() if r.state == ComponentState.SKIPPED])
        
        return InitializationReport(
            total_components=len(self.initialization_results),
            successful=successful,
            failed=failed,
            skipped=skipped,
            total_time=0.0,  # Not tracked in this context
            results=self.initialization_results.copy()
        )
    
    async def cleanup(self):
        """Cleanup all initialized components"""
        logger.info("🧹 Cleaning up initialized components...")
        
        for component_name, instance in self.container.instances.items():
            definition = self.container.definitions.get(component_name)
            if definition and definition.cleanup:
                try:
                    await definition.cleanup(instance)
                    logger.debug(f"✅ Cleaned up {component_name}")
                except Exception as e:
                    logger.error(f"❌ Cleanup failed for {component_name}: {e}")


# Global instance
_global_init_system: Optional[AsyncInitializationSystem] = None
_init_lock = threading.Lock()


def get_initialization_system(config: Optional[Dict[str, Any]] = None) -> AsyncInitializationSystem:
    """Get global initialization system instance"""
    global _global_init_system
    if _global_init_system is None:
        with _init_lock:
            if _global_init_system is None:
                _global_init_system = AsyncInitializationSystem(config)
    return _global_init_system


# Convenience functions for common component types
def create_qualia_component_definitions(config: Dict[str, Any]) -> List[ComponentDefinition]:
    """Create standard QUALIA component definitions"""
    definitions = []

    # Configuration Manager (no dependencies)
    definitions.append(ComponentDefinition(
        name="config_manager",
        factory=lambda: config,  # Simple factory returning config
        dependencies=[],
        strategy=InitializationStrategy.CRITICAL,
        timeout=5.0,
        required=True
    ))

    # Logging System (depends on config)
    definitions.append(ComponentDefinition(
        name="logging_system",
        factory=lambda config_manager: setup_logging_from_config(config_manager),
        dependencies=["config_manager"],
        strategy=InitializationStrategy.CRITICAL,
        timeout=5.0,
        required=True
    ))

    # Health System (no dependencies)
    definitions.append(ComponentDefinition(
        name="health_system",
        factory=lambda: get_health_system(),
        dependencies=[],
        strategy=InitializationStrategy.PARALLEL,
        timeout=10.0,
        required=False
    ))

    return definitions


def setup_logging_from_config(config: Dict[str, Any]) -> bool:
    """Setup logging from configuration"""
    try:
        from src.qualia.utils.logging_integration import setup_qualia_logging_from_config
        setup_qualia_logging_from_config(config)
        return True
    except Exception as e:
        logger.error(f"Failed to setup logging: {e}")
        return False


def get_health_system():
    """Get health system instance"""
    try:
        from src.qualia.monitoring.enhanced_health_system import get_health_system
        return get_health_system()
    except Exception as e:
        logger.error(f"Failed to get health system: {e}")
        return None
