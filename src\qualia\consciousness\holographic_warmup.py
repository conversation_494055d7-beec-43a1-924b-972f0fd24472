"""
QUALIA Holographic Warmup Manager.

Este módulo é responsável por "aquecer" o HolographicMarketUniverse,
alimentando-o com dados históricos antes do início do trading em tempo real.
Isso garante que o campo holográfico não comece "frio", permitindo a
detecção de padrões significativos desde o primeiro momento.
"""

from __future__ import annotations

import asyncio
import time
from typing import TYPE_CHECKING, Optional

from ..utils.logging_config import get_qualia_logger
from .enhanced_data_collector import EnhancedDataCollector
from .real_data_collectors import RealDataCollector

if TYPE_CHECKING:
    from .holographic_universe import HolographicMarketUniverse

# YAA TASK 4: Usar logger QUALIA padronizado
logger = get_qualia_logger("consciousness.holographic_warmup")


class HolographicWarmupManager:
    """
    Gerencia o processo de aquecimento do universo holográfico.

    Busca dados históricos de mercado e notícias e os injeta sequencialmente
    no universo para simular um período passado, criando um estado de campo
    com memória e padrões preexistentes.
    """

    def __init__(
        self,
        holographic_universe: "HolographicMarketUniverse",
        enhanced_data_collector: Optional[EnhancedDataCollector] = None,
        real_data_collector: Optional[RealDataCollector] = None,
    ):
        """
        Inicializa o Warmup Manager.

        Args:
            holographic_universe: A instância do universo a ser aquecida.
            enhanced_data_collector: Coletor para dados de mercado históricos (OHLCV).
            real_data_collector: Coletor para eventos de notícias históricos.
        """
        self.universe = holographic_universe
        self.enhanced_collector = enhanced_data_collector
        self.real_collector = real_data_collector
        logger.info("HolographicWarmupManager inicializado.")

    async def perform_warmup(
        self,
        warmup_period_hours: int = 72,
        simulation_step_seconds: int = 600,
    ) -> bool:
        """
        Executa o processo de aquecimento.

        Args:
            warmup_period_hours: O período histórico a ser simulado, em horas.
            simulation_step_seconds: A resolução da simulação, em segundos (ex: 600s = 10 min).

        Returns:
            True se o aquecimento foi bem-sucedido, False caso contrário.
        """
        if not self.enhanced_collector or not self.real_collector:
            logger.warning(
                "Coletores de dados não fornecidos. Não é possível executar o warm-up."
            )
            return False

        logger.info(
            f"🔥 Iniciando aquecimento holográfico para as últimas {warmup_period_hours} horas..."
        )
        logger.info(f"   Resolução da simulação: {simulation_step_seconds} segundos por passo.")

        end_time = int(time.time())
        start_time = end_time - (warmup_period_hours * 3600)
        total_steps = (end_time - start_time) // simulation_step_seconds

        logger.info(f"   Período: {time.ctime(start_time)} -> {time.ctime(end_time)}")
        logger.info(f"   Total de passos de simulação: {total_steps}")

        try:
            # YAA TASK 4: Coleta paralela de dados históricos para otimização
            logger.info("🚀 Iniciando coleta paralela de dados históricos...")

            # Criar tasks paralelas para coleta de dados
            tasks = []

            # Task 1: Dados históricos de mercado (OHLCV)
            logger.info("   📊 Agendando coleta de dados de mercado...")
            market_data_task = asyncio.create_task(
                self._fetch_historical_market_data_parallel(start_time, end_time),
                name="market_data_collection"
            )
            tasks.append(market_data_task)

            # Task 2: Eventos de notícias históricos
            logger.info("   📰 Agendando coleta de eventos de notícias...")
            news_data_task = asyncio.create_task(
                self._fetch_historical_news_data_parallel(),
                name="news_data_collection"
            )
            tasks.append(news_data_task)

            # Executar coletas em paralelo com timeout
            logger.info("⏱️ Executando coletas paralelas (timeout: 120s)...")
            start_collection_time = time.time()

            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=120.0  # 2 minutos máximo para coleta
                )

                historical_market_data, historical_news = results
                collection_time = time.time() - start_collection_time

                logger.info(f"✅ Coleta paralela concluída em {collection_time:.2f}s")

                # Verificar se houve exceções
                if isinstance(historical_market_data, Exception):
                    logger.error(f"Erro na coleta de dados de mercado: {historical_market_data}")
                    historical_market_data = []

                if isinstance(historical_news, Exception):
                    logger.error(f"Erro na coleta de notícias: {historical_news}")
                    historical_news = []

            except asyncio.TimeoutError:
                logger.warning("⏰ Timeout na coleta paralela - usando dados parciais")
                # Cancelar tasks pendentes
                for task in tasks:
                    if not task.done():
                        task.cancel()

                # Tentar obter resultados parciais
                historical_market_data = []
                historical_news = []

                for task in tasks:
                    try:
                        if task.done() and not task.cancelled():
                            result = task.result()
                            if task.get_name() == "market_data_collection":
                                historical_market_data = result if not isinstance(result, Exception) else []
                            elif task.get_name() == "news_data_collection":
                                historical_news = result if not isinstance(result, Exception) else []
                    except Exception as e:
                        logger.warning(f"Erro ao obter resultado parcial de {task.get_name()}: {e}")

            # Log dos resultados
            market_count = len(historical_market_data) if historical_market_data else 0
            news_count = len(historical_news) if historical_news else 0

            logger.info(f"📊 Dados coletados: {market_count} registros de mercado, {news_count} eventos de notícias")


            # 3. Processar dados de warm-up de forma otimizada
            logger.info("🚀 Iniciando processamento otimizado de warm-up...")

            if not historical_market_data and not historical_news:
                logger.warning("⚠️ Nenhum dado histórico disponível - warm-up será limitado")
                return False

            # Usar processamento paralelo otimizado
            processing_success = await self._process_warmup_data_optimized(
                historical_market_data or [],
                historical_news or [],
                simulation_step_seconds,
                start_time,
                end_time
            )

            if processing_success:
                final_summary = self.universe.get_field_summary()
                logger.info("✅ Warm-up holográfico concluído com sucesso!")
                logger.info(f"📊 Resumo final: Energia={final_summary.get('field_energy', 0):.2f}, Entropia={final_summary.get('field_entropy', 0):.2f}")
                logger.info("🔥 Universo Holográfico aquecido e pronto para operação.")
                return True
            else:
                logger.error("❌ Falha no processamento de warm-up")
                return False

        except Exception as e:
            logger.error(f"❌ Erro durante o warm-up holográfico: {e}", exc_info=True)
            return False

    def _get_slice(
        self, data: list, current_time: int, interval: int
    ) -> list:
        """Filtra dados para o intervalo de tempo atual."""
        return [
            item
            for item in data
            if current_time <= item.timestamp < current_time + interval
        ]

    def _get_news_slice(self, news: list, current_time: int, interval: int, total_steps: int) -> list:
        """Distribui as notícias coletadas ao longo do período de simulação."""
        if not news or not total_steps:
            return []
        
        # Simplesmente distribui as notícias uniformemente ao longo do tempo
        news_per_step = len(news) / total_steps
        current_step_index = (current_time - (time.time() - 72*3600)) // interval
        
        start_index = int(current_step_index * news_per_step)
        end_index = int((current_step_index + 1) * news_per_step)
        
        return news[start_index:end_index]

    async def _fetch_historical_market_data_parallel(
        self, start_time: float, end_time: float
    ) -> list:
        """
        YAA TASK 4: Coleta paralela de dados históricos de mercado.

        Args:
            start_time: Timestamp inicial em segundos
            end_time: Timestamp final em segundos

        Returns:
            Lista de dados históricos de mercado
        """
        try:
            if not self.enhanced_collector:
                logger.warning("Enhanced collector não disponível para coleta de mercado")
                return []

            logger.info("📊 Iniciando coleta paralela de dados de mercado...")

            historical_market_data = await self.enhanced_collector.fetch_historical_data_for_warmup(
                start_timestamp_ms=int(start_time * 1000),
                end_timestamp_ms=int(end_time * 1000),
            )

            if not historical_market_data:
                logger.warning("Nenhum dado de mercado histórico encontrado para o warm-up.")
                return []

            logger.info(f"✅ {len(historical_market_data)} registros de mercado coletados")
            return historical_market_data

        except Exception as e:
            logger.error(f"Erro na coleta paralela de dados de mercado: {e}")
            return []

    async def _fetch_historical_news_data_parallel(self) -> list:
        """
        YAA TASK 4: Coleta paralela de eventos de notícias históricos.

        Returns:
            Lista de eventos de notícias
        """
        try:
            if not self.real_collector:
                logger.warning("Real collector não disponível para coleta de notícias")
                return []

            logger.info("📰 Iniciando coleta paralela de eventos de notícias...")

            # Para dados históricos, podemos implementar múltiplas fontes em paralelo
            historical_news = await self.real_collector.collect_news_events()

            if not historical_news:
                logger.warning("Nenhum evento de notícias encontrado para simulação.")
                return []

            logger.info(f"✅ {len(historical_news)} eventos de notícias coletados")
            return historical_news

        except Exception as e:
            logger.error(f"Erro na coleta paralela de notícias: {e}")
            return []

    async def _process_warmup_data_optimized(
        self,
        historical_market_data: list,
        historical_news: list,
        simulation_step_seconds: int,
        start_time: float,
        end_time: float
    ) -> bool:
        """
        YAA TASK 4: Processa dados de warm-up de forma otimizada com paralelização.

        Args:
            historical_market_data: Dados de mercado coletados
            historical_news: Eventos de notícias coletados
            simulation_step_seconds: Intervalo de simulação em segundos
            start_time: Timestamp inicial
            end_time: Timestamp final

        Returns:
            True se processamento foi bem-sucedido
        """
        try:
            logger.info("🚀 Iniciando processamento otimizado de warm-up...")

            # Calcular steps de simulação
            total_steps = int((end_time - start_time) / simulation_step_seconds)
            logger.info(f"📈 Processando {total_steps} steps de {simulation_step_seconds}s cada")

            if total_steps == 0:
                logger.warning("⚠️ Nenhum step de simulação calculado")
                return False

            # Processar em batches menores para melhor performance e controle de memória
            batch_size = min(50, max(1, total_steps // 20))  # Batches de até 50 steps
            processed_steps = 0

            logger.info(f"🔄 Processando em batches de {batch_size} steps...")

            for batch_start in range(0, total_steps, batch_size):
                batch_end = min(batch_start + batch_size, total_steps)

                # Processar batch atual
                batch_success = await self._process_simulation_batch(
                    batch_start, batch_end,
                    historical_market_data, historical_news,
                    simulation_step_seconds, start_time
                )

                if not batch_success:
                    logger.warning(f"⚠️ Falha no processamento do batch {batch_start}-{batch_end}")
                    # Continua com próximo batch ao invés de falhar completamente

                processed_steps += (batch_end - batch_start)
                progress = (processed_steps / total_steps) * 100

                # Log de progresso a cada 20%
                if processed_steps % max(1, total_steps // 5) == 0:
                    logger.info(f"📊 Progresso do warm-up: {progress:.0f}% completo...")

            logger.info("✅ Processamento otimizado de warm-up concluído.")
            return True

        except Exception as e:
            logger.error(f"❌ Erro no processamento otimizado de warm-up: {e}")
            return False

    async def _process_simulation_batch(
        self,
        batch_start: int,
        batch_end: int,
        market_data: list,
        news_data: list,
        step_seconds: int,
        start_time: float
    ) -> bool:
        """
        Processa um batch de steps de simulação.

        Args:
            batch_start: Índice inicial do batch
            batch_end: Índice final do batch
            market_data: Dados de mercado
            news_data: Eventos de notícias
            step_seconds: Duração de cada step
            start_time: Timestamp inicial da simulação

        Returns:
            True se batch foi processado com sucesso
        """
        try:
            batch_tasks = []

            # Criar tasks para cada step do batch
            for i in range(batch_start, batch_end):
                current_simulation_time = start_time + (i * step_seconds)

                # Filtrar dados para este timestamp
                market_slice = self._get_slice(
                    market_data, current_simulation_time, step_seconds
                )
                news_slice = self._get_news_slice(
                    news_data, current_simulation_time, step_seconds, batch_end - batch_start
                )

                # Criar task para processar este step
                if market_slice or news_slice:
                    task = asyncio.create_task(
                        self._process_single_simulation_step(
                            market_slice, news_slice, current_simulation_time
                        ),
                        name=f"simulation_step_{i}"
                    )
                    batch_tasks.append(task)

            # Executar todas as tasks do batch em paralelo
            if batch_tasks:
                results = await asyncio.gather(*batch_tasks, return_exceptions=True)

                # Verificar se houve erros
                errors = [r for r in results if isinstance(r, Exception)]
                if errors:
                    logger.warning(f"⚠️ {len(errors)} erros no batch {batch_start}-{batch_end}")
                    for error in errors[:3]:  # Log apenas os primeiros 3 erros
                        logger.warning(f"   Erro: {error}")

            return True

        except Exception as e:
            logger.error(f"❌ Erro no processamento do batch {batch_start}-{batch_end}: {e}")
            return False

    async def _process_single_simulation_step(
        self, market_slice: list, news_slice: list, simulation_time: float
    ):
        """
        Processa um step individual da simulação de forma otimizada.

        Args:
            market_slice: Dados de mercado para este step
            news_slice: Eventos de notícias para este step
            simulation_time: Timestamp da simulação
        """
        try:
            # Converter dados para eventos holográficos
            if hasattr(self.enhanced_collector, 'convert_to_holographic_events'):
                events = self.enhanced_collector.convert_to_holographic_events(
                    market_slice, news_slice, self.universe.field_size
                )
            else:
                # Fallback: usar dados diretamente
                events = market_slice + news_slice

            # Injetar eventos no universo
            for event in events:
                if hasattr(event, 'time'):
                    event.time = simulation_time
                await self.universe.inject_holographic_event(event)

            # Evoluir o campo
            await self.universe.step_evolution(simulation_time)

        except Exception as e:
            logger.warning(f"⚠️ Erro no step de simulação {simulation_time}: {e}")
            # Não re-raise para não interromper o batch inteiro