# StatsD to Prometheus mapping configuration for QUALIA metrics
mappings:
  # QUALIA Hyperparameters metrics
  - match: "qualia.hyperparams.*"
    name: "qualia_hyperparams_${1}"
    labels:
      component: "${2}"
      symbol: "${3}"

  # QUALIA Trading decisions
  - match: "qualia.hyperparams.decisions_total"
    name: "qualia_hyperparams_decisions_total"
    labels:
      decision_type: "${1}"
      component: "${2}"

  # QUALIA Performance metrics
  - match: "qualia.hyperparams.total_return_pct"
    name: "qualia_hyperparams_total_return_pct"
    labels:
      timeframe: "${1}"
      symbol: "${2}"

  - match: "qualia.hyperparams.sharpe_ratio"
    name: "qualia_hyperparams_sharpe_ratio"
    labels:
      timeframe: "${1}"
      symbol: "${2}"

  - match: "qualia.hyperparams.max_drawdown_pct"
    name: "qualia_hyperparams_max_drawdown_pct"
    labels:
      timeframe: "${1}"
      symbol: "${2}"

  - match: "qualia.hyperparams.decision_rate"
    name: "qualia_hyperparams_decision_rate"
    labels:
      timeframe: "${1}"
      symbol: "${2}"

  # QUALIA Amplification parameters
  - match: "qualia.hyperparams.price_amplification"
    name: "qualia_hyperparams_price_amplification"
    labels:
      component: "${1}"

  - match: "qualia.hyperparams.news_amplification"
    name: "qualia_hyperparams_news_amplification"
    labels:
      component: "${1}"

  - match: "qualia.hyperparams.min_confidence"
    name: "qualia_hyperparams_min_confidence"
    labels:
      component: "${1}"

  - match: "qualia.hyperparams.final_confidence"
    name: "qualia_hyperparams_final_confidence"
    labels:
      component: "${1}"
      symbol: "${2}"

  # QUALIA Calibration metrics
  - match: "qualia.hyperparams.calibrations_total"
    name: "qualia_hyperparams_calibrations_total"
    labels:
      component: "${1}"

  - match: "qualia.hyperparams.patterns_detected"
    name: "qualia_hyperparams_patterns_detected"
    labels:
      component: "${1}"

  - match: "qualia.hyperparams.signals_generated"
    name: "qualia_hyperparams_signals_generated"
    labels:
      component: "${1}"

  - match: "qualia.hyperparams.execution_success_rate"
    name: "qualia_hyperparams_execution_success_rate"
    labels:
      component: "${1}"

  - match: "qualia.hyperparams.false_positive_rate"
    name: "qualia_hyperparams_false_positive_rate"
    labels:
      component: "${1}"

  # QUALIA Bayesian Optimizer metrics
  - match: "qualia.bayesian_optimizer.*"
    name: "qualia_bayesian_optimizer_${1}"
    labels:
      optimizer_id: "${2}"

  - match: "qualia.bayesian_optimizer.trials_completed"
    name: "qualia_bayesian_optimizer_trials_completed_total"
    labels:
      study_name: "${1}"

  - match: "qualia.bayesian_optimizer.best_score"
    name: "qualia_bayesian_optimizer_best_score"
    labels:
      study_name: "${1}"
      metric: "${2}"

  - match: "qualia.bayesian_optimizer.failed_trials"
    name: "qualia_bayesian_optimizer_failed_trials_total"
    labels:
      study_name: "${1}"

  # QUALIA System metrics
  - match: "qualia.system.*"
    name: "qualia_system_${1}"
    labels:
      instance: "${2}"

  - match: "qualia.system.processing_latency_ms"
    name: "qualia_hyperparams_processing_latency_ms"
    labels:
      component: "${1}"

  # HUD Performance metrics
  - match: "hud.avg_fps"
    name: "qualia_hud_avg_fps"

  - match: "hud.latency_ms"
    name: "qualia_hud_latency_ms"

  # PID Optimizer metrics
  - match: "pid.record"
    name: "qualia_pid_records_total"

  # Generic QUALIA metrics fallback
  - match: "qualia.*"
    name: "qualia_${1}"
    labels:
      metric_type: "generic"
