# Benchmark PCA/KMeans Cache

Data da Execucao: 2025-06-01

## Configuracao

- **Python**: 3.11
- **n_qubits**: 2
- **history_maxlen**: 50
- **Iteracoes**: 10 chamadas consecutivas de `_detect_patterns`

## Resultados

### Antes da Otimizacao

Tempo medio ~5.8 ms por execucao.

### Depois da Otimizacao

Tempo medio ~4.0-4.1 ms por execucao.

A reducao foi de aproximadamente 30%, validando a reutilizacao de modelos e a
extracao unica de dados do historico.
