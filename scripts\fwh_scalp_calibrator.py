#!/usr/bin/env python3
"""
FWH Scalp Calibrator - Sistema de Calibração Automática para FWH Scalp Trading

Este sistema calibra automaticamente os parâmetros da estratégia FWH para
maximizar a performance em paper trading, gerando sinais efetivos para trades reais.

Características:
- Calibração automática de parâmetros críticos
- Múltiplas estratégias de otimização (Grid Search, Genetic Algorithm)
- Validação em tempo real com paper trading
- Métricas avançadas de performance
- Geração de relatórios detalhados
- Backup e versionamento de configurações

Autor: YAA (YET ANOTHER AGENT) - QUALIA Quantum Consciousness
Data: 2024
"""

import asyncio
import json
import logging
import os
import sys
import time
import yaml
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from itertools import product
import copy
import argparse
from concurrent.futures import ThreadPoolExecutor
import matplotlib.pyplot as plt
import seaborn as sns
import aiohttp
import websockets
from binance.client import Client
from binance.exceptions import BinanceAPIException

# Adicionar o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))
sys.path.insert(0, str(Path(__file__).parent))

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fwh_calibrator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Carregar variáveis de ambiente do arquivo .env
try:
    from dotenv import load_dotenv
    env_path = Path(__file__).parent.parent / ".env"
    load_dotenv(env_path)
    logger.info(f"[OK] Arquivo .env carregado: {env_path}")
except ImportError:
    logger.warning("[WARNING] python-dotenv não instalado, usando variáveis de ambiente do sistema")
except Exception as e:
    logger.warning(f"[WARNING] Erro ao carregar .env: {e}")

class RealMarketDataManager:
    """Gerenciador de dados reais de mercado da Binance."""
    
    def __init__(self):
        self.client = None
        self.websocket_connections = {}
        self.market_data_cache = {}
        self.last_update_times = {}
        self.connection_status = False
        
    async def initialize(self, api_key: str = None, api_secret: str = None) -> bool:
        """Inicializa conexão com a Binance."""
        try:
            # Usar cliente público se não houver credenciais
            if api_key and api_secret:
                self.client = Client(api_key, api_secret)
            else:
                self.client = Client()  # Cliente público
            
            # Testar conexão
            server_time = self.client.get_server_time()
            if server_time:
                self.connection_status = True
                logger.info("[OK] Conexão com Binance estabelecida")
                return True
            
        except Exception as e:
            logger.error(f"[ERROR] Erro ao conectar com Binance: {e}")
            self.connection_status = False
            return False
    
    async def get_ticker(self, symbol: str) -> Optional[Dict]:
        """Obtém dados de ticker em tempo real."""
        try:
            if not self.client:
                return None
            
            ticker = self.client.get_symbol_ticker(symbol=symbol)
            if ticker:
                # Adicionar timestamp
                ticker['timestamp'] = time.time()
                self.market_data_cache[symbol] = ticker
                self.last_update_times[symbol] = time.time()
                
            return ticker
            
        except BinanceAPIException as e:
            logger.warning(f"Erro da API Binance para {symbol}: {e}")
            return None
        except Exception as e:
            logger.warning(f"Erro ao obter ticker {symbol}: {e}")
            return None
    
    async def get_orderbook(self, symbol: str, limit: int = 10) -> Optional[Dict]:
        """Obtém orderbook em tempo real."""
        try:
            if not self.client:
                return None
            
            orderbook = self.client.get_order_book(symbol=symbol, limit=limit)
            if orderbook:
                # Calcular spread
                best_bid = float(orderbook['bids'][0][0]) if orderbook['bids'] else 0
                best_ask = float(orderbook['asks'][0][0]) if orderbook['asks'] else 0
                spread = (best_ask - best_bid) / best_bid if best_bid > 0 else 0
                
                orderbook['spread'] = spread
                orderbook['timestamp'] = time.time()
                
            return orderbook
            
        except Exception as e:
            logger.warning(f"Erro ao obter orderbook {symbol}: {e}")
            return None
    
    async def get_klines(self, symbol: str, interval: str, limit: int = 100) -> Optional[List]:
        """Obtém dados de candlesticks."""
        try:
            if not self.client:
                return None
            
            klines = self.client.get_klines(
                symbol=symbol,
                interval=interval,
                limit=limit
            )
            
            return klines
            
        except Exception as e:
            logger.warning(f"Erro ao obter klines {symbol}: {e}")
            return None
    
    async def validate_market_conditions(self, symbols: List[str]) -> Dict[str, bool]:
        """Valida condições de mercado para trading."""
        validation_results = {}
        
        for symbol in symbols:
            try:
                # Verificar ticker
                ticker = await self.get_ticker(symbol)
                if not ticker:
                    validation_results[symbol] = False
                    continue
                
                # Verificar orderbook
                orderbook = await self.get_orderbook(symbol)
                if not orderbook:
                    validation_results[symbol] = False
                    continue
                
                # Validações específicas
                price = float(ticker['price'])
                spread = orderbook.get('spread', 1.0)
                
                # Critérios de validação
                valid_price = price > 0
                valid_spread = spread < 0.01  # Spread menor que 1%
                has_liquidity = len(orderbook['bids']) >= 5 and len(orderbook['asks']) >= 5
                
                validation_results[symbol] = valid_price and valid_spread and has_liquidity
                
                if validation_results[symbol]:
                    logger.info(f"[OK] {symbol}: Preço=${price:,.4f}, Spread={spread:.4%}")
                else:
                    logger.warning(f"[WARNING] {symbol}: Condições inadequadas")
                
            except Exception as e:
                logger.error(f"Erro na validação de {symbol}: {e}")
                validation_results[symbol] = False
        
        return validation_results
    
    def get_connection_status(self) -> bool:
        """Retorna status da conexão."""
        return self.connection_status
    
    async def cleanup(self):
        """Limpa recursos."""
        try:
            # Fechar conexões websocket se houver
            for ws in self.websocket_connections.values():
                if hasattr(ws, 'close'):
                    await ws.close()
            
            self.websocket_connections.clear()
            self.market_data_cache.clear()
            
        except Exception as e:
            logger.warning(f"Erro na limpeza: {e}")

@dataclass
class CalibrationResult:
    """Resultado de uma calibração individual."""
    config_id: str
    parameters: Dict[str, Any]
    metrics: Dict[str, float]
    score: float
    duration_minutes: float
    signals_generated: int
    trades_executed: int
    timestamp: str
    
@dataclass
class ParameterRange:
    """Define o range de um parâmetro para calibração."""
    name: str
    min_value: float
    max_value: float
    step_count: int
    current_value: Optional[float] = None
    
    def get_values(self) -> List[float]:
        """Retorna lista de valores para testar."""
        return np.linspace(self.min_value, self.max_value, self.step_count).tolist()

class ParameterSpace:
    """Define o espaço de parâmetros para calibração."""
    
    def __init__(self, mode: str = "fast"):
        """Inicializa espaço de parâmetros.
        
        Args:
            mode: 'fast' (3^6=729 combinações), 'balanced' (3^8=6561), 'full' (original)
        """
        if mode == "fast":
            # MODO RÁPIDO: Apenas 6 parâmetros mais críticos (3^6 = 729 combinações)
            self.parameters = {
                # Os 3 parâmetros mais críticos
                'hype_threshold_1m': ParameterRange('hype_threshold_1m', 0.15, 0.45, 3),
                'wave_min_strength_1m': ParameterRange('wave_min_strength_1m', 0.20, 0.40, 3),
                'quantum_boost_1m': ParameterRange('quantum_boost_1m', 0.95, 1.15, 3),
                
                # Trading levels críticos
                'stop_loss_pct': ParameterRange('stop_loss_pct', 0.4, 0.8, 3),
                'take_profit_pct': ParameterRange('take_profit_pct', 0.8, 1.5, 3),
                
                # OTOC principal
                'otoc_max_threshold': ParameterRange('otoc_max_threshold', 0.25, 0.65, 3),
            }
        elif mode == "balanced":
            # MODO BALANCEADO: 8 parâmetros principais (3^8 = 6561 combinações)
            self.parameters = {
                # Thresholds principais
                'hype_threshold_1m': ParameterRange('hype_threshold_1m', 0.15, 0.45, 3),
                'hype_threshold_5m': ParameterRange('hype_threshold_5m', 0.20, 0.50, 3),
                'wave_min_strength_1m': ParameterRange('wave_min_strength_1m', 0.20, 0.40, 3),
                
                # Fatores de boost
                'quantum_boost_1m': ParameterRange('quantum_boost_1m', 0.95, 1.15, 3),
                
                # OTOC Configuration
                'otoc_max_threshold': ParameterRange('otoc_max_threshold', 0.25, 0.65, 3),
                
                # Trading levels
                'stop_loss_pct': ParameterRange('stop_loss_pct', 0.4, 0.8, 3),
                'take_profit_pct': ParameterRange('take_profit_pct', 0.8, 1.5, 3),
                
                # Peso principal
                'weight_1m': ParameterRange('weight_1m', 0.15, 0.35, 3),
            }
        else:
            # MODO COMPLETO: Todos os parâmetros (APENAS para testes específicos)
            self.parameters = {
                # Thresholds críticos
                'hype_threshold_1m': ParameterRange('hype_threshold_1m', 0.15, 0.45, 3),
                'hype_threshold_5m': ParameterRange('hype_threshold_5m', 0.20, 0.50, 3),
                'wave_min_strength_1m': ParameterRange('wave_min_strength_1m', 0.20, 0.40, 3),
                'wave_min_strength_5m': ParameterRange('wave_min_strength_5m', 0.18, 0.35, 3),
                
                # Fatores de boost
                'quantum_boost_1m': ParameterRange('quantum_boost_1m', 0.95, 1.15, 3),
                'quantum_boost_5m': ParameterRange('quantum_boost_5m', 0.98, 1.20, 3),
                
                # Pesos holográficos
                'holographic_weight_1m': ParameterRange('holographic_weight_1m', 0.30, 0.70, 3),
                'holographic_weight_5m': ParameterRange('holographic_weight_5m', 0.40, 0.80, 3),
                
                # OTOC Configuration
                'otoc_max_threshold': ParameterRange('otoc_max_threshold', 0.25, 0.65, 3),
                'otoc_beta': ParameterRange('otoc_beta', 0.6, 0.9, 3),
                
                # Trading levels
                'stop_loss_pct': ParameterRange('stop_loss_pct', 0.4, 0.8, 3),
                'take_profit_pct': ParameterRange('take_profit_pct', 0.8, 1.5, 3),
                
                # Timeframe weights
                'weight_1m': ParameterRange('weight_1m', 0.15, 0.35, 3),
                'weight_5m': ParameterRange('weight_5m', 0.25, 0.45, 3),
            }
        
        self.mode = mode
        logger.info(f"🎯 ParameterSpace inicializado em modo '{mode}' com {self.get_total_combinations()} combinações")
    
    def get_total_combinations(self) -> int:
        """Retorna o número total de combinações possíveis."""
        total = 1
        for param in self.parameters.values():
            total *= param.step_count
        return total
    
    def generate_combinations(self, max_combinations: Optional[int] = None) -> List[Dict[str, float]]:
        """Gera combinações de parâmetros com limite inteligente."""
        param_names = list(self.parameters.keys())
        param_values = [self.parameters[name].get_values() for name in param_names]
        
        total_possible = self.get_total_combinations()
        
        # Limite absoluto de segurança
        absolute_max = 1000
        if max_combinations:
            effective_max = min(max_combinations, absolute_max)
        else:
            effective_max = min(total_possible, absolute_max)
        
        logger.info(f"Gerando combinações: {total_possible} possíveis, limitando a {effective_max}")
        
        combinations = []
        
        if total_possible <= effective_max:
            # Gerar todas as combinações
            for combo in product(*param_values):
                combination = dict(zip(param_names, combo))
                combinations.append(combination)
        else:
            # Amostragem aleatória estratificada
            import random
            random.seed(42)  # Para reprodutibilidade
            
            logger.info(f"Usando amostragem aleatória para reduzir de {total_possible} para {effective_max} combinações")
            
            all_combinations = list(product(*param_values))
            sampled_indices = random.sample(range(len(all_combinations)), effective_max)
            
            for idx in sampled_indices:
                combo = all_combinations[idx]
                combination = dict(zip(param_names, combo))
                combinations.append(combination)
        
        logger.info(f"[OK] {len(combinations)} combinações geradas para teste")
        return combinations

class MetricsEvaluator:
    """Avalia métricas de performance e calcula scores."""
    
    def __init__(self):
        self.weights = {
            'win_rate': 0.25,
            'profit_factor': 0.30,
            'signals_per_hour': 0.20,
            'max_drawdown_inv': 0.15,
            'sharpe_ratio': 0.10
        }
        
        self.min_requirements = {
            'win_rate': 0.45,  # Mínimo 45%
            'profit_factor': 1.2,  # Mínimo 1.2
            'signals_per_hour': 2.0,  # Mínimo 2 sinais/hora
            'max_drawdown': 0.05  # Máximo 5% drawdown
        }
    
    def calculate_score(self, metrics: Dict[str, float]) -> float:
        """Calcula score normalizado baseado nas métricas."""
        # Verificar requisitos mínimos
        if not self._meets_requirements(metrics):
            return 0.0
        
        # Normalizar métricas (0-1)
        normalized = self._normalize_metrics(metrics)
        
        # Calcular score ponderado
        score = 0.0
        for metric, weight in self.weights.items():
            if metric in normalized:
                score += normalized[metric] * weight
        
        return min(score, 1.0)
    
    def _meets_requirements(self, metrics: Dict[str, float]) -> bool:
        """Verifica se as métricas atendem aos requisitos mínimos."""
        for req, min_val in self.min_requirements.items():
            if req == 'max_drawdown':
                if metrics.get('max_drawdown', 1.0) > min_val:
                    return False
            else:
                if metrics.get(req, 0.0) < min_val:
                    return False
        return True
    
    def _normalize_metrics(self, metrics: Dict[str, float]) -> Dict[str, float]:
        """Normaliza métricas para escala 0-1."""
        normalized = {}
        
        # Win rate (0-100% -> 0-1)
        normalized['win_rate'] = min(metrics.get('win_rate', 0) / 100.0, 1.0)
        
        # Profit factor (1-5 -> 0-1)
        pf = metrics.get('profit_factor', 1.0)
        normalized['profit_factor'] = min((pf - 1.0) / 4.0, 1.0)
        
        # Signals per hour (0-20 -> 0-1)
        sph = metrics.get('signals_per_hour', 0)
        normalized['signals_per_hour'] = min(sph / 20.0, 1.0)
        
        # Max drawdown invertido (0-10% -> 1-0)
        dd = metrics.get('max_drawdown', 0.1)
        normalized['max_drawdown_inv'] = max(1.0 - (dd / 0.1), 0.0)
        
        # Sharpe ratio (0-3 -> 0-1)
        sr = metrics.get('sharpe_ratio', 0)
        normalized['sharpe_ratio'] = min(max(sr, 0) / 3.0, 1.0)
        
        return normalized

class ConfigGenerator:
    """Gera configurações YAML otimizadas."""
    
    def __init__(self, base_config_path: str):
        self.base_config_path = base_config_path
        self.base_config = self._load_base_config()
    
    def _load_base_config(self) -> Dict[str, Any]:
        """Carrega configuração base."""
        with open(self.base_config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def generate_config(self, parameters: Dict[str, float]) -> Dict[str, Any]:
        """Gera configuração com parâmetros específicos."""
        config = copy.deepcopy(self.base_config)
        
        # Aplicar parâmetros de timeframe
        fwh_config = config['fibonacci_wave_hype_config']['params']
        
        # Timeframe 1m
        if 'hype_threshold_1m' in parameters:
            fwh_config['timeframe_specific']['1m']['hype_threshold'] = parameters['hype_threshold_1m']
        if 'wave_min_strength_1m' in parameters:
            fwh_config['timeframe_specific']['1m']['wave_min_strength'] = parameters['wave_min_strength_1m']
        if 'quantum_boost_1m' in parameters:
            fwh_config['timeframe_specific']['1m']['quantum_boost_factor'] = parameters['quantum_boost_1m']
        if 'holographic_weight_1m' in parameters:
            fwh_config['timeframe_specific']['1m']['holographic_weight'] = parameters['holographic_weight_1m']
        
        # Timeframe 5m
        if 'hype_threshold_5m' in parameters:
            fwh_config['timeframe_specific']['5m']['hype_threshold'] = parameters['hype_threshold_5m']
        if 'wave_min_strength_5m' in parameters:
            fwh_config['timeframe_specific']['5m']['wave_min_strength'] = parameters['wave_min_strength_5m']
        if 'quantum_boost_5m' in parameters:
            fwh_config['timeframe_specific']['5m']['quantum_boost_factor'] = parameters['quantum_boost_5m']
        if 'holographic_weight_5m' in parameters:
            fwh_config['timeframe_specific']['5m']['holographic_weight'] = parameters['holographic_weight_5m']
        
        # OTOC Configuration
        if 'otoc_max_threshold' in parameters:
            fwh_config['multi_timeframe_config']['otoc_config']['max_threshold'] = parameters['otoc_max_threshold']
        if 'otoc_beta' in parameters:
            fwh_config['multi_timeframe_config']['otoc_config']['adaptive_threshold']['beta'] = parameters['otoc_beta']
        
        # Trading levels
        if 'stop_loss_pct' in parameters:
            config['trading_system']['risk_management']['stop_loss_pct'] = parameters['stop_loss_pct']
        if 'take_profit_pct' in parameters:
            config['trading_system']['risk_management']['take_profit_pct'] = parameters['take_profit_pct']
        
        # Timeframe weights
        if 'weight_1m' in parameters:
            fwh_config['multi_timeframe_config']['timeframe_weights']['1m'] = parameters['weight_1m']
            config['trading_system']['timeframe_weights']['1m'] = parameters['weight_1m']
        if 'weight_5m' in parameters:
            fwh_config['multi_timeframe_config']['timeframe_weights']['5m'] = parameters['weight_5m']
            config['trading_system']['timeframe_weights']['5m'] = parameters['weight_5m']
        
        return config
    
    def save_config(self, config: Dict[str, Any], output_path: str, parameters: Dict[str, float]) -> None:
        """Salva configuração otimizada com comentários."""
        # Adicionar header com informações da calibração
        header_comment = f"""# FWH Scalp Config - CALIBRADO AUTOMATICAMENTE
# Gerado por: YAA FWH Scalp Calibrator
# Data: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# Parâmetros otimizados:
"""
        
        for param, value in parameters.items():
            header_comment += f"# - {param}: {value:.4f}\n"
        
        header_comment += "#\n# ATENÇÃO: Esta configuração foi otimizada para condições específicas\n"
        header_comment += "# Recomenda-se re-calibrar periodicamente\n\n"
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(header_comment)
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)

class FWHScalpCalibrator:
    """Sistema principal de calibração FWH Scalp com dados reais."""
    
    def __init__(self, base_config_path: str, output_dir: str = "calibration_results", calibration_mode: str = "fast"):
        self.base_config_path = base_config_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Componentes
        self.parameter_space = ParameterSpace(mode=calibration_mode)
        self.metrics_evaluator = MetricsEvaluator()
        self.config_generator = ConfigGenerator(base_config_path)
        
        # Gerenciador de dados reais
        self.market_data_manager = RealMarketDataManager()
        self.market_validated = False
        
        # Resultados
        self.calibration_results: List[CalibrationResult] = []
        self.best_result: Optional[CalibrationResult] = None
        
        # Estado
        self.start_time = None
        self.total_combinations = 0
        self.completed_combinations = 0
        
        # Configurações de dados reais
        self.real_data_config = {
            'min_data_quality_score': 0.7,
            'required_symbols': ['BTCUSDT', 'ETHUSDT', 'BNBUSDT'],
            'market_hours_only': False,  # Crypto opera 24/7
            'validate_spreads': True,
            'max_latency_ms': 3000
        }
        
        logger.info(f"FWH Scalp Calibrator inicializado")
        logger.info(f"Resultados serão salvos em: {self.output_dir}")
    
    async def initialize_real_market_data(self, api_key: str = None, api_secret: str = None) -> bool:
        """Inicializa conexão com dados reais de mercado."""
        try:
            logger.info("Inicializando conexão com dados reais de mercado...")
            
            # Inicializar gerenciador de dados
            if not await self.market_data_manager.initialize(api_key, api_secret):
                logger.error("Falha na inicialização do gerenciador de dados")
                return False
            
            # Validar condições de mercado
            if not await self._validate_market_conditions():
                logger.error("Condições de mercado inadequadas para calibração")
                return False
            
            self.market_validated = True
            logger.info("Dados reais de mercado validados e prontos")
            return True
            
        except Exception as e:
            logger.error(f"Erro na inicialização de dados reais: {e}")
            return False
    
    async def _validate_market_conditions(self) -> bool:
        """Valida se as condições de mercado são adequadas para calibração."""
        try:
            logger.info("Validando condições de mercado...")
            
            # Verificar símbolos obrigatórios
            validation_results = await self.market_data_manager.validate_market_conditions(
                self.real_data_config['required_symbols']
            )
            
            # Verificar se pelo menos 80% dos símbolos estão válidos
            valid_symbols = sum(validation_results.values())
            total_symbols = len(validation_results)
            validation_rate = valid_symbols / total_symbols if total_symbols > 0 else 0
            
            if validation_rate < 0.8:
                logger.warning(f"⚠️ Apenas {validation_rate:.1%} dos símbolos estão válidos")
                return False
            
            # Verificar latência da conexão
            latency_ok = await self._check_connection_latency()
            if not latency_ok:
                logger.warning("⚠️ Latência da conexão muito alta")
                return False
            
            # Verificar horário de mercado (se aplicável)
            if self.real_data_config['market_hours_only']:
                if not self._is_market_hours():
                    logger.warning("Fora do horário de mercado")
                    return False
            
            logger.info(f"Condições de mercado validadas: {valid_symbols}/{total_symbols} símbolos OK")
            return True
            
        except Exception as e:
            logger.error(f"Erro na validação de mercado: {e}")
            return False
    
    async def _check_connection_latency(self) -> bool:
        """Verifica latência da conexão com a exchange."""
        try:
            start_time = time.time()
            
            # Fazer uma requisição simples para medir latência
            ticker = await self.market_data_manager.get_ticker('BTCUSDT')
            
            latency_ms = (time.time() - start_time) * 1000
            
            if latency_ms > self.real_data_config['max_latency_ms']:
                logger.warning(f"Latência alta: {latency_ms:.0f}ms")
                return False
            
            logger.info(f"Latência da conexão: {latency_ms:.0f}ms")
            return True
            
        except Exception as e:
            logger.warning(f"Erro na verificação de latência: {e}")
            return False
    
    def _is_market_hours(self) -> bool:
        """Verifica se está dentro do horário de mercado."""
        # Para crypto, sempre retorna True (24/7)
        # Para outros mercados, implementar lógica específica
        return True
    
    async def validate_real_data_quality(self) -> float:
        """Valida qualidade dos dados em tempo real e retorna score."""
        try:
            if not self.market_validated:
                logger.warning("Mercado não foi validado ainda")
                return 0.0
            
            # Coletar dados de múltiplos símbolos
            quality_scores = []
            
            for symbol in self.real_data_config['required_symbols']:
                # Verificar ticker
                ticker = await self.market_data_manager.get_ticker(symbol)
                if not ticker:
                    quality_scores.append(0.0)
                    continue
                
                # Verificar orderbook
                orderbook = await self.market_data_manager.get_orderbook(symbol)
                if not orderbook:
                    quality_scores.append(0.5)
                    continue
                
                # Calcular score de qualidade para este símbolo
                symbol_score = 1.0
                
                # Penalizar spread alto
                spread = orderbook.get('spread', 1.0)
                if spread > 0.005:  # Mais de 0.5%
                    symbol_score *= 0.8
                
                # Penalizar baixa liquidez
                bid_depth = len(orderbook.get('bids', []))
                ask_depth = len(orderbook.get('asks', []))
                if bid_depth < 5 or ask_depth < 5:
                    symbol_score *= 0.7
                
                quality_scores.append(symbol_score)
            
            overall_quality = np.mean(quality_scores) if quality_scores else 0.0
            
            logger.info(f"Qualidade dos dados: {overall_quality:.2%}")
            return overall_quality
            
        except Exception as e:
            logger.error(f"Erro na validação de qualidade: {e}")
            return 0.0
    
    async def calibrate(self, 
                       strategy: str = "grid_search",
                       max_combinations: Optional[int] = None,
                       test_duration_minutes: int = 15,
                       parallel_tests: int = 1,
                       api_key: str = None,
                       api_secret: str = None) -> CalibrationResult:
        """Executa calibração completa com dados reais de mercado."""
        
        logger.info(f"[INICIO] Calibração FWH Scalp com dados reais")
        logger.info(f"[ESTRATEGIA] {strategy}")
        logger.info(f"[DURACAO] {test_duration_minutes} minutos por teste")
        
        self.start_time = datetime.now()
        
        try:
            # Inicializar dados reais de mercado
            if not await self.initialize_real_market_data(api_key, api_secret):
                raise Exception("Falha na inicialização de dados reais de mercado")
            
            # Validar qualidade inicial dos dados
            initial_quality = await self.validate_real_data_quality()
            if initial_quality < self.real_data_config['min_data_quality_score']:
                logger.warning(f"⚠️ Qualidade dos dados abaixo do mínimo: {initial_quality:.2%}")
                logger.warning("Continuando com calibração, mas resultados podem ser comprometidos")
            
            if strategy == "grid_search":
                await self._grid_search_calibration(max_combinations, test_duration_minutes, parallel_tests)
            elif strategy == "adaptive":
                await self._adaptive_calibration(max_combinations, test_duration_minutes)
            else:
                raise ValueError(f"Estratégia não suportada: {strategy}")
            
            # Processar resultados
            self._process_results()
            
            # Gerar relatórios com métricas de dados reais
            await self._generate_reports_with_real_data_metrics()
            
            logger.info(f"[OK] Calibração com dados reais concluída!")
            if self.best_result:
                logger.info(f"[MELHOR] Score: {self.best_result.score:.4f}")
                logger.info(f"[WIN RATE] {self.best_result.metrics.get('win_rate', 0):.1f}%")
                logger.info(f"[PROFIT] Factor: {self.best_result.metrics.get('profit_factor', 0):.2f}")
                logger.info(f"[QUALIDADE] Dados: {self.best_result.metrics.get('data_quality_score', 0):.2%}")
            
            return self.best_result
            
        except Exception as e:
            logger.error(f"[ERRO] Calibração com dados reais: {e}")
            raise
        finally:
            # Cleanup
            try:
                await self.market_data_manager.cleanup()
            except:
                pass
    
    async def _grid_search_calibration(self, 
                                     max_combinations: Optional[int],
                                     test_duration_minutes: int,
                                     parallel_tests: int) -> None:
        """Executa calibração por grid search."""
        
        # Gerar combinações
        combinations = self.parameter_space.generate_combinations(max_combinations)
        self.total_combinations = len(combinations)
        
        logger.info(f"[GRID SEARCH] {self.total_combinations} combinações")
        
        # Executar testes
        if parallel_tests > 1:
            await self._run_parallel_tests(combinations, test_duration_minutes, parallel_tests)
        else:
            await self._run_sequential_tests(combinations, test_duration_minutes)
    
    async def _run_sequential_tests(self, combinations: List[Dict[str, float]], test_duration_minutes: int) -> None:
        """Executa testes sequencialmente."""
        
        for i, params in enumerate(combinations):
            logger.info(f"[TESTE] {i+1}/{len(combinations)} ({(i+1)/len(combinations)*100:.1f}%)")
            
            try:
                result = await self._test_configuration(params, test_duration_minutes)
                if result:
                    self.calibration_results.append(result)
                    logger.info(f"[SCORE] {result.score:.4f} | Sinais: {result.signals_generated} | Trades: {result.trades_executed}")
                
                self.completed_combinations += 1
                
            except Exception as e:
                logger.warning(f"[AVISO] Erro no teste {i+1}: {e}")
                continue
    
    async def _test_configuration(self, parameters: Dict[str, float], duration_minutes: int) -> Optional[CalibrationResult]:
        """Testa uma configuração específica."""
        
        config_id = f"test_{int(time.time())}_{hash(str(parameters)) % 10000}"
        
        try:
            # Gerar configuração
            config = self.config_generator.generate_config(parameters)
            
            # Salvar configuração temporária
            temp_config_path = self.output_dir / f"{config_id}.yaml"
            with open(temp_config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
            # Executar paper trading
            metrics = await self._run_paper_trading(str(temp_config_path), duration_minutes)
            
            # Limpar arquivo temporário
            temp_config_path.unlink()
            
            if not metrics:
                return None
            
            # Calcular score
            score = self.metrics_evaluator.calculate_score(metrics)
            
            # Criar resultado
            result = CalibrationResult(
                config_id=config_id,
                parameters=parameters,
                metrics=metrics,
                score=score,
                duration_minutes=duration_minutes,
                signals_generated=metrics.get('signals_generated', 0),
                trades_executed=metrics.get('trades_executed', 0),
                timestamp=datetime.now().isoformat()
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Erro ao testar configuração {config_id}: {e}")
            return None
    
    async def _run_paper_trading(self, config_path: str, duration_minutes: int) -> Optional[Dict[str, float]]:
        """Executa paper trading com dados reais de mercado e retorna métricas."""
        
        try:
            # Importar sistema de paper trading
            from run_fwh_scalp_paper_trading import FWHScalpPaperTradingSystem
            
            # Inicializar sistema com dados reais
            system = FWHScalpPaperTradingSystem(config_path)
            
            # Configurar para usar dados reais
            system.use_real_market_data = True
            system.calibration_mode = True
            
            await system.initialize_system()
            
            # Verificar conexão com dados reais
            if not await self._verify_real_data_connection(system):
                logger.error("[ERRO] Falha na conexão com dados reais de mercado")
                return None
            
            logger.info(f"[INFO] Iniciando calibração com dados reais por {duration_minutes} minutos")
            
            # Executar por tempo determinado
            start_time = time.time()
            end_time = start_time + (duration_minutes * 60)
            
            signals_count = 0
            trades_count = 0
            initial_balance = system.current_balance
            peak_balance = initial_balance
            max_drawdown = 0.0
            
            # Métricas de qualidade dos dados
            data_quality_checks = {
                'price_updates': 0,
                'volume_updates': 0,
                'spread_checks': 0,
                'latency_measurements': []
            }
            
            while time.time() < end_time and system.running:
                try:
                    cycle_start = time.time()
                    
                    # Verificar qualidade dos dados em tempo real
                    await self._check_data_quality(system, data_quality_checks)
                    
                    # Executar ciclo de trading com dados reais
                    await system.run_trading_cycle()
                    
                    # Medir latência do ciclo
                    cycle_latency = (time.time() - cycle_start) * 1000  # ms
                    data_quality_checks['latency_measurements'].append(cycle_latency)
                    
                    # Contar sinais e trades
                    current_signals = getattr(system, 'signals_generated', 0)
                    current_trades = getattr(system, 'trades_executed', 0)
                    
                    signals_count = max(signals_count, current_signals)
                    trades_count = max(trades_count, current_trades)
                    
                    # Calcular drawdown em tempo real
                    current_balance = system.current_balance + system.unrealized_pnl
                    peak_balance = max(peak_balance, current_balance)
                    current_drawdown = (peak_balance - current_balance) / peak_balance if peak_balance > 0 else 0
                    max_drawdown = max(max_drawdown, current_drawdown)
                    
                    # Early stopping se não há atividade ou problemas de dados
                    if time.time() - start_time > 300 and signals_count == 0:  # 5 minutos sem sinais
                        logger.warning("Early stopping: sem sinais em 5 minutos")
                        break
                    
                    # Verificar qualidade dos dados
                    if not await self._validate_data_quality(data_quality_checks):
                        logger.warning("Qualidade dos dados comprometida, interrompendo teste")
                        break
                    
                    await asyncio.sleep(3)  # 3 segundos entre ciclos para dados reais
                    
                except Exception as e:
                    logger.warning(f"Erro no ciclo de trading: {e}")
                    continue
            
            # Parar sistema
            system.running = False
            
            # Coletar métricas finais com validação de dados reais
            final_metrics = system.get_performance_metrics()
            
            # Calcular métricas adicionais com dados reais
            actual_duration = (time.time() - start_time) / 60  # em minutos
            signals_per_hour = (signals_count / actual_duration) * 60 if actual_duration > 0 else 0
            
            # Métricas de qualidade dos dados
            avg_latency = np.mean(data_quality_checks['latency_measurements']) if data_quality_checks['latency_measurements'] else 0
            data_quality_score = self._calculate_data_quality_score(data_quality_checks, actual_duration)
            
            metrics = {
                'win_rate': final_metrics.get('win_rate_pct', 0),
                'profit_factor': final_metrics.get('profit_factor', 0),
                'max_drawdown': max_drawdown,
                'total_return': final_metrics.get('total_return_pct', 0) / 100,
                'signals_generated': signals_count,
                'trades_executed': trades_count,
                'signals_per_hour': signals_per_hour,
                'sharpe_ratio': final_metrics.get('sharpe_ratio', 0),
                'duration_minutes': actual_duration,
                'avg_latency_ms': avg_latency,
                'data_quality_score': data_quality_score,
                'real_market_conditions': True  # Flag para indicar dados reais
            }
            
            logger.info(f"Teste concluído: {signals_count} sinais, {trades_count} trades, qualidade: {data_quality_score:.2f}")
            
            return metrics
            
        except Exception as e:
            logger.error(f"Erro no paper trading com dados reais: {e}")
            return None
    
    async def _verify_real_data_connection(self, system) -> bool:
        """Verifica se a conexão com dados reais está funcionando."""
        try:
            # Verificar se o sistema tem acesso a dados de mercado
            market_data_client = None
            if hasattr(system, 'market_data_manager'):
                market_data_client = system.market_data_manager
            elif hasattr(system, 'market_data_client'):
                market_data_client = system.market_data_client
            else:
                logger.error("Sistema não possui gerenciador de dados de mercado")
                return False
            
            # Testar conexão com exchange
            test_symbols = ['BTCUSDT', 'ETHUSDT']  # Símbolos de teste
            
            for symbol in test_symbols:
                try:
                    # Tentar obter dados recentes usando o método correto
                    if hasattr(market_data_client, 'get_ticker'):
                        ticker_data = await market_data_client.get_ticker(symbol)
                    elif hasattr(market_data_client, 'fetch_ticker'):
                        ticker_data = await market_data_client.fetch_ticker(symbol)
                    else:
                        logger.warning(f"Método de obtenção de ticker não encontrado")
                        continue
                        
                    if not ticker_data:
                        logger.warning(f"Dados inválidos para {symbol}")
                        continue
                    
                    # Verificar se o preço é realista (diferentes formatos possíveis)
                    price = None
                    if isinstance(ticker_data, dict):
                        price = float(ticker_data.get('price', ticker_data.get('last', 0)))
                    else:
                        price = float(getattr(ticker_data, 'price', getattr(ticker_data, 'last', 0)))
                        
                    if price <= 0:
                        logger.warning(f"Preço inválido para {symbol}: {price}")
                        continue
                    
                    logger.info(f"[OK] Conexão verificada: {symbol} = ${price:,.2f}")
                    return True
                    
                except Exception as e:
                    logger.warning(f"Erro ao verificar {symbol}: {e}")
                    continue
            
            logger.error("Falha na verificação de todos os símbolos de teste")
            return False
            
        except Exception as e:
            logger.error(f"Erro na verificação de conexão: {e}")
            return False
    
    async def _check_data_quality(self, system, quality_checks: Dict) -> None:
        """Verifica a qualidade dos dados em tempo real."""
        try:
            # Verificar atualizações de preço
            if hasattr(system, 'last_price_update'):
                time_since_update = time.time() - system.last_price_update
                if time_since_update < 10:  # Menos de 10 segundos
                    quality_checks['price_updates'] += 1
            
            # Verificar volume
            if hasattr(system, 'current_volume') and system.current_volume > 0:
                quality_checks['volume_updates'] += 1
            
            # Verificar spread
            if hasattr(system, 'current_spread'):
                spread_pct = system.current_spread * 100
                if spread_pct < 0.1:  # Spread menor que 0.1%
                    quality_checks['spread_checks'] += 1
            
        except Exception as e:
            logger.warning(f"Erro na verificação de qualidade: {e}")
    
    async def _validate_data_quality(self, quality_checks: Dict) -> bool:
        """Valida se a qualidade dos dados está adequada."""
        try:
            total_checks = len(quality_checks['latency_measurements'])
            if total_checks < 10:  # Muito poucos dados
                return True
            
            # Verificar taxa de atualizações de preço
            price_update_rate = quality_checks['price_updates'] / total_checks
            if price_update_rate < 0.8:  # Menos de 80% das verificações
                logger.warning(f"Taxa de atualização de preços baixa: {price_update_rate:.2%}")
                return False
            
            # Verificar latência média
            avg_latency = np.mean(quality_checks['latency_measurements'])
            if avg_latency > 5000:  # Mais de 5 segundos
                logger.warning(f"Latência muito alta: {avg_latency:.0f}ms")
                return False
            
            return True
            
        except Exception as e:
            logger.warning(f"Erro na validação de qualidade: {e}")
            return True  # Assumir OK em caso de erro
    
    def _calculate_data_quality_score(self, quality_checks: Dict, duration_minutes: float) -> float:
        """Calcula um score de qualidade dos dados (0-1)."""
        try:
            if not quality_checks['latency_measurements']:
                return 0.0
            
            total_checks = len(quality_checks['latency_measurements'])
            expected_checks = duration_minutes * 20  # ~20 verificações por minuto
            
            # Score baseado em múltiplos fatores
            scores = []
            
            # 1. Taxa de atualizações de preço
            price_rate = min(1.0, quality_checks['price_updates'] / max(1, total_checks))
            scores.append(price_rate * 0.3)
            
            # 2. Taxa de atualizações de volume
            volume_rate = min(1.0, quality_checks['volume_updates'] / max(1, total_checks))
            scores.append(volume_rate * 0.2)
            
            # 3. Qualidade do spread
            spread_rate = min(1.0, quality_checks['spread_checks'] / max(1, total_checks))
            scores.append(spread_rate * 0.2)
            
            # 4. Latência (inverso)
            avg_latency = np.mean(quality_checks['latency_measurements'])
            latency_score = max(0, 1 - (avg_latency / 5000))  # Normalizar para 5s
            scores.append(latency_score * 0.2)
            
            # 5. Consistência de dados
            consistency_score = min(1.0, total_checks / max(1, expected_checks))
            scores.append(consistency_score * 0.1)
            
            return sum(scores)
            
        except Exception as e:
            logger.warning(f"Erro no cálculo de qualidade: {e}")
            return 0.5  # Score médio em caso de erro
    
    async def _generate_reports_with_real_data_metrics(self) -> None:
        """Gera relatórios incluindo métricas de dados reais."""
        try:
            # Gerar relatório padrão
            await self._generate_reports()
            
            # Adicionar métricas específicas de dados reais
            real_data_report = {
                'market_validation': {
                    'connection_status': self.market_data_manager.get_connection_status(),
                    'validated_symbols': self.real_data_config['required_symbols'],
                    'data_quality_threshold': self.real_data_config['min_data_quality_score']
                },
                'data_quality_metrics': {
                    'avg_latency_ms': np.mean([r.metrics.get('avg_latency_ms', 0) for r in self.calibration_results if r.metrics.get('avg_latency_ms')]) if self.calibration_results else 0,
                    'avg_data_quality_score': np.mean([r.metrics.get('data_quality_score', 0) for r in self.calibration_results if r.metrics.get('data_quality_score')]) if self.calibration_results else 0,
                    'real_market_tests': len([r for r in self.calibration_results if r.metrics.get('real_market_conditions')])
                },
                'market_conditions_summary': {
                    'test_period': {
                        'start': self.start_time.isoformat() if self.start_time else None,
                        'end': datetime.now().isoformat()
                    },
                    'total_tests_with_real_data': len([r for r in self.calibration_results if r.metrics.get('real_market_conditions')])
                }
            }
            
            # Salvar relatório de dados reais
            real_data_report_path = self.output_dir / "real_data_metrics_report.json"
            with open(real_data_report_path, 'w', encoding='utf-8') as f:
                json.dump(real_data_report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"[RELATORIO] Dados reais salvo em: {real_data_report_path}")
            
        except Exception as e:
            logger.error(f"Erro ao gerar relatório de dados reais: {e}")
    
    def _process_results(self) -> None:
        """Processa resultados e encontra o melhor."""
        
        if not self.calibration_results:
            logger.warning("[AVISO] Nenhum resultado válido encontrado")
            return
        
        # Ordenar por score
        self.calibration_results.sort(key=lambda x: x.score, reverse=True)
        self.best_result = self.calibration_results[0]
        
        # Estatísticas
        scores = [r.score for r in self.calibration_results]
        valid_results = len([r for r in self.calibration_results if r.score > 0])
        
        logger.info(f"[RESULTADOS] Processados:")
        logger.info(f"   Total: {len(self.calibration_results)}")
        logger.info(f"   Válidos: {valid_results}")
        logger.info(f"   Score médio: {np.mean(scores):.4f}")
        logger.info(f"   Score máximo: {np.max(scores):.4f}")
    
    async def _generate_reports(self) -> None:
        """Gera relatórios detalhados."""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Salvar resultados em JSON
        results_file = self.output_dir / f"calibration_results_{timestamp}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            results_data = {
                'calibration_info': {
                    'start_time': self.start_time.isoformat() if self.start_time else None,
                    'end_time': datetime.now().isoformat(),
                    'total_combinations': self.total_combinations,
                    'completed_combinations': self.completed_combinations,
                    'valid_results': len([r for r in self.calibration_results if r.score > 0])
                },
                'results': [asdict(r) for r in self.calibration_results]
            }
            json.dump(results_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"[SALVOS] Resultados: {results_file}")
        
        # Gerar configuração otimizada
        if self.best_result:
            optimized_config = self.config_generator.generate_config(self.best_result.parameters)
            optimized_config_file = self.output_dir / f"fwh_scalp_config_optimized_{timestamp}.yaml"
            self.config_generator.save_config(optimized_config, str(optimized_config_file), self.best_result.parameters)
            
            logger.info(f"[CONFIG] Otimizada: {optimized_config_file}")
        
        # Gerar relatório de análise
        await self._generate_analysis_report(timestamp)
    
    async def _generate_analysis_report(self, timestamp: str) -> None:
        """Gera relatório de análise detalhado."""
        
        if not self.calibration_results:
            return
        
        report_file = self.output_dir / f"calibration_analysis_{timestamp}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"# FWH Scalp Calibration Analysis Report\n\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Resumo executivo
            f.write("## Executive Summary\n\n")
            if self.best_result:
                f.write(f"- **Best Score:** {self.best_result.score:.4f}\n")
                f.write(f"- **Win Rate:** {self.best_result.metrics.get('win_rate', 0):.1f}%\n")
                f.write(f"- **Profit Factor:** {self.best_result.metrics.get('profit_factor', 0):.2f}\n")
                f.write(f"- **Signals/Hour:** {self.best_result.metrics.get('signals_per_hour', 0):.1f}\n")
                f.write(f"- **Max Drawdown:** {self.best_result.metrics.get('max_drawdown', 0)*100:.2f}%\n\n")
            
            # Top 10 resultados
            f.write("## Top 10 Results\n\n")
            f.write("| Rank | Score | Win Rate | Profit Factor | Signals/Hour | Max DD |\n")
            f.write("|------|-------|----------|---------------|--------------|--------|\n")
            
            for i, result in enumerate(self.calibration_results[:10]):
                f.write(f"| {i+1} | {result.score:.4f} | {result.metrics.get('win_rate', 0):.1f}% | "
                       f"{result.metrics.get('profit_factor', 0):.2f} | {result.metrics.get('signals_per_hour', 0):.1f} | "
                       f"{result.metrics.get('max_drawdown', 0)*100:.2f}% |\n")
            
            # Parâmetros otimizados
            if self.best_result:
                f.write("\n## Optimized Parameters\n\n")
                for param, value in self.best_result.parameters.items():
                    f.write(f"- **{param}:** {value:.4f}\n")
            
            # Estatísticas
            f.write("\n## Statistics\n\n")
            scores = [r.score for r in self.calibration_results if r.score > 0]
            if scores:
                f.write(f"- **Total Tests:** {len(self.calibration_results)}\n")
                f.write(f"- **Valid Results:** {len(scores)}\n")
                f.write(f"- **Average Score:** {np.mean(scores):.4f}\n")
                f.write(f"- **Std Deviation:** {np.std(scores):.4f}\n")
                f.write(f"- **Min Score:** {np.min(scores):.4f}\n")
                f.write(f"- **Max Score:** {np.max(scores):.4f}\n")
        
        logger.info(f"[ANALISE] Relatório: {report_file}")

async def main():
    """Função principal CLI."""
    
    parser = argparse.ArgumentParser(description='FWH Scalp Calibrator com Dados Reais')
    parser.add_argument('--config', default='../config/fwh_scalp_config_optimized.yaml', help='Caminho da configuração base')
    parser.add_argument('--output', default='calibration_results', help='Diretório de saída')
    parser.add_argument('--strategy', choices=['grid_search', 'adaptive'], default='grid_search', help='Estratégia de calibração')
    parser.add_argument('--mode', choices=['fast', 'balanced', 'full'], default='fast', help='Modo de calibração (fast=27, balanced=243, full=3125 combinações)')
    parser.add_argument('--max-combinations', type=int, help='Máximo de combinações para testar')
    parser.add_argument('--test-duration', type=int, default=15, help='Duração de cada teste em minutos')
    parser.add_argument('--parallel', type=int, default=1, help='Número de testes paralelos')
    
    # Argumentos para dados reais da Binance
    parser.add_argument('--api-key', help='Binance API Key para dados reais')
    parser.add_argument('--api-secret', help='Binance API Secret para dados reais')
    parser.add_argument('--demo-mode', action='store_true', help='Modo demonstração sem dados reais')
    
    args = parser.parse_args()
    
    print("[YAA] FWH Scalp Calibrator")
    print("=" * 50)
    print("[QUALIA] Quantum Consciousness")
    print("[SISTEMA] Calibração Automática com Dados Reais")
    print("=" * 50)
    
    # Verificar credenciais para dados reais
    if not args.demo_mode and (not args.api_key or not args.api_secret):
        # Tentar carregar credenciais do .env
        api_key_env = os.environ.get('BINANCE_API_KEY')
        api_secret_env = os.environ.get('BINANCE_API_SECRET')
        
        if api_key_env and api_secret_env:
            args.api_key = api_key_env
            args.api_secret = api_secret_env
            print("[OK] Credenciais da Binance carregadas do arquivo .env")
        else:
            print("[AVISO] Credenciais da Binance não encontradas")
            print("[DICA] Configure BINANCE_API_KEY e BINANCE_API_SECRET no .env")
            print("[DICA] Ou use --api-key e --api-secret para dados reais")
            print("[MODO] Executando em modo demonstração...")
            args.demo_mode = True
    
    try:
        # Verificar se arquivo de configuração existe
        if not Path(args.config).exists():
            print(f"[ERRO] Arquivo de configuração não encontrado: {args.config}")
            sys.exit(1)
        
        # Inicializar calibrador
        calibrator = FWHScalpCalibrator(args.config, args.output, calibration_mode=args.mode)
        
        # Mostrar informações do modo selecionado
        mode_info = {
            'fast': '[RAPIDO] ~27 combinações (5-15 min)',
            'balanced': '[BALANCEADO] ~243 combinações (30-60 min)', 
            'full': '[COMPLETO] ~3125 combinações (3-8 horas)'
        }
        print(f"\n{mode_info.get(args.mode, 'Modo desconhecido')}")
        print(f"[LIMITE] Máximo: {args.max_combinations or 1000} combinações")
        
        # Executar calibração com ou sem dados reais
        if args.demo_mode:
            print("[DEMO] Executando calibração em modo demonstração...")
            result = await calibrator.calibrate(
                strategy=args.strategy,
                max_combinations=args.max_combinations,
                test_duration_minutes=args.test_duration,
                parallel_tests=args.parallel
            )
        else:
            print("[REAL] Executando calibração com dados reais da Binance...")
            result = await calibrator.calibrate(
                strategy=args.strategy,
                max_combinations=args.max_combinations,
                test_duration_minutes=args.test_duration,
                parallel_tests=args.parallel,
                api_key=args.api_key,
                api_secret=args.api_secret
            )
        
        if result:
            print("\n[SUCESSO] CALIBRAÇÃO CONCLUÍDA!")
            print(f"[SCORE] Final: {result.score:.4f}")
            print(f"[WIN RATE] {result.metrics.get('win_rate', 0):.1f}%")
            print(f"[PROFIT] Factor: {result.metrics.get('profit_factor', 0):.2f}")
            print(f"[SINAIS] Por hora: {result.metrics.get('signals_per_hour', 0):.1f}")
            
            if not args.demo_mode:
                print(f"[LATENCIA] Média: {result.metrics.get('avg_latency_ms', 0):.1f}ms")
                print(f"[QUALIDADE] Score dados: {result.metrics.get('data_quality_score', 0):.3f}")
            
            print(f"[SALVOS] Resultados em: {calibrator.output_dir}")
        else:
            print("[FALHA] Calibração falhou - nenhum resultado válido")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n[PARADO] Calibração interrompida pelo usuário")
    except Exception as e:
        print(f"[ERRO FATAL] {e}")
        sys.exit(1)


# Exemplo de uso programático
async def example_usage():
    """Exemplo de como usar o calibrador programaticamente."""
    
    print("[EXEMPLO] Exemplo de Uso do FWH Scalp Calibrator")
    print("=" * 50)
    
    # Configuração
    config_path = "../config/fwh_scalp_config_optimized.yaml"
    output_dir = "calibration_results_example"
    
    # Suas credenciais da Binance (substitua pelos valores reais)
    api_key = "your_binance_api_key_here"
    api_secret = "your_binance_api_secret_here"
    
    try:
        # Inicializar calibrador em modo rápido
        calibrator = FWHScalpCalibrator(config_path, output_dir, calibration_mode="fast")
        
        print("[INICIO] Iniciando calibração com dados reais...")
        
        # Executar calibração com dados reais
        result = await calibrator.calibrate(
            strategy='grid_search',
            max_combinations=100,  # Teste rápido
            test_duration_minutes=10,
            parallel_tests=1,
            api_key=api_key,
            api_secret=api_secret
        )
        
        if result:
            print("\n[OK] Calibração concluída!")
            print(f"[MELHOR] Melhor configuração encontrada:")
            print(f"   Score: {result.score:.4f}")
            print(f"   Win Rate: {result.metrics.get('win_rate', 0):.1f}%")
            print(f"   Profit Factor: {result.metrics.get('profit_factor', 0):.2f}")
            print(f"   Sinais/Hora: {result.metrics.get('signals_per_hour', 0):.1f}")
            print(f"   Latência Média: {result.metrics.get('avg_latency_ms', 0):.1f}ms")
            
            print(f"\n[PARAMETROS] Parâmetros otimizados:")
            for param, value in result.parameters.items():
                print(f"   {param}: {value:.4f}")
                
        else:
            print("[ERROR] Nenhum resultado válido encontrado")
            
    except Exception as e:
        print(f"[ERROR] Erro: {e}")


if __name__ == "__main__":
    # Para uso via linha de comando
    asyncio.run(main())
    
    # Para testar o exemplo programático, descomente a linha abaixo:
    # asyncio.run(example_usage())