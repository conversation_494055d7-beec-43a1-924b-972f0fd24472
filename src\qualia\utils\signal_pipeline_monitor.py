"""Signal pipeline transparency and monitoring system.

This module provides comprehensive visibility into the signal processing pipeline,
including quality metrics, rejection reasons, performance analytics, and detailed
tracing of signal flow through the system.

Features:
- Real-time signal pipeline monitoring
- Quality metrics and scoring
- Rejection reason tracking and analysis
- Performance analytics and trends
- Signal flow visualization
- Comprehensive logging and debugging
"""

from __future__ import annotations

import asyncio
import time
import statistics
from typing import Any, Dict, List, Optional, Callable, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
from collections import deque, defaultdict
import uuid

from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class SignalStage(Enum):
    """Signal processing stages for pipeline tracking."""
    GENERATION = "generation"
    VALIDATION = "validation"
    FILTERING = "filtering"
    COMBINATION = "combination"
    QUALITY_CHECK = "quality_check"
    FINAL_OUTPUT = "final_output"


class RejectionReason(Enum):
    """Reasons for signal rejection."""
    LOW_CONFIDENCE = "low_confidence"
    INSUFFICIENT_STRENGTH = "insufficient_strength"
    TIMING_CONFLICT = "timing_conflict"
    CONSISTENCY_FAILURE = "consistency_failure"
    ULTRA_CONSERVATIVE_FILTER = "ultra_conservative_filter"
    QUANTUM_THRESHOLD = "quantum_threshold"
    RISK_ASSESSMENT = "risk_assessment"
    MARKET_CONDITIONS = "market_conditions"
    TECHNICAL_VALIDATION = "technical_validation"
    DUPLICATE_SIGNAL = "duplicate_signal"
    STALE_DATA = "stale_data"
    SYSTEM_OVERRIDE = "system_override"


class SignalQuality(Enum):
    """Signal quality levels."""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    REJECTED = "rejected"


@dataclass
class SignalMetrics:
    """Comprehensive signal metrics."""
    signal_id: str
    symbol: str
    signal_type: str
    confidence: float
    strength: float
    quality_score: float
    latency_ms: float
    source_components: List[str]
    
    # Processing metrics
    generation_time: float
    validation_time: float
    filtering_time: float
    total_processing_time: float
    
    # Quality indicators
    consistency_score: float
    freshness_score: float
    reliability_score: float
    
    # Metadata
    timestamp: float
    stage: SignalStage
    is_rejected: bool = False
    rejection_reason: Optional[RejectionReason] = None


@dataclass
class PipelineStageMetrics:
    """Metrics for a specific pipeline stage."""
    stage: SignalStage
    signals_processed: int
    signals_passed: int
    signals_rejected: int
    average_processing_time: float
    rejection_reasons: Dict[RejectionReason, int]
    quality_distribution: Dict[SignalQuality, int]
    timestamp: float


class SignalPipelineMonitor:
    """Advanced signal pipeline monitoring and transparency system.
    
    This monitor provides:
    - Real-time pipeline visibility
    - Quality metrics and analytics
    - Rejection reason tracking
    - Performance monitoring
    - Signal flow analysis
    - Comprehensive debugging support
    """
    
    def __init__(
        self,
        name: str = "signal_pipeline_monitor",
        max_history_size: int = 10000,
        enable_detailed_logging: bool = True,
        quality_thresholds: Optional[Dict[str, float]] = None,
        performance_alert_thresholds: Optional[Dict[str, float]] = None,
    ):
        self.name = name
        self.max_history_size = max_history_size
        self.enable_detailed_logging = enable_detailed_logging
        
        # Quality thresholds
        self.quality_thresholds = quality_thresholds or {
            'excellent': 0.9,
            'good': 0.7,
            'acceptable': 0.5,
            'poor': 0.3
        }
        
        # Performance alert thresholds
        self.performance_thresholds = performance_alert_thresholds or {
            'max_processing_time_ms': 1000.0,
            'min_pass_rate': 0.3,
            'max_rejection_rate': 0.8
        }
        
        # Signal tracking
        self.signal_history: deque = deque(maxlen=max_history_size)
        self.active_signals: Dict[str, SignalMetrics] = {}
        
        # Stage metrics
        self.stage_metrics: Dict[SignalStage, PipelineStageMetrics] = {}
        self.stage_history: Dict[SignalStage, deque] = {
            stage: deque(maxlen=1000) for stage in SignalStage
        }
        
        # Performance tracking
        self.rejection_analytics: Dict[RejectionReason, deque] = {
            reason: deque(maxlen=1000) for reason in RejectionReason
        }
        self.quality_trends: deque = deque(maxlen=1000)
        self.throughput_history: deque = deque(maxlen=100)
        
        # Callbacks for alerts and notifications
        self.quality_callbacks: List[Callable] = []
        self.performance_callbacks: List[Callable] = []
        self.rejection_callbacks: List[Callable] = []
        
        # Adaptive thresholding system
        self.threshold_manager = AdaptiveThresholdManager(
            initial_thresholds={
                'confidence': 0.6,
                'strength': 0.5,
                'quality': 0.5,
                'consistency': 0.7
            },
            adaptation_rate=0.05,
            performance_window=50
        )

        # Statistics
        self.stats = {
            'total_signals_processed': 0,
            'total_signals_passed': 0,
            'total_signals_rejected': 0,
            'average_quality_score': 0.0,
            'average_processing_time_ms': 0.0,
            'quality_alerts_triggered': 0,
            'performance_alerts_triggered': 0,
            'threshold_adaptations': 0
        }

    def start_signal_tracking(
        self,
        signal_id: str,
        symbol: str,
        signal_type: str,
        initial_confidence: float,
        initial_strength: float,
        source_components: List[str]
    ) -> str:
        """Start tracking a new signal through the pipeline.
        
        Parameters
        ----------
        signal_id : str
            Unique signal identifier
        symbol : str
            Trading symbol
        signal_type : str
            Type of signal (buy, sell, hold)
        initial_confidence : float
            Initial confidence level
        initial_strength : float
            Initial signal strength
        source_components : List[str]
            Components that generated the signal
            
        Returns
        -------
        str
            Tracking ID for the signal
        """
        tracking_id = signal_id or str(uuid.uuid4())
        
        metrics = SignalMetrics(
            signal_id=tracking_id,
            symbol=symbol,
            signal_type=signal_type,
            confidence=initial_confidence,
            strength=initial_strength,
            quality_score=0.0,
            latency_ms=0.0,
            source_components=source_components,
            generation_time=time.time(),
            validation_time=0.0,
            filtering_time=0.0,
            total_processing_time=0.0,
            consistency_score=0.0,
            freshness_score=1.0,
            reliability_score=0.0,
            timestamp=time.time(),
            stage=SignalStage.GENERATION
        )
        
        self.active_signals[tracking_id] = metrics
        
        if self.enable_detailed_logging:
            logger.debug(
                f"Started tracking signal {tracking_id}: {symbol} {signal_type} "
                f"(confidence: {initial_confidence:.3f}, strength: {initial_strength:.3f})"
            )
        
        return tracking_id

    def update_signal_stage(
        self,
        tracking_id: str,
        stage: SignalStage,
        additional_metrics: Optional[Dict[str, Any]] = None
    ) -> None:
        """Update signal processing stage and metrics.
        
        Parameters
        ----------
        tracking_id : str
            Signal tracking ID
        stage : SignalStage
            Current processing stage
        additional_metrics : Dict, optional
            Additional metrics to update
        """
        if tracking_id not in self.active_signals:
            logger.warning(f"Unknown tracking ID: {tracking_id}")
            return
        
        metrics = self.active_signals[tracking_id]
        previous_stage = metrics.stage
        metrics.stage = stage
        
        # Update stage-specific timing
        current_time = time.time()
        if stage == SignalStage.VALIDATION:
            metrics.validation_time = current_time
        elif stage == SignalStage.FILTERING:
            metrics.filtering_time = current_time
        
        # Update additional metrics
        if additional_metrics:
            for key, value in additional_metrics.items():
                if hasattr(metrics, key):
                    setattr(metrics, key, value)
        
        # Calculate quality score
        metrics.quality_score = self._calculate_quality_score(metrics)

        # Update adaptive thresholds based on signal performance
        self.threshold_manager.update_signal_performance(metrics)

        # Update stage metrics
        self._update_stage_metrics(stage, metrics)
        
        if self.enable_detailed_logging:
            logger.debug(
                f"Signal {tracking_id} moved from {previous_stage.value} to {stage.value} "
                f"(quality: {metrics.quality_score:.3f})"
            )

    def reject_signal(
        self,
        tracking_id: str,
        reason: RejectionReason,
        details: Optional[str] = None
    ) -> None:
        """Mark signal as rejected with reason.
        
        Parameters
        ----------
        tracking_id : str
            Signal tracking ID
        reason : RejectionReason
            Reason for rejection
        details : str, optional
            Additional details about rejection
        """
        if tracking_id not in self.active_signals:
            logger.warning(f"Unknown tracking ID for rejection: {tracking_id}")
            return
        
        metrics = self.active_signals[tracking_id]
        metrics.is_rejected = True
        metrics.rejection_reason = reason
        
        # Record rejection
        self.rejection_analytics[reason].append({
            'timestamp': time.time(),
            'signal_id': tracking_id,
            'symbol': metrics.symbol,
            'signal_type': metrics.signal_type,
            'confidence': metrics.confidence,
            'stage': metrics.stage,
            'details': details
        })
        
        # Update statistics
        self.stats['total_signals_rejected'] += 1
        
        # Trigger rejection callbacks
        for callback in self.rejection_callbacks:
            try:
                callback(metrics, reason, details)
            except Exception as e:
                logger.warning(f"Rejection callback failed: {e}")
        
        if self.enable_detailed_logging:
            logger.info(
                f"Signal {tracking_id} rejected: {reason.value} "
                f"({metrics.symbol} {metrics.signal_type}, confidence: {metrics.confidence:.3f})"
                + (f" - {details}" if details else "")
            )

    def complete_signal_processing(
        self,
        tracking_id: str,
        final_confidence: Optional[float] = None,
        final_strength: Optional[float] = None,
        passed: bool = True
    ) -> Optional[SignalMetrics]:
        """Complete signal processing and finalize metrics.
        
        Parameters
        ----------
        tracking_id : str
            Signal tracking ID
        final_confidence : float, optional
            Final confidence level
        final_strength : float, optional
            Final signal strength
        passed : bool
            Whether signal passed all filters
            
        Returns
        -------
        SignalMetrics or None
            Final signal metrics
        """
        if tracking_id not in self.active_signals:
            logger.warning(f"Unknown tracking ID for completion: {tracking_id}")
            return None
        
        metrics = self.active_signals[tracking_id]
        
        # Update final metrics
        if final_confidence is not None:
            metrics.confidence = final_confidence
        if final_strength is not None:
            metrics.strength = final_strength
        
        # Calculate final processing time
        metrics.total_processing_time = time.time() - metrics.generation_time
        metrics.latency_ms = metrics.total_processing_time * 1000
        
        # Final quality calculation
        metrics.quality_score = self._calculate_quality_score(metrics)
        metrics.stage = SignalStage.FINAL_OUTPUT
        
        # Update statistics
        self.stats['total_signals_processed'] += 1
        if passed and not metrics.is_rejected:
            self.stats['total_signals_passed'] += 1
        
        # Update running averages
        self._update_running_averages(metrics)
        
        # Move to history
        self.signal_history.append(metrics)
        del self.active_signals[tracking_id]
        
        # Check for performance alerts
        self._check_performance_alerts(metrics)
        
        if self.enable_detailed_logging:
            status = "PASSED" if passed and not metrics.is_rejected else "REJECTED"
            logger.info(
                f"Signal {tracking_id} completed: {status} "
                f"(quality: {metrics.quality_score:.3f}, latency: {metrics.latency_ms:.1f}ms)"
            )
        
        return metrics

    def _calculate_quality_score(self, metrics: SignalMetrics) -> float:
        """Calculate comprehensive quality score for a signal."""
        # Base score from confidence and strength
        base_score = (metrics.confidence * 0.6 + metrics.strength * 0.4)
        
        # Freshness penalty (signals get stale over time)
        age_seconds = time.time() - metrics.generation_time
        freshness_penalty = min(age_seconds / 300.0, 0.3)  # Max 30% penalty over 5 minutes
        
        # Consistency bonus (based on source components)
        consistency_bonus = min(len(metrics.source_components) * 0.05, 0.2)  # Max 20% bonus
        
        # Processing time penalty (slower processing = lower quality)
        if metrics.total_processing_time > 0:
            latency_penalty = min(metrics.total_processing_time / 10.0, 0.2)  # Max 20% penalty
        else:
            latency_penalty = 0.0
        
        # Calculate final score
        quality_score = base_score + consistency_bonus - freshness_penalty - latency_penalty
        
        return max(0.0, min(1.0, quality_score))

    def _update_stage_metrics(self, stage: SignalStage, metrics: SignalMetrics) -> None:
        """Update metrics for a specific pipeline stage."""
        current_time = time.time()
        
        if stage not in self.stage_metrics:
            self.stage_metrics[stage] = PipelineStageMetrics(
                stage=stage,
                signals_processed=0,
                signals_passed=0,
                signals_rejected=0,
                average_processing_time=0.0,
                rejection_reasons=defaultdict(int),
                quality_distribution=defaultdict(int),
                timestamp=current_time
            )
        
        stage_metrics = self.stage_metrics[stage]
        stage_metrics.signals_processed += 1
        
        if metrics.is_rejected:
            stage_metrics.signals_rejected += 1
            if metrics.rejection_reason:
                stage_metrics.rejection_reasons[metrics.rejection_reason] += 1
        else:
            stage_metrics.signals_passed += 1
        
        # Update quality distribution
        quality_level = self._get_quality_level(metrics.quality_score)
        stage_metrics.quality_distribution[quality_level] += 1
        
        # Update processing time
        if metrics.total_processing_time > 0:
            # Exponential moving average
            alpha = 0.1
            stage_metrics.average_processing_time = (
                alpha * metrics.total_processing_time + 
                (1 - alpha) * stage_metrics.average_processing_time
            )
        
        stage_metrics.timestamp = current_time
        
        # Store in history
        self.stage_history[stage].append({
            'timestamp': current_time,
            'signals_processed': stage_metrics.signals_processed,
            'pass_rate': stage_metrics.signals_passed / max(1, stage_metrics.signals_processed),
            'average_quality': metrics.quality_score,
            'processing_time': metrics.total_processing_time
        })

    def _get_quality_level(self, quality_score: float) -> SignalQuality:
        """Determine quality level from score."""
        if quality_score >= self.quality_thresholds['excellent']:
            return SignalQuality.EXCELLENT
        elif quality_score >= self.quality_thresholds['good']:
            return SignalQuality.GOOD
        elif quality_score >= self.quality_thresholds['acceptable']:
            return SignalQuality.ACCEPTABLE
        elif quality_score >= self.quality_thresholds['poor']:
            return SignalQuality.POOR
        else:
            return SignalQuality.REJECTED

    def _update_running_averages(self, metrics: SignalMetrics) -> None:
        """Update running averages for statistics."""
        total_processed = self.stats['total_signals_processed']
        
        # Update average quality score
        current_avg_quality = self.stats['average_quality_score']
        self.stats['average_quality_score'] = (
            (current_avg_quality * (total_processed - 1) + metrics.quality_score) / total_processed
        )
        
        # Update average processing time
        current_avg_time = self.stats['average_processing_time_ms']
        self.stats['average_processing_time_ms'] = (
            (current_avg_time * (total_processed - 1) + metrics.latency_ms) / total_processed
        )
        
        # Update quality trends
        self.quality_trends.append({
            'timestamp': time.time(),
            'quality_score': metrics.quality_score,
            'confidence': metrics.confidence,
            'processing_time': metrics.latency_ms
        })

    def _check_performance_alerts(self, metrics: SignalMetrics) -> None:
        """Check for performance issues and trigger alerts."""
        alerts_triggered = []
        
        # Check processing time
        if metrics.latency_ms > self.performance_thresholds['max_processing_time_ms']:
            alerts_triggered.append(f"High latency: {metrics.latency_ms:.1f}ms")
        
        # Check overall pass rate
        if self.stats['total_signals_processed'] > 10:
            pass_rate = self.stats['total_signals_passed'] / self.stats['total_signals_processed']
            if pass_rate < self.performance_thresholds['min_pass_rate']:
                alerts_triggered.append(f"Low pass rate: {pass_rate:.1%}")
        
        # Check rejection rate
        if self.stats['total_signals_processed'] > 10:
            rejection_rate = self.stats['total_signals_rejected'] / self.stats['total_signals_processed']
            if rejection_rate > self.performance_thresholds['max_rejection_rate']:
                alerts_triggered.append(f"High rejection rate: {rejection_rate:.1%}")
        
        # Trigger alerts
        if alerts_triggered:
            self.stats['performance_alerts_triggered'] += len(alerts_triggered)
            
            for callback in self.performance_callbacks:
                try:
                    callback(metrics, alerts_triggered)
                except Exception as e:
                    logger.warning(f"Performance callback failed: {e}")
            
            if self.enable_detailed_logging:
                logger.warning(f"Performance alerts for {metrics.signal_id}: {', '.join(alerts_triggered)}")

    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get comprehensive pipeline status."""
        current_time = time.time()
        
        # Calculate recent throughput (last 5 minutes)
        recent_signals = [
            s for s in self.signal_history 
            if current_time - s.timestamp < 300
        ]
        
        throughput = len(recent_signals) / 5.0 if recent_signals else 0.0  # signals per minute
        
        # Calculate quality distribution
        quality_dist = defaultdict(int)
        for signal in list(self.signal_history)[-100:]:  # Last 100 signals
            quality_level = self._get_quality_level(signal.quality_score)
            quality_dist[quality_level.value] += 1
        
        # Calculate rejection reasons distribution
        rejection_dist = defaultdict(int)
        for reason, history in self.rejection_analytics.items():
            recent_rejections = [r for r in history if current_time - r['timestamp'] < 300]
            rejection_dist[reason.value] = len(recent_rejections)
        
        return {
            'name': self.name,
            'active_signals': len(self.active_signals),
            'total_processed': self.stats['total_signals_processed'],
            'pass_rate': self.stats['total_signals_passed'] / max(1, self.stats['total_signals_processed']),
            'average_quality': self.stats['average_quality_score'],
            'average_latency_ms': self.stats['average_processing_time_ms'],
            'current_throughput': throughput,
            'quality_distribution': dict(quality_dist),
            'rejection_reasons': dict(rejection_dist),
            'stage_metrics': {
                stage.value: {
                    'processed': metrics.signals_processed,
                    'passed': metrics.signals_passed,
                    'rejected': metrics.signals_rejected,
                    'avg_time': metrics.average_processing_time
                }
                for stage, metrics in self.stage_metrics.items()
            },
            'alerts_triggered': self.stats['performance_alerts_triggered']
        }

    def get_rejection_analysis(self, time_window: float = 3600.0) -> Dict[str, Any]:
        """Get detailed rejection analysis.
        
        Parameters
        ----------
        time_window : float
            Time window in seconds for analysis
            
        Returns
        -------
        Dict
            Comprehensive rejection analysis
        """
        current_time = time.time()
        cutoff_time = current_time - time_window
        
        analysis = {
            'time_window_hours': time_window / 3600.0,
            'rejection_reasons': {},
            'rejection_trends': {},
            'most_rejected_symbols': defaultdict(int),
            'most_rejected_signal_types': defaultdict(int)
        }
        
        for reason, history in self.rejection_analytics.items():
            recent_rejections = [r for r in history if r['timestamp'] > cutoff_time]
            
            analysis['rejection_reasons'][reason.value] = {
                'count': len(recent_rejections),
                'percentage': len(recent_rejections) / max(1, len(history)) * 100,
                'symbols': list(set(r['symbol'] for r in recent_rejections)),
                'average_confidence': statistics.mean([r['confidence'] for r in recent_rejections]) if recent_rejections else 0.0
            }
            
            # Track symbol and signal type rejections
            for rejection in recent_rejections:
                analysis['most_rejected_symbols'][rejection['symbol']] += 1
                analysis['most_rejected_signal_types'][rejection['signal_type']] += 1
        
        return analysis

    def add_quality_callback(self, callback: Callable) -> None:
        """Add callback for quality alerts."""
        self.quality_callbacks.append(callback)

    def add_performance_callback(self, callback: Callable) -> None:
        """Add callback for performance alerts."""
        self.performance_callbacks.append(callback)

    def add_rejection_callback(self, callback: Callable) -> None:
        """Add callback for rejection events."""
        self.rejection_callbacks.append(callback)

    def reset_statistics(self) -> None:
        """Reset all statistics and history."""
        self.signal_history.clear()
        self.active_signals.clear()
        self.stage_metrics.clear()
        
        for history in self.stage_history.values():
            history.clear()
        
        for history in self.rejection_analytics.values():
            history.clear()
        
        self.quality_trends.clear()
        self.throughput_history.clear()
        
        self.stats = {
            'total_signals_processed': 0,
            'total_signals_passed': 0,
            'total_signals_rejected': 0,
            'average_quality_score': 0.0,
            'average_processing_time_ms': 0.0,
            'quality_alerts_triggered': 0,
            'performance_alerts_triggered': 0
        }
        
        # Reset threshold manager
        self.threshold_manager.reset()

        logger.info(f"Signal pipeline monitor '{self.name}' statistics reset")

    def get_adaptive_thresholds(self) -> Dict[str, float]:
        """Get current adaptive thresholds."""
        return self.threshold_manager.get_current_thresholds()

    def get_threshold_adaptation_stats(self) -> Dict[str, Any]:
        """Get threshold adaptation statistics."""
        return self.threshold_manager.get_adaptation_stats()


class AdaptiveThresholdManager:
    """Manages adaptive thresholds based on signal performance and market conditions.

    This manager automatically adjusts thresholds for:
    - Signal confidence requirements
    - Quality score thresholds
    - Consistency requirements
    - Performance-based filtering

    Thresholds adapt based on:
    - Historical signal performance
    - Market volatility conditions
    - Success/failure patterns
    - System performance metrics
    """

    def __init__(
        self,
        initial_thresholds: Dict[str, float],
        adaptation_rate: float = 0.05,
        performance_window: int = 50,
        min_thresholds: Optional[Dict[str, float]] = None,
        max_thresholds: Optional[Dict[str, float]] = None,
        volatility_sensitivity: float = 0.1
    ):
        self.adaptation_rate = adaptation_rate
        self.performance_window = performance_window
        self.volatility_sensitivity = volatility_sensitivity

        # Current thresholds
        self.thresholds = initial_thresholds.copy()

        # Threshold bounds
        self.min_thresholds = min_thresholds or {
            'confidence': 0.3,
            'strength': 0.2,
            'quality': 0.2,
            'consistency': 0.4
        }
        self.max_thresholds = max_thresholds or {
            'confidence': 0.9,
            'strength': 0.9,
            'quality': 0.9,
            'consistency': 0.95
        }

        # Performance tracking
        self.signal_performance_history: deque = deque(maxlen=performance_window)
        self.threshold_history: Dict[str, deque] = {
            threshold: deque(maxlen=100) for threshold in self.thresholds.keys()
        }

        # Market condition tracking
        self.market_volatility_history: deque = deque(maxlen=50)
        self.performance_trends: Dict[str, deque] = {
            'success_rate': deque(maxlen=20),
            'quality_trend': deque(maxlen=20),
            'rejection_rate': deque(maxlen=20)
        }

        # Adaptation statistics
        self.adaptation_stats = {
            'total_adaptations': 0,
            'threshold_increases': defaultdict(int),
            'threshold_decreases': defaultdict(int),
            'performance_improvements': 0,
            'performance_degradations': 0
        }

    def update_signal_performance(self, signal_metrics: SignalMetrics) -> None:
        """Update performance tracking with new signal metrics.

        Parameters
        ----------
        signal_metrics : SignalMetrics
            Signal metrics to analyze for threshold adaptation
        """
        # Store signal performance
        performance_record = {
            'timestamp': time.time(),
            'confidence': signal_metrics.confidence,
            'strength': signal_metrics.strength,
            'quality_score': signal_metrics.quality_score,
            'is_rejected': signal_metrics.is_rejected,
            'rejection_reason': signal_metrics.rejection_reason,
            'processing_time': signal_metrics.total_processing_time,
            'consistency_score': signal_metrics.consistency_score
        }

        self.signal_performance_history.append(performance_record)

        # Update market volatility estimate
        self._update_market_volatility(signal_metrics)

        # Perform threshold adaptation if we have enough data
        if len(self.signal_performance_history) >= 10:
            self._adapt_thresholds()

    def _update_market_volatility(self, signal_metrics: SignalMetrics) -> None:
        """Update market volatility estimation."""
        # Simple volatility estimate based on signal confidence variance
        recent_confidences = [
            record['confidence'] for record in
            list(self.signal_performance_history)[-10:]
        ]

        if len(recent_confidences) > 1:
            volatility = statistics.stdev(recent_confidences)
            self.market_volatility_history.append(volatility)

    def _adapt_thresholds(self) -> None:
        """Adapt thresholds based on recent performance."""
        recent_performance = list(self.signal_performance_history)[-self.performance_window:]

        if len(recent_performance) < 10:
            return

        # Calculate performance metrics
        success_rate = len([p for p in recent_performance if not p['is_rejected']]) / len(recent_performance)
        average_quality = statistics.mean([p['quality_score'] for p in recent_performance])
        rejection_rate = 1.0 - success_rate

        # Update performance trends
        self.performance_trends['success_rate'].append(success_rate)
        self.performance_trends['quality_trend'].append(average_quality)
        self.performance_trends['rejection_rate'].append(rejection_rate)

        # Determine adaptation strategy
        adaptation_needed = self._determine_adaptation_strategy(
            success_rate, average_quality, rejection_rate
        )

        if adaptation_needed:
            self._apply_threshold_adaptations(adaptation_needed)

    def _determine_adaptation_strategy(
        self,
        success_rate: float,
        average_quality: float,
        rejection_rate: float
    ) -> Dict[str, float]:
        """Determine which thresholds need adaptation and by how much."""
        adaptations = {}

        # Target performance ranges
        target_success_rate = 0.4  # 40% of signals should pass
        target_quality = 0.6       # Average quality should be 60%
        max_rejection_rate = 0.7   # Max 70% rejection rate

        # Confidence threshold adaptation
        if success_rate < target_success_rate * 0.8:  # Too few signals passing
            # Lower confidence threshold to allow more signals
            adaptations['confidence'] = -self.adaptation_rate
        elif success_rate > target_success_rate * 1.3:  # Too many signals passing
            # Raise confidence threshold to be more selective
            adaptations['confidence'] = self.adaptation_rate

        # Quality threshold adaptation
        if average_quality < target_quality * 0.9:  # Quality too low
            # Raise quality threshold to improve signal quality
            adaptations['quality'] = self.adaptation_rate * 0.5
        elif average_quality > target_quality * 1.2:  # Quality very high
            # Can lower quality threshold slightly to allow more signals
            adaptations['quality'] = -self.adaptation_rate * 0.3

        # Strength threshold adaptation based on rejection patterns
        recent_rejections = [
            p for p in list(self.signal_performance_history)[-20:]
            if p['is_rejected'] and p['rejection_reason'] == RejectionReason.INSUFFICIENT_STRENGTH
        ]

        if len(recent_rejections) > 5:  # Many strength-based rejections
            adaptations['strength'] = -self.adaptation_rate * 0.5

        # Market volatility adjustment
        if self.market_volatility_history:
            current_volatility = statistics.mean(list(self.market_volatility_history)[-5:])
            if current_volatility > 0.2:  # High volatility
                # Be more conservative in volatile markets
                for threshold in ['confidence', 'quality', 'consistency']:
                    if threshold in adaptations:
                        adaptations[threshold] += self.volatility_sensitivity
                    else:
                        adaptations[threshold] = self.volatility_sensitivity

        return adaptations

    def _apply_threshold_adaptations(self, adaptations: Dict[str, float]) -> None:
        """Apply threshold adaptations with bounds checking."""
        for threshold_name, adaptation in adaptations.items():
            if threshold_name not in self.thresholds:
                continue

            old_value = self.thresholds[threshold_name]
            new_value = old_value + adaptation

            # Apply bounds
            min_val = self.min_thresholds.get(threshold_name, 0.0)
            max_val = self.max_thresholds.get(threshold_name, 1.0)
            new_value = max(min_val, min(new_value, max_val))

            # Only update if change is significant
            if abs(new_value - old_value) > 0.01:
                self.thresholds[threshold_name] = new_value

                # Record adaptation
                self.adaptation_stats['total_adaptations'] += 1
                if new_value > old_value:
                    self.adaptation_stats['threshold_increases'][threshold_name] += 1
                else:
                    self.adaptation_stats['threshold_decreases'][threshold_name] += 1

                # Store in history
                self.threshold_history[threshold_name].append({
                    'timestamp': time.time(),
                    'old_value': old_value,
                    'new_value': new_value,
                    'adaptation': adaptation
                })

                logger.debug(
                    f"Adapted {threshold_name} threshold: {old_value:.3f} → {new_value:.3f} "
                    f"(change: {adaptation:+.3f})"
                )

    def get_current_thresholds(self) -> Dict[str, float]:
        """Get current adaptive thresholds."""
        return self.thresholds.copy()

    def get_adaptation_stats(self) -> Dict[str, Any]:
        """Get comprehensive adaptation statistics."""
        # Calculate recent adaptation frequency
        recent_adaptations = []
        for threshold_history in self.threshold_history.values():
            recent_adaptations.extend([
                h for h in threshold_history
                if time.time() - h['timestamp'] < 3600  # Last hour
            ])

        # Calculate performance trends
        trend_analysis = {}
        for metric, history in self.performance_trends.items():
            if len(history) >= 3:
                recent_values = list(history)[-3:]
                trend = (recent_values[-1] - recent_values[0]) / len(recent_values)
                trend_analysis[metric] = {
                    'current': recent_values[-1],
                    'trend': trend,
                    'improving': trend > 0.01 if metric != 'rejection_rate' else trend < -0.01
                }

        return {
            'current_thresholds': self.thresholds.copy(),
            'adaptation_stats': dict(self.adaptation_stats),
            'recent_adaptations_count': len(recent_adaptations),
            'performance_trends': trend_analysis,
            'market_volatility': statistics.mean(list(self.market_volatility_history)) if self.market_volatility_history else 0.0,
            'threshold_ranges': {
                threshold: {
                    'min': self.min_thresholds.get(threshold, 0.0),
                    'max': self.max_thresholds.get(threshold, 1.0),
                    'current': value,
                    'utilization': (value - self.min_thresholds.get(threshold, 0.0)) /
                                  (self.max_thresholds.get(threshold, 1.0) - self.min_thresholds.get(threshold, 0.0))
                }
                for threshold, value in self.thresholds.items()
            }
        }

    def reset(self) -> None:
        """Reset threshold manager to initial state."""
        # Reset to initial thresholds (stored in min_thresholds as backup)
        initial_thresholds = {
            'confidence': 0.6,
            'strength': 0.5,
            'quality': 0.5,
            'consistency': 0.7
        }
        self.thresholds = initial_thresholds.copy()

        # Clear history
        self.signal_performance_history.clear()
        for history in self.threshold_history.values():
            history.clear()
        self.market_volatility_history.clear()
        for history in self.performance_trends.values():
            history.clear()

        # Reset statistics
        self.adaptation_stats = {
            'total_adaptations': 0,
            'threshold_increases': defaultdict(int),
            'threshold_decreases': defaultdict(int),
            'performance_improvements': 0,
            'performance_degradations': 0
        }

        logger.info("Adaptive threshold manager reset to initial state")

    def force_threshold_update(self, threshold_updates: Dict[str, float]) -> None:
        """Force update specific thresholds (for manual override).

        Parameters
        ----------
        threshold_updates : Dict[str, float]
            Threshold updates to apply
        """
        for threshold_name, new_value in threshold_updates.items():
            if threshold_name in self.thresholds:
                old_value = self.thresholds[threshold_name]

                # Apply bounds
                min_val = self.min_thresholds.get(threshold_name, 0.0)
                max_val = self.max_thresholds.get(threshold_name, 1.0)
                new_value = max(min_val, min(new_value, max_val))

                self.thresholds[threshold_name] = new_value

                logger.info(f"Manually updated {threshold_name} threshold: {old_value:.3f} → {new_value:.3f}")
            else:
                logger.warning(f"Unknown threshold: {threshold_name}")
