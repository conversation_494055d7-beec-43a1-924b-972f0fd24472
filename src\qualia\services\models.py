"""
Modelos Pydantic para o BayesOpt Microservice.
Definições de request/response para endpoints REST.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


class SuggestRequest(BaseModel):
    """Request para endpoint /suggest - solicita novos parâmetros."""
    study_name: str = Field(..., description="Nome único do estudo")
    symbol: str = Field(..., description="Símbolo para otimização (ex: BTCUSDT)")
    
    # Search space bounds
    price_amp_range: tuple[float, float] = Field(default=(1.0, 10.0), description="Range para price_amplification")
    news_amp_range: tuple[float, float] = Field(default=(1.0, 15.0), description="Range para news_amplification")
    min_conf_range: tuple[float, float] = Field(default=(0.20, 0.80), description="Range para min_confidence")
    
    # Optuna configuration
    sampler_type: str = Field(default="TPE", description="Tipo de sampler (TPE, Random, CmaEs)")
    pruning_enabled: bool = Field(default=True, description="Habilitar pruning")
    n_startup_trials: int = Field(default=10, description="Número de trials de startup")
    
    # Study metadata
    direction: str = Field(default="maximize", description="Direção da otimização")
    load_if_exists: bool = Field(default=True, description="Carregar estudo existente se disponível")


class SuggestResponse(BaseModel):
    """Response do endpoint /suggest - retorna parâmetros sugeridos."""
    study_name: str
    symbol: str
    trial_number: int
    parameters: Dict[str, float] = Field(..., description="Parâmetros sugeridos")
    timestamp: datetime
    
    # Trial metadata
    trial_id: str = Field(..., description="ID único do trial")
    sampler_info: Dict[str, Any] = Field(default_factory=dict, description="Informações do sampler")


class ReportRequest(BaseModel):
    """Request para endpoint /report - reporta resultado de um trial."""
    study_name: str = Field(..., description="Nome do estudo")
    symbol: str = Field(..., description="Símbolo otimizado")
    trial_id: str = Field(..., description="ID do trial")
    
    # Objective value
    objective_value: float = Field(..., description="Valor da função objetivo")
    
    # Performance metrics
    metrics: Dict[str, float] = Field(..., description="Métricas de performance")
    
    # Execution metadata
    duration_seconds: float = Field(..., description="Duração da execução em segundos")
    success: bool = Field(default=True, description="Se a execução foi bem-sucedida")
    error_message: Optional[str] = Field(default=None, description="Mensagem de erro se houver")
    
    # Additional context
    evaluation_type: str = Field(default="realistic", description="Tipo de avaliação (realistic/fallback)")
    cost_ratio_pct: Optional[float] = Field(default=None, description="Percentual de custos de transação")


class ReportResponse(BaseModel):
    """Response do endpoint /report - confirma recebimento do resultado."""
    study_name: str
    symbol: str
    trial_id: str
    trial_number: int
    
    # Study status
    n_trials: int = Field(..., description="Número total de trials no estudo")
    best_value: Optional[float] = Field(default=None, description="Melhor valor objetivo até agora")
    best_parameters: Optional[Dict[str, float]] = Field(default=None, description="Melhores parâmetros até agora")
    
    # Pruning info
    should_prune: bool = Field(default=False, description="Se o trial deve ser podado")
    pruning_reason: Optional[str] = Field(default=None, description="Razão do pruning")
    
    timestamp: datetime


class StudyInfo(BaseModel):
    """Informações sobre um estudo."""
    study_name: str
    symbol: str
    direction: str
    n_trials: int
    best_value: Optional[float]
    best_parameters: Optional[Dict[str, float]]
    
    # Study metadata
    created_at: datetime
    last_updated: datetime
    sampler_type: str
    pruning_enabled: bool
    
    # Performance stats
    successful_trials: int
    failed_trials: int
    pruned_trials: int
    average_duration: float


class StudyListResponse(BaseModel):
    """Response para listar estudos."""
    studies: List[StudyInfo]
    total_count: int
    timestamp: datetime


class StudyStatsResponse(BaseModel):
    """Response com estatísticas detalhadas de um estudo."""
    study_info: StudyInfo
    
    # Trial history
    trial_history: List[Dict[str, Any]] = Field(..., description="Histórico de trials")
    
    # Performance evolution
    objective_evolution: List[tuple[int, float]] = Field(..., description="Evolução do objetivo por trial")
    parameter_evolution: Dict[str, List[tuple[int, float]]] = Field(..., description="Evolução dos parâmetros")
    
    # Optimization insights
    parameter_importance: Dict[str, float] = Field(default_factory=dict, description="Importância dos parâmetros")
    convergence_info: Dict[str, Any] = Field(default_factory=dict, description="Informações de convergência")


class HealthResponse(BaseModel):
    """Response do endpoint de health check."""
    status: str = Field(default="healthy", description="Status do serviço")
    timestamp: datetime
    version: str = Field(default="1.0.0", description="Versão do serviço")
    
    # Service stats
    uptime_seconds: float
    active_studies: int
    total_trials: int
    
    # Database status
    database_status: str = Field(default="connected", description="Status da conexão com database")
    database_size_mb: float = Field(default=0.0, description="Tamanho do database em MB")
    
    # Memory usage
    memory_usage_mb: float = Field(default=0.0, description="Uso de memória em MB")
    cpu_usage_percent: float = Field(default=0.0, description="Uso de CPU em %")


class ErrorResponse(BaseModel):
    """Response padrão para erros."""
    error: str = Field(..., description="Tipo do erro")
    message: str = Field(..., description="Mensagem de erro")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Detalhes adicionais do erro")
    timestamp: datetime
    request_id: Optional[str] = Field(default=None, description="ID da requisição para debugging")


class ServiceConfig(BaseModel):
    """Configuração do BayesOpt Service."""
    # Server settings
    host: str = Field(default="0.0.0.0", description="Host do servidor")
    port: int = Field(default=8080, description="Porta do servidor")
    workers: int = Field(default=1, description="Número de workers")
    
    # Database settings
    database_url: str = Field(default="sqlite:///data/bayes_service.db", description="URL do database")
    database_pool_size: int = Field(default=10, description="Tamanho do pool de conexões")
    
    # Study settings
    max_concurrent_studies: int = Field(default=100, description="Máximo de estudos simultâneos")
    study_timeout_hours: int = Field(default=24, description="Timeout para estudos inativos")
    auto_cleanup_enabled: bool = Field(default=True, description="Limpeza automática de estudos antigos")
    
    # Performance settings
    max_trials_per_study: int = Field(default=10000, description="Máximo de trials por estudo")
    trial_timeout_seconds: int = Field(default=300, description="Timeout por trial")
    
    # Logging settings
    log_level: str = Field(default="INFO", description="Nível de logging")
    log_file: Optional[str] = Field(default=None, description="Arquivo de log")
    
    # Security settings
    api_key_required: bool = Field(default=False, description="Requer API key")
    api_key: Optional[str] = Field(default=None, description="API key para autenticação")
    
    # Monitoring settings
    metrics_enabled: bool = Field(default=True, description="Habilitar métricas")
    health_check_interval: int = Field(default=60, description="Intervalo de health check em segundos")


# Aliases para compatibilidade
SuggestionRequest = SuggestRequest
SuggestionResponse = SuggestResponse
