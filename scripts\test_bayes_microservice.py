#!/usr/bin/env python3
"""
Testes para o BayesOpt Microservice - D-02 Implementation.
Testa endpoints REST, integração Optuna + SQLite, e cliente distribuído.
"""

import os
import sys
import asyncio
import time
import tempfile
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import pytest
import httpx
from fastapi.testclient import TestClient

from qualia.services.bayes_service import app, BayesOptService
from qualia.services.bayes_client import BayesOptClient, BayesClientConfig
from qualia.services.models import ServiceConfig, SuggestRequest, ReportRequest
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class TestBayesOptMicroservice:
    """Test suite for BayesOpt Microservice."""
    
    def setup_method(self):
        """Setup for each test."""
        # Create temporary database
        self.temp_db = tempfile.NamedTemporaryFile(suffix=".db", delete=False)
        self.temp_db.close()
        
        # Configure service with temp database
        self.config = ServiceConfig(
            database_url=f"sqlite:///{self.temp_db.name}",
            max_concurrent_studies=10,
            study_timeout_hours=1,
            auto_cleanup_enabled=False  # Disable for tests
        )
        
        # Create test client
        self.client = TestClient(app)
        
        logger.info(f"🧪 Test setup com database: {self.temp_db.name}")
    
    def teardown_method(self):
        """Cleanup after each test."""
        try:
            os.unlink(self.temp_db.name)
        except:
            pass
    
    def test_health_endpoint(self):
        """Test health check endpoint."""
        response = self.client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "uptime_seconds" in data
        assert data["active_studies"] >= 0
        
        logger.info("✅ Health endpoint funcionando")
    
    def test_root_endpoint(self):
        """Test root endpoint."""
        response = self.client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        assert data["service"] == "QUALIA BayesOpt Microservice"
        assert data["version"] == "1.0.0"
        assert "endpoints" in data
        
        logger.info("✅ Root endpoint funcionando")
    
    def test_suggest_parameters(self):
        """Test parameter suggestion endpoint."""
        request_data = {
            "study_name": "test_study",
            "symbol": "BTCUSDT",
            "price_amp_range": [1.0, 10.0],
            "news_amp_range": [1.0, 15.0],
            "min_conf_range": [0.2, 0.8],
            "sampler_type": "TPE",
            "pruning_enabled": True,
            "n_startup_trials": 5
        }
        
        response = self.client.post("/suggest", json=request_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["study_name"] == "test_study"
        assert data["symbol"] == "BTCUSDT"
        assert "parameters" in data
        assert "trial_id" in data
        assert "trial_number" in data
        
        # Check parameter bounds
        params = data["parameters"]
        assert 1.0 <= params["price_amplification"] <= 10.0
        assert 1.0 <= params["news_amplification"] <= 15.0
        assert 0.2 <= params["min_confidence"] <= 0.8
        
        logger.info(f"✅ Suggest endpoint funcionando: {params}")
        
        return data  # Return for use in report test
    
    def test_report_result(self):
        """Test result reporting endpoint."""
        # First get a suggestion
        suggest_data = self.test_suggest_parameters()
        
        # Report result
        report_data = {
            "study_name": "test_study",
            "symbol": "BTCUSDT",
            "trial_id": suggest_data["trial_id"],
            "objective_value": 1.5,
            "metrics": {
                "sharpe_ratio": 2.0,
                "pnl_24h": 1000.0,
                "cost_ratio_pct": 50.0
            },
            "duration_seconds": 10.5,
            "success": True,
            "evaluation_type": "realistic"
        }
        
        response = self.client.post("/report", json=report_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["study_name"] == "test_study"
        assert data["symbol"] == "BTCUSDT"
        assert data["trial_id"] == suggest_data["trial_id"]
        assert data["n_trials"] >= 1
        assert data["best_value"] is not None
        assert data["best_parameters"] is not None
        
        logger.info(f"✅ Report endpoint funcionando: best_value={data['best_value']}")
    
    def test_list_studies(self):
        """Test studies listing endpoint."""
        # Create a study first
        self.test_suggest_parameters()
        
        response = self.client.get("/studies")
        assert response.status_code == 200
        
        data = response.json()
        assert "studies" in data
        assert "total_count" in data
        assert data["total_count"] >= 1
        
        # Check study info
        study = data["studies"][0]
        assert study["study_name"] == "test_study"
        assert study["symbol"] == "BTCUSDT"
        assert study["n_trials"] >= 1
        
        logger.info(f"✅ List studies funcionando: {data['total_count']} studies")
    
    def test_get_study_info(self):
        """Test individual study info endpoint."""
        # Create a study first
        self.test_suggest_parameters()
        
        response = self.client.get("/studies/test_study/BTCUSDT")
        assert response.status_code == 200
        
        data = response.json()
        assert data["study_name"] == "test_study"
        assert data["symbol"] == "BTCUSDT"
        assert data["n_trials"] >= 1
        assert "created_at" in data
        assert "sampler_type" in data
        
        logger.info("✅ Study info endpoint funcionando")
    
    def test_multiple_trials(self):
        """Test multiple trials for optimization."""
        study_name = "multi_trial_test"
        symbol = "ETHUSDT"
        
        # Run multiple suggest/report cycles
        best_values = []
        for i in range(5):
            # Suggest parameters
            suggest_response = self.client.post("/suggest", json={
                "study_name": study_name,
                "symbol": symbol,
                "sampler_type": "TPE",
                "n_startup_trials": 2
            })
            assert suggest_response.status_code == 200
            suggest_data = suggest_response.json()
            
            # Simulate objective value (higher is better)
            objective_value = 1.0 + i * 0.1 + (suggest_data["parameters"]["news_amplification"] / 10.0)
            
            # Report result
            report_response = self.client.post("/report", json={
                "study_name": study_name,
                "symbol": symbol,
                "trial_id": suggest_data["trial_id"],
                "objective_value": objective_value,
                "metrics": {"sharpe_ratio": objective_value, "pnl_24h": objective_value * 500},
                "duration_seconds": 5.0,
                "success": True
            })
            assert report_response.status_code == 200
            report_data = report_response.json()
            
            best_values.append(report_data["best_value"])
            logger.info(f"Trial {i+1}: objective={objective_value:.3f}, best={report_data['best_value']:.3f}")
        
        # Check that optimization is working (best value should improve or stay same)
        assert best_values[-1] >= best_values[0], "Optimization should improve or maintain best value"
        
        logger.info("✅ Multiple trials funcionando - otimização ativa")


@pytest.mark.asyncio
class TestBayesOptClient:
    """Test suite for BayesOpt Client."""
    
    async def test_client_fallback_mode(self):
        """Test client in fallback mode (no service)."""
        # Configure client with invalid service URL
        config = BayesClientConfig(
            service_url="http://localhost:9999",  # Invalid port
            fallback_to_local=True,
            timeout_seconds=1.0
        )
        
        async with BayesOptClient(config) as client:
            # Test parameter suggestion (should fallback to local)
            result = await client.suggest_parameters(
                study_name="fallback_test",
                symbol="BTCUSDT"
            )
            
            assert result["source"] == "local"
            assert "parameters" in result
            assert "trial_id" in result
            
            # Test result reporting
            report_result = await client.report_result(
                study_name="fallback_test",
                symbol="BTCUSDT",
                trial_id=result["trial_id"],
                objective_value=1.5,
                metrics={"sharpe_ratio": 2.0},
                duration_seconds=5.0,
                trial_metadata=result
            )
            
            assert report_result["source"] == "local"
            assert report_result["n_trials"] >= 1
            
            logger.info("✅ Client fallback mode funcionando")
    
    async def test_client_service_mode(self):
        """Test client with actual service (if available)."""
        config = BayesClientConfig(
            service_url="http://localhost:8080",
            fallback_to_local=True,
            timeout_seconds=5.0
        )
        
        async with BayesOptClient(config) as client:
            # Check if service is available
            service_available = await client.check_service_health()
            
            if service_available:
                # Test with service
                result = await client.suggest_parameters(
                    study_name="service_test",
                    symbol="BTCUSDT"
                )
                
                assert result["source"] == "service"
                logger.info("✅ Client service mode funcionando")
            else:
                # Test fallback
                result = await client.suggest_parameters(
                    study_name="service_test",
                    symbol="BTCUSDT"
                )
                
                assert result["source"] == "local"
                logger.info("✅ Client fallback quando service indisponível")


def run_tests():
    """Run all tests."""
    print("🧪 TESTES BAYESOPT MICROSERVICE - D-02")
    print("=" * 60)
    
    # Run synchronous tests
    test_suite = TestBayesOptMicroservice()
    
    try:
        test_suite.setup_method()
        
        print("\n📊 Testando endpoints REST...")
        test_suite.test_health_endpoint()
        test_suite.test_root_endpoint()
        test_suite.test_suggest_parameters()
        test_suite.test_report_result()
        test_suite.test_list_studies()
        test_suite.test_get_study_info()
        test_suite.test_multiple_trials()
        
        test_suite.teardown_method()
        
        print("\n✅ Todos os testes REST passaram!")
        
    except Exception as e:
        print(f"\n❌ Erro nos testes REST: {e}")
        test_suite.teardown_method()
        return False
    
    # Run async client tests
    try:
        print("\n🔗 Testando cliente distribuído...")
        
        async def run_client_tests():
            client_suite = TestBayesOptClient()
            await client_suite.test_client_fallback_mode()
            await client_suite.test_client_service_mode()
        
        asyncio.run(run_client_tests())
        
        print("\n✅ Todos os testes do cliente passaram!")
        
    except Exception as e:
        print(f"\n❌ Erro nos testes do cliente: {e}")
        return False
    
    print("\n🎉 TODOS OS TESTES D-02 PASSARAM!")
    print("   • REST endpoints funcionando")
    print("   • Optuna + SQLite integrado")
    print("   • Cliente distribuído operacional")
    print("   • Fallback local funcionando")
    
    return True


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
