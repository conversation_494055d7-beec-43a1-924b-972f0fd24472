# Métodos de Assinatura Quântica

O módulo `QUALIAQuantumUniverse` oferece diversas formas de gerar vetores de assinatura.
As assinaturas capturam características do estado quântico e alimentam a camada de
metacognição. A partir do Issue 8 foram adicionados dois novos métodos:

- **phase_vector** – retorna as fases (em radianos) de cada amplitude do `Statevector`.
  Útil para detectar padrões sutis de interferência.
- **cumulative_probabilities** – distribui as probabilidades acumuladas de medição,
  destacando o peso relativo de cada base.

Todos os métodos disponíveis são acessados via
`QUALIAQuantumUniverse.generate_quantum_signature()` usando o parâmetro
`method`.
