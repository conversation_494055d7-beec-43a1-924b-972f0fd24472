#!/usr/bin/env python3
"""
Teste final para verificar se o sistema QUALIA funciona com KuCoin real
"""

import asyncio
import sys
import os
from datetime import datetime

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


async def test_qualia_kucoin_integration():
    """Testa a integração completa QUALIA + KuCoin"""
    
    print("🚀 TESTE FINAL: QUALIA + KUCOIN REAL")
    print("=" * 60)
    
    try:
        from qualia.market.kucoin_integration import KucoinIntegration
        print("✅ KucoinIntegration importado")
        
        # Criar integração KuCoin
        kucoin = KucoinIntegration(
            api_key=None,  # API pública
            api_secret=None,
            password=None,
            use_websocket=False,
            conn_timeout=30.0,
            ticker_timeout=20.0
        )
        
        print(f"✅ Integração criada: {type(kucoin.exchange)}")
        
        # Testar inicialização
        print("🔄 Inicializando conexão...")
        start_time = datetime.now()
        await kucoin.initialize_connection()
        init_duration = (datetime.now() - start_time).total_seconds()
        print(f"✅ Conexão inicializada em {init_duration:.2f}s")
        
        # Testar fetch_ticker
        print("🔄 Testando fetch_ticker...")
        start_time = datetime.now()
        ticker = await kucoin.fetch_ticker("BTC/USDT")
        ticker_duration = (datetime.now() - start_time).total_seconds()
        
        if ticker:
            print(f"✅ Ticker obtido em {ticker_duration:.2f}s")
            print(f"   Preço: ${ticker.get('last', 'N/A'):,.2f}")
            print(f"   Volume: {ticker.get('baseVolume', 'N/A')}")
        else:
            print("❌ Ticker não obtido")
            return False
        
        # Testar fetch_ohlcv
        print("🔄 Testando fetch_ohlcv...")
        start_time = datetime.now()
        df = await kucoin.fetch_ohlcv("BTC/USDT", "5m", limit=5)
        ohlcv_duration = (datetime.now() - start_time).total_seconds()
        
        if df is not None and not df.empty:
            print(f"✅ OHLCV obtido em {ohlcv_duration:.2f}s")
            print(f"   Candles: {len(df)}")
            print(f"   Último preço: ${df['close'].iloc[-1]:,.2f}")
        else:
            print("❌ OHLCV não obtido")
            return False
        
        # Análise de performance
        print("\n📊 ANÁLISE DE PERFORMANCE:")
        print(f"   Inicialização: {init_duration:.2f}s")
        print(f"   Fetch Ticker: {ticker_duration:.2f}s")
        print(f"   Fetch OHLCV: {ohlcv_duration:.2f}s")
        
        # Verificar se está dentro dos parâmetros aceitáveis
        if init_duration > 60:
            print(f"⚠️  Inicialização lenta: {init_duration:.2f}s")
        if ticker_duration > 20:
            print(f"⚠️  Ticker lento: {ticker_duration:.2f}s")
        if ohlcv_duration > 45:
            print(f"⚠️  OHLCV lento: {ohlcv_duration:.2f}s")
        
        await kucoin.close()
        print("✅ Conexão fechada")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_circuit_breaker_behavior():
    """Testa o comportamento do circuit breaker"""
    
    print("\n🔍 TESTE: CIRCUIT BREAKER")
    print("=" * 50)
    
    try:
        from qualia.market.kucoin_integration import KucoinIntegration
        
        # Criar integração com timeouts agressivos
        kucoin = KucoinIntegration(
            api_key=None,
            api_secret=None,
            password=None,
            use_websocket=False,
            conn_timeout=30.0,
            ticker_timeout=5.0,  # Timeout agressivo
            fail_threshold=2,    # Falha após 2 tentativas
            recovery_timeout=10.0  # Recuperação em 10s
        )
        
        await kucoin.initialize_connection()
        
        success_count = 0
        timeout_count = 0
        
        # Fazer várias requisições para testar o circuit breaker
        for i in range(5):
            try:
                start_time = datetime.now()
                ticker = await kucoin.fetch_ticker("BTC/USDT")
                duration = (datetime.now() - start_time).total_seconds()
                
                if ticker:
                    success_count += 1
                    print(f"   Req {i+1}: ✅ {duration:.2f}s")
                else:
                    print(f"   Req {i+1}: ❌ Ticker vazio")
                    
            except asyncio.TimeoutError:
                timeout_count += 1
                print(f"   Req {i+1}: ⏱️ TIMEOUT")
            except Exception as e:
                print(f"   Req {i+1}: ❌ {str(e)[:50]}")
            
            await asyncio.sleep(1)
        
        print("\n📊 Resultados Circuit Breaker:")
        print(f"   Sucessos: {success_count}/5")
        print(f"   Timeouts: {timeout_count}/5")
        
        await kucoin.close()
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste de circuit breaker: {e}")
        return False


async def main():
    """Função principal"""
    
    print("🌟 TESTE FINAL DO SISTEMA QUALIA")
    print("=" * 70)
    print(f"Iniciado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Teste 1: Integração básica
    integration_ok = await test_qualia_kucoin_integration()
    
    # Teste 2: Circuit breaker
    circuit_ok = await test_circuit_breaker_behavior()
    
    # Resultado final
    print("\n🏁 RESULTADO FINAL")
    print("=" * 70)
    
    if integration_ok and circuit_ok:
        print("🎉 TODOS OS TESTES PASSARAM!")
        print("✅ Sistema QUALIA funcionando corretamente com KuCoin")
        print("✅ Conectividade real estabelecida")
        print("✅ Performance dentro dos parâmetros")
        print("✅ Circuit breaker funcionando")
        
        print("\n💡 SISTEMA PRONTO PARA PRODUÇÃO!")
        
    else:
        print("❌ ALGUNS TESTES FALHARAM")
        if not integration_ok:
            print("   ❌ Integração básica falhou")
        if not circuit_ok:
            print("   ❌ Circuit breaker falhou")
    
    print(f"\nConcluído em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    asyncio.run(main()) 
