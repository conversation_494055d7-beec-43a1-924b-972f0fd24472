from unittest.mock import MagicMock

import pytest

from tests.stub_utils import install_stubs

install_stubs()

from qualia.adaptive_evolution import AdaptiveConsciousnessEvolution


def test_apply_calibrated_thresholds_updates_values():
    ace = AdaptiveConsciousnessEvolution(
        qualia_universe=MagicMock(),
        config={
            "complexity_threshold_calm": 0.3,
            "complexity_threshold_volatile": 0.7,
        },
    )
    ace.apply_calibrated_thresholds(
        {"complexity_threshold_calm": 0.25, "complexity_threshold_volatile": 0.6}
    )
    assert ace.base_complexity_threshold_calm == pytest.approx(0.25)
    assert ace.base_complexity_threshold_volatile == pytest.approx(0.6)
    assert ace.config["complexity_threshold_calm"] == pytest.approx(0.25)
    assert ace.config["complexity_threshold_volatile"] == pytest.approx(0.6)


def test_apply_calibrated_thresholds_respects_limits():
    ace = AdaptiveConsciousnessEvolution(
        qualia_universe=MagicMock(),
        config={
            "complexity_threshold_calm": 0.3,
            "complexity_threshold_volatile": 0.7,
            "complexity_min_threshold_calm": 0.2,
            "complexity_max_threshold_volatile": 0.8,
        },
    )
    ace.apply_calibrated_thresholds(
        {"complexity_threshold_calm": 0.1, "complexity_threshold_volatile": 0.95}
    )
    assert ace.base_complexity_threshold_calm == pytest.approx(0.2)
    assert ace.base_complexity_threshold_volatile == pytest.approx(0.8)
