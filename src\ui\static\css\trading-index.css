/* Revisado em 2025-06-13 por Codex */
:root {
/* Cores do Sistema de Trading */
--trading-dark: #10151F;
--trading-panel: #1A1F2C;
--trading-panel-hover: #222736;
--trading-accent: #485bc4;
--trading-accent-hover: #5A6DD6;
--trading-success: #4CAF50;
--trading-danger: #F44336;
--trading-warning: #FF9800;
--trading-separator: rgba(72, 91, 196, 0.2);
--trading-text-primary: #E9ECEF;
--trading-text-secondary: rgba(233, 236, 239, 0.7);

/* Transições */
--trading-transition: all 0.3s ease;
}

body {
background-color: var(--trading-dark);
color: var(--trading-text-primary);
font-family: 'Space Grotesk', sans-serif;
margin: 0;
padding: 0;
min-height: 100vh;
}

/* Layout Principal */
.trading-app {
display: grid;
grid-template-columns: 250px 1fr;
grid-template-rows: 60px 1fr 40px;
grid-template-areas:
    "header header"
    "sidebar main"
    "footer footer";
min-height: 100vh;
}

/* Header */
.trading-header {
grid-area: header;
display: flex;
justify-content: space-between;
align-items: center;
padding: 0 20px;
background-color: var(--trading-panel);
border-bottom: 1px solid var(--trading-separator);
}

.app-title {
display: flex;
align-items: center;
}

.app-logo {
font-size: 24px;
margin-right: 10px;
color: var(--trading-accent);
}

.app-name {
font-size: 18px;
font-weight: 600;
}

.header-controls {
display: flex;
align-items: center;
gap: 15px;
}

.system-status-indicator {
display: flex;
align-items: center;
gap: 8px;
font-size: 14px;
padding: 5px 10px;
background-color: rgba(0,0,0,0.2);
border-radius: 20px;
}

/* Indicador LED */
.indicator-led {
width: 10px;
height: 10px;
border-radius: 50%;
background-color: #555;
}

.indicator-led.active {
background-color: var(--trading-success);
box-shadow: 0 0 10px var(--trading-success);
}

.indicator-led.warning {
background-color: var(--trading-warning);
box-shadow: 0 0 10px var(--trading-warning);
}

.indicator-led.error {
background-color: var(--trading-danger);
box-shadow: 0 0 10px var(--trading-danger);
}

/* Sidebar */
.trading-sidebar {
grid-area: sidebar;
background-color: var(--trading-panel);
border-right: 1px solid var(--trading-separator);
overflow-y: auto;
display: flex;
flex-direction: column;
}

.sidebar-section {
padding: 20px;
border-bottom: 1px solid var(--trading-separator);
}

.sidebar-section-title {
font-size: 16px;
font-weight: 600;
margin-bottom: 15px;
color: var(--trading-accent);
}

.api-credentials {
display: flex;
flex-direction: column;
gap: 10px;
}

.form-group {
display: flex;
flex-direction: column;
gap: 5px;
}

.form-group label {
font-size: 12px;
color: var(--trading-text-secondary);
}

.form-control {
background-color: rgba(0,0,0,0.2);
border: 1px solid var(--trading-separator);
color: var(--trading-text-primary);
border-radius: 4px;
padding: 8px 10px;
font-family: 'Space Grotesk', sans-serif;
font-size: 14px;
width: 100%;
box-sizing: border-box;
}

.controls-group {
display: flex;
flex-direction: column;
gap: 10px;
margin-top: 15px;
}

.status-indicators {
display: flex;
justify-content: space-between;
margin-top: 15px;
}

.status-indicator {
display: flex;
flex-direction: column;
align-items: center;
gap: 5px;
font-size: 12px;
}

.status-indicator .indicator-label {
color: var(--trading-text-secondary);
}

/* Conteúdo Principal */
.trading-main {
grid-area: main;
overflow-y: auto;
padding: 20px;
}

.main-panels {
display: grid;
grid-template-columns: 1fr;
gap: 20px;
margin-bottom: 20px;
}

.panel {
background-color: var(--trading-panel);
border-radius: 8px;
overflow: hidden;
}

.panel-header {
padding: 15px;
font-weight: 600;
border-bottom: 1px solid var(--trading-separator);
display: flex;
justify-content: space-between;
align-items: center;
}

.panel-content {
padding: 15px;
}

/* Tabs de mercado */
.market-tabs {
display: flex;
flex-direction: column;
}

.tab-headers {
display: flex;
overflow-x: auto;
scrollbar-width: thin;
border-bottom: 1px solid var(--trading-separator);
}

.tab-header {
padding: 10px 15px;
font-size: 14px;
cursor: pointer;
white-space: nowrap;
transition: var(--trading-transition);
border-bottom: 2px solid transparent;
}

.tab-header.active {
color: var(--trading-accent);
border-bottom-color: var(--trading-accent);
}

.tab-header:hover {
background-color: var(--trading-panel-hover);
}

.tab-content {
flex: 1;
}

.tab-pane {
display: none;
height: 100%;
}

.tab-pane.active {
display: block;
}

/* Gráfico de mercado */
.market-chart-container {
height: 350px;
width: 100%;
}

.market-chart {
height: 100%;
width: 100%;
}

.market-data-grid {
display: grid;
grid-template-columns: repeat(4, 1fr);
gap: 15px;
margin-top: 15px;
}

.market-data-item {
background-color: rgba(0,0,0,0.1);
padding: 15px;
border-radius: 5px;
text-align: center;
}

.item-label {
font-size: 12px;
color: var(--trading-text-secondary);
margin-bottom: 5px;
}

.item-value {
font-size: 18px;
font-weight: 600;
}

.item-value.positive {
color: var(--trading-success);
}

.item-value.negative {
color: var(--trading-danger);
}

/* Grades de 3 colunas */
.grid-3-col {
display: grid;
grid-template-columns: repeat(3, 1fr);
gap: 20px;
}

/* Métricas de performance */
.metrics-display {
background-color: rgba(0,0,0,0.1);
padding: 15px;
border-radius: 8px;
text-align: center;
}

.metric-label {
font-size: 14px;
color: var(--trading-text-secondary);
margin-bottom: 5px;
}

.metric-value {
font-size: 24px;
font-weight: 600;
}

.metric-subvalue {
font-size: 14px;
color: var(--trading-text-secondary);
}

/* Tabelas */
.trading-table {
width: 100%;
border-collapse: collapse;
font-size: 14px;
}

.trading-table th {
text-align: left;
padding: 12px 15px;
background-color: rgba(0,0,0,0.2);
font-weight: 600;
color: var(--trading-accent);
}

.trading-table td {
padding: 10px 15px;
border-bottom: 1px solid var(--trading-separator);
}

.trading-table tr:hover td {
background-color: var(--trading-panel-hover);
}

.trading-table tr.empty-row td {
text-align: center;
color: var(--trading-text-secondary);
padding: 30px 15px;
}

/* Estilos para Visualização do Circuito Quântico */
.circuit-visualization-container {
display: flex;
flex-direction: column;
gap: 20px;
}

.circuit-options {
display: flex;
gap: 15px;
margin-bottom: 10px;
}

.circuit-visualization {
min-height: 300px;
border-radius: 8px;
background-color: rgba(0,0,0,0.2);
display: flex;
align-items: center;
justify-content: center;
overflow: auto;
position: relative;
}

.circuit-placeholder {
text-align: center;
color: var(--trading-text-secondary);
padding: 20px;
}

.circuit-image {
max-width: 100%;
height: auto;
}

.circuit-text {
font-family: monospace;
white-space: pre;
overflow: auto;
padding: 15px;
width: 100%;
font-size: 12px;
}

.loading-indicator {
text-align: center;
padding: 20px;
color: var(--trading-text-secondary);
}

.error-message {
color: var(--trading-danger);
text-align: center;
padding: 20px;
}

.compatibility-status {
transition: all 0.3s ease;
}

.compatibility-status.compatible {
background-color: rgba(76, 175, 80, 0.1) !important;
}

.compatibility-status.incompatible {
background-color: rgba(244, 67, 54, 0.1) !important;
}

.circuit-info-item {
background-color: rgba(0,0,0,0.2);
padding: 10px;
border-radius: 4px;
text-align: center;
}

.circuit-info-item .item-label {
font-size: 12px;
color: var(--trading-text-secondary);
}

.circuit-info-item .item-value {
font-size: 18px;
font-weight: bold;
margin-top: 5px;
}

.table-action-btn {
padding: 5px 10px;
font-size: 12px;
}

/* Entrada de ordens manuais */
.order-form {
display: grid;
grid-template-columns: repeat(6, 1fr);
gap: 15px;
align-items: flex-end;
}

.quantum-button {
background: linear-gradient(135deg, var(--trading-accent), var(--probability-purple));
color: #fff;
border: none;
border-radius: 4px;
padding: 10px 15px;
font-family: 'Space Grotesk', sans-serif;
font-size: 14px;
font-weight: 500;
cursor: pointer;
transition: var(--trading-transition);
}

.quantum-button:hover {
background: linear-gradient(135deg, var(--trading-accent-hover), var(--probability-purple));
transform: translateY(-2px);
}

.quantum-button.small {
padding: 5px 10px;
font-size: 12px;
}

/* Toggle */
.toggle-container {
display: flex;
align-items: center;
margin-top: 15px;
}

.toggle-label {
margin-right: 10px;
font-size: 14px;
color: var(--trading-text-secondary);
}

.toggle-switch {
position: relative;
display: inline-block;
width: 50px;
height: 24px;
}

.toggle-switch input {
opacity: 0;
width: 0;
height: 0;
}

.toggle-slider {
position: absolute;
cursor: pointer;
top: 0;
left: 0;
right: 0;
bottom: 0;
background-color: rgba(0,0,0,0.2);
transition: var(--trading-transition);
border-radius: 24px;
}

.toggle-slider:before {
position: absolute;
content: "";
height: 16px;
width: 16px;
left: 4px;
bottom: 4px;
background-color: var(--trading-text-primary);
transition: var(--trading-transition);
border-radius: 50%;
}

input:checked + .toggle-slider {
background-color: var(--trading-danger);
}

input:checked + .toggle-slider:before {
transform: translateX(26px);
}

.toggle-text {
margin-left: 10px;
font-size: 14px;
font-weight: 600;
}

/* Footer */
.trading-footer {
grid-area: footer;
border-top: 1px solid var(--trading-separator);
display: flex;
justify-content: space-between;
align-items: center;
padding: 0 20px;
font-size: 12px;
color: var(--trading-text-secondary);
}

.footer-status {
display: flex;
gap: 15px;
}

/* Notificações */
#notifications-container {
position: fixed;
top: 20px;
right: 20px;
z-index: 1000;
display: flex;
flex-direction: column;
gap: 10px;
max-width: 350px;
}

.notification {
background-color: var(--trading-panel);
border-left: 4px solid;
border-radius: 4px;
padding: 15px;
display: flex;
justify-content: space-between;
align-items: flex-start;
box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
animation: slide-in 0.3s ease;
}

.notification.success {
border-left-color: var(--trading-success);
}

.notification.error {
border-left-color: var(--trading-danger);
}

.notification.warning {
border-left-color: var(--trading-warning);
}

.notification.info {
border-left-color: var(--trading-accent);
}

.notification-content {
flex-grow: 1;
margin-right: 10px;
}

.notification-title {
font-weight: 600;
margin-bottom: 5px;
}

.notification-message {
font-size: 14px;
color: var(--trading-text-secondary);
}

.notification-close {
background: none;
border: none;
color: var(--trading-text-secondary);
font-size: 16px;
cursor: pointer;
transition: var(--trading-transition);
}

.notification-close:hover {
color: var(--trading-text-primary);
}

/* Modal */
.modal-overlay {
position: fixed;
top: 0;
left: 0;
right: 0;
bottom: 0;
background-color: rgba(0, 0, 0, 0.5);
display: flex;
justify-content: center;
align-items: center;
z-index: 1000;
opacity: 0;
visibility: hidden;
transition: var(--trading-transition);
}

.modal-overlay.active {
opacity: 1;
visibility: visible;
}

.modal {
background-color: var(--trading-panel);
border-radius: 8px;
width: 100%;
max-width: 500px;
overflow: hidden;
transform: translateY(20px);
transition: transform 0.3s ease;
}

.modal-overlay.active .modal {
transform: translateY(0);
}

.modal-header {
padding: 15px 20px;
border-bottom: 1px solid var(--trading-separator);
display: flex;
justify-content: space-between;
align-items: center;
}

.modal-title {
font-weight: 600;
font-size: 18px;
}

.modal-close {
background: none;
border: none;
color: var(--trading-text-secondary);
font-size: 18px;
cursor: pointer;
}

.modal-body {
padding: 20px;
white-space: pre-line;
}

.modal-footer {
padding: 15px 20px;
border-top: 1px solid var(--trading-separator);
display: flex;
justify-content: flex-end;
gap: 10px;
}

/* Animações */
@keyframes slide-in {
from {
    transform: translateX(30px);
    opacity: 0;
}
to {
    transform: translateX(0);
    opacity: 1;
}
}

@keyframes fade-in {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}

/* Media Queries */
@media (max-width: 1000px) {
.trading-app {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr auto;
    grid-template-areas:
        "header"
        "sidebar"
        "main"
        "footer";
}

.trading-sidebar {
    border-right: none;
    border-bottom: 1px solid var(--trading-separator);
}

.grid-3-col {
    grid-template-columns: 1fr;
}

.order-form {
    grid-template-columns: repeat(3, 1fr);
}
}

@media (max-width: 600px) {
.market-data-grid {
    grid-template-columns: repeat(2, 1fr);
}

.order-form {
    grid-template-columns: 1fr;
}
}

/* Estilos para Visualização do Circuito Quântico */
.circuit-visualization-container {
display: flex;
flex-direction: column;
gap: 20px;
}

.circuit-options {
display: flex;
gap: 20px;
margin-bottom: 15px;
}

.circuit-visualization {
background-color: rgba(0,0,0,0.1);
border-radius: 8px;
padding: 20px;
min-height: 300px;
display: flex;
justify-content: center;
align-items: center;
overflow: auto;
}

.circuit-placeholder {
text-align: center;
color: var(--trading-text-secondary);
padding: 40px 0;
}

.circuit-image {
max-width: 100%;
max-height: 500px;
}

.circuit-text {
white-space: pre;
font-family: monospace;
font-size: 12px;
overflow: auto;
max-height: 400px;
background-color: rgba(0,0,0,0.2);
padding: 15px;
border-radius: 4px;
color: var(--trading-text-primary);
}

.compatibility-status.compatible {
background-color: rgba(76, 175, 80, 0.1) !important;
}

.compatibility-status.incompatible {
background-color: rgba(244, 67, 54, 0.1) !important;
}
