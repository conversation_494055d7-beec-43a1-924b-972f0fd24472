"""
QUALIA D-06: Regime-aware Configuration Manager
===============================================

Sistema de configuração dinâmica baseado em regimes de mercado.
Aplica presets inteligentes e configurações adaptativas conforme condições de mercado.
"""

from __future__ import annotations

import asyncio
import yaml
import json
import numpy as np
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field, asdict
from pathlib import Path
from enum import Enum
import logging

from ..utils.logger import get_logger
from ..market.regime_detector import MarketRegime, MarketRegimeDetector, RegimeDetection
from ..config.hot_reload import ConfigurationHotReloader
from ..utils.event_bus import EventBus

logger = get_logger(__name__)


@dataclass
class RegimePreset:
    """Preset de configuração para um regime específico."""

    regime: MarketRegime
    name: str
    description: str
    config: Dict[str, Any]
    hyperparams: Dict[str, Any] = field(default_factory=dict)

    # Condições de aplicação
    min_confidence: float = 0.7  # Confiança mínima para aplicar
    min_duration: int = 3  # Períodos mínimos no regime
    cooldown_minutes: int = 15  # Tempo mínimo entre mudanças

    # Métricas de validação
    expected_sharpe: Optional[float] = None
    expected_drawdown: Optional[float] = None

    # Metadados
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    last_used: Optional[datetime] = None
    usage_count: int = 0
    performance_history: List[Dict[str, float]] = field(default_factory=list)


@dataclass
class RegimeTransition:
    """Transição entre regimes."""

    from_regime: MarketRegime
    to_regime: MarketRegime
    timestamp: datetime
    confidence: float

    # Configuração aplicada
    preset_applied: Optional[str] = None
    config_changes: Dict[str, Any] = field(default_factory=dict)

    # Contexto da transição
    market_conditions: Dict[str, float] = field(default_factory=dict)
    performance_before: Dict[str, float] = field(default_factory=dict)
    performance_after: Dict[str, float] = field(default_factory=dict)


@dataclass
class RegimeAwareConfig:
    """Configuração do sistema regime-aware."""

    # Arquivos de configuração
    presets_file: str = "config/regime_presets.yaml"
    base_config_file: str = "config/holographic_enhanced_config.yaml"

    # Parâmetros de aplicação
    auto_apply_enabled: bool = True
    min_regime_confidence: float = 0.7
    min_regime_duration: int = 3
    transition_cooldown_minutes: int = 15

    # Validação de performance
    performance_validation_enabled: bool = True
    performance_window_minutes: int = 30
    min_performance_improvement: float = 0.05  # 5% melhoria mínima

    # Backup e rollback
    backup_enabled: bool = True
    max_backup_files: int = 50
    auto_rollback_on_poor_performance: bool = True
    rollback_threshold_drawdown: float = 0.10  # 10% drawdown

    # Logging e monitoramento
    log_all_transitions: bool = True
    save_performance_history: bool = True
    alert_on_regime_change: bool = True


class RegimeAwareConfigManager:
    """
    Gerenciador de configurações adaptativas baseado em regimes de mercado.

    Funcionalidades:
    - Detecção automática de mudanças de regime
    - Aplicação de presets específicos por regime
    - Validação de performance pós-aplicação
    - Rollback automático em caso de performance ruim
    - Histórico de transições e performance
    - Integração com hot-reload e A/B testing
    """

    def __init__(
        self,
        regime_detector: MarketRegimeDetector,
        hot_reloader: ConfigurationHotReloader,
        config: Optional[RegimeAwareConfig] = None,
        event_bus: Optional[EventBus] = None,
    ):
        self.regime_detector = regime_detector
        self.hot_reloader = hot_reloader
        self.config = config or RegimeAwareConfig()
        self.event_bus = event_bus

        # Estado do sistema
        self.current_regime: Optional[MarketRegime] = None
        self.current_preset: Optional[RegimePreset] = None
        self.last_transition: Optional[datetime] = None

        # Presets carregados
        self.presets: Dict[MarketRegime, List[RegimePreset]] = {}
        self.default_presets: Dict[MarketRegime, RegimePreset] = {}

        # Histórico
        self.transition_history: List[RegimeTransition] = []
        self.performance_history: List[Dict[str, Any]] = []

        # Estado de execução
        self.is_running = False
        self.monitoring_task: Optional[asyncio.Task] = None

        # Callbacks
        self.performance_callbacks: List[Callable[[], Dict[str, float]]] = []

        logger.info("✅ RegimeAwareConfigManager inicializado")

    async def start(self):
        """Inicia o gerenciador de configurações regime-aware."""
        if self.is_running:
            logger.warning("Gerenciador já está rodando")
            return

        try:
            # Carregar presets
            await self.load_presets()

            # Iniciar monitoramento
            self.is_running = True
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())

            logger.info("🚀 RegimeAwareConfigManager iniciado")

        except Exception as e:
            logger.error(f"Erro ao iniciar gerenciador: {e}")
            raise

    async def stop(self):
        """Para o gerenciador."""
        self.is_running = False

        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass

        logger.info("⏹️ RegimeAwareConfigManager parado")

    async def load_presets(self):
        """Carrega presets de configuração do arquivo."""
        try:
            presets_path = Path(self.config.presets_file)

            if not presets_path.exists():
                logger.warning(f"Arquivo de presets não encontrado: {presets_path}")
                await self._create_default_presets()
                return

            with open(presets_path, "r", encoding="utf-8") as f:
                data = yaml.safe_load(f)

            # Limpar presets existentes
            self.presets.clear()
            self.default_presets.clear()

            # Carregar presets
            for regime_name, regime_data in data.get("presets", {}).items():
                try:
                    regime = MarketRegime(regime_name)

                    if regime not in self.presets:
                        self.presets[regime] = []

                    for preset_data in regime_data:
                        preset = RegimePreset(
                            regime=regime,
                            name=preset_data["name"],
                            description=preset_data["description"],
                            config=preset_data["config"],
                            hyperparams=preset_data.get("hyperparams", {}),
                            min_confidence=preset_data.get("min_confidence", 0.7),
                            min_duration=preset_data.get("min_duration", 3),
                            cooldown_minutes=preset_data.get("cooldown_minutes", 15),
                            expected_sharpe=preset_data.get("expected_sharpe"),
                            expected_drawdown=preset_data.get("expected_drawdown"),
                        )

                        self.presets[regime].append(preset)

                        # Marcar como default se especificado
                        if preset_data.get("is_default", False):
                            self.default_presets[regime] = preset

                except ValueError as e:
                    logger.warning(f"Regime inválido ignorado: {regime_name} ({e})")

            logger.info(
                f"📋 Presets carregados: {sum(len(p) for p in self.presets.values())} presets para {len(self.presets)} regimes"
            )

        except Exception as e:
            logger.error(f"Erro ao carregar presets: {e}")
            await self._create_default_presets()

    async def save_presets(self):
        """Salva presets no arquivo."""
        try:
            # Preparar dados para salvamento
            data = {
                "presets": {},
                "metadata": {
                    "created_at": datetime.now(timezone.utc).isoformat(),
                    "total_presets": sum(len(p) for p in self.presets.values()),
                },
            }

            for regime, presets in self.presets.items():
                data["presets"][regime.value] = []

                for preset in presets:
                    preset_data = {
                        "name": preset.name,
                        "description": preset.description,
                        "config": preset.config,
                        "hyperparams": preset.hyperparams,
                        "min_confidence": preset.min_confidence,
                        "min_duration": preset.min_duration,
                        "cooldown_minutes": preset.cooldown_minutes,
                        "is_default": self.default_presets.get(regime) == preset,
                    }

                    if preset.expected_sharpe is not None:
                        preset_data["expected_sharpe"] = preset.expected_sharpe
                    if preset.expected_drawdown is not None:
                        preset_data["expected_drawdown"] = preset.expected_drawdown

                    data["presets"][regime.value].append(preset_data)

            # Salvar arquivo
            presets_path = Path(self.config.presets_file)
            presets_path.parent.mkdir(parents=True, exist_ok=True)

            with open(presets_path, "w", encoding="utf-8") as f:
                yaml.dump(
                    data, f, default_flow_style=False, allow_unicode=True, indent=2
                )

            logger.info(f"💾 Presets salvos: {presets_path}")

        except Exception as e:
            logger.error(f"Erro ao salvar presets: {e}")

    def register_performance_callback(self, callback: Callable[[], Dict[str, float]]):
        """Registra callback para coleta de métricas de performance."""
        self.performance_callbacks.append(callback)
        logger.debug(f"Callback de performance registrado: {callback.__name__}")

    async def add_preset(self, preset: RegimePreset, set_as_default: bool = False):
        """Adiciona um novo preset."""
        if preset.regime not in self.presets:
            self.presets[preset.regime] = []

        self.presets[preset.regime].append(preset)

        if set_as_default:
            self.default_presets[preset.regime] = preset

        logger.info(
            f"➕ Preset adicionado: {preset.name} para regime {preset.regime.value}"
        )

        # Salvar automaticamente
        await self.save_presets()

    async def remove_preset(self, regime: MarketRegime, preset_name: str):
        """Remove um preset."""
        if regime not in self.presets:
            return False

    async def _monitoring_loop(self):
        """Loop principal de monitoramento de regimes."""
        while self.is_running:
            try:
                # Verificar regime atual para todos os símbolos
                await self._check_regime_changes()

                # Validar performance se necessário
                if self.config.performance_validation_enabled:
                    await self._validate_current_performance()

                # Aguardar próxima verificação
                await asyncio.sleep(30)  # Verificar a cada 30 segundos

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Erro no loop de monitoramento: {e}")
                await asyncio.sleep(10)

    async def _check_regime_changes(self):
        """Verifica mudanças de regime e aplica configurações."""
        try:
            # Obter regimes atuais de todos os símbolos
            regime_detections = {}

            # Para simplicidade, usar o primeiro símbolo disponível
            # Em implementação completa, considerar múltiplos símbolos
            for symbol in self.regime_detector.current_regimes:
                detection = await self.regime_detector.get_current_regime(symbol)
                if detection:
                    regime_detections[symbol] = detection

            if not regime_detections:
                return

            # Determinar regime dominante (mais comum ou maior confiança)
            regime_votes = {}
            for detection in regime_detections.values():
                regime = detection.regime
                if regime not in regime_votes:
                    regime_votes[regime] = []
                regime_votes[regime].append(detection.confidence)

            # Regime com maior confiança média
            dominant_regime = max(
                regime_votes.keys(), key=lambda r: np.mean(regime_votes[r])
            )
            avg_confidence = np.mean(regime_votes[dominant_regime])

            # Verificar se houve mudança de regime
            if dominant_regime != self.current_regime:
                await self._handle_regime_change(
                    dominant_regime, avg_confidence, regime_detections
                )

        except Exception as e:
            logger.error(f"Erro ao verificar mudanças de regime: {e}")

    async def _handle_regime_change(
        self,
        new_regime: MarketRegime,
        confidence: float,
        detections: Dict[str, RegimeDetection],
    ):
        """Processa mudança de regime."""
        try:
            old_regime = self.current_regime

            # Verificar critérios para aplicação
            if not await self._should_apply_regime_change(
                new_regime, confidence, detections
            ):
                return

            # Selecionar preset apropriado
            preset = await self._select_preset_for_regime(new_regime, confidence)

            if not preset:
                logger.warning(
                    f"Nenhum preset disponível para regime {new_regime.value}"
                )
                return

            # Aplicar configuração
            success = await self._apply_preset(preset)

            if success:
                # Atualizar estado
                self.current_regime = new_regime
                self.current_preset = preset
                self.last_transition = datetime.now(timezone.utc)

                # Registrar transição
                transition = RegimeTransition(
                    from_regime=old_regime,
                    to_regime=new_regime,
                    timestamp=self.last_transition,
                    confidence=confidence,
                    preset_applied=preset.name,
                    config_changes=preset.config,
                    market_conditions={
                        symbol: {
                            "trend_strength": d.metrics.trend_strength,
                            "volatility_percentile": d.metrics.volatility_percentile,
                            "momentum_score": d.metrics.momentum_score,
                        }
                        for symbol, d in detections.items()
                    },
                )

                self.transition_history.append(transition)

                # Atualizar estatísticas do preset
                preset.last_used = self.last_transition
                preset.usage_count += 1

                # Notificar mudança
                await self._notify_regime_change(transition)

                logger.info(
                    f"🔄 Regime alterado: {old_regime.value if old_regime else 'None'} → {new_regime.value} "
                    f"(preset: {preset.name}, confiança: {confidence:.2f})"
                )

        except Exception as e:
            logger.error(f"Erro ao processar mudança de regime: {e}")

    async def _should_apply_regime_change(
        self,
        new_regime: MarketRegime,
        confidence: float,
        detections: Dict[str, RegimeDetection],
    ) -> bool:
        """Verifica se deve aplicar mudança de regime."""
        # Verificar confiança mínima
        if confidence < self.config.min_regime_confidence:
            logger.debug(
                f"Confiança insuficiente para mudança: {confidence:.2f} < {self.config.min_regime_confidence}"
            )
            return False

        # Verificar cooldown
        if self.last_transition:
            elapsed = (
                datetime.now(timezone.utc) - self.last_transition
            ).total_seconds() / 60
            if elapsed < self.config.transition_cooldown_minutes:
                logger.debug(
                    f"Cooldown ativo: {elapsed:.1f}min < {self.config.transition_cooldown_minutes}min"
                )
                return False

        # Verificar duração mínima no regime
        min_duration_met = True
        for detection in detections.values():
            if detection.regime_duration < self.config.min_regime_duration:
                min_duration_met = False
                break

        if not min_duration_met:
            logger.debug(f"Duração mínima não atingida para regime {new_regime.value}")
            return False

        # Verificar se auto-apply está habilitado
        if not self.config.auto_apply_enabled:
            logger.debug("Auto-apply desabilitado")
            return False

        return True

    async def _select_preset_for_regime(
        self, regime: MarketRegime, confidence: float
    ) -> Optional[RegimePreset]:
        """Seleciona o melhor preset para um regime."""
        if regime not in self.presets:
            return None

        available_presets = self.presets[regime]

        if not available_presets:
            return None

        # Filtrar por confiança mínima
        suitable_presets = [
            p for p in available_presets if confidence >= p.min_confidence
        ]

        if not suitable_presets:
            # Se nenhum atende a confiança, usar o default se existir
            return self.default_presets.get(regime)

        # Selecionar baseado em performance histórica
        best_preset = None
        best_score = -float("inf")

        for preset in suitable_presets:
            score = self._calculate_preset_score(preset)
            if score > best_score:
                best_score = score
                best_preset = preset

        return best_preset or self.default_presets.get(regime)

    def _calculate_preset_score(self, preset: RegimePreset) -> float:
        """Calcula score de um preset baseado em histórico de performance."""
        if not preset.performance_history:
            return 0.0  # Neutro para presets sem histórico

        # Calcular média ponderada das performances recentes
        recent_performances = preset.performance_history[-10:]  # Últimas 10 aplicações

        if not recent_performances:
            return 0.0

        # Score baseado em Sharpe ratio médio
        sharpe_scores = [p.get("sharpe_ratio", 0.0) for p in recent_performances]
        avg_sharpe = np.mean(sharpe_scores) if sharpe_scores else 0.0

        # Penalizar por drawdown alto
        drawdown_scores = [p.get("max_drawdown", 0.0) for p in recent_performances]
        avg_drawdown = np.mean(drawdown_scores) if drawdown_scores else 0.0

        # Score combinado
        score = avg_sharpe - (avg_drawdown * 2)  # Penalizar drawdown 2x

        return score

    async def _apply_preset(self, preset: RegimePreset) -> bool:
        """Aplica um preset de configuração."""
        try:
            # Fazer backup da configuração atual se habilitado
            if self.config.backup_enabled:
                await self._backup_current_config()

            # Aplicar configuração via hot-reload (se suportado)
            if hasattr(self.hot_reloader, "update_config"):
                success = await self.hot_reloader.update_config(
                    preset.config, source=f"regime_preset_{preset.name}"
                )
            else:
                logger.warning(
                    "Hot reloader não suporta update_config - pulando aplicação de arquivo"
                )
                success = True

            if success:
                logger.info(
                    f"✅ Preset aplicado: {preset.name} para regime {preset.regime.value}"
                )
                self._apply_hyperparam_overrides(preset.hyperparams)
                return True
            else:
                logger.error(f"❌ Falha ao aplicar preset: {preset.name}")
                return False

        except Exception as e:
            logger.error(f"Erro ao aplicar preset {preset.name}: {e}")
            return False

    async def _validate_current_performance(self):
        """Valida performance da configuração atual."""
        if not self.current_preset or not self.performance_callbacks:
            return

        try:
            # Coletar métricas atuais
            current_metrics = {}
            for callback in self.performance_callbacks:
                try:
                    metrics = callback()
                    current_metrics.update(metrics)
                except Exception as e:
                    logger.error(f"Erro em callback de performance: {e}")

            if not current_metrics:
                return

            # Verificar se performance está abaixo do esperado
            should_rollback = False
            rollback_reason = ""

            # Verificar drawdown
            current_drawdown = current_metrics.get("max_drawdown", 0.0)
            if current_drawdown > self.config.rollback_threshold_drawdown:
                should_rollback = True
                rollback_reason = f"drawdown_excessivo ({current_drawdown:.2%})"

            # Verificar performance vs expectativa do preset
            if self.current_preset.expected_sharpe is not None:
                current_sharpe = current_metrics.get("sharpe_ratio", 0.0)
                expected_sharpe = self.current_preset.expected_sharpe

                if (
                    current_sharpe < expected_sharpe * 0.5
                ):  # 50% da performance esperada
                    should_rollback = True
                    rollback_reason = (
                        f"sharpe_baixo ({current_sharpe:.2f} vs {expected_sharpe:.2f})"
                    )

            # Executar rollback se necessário
            if should_rollback and self.config.auto_rollback_on_poor_performance:
                await self._execute_rollback(rollback_reason)

            # Salvar métricas no histórico do preset
            performance_record = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "sharpe_ratio": current_metrics.get("sharpe_ratio", 0.0),
                "max_drawdown": current_metrics.get("max_drawdown", 0.0),
                "pnl_24h": current_metrics.get("pnl_24h", 0.0),
                "win_rate": current_metrics.get("win_rate", 0.0),
            }

            self.current_preset.performance_history.append(performance_record)

            # Manter apenas últimos 50 registros
            if len(self.current_preset.performance_history) > 50:
                self.current_preset.performance_history = (
                    self.current_preset.performance_history[-50:]
                )

        except Exception as e:
            logger.error(f"Erro ao validar performance: {e}")

    async def _execute_rollback(self, reason: str):
        """Executa rollback para configuração anterior."""
        try:
            # Tentar restaurar backup mais recente
            success = await self.hot_reloader.rollback_config()

            if success:
                # Resetar estado
                old_preset = self.current_preset
                self.current_preset = None
                self.current_regime = None

                # Registrar rollback
                rollback_event = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "reason": reason,
                    "preset_rolled_back": old_preset.name if old_preset else None,
                    "regime_rolled_back": (
                        old_preset.regime.value if old_preset else None
                    ),
                }

                # Notificar rollback
                if self.event_bus:
                    self.event_bus.publish("regime_config.rollback", rollback_event)

                logger.warning(f"🔙 Rollback executado: {reason}")
            else:
                logger.error("❌ Falha ao executar rollback")

        except Exception as e:
            logger.error(f"Erro ao executar rollback: {e}")

    async def _backup_current_config(self):
        """Faz backup da configuração atual."""
        try:
            # Usar sistema de backup do hot-reloader
            await self.hot_reloader.create_backup()
            logger.debug("💾 Backup da configuração criado")

        except Exception as e:
            logger.error(f"Erro ao criar backup: {e}")

    def _apply_hyperparam_overrides(self, overrides: Dict[str, Any]) -> None:
        """Aplica overrides de hiperparâmetros dinamicamente."""
        if not overrides:
            return

        from .hyperparams_loader import get_global_hyperparams_loader

        loader = get_global_hyperparams_loader()
        for param, value in overrides.items():
            loader.set_dynamic_override(param, value)

        loader.load()

    async def _notify_regime_change(self, transition: RegimeTransition):
        """Notifica mudança de regime."""
        if not self.event_bus:
            return

        try:
            event_data = {
                "from_regime": (
                    transition.from_regime.value if transition.from_regime else None
                ),
                "to_regime": transition.to_regime.value,
                "confidence": transition.confidence,
                "preset_applied": transition.preset_applied,
                "timestamp": transition.timestamp.isoformat(),
                "market_conditions": transition.market_conditions,
            }

            self.event_bus.publish("regime_config.changed", event_data)

            if self.config.alert_on_regime_change:
                logger.info(
                    f"🚨 Alerta: Mudança de regime {transition.from_regime.value if transition.from_regime else 'None'} → {transition.to_regime.value}"
                )

        except Exception as e:
            logger.error(f"Erro ao notificar mudança de regime: {e}")

    async def _create_default_presets(self):
        """Cria presets padrão se não existirem."""
        try:
            # Presets básicos para cada regime
            default_configs = {
                MarketRegime.BULL: {
                    "name": "Bull Market Default",
                    "description": "Configuração otimizada para mercado em alta",
                    "config": {
                        "risk_management": {
                            "max_position_size": 0.15,
                            "stop_loss_pct": 0.08,
                            "take_profit_pct": 0.20,
                        },
                        "trading_params": {
                            "trend_following_weight": 0.7,
                            "mean_reversion_weight": 0.3,
                            "momentum_threshold": 0.6,
                        },
                    },
                    "hyperparams": {
                        "price_amplification": 1.2,
                        "news_amplification": 8.5,
                        "min_confidence": 0.35,
                    },
                },
                MarketRegime.BEAR: {
                    "name": "Bear Market Default",
                    "description": "Configuração defensiva para mercado em baixa",
                    "config": {
                        "risk_management": {
                            "max_position_size": 0.08,
                            "stop_loss_pct": 0.05,
                            "take_profit_pct": 0.10,
                        },
                        "trading_params": {
                            "trend_following_weight": 0.3,
                            "mean_reversion_weight": 0.7,
                            "momentum_threshold": 0.4,
                        },
                    },
                    "hyperparams": {
                        "price_amplification": 0.8,
                        "news_amplification": 12.0,
                        "min_confidence": 0.45,
                    },
                },
                MarketRegime.SIDEWAYS: {
                    "name": "Sideways Market Default",
                    "description": "Configuração para mercado lateral",
                    "config": {
                        "risk_management": {
                            "max_position_size": 0.12,
                            "stop_loss_pct": 0.06,
                            "take_profit_pct": 0.12,
                        },
                        "trading_params": {
                            "trend_following_weight": 0.2,
                            "mean_reversion_weight": 0.8,
                            "momentum_threshold": 0.3,
                        },
                    },
                    "hyperparams": {
                        "price_amplification": 1.0,
                        "news_amplification": 11.3,
                        "min_confidence": 0.37,
                    },
                },
                MarketRegime.VOLATILE: {
                    "name": "Volatile Market Default",
                    "description": "Configuração para mercado volátil",
                    "config": {
                        "risk_management": {
                            "max_position_size": 0.06,
                            "stop_loss_pct": 0.04,
                            "take_profit_pct": 0.08,
                        },
                        "trading_params": {
                            "trend_following_weight": 0.4,
                            "mean_reversion_weight": 0.6,
                            "momentum_threshold": 0.7,
                        },
                    },
                    "hyperparams": {
                        "price_amplification": 1.3,
                        "news_amplification": 12.0,
                        "min_confidence": 0.5,
                    },
                },
                MarketRegime.STABLE: {
                    "name": "Stable Market Default",
                    "description": "Configuração para mercado estável",
                    "config": {
                        "risk_management": {
                            "max_position_size": 0.20,
                            "stop_loss_pct": 0.10,
                            "take_profit_pct": 0.25,
                        },
                        "trading_params": {
                            "trend_following_weight": 0.6,
                            "mean_reversion_weight": 0.4,
                            "momentum_threshold": 0.5,
                        },
                    },
                    "hyperparams": {
                        "price_amplification": 0.9,
                        "news_amplification": 10.0,
                        "min_confidence": 0.4,
                    },
                },
            }

            # Criar presets
            for regime, config_data in default_configs.items():
                preset = RegimePreset(
                    regime=regime,
                    name=config_data["name"],
                    description=config_data["description"],
                    config=config_data["config"],
                    hyperparams=config_data.get("hyperparams", {}),
                    min_confidence=0.6,
                    min_duration=2,
                    cooldown_minutes=10,
                )

                await self.add_preset(preset, set_as_default=True)

            logger.info("📋 Presets padrão criados")

        except Exception as e:
            logger.error(f"Erro ao criar presets padrão: {e}")

    def get_status(self) -> Dict[str, Any]:
        """Retorna status atual do gerenciador."""
        return {
            "is_running": self.is_running,
            "current_regime": (
                self.current_regime.value if self.current_regime else None
            ),
            "current_preset": self.current_preset.name if self.current_preset else None,
            "last_transition": (
                self.last_transition.isoformat() if self.last_transition else None
            ),
            "total_presets": sum(len(p) for p in self.presets.values()),
            "total_transitions": len(self.transition_history),
            "config": asdict(self.config),
        }

    def get_regime_summary(self) -> Dict[str, Any]:
        """Retorna resumo dos regimes e presets."""
        summary = {
            "regimes": {},
            "statistics": {
                "total_presets": sum(len(p) for p in self.presets.values()),
                "total_transitions": len(self.transition_history),
                "most_used_regime": None,
                "most_used_preset": None,
            },
        }

        # Informações por regime
        for regime, presets in self.presets.items():
            regime_info = {
                "total_presets": len(presets),
                "default_preset": (
                    self.default_presets.get(regime).name
                    if regime in self.default_presets
                    else None
                ),
                "presets": [
                    {
                        "name": p.name,
                        "description": p.description,
                        "usage_count": p.usage_count,
                        "last_used": p.last_used.isoformat() if p.last_used else None,
                        "performance_records": len(p.performance_history),
                    }
                    for p in presets
                ],
            }
            summary["regimes"][regime.value] = regime_info

        # Estatísticas de uso
        if self.transition_history:
            regime_usage = {}
            preset_usage = {}

            for transition in self.transition_history:
                regime = transition.to_regime.value
                preset = transition.preset_applied

                regime_usage[regime] = regime_usage.get(regime, 0) + 1
                if preset:
                    preset_usage[preset] = preset_usage.get(preset, 0) + 1

            if regime_usage:
                summary["statistics"]["most_used_regime"] = max(
                    regime_usage.keys(), key=regime_usage.get
                )
            if preset_usage:
                summary["statistics"]["most_used_preset"] = max(
                    preset_usage.keys(), key=preset_usage.get
                )

        return summary


# Função de conveniência para criar gerenciador global
_global_manager: Optional[RegimeAwareConfigManager] = None


def get_global_regime_config_manager(
    regime_detector: MarketRegimeDetector,
    hot_reloader: ConfigurationHotReloader,
    config: Optional[RegimeAwareConfig] = None,
    event_bus: Optional[EventBus] = None,
) -> RegimeAwareConfigManager:
    """Retorna instância global do gerenciador de configurações regime-aware."""
    global _global_manager

    if _global_manager is None:
        _global_manager = RegimeAwareConfigManager(
            regime_detector=regime_detector,
            hot_reloader=hot_reloader,
            config=config,
            event_bus=event_bus,
        )

    return _global_manager
