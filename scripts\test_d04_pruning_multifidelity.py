#!/usr/bin/env python3
"""
QUALIA D-04 Test Script: Advanced Pruning & Multi-fidelity Optimization

Script para testar e validar a implementação D-04:
- Sistema de pruning avançado
- Otimização multi-fidelidade
- Integração com BayesianOptimizer
- Diferentes regimes de mercado
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from typing import Dict, Any

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.optimization.advanced_pruning import (
    AdvancedPruner, PruningConfig, PruningStrategy, 
    TradingMetrics, MarketRegime
)
from qualia.optimization.multi_fidelity import (
    MultiFidelityEvaluator, FidelityConfig, FidelityLevel
)
from qualia.optimization.bayesian_optimizer import BayesianOptimizer, OptimizationConfig
from qualia.utils.logger import get_logger

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/d04_test.log')
    ]
)

logger = get_logger(__name__)


class D04TestSuite:
    """Suite de testes para D-04."""
    
    def __init__(self):
        self.test_results = {}
        
    async def run_all_tests(self):
        """Executa todos os testes D-04."""
        
        logger.info("[ROCKET] Iniciando D-04 Test Suite")
        logger.info("=" * 60)
        
        tests = [
            ("test_advanced_pruner", self.test_advanced_pruner),
            ("test_multi_fidelity_evaluator", self.test_multi_fidelity_evaluator),
            ("test_trading_metrics", self.test_trading_metrics),
            ("test_market_regime_adaptation", self.test_market_regime_adaptation),
            ("test_bayesian_optimizer_integration", self.test_bayesian_optimizer_integration),
            ("test_pruning_strategies", self.test_pruning_strategies),
            ("test_multi_fidelity_promotion", self.test_multi_fidelity_promotion),
            ("test_performance_benchmarks", self.test_performance_benchmarks)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                logger.info(f"\n[TEST] Executando: {test_name}")
                result = await test_func()

                if result:
                    logger.info(f"[PASS] {test_name}: PASSOU")
                    passed += 1
                else:
                    logger.error(f"[FAIL] {test_name}: FALHOU")
                
                self.test_results[test_name] = result
                
            except Exception as e:
                logger.error(f"[ERROR] {test_name}: ERRO - {e}")
                self.test_results[test_name] = False
        
        # Relatório final
        logger.info("\n" + "=" * 60)
        logger.info(f"[STATS] RESULTADOS FINAIS: {passed}/{total} testes passaram")
        logger.info(f"Taxa de sucesso: {passed/total*100:.1f}%")

        if passed == total:
            logger.info("[SUCCESS] TODOS OS TESTES PASSARAM! D-04 está funcionando corretamente.")
        else:
            logger.warning("[WARNING] Alguns testes falharam. Revisar implementação.")
        
        return passed == total
    
    async def test_advanced_pruner(self) -> bool:
        """Testa o sistema de pruning avançado."""
        
        try:
            # Criar configuração de teste
            config = PruningConfig(
                strategy=PruningStrategy.ADAPTIVE,
                median_percentile=60.0,
                min_sharpe_threshold=0.8
            )
            
            pruner = AdvancedPruner(config)
            
            # Testar diferentes estratégias
            strategies = [
                PruningStrategy.MEDIAN,
                PruningStrategy.SUCCESSIVE_HALVING,
                PruningStrategy.ADAPTIVE,
                PruningStrategy.TRADING_AWARE
            ]
            
            for strategy in strategies:
                config.strategy = strategy
                pruner.config = config
                
                optuna_pruner = pruner.get_pruner(f"test_study_{strategy.value}")
                assert optuna_pruner is not None, f"Pruner não criado para {strategy.value}"
                
                logger.info(f"   [OK] Estratégia {strategy.value}: OK")

            # Testar métricas de trading
            metrics = TradingMetrics()
            metrics.sharpe_ratio = 0.5  # Abaixo do threshold
            metrics.max_drawdown = 0.20  # Acima do threshold
            metrics.trade_count = 15

            should_prune = metrics.should_prune(config)
            assert should_prune, "Deveria podar com métricas ruins"

            logger.info("   [OK] Trading metrics pruning: OK")

            # Testar estatísticas
            stats = pruner.get_pruning_stats()
            assert isinstance(stats, dict), "Estatísticas devem ser dict"

            logger.info("   [OK] Estatísticas: OK")
            
            return True
            
        except Exception as e:
            logger.error(f"Erro no teste de pruning: {e}")
            return False
    
    async def test_multi_fidelity_evaluator(self) -> bool:
        """Testa o avaliador multi-fidelidade."""
        
        try:
            # Criar configuração
            config = FidelityConfig(
                fidelity_levels=[FidelityLevel.LOW, FidelityLevel.MEDIUM, FidelityLevel.HIGH],
                budget_allocation={
                    FidelityLevel.LOW: 0.6,
                    FidelityLevel.MEDIUM: 0.3,
                    FidelityLevel.HIGH: 0.1
                }
            )
            
            evaluator = MultiFidelityEvaluator(config)
            
            # Criar função de avaliação mock
            async def mock_evaluation(symbol: str, params: Dict[str, float], fidelity_hours: int) -> TradingMetrics:
                # Simular avaliação baseada na fidelidade
                await asyncio.sleep(0.1)  # Simular tempo de processamento
                
                metrics = TradingMetrics()
                metrics.sharpe_ratio = 1.0 + (fidelity_hours / 24.0)  # Melhor com mais fidelidade
                metrics.max_drawdown = 0.10 - (fidelity_hours / 240.0)  # Menor drawdown com mais dados
                metrics.trade_count = fidelity_hours * 2  # Mais trades com mais tempo
                metrics.total_pnl = fidelity_hours * 100.0  # PnL proporcional
                
                return metrics
            
            evaluator.set_evaluation_function(mock_evaluation)
            
            # Criar trial mock
            class MockTrial:
                def __init__(self):
                    self.number = 1
                    self.params = {
                        "price_amplification": 2.0,
                        "news_amplification": 5.0,
                        "min_confidence": 0.6
                    }
            
            trial = MockTrial()
            
            # Testar avaliação
            result_metrics = await evaluator.evaluate_trial(trial, "BTCUSDT")
            
            assert result_metrics.sharpe_ratio > 0, "Sharpe ratio deve ser positivo"
            assert result_metrics.trade_count > 0, "Deve ter trades"
            
            logger.info("   [OK] Avaliação multi-fidelidade: OK")

            # Testar estatísticas
            stats = evaluator.get_stats()
            assert stats["total_evaluations"] > 0, "Deve ter avaliações registradas"

            logger.info("   [OK] Estatísticas multi-fidelidade: OK")
            
            return True
            
        except Exception as e:
            logger.error(f"Erro no teste multi-fidelidade: {e}")
            return False
    
    async def test_trading_metrics(self) -> bool:
        """Testa a classe TradingMetrics."""
        
        try:
            metrics = TradingMetrics()
            
            # Testar métricas inválidas
            assert not metrics.is_valid(), "Métricas vazias devem ser inválidas"
            
            # Preencher métricas
            metrics.sharpe_ratio = 1.5
            metrics.max_drawdown = 0.08
            metrics.trade_count = 25
            metrics.total_pnl = 1500.0
            
            assert metrics.is_valid(), "Métricas preenchidas devem ser válidas"
            
            # Testar critérios de pruning
            config = PruningConfig(
                min_sharpe_threshold=1.0,
                max_drawdown_threshold=0.10,
                min_trades_threshold=20
            )
            
            should_prune = metrics.should_prune(config)
            assert not should_prune, "Métricas boas não devem ser podadas"
            
            # Testar com métricas ruins
            metrics.sharpe_ratio = 0.3  # Muito baixo
            should_prune = metrics.should_prune(config)
            assert should_prune, "Métricas ruins devem ser podadas"
            
            logger.info("   [OK] TradingMetrics: OK")
            
            return True
            
        except Exception as e:
            logger.error(f"Erro no teste de trading metrics: {e}")
            return False
    
    async def test_market_regime_adaptation(self) -> bool:
        """Testa adaptação a regimes de mercado."""
        
        try:
            # Testar pruner
            pruner = AdvancedPruner()
            
            regimes = [MarketRegime.BULL, MarketRegime.BEAR, MarketRegime.VOLATILE, MarketRegime.STABLE]
            
            for regime in regimes:
                pruner.update_market_regime(regime)
                assert pruner.current_regime == regime, f"Regime não atualizado para {regime.value}"
                logger.info(f"   [OK] Regime {regime.value}: OK")

            # Testar multi-fidelidade
            evaluator = MultiFidelityEvaluator()

            for regime in regimes:
                evaluator.update_market_regime(regime)
                assert evaluator.current_regime == regime, f"Regime MF não atualizado para {regime.value}"

            logger.info("   [OK] Adaptação de regime: OK")
            
            return True
            
        except Exception as e:
            logger.error(f"Erro no teste de regime: {e}")
            return False
    
    async def test_bayesian_optimizer_integration(self) -> bool:
        """Testa integração com BayesianOptimizer."""
        
        try:
            # Criar configuração com D-04 habilitado
            config = OptimizationConfig(
                n_trials_per_cycle=5,  # Poucos trials para teste rápido
                pruning_strategy=PruningStrategy.ADAPTIVE,
                enable_multi_fidelity=True,
                multi_fidelity_levels=[1, 6]  # Apenas 2 níveis para teste
            )
            
            optimizer = BayesianOptimizer(config, use_realistic_evaluation=False)
            
            # Verificar componentes criados
            assert optimizer.advanced_pruner is not None, "AdvancedPruner não criado"
            assert optimizer.multi_fidelity_evaluator is not None, "MultiFidelityEvaluator não criado"
            
            logger.info("   [OK] Componentes D-04 criados: OK")

            # Testar atualização de regime
            optimizer.update_market_regime(MarketRegime.VOLATILE)
            assert optimizer.current_market_regime == MarketRegime.VOLATILE, "Regime não atualizado"

            logger.info("   [OK] Atualização de regime: OK")

            # Testar estatísticas
            stats = optimizer.get_optimization_stats()
            assert "pruning_stats" in stats, "Estatísticas de pruning não incluídas"
            assert "multi_fidelity_stats" in stats, "Estatísticas MF não incluídas"

            logger.info("   [OK] Estatísticas integradas: OK")
            
            return True
            
        except Exception as e:
            logger.error(f"Erro no teste de integração: {e}")
            return False
    
    async def test_pruning_strategies(self) -> bool:
        """Testa diferentes estratégias de pruning."""
        
        try:
            strategies = [
                PruningStrategy.NONE,
                PruningStrategy.MEDIAN,
                PruningStrategy.SUCCESSIVE_HALVING,
                PruningStrategy.ADAPTIVE,
                PruningStrategy.TRADING_AWARE
            ]
            
            for strategy in strategies:
                config = PruningConfig(strategy=strategy)
                pruner = AdvancedPruner(config)
                
                optuna_pruner = pruner.get_pruner()
                assert optuna_pruner is not None, f"Pruner não criado para {strategy.value}"
                
                logger.info(f"   [OK] Estratégia {strategy.value}: OK")
            
            return True
            
        except Exception as e:
            logger.error(f"Erro no teste de estratégias: {e}")
            return False
    
    async def test_multi_fidelity_promotion(self) -> bool:
        """Testa sistema de promoção multi-fidelidade."""
        
        try:
            config = FidelityConfig()
            evaluator = MultiFidelityEvaluator(config)
            
            # Criar resultado que deve ser promovido
            from qualia.optimization.multi_fidelity import FidelityResult
            
            good_metrics = TradingMetrics()
            good_metrics.sharpe_ratio = 1.5  # Bom Sharpe
            good_metrics.max_drawdown = 0.08  # Baixo drawdown
            good_metrics.trade_count = 20  # Suficientes trades
            
            result = FidelityResult(
                trial_number=1,
                fidelity_level=FidelityLevel.LOW,
                parameters={"price_amplification": 2.0},
                metrics=good_metrics,
                evaluation_time=10.0,
                timestamp=time.time()
            )
            
            # Testar critérios de promoção
            should_promote = evaluator._should_promote(result)
            # Note: pode não promover se não há histórico suficiente, mas não deve dar erro
            
            logger.info(f"   [OK] Critérios de promoção avaliados: {should_promote}")

            # Testar próximo nível
            next_level = evaluator._get_next_fidelity(FidelityLevel.LOW)
            assert next_level == FidelityLevel.MEDIUM, "Próximo nível incorreto"

            logger.info("   [OK] Sistema de promoção: OK")
            
            return True
            
        except Exception as e:
            logger.error(f"Erro no teste de promoção: {e}")
            return False
    
    async def test_performance_benchmarks(self) -> bool:
        """Testa benchmarks de performance."""
        
        try:
            # Testar tempo de criação de componentes
            start_time = time.time()
            
            pruner = AdvancedPruner()
            evaluator = MultiFidelityEvaluator()
            
            creation_time = time.time() - start_time
            assert creation_time < 1.0, f"Criação muito lenta: {creation_time:.3f}s"
            
            logger.info(f"   [OK] Tempo de criação: {creation_time:.3f}s")

            # Testar memória (básico)
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024

            logger.info(f"   [OK] Uso de memória: {memory_mb:.1f}MB")
            
            return True
            
        except Exception as e:
            logger.error(f"Erro no teste de performance: {e}")
            return False


async def main():
    """Função principal."""
    
    print("🚀 QUALIA D-04 TEST SUITE")
    print("Advanced Pruning & Multi-fidelity Optimization")
    print("=" * 60)
    
    # Criar diretório de logs se não existir
    Path("logs").mkdir(exist_ok=True)
    
    # Executar testes
    test_suite = D04TestSuite()
    success = await test_suite.run_all_tests()
    
    # Resultado final
    if success:
        print("\n[SUCCESS] D-04 IMPLEMENTATION: SUCCESS!")
        print("Sistema de pruning avançado e multi-fidelidade funcionando corretamente.")
        return 0
    else:
        print("\n[FAIL] D-04 IMPLEMENTATION: ISSUES FOUND")
        print("Revisar logs para detalhes dos problemas.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
