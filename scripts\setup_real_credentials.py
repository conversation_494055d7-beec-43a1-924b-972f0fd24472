#!/usr/bin/env python3
"""
QUALIA P-02.3: Real Credentials Setup
Secure setup process for real KuCoin production API credentials

This script replaces demo credentials with real KuCoin production API credentials
while maintaining the same encryption and security standards.

SECURITY FEATURES:
- Fernet encryption for credential storage
- Master key generation and management
- Input validation and sanitization
- Secure credential verification
- Backup of existing credentials
"""

import os
import sys
import json
import base64
import getpass
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

from cryptography.fernet import Fernet
import yaml

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RealCredentialSetup:
    """Secure setup for real KuCoin production API credentials"""
    
    def __init__(self):
        self.config_dir = Path("config")
        self.backup_dir = Path("backups/credentials")
        self.master_key_file = self.config_dir / ".pilot_master.key"
        self.credentials_file = self.config_dir / ".pilot_credentials"
        
        # Ensure directories exist
        self.config_dir.mkdir(exist_ok=True)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
    def generate_master_key(self) -> bytes:
        """Generate new master key for encryption"""
        return Fernet.generate_key()
    
    def encrypt_credentials(self, credentials: Dict[str, Any], key: bytes) -> str:
        """Encrypt credentials using Fernet encryption"""
        try:
            f = Fernet(key)
            credentials_json = json.dumps(credentials, indent=2)
            encrypted_data = f.encrypt(credentials_json.encode())
            return base64.b64encode(encrypted_data).decode()
        except Exception as e:
            raise Exception(f"Failed to encrypt credentials: {e}")
    
    def decrypt_credentials(self, encrypted_data: str, key: bytes) -> Dict[str, Any]:
        """Decrypt credentials for verification"""
        try:
            f = Fernet(key)
            encrypted_bytes = base64.b64decode(encrypted_data.encode())
            decrypted = f.decrypt(encrypted_bytes)
            return json.loads(decrypted.decode())
        except Exception as e:
            raise Exception(f"Failed to decrypt credentials: {e}")
    
    def backup_existing_credentials(self) -> bool:
        """Backup existing credentials before replacement"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Backup master key
            if self.master_key_file.exists():
                backup_key_file = self.backup_dir / f"pilot_master_key_{timestamp}.backup"
                backup_key_file.write_bytes(self.master_key_file.read_bytes())
                logger.info(f"Master key backed up to: {backup_key_file}")
            
            # Backup credentials
            if self.credentials_file.exists():
                backup_cred_file = self.backup_dir / f"pilot_credentials_{timestamp}.backup"
                backup_cred_file.write_text(self.credentials_file.read_text())
                logger.info(f"Credentials backed up to: {backup_cred_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to backup existing credentials: {e}")
            return False
    
    def validate_kucoin_credentials(self, api_key: str, api_secret: str, passphrase: str) -> bool:
        """Validate KuCoin API credentials format"""
        try:
            # Basic format validation
            if not api_key or len(api_key) < 20:
                logger.error("Invalid API key format")
                return False
            
            if not api_secret or len(api_secret) < 20:
                logger.error("Invalid API secret format")
                return False
            
            if not passphrase or len(passphrase) < 8:
                logger.error("Invalid passphrase format")
                return False
            
            # Check for common patterns
            if api_key.startswith('demo_') or 'test' in api_key.lower():
                logger.error("API key appears to be demo/test credentials")
                return False
            
            logger.info("✅ Credential format validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Credential validation failed: {e}")
            return False
    
    def collect_real_credentials(self) -> Optional[Dict[str, Any]]:
        """Securely collect real KuCoin API credentials from user"""
        try:
            print("\n" + "="*60)
            print("🔐 QUALIA P-02.3: Real KuCoin API Credentials Setup")
            print("="*60)
            print("\nIMPORTANT SECURITY NOTES:")
            print("• Ensure you're using KuCoin PRODUCTION API credentials")
            print("• API key must have TRADING permissions enabled")
            print("• Recommended: Create dedicated API key for QUALIA trading")
            print("• Enable IP whitelist if supported by your KuCoin account")
            print("• Keep your credentials secure and never share them")
            print("\n" + "="*60)
            
            # Collect API credentials
            api_key = getpass.getpass("Enter KuCoin API Key: ").strip()
            api_secret = getpass.getpass("Enter KuCoin API Secret: ").strip()
            passphrase = getpass.getpass("Enter KuCoin API Passphrase: ").strip()
            
            # Validate credentials
            if not self.validate_kucoin_credentials(api_key, api_secret, passphrase):
                return None
            
            # Create credential structure
            credentials = {
                "environment": "production",  # Changed from "pilot" to "production"
                "exchange": "kucoin",
                "api_version": "v2",
                "created_at": datetime.now().isoformat(),
                "credentials": {
                    "api_key": api_key,
                    "api_secret": api_secret,
                    "passphrase": passphrase
                },
                "permissions": {
                    "trading": True,
                    "futures": False,  # Disabled for safety
                    "margin": False    # Disabled for safety
                },
                "security": {
                    "ip_whitelist_enabled": False,  # User can enable manually
                    "withdrawal_whitelist_enabled": True
                },
                "limits": {
                    "max_capital_usd": 1000.0,
                    "max_position_size_usd": 20.0,
                    "max_daily_trades": 10
                }
            }
            
            return credentials
            
        except KeyboardInterrupt:
            print("\n\nSetup cancelled by user.")
            return None
        except Exception as e:
            logger.error(f"Failed to collect credentials: {e}")
            return None
    
    def setup_real_credentials(self) -> bool:
        """Main setup process for real credentials"""
        try:
            logger.info("🚀 Starting real credentials setup...")
            
            # 1. Backup existing credentials
            if not self.backup_existing_credentials():
                logger.error("Failed to backup existing credentials")
                return False
            
            # 2. Collect real credentials
            credentials = self.collect_real_credentials()
            if not credentials:
                logger.error("Failed to collect valid credentials")
                return False
            
            # 3. Generate new master key
            master_key = self.generate_master_key()
            
            # 4. Encrypt credentials
            encrypted_credentials = self.encrypt_credentials(credentials, master_key)
            
            # 5. Save master key
            self.master_key_file.write_bytes(master_key)
            os.chmod(self.master_key_file, 0o600)  # Restrict permissions
            logger.info(f"✅ Master key saved: {self.master_key_file}")
            
            # 6. Save encrypted credentials
            self.credentials_file.write_text(encrypted_credentials)
            os.chmod(self.credentials_file, 0o600)  # Restrict permissions
            logger.info(f"✅ Encrypted credentials saved: {self.credentials_file}")
            
            # 7. Verify credentials can be decrypted
            decrypted = self.decrypt_credentials(encrypted_credentials, master_key)
            if decrypted.get('environment') != 'production':
                raise Exception("Credential verification failed")
            
            logger.info("✅ Credential verification successful")
            
            # 8. Update pilot configuration
            self.update_pilot_config_for_production()
            
            print("\n" + "="*60)
            print("🎉 REAL CREDENTIALS SETUP COMPLETED SUCCESSFULLY!")
            print("="*60)
            print("✅ KuCoin production API credentials encrypted and stored")
            print("✅ Master key generated and secured")
            print("✅ Pilot configuration updated for production mode")
            print("✅ Backup of previous credentials created")
            print("\nNEXT STEPS:")
            print("1. Run live trading validation: python scripts/validate_live_trading.py")
            print("2. Start pilot trading: python scripts/start_pilot_trading.py")
            print("3. Monitor trading: python scripts/monitor_pilot_trading.py")
            print("="*60)
            
            return True
            
        except Exception as e:
            logger.error(f"Real credentials setup failed: {e}")
            return False
    
    def update_pilot_config_for_production(self):
        """Update pilot configuration for production trading"""
        try:
            config_file = self.config_dir / "pilot_config.yaml"
            
            # Load current config
            with open(config_file, 'r') as f:
                config = yaml.safe_load(f)
            
            # Update for production
            config['qualia_integration']['mode'] = 'live'  # Changed from paper_trading
            config['exchange']['environment'] = 'production'
            
            # Add production validation settings
            config['validation']['production_mode'] = True
            config['validation']['real_credentials'] = True
            
            # Save updated config
            with open(config_file, 'w') as f:
                yaml.dump(config, f, default_flow_style=False, indent=2)
            
            logger.info("✅ Pilot configuration updated for production mode")
            
        except Exception as e:
            logger.error(f"Failed to update pilot configuration: {e}")
            raise

def main():
    """Main entry point"""
    try:
        setup = RealCredentialSetup()
        
        print("🔐 QUALIA P-02.3: Real Credentials Setup")
        print("This will replace demo credentials with real KuCoin production API credentials.")
        
        confirm = input("\nProceed with real credentials setup? (yes/no): ").strip().lower()
        if confirm not in ['yes', 'y']:
            print("Setup cancelled.")
            return False
        
        success = setup.setup_real_credentials()
        
        if success:
            print("\n✅ Real credentials setup completed successfully!")
            return True
        else:
            print("\n❌ Real credentials setup failed!")
            return False
            
    except KeyboardInterrupt:
        print("\n\nSetup cancelled by user.")
        return False
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
