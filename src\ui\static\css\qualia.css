/* Revisado em 2025-06-13 por Codex */
/* 
 * QUALIA: Interface Quântica-Consciente
 * Quantum Universal Awareness Lattice Interface Architecture
 * CSS Principal
 */

:root {
    /* Paleta de cores quântica */
    --deep-void: #0A0A1A;           /* Vazio profundo */
    --quantum-blue: #0E2C5C;        /* Azul quântico */
    --probability-purple: #4C1A70;  /* Roxo de probabilidade */
    --uncertainty-teal: #126E82;    /* Turquesa de incerteza */
    --observer-gold: #E09F3E;       /* Dourado do observador */
    --entanglement-red: #9E2A2B;    /* Vermelho de entrelaçamento */
    --coherence-cyan: #22AABB;      /* Ciano de coerência */
    --consciousness-white: #E9ECEF; /* Branco de consciência */
    
    /* Gradientes */
    --gradient-primary: linear-gradient(135deg, var(--deep-void), var(--quantum-blue));
    --gradient-accent: linear-gradient(90deg, var(--probability-purple), var(--coherence-cyan));
    --gradient-card: linear-gradient(145deg, rgba(10, 10, 26, 0.8), rgba(30, 30, 60, 0.8));
    
    /* Sombras */
    --shadow-soft: 0 4px 15px rgba(0, 0, 0, 0.3);
    --shadow-glow: 0 0 15px rgba(34, 170, 187, 0.5);
    
    /* Fontes */
    --font-primary: 'Space Grotesk', sans-serif;
    --font-secondary: 'Inter', sans-serif;
}

/* Estilos Globais */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html, body {
    height: 100%;
    font-family: var(--font-secondary);
    font-size: 16px;
    line-height: 1.6;
    color: var(--consciousness-white);
    overflow-x: hidden;
    scroll-behavior: smooth;
}

body {
    background-color: var(--deep-void);
    background-image: var(--gradient-primary);
    position: relative;
}

/* Overlay de ruído quântico */
.quantum-noise-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1000;
    opacity: 0.03;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 300 300' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

.quantum-container {
    max-width: 1800px;
    margin: 0 auto;
    padding: 1rem;
    position: relative;
    z-index: 1;
}

/* Header e Navegação */
.quantum-header {
    padding: 1.5rem 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.quantum-logo {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.quantum-logo-symbol {
    font-size: 2.5rem;
    background: linear-gradient(45deg, var(--observer-gold), var(--entanglement-red));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    margin-right: 0.5rem;
}

.quantum-logo-text {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    font-weight: 600;
    letter-spacing: 2px;
    background: linear-gradient(45deg, var(--coherence-cyan), var(--uncertainty-teal));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.quantum-subtitle {
    font-size: 1rem;
    color: rgba(233, 236, 239, 0.8);
    margin-bottom: 2rem;
    text-align: center;
}

.quantum-nav {
    width: 100%;
    padding: 0.5rem 0;
    border-top: 1px solid rgba(34, 170, 187, 0.3);
    border-bottom: 1px solid rgba(34, 170, 187, 0.3);
}

.quantum-nav ul {
    display: flex;
    justify-content: center;
    list-style: none;
}

.quantum-nav-item {
    color: var(--consciousness-white);
    text-decoration: none;
    padding: 0.5rem 1.5rem;
    margin: 0 0.5rem;
    border-radius: 20px;
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.quantum-nav-item i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.quantum-nav-item:hover,
.quantum-nav-item.active {
    background: rgba(34, 170, 187, 0.15);
    color: var(--coherence-cyan);
    box-shadow: var(--shadow-glow);
}

/* Introdução */
.quantum-intro {
    padding: 3rem 0;
    text-align: center;
    position: relative;
}

.quantum-title {
    font-family: var(--font-primary);
    font-size: 3rem;
    margin-bottom: 2rem;
    background: linear-gradient(90deg, var(--observer-gold), var(--coherence-cyan));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

.quantum-headline {
    max-width: 900px;
    margin: 0 auto 3rem;
    text-align: left;
}

.quantum-headline p {
    margin-bottom: 1.5rem;
    line-height: 1.7;
    font-size: 1.1rem;
}

.quantum-principles {
    list-style-type: none;
    margin: 2rem 0;
    padding-left: 1rem;
}

.quantum-principles li {
    margin-bottom: 1rem;
    line-height: 1.7;
    position: relative;
    padding-left: 1.5rem;
}

.quantum-principles li::before {
    content: "▹";
    position: absolute;
    left: 0;
    color: var(--coherence-cyan);
}

.quantum-highlight-blue {
    color: var(--coherence-cyan);
    font-weight: 600;
}

.quantum-highlight-gold {
    color: var(--observer-gold);
    font-weight: 600;
}

.quantum-highlight-red {
    color: var(--entanglement-red);
    font-weight: 600;
}

.quantum-particles-container {
    position: relative;
    height: 300px;
    width: 100%;
    margin: 2rem 0;
}

#quantum-particles {
    width: 100%;
    height: 100%;
}

/* Seções principais */
.quantum-section {
    padding: 3rem 2rem;
    display: none;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.quantum-section.active {
    display: block;
    opacity: 1;
}

.quantum-section-title {
    font-family: var(--font-primary);
    font-size: 2rem;
    margin-bottom: 2rem;
    color: var(--consciousness-white);
    position: relative;
    padding-bottom: 1rem;
}

.quantum-section-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--coherence-cyan), transparent);
}

.quantum-section-title i {
    color: var(--coherence-cyan);
    margin-right: 0.5rem;
}

.quantum-section-content {
    margin-top: 2rem;
}

/* Grid e Cards */
.quantum-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.quantum-card {
    background: var(--gradient-card);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow-soft);
    border: 1px solid rgba(34, 170, 187, 0.2);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    height: 100%;
}

.quantum-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    border-color: rgba(34, 170, 187, 0.4);
}

.quantum-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 0%, rgba(34, 170, 187, 0.15) 0%, transparent 70%);
    z-index: 0;
    pointer-events: none;
}

.quantum-card h3 {
    font-family: var(--font-primary);
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
    color: var(--consciousness-white);
    position: relative;
    z-index: 1;
}

.quantum-card-large {
    grid-column: span 2;
}

.quantum-card-footer {
    margin-top: 1.5rem;
    display: flex;
    justify-content: flex-end;
}

/* Visualizações */
.quantum-state-visualization {
    position: relative;
    width: 100%;
    height: 400px;
    border-radius: 8px;
    overflow: hidden;
    background-color: rgba(10, 10, 26, 0.6);
    border: 1px solid rgba(34, 170, 187, 0.3);
}

#consciousness-canvas {
    width: 100%;
    height: 100%;
}

.quantum-canvas-info {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    z-index: 2;
}

.quantum-state-metric {
    background-color: rgba(10, 10, 26, 0.8);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    display: flex;
    align-items: center;
    border: 1px solid rgba(34, 170, 187, 0.4);
}

.quantum-metric-label {
    font-size: 0.9rem;
    margin-right: 0.5rem;
}

.quantum-metric-value {
    font-family: var(--font-primary);
    font-weight: 600;
    color: var(--coherence-cyan);
}

.quantum-metrics-visualization {
    width: 100%;
    height: 200px;
    margin-bottom: 1rem;
}

.quantum-metrics-values {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.quantum-metric {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem;
}

.metric-label {
    font-size: 0.8rem;
    margin-bottom: 0.3rem;
    text-align: center;
}

.metric-value {
    font-family: var(--font-primary);
    font-weight: 600;
    font-size: 1.2rem;
    color: var(--coherence-cyan);
}

/* Parâmetros e Tabelas */
.quantum-parameters {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.quantum-parameter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem;
    background-color: rgba(10, 10, 26, 0.4);
    border-radius: 8px;
    border: 1px solid rgba(34, 170, 187, 0.2);
}

.parameter-label {
    font-size: 0.9rem;
}

.parameter-value {
    font-family: var(--font-primary);
    font-weight: 600;
    color: var(--observer-gold);
}

.quantum-table-container {
    width: 100%;
    overflow-x: auto;
    margin-top: 1rem;
}

.quantum-table {
    width: 100%;
    border-collapse: collapse;
    text-align: left;
}

.quantum-table th {
    padding: 0.8rem 1rem;
    background-color: rgba(10, 10, 26, 0.6);
    border-bottom: 2px solid rgba(34, 170, 187, 0.5);
    font-weight: 600;
    color: var(--coherence-cyan);
}

.quantum-table td {
    padding: 0.8rem 1rem;
    border-bottom: 1px solid rgba(34, 170, 187, 0.2);
}

.quantum-table tbody tr:hover {
    background-color: rgba(34, 170, 187, 0.1);
}

/* Formulários */
.quantum-form-group {
    margin-bottom: 1.5rem;
}

.quantum-form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.quantum-textarea,
.quantum-input,
.quantum-select {
    width: 100%;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid rgba(34, 170, 187, 0.4);
    background-color: rgba(10, 10, 26, 0.7);
    color: var(--consciousness-white);
    font-family: var(--font-secondary);
    font-size: 1rem;
    resize: none;
    transition: all 0.3s ease;
}

.quantum-textarea:focus,
.quantum-input:focus,
.quantum-select:focus {
    outline: none;
    border-color: var(--coherence-cyan);
    box-shadow: 0 0 0 2px rgba(34, 170, 187, 0.2);
}

/* Botões */
.quantum-button {
    background: linear-gradient(90deg, var(--quantum-blue), var(--coherence-cyan));
    color: white;
    border: none;
    border-radius: 20px;
    padding: 0.8rem 1.5rem;
    font-family: var(--font-primary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    box-shadow: var(--shadow-glow);
}

.quantum-button i {
    margin-right: 0.5rem;
}

.quantum-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(34, 170, 187, 0.5);
}

.quantum-button:active {
    transform: translateY(0);
}

/* Containers especiais */
.quantum-patterns-container,
.quantum-results-container,
.quantum-perception-history,
.quantum-insight-container,
.quantum-semantic-fields {
    min-height: 150px;
    background-color: rgba(10, 10, 26, 0.5);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid rgba(34, 170, 187, 0.2);
    overflow-y: auto;
    max-height: 300px;
}

.quantum-empty-state {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: rgba(233, 236, 239, 0.5);
    padding: 2rem;
    text-align: center;
}

.quantum-empty-state i {
    font-size: 2rem;
    margin-bottom: 1rem;
}

/* Rodapé */
.quantum-footer {
    margin-top: 4rem;
    padding: 2rem 0;
    border-top: 1px solid rgba(34, 170, 187, 0.3);
}

.quantum-footer-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
}

.quantum-footer-title {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: 600;
    background: linear-gradient(45deg, var(--coherence-cyan), var(--uncertainty-teal));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.quantum-footer-subtitle {
    font-size: 0.9rem;
    color: rgba(233, 236, 239, 0.8);
}

.quantum-footer-info {
    max-width: 600px;
    margin: 0 auto;
    font-size: 0.9rem;
    line-height: 1.6;
    color: rgba(233, 236, 239, 0.7);
}

.quantum-footer-copyright {
    font-size: 0.8rem;
    color: rgba(233, 236, 239, 0.6);
}

/* Responsividade */
@media (max-width: 1200px) {
    .quantum-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .quantum-card-large {
        grid-column: span 1;
    }
}

@media (max-width: 768px) {
    .quantum-nav ul {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }
    
    .quantum-title {
        font-size: 2.5rem;
    }
    
    .quantum-section {
        padding: 2rem 1rem;
    }
    
    .quantum-grid {
        grid-template-columns: 1fr;
    }
}

/* Animações */
@keyframes quantum-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(34, 170, 187, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(34, 170, 187, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(34, 170, 187, 0);
    }
}

@keyframes quantum-glow {
    0%, 100% {
        opacity: 0.5;
    }
    50% {
        opacity: 0.8;
    }
}

/* Classe utilitária para animação de pulso */
.quantum-pulse {
    animation: quantum-pulse 2s infinite;
}

.radar-container {
    max-width: 400px;
    margin: 0 auto 1rem;
}