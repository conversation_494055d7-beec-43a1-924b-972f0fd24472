#!/usr/bin/env python3
"""
Diagnóstico Rápido: Por que Quantum Momentum perde apesar do alto win rate?
"""

import pandas as pd
import numpy as np
import requests
from datetime import datetime, timedelta


def fetch_btc_data():
    """Busca dados do BTC rapidamente."""
    end_time = int(datetime.now().timestamp() * 1000)
    start_time = int((datetime.now() - timedelta(days=30)).timestamp() * 1000)
    
    url = "https://api.binance.com/api/v3/klines"
    params = {
        'symbol': 'BTCUSDT',
        'interval': '1h',
        'startTime': start_time,
        'endTime': end_time,
        'limit': 500
    }
    
    response = requests.get(url, params=params)
    data = response.json()
    
    df = pd.DataFrame(data, columns=[
        'timestamp', 'open', 'high', 'low', 'close', 'volume',
        'close_time', 'quote_asset_volume', 'number_of_trades',
        'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
    ])
    
    df['close'] = pd.to_numeric(df['close'])
    df['volume'] = pd.to_numeric(df['volume'])
    df['sma_20'] = df['close'].rolling(20).mean()
    
    # RSI simples
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    return df


def analyze_quantum_momentum_trades():
    """Analisa trades da Quantum Momentum em detalhes."""
    print("🔍 DIAGNÓSTICO: Por que Quantum Momentum perde?")
    print("=" * 50)
    
    df = fetch_btc_data()
    
    trades = []
    
    # Simula estratégia Quantum Momentum
    for i in range(50, len(df)-1):
        # Momentum clássico
        price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
        
        # Momentum de volume
        vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
        
        # RSI momentum
        rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
        
        # Sinal combinado
        signal = (
            price_momentum * 0.5 +
            vol_momentum * 0.3 +
            rsi_momentum * 0.2
        )
        
        if abs(signal) > 0.015:  # Threshold
            position = np.clip(signal * 8, -1, 1)
            
            # Calcula retorno do trade
            entry_price = df['close'].iloc[i]
            exit_price = df['close'].iloc[i+1]
            price_change = (exit_price - entry_price) / entry_price
            trade_return = position * price_change
            
            trades.append({
                'entry_price': entry_price,
                'exit_price': exit_price,
                'position': position,
                'price_change_pct': price_change * 100,
                'trade_return_pct': trade_return * 100,
                'is_win': trade_return > 0,
                'signal_strength': abs(signal),
                'price_momentum': price_momentum,
                'vol_momentum': vol_momentum,
                'rsi': df['rsi'].iloc[i]
            })
    
    # Análise dos trades
    trades_df = pd.DataFrame(trades)
    
    if len(trades_df) == 0:
        print("❌ Nenhum trade encontrado")
        return
    
    winning_trades = trades_df[trades_df['is_win']]
    losing_trades = trades_df[~trades_df['is_win']]
    
    print(f"📊 ESTATÍSTICAS GERAIS:")
    print(f"   Total de trades: {len(trades_df)}")
    print(f"   Trades vencedores: {len(winning_trades)} ({len(winning_trades)/len(trades_df)*100:.1f}%)")
    print(f"   Trades perdedores: {len(losing_trades)} ({len(losing_trades)/len(trades_df)*100:.1f}%)")
    print(f"   Retorno total: {trades_df['trade_return_pct'].sum():.2f}%")
    
    print(f"\n💚 TRADES VENCEDORES:")
    print(f"   Ganho médio: {winning_trades['trade_return_pct'].mean():.3f}%")
    print(f"   Maior ganho: {winning_trades['trade_return_pct'].max():.3f}%")
    print(f"   Ganho total: {winning_trades['trade_return_pct'].sum():.2f}%")
    
    print(f"\n💔 TRADES PERDEDORES:")
    print(f"   Perda média: {losing_trades['trade_return_pct'].mean():.3f}%")
    print(f"   Maior perda: {losing_trades['trade_return_pct'].min():.3f}%")
    print(f"   Perda total: {losing_trades['trade_return_pct'].sum():.2f}%")
    
    # Diagnóstico principal
    avg_win = winning_trades['trade_return_pct'].mean() if len(winning_trades) > 0 else 0
    avg_loss = losing_trades['trade_return_pct'].mean() if len(losing_trades) > 0 else 0
    win_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0
    
    print(f"\n🔍 DIAGNÓSTICO:")
    print(f"   Win/Loss Ratio: {win_loss_ratio:.2f}")
    
    if win_loss_ratio < 1.0:
        print(f"   ⚠️  PROBLEMA: Perdas médias MAIORES que ganhos médios!")
        print(f"   💡 Ganho médio: {avg_win:.3f}% vs Perda média: {avg_loss:.3f}%")
        print(f"   🎯 Para ser lucrativa, precisa de win rate > {abs(avg_loss)/(abs(avg_loss)+avg_win)*100:.1f}%")
        print(f"   📊 Win rate atual: {len(winning_trades)/len(trades_df)*100:.1f}%")
    
    # Análise por força do sinal
    print(f"\n📈 ANÁLISE POR FORÇA DO SINAL:")
    
    # Divide em quartis por força do sinal
    quartiles = trades_df['signal_strength'].quantile([0.25, 0.5, 0.75])
    
    weak_signals = trades_df[trades_df['signal_strength'] <= quartiles[0.25]]
    medium_signals = trades_df[(trades_df['signal_strength'] > quartiles[0.25]) & 
                              (trades_df['signal_strength'] <= quartiles[0.75])]
    strong_signals = trades_df[trades_df['signal_strength'] > quartiles[0.75]]
    
    for name, group in [("Sinais Fracos", weak_signals), 
                       ("Sinais Médios", medium_signals), 
                       ("Sinais Fortes", strong_signals)]:
        if len(group) > 0:
            win_rate = (group['is_win']).mean() * 100
            avg_return = group['trade_return_pct'].mean()
            print(f"   {name}: Win Rate {win_rate:.1f}%, Retorno Médio {avg_return:.3f}%")
    
    # Análise por condições de mercado
    print(f"\n🌊 ANÁLISE POR RSI:")
    
    oversold = trades_df[trades_df['rsi'] < 30]
    neutral = trades_df[(trades_df['rsi'] >= 30) & (trades_df['rsi'] <= 70)]
    overbought = trades_df[trades_df['rsi'] > 70]
    
    for name, group in [("RSI Oversold (<30)", oversold),
                       ("RSI Neutral (30-70)", neutral),
                       ("RSI Overbought (>70)", overbought)]:
        if len(group) > 0:
            win_rate = (group['is_win']).mean() * 100
            avg_return = group['trade_return_pct'].mean()
            print(f"   {name}: Win Rate {win_rate:.1f}%, Retorno Médio {avg_return:.3f}%")
    
    print(f"\n🎯 CONCLUSÕES:")
    
    total_wins = winning_trades['trade_return_pct'].sum()
    total_losses = abs(losing_trades['trade_return_pct'].sum())
    
    print(f"   • Total ganho: +{total_wins:.2f}%")
    print(f"   • Total perda: -{total_losses:.2f}%")
    print(f"   • Resultado líquido: {total_wins - total_losses:.2f}%")
    
    if total_losses > total_wins:
        print(f"   ⚠️  PROBLEMA: Perdas totais > Ganhos totais")
        print(f"   💡 SOLUÇÕES POSSÍVEIS:")
        print(f"      1. Aumentar take-profit (ganhos muito pequenos)")
        print(f"      2. Melhorar stop-loss (perdas muito grandes)")
        print(f"      3. Filtrar sinais fracos (usar threshold maior)")
        print(f"      4. Evitar trades em RSI extremos")
    
    # Mostra piores trades
    print(f"\n💥 TOP 5 PIORES TRADES:")
    worst_trades = trades_df.nsmallest(5, 'trade_return_pct')
    for i, trade in worst_trades.iterrows():
        print(f"   {trade['trade_return_pct']:.3f}% | RSI: {trade['rsi']:.1f} | Signal: {trade['signal_strength']:.3f}")
    
    print(f"\n🔥 TOP 5 MELHORES TRADES:")
    best_trades = trades_df.nlargest(5, 'trade_return_pct')
    for i, trade in best_trades.iterrows():
        print(f"   {trade['trade_return_pct']:.3f}% | RSI: {trade['rsi']:.1f} | Signal: {trade['signal_strength']:.3f}")


if __name__ == "__main__":
    analyze_quantum_momentum_trades()
