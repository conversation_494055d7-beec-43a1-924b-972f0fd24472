#!/usr/bin/env python
"""Inspeciona o arquivo da QuantumPatternMemory e exibe padrões resumidos."""

from __future__ import annotations

import argparse
from pathlib import Path
from typing import Any

from qualia.memory.quantum_pattern_memory import QuantumPatternMemory
from qualia.config.settings import qpm_memory_file


def main() -> None:
    parser = argparse.ArgumentParser(description="Exibe padrões gravados no QPM")
    parser.add_argument(
        "--path",
        default=qpm_memory_file,
        help="Caminho para o arquivo de memória",
    )
    args = parser.parse_args()
    qpm = QuantumPatternMemory(
        persistence_path=args.path,
        enable_warmstart=False,
        auto_persist=False,
    )
    total = 0
    for dim, patterns in qpm.memory.items():
        print(f"Dimensão {dim}D: {len(patterns)} padrões")
        for p in patterns:
            ts = p.get("timestamp")
            scenario = p.get("market_snapshot", {}).get("scenario_type")
            pid = p.get("id")
            print(f"  id={pid} ts={ts} scenario={scenario}")
            total += 1
    if total == 0:
        print("Nenhum padrão encontrado.")


if __name__ == "__main__":
    main()
