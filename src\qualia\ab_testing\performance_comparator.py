"""
QUALIA Performance Comparator - D-07.2 Implementation

Sistema para comparar métricas de performance entre simulador e trading ao vivo.
Calcula métricas chave, tracking em tempo real e significância estatística.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple, Any
import logging

from ..utils.logging_config import get_qualia_logger

logger = get_qualia_logger(__name__)


@dataclass
class TradeResult:
    """Resultado de um trade individual."""
    
    timestamp: datetime
    symbol: str
    side: str  # "buy" ou "sell"
    entry_price: float
    exit_price: float
    quantity: float
    pnl: float
    pnl_pct: float
    duration_seconds: float
    fees: float = 0.0
    slippage: float = 0.0
    
    @property
    def is_profitable(self) -> bool:
        return self.pnl > 0


@dataclass
class PerformanceMetrics:
    """Métricas de performance de uma sessão de trading."""
    
    # Identificação
    session_id: str
    session_type: str  # "simulator" ou "live"
    start_time: datetime
    end_time: Optional[datetime] = None
    
    # Métricas básicas
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    
    # PnL
    total_pnl: float = 0.0
    total_pnl_pct: float = 0.0
    gross_profit: float = 0.0
    gross_loss: float = 0.0
    
    # Ratios
    win_rate: float = 0.0
    profit_factor: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    
    # Drawdown
    max_drawdown: float = 0.0
    max_drawdown_pct: float = 0.0
    current_drawdown: float = 0.0
    
    # Timing
    avg_trade_duration_minutes: float = 0.0
    avg_winning_duration_minutes: float = 0.0
    avg_losing_duration_minutes: float = 0.0
    
    # Distribuições
    avg_win: float = 0.0
    avg_loss: float = 0.0
    largest_win: float = 0.0
    largest_loss: float = 0.0
    
    # Custos
    total_fees: float = 0.0
    total_slippage: float = 0.0
    
    # Dados históricos
    equity_curve: List[float] = field(default_factory=list)
    daily_returns: List[float] = field(default_factory=list)
    trade_history: List[TradeResult] = field(default_factory=list)
    
    # Metadados
    symbols_traded: List[str] = field(default_factory=list)
    last_update: datetime = field(default_factory=datetime.now)


@dataclass
class PerformanceComparison:
    """Comparação entre duas sessões de performance."""
    
    simulator_metrics: PerformanceMetrics
    live_metrics: PerformanceMetrics
    
    # Diferenças absolutas
    pnl_difference: float = 0.0
    sharpe_difference: float = 0.0
    drawdown_difference: float = 0.0
    win_rate_difference: float = 0.0
    
    # Diferenças percentuais
    pnl_difference_pct: float = 0.0
    sharpe_difference_pct: float = 0.0
    drawdown_difference_pct: float = 0.0
    win_rate_difference_pct: float = 0.0
    
    # Análise de correlação
    returns_correlation: float = 0.0
    equity_correlation: float = 0.0
    
    # Scores de qualidade
    overall_similarity_score: float = 0.0  # 0-1, onde 1 = idêntico
    performance_divergence_score: float = 0.0  # 0-1, onde 0 = sem divergência
    
    # Análise temporal
    divergence_over_time: List[float] = field(default_factory=list)
    correlation_over_time: List[float] = field(default_factory=list)


class PerformanceComparator:
    """Comparador de performance entre simulador e trading ao vivo."""
    
    def __init__(self):
        self.simulator_metrics: Optional[PerformanceMetrics] = None
        self.live_metrics: Optional[PerformanceMetrics] = None
        self.comparison: Optional[PerformanceComparison] = None
        
        # Configurações
        self.update_interval_seconds = 60  # Update a cada minuto
        self.rolling_window_minutes = 60  # Janela de 1 hora para métricas móveis
        
        logger.info("📊 PerformanceComparator inicializado")
    
    def initialize_session(self, session_id: str, session_type: str) -> PerformanceMetrics:
        """Inicializa uma nova sessão de métricas."""
        metrics = PerformanceMetrics(
            session_id=session_id,
            session_type=session_type,
            start_time=datetime.now()
        )
        
        if session_type == "simulator":
            self.simulator_metrics = metrics
        elif session_type == "live":
            self.live_metrics = metrics
        else:
            raise ValueError(f"Tipo de sessão inválido: {session_type}")
        
        logger.info(f"📈 Sessão {session_type} inicializada: {session_id}")
        return metrics
    
    def add_trade(self, session_type: str, trade: TradeResult) -> None:
        """Adiciona um trade e atualiza métricas."""
        metrics = self._get_metrics(session_type)
        if not metrics:
            raise ValueError(f"Sessão {session_type} não inicializada")
        
        # Adicionar trade ao histórico
        metrics.trade_history.append(trade)
        
        # Atualizar métricas básicas
        metrics.total_trades += 1
        metrics.total_pnl += trade.pnl
        metrics.total_fees += trade.fees
        metrics.total_slippage += trade.slippage
        
        if trade.is_profitable:
            metrics.winning_trades += 1
            metrics.gross_profit += trade.pnl
        else:
            metrics.losing_trades += 1
            metrics.gross_loss += abs(trade.pnl)
        
        # Adicionar símbolo se novo
        if trade.symbol not in metrics.symbols_traded:
            metrics.symbols_traded.append(trade.symbol)
        
        # Recalcular métricas derivadas
        self._recalculate_metrics(metrics)
        
        logger.debug(f"📊 Trade adicionado à sessão {session_type}: {trade.pnl:.2f}")
    
    def update_equity(self, session_type: str, equity_value: float) -> None:
        """Atualiza valor do equity e curva de equity."""
        metrics = self._get_metrics(session_type)
        if not metrics:
            return
        
        metrics.equity_curve.append(equity_value)
        metrics.last_update = datetime.now()
        
        # Calcular drawdown atual
        if metrics.equity_curve:
            peak = max(metrics.equity_curve)
            current_dd = (peak - equity_value) / peak if peak > 0 else 0
            metrics.current_drawdown = current_dd
            metrics.max_drawdown = max(metrics.max_drawdown, current_dd)
    
    def calculate_comparison(self) -> Optional[PerformanceComparison]:
        """Calcula comparação entre simulador e live trading."""
        if not self.simulator_metrics or not self.live_metrics:
            logger.warning("⚠️ Métricas incompletas para comparação")
            return None
        
        logger.info("🔄 Calculando comparação de performance...")
        
        sim = self.simulator_metrics
        live = self.live_metrics
        
        # Diferenças absolutas
        pnl_diff = sim.total_pnl - live.total_pnl
        sharpe_diff = sim.sharpe_ratio - live.sharpe_ratio
        drawdown_diff = sim.max_drawdown - live.max_drawdown
        win_rate_diff = sim.win_rate - live.win_rate
        
        # Diferenças percentuais
        pnl_diff_pct = (pnl_diff / abs(live.total_pnl)) * 100 if live.total_pnl != 0 else 0
        sharpe_diff_pct = (sharpe_diff / abs(live.sharpe_ratio)) * 100 if live.sharpe_ratio != 0 else 0
        drawdown_diff_pct = (drawdown_diff / live.max_drawdown) * 100 if live.max_drawdown != 0 else 0
        win_rate_diff_pct = (win_rate_diff / live.win_rate) * 100 if live.win_rate != 0 else 0
        
        # Correlações
        returns_corr = self._calculate_returns_correlation(sim, live)
        equity_corr = self._calculate_equity_correlation(sim, live)
        
        # Scores de qualidade
        similarity_score = self._calculate_similarity_score(sim, live)
        divergence_score = 1.0 - similarity_score
        
        self.comparison = PerformanceComparison(
            simulator_metrics=sim,
            live_metrics=live,
            pnl_difference=pnl_diff,
            sharpe_difference=sharpe_diff,
            drawdown_difference=drawdown_diff,
            win_rate_difference=win_rate_diff,
            pnl_difference_pct=pnl_diff_pct,
            sharpe_difference_pct=sharpe_diff_pct,
            drawdown_difference_pct=drawdown_diff_pct,
            win_rate_difference_pct=win_rate_diff_pct,
            returns_correlation=returns_corr,
            equity_correlation=equity_corr,
            overall_similarity_score=similarity_score,
            performance_divergence_score=divergence_score
        )
        
        logger.info(f"✅ Comparação calculada - Similaridade: {similarity_score:.3f}")
        return self.comparison
    
    def get_real_time_metrics(self, session_type: str) -> Optional[Dict[str, Any]]:
        """Retorna métricas em tempo real para uma sessão."""
        metrics = self._get_metrics(session_type)
        if not metrics:
            return None
        
        return {
            "session_id": metrics.session_id,
            "session_type": metrics.session_type,
            "total_trades": metrics.total_trades,
            "total_pnl": metrics.total_pnl,
            "total_pnl_pct": metrics.total_pnl_pct,
            "win_rate": metrics.win_rate,
            "sharpe_ratio": metrics.sharpe_ratio,
            "max_drawdown": metrics.max_drawdown,
            "current_drawdown": metrics.current_drawdown,
            "last_update": metrics.last_update.isoformat(),
            "symbols_traded": metrics.symbols_traded,
        }
    
    def _get_metrics(self, session_type: str) -> Optional[PerformanceMetrics]:
        """Retorna métricas para um tipo de sessão."""
        if session_type == "simulator":
            return self.simulator_metrics
        elif session_type == "live":
            return self.live_metrics
        return None
    
    def _recalculate_metrics(self, metrics: PerformanceMetrics) -> None:
        """Recalcula métricas derivadas."""
        if metrics.total_trades == 0:
            return
        
        # Win rate
        metrics.win_rate = metrics.winning_trades / metrics.total_trades
        
        # Profit factor
        if metrics.gross_loss > 0:
            metrics.profit_factor = metrics.gross_profit / metrics.gross_loss
        
        # Médias
        if metrics.winning_trades > 0:
            metrics.avg_win = metrics.gross_profit / metrics.winning_trades
        if metrics.losing_trades > 0:
            metrics.avg_loss = metrics.gross_loss / metrics.losing_trades
        
        # Duração média
        if metrics.trade_history:
            durations = [t.duration_seconds / 60 for t in metrics.trade_history]
            metrics.avg_trade_duration_minutes = np.mean(durations)
            
            winning_durations = [t.duration_seconds / 60 for t in metrics.trade_history if t.is_profitable]
            if winning_durations:
                metrics.avg_winning_duration_minutes = np.mean(winning_durations)
            
            losing_durations = [t.duration_seconds / 60 for t in metrics.trade_history if not t.is_profitable]
            if losing_durations:
                metrics.avg_losing_duration_minutes = np.mean(losing_durations)
        
        # Sharpe ratio (simplificado)
        if metrics.trade_history:
            returns = [t.pnl_pct for t in metrics.trade_history]
            if len(returns) > 1:
                mean_return = np.mean(returns)
                std_return = np.std(returns)
                if std_return > 0:
                    metrics.sharpe_ratio = mean_return / std_return
        
        # Largest win/loss
        if metrics.trade_history:
            pnls = [t.pnl for t in metrics.trade_history]
            metrics.largest_win = max(pnls) if pnls else 0
            metrics.largest_loss = min(pnls) if pnls else 0
    
    def _calculate_returns_correlation(self, sim: PerformanceMetrics, live: PerformanceMetrics) -> float:
        """Calcula correlação entre retornos."""
        if not sim.trade_history or not live.trade_history:
            return 0.0
        
        sim_returns = [t.pnl_pct for t in sim.trade_history]
        live_returns = [t.pnl_pct for t in live.trade_history]
        
        # Alinhar tamanhos
        min_len = min(len(sim_returns), len(live_returns))
        if min_len < 2:
            return 0.0
        
        sim_returns = sim_returns[:min_len]
        live_returns = live_returns[:min_len]
        
        return np.corrcoef(sim_returns, live_returns)[0, 1] if min_len > 1 else 0.0
    
    def _calculate_equity_correlation(self, sim: PerformanceMetrics, live: PerformanceMetrics) -> float:
        """Calcula correlação entre curvas de equity."""
        if not sim.equity_curve or not live.equity_curve:
            return 0.0
        
        min_len = min(len(sim.equity_curve), len(live.equity_curve))
        if min_len < 2:
            return 0.0
        
        sim_equity = sim.equity_curve[:min_len]
        live_equity = live.equity_curve[:min_len]
        
        return np.corrcoef(sim_equity, live_equity)[0, 1]
    
    def _calculate_similarity_score(self, sim: PerformanceMetrics, live: PerformanceMetrics) -> float:
        """Calcula score de similaridade geral (0-1)."""
        scores = []
        
        # PnL similarity (normalizado)
        if live.total_pnl != 0:
            pnl_similarity = 1 - abs(sim.total_pnl - live.total_pnl) / abs(live.total_pnl)
            scores.append(max(0, min(1, pnl_similarity)))
        
        # Win rate similarity
        win_rate_similarity = 1 - abs(sim.win_rate - live.win_rate)
        scores.append(max(0, min(1, win_rate_similarity)))
        
        # Sharpe similarity
        if live.sharpe_ratio != 0:
            sharpe_similarity = 1 - abs(sim.sharpe_ratio - live.sharpe_ratio) / abs(live.sharpe_ratio)
            scores.append(max(0, min(1, sharpe_similarity)))
        
        # Drawdown similarity
        if live.max_drawdown != 0:
            dd_similarity = 1 - abs(sim.max_drawdown - live.max_drawdown) / live.max_drawdown
            scores.append(max(0, min(1, dd_similarity)))
        
        return np.mean(scores) if scores else 0.0
