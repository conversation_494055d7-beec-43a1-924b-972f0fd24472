import numpy as np
from qualia.memory.event_bus import SimpleEventBus
from qualia.analysis.ga_threshold_evolver import GAThresholdEvolver
from qualia.events import CrossModalCoherenceEvent


def test_ga_converges_small():
    bus = SimpleEventBus()
    ga = GAThresholdEvolver(bus, population_size=2, offspring_size=2, window_events=50)
    # feed synthetic coherence values
    for c in np.linspace(0, 1, 100):
        bus.publish(
            "nexus.cross_modal_coherence", CrossModalCoherenceEvent(coherence=float(c))
        )
    # best published via override should exist
    # nothing to assert strictly; just ensure no crash
