"""Stress test simplificado para o DynamicRiskController.

Esta versão reproduz o MonteCarloStressTester definido em
``tests/market/test_dynamic_risk_regime_validation.py`` porém
utiliza apenas dependências leves para facilitar a execução
fora do ambiente de testes completo.
"""

from __future__ import annotations

import json
from typing import Any, Dict, List
import numpy as np
from pathlib import Path
import sys

# Garantir que o pacote src esteja no caminho
ROOT = Path(__file__).resolve().parents[1]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

# Importações limitadas para evitar dependências pesadas
try:
    from qualia.market.dynamic_risk_controller import DynamicRiskController
except Exception as exc:  # pragma: no cover - fallback
    raise SystemExit(
        "DynamicRiskController não pôde ser importado. Verifique as dependências:"
        f" {exc}"
    )


class MonteCarloStressTester:
    """Execução simplificada de testes de stress via <PERSON> Carlo."""

    def __init__(self, controller: DynamicRiskController) -> None:
        self.controller = controller

    def run_stress_tests(
        self, n_simulations: int = 50, base_data_length: int = 50
    ) -> Dict[str, Any]:
        results: Dict[str, List[Dict[str, Any]]] = {
            "flash_crash": [],
            "gap": [],
            "volatility_spike": [],
            "dead_market": [],
        }
        for sim in range(n_simulations):
            results["flash_crash"].append(
                self._test_flash_crash_resilience(base_data_length, sim)
            )
            results["gap"].append(self._test_gap_resilience(base_data_length, sim))
            results["volatility_spike"].append(
                self._test_volatility_spike(base_data_length, sim)
            )
            results["dead_market"].append(self._test_dead_market(base_data_length, sim))

        aggregate = self._calculate_stats(results)
        return {"tests": results, "aggregate": aggregate}

    def _test_flash_crash_resilience(self, length: int, seed: int) -> Dict[str, Any]:
        rng = np.random.default_rng(seed)
        base_price = 100.0
        returns = rng.normal(0, 0.015, length - 1)
        crash = length // 2
        returns[crash] = -0.15
        returns[crash + 1] = 0.08
        prices = [base_price]
        for r in returns:
            prices.append(prices[-1] * (1 + r))
        data = np.array(prices[1:])
        market_data = {"high": data, "low": data, "close": data}
        result = self.controller.calibrate_risk_levels(
            symbol=f"FLASH_{seed}", market_data=market_data, current_price=data[-1]
        )
        return {
            "success": True,
            "atr": result.atr_value,
            "regime": result.market_regime,
            "stop_distance": result.stop_loss_distance,
        }

    def _test_gap_resilience(self, length: int, seed: int) -> Dict[str, Any]:
        rng = np.random.default_rng(seed + 1000)
        base_price = 100.0
        returns = rng.normal(0, 0.01, length - 1)
        gap_point = length // 3
        gap_size = rng.choice([-0.08, 0.08])
        returns[gap_point] = gap_size
        prices = [base_price]
        for r in returns:
            prices.append(prices[-1] * (1 + r))
        data = np.array(prices[1:])
        market_data = {"high": data, "low": data, "close": data}
        result = self.controller.calibrate_risk_levels(
            symbol=f"GAP_{seed}", market_data=market_data, current_price=data[-1]
        )
        return {
            "success": True,
            "gap_size": abs(gap_size),
            "atr": result.atr_value,
            "regime": result.market_regime,
        }

    def _test_volatility_spike(self, length: int, seed: int) -> Dict[str, Any]:
        rng = np.random.default_rng(seed + 2000)
        base_price = 100.0
        low_vol = rng.normal(0, 0.003, length // 2)
        high_vol = rng.normal(0, 0.05, length - length // 2)
        returns = np.concatenate([low_vol, high_vol])
        prices = [base_price]
        for r in returns:
            prices.append(prices[-1] * (1 + r))
        data = np.array(prices[1:])
        market_data = {"high": data, "low": data, "close": data}
        result = self.controller.calibrate_risk_levels(
            symbol=f"SPIKE_{seed}", market_data=market_data, current_price=data[-1]
        )
        return {
            "success": True,
            "atr": result.atr_value,
            "regime": result.market_regime,
        }

    def _test_dead_market(self, length: int, seed: int) -> Dict[str, Any]:
        rng = np.random.default_rng(seed + 3000)
        base_price = 100.0
        tiny_returns = rng.normal(0, 0.0001, length - 1)
        prices = [base_price]
        for r in tiny_returns:
            prices.append(prices[-1] * (1 + r))
        data = np.array(prices[1:])
        market_data = {"high": data, "low": data, "close": data}
        result = self.controller.calibrate_risk_levels(
            symbol=f"DEAD_{seed}", market_data=market_data, current_price=data[-1]
        )
        return {
            "success": True,
            "atr": result.atr_value,
            "regime": result.market_regime,
        }

    def _calculate_stats(
        self, results: Dict[str, List[Dict[str, Any]]]
    ) -> Dict[str, Any]:
        stats = {}
        for key, vals in results.items():
            successes = [1 for v in vals if v.get("success")] or [0]
            stats[key] = {
                "success_rate": sum(successes) / len(successes),
                "total_tests": len(vals),
            }
        overall = [stats[k]["success_rate"] for k in stats]
        stats["overall_mean_success"] = float(np.mean(overall)) if overall else 0.0
        return stats


if __name__ == "__main__":  # pragma: no cover - execução manual
    controller = DynamicRiskController(risk_profile="balanced")
    tester = MonteCarloStressTester(controller)
    results = tester.run_stress_tests(n_simulations=10, base_data_length=50)
    with open("docs/data/stress_test_results.json", "w") as f:
        json.dump(results, f, indent=2)
    print(json.dumps(results["aggregate"], indent=2))
