import time
from datetime import datetime

def test_insufficient_period_validation():
    """Testa a validação de período insuficiente para dados"""
    
    # Timestamp problemático do log (8 minutos atrás)
    problematic_timestamp = 1753403940001
    current_time_ms = int(time.time() * 1000)
    
    print(f"Timestamp problemático: {problematic_timestamp}")
    print(f"Timestamp atual: {current_time_ms}")
    
    # Converter para datetime para visualização
    problematic_dt = datetime.fromtimestamp(problematic_timestamp / 1000)
    current_dt = datetime.fromtimestamp(current_time_ms / 1000)
    
    print(f"Data problemática: {problematic_dt}")
    print(f"Data atual: {current_dt}")
    
    # Simular parâmetros do problema
    limit = 30  # 30 candles solicitadas
    timeframe_ms = 60 * 1000  # 1 minuto em ms
    
    # Calcular período necessário vs disponível
    min_period_ms = limit * timeframe_ms  # 30 min necessários
    time_since_request = current_time_ms - problematic_timestamp
    
    minutes_available = time_since_request / (60 * 1000)
    minutes_needed = min_period_ms / (60 * 1000)
    
    print(f"\nAnálise do período:")
    print(f"Tempo disponível desde o timestamp: {minutes_available:.1f} minutos")
    print(f"Tempo necessário para {limit} candles: {minutes_needed:.1f} minutos")
    
    # Testar a lógica de validação
    is_insufficient = time_since_request < min_period_ms
    
    print(f"\nValidação:")
    print(f"Período insuficiente? {is_insufficient}")
    
    if is_insufficient:
        print("✅ CORREÇÃO APLICADA: Timestamp seria resetado para None")
        print(f"Motivo: Apenas {minutes_available:.1f} min disponíveis, mas precisamos de {minutes_needed:.1f} min")
    else:
        print("❌ CORREÇÃO NÃO APLICADA: Período considerado suficiente")
    
    return is_insufficient

def test_edge_cases():
    """Testa casos extremos da validação de período"""
    current_time_ms = int(time.time() * 1000)
    limit = 30
    timeframe_ms = 60 * 1000  # 1 minuto
    min_period_ms = limit * timeframe_ms  # 30 minutos
    
    test_cases = [
        ("5 min atrás", current_time_ms - 5 * 60 * 1000),
        ("15 min atrás", current_time_ms - 15 * 60 * 1000),
        ("30 min atrás", current_time_ms - 30 * 60 * 1000),
        ("45 min atrás", current_time_ms - 45 * 60 * 1000),
        ("1 hora atrás", current_time_ms - 60 * 60 * 1000),
    ]
    
    print("\n=== Testando casos extremos ===")
    
    for description, timestamp in test_cases:
        time_since = current_time_ms - timestamp
        is_insufficient = time_since < min_period_ms
        minutes_available = time_since / (60 * 1000)
        status = "INSUFICIENTE" if is_insufficient else "SUFICIENTE"
        print(f"{description} ({minutes_available:.1f} min): {status}")

def test_correction_logic():
    """Testa a lógica completa de correção"""
    print("\n=== Testando lógica de correção ===")
    
    # Simular a lógica do base_integration.py
    problematic_timestamp = 1753403940001
    current_time_ms = int(time.time() * 1000)
    limit = 30
    timeframe_ms = 60 * 1000
    
    # Simular request_since
    request_since = problematic_timestamp
    
    if request_since is not None:
        min_period_ms = limit * timeframe_ms
        time_since_request = current_time_ms - request_since
        
        if time_since_request < min_period_ms:
            minutes_available = time_since_request / (60 * 1000)
            minutes_needed = min_period_ms / (60 * 1000)
            
            print(f"⚠️ PERÍODO INSUFICIENTE: since={request_since}")
            print(f"Disponível: {minutes_available:.1f} min, Necessário: {minutes_needed:.1f} min")
            request_since = None
            print("✅ Timestamp resetado para None")
        else:
            minutes_available = time_since_request / (60 * 1000)
            print(f"ℹ️ Período suficiente: {minutes_available:.1f} min disponíveis")
    
    return request_since

if __name__ == "__main__":
    print("=== Teste de Validação de Período Insuficiente ===")
    
    # Teste principal
    is_detected = test_insufficient_period_validation()
    
    # Testes de casos extremos
    test_edge_cases()
    
    # Teste da lógica de correção
    final_since = test_correction_logic()
    
    print(f"\n=== Resultado Final ===")
    print(f"Período insuficiente detectado: {is_detected}")
    print(f"Valor final de since: {final_since}")
    
    if is_detected and final_since is None:
        print("✅ TESTE PASSOU: Correção funcionando corretamente")
    else:
        print("❌ TESTE FALHOU: Correção não está funcionando")
    
    print("\n=== Explicação do Problema ===")
    print("O timestamp 1753403940001 está ~8 minutos no passado.")
    print("Para timeframe 1m e limit=30, precisamos de 30 minutos de dados.")
    print("Com apenas 8 minutos disponíveis, a API retorna só ~8 candles (não 30).")
    print("Isso explica o '1/2 candles (50.0%)' no log original.")
    print("A correção detecta e reseta o timestamp para buscar dados completos.")