complexity_threshold_calm: 0.3
complexity_threshold_volatile: 0.7
qubits_calm: 4
qubits_normal: 8
qubits_volatile: 12
entanglement_calm: linear
entanglement_normal: linear
entanglement_volatile: full
scr_depth_calm: 5
scr_depth_normal: 10
scr_depth_volatile: 15
measure_frequency_calm: 2
measure_frequency_normal: 1
measure_frequency_volatile: 1
adaptation_aggressiveness: 0.8
min_qubits: 2
max_qubits: 12
delta_threshold_non_linearity_factor: 0.03
complexity_threshold_volatility_factor: 0.5
complexity_min_threshold_calm: 0.1
complexity_max_threshold_volatile: 0.9
qubit_increase_threshold_pct: 0.05

# QUALIA - Configuração de Evolução Adaptativa e Consciência
# Configurações para sistemas quânticos evolutivos

quantum_consciousness:
  coherence_threshold: 0.6
  decoherence_protection: true
  eigenstate_stability: 0.85
  
adaptive_evolution:
  population_size: 120
  mutation_rate: 0.15
  crossover_rate: 0.8
  selection_pressure: 1.2
  elitism_rate: 0.1
  
# YAA: Configurações para Multi-Exchange Dimensional Trading
multi_exchange:
  enabled: true  # 🔥 HABILITADO - Diversificação dimensional ativa
  
  # Configurações dimensionais otimizadas
  dimensional:
    min_spread_threshold: 0.0008       # 0.08% spread mínimo (reduzido para mais oportunidades)
    max_latency_ms: 800                # Latência máxima otimizada
    quantum_coherence_threshold: 0.50  # Threshold de coerência quântica reduzido
    position_size_pct: 0.003           # 0.3% do capital por arbitragem
    max_daily_arbitrages: 30           # Máximo de arbitragens por dia aumentado
    
    # Símbolos para análise cross-exchange
    cross_exchange_symbols:
      - "BTC/USDT"
      - "ETH/USDT"
      - "BTC/USD"
      - "ETH/USD"
    
    # Rate limiting otimizado por exchange
    rate_limits:
      kraken_requests_per_second: 1
      kucoin_requests_per_second: 10   # Aumentado para KuCoin
      circuit_breaker_threshold: 3     # Reduzido para maior sensibilidade
      circuit_breaker_timeout: 180     # 3 minutos de timeout
    
    # Configurações de monitoramento otimizadas
    monitoring:
      opportunity_scan_interval: 2.0    # Scan mais frequente
      max_opportunities_cache: 75       # Cache aumentado
      cleanup_old_opportunities: 180    # Limpeza mais frequente
      
    # Configurações de resiliência dimensional
    resilience:
      auto_failover: true               # Failover automático entre exchanges
      health_check_interval: 30.0      # Verificação de saúde a cada 30s
      connection_retry_attempts: 5     # Tentativas de reconexão
      backoff_multiplier: 1.5          # Multiplicador de backoff exponencial
  
  # Configurações de exchanges otimizadas
  exchanges:
    kraken:
      enabled: true                     # 🔥 ATIVO
      role: "primary"                   # Exchange principal
      timeout: 25.0                     # Timeout otimizado
      priority: 1                       # Prioridade alta
      api_key: ""                       # Vazio para paper trading
      api_secret: ""                    # Vazio para paper trading
      
    kucoin:
      enabled: true                     # 🔥 ATIVO  
      role: "secondary"                 # Exchange secundária para arbitragem
      timeout: 20.0                     # Timeout menor para KuCoin
      priority: 2                       # Prioridade secundária
      api_key: ""                       # Vazio para paper trading
      api_secret: ""                    # Vazio para paper trading
      password: ""                      # Vazio para paper trading

# Configurações de arbitragem dimensional no trader
arbitrage:
  min_spread_threshold: 0.0015          # 0.15% spread mínimo para execução
  min_quantum_confidence: 0.55          # Confiança quântica mínima
  max_position_risk_pct: 0.4            # Máximo 0.4% do capital por oportunidade
  execution_timeout: 20.0               # Timeout para execução de arbitragem
  slippage_tolerance: 0.0005            # Tolerância ao slippage (0.05%)
  
  # Estratégias de execução dimensional
  execution_strategies:
    parallel_execution: true            # Execução paralela de ordens
    smart_routing: true                 # Roteamento inteligente
    latency_optimization: true          # Otimização de latência
  
trading:
  base_currency: "USD"
  quote_currencies: ["BTC", "ETH"]
  
risk_management:
  max_portfolio_risk: 0.015             # 1.5% do capital total
  position_sizing_method: "adaptive"    # Sizing adaptativo
  
logging:
  level: "INFO"
  structured: true
  enable_performance_metrics: true
