#!/usr/bin/env python3
"""
Aplicar configurações otimizadas da estratégia FWH baseadas na análise real.
"""

import sys
import os
import yaml
from pathlib import Path

# Configurações otimizadas baseadas na análise dos resultados reais
OPTIMIZED_FWH_CONFIG = {
    'fibonacci_wave_hype_config': {
        'name': 'FibonacciWaveHypeStrategy',
        'enabled': True,
        'params': {
            'fib_lookback': 20,  # Mais rápido para capturar movimentos
            'sentiment_cache_ttl': 300,
            
            # Configurações específicas por timeframe (OTIMIZADAS)
            'timeframe_specific': {
                '1m': {
                    'hype_threshold': 0.03,      # REDUZIDO de 0.05 para 0.03
                    'wave_min_strength': 0.005,  # REDUZIDO de 0.01 para 0.005
                    'quantum_boost_factor': 1.08, # MANTIDO (valor otimizado)
                    'holographic_weight': 0.4,
                    'tsvf_validation_threshold': 0.3  # REDUZIDO de 0.5 para 0.3
                },
                '5m': {
                    'hype_threshold': 0.05,      # REDUZIDO de 0.10 para 0.05
                    'wave_min_strength': 0.01,   # REDUZIDO de 0.05 para 0.01
                    'quantum_boost_factor': 1.08, # AUMENTADO de 1.05 para 1.08
                    'holographic_weight': 0.5,
                    'tsvf_validation_threshold': 0.3  # REDUZIDO de 0.5 para 0.3
                },
                '15m': {
                    'hype_threshold': 0.08,      # REDUZIDO de 0.15 para 0.08
                    'wave_min_strength': 0.02,   # REDUZIDO de 0.10 para 0.02
                    'quantum_boost_factor': 1.05, # AUMENTADO de 1.03 para 1.05
                    'holographic_weight': 0.6,
                    'tsvf_validation_threshold': 0.35 # REDUZIDO de 0.5 para 0.35
                },
                '1h': {
                    'hype_threshold': 0.10,      # REDUZIDO de 0.20 para 0.10
                    'wave_min_strength': 0.03,   # REDUZIDO de 0.15 para 0.03
                    'quantum_boost_factor': 1.03, # AUMENTADO de 1.01 para 1.03
                    'holographic_weight': 0.7,
                    'tsvf_validation_threshold': 0.35 # REDUZIDO de 0.5 para 0.35
                }
            },
            
            # Configuração multi-timeframe (OTIMIZADA)
            'multi_timeframe_config': {
                'otoc_config': {
                    'enabled': True,
                    'max_threshold': 0.12,  # REDUZIDO de 0.15 para 0.12
                    'window': 30,           # REDUZIDO de 50 para 30
                    'method': 'correlation',
                    'adaptive_threshold': {
                        'enabled': True,
                        'beta': 1.2,        # AUMENTADO de 1.0 para 1.2
                        'vol_window': 15    # REDUZIDO de 20 para 15
                    }
                },
                'timeframe_weights': {
                    '1m': 0.30,   # AUMENTADO de 0.25 para 0.30
                    '5m': 0.40,   # AUMENTADO de 0.35 para 0.40
                    '15m': 0.70,  # AUMENTADO de 0.65 para 0.70
                    '1h': 0.90    # AUMENTADO de 0.85 para 0.90
                },
                'consolidation_method': 'weighted_average',
                'min_timeframes_required': 1,  # REDUZIDO de 2 para 1
                'confidence_threshold': 0.10,  # NOVO: threshold mais baixo
                'convergence_threshold': 0.6   # REDUZIDO de 0.7 para 0.6
            },
            
            # Níveis Fibonacci (OTIMIZADOS)
            'fibonacci_levels': {
                'primary': [0.236, 0.382, 0.618],
                'secondary': [0.146, 0.5, 0.786],
                'extensions': [1.272, 1.618, 2.618],
                'sensitivity': 1.2  # NOVO: maior sensibilidade
            },
            
            # Detecção de ondas (OTIMIZADA)
            'wave_detection': {
                'min_wave_bars': 3,      # REDUZIDO de 5 para 3
                'max_wave_bars': 15,     # REDUZIDO de 20 para 15
                'volume_weight': 0.7,    # AUMENTADO de 0.6 para 0.7
                'price_weight': 0.3,     # REDUZIDO de 0.4 para 0.3
                'momentum_threshold': 0.015  # REDUZIDO de 0.02 para 0.015
            },
            
            # Gestão de risco (OTIMIZADA)
            'risk_management': {
                'max_position_size': 0.15,     # AUMENTADO de 0.1 para 0.15
                'stop_loss_fib_level': 0.618,  # REDUZIDO de 0.786 para 0.618
                'take_profit_fib_level': 1.272, # REDUZIDO de 1.618 para 1.272
                'dynamic_sizing': True,
                'confidence_scaling': True     # NOVO: escalar posição por confidence
            }
        },
        
        # Integração com componentes (OTIMIZADA)
        'integration': {
            'holographic_engine': True,
            'tsvf_calculator': True,
            'quantum_metrics': True,
            'sentiment_analysis': True,
            'adaptive_thresholds': True  # NOVO: thresholds adaptativos
        },
        
        # Configuração de backtesting (OTIMIZADA)
        'backtesting': {
            'lookback_days': 7,  # REDUZIDO de 30 para 7
            'benchmark_strategies': ['QualiaTSVFStrategy'],
            'performance_metrics': ['sharpe_ratio', 'max_drawdown', 'win_rate', 'profit_factor'],
            'min_confidence_for_trade': 0.15  # NOVO: confidence mínima para trade
        }
    },
    
    # Configuração do sistema de trading (OTIMIZADA)
    'trading_system': {
        'limits': {
            'max_position_size_usd': 1500.0,  # AUMENTADO de 1000 para 1500
            'max_daily_trades': 10,           # NOVO: limite diário
            'min_trade_interval_minutes': 15  # NOVO: intervalo mínimo entre trades
        },
        'execution': {
            'slippage_tolerance': 0.001,      # NOVO: tolerância ao slippage
            'partial_fill_enabled': True     # NOVO: permitir preenchimento parcial
        }
    },
    
    # Configuração de backtesting (OTIMIZADA)
    'backtesting': {
        'initial_capital': 10000.0,
        'commission': 0.001,
        'slippage': 0.0005,
        'realistic_execution': True  # NOVO: execução mais realista
    }
}

def apply_optimized_config():
    """Aplica configurações otimizadas ao arquivo YAML."""
    print("🎯 APLICANDO CONFIGURAÇÕES OTIMIZADAS FWH")
    print("=" * 60)
    
    # Caminho para o arquivo de configuração
    config_path = Path(__file__).parent.parent / 'config' / 'strategies.yaml'
    
    try:
        # Ler configuração atual
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                current_config = yaml.safe_load(f) or {}
            print(f"✅ Configuração atual carregada de: {config_path}")
        else:
            current_config = {}
            print(f"📝 Criando nova configuração em: {config_path}")
        
        # Aplicar configurações otimizadas
        current_config.update(OPTIMIZED_FWH_CONFIG)
        
        # Criar diretório se não existir
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Salvar configuração otimizada
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(current_config, f, default_flow_style=False, indent=2, allow_unicode=True)
        
        print(f"✅ Configurações otimizadas aplicadas com sucesso!")
        print(f"📁 Arquivo salvo em: {config_path}")
        
        # Mostrar principais otimizações
        print(f"\n🚀 PRINCIPAIS OTIMIZAÇÕES APLICADAS:")
        print(f"   📉 Thresholds REDUZIDOS para mais sinais:")
        print(f"      • hype_threshold: 0.03-0.10 (era 0.05-0.20)")
        print(f"      • wave_min_strength: 0.005-0.03 (era 0.01-0.15)")
        print(f"      • confidence_threshold: 0.10 (era 0.12)")
        print(f"   📈 Boost factors AUMENTADOS:")
        print(f"      • quantum_boost_factor: 1.03-1.08 (era 1.01-1.05)")
        print(f"   ⚡ Responsividade MELHORADA:")
        print(f"      • fib_lookback: 20 (era 50)")
        print(f"      • min_timeframes_required: 1 (era 2)")
        print(f"      • wave detection mais sensível")
        print(f"   💰 Gestão de risco OTIMIZADA:")
        print(f"      • max_position_size: 15% (era 10%)")
        print(f"      • stop_loss mais próximo: 0.618 (era 0.786)")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao aplicar configurações: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_production_config():
    """Cria configuração específica para produção."""
    print(f"\n🏭 CRIANDO CONFIGURAÇÃO DE PRODUÇÃO")
    print("=" * 50)
    
    # Configuração de produção (mais conservadora)
    production_config = OPTIMIZED_FWH_CONFIG.copy()
    
    # Ajustes para produção
    production_config['fibonacci_wave_hype_config']['params']['risk_management'].update({
        'max_position_size': 0.10,  # Mais conservador em produção
        'stop_loss_fib_level': 0.786,  # Stop loss mais amplo
        'take_profit_fib_level': 1.618  # Take profit mais amplo
    })
    
    production_config['trading_system']['limits'].update({
        'max_position_size_usd': 1000.0,  # Limite menor em produção
        'max_daily_trades': 5  # Menos trades por dia
    })
    
    # Salvar configuração de produção
    prod_config_path = Path(__file__).parent.parent / 'config' / 'strategies_production.yaml'
    
    try:
        with open(prod_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(production_config, f, default_flow_style=False, indent=2, allow_unicode=True)
        
        print(f"✅ Configuração de produção criada: {prod_config_path}")
        print(f"🛡️ Configurações mais conservadoras para ambiente real")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar configuração de produção: {e}")
        return False

def main():
    """Executa aplicação das configurações."""
    print("🎯 APLICAÇÃO DE CONFIGURAÇÕES OTIMIZADAS FWH")
    print("=" * 70)
    print("📊 Baseado na análise de dados reais da API Binance")
    print("🔬 Otimizações para melhor execução de sinais")
    print()
    
    # Aplicar configurações otimizadas
    success1 = apply_optimized_config()
    
    # Criar configuração de produção
    success2 = create_production_config()
    
    if success1 and success2:
        print(f"\n🎉 CONFIGURAÇÕES APLICADAS COM SUCESSO!")
        print(f"✅ Estratégia FWH otimizada para dados reais")
        print(f"✅ Configuração de produção criada")
        print(f"🚀 Sistema pronto para trading com parâmetros otimizados")
        print(f"\n📋 PRÓXIMOS PASSOS:")
        print(f"   1. Testar configurações com backtest")
        print(f"   2. Validar em ambiente de simulação")
        print(f"   3. Implementar em produção com configuração conservadora")
        return True
    else:
        print(f"\n❌ FALHA NA APLICAÇÃO DAS CONFIGURAÇÕES")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ OTIMIZAÇÃO FWH CONCLUÍDA!")
    else:
        print(f"\n🔧 REVISAR CONFIGURAÇÕES")
