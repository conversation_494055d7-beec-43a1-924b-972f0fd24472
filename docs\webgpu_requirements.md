# Requisitos para Suporte a WebGPU

A implementação de WebGPU no QUALIA depende de suporte de hardware e de bibliotecas específicas. A tabela abaixo resume os requisitos mínimos sugeridos para um funcionamento estável.

## Hardware
- GPU compatível com Vulkan 1.3 ou DirectX 12 (NVIDIA série GTX 900+, AMD RX 400+, Apple Silicon, Intel de 11ª geração ou superior).
- 4 GB de memória dedicada para renderizações mais complexas.
- Drivers atualizados (Mesa 22+ no Linux, drivers oficiais no Windows/macOS).

## Sistemas Operacionais
- **Windows** 10 20H2 ou superior.
- **macOS** 13 (Ventura) ou superior.
- **Linux** com kernel 5.15+ e mesa 22+.

## Navegadores e Bibliotecas
- Chrome 113+, Edge 113+ ou Safari 17+ com WebGPU ativado.
- Para Node.js, recomenda-se o pacote `@webgpu/glslang` e `gpuweb` quando disponível.
- A biblioteca Three.js é usada como camada de abstração na HUD. Para WebGPU, é necessário utilizar a versão experimental de Three.js com suporte a WebGPU (`examples/jsm/renderers/webgpu/WebGPURenderer.js`).

Habilite a flag `--use_webgpu` ao iniciar o `QUALIARealTimeTrader` para ativar o caminho de renderização WebGPU quando essas condições forem atendidas.
