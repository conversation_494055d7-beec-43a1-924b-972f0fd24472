"""Visualização em tempo-real de mercado usando DynamicLogoEngine.

Mapeamentos:
• Brilho  → intensidade de micro-flutuações (Digital Rain Encoder simplificado)
• Matiz   → RSI normalizado (RSI Phase Encoder simplificado)

Dependências: yfinance, pandas, numpy, opencv-python (ou Pillow)
Instale com:
    pip install yfinance pandas numpy opencv-python

Execute:
    python scripts/live_market_dynamic_logo.py AAPL
Substitua "AAPL" pelo ticker desejado.
"""
from __future__ import annotations

import os
import sys
import time
from datetime import datetime, timezone
from typing import Optional

import numpy as np
import pandas as pd
import yfinance as yf

# torna src importável
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src')))
from qualia.visuals.dynamic_logo import DynamicLogoEngine

# ---------------------------- utilidades de mercado --------------------------- #

def compute_rsi(close: pd.Series, period: int = 14) -> pd.Series:
    """RSI rápido (talvez impreciso para tickers muito curtos)."""
    delta = close.diff()
    gain = (delta.clip(lower=0)).ewm(alpha=1 / period, adjust=False).mean()
    loss = (-delta.clip(upper=0)).ewm(alpha=1 / period, adjust=False).mean()
    rs = gain / (loss.replace(0, np.nan))
    rsi = 100 - (100 / (1 + rs))
    return rsi


def normalize(series: pd.Series) -> pd.Series:
    return (series - series.min()) / (series.max() - series.min() + 1e-9)

# ---------------------------------------------------------------------------- #

def main(ticker: str, poll_interval: int = 60) -> None:
    engine = DynamicLogoEngine(fusion_mode="audio→color")

    # janela de histórico
    hist_minutes = 120  # 2h
    data: Optional[pd.DataFrame] = None

    print(f"[ {datetime.now().isoformat()} ] Iniciando visualização para {ticker} …")
    while True:
        try:
            new_data = yf.download(tickers=ticker, period="1d", interval="1m", progress=False)
            if new_data.empty:
                print("Sem dados – aguardando…")
                time.sleep(poll_interval)
                continue

            if data is None:
                data = new_data.copy()
            else:
                # concat e remove duplicatas
                data = pd.concat([data, new_data]).drop_duplicates(keep="last")

            # mantém última janela
            data = data.tail(hist_minutes)

            # Digital Rain Encoder – brilho: magnitude de micro-flutuação
            price_diff = data["Close"].diff().fillna(0)
            volume = data["Volume"].fillna(0)
            micro_fluct = (price_diff.abs() * volume).rolling(5).mean()
            brightness_series = normalize(micro_fluct)
            brightness = float(brightness_series.iloc[-1])

            # RSI Phase Encoder – hue: RSI normalizado [0,1] * 360
            rsi_series = compute_rsi(data["Close"])
            rsi_val = float(rsi_series.iloc[-1]) if not np.isnan(rsi_series.iloc[-1]) else 50.0
            hue = rsi_val / 100.0 * 360.0

            # atualiza engine
            engine.update_audio_color(hue=hue, brightness=brightness)
            frame = engine.render()
            engine.show()

            ts = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
            print(f"[{ts}] hue={hue:.1f}  brightness={brightness:.3f}")

            time.sleep(poll_interval)
        except KeyboardInterrupt:
            print("Encerrado pelo usuário.")
            break
        except Exception as e:
            print("Erro:", e)
            time.sleep(poll_interval)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        ticker_symbol = sys.argv[1]
    else:
        ticker_symbol = "AAPL"
    main(ticker_symbol)
