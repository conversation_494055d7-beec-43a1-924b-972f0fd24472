// Mini experimento de InstancedMesh para QUALIA
// Gera algumas instâncias e calcula FPS médio

document.addEventListener('DOMContentLoaded', () => {
  const container = document.getElementById('instancing-view');
  if (!container) return;

  const width = container.clientWidth;
  const height = container.clientHeight;

  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(70, width / height, 0.1, 100);
  camera.position.z = 5;

  const renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(width, height);
  container.appendChild(renderer.domElement);

  const geometry = new THREE.BoxGeometry(0.2, 0.2, 0.2);
  const material = new THREE.MeshNormalMaterial();
  const count = 50;
  const mesh = new THREE.InstancedMesh(geometry, material, count);
  const matrix = new THREE.Matrix4();
  for (let i = 0; i < count; i++) {
    matrix.setPosition(
      (Math.random() - 0.5) * 4,
      (Math.random() - 0.5) * 4,
      (Math.random() - 0.5) * 4
    );
    mesh.setMatrixAt(i, matrix);
  }
  scene.add(mesh);

  let frames = 0;
  const start = performance.now();
  function animate() {
    frames += 1;
    mesh.rotation.x += 0.01;
    mesh.rotation.y += 0.01;
    renderer.render(scene, camera);
    if (performance.now() - start < 2000) {
      requestAnimationFrame(animate);
    } else {
      const elapsed = (performance.now() - start) / 1000;
      const fps = frames / elapsed;
      window.performanceData = { fps };
    }
  }
  animate();
});
