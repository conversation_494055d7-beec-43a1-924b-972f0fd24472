{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Exemplo básico de RetroSelector\n", "Este notebook demonstra como utilizar `IntentionEnvelope` e `RetroSelector` para filtrar trajetórias de PnL."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from src.qualia.intentions import IntentionEnvelope\n", "from src.qualia.retro import RetroSelector\n", "\n", "# Gera 5 trajetórias simuladas de PnL com 10 etapas cada\n", "rng = np.random.default_rng(seed=42)\n", "paths = rng.normal(loc=0.1, scale=0.2, size=(5, 10))\n", "\n", "envelope = IntentionEnvelope(profit_target=0.5, max_drawdown=0.3, horizon_hours=4)\n", "selector = RetroSelector()\n", "filtered = selector.select(paths, envelope)\n", "filtered"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12"}}, "nbformat": 4, "nbformat_minor": 5}