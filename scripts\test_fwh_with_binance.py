#!/usr/bin/env python3
"""
Script de teste da estratégia FWH com infraestrutura Binance real.

Utiliza a infraestrutura existente do QUALIA para conectar com a Binance
e testar a estratégia FWH com dados reais e corroboráveis.
"""

import sys
import os
import asyncio
import pandas as pd
from datetime import datetime, timedelta, timezone
from pathlib import Path
from dotenv import load_dotenv

# Carrega variáveis de ambiente do arquivo .env
load_dotenv()

# Adiciona o diretório src ao path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy
from qualia.strategies.fibonacci_wave_hype.indicators import (
    calculate_fibonacci_levels,
    detect_wave_patterns
)
from qualia.market.binance_integration import BinanceIntegration
from qualia.market.market_data_client import MarketDataClient
from qualia.strategies.strategy_interface import TradingContext
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class FWHBinanceTest:
    """Teste da estratégia FWH com dados reais da Binance."""
    
    def __init__(self):
        self.binance_integration = None
        self.market_data_client = None
        self.fwh_strategy = None
        
    async def initialize(self):
        """Inicializa conexões com Binance."""
        print("🔧 Inicializando conexão com Binance...")
        
        try:
            # Inicializa integração Binance (sem credenciais para dados públicos)
            self.binance_integration = BinanceIntegration()
            await self.binance_integration.initialize_connection()
            
            # Cria cliente de dados de mercado
            self.market_data_client = MarketDataClient(self.binance_integration)
            
            # Inicializa estratégia FWH
            self.fwh_strategy = FibonacciWaveHypeStrategy(
                symbol="BTC/USDT",
                timeframe="1h",
                parameters={
                    "fib_lookback": 50,
                    "hype_threshold": 0.618,
                    "wave_min_strength": 0.3,
                }
            )
            
            # Inicializa estratégia com contexto Binance
            context = {
                "binance_integration": self.binance_integration,
                "exchange_client": self.binance_integration
            }
            self.fwh_strategy.initialize(context)
            
            print("✅ Conexão com Binance estabelecida")
            return True
            
        except Exception as e:
            print(f"❌ Erro ao conectar com Binance: {e}")
            logger.error(f"Erro na inicialização: {e}")
            return False
    
    async def test_real_data_collection(self):
        """Testa coleta de dados reais."""
        print("\n📊 Testando coleta de dados reais...")
        
        try:
            # Testa ticker atual
            ticker = await self.market_data_client.fetch_ticker("BTC/USDT")
            if ticker:
                print(f"📈 Ticker BTC/USDT:")
                print(f"   Preço atual: ${ticker.get('last', 'N/A'):,}")
                print(f"   Volume 24h: {ticker.get('baseVolume', 'N/A'):,}")
                print(f"   Variação 24h: {ticker.get('percentage', 'N/A')}%")
            
            # Testa dados OHLCV
            from qualia.common.specs import MarketSpec
            spec = MarketSpec(symbol="BTC/USDT", timeframe="1h")
            ohlcv_data = await self.market_data_client.fetch_ohlcv(
                spec=spec,
                limit=50
            )
            
            if ohlcv_data is not None and not ohlcv_data.empty:
                print(f"📊 Dados OHLCV coletados:")
                print(f"   Períodos: {len(ohlcv_data)}")
                print(f"   Período: {ohlcv_data.index[0]} a {ohlcv_data.index[-1]}")
                print(f"   Preço atual: ${ohlcv_data['close'].iloc[-1]:,.2f}")
                print(f"   Máxima 50h: ${ohlcv_data['high'].max():,.2f}")
                print(f"   Mínima 50h: ${ohlcv_data['low'].min():,.2f}")
                
                return ohlcv_data
            else:
                print("❌ Nenhum dado OHLCV obtido")
                return None
                
        except Exception as e:
            print(f"❌ Erro na coleta de dados: {e}")
            logger.error(f"Erro na coleta: {e}")
            return None
    
    async def test_fibonacci_analysis(self, market_data):
        """Testa análise de Fibonacci com dados reais."""
        print("\n🔢 Testando análise de Fibonacci...")
        
        try:
            # Calcula níveis de Fibonacci
            fib_levels = calculate_fibonacci_levels(
                market_data["high"].tail(50),
                market_data["low"].tail(50)
            )
            
            current_price = market_data["close"].iloc[-1]
            
            print(f"📊 Níveis de Fibonacci (preço atual: ${current_price:,.2f}):")
            for level, price in fib_levels.items():
                if level in ["0.0", "0.236", "0.382", "0.618", "1.0"]:
                    distance = ((current_price - price) / price) * 100
                    print(f"   {level:>5}: ${price:>10,.2f} ({distance:+6.2f}%)")
            
            # Detecta padrões de ondas
            wave_patterns = detect_wave_patterns(market_data, fib_levels)
            
            print(f"\n🌊 Padrões de onda detectados:")
            print(f"   Nível Fibonacci: {wave_patterns['fib_level']}")
            print(f"   Direção: {'📈 Alta' if wave_patterns['direction'] == 1 else '📉 Baixa' if wave_patterns['direction'] == -1 else '➡️ Lateral'}")
            print(f"   Força da tendência: {wave_patterns['trend_strength']:.4f}")
            
            return fib_levels, wave_patterns
            
        except Exception as e:
            print(f"❌ Erro na análise de Fibonacci: {e}")
            logger.error(f"Erro na análise: {e}")
            return None, None
    
    async def test_strategy_signal_generation(self, market_data):
        """Testa geração de sinais da estratégia."""
        print("\n🎯 Testando geração de sinais...")
        
        try:
            # Cria contexto de trading
            from datetime import datetime, timezone
            current_price = market_data['close'].iloc[-1]
            timestamp = pd.Timestamp.now(tz='UTC')
            
            trading_context = TradingContext(
                symbol="BTC/USDT",
                timeframe="1h",
                current_price=current_price,
                ohlcv=market_data,
                timestamp=timestamp,
                wallet_state={"BTC": 0.0, "USDT": 10000.0}  # Mock wallet state
            )
            
            # Gera sinal
            signals = self.fwh_strategy.generate_signal(trading_context)
            
            if not signals.empty:
                signal = signals.iloc[0]
                print(f"🚀 Sinal gerado:")
                print(f"   Tipo: {signal.get('signal', 'N/A').upper()}")
                print(f"   Confiança: {signal.get('confidence', 0):.3f}")
                print(f"   Nível Fibonacci: {signal.get('fibonacci_level', 'N/A')}")
                print(f"   Momentum de Hype: {signal.get('hype_momentum', 0):.3f}")
                print(f"   Boost Holográfico: {signal.get('holographic_boost', 1):.3f}")
                print(f"   Validação TSVF: {signal.get('tsvf_validation', 0):.3f}")
                
                if 'data_source' in signal:
                    print(f"   Fonte dos dados: {signal['data_source']}")
                
                return signals
            else:
                print("⏸️ Nenhum sinal gerado (HOLD)")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"❌ Erro na geração de sinais: {e}")
            logger.error(f"Erro na geração: {e}")
            return pd.DataFrame()
    
    async def test_data_quality_validation(self, market_data):
        """Testa validação de qualidade dos dados."""
        print("\n✅ Validando qualidade dos dados...")
        
        try:
            # Verifica completude
            completeness = (len(market_data) - market_data.isnull().sum().sum()) / (len(market_data) * len(market_data.columns))
            
            # Verifica consistência (OHLC)
            consistency_errors = 0
            for i, row in market_data.iterrows():
                if not (row['low'] <= row['open'] <= row['high'] and 
                       row['low'] <= row['close'] <= row['high']):
                    consistency_errors += 1
            
            consistency = 1 - (consistency_errors / len(market_data))
            
            # Verifica freshness (dados recentes)
            latest_timestamp = market_data.index[-1]
            if hasattr(latest_timestamp, 'to_pydatetime'):
                latest_dt = latest_timestamp.to_pydatetime()
            else:
                # Se for timestamp numérico, converter para datetime
                latest_dt = datetime.fromtimestamp(latest_timestamp / 1000, tz=timezone.utc)
            
            time_diff = datetime.now(timezone.utc) - latest_dt
            freshness = max(0, 1 - (time_diff.total_seconds() / 3600))  # Penaliza dados > 1h
            
            print(f"📊 Métricas de qualidade:")
            print(f"   Completude: {completeness:.2%}")
            print(f"   Consistência: {consistency:.2%}")
            print(f"   Atualização: {freshness:.2%}")
            print(f"   Último dado: {latest_timestamp}")
            
            # Score geral
            quality_score = (completeness * 0.4 + consistency * 0.4 + freshness * 0.2)
            print(f"   Score geral: {quality_score:.2%}")
            
            if quality_score > 0.8:
                print("✅ Qualidade dos dados: EXCELENTE")
            elif quality_score > 0.6:
                print("⚠️ Qualidade dos dados: BOA")
            else:
                print("❌ Qualidade dos dados: BAIXA")
            
            return quality_score
            
        except Exception as e:
            print(f"❌ Erro na validação: {e}")
            logger.error(f"Erro na validação: {e}")
            return 0.0
    
    async def run_full_test(self):
        """Executa teste completo."""
        print("🚀 QUALIA - Teste FWH com Dados Reais da Binance")
        print("=" * 60)
        
        # 1. Inicialização
        if not await self.initialize():
            return False
        
        # 2. Coleta de dados reais
        market_data = await self.test_real_data_collection()
        if market_data is None:
            return False
        
        # 3. Análise de Fibonacci
        fib_levels, wave_patterns = await self.test_fibonacci_analysis(market_data)
        if fib_levels is None:
            return False
        
        # 4. Geração de sinais
        signals = await self.test_strategy_signal_generation(market_data)
        
        # 5. Validação de qualidade
        quality_score = await self.test_data_quality_validation(market_data)
        
        # Resumo final
        print("\n" + "=" * 60)
        print("🎯 RESUMO DO TESTE")
        print("-" * 30)
        print(f"✅ Conexão Binance: OK")
        print(f"✅ Coleta de dados: {len(market_data)} períodos")
        print(f"✅ Análise Fibonacci: {len(fib_levels)} níveis")
        print(f"✅ Qualidade dos dados: {quality_score:.1%}")
        
        if not signals.empty:
            signal_type = signals.iloc[0].get('signal', 'HOLD')
            confidence = signals.iloc[0].get('confidence', 0)
            print(f"🚀 Sinal gerado: {signal_type.upper()} (confiança: {confidence:.1%})")
        else:
            print(f"⏸️ Sinal: HOLD")
        
        print("\n🌟 Teste concluído com sucesso!")
        print("📊 A estratégia FWH está operando com dados reais da Binance!")
        
        return True


async def main():
    """Função principal."""
    test = FWHBinanceTest()
    
    try:
        success = await test.run_full_test()
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⏹️ Teste interrompido pelo usuário")
        return 1
    except Exception as e:
        print(f"\n💥 Erro inesperado: {e}")
        logger.error(f"Erro inesperado: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
