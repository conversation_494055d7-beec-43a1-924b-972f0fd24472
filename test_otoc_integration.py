#!/usr/bin/env python3
"""
Teste de integração OTOC no paper trading.
"""

import sys
sys.path.append('src')
sys.path.append('scripts')

def test_otoc_integration():
    """Testa se a integração OTOC foi bem-sucedida."""
    try:
        print("🔍 Testando integração OTOC...")
        
        # Teste de importação
        from run_fwh_scalp_paper_trading import FWHScalpPaperTradingSystem
        print('✅ Importação do sistema de paper trading: OK')
        
        # Teste de inicialização básica
        config_path = 'config/fwh_scalp_config.yaml'
        system = FWHScalpPaperTradingSystem(config_path)
        print('✅ Inicialização do sistema: OK')
        
        # Verificar se OTOC foi integrado
        if hasattr(system, 'otoc_enabled'):
            print(f'✅ OTOC integrado: {system.otoc_enabled}')
        else:
            print('❌ OTOC não integrado')
            return False
            
        if hasattr(system, '_calculate_otoc_for_symbol'):
            print('✅ Método _calculate_otoc_for_symbol: OK')
        else:
            print('❌ Método _calculate_otoc_for_symbol: AUSENTE')
            return False
            
        if hasattr(system, '_apply_otoc_filter'):
            print('✅ Método _apply_otoc_filter: OK')
        else:
            print('❌ Método _apply_otoc_filter: AUSENTE')
            return False
            
        if hasattr(system, '_log_otoc_metrics'):
            print('✅ Método _log_otoc_metrics: OK')
        else:
            print('❌ Método _log_otoc_metrics: AUSENTE')
            return False
            
        # Verificar se consolidador OTOC foi inicializado
        if hasattr(system, 'mtf_consolidator'):
            if system.otoc_enabled and system.mtf_consolidator is not None:
                print('✅ MultiTimeframeSignalConsolidator com OTOC: OK')
            elif not system.otoc_enabled:
                print('⚠️ OTOC desabilitado na configuração (esperado)')
            else:
                print('❌ MultiTimeframeSignalConsolidator: AUSENTE')
                return False
        else:
            print('❌ Atributo mtf_consolidator: AUSENTE')
            return False
            
        # Verificar se coletor de métricas foi inicializado
        if hasattr(system, 'otoc_metrics_collector'):
            if system.otoc_enabled and system.otoc_metrics_collector is not None:
                print('✅ OTOCMetricsCollector: OK')
            elif not system.otoc_enabled:
                print('⚠️ OTOCMetricsCollector desabilitado (esperado)')
            else:
                print('❌ OTOCMetricsCollector: AUSENTE')
                return False
        else:
            print('❌ Atributo otoc_metrics_collector: AUSENTE')
            return False
        
        print('\n🎉 INTEGRAÇÃO OTOC COMPLETA E FUNCIONAL!')
        print('📊 Status da integração:')
        print(f'   - OTOC habilitado: {system.otoc_enabled}')
        print(f'   - Consolidador OTOC: {"✅" if system.mtf_consolidator else "❌"}')
        print(f'   - Coletor de métricas: {"✅" if system.otoc_metrics_collector else "❌"}')
        
        return True
        
    except Exception as e:
        print(f'❌ Erro na integração: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_otoc_integration()
    sys.exit(0 if success else 1)
