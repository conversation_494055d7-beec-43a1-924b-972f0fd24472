import numpy as np
import pytest

pytest.importorskip("pytest_benchmark")

# Importar o Universe antes para evitar ciclo de importacao durante o benchmark
from qualia.core.universe import QUALIAQuantumUniverse  # noqa:F401
from qualia.memory.quantum_pattern_memory import QuantumPatternMemory
from qualia.common_types import QuantumSignaturePacket

NUM_PATTERNS = 1000
VECTOR_DIM = 8
TOP_N = 5


def _populate_qpm(num_patterns: int, dim: int) -> QuantumPatternMemory:
    qpm = QuantumPatternMemory(
        max_memory_size_per_dimension=num_patterns + 10,
        enable_warmstart=False,
    )
    for _ in range(num_patterns):
        vec = np.random.rand(dim).tolist()
        qsp = QuantumSignaturePacket(vector=vec, metrics={})
        qpm.store_pattern(qsp, market_snapshot={}, outcome={})
    return qpm


@pytest.mark.benchmark(group="qpm-retrieval")
def test_qpm_retrieval_performance(benchmark):
    qpm = _populate_qpm(NUM_PATTERNS, VECTOR_DIM)
    query = QuantumSignaturePacket(
        vector=np.random.rand(VECTOR_DIM).tolist(), metrics={}
    )
    result = benchmark(lambda: qpm.retrieve_similar_patterns(query, top_n=TOP_N))
    assert isinstance(result, list)
