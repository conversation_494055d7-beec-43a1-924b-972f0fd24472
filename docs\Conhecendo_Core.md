# Conhecendo o Núcleo do QUALIA

O módulo `core` reúne as classes e utilidades responsáveis por orquestrar a evolução quântica, armazenar o estado e aplicar controles adaptativos. Ele é o coração do sistema e serve de base para `market`, `memory` e `strategies`.

## Principais componentes

- **QASTCore** – coordena o ciclo QAST usando `SymbolicEntropy` e controladores PID.
- **QUALIAQuantumUniverse** – mantém o vetor de estado e aplica operadores quânticos.
- **StateManagement** – gerencia persistência e restauração de estados.
- **PIDOptimizer** – calcula ajustes dinâmicos a partir das métricas de entropia.
- **Stability** – fornece métricas de consistência da massa informacional.
- **Channels / Position** – estruturas de comunicação e representação de posições.

Para detalhes de implementação consulte [docs/api/qast_core.md](api/qast_core.md) e os arquivos em `src/qualia/core`.

## Por que ele importa?

O núcleo centraliza o processamento de informações do QUALIA. Toda iteração do sistema passa pelo vetor de estado mantido pelo `QUALIAQuantumUniverse`, gerando métricas que guiam as estratégias. Alterações neste módulo afetam diretamente a evolução do projeto.

## Caminhos futuros

- **Modularização de operadores** para facilitar testes e manutenção.
- **Melhoria dos controladores adaptativos** com técnicas de aprendizado online.
- **Integração direta** com a metacognição para priorizar dinamicamente cada operador.
