#!/usr/bin/env python3
"""
Script de teste para o Warmup Manager
Testa o carregamento de dados históricos antes de iniciar o sistema.
"""

import asyncio
import sys
import os
from pathlib import Path

# Adicionar o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.qualia.core.warmup_manager import WarmupManager
from src.qualia.exchanges.kucoin_client import KuCoinClient
from src.qualia.utils.logger import setup_logging


async def test_warmup():
    """Testa o sistema de warmup com dados reais."""
    
    # Configurar logging
    setup_logging(log_level="INFO")
    
    print("🚀 Iniciando teste do Warmup Manager...")
    
    # Criar cliente KuCoin
    kucoin_client = KuCoinClient(
        api_key=os.getenv("KUCOIN_API_KEY", ""),
        api_secret=os.getenv("KUCOIN_API_SECRET", ""),
        api_passphrase=os.getenv("KUCOIN_API_PASSPHRASE", ""),
        sandbox=False
    )
    
    # Configurar requisitos mínimos para teste
    min_candles_test = {
        "5m": 50,   # 50 candles = ~4 horas
        "1h": 24,   # 24 candles = 1 dia
        "4h": 12,   # 12 candles = 2 dias
    }
    
    # Criar warmup manager
    warmup = WarmupManager(
        kucoin_client=kucoin_client,
        min_candles_required=min_candles_test,
        cache_dir="data/warmup_cache_test"
    )
    
    # Símbolos para testar
    symbols = ["BTC-USDT", "ETH-USDT"]
    timeframes = ["5m", "1h", "4h"]
    
    try:
        # Carregar dados históricos
        print(f"\n📊 Carregando dados para {symbols} em {timeframes}...")
        
        success = await warmup.load_historical_data(
            symbols=symbols,
            timeframes=timeframes,
            force_reload=False  # Usar cache se disponível
        )
        
        if success:
            print("\n✅ Warmup concluído com sucesso!")
            
            # Verificar se está pronto
            if warmup.is_ready(symbols, timeframes):
                print("✅ Sistema pronto para operar!")
                
                # Mostrar alguns dados de exemplo
                for symbol in symbols:
                    print(f"\n📈 Dados para {symbol}:")
                    for timeframe in timeframes:
                        df = warmup.get_data(symbol, timeframe)
                        if df is not None and not df.empty:
                            print(f"  {timeframe}: {len(df)} candles")
                            print(f"    Último preço: ${df['close'].iloc[-1]:.2f}")
                            print(f"    Volume 24h: ${df['volume'].sum():.2f}")
            else:
                print("❌ Sistema não está pronto - dados insuficientes")
                
        else:
            print("❌ Falha no warmup")
            
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Fechar cliente
        await kucoin_client.close()


if __name__ == "__main__":
    # Executar teste
    asyncio.run(test_warmup()) 