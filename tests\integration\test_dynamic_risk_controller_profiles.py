import numpy as np
from qualia.market.dynamic_risk_controller import DynamicRiskController
from tests.market.helpers import create_regime_data


def test_profiles_affect_calibration() -> None:
    data = create_regime_data("normal")
    price = float(data["close"][-1])

    controller_cons = DynamicRiskController(risk_profile="conservative")
    controller_aggr = DynamicRiskController(risk_profile="aggressive")

    result_cons = controller_cons.calibrate_risk_levels("TEST", data, price)
    result_aggr = controller_aggr.calibrate_risk_levels("TEST", data, price)

    assert result_aggr.stop_loss_distance > result_cons.stop_loss_distance
    assert np.isfinite(result_aggr.take_profit_distance)
    assert np.isfinite(result_cons.take_profit_distance)
