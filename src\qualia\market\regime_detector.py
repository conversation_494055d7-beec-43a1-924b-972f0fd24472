"""
QUALIA D-06: Market Regime Detector
===================================

Sistema de detecção de regimes de mercado para otimização adaptativa.
Classifica mercados em bull/bear/sideways/volatile/stable baseado em indicadores técnicos.
"""

from __future__ import annotations

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging

from ..utils.logger import get_logger
from ..indicators import ema, rsi, atr, macd
# from ..live_feed import NormalizedTicker  # Removido para evitar dependência circular
from ..utils.event_bus import EventBus

logger = get_logger(__name__)


class MarketRegime(Enum):
    """Regimes de mercado detectáveis."""
    BULL = "bull"           # Tendência de alta sustentada
    BEAR = "bear"           # Tendência de baixa sustentada  
    SIDEWAYS = "sideways"   # Movimento lateral/consolidação
    VOLATILE = "volatile"   # Alta volatilidade sem direção clara
    STABLE = "stable"       # Baixa volatilidade, movimento mínimo


@dataclass
class RegimeMetrics:
    """Métricas calculadas para detecção de regime."""
    # Trend metrics
    trend_strength: float = 0.0      # -1 (bear) to +1 (bull)
    trend_consistency: float = 0.0   # 0 to 1
    
    # Volatility metrics
    volatility_percentile: float = 0.0  # 0 to 100
    volatility_trend: float = 0.0       # -1 (decreasing) to +1 (increasing)
    
    # Momentum metrics
    momentum_score: float = 0.0      # -1 to +1
    momentum_divergence: float = 0.0 # 0 to 1
    
    # Volume metrics (if available)
    volume_trend: float = 0.0        # -1 to +1
    volume_spike: bool = False
    
    # Technical indicators
    rsi_level: float = 50.0          # 0 to 100
    macd_signal: float = 0.0         # -1 to +1
    
    # Confidence metrics
    regime_confidence: float = 0.0   # 0 to 1
    data_quality: float = 1.0        # 0 to 1


@dataclass
class RegimeDetection:
    """Resultado da detecção de regime."""
    regime: MarketRegime
    confidence: float
    metrics: RegimeMetrics
    timestamp: datetime
    symbol: str
    lookback_periods: int
    
    # Historical context
    previous_regime: Optional[MarketRegime] = None
    regime_duration: int = 0  # periods in current regime
    regime_changes_24h: int = 0


@dataclass
class RegimeDetectorConfig:
    """Configuração do detector de regimes."""
    # Data requirements
    min_periods: int = 50           # Mínimo de períodos para análise
    lookback_periods: int = 100     # Períodos para análise
    update_interval: float = 60.0   # Segundos entre atualizações
    
    # Trend detection
    trend_ema_fast: int = 12        # EMA rápida para trend
    trend_ema_slow: int = 26        # EMA lenta para trend
    trend_strength_threshold: float = 0.02  # 2% para trend significativo
    
    # Volatility analysis
    volatility_window: int = 20     # Janela para cálculo de volatilidade
    volatility_lookback: int = 252  # Períodos para percentil de volatilidade
    high_volatility_threshold: float = 75.0  # Percentil para alta volatilidade
    low_volatility_threshold: float = 25.0   # Percentil para baixa volatilidade
    
    # Momentum indicators
    rsi_period: int = 14            # Período do RSI
    macd_fast: int = 12             # MACD EMA rápida
    macd_slow: int = 26             # MACD EMA lenta
    macd_signal: int = 9            # MACD signal line
    
    # Regime classification thresholds
    bull_trend_threshold: float = 0.3       # Trend strength para bull
    bear_trend_threshold: float = -0.3      # Trend strength para bear
    sideways_volatility_max: float = 50.0   # Max volatility para sideways
    volatile_threshold: float = 75.0        # Min volatility para volatile
    stable_threshold: float = 25.0          # Max volatility para stable
    
    # Confidence calculation
    min_confidence: float = 0.6     # Confiança mínima para mudança de regime
    confidence_decay: float = 0.95  # Decay da confiança ao longo do tempo


class MarketRegimeDetector:
    """
    Detector de regimes de mercado usando análise técnica multi-dimensional.
    
    Funcionalidades:
    - Detecção automática de regimes bull/bear/sideways/volatile/stable
    - Análise de trend, volatilidade, momentum e volume
    - Cálculo de confiança e qualidade dos dados
    - Integração com event bus para notificações
    - Histórico de mudanças de regime
    """
    
    def __init__(self, 
                 config: Optional[RegimeDetectorConfig] = None,
                 event_bus: Optional[EventBus] = None):
        self.config = config or RegimeDetectorConfig()
        self.event_bus = event_bus
        
        # State management
        self.current_regimes: Dict[str, RegimeDetection] = {}
        self.price_history: Dict[str, List[Tuple[datetime, float]]] = {}
        self.volume_history: Dict[str, List[Tuple[datetime, float]]] = {}
        self.regime_history: Dict[str, List[RegimeDetection]] = {}
        
        # Analysis cache
        self.metrics_cache: Dict[str, RegimeMetrics] = {}
        self.last_update: Dict[str, datetime] = {}
        
        # Running state
        self.is_running = False
        self.update_task: Optional[asyncio.Task] = None
        
        logger.info("✅ MarketRegimeDetector inicializado")
    
    async def start(self):
        """Inicia o detector de regimes."""
        if self.is_running:
            logger.warning("Detector já está rodando")
            return
            
        self.is_running = True
        self.update_task = asyncio.create_task(self._update_loop())
        logger.info("🚀 MarketRegimeDetector iniciado")
    
    async def stop(self):
        """Para o detector de regimes."""
        self.is_running = False
        if self.update_task:
            self.update_task.cancel()
            try:
                await self.update_task
            except asyncio.CancelledError:
                pass
        logger.info("⏹️ MarketRegimeDetector parado")
    
    def add_price_data(self, symbol: str, price: float, volume: Optional[float] = None, 
                      timestamp: Optional[datetime] = None):
        """Adiciona dados de preço para análise."""
        if timestamp is None:
            timestamp = datetime.now(timezone.utc)
            
        # Adicionar preço
        if symbol not in self.price_history:
            self.price_history[symbol] = []
        self.price_history[symbol].append((timestamp, price))
        
        # Manter apenas dados necessários
        max_periods = max(self.config.lookback_periods, self.config.volatility_lookback) + 50
        if len(self.price_history[symbol]) > max_periods:
            self.price_history[symbol] = self.price_history[symbol][-max_periods:]
        
        # Adicionar volume se disponível
        if volume is not None:
            if symbol not in self.volume_history:
                self.volume_history[symbol] = []
            self.volume_history[symbol].append((timestamp, volume))
            if len(self.volume_history[symbol]) > max_periods:
                self.volume_history[symbol] = self.volume_history[symbol][-max_periods:]
    
    def on_ticker_update(self, ticker: Dict[str, Any]):
        """Callback para atualizações de ticker do live feed."""
        self.add_price_data(
            symbol=ticker.get('symbol', ''),
            price=ticker.get('price', 0.0),
            volume=ticker.get('volume', 0.0),
            timestamp=ticker.get('timestamp', datetime.now(timezone.utc))
        )
    
    async def detect_regime(self, symbol: str, force_update: bool = False) -> Optional[RegimeDetection]:
        """
        Detecta o regime de mercado atual para um símbolo.
        
        Args:
            symbol: Símbolo para análise
            force_update: Força atualização mesmo se recente
            
        Returns:
            RegimeDetection ou None se dados insuficientes
        """
        # Verificar se precisa atualizar
        if not force_update:
            last_update = self.last_update.get(symbol)
            if last_update and (datetime.now(timezone.utc) - last_update).total_seconds() < self.config.update_interval:
                return self.current_regimes.get(symbol)
        
        # Verificar dados suficientes
        if symbol not in self.price_history or len(self.price_history[symbol]) < self.config.min_periods:
            logger.debug(f"Dados insuficientes para {symbol}: {len(self.price_history.get(symbol, []))} períodos")
            return None
        
        try:
            # Calcular métricas
            metrics = await self._calculate_regime_metrics(symbol)
            
            # Classificar regime
            regime, confidence = self._classify_regime(metrics)
            
            # Criar detecção
            detection = RegimeDetection(
                regime=regime,
                confidence=confidence,
                metrics=metrics,
                timestamp=datetime.now(timezone.utc),
                symbol=symbol,
                lookback_periods=self.config.lookback_periods
            )
            
            # Adicionar contexto histórico
            self._add_historical_context(symbol, detection)
            
            # Atualizar estado
            previous_regime = self.current_regimes.get(symbol)
            self.current_regimes[symbol] = detection
            self.last_update[symbol] = detection.timestamp
            
            # Adicionar ao histórico
            if symbol not in self.regime_history:
                self.regime_history[symbol] = []
            self.regime_history[symbol].append(detection)
            
            # Manter histórico limitado
            if len(self.regime_history[symbol]) > 1000:
                self.regime_history[symbol] = self.regime_history[symbol][-1000:]
            
            # Notificar mudança de regime
            if previous_regime and previous_regime.regime != regime and confidence >= self.config.min_confidence:
                await self._notify_regime_change(symbol, previous_regime.regime, detection)
            
            logger.debug(f"Regime detectado para {symbol}: {regime.value} (confiança: {confidence:.2f})")
            return detection
            
        except Exception as e:
            logger.error(f"Erro ao detectar regime para {symbol}: {e}")
            return None
    
    async def get_current_regime(self, symbol: str) -> Optional[RegimeDetection]:
        """Retorna o regime atual para um símbolo."""
        return self.current_regimes.get(symbol)
    
    def get_regime_history(self, symbol: str, hours: int = 24) -> List[RegimeDetection]:
        """Retorna histórico de regimes para um símbolo."""
        if symbol not in self.regime_history:
            return []
            
        cutoff = datetime.now(timezone.utc) - timedelta(hours=hours)
        return [d for d in self.regime_history[symbol] if d.timestamp >= cutoff]
    
    async def _update_loop(self):
        """Loop principal de atualização."""
        while self.is_running:
            try:
                # Atualizar todos os símbolos com dados
                for symbol in list(self.price_history.keys()):
                    await self.detect_regime(symbol)
                
                # Aguardar próxima atualização
                await asyncio.sleep(self.config.update_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Erro no loop de atualização: {e}")
                await asyncio.sleep(5.0)  # Aguardar antes de tentar novamente

    async def _calculate_regime_metrics(self, symbol: str) -> RegimeMetrics:
        """Calcula métricas para detecção de regime."""
        prices_data = self.price_history[symbol]
        volumes_data = self.volume_history.get(symbol, [])

        # Extrair arrays de preços
        prices = np.array([p[1] for p in prices_data[-self.config.lookback_periods:]])
        timestamps = [p[0] for p in prices_data[-self.config.lookback_periods:]]

        # Calcular métricas de trend
        trend_strength, trend_consistency = self._calculate_trend_metrics(prices)

        # Calcular métricas de volatilidade
        volatility_percentile, volatility_trend = self._calculate_volatility_metrics(symbol, prices)

        # Calcular métricas de momentum
        momentum_score, momentum_divergence = self._calculate_momentum_metrics(prices)

        # Calcular métricas de volume
        volume_trend, volume_spike = self._calculate_volume_metrics(volumes_data, timestamps)

        # Calcular indicadores técnicos
        rsi_level = self._calculate_rsi_level(prices)
        macd_signal = self._calculate_macd_signal(prices)

        # Calcular confiança e qualidade dos dados
        regime_confidence = self._calculate_regime_confidence(prices, len(timestamps))
        data_quality = self._calculate_data_quality(timestamps)

        return RegimeMetrics(
            trend_strength=trend_strength,
            trend_consistency=trend_consistency,
            volatility_percentile=volatility_percentile,
            volatility_trend=volatility_trend,
            momentum_score=momentum_score,
            momentum_divergence=momentum_divergence,
            volume_trend=volume_trend,
            volume_spike=volume_spike,
            rsi_level=rsi_level,
            macd_signal=macd_signal,
            regime_confidence=regime_confidence,
            data_quality=data_quality
        )

    def _calculate_trend_metrics(self, prices: np.ndarray) -> Tuple[float, float]:
        """Calcula força e consistência da tendência."""
        if len(prices) < self.config.trend_ema_slow:
            return 0.0, 0.0

        # Calcular EMAs
        ema_fast = ema(prices, self.config.trend_ema_fast)
        ema_slow = ema(prices, self.config.trend_ema_slow)

        # Força da tendência (diferença percentual entre EMAs)
        current_fast = ema_fast[-1]
        current_slow = ema_slow[-1]

        if current_slow != 0:
            trend_strength = (current_fast - current_slow) / current_slow
            # Normalizar para [-1, 1]
            trend_strength = np.tanh(trend_strength * 10)
        else:
            trend_strength = 0.0

        # Consistência da tendência (% de períodos onde fast > slow para uptrend)
        valid_periods = min(len(ema_fast), len(ema_slow))
        if valid_periods > 10:
            fast_above_slow = np.sum(ema_fast[-valid_periods:] > ema_slow[-valid_periods:])
            trend_consistency = abs(fast_above_slow / valid_periods - 0.5) * 2  # 0 to 1
        else:
            trend_consistency = 0.0

        return float(trend_strength), float(trend_consistency)

    def _calculate_volatility_metrics(self, symbol: str, prices: np.ndarray) -> Tuple[float, float]:
        """Calcula percentil de volatilidade e tendência de volatilidade."""
        if len(prices) < self.config.volatility_window:
            return 50.0, 0.0

        # Calcular volatilidade atual (desvio padrão dos retornos)
        returns = np.diff(np.log(prices))
        current_volatility = np.std(returns[-self.config.volatility_window:]) * np.sqrt(252)  # Anualizada

        # Calcular percentil histórico
        all_prices = np.array([p[1] for p in self.price_history[symbol]])
        if len(all_prices) >= self.config.volatility_lookback:
            historical_returns = np.diff(np.log(all_prices))

            # Calcular volatilidades históricas em janelas móveis
            historical_vols = []
            for i in range(self.config.volatility_window, len(historical_returns)):
                window_vol = np.std(historical_returns[i-self.config.volatility_window:i]) * np.sqrt(252)
                historical_vols.append(window_vol)

            if historical_vols:
                volatility_percentile = (np.sum(np.array(historical_vols) <= current_volatility) /
                                       len(historical_vols)) * 100
            else:
                volatility_percentile = 50.0
        else:
            volatility_percentile = 50.0

        # Tendência de volatilidade (comparar últimas 2 janelas)
        if len(returns) >= self.config.volatility_window * 2:
            recent_vol = np.std(returns[-self.config.volatility_window:])
            previous_vol = np.std(returns[-self.config.volatility_window*2:-self.config.volatility_window])

            if previous_vol != 0:
                volatility_trend = (recent_vol - previous_vol) / previous_vol
                volatility_trend = np.tanh(volatility_trend * 5)  # Normalizar para [-1, 1]
            else:
                volatility_trend = 0.0
        else:
            volatility_trend = 0.0

        return float(volatility_percentile), float(volatility_trend)

    def _calculate_momentum_metrics(self, prices: np.ndarray) -> Tuple[float, float]:
        """Calcula score de momentum e divergência."""
        if len(prices) < 20:
            return 0.0, 0.0

        # Score de momentum (taxa de mudança de preço)
        lookback = min(20, len(prices) // 2)
        momentum_score = (prices[-1] - prices[-lookback]) / prices[-lookback]
        momentum_score = np.tanh(momentum_score * 10)  # Normalizar para [-1, 1]

        # Divergência de momentum (comparar momentum de preço vs momentum de volume)
        # Por simplicidade, usar divergência entre momentum de curto e longo prazo
        if len(prices) >= 40:
            short_momentum = (prices[-1] - prices[-10]) / prices[-10]
            long_momentum = (prices[-1] - prices[-30]) / prices[-30]

            # Divergência quando momentums têm sinais opostos
            if short_momentum * long_momentum < 0:
                momentum_divergence = abs(short_momentum - long_momentum)
            else:
                momentum_divergence = 0.0

            momentum_divergence = min(momentum_divergence, 1.0)  # Cap em 1.0
        else:
            momentum_divergence = 0.0

        return float(momentum_score), float(momentum_divergence)

    def _calculate_volume_metrics(self, volumes_data: List[Tuple[datetime, float]],
                                timestamps: List[datetime]) -> Tuple[float, bool]:
        """Calcula tendência de volume e detecta spikes."""
        if not volumes_data or len(volumes_data) < 10:
            return 0.0, False

        # Alinhar volumes com timestamps de preços
        volume_dict = {v[0]: v[1] for v in volumes_data}
        aligned_volumes = []

        for ts in timestamps[-20:]:  # Últimos 20 períodos
            # Encontrar volume mais próximo no tempo
            closest_volume = None
            min_diff = timedelta.max

            for vol_ts, vol_val in volumes_data:
                diff = abs(vol_ts - ts)
                if diff < min_diff:
                    min_diff = diff
                    closest_volume = vol_val

            if closest_volume is not None:
                aligned_volumes.append(closest_volume)

        if len(aligned_volumes) < 10:
            return 0.0, False

        volumes = np.array(aligned_volumes)

        # Tendência de volume (regressão linear simples)
        x = np.arange(len(volumes))
        if len(volumes) > 1:
            slope = np.polyfit(x, volumes, 1)[0]
            volume_trend = np.tanh(slope / np.mean(volumes) * 10)  # Normalizar
        else:
            volume_trend = 0.0

        # Spike de volume (volume atual vs média)
        if len(volumes) >= 5:
            recent_volume = volumes[-1]
            avg_volume = np.mean(volumes[:-1])
            volume_spike = recent_volume > avg_volume * 2.0  # 2x a média
        else:
            volume_spike = False

        return float(volume_trend), volume_spike

    def _calculate_rsi_level(self, prices: np.ndarray) -> float:
        """Calcula nível atual do RSI."""
        if len(prices) < self.config.rsi_period + 1:
            return 50.0

        try:
            rsi_values = rsi(prices, self.config.rsi_period)
            return float(rsi_values[-1]) if not np.isnan(rsi_values[-1]) else 50.0
        except:
            return 50.0

    def _calculate_macd_signal(self, prices: np.ndarray) -> float:
        """Calcula sinal do MACD (-1 a +1)."""
        if len(prices) < self.config.macd_slow + self.config.macd_signal:
            return 0.0

        try:
            macd_line, macd_signal, _ = macd(prices,
                                           self.config.macd_fast,
                                           self.config.macd_slow,
                                           self.config.macd_signal)

            # Sinal baseado na posição do MACD vs signal line
            if not np.isnan(macd_line[-1]) and not np.isnan(macd_signal[-1]):
                diff = macd_line[-1] - macd_signal[-1]
                return float(np.tanh(diff * 100))  # Normalizar para [-1, 1]
            else:
                return 0.0
        except:
            return 0.0

    def _calculate_regime_confidence(self, prices: np.ndarray, data_points: int) -> float:
        """Calcula confiança na detecção do regime."""
        # Fatores que afetam a confiança:
        # 1. Quantidade de dados
        data_factor = min(data_points / self.config.lookback_periods, 1.0)

        # 2. Consistência dos preços (menos gaps = mais confiança)
        if len(prices) > 1:
            returns = np.diff(prices) / prices[:-1]
            consistency_factor = 1.0 - min(np.std(returns) * 10, 1.0)
        else:
            consistency_factor = 0.5

        # 3. Idade dos dados (dados mais recentes = mais confiança)
        age_factor = 1.0  # Assumir dados recentes por simplicidade

        confidence = (data_factor * 0.4 + consistency_factor * 0.4 + age_factor * 0.2)
        return float(max(0.0, min(1.0, confidence)))

    def _calculate_data_quality(self, timestamps: List[datetime]) -> float:
        """Calcula qualidade dos dados baseado na consistência temporal."""
        if len(timestamps) < 2:
            return 0.5

        # Calcular intervalos entre timestamps
        intervals = [(timestamps[i] - timestamps[i-1]).total_seconds()
                    for i in range(1, len(timestamps))]

        if not intervals:
            return 0.5

        # Qualidade baseada na consistência dos intervalos
        mean_interval = np.mean(intervals)
        std_interval = np.std(intervals)

        if mean_interval > 0:
            consistency = 1.0 - min(std_interval / mean_interval, 1.0)
        else:
            consistency = 0.0

        return float(max(0.0, min(1.0, consistency)))

    def _classify_regime(self, metrics: RegimeMetrics) -> Tuple[MarketRegime, float]:
        """Classifica o regime baseado nas métricas."""
        # Scores para cada regime
        regime_scores = {
            MarketRegime.BULL: 0.0,
            MarketRegime.BEAR: 0.0,
            MarketRegime.SIDEWAYS: 0.0,
            MarketRegime.VOLATILE: 0.0,
            MarketRegime.STABLE: 0.0
        }

        # Bull market indicators
        if metrics.trend_strength > self.config.bull_trend_threshold:
            regime_scores[MarketRegime.BULL] += metrics.trend_strength * 2
        if metrics.momentum_score > 0.2:
            regime_scores[MarketRegime.BULL] += metrics.momentum_score
        if metrics.rsi_level > 50:
            regime_scores[MarketRegime.BULL] += (metrics.rsi_level - 50) / 50 * 0.5
        if metrics.macd_signal > 0:
            regime_scores[MarketRegime.BULL] += metrics.macd_signal * 0.5

        # Bear market indicators
        if metrics.trend_strength < self.config.bear_trend_threshold:
            regime_scores[MarketRegime.BEAR] += abs(metrics.trend_strength) * 2
        if metrics.momentum_score < -0.2:
            regime_scores[MarketRegime.BEAR] += abs(metrics.momentum_score)
        if metrics.rsi_level < 50:
            regime_scores[MarketRegime.BEAR] += (50 - metrics.rsi_level) / 50 * 0.5
        if metrics.macd_signal < 0:
            regime_scores[MarketRegime.BEAR] += abs(metrics.macd_signal) * 0.5

        # Sideways market indicators
        if abs(metrics.trend_strength) < 0.1:
            regime_scores[MarketRegime.SIDEWAYS] += 1.0
        if metrics.volatility_percentile < self.config.sideways_volatility_max:
            regime_scores[MarketRegime.SIDEWAYS] += (self.config.sideways_volatility_max - metrics.volatility_percentile) / 50
        if 40 < metrics.rsi_level < 60:
            regime_scores[MarketRegime.SIDEWAYS] += 0.5

        # Volatile market indicators
        if metrics.volatility_percentile > self.config.volatile_threshold:
            regime_scores[MarketRegime.VOLATILE] += (metrics.volatility_percentile - self.config.volatile_threshold) / 25
        if metrics.momentum_divergence > 0.3:
            regime_scores[MarketRegime.VOLATILE] += metrics.momentum_divergence
        if metrics.volume_spike:
            regime_scores[MarketRegime.VOLATILE] += 0.5

        # Stable market indicators
        if metrics.volatility_percentile < self.config.stable_threshold:
            regime_scores[MarketRegime.STABLE] += (self.config.stable_threshold - metrics.volatility_percentile) / 25
        if abs(metrics.trend_strength) < 0.05:
            regime_scores[MarketRegime.STABLE] += 1.0
        if abs(metrics.momentum_score) < 0.1:
            regime_scores[MarketRegime.STABLE] += 0.5

        # Encontrar regime com maior score
        best_regime = max(regime_scores.keys(), key=lambda k: regime_scores[k])
        max_score = regime_scores[best_regime]

        # Calcular confiança baseada na separação entre o melhor e segundo melhor
        sorted_scores = sorted(regime_scores.values(), reverse=True)
        if len(sorted_scores) > 1 and sorted_scores[0] > 0:
            separation = (sorted_scores[0] - sorted_scores[1]) / sorted_scores[0]
            confidence = min(max_score * separation * metrics.regime_confidence, 1.0)
        else:
            confidence = max_score * metrics.regime_confidence

        return best_regime, float(max(0.0, min(1.0, confidence)))

    def _add_historical_context(self, symbol: str, detection: RegimeDetection):
        """Adiciona contexto histórico à detecção."""
        # Regime anterior
        if symbol in self.current_regimes:
            detection.previous_regime = self.current_regimes[symbol].regime

        # Duração no regime atual
        if symbol in self.regime_history:
            current_regime_detections = [
                d for d in reversed(self.regime_history[symbol])
                if d.regime == detection.regime
            ]
            detection.regime_duration = len(current_regime_detections)

        # Mudanças de regime nas últimas 24h
        if symbol in self.regime_history:
            cutoff = datetime.now(timezone.utc) - timedelta(hours=24)
            recent_detections = [d for d in self.regime_history[symbol] if d.timestamp >= cutoff]

            regime_changes = 0
            if recent_detections:
                current_regime = recent_detections[0].regime
                for d in recent_detections[1:]:
                    if d.regime != current_regime:
                        regime_changes += 1
                        current_regime = d.regime

            detection.regime_changes_24h = regime_changes

    async def _notify_regime_change(self, symbol: str, old_regime: MarketRegime,
                                  new_detection: RegimeDetection):
        """Notifica mudança de regime via event bus."""
        if not self.event_bus:
            return

        try:
            event_data = {
                "symbol": symbol,
                "old_regime": old_regime.value,
                "new_regime": new_detection.regime.value,
                "confidence": new_detection.confidence,
                "timestamp": new_detection.timestamp.isoformat(),
                "metrics": {
                    "trend_strength": new_detection.metrics.trend_strength,
                    "volatility_percentile": new_detection.metrics.volatility_percentile,
                    "momentum_score": new_detection.metrics.momentum_score,
                    "rsi_level": new_detection.metrics.rsi_level
                }
            }

            self.event_bus.publish("market.regime.changed", event_data)
            logger.info(f"🔄 Mudança de regime {symbol}: {old_regime.value} → {new_detection.regime.value} "
                       f"(confiança: {new_detection.confidence:.2f})")

        except Exception as e:
            logger.error(f"Erro ao notificar mudança de regime: {e}")

    def get_regime_summary(self) -> Dict[str, Any]:
        """Retorna resumo dos regimes atuais."""
        summary = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "total_symbols": len(self.current_regimes),
            "regimes": {}
        }

        # Contar regimes por tipo
        regime_counts = {}
        for detection in self.current_regimes.values():
            regime = detection.regime.value
            if regime not in regime_counts:
                regime_counts[regime] = 0
            regime_counts[regime] += 1

        summary["regime_distribution"] = regime_counts

        # Detalhes por símbolo
        for symbol, detection in self.current_regimes.items():
            summary["regimes"][symbol] = {
                "regime": detection.regime.value,
                "confidence": detection.confidence,
                "duration": detection.regime_duration,
                "changes_24h": detection.regime_changes_24h,
                "trend_strength": detection.metrics.trend_strength,
                "volatility_percentile": detection.metrics.volatility_percentile
            }

        return summary


# Função de conveniência para criar detector global
_global_detector: Optional[MarketRegimeDetector] = None

def get_global_regime_detector(config: Optional[RegimeDetectorConfig] = None,
                              event_bus: Optional[EventBus] = None) -> MarketRegimeDetector:
    """Retorna instância global do detector de regimes."""
    global _global_detector

    if _global_detector is None:
        _global_detector = MarketRegimeDetector(config=config, event_bus=event_bus)

    return _global_detector

def start_global_regime_detector(config: Optional[RegimeDetectorConfig] = None,
                                event_bus: Optional[EventBus] = None) -> MarketRegimeDetector:
    """Inicia detector global de regimes."""
    detector = get_global_regime_detector(config=config, event_bus=event_bus)
    asyncio.create_task(detector.start())
    return detector
