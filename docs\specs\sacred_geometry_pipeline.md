# Pipeline de Geometria Sagrada

Este documento descreve o fluxo utilizado para analisar fractais guiados por geometria no QUALIA.

## Etapas principais

1. **Geração do padrão** – `generate_sri_yantra(n)` cria as coordenadas normalizadas do símbolo Sri Yantra com `n` camadas.
2. **Coerência topológica** – `compute_topological_coherence` avalia a uniformidade do padrão produzido.
3. **Detecção fractal** – `detect_geometry_guided_fractals` aplica a análise fractal tradicional e pondera os resultados pela coerência geométrica.

## Exemplo de uso

```python
from qualia.temporal.pattern_detector import QuantumTemporalPatternDetector
import numpy as np

series = np.random.rand(64)

detector = QuantumTemporalPatternDetector(geometry_guided=True)
result = detector.detect_geometry_guided_fractals(series, levels=5)
print(result)
```
