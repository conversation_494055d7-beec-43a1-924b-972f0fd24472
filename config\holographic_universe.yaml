experimental:
  consciousness_influence: 0.05
  consciousness_integration: false
  entanglement_range: 50
  quantum_entanglement: false
  retrocausal_feedback: false
  retrocausal_strength: 0.1
farsight_extension:
  event_conversion:
    amplitude_multiplier: 1.5
    spatial_base: 5.0
    spatial_velocity_factor: 20.0
    temporal_base: 2.0
    temporal_curvature_factor: 8.0
  insight_combination:
    high_confidence_threshold: 0.8
    max_signals_summary: 5
  simulation:
    analysis_interval: 10
    sleep_between_steps: 0.001
    step_duration: 0.1
    steps: 100
holographic_universe:
  diffusion_rate: 0.25
  feedback_strength: 0.06
  field_size:
  - 200
  - 200
  modality_regions:
    audio:
    - 50
    - 50
    market:
    - 100
    - 100
    news:
    - 150
    - 150
    sentiment:
    - 50
    - 150
    visual:
    - 150
    - 50
  pattern_detection:
    analysis_window: 50
    min_history_length: 20
  symbol_positions:
    ADA: auto
    BTC: auto
    DOT: auto
    ETH: auto
    LINK: auto
    MATIC: auto
    SOL: auto
    UNI: auto
  time_steps: 500
  trading_signals:
    min_strength: 0.7
    proximity_threshold: 30
  wavelet_scales:
    max: 40
    min: 1
    step: 1
hyperparams:
  note: Parâmetros de amplificação e confiança movidos para hyperparams.yaml
  source: ../qualia/config/hyperparams.yaml
monitoring:
  logging:
    holographic_events: true
    level: INFO
    pattern_detection: true
    signal_generation: true
  metrics:
    active_events: true
    enabled: true
    field_energy: true
    field_entropy: true
    patterns_detected: true
    trading_signals: true
performance:
  event_time_threshold: 1e-6
  max_events_queue: 1000
  max_field_history: 500
  max_patterns_history: 1000
  pattern_deduplication: true
