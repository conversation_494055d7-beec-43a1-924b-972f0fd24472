"""
QUALIA Statistical Analyzer - D-07 Implementation

Análise estatística avançada para testes A/B, incluindo testes de significância,
intervalos de confiança e validação de hipóteses para comparação simulador vs live trading.
"""

import numpy as np
import pandas as pd
from datetime import datetime
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple, Any
from scipy import stats
import logging

from ..utils.logging_config import get_qualia_logger
from .performance_comparator import PerformanceMetrics

logger = get_qualia_logger(__name__)


@dataclass
class StatisticalTest:
    """Resultado de um teste estatístico."""
    
    test_name: str
    test_statistic: float
    p_value: float
    is_significant: bool
    confidence_level: float
    critical_value: Optional[float] = None
    degrees_of_freedom: Optional[int] = None
    effect_size: Optional[float] = None


@dataclass
class StatisticalResult:
    """Resultado completo da análise estatística."""
    
    # Identificação
    analysis_id: str
    timestamp: datetime
    
    # Configurações
    confidence_level: float
    significance_threshold: float
    
    # Testes realizados
    tests_performed: List[StatisticalTest] = field(default_factory=list)
    
    # Resultados principais
    is_significant: bool = False
    overall_p_value: float = 1.0
    confidence_interval: Tuple[float, float] = (0.0, 0.0)
    
    # Métricas de comparação
    mean_difference: float = 0.0
    std_difference: float = 0.0
    effect_size_cohens_d: float = 0.0
    
    # Power analysis
    statistical_power: float = 0.0
    required_sample_size: int = 0
    actual_sample_size: int = 0
    
    # Distribuições
    simulator_distribution: Dict[str, float] = field(default_factory=dict)
    live_distribution: Dict[str, float] = field(default_factory=dict)
    
    # Recomendações
    recommendations: List[str] = field(default_factory=list)
    interpretation: str = ""


class StatisticalAnalyzer:
    """Analisador estatístico para testes A/B."""
    
    def __init__(self):
        # Configurações padrão
        self.default_confidence_level = 0.95
        self.default_significance_threshold = 0.05
        self.min_sample_size = 30  # Para CLT
        self.effect_size_thresholds = {
            "small": 0.2,
            "medium": 0.5,
            "large": 0.8
        }
        
        logger.info("📊 StatisticalAnalyzer inicializado")
    
    async def analyze_performance_difference(
        self,
        simulator_metrics: PerformanceMetrics,
        live_metrics: PerformanceMetrics,
        confidence_level: float = 0.95,
        significance_threshold: float = 0.05
    ) -> StatisticalResult:
        """Analisa diferença de performance entre simulador e live trading."""
        
        logger.info("🔬 Iniciando análise estatística de performance...")
        
        analysis_id = f"perf_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        result = StatisticalResult(
            analysis_id=analysis_id,
            timestamp=datetime.now(),
            confidence_level=confidence_level,
            significance_threshold=significance_threshold
        )
        
        # Extrair dados para análise
        sim_returns = self._extract_returns(simulator_metrics)
        live_returns = self._extract_returns(live_metrics)
        
        if len(sim_returns) < self.min_sample_size or len(live_returns) < self.min_sample_size:
            logger.warning(f"⚠️ Tamanho de amostra insuficiente: sim={len(sim_returns)}, live={len(live_returns)}")
            result.recommendations.append("Aumentar duração do teste para obter amostra estatisticamente válida")
            return result
        
        # Realizar testes estatísticos
        await self._perform_statistical_tests(sim_returns, live_returns, result)
        
        # Calcular métricas de comparação
        self._calculate_comparison_metrics(sim_returns, live_returns, result)
        
        # Power analysis
        self._perform_power_analysis(sim_returns, live_returns, result)
        
        # Análise de distribuições
        self._analyze_distributions(sim_returns, live_returns, result)
        
        # Gerar interpretação e recomendações
        self._generate_interpretation(result)
        
        logger.info(f"✅ Análise estatística concluída - Significativo: {result.is_significant}")
        return result
    
    async def analyze_metric_comparison(
        self,
        metric_name: str,
        simulator_values: List[float],
        live_values: List[float],
        confidence_level: float = 0.95
    ) -> StatisticalTest:
        """Analisa comparação de uma métrica específica."""
        
        logger.info(f"📈 Analisando métrica: {metric_name}")
        
        # Teste t para duas amostras independentes
        if len(simulator_values) >= self.min_sample_size and len(live_values) >= self.min_sample_size:
            # Teste de normalidade primeiro
            sim_normal = self._test_normality(simulator_values)
            live_normal = self._test_normality(live_values)
            
            if sim_normal and live_normal:
                # Usar teste t paramétrico
                statistic, p_value = stats.ttest_ind(simulator_values, live_values)
                test_name = f"T-test independente - {metric_name}"
            else:
                # Usar teste não-paramétrico
                statistic, p_value = stats.mannwhitneyu(simulator_values, live_values, alternative='two-sided')
                test_name = f"Mann-Whitney U - {metric_name}"
        else:
            # Amostras pequenas - usar teste não-paramétrico
            statistic, p_value = stats.mannwhitneyu(simulator_values, live_values, alternative='two-sided')
            test_name = f"Mann-Whitney U (pequena amostra) - {metric_name}"
        
        # Calcular effect size
        effect_size = self._calculate_cohens_d(simulator_values, live_values)
        
        is_significant = p_value < (1 - confidence_level)
        
        return StatisticalTest(
            test_name=test_name,
            test_statistic=statistic,
            p_value=p_value,
            is_significant=is_significant,
            confidence_level=confidence_level,
            effect_size=effect_size
        )
    
    def calculate_confidence_interval(
        self,
        data: List[float],
        confidence_level: float = 0.95
    ) -> Tuple[float, float]:
        """Calcula intervalo de confiança para uma amostra."""
        
        if len(data) < 2:
            return (0.0, 0.0)
        
        mean = np.mean(data)
        std_err = stats.sem(data)  # Standard error of the mean
        
        # Graus de liberdade
        df = len(data) - 1
        
        # Valor crítico t
        alpha = 1 - confidence_level
        t_critical = stats.t.ppf(1 - alpha/2, df)
        
        # Intervalo de confiança
        margin_error = t_critical * std_err
        ci_lower = mean - margin_error
        ci_upper = mean + margin_error
        
        return (ci_lower, ci_upper)
    
    def calculate_required_sample_size(
        self,
        effect_size: float,
        power: float = 0.8,
        alpha: float = 0.05
    ) -> int:
        """Calcula tamanho de amostra necessário."""
        
        # Fórmula aproximada para teste t de duas amostras
        z_alpha = stats.norm.ppf(1 - alpha/2)
        z_beta = stats.norm.ppf(power)
        
        n = 2 * ((z_alpha + z_beta) / effect_size) ** 2
        
        return max(int(np.ceil(n)), self.min_sample_size)
    
    def _extract_returns(self, metrics: PerformanceMetrics) -> List[float]:
        """Extrai retornos dos trades para análise."""
        if metrics.trade_history:
            return [trade.pnl_pct for trade in metrics.trade_history]
        elif metrics.daily_returns:
            return metrics.daily_returns
        else:
            # Fallback: simular retornos baseados em métricas agregadas
            if metrics.total_trades > 0:
                avg_return = metrics.total_pnl_pct / metrics.total_trades
                # Simular distribuição de retornos
                return [avg_return + np.random.normal(0, 0.01) for _ in range(metrics.total_trades)]
        
        return []
    
    async def _perform_statistical_tests(
        self,
        sim_returns: List[float],
        live_returns: List[float],
        result: StatisticalResult
    ) -> None:
        """Realiza bateria de testes estatísticos."""
        
        # 1. Teste t para diferença de médias
        t_test = await self.analyze_metric_comparison(
            "Returns", sim_returns, live_returns, result.confidence_level
        )
        result.tests_performed.append(t_test)
        
        # 2. Teste F para diferença de variâncias
        f_statistic = np.var(sim_returns) / np.var(live_returns) if np.var(live_returns) > 0 else 1
        f_p_value = 2 * min(stats.f.cdf(f_statistic, len(sim_returns)-1, len(live_returns)-1),
                           1 - stats.f.cdf(f_statistic, len(sim_returns)-1, len(live_returns)-1))
        
        f_test = StatisticalTest(
            test_name="F-test para variâncias",
            test_statistic=f_statistic,
            p_value=f_p_value,
            is_significant=f_p_value < result.significance_threshold,
            confidence_level=result.confidence_level
        )
        result.tests_performed.append(f_test)
        
        # 3. Teste Kolmogorov-Smirnov para diferença de distribuições
        ks_statistic, ks_p_value = stats.ks_2samp(sim_returns, live_returns)
        
        ks_test = StatisticalTest(
            test_name="Kolmogorov-Smirnov",
            test_statistic=ks_statistic,
            p_value=ks_p_value,
            is_significant=ks_p_value < result.significance_threshold,
            confidence_level=result.confidence_level
        )
        result.tests_performed.append(ks_test)
        
        # Determinar significância geral
        significant_tests = [t for t in result.tests_performed if t.is_significant]
        result.is_significant = len(significant_tests) > 0
        result.overall_p_value = min([t.p_value for t in result.tests_performed])
    
    def _calculate_comparison_metrics(
        self,
        sim_returns: List[float],
        live_returns: List[float],
        result: StatisticalResult
    ) -> None:
        """Calcula métricas de comparação."""
        
        # Diferenças
        result.mean_difference = np.mean(sim_returns) - np.mean(live_returns)
        result.std_difference = np.std(sim_returns) - np.std(live_returns)
        
        # Effect size (Cohen's d)
        result.effect_size_cohens_d = self._calculate_cohens_d(sim_returns, live_returns)
        
        # Intervalo de confiança para a diferença de médias
        pooled_std = np.sqrt((np.var(sim_returns) + np.var(live_returns)) / 2)
        n1, n2 = len(sim_returns), len(live_returns)
        std_err_diff = pooled_std * np.sqrt(1/n1 + 1/n2)
        
        df = n1 + n2 - 2
        t_critical = stats.t.ppf(1 - (1-result.confidence_level)/2, df)
        margin_error = t_critical * std_err_diff
        
        result.confidence_interval = (
            result.mean_difference - margin_error,
            result.mean_difference + margin_error
        )
        
        result.actual_sample_size = min(n1, n2)
    
    def _perform_power_analysis(
        self,
        sim_returns: List[float],
        live_returns: List[float],
        result: StatisticalResult
    ) -> None:
        """Realiza análise de poder estatístico."""
        
        effect_size = abs(result.effect_size_cohens_d)
        n = min(len(sim_returns), len(live_returns))
        
        # Calcular poder estatístico atual
        if effect_size > 0 and n > 0:
            # Aproximação para poder do teste t
            delta = effect_size * np.sqrt(n / 2)
            t_critical = stats.t.ppf(1 - result.significance_threshold/2, 2*n-2)
            result.statistical_power = 1 - stats.t.cdf(t_critical - delta, 2*n-2) + stats.t.cdf(-t_critical - delta, 2*n-2)
        
        # Calcular tamanho de amostra necessário para poder de 0.8
        result.required_sample_size = self.calculate_required_sample_size(
            effect_size, power=0.8, alpha=result.significance_threshold
        )
    
    def _analyze_distributions(
        self,
        sim_returns: List[float],
        live_returns: List[float],
        result: StatisticalResult
    ) -> None:
        """Analisa distribuições dos dados."""
        
        # Estatísticas descritivas do simulador
        result.simulator_distribution = {
            "mean": float(np.mean(sim_returns)),
            "std": float(np.std(sim_returns)),
            "skewness": float(stats.skew(sim_returns)),
            "kurtosis": float(stats.kurtosis(sim_returns)),
            "min": float(np.min(sim_returns)),
            "max": float(np.max(sim_returns)),
            "median": float(np.median(sim_returns)),
            "q25": float(np.percentile(sim_returns, 25)),
            "q75": float(np.percentile(sim_returns, 75))
        }
        
        # Estatísticas descritivas do live trading
        result.live_distribution = {
            "mean": float(np.mean(live_returns)),
            "std": float(np.std(live_returns)),
            "skewness": float(stats.skew(live_returns)),
            "kurtosis": float(stats.kurtosis(live_returns)),
            "min": float(np.min(live_returns)),
            "max": float(np.max(live_returns)),
            "median": float(np.median(live_returns)),
            "q25": float(np.percentile(live_returns, 25)),
            "q75": float(np.percentile(live_returns, 75))
        }
    
    def _test_normality(self, data: List[float]) -> bool:
        """Testa normalidade dos dados."""
        if len(data) < 8:  # Shapiro-Wilk requer pelo menos 3 observações
            return False
        
        # Usar Shapiro-Wilk para amostras pequenas, Anderson-Darling para grandes
        if len(data) <= 5000:
            _, p_value = stats.shapiro(data)
        else:
            result = stats.anderson(data, dist='norm')
            # Usar nível de significância de 5%
            p_value = 0.05 if result.statistic > result.critical_values[2] else 0.1
        
        return p_value > 0.05
    
    def _calculate_cohens_d(self, group1: List[float], group2: List[float]) -> float:
        """Calcula Cohen's d (effect size)."""
        if len(group1) == 0 or len(group2) == 0:
            return 0.0
        
        mean1, mean2 = np.mean(group1), np.mean(group2)
        std1, std2 = np.std(group1, ddof=1), np.std(group2, ddof=1)
        
        # Pooled standard deviation
        n1, n2 = len(group1), len(group2)
        pooled_std = np.sqrt(((n1-1)*std1**2 + (n2-1)*std2**2) / (n1+n2-2))
        
        if pooled_std == 0:
            return 0.0
        
        return (mean1 - mean2) / pooled_std
    
    def _generate_interpretation(self, result: StatisticalResult) -> None:
        """Gera interpretação dos resultados."""
        
        interpretations = []
        recommendations = []
        
        # Interpretação da significância
        if result.is_significant:
            interpretations.append(f"Diferença estatisticamente significativa detectada (p < {result.significance_threshold})")
            if abs(result.mean_difference) > 0.01:  # 1%
                interpretations.append("A diferença de performance é substancial")
            else:
                interpretations.append("A diferença de performance é estatisticamente significativa mas pequena")
        else:
            interpretations.append(f"Não há evidência de diferença significativa (p >= {result.significance_threshold})")
            interpretations.append("Simulador e live trading apresentam performance similar")
        
        # Interpretação do effect size
        effect_size = abs(result.effect_size_cohens_d)
        if effect_size < self.effect_size_thresholds["small"]:
            interpretations.append("Tamanho do efeito: negligível")
        elif effect_size < self.effect_size_thresholds["medium"]:
            interpretations.append("Tamanho do efeito: pequeno")
        elif effect_size < self.effect_size_thresholds["large"]:
            interpretations.append("Tamanho do efeito: médio")
        else:
            interpretations.append("Tamanho do efeito: grande")
        
        # Interpretação do poder estatístico
        if result.statistical_power < 0.8:
            recommendations.append(f"Poder estatístico baixo ({result.statistical_power:.2f}). Considere aumentar tamanho da amostra para {result.required_sample_size}")
        
        # Recomendações baseadas na amostra
        if result.actual_sample_size < result.required_sample_size:
            recommendations.append(f"Tamanho de amostra insuficiente. Recomendado: {result.required_sample_size}, atual: {result.actual_sample_size}")
        
        result.interpretation = " | ".join(interpretations)
        result.recommendations.extend(recommendations)
