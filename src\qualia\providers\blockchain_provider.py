from __future__ import annotations

"""Blockchain metrics provider.

Fetches on-chain statistics from public APIs and publishes normalized
metrics on the event bus.
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any
import time
import aiohttp

from ..memory.event_bus import SimpleEventBus, ONCHAIN_SIGNAL_STREAM
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class BlockchainMetric:
    """Normalized blockchain metric."""

    metric: str
    value: float
    timestamp: float
    source: str


class BlockchainDataProvider:
    """Coleta métricas on-chain de diferentes APIs."""

    def __init__(self, event_bus: Optional[SimpleEventBus] = None) -> None:
        self.event_bus = event_bus
        self.session: aiohttp.ClientSession | None = None

    async def __aenter__(self) -> "BlockchainDataProvider":
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=10),
            headers={"User-Agent": "QUALIA-BlockchainProvider/1.0"},
        )
        return self

    async def __aexit__(self, exc_type, exc, tb) -> None:
        if self.session:
            await self.session.close()
            self.session = None

    # ------------------------------------------------------------------
    async def _fetch_blockchain_info(self) -> Dict[str, Any]:
        if not self.session:
            return {}
        url = "https://api.blockchain.info/stats"
        try:
            async with self.session.get(url) as resp:
                if resp.status == 200:
                    return await resp.json()
        except Exception as exc:  # pragma: no cover - network errors not in tests
            logger.debug("blockchain.info error: %s", exc)
        return {}

    async def collect_metrics(self) -> List[BlockchainMetric]:
        """Collect metrics and publish them."""

        data = await self._fetch_blockchain_info()
        metrics: List[BlockchainMetric] = []
        ts = time.time()
        for key, value in data.items():
            if isinstance(value, (int, float)):
                metrics.append(
                    BlockchainMetric(
                        metric=key,
                        value=float(value),
                        timestamp=ts,
                        source="blockchain.info",
                    )
                )

        if self.event_bus:
            for metric in metrics:
                self.event_bus.publish(ONCHAIN_SIGNAL_STREAM, metric)

        logger.debug("BlockchainDataProvider coletou %d métricas", len(metrics))
        return metrics
