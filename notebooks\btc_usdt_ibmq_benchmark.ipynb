{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Benchmark BTC/USDT em IBM Q (5 Qubits)\n", "Este notebook executa um circuito simples de 5 qubits no backend IBM Q `ibm_iken` para medir latência, diversity, entropy e um PnL simulado. Os resultados são salvos em JSON."]}, {"cell_type": "code", "metadata": {}, "execution_count": null, "outputs": [], "source": ["from qiskit import IBMQ, QuantumCircuit, transpile\n", "from qiskit.quantum_info import entropy\n", "import numpy as np, json, time"]}, {"cell_type": "code", "metadata": {}, "execution_count": null, "outputs": [], "source": ["provider = IBMQ.load_account()\n", "backend = provider.get_backend('ibm_iken')  # dispositivo de 127 qubits\n", "qc = QuantumCircuit(5, 5)\n", "for i in range(5):\n", "    qc.h(i)\n", "for i in range(4):\n", "    qc.cx(i, i + 1)\n", "qc.measure(range(5), range(5))\n", "compiled = transpile(qc, backend)"]}, {"cell_type": "code", "metadata": {}, "execution_count": null, "outputs": [], "source": ["start = time.time()\n", "job = backend.run(compiled, shots=1024)\n", "result = job.result()\n", "end = time.time()\n", "latency_ms = (end - start) * 1000\n", "counts = result.get_counts()\n", "total_shots = sum(counts.values())\n", "diversity = len(counts) / (2 ** 5)\n", "probs = np.array(list(counts.values())) / total_shots\n", "entropy_val = float(-np.sum(probs * np.log2(probs)))\n", "values = [((int(state, 2) / 31) - 0.5) for state in counts for _ in range(counts[state])]\n", "pnl_sim = float(np.mean(values))\n", "metrics = {\n", "    'latency_ms': latency_ms,\n", "    'diversity': diversity,\n", "    'entropy': entropy_val,\n", "    'pnl_sim': pnl_sim,\n", "    'credit_cost': getattr(job, 'cost', None)\n", "}\n", "with open('benchmarks/btc_usdt_ibmq_metrics.json', 'w') as f:\n", "    json.dump(metrics, f, indent=2)\n", "metrics"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}