#!/usr/bin/env python3
"""Simple test for OTOC."""

import sys
sys.path.append('src')

import numpy as np

try:
    from qualia.utils.otoc_calculator import calculate_otoc
    
    # Test 1: Gaussian noise (should have high OTOC)
    np.random.seed(42)
    gaussian_data = np.random.normal(0, 0.02, 200)
    otoc_chaos = calculate_otoc(gaussian_data, idx=100, window=50, method="correlation")
    print(f"✅ OTOC Gaussian noise: {otoc_chaos:.4f}")
    
    # Test 2: Trending data (should have low OTOC)
    np.random.seed(123)
    trend = np.linspace(100, 200, 200)
    noise = np.random.normal(0, 0.5, 200)
    trending_data = trend + noise
    otoc_trend = calculate_otoc(trending_data, idx=100, window=50, method="correlation")
    print(f"✅ OTOC trending data: {otoc_trend:.4f}")
    
    # Validation
    if not np.isnan(otoc_chaos) and not np.isnan(otoc_trend):
        print("✅ Both calculations successful!")
        if otoc_chaos > 0.3:
            print("✅ Gaussian noise has high OTOC (chaotic)")
        if otoc_trend < 0.5:
            print("✅ Trending data has low OTOC (ordered)")
        print("🎯 OTOC calculator is working correctly!")
    else:
        print("❌ Some calculations returned NaN")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
