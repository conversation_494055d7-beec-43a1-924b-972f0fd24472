"""
QUALIA A/B Testing Framework - D-07.6 End-to-End Testing

Framework completo de testes end-to-end para validar o ciclo completo
de A/B testing integrado com todos os componentes do QUALIA.

Testa:
1. Configuração automática de testes
2. Integração com live feed
3. Coleta de dados de performance
4. Análise estatística
5. Geração de relatórios
6. Integração com otimização de parâmetros
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import json
import tempfile
from pathlib import Path

from .system_integration import QualiaABTestingIntegration, SystemIntegrationConfig
from .ab_test_framework import ABTestConfig, TestType, TestStatus
from .test_config_manager import TestConfiguration, ExecutionConfig

logger = logging.getLogger(__name__)


@dataclass
class EndToEndTestResult:
    """Resultado de um teste end-to-end."""
    test_name: str
    success: bool
    duration_seconds: float
    details: str
    components_tested: List[str]
    metrics: Dict[str, Any]
    errors: List[str]
    timestamp: datetime


class EndToEndTestFramework:
    """
    Framework de testes end-to-end para o sistema A/B Testing integrado.
    
    Executa testes completos que validam:
    - Integração entre todos os componentes
    - Fluxo completo de dados
    - Geração de relatórios
    - Alertas e monitoramento
    - Performance e estabilidade
    """
    
    def __init__(self):
        self.integration: Optional[QualiaABTestingIntegration] = None
        self.test_results: List[EndToEndTestResult] = []
        self.temp_dir = tempfile.mkdtemp(prefix="qualia_e2e_")
        
        logger.info(f"🧪 EndToEndTestFramework inicializado (temp: {self.temp_dir})")
    
    async def run_complete_test_suite(self) -> Dict[str, Any]:
        """Executa suite completa de testes end-to-end."""
        logger.info("🚀 Iniciando suite completa de testes end-to-end...")
        
        start_time = datetime.now()
        
        # Lista de testes a executar
        tests = [
            ("System Initialization", self._test_system_initialization),
            ("Live Feed Integration", self._test_live_feed_integration),
            ("A/B Test Configuration", self._test_ab_test_configuration),
            ("Complete A/B Test Cycle", self._test_complete_ab_test_cycle),
            ("Performance Comparison", self._test_performance_comparison),
            ("Statistical Analysis", self._test_statistical_analysis),
            ("Report Generation", self._test_report_generation),
            ("Monitoring & Alerts", self._test_monitoring_alerts),
            ("Parameter Optimization", self._test_parameter_optimization),
            ("Error Handling", self._test_error_handling),
            ("System Cleanup", self._test_system_cleanup)
        ]
        
        passed_tests = 0
        failed_tests = 0
        
        for test_name, test_func in tests:
            try:
                logger.info(f"🧪 Executando: {test_name}")
                result = await test_func()
                
                if result.success:
                    logger.info(f"✅ {test_name}: PASSOU - {result.details}")
                    passed_tests += 1
                else:
                    logger.error(f"❌ {test_name}: FALHOU - {result.details}")
                    failed_tests += 1
                
                self.test_results.append(result)
                
            except Exception as e:
                logger.error(f"💥 {test_name}: ERRO CRÍTICO - {e}")
                error_result = EndToEndTestResult(
                    test_name=test_name,
                    success=False,
                    duration_seconds=0.0,
                    details=f"Erro crítico: {e}",
                    components_tested=[],
                    metrics={},
                    errors=[str(e)],
                    timestamp=datetime.now()
                )
                self.test_results.append(error_result)
                failed_tests += 1
        
        total_duration = (datetime.now() - start_time).total_seconds()
        
        # Gerar relatório final
        summary = {
            "total_tests": len(tests),
            "passed": passed_tests,
            "failed": failed_tests,
            "success_rate": passed_tests / len(tests) * 100,
            "total_duration_seconds": total_duration,
            "timestamp": datetime.now().isoformat(),
            "test_results": [
                {
                    "name": r.test_name,
                    "success": r.success,
                    "duration": r.duration_seconds,
                    "details": r.details,
                    "components": r.components_tested,
                    "metrics": r.metrics,
                    "errors": r.errors
                }
                for r in self.test_results
            ]
        }
        
        logger.info(f"📊 Suite completa finalizada: {passed_tests}/{len(tests)} testes passaram ({summary['success_rate']:.1f}%)")
        
        return summary
    
    async def _test_system_initialization(self) -> EndToEndTestResult:
        """Testa inicialização completa do sistema."""
        start_time = datetime.now()
        components_tested = []
        metrics = {}
        errors = []
        
        try:
            # Configurar integração para teste
            config = SystemIntegrationConfig(
                ab_test_duration_hours=1,  # Teste curto
                ab_test_symbols=["BTC/USDT"],
                monitoring_interval_seconds=10,
                report_storage_path=self.temp_dir
            )
            
            self.integration = QualiaABTestingIntegration(config)
            components_tested.append("QualiaABTestingIntegration")
            
            # Inicializar sistema
            init_success = await self.integration.initialize()
            components_tested.extend(["LiveFeedIntegration", "BayesianOptimizer", "ParameterTuner", "ABTestFramework"])
            
            if not init_success:
                errors.append("Falha na inicialização do sistema")
                return EndToEndTestResult(
                    test_name="System Initialization",
                    success=False,
                    duration_seconds=(datetime.now() - start_time).total_seconds(),
                    details="Falha na inicialização",
                    components_tested=components_tested,
                    metrics=metrics,
                    errors=errors,
                    timestamp=datetime.now()
                )
            
            # Verificar status
            status = self.integration.get_integration_status()
            metrics.update(status)
            
            success = status["is_initialized"]
            details = f"Sistema inicializado: {status['components']}"
            
        except Exception as e:
            errors.append(str(e))
            success = False
            details = f"Erro na inicialização: {e}"
        
        return EndToEndTestResult(
            test_name="System Initialization",
            success=success,
            duration_seconds=(datetime.now() - start_time).total_seconds(),
            details=details,
            components_tested=components_tested,
            metrics=metrics,
            errors=errors,
            timestamp=datetime.now()
        )
    
    async def _test_live_feed_integration(self) -> EndToEndTestResult:
        """Testa integração com live feed."""
        start_time = datetime.now()
        components_tested = ["LiveFeedIntegration"]
        metrics = {}
        errors = []
        
        try:
            if not self.integration or not self.integration.live_feed_integration:
                errors.append("Live feed integration não disponível")
                return EndToEndTestResult(
                    test_name="Live Feed Integration",
                    success=False,
                    duration_seconds=(datetime.now() - start_time).total_seconds(),
                    details="Live feed não disponível",
                    components_tested=components_tested,
                    metrics=metrics,
                    errors=errors,
                    timestamp=datetime.now()
                )
            
            # Iniciar live feed
            start_success = await self.integration.start_integration()
            
            if start_success:
                # Aguardar alguns segundos para coleta de dados
                await asyncio.sleep(5)
                
                # Verificar status
                feed_status = self.integration.live_feed_integration.get_integration_status()
                metrics.update(feed_status)
                
                success = feed_status.get("live_feed_active", False)
                details = f"Live feed ativo: {success}, Stats: {feed_status.get('stats', {})}"
            else:
                success = False
                details = "Falha ao iniciar live feed"
                errors.append("Falha ao iniciar live feed")
        
        except Exception as e:
            errors.append(str(e))
            success = False
            details = f"Erro no live feed: {e}"
        
        return EndToEndTestResult(
            test_name="Live Feed Integration",
            success=success,
            duration_seconds=(datetime.now() - start_time).total_seconds(),
            details=details,
            components_tested=components_tested,
            metrics=metrics,
            errors=errors,
            timestamp=datetime.now()
        )
    
    async def _test_ab_test_configuration(self) -> EndToEndTestResult:
        """Testa configuração de testes A/B."""
        start_time = datetime.now()
        components_tested = ["TestConfigManager"]
        metrics = {}
        errors = []
        
        try:
            if not self.integration or not self.integration.test_config_manager:
                errors.append("Test config manager não disponível")
                return EndToEndTestResult(
                    test_name="A/B Test Configuration",
                    success=False,
                    duration_seconds=(datetime.now() - start_time).total_seconds(),
                    details="Config manager não disponível",
                    components_tested=components_tested,
                    metrics=metrics,
                    errors=errors,
                    timestamp=datetime.now()
                )
            
            # Criar configuração de teste
            test_config = {
                "test_name": "E2E_Test_Configuration",
                "symbols": ["BTC/USDT"],
                "duration_hours": 1,
                "strategy_template": "momentum_rsi",
                "execution_config": {
                    "paper_trading": True,
                    "max_slippage_pct": 0.1,
                    "order_timeout_seconds": 30
                }
            }
            
            # Criar e salvar configuração
            config = await self.integration.test_config_manager.create_test_configuration(test_config)
            await self.integration.test_config_manager.save_configuration(config)
            
            # Validar configuração
            validation_result = await self.integration.test_config_manager.validate_configuration(config)
            
            metrics["config_created"] = True
            metrics["config_valid"] = validation_result.is_valid
            metrics["validation_errors"] = len(validation_result.errors)
            
            success = validation_result.is_valid
            details = f"Config válida: {success}, Erros: {len(validation_result.errors)}"
            
            if validation_result.errors:
                errors.extend(validation_result.errors)
        
        except Exception as e:
            errors.append(str(e))
            success = False
            details = f"Erro na configuração: {e}"
        
        return EndToEndTestResult(
            test_name="A/B Test Configuration",
            success=success,
            duration_seconds=(datetime.now() - start_time).total_seconds(),
            details=details,
            components_tested=components_tested,
            metrics=metrics,
            errors=errors,
            timestamp=datetime.now()
        )

    async def _test_complete_ab_test_cycle(self) -> EndToEndTestResult:
        """Testa ciclo completo de A/B testing."""
        start_time = datetime.now()
        components_tested = ["ABTestFramework", "PerformanceComparator", "DataQualityValidator"]
        metrics = {}
        errors = []

        try:
            if not self.integration or not self.integration.ab_test_framework:
                errors.append("A/B test framework não disponível")
                return EndToEndTestResult(
                    test_name="Complete A/B Test Cycle",
                    success=False,
                    duration_seconds=(datetime.now() - start_time).total_seconds(),
                    details="Framework não disponível",
                    components_tested=components_tested,
                    metrics=metrics,
                    errors=errors,
                    timestamp=datetime.now()
                )

            # Executar teste A/B (versão acelerada para E2E)
            test_config = {
                "test_name": "E2E_Complete_Cycle",
                "symbols": ["BTC/USDT"],
                "duration_hours": 0.1,  # 6 minutos para teste rápido
                "strategy_template": "momentum_rsi"
            }

            test_result = await self.integration.run_ab_test(test_config)

            if test_result:
                metrics["test_id"] = test_result.test_id
                metrics["test_status"] = test_result.status.value
                metrics["duration_seconds"] = test_result.duration_seconds

                success = test_result.status.value in ["completed", "stopped"]
                details = f"Teste executado: {test_result.test_id}, Status: {test_result.status.value}"
            else:
                success = False
                details = "Falha na execução do teste A/B"
                errors.append("Teste A/B retornou None")

        except Exception as e:
            errors.append(str(e))
            success = False
            details = f"Erro no ciclo A/B: {e}"

        return EndToEndTestResult(
            test_name="Complete A/B Test Cycle",
            success=success,
            duration_seconds=(datetime.now() - start_time).total_seconds(),
            details=details,
            components_tested=components_tested,
            metrics=metrics,
            errors=errors,
            timestamp=datetime.now()
        )

    async def _test_performance_comparison(self) -> EndToEndTestResult:
        """Testa sistema de comparação de performance."""
        start_time = datetime.now()
        components_tested = ["PerformanceComparator"]
        metrics = {}
        errors = []

        try:
            if not self.integration or not self.integration.ab_test_framework:
                errors.append("Performance comparator não disponível")
                return EndToEndTestResult(
                    test_name="Performance Comparison",
                    success=False,
                    duration_seconds=(datetime.now() - start_time).total_seconds(),
                    details="Comparator não disponível",
                    components_tested=components_tested,
                    metrics=metrics,
                    errors=errors,
                    timestamp=datetime.now()
                )

            comparator = self.integration.ab_test_framework.performance_comparator

            # Simular algumas métricas para teste
            await asyncio.sleep(2)  # Aguardar coleta de dados

            # Tentar calcular comparação
            comparison = comparator.calculate_comparison()

            if comparison:
                metrics["simulator_pnl"] = comparison.simulator_metrics.total_pnl
                metrics["live_pnl"] = comparison.live_metrics.total_pnl
                metrics["performance_diff_pct"] = comparison.performance_difference_pct

                success = True
                details = f"Comparação calculada: Diff {comparison.performance_difference_pct:.2f}%"
            else:
                success = False
                details = "Não foi possível calcular comparação"
                errors.append("Comparação retornou None")

        except Exception as e:
            errors.append(str(e))
            success = False
            details = f"Erro na comparação: {e}"

        return EndToEndTestResult(
            test_name="Performance Comparison",
            success=success,
            duration_seconds=(datetime.now() - start_time).total_seconds(),
            details=details,
            components_tested=components_tested,
            metrics=metrics,
            errors=errors,
            timestamp=datetime.now()
        )

    async def _test_statistical_analysis(self) -> EndToEndTestResult:
        """Testa análise estatística."""
        start_time = datetime.now()
        components_tested = ["StatisticalAnalyzer"]
        metrics = {}
        errors = []

        try:
            if not self.integration or not self.integration.ab_test_framework:
                errors.append("Statistical analyzer não disponível")
                return EndToEndTestResult(
                    test_name="Statistical Analysis",
                    success=False,
                    duration_seconds=(datetime.now() - start_time).total_seconds(),
                    details="Analyzer não disponível",
                    components_tested=components_tested,
                    metrics=metrics,
                    errors=errors,
                    timestamp=datetime.now()
                )

            analyzer = self.integration.ab_test_framework.statistical_analyzer

            # Criar métricas mock para análise
            from .performance_comparator import PerformanceMetrics

            simulator_metrics = PerformanceMetrics(
                total_pnl=100.0,
                total_return_pct=5.0,
                sharpe_ratio=1.5,
                max_drawdown_pct=2.0,
                win_rate=0.6,
                total_trades=10,
                avg_trade_duration_hours=2.0
            )

            live_metrics = PerformanceMetrics(
                total_pnl=95.0,
                total_return_pct=4.8,
                sharpe_ratio=1.4,
                max_drawdown_pct=2.2,
                win_rate=0.58,
                total_trades=9,
                avg_trade_duration_hours=2.1
            )

            # Executar análise
            result = await analyzer.analyze_performance_difference(
                simulator_metrics, live_metrics, confidence_level=0.95
            )

            if result:
                metrics["p_value"] = result.overall_p_value
                metrics["is_significant"] = result.is_significant
                metrics["effect_size"] = result.effect_size_cohens_d
                metrics["tests_performed"] = len(result.tests_performed)

                success = True
                details = f"Análise executada: p={result.overall_p_value:.4f}, Significativo: {result.is_significant}"
            else:
                success = False
                details = "Análise estatística falhou"
                errors.append("Análise retornou None")

        except Exception as e:
            errors.append(str(e))
            success = False
            details = f"Erro na análise: {e}"

        return EndToEndTestResult(
            test_name="Statistical Analysis",
            success=success,
            duration_seconds=(datetime.now() - start_time).total_seconds(),
            details=details,
            components_tested=components_tested,
            metrics=metrics,
            errors=errors,
            timestamp=datetime.now()
        )

    async def _test_report_generation(self) -> EndToEndTestResult:
        """Testa geração de relatórios."""
        start_time = datetime.now()
        components_tested = ["ReportingEngine"]
        metrics = {}
        errors = []

        try:
            if not self.integration or not self.integration.reporting_engine:
                errors.append("Reporting engine não disponível")
                return EndToEndTestResult(
                    test_name="Report Generation",
                    success=False,
                    duration_seconds=(datetime.now() - start_time).total_seconds(),
                    details="Engine não disponível",
                    components_tested=components_tested,
                    metrics=metrics,
                    errors=errors,
                    timestamp=datetime.now()
                )

            # Criar dados mock para relatório
            from .ab_test_framework import ABTestResult, TestType, TestStatus

            test_result = ABTestResult(
                test_id="e2e_report_test",
                test_name="E2E Report Generation Test",
                test_type=TestType.SIMULATOR_VS_LIVE,
                status=TestStatus.COMPLETED,
                start_time=datetime.now() - timedelta(hours=1),
                end_time=datetime.now(),
                duration_seconds=3600.0
            )

            # Gerar relatório
            report = await self.integration.reporting_engine.generate_executive_summary(
                test_result, [], [], []
            )

            if report and len(report) > 0:
                # Tentar exportar HTML
                html_file = Path(self.temp_dir) / "e2e_test_report.html"
                await self.integration.reporting_engine.export_html_report(report, str(html_file))

                # Verificar se arquivo foi criado
                if html_file.exists():
                    file_size = html_file.stat().st_size
                    metrics["html_generated"] = True
                    metrics["html_file_size"] = file_size

                    success = file_size > 1000  # Pelo menos 1KB
                    details = f"Relatório HTML gerado: {file_size} bytes"
                else:
                    success = False
                    details = "Arquivo HTML não foi criado"
                    errors.append("HTML file not created")
            else:
                success = False
                details = "Relatório não foi gerado"
                errors.append("Report generation returned empty")

        except Exception as e:
            errors.append(str(e))
            success = False
            details = f"Erro na geração: {e}"

        return EndToEndTestResult(
            test_name="Report Generation",
            success=success,
            duration_seconds=(datetime.now() - start_time).total_seconds(),
            details=details,
            components_tested=components_tested,
            metrics=metrics,
            errors=errors,
            timestamp=datetime.now()
        )

    async def _test_monitoring_alerts(self) -> EndToEndTestResult:
        """Testa sistema de monitoramento e alertas."""
        start_time = datetime.now()
        components_tested = ["MonitoringSystem", "EventBus"]
        metrics = {}
        errors = []

        try:
            if not self.integration:
                errors.append("Integration não disponível")
                return EndToEndTestResult(
                    test_name="Monitoring & Alerts",
                    success=False,
                    duration_seconds=(datetime.now() - start_time).total_seconds(),
                    details="Integration não disponível",
                    components_tested=components_tested,
                    metrics=metrics,
                    errors=errors,
                    timestamp=datetime.now()
                )

            alerts_received = []

            # Registrar callback para capturar alertas
            async def alert_callback(alert_type: str, alert_data: Dict[str, Any]):
                alerts_received.append((alert_type, alert_data))

            self.integration.add_alert_callback(alert_callback)

            # Simular condição de alerta
            alert_data = {
                "type": "test_alert",
                "message": "E2E test alert",
                "timestamp": datetime.now().isoformat()
            }

            await self.integration._trigger_alert("test_alert", alert_data)

            # Aguardar processamento
            await asyncio.sleep(1)

            metrics["alerts_triggered"] = 1
            metrics["alerts_received"] = len(alerts_received)

            success = len(alerts_received) > 0
            details = f"Alertas recebidos: {len(alerts_received)}"

            if not success:
                errors.append("Nenhum alerta foi recebido")

        except Exception as e:
            errors.append(str(e))
            success = False
            details = f"Erro no monitoramento: {e}"

        return EndToEndTestResult(
            test_name="Monitoring & Alerts",
            success=success,
            duration_seconds=(datetime.now() - start_time).total_seconds(),
            details=details,
            components_tested=components_tested,
            metrics=metrics,
            errors=errors,
            timestamp=datetime.now()
        )

    async def _test_parameter_optimization(self) -> EndToEndTestResult:
        """Testa integração com otimização de parâmetros."""
        start_time = datetime.now()
        components_tested = ["BayesianOptimizer", "ParameterTuner"]
        metrics = {}
        errors = []

        try:
            if not self.integration or not self.integration.bayesian_optimizer:
                # Este teste pode falhar se componentes opcionais não estiverem disponíveis
                success = True  # Consideramos sucesso se não está habilitado
                details = "Otimização não habilitada (OK para teste)"
            else:
                # Verificar se otimizador está funcionando
                optimizer = self.integration.bayesian_optimizer

                # Verificar se study está inicializado
                if hasattr(optimizer, 'studies') and optimizer.studies:
                    metrics["studies_initialized"] = len(optimizer.studies)
                    success = True
                    details = f"Otimizador ativo: {len(optimizer.studies)} studies"
                else:
                    success = False
                    details = "Studies não inicializados"
                    errors.append("Studies not initialized")

        except Exception as e:
            errors.append(str(e))
            success = False
            details = f"Erro na otimização: {e}"

        return EndToEndTestResult(
            test_name="Parameter Optimization",
            success=success,
            duration_seconds=(datetime.now() - start_time).total_seconds(),
            details=details,
            components_tested=components_tested,
            metrics=metrics,
            errors=errors,
            timestamp=datetime.now()
        )

    async def _test_error_handling(self) -> EndToEndTestResult:
        """Testa tratamento de erros."""
        start_time = datetime.now()
        components_tested = ["ErrorHandling"]
        metrics = {}
        errors = []

        try:
            if not self.integration:
                errors.append("Integration não disponível")
                return EndToEndTestResult(
                    test_name="Error Handling",
                    success=False,
                    duration_seconds=(datetime.now() - start_time).total_seconds(),
                    details="Integration não disponível",
                    components_tested=components_tested,
                    metrics=metrics,
                    errors=errors,
                    timestamp=datetime.now()
                )

            # Testar cenários de erro controlados
            error_scenarios_passed = 0
            total_scenarios = 3

            # Cenário 1: Configuração inválida
            try:
                invalid_config = {"invalid": "config"}
                await self.integration.run_ab_test(invalid_config)
                # Se chegou aqui, deveria ter dado erro
                errors.append("Configuração inválida não foi rejeitada")
            except Exception:
                # Erro esperado
                error_scenarios_passed += 1

            # Cenário 2: Status de sistema não inicializado
            try:
                temp_integration = QualiaABTestingIntegration()
                status = temp_integration.get_integration_status()
                if not status["is_initialized"]:
                    error_scenarios_passed += 1
                else:
                    errors.append("Status deveria indicar não inicializado")
            except Exception as e:
                errors.append(f"Erro inesperado no status: {e}")

            # Cenário 3: Graceful degradation
            try:
                # Simular componente indisponível
                original_live_feed = self.integration.live_feed_integration
                self.integration.live_feed_integration = None

                # Sistema deve continuar funcionando
                status = self.integration.get_integration_status()
                if not status["components"]["live_feed"]:
                    error_scenarios_passed += 1
                else:
                    errors.append("Status deveria indicar live feed indisponível")

                # Restaurar
                self.integration.live_feed_integration = original_live_feed

            except Exception as e:
                errors.append(f"Erro no graceful degradation: {e}")

            metrics["error_scenarios_passed"] = error_scenarios_passed
            metrics["total_scenarios"] = total_scenarios

            success = error_scenarios_passed >= 2  # Pelo menos 2 de 3
            details = f"Cenários de erro: {error_scenarios_passed}/{total_scenarios}"

        except Exception as e:
            errors.append(str(e))
            success = False
            details = f"Erro no teste de erros: {e}"

        return EndToEndTestResult(
            test_name="Error Handling",
            success=success,
            duration_seconds=(datetime.now() - start_time).total_seconds(),
            details=details,
            components_tested=components_tested,
            metrics=metrics,
            errors=errors,
            timestamp=datetime.now()
        )

    async def _test_system_cleanup(self) -> EndToEndTestResult:
        """Testa limpeza do sistema."""
        start_time = datetime.now()
        components_tested = ["SystemCleanup"]
        metrics = {}
        errors = []

        try:
            if not self.integration:
                success = True  # Nada para limpar
                details = "Nenhum sistema para limpar"
            else:
                # Parar integração
                await self.integration.stop_integration()

                # Verificar status
                status = self.integration.get_integration_status()

                metrics["final_status"] = status

                success = not status["is_running"]
                details = f"Sistema parado: {not status['is_running']}"

                if status["is_running"]:
                    errors.append("Sistema ainda está rodando após stop")

        except Exception as e:
            errors.append(str(e))
            success = False
            details = f"Erro na limpeza: {e}"

        return EndToEndTestResult(
            test_name="System Cleanup",
            success=success,
            duration_seconds=(datetime.now() - start_time).total_seconds(),
            details=details,
            components_tested=components_tested,
            metrics=metrics,
            errors=errors,
            timestamp=datetime.now()
        )
