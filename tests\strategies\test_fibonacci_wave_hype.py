"""
Testes unitários para a estratégia Fibonacci Wave Hype (FWH).

Testa detecção de ondas, cálculo de níveis de Fibonacci,
integração com componentes quânticos e sentiment holográfico.
"""

import pytest
import numpy as np
import pandas as pd
from unittest.mock import Mock, MagicMock, patch
from datetime import datetime, timedelta

from qualia.strategies.fibonacci_wave_hype import (
    FibonacciWaveHypeStrategy,
    calculate_fibonacci_levels,
    detect_wave_patterns,
    integrate_holographic_sentiment,
)
from qualia.strategies.fibonacci_wave_hype.sentiment_integration import (
    HolographicSentimentAnalyzer,
    _apply_quantum_transformation,
    _calculate_holographic_coherence,
    _calculate_temporal_decay,
)
from qualia.strategies.strategy_interface import TradingContext
from qualia.custom_types import CollectiveMindState


@pytest.fixture
def sample_market_data():
    """Dados de mercado para testes."""
    dates = pd.date_range("2024-01-01", periods=100, freq="1H")

    # Simula movimento de preços com padrão Fibonacci
    base_price = 50000
    prices = []
    for i in range(100):
        # Adiciona volatilidade e tendência
        trend = i * 10
        volatility = np.sin(i * 0.1) * 500
        noise = np.random.normal(0, 100)
        price = base_price + trend + volatility + noise
        prices.append(max(price, 1000))  # Evita preços negativos

    volumes = np.random.uniform(1000, 10000, 100)

    return pd.DataFrame({
        "open": prices,
        "high": [p * 1.02 for p in prices],
        "low": [p * 0.98 for p in prices],
        "close": prices,
        "volume": volumes,
    }, index=dates)


@pytest.fixture
def mock_holographic_engine():
    """Mock do HolographicFarsightEngine."""
    engine = Mock()

    # Mock do CollectiveMindState
    collective_state = CollectiveMindState(
        timestamp=datetime.now().timestamp(),
        dominant_narrative="AI_QUANTUM_SURGE",
        persona_impact={
            "RetailCluster": {
                "sentiment": 0.7,
                "confidence_boost": 0.8,
                "action_bias": "BUY"
            },
            "InstitutionalCluster": {
                "sentiment": 0.4,
                "confidence_boost": 0.6,
                "action_bias": "HOLD"
            },
            "MomentumQuant": {
                "sentiment": 0.8,
                "confidence_boost": 0.9,
                "action_bias": "BUY"
            }
        }
    )

    engine.generate_collective_mind_state.return_value = collective_state
    engine.run_holographic_analysis.return_value = [
        {
            "topic": "quantum_momentum",
            "confidence": 0.85,
            "holographic_analysis": {
                "pattern_type": "fibonacci_wave",
                "confidence": 0.8,
                "position": "LONG"
            }
        }
    ]

    return engine


@pytest.fixture
def fwh_strategy():
    """Instância da estratégia FWH para testes."""
    parameters = {
        "fib_lookback": 20,
        "hype_threshold": 0.618,
        "wave_min_strength": 0.3,
    }

    strategy = FibonacciWaveHypeStrategy(
        symbol="BTC/USDT",
        timeframe="1h",
        parameters=parameters
    )

    return strategy


class TestFibonacciLevels:
    """Testes para cálculo de níveis de Fibonacci."""

    def test_calculate_fibonacci_levels_basic(self, sample_market_data):
        """Testa cálculo básico de níveis de Fibonacci."""
        highs = sample_market_data["high"].tail(20)
        lows = sample_market_data["low"].tail(20)

        levels = calculate_fibonacci_levels(highs, lows)

        # Verifica estrutura
        expected_keys = ["0.0", "0.236", "0.382", "0.618", "1.0", "support", "resistance"]
        assert all(key in levels for key in expected_keys)

        # Verifica ordem dos níveis
        assert levels["0.0"] >= levels["0.236"]
        assert levels["0.236"] >= levels["0.382"]
        assert levels["0.382"] >= levels["0.618"]
        assert levels["0.618"] >= levels["1.0"]

        # Verifica support/resistance
        assert levels["support"] == levels["1.0"]
        assert levels["resistance"] == levels["0.0"]

    def test_fibonacci_levels_edge_cases(self):
        """Testa casos extremos para níveis de Fibonacci."""
        # Preços iguais
        equal_prices = pd.Series([100] * 10)
        levels = calculate_fibonacci_levels(equal_prices, equal_prices)

        assert levels["0.0"] == levels["1.0"] == 100
        assert all(level == 100 for level in levels.values())


class TestWavePatterns:
    """Testes para detecção de padrões de ondas."""

    def test_detect_wave_patterns_basic(self, sample_market_data):
        """Testa detecção básica de padrões de ondas."""
        fib_levels = calculate_fibonacci_levels(
            sample_market_data["high"].tail(20),
            sample_market_data["low"].tail(20)
        )

        patterns = detect_wave_patterns(sample_market_data, fib_levels)

        # Verifica estrutura
        assert "fib_level" in patterns
        assert "direction" in patterns
        assert "trend_strength" in patterns

        # Verifica valores
        assert patterns["direction"] in [-1, 1]
        assert patterns["trend_strength"] >= 0
        assert patterns["fib_level"] in ["0.236", "0.382", "0.618", "none"]


class TestHolographicSentiment:
    """Testes para integração de sentiment holográfico."""

    def test_integrate_holographic_sentiment_basic(self, mock_holographic_engine):
        """Testa integração básica de sentiment holográfico."""
        boost_factor = integrate_holographic_sentiment(
            mock_holographic_engine,
            "BTC/USDT",
            use_cache=False
        )

        # Verifica range do fator de boost
        assert 0.3 <= boost_factor <= 2.5
        assert isinstance(boost_factor, float)

    def test_sentiment_cache_functionality(self, mock_holographic_engine):
        """Testa funcionalidade de cache do sentiment."""
        analyzer = HolographicSentimentAnalyzer()

        # Primeiro call - sem cache
        boost1 = integrate_holographic_sentiment(
            mock_holographic_engine,
            "BTC/USDT",
            use_cache=True
        )

        # Segundo call - deve usar cache
        with patch.object(mock_holographic_engine, 'generate_collective_mind_state') as mock_gen:
            boost2 = integrate_holographic_sentiment(
                mock_holographic_engine,
                "BTC/USDT",
                use_cache=True
            )

            # Não deve chamar o engine novamente
            mock_gen.assert_not_called()
            assert boost1 == boost2

    def test_quantum_transformation(self):
        """Testa transformação quântica do sentiment."""
        result = _apply_quantum_transformation(0.5, 0.8, 0.9)

        assert isinstance(result, float)
        # Deve ser diferente do valor original devido à transformação
        assert result != 0.5

    def test_holographic_coherence(self):
        """Testa cálculo de coerência holográfica."""
        # Narrativa AI com símbolo BTC (deve ter alta coerência)
        coherence_high = _calculate_holographic_coherence("AI_SURGE", "BTC")
        assert coherence_high > 1.0

        # Narrativa AI com símbolo não relacionado (coerência menor)
        coherence_low = _calculate_holographic_coherence("AI_SURGE", "DOGE")
        assert coherence_low <= 1.0

    def test_temporal_decay(self):
        """Testa fator de decaimento temporal."""
        import time

        # Timestamp atual
        current_time = time.time()
        decay_current = _calculate_temporal_decay(current_time)
        assert decay_current >= 0.9  # Quase sem decaimento

        # Timestamp antigo (1 hora)
        old_time = current_time - 3600
        decay_old = _calculate_temporal_decay(old_time)
        assert decay_old < decay_current  # Deve ter decaído


class TestFWHStrategy:
    """Testes para a estratégia FWH completa."""

    def test_strategy_initialization(self, fwh_strategy):
        """Testa inicialização da estratégia."""
        assert fwh_strategy.symbol == "BTC/USDT"
        assert fwh_strategy.timeframe == "1h"
        assert fwh_strategy.fib_lookback == 20
        assert fwh_strategy.hype_threshold == 0.618

    def test_strategy_with_holographic_integration(
        self, fwh_strategy, mock_holographic_engine, sample_market_data
    ):
        """Testa estratégia com integração holográfica."""
        # Inicializa com engine holográfico
        context = {"holographic_engine": mock_holographic_engine}
        fwh_strategy.initialize(context)

        assert fwh_strategy.holographic_engine is not None
        assert fwh_strategy.initialized is True

    @pytest.mark.parametrize("symbol,expected_boost_range", [
        ("BTC/USDT", (0.8, 2.0)),
        ("ETH/USDT", (0.7, 1.8)),
        ("DOGE/USDT", (0.5, 1.5)),
    ])
    def test_sentiment_boost_by_symbol(
        self, mock_holographic_engine, symbol, expected_boost_range
    ):
        """Testa boost de sentiment por símbolo."""
        boost = integrate_holographic_sentiment(
            mock_holographic_engine,
            symbol,
            use_cache=False
        )

        min_boost, max_boost = expected_boost_range
        assert min_boost <= boost <= max_boost


@pytest.mark.integration
class TestFWHIntegration:
    """Testes de integração da estratégia FWH."""

    def test_full_signal_generation_pipeline(
        self, fwh_strategy, mock_holographic_engine, sample_market_data
    ):
        """Testa pipeline completo de geração de sinais."""
        # Setup
        context = {"holographic_engine": mock_holographic_engine}
        fwh_strategy.initialize(context)

        trading_context = TradingContext(
            data=sample_market_data,
            symbol="BTC/USDT",
            timeframe="1h"
        )

        # Executa geração de sinal
        with patch.object(fwh_strategy, '_validate_with_tsvf', return_value=0.8):
            signals = fwh_strategy.generate_signal(trading_context)

        # Verifica resultado
        assert isinstance(signals, pd.DataFrame)
        # Pode estar vazio se condições não forem atendidas
        if not signals.empty:
            assert "signal" in signals.columns
            assert "confidence" in signals.columns