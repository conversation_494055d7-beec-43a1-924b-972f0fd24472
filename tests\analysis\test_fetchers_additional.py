import pytest
import types

from qualia.farsight.fetcher import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TwitterFetcher
from qualia.analysis.farsight_engine import FarsightEngine
from qualia.persistence.farsight_history import FarsightHistory


class DummyResponse:
    def __init__(self, data):
        self._data = data

    def raise_for_status(self):
        pass

    def json(self):
        return self._data


def test_github_fetcher_error_returns_empty(monkeypatch):
    def fake_get(*args, **kwargs):
        raise RuntimeError("boom")

    monkeypatch.setattr("requests.get", fake_get)
    fetcher = GitHubFetcher()
    result = fetcher.run()
    assert result == []


def test_github_fetcher_uses_token(monkeypatch):
    captured = {}

    def fake_get(url, headers=None, params=None, timeout=None):
        captured["auth"] = headers.get("Authorization")
        return DummyResponse({"items": []})

    monkeypatch.setattr("requests.get", fake_get)
    monkeypatch.setenv("GITHUB_TOKEN", "abc")
    fetcher = GitHubFetcher()
    fetcher.run()
    assert captured["auth"] == "Bearer abc"


def test_twitter_fetcher_error_and_token(monkeypatch):
    captured = {}

    def fake_get(url, headers=None, params=None, timeout=None):
        captured["auth"] = headers.get("Authorization")
        raise RuntimeError("fail")

    monkeypatch.setattr("requests.get", fake_get)
    monkeypatch.setenv("TWITTER_BEARER_TOKEN", "tkn")
    fetcher = TwitterFetcher()
    result = fetcher.run()
    assert result == []
    assert captured["auth"] == "Bearer tkn"


def test_engine_appends_history_with_collectors(monkeypatch):
    clusters = [
        {
            "topic": "A",
            "curvature": 0.1,
            "velocity": 0.1,
            "prediction_window": "na",
            "sources": [],
        }
    ]

    def fake_fetch(self):
        self._papers = [types.SimpleNamespace(title="p", summary="s", entry_id="l")]
        self._repos = [{"full_name": "repo", "html_url": "url", "description": "desc"}]
        self._tweets = [{"id": "1", "text": "t"}]
        self._items = [
            {"title": "p", "summary": "s", "link": "l"},
            {"title": "repo", "summary": "desc", "link": "url"},
            {"title": "t", "summary": "", "link": "https://twitter.com/i/web/status/1"},
        ]

    def fake_cluster(self):
        self._clusters = clusters

    monkeypatch.setattr("qualia.analysis.farsight_engine.ENABLE_V2", True)
    monkeypatch.setattr(FarsightEngine, "_fetch_papers_v2", fake_fetch)
    monkeypatch.setattr(FarsightEngine, "_cluster_papers_v2", fake_cluster)

    monkeypatch.setattr(FarsightHistory, "__init__", lambda self, path=None: None)
    called = {}

    def fake_append(self, data):
        called["data"] = data

    monkeypatch.setattr(FarsightHistory, "append_batch", fake_append)

    engine = FarsightEngine()
    engine.run()
    assert called["data"] == clusters
