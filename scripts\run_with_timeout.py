#!/usr/bin/env python3
"""Utility to execute scripts with timeout and monitoring.

This module runs another Python script, capturing its logs and applying a
runtime limit.
"""

from __future__ import annotations

import importlib.util
import os
import sys
from typing import Sequence

from qualia.utils.logger import get_logger

logger = get_logger(__name__)


def run_with_monitoring(
    script_path: str, timeout: int, args: Sequence[str] | None = None
) -> int:
    """Execute ``script_path`` with the specified timeout.

    Parameters
    ----------
    script_path
        Caminho para o script Python a ser executado.
    timeout
        Tempo limite em segundos.
    args
        Lista de argumentos adicionais a serem expostos via ``sys.argv``.

    Returns
    -------
    int
        ``0`` se o script foi executado, ``1`` caso o carregamento falhe.
    """

    script_path = os.path.abspath(script_path)
    logger.info(
        "Iniciando execucao de %s com timeout de %s segundos", script_path, timeout
    )
    if args is not None:
        sys.argv = [script_path] + list(args)
        logger.info("Argumentos: %s", args)

    logger.info("Carregando modulo...")
    if not os.path.exists(script_path):
        logger.error(
            "Falha ao carregar script: caminho inexistente ou invalido -> %s",
            script_path,
        )
        return 1

    spec = importlib.util.spec_from_file_location("__main__", script_path)
    if spec is None or spec.loader is None:
        logger.error(
            "Falha ao carregar script: caminho inexistente ou invalido -> %s",
            script_path,
        )
        return 1

    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)  # type: ignore[attr-defined]
    return 0


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Executa scripts com timeout")
    parser.add_argument("script_path", help="Caminho para o script a executar")
    parser.add_argument("--timeout", type=int, default=60, help="Timeout em segundos")
    parser.add_argument("args", nargs="*", help="Argumentos adicionais para o script")
    parsed = parser.parse_args()

    exit_code = run_with_monitoring(parsed.script_path, parsed.timeout, parsed.args)
    sys.exit(exit_code)
