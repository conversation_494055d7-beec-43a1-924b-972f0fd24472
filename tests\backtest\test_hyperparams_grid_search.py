#!/usr/bin/env python3
"""
Testes para o Sistema de Grid Search - Etapa C

YAA IMPLEMENTATION: Testes unitários e de integração para o sistema de backtest
offline de hiperparâmetros QUALIA.
"""

import asyncio
import tempfile
from datetime import datetime
from pathlib import Path
from unittest.mock import patch

import pytest
import pandas as pd
import numpy as np

from qualia.backtest.hyperparams_grid_search import (
    GridSearchParams,
    HyperParamsGridSearch,
    BacktestResult,
    GridSearchResults,
)
from qualia.config.hyperparams_loader import HyperParams


class TestGridSearchParams:
    """Testes para GridSearchParams."""
    
    def test_default_params(self):
        """Testa parâmetros padrão."""
        params = GridSearchParams()
        
        assert params.price_amp_range == (1.0, 10.0, 10)
        assert params.news_amp_range == (1.0, 10.0, 10)
        assert params.min_conf_range == (0.3, 0.8, 6)
        assert params.backtest_days == 90
        assert params.initial_capital == 10000.0
        assert params.symbols == ["BTC/USDT", "ETH/USDT"]
        assert params.max_workers == 4
    
    def test_custom_params(self):
        """Testa parâmetros customizados."""
        params = GridSearchParams(
            price_amp_range=(2.0, 8.0, 4),
            news_amp_range=(3.0, 7.0, 3),
            min_conf_range=(0.4, 0.7, 4),
            backtest_days=30,
            initial_capital=5000.0,
            symbols=["BTC/USDT"],
            max_workers=2
        )
        
        assert params.price_amp_range == (2.0, 8.0, 4)
        assert params.news_amp_range == (3.0, 7.0, 3)
        assert params.min_conf_range == (0.4, 0.7, 4)
        assert params.backtest_days == 30
        assert params.initial_capital == 5000.0
        assert params.symbols == ["BTC/USDT"]
        assert params.max_workers == 2


class TestBacktestResult:
    """Testes para BacktestResult."""
    
    def test_result_creation(self):
        """Testa criação de resultado."""
        result = BacktestResult(
            price_amplification=5.0,
            news_amplification=4.0,
            min_confidence=0.6,
            total_trades=50,
            winning_trades=30,
            losing_trades=20,
            win_rate=0.6,
            total_return=1500.0,
            total_return_pct=0.15,
            sharpe_ratio=1.2,
            max_drawdown=800.0,
            max_drawdown_pct=0.08,
            profit_factor=1.8,
            volatility=0.12,
            calmar_ratio=0.0,  # Será calculado
            sortino_ratio=1.5,
            backtest_duration_days=90,
            symbols_tested=["BTC/USDT"],
            start_date="2024-01-01",
            end_date="2024-03-31",
            execution_time_seconds=5.2
        )
        
        assert result.price_amplification == 5.0
        assert result.news_amplification == 4.0
        assert result.min_confidence == 0.6
        assert result.total_trades == 50
        assert result.win_rate == 0.6
        assert result.total_return_pct == 0.15
        assert result.sharpe_ratio == 1.2
        
        # Testa cálculo do Calmar ratio
        expected_annual_return = (1 + 0.15) ** (365 / 90) - 1
        expected_calmar = expected_annual_return / 0.08
        assert abs(result.calmar_ratio - expected_calmar) < 0.01
    
    def test_result_with_zero_drawdown(self):
        """Testa resultado com drawdown zero."""
        result = BacktestResult(
            price_amplification=5.0,
            news_amplification=4.0,
            min_confidence=0.6,
            total_trades=50,
            winning_trades=30,
            losing_trades=20,
            win_rate=0.6,
            total_return=1500.0,
            total_return_pct=0.15,
            sharpe_ratio=1.2,
            max_drawdown=0.0,
            max_drawdown_pct=0.0,  # Zero drawdown
            profit_factor=1.8,
            volatility=0.12,
            calmar_ratio=0.0,
            sortino_ratio=1.5,
            backtest_duration_days=90,
            symbols_tested=["BTC/USDT"],
            start_date="2024-01-01",
            end_date="2024-03-31",
            execution_time_seconds=5.2
        )
        
        # Com drawdown zero e retorno positivo, Calmar deve ser infinito
        assert result.calmar_ratio == float('inf')


class TestHyperParamsGridSearch:
    """Testes para HyperParamsGridSearch."""
    
    def test_initialization(self):
        """Testa inicialização do grid search."""
        params = GridSearchParams(
            price_amp_range=(2.0, 8.0, 4),
            news_amp_range=(3.0, 7.0, 3),
            min_conf_range=(0.4, 0.7, 4)
        )
        
        grid_search = HyperParamsGridSearch(params)
        
        assert grid_search.params == params
        assert grid_search._calculate_total_combinations() == 4 * 3 * 4  # 48 combinações
    
    def test_parameter_combinations_generation(self):
        """Testa geração de combinações de parâmetros."""
        params = GridSearchParams(
            price_amp_range=(2.0, 4.0, 3),  # 2.0, 3.0, 4.0
            news_amp_range=(1.0, 3.0, 3),   # 1.0, 2.0, 3.0
            min_conf_range=(0.4, 0.6, 3)    # 0.4, 0.5, 0.6
        )
        
        grid_search = HyperParamsGridSearch(params)
        combinations = grid_search._generate_parameter_combinations()
        
        assert len(combinations) == 27  # 3 * 3 * 3
        
        # Verifica se todas as combinações estão presentes
        price_amps = set(combo[0] for combo in combinations)
        news_amps = set(combo[1] for combo in combinations)
        min_confs = set(combo[2] for combo in combinations)
        
        assert len(price_amps) == 3
        assert len(news_amps) == 3
        assert len(min_confs) == 3
        
        # Verifica ranges
        assert min(price_amps) >= 2.0 and max(price_amps) <= 4.0
        assert min(news_amps) >= 1.0 and max(news_amps) <= 3.0
        assert min(min_confs) >= 0.4 and max(min_confs) <= 0.6
    
    def test_mock_data_generation(self):
        """Testa geração de dados mock."""
        from datetime import datetime, timedelta
        
        params = GridSearchParams()
        grid_search = HyperParamsGridSearch(params)
        
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 31)
        
        df = grid_search._generate_mock_data("BTC/USDT", start_date, end_date)
        
        assert isinstance(df, pd.DataFrame)
        assert len(df) > 0
        assert all(col in df.columns for col in ['open', 'high', 'low', 'close', 'volume'])
        assert df.index.name == 'timestamp'
        
        # Verifica se high >= low para todos os candles
        assert all(df['high'] >= df['low'])
        assert all(df['high'] >= df['open'])
        assert all(df['high'] >= df['close'])
        assert all(df['low'] <= df['open'])
        assert all(df['low'] <= df['close'])
    
    def test_single_backtest_execution(self):
        """Testa execução de backtest individual."""
        params = GridSearchParams(backtest_days=30)
        grid_search = HyperParamsGridSearch(params)
        
        # Gera dados mock
        market_data = {
            "BTC/USDT": grid_search._generate_mock_data(
                "BTC/USDT",
                datetime(2024, 1, 1),
                datetime(2024, 1, 31)
            )
        }
        
        # Executa backtest
        result = grid_search._run_single_backtest(5.0, 4.0, 0.6, market_data)
        
        assert isinstance(result, BacktestResult)
        assert result.price_amplification == 5.0
        assert result.news_amplification == 4.0
        assert result.min_confidence == 0.6
        assert result.total_trades >= 0
        assert 0.0 <= result.win_rate <= 1.0
        assert result.execution_time_seconds >= 0
    
    @pytest.mark.asyncio
    async def test_sequential_backtests(self):
        """Testa execução sequencial de backtests."""
        params = GridSearchParams(
            price_amp_range=(2.0, 4.0, 2),  # 2 valores
            news_amp_range=(3.0, 5.0, 2),   # 2 valores
            min_conf_range=(0.5, 0.6, 2),   # 2 valores
            backtest_days=30,
            max_workers=1  # Força execução sequencial
        )
        
        grid_search = HyperParamsGridSearch(params)
        
        # Carrega dados mock
        market_data = await grid_search._load_historical_data()
        
        # Gera combinações
        combinations = grid_search._generate_parameter_combinations()
        assert len(combinations) == 8  # 2 * 2 * 2
        
        # Executa backtests sequenciais
        results = await grid_search._run_sequential_backtests(combinations, market_data)
        
        assert len(results) == 8
        assert all(isinstance(r, BacktestResult) for r in results)
    
    def test_results_saving(self):
        """Testa salvamento de resultados."""
        # Cria resultados mock
        params = GridSearchParams()
        
        results = [
            BacktestResult(
                price_amplification=5.0,
                news_amplification=4.0,
                min_confidence=0.6,
                total_trades=50,
                winning_trades=30,
                losing_trades=20,
                win_rate=0.6,
                total_return=1500.0,
                total_return_pct=0.15,
                sharpe_ratio=1.2,
                max_drawdown=800.0,
                max_drawdown_pct=0.08,
                profit_factor=1.8,
                volatility=0.12,
                calmar_ratio=1.875,
                sortino_ratio=1.5,
                backtest_duration_days=90,
                symbols_tested=["BTC/USDT"],
                start_date="2024-01-01",
                end_date="2024-03-31",
                execution_time_seconds=5.2
            )
        ]
        
        grid_results = GridSearchResults(
            search_params=params,
            results=results,
            total_combinations=1,
            successful_backtests=1,
            failed_backtests=0
        )
        
        grid_search = HyperParamsGridSearch(params)
        
        # Testa salvamento em arquivo temporário
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_path = f.name
        
        try:
            grid_search.save_results(grid_results, temp_path)
            
            # Verifica se arquivo foi criado
            assert Path(temp_path).exists()
            
            # Verifica se CSV também foi criado
            csv_path = Path(temp_path).with_suffix('.csv')
            assert csv_path.exists()
            
            # Carrega e verifica conteúdo JSON
            import json
            with open(temp_path, 'r') as f:
                saved_data = json.load(f)
            
            assert 'search_params' in saved_data
            assert 'summary' in saved_data
            assert 'best_results' in saved_data
            assert 'all_results' in saved_data
            assert len(saved_data['all_results']) == 1
            
            # Verifica CSV
            df = pd.read_csv(csv_path)
            assert len(df) == 1
            assert 'price_amplification' in df.columns
            assert 'sharpe_ratio' in df.columns
            
        finally:
            # Limpa arquivos temporários
            Path(temp_path).unlink(missing_ok=True)
            Path(temp_path).with_suffix('.csv').unlink(missing_ok=True)


class TestGridSearchResults:
    """Testes para GridSearchResults."""
    
    def test_results_analysis(self):
        """Testa análise de resultados."""
        params = GridSearchParams(min_trades_required=10)
        
        # Cria resultados mock
        results = [
            BacktestResult(
                price_amplification=5.0, news_amplification=4.0, min_confidence=0.6,
                total_trades=20, winning_trades=12, losing_trades=8, win_rate=0.6,
                total_return=1500.0, total_return_pct=0.15, sharpe_ratio=1.2,
                max_drawdown=800.0, max_drawdown_pct=0.08, profit_factor=1.8,
                volatility=0.12, calmar_ratio=1.875, sortino_ratio=1.5,
                backtest_duration_days=90, symbols_tested=["BTC/USDT"],
                start_date="2024-01-01", end_date="2024-03-31", execution_time_seconds=5.2
            ),
            BacktestResult(
                price_amplification=3.0, news_amplification=6.0, min_confidence=0.7,
                total_trades=15, winning_trades=10, losing_trades=5, win_rate=0.67,
                total_return=2000.0, total_return_pct=0.20, sharpe_ratio=1.5,
                max_drawdown=600.0, max_drawdown_pct=0.06, profit_factor=2.2,
                volatility=0.10, calmar_ratio=3.33, sortino_ratio=1.8,
                backtest_duration_days=90, symbols_tested=["BTC/USDT"],
                start_date="2024-01-01", end_date="2024-03-31", execution_time_seconds=4.8
            ),
            BacktestResult(  # Resultado inválido (poucos trades)
                price_amplification=8.0, news_amplification=2.0, min_confidence=0.8,
                total_trades=5, winning_trades=3, losing_trades=2, win_rate=0.6,
                total_return=500.0, total_return_pct=0.05, sharpe_ratio=0.8,
                max_drawdown=300.0, max_drawdown_pct=0.03, profit_factor=1.5,
                volatility=0.08, calmar_ratio=1.67, sortino_ratio=1.2,
                backtest_duration_days=90, symbols_tested=["BTC/USDT"],
                start_date="2024-01-01", end_date="2024-03-31", execution_time_seconds=3.1
            )
        ]
        
        grid_results = GridSearchResults(
            search_params=params,
            results=results,
            total_combinations=3,
            successful_backtests=3,
            failed_backtests=0
        )
        
        # Verifica identificação dos melhores resultados
        assert grid_results.best_sharpe is not None
        assert grid_results.best_sharpe.sharpe_ratio == 1.5  # Segundo resultado
        
        assert grid_results.best_return is not None
        assert grid_results.best_return.total_return_pct == 0.20  # Segundo resultado
        
        assert grid_results.best_drawdown is not None
        assert grid_results.best_drawdown.max_drawdown_pct == 0.06  # Segundo resultado
        
        assert grid_results.best_calmar is not None
        assert grid_results.best_calmar.calmar_ratio == 3.33  # Segundo resultado


@pytest.mark.asyncio
async def test_full_grid_search_integration():
    """Teste de integração completo do grid search."""
    # Configuração pequena para teste rápido
    params = GridSearchParams(
        price_amp_range=(3.0, 7.0, 3),  # 3 valores
        news_amp_range=(2.0, 6.0, 3),   # 3 valores
        min_conf_range=(0.4, 0.6, 3),   # 3 valores
        backtest_days=30,
        symbols=["BTC/USDT"],
        max_workers=1,  # Execução sequencial para teste
        min_trades_required=5
    )
    
    grid_search = HyperParamsGridSearch(params)
    
    # Executa grid search completo
    results = await grid_search.run_grid_search()
    
    # Verifica resultados
    assert isinstance(results, GridSearchResults)
    assert results.total_combinations == 27  # 3 * 3 * 3
    assert results.successful_backtests > 0
    assert len(results.results) > 0
    assert results.execution_end is not None
    assert results.total_execution_time is not None
    assert results.total_execution_time > 0


if __name__ == "__main__":
    # Executa testes
    pytest.main([__file__, "-v"])
