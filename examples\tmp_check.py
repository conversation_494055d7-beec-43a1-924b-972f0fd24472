import importlib, sys

QE_base = importlib.import_module("src.qualia.core.encoders").QuantumEncoder
MFCC = importlib.import_module("src.qualia.core.audio_encoders").MFCCQuantumEncoder
from qualia.config import encoder_registry

QE_registry = encoder_registry.get_encoder_class("quantum_mfcc").__bases__[0]
print("MFCC", MFCC)
print("QE_base", QE_base, id(QE_base))
print("QE_registry base", QE_registry, id(QE_registry))
print("issubclass direct", issubclass(MFCC, QE_base))
