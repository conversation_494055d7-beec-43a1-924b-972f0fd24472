# Plano de Testes de Carga para Múltiplas Conexões

Este documento descreve uma estratégia para avaliar a performance do módulo de mercado do QUALIA quando múltiplas conexões são abertas em paralelo. O objetivo é medir latência, uso de CPU e comportamento de erros ao escalar o número de instâncias do `CryptoDataFetcher`.

## Metodologia

1. **Cenário de Teste**
   - <PERSON><PERSON>r v<PERSON>rias instâncias das integrações (`KrakenIntegration`, `BinanceIntegration`, `CoinbaseIntegration`).
   - Cada instância executa `fetch_ticker` e `fetch_ohlcv` para símbolos distintos em ciclos assíncronos.
   - Utilizar `asyncio.gather` para coordenar até 50 conexões simultâneas.

2. **Coleta de Métricas**
   - Habilitar o envio de métricas via StatsD para cada integração.
   - Registrar latência média de `fetch_ticker` e `fetch_ohlcv`.
   - Monitorar contagem de erros e abertura de `CircuitBreaker`.

3. **Ferramentas**
   - `pytest-benchmark` para medir tempos de execução.
   - `docker-compose` opcional para orquestrar serviços externos, como Redis ou um servidor StatsD local.

4. **Critérios de Sucesso**
   - Todas as conexões são estabelecidas em menos de 5 segundos em média.
   - A latência de `fetch_ticker` permanece abaixo de 300 ms com até 20 conexões simultâneas.
   - Não devem ocorrer mais de 2% de erros de timeout durante o teste.

## Execução

O teste pode ser implementado em `benchmarks/market/test_load_multiconnections.py`, criando tarefas assíncronas que iniciam as integrações, realizam operações de mercado e encerram as conexões após um período definido.

