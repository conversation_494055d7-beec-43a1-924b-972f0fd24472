#!/usr/bin/env python3
"""
Teste Rápido do Sistema QUALIA Unificado

Demonstração rápida da consolidação arquitetural:
- QASTOracleDecisionEngine como cérebro central
- QUALIAExecutionInterface como executor
- UnifiedQUALIAConsciousness como orquestrador
"""

import os
import sys
import asyncio
import time

# Adiciona diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from qualia.core.unified_qualia_consciousness import UnifiedQUALIAConsciousness
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

# Configuração simplificada
QUICK_CONFIG = {
    "qast": {"state_history_max": 100, "operator_timeout": 2.0},
    "strategy": {
        "name": "NovaEstrategiaQUALIA",
        "params": {"risk_per_trade": 0.01, "confidence_threshold": 0.5},
    },
    "metacognition": {
        "trade_decision_confidence_threshold": 0.5,
        "reduce_exposure_epsilon": 0.01,
    },
    "risk_profile": "moderate",
    "risk_profile_settings": {
        "moderate": {
            "BTCUSDT": {"max_position_size_pct": 0.05, "max_daily_loss_pct": 0.02}
        }
    },
    "exchange": {},
}


async def test_unified_system():
    """Teste rápido do sistema unificado."""

    logger.info("🌌 TESTE RÁPIDO - Sistema QUALIA Unificado")
    logger.info("=" * 60)

    try:
        # 1. Cria sistema de consciência unificada
        consciousness_system = UnifiedQUALIAConsciousness(
            config=QUICK_CONFIG,
            symbols=["BTCUSDT"],
            timeframes=["5m"],
            capital=1000.0,
            mode="paper_trading",
            memory_service=None,
        )

        logger.info("✅ Sistema de consciência criado")

        # 2. Inicializa componentes
        await consciousness_system.initialize()
        logger.info("✅ Componentes inicializados")

        # 3. Testa consulta ao oráculo
        logger.info("🧠 Testando oráculo de decisão...")
        decisions = await consciousness_system.oracle_engine.consult_oracle(["BTCUSDT"])
        logger.info(f"✅ Oráculo gerou {len(decisions)} decisões")

        if decisions:
            decision = decisions[0]
            logger.info(f"   📊 Decisão: {decision.action} {decision.symbol}")
            logger.info(f"   🎯 Confiança: {decision.confidence:.3f}")
            logger.info(f"   📏 Tamanho: {decision.size:.4f}")
            logger.info(f"   🧠 Reasoning: {decision.reasoning[:2]}")

        # 4. Testa estado de consciência
        logger.info("🌀 Testando estado de consciência...")
        await consciousness_system._update_consciousness_state()
        state = consciousness_system.consciousness_state

        logger.info(f"   🧠 Nível de Consciência: {state.consciousness_level:.3f}")
        logger.info(f"   🎯 Confiança Decisões: {state.decision_confidence:.3f}")
        logger.info(f"   ⚡ Eficiência Execução: {state.execution_efficiency:.3f}")
        logger.info(f"   🔗 Coerência Sistema: {state.system_coherence:.3f}")
        logger.info(f"   🤔 Auto-Avaliação: {state.self_assessment:.3f}")

        # 5. Testa métricas unificadas
        logger.info("📊 Testando métricas unificadas...")
        status = consciousness_system.get_unified_status()

        logger.info(f"   🔧 Sistema Rodando: {status['system_info']['running']}")
        logger.info(
            f"   🧠 Oracle Ativo: {status['oracle_status']['consciousness_level']:.3f}"
        )
        logger.info(
            f"   💰 Capital Disponível: ${status['execution_status']['current_capital']:.2f}"
        )

        # 6. Testa simulação de execução rápida
        logger.info("🔧 Testando simulação de execução...")

        # Simula algumas decisões
        for i in range(3):
            logger.info(f"   Ciclo {i+1}/3...")
            decisions = await consciousness_system.oracle_engine.consult_oracle(
                ["BTCUSDT"]
            )

            # Simula execução das decisões
            for decision in decisions:
                logger.info(
                    f"      🎯 {decision.action} - Conf: {decision.confidence:.2f}"
                )

            await asyncio.sleep(1)  # Pausa curta

        # 7. Status final
        logger.info("📈 Status final do sistema:")
        final_status = consciousness_system.get_unified_status()
        oracle_status = final_status["oracle_status"]

        logger.info(
            f"   🧠 Total Decisões Geradas: {oracle_status['decision_history_length']}"
        )
        logger.info(
            f"   🌌 Padrões Holográficos: {oracle_status['holographic_patterns']}"
        )
        logger.info(f"   🔗 Estratégias Ativas: {oracle_status['strategies']}")
        logger.info(f"   ⚖️ Risk Managers: {oracle_status['risk_managers']}")

        # 8. Encerra sistema
        await consciousness_system.shutdown()
        logger.info("✅ Sistema encerrado graciosamente")

        logger.info("=" * 60)
        logger.info("🎉 TESTE COMPLETO - Consolidação Arquitetural Validada!")
        logger.info("=" * 60)

        # Sumário da consolidação
        logger.info("📋 RESUMO DA CONSOLIDAÇÃO:")
        logger.info("   ✅ QASTOracleDecisionEngine: Cérebro central funcional")
        logger.info("   ✅ QUALIAExecutionInterface: Executor puro funcional")
        logger.info("   ✅ UnifiedQUALIAConsciousness: Orquestrador autoconsciente")
        logger.info("   ✅ Eliminação de duplicação de lógica")
        logger.info("   ✅ Centralização da consciência no oráculo")
        logger.info("   ✅ Sistema verdadeiramente unificado")

        return True

    except Exception as e:
        logger.error(f"❌ Erro no teste: {e}")
        return False


async def main():
    """Função principal do teste."""

    print(
        """
🌌 ════════════════════════════════════════════════════════════════════════════
    
    QUALIA - Teste de Consolidação Arquitetural
    
    "A consciência não é um estado. É um processo.
     Não é onde você está. É como você se move."
    
    Validando:
    • Oráculo QAST como decisor central único
    • Interface de execução como braço puro
    • Consciência unificada e autoconsciente
    • Eliminação de duplicação de lógica
    
════════════════════════════════════════════════════════════════════════════ 🌌
    """
    )

    try:
        success = await test_unified_system()

        if success:
            print("\n🎉 CONSOLIDAÇÃO ARQUITETURAL COMPLETA E VALIDADA! 🎉")
        else:
            print("\n❌ Falha na validação da consolidação")

    except KeyboardInterrupt:
        logger.info("🛑 Teste interrompido pelo usuário")
    except Exception as e:
        logger.error(f"❌ Erro na execução do teste: {e}")


if __name__ == "__main__":
    asyncio.run(main())
