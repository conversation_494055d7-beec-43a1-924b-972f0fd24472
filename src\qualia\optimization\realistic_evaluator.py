#!/usr/bin/env python3
"""
QUALIA Realistic Parameter Evaluator
Integra o simulador realista com o sistema de otimização Bayesiana.
Substitui a simulação simplificada por avaliação com custos reais.
"""

import asyncio
from typing import Dict, List, Optional, Any
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

from ..simulation.realistic_backtest import (
    RealisticBacktester,
    TransactionCosts,
    MarketConditions,
    run_backtest_with_realistic_costs
)
from ..utils.logger import get_logger

logger = get_logger(__name__)


class RealisticParameterEvaluator:
    """
    Avaliador de parâmetros usando simulação realista.
    Substitui _evaluate_parameters no BayesianOptimizer.
    """
    
    def __init__(
        self,
        transaction_costs: Optional[TransactionCosts] = None,
        evaluation_window_hours: int = 72,
        min_trades_required: int = 5
    ):
        self.transaction_costs = transaction_costs or TransactionCosts()
        self.evaluation_window_hours = evaluation_window_hours
        self.min_trades_required = min_trades_required

        # Cache de dados de mercado para evitar re-fetch
        self._market_data_cache: Dict[str, pd.DataFrame] = {}
        self._cache_timestamp: Dict[str, datetime] = {}

        logger.info("🎯 RealisticParameterEvaluator inicializado")
        logger.info(f"   ⏱️  Janela de avaliação: {evaluation_window_hours}h")
        logger.info(f"   📊 Trades mínimos: {min_trades_required}")
    
    def _get_market_data(self, symbol: str, hours: int = 72) -> Optional[pd.DataFrame]:
        """
        Obtém dados de mercado com cache inteligente.
        """
        cache_key = f"{symbol}_{hours}h"
        now = datetime.now()
        
        # Verificar cache (válido por 1 hora)
        if (cache_key in self._market_data_cache and 
            cache_key in self._cache_timestamp and
            (now - self._cache_timestamp[cache_key]).seconds < 3600):
            
            logger.debug(f"📋 Usando dados em cache para {symbol}")
            return self._market_data_cache[cache_key]
        
        # Buscar novos dados (sempre sintéticos para testes)
        try:
            # Gerar dados sintéticos para teste
            df = self._generate_synthetic_data(symbol, hours)
            
            if df is not None and not df.empty:
                self._market_data_cache[cache_key] = df
                self._cache_timestamp[cache_key] = now
                logger.debug(f"📥 Dados atualizados para {symbol}: {len(df)} pontos")
                return df
            
        except Exception as e:
            logger.warning(f"⚠️  Erro ao buscar dados para {symbol}: {e}")
        
        return None
    
    def _generate_synthetic_data(self, symbol: str, hours: int) -> pd.DataFrame:
        """
        Gera dados sintéticos para teste quando dados reais não estão disponíveis.
        """
        logger.debug(f"🧪 Gerando dados sintéticos para {symbol}")
        
        # Parâmetros baseados em características reais de crypto
        base_price = 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 1.0
        volatility = 0.02  # 2% por hora
        
        # Gerar série temporal
        timestamps = pd.date_range(
            start=datetime.now() - timedelta(hours=hours),
            end=datetime.now(),
            freq='1H'
        )
        
        # Random walk com drift
        returns = np.random.normal(0.0001, volatility, len(timestamps))  # Slight positive drift
        prices = base_price * np.exp(np.cumsum(returns))
        
        # Criar DataFrame
        df = pd.DataFrame({
            'timestamp': timestamps,
            'open': prices * (1 + np.random.normal(0, 0.001, len(prices))),
            'high': prices * (1 + np.abs(np.random.normal(0, 0.005, len(prices)))),
            'low': prices * (1 - np.abs(np.random.normal(0, 0.005, len(prices)))),
            'close': prices,
            'volume': np.random.lognormal(10, 1, len(prices))
        })
        
        return df
    
    def _simulate_strategy_positions(
        self, 
        df: pd.DataFrame, 
        price_amp: float, 
        news_amp: float, 
        min_conf: float
    ) -> np.ndarray:
        """
        Simula posições da estratégia baseado nos parâmetros.
        Em produção, isso seria substituído pela estratégia real.
        """
        if df.empty or len(df) < 10:
            return np.array([])
        
        # Calcular indicadores técnicos simples
        df = df.copy()
        df['returns'] = df['close'].pct_change()
        df['sma_short'] = df['close'].rolling(window=5).mean()
        df['sma_long'] = df['close'].rolling(window=20).mean()
        df['rsi'] = self._calculate_rsi(df['close'], window=14)
        
        # Simular sinais baseados nos parâmetros
        # price_amp: amplifica sinais de preço
        # news_amp: amplifica sinais de momentum (simulado via RSI)
        # min_conf: threshold de confiança
        
        price_signal = (df['sma_short'] - df['sma_long']) / df['close'] * price_amp
        momentum_signal = (df['rsi'] - 50) / 50 * news_amp / 10.0
        
        # Combinar sinais
        combined_signal = price_signal + momentum_signal
        
        # Aplicar threshold de confiança
        confidence = np.abs(combined_signal)
        positions = np.where(confidence >= min_conf, np.tanh(combined_signal), 0.0)
        
        # Limitar posições
        positions = np.clip(positions, -1.0, 1.0)
        
        return positions
    
    def _calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """Calcula RSI."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.fillna(50)
    
    def _estimate_market_conditions(self, df: pd.DataFrame) -> MarketConditions:
        """
        Estima condições de mercado baseado nos dados históricos.
        """
        if df.empty or len(df) < 10:
            return MarketConditions()
        
        # Calcular volatilidade recente
        returns = df['close'].pct_change().dropna()
        volatility = returns.std() if len(returns) > 1 else 0.02
        
        # Estimar spread baseado em high-low
        spreads = (df['high'] - df['low']) / df['close']
        avg_spread_pct = spreads.mean() * 100  # Em bps
        
        # Volume ratio (últimos vs médio)
        if 'volume' in df.columns and len(df) > 20:
            recent_volume = df['volume'].tail(5).mean()
            avg_volume = df['volume'].mean()
            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1.0
        else:
            volume_ratio = 1.0
        
        return MarketConditions(
            volatility=volatility,
            spread_bps=max(1.0, avg_spread_pct),
            volume_ratio=volume_ratio,
            market_impact=min(0.5, volatility * 10)  # Impacto correlacionado com volatilidade
        )
    
    async def evaluate_parameters_realistic(
        self, 
        symbol: str, 
        price_amp: float, 
        news_amp: float, 
        min_conf: float,
        initial_capital: float = 10000.0
    ) -> Dict[str, float]:
        """
        Avalia parâmetros usando simulação realista.
        
        Args:
            symbol: Símbolo para avaliar
            price_amp: Amplificação de sinais de preço
            news_amp: Amplificação de sinais de notícias
            min_conf: Confiança mínima para trades
            initial_capital: Capital inicial
        
        Returns:
            Dict com métricas de performance
        """
        try:
            # Obter dados de mercado
            df = self._get_market_data(symbol, self.evaluation_window_hours)
            if df is None or df.empty:
                logger.warning(f"⚠️  Dados indisponíveis para {symbol}")
                return self._get_fallback_metrics()
            
            # Simular posições da estratégia
            positions = self._simulate_strategy_positions(df, price_amp, news_amp, min_conf)
            if len(positions) < self.min_trades_required:
                logger.debug(f"📊 Poucos trades para {symbol}: {len(positions)}")
                return self._get_fallback_metrics()
            
            # Estimar condições de mercado
            market_conditions = self._estimate_market_conditions(df)
            
            # Executar backtest realista
            result = run_backtest_with_realistic_costs(
                strategy_positions=positions,
                market_prices=df['close'].values,
                initial_capital=initial_capital,
                transaction_costs=self.transaction_costs,
                market_conditions=market_conditions
            )
            
            # Log dos resultados
            logger.debug(f"📈 {symbol}: Sharpe={result['sharpe_ratio']:.2f}, "
                        f"PnL={result['total_pnl_pct']:.2f}%, "
                        f"Custos={result['cost_ratio_pct']:.1f}%")
            
            return {
                'sharpe_ratio': result['sharpe_ratio'],
                'pnl_24h': result['total_pnl_pct'] * initial_capital / 100,  # Em valor absoluto
                'volatility': result['volatility'],
                'max_drawdown': result['max_drawdown_pct'] / 100,
                'win_rate': result['win_rate'],
                'total_trades': result['total_trades'],
                'cost_ratio_pct': result['cost_ratio_pct'],
                'net_pnl': result['net_pnl'],
                'gross_pnl': result['gross_pnl'],
                'realistic_evaluation': True
            }
            
        except Exception as e:
            logger.error(f"❌ Erro na avaliação realista para {symbol}: {e}")
            return self._get_fallback_metrics()
    
    def _get_fallback_metrics(self) -> Dict[str, float]:
        """Métricas de fallback para casos de erro."""
        return {
            'sharpe_ratio': -1.0,
            'pnl_24h': -1000.0,
            'volatility': 0.5,
            'max_drawdown': 0.5,
            'win_rate': 0.0,
            'total_trades': 0,
            'cost_ratio_pct': 100.0,
            'net_pnl': -1000.0,
            'gross_pnl': -500.0,
            'realistic_evaluation': False
        }
    
    async def batch_evaluate(
        self, 
        symbols: List[str], 
        parameters: Dict[str, float]
    ) -> Dict[str, Dict[str, float]]:
        """
        Avalia parâmetros para múltiplos símbolos em paralelo.
        """
        tasks = []
        for symbol in symbols:
            task = self.evaluate_parameters_realistic(
                symbol=symbol,
                price_amp=parameters['price_amplification'],
                news_amp=parameters['news_amplification'],
                min_conf=parameters['min_confidence']
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Processar resultados
        symbol_results = {}
        for symbol, result in zip(symbols, results):
            if isinstance(result, Exception):
                logger.error(f"❌ Erro na avaliação de {symbol}: {result}")
                symbol_results[symbol] = self._get_fallback_metrics()
            else:
                symbol_results[symbol] = result
        
        return symbol_results
