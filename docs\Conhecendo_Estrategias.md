# Conhecendo as Estratégias do QUALIA

As estratégias de trading do QUALIA são como "caminhos" que o sistema percorre para transformar sinais em decisões concretas. Cada estratégia combina indicadores clássicos com métricas quânticas, adaptando-se aos padrões detectados.

## O que torna essas estratégias diferentes?

- **Integração com o QAST**: o ciclo de evolução quântica analisa o estado do mercado e ajusta parâmetros automaticamente.
- **Composição modular**: é possível combinar técnicas de scalping, análise de tendência e reconhecimento de padrões em uma única estratégia.
- **Configurações de risco flexíveis**: cada estratégia respeita perfis de risco descritos em [docs/risk_management.md](risk_management.md).

## Estrutura Básica

1. **QualiaEnhancedScalpingStrategy** – exemplo mais completo, unindo indicadores de preço (EMAs, RSI) a métricas quânticas.
2. **CompositeStrategy** – agrupa outras estratégias e orquestra execuções simultâneas.
3. **EnhancedQuantumMomentumStrategy** – foca em padrões de inversão e momentos de força do mercado.
4. **QualiaTSVFStrategy** – integrada ao Event Bus, publica `strategy.signal` e pode ser ativada via flag `QUALIA_FT_NOVA_ESTRATEGIA`.

### Feature Flags

Alguns comportamentos da `QualiaTSVFStrategy` podem ser controlados por variáveis de ambiente:

- `QUALIA_FT_S1`, `QUALIA_FT_S2` e `QUALIA_FT_S3` ativam ou desativam as sub-estratégias S1, S2 e S3.
- `QUALIA_FT_TSVF_CACHE` controla o uso do cache interno de estados TSVF.

Todas ficam habilitadas por padrão quando as variáveis não são definidas.

Desde a versão 1.0.0 a estratégia passa a armazenar o valor final exigido de histórico em `_final_required_initial_data_length`. Quando o cálculo ultrapassa o limite de candles aceito pela exchange, o aviso correspondente é exibido somente quando esse valor for alterado.

Indicadores como EMAs, RSI e ATR passaram a ser calculados pelo módulo
`qualia.indicators`, reduzindo duplicação de código nas estratégias de
scalping e momentum.

Para inspeções de código, consulte `src/qualia/strategies` e [docs/analysis/strategies_audit.md](analysis/strategies_audit.md).

## Multiplicadores de ATR

Várias estratégias recorrem a `atr_stop_take_profit` (em
`src/qualia/strategies/nova_estrategia_qualia/risk_utils.py`) para determinar
stop-loss e take-profit. O período padrão de cálculo do ATR é ``14`` – valor
obtido em backtests que equilibrou sensibilidade e estabilidade.

O multiplicador do stop usa a fórmula::

    sl_multiplier = 3.0 * (1.0 - min(confidence, 0.5))

Logo, quanto maior ``confidence``, menor a distância do stop.

```python
from qualia.strategies.nova_estrategia_qualia.risk_utils import atr_stop_take_profit

prices = list(range(100, 115))  # 15 candles
sl_low, _ = atr_stop_take_profit(prices, "buy", confidence=0.2, transaction_cost=0.001)
sl_high, _ = atr_stop_take_profit(prices, "buy", confidence=0.8, transaction_cost=0.001)
print(round(sl_low, 2), round(sl_high, 2))
# 111.6 112.5
```

Neste exemplo o stop-loss para ``confidence=0.8`` fica mais próximo do preço
atual, demonstrando o efeito do multiplicador.

## Perfis de risco por ativo

Desde a versão 1.2 o ``StrategyFactory`` aceita a chave
``risk_profile_by_symbol`` no contexto de criação da estratégia. O valor é um
dicionário que mapeia cada símbolo ao perfil desejado. Quando presente, o
perfil específico do símbolo sobrescreve o ``risk_profile`` global, permitindo
ajustes finos de risco em portfólios com múltiplos ativos.

## Próximos Passos

- **Segmentação por ativos e timeframes** para especializar estratégias em diferentes contextos.
- **Mais testes de backtest** para validar performance antes de operar ao vivo.
- **Ferramentas visuais de acompanhamento** que apresentem sinais e decisões em tempo real.
- **Painel de Sinais** em ``src/qualia/ui/dashboards/signals_panel.py`` exibe os eventos ``strategy.signal`` conforme publicados.
