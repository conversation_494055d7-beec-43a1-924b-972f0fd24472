"""
QUALIA Production Credentials Manager
Secure management of API credentials with encryption, rotation, and validation
"""

import os
import json
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Dict, Optional, Any, List
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import logging
from dataclasses import dataclass, asdict
import asyncio
import aiofiles

logger = logging.getLogger(__name__)


@dataclass
class CredentialInfo:
    """Information about stored credentials"""

    name: str
    created_at: datetime
    last_used: datetime
    last_rotated: datetime
    expires_at: Optional[datetime]
    is_active: bool
    rotation_days: int
    validation_status: str
    checksum: str


class ProductionCredentialsManager:
    """
    Secure credentials manager for production environment
    Features:
    - AES-256 encryption
    - Automatic key rotation
    - Credential validation
    - Audit logging
    - Secure storage
    """

    def __init__(self, config_path: str = "config/production_config.yaml"):
        self.config_path = config_path
        self.credentials_file = "config/.credentials.enc"
        self.key_file = "config/.master.key"
        self.audit_file = "logs/credentials_audit.log"

        # Initialize encryption
        self._master_key = None
        self._fernet = None
        self._credentials_cache = {}

        # Setup directories
        os.makedirs("config", exist_ok=True)
        os.makedirs("logs", exist_ok=True)

        # Initialize encryption system
        self._initialize_encryption()

    def _initialize_encryption(self):
        """Initialize encryption system with master key"""
        try:
            if os.path.exists(self.key_file):
                # Load existing master key
                with open(self.key_file, "rb") as f:
                    self._master_key = f.read()
            else:
                # Generate new master key
                self._master_key = self._generate_master_key()
                with open(self.key_file, "wb") as f:
                    f.write(self._master_key)
                # Secure the key file
                os.chmod(self.key_file, 0o600)

            # Initialize Fernet cipher
            self._fernet = Fernet(self._master_key)
            logger.info("Encryption system initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize encryption: {e}")
            raise

    def _generate_master_key(self) -> bytes:
        """Generate a secure master key using PBKDF2"""
        # Use system entropy for password
        password = secrets.token_bytes(32)
        salt = secrets.token_bytes(16)

        # Derive key using PBKDF2
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))

        # Store salt for future use (in production, this should be more secure)
        with open("config/.salt", "wb") as f:
            f.write(salt)
        os.chmod("config/.salt", 0o600)

        return key

    async def store_credentials(
        self, name: str, credentials: Dict[str, Any], rotation_days: int = 30
    ) -> bool:
        """
        Store encrypted credentials

        Args:
            name: Credential identifier (e.g., 'kucoin_production')
            credentials: Dictionary with credential data
            rotation_days: Days until rotation is required

        Returns:
            bool: Success status
        """
        try:
            # Create credential info
            now = datetime.utcnow()
            expires_at = (
                now + timedelta(days=rotation_days) if rotation_days > 0 else None
            )

            credential_info = CredentialInfo(
                name=name,
                created_at=now,
                last_used=now,
                last_rotated=now,
                expires_at=expires_at,
                is_active=True,
                rotation_days=rotation_days,
                validation_status="pending",
                checksum=self._calculate_checksum(credentials),
            )

            # Load existing credentials
            all_credentials = await self._load_credentials()

            # Encrypt and store
            encrypted_data = self._fernet.encrypt(json.dumps(credentials).encode())
            all_credentials[name] = {
                "data": base64.b64encode(encrypted_data).decode(),
                "info": asdict(credential_info),
            }

            # Save to file
            await self._save_credentials(all_credentials)

            # Update cache
            self._credentials_cache[name] = credentials

            # Audit log
            await self._audit_log("STORE", name, "Credentials stored successfully")

            logger.info(f"Credentials stored successfully: {name}")
            return True

        except Exception as e:
            logger.error(f"Failed to store credentials {name}: {e}")
            await self._audit_log("ERROR", name, f"Failed to store: {e}")
            return False

    async def get_credentials(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve and decrypt credentials

        Args:
            name: Credential identifier

        Returns:
            Dict with credentials or None if not found
        """
        try:
            # Check cache first
            if name in self._credentials_cache:
                await self._update_last_used(name)
                return self._credentials_cache[name]

            # Load from file
            all_credentials = await self._load_credentials()

            if name not in all_credentials:
                logger.warning(f"Credentials not found: {name}")
                return None

            # Decrypt credentials
            encrypted_data = base64.b64decode(all_credentials[name]["data"])
            decrypted_data = self._fernet.decrypt(encrypted_data)
            credentials = json.loads(decrypted_data.decode())

            # Update cache
            self._credentials_cache[name] = credentials

            # Update last used
            await self._update_last_used(name)

            # Audit log
            await self._audit_log("ACCESS", name, "Credentials accessed")

            return credentials

        except Exception as e:
            logger.error(f"Failed to retrieve credentials {name}: {e}")
            await self._audit_log("ERROR", name, f"Failed to retrieve: {e}")
            return None

    async def validate_credentials(self, name: str) -> bool:
        """
        Validate credentials by testing API connection

        Args:
            name: Credential identifier

        Returns:
            bool: Validation status
        """
        try:
            credentials = await self.get_credentials(name)
            if not credentials:
                return False

            # Test credentials based on type
            if "kucoin" in name.lower():
                return await self._validate_kucoin_credentials(credentials)
            if "binance" in name.lower():
                return await self._validate_binance_credentials(credentials)
            else:
                logger.warning(f"Unknown credential type for validation: {name}")
                return False

        except Exception as e:
            logger.error(f"Credential validation failed for {name}: {e}")
            await self._audit_log("VALIDATION_ERROR", name, f"Validation failed: {e}")
            return False

    async def _validate_kucoin_credentials(self, credentials: Dict[str, Any]) -> bool:
        """Validate KuCoin API credentials"""
        try:
            import ccxt

            # Create exchange instance
            exchange = ccxt.kucoin(
                {
                    "apiKey": credentials.get("api_key"),
                    "secret": credentials.get("secret"),
                    "password": credentials.get("passphrase"),
                    "sandbox": credentials.get("sandbox", False),
                    "enableRateLimit": True,
                }
            )

            # Test API call
            balance = await exchange.fetch_balance()

            # Close exchange
            await exchange.close()

            logger.info("KuCoin credentials validated successfully")
            return True

        except Exception as e:
            logger.error(f"KuCoin credential validation failed: {e}")
            return False

    async def _validate_binance_credentials(self, credentials: Dict[str, Any]) -> bool:
        """Validate Binance API credentials."""

        try:
            import ccxt

            exchange = ccxt.binance(
                {
                    "apiKey": credentials.get("api_key"),
                    "secret": credentials.get("secret"),
                    "enableRateLimit": True,
                    "sandbox": credentials.get("sandbox", False),
                }
            )

            await exchange.fetch_balance()
            await exchange.close()

            logger.info("Binance credentials validated successfully")
            return True
        except Exception as e:
            logger.error(f"Binance credential validation failed: {e}")
            return False

    async def rotate_credentials(self, name: str) -> bool:
        """
        Rotate credentials (placeholder for manual rotation)
        In production, this would integrate with exchange APIs

        Args:
            name: Credential identifier

        Returns:
            bool: Success status
        """
        try:
            # Load current credentials
            all_credentials = await self._load_credentials()

            if name not in all_credentials:
                logger.error(f"Cannot rotate non-existent credentials: {name}")
                return False

            # Update rotation timestamp
            all_credentials[name]["info"][
                "last_rotated"
            ] = datetime.utcnow().isoformat()
            all_credentials[name]["info"]["validation_status"] = "rotation_required"

            # Save updated info
            await self._save_credentials(all_credentials)

            # Clear cache to force reload
            if name in self._credentials_cache:
                del self._credentials_cache[name]

            # Audit log
            await self._audit_log("ROTATE", name, "Credentials marked for rotation")

            logger.info(f"Credentials rotation initiated: {name}")
            return True

        except Exception as e:
            logger.error(f"Failed to rotate credentials {name}: {e}")
            return False

    async def list_credentials(self) -> List[CredentialInfo]:
        """List all stored credentials with their info"""
        try:
            all_credentials = await self._load_credentials()

            credential_list = []
            for name, data in all_credentials.items():
                info_dict = data["info"]
                # Convert string dates back to datetime
                for date_field in [
                    "created_at",
                    "last_used",
                    "last_rotated",
                    "expires_at",
                ]:
                    if info_dict.get(date_field):
                        info_dict[date_field] = datetime.fromisoformat(
                            info_dict[date_field]
                        )

                credential_info = CredentialInfo(**info_dict)
                credential_list.append(credential_info)

            return credential_list

        except Exception as e:
            logger.error(f"Failed to list credentials: {e}")
            return []

    async def check_expiring_credentials(self, days_ahead: int = 7) -> List[str]:
        """Check for credentials expiring within specified days"""
        try:
            credentials = await self.list_credentials()
            expiring = []

            cutoff_date = datetime.utcnow() + timedelta(days=days_ahead)

            for cred in credentials:
                if cred.expires_at and cred.expires_at <= cutoff_date:
                    expiring.append(cred.name)

            return expiring

        except Exception as e:
            logger.error(f"Failed to check expiring credentials: {e}")
            return []

    async def _load_credentials(self) -> Dict[str, Any]:
        """Load credentials from encrypted file"""
        try:
            if not os.path.exists(self.credentials_file):
                return {}

            async with aiofiles.open(self.credentials_file, "rb") as f:
                encrypted_content = await f.read()

            if not encrypted_content:
                return {}

            # Decrypt content
            decrypted_content = self._fernet.decrypt(encrypted_content)
            return json.loads(decrypted_content.decode())

        except Exception as e:
            logger.error(f"Failed to load credentials: {e}")
            return {}

    async def _save_credentials(self, credentials: Dict[str, Any]):
        """Save credentials to encrypted file"""
        try:
            # Convert datetime objects to strings for JSON serialization
            serializable_creds = self._make_serializable(credentials)

            # Encrypt content
            content = json.dumps(serializable_creds, indent=2)
            encrypted_content = self._fernet.encrypt(content.encode())

            # Save to file
            async with aiofiles.open(self.credentials_file, "wb") as f:
                await f.write(encrypted_content)

            # Secure the file
            os.chmod(self.credentials_file, 0o600)

        except Exception as e:
            logger.error(f"Failed to save credentials: {e}")
            raise

    def _make_serializable(self, obj):
        """Convert datetime objects to strings for JSON serialization"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, datetime):
            return obj.isoformat()
        else:
            return obj

    async def _update_last_used(self, name: str):
        """Update last used timestamp for credentials"""
        try:
            all_credentials = await self._load_credentials()
            if name in all_credentials:
                all_credentials[name]["info"][
                    "last_used"
                ] = datetime.utcnow().isoformat()
                await self._save_credentials(all_credentials)
        except Exception as e:
            logger.error(f"Failed to update last used for {name}: {e}")

    def _calculate_checksum(self, data: Dict[str, Any]) -> str:
        """Calculate SHA-256 checksum of credential data"""
        content = json.dumps(data, sort_keys=True)
        return hashlib.sha256(content.encode()).hexdigest()

    async def _audit_log(self, action: str, credential_name: str, message: str):
        """Log credential operations for audit trail"""
        try:
            timestamp = datetime.utcnow().isoformat()
            log_entry = {
                "timestamp": timestamp,
                "action": action,
                "credential": credential_name,
                "message": message,
                "user": os.getenv("USER", "system"),
            }

            async with aiofiles.open(self.audit_file, "a") as f:
                await f.write(json.dumps(log_entry) + "\n")

        except Exception as e:
            logger.error(f"Failed to write audit log: {e}")


# Global instance for production use
production_credentials = ProductionCredentialsManager()


# Convenience functions
async def store_production_credentials(name: str, credentials: Dict[str, Any]) -> bool:
    """Store production credentials"""
    return await production_credentials.store_credentials(name, credentials)


async def get_production_credentials(name: str) -> Optional[Dict[str, Any]]:
    """Get production credentials"""
    return await production_credentials.get_credentials(name)


async def validate_production_credentials(name: str) -> bool:
    """Validate production credentials"""
    return await production_credentials.validate_credentials(name)
