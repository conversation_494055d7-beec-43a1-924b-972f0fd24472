"""Utility to validate YAML configuration files."""

from pathlib import Path
from qualia.utils.config_loader import load_config_file


def load_strategy_parameters() -> dict:
    """Load and parse ``strategy_parameters.yaml``."""

    config_path = (
        Path(__file__).resolve().parents[1] / "config" / "strategy_parameters.yaml"
    )
    return load_config_file(str(config_path))


if __name__ == "__main__":
    load_strategy_parameters()
