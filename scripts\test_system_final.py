#!/usr/bin/env python3
"""
Teste final do sistema QUALIA com configurações ultra conservadoras
"""

import os
import sys
import subprocess

def setup_conservative_env():
    """Configura variáveis de ambiente ultra conservadoras"""
    print("🔧 CONFIGURANDO AMBIENTE ULTRA CONSERVADOR")
    
    # Timeouts muito altos
    os.environ['TICKER_TIMEOUT'] = '45'
    os.environ['OHLCV_TIMEOUT'] = '120' 
    os.environ['RATE_LIMIT'] = '5.0'
    
    # Cache mais agressivo
    os.environ['TICKER_CACHE_TTL'] = '30'
    os.environ['OHLCV_CACHE_TTL'] = '300'
    
    # Circuit breaker mais tolerante
    os.environ['API_FAIL_THRESHOLD'] = '1'
    os.environ['API_RECOVERY_TIMEOUT'] = '20'
    
    print("✅ Ambiente configurado")

def run_system_test():
    """Executa teste do sistema"""
    print("\n🚀 EXECUTANDO SISTEMA QUALIA (10s)")
    
    cmd = [
        sys.executable, "-m", "src.qualia.qualia_trading_system",
        "--symbols", "BTC/USDT",
        "--timeframes", "5m", 
        "--capital", "1000",
        "--mode", "paper_trading",
        "--duration_seconds", "10",
        "--log_level", "INFO",
        "--strategy_config_path", "config/strategy_parameters.json",
        "--risk_profile", "aggressive",
        "--data_source", "kucoin",
        "--skip_preflight"  # Pular preflight para acelerar
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if "✅" in result.stdout or "Sistema principal de trading" in result.stdout:
            print("✅ SISTEMA EXECUTOU COM SUCESSO")
            return True
        else:
            print("⚠️ Sistema executou mas com problemas")
            print("STDOUT:", result.stdout[-500:])  # Últimas 500 chars
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Sistema travou (timeout 60s)")
        return False
    except Exception as e:
        print(f"❌ Erro ao executar: {e}")
        return False

def main():
    """Função principal"""
    setup_conservative_env()
    success = run_system_test()
    
    if success:
        print("\n🎉 QUALIA TRADING SYSTEM FUNCIONANDO!")
        print("🔥 Sistema pronto para produção com configurações conservadoras")
    else:
        print("\n⚠️ Sistema precisa de mais ajustes")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 