"""
Structured Logging System for QUALIA
Provides secure, structured, and efficient logging with sensitive data protection.
"""

import logging
import json
import re
import sys
import os
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
import threading
from dataclasses import dataclass, asdict

# Sensitive data patterns to redact
SENSITIVE_PATTERNS = [
    # API Keys and secrets
    (re.compile(r'(api[_-]?key["\']?\s*[:=]\s*["\']?)([^"\']+)', re.IGNORECASE), r'\1***REDACTED***'),
    (re.compile(r'(secret["\']?\s*[:=]\s*["\']?)([^"\']+)', re.IGNORECASE), r'\1***REDACTED***'),
    (re.compile(r'(passphrase["\']?\s*[:=]\s*["\']?)([^"\']+)', re.IGNORECASE), r'\1***REDACTED***'),
    (re.compile(r'(password["\']?\s*[:=]\s*["\']?)([^"\']+)', re.IGNORECASE), r'\1***REDACTED***'),
    (re.compile(r'(token["\']?\s*[:=]\s*["\']?)([^"\']+)', re.IGNORECASE), r'\1***REDACTED***'),
    
    # Environment variable references
    (re.compile(r'(\$\{[A-Z_]*(?:KEY|SECRET|PASS|TOKEN)[A-Z_]*\})', re.IGNORECASE), r'***ENV_VAR***'),
    
    # Potential private keys or long hex strings
    (re.compile(r'([a-fA-F0-9]{32,})'), lambda m: f"***{len(m.group(1))}CHARS***"),
    
    # Credit card patterns (basic)
    (re.compile(r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b'), r'***CARD***'),
    
    # Email addresses (partial redaction)
    (re.compile(r'([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'), r'***@\2'),
]

# ANSI escape sequence pattern for cleaning terminal colors
ANSI_ESCAPE_PATTERN = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')


@dataclass
class LogContext:
    """Context information for structured logging"""
    correlation_id: Optional[str] = None
    trace_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    component: Optional[str] = None
    operation: Optional[str] = None
    symbol: Optional[str] = None
    exchange: Optional[str] = None


class SensitiveDataRedactor:
    """Redacts sensitive information from log messages"""
    
    def __init__(self, additional_patterns: Optional[List[tuple]] = None):
        self.patterns = SENSITIVE_PATTERNS.copy()
        if additional_patterns:
            self.patterns.extend(additional_patterns)
    
    def redact(self, text: str) -> str:
        """Redact sensitive information from text"""
        if not isinstance(text, str):
            text = str(text)

        for pattern, replacement in self.patterns:
            if callable(replacement):
                # For callable replacements, we need to handle the match object
                def repl_func(match):
                    return replacement(match)
                text = pattern.sub(repl_func, text)
            else:
                text = pattern.sub(replacement, text)

        return text


class StructuredFormatter(logging.Formatter):
    """Structured JSON formatter with sensitive data protection"""
    
    def __init__(self, 
                 include_extra: bool = True,
                 redact_sensitive: bool = True,
                 compact: bool = False):
        super().__init__()
        self.include_extra = include_extra
        self.redact_sensitive = redact_sensitive
        self.compact = compact
        self.redactor = SensitiveDataRedactor() if redact_sensitive else None
        
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON"""
        
        # Get message and redact sensitive data first
        message = record.getMessage()
        if self.redactor:
            message = self.redactor.redact(message)

        # Get basic log data
        log_data = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': message,
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # Add process and thread info
        log_data.update({
            'process_id': os.getpid(),
            'thread_id': threading.get_ident(),
            'thread_name': threading.current_thread().name,
        })
        
        # Add exception info if present
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields from record
        if self.include_extra:
            extra_fields = {}
            for key, value in record.__dict__.items():
                if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 
                              'pathname', 'filename', 'module', 'lineno', 
                              'funcName', 'created', 'msecs', 'relativeCreated',
                              'thread', 'threadName', 'processName', 'process',
                              'getMessage', 'exc_info', 'exc_text', 'stack_info']:
                    extra_fields[key] = value
            
            if extra_fields:
                log_data['extra'] = extra_fields
        
        # Redact sensitive information from extra fields
        if self.redactor:
            log_data = self._redact_dict(log_data)

        # Clean ANSI escape sequences from message
        log_data['message'] = ANSI_ESCAPE_PATTERN.sub('', log_data['message'])
        
        # Format as JSON
        try:
            if self.compact:
                return json.dumps(log_data, separators=(',', ':'), ensure_ascii=False)
            else:
                return json.dumps(log_data, indent=None, ensure_ascii=False)
        except (TypeError, ValueError) as e:
            # Fallback for non-serializable objects
            log_data['message'] = f"[SERIALIZATION_ERROR] {str(e)}: {log_data['message']}"
            return json.dumps({
                'timestamp': log_data['timestamp'],
                'level': 'ERROR',
                'logger': 'structured_logging',
                'message': log_data['message']
            })
    
    def _redact_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively redact sensitive data from dictionary"""
        if isinstance(data, dict):
            return {k: self._redact_dict(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._redact_dict(item) for item in data]
        elif isinstance(data, str):
            return self.redactor.redact(data)
        else:
            return data


class ConsoleFormatter(logging.Formatter):
    """Human-readable console formatter with colors and sensitive data protection"""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',     # Cyan
        'INFO': '\033[32m',      # Green
        'WARNING': '\033[33m',   # Yellow
        'ERROR': '\033[31m',     # Red
        'CRITICAL': '\033[35m',  # Magenta
        'RESET': '\033[0m'       # Reset
    }
    
    def __init__(self, 
                 use_colors: bool = None,
                 redact_sensitive: bool = True,
                 compact: bool = False):
        super().__init__()
        self.use_colors = use_colors if use_colors is not None else sys.stdout.isatty()
        self.redact_sensitive = redact_sensitive
        self.compact = compact
        self.redactor = SensitiveDataRedactor() if redact_sensitive else None
        
    def format(self, record: logging.LogRecord) -> str:
        """Format log record for console output"""
        
        # Get message and redact if needed
        message = record.getMessage()
        if self.redactor:
            message = self.redactor.redact(message)
        
        # Clean ANSI escape sequences from message
        message = ANSI_ESCAPE_PATTERN.sub('', message)
        
        # Format timestamp
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        
        # Get level with color
        level = record.levelname
        if self.use_colors and level in self.COLORS:
            level = f"{self.COLORS[level]}{level}{self.COLORS['RESET']}"
        
        # Format based on compact mode
        if self.compact:
            return f"{level[0]} {message}"
        else:
            # Include context if available
            context_parts = []
            if hasattr(record, 'symbol'):
                context_parts.append(f"symbol={record.symbol}")
            if hasattr(record, 'component'):
                context_parts.append(f"component={record.component}")
            if hasattr(record, 'operation'):
                context_parts.append(f"op={record.operation}")
            
            context_str = f" [{', '.join(context_parts)}]" if context_parts else ""
            
            return f"{timestamp} - {record.name} - {level}{context_str} - {message}"


class QualiaLogger:
    """Enhanced logger with structured logging capabilities"""
    
    def __init__(self, name: str, context: Optional[LogContext] = None):
        self.logger = logging.getLogger(name)
        self.context = context or LogContext()
        
    def _log_with_context(self, level: int, msg: str, *args, **kwargs):
        """Log message with context information"""
        extra = kwargs.pop('extra', {})
        
        # Add context to extra
        if self.context:
            context_dict = asdict(self.context)
            for key, value in context_dict.items():
                if value is not None:
                    extra[key] = value
        
        # Add any additional context from kwargs
        for key in ['symbol', 'component', 'operation', 'exchange', 'correlation_id']:
            if key in kwargs:
                extra[key] = kwargs.pop(key)
        
        kwargs['extra'] = extra
        self.logger.log(level, msg, *args, **kwargs)
    
    def debug(self, msg: str, *args, **kwargs):
        self._log_with_context(logging.DEBUG, msg, *args, **kwargs)
    
    def info(self, msg: str, *args, **kwargs):
        self._log_with_context(logging.INFO, msg, *args, **kwargs)
    
    def warning(self, msg: str, *args, **kwargs):
        self._log_with_context(logging.WARNING, msg, *args, **kwargs)
    
    def error(self, msg: str, *args, **kwargs):
        self._log_with_context(logging.ERROR, msg, *args, **kwargs)
    
    def critical(self, msg: str, *args, **kwargs):
        self._log_with_context(logging.CRITICAL, msg, *args, **kwargs)
    
    def set_context(self, **kwargs):
        """Update logging context"""
        for key, value in kwargs.items():
            if hasattr(self.context, key):
                setattr(self.context, key, value)


def configure_structured_logging(
    level: str = "INFO",
    console_enabled: bool = True,
    file_enabled: bool = True,
    file_path: Optional[Path] = None,
    structured_format: bool = True,
    redact_sensitive: bool = True,
    compact_console: bool = False,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> None:
    """Configure structured logging for QUALIA system"""
    
    # Clear existing handlers
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Set root level
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    root_logger.setLevel(numeric_level)
    
    handlers = []
    
    # Console handler
    if console_enabled:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        
        if structured_format:
            console_handler.setFormatter(StructuredFormatter(
                compact=compact_console,
                redact_sensitive=redact_sensitive
            ))
        else:
            console_handler.setFormatter(ConsoleFormatter(
                compact=compact_console,
                redact_sensitive=redact_sensitive
            ))
        
        handlers.append(console_handler)
    
    # File handler
    if file_enabled:
        if file_path is None:
            file_path = Path("logs/qualia_structured.log")
        
        # Ensure log directory exists
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            file_path,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(StructuredFormatter(
            redact_sensitive=redact_sensitive,
            compact=True
        ))
        
        handlers.append(file_handler)
    
    # Add handlers to root logger
    for handler in handlers:
        root_logger.addHandler(handler)
    
    # Configure external library loggers
    external_loggers = {
        'qiskit': logging.WARNING,
        'qiskit.compiler': logging.ERROR,
        'qiskit.passmanager': logging.ERROR,
        'matplotlib': logging.WARNING,
        'urllib3': logging.WARNING,
        'requests': logging.WARNING,
        'ccxt': logging.WARNING,
        'asyncio': logging.WARNING,
    }
    
    for logger_name, logger_level in external_loggers.items():
        logging.getLogger(logger_name).setLevel(logger_level)


def get_qualia_logger(name: str, context: Optional[LogContext] = None) -> QualiaLogger:
    """Get a QUALIA logger with structured logging capabilities"""
    return QualiaLogger(name, context)


# Convenience function for backward compatibility
def setup_logging(config: Optional[Dict[str, Any]] = None) -> None:
    """Setup structured logging with configuration"""
    if config is None:
        config = {}
    
    configure_structured_logging(
        level=config.get('level', 'INFO'),
        console_enabled=config.get('console', True),
        file_enabled=config.get('file_enabled', True),
        file_path=Path(config['file']) if 'file' in config else None,
        structured_format=config.get('structured', True),
        redact_sensitive=config.get('redact_sensitive', True),
        compact_console=config.get('compact_console', False),
        max_file_size=config.get('max_file_size', 10 * 1024 * 1024),
        backup_count=config.get('backup_count', 5)
    )
