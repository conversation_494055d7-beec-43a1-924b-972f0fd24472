"""
QUALIA D-06: System Integration
===============================

Integração completa dos componentes D-06:
- Market Regime Detector
- A/B Testing Framework  
- Regime-aware Configuration Manager

Orquestra todos os componentes para funcionar em harmonia.
"""

from __future__ import annotations

import asyncio
import logging
import numpy as np
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field

from ..utils.logger import get_logger
from ..utils.event_bus import EventBus
from ..config.hot_reload import ConfigurationHotReloader
from ..market.regime_detector import MarketRegimeDetector, RegimeDetectorConfig
from ..testing.ab_testing_framework import ABTestingFramework, ABTestConfig, ABTestMetrics
from ..config.regime_aware_config import RegimeAwareConfigManager, RegimeAwareConfig

logger = get_logger(__name__)


@dataclass
class D06IntegrationConfig:
    """Configuração da integração D-06."""
    # Componentes habilitados
    regime_detection_enabled: bool = True
    ab_testing_enabled: bool = True
    regime_aware_config_enabled: bool = True
    
    # Configurações específicas
    regime_detector_config: Optional[RegimeDetectorConfig] = None
    regime_aware_config: Optional[RegimeAwareConfig] = None
    
    # Integração com live feed
    live_feed_integration: bool = True
    monitored_symbols: List[str] = field(default_factory=lambda: ["BTC/USDT", "ETH/USDT"])
    
    # Métricas e monitoramento
    metrics_collection_interval: int = 60  # segundos
    performance_validation_interval: int = 300  # segundos
    
    # Event bus
    event_bus_enabled: bool = True
    
    # Logging
    detailed_logging: bool = True
    log_regime_changes: bool = True
    log_ab_test_events: bool = True


class D06SystemIntegration:
    """
    Integração completa do sistema D-06.
    
    Funcionalidades:
    - Orquestração de todos os componentes D-06
    - Integração com live feed para dados em tempo real
    - Coleta automática de métricas para A/B testing
    - Coordenação entre detecção de regime e configuração adaptativa
    - Monitoramento unificado e logging
    - Event bus para comunicação entre componentes
    """
    
    def __init__(self,
                 hot_reloader: ConfigurationHotReloader,
                 feed_manager: Optional[FeedManager] = None,
                 config: Optional[D06IntegrationConfig] = None):
        self.hot_reloader = hot_reloader
        self.feed_manager = feed_manager
        self.config = config or D06IntegrationConfig()
        
        # Event bus
        self.event_bus = EventBus() if self.config.event_bus_enabled else None
        
        # Componentes D-06
        self.regime_detector: Optional[MarketRegimeDetector] = None
        self.ab_framework: Optional[ABTestingFramework] = None
        self.regime_config_manager: Optional[RegimeAwareConfigManager] = None
        
        # Estado de execução
        self.is_running = False
        self.monitoring_task: Optional[asyncio.Task] = None
        self.metrics_task: Optional[asyncio.Task] = None
        
        # Callbacks e métricas
        self.performance_callbacks: List[Callable[[], Dict[str, float]]] = []
        self.current_metrics: Dict[str, float] = {}
        
        logger.info("✅ D06SystemIntegration inicializado")
    
    async def initialize(self):
        """Inicializa todos os componentes D-06."""
        try:
            logger.info("🚀 Inicializando sistema D-06...")
            
            # Inicializar componentes conforme configuração
            if self.config.regime_detection_enabled:
                await self._initialize_regime_detector()
            
            if self.config.ab_testing_enabled:
                await self._initialize_ab_framework()
            
            if self.config.regime_aware_config_enabled:
                await self._initialize_regime_config_manager()
            
            # Configurar integrações
            await self._setup_integrations()
            
            logger.info("✅ Sistema D-06 inicializado com sucesso")
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar sistema D-06: {e}")
            raise
    
    async def start(self):
        """Inicia todos os componentes D-06."""
        if self.is_running:
            logger.warning("Sistema D-06 já está rodando")
            return
        
        try:
            # Inicializar se necessário
            if not self._components_initialized():
                await self.initialize()
            
            # Iniciar componentes
            if self.regime_detector:
                await self.regime_detector.start()
            
            if self.ab_framework:
                await self.ab_framework.start()
            
            if self.regime_config_manager:
                await self.regime_config_manager.start()
            
            # Iniciar tasks de monitoramento
            self.is_running = True
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            self.metrics_task = asyncio.create_task(self._metrics_collection_loop())
            
            logger.info("🚀 Sistema D-06 iniciado")
            
        except Exception as e:
            logger.error(f"❌ Erro ao iniciar sistema D-06: {e}")
            raise
    
    async def stop(self):
        """Para todos os componentes D-06."""
        self.is_running = False
        
        try:
            # Parar tasks de monitoramento
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
            
            if self.metrics_task:
                self.metrics_task.cancel()
                try:
                    await self.metrics_task
                except asyncio.CancelledError:
                    pass
            
            # Parar componentes
            if self.regime_config_manager:
                await self.regime_config_manager.stop()
            
            if self.ab_framework:
                await self.ab_framework.stop()
            
            if self.regime_detector:
                await self.regime_detector.stop()
            
            logger.info("⏹️ Sistema D-06 parado")
            
        except Exception as e:
            logger.error(f"❌ Erro ao parar sistema D-06: {e}")
    
    def register_performance_callback(self, callback: Callable[[], Dict[str, float]]):
        """Registra callback para coleta de métricas de performance."""
        self.performance_callbacks.append(callback)
        
        # Registrar nos componentes que precisam
        if self.ab_framework:
            self.ab_framework.register_metrics_callback(self._create_ab_metrics_callback(callback))
        
        if self.regime_config_manager:
            self.regime_config_manager.register_performance_callback(callback)
        
        logger.debug(f"Callback de performance registrado: {callback.__name__}")
    
    async def create_ab_test(self, config: ABTestConfig) -> str:
        """Cria um novo teste A/B."""
        if not self.ab_framework:
            raise RuntimeError("A/B Testing Framework não está inicializado")
        
        test_id = await self.ab_framework.create_test(config)
        
        if self.config.log_ab_test_events:
            logger.info(f"📊 Teste A/B criado: {config.name} (ID: {test_id})")
        
        return test_id
    
    async def start_ab_test(self, test_id: str) -> bool:
        """Inicia um teste A/B."""
        if not self.ab_framework:
            raise RuntimeError("A/B Testing Framework não está inicializado")
        
        success = await self.ab_framework.start_test(test_id)
        
        if success and self.config.log_ab_test_events:
            logger.info(f"🚀 Teste A/B iniciado: {test_id}")
        
        return success
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Retorna status completo do sistema D-06."""
        status = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'is_running': self.is_running,
            'components': {
                'regime_detector': {
                    'enabled': self.config.regime_detection_enabled,
                    'initialized': self.regime_detector is not None,
                    'running': self.regime_detector.is_running if self.regime_detector else False
                },
                'ab_framework': {
                    'enabled': self.config.ab_testing_enabled,
                    'initialized': self.ab_framework is not None,
                    'running': self.ab_framework.is_running if self.ab_framework else False
                },
                'regime_config_manager': {
                    'enabled': self.config.regime_aware_config_enabled,
                    'initialized': self.regime_config_manager is not None,
                    'running': self.regime_config_manager.is_running if self.regime_config_manager else False
                }
            },
            'current_metrics': self.current_metrics.copy(),
            'monitored_symbols': self.config.monitored_symbols.copy()
        }
        
        # Adicionar status específico dos componentes
        if self.regime_detector:
            status['regime_detector_status'] = self.regime_detector.get_regime_summary()
        
        if self.ab_framework:
            status['active_ab_tests'] = await self.ab_framework.list_tests(include_completed=False)
        
        if self.regime_config_manager:
            status['regime_config_status'] = self.regime_config_manager.get_status()
        
        return status
    
    async def _initialize_regime_detector(self):
        """Inicializa o detector de regimes."""
        try:
            config = self.config.regime_detector_config or RegimeDetectorConfig()
            self.regime_detector = MarketRegimeDetector(
                config=config,
                event_bus=self.event_bus
            )
            
            # Configurar integração com live feed
            if self.feed_manager and self.config.live_feed_integration:
                self.feed_manager.set_ticker_callback(self.regime_detector.on_ticker_update)
            
            logger.info("✅ MarketRegimeDetector inicializado")
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar MarketRegimeDetector: {e}")
            raise
    
    async def _initialize_ab_framework(self):
        """Inicializa o framework de A/B testing."""
        try:
            self.ab_framework = ABTestingFramework(
                hot_reloader=self.hot_reloader,
                event_bus=self.event_bus
            )
            
            logger.info("✅ ABTestingFramework inicializado")
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar ABTestingFramework: {e}")
            raise
    
    async def _initialize_regime_config_manager(self):
        """Inicializa o gerenciador de configurações regime-aware."""
        try:
            if not self.regime_detector:
                raise RuntimeError("MarketRegimeDetector deve ser inicializado primeiro")
            
            config = self.config.regime_aware_config or RegimeAwareConfig()
            self.regime_config_manager = RegimeAwareConfigManager(
                regime_detector=self.regime_detector,
                hot_reloader=self.hot_reloader,
                config=config,
                event_bus=self.event_bus
            )
            
            logger.info("✅ RegimeAwareConfigManager inicializado")
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar RegimeAwareConfigManager: {e}")
            raise

    async def _setup_integrations(self):
        """Configura integrações entre componentes."""
        try:
            # Configurar event bus callbacks
            if self.event_bus:
                self._setup_event_handlers()

            # Configurar callbacks de performance
            for callback in self.performance_callbacks:
                if self.regime_config_manager:
                    self.regime_config_manager.register_performance_callback(callback)

                if self.ab_framework:
                    self.ab_framework.register_metrics_callback(self._create_ab_metrics_callback(callback))

            logger.info("✅ Integrações configuradas")

        except Exception as e:
            logger.error(f"❌ Erro ao configurar integrações: {e}")
            raise

    def _setup_event_handlers(self):
        """Configura handlers do event bus."""
        if not self.event_bus:
            return

        # Handler para mudanças de regime
        self.event_bus.subscribe("regime.changed", self._on_regime_changed)

        # Handler para conclusão de testes A/B
        self.event_bus.subscribe("ab_test.completed", self._on_ab_test_completed)

        # Handler para mudanças de configuração regime-aware
        self.event_bus.subscribe("regime_config.changed", self._on_regime_config_changed)

        # Handler para rollbacks
        self.event_bus.subscribe("regime_config.rollback", self._on_regime_config_rollback)

        logger.debug("Event handlers configurados")

    async def _on_regime_changed(self, event_data: Dict[str, Any]):
        """Handler para mudanças de regime."""
        if self.config.log_regime_changes:
            logger.info(f"📈 Regime alterado: {event_data.get('from_regime')} → {event_data.get('to_regime')} "
                       f"(confiança: {event_data.get('confidence', 0):.2f})")

    async def _on_ab_test_completed(self, event_data: Dict[str, Any]):
        """Handler para conclusão de testes A/B."""
        if self.config.log_ab_test_events:
            logger.info(f"📊 Teste A/B concluído: {event_data.get('test_name')} "
                       f"(vencedor: {event_data.get('winner')}, confiança: {event_data.get('confidence', 0):.2f})")

    async def _on_regime_config_changed(self, event_data: Dict[str, Any]):
        """Handler para mudanças de configuração regime-aware."""
        logger.info(f"⚙️ Configuração regime-aware alterada: {event_data.get('preset_applied')} "
                   f"para regime {event_data.get('to_regime')}")

    async def _on_regime_config_rollback(self, event_data: Dict[str, Any]):
        """Handler para rollbacks de configuração."""
        logger.warning(f"🔙 Rollback executado: {event_data.get('reason')} "
                      f"(preset: {event_data.get('preset_rolled_back')})")

    def _create_ab_metrics_callback(self, performance_callback: Callable[[], Dict[str, float]]) -> Callable[[], ABTestMetrics]:
        """Cria callback de métricas para A/B testing."""
        def ab_metrics_callback() -> ABTestMetrics:
            try:
                metrics = performance_callback()

                return ABTestMetrics(
                    timestamp=datetime.now(timezone.utc),
                    pnl_24h=metrics.get('pnl_24h', 0.0),
                    sharpe_ratio=metrics.get('sharpe_ratio', 0.0),
                    max_drawdown=metrics.get('max_drawdown', 0.0),
                    win_rate=metrics.get('win_rate', 0.0),
                    total_trades=int(metrics.get('total_trades', 0)),
                    avg_trade_duration=metrics.get('avg_trade_duration', 0.0),
                    volatility=metrics.get('volatility', 0.0),
                    custom_metrics=metrics
                )
            except Exception as e:
                logger.error(f"Erro ao coletar métricas para A/B testing: {e}")
                return ABTestMetrics(timestamp=datetime.now(timezone.utc))

        return ab_metrics_callback

    def _components_initialized(self) -> bool:
        """Verifica se todos os componentes habilitados foram inicializados."""
        if self.config.regime_detection_enabled and not self.regime_detector:
            return False

        if self.config.ab_testing_enabled and not self.ab_framework:
            return False

        if self.config.regime_aware_config_enabled and not self.regime_config_manager:
            return False

        return True

    async def _monitoring_loop(self):
        """Loop de monitoramento geral do sistema."""
        while self.is_running:
            try:
                # Verificar saúde dos componentes
                await self._check_component_health()

                # Aguardar próxima verificação
                await asyncio.sleep(60)  # Verificar a cada minuto

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Erro no loop de monitoramento D-06: {e}")
                await asyncio.sleep(10)

    async def _metrics_collection_loop(self):
        """Loop de coleta de métricas."""
        while self.is_running:
            try:
                # Coletar métricas atuais
                await self._collect_current_metrics()

                # Aguardar próxima coleta
                await asyncio.sleep(self.config.metrics_collection_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Erro na coleta de métricas D-06: {e}")
                await asyncio.sleep(30)

    async def _check_component_health(self):
        """Verifica saúde dos componentes."""
        try:
            # Verificar regime detector
            if self.regime_detector and not self.regime_detector.is_running:
                logger.warning("⚠️ MarketRegimeDetector não está rodando")

            # Verificar A/B framework
            if self.ab_framework and not self.ab_framework.is_running:
                logger.warning("⚠️ ABTestingFramework não está rodando")

            # Verificar regime config manager
            if self.regime_config_manager and not self.regime_config_manager.is_running:
                logger.warning("⚠️ RegimeAwareConfigManager não está rodando")

        except Exception as e:
            logger.error(f"Erro ao verificar saúde dos componentes: {e}")

    async def _collect_current_metrics(self):
        """Coleta métricas atuais do sistema."""
        try:
            # Coletar de todos os callbacks registrados
            collected_metrics = {}

            for callback in self.performance_callbacks:
                try:
                    metrics = callback()
                    collected_metrics.update(metrics)
                except Exception as e:
                    logger.error(f"Erro em callback de métricas: {e}")

            # Atualizar métricas atuais
            self.current_metrics = collected_metrics

            # Log periódico das métricas (opcional)
            if self.config.detailed_logging and collected_metrics:
                logger.debug(f"📊 Métricas atuais: {collected_metrics}")

        except Exception as e:
            logger.error(f"Erro ao coletar métricas: {e}")


# Função de conveniência para criar integração global
_global_integration: Optional[D06SystemIntegration] = None

def get_global_d06_integration(hot_reloader: ConfigurationHotReloader,
                              feed_manager: Optional[FeedManager] = None,
                              config: Optional[D06IntegrationConfig] = None) -> D06SystemIntegration:
    """Retorna instância global da integração D-06."""
    global _global_integration

    if _global_integration is None:
        _global_integration = D06SystemIntegration(
            hot_reloader=hot_reloader,
            feed_manager=feed_manager,
            config=config
        )

    return _global_integration


# Função de inicialização rápida
async def initialize_d06_system(hot_reloader: ConfigurationHotReloader,
                               feed_manager: Optional[FeedManager] = None,
                               config: Optional[D06IntegrationConfig] = None,
                               performance_callbacks: Optional[List[Callable[[], Dict[str, float]]]] = None) -> D06SystemIntegration:
    """
    Inicializa e inicia o sistema D-06 completo.

    Args:
        hot_reloader: Sistema de hot-reload de configurações
        feed_manager: Gerenciador de feed de dados (opcional)
        config: Configuração da integração (opcional)
        performance_callbacks: Callbacks de métricas de performance (opcional)

    Returns:
        Instância do sistema D-06 inicializada e rodando
    """
    try:
        # Criar integração
        integration = D06SystemIntegration(
            hot_reloader=hot_reloader,
            feed_manager=feed_manager,
            config=config
        )

        # Registrar callbacks de performance
        if performance_callbacks:
            for callback in performance_callbacks:
                integration.register_performance_callback(callback)

        # Inicializar e iniciar
        await integration.initialize()
        await integration.start()

        logger.info("🎉 Sistema D-06 inicializado e iniciado com sucesso!")
        return integration

    except Exception as e:
        logger.error(f"❌ Erro ao inicializar sistema D-06: {e}")
        raise
