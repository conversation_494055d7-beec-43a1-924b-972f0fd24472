{"cells": [{"cell_type": "markdown", "id": "3110c7e6", "metadata": {}, "source": ["# Visualização de Resultados"]}, {"cell_type": "code", "execution_count": null, "id": "91c27ac7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "bench_df = pd.read_json('benchmarks/results/benchmark_results.jsonl', lines=True)\n", "mitig_df = pd.read_json('experiments/mitigation_results.jsonl', lines=True)\n", "\n", "if not bench_df.empty and not mitig_df.empty:\n", "    fig, ax = plt.subplots(figsize=(6, 4))\n", "    ax.plot(mitig_df.index, mitig_df['fidelity_delta'], label='Δ Fidelidade')\n", "    ax.set_xlabel('Execução')\n", "    ax.set_ylabel('Fidelidade')\n", "    ax.legend()\n", "    plt.show()\n", "else:\n", "    print('Arquivos de resultados ausentes ou vazios.')\n"]}], "metadata": {}, "nbformat": 4, "nbformat_minor": 5}