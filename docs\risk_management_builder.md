# Gerenciadores de Risco Personalizados

O módulo `risk_manager_builder` oferece uma pequena fábrica para registrar e instanciar implementações de gerenciadores de risco. Duas classes são fornecidas por padrão (`SimpleRiskManager` e `AdvancedRiskManager`), mas qualquer classe que herde de `QUALIARiskManagerBase` pode ser incluída no registro.

## Registrando novos gerenciadores

1. Crie uma subclasse de `QUALIARiskManagerBase` implementando os métodos obrigatórios.
2. Utilize `register_risk_manager(alias, cls)` informando um alias único para a sua classe.

```python
from qualia.risk_management import register_risk_manager
from qualia.risk_management.risk_manager_base import QUALIARiskManagerBase

class CustomRiskManager(QUALIARiskManagerBase):
    def calculate_position_size(
        self,
        symbol,
        current_price,
        stop_loss_price,
        confidence=0.5,
        volatility=None,
        min_lot_size=None,
        informational_mass=None,
        initial_informational_mass=None,
        lambda_factor=None,
    ):
        return {"position_allowed": True, "position_size": 0.0, "quantity": 0.0}

    def update_capital(self, new_capital):
        self.current_capital = new_capital
        return {"current_capital": self.current_capital}

    def can_open_new_position(self, current_positions):
        return True, "ok"

    def process_trade_result(self, trade_info):
        return self.update_capital(self.current_capital)

register_risk_manager("custom", CustomRiskManager)
```

O registro é protegido por `threading.Lock`, evitando condições de corrida quando múltiplas threads adicionam ou instanciam gerenciadores simultaneamente.

## Criando instâncias

Depois de registrar a classe, chame `create_risk_manager(alias, **kwargs)` para obter uma instância. Os parâmetros serão repassados diretamente ao construtor da classe registrada.

```python
from qualia.risk_management import create_risk_manager

risk_manager = create_risk_manager("custom", initial_capital=1000.0)
```

Outras funções úteis:

- `get_risk_manager_class(alias)` retorna a classe associada ao alias.
- `get_registered_risk_managers()` devolve o dicionário completo de registros.

Consulte [risk_management.md](risk_management.md) para detalhes sobre perfis e parâmetros de risco.
