"""
QUALIA Live Feed - Executor de Testes Flexível

<PERSON> para executar testes específicos do sistema de live feed com opções avançadas.
"""

import asyncio
import argparse
import json
import os
import sys
from datetime import datetime
from typing import List, Optional

from .test_suite import LiveFeedTestSuite, TestSuiteResults
from ..utils.logger import get_logger

logger = get_logger(__name__)


class TestRunner:
    """Executor flexível de testes."""
    
    def __init__(self):
        self.available_tests = [
            'credentials',
            'normalizer', 
            'aggregator',
            'rest',
            'websocket',
            'manager',
            'performance',
            'errors',
            'reconnection',
            'alerts'
        ]
    
    def parse_arguments(self):
        """Parse argumentos da linha de comando."""
        parser = argparse.ArgumentParser(
            description='QUALIA Live Feed Test Runner',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Exemplos de uso:
  python run_tests.py                          # Executar todos os testes
  python run_tests.py --sandbox-only           # Apenas sandbox
  python run_tests.py --production-only        # Apenas produção
  python run_tests.py --tests credentials rest # Testes específicos
  python run_tests.py --quick                  # Testes rápidos
  python run_tests.py --report results.json   # Salvar relatório
  python run_tests.py --verbose               # Logs detalhados
            """
        )
        
        parser.add_argument(
            '--tests',
            nargs='+',
            choices=self.available_tests,
            help='Testes específicos para executar'
        )
        
        parser.add_argument(
            '--sandbox-only',
            action='store_true',
            help='Executar apenas testes em sandbox'
        )
        
        parser.add_argument(
            '--production-only',
            action='store_true',
            help='Executar apenas testes em produção'
        )
        
        parser.add_argument(
            '--quick',
            action='store_true',
            help='Executar apenas testes rápidos (credentials, normalizer, aggregator)'
        )
        
        parser.add_argument(
            '--report',
            type=str,
            help='Arquivo para salvar relatório JSON dos resultados'
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Logs detalhados'
        )
        
        parser.add_argument(
            '--no-color',
            action='store_true',
            help='Desabilitar cores na saída'
        )
        
        return parser.parse_args()
    
    def check_credentials(self) -> bool:
        """Verifica se credenciais estão configuradas."""
        required_vars = ['KUCOIN_API_KEY', 'KUCOIN_API_SECRET', 'KUCOIN_PASSPHRASE']
        missing = [var for var in required_vars if not os.getenv(var)]
        
        if missing:
            logger.error("❌ Credenciais ausentes:")
            for var in missing:
                logger.error(f"   {var}")
            logger.info("\n💡 Execute primeiro: python -m qualia.live_feed.setup_credentials")
            return False
        
        return True
    
    async def run_specific_tests(self, 
                               test_names: List[str], 
                               use_sandbox: bool = True) -> TestSuiteResults:
        """Executa testes específicos."""
        logger.info(f"🧪 Executando testes específicos: {', '.join(test_names)}")
        
        suite = LiveFeedTestSuite(use_sandbox=use_sandbox)
        
        # Mapear nomes de testes para métodos
        test_methods = {
            'credentials': suite._test_credentials_validation,
            'normalizer': suite._test_data_normalizer,
            'aggregator': suite._test_feed_aggregator,
            'rest': suite._test_kucoin_feed_rest,
            'websocket': suite._test_kucoin_feed_websocket,
            'manager': suite._test_feed_manager_integration,
            'performance': suite._test_performance_metrics,
            'errors': suite._test_error_handling,
            'reconnection': suite._test_reconnection_mechanism,
            'alerts': suite._test_system_alerts,
        }
        
        # Executar testes selecionados
        suite.start_time = asyncio.get_event_loop().time()
        
        for test_name in test_names:
            if test_name in test_methods:
                await test_methods[test_name]()
            else:
                logger.warning(f"⚠️ Teste desconhecido: {test_name}")
        
        return suite._compile_results()
    
    def print_results(self, results: TestSuiteResults, mode: str = ""):
        """Imprime resultados formatados."""
        mode_str = f" - {mode}" if mode else ""
        
        print(f"\n{'='*60}")
        print(f"📊 RESULTADOS DOS TESTES{mode_str}")
        print(f"{'='*60}")
        print(f"Total de testes: {results.total_tests}")
        print(f"Testes aprovados: {results.passed_tests}")
        print(f"Testes falharam: {results.failed_tests}")
        print(f"Taxa de sucesso: {results.success_rate:.1f}%")
        print(f"Duração total: {results.total_duration_ms:.1f}ms")
        
        # Status por teste
        print(f"\n📋 DETALHES DOS TESTES:")
        for result in results.test_results:
            status = "✅ PASSOU" if result.success else "❌ FALHOU"
            print(f"  {status} - {result.test_name} ({result.duration_ms:.1f}ms)")
            if not result.success and result.error_message:
                print(f"    💥 Erro: {result.error_message}")
        
        # Métricas de performance
        if results.performance_metrics:
            print(f"\n📈 MÉTRICAS DE PERFORMANCE:")
            metrics = results.performance_metrics
            print(f"  Dados recebidos: {metrics.get('data_points_received', 0)}")
            print(f"  Erros: {metrics.get('error_count', 0)}")
            print(f"  Alertas: {metrics.get('alert_count', 0)}")
            
            latencies = metrics.get('latency_measurements', [])
            if latencies:
                avg_latency = sum(latencies) / len(latencies)
                print(f"  Latência média: {avg_latency:.1f}ms")
                print(f"  Latência máxima: {max(latencies):.1f}ms")
                print(f"  Latência mínima: {min(latencies):.1f}ms")
    
    def save_report(self, results: TestSuiteResults, filename: str, mode: str = ""):
        """Salva relatório em JSON."""
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'mode': mode,
                'summary': {
                    'total_tests': results.total_tests,
                    'passed_tests': results.passed_tests,
                    'failed_tests': results.failed_tests,
                    'success_rate': results.success_rate,
                    'total_duration_ms': results.total_duration_ms,
                },
                'test_results': [
                    {
                        'test_name': r.test_name,
                        'success': r.success,
                        'duration_ms': r.duration_ms,
                        'details': r.details,
                        'error_message': r.error_message,
                    }
                    for r in results.test_results
                ],
                'performance_metrics': results.performance_metrics,
            }
            
            with open(filename, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            logger.info(f"📄 Relatório salvo em: {filename}")
            
        except Exception as e:
            logger.error(f"❌ Erro ao salvar relatório: {e}")
    
    async def run(self):
        """Executa testes baseado nos argumentos."""
        args = self.parse_arguments()
        
        # Configurar logging
        if args.verbose:
            import logging
            logging.getLogger('qualia.live_feed').setLevel(logging.DEBUG)
        
        # Verificar credenciais
        if not self.check_credentials():
            return 1
        
        # Determinar quais testes executar
        if args.quick:
            test_names = ['credentials', 'normalizer', 'aggregator']
        elif args.tests:
            test_names = args.tests
        else:
            test_names = self.available_tests
        
        # Determinar modos de execução
        run_sandbox = not args.production_only
        run_production = not args.sandbox_only
        
        results_list = []
        
        # Executar em sandbox
        if run_sandbox:
            logger.info("🧪 Executando testes em SANDBOX")
            
            if args.tests or args.quick:
                sandbox_results = await self.run_specific_tests(test_names, use_sandbox=True)
            else:
                suite = LiveFeedTestSuite(use_sandbox=True)
                sandbox_results = await suite.run_full_test_suite()
            
            self.print_results(sandbox_results, "SANDBOX")
            results_list.append(('sandbox', sandbox_results))
            
            # Se sandbox falhou muito, não executar produção
            if sandbox_results.success_rate < 70.0 and run_production:
                logger.warning(f"⚠️ Sandbox teve baixo sucesso ({sandbox_results.success_rate:.1f}%)")
                choice = input("Continuar com testes em produção? (s/n): ").lower().strip()
                if choice not in ['s', 'sim', 'y', 'yes']:
                    run_production = False
        
        # Executar em produção
        if run_production:
            logger.info("🚀 Executando testes em PRODUÇÃO")
            
            if args.tests or args.quick:
                prod_results = await self.run_specific_tests(test_names, use_sandbox=False)
            else:
                suite = LiveFeedTestSuite(use_sandbox=False)
                prod_results = await suite.run_full_test_suite()
            
            self.print_results(prod_results, "PRODUÇÃO")
            results_list.append(('production', prod_results))
        
        # Salvar relatório se solicitado
        if args.report:
            for mode, results in results_list:
                filename = args.report.replace('.json', f'_{mode}.json')
                self.save_report(results, filename, mode.upper())
        
        # Resumo final
        print(f"\n🏁 RESUMO FINAL")
        print(f"{'='*40}")
        
        overall_success = True
        for mode, results in results_list:
            print(f"{mode.upper()}: {results.success_rate:.1f}% sucesso")
            if results.success_rate < 80.0:
                overall_success = False
        
        if overall_success:
            print("✅ Todos os testes passaram com sucesso!")
            return 0
        else:
            print("⚠️ Alguns testes falharam - verifique os detalhes acima")
            return 1


async def main():
    """Função principal."""
    runner = TestRunner()
    exit_code = await runner.run()
    sys.exit(exit_code)


if __name__ == "__main__":
    asyncio.run(main())
