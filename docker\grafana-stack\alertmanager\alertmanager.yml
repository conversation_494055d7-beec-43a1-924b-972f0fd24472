global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: 'alerts'
  smtp_auth_password: 'password'

# Definir templates para notificações
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Configuração de roteamento de alertas
route:
  group_by: ['alertname', 'component']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
    # Alertas críticos - notificação imediata
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 15m
    
    # Alertas de drawdown - canal específico
    - match:
        alert_type: drawdown
      receiver: 'drawdown-alerts'
      group_wait: 30s
      repeat_interval: 30m
    
    # Alertas de sistema - canal técnico
    - match:
        component: system
      receiver: 'system-alerts'
      group_wait: 1m
      repeat_interval: 2h
    
    # Alertas informativos - menos frequentes
    - match:
        severity: info
      receiver: 'info-alerts'
      group_wait: 5m
      repeat_interval: 6h

# Definir receptores de notificação
receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://localhost:5001/webhook'
        send_resolved: true

  - name: 'critical-alerts'
    # Slack para alertas críticos
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL_HERE'
        channel: '#qualia-critical'
        title: '🚨 QUALIA CRITICAL ALERT'
        text: |
          *Alert:* {{ .GroupLabels.alertname }}
          *Severity:* {{ .CommonLabels.severity }}
          *Component:* {{ .CommonLabels.component }}
          *Description:* {{ range .Alerts }}{{ .Annotations.description }}{{ end }}
          *Time:* {{ .CommonAnnotations.timestamp }}
        send_resolved: true
    
    # Email para alertas críticos
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 QUALIA Critical Alert: {{ .GroupLabels.alertname }}'
        body: |
          QUALIA Critical Alert Triggered
          
          Alert: {{ .GroupLabels.alertname }}
          Severity: {{ .CommonLabels.severity }}
          Component: {{ .CommonLabels.component }}
          
          Description:
          {{ range .Alerts }}{{ .Annotations.description }}{{ end }}
          
          Time: {{ .CommonAnnotations.timestamp }}
          
          Please investigate immediately.

  - name: 'drawdown-alerts'
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL_HERE'
        channel: '#qualia-trading'
        title: '📉 QUALIA Drawdown Alert'
        text: |
          *Drawdown Alert:* {{ .GroupLabels.alertname }}
          *Current Drawdown:* {{ .CommonAnnotations.value }}%
          *Description:* {{ range .Alerts }}{{ .Annotations.description }}{{ end }}
          
          Action may be required to limit losses.
        send_resolved: true

  - name: 'system-alerts'
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL_HERE'
        channel: '#qualia-system'
        title: '⚙️ QUALIA System Alert'
        text: |
          *System Alert:* {{ .GroupLabels.alertname }}
          *Component:* {{ .CommonLabels.component }}
          *Type:* {{ .CommonLabels.alert_type }}
          *Description:* {{ range .Alerts }}{{ .Annotations.description }}{{ end }}
        send_resolved: true

  - name: 'info-alerts'
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL_HERE'
        channel: '#qualia-info'
        title: 'ℹ️ QUALIA Info'
        text: |
          *Info:* {{ .GroupLabels.alertname }}
          *Description:* {{ range .Alerts }}{{ .Annotations.description }}{{ end }}
        send_resolved: false

# Configuração de inibição (evitar spam de alertas)
inhibit_rules:
  # Se sistema está down, não alertar sobre outras métricas
  - source_match:
      alertname: QualiaSystemDown
    target_match:
      component: trading
    equal: ['component']
  
  # Se há drawdown crítico, não alertar sobre Sharpe baixo
  - source_match:
      alert_type: drawdown
      severity: critical
    target_match:
      alert_type: performance
    equal: ['component']
