#!/usr/bin/env python3
"""
QUALIA Staging Deployment Script
P-02.1: Deploy em Ambiente de Staging

Comprehensive staging deployment with validation and monitoring
"""

import os
import sys
import json
import time
import asyncio
import logging
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import yaml
import psutil
from src.qualia.validation.production_validator import ProductionValidator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StagingDeployment:
    """Comprehensive staging deployment manager"""
    
    def __init__(self, config_path: str = "config/staging_config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.deployment_id = f"staging_deploy_{int(time.time())}"
        self.pid_file = "run/qualia_staging.pid"
        self.log_file = "logs/staging_deployment.log"
        
        # Create necessary directories
        self._create_directories()
        
        # Setup deployment logging
        self._setup_deployment_logging()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load staging configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load config from {self.config_path}: {e}")
            raise
    
    def _create_directories(self):
        """Create necessary directories for staging"""
        directories = [
            "logs", "run", "data/staging", "backups/staging", 
            "reports/staging", "tmp/staging"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {directory}")
    
    def _setup_deployment_logging(self):
        """Setup deployment-specific logging"""
        # Create file handler for deployment logs
        file_handler = logging.FileHandler(self.log_file)
        file_handler.setLevel(logging.DEBUG)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(deployment_id)s] - %(message)s'
        )
        file_handler.setFormatter(formatter)
        
        # Add handler to logger
        logger.addHandler(file_handler)
        
        # Add deployment_id to all log records
        old_factory = logging.getLogRecordFactory()
        def record_factory(*args, **kwargs):
            record = old_factory(*args, **kwargs)
            record.deployment_id = self.deployment_id
            return record
        logging.setLogRecordFactory(record_factory)
    
    async def deploy_staging(self) -> Dict[str, Any]:
        """Execute complete staging deployment"""
        logger.info(f"Starting staging deployment: {self.deployment_id}")
        
        deployment_report = {
            'deployment_id': self.deployment_id,
            'start_time': datetime.utcnow().isoformat(),
            'config_path': self.config_path,
            'steps': [],
            'status': 'in_progress'
        }
        
        try:
            # Step 1: Pre-deployment validation
            step_result = await self._pre_deployment_validation()
            deployment_report['steps'].append(step_result)
            if not step_result['success']:
                raise Exception("Pre-deployment validation failed")
            
            # Step 2: Environment setup
            step_result = await self._setup_staging_environment()
            deployment_report['steps'].append(step_result)
            if not step_result['success']:
                raise Exception("Environment setup failed")
            
            # Step 3: Deploy application components
            step_result = await self._deploy_application_components()
            deployment_report['steps'].append(step_result)
            if not step_result['success']:
                raise Exception("Application deployment failed")
            
            # Step 4: Start services
            step_result = await self._start_staging_services()
            deployment_report['steps'].append(step_result)
            if not step_result['success']:
                raise Exception("Service startup failed")
            
            # Step 5: Post-deployment validation
            step_result = await self._post_deployment_validation()
            deployment_report['steps'].append(step_result)
            if not step_result['success']:
                raise Exception("Post-deployment validation failed")
            
            # Step 6: Health checks
            step_result = await self._staging_health_checks()
            deployment_report['steps'].append(step_result)
            if not step_result['success']:
                raise Exception("Health checks failed")
            
            deployment_report['status'] = 'success'
            deployment_report['end_time'] = datetime.utcnow().isoformat()
            
            logger.info(f"Staging deployment completed successfully: {self.deployment_id}")
            
        except Exception as e:
            deployment_report['status'] = 'failed'
            deployment_report['error'] = str(e)
            deployment_report['end_time'] = datetime.utcnow().isoformat()
            
            logger.error(f"Staging deployment failed: {e}")
            
            # Attempt rollback
            await self._rollback_staging()
        
        # Save deployment report
        await self._save_deployment_report(deployment_report)
        
        return deployment_report
    
    async def _pre_deployment_validation(self) -> Dict[str, Any]:
        """Execute pre-deployment validation"""
        logger.info("Executing pre-deployment validation")
        
        try:
            # Use production validator with staging config
            validator = ProductionValidator(self.config)
            validation_report = await validator.validate_production_environment()
            
            success = validation_report.overall_status in ['PASS', 'WARNING']
            
            return {
                'step': 'pre_deployment_validation',
                'success': success,
                'validation_report': validation_report.validation_id,
                'total_tests': validation_report.total_tests,
                'passed_tests': validation_report.passed_tests,
                'failed_tests': validation_report.failed_tests,
                'warning_tests': validation_report.warning_tests,
                'execution_time_ms': validation_report.execution_time_ms
            }
            
        except Exception as e:
            logger.error(f"Pre-deployment validation failed: {e}")
            return {
                'step': 'pre_deployment_validation',
                'success': False,
                'error': str(e)
            }
    
    async def _setup_staging_environment(self) -> Dict[str, Any]:
        """Setup staging environment"""
        logger.info("Setting up staging environment")
        
        try:
            # Set environment variables
            os.environ['QUALIA_ENV'] = 'staging'
            os.environ['QUALIA_CONFIG'] = self.config_path
            os.environ['QUALIA_LOG_LEVEL'] = 'DEBUG'
            
            # Create staging-specific directories
            staging_dirs = [
                "data/staging/market_data",
                "data/staging/trading_data", 
                "data/staging/optimization_data",
                "logs/staging",
                "backups/staging",
                "tmp/staging"
            ]
            
            for directory in staging_dirs:
                Path(directory).mkdir(parents=True, exist_ok=True)
            
            # Copy production credentials to staging (if they exist)
            prod_creds = Path("config/.credentials")
            staging_creds = Path("config/.staging_credentials")
            
            if prod_creds.exists():
                import shutil
                shutil.copy2(prod_creds, staging_creds)
                os.chmod(staging_creds, 0o600)
            
            return {
                'step': 'setup_staging_environment',
                'success': True,
                'directories_created': len(staging_dirs),
                'environment_variables_set': 3
            }
            
        except Exception as e:
            logger.error(f"Environment setup failed: {e}")
            return {
                'step': 'setup_staging_environment',
                'success': False,
                'error': str(e)
            }
    
    async def _deploy_application_components(self) -> Dict[str, Any]:
        """Deploy application components"""
        logger.info("Deploying application components")
        
        try:
            # Validate all required files exist
            required_files = [
                "src/qualia/trading/qualia_trading_system.py",
                "src/qualia/live_feed/kucoin_feed.py",
                "src/qualia/optimization/bayesian_optimizer.py",
                "src/qualia/monitoring/production_monitor.py",
                "src/qualia/backup/backup_manager.py"
            ]
            
            missing_files = []
            for file_path in required_files:
                if not Path(file_path).exists():
                    missing_files.append(file_path)
            
            if missing_files:
                raise Exception(f"Missing required files: {missing_files}")
            
            # Test imports
            import_tests = [
                "from src.qualia.trading.qualia_trading_system import QUALIATradingSystem",
                "from src.qualia.live_feed.kucoin_feed import KuCoinFeed",
                "from src.qualia.optimization.bayesian_optimizer import BayesianOptimizer",
                "from src.qualia.monitoring.production_monitor import ProductionMonitor"
            ]
            
            for import_test in import_tests:
                try:
                    exec(import_test)
                except Exception as e:
                    raise Exception(f"Import test failed: {import_test} - {e}")
            
            return {
                'step': 'deploy_application_components',
                'success': True,
                'files_validated': len(required_files),
                'imports_tested': len(import_tests)
            }
            
        except Exception as e:
            logger.error(f"Application deployment failed: {e}")
            return {
                'step': 'deploy_application_components',
                'success': False,
                'error': str(e)
            }
    
    async def _start_staging_services(self) -> Dict[str, Any]:
        """Start staging services"""
        logger.info("Starting staging services")
        
        try:
            # Create startup script for staging
            startup_script = f"""
import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.qualia.trading.qualia_trading_system import QUALIATradingSystem

async def main():
    # Initialize trading system with staging config
    trading_system = QUALIATradingSystem(config_path="{self.config_path}")
    
    # Start in paper trading mode for staging
    await trading_system.start_paper_trading()

if __name__ == "__main__":
    asyncio.run(main())
"""
            
            # Save startup script
            startup_script_path = "scripts/start_staging.py"
            with open(startup_script_path, 'w') as f:
                f.write(startup_script)
            
            # Make executable
            os.chmod(startup_script_path, 0o755)
            
            # Start staging service in background
            process = subprocess.Popen([
                sys.executable, startup_script_path
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Save PID
            with open(self.pid_file, 'w') as f:
                f.write(str(process.pid))
            
            # Wait a moment for startup
            await asyncio.sleep(5)
            
            # Check if process is still running
            if process.poll() is None:
                return {
                    'step': 'start_staging_services',
                    'success': True,
                    'pid': process.pid,
                    'startup_script': startup_script_path
                }
            else:
                stdout, stderr = process.communicate()
                raise Exception(f"Service failed to start: {stderr.decode()}")
            
        except Exception as e:
            logger.error(f"Service startup failed: {e}")
            return {
                'step': 'start_staging_services',
                'success': False,
                'error': str(e)
            }
    
    async def _post_deployment_validation(self) -> Dict[str, Any]:
        """Execute post-deployment validation"""
        logger.info("Executing post-deployment validation")
        
        try:
            # Check if service is running
            if not Path(self.pid_file).exists():
                raise Exception("PID file not found")
            
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            if not psutil.pid_exists(pid):
                raise Exception(f"Process {pid} is not running")
            
            # Check process health
            process = psutil.Process(pid)
            process_info = {
                'pid': pid,
                'status': process.status(),
                'cpu_percent': process.cpu_percent(),
                'memory_percent': process.memory_percent(),
                'create_time': process.create_time()
            }
            
            return {
                'step': 'post_deployment_validation',
                'success': True,
                'process_info': process_info
            }
            
        except Exception as e:
            logger.error(f"Post-deployment validation failed: {e}")
            return {
                'step': 'post_deployment_validation',
                'success': False,
                'error': str(e)
            }
    
    async def _staging_health_checks(self) -> Dict[str, Any]:
        """Execute staging health checks"""
        logger.info("Executing staging health checks")
        
        try:
            health_checks = []
            
            # System health
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent
            disk_percent = psutil.disk_usage('.').percent
            
            health_checks.append({
                'check': 'system_resources',
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'disk_percent': disk_percent,
                'status': 'healthy' if cpu_percent < 80 and memory_percent < 80 and disk_percent < 80 else 'warning'
            })
            
            # Log files health
            log_files = [
                "logs/staging_qualia.log",
                "logs/staging_trading.log",
                "logs/staging_deployment.log"
            ]
            
            log_health = []
            for log_file in log_files:
                if Path(log_file).exists():
                    size = Path(log_file).stat().st_size
                    log_health.append({
                        'file': log_file,
                        'size_bytes': size,
                        'status': 'healthy'
                    })
                else:
                    log_health.append({
                        'file': log_file,
                        'status': 'missing'
                    })
            
            health_checks.append({
                'check': 'log_files',
                'files': log_health,
                'status': 'healthy' if all(f['status'] == 'healthy' for f in log_health) else 'warning'
            })
            
            # Overall health status
            overall_status = 'healthy' if all(check['status'] == 'healthy' for check in health_checks) else 'warning'
            
            return {
                'step': 'staging_health_checks',
                'success': True,
                'overall_status': overall_status,
                'health_checks': health_checks
            }
            
        except Exception as e:
            logger.error(f"Health checks failed: {e}")
            return {
                'step': 'staging_health_checks',
                'success': False,
                'error': str(e)
            }
    
    async def _rollback_staging(self):
        """Rollback staging deployment"""
        logger.warning("Executing staging rollback")
        
        try:
            # Stop staging service
            if Path(self.pid_file).exists():
                with open(self.pid_file, 'r') as f:
                    pid = int(f.read().strip())
                
                if psutil.pid_exists(pid):
                    process = psutil.Process(pid)
                    process.terminate()
                    
                    # Wait for graceful shutdown
                    try:
                        process.wait(timeout=10)
                    except psutil.TimeoutExpired:
                        process.kill()
                
                # Remove PID file
                Path(self.pid_file).unlink()
            
            logger.info("Staging rollback completed")
            
        except Exception as e:
            logger.error(f"Rollback failed: {e}")
    
    async def _save_deployment_report(self, report: Dict[str, Any]):
        """Save deployment report"""
        try:
            report_file = f"reports/staging/deployment_{self.deployment_id}.json"
            Path("reports/staging").mkdir(parents=True, exist_ok=True)
            
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"Deployment report saved: {report_file}")
            
        except Exception as e:
            logger.error(f"Failed to save deployment report: {e}")

async def main():
    """Main deployment execution"""
    import argparse
    
    parser = argparse.ArgumentParser(description='QUALIA Staging Deployment')
    parser.add_argument('--config', default='config/staging_config.yaml',
                       help='Staging configuration file')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Execute staging deployment
    deployment = StagingDeployment(args.config)
    report = await deployment.deploy_staging()
    
    # Print summary
    print("\n" + "="*80)
    print("QUALIA STAGING DEPLOYMENT RESULTS")
    print("="*80)
    print(f"Deployment ID: {report['deployment_id']}")
    print(f"Status: {report['status'].upper()}")
    print(f"Steps Completed: {len(report['steps'])}")
    
    if report['status'] == 'success':
        print("🎉 STAGING DEPLOYMENT SUCCESSFUL!")
        print("Staging environment is ready for integration testing.")
    else:
        print("❌ STAGING DEPLOYMENT FAILED")
        if 'error' in report:
            print(f"Error: {report['error']}")
    
    print("="*80)
    
    # Exit with appropriate code
    sys.exit(0 if report['status'] == 'success' else 1)

if __name__ == '__main__':
    asyncio.run(main())
