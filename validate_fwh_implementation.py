#!/usr/bin/env python3
"""
Validação da Implementação FWH Multi-Timeframe Aprimorada

Script simples para validar as mudanças implementadas.
"""

import yaml
import os
from datetime import datetime

def validate_configuration():
    """Valida se a configuração foi atualizada corretamente."""
    print("=== Validação da Configuração FWH ===")
    
    config_path = 'config/fwh_scalp_config.yaml'
    
    if not os.path.exists(config_path):
        print("❌ Arquivo de configuração não encontrado")
        return False
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # Verifica timeframes
    timeframes = config.get('trading_system', {}).get('timeframes', [])
    expected_timeframes = ['1m', '5m', '15m', '1h']
    
    print(f"Timeframes configurados: {timeframes}")
    print(f"Timeframes esperados: {expected_timeframes}")
    
    if timeframes == expected_timeframes:
        print("✅ Timeframes configurados corretamente")
        return True
    else:
        print("❌ Timeframes não estão corretos")
        return False

def validate_consolidator_code():
    """Valida se o código do consolidador foi atualizado."""
    print("\n=== Validação do Código do Consolidador ===")
    
    consolidator_path = 'src/qualia/strategies/fibonacci_wave_hype/multi_timeframe_consolidator.py'
    
    if not os.path.exists(consolidator_path):
        print("❌ Arquivo do consolidador não encontrado")
        return False
    
    with open(consolidator_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Verifica implementações específicas
    checks = [
        ('Pesos otimizados', '"1m": 0.3' in content and '"15m": 0.6' in content and '"1h": 0.8' in content),
        ('Sistema de cache', 'cache_enabled' in content and 'resample_cache' in content),
        ('Filtro de tendência', 'apply_trend_filter' in content),
        ('Import fallback', 'except ImportError:' in content),
        ('Cache TTL', 'cache_ttl_minutes' in content)
    ]
    
    all_passed = True
    for check_name, check_result in checks:
        if check_result:
            print(f"✅ {check_name}: Implementado")
        else:
            print(f"❌ {check_name}: Não encontrado")
            all_passed = False
    
    return all_passed

def validate_core_strategy():
    """Valida se a estratégia core foi atualizada."""
    print("\n=== Validação da Estratégia Core ===")
    
    core_path = 'src/qualia/strategies/fibonacci_wave_hype/core.py'
    
    if not os.path.exists(core_path):
        print("❌ Arquivo core da estratégia não encontrado")
        return False
    
    with open(core_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Verifica se os timeframes suportados foram atualizados
    if '["1m", "5m", "15m", "1h"]' in content:
        print("✅ Timeframes suportados atualizados na estratégia core")
        return True
    else:
        print("❌ Timeframes suportados não foram atualizados")
        return False

def generate_implementation_summary():
    """Gera um resumo da implementação."""
    print("\n" + "="*60)
    print("📋 RESUMO DA IMPLEMENTAÇÃO FWH MULTI-TIMEFRAME")
    print("="*60)
    
    features = [
        "✅ Timeframes 15m e 1h adicionados à configuração",
        "✅ Pesos otimizados por timeframe:",
        "   • 1m: 0.3 (entrada precisa)",
        "   • 5m: 0.4 (confirmação rápida)", 
        "   • 15m: 0.6 (ondas de hype)",
        "   • 1h: 0.8 (tendência principal)",
        "✅ Filtro de tendência principal baseado em 1h",
        "✅ Sistema de cache para dados reamostrados",
        "✅ Penalização de sinais contrários à tendência",
        "✅ Consolidação hierárquica de sinais",
        "✅ Imports com fallback para compatibilidade"
    ]
    
    for feature in features:
        print(feature)
    
    print("\n🎯 BENEFÍCIOS ESPERADOS:")
    benefits = [
        "• Melhor detecção de tendências de médio prazo (15m, 1h)",
        "• Redução de falsos sinais através do filtro de tendência",
        "• Performance otimizada com cache de reamostragem",
        "• Sinais mais robustos e confiáveis",
        "• Alinhamento com ondas de hype em múltiplos timeframes"
    ]
    
    for benefit in benefits:
        print(benefit)
    
    print("\n⚡ PRÓXIMOS PASSOS RECOMENDADOS:")
    next_steps = [
        "1. Executar backtesting com novos timeframes",
        "2. Monitorar performance em paper trading",
        "3. Ajustar pesos baseado em resultados",
        "4. Implementar métricas específicas de convergência",
        "5. Considerar timeframes adicionais (4h) se necessário"
    ]
    
    for step in next_steps:
        print(step)
    
    print("="*60)

def main():
    """Executa todas as validações."""
    print(f"Validação executada em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Executa validações
    config_ok = validate_configuration()
    consolidator_ok = validate_consolidator_code()
    core_ok = validate_core_strategy()
    
    # Resultado final
    if config_ok and consolidator_ok and core_ok:
        print("\n🎉 IMPLEMENTAÇÃO CONCLUÍDA COM SUCESSO!")
        generate_implementation_summary()
        return True
    else:
        print("\n⚠️  IMPLEMENTAÇÃO INCOMPLETA - Verifique os erros acima")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)