#!/usr/bin/env python3
"""
Script de teste para a otimização multi-timeframe da estratégia FWH.

Este script testa a nova funcionalidade de consolidação de sinais
para verificar se a otimização resolve os problemas identificados.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Adiciona o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy
from qualia.strategies.fibonacci_wave_hype.multi_timeframe_consolidator import (
    MultiTimeframeSignalConsolidator, TimeframeSignal, ConsolidatedSignal
)
from qualia.strategies.strategy_interface import TradingContext
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

def create_sample_ohlcv_data(periods: int = 100, start_price: float = 50000.0) -> pd.DataFrame:
    """Cria dados OHLCV sintéticos para teste."""
    
    # Gera timestamps de 1 minuto
    timestamps = pd.date_range(
        start=datetime.now() - timedelta(minutes=periods),
        periods=periods,
        freq='1T'
    )
    
    # Simula movimento de preços com tendência e volatilidade
    np.random.seed(42)  # Para resultados reproduzíveis
    
    # Gera retornos com tendência leve de alta
    returns = np.random.normal(0.0002, 0.01, periods)  # 0.02% média, 1% volatilidade
    
    # Calcula preços de fechamento
    prices = [start_price]
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # Gera OHLCV
    data = []
    for i, (ts, close) in enumerate(zip(timestamps, prices)):
        # Simula variação intrabar
        volatility = close * 0.005  # 0.5% de volatilidade intrabar
        
        high = close + np.random.uniform(0, volatility)
        low = close - np.random.uniform(0, volatility)
        
        if i == 0:
            open_price = close
        else:
            open_price = prices[i-1]
        
        # Garante que high >= max(open, close) e low <= min(open, close)
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        volume = np.random.uniform(100, 1000)
        
        data.append({
            'timestamp': ts,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    return df

def test_consolidator_basic():
    """Testa funcionalidade básica do consolidador."""
    logger.info("=== Teste Básico do Consolidador ===")
    
    consolidator = MultiTimeframeSignalConsolidator()
    
    # Cria sinais de teste
    signals = [
        TimeframeSignal(
            timeframe='1m',
            signal='buy',
            confidence=0.7,
            signal_strength=0.8,
            hype_momentum=0.6,
            holographic_boost=1.2,
            tsvf_validation=0.7,
            timestamp=datetime.now()
        ),
        TimeframeSignal(
            timeframe='5m',
            signal='buy',
            confidence=0.6,
            signal_strength=0.7,
            hype_momentum=0.5,
            holographic_boost=1.1,
            tsvf_validation=0.8,
            timestamp=datetime.now()
        )
    ]
    
    # Testa consolidação
    consolidated = consolidator.consolidate_signals(signals)
    
    logger.info(f"Sinal consolidado: {consolidated.signal}")
    logger.info(f"Confiança: {consolidated.confidence:.3f}")
    logger.info(f"Convergência: {consolidated.convergence_score:.3f}")
    logger.info(f"Timeframe primário: {consolidated.primary_timeframe}")
    logger.info(f"Reasoning: {consolidated.reasoning}")
    
    assert consolidated.signal == 'buy', "Deveria gerar sinal de compra"
    assert consolidated.confidence > 0.5, "Confiança deveria ser alta"
    
    logger.info("✅ Teste básico do consolidador passou!")

def test_resample_functionality():
    """Testa funcionalidade de reamostragem."""
    logger.info("=== Teste de Reamostragem ===")
    
    # Cria dados de 1m
    data_1m = create_sample_ohlcv_data(50)
    logger.info(f"Dados 1m criados: {len(data_1m)} períodos")
    
    consolidator = MultiTimeframeSignalConsolidator()
    
    # Testa reamostragem para 5m
    data_5m = consolidator.resample_data(data_1m, '5m')
    logger.info(f"Dados 5m após resample: {len(data_5m)} períodos")
    
    # Verifica se a reamostragem funcionou
    assert not data_5m.empty, "Dados reamostrados não deveriam estar vazios"
    assert len(data_5m) <= len(data_1m) // 5 + 1, "Dados 5m deveriam ter ~1/5 dos períodos"
    
    # Verifica estrutura OHLCV
    expected_columns = ['open', 'high', 'low', 'close', 'volume']
    assert all(col in data_5m.columns for col in expected_columns), "Colunas OHLCV ausentes"
    
    logger.info("✅ Teste de reamostragem passou!")

def test_strategy_integration():
    """Testa integração completa com a estratégia FWH."""
    logger.info("=== Teste de Integração com Estratégia ===")
    
    # Cria dados de teste
    market_data = create_sample_ohlcv_data(100)
    
    # Inicializa estratégia
    strategy = FibonacciWaveHypeStrategy(
        symbol="BTC/USDT",
        timeframe="1m",
        fib_lookback=20,
        hype_threshold=0.15,
        wave_min_strength=0.3
    )
    
    # Cria contexto de trading
    context = TradingContext(
        data=market_data,
        symbol="BTC/USDT",
        timeframe="1m"
    )
    context.ohlcv = market_data
    
    try:
        # Testa geração de sinal multi-timeframe
        signal_df = strategy.generate_signal(context)
        
        logger.info(f"Sinal gerado: {not signal_df.empty}")
        
        if not signal_df.empty:
            signal_info = signal_df.iloc[0]
            logger.info(f"Tipo: {signal_info.get('signal', 'N/A')}")
            logger.info(f"Confiança: {signal_info.get('confidence', 0):.3f}")
            
            # Verifica campos multi-timeframe
            if 'convergence_score' in signal_info:
                logger.info(f"Convergência: {signal_info['convergence_score']:.3f}")
            if 'supporting_timeframes' in signal_info:
                logger.info(f"Timeframes suporte: {signal_info['supporting_timeframes']}")
            if 'consolidation_reasoning' in signal_info:
                logger.info(f"Reasoning: {signal_info['consolidation_reasoning']}")
        
        logger.info("✅ Teste de integração passou!")
        
    except Exception as e:
        logger.error(f"❌ Erro na integração: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise

def test_divergent_signals():
    """Testa comportamento com sinais divergentes."""
    logger.info("=== Teste de Sinais Divergentes ===")
    
    consolidator = MultiTimeframeSignalConsolidator()
    
    # Cria sinais divergentes
    signals = [
        TimeframeSignal(
            timeframe='1m',
            signal='buy',
            confidence=0.6,
            signal_strength=0.7,
            hype_momentum=0.5,
            holographic_boost=1.1,
            tsvf_validation=0.6,
            timestamp=datetime.now()
        ),
        TimeframeSignal(
            timeframe='5m',
            signal='sell',
            confidence=0.5,
            signal_strength=0.6,
            hype_momentum=0.4,
            holographic_boost=1.0,
            tsvf_validation=0.7,
            timestamp=datetime.now()
        )
    ]
    
    # Testa consolidação
    consolidated = consolidator.consolidate_signals(signals)
    
    logger.info(f"Sinal consolidado: {consolidated.signal}")
    logger.info(f"Confiança: {consolidated.confidence:.3f}")
    logger.info(f"Convergência: {consolidated.convergence_score:.3f}")
    logger.info(f"Reasoning: {consolidated.reasoning}")
    
    # Com sinais divergentes, deveria ter baixa convergência
    assert consolidated.convergence_score < 0.7, "Convergência deveria ser baixa para sinais divergentes"
    
    logger.info("✅ Teste de sinais divergentes passou!")

def main():
    """Executa todos os testes."""
    logger.info("🚀 Iniciando testes da otimização multi-timeframe FWH")
    
    try:
        test_consolidator_basic()
        test_resample_functionality()
        test_divergent_signals()
        test_strategy_integration()
        
        logger.info("\n🎉 Todos os testes passaram com sucesso!")
        logger.info("\n📊 Resumo da otimização:")
        logger.info("✅ Consolidação de sinais multi-timeframe implementada")
        logger.info("✅ Reamostragem de dados funcionando")
        logger.info("✅ Integração com estratégia FWH completa")
        logger.info("✅ Tratamento de sinais divergentes")
        logger.info("\n🔧 Benefícios esperados:")
        logger.info("• Redução de ~50% nas chamadas de API")
        logger.info("• Eliminação do problema 'PERÍODO INSUFICIENTE'")
        logger.info("• Melhoria na qualidade dos sinais consolidados")
        logger.info("• Redução da latência do sistema")
        
    except Exception as e:
        logger.error(f"❌ Teste falhou: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    main()