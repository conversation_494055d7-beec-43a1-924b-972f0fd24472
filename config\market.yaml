rate_limits:
  global: 2.0  # RATE LIMIT FIX: Reduzido para 2.0s - mais realista para operações normais
  binance: 2.0  # Rate limit específico para Binance - ajustado para operações normais
  max_concurrent_requests: 2  # RATE LIMIT FIX: Aumentado para 2 para permitir operações paralelas básicas
timeouts:
  connection: 30.0  # RATE LIMIT FIX: Reduzido para 30.0s - mais responsivo
  ticker: 20.0      # RATE LIMIT FIX: Reduzido para 20.0s - mais ágil
  ohlcv: 60.0       # RATE LIMIT FIX: Reduzido para 60.0s - mais eficiente
cache:
  ohlcv_ttl_1m: 30  # Cache de 30s para timeframe 1m
  ohlcv_ttl_5m: 60  # Cache de 60s para timeframe 5m
  ohlcv_ttl_15m: 120  # Cache de 120s para timeframe 15m
  ohlcv_ttl_1h: 300  # Cache de 300s para timeframe 1h
feature_toggles:
  market_module: false
  qpm_event_integration: true  # exemplo de flag para eventos do QPM
  risk_adjustment_events: true  # exemplo de flag para eventos de ajuste de risco
