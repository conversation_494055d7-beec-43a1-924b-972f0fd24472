# Especificação: CTX - CortexPlusPlus (QUALIA Consciousness Core)

**Versão:** R-0.3 (Foco em CTX-1: `_detect_patterns`)

## 1. Visão Geral

O componente CortexPlusPlus (CTX) é o núcleo de processamento cognitivo da `QUALIAConsciousness`. É responsável por analisar o fluxo de estados do `QUALIAQuantumUniverse`, detectar padrões emergentes significativos e, subsequentemente, modular a expressão simbólica do Universo através de um mecanismo de auto-reflexão.

Esta especificação detalha a implementação e os guardrails de performance para as funcionalidades do CTX, começando com o `CTX-1: _detect_patterns`.

## 2. Módulos Chave e Funcionalidades

-   **`QUALIAConsciousness` (`src/qualia/core/consciousness.py`)**:
    -   `_detect_patterns()`: Analisa o histórico de `UniverseState` para identificar `CortexPattern` latentes.
    -   `_execute_self_reflection()`: (Escopo de CTX-6) Modifica a `symbolic_expr` do Universo com base nos padrões detectados.
    -   `record_universe_state()`: Registra os estados do universo para análise.
    -   **`QualiaSelfObserver` (`src/qualia/core/observer.py`)**: Registra os padrões detectados e outros eventos cognitivos.

## 3. Detalhamento CTX-1: `_detect_patterns`

### 3.1. Fluxo de Processamento

1.  **Gatilho (Trigger)**: A detecção é disparada se a variação absoluta da `page_entropy` entre o estado atual e o anterior do `UniverseState` excede um múltiplo (`entropy_sensitivity_threshold_sigma`) do desvio padrão da `page_entropy` na janela de histórico recente (`universe_state_history`).
2.  **Coleta de Vetores**: Os vetores de estado (`UniverseState.vector`) do histórico são coletados.
3.  **Redução de Dimensionalidade (PCA)**:
    -   PCA é aplicado para reduzir a dimensionalidade dos vetores, mantendo uma porcentagem configurável da variância (e.g., >=95%).
    -   O número de componentes é limitado por um máximo configurável (e.g., `pca_max_components = 16`).
4.  **Clustering (k-Means Adaptativo)**:
    -   k-Means é aplicado nos vetores de dimensão reduzida.
    -   O número de clusters `k` é determinado adaptativamente dentro de um range configurável (e.g., `kmeans_k_range = (2, 10)`), selecionando o `k` que maximiza o `silhouette_score`.
5.  **Geração de `CortexPattern`**:
    -   Um `CortexPattern` é gerado, contendo:
        -   `pattern_id`: UUID.
        -   `centroid`: Centroides dos clusters encontrados na dimensão reduzida.
        -   `inertia`: Inertia total do modelo k-Means.
        -   `n_clusters`: O `k` ótimo encontrado.
        -   `source_states_timestamps`: Timestamps dos `UniverseState` que contribuíram.
        -   `created_at`: Timestamp da criação do padrão.
6.  **Observação**: O `CortexPattern` é registrado pelo `QualiaSelfObserver`.

### 3.2. Tipos de Dados

-   `UniverseState(TypedDict)`:
    -   `vector: np.ndarray`
    -   `page_entropy: float`
    -   `timestamp: float` (epoch ms)
    -   `trace_id: Optional[str]`
-   `CortexPattern(TypedDict)`: (Conforme definido acima)

## 4. Performance Guardrails & Benchmarks (`_detect_patterns` - CTX-1)

Os seguintes guardrails de performance são estabelecidos para a função `_detect_patterns` e são validados pelo CI (quando configurado).

-   **Script de Benchmark**: O script de benchmark para o `_detect_patterns` está localizado em [`benchmarks/cortex/run_cortex_bench.py`](../../benchmarks/cortex/run_cortex_bench.py).

-   **Resultados de Benchmark (`_detect_patterns`)**:

    *Inicial (antes da correção de bug interno e otimizações V1)*:

    | Métrica                              | Valor Medido                         | Guardrail   |
    | ------------------------------------ | ------------------------------------ | ----------- |
    | Latência Média `_detect_patterns`    | *(não aplicável)*                   | N/A         |
    | Latência P95 `_detect_patterns`      | 571.538 ms                           | < 150 ms    |
    | Pico de Heap Inc. `_detect_patterns` | 2.221 MB                             | < 5 MB      |

    *Baseline Corrigido (Pós-correção de bug interno, antes de otimizações V1)*:

    | Métrica                              | Valor Medido                         | Guardrail   |
    | ------------------------------------ | ------------------------------------ | ----------- |
    | Latência Média `_detect_patterns`    | 210.638 ms                           | N/A         |
    | Latência P95 `_detect_patterns`      | 357.848 ms                           | < 150 ms    |
    | Pico de Heap Inc. `_detect_patterns` | 2.195 MB                             | < 5 MB      |

    *(Nota: Baseline Corrigido medido com N=1000 iterações, 256 histórico, 256 dimensão)*

    *Otimização V1 (MiniBatchKMeans + Heurística de k + PCA)*:

    Com esta otimização, a latência P95 medida em um conjunto maior de iterações (N=1000) foi de **214.976 ms**.
    Análises de perfilamento subsequentes foram conduzidas com N=30 iterações para investigar gargalos.

    | Métrica                              | Valor Medido (N=1000 iters)          | Guardrail   |
    | ------------------------------------ | ------------------------------------ | ----------- |
    | Latência Média `_detect_patterns`    | 163.108 ms                           | N/A         |
    | Latência P95 `_detect_patterns`      | 214.976 ms                           | < 150 ms    |
    | Pico de Heap Inc. `_detect_patterns` | 2.190 MB                             | < 5 MB      |

    **Análise de Perfil Detalhada da Otimização V1 (com `MiniBatchKMeans(n_init=...)`)**

    Foram realizados testes de perfilamento com `cProfile` (N=30 iterações, 256 histórico, 256 dimensão) para entender o impacto do parâmetro `n_init` do `MiniBatchKMeans`.

    1.  **Otimização V1 com `n_init=3` (Padrão MiniBatchKMeans)**
        *   **Resultados do Benchmark (N=30 iters):**
            *   Latência Média: 185.718 ms
            *   Latência P95: **120.052 ms (ATENDEU guardrail de < 150 ms)**
            *   Pico de Heap: 4.860 MB (ATENDEU guardrail de < 5.0 MB)
        *   **Principais Observações do `cProfile`:**
            *   Tempo total da execução do benchmark (30 chamadas a `_detect_patterns`): ~5.58 segundos.
            *   `MiniBatchKMeans.fit()`: ~3.92s (cumtime).
            *   Overhead de `joblib/loky` para `cpu_count` e `threading` (associado ao `n_init=3`): ~2.36s (cumtime).
            *   `scipy.linalg.svd` (dentro do `PCA`): ~1.34s (tottime), principal consumidor individual.
            *   `_labels_inertia` (no KMeans): ~0.54s (tottime).
        *   *Nota*: A latência P95 de 120ms com N=30 é promissora, mas deve-se notar a variabilidade em relação aos testes com N=1000 (onde foi ~215ms).

    2.  **Teste de Otimização V1 com `n_init=1`**
        *   **Objetivo**: Verificar se reduzir `n_init` para 1 eliminaria o overhead do `joblib` sem degradar significativamente a performance.
        *   **Resultados do Benchmark (N=30 iters):**
            *   Latência Média: 182.906 ms (ligeiramente melhor)
            *   Latência P95: **368.222 ms (NÃO ATENDEU guardrail)**
            *   Pico de Heap: 4.854 MB (ATENDEU guardrail)
        *   **Principais Observações do `cProfile`:**
            *   Tempo total da execução do benchmark: ~5.49 segundos (ligeiramente mais rápido).
            *   Overhead de `joblib/loky` para `cpu_count`: Reduzido para ~1.53s (cumtime) - **melhora confirmada**.
            *   `_labels_inertia` (no KMeans): Aumentou para ~0.97s (tottime) - **degradação significativa**.
            *   `scipy.linalg.svd` (dentro do `PCA`): ~1.48s (tottime).

    3.  **Conclusão sobre `n_init`**:
        *   Embora `n_init=1` tenha reduzido o overhead do `joblib` associado à paralelização de múltiplas inicializações, a latência P95 piorou drasticamente. Isso sugere que uma única inicialização pode levar a uma maior variabilidade na convergência do `MiniBatchKMeans`, resultando em execuções ocasionais mais lentas.
        *   **Decisão**: Manter `n_init=3` (padrão do `MiniBatchKMeans`) é a configuração preferida para a Otimização V1, pois oferece uma melhor estabilidade de performance (P95), apesar do overhead de `joblib`. A configuração atual do código reflete esta decisão.

    *Otimização V2 (MiniBatchKMeans + Heurística de k + TruncatedSVD)*:

    | Métrica                              | Valor Medido                         | Guardrail   |
    | ------------------------------------ | ------------------------------------ | ----------- |
    | Latência Média `_detect_patterns`    | 180.271 ms                           | N/A         |
    | Latência P95 `_detect_patterns`      | 278.631 ms                           | < 150 ms    |
    | Pico de Heap Inc. `_detect_patterns` | 2.032 MB                             | < 5 MB      |


    *Nota: Os valores medidos são populados por execuções locais de benchmark. O guardrail de latência P95 para `_detect_patterns` (< 150 ms) ainda é excedido. A Otimização V1 (com PCA) foi mais eficaz que a V2 (com TruncatedSVD) em termos de latência. Próximas otimizações ou ajustes no guardrail podem ser necessários.*

    *Otimização V3 (IncrementalPCA + MiniBatchKMeans parcial)*:

    | Métrica                              | Valor Medido (N=10 iters) | Guardrail |
    | ------------------------------------ | ------------------------- | --------- |
    | Latência Média `_detect_patterns`    | 24.494 ms                 | N/A       |
    | Latência P95 `_detect_patterns`      | 80.681 ms                 | < 150 ms  |
    | Pico de Heap Inc. `_detect_patterns` | 0.541 MB                  | < 5 MB    |

## 5. Backlog e Próximas Etapas (Pós CTX-1)

-   Integração com dashboard de visualização para padrões e métricas do Córtex. 

## 6. Funcionalidades Implementadas (Pós CTX-1)

### 6.1. CTX-6: Implementar `_execute_self_reflection()`
    -   *Status*: Implementação inicial concluída em `QUALIAConsciousness._execute_self_reflection`.
    -   *Detalhes*: A função agora analisa `CortexPattern`s, deriva "observações" ou "regras" simbólicas, atualiza um atributo `self.symbolic_expr` (um dicionário de regras) e retorna um `delta_symbolic_expr` detalhando as adições e atualizações. A estrutura de `self.symbolic_expr` e a lógica de derivação de regras são baseadas em exemplos e podem ser refinadas.
    -   *Nota*: Estes guardrails são propostas iniciais e podem ser ajustados com base nos resultados de execuções de CI e análises de performance mais detalhadas. O objetivo é garantir que o ciclo completo da consciência permaneça eficiente.

### 6.2. CTX-12: Benchmark do Ciclo Completo do Córtex (`process_qast_cycle`)
    -   *Status*: Implementado e otimizado.
    -   *Script de Benchmark*: [`benchmarks/cortex/run_cortex_full_cycle_bench.py`](../../benchmarks/cortex/run_cortex_full_cycle_bench.py)
    -   *Resultados de Benchmark (Ciclo Completo do Córtex - `process_qast_cycle`)*:

        *Valores após otimizações em `_detect_patterns` (Otimização V1 - `n_init=3`)*

        | Métrica                                  | Valor Medido                         | Guardrail   |
        | ---------------------------------------- | ------------------------------------ | ----------- |
        | Latência Média `process_qast_cycle`      | 144.793 ms                           | N/A         |
        | Latência P95 `process_qast_cycle`        | 170.932 ms                           | < 300 ms    |
        | Pico de Heap Inc. `process_qast_cycle`   | 4.347 MB                             | < 10 MB     |

    -   *Nota*: Os guardrails de latência e memória para o ciclo completo foram atendidos após as otimizações aplicadas em `_detect_patterns`. A performance do ciclo completo é influenciada diretamente pela eficiência dos seus componentes internos, como `_detect_patterns` e `_execute_self_reflection`.

### 6.3. Persistência Avançada para `QualiaSelfObserver` (Fase 1: Redis)
    -   *Status*: Implementação inicial concluída.
    -   *Detalhes*: `QualiaSelfObserver` foi modificado para persistir `CortexPattern` e observações de `entropy_delta` em uma instância Redis. 
        -   O cliente Redis é configurável (host, port, db) na inicialização do `QualiaSelfObserver`.
        -   Se a conexão com Redis falhar, o observador opera em modo fallback, utilizando apenas o buffer em memória.
        -   `CortexPattern`s são serializados (incluindo a conversão de `np.ndarray` para lista) e armazenados individualmente usando chaves no formato `qualia:observer:pattern:{pattern_id}`.
        -   Observações de `entropy_delta` são armazenadas em listas Redis, usando chaves como `qualia:observer:entropy_deltas:trace:{trace_id}` ou uma chave genérica se `trace_id` não estiver disponível.
        -   Os métodos `get_observed_patterns` e `get_entropy_observations` atualmente retornam dados do buffer em memória; a leitura direta do Redis pode ser uma melhoria futura.
     -   *Dependência Adicionada*: `redis>=4.0,<5.0` (ver `requirements.txt` e `requirements-test.txt`).
    -   *Dependência Adicionada*: `portalocker>=3.2.0` para travas de arquivo
        portáveis no `JsonStore`.

    *Resultados de Benchmark (Ciclo Completo do Córtex - `process_qast_cycle`)*:

    | Métrica                                  | Valor Medido                         | Guardrail   |
    | ---------------------------------------- | ------------------------------------ | ----------- |
    | Latência Média `process_qast_cycle`      | *(a ser preenchido pelo CI)*        | N/A         |
    | Latência P95 `process_qast_cycle`        | 436.417 ms                           | < 300 ms    |
    | Pico de Heap Inc. `process_qast_cycle`   | 4.347 MB                             | < 10 MB     |

    *Nota*: Estes guardrails são propostas iniciais e podem ser ajustados com base nos resultados de execuções de CI e análises de performance mais detalhadas. O objetivo é garantir que o ciclo completo da consciência permaneça eficiente. 