#!/usr/bin/env python3
"""
QUALIA Benchmark Analysis Tool
Analisa resultados do benchmark offline e gera relatórios detalhados.
"""

import sys
import os
import pandas as pd
import numpy as np
import json
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from datetime import datetime
import argparse

# Setup
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

try:
    from qualia.utils.logger import get_logger, setup_logging
    setup_logging()
    logger = get_logger(__name__)
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

class BenchmarkAnalyzer:
    """Analisador de resultados de benchmark."""
    
    def __init__(self, results_file: str):
        """Inicializar analisador."""
        self.results_file = results_file
        self.data = None
        self.df = None
        
    def load_results(self) -> bool:
        """Carregar resultados do benchmark."""
        try:
            with open(self.results_file, 'r') as f:
                self.data = json.load(f)
            
            self.df = pd.DataFrame(self.data['results'])
            logger.info(f"✅ Carregados {len(self.df)} resultados de {self.results_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao carregar resultados: {e}")
            return False
    
    def generate_summary_report(self) -> str:
        """Gerar relatório resumido."""
        if self.df is None:
            return "❌ Nenhum dado carregado"
        
        report = []
        report.append("=" * 60)
        report.append("🏆 QUALIA BENCHMARK ANALYSIS REPORT")
        report.append("=" * 60)
        report.append("")
        
        # Metadata
        metadata = self.data.get('metadata', {})
        report.append(f"📅 Período: {metadata.get('start_date', 'N/A')} → {metadata.get('end_date', 'N/A')}")
        report.append(f"📊 Total de runs: {len(self.df)}")
        report.append(f"🎯 Símbolos testados: {self.df['symbol'].nunique()}")
        report.append("")
        
        # Top performers por métrica
        report.append("🏆 TOP PERFORMERS:")
        report.append("")
        
        # Melhor Sharpe Ratio
        best_sharpe = self.df.loc[self.df['sharpe_ratio'].idxmax()]
        report.append(f"📈 MELHOR SHARPE RATIO: {best_sharpe['sharpe_ratio']:.3f}")
        report.append(f"   Symbol: {best_sharpe['symbol']}")
        report.append(f"   Params: price_amp={best_sharpe['price_amp']:.1f}, news_amp={best_sharpe['news_amp']:.1f}, min_conf={best_sharpe['min_conf']:.2f}")
        report.append(f"   Return: {best_sharpe['total_return']:.3f}, Drawdown: {best_sharpe['max_drawdown']:.3f}")
        report.append("")
        
        # Melhor Return
        best_return = self.df.loc[self.df['total_return'].idxmax()]
        report.append(f"💰 MELHOR RETURN: {best_return['total_return']:.3f}")
        report.append(f"   Symbol: {best_return['symbol']}")
        report.append(f"   Params: price_amp={best_return['price_amp']:.1f}, news_amp={best_return['news_amp']:.1f}, min_conf={best_return['min_conf']:.2f}")
        report.append(f"   Sharpe: {best_return['sharpe_ratio']:.3f}, Drawdown: {best_return['max_drawdown']:.3f}")
        report.append("")
        
        # Menor Drawdown (mais próximo de 0)
        best_dd = self.df.loc[self.df['max_drawdown'].idxmax()]
        report.append(f"🛡️ MENOR DRAWDOWN: {best_dd['max_drawdown']:.3f}")
        report.append(f"   Symbol: {best_dd['symbol']}")
        report.append(f"   Params: price_amp={best_dd['price_amp']:.1f}, news_amp={best_dd['news_amp']:.1f}, min_conf={best_dd['min_conf']:.2f}")
        report.append(f"   Sharpe: {best_dd['sharpe_ratio']:.3f}, Return: {best_dd['total_return']:.3f}")
        report.append("")
        
        # Estatísticas gerais
        report.append("📊 ESTATÍSTICAS GERAIS:")
        report.append(f"   Sharpe Ratio - Média: {self.df['sharpe_ratio'].mean():.3f}, Std: {self.df['sharpe_ratio'].std():.3f}")
        report.append(f"   Total Return - Média: {self.df['total_return'].mean():.3f}, Std: {self.df['total_return'].std():.3f}")
        report.append(f"   Max Drawdown - Média: {self.df['max_drawdown'].mean():.3f}, Std: {self.df['max_drawdown'].std():.3f}")
        report.append(f"   Win Rate - Média: {self.df['win_rate'].mean():.3f}, Std: {self.df['win_rate'].std():.3f}")
        report.append("")
        
        # Análise por parâmetro
        report.append("🔍 ANÁLISE POR PARÂMETRO:")
        report.append("")
        
        # Price Amplification
        price_analysis = self.df.groupby('price_amp')['sharpe_ratio'].agg(['mean', 'std', 'count'])
        best_price_amp = price_analysis['mean'].idxmax()
        report.append(f"📊 PRICE_AMP - Melhor: {best_price_amp:.1f} (Sharpe médio: {price_analysis.loc[best_price_amp, 'mean']:.3f})")
        
        # News Amplification
        news_analysis = self.df.groupby('news_amp')['sharpe_ratio'].agg(['mean', 'std', 'count'])
        best_news_amp = news_analysis['mean'].idxmax()
        report.append(f"📰 NEWS_AMP - Melhor: {best_news_amp:.1f} (Sharpe médio: {news_analysis.loc[best_news_amp, 'mean']:.3f})")
        
        # Min Confidence
        conf_analysis = self.df.groupby('min_conf')['sharpe_ratio'].agg(['mean', 'std', 'count'])
        best_min_conf = conf_analysis['mean'].idxmax()
        report.append(f"🎯 MIN_CONF - Melhor: {best_min_conf:.2f} (Sharpe médio: {conf_analysis.loc[best_min_conf, 'mean']:.3f})")
        report.append("")
        
        # Top 10 combinações
        report.append("🏆 TOP 10 COMBINAÇÕES (por Sharpe Ratio):")
        top_10 = self.df.nlargest(10, 'sharpe_ratio')
        for i, (_, row) in enumerate(top_10.iterrows(), 1):
            report.append(f"   {i:2d}. {row['symbol']:8s} | p={row['price_amp']:4.1f} n={row['news_amp']:4.1f} c={row['min_conf']:.2f} | Sharpe={row['sharpe_ratio']:6.3f} Return={row['total_return']:6.3f}")
        
        report.append("")
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def generate_parameter_heatmaps(self, output_dir: str = "data/plots"):
        """Gerar heatmaps de parâmetros."""
        if self.df is None:
            logger.error("❌ Nenhum dado carregado")
            return
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Configurar estilo
        plt.style.use('seaborn-v0_8')
        
        # Heatmap: Price Amp vs News Amp (média do Sharpe Ratio)
        pivot_sharpe = self.df.groupby(['price_amp', 'news_amp'])['sharpe_ratio'].mean().unstack()
        
        plt.figure(figsize=(12, 8))
        sns.heatmap(pivot_sharpe, annot=True, fmt='.3f', cmap='RdYlGn', center=0)
        plt.title('Sharpe Ratio Médio: Price Amplification vs News Amplification')
        plt.xlabel('News Amplification')
        plt.ylabel('Price Amplification')
        plt.tight_layout()
        plt.savefig(f"{output_dir}/heatmap_price_vs_news_sharpe.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        # Heatmap: Price Amp vs Min Confidence
        pivot_conf = self.df.groupby(['price_amp', 'min_conf'])['sharpe_ratio'].mean().unstack()
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(pivot_conf, annot=True, fmt='.3f', cmap='RdYlGn', center=0)
        plt.title('Sharpe Ratio Médio: Price Amplification vs Min Confidence')
        plt.xlabel('Min Confidence')
        plt.ylabel('Price Amplification')
        plt.tight_layout()
        plt.savefig(f"{output_dir}/heatmap_price_vs_conf_sharpe.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        # Heatmap: News Amp vs Min Confidence
        pivot_news_conf = self.df.groupby(['news_amp', 'min_conf'])['sharpe_ratio'].mean().unstack()
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(pivot_news_conf, annot=True, fmt='.3f', cmap='RdYlGn', center=0)
        plt.title('Sharpe Ratio Médio: News Amplification vs Min Confidence')
        plt.xlabel('Min Confidence')
        plt.ylabel('News Amplification')
        plt.tight_layout()
        plt.savefig(f"{output_dir}/heatmap_news_vs_conf_sharpe.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"📊 Heatmaps salvos em: {output_dir}")
    
    def generate_distribution_plots(self, output_dir: str = "data/plots"):
        """Gerar gráficos de distribuição."""
        if self.df is None:
            logger.error("❌ Nenhum dado carregado")
            return
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Distribuições das métricas principais
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Sharpe Ratio
        axes[0, 0].hist(self.df['sharpe_ratio'], bins=30, alpha=0.7, color='blue')
        axes[0, 0].axvline(self.df['sharpe_ratio'].mean(), color='red', linestyle='--', label=f'Média: {self.df["sharpe_ratio"].mean():.3f}')
        axes[0, 0].set_title('Distribuição do Sharpe Ratio')
        axes[0, 0].set_xlabel('Sharpe Ratio')
        axes[0, 0].set_ylabel('Frequência')
        axes[0, 0].legend()
        
        # Total Return
        axes[0, 1].hist(self.df['total_return'], bins=30, alpha=0.7, color='green')
        axes[0, 1].axvline(self.df['total_return'].mean(), color='red', linestyle='--', label=f'Média: {self.df["total_return"].mean():.3f}')
        axes[0, 1].set_title('Distribuição do Total Return')
        axes[0, 1].set_xlabel('Total Return')
        axes[0, 1].set_ylabel('Frequência')
        axes[0, 1].legend()
        
        # Max Drawdown
        axes[1, 0].hist(self.df['max_drawdown'], bins=30, alpha=0.7, color='red')
        axes[1, 0].axvline(self.df['max_drawdown'].mean(), color='blue', linestyle='--', label=f'Média: {self.df["max_drawdown"].mean():.3f}')
        axes[1, 0].set_title('Distribuição do Max Drawdown')
        axes[1, 0].set_xlabel('Max Drawdown')
        axes[1, 0].set_ylabel('Frequência')
        axes[1, 0].legend()
        
        # Win Rate
        axes[1, 1].hist(self.df['win_rate'], bins=30, alpha=0.7, color='purple')
        axes[1, 1].axvline(self.df['win_rate'].mean(), color='orange', linestyle='--', label=f'Média: {self.df["win_rate"].mean():.3f}')
        axes[1, 1].set_title('Distribuição da Win Rate')
        axes[1, 1].set_xlabel('Win Rate')
        axes[1, 1].set_ylabel('Frequência')
        axes[1, 1].legend()
        
        plt.tight_layout()
        plt.savefig(f"{output_dir}/distributions.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"📊 Gráficos de distribuição salvos em: {output_dir}")
    
    def export_best_configs(self, output_file: str = "data/best_configs.json", top_n: int = 10):
        """Exportar melhores configurações."""
        if self.df is None:
            logger.error("❌ Nenhum dado carregado")
            return
        
        # Top N por Sharpe Ratio
        top_configs = self.df.nlargest(top_n, 'sharpe_ratio')
        
        configs = []
        for _, row in top_configs.iterrows():
            config = {
                'rank': len(configs) + 1,
                'symbol': row['symbol'],
                'parameters': {
                    'price_amplification': float(row['price_amp']),
                    'news_amplification': float(row['news_amp']),
                    'min_confidence': float(row['min_conf'])
                },
                'metrics': {
                    'sharpe_ratio': float(row['sharpe_ratio']),
                    'total_return': float(row['total_return']),
                    'max_drawdown': float(row['max_drawdown']),
                    'win_rate': float(row['win_rate']),
                    'volatility': float(row['volatility']),
                    'num_trades': int(row['num_trades'])
                }
            }
            configs.append(config)
        
        # Salvar configurações
        export_data = {
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'source_file': self.results_file,
                'total_tested': len(self.df),
                'top_n': top_n
            },
            'best_configurations': configs
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Top {top_n} configurações exportadas para: {output_file}")

def main():
    """Função principal."""
    parser = argparse.ArgumentParser(description='Analisar resultados do benchmark QUALIA')
    parser.add_argument('results_file', help='Arquivo JSON com resultados do benchmark')
    parser.add_argument('--plots', action='store_true', help='Gerar gráficos')
    parser.add_argument('--export', action='store_true', help='Exportar melhores configurações')
    parser.add_argument('--top-n', type=int, default=10, help='Número de melhores configurações para exportar')
    
    args = parser.parse_args()
    
    # Verificar se arquivo existe
    if not os.path.exists(args.results_file):
        logger.error(f"❌ Arquivo não encontrado: {args.results_file}")
        return
    
    # Inicializar analisador
    analyzer = BenchmarkAnalyzer(args.results_file)
    
    if not analyzer.load_results():
        logger.error("❌ Falha ao carregar resultados")
        return
    
    # Gerar relatório
    report = analyzer.generate_summary_report()
    print(report)
    
    # Salvar relatório
    report_file = args.results_file.replace('.json', '_report.txt')
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    logger.info(f"📄 Relatório salvo em: {report_file}")
    
    # Gerar gráficos se solicitado
    if args.plots:
        analyzer.generate_parameter_heatmaps()
        analyzer.generate_distribution_plots()
    
    # Exportar melhores configurações se solicitado
    if args.export:
        export_file = args.results_file.replace('.json', '_best_configs.json')
        analyzer.export_best_configs(export_file, args.top_n)

if __name__ == "__main__":
    main()
