# SimulationQASTCore – `run_internal_reality_simulation`

Este documento descreve o método responsável por evoluir o universo social do QUALIA até que um vetor de intenção estável seja identificado.

## V<PERSON><PERSON>eral

`run_internal_reality_simulation` executa ciclos sucessivos do `SocialSimulationUniverse`, processando as ações prováveis de cada Persona. O loop termina quando a diferença entre dois resumos consecutivos do campo social fica abaixo de um limite configurado ou quando o número máximo de ciclos é atingido.

### Parâmetros de Configuração

- `internal_stability_threshold` – diferença máxima aceita entre dois resumos consecutivos do campo social. Valores menores fazem a simulação parar apenas quando o universo interno estiver muito estabilizado.
- `max_internal_cycles` – quantidade máxima de ciclos executados em busca do equilíbrio. Por padrão utiliza `simulation_steps_per_cycle` quando não definido.

### Formato de Retorno

A função devolve um dicionário com três entradas principais:

- `intention_vector` – vetor normalizado `[buy, sell, hold]` representando a média das intenções observadas durante os ciclos executados.
- `field_summary` – resumo final do campo de influência social, retornado por `SocialSimulationUniverse.get_field_summary()`.
- `current_field` – estado estabilizado do campo social no momento do término da simulação.

Essas informações são utilizadas pelo `QASTOracleDecisionEngine` para confrontar a percepção do mercado externo com a realidade interna e determinar a decisão final de trading.
