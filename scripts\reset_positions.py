import os
import argparse
import json
from qualia.utils.logger import get_logger
from qualia.config import config
from qualia.utils.logging_config import init_logging

# Configurar logger
logger = get_logger("reset_positions")


def reset_positions(base_dir=None):
    """Limpa todas as posições abertas do sistema QUALIA."""
    if base_dir is None:
        base_dir = os.getcwd()

    # Caminho para o arquivo de cache de posições abertas
    positions_file = config.open_positions_file

    # Verificar se o arquivo existe
    if os.path.exists(positions_file):
        try:
            # Fazer backup do arquivo atual
            backup_file = positions_file + ".bak"
            os.rename(positions_file, backup_file)
            logger.info(f"Backup das posições abertas criado em {backup_file}")

            # Criar novo arquivo com dicionário vazio
            with open(positions_file, "w") as f:
                json.dump({}, f)
            logger.info(f"Arquivo de posições abertas resetado: {positions_file}")
        except Exception as e:
            logger.error(f"Erro ao resetar posições: {str(e)}")
            return False
    else:
        # Se o arquivo não existe, criar diretório e arquivo vazio
        os.makedirs(os.path.dirname(positions_file), exist_ok=True)
        with open(positions_file, "w") as f:
            json.dump({}, f)
        logger.info(f"Novo arquivo de posições abertas criado: {positions_file}")

    # Também resetar os arquivos de estado de wallet se existirem
    wallet_file = os.path.join(config.cache_dir, "wallet_state.json")
    if os.path.exists(wallet_file):
        try:
            # Ler o arquivo atual para preservar o capital inicial
            with open(wallet_file, "r") as f:
                wallet_data = json.load(f)

            # Fazer backup
            backup_wallet = wallet_file + ".bak"
            os.rename(wallet_file, backup_wallet)
            logger.info(f"Backup do estado da wallet criado em {backup_wallet}")

            # Criar novo estado com posições zeradas
            initial_capital = wallet_data.get("initial_capital", 1000.0)
            new_wallet = {
                "initial_capital": initial_capital,
                "current_capital": initial_capital,
                "available_cash": initial_capital,
                "positions_value": 0.0,
                "total_pnl": 0.0,
                "total_pnl_pct": 0.0,
                "current_drawdown": 0.0,
                "max_drawdown": 0.0,
                "win_rate": 0.0,
                "loss_rate": 0.0,
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
            }

            with open(wallet_file, "w") as f:
                json.dump(new_wallet, f, indent=2)
            logger.info(f"Estado da wallet resetado: {wallet_file}")
        except Exception as e:
            logger.error(f"Erro ao resetar wallet: {str(e)}")

    return True


def main():
    parser = argparse.ArgumentParser(
        description="Resetar posições abertas do sistema QUALIA"
    )
    parser.add_argument(
        "--base_dir", help="Diretório base do projeto QUALIA", default=None
    )

    args = parser.parse_args()

    # Aviso é gerado via ``logger.warning`` se init_logging já foi chamado anteriormente
    init_logging(
        log_level="INFO",
        enable_console=True,
        enable_structured=True,
    )

    if reset_positions(args.base_dir):
        logger.info("Reset de posições concluído com sucesso!")
    else:
        logger.error("Falha no reset de posições.")


if __name__ == "__main__":
    main()
