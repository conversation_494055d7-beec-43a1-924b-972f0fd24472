#!/usr/bin/env python3
"""
Demonstração completa D-02: BayesOpt Microservice Implementation.
Mostra todas as funcionalidades implementadas e integração com QUALIA.
"""

import os
import sys
import asyncio
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.services.bayes_client import BayesOptClient, BayesClientConfig
from qualia.optimization.bayesian_optimizer import BayesianOptimizer, OptimizationConfig
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


async def demo_distributed_client():
    """Demonstra o cliente distribuído."""
    print("🌐 DEMO: CLIENTE DISTRIBUÍDO")
    print("-" * 50)
    
    # Configure client
    config = BayesClientConfig(
        service_url="http://localhost:8080",
        fallback_to_local=True,
        timeout_seconds=5.0
    )
    
    async with BayesOptClient(config) as client:
        # Check service health
        health = await client.check_service_health()
        print(f"   🏥 Service Health: {'✅ OK' if health else '❌ Offline'}")
        
        # Test parameter suggestion
        print("\n   💡 Testando sugestão de parâmetros...")
        result = await client.suggest_parameters(
            study_name="demo_study",
            symbol="BTCUSDT",
            price_amp_range=(1.0, 10.0),
            news_amp_range=(1.0, 15.0),
            min_conf_range=(0.2, 0.8)
        )
        
        print(f"   📊 Source: {result['source']}")
        print(f"   🎯 Trial: {result['trial_number']}")
        params = result['parameters']
        print(f"   📈 Price Amp: {params['price_amplification']:.3f}")
        print(f"   📰 News Amp: {params['news_amplification']:.3f}")
        print(f"   🎚️  Min Conf: {params['min_confidence']:.3f}")
        
        # Test result reporting
        print("\n   📈 Testando report de resultado...")
        report_result = await client.report_result(
            study_name="demo_study",
            symbol="BTCUSDT",
            trial_id=result['trial_id'],
            objective_value=2.5,
            metrics={
                "sharpe_ratio": 3.0,
                "pnl_24h": 1500.0,
                "cost_ratio_pct": 45.0
            },
            duration_seconds=12.5,
            trial_metadata=result
        )
        
        print(f"   📊 Source: {report_result['source']}")
        print(f"   🎯 Trials: {report_result['n_trials']}")
        print(f"   🏆 Best Value: {report_result.get('best_value', 'N/A')}")
        
        return True


async def demo_integrated_optimizer():
    """Demonstra o BayesianOptimizer integrado com microserviço."""
    print("\n🔧 DEMO: BAYESIAN OPTIMIZER INTEGRADO")
    print("-" * 50)
    
    # Configure optimizer for distributed mode
    config = OptimizationConfig(
        n_trials_per_cycle=5,  # Reduced for demo
        use_distributed_optimization=True,
        bayes_service_url="http://localhost:8080",
        distributed_fallback_enabled=True,
        price_amp_range=(2.0, 8.0),
        news_amp_range=(2.0, 12.0),
        min_conf_range=(0.3, 0.7)
    )
    
    optimizer = BayesianOptimizer(config, use_realistic_evaluation=False)
    
    print(f"   🌐 Modo distribuído: {'✅ Habilitado' if config.use_distributed_optimization else '❌ Desabilitado'}")
    print(f"   🔄 Fallback local: {'✅ Habilitado' if config.distributed_fallback_enabled else '❌ Desabilitado'}")
    print(f"   🎯 Trials por ciclo: {config.n_trials_per_cycle}")
    
    # Test optimization
    print("\n   🚀 Executando otimização distribuída...")
    start_time = time.time()
    
    try:
        result = await optimizer.optimize_symbol("ETHUSDT")
        duration = time.time() - start_time
        
        if result:
            print(f"   ✅ Otimização concluída em {duration:.1f}s")
            print(f"   🎯 Objetivo: {result.objective_value:.4f}")
            print(f"   📊 Parâmetros:")
            for key, value in result.parameters.items():
                print(f"      • {key}: {value:.3f}")
            print(f"   📈 Métricas: {result.metrics}")
        else:
            print("   ❌ Otimização falhou")
            return False
            
    except Exception as e:
        print(f"   ❌ Erro na otimização: {e}")
        return False
    
    return True


def demo_microservice_features():
    """Demonstra funcionalidades do microserviço."""
    print("\n🚀 DEMO: FUNCIONALIDADES DO MICROSERVIÇO")
    print("-" * 50)
    
    from fastapi.testclient import TestClient
    from qualia.services.bayes_service import app
    
    client = TestClient(app)
    
    # Test health
    print("   🏥 Health Check...")
    response = client.get("/health")
    if response.status_code == 200:
        data = response.json()
        print(f"      Status: {data['status']}")
        print(f"      Uptime: {data['uptime_seconds']:.1f}s")
        print(f"      Studies: {data['active_studies']}")
        print(f"      Memory: {data['memory_usage_mb']:.1f} MB")
    
    # Test multiple studies
    print("\n   📚 Testando múltiplos studies...")
    symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
    
    for symbol in symbols:
        # Suggest parameters
        suggest_response = client.post("/suggest", json={
            "study_name": f"multi_study_{symbol.lower()}",
            "symbol": symbol,
            "price_amp_range": [1.0, 10.0],
            "news_amp_range": [1.0, 15.0],
            "min_conf_range": [0.2, 0.8],
            "sampler_type": "TPE"
        })
        
        if suggest_response.status_code == 200:
            data = suggest_response.json()
            print(f"      {symbol}: Trial {data['trial_number']} - Price Amp {data['parameters']['price_amplification']:.2f}")
            
            # Report result
            client.post("/report", json={
                "study_name": f"multi_study_{symbol.lower()}",
                "symbol": symbol,
                "trial_id": data["trial_id"],
                "objective_value": 1.0 + (hash(symbol) % 100) / 100.0,  # Fake objective
                "metrics": {"sharpe_ratio": 2.0, "pnl_24h": 1000.0},
                "duration_seconds": 5.0,
                "success": True
            })
    
    # List all studies
    print("\n   📋 Listando studies...")
    response = client.get("/studies")
    if response.status_code == 200:
        data = response.json()
        print(f"      Total studies: {data['total_count']}")
        for study in data['studies'][:3]:  # Show first 3
            print(f"      • {study['study_name']} ({study['symbol']}): {study['n_trials']} trials")
    
    return True


async def main():
    """Função principal da demonstração."""
    print("🎯 DEMONSTRAÇÃO COMPLETA D-02: BAYESOPT MICROSERVICE")
    print("=" * 80)
    print("Implementação de microserviço para otimização Bayesiana distribuída")
    print("Integração com QUALIA BayesianOptimizer + Optuna + SQLite")
    print()
    
    success_count = 0
    total_tests = 4
    
    # Demo 1: Microservice features
    try:
        if demo_microservice_features():
            success_count += 1
            print("   ✅ Funcionalidades do microserviço: OK")
        else:
            print("   ❌ Funcionalidades do microserviço: FALHOU")
    except Exception as e:
        print(f"   ❌ Funcionalidades do microserviço: ERRO - {e}")
    
    # Demo 2: Distributed client
    try:
        if await demo_distributed_client():
            success_count += 1
            print("   ✅ Cliente distribuído: OK")
        else:
            print("   ❌ Cliente distribuído: FALHOU")
    except Exception as e:
        print(f"   ❌ Cliente distribuído: ERRO - {e}")
    
    # Demo 3: Integrated optimizer
    try:
        if await demo_integrated_optimizer():
            success_count += 1
            print("   ✅ Optimizer integrado: OK")
        else:
            print("   ❌ Optimizer integrado: FALHOU")
    except Exception as e:
        print(f"   ❌ Optimizer integrado: ERRO - {e}")
    
    # Demo 4: Performance test
    try:
        print("\n⚡ DEMO: TESTE DE PERFORMANCE")
        print("-" * 50)
        
        start_time = time.time()
        
        # Simulate multiple concurrent optimizations
        tasks = []
        for i in range(3):
            config = BayesClientConfig(
                service_url="http://localhost:8080",
                fallback_to_local=True
            )
            
            async def optimize_task(task_id):
                async with BayesOptClient(config) as client:
                    result = await client.suggest_parameters(
                        study_name=f"perf_test_{task_id}",
                        symbol="BTCUSDT"
                    )
                    await client.report_result(
                        study_name=f"perf_test_{task_id}",
                        symbol="BTCUSDT",
                        trial_id=result['trial_id'],
                        objective_value=1.5 + task_id * 0.1,
                        metrics={"sharpe_ratio": 2.0},
                        duration_seconds=1.0,
                        trial_metadata=result
                    )
                    return task_id
            
            tasks.append(optimize_task(i))
        
        results = await asyncio.gather(*tasks)
        duration = time.time() - start_time
        
        print(f"   🚀 {len(results)} otimizações concorrentes em {duration:.2f}s")
        print(f"   ⚡ Performance: {len(results)/duration:.1f} otimizações/segundo")
        
        success_count += 1
        print("   ✅ Teste de performance: OK")
        
    except Exception as e:
        print(f"   ❌ Teste de performance: ERRO - {e}")
    
    # Final summary
    print(f"\n🎉 RESUMO FINAL D-02")
    print("=" * 80)
    print(f"✅ Testes passaram: {success_count}/{total_tests}")
    print(f"📊 Taxa de sucesso: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print("\n🚀 D-02 BAYESOPT MICROSERVICE IMPLEMENTADO COM SUCESSO!")
        print("   • Microserviço FastAPI + Optuna + SQLite: ✅")
        print("   • Cliente distribuído com fallback: ✅")
        print("   • Integração BayesianOptimizer: ✅")
        print("   • Performance concorrente: ✅")
        print("   • Persistência de studies: ✅")
        print("   • REST API completa: ✅")
        print("\n🎯 Pronto para D-03: KuCoin Feed Integration!")
    else:
        print(f"\n⚠️  Alguns testes falharam ({total_tests-success_count}/{total_tests})")
        print("   Verifique os logs acima para detalhes")
    
    return success_count == total_tests


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
