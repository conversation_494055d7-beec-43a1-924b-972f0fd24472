import math
import pandas as pd
from qualia.adaptive_evolution import AdaptiveConsciousnessEvolution


def test_auto_depth_returns_nearest():
    AdaptiveConsciousnessEvolution._otoc_lookup_df = pd.DataFrame(
        {
            "num_qubits": [3, 3],
            "scr_depth": [1, 2],
            "noise": [0.0, 0.0],
            "otoc": [0.95, 0.99],
        }
    )
    depth = AdaptiveConsciousnessEvolution._auto_scr_depth(3, 0.98)
    assert depth == 2


def test_auto_depth_fallback():
    AdaptiveConsciousnessEvolution._otoc_lookup_df = pd.DataFrame(
        columns=["num_qubits", "scr_depth", "noise", "otoc"]
    )
    depth = AdaptiveConsciousnessEvolution._auto_scr_depth(4)
    assert depth == math.ceil(1.5 * 4)
