{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Retrocausal Mode Backtest\n", "Este notebook demonstra como ativar o modo retrocausal com `apply_retrocausality` e compara o resultado de um backtest simplificado."]}, {"cell_type": "code", "metadata": {}, "execution_count": null, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from src.qualia.core.retrocausality import apply_retrocausality\n", "\n", "rng = np.random.default_rng(seed=42)\n", "prices = 100 + np.cumsum(rng.normal(0, 1, size=100))\n", "returns = np.diff(prices, prepend=prices[0]) / prices[:-1]\n", "\n", "# Estrategia simples: sinal pela direcao do retorno\n", "signals_base = np.sign(returns)\n", "pnl_base = np.cumsum(signals_base * returns)\n", "\n", "# Ativa retrocausalidade misturando retorno com uma estimativa futura\n", "future = np.roll(returns, -1)\n", "future[-1] = 0.0\n", "retro_returns = apply_retrocausality(returns, future, gamma=0.3)\n", "signals_retro = np.sign(retro_returns)\n", "pnl_retro = np.cumsum(signals_retro * returns)\n", "\n", "plt.plot(pnl_base, label='Sem retrocausalidade')\n", "plt.plot(pnl_retro, label='Com retrocausalidade')\n", "plt.legend()\n", "plt.xlabel('Passo')\n", "plt.ylabel('PnL acumulado')\n", "plt.show()\n", "\n", "print('PnL final sem retrocausalidade:', pnl_base[-1].round(4))\n", "print('PnL final com retrocausalidade:', pnl_retro[-1].round(4))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.10"}}, "nbformat": 4, "nbformat_minor": 5}