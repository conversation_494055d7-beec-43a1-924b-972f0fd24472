development:
  debug_mode: false
  enable_integration_tests: true
  enable_profiling: false
  enable_unit_tests: true
  profiling_output_dir: data/profiling
  simulation_mode: false
experimental:
  ab_testing:
    enabled: false
    test_ratio: 0.1
  adaptive_search_space: false
  ensemble_methods: false
  multi_objective_optimization: false
grpc:
  enabled: true
  host: localhost
  max_workers: 4
  port: 50051
  timeouts:
    connection_timeout: 10
    request_timeout: 30
integration:
  monitoring_integration:
    grafana_dashboard: false
    prometheus_enabled: false
    slack_notifications: false
  qualia_integration:
    config_update_method: grpc
    enabled: true
  trading_system:
    parameter_update_delay_seconds: 5
    validation_period_minutes: 10
monitoring:
  alerts:
    enabled: true
    optimization_failure_threshold: 3
    performance_threshold: -0.2
  enable_metrics: true
  log_level: INFO
  metrics_interval_seconds: 60
  verbose_logging: false
multi_fidelity:
  budget_allocation:
    high: 0.1
    low: 0.6
    medium: 0.3
  early_stopping_enabled: true
  early_stopping_patience: 3
  evaluation_timeout_multiplier: 1.5
  max_concurrent_evaluations: 4
  promotion_thresholds:
    low:
      max_drawdown: 0.12
      min_sharpe: 0.8
      min_trades: 5
      percentile_rank: 0.7
    medium:
      max_drawdown: 0.1
      min_sharpe: 1.2
      min_trades: 15
      percentile_rank: 0.8
  pruning:
    enabled: true
    n_startup_trials: 5
    type: MedianPruner
  sampler:
    n_startup_trials: 10
    seed: 42
    type: TPE
optimization:
  enable_multi_fidelity: true
  lookback_hours: 24
  max_concurrent_optimizations: 4
  min_trades_for_optimization: 10
  multi_fidelity_levels:
  - 1
  - 6
  - 24
  n_trials_per_cycle: 25
  objective_metric: sharpe_pnl_combined
  optimization_interval_cycles: 500
  pruning_strategy: adaptive
  weights:
    pnl_weight: 0.4
    sharpe_weight: 0.6
parameter_space:
  min_confidence:
    max: 0.8
    min: 0.2
    type: float
  news_amplification:
    max: 15.0
    min: 1.0
    type: float
  price_amplification:
    max: 10.0
    min: 1.0
    type: float
persistence:
  backup_best_configs: true
  backup_interval_hours: 6
  optimization_log_file: data/optimization_log.jsonl
  optimization_log_max_size_mb: 100
  state_file: data/bayesian_optimizer_state.json
  state_save_interval_minutes: 30
pruning:
  adaptive_adjustment_factor: 0.1
  adaptive_percentile_range:
  - 25.0
  - 75.0
  max_drawdown_threshold: 0.15
  median_interval_steps: 1
  median_n_startup_trials: 10
  median_n_warmup_steps: 5
  median_percentile: 50.0
  min_sharpe_threshold: 0.5
  min_trades_threshold: 10
  sh_min_early_stopping_rate: 0
  sh_min_resource: 1
  sh_reduction_factor: 3
safety:
  auto_rollback:
    enabled: true
    performance_degradation_threshold: 0.3
    rollback_to_previous_best: true
  parameter_bounds:
    min_confidence:
      absolute_max: 1.0
      absolute_min: 0.05
    news_amplification:
      absolute_max: 20.0
      absolute_min: 0.1
    price_amplification:
      absolute_max: 20.0
      absolute_min: 0.1
  performance_validation:
    enabled: true
    max_drawdown_threshold: 0.5
    min_sharpe_ratio: -5.0
symbols:
  active:
  - BTCUSDT
  - ETHUSDT
  - BNBUSDT
  - ADAUSDT
  - SOLUSDT
  - DOTUSDT
  - LINKUSDT
  - POLUSDT
  specific_configs:
    BTCUSDT:
      initial_params:
        min_confidence: 0.37
        news_amplification: 11.3
        price_amplification: 1.0
    ETHUSDT:
      initial_params:
        min_confidence: 0.3
        news_amplification: 1.0
        price_amplification: 10.0
