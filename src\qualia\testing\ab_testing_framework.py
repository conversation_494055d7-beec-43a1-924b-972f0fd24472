"""
QUALIA D-06: A/B Testing Framework
==================================

Framework para executar testes A/B de configurações simultaneamente.
Inclui métricas de performance, significância estatística, e integração com hot-reload.
"""

from __future__ import annotations

import asyncio
import json
import numpy as np
import pandas as pd
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, field, asdict
from enum import Enum
import logging
from pathlib import Path
import uuid
from scipy import stats

from ..utils.logger import get_logger
from ..config.hot_reload import ConfigurationHotReloader
from ..utils.event_bus import EventBus

logger = get_logger(__name__)


class ABTestStatus(Enum):
    """Status de um teste A/B."""
    PENDING = "pending"         # Aguardando início
    RUNNING = "running"         # Em execução
    COMPLETED = "completed"     # Concluído com sucesso
    STOPPED = "stopped"         # Parado manualmente
    FAILED = "failed"          # Falhou por erro


class ABTestResult(Enum):
    """Resultado de um teste A/B."""
    A_WINS = "a_wins"          # Configuração A é melhor
    B_WINS = "b_wins"          # Configuração B é melhor
    NO_DIFFERENCE = "no_diff"   # Sem diferença significativa
    INCONCLUSIVE = "inconclusive"  # Dados insuficientes


@dataclass
class ABTestConfig:
    """Configuração de um teste A/B."""
    test_id: str
    name: str
    description: str
    
    # Configurações a testar
    config_a: Dict[str, Any]
    config_b: Dict[str, Any]
    config_file_path: str
    
    # Parâmetros do teste
    duration_minutes: int = 60          # Duração do teste
    switch_interval_minutes: int = 5    # Intervalo entre mudanças A/B
    min_samples_per_config: int = 10    # Mínimo de amostras por configuração
    significance_level: float = 0.05    # Nível de significância (p-value)
    
    # Métricas a avaliar
    primary_metric: str = "sharpe_ratio"  # Métrica principal
    secondary_metrics: List[str] = field(default_factory=lambda: ["pnl_24h", "win_rate", "max_drawdown"])
    
    # Critérios de parada antecipada
    early_stopping_enabled: bool = True
    min_effect_size: float = 0.1        # Tamanho mínimo do efeito
    power_threshold: float = 0.8        # Poder estatístico mínimo
    
    # Configurações de segurança
    max_drawdown_threshold: float = 0.15  # Parar se drawdown > 15%
    min_confidence_threshold: float = 0.3  # Parar se confiança < 30%


@dataclass
class ABTestMetrics:
    """Métricas coletadas durante um teste A/B."""
    timestamp: datetime
    config_name: str  # "A" ou "B"
    
    # Métricas de performance
    sharpe_ratio: float = 0.0
    pnl_24h: float = 0.0
    win_rate: float = 0.0
    max_drawdown: float = 0.0
    total_trades: int = 0
    
    # Métricas de confiança
    decision_confidence: float = 0.0
    signal_quality: float = 0.0
    
    # Contexto adicional
    market_regime: Optional[str] = None
    symbol: Optional[str] = None
    extra_metrics: Dict[str, float] = field(default_factory=dict)


@dataclass
class ABTestStatistics:
    """Estatísticas de um teste A/B."""
    # Dados básicos
    samples_a: int = 0
    samples_b: int = 0
    
    # Estatísticas da métrica principal
    mean_a: float = 0.0
    mean_b: float = 0.0
    std_a: float = 0.0
    std_b: float = 0.0
    
    # Teste estatístico
    t_statistic: float = 0.0
    p_value: float = 1.0
    effect_size: float = 0.0  # Cohen's d
    confidence_interval: Tuple[float, float] = (0.0, 0.0)
    
    # Poder estatístico
    statistical_power: float = 0.0
    
    # Resultado
    result: ABTestResult = ABTestResult.INCONCLUSIVE
    winner: Optional[str] = None
    confidence: float = 0.0


@dataclass
class ABTestExecution:
    """Estado de execução de um teste A/B."""
    config: ABTestConfig
    status: ABTestStatus = ABTestStatus.PENDING
    
    # Timestamps
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    last_switch_time: Optional[datetime] = None
    
    # Estado atual
    current_config: str = "A"  # "A" ou "B"
    switch_count: int = 0
    
    # Dados coletados
    metrics_a: List[ABTestMetrics] = field(default_factory=list)
    metrics_b: List[ABTestMetrics] = field(default_factory=list)
    
    # Estatísticas
    statistics: ABTestStatistics = field(default_factory=ABTestStatistics)
    
    # Logs e eventos
    events: List[Dict[str, Any]] = field(default_factory=list)
    error_message: Optional[str] = None


class ABTestingFramework:
    """
    Framework para execução de testes A/B de configurações QUALIA.
    
    Funcionalidades:
    - Execução simultânea de múltiplos testes A/B
    - Alternância automática entre configurações A e B
    - Coleta de métricas de performance em tempo real
    - Análise estatística com significância e poder
    - Parada antecipada baseada em critérios estatísticos
    - Integração com sistema de hot-reload
    - Persistência de resultados e histórico
    """
    
    def __init__(self, 
                 hot_reloader: Optional[ConfigurationHotReloader] = None,
                 event_bus: Optional[EventBus] = None,
                 results_dir: str = "results/ab_tests"):
        self.hot_reloader = hot_reloader
        self.event_bus = event_bus
        self.results_dir = Path(results_dir)
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # Estado dos testes
        self.active_tests: Dict[str, ABTestExecution] = {}
        self.completed_tests: Dict[str, ABTestExecution] = {}
        
        # Callbacks para coleta de métricas
        self.metrics_callbacks: List[Callable[[str], ABTestMetrics]] = []
        
        # Tasks de execução
        self.test_tasks: Dict[str, asyncio.Task] = {}
        
        # Estado de execução
        self.is_running = False
        self.monitoring_task: Optional[asyncio.Task] = None
        
        logger.info("✅ ABTestingFramework inicializado")
    
    async def start(self):
        """Inicia o framework de A/B testing."""
        if self.is_running:
            logger.warning("Framework já está rodando")
            return
            
        self.is_running = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("🚀 ABTestingFramework iniciado")
    
    async def stop(self):
        """Para o framework e todos os testes ativos."""
        self.is_running = False
        
        # Parar todos os testes ativos
        for test_id in list(self.active_tests.keys()):
            await self.stop_test(test_id)
        
        # Parar monitoring
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("⏹️ ABTestingFramework parado")
    
    def register_metrics_callback(self, callback: Callable[[str], ABTestMetrics]):
        """Registra callback para coleta de métricas."""
        self.metrics_callbacks.append(callback)
        logger.debug(f"Callback de métricas registrado: {callback.__name__}")
    
    async def create_test(self, config: ABTestConfig) -> str:
        """
        Cria um novo teste A/B.
        
        Args:
            config: Configuração do teste
            
        Returns:
            ID do teste criado
        """
        if not config.test_id:
            config.test_id = str(uuid.uuid4())
        
        if config.test_id in self.active_tests or config.test_id in self.completed_tests:
            raise ValueError(f"Teste {config.test_id} já existe")
        
        # Validar configuração
        self._validate_test_config(config)
        
        # Criar execução
        execution = ABTestExecution(config=config)
        self.active_tests[config.test_id] = execution
        
        # Log evento
        self._log_test_event(config.test_id, "test_created", {
            "name": config.name,
            "duration_minutes": config.duration_minutes,
            "primary_metric": config.primary_metric
        })
        
        logger.info(f"📊 Teste A/B criado: {config.name} (ID: {config.test_id})")
        return config.test_id
    
    async def start_test(self, test_id: str) -> bool:
        """
        Inicia um teste A/B.
        
        Args:
            test_id: ID do teste
            
        Returns:
            True se iniciado com sucesso
        """
        if test_id not in self.active_tests:
            logger.error(f"Teste {test_id} não encontrado")
            return False
        
        execution = self.active_tests[test_id]
        
        if execution.status != ABTestStatus.PENDING:
            logger.error(f"Teste {test_id} não está pendente (status: {execution.status.value})")
            return False
        
        try:
            # Iniciar execução
            execution.status = ABTestStatus.RUNNING
            execution.start_time = datetime.now(timezone.utc)
            execution.current_config = "A"
            execution.last_switch_time = execution.start_time
            
            # Aplicar configuração inicial (A)
            await self._apply_config(execution, "A")
            
            # Criar task de execução
            self.test_tasks[test_id] = asyncio.create_task(self._run_test(test_id))
            
            # Log evento
            self._log_test_event(test_id, "test_started", {
                "start_time": execution.start_time.isoformat(),
                "initial_config": "A"
            })
            
            logger.info(f"🚀 Teste A/B iniciado: {execution.config.name}")
            return True
            
        except Exception as e:
            execution.status = ABTestStatus.FAILED
            execution.error_message = str(e)
            logger.error(f"Erro ao iniciar teste {test_id}: {e}")
            return False

    async def stop_test(self, test_id: str, reason: str = "manual_stop") -> bool:
        """
        Para um teste A/B.

        Args:
            test_id: ID do teste
            reason: Motivo da parada

        Returns:
            True se parado com sucesso
        """
        if test_id not in self.active_tests:
            logger.error(f"Teste {test_id} não encontrado")
            return False

        execution = self.active_tests[test_id]

        if execution.status != ABTestStatus.RUNNING:
            logger.warning(f"Teste {test_id} não está rodando (status: {execution.status.value})")
            return True

        try:
            # Parar task de execução
            if test_id in self.test_tasks:
                self.test_tasks[test_id].cancel()
                try:
                    await self.test_tasks[test_id]
                except asyncio.CancelledError:
                    pass
                del self.test_tasks[test_id]

            # Atualizar status
            execution.status = ABTestStatus.STOPPED
            execution.end_time = datetime.now(timezone.utc)

            # Calcular estatísticas finais
            await self._calculate_final_statistics(execution)

            # Salvar resultados
            await self._save_test_results(execution)

            # Mover para testes concluídos
            self.completed_tests[test_id] = execution
            del self.active_tests[test_id]

            # Log evento
            self._log_test_event(test_id, "test_stopped", {
                "reason": reason,
                "end_time": execution.end_time.isoformat(),
                "duration_minutes": (execution.end_time - execution.start_time).total_seconds() / 60,
                "result": execution.statistics.result.value
            })

            logger.info(f"⏹️ Teste A/B parado: {execution.config.name} (motivo: {reason})")
            return True

        except Exception as e:
            logger.error(f"Erro ao parar teste {test_id}: {e}")
            return False

    async def get_test_status(self, test_id: str) -> Optional[Dict[str, Any]]:
        """Retorna status detalhado de um teste."""
        execution = self.active_tests.get(test_id) or self.completed_tests.get(test_id)

        if not execution:
            return None

        # Calcular estatísticas atuais
        if execution.status == ABTestStatus.RUNNING:
            await self._update_test_statistics(execution)

        status = {
            "test_id": test_id,
            "name": execution.config.name,
            "status": execution.status.value,
            "current_config": execution.current_config,
            "switch_count": execution.switch_count,
            "samples_a": len(execution.metrics_a),
            "samples_b": len(execution.metrics_b),
            "statistics": asdict(execution.statistics),
            "progress": self._calculate_test_progress(execution)
        }

        if execution.start_time:
            status["start_time"] = execution.start_time.isoformat()
            if execution.status == ABTestStatus.RUNNING:
                elapsed = (datetime.now(timezone.utc) - execution.start_time).total_seconds() / 60
                status["elapsed_minutes"] = elapsed
                status["remaining_minutes"] = max(0, execution.config.duration_minutes - elapsed)

        if execution.end_time:
            status["end_time"] = execution.end_time.isoformat()
            status["total_duration_minutes"] = (execution.end_time - execution.start_time).total_seconds() / 60

        if execution.error_message:
            status["error_message"] = execution.error_message

        return status

    async def list_tests(self, include_completed: bool = True) -> List[Dict[str, Any]]:
        """Lista todos os testes."""
        tests = []

        # Testes ativos
        for test_id in self.active_tests:
            status = await self.get_test_status(test_id)
            if status:
                tests.append(status)

        # Testes concluídos
        if include_completed:
            for test_id in self.completed_tests:
                status = await self.get_test_status(test_id)
                if status:
                    tests.append(status)

        return sorted(tests, key=lambda x: x.get("start_time", ""), reverse=True)

    async def _run_test(self, test_id: str):
        """Loop principal de execução de um teste A/B."""
        execution = self.active_tests[test_id]
        config = execution.config

        try:
            logger.info(f"🔄 Iniciando loop de execução para teste: {config.name}")

            while execution.status == ABTestStatus.RUNNING:
                current_time = datetime.now(timezone.utc)

                # Verificar se deve terminar por tempo
                if execution.start_time:
                    elapsed_minutes = (current_time - execution.start_time).total_seconds() / 60
                    if elapsed_minutes >= config.duration_minutes:
                        logger.info(f"⏰ Teste {test_id} concluído por tempo")
                        break

                # Verificar se deve alternar configuração
                if execution.last_switch_time:
                    switch_elapsed = (current_time - execution.last_switch_time).total_seconds() / 60
                    if switch_elapsed >= config.switch_interval_minutes:
                        await self._switch_config(execution)

                # Coletar métricas
                await self._collect_metrics(execution)

                # Atualizar estatísticas
                await self._update_test_statistics(execution)

                # Verificar critérios de parada antecipada
                if config.early_stopping_enabled:
                    should_stop, reason = await self._check_early_stopping(execution)
                    if should_stop:
                        logger.info(f"🛑 Parada antecipada para teste {test_id}: {reason}")
                        self._log_test_event(test_id, "early_stopping", {"reason": reason})
                        break

                # Aguardar próxima iteração
                await asyncio.sleep(30)  # Verificar a cada 30 segundos

            # Finalizar teste
            if execution.status == ABTestStatus.RUNNING:
                execution.status = ABTestStatus.COMPLETED
                execution.end_time = datetime.now(timezone.utc)

                # Calcular estatísticas finais
                await self._calculate_final_statistics(execution)

                # Salvar resultados
                await self._save_test_results(execution)

                # Mover para concluídos
                self.completed_tests[test_id] = execution
                del self.active_tests[test_id]

                logger.info(f"✅ Teste A/B concluído: {config.name}")

                # Notificar conclusão
                if self.event_bus:
                    await self._notify_test_completion(execution)

        except asyncio.CancelledError:
            logger.info(f"🛑 Teste {test_id} cancelado")
            raise
        except Exception as e:
            logger.error(f"❌ Erro na execução do teste {test_id}: {e}")
            execution.status = ABTestStatus.FAILED
            execution.error_message = str(e)
            execution.end_time = datetime.now(timezone.utc)
        finally:
            # Cleanup
            if test_id in self.test_tasks:
                del self.test_tasks[test_id]

    async def _switch_config(self, execution: ABTestExecution):
        """Alterna entre configurações A e B."""
        try:
            # Alternar configuração
            new_config = "B" if execution.current_config == "A" else "A"

            # Aplicar nova configuração
            await self._apply_config(execution, new_config)

            # Atualizar estado
            execution.current_config = new_config
            execution.switch_count += 1
            execution.last_switch_time = datetime.now(timezone.utc)

            # Log evento
            self._log_test_event(execution.config.test_id, "config_switched", {
                "from": "A" if new_config == "B" else "B",
                "to": new_config,
                "switch_count": execution.switch_count
            })

            logger.debug(f"🔄 Configuração alternada para {new_config} (teste: {execution.config.name})")

        except Exception as e:
            logger.error(f"Erro ao alternar configuração: {e}")
            raise

    async def _apply_config(self, execution: ABTestExecution, config_name: str):
        """Aplica uma configuração (A ou B) usando hot-reload."""
        if not self.hot_reloader:
            logger.warning("Hot-reloader não disponível, configuração não aplicada")
            return

        try:
            # Selecionar configuração
            config_data = (execution.config.config_a if config_name == "A"
                          else execution.config.config_b)

            # Aplicar via hot-reload
            success = await self.hot_reloader.update_config(
                config_data,
                source=f"ab_test_{execution.config.test_id}_{config_name}"
            )

            if not success:
                raise Exception(f"Falha ao aplicar configuração {config_name}")

            logger.debug(f"✅ Configuração {config_name} aplicada (teste: {execution.config.name})")

        except Exception as e:
            logger.error(f"Erro ao aplicar configuração {config_name}: {e}")
            raise

    async def _collect_metrics(self, execution: ABTestExecution):
        """Coleta métricas do teste atual."""
        try:
            # Coletar métricas de todos os callbacks registrados
            for callback in self.metrics_callbacks:
                try:
                    metrics = callback(execution.config.test_id)
                    if metrics:
                        metrics.config_name = execution.current_config
                        metrics.timestamp = datetime.now(timezone.utc)

                        # Adicionar à lista apropriada
                        if execution.current_config == "A":
                            execution.metrics_a.append(metrics)
                        else:
                            execution.metrics_b.append(metrics)

                        logger.debug(f"📊 Métricas coletadas para config {execution.current_config}")

                except Exception as e:
                    logger.error(f"Erro em callback de métricas: {e}")

        except Exception as e:
            logger.error(f"Erro ao coletar métricas: {e}")

    async def _update_test_statistics(self, execution: ABTestExecution):
        """Atualiza estatísticas do teste."""
        try:
            stats = execution.statistics

            # Contar amostras
            stats.samples_a = len(execution.metrics_a)
            stats.samples_b = len(execution.metrics_b)

            if stats.samples_a == 0 or stats.samples_b == 0:
                return

            # Extrair valores da métrica principal
            primary_metric = execution.config.primary_metric

            values_a = [getattr(m, primary_metric, 0.0) for m in execution.metrics_a]
            values_b = [getattr(m, primary_metric, 0.0) for m in execution.metrics_b]

            # Calcular estatísticas básicas
            stats.mean_a = float(np.mean(values_a))
            stats.mean_b = float(np.mean(values_b))
            stats.std_a = float(np.std(values_a, ddof=1)) if len(values_a) > 1 else 0.0
            stats.std_b = float(np.std(values_b, ddof=1)) if len(values_b) > 1 else 0.0

            # Teste t de Student
            if len(values_a) > 1 and len(values_b) > 1:
                t_stat, p_val = stats.ttest_ind(values_a, values_b, equal_var=False)
                stats.t_statistic = float(t_stat) if not np.isnan(t_stat) else 0.0
                stats.p_value = float(p_val) if not np.isnan(p_val) else 1.0

                # Tamanho do efeito (Cohen's d)
                pooled_std = np.sqrt(((len(values_a) - 1) * stats.std_a**2 +
                                     (len(values_b) - 1) * stats.std_b**2) /
                                    (len(values_a) + len(values_b) - 2))
                if pooled_std > 0:
                    stats.effect_size = abs(stats.mean_a - stats.mean_b) / pooled_std
                else:
                    stats.effect_size = 0.0

                # Intervalo de confiança da diferença
                diff_mean = stats.mean_a - stats.mean_b
                se_diff = np.sqrt(stats.std_a**2/len(values_a) + stats.std_b**2/len(values_b))
                df = len(values_a) + len(values_b) - 2
                t_critical = stats.t.ppf(1 - execution.config.significance_level/2, df)
                margin_error = t_critical * se_diff

                stats.confidence_interval = (
                    float(diff_mean - margin_error),
                    float(diff_mean + margin_error)
                )

                # Determinar resultado
                if stats.p_value < execution.config.significance_level:
                    if stats.mean_a > stats.mean_b:
                        stats.result = ABTestResult.A_WINS
                        stats.winner = "A"
                    else:
                        stats.result = ABTestResult.B_WINS
                        stats.winner = "B"
                    stats.confidence = 1.0 - stats.p_value
                else:
                    stats.result = ABTestResult.NO_DIFFERENCE
                    stats.winner = None
                    stats.confidence = stats.p_value

            # Calcular poder estatístico (aproximação)
            if stats.effect_size > 0 and stats.samples_a > 1 and stats.samples_b > 1:
                # Usar aproximação para poder estatístico
                n_total = stats.samples_a + stats.samples_b
                stats.statistical_power = min(1.0, stats.effect_size * np.sqrt(n_total) / 2.8)

        except Exception as e:
            logger.error(f"Erro ao atualizar estatísticas: {e}")

    async def _check_early_stopping(self, execution: ABTestExecution) -> Tuple[bool, str]:
        """Verifica critérios de parada antecipada."""
        try:
            config = execution.config
            stats = execution.statistics

            # Verificar amostras mínimas
            if (stats.samples_a < config.min_samples_per_config or
                stats.samples_b < config.min_samples_per_config):
                return False, ""

            # Verificar significância estatística com tamanho de efeito suficiente
            if (stats.p_value < config.significance_level and
                stats.effect_size >= config.min_effect_size and
                stats.statistical_power >= config.power_threshold):
                return True, f"significância_atingida (p={stats.p_value:.4f}, effect_size={stats.effect_size:.3f})"

            # Verificar drawdown máximo
            if execution.metrics_a or execution.metrics_b:
                recent_metrics = (execution.metrics_a[-10:] if execution.current_config == "A"
                                else execution.metrics_b[-10:])

                if recent_metrics:
                    max_drawdown = max(m.max_drawdown for m in recent_metrics)
                    if max_drawdown > config.max_drawdown_threshold:
                        return True, f"drawdown_excessivo ({max_drawdown:.2%})"

            # Verificar confiança mínima
            if execution.metrics_a or execution.metrics_b:
                recent_metrics = (execution.metrics_a[-5:] if execution.current_config == "A"
                                else execution.metrics_b[-5:])

                if recent_metrics:
                    avg_confidence = np.mean([m.decision_confidence for m in recent_metrics])
                    if avg_confidence < config.min_confidence_threshold:
                        return True, f"confiança_baixa ({avg_confidence:.2f})"

            return False, ""

        except Exception as e:
            logger.error(f"Erro ao verificar parada antecipada: {e}")
            return False, ""

    async def _calculate_final_statistics(self, execution: ABTestExecution):
        """Calcula estatísticas finais do teste."""
        await self._update_test_statistics(execution)

        # Log estatísticas finais
        stats = execution.statistics
        self._log_test_event(execution.config.test_id, "final_statistics", {
            "samples_a": stats.samples_a,
            "samples_b": stats.samples_b,
            "mean_a": stats.mean_a,
            "mean_b": stats.mean_b,
            "p_value": stats.p_value,
            "effect_size": stats.effect_size,
            "result": stats.result.value,
            "winner": stats.winner,
            "confidence": stats.confidence
        })

    async def _save_test_results(self, execution: ABTestExecution):
        """Salva resultados do teste em arquivo."""
        try:
            # Preparar dados para salvamento
            results = {
                "test_config": asdict(execution.config),
                "execution_info": {
                    "status": execution.status.value,
                    "start_time": execution.start_time.isoformat() if execution.start_time else None,
                    "end_time": execution.end_time.isoformat() if execution.end_time else None,
                    "switch_count": execution.switch_count,
                    "error_message": execution.error_message
                },
                "statistics": asdict(execution.statistics),
                "metrics_summary": {
                    "total_samples_a": len(execution.metrics_a),
                    "total_samples_b": len(execution.metrics_b),
                    "metrics_a": [asdict(m) for m in execution.metrics_a[-100:]],  # Últimas 100
                    "metrics_b": [asdict(m) for m in execution.metrics_b[-100:]]   # Últimas 100
                },
                "events": execution.events[-50:]  # Últimos 50 eventos
            }

            # Salvar arquivo
            filename = f"ab_test_{execution.config.test_id}_{execution.start_time.strftime('%Y%m%d_%H%M%S')}.json"
            filepath = self.results_dir / filename

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)

            logger.info(f"💾 Resultados salvos: {filepath}")

        except Exception as e:
            logger.error(f"Erro ao salvar resultados: {e}")

    async def _monitoring_loop(self):
        """Loop de monitoramento geral."""
        while self.is_running:
            try:
                # Verificar saúde dos testes ativos
                for test_id, execution in list(self.active_tests.items()):
                    if execution.status == ABTestStatus.RUNNING:
                        # Verificar se task ainda está rodando
                        if test_id in self.test_tasks and self.test_tasks[test_id].done():
                            logger.warning(f"Task do teste {test_id} terminou inesperadamente")
                            await self.stop_test(test_id, "task_terminated")

                # Aguardar próxima verificação
                await asyncio.sleep(60)  # Verificar a cada minuto

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Erro no loop de monitoramento: {e}")
                await asyncio.sleep(10)

    async def _notify_test_completion(self, execution: ABTestExecution):
        """Notifica conclusão do teste via event bus."""
        if not self.event_bus:
            return

        try:
            event_data = {
                "test_id": execution.config.test_id,
                "test_name": execution.config.name,
                "result": execution.statistics.result.value,
                "winner": execution.statistics.winner,
                "confidence": execution.statistics.confidence,
                "p_value": execution.statistics.p_value,
                "effect_size": execution.statistics.effect_size,
                "samples_a": execution.statistics.samples_a,
                "samples_b": execution.statistics.samples_b,
                "duration_minutes": (execution.end_time - execution.start_time).total_seconds() / 60
            }

            self.event_bus.publish("ab_test.completed", event_data)
            logger.info(f"📢 Notificação de conclusão enviada para teste: {execution.config.name}")

        except Exception as e:
            logger.error(f"Erro ao notificar conclusão: {e}")

    def _validate_test_config(self, config: ABTestConfig):
        """Valida configuração do teste."""
        if not config.name:
            raise ValueError("Nome do teste é obrigatório")

        if not config.config_a or not config.config_b:
            raise ValueError("Configurações A e B são obrigatórias")

        if config.duration_minutes <= 0:
            raise ValueError("Duração deve ser positiva")

        if config.switch_interval_minutes <= 0:
            raise ValueError("Intervalo de alternância deve ser positivo")

        if config.switch_interval_minutes >= config.duration_minutes:
            raise ValueError("Intervalo de alternância deve ser menor que duração total")

        if not config.primary_metric:
            raise ValueError("Métrica principal é obrigatória")

        if not (0 < config.significance_level < 1):
            raise ValueError("Nível de significância deve estar entre 0 e 1")

    def _calculate_test_progress(self, execution: ABTestExecution) -> float:
        """Calcula progresso do teste (0.0 a 1.0)."""
        if not execution.start_time or execution.status != ABTestStatus.RUNNING:
            return 1.0 if execution.status in [ABTestStatus.COMPLETED, ABTestStatus.STOPPED] else 0.0

        elapsed = (datetime.now(timezone.utc) - execution.start_time).total_seconds() / 60
        return min(1.0, elapsed / execution.config.duration_minutes)

    def _log_test_event(self, test_id: str, event_type: str, data: Dict[str, Any]):
        """Registra evento do teste."""
        if test_id in self.active_tests:
            execution = self.active_tests[test_id]
        elif test_id in self.completed_tests:
            execution = self.completed_tests[test_id]
        else:
            return

        event = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "event_type": event_type,
            "data": data
        }

        execution.events.append(event)

        # Manter apenas últimos 100 eventos
        if len(execution.events) > 100:
            execution.events = execution.events[-100:]


# Função de conveniência para criar framework global
_global_framework: Optional[ABTestingFramework] = None

def get_global_ab_framework(hot_reloader: Optional[ConfigurationHotReloader] = None,
                           event_bus: Optional[EventBus] = None) -> ABTestingFramework:
    """Retorna instância global do framework de A/B testing."""
    global _global_framework

    if _global_framework is None:
        _global_framework = ABTestingFramework(
            hot_reloader=hot_reloader,
            event_bus=event_bus
        )

    return _global_framework
