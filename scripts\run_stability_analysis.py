# scripts/run_stability_analysis.py

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Sequence, Tuple
import os

from qualia.core.stability import (
    simulate_mass_dynamics,
    # compute_lyapunov, # Não usado diretamente aqui, V_series vem de simulate_mass_dynamics
    compute_delta_V,
)

# --- Configurações Globais para Análise ---
PLOTS_DIR = os.path.join("results", "stability_analysis", "plots")
REPORT_FILE_PATH = os.path.join("docs", "analysis", "stability_analysis_report.md")
M0_DEFAULT = 100.0
M_MIN_DEFAULT = 10.0
ALPHA_PARAMS_DEFAULT = {"alpha0": 0.01, "alpha1": 0.05, "alpha2": 0.02}


# --- De<PERSON><PERSON><PERSON> de Perfis de H_k (similar ao notebook) ---
def generate_Hk_constant(value: float, length: int) -> List[float]:
    return [value] * length


def generate_Hk_ramp(start_val: float, end_val: float, length: int) -> List[float]:
    return np.linspace(start_val, end_val, length).tolist()


def generate_Hk_exponential_decay(
    initial_val: float, decay_rate: float, length: int
) -> List[float]:
    return [initial_val * np.exp(-decay_rate * i) for i in range(length)]


def generate_Hk_exponential_growth(
    initial_val: float, growth_rate: float, length: int
) -> List[float]:
    return [initial_val * np.exp(growth_rate * i) for i in range(length)]


def generate_Hk_sinusoidal(
    amplitude: float, frequency: float, offset: float, length: int
) -> List[float]:
    return [
        (amplitude * np.sin(2 * np.pi * frequency * i / length) + offset)
        for i in range(length)
    ]


def generate_Hk_single_peak(
    peak_value: float,
    peak_position: int,
    base_value: float,
    length: int,
    peak_width: int = 1,
) -> List[float]:
    Hk = [base_value] * length
    for i in range(
        max(0, peak_position - peak_width // 2),
        min(length, peak_position + (peak_width + 1) // 2),
    ):
        Hk[i] = peak_value
    return Hk


def generate_Hk_random(
    min_val: float, max_val: float, length: int, seed: int | None = None
) -> List[float]:
    """Generate a random H_k profile using an isolated RNG."""

    rng = np.random.default_rng(seed)
    return rng.uniform(min_val, max_val, length).tolist()


HK_PROFILES = {
    "Constant_Zero": generate_Hk_constant(0.0, 50),
    "Constant_Low": generate_Hk_constant(0.1, 50),
    "Constant_Medium": generate_Hk_constant(0.5, 50),
    "Constant_High": generate_Hk_constant(1.0, 50),
    "Ramp_Up": generate_Hk_ramp(0.0, 1.0, 50),
    "Ramp_Down": generate_Hk_ramp(1.0, 0.0, 50),
    "Exp_Decay": generate_Hk_exponential_decay(1.0, 0.1, 50),
    "Exp_Growth_Clipped": [
        min(val, 1.0) for val in generate_Hk_exponential_growth(0.1, 0.1, 50)
    ],
    "Sinusoidal_Positive": generate_Hk_sinusoidal(
        amplitude=0.5, frequency=2, offset=0.5, length=50
    ),
    "Single_Peak_Early": generate_Hk_single_peak(
        peak_value=1.0, peak_position=10, base_value=0.1, length=50, peak_width=3
    ),
    "Single_Peak_Late": generate_Hk_single_peak(
        peak_value=0.8, peak_position=40, base_value=0.05, length=50, peak_width=5
    ),
    "Random_0_to_1": generate_Hk_random(0.0, 1.0, 50, seed=42),
}


# --- Função de Simulação e Salvamento de Plots ---
def run_experiment_and_save_plots(
    H_series: Sequence[float],
    alpha_params: Dict[str, float],
    M0: float,
    M_min: float,
    profile_name: str,
    plots_output_dir: str,
) -> Tuple[str, str]:  # Retorna caminho do plot e uma string de sumário
    """Simula a dinâmica, plota e salva os gráficos, retorna caminho e sumário."""

    M_series, V_series_sim = simulate_mass_dynamics(H_series, alpha_params, M0, M_min)
    delta_V_series = compute_delta_V(V_series_sim)

    time_steps_M_V = np.arange(len(M_series))
    time_steps_delta_V = np.arange(len(delta_V_series))
    time_steps_H = np.arange(len(H_series))

    # Limpar nomes para uso em arquivos
    safe_profile_name = profile_name.replace(" ", "_").replace("/", "-").lower()
    alpha_str = (
        f"a0_{alpha_params.get('alpha0', 0):.2f}_"
        f"a1_{alpha_params.get('alpha1', 0):.2f}_"
        f"a2_{alpha_params.get('alpha2', 0):.2f}"
    ).replace(".", "p")
    plot_filename = f"{safe_profile_name}_{alpha_str}.png"
    plot_filepath = os.path.join(plots_output_dir, plot_filename)

    fig, axs = plt.subplots(4, 1, figsize=(12, 15), sharex=True)
    fig.suptitle(
        f"Dinâmica da Massa para {profile_name}\nAlpha Params: {alpha_params}\nM0={M0}, M_min={M_min}",
        fontsize=14,
    )

    axs[0].plot(
        time_steps_H,
        H_series,
        label="H_k (Entropia Simbólica)",
        color="cyan",
        linestyle=":",
    )
    axs[0].set_ylabel("H_k")
    axs[0].legend()
    axs[0].grid(True)

    axs[1].plot(
        time_steps_M_V, M_series, label="M_k (Massa Informacional)", color="blue"
    )
    axs[1].axhline(M_min, color="gray", linestyle="--", label=f"M_min = {M_min}")
    axs[1].set_ylabel("M_k")
    axs[1].legend()
    axs[1].grid(True)

    axs[2].plot(time_steps_M_V, V_series_sim, label="V_k = M_k - M_min", color="green")
    axs[2].axhline(0, color="gray", linestyle="--")
    axs[2].set_ylabel("V_k")
    axs[2].legend()
    axs[2].grid(True)

    if delta_V_series:
        axs[3].plot(
            time_steps_delta_V,
            delta_V_series,
            label="ΔV_k = V_{k+1} - V_k",
            color="red",
            marker=".",
        )
        axs[3].axhline(0, color="gray", linestyle="--")
        axs[3].set_ylabel("ΔV_k")
        axs[3].legend()
        axs[3].grid(True)
    else:
        axs[3].text(
            0.5, 0.5, "ΔV_k N/A", ha="center", va="center", transform=axs[3].transAxes
        )
        axs[3].grid(False)

    plt.xlabel("Passo de Tempo k")
    plt.tight_layout(rect=[0, 0, 1, 0.96])  # Ajustar para suptitle

    os.makedirs(plots_output_dir, exist_ok=True)
    plt.savefig(plot_filepath)
    plt.close(fig)  # Fechar figura para economizar memória

    summary = (
        f"Experimento: {profile_name}, Alpha: {alpha_params}, "
        f"M0: {M0}, M_min: {M_min}. Plot salvo em: {plot_filepath}"
    )
    print(summary)
    return plot_filepath, summary


# --- Função para Gerar Relatório Markdown ---
def generate_markdown_report(report_items: List[Dict[str, str]], report_filepath: str):
    """Gera um relatório Markdown com os resultados dos experimentos."""
    content = ["# Relatório da Análise de Estabilidade da Massa Informacional\n\n"]
    content.append(
        "Este relatório documenta a análise da dinâmica da massa informacional "
        "`M_k` e da função candidata de Lyapunov `V_k = M_k - M_min` sob "
        "diferentes perfis de entropia simbólica `H_k` e parâmetros `alpha`.\n\n"
    )

    for item in report_items:
        content.append(f"## Experimento: {item['title']}\n")
        content.append(f"**Perfil H_k:** `{item['profile_name']}`\n")
        content.append(f"**Parâmetros Alpha:** `{item['alpha_params']}`\n")
        content.append(f"**M0:** `{item['m0']}`, **M_min:** `{item['m_min']}`\n\n")
        # Gerar caminho relativo para o plot se possível, assumindo que docs/ e results/ estão no mesmo nível
        try:
            relative_plot_path = os.path.relpath(
                item["plot_path"], start=os.path.dirname(report_filepath)
            )
            content.append(f"![Plot para {item['title']}]({relative_plot_path})\n\n")
        except ValueError:
            # Fallback se não puder criar caminho relativo (ex: drives diferentes no Windows)
            content.append(
                f"![Plot para {item['title']}]({item['plot_path']}) (Caminho absoluto)\n\n"
            )
        content.append("**Sumário:**\n")
        content.append(f"```\n{item['summary']}\n```\n\n")
        content.append("**Análise e Observações:**\n")
        content.append("*(Adicione suas observações aqui)*\n\n")
        content.append("---\n")

    os.makedirs(os.path.dirname(report_filepath), exist_ok=True)
    with open(report_filepath, "w", encoding="utf-8") as f:
        f.writelines(content)
    print(f"Relatório Markdown gerado em: {report_filepath}")


# --- Orquestração dos Experimentos ---
def main():
    """Função principal para rodar todos os experimentos e gerar o relatório."""

    print(f"Salvando plots em: {PLOTS_DIR}")
    print(f"Salvando relatório em: {REPORT_FILE_PATH}")

    report_items = []

    # Experimento 1: Parâmetros Padrão, Perfil Constante Baixo
    print("\n--- Experimento 1: Padrão com H_k Constante Baixo ---")
    profile_name_exp1 = "Constant_Low"
    plot_path, summary = run_experiment_and_save_plots(
        HK_PROFILES[profile_name_exp1],
        ALPHA_PARAMS_DEFAULT,
        M0_DEFAULT,
        M_MIN_DEFAULT,
        profile_name_exp1,
        PLOTS_DIR,
    )
    report_items.append(
        {
            "title": "Padrão com H_k Constante Baixo",
            "profile_name": profile_name_exp1,
            "alpha_params": ALPHA_PARAMS_DEFAULT,
            "m0": M0_DEFAULT,
            "m_min": M_MIN_DEFAULT,
            "plot_path": plot_path,
            "summary": summary,
        }
    )

    # Experimento 2: Impacto Individual dos Parâmetros Alpha
    Hk_profile_const_medium = HK_PROFILES["Constant_Medium"]

    # 2a. Variando alpha0
    print("\n--- Experimento 2a: Variando alpha0 ---")
    alpha_variations_a0 = [
        {"alpha0": 0.005, "alpha1": 0.0, "alpha2": 0.0},
        {
            "alpha0": 0.01,
            "alpha1": 0.0,
            "alpha2": 0.0,
        },  # Padrão para a0 em alguns casos
        {"alpha0": 0.05, "alpha1": 0.0, "alpha2": 0.0},
        {"alpha0": 0.2, "alpha1": 0.0, "alpha2": 0.0},
    ]
    for alpha_p in alpha_variations_a0:
        exp_title = f"Variação alpha0 ({alpha_p['alpha0']}) com H_k Constante Médio"
        plot_path, summary = run_experiment_and_save_plots(
            Hk_profile_const_medium,
            alpha_p,
            M0_DEFAULT,
            M_MIN_DEFAULT,
            exp_title,
            PLOTS_DIR,
        )
        report_items.append(
            {
                "title": exp_title,
                "profile_name": "Constant_Medium",
                "alpha_params": alpha_p,
                "m0": M0_DEFAULT,
                "m_min": M_MIN_DEFAULT,
                "plot_path": plot_path,
                "summary": summary,
            }
        )

    # 2b. Variando alpha1 (com alpha0=0.01)
    print("\n--- Experimento 2b: Variando alpha1 ---")
    alpha_variations_a1 = [
        {"alpha0": 0.01, "alpha1": 0.01, "alpha2": 0.0},
        {"alpha0": 0.01, "alpha1": 0.05, "alpha2": 0.0},  # Padrão para a1
        {"alpha0": 0.01, "alpha1": 0.1, "alpha2": 0.0},
        {"alpha0": 0.01, "alpha1": 0.3, "alpha2": 0.0},
    ]
    for alpha_p in alpha_variations_a1:
        exp_title = f"Variação alpha1 ({alpha_p['alpha1']}) com H_k Constante Médio"
        plot_path, summary = run_experiment_and_save_plots(
            Hk_profile_const_medium,
            alpha_p,
            M0_DEFAULT,
            M_MIN_DEFAULT,
            exp_title,
            PLOTS_DIR,
        )
        report_items.append(
            {
                "title": exp_title,
                "profile_name": "Constant_Medium",
                "alpha_params": alpha_p,
                "m0": M0_DEFAULT,
                "m_min": M_MIN_DEFAULT,
                "plot_path": plot_path,
                "summary": summary,
            }
        )

    # 2c. Variando alpha2 (com alpha0=0.01)
    print("\n--- Experimento 2c: Variando alpha2 ---")
    alpha_variations_a2 = [
        {"alpha0": 0.01, "alpha1": 0.0, "alpha2": 0.005},
        {"alpha0": 0.01, "alpha1": 0.0, "alpha2": 0.02},  # Padrão para a2
        {"alpha0": 0.01, "alpha1": 0.0, "alpha2": 0.05},
        {"alpha0": 0.01, "alpha1": 0.0, "alpha2": 0.15},
    ]
    for alpha_p in alpha_variations_a2:
        exp_title = f"Variação alpha2 ({alpha_p['alpha2']}) com H_k Constante Médio"
        plot_path, summary = run_experiment_and_save_plots(
            Hk_profile_const_medium,
            alpha_p,
            M0_DEFAULT,
            M_MIN_DEFAULT,
            exp_title,
            PLOTS_DIR,
        )
        report_items.append(
            {
                "title": exp_title,
                "profile_name": "Constant_Medium",
                "alpha_params": alpha_p,
                "m0": M0_DEFAULT,
                "m_min": M_MIN_DEFAULT,
                "plot_path": plot_path,
                "summary": summary,
            }
        )

    # Experimento 3: Robustez a Diferentes Perfis de H_k (com alpha_params_default)
    print("\n--- Experimento 3: Robustez a Perfis de H_k ---")
    for name, Hk_profile in HK_PROFILES.items():
        exp_title = f"Perfil H_k: {name} (Alphas Padrão)"
        print(f"  Analisando perfil: {name}")
        plot_path, summary = run_experiment_and_save_plots(
            Hk_profile, ALPHA_PARAMS_DEFAULT, M0_DEFAULT, M_MIN_DEFAULT, name, PLOTS_DIR
        )
        report_items.append(
            {
                "title": exp_title,
                "profile_name": name,
                "alpha_params": ALPHA_PARAMS_DEFAULT,
                "m0": M0_DEFAULT,
                "m_min": M_MIN_DEFAULT,
                "plot_path": plot_path,
                "summary": summary,
            }
        )

    # Gerar o relatório Markdown
    generate_markdown_report(report_items, REPORT_FILE_PATH)


if __name__ == "__main__":
    # Configurar matplotlib para não usar GUI (útil para scripts)
    # plt.switch_backend('Agg') # Comentado para permitir visualização se rodado em ambiente com GUI
    main()
