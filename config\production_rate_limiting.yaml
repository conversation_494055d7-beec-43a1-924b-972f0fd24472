# Configurações de Rate Limiting Otimizadas para Produção
# QUALIA Trading System - Configuração KuCoin

# === CONFIGURAÇÕES PRINCIPAIS ===
rate_limiting:
  # Rate limit base entre requisições (segundos)
  base_rate_limit: 2.0  # Aumentado de 1.0 para 2.0s
  
  # Multiplicador para requisições subsequentes
  backoff_multiplier: 1.5  # Mais conservador que 2.0
  
  # Máximo backoff permitido
  max_backoff: 15.0  # Reduzido de 30.0s
  
  # Número máximo de requisições simultâneas
  max_concurrent_requests: 3  # Reduzido de 5 para 3

# === CONFIGURAÇÕES TICKER ===
ticker:
  # Timeout para requisições de ticker
  timeout: 20.0  # Aumentado de 15.0s
  
  # Número de tentativas
  retries: 2  # Reduzido de 3 para 2
  
  # Tempo base para backoff exponencial
  backoff_base: 2.0  # Aumentado de 1.0s
  
  # TTL do cache
  cache_ttl: 10.0  # Aumentado de 5.0s
  
  # Intervalo mínimo entre requisições de ticker
  min_interval: 3.0  # Novo: intervalo mínimo

# === CONFIGURAÇÕES OHLCV ===
ohlcv:
  # Timeout para requisições OHLCV
  timeout: 60.0  # Aumentado de 45.0s
  
  # Número de tentativas
  retries: 2  # Reduzido de 3 para 2
  
  # Tempo base para backoff
  backoff_base: 5.0  # Aumentado de 1.0s
  
  # TTL do cache
  cache_ttl: 120.0  # Aumentado de 60.0s
  
  # Máximo de candles por requisição
  max_candles: 500  # Reduzido de 1500

# === CIRCUIT BREAKER ===
circuit_breaker:
  # NETWORK FIX: Número de falhas antes de abrir - aumentado para maior tolerância
  fail_threshold: 5  # NETWORK FIX: Aumentado de 3 para 5 para resolver abertura prematura

  # NETWORK FIX: Tempo de recuperação (segundos) - aumentado para rede instável
  recovery_timeout: 60.0  # NETWORK FIX: Aumentado de 30.0s para 60.0s

  # Tempo de half-open (teste)
  half_open_timeout: 15.0  # NETWORK FIX: Aumentado de 10.0s para 15.0s

# === CONFIGURAÇÕES ESPECÍFICAS KUCOIN ===
kucoin:
  # NETWORK FIX: Rate limit mais conservador para KuCoin
  api_rate_limit: 0.3  # NETWORK FIX: Reduzido de 0.4 para 0.3 (1 req a cada 3.3s)

  # NETWORK FIX: Timeout de conexão aumentado
  connection_timeout: 60.0  # NETWORK FIX: Aumentado de 30.0s para 60.0s

  # NETWORK FIX: Timeout de inicialização aumentado
  initialization_timeout: 120.0  # NETWORK FIX: Aumentado de 90.0s para 120.0s

  # NETWORK FIX: Timeout específico para ticker
  ticker_timeout: 90.0  # NETWORK FIX: Novo parâmetro para resolver TimeoutError

  # NETWORK FIX: Timeout específico para OHLCV
  ohlcv_timeout: 90.0  # NETWORK FIX: Novo parâmetro para operações OHLCV

  # Configurações WebSocket
  websocket:
    enabled: true
    heartbeat_interval: 45.0  # NETWORK FIX: Aumentado de 30.0s para 45.0s
    reconnect_attempts: 5  # NETWORK FIX: Aumentado de 3 para 5
    reconnect_delay: 10.0  # NETWORK FIX: Aumentado de 5.0s para 10.0s
    subscription_timeout: 30.0  # NETWORK FIX: Novo timeout para subscrições

# === CONFIGURAÇÕES DE PRODUÇÃO ===
production:
  # Modo de operação
  mode: "conservative"  # conservative | balanced | aggressive
  
  # Logging detalhado
  verbose_logging: false
  
  # Métricas habilitadas
  metrics_enabled: true
  
  # Cache habilitado
  cache_enabled: true
  
  # Retry automático
  auto_retry: true
  
  # Graceful degradation
  graceful_degradation: true

# === LIMITES DE EMERGÊNCIA ===
emergency:
  # Máximo de falhas consecutivas antes de parar
  max_consecutive_failures: 10
  
  # Tempo de cooldown após emergência
  emergency_cooldown: 300.0  # 5 minutos
  
  # Fallback para modo offline
  offline_fallback: true 