#!/usr/bin/env python
"""Ingest hardware statevectors and scores into Quantum Pattern Memory.

This script scans a directory for JSON files containing statevectors,
measurement counts and optional scores produced by hardware runs. For each
file, diversity and ``sv_entropy`` are computed and the pattern is stored in
QPM when ``diversity > 0.4`` and ``sv_entropy`` exceeds the chosen threshold.
"""

from __future__ import annotations

import argparse
import json
from pathlib import Path
from typing import Any, Dict

import numpy as np

from qualia.memory.quantum_pattern_memory import QuantumPatternMemory
from qualia.common_types import QuantumSignaturePacket
from qualia.utils.quantum_utils import sv_entropy
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

DEFAULT_PERSISTENCE_PATH = Path("data") / "cache" / "qpm_memory.json"
DEFAULT_SOURCE_DIR = Path("data") / "hardware_patterns"


def _load_json(path: str) -> Dict[str, Any]:
    with open(path, "r", encoding="utf-8") as fh:
        return json.load(fh)


def _compute_diversity(counts: Dict[str, int], n_qubits: int) -> float:
    if not counts:
        return 0.0
    unique = len(counts)
    return unique / float(2**n_qubits)


def _process_file(
    path: str, qpm: QuantumPatternMemory, entropy_threshold: float
) -> bool:
    data = _load_json(path)
    statevector = np.asarray(data.get("statevector", []), dtype=complex)
    if statevector.size == 0:
        logger.warning("Arquivo %s sem statevector", path)
        return False
    num_qubits = int(np.log2(statevector.size))
    counts = data.get("counts", {})
    diversity = _compute_diversity(counts, num_qubits)
    entropy = float(data.get("sv_entropy", sv_entropy(statevector)))
    score = float(data.get("score", 0.0))

    if diversity > 0.4 and entropy > entropy_threshold:
        packet = QuantumSignaturePacket(
            vector=statevector.tolist(), metrics={"quantum_score": score}
        )
        qpm.store_pattern(
            quantum_signature_packet=packet,
            market_snapshot={},
            outcome={"source": "hardware"},
            decision_context={"diversity": diversity, "sv_entropy": entropy},
        )
        logger.info(
            "Padr\u00e3o armazenado: %s (diversity=%.3f, sv_entropy=%.3f)",
            Path(path).name,
            diversity,
            entropy,
        )
        return True

    logger.info(
        "Padr\u00e3o ignorado: %s (diversity=%.3f, sv_entropy=%.3f)",
        Path(path).name,
        diversity,
        entropy,
    )
    return False


def main() -> None:
    parser = argparse.ArgumentParser(description="Ingest hardware patterns into QPM")
    parser.add_argument(
        "--path", default=DEFAULT_PERSISTENCE_PATH, help="Path to QPM persistence JSON"
    )
    parser.add_argument(
        "--source-dir",
        default=DEFAULT_SOURCE_DIR,
        help="Directory with hardware JSON files",
    )
    parser.add_argument(
        "--entropy-threshold",
        type=float,
        default=1.0,
        help="Minimum sv_entropy to store pattern",
    )
    args = parser.parse_args()

    qpm = QuantumPatternMemory(persistence_path=args.path, enable_warmstart=False)
    stored = 0
    src_dir = Path(args.source_dir)
    if not src_dir.is_dir():
        logger.error("Diret\u00f3rio de origem n\u00e3o encontrado: %s", args.source_dir)
        return
    for entry in src_dir.iterdir():
        if not entry.name.endswith(".json"):
            continue
        fpath = entry
        try:
            if _process_file(str(fpath), qpm, args.entropy_threshold):
                stored += 1
        except Exception as exc:  # pragma: no cover - defensivo
            logger.error("Erro ao processar %s: %s", entry.name, exc, exc_info=True)

    if stored:
        qpm.save_to_file()
        logger.info(
            "%d padr\u00f5es armazenados. Mem\u00f3ria salva em %s", stored, args.path
        )
    else:
        logger.info("Nenhum padr\u00e3o armazenado.")


if __name__ == "__main__":
    main()
