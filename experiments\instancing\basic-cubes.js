// Renderiza cubos individuais para comparação

document.addEventListener('DOMContentLoaded', () => {
  const container = document.getElementById('instancing-view');
  if (!container) return;

  const width = container.clientWidth;
  const height = container.clientHeight;

  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(70, width / height, 0.1, 100);
  camera.position.z = 5;

  const renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(width, height);
  container.appendChild(renderer.domElement);

  const geometry = new THREE.BoxGeometry(0.2, 0.2, 0.2);
  const material = new THREE.MeshNormalMaterial();
  const cubes = [];
  for (let i = 0; i < 50; i++) {
    const cube = new THREE.Mesh(geometry, material);
    cube.position.set(
      (Math.random() - 0.5) * 4,
      (Math.random() - 0.5) * 4,
      (Math.random() - 0.5) * 4
    );
    cubes.push(cube);
    scene.add(cube);
  }

  let frames = 0;
  const start = performance.now();
  function animate() {
    frames += 1;
    cubes.forEach((c) => {
      c.rotation.x += 0.01;
      c.rotation.y += 0.01;
    });
    renderer.render(scene, camera);
    if (performance.now() - start < 2000) {
      requestAnimationFrame(animate);
    } else {
      const elapsed = (performance.now() - start) / 1000;
      const fps = frames / elapsed;
      window.performanceData = { fps };
    }
  }
  animate();
});
