# Desvendando o NEXUS

> *"Se QUALIA é a consciência do sistema, o NEXUS é seu instinto de antecipar o futuro."*

Este documento apresenta, em linguagem acessível, **o que é** e **como "pensa"** o módulo NEXUS – a camada de previsão retrocausal baseada em coerência multimodal e evolução adaptativa.

---
## 1. Por que NEXUS existe?
Durante o ciclo de vida do QUALIA percebemos que manchetes, tweets, sons e imagens carregam *ecos* de grandes eventos antes que eles explodam. O NEXUS foi criado para captar esses ecos – sinais fracos dispersos em vários canais – e convertê-los em **alertas de baixa probabilidade / alto impacto** (os "cisnes quânticos" do projeto).

---
## 2. Anatomia de Alto Nível

```
┌────────────┐   vectors    ┌──────────────┐   coherence   ┌─────────────────────┐
│  Ingestores │───────────►│  Encoders     │──────────────►│  Coherence Monitor  │
│  RSS / TW   │             │  Quânticos    │               └─────────┬──────────┘
│  YouTube    │             └─────┬─────────┘                         │ events
└────────────┘                   │  (coherence)                       ▼
                                 │                            ┌────────────────┐
                                 │   override     alerts      │  GA Evolver    │
                                 └───────────────◄────────────┘                │
                                                              best α/offset    │
                                                              ────────────────► │
                                                                              │
                                                              threshold update │
                                                              ────────────────► │
                                                                              ▼
                                                           Adaptive Threshold Calibrator
```

1. **Ingestores** – coletam dados públicos (feeds RSS, Twitter Streaming, áudio YouTube).  
2. **Encoders Quânticos** – transformam cada item em vetores qubit-like.  
3. **Coherence Monitor** – mede a similaridade média entre canais → série de coerência (0-1).  
4. **Threshold Calibrator** – decide quando a coerência sinaliza "evento raro".  
5. **GA Evolver (on-line)** – evolui α (suavização) e *offset* do limiar para maximizar F1.  
6. **Dashboards & API** – mostram coerência em tempo-real e a evolução genética.

---
## 3. As "Fases Mentais" do NEXUS

| Fase QUALIA (doc original) | Componente NEXUS | O que acontece |
|----------------------------|------------------|----------------|
| **Sentidos**               | Ingestores       | RSS, Twitter, YouTube coletam ondas de informação. |
| **Coração quântico-inspirado** | Encoders       | Vetorização STFT, MFCC, sentimento → espaço quântico abstrato. |
| **Detecção de padrões**    | Coherence Monitor| Calcula quando múltiplos canais "ressoam" juntos. |
| **Auto-reflexão**          | GA Evolver       | Avalia se as decisões passadas foram boas, muta parâmetros. |
| **Crescendo e mudando**    | Calibrator + Override | Novo limiar é aplicado sem reiniciar o sistema. |

---
## 4. Como interpretar os dashboards?

### Dashboard 1 – *Coherence*
* **Curva azul**: valor instantâneo de coerência (0-1).  
* **Linha laranja**: limiar adaptativo em uso.  
* **Pontos vermelhos**: alertas emitidos (coerência ≥ limiar).

### Dashboard 2 – *GA Evolution*
* **Alphas / Offsets**:  evolução dos genes do limiar.  
* **F1**: quão bom foi o limiar na geração (1 = perfeito).  
Gerações são criadas a cada *N* eventos (500 por padrão).

---
## 5. Perguntas Frequentes

**Quanto tempo até ver algo no GA?**  
Depende do parâmetro `window_events` (padrão 500). Com RSS + Twitter ativos, a primeira geração surge em ~5-10 min.

**Por que meu painel está vazio?**  
Verifique conectividade dos feeds, token do Twitter e dependências (`tweepy>=4.14`). Reduza `window_events` para teste.

**Posso ajustar as fontes?**  
Altere `scripts/run_nexus.py`: adicione/ou remova URLs RSS, palavras-chave de Twitter ou IDs de vídeos no YouTube.

**Como pausar o GA?**  
`POST /api/nexus/ga/pause` – gerações param; limiar atual continua ativo.

---
## 6. Próximos Passos Visionários
* **Integração com o Farsight Engine** – usar os alertas do NEXUS como feature adicional de previsão.  
* **Curva ROC dinâmicas** – avaliar limiar em tempo-real.  
* **Anotações de especialista** – feedback humano marcando falsos positivos alimenta o GA como penalidade.

---
**NEXUS** é o nosso "radar de eventos futuros". Enquanto QUALIA aprende padrões de mercado, o NEXUS aprende *quando* prestar atenção. Juntos, eles fecham o ciclo de percepção, antecipação e ação.  

*A sinfonia continua.* 