data_collection:
  fear_greed_interval: 300
  market_data_interval: 30
  news_feed_interval: 60
  rss_feeds:
  - https://cointelegraph.com/rss
  - https://decrypt.co/feed
  - https://cryptonews.com/news/feed/
  - https://www.coindesk.com/arc/outboundfeeds/rss/
  symbols:
  - BTCUSDT
  - ETHUSDT
  - ADAUSDT
  - SOLUSDT
  - MATICUSDT
  - DOTUSDT
  - LINKUSDT
  - UNIUSDT
holographic:
  diffusion_rate: 0.35
  evolution:
    entropy_threshold: 0.8
    max_field_energy: 100.0
    min_field_energy: 0.01
    step_interval: 15.0
  feedback_strength: 0.08
  field_size:
  - 200
  - 200
  pattern_detection:
    coherence_threshold: 0.7
    min_strength: 0.3
    wavelet_scales:
    - 2
    - 4
    - 8
    - 16
  signal_generation:
    max_signals_per_cycle: 10
    min_pattern_confidence: 0.6
    min_pattern_strength: 0.4
    signal_confidence_boost: 0.1
  time_steps: 100
holographic_adapter:
  enable_holographic_risk_override: true
  max_concurrent_positions: 3
  risk_overrides:
    max_rsi_buy: 75
    max_volatility: 8.0
    min_pattern_strength: 0.4
    min_rsi_sell: 25
    min_volume_ratio: 0.5
hyperparams:
  note: Parâmetros de amplificação e confiança movidos para hyperparams.yaml
  source: ../qualia/config/hyperparams.yaml
monitoring:
  alerts:
    high_false_positive_rate: 0.3
    high_field_energy: 80.0
    low_pattern_detection: 0.05
  field_summary_interval: 60
  log_calibration_updates: true
  log_field_summary: true
  log_pattern_detection: true
  log_signal_generation: true
  performance_report_interval: 300
performance:
  batch_size: 50
  cache_market_data: true
  cache_ttl: 300
  max_batch_wait: 5.0
  max_workers: 4
  parallel_pattern_detection: true
quantum_encoding:
  market_indicators:
    rsi_encoding: true
    volatility_encoding: false
    volume_encoding: true
  rss_sentiment:
    confidence_boost: 0.15
    enabled: true
    quantum_weight: 1.2
