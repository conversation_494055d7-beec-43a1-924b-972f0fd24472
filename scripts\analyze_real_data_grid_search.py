#!/usr/bin/env python3
"""
Análise Avançada dos Resultados do Grid Search com Dados Reais
YAA IMPLEMENTATION: Análise comparativa entre dados simulados vs reais.
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

from src.qualia.utils.logger import get_logger

logger = get_logger(__name__)


class RealDataGridSearchAnalyzer:
    """Analisador de resultados do grid search com dados reais."""
    
    def __init__(self, results_file: str):
        """Inicializa analisador com arquivo de resultados."""
        self.results_file = Path(results_file)
        self.results_data = None
        self.df = None
        
    def load_results(self) -> bool:
        """Carrega resultados do arquivo JSON."""
        try:
            with open(self.results_file, 'r') as f:
                self.results_data = json.load(f)
            
            # Converte para DataFrame
            results_list = self.results_data.get('results', [])
            if not results_list:
                logger.error("Nenhum resultado encontrado no arquivo")
                return False
            
            self.df = pd.DataFrame(results_list)
            logger.info(f"✅ Carregados {len(self.df)} resultados do grid search")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao carregar resultados: {e}")
            return False
    
    def analyze_performance_distribution(self):
        """Analisa distribuição de performance."""
        print("\n📊 ANÁLISE DE DISTRIBUIÇÃO DE PERFORMANCE")
        print("=" * 50)
        
        # Estatísticas básicas
        print(f"\n📈 Sharpe Ratio:")
        print(f"   • Média: {self.df['sharpe_ratio'].mean():.3f}")
        print(f"   • Mediana: {self.df['sharpe_ratio'].median():.3f}")
        print(f"   • Desvio Padrão: {self.df['sharpe_ratio'].std():.3f}")
        print(f"   • Min: {self.df['sharpe_ratio'].min():.3f}")
        print(f"   • Max: {self.df['sharpe_ratio'].max():.3f}")
        
        print(f"\n💰 Total Return (%):")
        print(f"   • Média: {self.df['total_return_pct'].mean():.2%}")
        print(f"   • Mediana: {self.df['total_return_pct'].median():.2%}")
        print(f"   • Desvio Padrão: {self.df['total_return_pct'].std():.2%}")
        print(f"   • Min: {self.df['total_return_pct'].min():.2%}")
        print(f"   • Max: {self.df['total_return_pct'].max():.2%}")
        
        print(f"\n🛡️ Max Drawdown (%):")
        print(f"   • Média: {self.df['max_drawdown_pct'].mean():.2%}")
        print(f"   • Mediana: {self.df['max_drawdown_pct'].median():.2%}")
        print(f"   • Min: {self.df['max_drawdown_pct'].min():.2%}")
        print(f"   • Max: {self.df['max_drawdown_pct'].max():.2%}")
        
        print(f"\n🎯 Win Rate (%):")
        print(f"   • Média: {self.df['win_rate'].mean():.2%}")
        print(f"   • Mediana: {self.df['win_rate'].median():.2%}")
        print(f"   • Min: {self.df['win_rate'].min():.2%}")
        print(f"   • Max: {self.df['win_rate'].max():.2%}")
    
    def analyze_parameter_correlations(self):
        """Analisa correlações entre parâmetros e performance."""
        print("\n🔗 ANÁLISE DE CORRELAÇÕES")
        print("=" * 50)
        
        # Correlações com Sharpe Ratio
        sharpe_corr = self.df[['price_amplification', 'news_amplification', 'min_confidence']].corrwith(
            self.df['sharpe_ratio']
        )
        
        print(f"\n📈 Correlações com Sharpe Ratio:")
        for param, corr in sharpe_corr.items():
            print(f"   • {param}: {corr:.3f}")
        
        # Correlações com Total Return
        return_corr = self.df[['price_amplification', 'news_amplification', 'min_confidence']].corrwith(
            self.df['total_return_pct']
        )
        
        print(f"\n💰 Correlações com Total Return:")
        for param, corr in return_corr.items():
            print(f"   • {param}: {corr:.3f}")
        
        # Correlações com Win Rate
        winrate_corr = self.df[['price_amplification', 'news_amplification', 'min_confidence']].corrwith(
            self.df['win_rate']
        )
        
        print(f"\n🎯 Correlações com Win Rate:")
        for param, corr in winrate_corr.items():
            print(f"   • {param}: {corr:.3f}")
    
    def find_optimal_configurations(self):
        """Identifica configurações ótimas."""
        print("\n🏆 CONFIGURAÇÕES ÓTIMAS")
        print("=" * 50)
        
        # Top 5 por Sharpe Ratio
        top_sharpe = self.df.nlargest(5, 'sharpe_ratio')
        print(f"\n📈 Top 5 Sharpe Ratio:")
        for i, (_, row) in enumerate(top_sharpe.iterrows(), 1):
            print(f"   {i}. Sharpe: {row['sharpe_ratio']:.3f} | "
                  f"price_amp: {row['price_amplification']:.1f} | "
                  f"news_amp: {row['news_amplification']:.1f} | "
                  f"min_conf: {row['min_confidence']:.2f} | "
                  f"Return: {row['total_return_pct']:.2%}")
        
        # Top 5 por Return
        top_return = self.df.nlargest(5, 'total_return_pct')
        print(f"\n💰 Top 5 Total Return:")
        for i, (_, row) in enumerate(top_return.iterrows(), 1):
            print(f"   {i}. Return: {row['total_return_pct']:.2%} | "
                  f"price_amp: {row['price_amplification']:.1f} | "
                  f"news_amp: {row['news_amplification']:.1f} | "
                  f"min_conf: {row['min_confidence']:.2f} | "
                  f"Sharpe: {row['sharpe_ratio']:.3f}")
        
        # Top 5 por menor Drawdown
        top_drawdown = self.df.nsmallest(5, 'max_drawdown_pct')
        print(f"\n🛡️ Top 5 Menor Drawdown:")
        for i, (_, row) in enumerate(top_drawdown.iterrows(), 1):
            print(f"   {i}. Drawdown: {row['max_drawdown_pct']:.2%} | "
                  f"price_amp: {row['price_amplification']:.1f} | "
                  f"news_amp: {row['news_amplification']:.1f} | "
                  f"min_conf: {row['min_confidence']:.2f} | "
                  f"Return: {row['total_return_pct']:.2%}")
    
    def analyze_risk_profiles(self):
        """Analisa performance por perfis de risco."""
        print("\n⚖️ ANÁLISE POR PERFIS DE RISCO")
        print("=" * 50)
        
        # Define perfis baseados nos parâmetros
        def classify_risk_profile(row):
            price_amp = row['price_amplification']
            news_amp = row['news_amplification']
            min_conf = row['min_confidence']
            
            if price_amp <= 3.0 and news_amp <= 2.5 and min_conf >= 0.65:
                return 'Conservative'
            elif price_amp >= 7.0 and news_amp >= 6.0 and min_conf <= 0.5:
                return 'Aggressive'
            else:
                return 'Moderate'
        
        self.df['risk_profile'] = self.df.apply(classify_risk_profile, axis=1)
        
        # Análise por perfil
        for profile in ['Conservative', 'Moderate', 'Aggressive']:
            profile_data = self.df[self.df['risk_profile'] == profile]
            
            if len(profile_data) == 0:
                continue
                
            print(f"\n📊 Perfil {profile} ({len(profile_data)} configurações):")
            print(f"   • Sharpe Médio: {profile_data['sharpe_ratio'].mean():.3f}")
            print(f"   • Return Médio: {profile_data['total_return_pct'].mean():.2%}")
            print(f"   • Drawdown Médio: {profile_data['max_drawdown_pct'].mean():.2%}")
            print(f"   • Win Rate Médio: {profile_data['win_rate'].mean():.2%}")
            
            # Melhor configuração do perfil
            best = profile_data.loc[profile_data['sharpe_ratio'].idxmax()]
            print(f"   • Melhor Config: price_amp={best['price_amplification']:.1f}, "
                  f"news_amp={best['news_amplification']:.1f}, "
                  f"min_conf={best['min_confidence']:.2f}")
    
    def generate_recommendations(self):
        """Gera recomendações baseadas na análise."""
        print("\n💡 RECOMENDAÇÕES BASEADAS EM DADOS REAIS")
        print("=" * 50)
        
        # Melhor configuração geral
        best_overall = self.df.loc[self.df['sharpe_ratio'].idxmax()]
        print(f"\n🏆 Configuração Recomendada (Melhor Sharpe):")
        print(f"   • price_amplification: {best_overall['price_amplification']:.1f}")
        print(f"   • news_amplification: {best_overall['news_amplification']:.1f}")
        print(f"   • min_confidence: {best_overall['min_confidence']:.2f}")
        print(f"   • Performance: Sharpe {best_overall['sharpe_ratio']:.3f}, "
              f"Return {best_overall['total_return_pct']:.2%}, "
              f"Drawdown {best_overall['max_drawdown_pct']:.2%}")
        
        # Configuração balanceada
        # Filtra configurações com Sharpe > mediana e Drawdown < mediana
        balanced = self.df[
            (self.df['sharpe_ratio'] > self.df['sharpe_ratio'].median()) &
            (self.df['max_drawdown_pct'] < self.df['max_drawdown_pct'].median())
        ]
        
        if len(balanced) > 0:
            best_balanced = balanced.loc[balanced['sharpe_ratio'].idxmax()]
            print(f"\n⚖️ Configuração Balanceada (Risco/Retorno):")
            print(f"   • price_amplification: {best_balanced['price_amplification']:.1f}")
            print(f"   • news_amplification: {best_balanced['news_amplification']:.1f}")
            print(f"   • min_confidence: {best_balanced['min_confidence']:.2f}")
            print(f"   • Performance: Sharpe {best_balanced['sharpe_ratio']:.3f}, "
                  f"Return {best_balanced['total_return_pct']:.2%}, "
                  f"Drawdown {best_balanced['max_drawdown_pct']:.2%}")
        
        # Insights sobre parâmetros
        print(f"\n🔍 Insights dos Dados Reais:")
        
        # Min confidence analysis
        high_conf = self.df[self.df['min_confidence'] >= 0.7]
        low_conf = self.df[self.df['min_confidence'] <= 0.4]
        
        if len(high_conf) > 0 and len(low_conf) > 0:
            print(f"   • Min Confidence Alto (≥0.7): Sharpe médio {high_conf['sharpe_ratio'].mean():.3f}")
            print(f"   • Min Confidence Baixo (≤0.4): Sharpe médio {low_conf['sharpe_ratio'].mean():.3f}")
        
        # Amplification analysis
        high_price_amp = self.df[self.df['price_amplification'] >= 7.0]
        low_price_amp = self.df[self.df['price_amplification'] <= 3.0]
        
        if len(high_price_amp) > 0 and len(low_price_amp) > 0:
            print(f"   • Price Amp Alto (≥7.0): Return médio {high_price_amp['total_return_pct'].mean():.2%}")
            print(f"   • Price Amp Baixo (≤3.0): Return médio {low_price_amp['total_return_pct'].mean():.2%}")
    
    def run_complete_analysis(self):
        """Executa análise completa."""
        if not self.load_results():
            return False
        
        print(f"🔍 ANÁLISE COMPLETA - GRID SEARCH COM DADOS REAIS")
        print(f"📁 Arquivo: {self.results_file}")
        print(f"📊 Total de Resultados: {len(self.df)}")
        
        self.analyze_performance_distribution()
        self.analyze_parameter_correlations()
        self.find_optimal_configurations()
        self.analyze_risk_profiles()
        self.generate_recommendations()
        
        print(f"\n✅ Análise completa concluída!")
        return True


def main():
    """Função principal."""
    if len(sys.argv) < 2:
        print("❌ Uso: python analyze_real_data_grid_search.py <arquivo_resultados.json>")
        print("📁 Exemplo: python analyze_real_data_grid_search.py results/grid_search_real_data/grid_search_real_1234567890.json")
        return
    
    results_file = sys.argv[1]
    
    if not Path(results_file).exists():
        print(f"❌ Arquivo não encontrado: {results_file}")
        return
    
    analyzer = RealDataGridSearchAnalyzer(results_file)
    analyzer.run_complete_analysis()


if __name__ == "__main__":
    main()
