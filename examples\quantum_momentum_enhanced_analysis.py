#!/usr/bin/env python3
"""
QUANTUM MOMENTUM ENHANCED - Análise e Melhorias Avançadas
Objetivo: Transformar em estratégia principal do QUALIA
Target: Win Rate >60%, Sharpe >0.5, Return >3%, Max DD <2%
"""

import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Tuple

import pandas as pd
import numpy as np
import requests


class QuantumMomentumEnhancer:
    """Analisador e otimizador avançado da Quantum Momentum."""
    
    def __init__(self):
        self.session = requests.Session()
    
    def fetch_data(self, symbol: str, days: int = 90) -> pd.DataFrame:
        """Busca dados históricos com indicadores avançados."""
        try:
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': symbol.replace('/', ''),
                'interval': '1h',
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index().dropna()
            
            # Indicadores básicos
            df['returns'] = df['close'].pct_change()
            df['sma_20'] = df['close'].rolling(20).mean()
            df['sma_50'] = df['close'].rolling(50).mean()
            df['ema_12'] = df['close'].ewm(span=12).mean()
            df['ema_26'] = df['close'].ewm(span=26).mean()
            df['rsi'] = self._calculate_rsi(df['close'], 14)
            df['volatility'] = df['returns'].rolling(20).std()
            df['atr'] = self._calculate_atr(df, 14)
            
            # Indicadores avançados para análise
            df['macd'] = df['ema_12'] - df['ema_26']
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']
            df['bb_upper'], df['bb_lower'] = self._calculate_bollinger_bands(df['close'], 20, 2)
            df['volume_sma'] = df['volume'].rolling(20).mean()
            df['price_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            # Market regime indicators
            df['trend_strength'] = abs(df['sma_20'] - df['sma_50']) / df['sma_50']
            df['volatility_regime'] = df['volatility'] / df['volatility'].rolling(50).mean()
            
            return df
            
        except Exception as e:
            print(f"❌ Erro ao buscar dados para {symbol}: {e}")
            return pd.DataFrame()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calcula RSI."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calcula Average True Range."""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        return true_range.rolling(period).mean()
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series]:
        """Calcula Bollinger Bands."""
        sma = prices.rolling(period).mean()
        std = prices.rolling(period).std()
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, lower
    
    def analyze_current_quantum_momentum(self, df: pd.DataFrame, symbol: str) -> Dict[str, Any]:
        """Análise detalhada da versão atual."""
        
        print(f"🔍 Analisando Quantum Momentum ATUAL para {symbol}...")
        
        signals = []
        trade_details = []
        
        for i in range(50, len(df)):
            # Implementação atual (otimizada)
            
            # Filtros de qualidade
            vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-20:i].quantile(0.7)
            trend_filter = abs(df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i] > 0.02
            rsi_filter = 35 < df['rsi'].iloc[i] < 65
            
            if not (vol_filter and trend_filter and rsi_filter):
                signals.append(0)
                continue
            
            # Sinais
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
            long_momentum = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            
            signal = (
                price_momentum * 0.4 +
                vol_momentum * 0.2 +
                rsi_momentum * 0.2 +
                long_momentum * 0.2
            )
            
            if abs(signal) > 0.03:
                final_signal = np.clip(signal * 6, -1, 1)
                signals.append(final_signal)
                
                # Registra detalhes para análise
                trade_details.append({
                    'timestamp': df.index[i],
                    'price': df['close'].iloc[i],
                    'signal': final_signal,
                    'price_momentum': price_momentum,
                    'vol_momentum': vol_momentum,
                    'rsi_momentum': rsi_momentum,
                    'long_momentum': long_momentum,
                    'rsi': df['rsi'].iloc[i],
                    'volatility': df['volatility'].iloc[i],
                    'trend_strength': df['trend_strength'].iloc[i],
                    'volatility_regime': df['volatility_regime'].iloc[i],
                    'price_position': df['price_position'].iloc[i],
                    'macd': df['macd'].iloc[i],
                    'macd_histogram': df['macd_histogram'].iloc[i]
                })
            else:
                signals.append(0)
        
        # Calcula performance com gestão de risco
        return self._analyze_performance_detailed(df.iloc[50:], signals, trade_details, "CURRENT_QM")
    
    def _analyze_performance_detailed(self, df: pd.DataFrame, signals: List[float], trade_details: List[Dict], strategy_name: str) -> Dict[str, Any]:
        """Análise de performance detalhada com insights."""
        
        returns = []
        trades = 0
        winning_trades = []
        losing_trades = []
        
        for i in range(1, len(signals)):
            if abs(signals[i-1]) > 0.1:
                price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
                
                # Gestão de risco atual
                raw_return = signals[i-1] * price_return
                
                # Stop-loss e take-profit atuais
                if raw_return < -0.005:
                    final_return = -0.005
                elif raw_return > 0.008:
                    final_return = 0.008
                else:
                    final_return = raw_return
                
                returns.append(final_return)
                trades += 1
                
                # Detalhes do trade para análise
                trade_info = {
                    'return': final_return,
                    'raw_return': raw_return,
                    'position': signals[i-1],
                    'price_change': price_return,
                    'timestamp': df.index[i],
                    'was_stopped': raw_return != final_return
                }
                
                # Adiciona contexto de mercado se disponível
                if i-1 < len(trade_details):
                    trade_info.update(trade_details[i-1])
                
                if final_return > 0:
                    winning_trades.append(trade_info)
                else:
                    losing_trades.append(trade_info)
        
        if not returns:
            return {'error': 'Nenhum trade executado'}
        
        returns_series = pd.Series(returns)
        
        # Métricas básicas
        total_return = returns_series.sum()
        volatility = returns_series.std() * np.sqrt(252)
        sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
        
        # Drawdown
        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        # Métricas de trading
        win_rate = len(winning_trades) / trades if trades > 0 else 0
        avg_win = np.mean([t['return'] for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t['return'] for t in losing_trades]) if losing_trades else 0
        
        # Análise de padrões
        stopped_trades = [t for t in winning_trades + losing_trades if t.get('was_stopped', False)]
        stop_rate = len(stopped_trades) / trades if trades > 0 else 0
        
        # Análise por condições de mercado
        market_analysis = self._analyze_market_conditions(winning_trades, losing_trades)
        
        return {
            'strategy': strategy_name,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown * 100,
            'win_rate': win_rate * 100,
            'total_trades': trades,
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'volatility': volatility * 100,
            'avg_win_pct': avg_win * 100,
            'avg_loss_pct': avg_loss * 100,
            'win_loss_ratio': abs(avg_win / avg_loss) if avg_loss != 0 else 0,
            'stop_rate': stop_rate * 100,
            'market_analysis': market_analysis,
            'winning_trades_sample': winning_trades[:5],
            'losing_trades_sample': losing_trades[:5]
        }
    
    def _analyze_market_conditions(self, winning_trades: List[Dict], losing_trades: List[Dict]) -> Dict[str, Any]:
        """Analisa performance por condições de mercado."""
        
        analysis = {
            'rsi_analysis': {},
            'volatility_analysis': {},
            'trend_analysis': {},
            'macd_analysis': {}
        }
        
        # Análise por RSI
        for trade_type, trades in [('wins', winning_trades), ('losses', losing_trades)]:
            if trades:
                rsi_values = [t.get('rsi', 50) for t in trades if 'rsi' in t]
                if rsi_values:
                    analysis['rsi_analysis'][trade_type] = {
                        'mean': np.mean(rsi_values),
                        'std': np.std(rsi_values),
                        'oversold_pct': sum(1 for r in rsi_values if r < 30) / len(rsi_values) * 100,
                        'overbought_pct': sum(1 for r in rsi_values if r > 70) / len(rsi_values) * 100
                    }
                
                # Análise de volatilidade
                vol_values = [t.get('volatility_regime', 1) for t in trades if 'volatility_regime' in t]
                if vol_values:
                    analysis['volatility_analysis'][trade_type] = {
                        'mean_regime': np.mean(vol_values),
                        'high_vol_pct': sum(1 for v in vol_values if v > 1.2) / len(vol_values) * 100
                    }
                
                # Análise de trend
                trend_values = [t.get('trend_strength', 0) for t in trades if 'trend_strength' in t]
                if trend_values:
                    analysis['trend_analysis'][trade_type] = {
                        'mean_strength': np.mean(trend_values),
                        'strong_trend_pct': sum(1 for t in trend_values if t > 0.03) / len(trend_values) * 100
                    }
        
        return analysis
    
    def identify_improvement_opportunities(self, analysis_results: List[Dict]) -> Dict[str, Any]:
        """Identifica oportunidades de melhoria baseadas na análise."""
        
        print(f"\n🔍 IDENTIFICANDO OPORTUNIDADES DE MELHORIA...")
        
        opportunities = {
            'signal_quality': [],
            'risk_management': [],
            'market_timing': [],
            'filter_optimization': []
        }
        
        for result in analysis_results:
            if 'error' in result:
                continue
            
            symbol = result.get('symbol', 'Unknown')
            
            # Análise de qualidade de sinais
            if result['win_rate'] < 60:
                opportunities['signal_quality'].append({
                    'symbol': symbol,
                    'issue': f"Win rate baixo ({result['win_rate']:.1f}%)",
                    'suggestion': "Melhorar filtros de entrada"
                })
            
            if result['win_loss_ratio'] < 1.5:
                opportunities['signal_quality'].append({
                    'symbol': symbol,
                    'issue': f"Win/Loss ratio baixo ({result['win_loss_ratio']:.2f})",
                    'suggestion': "Otimizar take-profit e stop-loss"
                })
            
            # Análise de gestão de risco
            if result['stop_rate'] > 30:
                opportunities['risk_management'].append({
                    'symbol': symbol,
                    'issue': f"Taxa de stop alta ({result['stop_rate']:.1f}%)",
                    'suggestion': "Revisar níveis de stop-loss"
                })
            
            if result['max_drawdown_pct'] > 1.5:
                opportunities['risk_management'].append({
                    'symbol': symbol,
                    'issue': f"Drawdown alto ({result['max_drawdown_pct']:.2f}%)",
                    'suggestion': "Implementar position sizing dinâmico"
                })
            
            # Análise de timing de mercado
            market_analysis = result.get('market_analysis', {})
            
            # RSI analysis
            rsi_wins = market_analysis.get('rsi_analysis', {}).get('wins', {})
            rsi_losses = market_analysis.get('rsi_analysis', {}).get('losses', {})
            
            if rsi_wins and rsi_losses:
                if rsi_losses.get('oversold_pct', 0) > rsi_wins.get('oversold_pct', 0):
                    opportunities['market_timing'].append({
                        'symbol': symbol,
                        'issue': "Mais perdas em condições oversold",
                        'suggestion': "Evitar trades quando RSI < 25"
                    })
                
                if rsi_losses.get('overbought_pct', 0) > rsi_wins.get('overbought_pct', 0):
                    opportunities['market_timing'].append({
                        'symbol': symbol,
                        'issue': "Mais perdas em condições overbought",
                        'suggestion': "Evitar trades quando RSI > 75"
                    })
            
            # Volatility analysis
            vol_wins = market_analysis.get('volatility_analysis', {}).get('wins', {})
            vol_losses = market_analysis.get('volatility_analysis', {}).get('losses', {})
            
            if vol_wins and vol_losses:
                if vol_losses.get('high_vol_pct', 0) > vol_wins.get('high_vol_pct', 0):
                    opportunities['filter_optimization'].append({
                        'symbol': symbol,
                        'issue': "Mais perdas em alta volatilidade",
                        'suggestion': "Filtro de volatilidade mais restritivo"
                    })
        
        return opportunities


def run_comprehensive_analysis():
    """Executa análise abrangente da Quantum Momentum atual."""
    print("🚀 QUANTUM MOMENTUM ENHANCED - Análise Abrangente")
    print("=" * 60)
    print("🎯 Objetivo: Win Rate >60%, Sharpe >0.5, Return >3%, Max DD <2%")
    print("=" * 60)
    
    enhancer = QuantumMomentumEnhancer()
    
    symbols = ["BTCUSDT", "ETHUSDT"]
    analysis_results = []
    
    for symbol in symbols:
        print(f"\n📈 Analisando {symbol}...")
        
        df = enhancer.fetch_data(symbol, days=90)
        if df.empty or len(df) < 100:
            print(f"❌ Dados insuficientes para {symbol}")
            continue
        
        # Análise da versão atual
        result = enhancer.analyze_current_quantum_momentum(df, symbol)
        
        if 'error' not in result:
            result['symbol'] = symbol
            analysis_results.append(result)
            
            print(f"   📊 Performance Atual:")
            print(f"      Return: {result['total_return_pct']:.2f}%")
            print(f"      Sharpe: {result['sharpe_ratio']:.3f}")
            print(f"      Win Rate: {result['win_rate']:.1f}%")
            print(f"      Trades: {result['total_trades']}")
            print(f"      Max DD: {result['max_drawdown_pct']:.2f}%")
            print(f"      Win/Loss Ratio: {result['win_loss_ratio']:.2f}")
            print(f"      Stop Rate: {result['stop_rate']:.1f}%")
            
            # Status vs objetivos
            print(f"   🎯 Status vs Objetivos:")
            print(f"      Win Rate: {result['win_rate']:.1f}% {'✅' if result['win_rate'] >= 60 else '❌'} (>60%)")
            print(f"      Sharpe: {result['sharpe_ratio']:.3f} {'✅' if result['sharpe_ratio'] >= 0.5 else '❌'} (>0.5)")
            print(f"      Return: {result['total_return_pct']:.2f}% {'✅' if result['total_return_pct'] >= 3 else '❌'} (>3%)")
            print(f"      Max DD: {result['max_drawdown_pct']:.2f}% {'✅' if result['max_drawdown_pct'] <= 2 else '❌'} (<2%)")
        else:
            print(f"   ❌ {result['error']}")
    
    # Identifica oportunidades de melhoria
    if analysis_results:
        opportunities = enhancer.identify_improvement_opportunities(analysis_results)
        
        print(f"\n" + "="*60)
        print(f"🔍 OPORTUNIDADES DE MELHORIA IDENTIFICADAS")
        print(f"="*60)
        
        for category, issues in opportunities.items():
            if issues:
                print(f"\n📊 {category.upper().replace('_', ' ')}:")
                for issue in issues:
                    print(f"   • {issue['symbol']}: {issue['issue']}")
                    print(f"     💡 Sugestão: {issue['suggestion']}")
        
        # Salva análise
        output_dir = Path("results/quantum_momentum_analysis")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = int(time.time())
        with open(output_dir / f"analysis_results_{timestamp}.json", 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'analysis_results': analysis_results,
                'improvement_opportunities': opportunities
            }, f, indent=2, default=str)
        
        print(f"\n💾 Análise detalhada salva em results/quantum_momentum_analysis/")
        
        # Próximos passos
        print(f"\n🚀 PRÓXIMOS PASSOS:")
        print(f"   1. Implementar melhorias identificadas")
        print(f"   2. Testar versão enhanced")
        print(f"   3. Validar performance vs objetivos")
        print(f"   4. Integrar no sistema QUALIA")


if __name__ == "__main__":
    run_comprehensive_analysis()
