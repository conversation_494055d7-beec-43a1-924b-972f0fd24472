# Carta de Orientação Técnica - Interface de Usuário (src/ui)

Este documento consolida as observações da revisão holística dos componentes em `src/ui` conforme as diretrizes do roadmap QUALIA 2025-H2.

## 1. Papel do Módulo
- **Integração da Pipeline:** a interface web provê pontos de entrada para visualizar circuitos quânticos, gerenciar sessões de trading e apresentar métricas em tempo real. Atua no estágio final da cadeia (execution) e faz a ponte com o `QualiaState` para expor dados provenientes de *feature engineering* e do `quantum layer`.

## 2. Dependências e Acoplamentos
- **Internas:** `src.qualia.ui.*` para blueprints e inicialização, `QualiaStateExtension` para compartilhar estado, módulos de estratégia em `src/qualia/strategies`.
- **Externas:** `Flask`, `flask-socketio`, `flask-wtf`, `numpy`, `pandas` (mock data), e `ccxt` para integrações de exchanges quando disponíveis.
- **Pontos de acoplamento:** compartilhamento de `QualiaState` via `g`/`current_app.extensions`, uso direto do `TradingEngine` e do `QUALIAQuantumUniverse` em `trading.routes`.

## 3. Débitos Técnicos Identificados
1. **Falta de centralização de configuração** – URLs e parâmetros de socket são definidos diretamente no código.
2. **Tratamento genérico de exceções** – diversos `except Exception` silenciam erros críticos, dificultando depuração.
3. **Mock de mercado acoplado** – lógica de geração de dados simulados reside em `src/qualia/ui/trading/simulation.py`, impedindo reutilização por outras partes do sistema.
4. **Coleta de métricas limitada** – endpoints retornam informações sem registrar métricas ou eventos para auditabilidade.
5. **Testabilidade restrita** – algumas funções dependem de estado global (`TRADING_STATE`, `TRADING_ENGINE`), dificultando testes unitários.

## 4. Recomendações para 2025-H2
1. **Configuração via YAML** – mover parâmetros de interface (chaves, URLs, opções de CORS) para arquivos em `config/` lidos por `qualia.config`.
2. **Modularização da simulação** – extrair geradores de dados de `simulation.py` para um módulo reutilizável em `src/qualia/market`.
3. **Observabilidade aprimorada** – instrumentar métricas de latência e contagem de conexões no `SocketIO` com `DogStatsd`. Incluir `trace_id` nos logs dos blueprints.
4. **Refino de exceções** – substituir blocos `except Exception` por exceções específicas e logs `logger.exception` quando apropriado.
5. **Isolamento de estado** – tornar `TRADING_ENGINE` e `TRADING_STATE` injetáveis para permitir testes e múltiplas instâncias.
6. **Processo independente** – avaliar mover a interface para um processo separado, comunicando-se com o núcleo via `SocketIO` ou filas para reduzir acoplamento.

Essas medidas direcionam a interface de usuário para maior coesão, observabilidade e manutenibilidade, alinhando-a ao padrão de qualidade esperado para o QUALIA em 2025-H2.
