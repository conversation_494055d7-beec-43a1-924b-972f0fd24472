#!/usr/bin/env python3
"""
Estratégia QUALIA Corrigida - Resolve Problemas Fundamentais
YAA IMPLEMENTATION: Estratégia bidirecional com sinais robustos e correlação validada.
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path
import time
import json
import pandas as pd
import numpy as np
import requests
from typing import Dict, Any

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

class QualiaDataFetcher:
    """Fetcher otimizado para dados QUALIA."""
    
    BASE_URL = "https://api.binance.com/api/v3"
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'QUALIA-Corrected/1.0'})
    
    def fetch_historical_data(self, symbol: str, interval: str = "1h", days: int = 90) -> pd.DataFrame:
        """Busca dados históricos com indicadores técnicos."""
        try:
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            params = {
                'symbol': symbol.replace('/', ''),
                'interval': interval,
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            response = self.session.get(f"{self.BASE_URL}/klines", params=params)
            response.raise_for_status()
            data = response.json()
            
            if not data:
                return pd.DataFrame()
            
            # Converte para DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']].copy()
            
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index().dropna()
            
            # Adiciona indicadores técnicos robustos
            df = self.add_technical_indicators(df)
            
            print(f"✅ Dados obtidos para {symbol}: {len(df)} candles")
            return df
            
        except Exception as e:
            print(f"❌ Erro ao buscar dados para {symbol}: {e}")
            return pd.DataFrame()
    
    def add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Adiciona indicadores técnicos robustos."""
        
        # Retornos
        df['returns'] = df['close'].pct_change().fillna(0)
        
        # Médias móveis
        df['sma_10'] = df['close'].rolling(10).mean()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['sma_50'] = df['close'].rolling(50).mean()
        df['ema_12'] = df['close'].ewm(span=12).mean()
        df['ema_26'] = df['close'].ewm(span=26).mean()
        
        # RSI (Relative Strength Index)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        df['macd'] = df['ema_12'] - df['ema_26']
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(20).mean()
        bb_std = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # Volatilidade
        df['volatility'] = df['returns'].rolling(20).std()
        df['atr'] = self.calculate_atr(df, 14)
        
        # Volume indicators
        df['volume_sma'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        return df
    
    def calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calcula Average True Range."""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        return true_range.rolling(period).mean()


def run_corrected_qualia_backtest(
    df: pd.DataFrame, 
    symbol: str,
    price_amp: float, 
    news_amp: float, 
    min_conf: float
) -> Dict[str, Any]:
    """Executa backtest com estratégia QUALIA corrigida."""
    
    if df.empty or len(df) < 100:
        return {
            'total_return_pct': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown_pct': 0.0,
            'win_rate': 0.0,
            'total_trades': 0,
            'volatility': 0.0,
            'error': 'Dados insuficientes'
        }
    
    try:
        positions = []
        returns = []
        
        # Janela mínima para análise
        min_window = 50
        
        for i in range(min_window, len(df)):
            try:
                # Dados da janela atual
                current_data = df.iloc[i]
                
                # SINAL 1: RSI Mean Reversion (BIDIRECIONAL)
                rsi = current_data['rsi']
                if pd.isna(rsi):
                    rsi_signal = 0.0
                elif rsi > 70:
                    rsi_signal = -1.0  # Overbought -> SELL
                elif rsi < 30:
                    rsi_signal = 1.0   # Oversold -> BUY
                else:
                    rsi_signal = (50 - rsi) / 20  # Normalized between -1 and 1
                
                # SINAL 2: MACD Momentum (BIDIRECIONAL)
                macd = current_data['macd']
                macd_signal = current_data['macd_signal']
                if pd.isna(macd) or pd.isna(macd_signal):
                    macd_momentum = 0.0
                else:
                    macd_momentum = np.tanh((macd - macd_signal) * 1000)  # Normalized
                
                # SINAL 3: Bollinger Bands Position (BIDIRECIONAL)
                bb_position = current_data['bb_position']
                if pd.isna(bb_position):
                    bb_signal = 0.0
                else:
                    # Convert to signal: 0 = lower band (buy), 1 = upper band (sell)
                    bb_signal = (0.5 - bb_position) * 2  # Range: -1 to 1
                
                # SINAL 4: Trend Following (BIDIRECIONAL)
                sma_10 = current_data['sma_10']
                sma_20 = current_data['sma_20']
                if pd.isna(sma_10) or pd.isna(sma_20):
                    trend_signal = 0.0
                else:
                    trend_signal = np.tanh((sma_10 - sma_20) / sma_20 * 10)
                
                # CONFIANÇA baseada em volatilidade e volume
                volatility = current_data['volatility']
                volume_ratio = current_data['volume_ratio']
                
                if pd.isna(volatility) or pd.isna(volume_ratio):
                    confidence = 0.5
                else:
                    # Alta volatilidade = baixa confiança, alto volume = alta confiança
                    vol_factor = 1.0 / (1.0 + volatility * 50)  # 0 to 1
                    vol_factor = max(0.2, min(0.8, vol_factor))
                    
                    volume_factor = np.clip(volume_ratio / 2.0, 0.5, 1.5)  # 0.5 to 1.5
                    volume_factor = (volume_factor - 0.5) / 1.0  # 0 to 1
                    
                    confidence = 0.3 + 0.4 * vol_factor + 0.3 * volume_factor
                    confidence = np.clip(confidence, 0.2, 0.9)
                
                # Aplica filtro de confiança mínima
                if confidence < min_conf:
                    position = 0.0
                else:
                    # Combina sinais com pesos QUALIA
                    # price_amp afeta sinais de preço (RSI, BB)
                    # news_amp afeta sinais de momentum (MACD, Trend)
                    
                    price_weight = price_amp / 10.0
                    momentum_weight = news_amp / 10.0
                    
                    price_signals = (rsi_signal + bb_signal) / 2.0
                    momentum_signals = (macd_momentum + trend_signal) / 2.0
                    
                    combined_signal = (
                        price_weight * price_signals + 
                        momentum_weight * momentum_signals
                    ) / 2.0
                    
                    # Aplica confiança como multiplicador
                    position = np.clip(combined_signal * confidence, -1.0, 1.0)
                
                positions.append(position)
                
                # Calcula retorno
                if i > 0:
                    market_return = (df['close'].iloc[i] / df['close'].iloc[i-1]) - 1
                    strategy_return = positions[-2] * market_return if len(positions) > 1 else 0.0
                    
                    # Aplica custos de transação realistas (0.02%)
                    if len(positions) > 1:
                        position_change = abs(positions[-1] - positions[-2])
                        transaction_cost = position_change * 0.0002  # 0.02%
                        strategy_return -= transaction_cost
                    
                    returns.append(strategy_return)
                else:
                    returns.append(0.0)
                    
            except Exception as e:
                positions.append(0.0)
                returns.append(0.0)
        
        # Calcula métricas de performance
        if not returns or len(returns) == 0:
            return {
                'total_return_pct': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown_pct': 0.0,
                'win_rate': 0.0,
                'total_trades': 0,
                'volatility': 0.0,
                'error': 'Sem retornos calculados'
            }
        
        returns_series = pd.Series(returns)
        cumulative_returns = (1 + returns_series).cumprod()
        
        # Métricas
        total_return = cumulative_returns.iloc[-1] - 1 if len(cumulative_returns) > 0 else 0.0
        
        # Anualizadas (8760 horas por ano)
        periods_per_year = 8760
        mean_return = returns_series.mean() * periods_per_year
        volatility = returns_series.std() * np.sqrt(periods_per_year)
        sharpe_ratio = mean_return / volatility if volatility > 0 else 0.0
        
        # Max drawdown
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = abs(drawdown.min()) if len(drawdown) > 0 else 0.0
        
        # Trades
        position_changes = pd.Series(positions).diff().abs()
        total_trades = int(position_changes.sum())
        
        # Win rate
        winning_periods = (returns_series > 0).sum()
        win_rate = winning_periods / len(returns_series) if len(returns_series) > 0 else 0.0
        
        # Estatísticas de posição
        positions_array = np.array(positions)
        long_pct = np.mean(positions_array > 0) * 100
        short_pct = np.mean(positions_array < 0) * 100
        neutral_pct = np.mean(positions_array == 0) * 100
        
        return {
            'total_return_pct': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'volatility': volatility,
            'periods_analyzed': len(returns),
            'avg_position': np.mean(np.abs(positions)) if positions else 0.0,
            'long_pct': long_pct,
            'short_pct': short_pct,
            'neutral_pct': neutral_pct,
            'avg_confidence': confidence if 'confidence' in locals() else 0.5
        }
        
    except Exception as e:
        print(f"❌ Erro no backtest: {e}")
        return {
            'total_return_pct': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown_pct': 0.0,
            'win_rate': 0.0,
            'total_trades': 0,
            'volatility': 0.0,
            'error': str(e)
        }


def run_corrected_grid_search():
    """Executa grid search com estratégia QUALIA corrigida."""
    print("🚀 QUALIA ESTRATÉGIA CORRIGIDA - GRID SEARCH")
    print("=" * 60)
    
    # Inicializa fetcher
    fetcher = QualiaDataFetcher()
    
    # Configuração do grid search
    symbols = ["BTC/USDT", "ETH/USDT"]
    
    # Grid de parâmetros QUALIA
    price_amps = [2.0, 5.0, 8.0]      # Amplificação de preço (RSI, BB)
    news_amps = [1.5, 4.0, 7.0]       # Amplificação de momentum (MACD, Trend)
    min_confs = [0.4, 0.6, 0.8]       # Confiança mínima
    
    total_combinations = len(price_amps) * len(news_amps) * len(min_confs) * len(symbols)
    print(f"📊 Total de combinações: {total_combinations}")
    print(f"🧠 Estratégia: RSI + MACD + Bollinger + Trend (BIDIRECIONAL)")
    
    results = []
    
    for symbol in symbols:
        print(f"\n📈 Processando {symbol} com Estratégia Corrigida...")
        
        # Busca dados reais
        df = fetcher.fetch_historical_data(symbol, days=120)
        
        if df.empty:
            print(f"❌ Falha ao obter dados para {symbol}")
            continue
        
        for price_amp in price_amps:
            for news_amp in news_amps:
                for min_conf in min_confs:
                    print(f"🔄 Testando ({price_amp:.1f}, {news_amp:.1f}, {min_conf:.1f})")
                    
                    result = run_corrected_qualia_backtest(df, symbol, price_amp, news_amp, min_conf)
                    result.update({
                        'symbol': symbol,
                        'price_amplification': price_amp,
                        'news_amplification': news_amp,
                        'min_confidence': min_conf,
                        'data_points': len(df),
                        'strategy': 'QUALIA_Corrected'
                    })
                    results.append(result)
                    
                    if 'error' not in result:
                        print(f"   ✅ Return {result['total_return_pct']:.2%}, "
                              f"Sharpe {result['sharpe_ratio']:.3f}, "
                              f"Long {result['long_pct']:.0f}%, "
                              f"Short {result['short_pct']:.0f}%")
                    else:
                        print(f"   ❌ Erro: {result['error']}")
    
    # Análise dos resultados
    if results:
        print(f"\n" + "="*60)
        print(f"📊 RESULTADOS ESTRATÉGIA QUALIA CORRIGIDA")
        print(f"="*60)
        
        # Filtra resultados válidos
        valid_results = [r for r in results if 'error' not in r]
        
        if valid_results:
            df_results = pd.DataFrame(valid_results)
            
            print(f"\n📈 Estatísticas (Estratégia Corrigida):")
            print(f"   • Testes válidos: {len(valid_results)}/{len(results)}")
            print(f"   • Sharpe médio: {df_results['sharpe_ratio'].mean():.3f}")
            print(f"   • Return médio: {df_results['total_return_pct'].mean():.2%}")
            print(f"   • Drawdown médio: {df_results['max_drawdown_pct'].mean():.2%}")
            print(f"   • Win Rate médio: {df_results['win_rate'].mean():.2%}")
            print(f"   • Long médio: {df_results['long_pct'].mean():.1f}%")
            print(f"   • Short médio: {df_results['short_pct'].mean():.1f}%")
            
            # Conta resultados positivos
            positive_results = df_results[df_results['total_return_pct'] > 0]
            print(f"   • Configurações positivas: {len(positive_results)}/{len(valid_results)} ({len(positive_results)/len(valid_results)*100:.1f}%)")
            
            # Top 5 configurações
            top_configs = df_results.nlargest(5, 'sharpe_ratio')
            print(f"\n🏆 TOP 5 CONFIGURAÇÕES CORRIGIDAS:")
            for i, (_, row) in enumerate(top_configs.iterrows(), 1):
                print(f"   {i}. {row['symbol']}: ({row['price_amplification']:.1f}, "
                      f"{row['news_amplification']:.1f}, {row['min_confidence']:.1f})")
                print(f"      Return: {row['total_return_pct']:.2%}, "
                      f"Sharpe: {row['sharpe_ratio']:.3f}, "
                      f"Long: {row['long_pct']:.0f}%, "
                      f"Short: {row['short_pct']:.0f}%")
        
        # Salva resultados
        output_dir = Path("results/qualia_corrected_strategy")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = int(time.time())
        output_file = output_dir / f"corrected_results_{timestamp}.json"
        
        # Salva como JSON
        with open(output_file, 'w') as f:
            json.dump({
                'metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'total_combinations': len(results),
                    'valid_results': len(valid_results) if valid_results else 0,
                    'positive_results': len(positive_results) if valid_results else 0,
                    'symbols': symbols,
                    'strategy': 'QUALIA_Corrected',
                    'indicators': ['RSI', 'MACD', 'Bollinger_Bands', 'Trend_Following'],
                    'bidirectional': True
                },
                'results': results
            }, f, indent=2)
        
        print(f"\n💾 Resultados salvos em: {output_file}")
        
        # Salva CSV
        if valid_results:
            csv_file = output_dir / f"corrected_results_{timestamp}.csv"
            df_results.to_csv(csv_file, index=False)
            print(f"📊 CSV salvo em: {csv_file}")
        
        print(f"\n✅ Estratégia QUALIA Corrigida testada!")
        
        if valid_results and len(positive_results) > 0:
            print(f"🎉 SUCESSO: {len(positive_results)} configurações com performance POSITIVA!")
        else:
            print(f"⚠️ Ainda sem configurações positivas - mais ajustes necessários")
        
    else:
        print("❌ Nenhum resultado obtido")


if __name__ == "__main__":
    run_corrected_grid_search()
