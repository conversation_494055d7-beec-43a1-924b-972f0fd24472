"""
Enhanced Health Check System for QUALIA
Provides comprehensive health monitoring with real-time status, alerts, and recovery.
"""

import asyncio
import time
import psutil
import threading
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import json

from src.qualia.utils.logging_integration import get_component_logger

logger = get_component_logger("enhanced_health_system")


class HealthStatus(Enum):
    """Health status levels"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"
    INITIALIZING = "initializing"
    DEGRADED = "degraded"


class ComponentType(Enum):
    """Types of components"""
    CORE = "core"
    DATA = "data"
    TRADING = "trading"
    QUANTUM = "quantum"
    EXTERNAL = "external"
    INFRASTRUCTURE = "infrastructure"
    UNKNOWN = "unknown"


@dataclass
class HealthMetric:
    """Individual health metric"""
    name: str
    value: Union[float, int, str, bool]
    unit: str = ""
    threshold_warning: Optional[float] = None
    threshold_critical: Optional[float] = None
    timestamp: float = field(default_factory=time.time)
    
    @property
    def status(self) -> HealthStatus:
        """Get status based on thresholds"""
        if not isinstance(self.value, (int, float)):
            return HealthStatus.UNKNOWN

        if self.threshold_critical is not None and self.value >= self.threshold_critical:
            return HealthStatus.CRITICAL
        elif self.threshold_warning is not None and self.value >= self.threshold_warning:
            return HealthStatus.WARNING
        else:
            return HealthStatus.HEALTHY


@dataclass
class ComponentHealth:
    """Health status for a component"""
    component_id: str
    component_type: ComponentType
    status: HealthStatus
    message: str
    metrics: Dict[str, HealthMetric] = field(default_factory=dict)
    last_check: float = field(default_factory=time.time)
    uptime: float = 0.0
    error_count: int = 0
    warning_count: int = 0
    dependencies: List[str] = field(default_factory=list)
    
    @property
    def health_score(self) -> float:
        """Calculate overall health score (0.0 to 1.0)"""
        if not self.metrics:
            return 0.5
        
        scores = []
        for metric in self.metrics.values():
            if metric.status == HealthStatus.HEALTHY:
                scores.append(1.0)
            elif metric.status == HealthStatus.WARNING:
                scores.append(0.6)
            elif metric.status == HealthStatus.CRITICAL:
                scores.append(0.2)
            else:
                scores.append(0.5)
        
        return sum(scores) / len(scores) if scores else 0.5


@dataclass
class SystemHealth:
    """Overall system health"""
    overall_status: HealthStatus
    health_score: float
    components: Dict[str, ComponentHealth]
    critical_issues: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    timestamp: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'overall_status': self.overall_status.value,
            'health_score': self.health_score,
            'components': {
                name: {
                    'component_id': comp.component_id,
                    'component_type': comp.component_type.value,
                    'status': comp.status.value,
                    'message': comp.message,
                    'health_score': comp.health_score,
                    'last_check': comp.last_check,
                    'uptime': comp.uptime,
                    'error_count': comp.error_count,
                    'warning_count': comp.warning_count,
                    'metrics': {
                        metric_name: {
                            'name': metric.name,
                            'value': metric.value,
                            'unit': metric.unit,
                            'status': metric.status.value,
                            'timestamp': metric.timestamp
                        }
                        for metric_name, metric in comp.metrics.items()
                    }
                }
                for name, comp in self.components.items()
            },
            'critical_issues': self.critical_issues,
            'warnings': self.warnings,
            'recommendations': self.recommendations,
            'timestamp': self.timestamp
        }


class HealthChecker:
    """Base class for component health checkers"""
    
    def __init__(self, component_id: str, component_type: ComponentType):
        self.component_id = component_id
        self.component_type = component_type
        self.logger = get_component_logger(f"health_checker.{component_id}")
    
    async def check_health(self, component: Any) -> ComponentHealth:
        """Check component health - generic implementation"""
        # Generic health check for unknown components
        status = HealthStatus.HEALTHY
        message = f"{self.component_id} operational"
        metrics = {}

        try:
            # Basic checks for any component
            if hasattr(component, '__dict__'):
                metrics['attributes_count'] = HealthMetric(
                    name="Attributes Count",
                    value=len(component.__dict__),
                    threshold_warning=1,
                    threshold_critical=0
                )

            # Check if component has basic methods
            if hasattr(component, '__class__'):
                metrics['class_available'] = HealthMetric(
                    name="Class Available",
                    value=True
                )

        except Exception as e:
            status = HealthStatus.CRITICAL
            message = f"Error checking {self.component_id}: {e}"
            self.logger.error(f"Generic health check failed: {e}")

        return ComponentHealth(
            component_id=self.component_id,
            component_type=self.component_type,
            status=status,
            message=message,
            metrics=metrics
        )


class QASTCoreHealthChecker(HealthChecker):
    """Health checker for QAST Core"""
    
    async def check_health(self, component: Any) -> ComponentHealth:
        """Check QAST Core health"""
        metrics = {}
        status = HealthStatus.HEALTHY
        message = "QAST Core operational"
        
        try:
            # Check initialization
            if hasattr(component, 'quantum_universe') and component.quantum_universe:
                metrics['quantum_universe_initialized'] = HealthMetric(
                    name="Quantum Universe Initialized",
                    value=True
                )
            else:
                status = HealthStatus.CRITICAL
                message = "Quantum Universe not initialized"
                metrics['quantum_universe_initialized'] = HealthMetric(
                    name="Quantum Universe Initialized",
                    value=False
                )
            
            # Check processing state
            if hasattr(component, 'current_qualia_state'):
                state_available = component.current_qualia_state is not None
                metrics['qualia_state_available'] = HealthMetric(
                    name="QUALIA State Available",
                    value=state_available
                )
                
                if not state_available and status == HealthStatus.HEALTHY:
                    status = HealthStatus.WARNING
                    message = "No current QUALIA state available"
            
            # Check state history
            if hasattr(component, 'state_history'):
                history_count = len(component.state_history)
                metrics['state_history_count'] = HealthMetric(
                    name="State History Count",
                    value=history_count,
                    threshold_warning=5,
                    threshold_critical=1
                )
            
            # Check exchanges
            if hasattr(component, 'exchanges'):
                exchange_count = len(component.exchanges)
                metrics['active_exchanges'] = HealthMetric(
                    name="Active Exchanges",
                    value=exchange_count,
                    threshold_warning=1,
                    threshold_critical=0
                )
                
                if exchange_count == 0:
                    status = HealthStatus.WARNING
                    message = "No exchanges initialized - running in simulation mode"
            
        except Exception as e:
            status = HealthStatus.CRITICAL
            message = f"Error checking QAST Core: {e}"
            self.logger.error(f"Health check failed: {e}")
        
        return ComponentHealth(
            component_id=self.component_id,
            component_type=self.component_type,
            status=status,
            message=message,
            metrics=metrics
        )


class DataCollectorHealthChecker(HealthChecker):
    """Health checker for Data Collector"""
    
    async def check_health(self, component: Any) -> ComponentHealth:
        """Check Data Collector health"""
        metrics = {}
        status = HealthStatus.HEALTHY
        message = "Data Collector operational"
        
        try:
            # Check data availability
            if hasattr(component, 'get_data_count'):
                data_count = component.get_data_count()
                metrics['data_points'] = HealthMetric(
                    name="Data Points Available",
                    value=data_count,
                    threshold_warning=20,
                    threshold_critical=10
                )
            
            # Check active connections
            if hasattr(component, 'active_connections'):
                connections = getattr(component, 'active_connections', 0)
                metrics['active_connections'] = HealthMetric(
                    name="Active Connections",
                    value=connections,
                    threshold_warning=1,
                    threshold_critical=0
                )
            
            # Check last update time
            if hasattr(component, 'last_update_time'):
                last_update = getattr(component, 'last_update_time', 0)
                time_since_update = time.time() - last_update
                metrics['seconds_since_update'] = HealthMetric(
                    name="Seconds Since Last Update",
                    value=time_since_update,
                    threshold_warning=300,  # 5 minutes
                    threshold_critical=600  # 10 minutes
                )
            
            # Determine overall status from metrics
            for metric in metrics.values():
                if metric.status == HealthStatus.CRITICAL:
                    status = HealthStatus.CRITICAL
                    message = f"Critical issue: {metric.name}"
                    break
                elif metric.status == HealthStatus.WARNING and status == HealthStatus.HEALTHY:
                    status = HealthStatus.WARNING
                    message = f"Warning: {metric.name}"
        
        except Exception as e:
            status = HealthStatus.CRITICAL
            message = f"Error checking Data Collector: {e}"
            self.logger.error(f"Health check failed: {e}")
        
        return ComponentHealth(
            component_id=self.component_id,
            component_type=self.component_type,
            status=status,
            message=message,
            metrics=metrics
        )


class SystemResourcesHealthChecker(HealthChecker):
    """Health checker for system resources"""
    
    async def check_health(self, component: Any = None) -> ComponentHealth:
        """Check system resources health"""
        metrics = {}
        status = HealthStatus.HEALTHY
        message = "System resources normal"
        
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.1)  # Shorter interval for tests
            metrics['cpu_usage'] = HealthMetric(
                name="CPU Usage",
                value=cpu_percent,
                unit="%",
                threshold_warning=85,
                threshold_critical=98
            )

            # Memory usage
            memory = psutil.virtual_memory()
            metrics['memory_usage'] = HealthMetric(
                name="Memory Usage",
                value=memory.percent,
                unit="%",
                threshold_warning=90,
                threshold_critical=98
            )

            # Disk usage (use current directory for cross-platform compatibility)
            try:
                disk = psutil.disk_usage('.')
                metrics['disk_usage'] = HealthMetric(
                    name="Disk Usage",
                    value=disk.percent,
                    unit="%",
                    threshold_warning=90,
                    threshold_critical=98
                )
            except:
                # Fallback if disk check fails
                metrics['disk_usage'] = HealthMetric(
                    name="Disk Usage",
                    value=50.0,  # Assume 50% for testing
                    unit="%",
                    threshold_warning=90,
                    threshold_critical=98
                )
            
            # Process count
            process_count = len(psutil.pids())
            metrics['process_count'] = HealthMetric(
                name="Process Count",
                value=process_count,
                threshold_warning=500,
                threshold_critical=1000
            )
            
            # Determine overall status
            critical_metrics = [m for m in metrics.values() if m.status == HealthStatus.CRITICAL]
            warning_metrics = [m for m in metrics.values() if m.status == HealthStatus.WARNING]
            
            if critical_metrics:
                status = HealthStatus.CRITICAL
                message = f"Critical resource issue: {critical_metrics[0].name}"
            elif warning_metrics:
                status = HealthStatus.WARNING
                message = f"Resource warning: {warning_metrics[0].name}"
        
        except Exception as e:
            status = HealthStatus.CRITICAL
            message = f"Error checking system resources: {e}"
            self.logger.error(f"Health check failed: {e}")
        
        return ComponentHealth(
            component_id=self.component_id,
            component_type=self.component_type,
            status=status,
            message=message,
            metrics=metrics
        )


class EnhancedHealthSystem:
    """Enhanced health monitoring system for QUALIA"""
    
    def __init__(self):
        self.components: Dict[str, Any] = {}
        self.health_checkers: Dict[str, HealthChecker] = {}
        self.health_history: List[SystemHealth] = []
        self.alert_callbacks: List[Callable[[SystemHealth], None]] = []
        
        # Monitoring configuration
        self.check_interval = 30  # seconds
        self.history_max_size = 100
        self.running = False
        self.monitor_task: Optional[asyncio.Task] = None
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Initialize default checkers
        self._initialize_default_checkers()
        
        logger.info("🏥 Enhanced Health System initialized")
    
    def _initialize_default_checkers(self):
        """Initialize default health checkers"""
        # System resources checker (always available)
        self.health_checkers['system_resources'] = SystemResourcesHealthChecker(
            'system_resources', ComponentType.INFRASTRUCTURE
        )
    
    def register_component(self, 
                          component_id: str, 
                          component: Any,
                          component_type: ComponentType = ComponentType.CORE,
                          custom_checker: Optional[HealthChecker] = None) -> None:
        """
        Register a component for health monitoring
        
        Args:
            component_id: Unique identifier for the component
            component: The component instance to monitor
            component_type: Type of component
            custom_checker: Custom health checker (optional)
        """
        with self._lock:
            self.components[component_id] = component
            
            if custom_checker:
                self.health_checkers[component_id] = custom_checker
            else:
                # Use default checkers based on component type
                if component_id.lower().startswith('qast'):
                    self.health_checkers[component_id] = QASTCoreHealthChecker(
                        component_id, component_type
                    )
                elif 'data' in component_id.lower() or 'collector' in component_id.lower():
                    self.health_checkers[component_id] = DataCollectorHealthChecker(
                        component_id, component_type
                    )
                else:
                    # Generic checker for unknown components
                    self.health_checkers[component_id] = HealthChecker(
                        component_id, component_type
                    )
            
            logger.info(f"📋 Registered component for health monitoring: {component_id}")
    
    def add_alert_callback(self, callback: Callable[[SystemHealth], None]) -> None:
        """Add callback to be called when health status changes"""
        self.alert_callbacks.append(callback)
        logger.info("📞 Added health alert callback")
    
    async def check_system_health(self) -> SystemHealth:
        """Perform comprehensive system health check"""
        component_healths = {}
        
        # Check all registered components
        for component_id, component in self.components.items():
            if component_id in self.health_checkers:
                try:
                    health = await self.health_checkers[component_id].check_health(component)
                    component_healths[component_id] = health
                except Exception as e:
                    logger.error(f"❌ Health check failed for {component_id}: {e}")
                    component_healths[component_id] = ComponentHealth(
                        component_id=component_id,
                        component_type=ComponentType.UNKNOWN,
                        status=HealthStatus.CRITICAL,
                        message=f"Health check failed: {e}"
                    )
        
        # Check system resources
        if 'system_resources' in self.health_checkers:
            try:
                resource_health = await self.health_checkers['system_resources'].check_health()
                component_healths['system_resources'] = resource_health
            except Exception as e:
                logger.error(f"❌ System resources check failed: {e}")
        
        # Calculate overall system health
        overall_status, health_score = self._calculate_overall_health(component_healths)
        
        # Collect issues and recommendations
        critical_issues, warnings, recommendations = self._analyze_health_issues(component_healths)
        
        system_health = SystemHealth(
            overall_status=overall_status,
            health_score=health_score,
            components=component_healths,
            critical_issues=critical_issues,
            warnings=warnings,
            recommendations=recommendations
        )
        
        # Store in history
        with self._lock:
            self.health_history.append(system_health)
            if len(self.health_history) > self.history_max_size:
                self.health_history = self.health_history[-self.history_max_size:]
        
        # Trigger alerts
        for callback in self.alert_callbacks:
            try:
                callback(system_health)
            except Exception as e:
                logger.error(f"❌ Alert callback failed: {e}")
        
        return system_health
    
    def _calculate_overall_health(self, components: Dict[str, ComponentHealth]) -> tuple[HealthStatus, float]:
        """Calculate overall system health status and score"""
        if not components:
            return HealthStatus.UNKNOWN, 0.0
        
        # Count status types
        critical_count = sum(1 for c in components.values() if c.status == HealthStatus.CRITICAL)
        warning_count = sum(1 for c in components.values() if c.status == HealthStatus.WARNING)
        
        # Calculate health score
        total_score = sum(c.health_score for c in components.values())
        health_score = total_score / len(components)
        
        # Determine overall status
        if critical_count > 0:
            return HealthStatus.CRITICAL, health_score
        elif warning_count > 0:
            return HealthStatus.WARNING, health_score
        else:
            return HealthStatus.HEALTHY, health_score
    
    def _analyze_health_issues(self, components: Dict[str, ComponentHealth]) -> tuple[List[str], List[str], List[str]]:
        """Analyze health issues and generate recommendations"""
        critical_issues = []
        warnings = []
        recommendations = []
        
        for comp_id, comp in components.items():
            if comp.status == HealthStatus.CRITICAL:
                critical_issues.append(f"{comp_id}: {comp.message}")
            elif comp.status == HealthStatus.WARNING:
                warnings.append(f"{comp_id}: {comp.message}")
            
            # Generate recommendations based on metrics
            for metric in comp.metrics.values():
                if metric.status == HealthStatus.CRITICAL:
                    if 'memory' in metric.name.lower():
                        recommendations.append("Consider increasing available memory or optimizing memory usage")
                    elif 'cpu' in metric.name.lower():
                        recommendations.append("Consider reducing CPU load or scaling resources")
                    elif 'disk' in metric.name.lower():
                        recommendations.append("Free up disk space or expand storage capacity")
                    elif 'connection' in metric.name.lower():
                        recommendations.append("Check network connectivity and external service availability")
        
        return critical_issues, warnings, list(set(recommendations))
    
    async def start_monitoring(self) -> None:
        """Start continuous health monitoring"""
        if self.running:
            logger.warning("⚠️ Health monitoring already running")
            return
        
        self.running = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        logger.info(f"🔄 Started health monitoring (interval: {self.check_interval}s)")
    
    async def stop_monitoring(self) -> None:
        """Stop health monitoring"""
        self.running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("🛑 Stopped health monitoring")
    
    async def _monitoring_loop(self) -> None:
        """Main monitoring loop"""
        while self.running:
            try:
                await self.check_system_health()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in monitoring loop: {e}")
                await asyncio.sleep(self.check_interval)
    
    def get_health_status(self) -> Optional[SystemHealth]:
        """Get latest health status"""
        with self._lock:
            return self.health_history[-1] if self.health_history else None
    
    def get_health_history(self, limit: int = 10) -> List[SystemHealth]:
        """Get health history"""
        with self._lock:
            return self.health_history[-limit:] if self.health_history else []
    
    def export_health_report(self, file_path: Path) -> None:
        """Export health report to file"""
        latest_health = self.get_health_status()
        if latest_health:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(latest_health.to_dict(), f, indent=2)
            logger.info(f"📄 Health report exported to: {file_path}")


# Global instance
_global_health_system: Optional[EnhancedHealthSystem] = None
_health_lock = threading.Lock()


def get_health_system() -> EnhancedHealthSystem:
    """Get global health system instance"""
    global _global_health_system
    if _global_health_system is None:
        with _health_lock:
            if _global_health_system is None:
                _global_health_system = EnhancedHealthSystem()
    return _global_health_system


class HealthDashboard:
    """Simple web dashboard for health monitoring"""

    def __init__(self, health_system: EnhancedHealthSystem, port: int = 8080):
        self.health_system = health_system
        self.port = port
        self.logger = get_component_logger("health_dashboard")

    def generate_html_dashboard(self) -> str:
        """Generate HTML dashboard"""
        health = self.health_system.get_health_status()

        if not health:
            return """
            <html>
            <head><title>QUALIA Health Dashboard</title></head>
            <body>
                <h1>QUALIA Health Dashboard</h1>
                <p>No health data available</p>
            </body>
            </html>
            """

        # Status color mapping
        status_colors = {
            'healthy': '#28a745',
            'warning': '#ffc107',
            'critical': '#dc3545',
            'unknown': '#6c757d',
            'initializing': '#17a2b8',
            'degraded': '#fd7e14'
        }

        overall_color = status_colors.get(health.overall_status.value, '#6c757d')

        # Generate component cards
        component_cards = ""
        for comp_id, comp in health.components.items():
            comp_color = status_colors.get(comp.status.value, '#6c757d')

            metrics_html = ""
            for metric_name, metric in comp.metrics.items():
                metric_color = status_colors.get(metric.status.value, '#6c757d')
                metrics_html += f"""
                <div style="margin: 5px 0; padding: 5px; border-left: 3px solid {metric_color};">
                    <strong>{metric.name}:</strong> {metric.value} {metric.unit}
                    <span style="color: {metric_color};">({metric.status.value})</span>
                </div>
                """

            component_cards += f"""
            <div style="border: 1px solid #ddd; margin: 10px; padding: 15px; border-radius: 5px;">
                <h3 style="color: {comp_color}; margin-top: 0;">
                    {comp_id}
                    <span style="font-size: 0.8em;">({comp.component_type.value})</span>
                </h3>
                <p><strong>Status:</strong> <span style="color: {comp_color};">{comp.status.value}</span></p>
                <p><strong>Message:</strong> {comp.message}</p>
                <p><strong>Health Score:</strong> {comp.health_score:.2f}</p>
                <p><strong>Uptime:</strong> {comp.uptime:.1f}s</p>
                <div style="margin-top: 10px;">
                    <strong>Metrics:</strong>
                    {metrics_html}
                </div>
            </div>
            """

        # Generate issues and recommendations
        issues_html = ""
        if health.critical_issues:
            issues_html += "<h3 style='color: #dc3545;'>Critical Issues:</h3><ul>"
            for issue in health.critical_issues:
                issues_html += f"<li style='color: #dc3545;'>{issue}</li>"
            issues_html += "</ul>"

        if health.warnings:
            issues_html += "<h3 style='color: #ffc107;'>Warnings:</h3><ul>"
            for warning in health.warnings:
                issues_html += f"<li style='color: #ffc107;'>{warning}</li>"
            issues_html += "</ul>"

        if health.recommendations:
            issues_html += "<h3 style='color: #17a2b8;'>Recommendations:</h3><ul>"
            for rec in health.recommendations:
                issues_html += f"<li style='color: #17a2b8;'>{rec}</li>"
            issues_html += "</ul>"

        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>QUALIA Health Dashboard</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f8f9fa; }}
                .header {{ background-color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .status-badge {{ padding: 5px 10px; border-radius: 3px; color: white; font-weight: bold; }}
                .components {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }}
                .refresh-btn {{ background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }}
                .refresh-btn:hover {{ background-color: #0056b3; }}
            </style>
            <script>
                function refreshPage() {{
                    location.reload();
                }}
                // Auto-refresh every 30 seconds
                setTimeout(refreshPage, 30000);
            </script>
        </head>
        <body>
            <div class="header">
                <h1>🏥 QUALIA Health Dashboard</h1>
                <p><strong>Overall Status:</strong>
                   <span class="status-badge" style="background-color: {overall_color};">
                       {health.overall_status.value.upper()}
                   </span>
                </p>
                <p><strong>Health Score:</strong> {health.health_score:.2f}/1.0</p>
                <p><strong>Last Check:</strong> {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(health.timestamp))}</p>
                <button class="refresh-btn" onclick="refreshPage()">🔄 Refresh</button>
            </div>

            {issues_html}

            <h2>Components Status</h2>
            <div class="components">
                {component_cards}
            </div>

            <div style="margin-top: 30px; padding: 15px; background-color: white; border-radius: 5px;">
                <p><small>Dashboard auto-refreshes every 30 seconds |
                   Components: {len(health.components)} |
                   Monitoring since system start</small></p>
            </div>
        </body>
        </html>
        """

    async def start_dashboard_server(self):
        """Start simple HTTP server for dashboard"""
        try:
            from http.server import HTTPServer, BaseHTTPRequestHandler
            import threading

            class DashboardHandler(BaseHTTPRequestHandler):
                def do_GET(self):
                    if self.path == '/' or self.path == '/health':
                        self.send_response(200)
                        self.send_header('Content-type', 'text/html')
                        self.end_headers()
                        html = self.server.dashboard.generate_html_dashboard()
                        self.wfile.write(html.encode('utf-8'))
                    elif self.path == '/api/health':
                        self.send_response(200)
                        self.send_header('Content-type', 'application/json')
                        self.end_headers()
                        health = self.server.dashboard.health_system.get_health_status()
                        if health:
                            import json
                            self.wfile.write(json.dumps(health.to_dict()).encode('utf-8'))
                        else:
                            self.wfile.write(b'{"status": "no_data"}')
                    else:
                        self.send_response(404)
                        self.end_headers()

                def log_message(self, format, *args):
                    # Suppress default logging
                    pass

            server = HTTPServer(('localhost', self.port), DashboardHandler)
            server.dashboard = self

            def run_server():
                self.logger.info(f"🌐 Health dashboard started at http://localhost:{self.port}")
                server.serve_forever()

            server_thread = threading.Thread(target=run_server, daemon=True)
            server_thread.start()

            return server

        except Exception as e:
            self.logger.error(f"❌ Failed to start dashboard server: {e}")
            return None


def create_health_dashboard(port: int = 8080) -> HealthDashboard:
    """Create health dashboard instance"""
    health_system = get_health_system()
    return HealthDashboard(health_system, port)
