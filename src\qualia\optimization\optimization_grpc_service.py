"""
QUALIA Optimization gRPC Service
Serviço gRPC para comunicação em tempo real entre o otimizador e o sistema de trading
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor

import grpc
from grpc import aio

from ..utils.logger import get_logger

logger = get_logger(__name__)

# Definições de protocolo gRPC (simuladas - em produção usaria .proto compilado)
class OptimizationRequest:
    def __init__(self, symbol: str, request_type: str, data: Dict = None):
        self.symbol = symbol
        self.request_type = request_type  # GET_PARAMETERS, UPDATE_PARAMETERS, GET_STATUS
        self.data = data or {}

class OptimizationResponse:
    def __init__(self, success: bool, data: Dict = None, error: str = None):
        self.success = success
        self.data = data or {}
        self.error = error

class ParameterUpdate:
    def __init__(self, symbol: str, parameters: Dict[str, float], timestamp: datetime = None):
        self.symbol = symbol
        self.parameters = parameters
        self.timestamp = timestamp or datetime.now()

class OptimizationGRPCService:
    """
    Serviço gRPC para comunicação com o sistema de trading.
    
    Endpoints:
    - GetCurrentParameters: Retorna parâmetros atuais para um símbolo
    - UpdateParameters: Atualiza parâmetros de um símbolo
    - GetOptimizationStatus: Retorna status do otimizador
    - SubscribeParameterUpdates: Stream de atualizações de parâmetros
    """
    
    def __init__(self, port: int = 50051):
        self.port = port
        self.server = None
        self.is_running = False
        
        # Estado do serviço
        self.current_parameters: Dict[str, Dict[str, float]] = {}
        self.parameter_subscribers: List[Any] = []
        self.optimization_stats = {}
        
        # Executor para operações assíncronas
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        logger.info(f"🌐 OptimizationGRPCService configurado na porta {port}")
    
    async def start(self):
        """Inicia o servidor gRPC."""
        try:
            # Criar servidor gRPC
            self.server = aio.server(self.executor)
            
            # Registrar serviços (simulado - em produção usaria protobuf)
            # self.server.add_insecure_port(f'[::]:{self.port}')
            
            # Simular inicialização do servidor
            self.is_running = True
            
            logger.info(f"🚀 Servidor gRPC iniciado na porta {self.port}")
            
            # Iniciar loop de processamento
            asyncio.create_task(self._process_requests())
            
        except Exception as e:
            logger.error(f"❌ Erro ao iniciar servidor gRPC: {e}")
            raise
    
    async def stop(self):
        """Para o servidor gRPC."""
        logger.info("🛑 Parando servidor gRPC...")
        
        self.is_running = False
        
        if self.server:
            await self.server.stop(grace=5.0)
        
        self.executor.shutdown(wait=True)
        
        logger.info("✅ Servidor gRPC parado")
    
    async def _process_requests(self):
        """Loop de processamento de requisições (simulado)."""
        while self.is_running:
            try:
                # Simular processamento de requisições
                await asyncio.sleep(1.0)
                
                # Em produção, isso seria tratado pelo framework gRPC
                # Aqui apenas mantemos o serviço ativo
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Erro no processamento de requisições: {e}")
                await asyncio.sleep(5.0)
    
    # Métodos do serviço gRPC (simulados)
    
    async def get_current_parameters(self, symbol: str) -> OptimizationResponse:
        """
        Retorna os parâmetros atuais para um símbolo.
        """
        try:
            parameters = self.current_parameters.get(symbol, {
                "price_amplification": 1.0,
                "news_amplification": 11.3,
                "min_confidence": 0.37
            })
            
            response_data = {
                "symbol": symbol,
                "parameters": parameters,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.debug(f"📊 Parâmetros solicitados para {symbol}: {parameters}")
            
            return OptimizationResponse(success=True, data=response_data)
            
        except Exception as e:
            logger.error(f"❌ Erro ao obter parâmetros para {symbol}: {e}")
            return OptimizationResponse(success=False, error=str(e))
    
    async def update_parameters(self, symbol: str, parameters: Dict[str, float]) -> OptimizationResponse:
        """
        Atualiza os parâmetros de um símbolo.
        """
        try:
            # Validar parâmetros
            required_params = ["price_amplification", "news_amplification", "min_confidence"]
            for param in required_params:
                if param not in parameters:
                    raise ValueError(f"Parâmetro obrigatório ausente: {param}")
            
            # Validar ranges
            if not (0.1 <= parameters["price_amplification"] <= 20.0):
                raise ValueError("price_amplification deve estar entre 0.1 e 20.0")
            if not (0.1 <= parameters["news_amplification"] <= 20.0):
                raise ValueError("news_amplification deve estar entre 0.1 e 20.0")
            if not (0.1 <= parameters["min_confidence"] <= 1.0):
                raise ValueError("min_confidence deve estar entre 0.1 e 1.0")
            
            # Atualizar parâmetros
            self.current_parameters[symbol] = parameters.copy()
            
            # Notificar subscribers
            await self._notify_parameter_update(symbol, parameters)
            
            response_data = {
                "symbol": symbol,
                "parameters": parameters,
                "updated_at": datetime.now().isoformat()
            }
            
            logger.info(f"✅ Parâmetros atualizados para {symbol}: {parameters}")
            
            return OptimizationResponse(success=True, data=response_data)
            
        except Exception as e:
            logger.error(f"❌ Erro ao atualizar parâmetros para {symbol}: {e}")
            return OptimizationResponse(success=False, error=str(e))
    
    async def get_optimization_status(self) -> OptimizationResponse:
        """
        Retorna o status atual do otimizador.
        """
        try:
            status_data = {
                "is_running": self.is_running,
                "active_symbols": list(self.current_parameters.keys()),
                "total_symbols": len(self.current_parameters),
                "server_port": self.port,
                "subscribers_count": len(self.parameter_subscribers),
                "timestamp": datetime.now().isoformat(),
                "optimization_stats": self.optimization_stats
            }
            
            return OptimizationResponse(success=True, data=status_data)
            
        except Exception as e:
            logger.error(f"❌ Erro ao obter status: {e}")
            return OptimizationResponse(success=False, error=str(e))
    
    async def notify_parameter_update(self, symbol: str, parameters: Dict[str, float]):
        """
        Notifica sobre atualização de parâmetros (chamado pelo otimizador).
        """
        try:
            # Atualizar parâmetros internos
            self.current_parameters[symbol] = parameters.copy()
            
            # Notificar subscribers
            await self._notify_parameter_update(symbol, parameters)
            
            logger.info(f"📡 Notificação enviada para {symbol}: {parameters}")
            
        except Exception as e:
            logger.error(f"❌ Erro ao notificar atualização: {e}")
    
    async def _notify_parameter_update(self, symbol: str, parameters: Dict[str, float]):
        """
        Notifica todos os subscribers sobre atualização de parâmetros.
        """
        if not self.parameter_subscribers:
            return
        
        update = ParameterUpdate(symbol, parameters)
        
        # Notificar todos os subscribers (simulado)
        for subscriber in self.parameter_subscribers.copy():
            try:
                # Em produção, isso seria um stream gRPC
                await self._send_to_subscriber(subscriber, update)
            except Exception as e:
                logger.error(f"❌ Erro ao notificar subscriber: {e}")
                # Remover subscriber com falha
                if subscriber in self.parameter_subscribers:
                    self.parameter_subscribers.remove(subscriber)
    
    async def _send_to_subscriber(self, subscriber, update: ParameterUpdate):
        """
        Envia atualização para um subscriber específico.
        """
        # Simulação de envio - em produção seria stream gRPC
        message = {
            "symbol": update.symbol,
            "parameters": update.parameters,
            "timestamp": update.timestamp.isoformat()
        }
        
        logger.debug(f"📤 Enviando para subscriber: {message}")
    
    def add_subscriber(self, subscriber):
        """Adiciona um subscriber para atualizações de parâmetros."""
        if subscriber not in self.parameter_subscribers:
            self.parameter_subscribers.append(subscriber)
            logger.info(f"📥 Novo subscriber adicionado. Total: {len(self.parameter_subscribers)}")
    
    def remove_subscriber(self, subscriber):
        """Remove um subscriber."""
        if subscriber in self.parameter_subscribers:
            self.parameter_subscribers.remove(subscriber)
            logger.info(f"📤 Subscriber removido. Total: {len(self.parameter_subscribers)}")
    
    def update_optimization_stats(self, stats: Dict[str, Any]):
        """Atualiza estatísticas de otimização."""
        self.optimization_stats = stats.copy()
        logger.debug("📊 Estatísticas de otimização atualizadas")
    
    def get_all_parameters(self) -> Dict[str, Dict[str, float]]:
        """Retorna todos os parâmetros atuais."""
        return self.current_parameters.copy()
    
    def set_parameters_batch(self, parameters_batch: Dict[str, Dict[str, float]]):
        """Define parâmetros em lote."""
        for symbol, params in parameters_batch.items():
            self.current_parameters[symbol] = params.copy()
        
        logger.info(f"📊 Parâmetros definidos em lote para {len(parameters_batch)} símbolos")

# Cliente gRPC para comunicação com o serviço
class OptimizationGRPCClient:
    """
    Cliente gRPC para comunicação com o serviço de otimização.
    """
    
    def __init__(self, host: str = "localhost", port: int = 50051):
        self.host = host
        self.port = port
        self.channel = None
        self.stub = None
        
        logger.info(f"🔌 Cliente gRPC configurado para {host}:{port}")
    
    async def connect(self):
        """Conecta ao serviço gRPC."""
        try:
            # Em produção, usaria grpc.aio.insecure_channel
            # self.channel = grpc.aio.insecure_channel(f'{self.host}:{self.port}')
            # self.stub = OptimizationServiceStub(self.channel)
            
            # Simulação de conexão
            logger.info(f"🔗 Conectado ao serviço de otimização em {self.host}:{self.port}")
            
        except Exception as e:
            logger.error(f"❌ Erro ao conectar: {e}")
            raise
    
    async def disconnect(self):
        """Desconecta do serviço."""
        if self.channel:
            await self.channel.close()
        logger.info("🔌 Desconectado do serviço de otimização")
    
    async def get_parameters(self, symbol: str) -> Dict[str, float]:
        """Obtém parâmetros atuais para um símbolo."""
        # Simulação - em produção faria chamada gRPC real
        default_params = {
            "price_amplification": 1.0,
            "news_amplification": 11.3,
            "min_confidence": 0.37
        }
        
        logger.debug(f"📊 Parâmetros obtidos para {symbol}: {default_params}")
        return default_params
    
    async def update_parameters(self, symbol: str, parameters: Dict[str, float]) -> bool:
        """Atualiza parâmetros de um símbolo."""
        # Simulação - em produção faria chamada gRPC real
        logger.info(f"📤 Atualizando parâmetros para {symbol}: {parameters}")
        return True
