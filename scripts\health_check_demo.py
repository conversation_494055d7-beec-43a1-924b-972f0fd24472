#!/usr/bin/env python3
"""
Script de demonstração do Sistema de Health Check do QUALIA
Mostra como verificar a saúde de todos os componentes do sistema.
"""

import asyncio
import sys
from pathlib import Path

# Adicionar o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.monitoring.health_check import (
    SystemHealthChecker,
    ComponentStatus,
    SystemReadiness,
    quick_health_check
)


class MockDataCollector:
    """Mock do data collector para demonstração."""
    
    def __init__(self, healthy: bool = True):
        self.healthy = healthy
        self.last_update_time = None
        self.active_connections = 1 if healthy else 0
        
        if healthy:
            import time
            self.last_update_time = time.time()
    
    def get_data_count(self):
        return 50 if self.healthy else 5


class MockQuantumLayer:
    """Mock da quantum layer para demonstração."""
    
    def __init__(self, healthy: bool = True):
        self.healthy = healthy
        self._initialized = healthy
        
    def is_initialized(self):
        return self._initialized
    
    def pattern_cache_size(self):
        return 10 if self.healthy else 0


class MockHolographicUniverse:
    """Mock do universo holográfico para demonstração."""
    
    def __init__(self, healthy: bool = True):
        self.healthy = healthy
        
    def get_field_energy(self):
        return 0.5 if self.healthy else 0.0
    
    def get_pattern_count(self):
        return 14 if self.healthy else 0


class MockStrategy:
    """Mock de estratégia para demonstração."""
    
    def __init__(self, ready: bool = True):
        self.ready = ready
        
    def is_ready(self):
        return self.ready


class MockRiskManager:
    """Mock do risk manager para demonstração."""
    
    def __init__(self, healthy: bool = True):
        self.healthy = healthy
        self.max_risk_per_trade = 0.02 if healthy else 0.0
        self.available_capital = 10000.0 if healthy else 0.0
        
    def get_open_positions(self):
        return [] if self.healthy else None


async def demo_healthy_system():
    """Demonstra um sistema saudável."""
    print("🟢 DEMONSTRAÇÃO: Sistema Saudável")
    print("=" * 50)
    
    # Criar componentes mock saudáveis
    components = {
        "data_collector": MockDataCollector(healthy=True),
        "quantum_layer": MockQuantumLayer(healthy=True),
        "holographic_universe": MockHolographicUniverse(healthy=True),
        "strategies": {
            "BTCUSDT": MockStrategy(ready=True),
            "ETHUSDT": MockStrategy(ready=True),
        },
        "risk_manager": MockRiskManager(healthy=True)
    }
    
    # Executar health check
    report = await quick_health_check(components)
    
    # Imprimir relatório
    checker = SystemHealthChecker()
    checker.print_health_report(report)
    
    return report


async def demo_problematic_system():
    """Demonstra um sistema com problemas."""
    print("\n🔴 DEMONSTRAÇÃO: Sistema com Problemas")
    print("=" * 50)
    
    # Criar componentes mock com problemas
    components = {
        "data_collector": MockDataCollector(healthy=False),  # Poucos dados
        "quantum_layer": MockQuantumLayer(healthy=False),    # Não inicializada
        "holographic_universe": MockHolographicUniverse(healthy=False),  # Energia zero
        "strategies": {
            "BTCUSDT": MockStrategy(ready=True),
            "ETHUSDT": MockStrategy(ready=False),  # Uma estratégia com problema
        },
        "risk_manager": MockRiskManager(healthy=False)  # Capital zero
    }
    
    # Executar health check
    report = await quick_health_check(components)
    
    # Imprimir relatório
    checker = SystemHealthChecker()
    checker.print_health_report(report)
    
    return report


async def demo_continuous_monitoring():
    """Demonstra monitoramento contínuo."""
    print("\n🔄 DEMONSTRAÇÃO: Monitoramento Contínuo")
    print("=" * 50)
    
    # Componentes que mudam de estado
    data_collector = MockDataCollector(healthy=True)
    
    components = {
        "data_collector": data_collector,
        "quantum_layer": MockQuantumLayer(healthy=True),
        "holographic_universe": MockHolographicUniverse(healthy=True),
        "strategies": {"BTCUSDT": MockStrategy(ready=True)},
        "risk_manager": MockRiskManager(healthy=True)
    }
    
    checker = SystemHealthChecker(components)
    
    print("Executando 3 verificações com intervalo de 2 segundos...")
    
    for i in range(3):
        print(f"\n--- Verificação {i+1} ---")
        
        # Simular mudança de estado
        if i == 1:
            data_collector.healthy = False
            print("🔧 Simulando problema no data collector...")
        elif i == 2:
            data_collector.healthy = True
            print("✅ Simulando correção do data collector...")
        
        report = await checker.get_readiness_report()
        
        # Mostrar resumo
        summary = checker.get_health_summary()
        print(f"Status: {summary['overall_status']}")
        print(f"Score: {summary['readiness_score']:.2%}")
        print(f"Pronto para trading: {'SIM' if summary['is_ready_for_trading'] else 'NÃO'}")
        
        if i < 2:  # Não aguardar na última iteração
            await asyncio.sleep(2)


async def demo_custom_component():
    """Demonstra adição de componente customizado."""
    print("\n🔧 DEMONSTRAÇÃO: Componente Customizado")
    print("=" * 50)
    
    class CustomComponent:
        def __init__(self):
            self.status = "operational"
            
        def is_healthy(self):
            return self.status == "operational"
            
        def get_status(self):
            return {"status": "ok", "details": "Custom component working"}
    
    components = {
        "data_collector": MockDataCollector(healthy=True),
        "custom_module": CustomComponent()
    }
    
    checker = SystemHealthChecker(components)
    
    # Registrar componente adicional
    checker.register_component("new_feature", CustomComponent())
    
    report = await checker.get_readiness_report()
    checker.print_health_report(report)


async def main():
    """Função principal de demonstração."""
    print("🏥 SISTEMA DE HEALTH CHECK DO QUALIA")
    print("=" * 60)
    print("Este script demonstra as capacidades do sistema de health check.")
    print("=" * 60)
    
    try:
        # Demonstrar sistema saudável
        healthy_report = await demo_healthy_system()
        
        # Demonstrar sistema com problemas
        problem_report = await demo_problematic_system()
        
        # Demonstrar monitoramento contínuo
        await demo_continuous_monitoring()
        
        # Demonstrar componente customizado
        await demo_custom_component()
        
        print("\n✅ DEMONSTRAÇÃO CONCLUÍDA")
        print("=" * 60)
        print("O sistema de health check está pronto para uso!")
        print("Integre-o ao seu sistema QUALIA para monitoramento robusto.")
        
    except Exception as e:
        print(f"\n❌ Erro na demonstração: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main()) 