#!/usr/bin/env python3
"""
Teste de Impacto do News Amplification na Quantum Momentum
Analisa como diferentes níveis de news_amplification afetam a performance.
"""

import pandas as pd
import numpy as np
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any


def fetch_current_data(symbol: str = "BTCUSDT", days: int = 30) -> pd.DataFrame:
    """Busca dados atuais do mercado."""
    try:
        end_time = int(datetime.now().timestamp() * 1000)
        start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
        
        url = "https://api.binance.com/api/v3/klines"
        params = {
            'symbol': symbol,
            'interval': '1h',
            'startTime': start_time,
            'endTime': end_time,
            'limit': 1000
        }
        
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        data = response.json()
        df = pd.DataFrame(data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])
        
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df.set_index('timestamp', inplace=True)
        df = df.sort_index().dropna()
        
        # Indicadores básicos
        df['returns'] = df['close'].pct_change()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['sma_50'] = df['close'].rolling(50).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        df['volatility'] = df['returns'].rolling(20).std()
        
        return df
        
    except Exception as e:
        print(f"❌ Erro ao buscar dados: {e}")
        return pd.DataFrame()


def simulate_news_amplification_effect(df: pd.DataFrame, news_amp: float) -> pd.DataFrame:
    """Simula o efeito do news amplification nos sinais."""
    
    # Simula eventos de notícias baseados em volatilidade e movimentos de preço
    df_sim = df.copy()
    
    # Detecta eventos de alta volatilidade como proxy para notícias
    vol_threshold = df['volatility'].quantile(0.8)  # Top 20% volatilidade
    price_change_threshold = df['returns'].abs().quantile(0.8)  # Top 20% movimentos
    
    # Cria "news events" sintéticos
    news_events = (df['volatility'] > vol_threshold) | (df['returns'].abs() > price_change_threshold)
    
    # Simula sentiment score baseado no movimento de preço
    # Movimento positivo = sentiment positivo, negativo = sentiment negativo
    sentiment_score = np.where(df['returns'] > 0, 
                              np.random.normal(0.6, 0.2, len(df)),  # Sentiment positivo
                              np.random.normal(-0.6, 0.2, len(df)))  # Sentiment negativo
    
    # Aplica amplificação de notícias
    news_amplification_factor = np.where(news_events, 
                                        1 + (news_amp - 1) * np.abs(sentiment_score),
                                        1.0)
    
    df_sim['news_events'] = news_events
    df_sim['sentiment_score'] = sentiment_score
    df_sim['news_amp_factor'] = news_amplification_factor
    
    return df_sim


def quantum_momentum_with_news_amp(df: pd.DataFrame, news_amp: float = 1.0) -> Dict[str, Any]:
    """Testa Quantum Momentum com diferentes níveis de news amplification."""
    
    # Simula efeito do news amplification
    df_sim = simulate_news_amplification_effect(df, news_amp)
    
    signals = []
    
    for i in range(50, len(df_sim)):
        # Filtros otimizados
        vol_filter = df_sim['volatility'].iloc[i] < df_sim['volatility'].iloc[i-20:i].quantile(0.7)
        trend_filter = abs(df_sim['sma_20'].iloc[i] - df_sim['sma_50'].iloc[i]) / df_sim['sma_50'].iloc[i] > 0.02
        rsi_filter = 32 < df_sim['rsi'].iloc[i] < 68
        
        if not (vol_filter and trend_filter and rsi_filter):
            signals.append(0)
            continue
        
        # Sinais base
        price_momentum = (df_sim['close'].iloc[i] / df_sim['sma_20'].iloc[i] - 1)
        vol_momentum = (df_sim['volume'].iloc[i] / df_sim['volume'].iloc[i-20:i].mean() - 1)
        rsi_momentum = (df_sim['rsi'].iloc[i] - df_sim['rsi'].iloc[i-5:i].mean()) / 50
        long_momentum = (df_sim['sma_20'].iloc[i] - df_sim['sma_50'].iloc[i]) / df_sim['sma_50'].iloc[i]
        
        base_signal = (
            price_momentum * 0.4 +
            vol_momentum * 0.2 +
            rsi_momentum * 0.2 +
            long_momentum * 0.2
        )
        
        # 🔍 APLICA NEWS AMPLIFICATION
        news_factor = df_sim['news_amp_factor'].iloc[i]
        amplified_signal = base_signal * news_factor
        
        # Threshold otimizado
        if abs(amplified_signal) > 0.027:
            signals.append(np.clip(amplified_signal * 6, -1, 1))
        else:
            signals.append(0)
    
    # Calcula performance com gestão de risco otimizada
    returns = []
    trades = 0
    winning_trades = 0
    total_wins = 0
    total_losses = 0
    news_influenced_trades = 0
    
    for i in range(1, len(signals)):
        if abs(signals[i-1]) > 0.1:
            price_return = (df_sim['close'].iloc[i] - df_sim['close'].iloc[i-1]) / df_sim['close'].iloc[i-1]
            raw_return = signals[i-1] * price_return
            
            # Gestão de risco otimizada
            if raw_return < -0.0048:
                final_return = -0.0048
            elif raw_return > 0.0095:
                final_return = 0.0095
            else:
                final_return = raw_return
            
            returns.append(final_return)
            trades += 1
            
            # Conta trades influenciados por notícias
            if df_sim['news_events'].iloc[i+49]:  # +49 porque começamos em 50
                news_influenced_trades += 1
            
            if final_return > 0:
                winning_trades += 1
                total_wins += final_return
            else:
                total_losses += abs(final_return)
    
    if not returns:
        return {'error': 'Nenhum trade executado'}
    
    returns_series = pd.Series(returns)
    
    # Métricas
    total_return = returns_series.sum()
    volatility = returns_series.std() * np.sqrt(252)
    sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
    
    cumulative = (1 + returns_series).cumprod()
    rolling_max = cumulative.expanding().max()
    drawdowns = (cumulative - rolling_max) / rolling_max
    max_drawdown = abs(drawdowns.min())
    
    win_rate = winning_trades / trades if trades > 0 else 0
    profit_factor = total_wins / total_losses if total_losses > 0 else 0
    
    return {
        'news_amplification': news_amp,
        'total_return_pct': total_return * 100,
        'sharpe_ratio': sharpe_ratio,
        'win_rate': win_rate * 100,
        'max_drawdown_pct': max_drawdown * 100,
        'total_trades': trades,
        'profit_factor': profit_factor,
        'news_influenced_trades': news_influenced_trades,
        'news_influence_pct': news_influenced_trades / trades * 100 if trades > 0 else 0
    }


def test_news_amplification_impact():
    """Testa diferentes níveis de news amplification."""
    
    print("🔍 TESTE DE IMPACTO DO NEWS AMPLIFICATION")
    print("=" * 60)
    print("📊 Analisando como news_amplification afeta Quantum Momentum")
    print("=" * 60)
    
    symbols = ["BTCUSDT", "ETHUSDT"]
    
    # Diferentes níveis de news amplification para testar
    news_amp_levels = [
        1.0,   # Sem amplificação
        4.0,   # Configuração atual (moderate)
        11.3,  # Configuração pilot (alta)
        2.0,   # Conservador
        7.0    # Agressivo
    ]
    
    for symbol in symbols:
        print(f"\n📈 Testando {symbol}...")
        
        df = fetch_current_data(symbol, days=30)
        if df.empty:
            print(f"❌ Dados insuficientes para {symbol}")
            continue
        
        results = []
        
        for news_amp in news_amp_levels:
            result = quantum_momentum_with_news_amp(df, news_amp)
            if 'error' not in result:
                results.append(result)
        
        if not results:
            print(f"❌ Nenhum resultado válido para {symbol}")
            continue
        
        # Análise dos resultados
        print(f"\n📊 RESULTADOS PARA {symbol}:")
        print(f"{'News Amp':<10} {'Return':<8} {'Sharpe':<8} {'Win%':<6} {'Trades':<7} {'News%':<6}")
        print("-" * 50)
        
        best_result = None
        best_score = -999
        
        for result in results:
            # Score baseado em Sharpe e Return
            score = result['sharpe_ratio'] * 0.6 + result['total_return_pct'] * 0.4
            
            print(f"{result['news_amplification']:<10.1f} "
                  f"{result['total_return_pct']:>6.2f}% "
                  f"{result['sharpe_ratio']:>6.3f} "
                  f"{result['win_rate']:>5.1f}% "
                  f"{result['total_trades']:>6} "
                  f"{result['news_influence_pct']:>5.1f}%")
            
            if score > best_score:
                best_score = score
                best_result = result
        
        if best_result:
            print(f"\n🏆 MELHOR CONFIGURAÇÃO PARA {symbol}:")
            print(f"   News Amplification: {best_result['news_amplification']}")
            print(f"   Return: {best_result['total_return_pct']:.2f}%")
            print(f"   Sharpe: {best_result['sharpe_ratio']:.3f}")
            print(f"   Win Rate: {best_result['win_rate']:.1f}%")
            print(f"   Trades influenciados por notícias: {best_result['news_influence_pct']:.1f}%")
            
            # Comparação com configuração atual (4.0)
            current_config = next((r for r in results if r['news_amplification'] == 4.0), None)
            if current_config and best_result['news_amplification'] != 4.0:
                return_diff = best_result['total_return_pct'] - current_config['total_return_pct']
                sharpe_diff = best_result['sharpe_ratio'] - current_config['sharpe_ratio']
                
                print(f"\n📈 MELHORIA vs CONFIGURAÇÃO ATUAL (4.0):")
                print(f"   Return: {return_diff:+.2f}% pontos")
                print(f"   Sharpe: {sharpe_diff:+.3f} pontos")
    
    print(f"\n💡 RECOMENDAÇÕES:")
    print(f"   1. Teste news_amplification = 1.0 (sem amplificação)")
    print(f"   2. Se performance melhorar, considere reduzir news_amp")
    print(f"   3. Monitor impacto de notícias em tempo real")
    print(f"   4. Considere filtros adaptativos baseados em volatilidade")
    
    print(f"\n🔧 CONFIGURAÇÃO SUGERIDA:")
    print(f"   # Em config/pilot_config.yaml ou hyperparams.yaml")
    print(f"   trading:")
    print(f"     amplification:")
    print(f"       news_amplification: 1.0  # Teste sem amplificação")
    print(f"       price_amplification: 1.0  # Manter conservador")


if __name__ == "__main__":
    test_news_amplification_impact()
