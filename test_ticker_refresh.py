#!/usr/bin/env python3
"""
Teste para validar o sistema de refresh proativo do ticker cache.

Este script testa:
1. Se o refresh proativo é acionado quando o ticker está próximo de expirar
2. Se múltiplas chamadas não criam tasks duplicadas
3. Se o sistema funciona corretamente com diferentes configurações de TTL
"""

import asyncio
import time
from unittest.mock import AsyncMock, MagicMock


def test_ticker_refresh_threshold():
    """Testa se o threshold de refresh está configurado corretamente."""
    print("=== Teste 1: Configuração do Ticker Refresh Threshold ===")
    
    # Simular configuração padrão
    ticker_cache_ttl = 10.0  # 10 segundos
    ticker_refresh_threshold = 0.7  # 70%
    
    refresh_trigger_age = ticker_cache_ttl * ticker_refresh_threshold
    print(f"TTL do ticker cache: {ticker_cache_ttl}s")
    print(f"Threshold de refresh: {ticker_refresh_threshold} ({ticker_refresh_threshold*100}%)")
    print(f"Refresh será acionado quando idade >= {refresh_trigger_age}s")
    
    # Teste com diferentes idades
    test_ages = [5.0, 7.0, 7.5, 8.0, 10.0, 12.0]
    
    for age in test_ages:
        should_refresh = age >= refresh_trigger_age
        should_expire = age >= ticker_cache_ttl
        
        status = "EXPIRADO" if should_expire else ("REFRESH" if should_refresh else "VÁLIDO")
        print(f"  Idade {age}s: {status}")
    
    print("✓ Teste de threshold concluído\n")


def test_ticker_cache_logic():
    """Testa a lógica do cache de ticker com refresh proativo."""
    print("=== Teste 2: Lógica do Cache de Ticker ===")
    
    # Simular dados de cache
    ticker_cache_ttl = 10.0
    ticker_refresh_threshold = 0.7
    current_time = time.time()
    
    # Cenários de teste
    scenarios = [
        {"name": "Cache novo (2s)", "age": 2.0, "expected": "VÁLIDO"},
        {"name": "Cache próximo do refresh (7.5s)", "age": 7.5, "expected": "REFRESH"},
        {"name": "Cache expirado (12s)", "age": 12.0, "expected": "EXPIRADO"},
        {"name": "Cache muito defasado (68.6s)", "age": 68.6, "expected": "WARNING"},
    ]
    
    for scenario in scenarios:
        age = scenario["age"]
        cache_timestamp = current_time - age
        
        # Simular lógica do _get_cached_ticker
        should_warn = age > ticker_cache_ttl * 3
        should_refresh = (ticker_cache_ttl <= 0 or age < ticker_cache_ttl) and age >= ticker_cache_ttl * ticker_refresh_threshold
        is_valid = ticker_cache_ttl <= 0 or age < ticker_cache_ttl
        
        status = []
        if should_warn:
            status.append("WARNING")
        if is_valid:
            if should_refresh:
                status.append("REFRESH")
            status.append("VÁLIDO")
        else:
            status.append("EXPIRADO")
        
        result = " + ".join(status)
        print(f"  {scenario['name']}: {result}")
        
        if "WARNING" in result and age == 68.6:
            print(f"    ⚠️  Este é o caso do log atual: ticker defasado há {age}s")
    
    print("✓ Teste de lógica de cache concluído\n")


def test_refresh_task_management():
    """Testa o gerenciamento de tasks de refresh."""
    print("=== Teste 3: Gerenciamento de Tasks de Refresh ===")
    
    # Simular dicionário de tasks
    ticker_refresh_tasks = {}
    
    def simulate_task_check(symbol: str, task_done: bool = False):
        """Simula verificação de task existente."""
        task = ticker_refresh_tasks.get(symbol)
        
        if task is None:
            print(f"  {symbol}: Nenhuma task existente - CRIAR NOVA")
            # Simular criação de nova task
            ticker_refresh_tasks[symbol] = MagicMock()
            ticker_refresh_tasks[symbol].done.return_value = task_done
            return True
        elif task.done():
            print(f"  {symbol}: Task existente finalizada - CRIAR NOVA")
            # Simular criação de nova task
            ticker_refresh_tasks[symbol] = MagicMock()
            ticker_refresh_tasks[symbol].done.return_value = False
            return True
        else:
            print(f"  {symbol}: Task existente em execução - PULAR")
            return False
    
    # Teste com diferentes cenários
    print("Primeira chamada para BTC/USDT:")
    simulate_task_check("BTC/USDT")
    
    print("\nSegunda chamada para BTC/USDT (task em execução):")
    simulate_task_check("BTC/USDT")
    
    print("\nTerceira chamada para BTC/USDT (task finalizada):")
    ticker_refresh_tasks["BTC/USDT"].done.return_value = True
    simulate_task_check("BTC/USDT")
    
    print("\nPrimeira chamada para ETH/USDT:")
    simulate_task_check("ETH/USDT")
    
    print(f"\nTasks ativas: {list(ticker_refresh_tasks.keys())}")
    print("✓ Teste de gerenciamento de tasks concluído\n")


def test_configuration_impact():
    """Testa o impacto de diferentes configurações."""
    print("=== Teste 4: Impacto das Configurações ===")
    
    configurations = [
        {"ttl": 5.0, "threshold": 0.7, "name": "Configuração rápida"},
        {"ttl": 10.0, "threshold": 0.7, "name": "Configuração padrão"},
        {"ttl": 30.0, "threshold": 0.8, "name": "Configuração conservadora"},
    ]
    
    for config in configurations:
        ttl = config["ttl"]
        threshold = config["threshold"]
        refresh_age = ttl * threshold
        warning_age = ttl * 3
        
        print(f"\n{config['name']} (TTL: {ttl}s, Threshold: {threshold}):")
        print(f"  Refresh proativo em: {refresh_age}s")
        print(f"  Expiração em: {ttl}s")
        print(f"  Warning em: {warning_age}s")
        
        # Verificar se 68.6s geraria warning
        if 68.6 > warning_age:
            print(f"  ⚠️  68.6s geraria WARNING (defasado há {68.6 - warning_age:.1f}s além do limite)")
        else:
            print(f"  ✓ 68.6s não geraria warning")
    
    print("\n✓ Teste de configurações concluído\n")


if __name__ == "__main__":
    print("🧪 Testando sistema de refresh proativo do ticker cache\n")
    
    test_ticker_refresh_threshold()
    test_ticker_cache_logic()
    test_refresh_task_management()
    test_configuration_impact()
    
    print("🎉 Todos os testes concluídos!")
    print("\n📋 Resumo da solução implementada:")
    print("   • Refresh proativo acionado em 70% do TTL (7s para TTL de 10s)")
    print("   • Tasks assíncronas evitam bloqueio das operações")
    print("   • Gerenciamento de tasks previne chamadas duplicadas")
    print("   • Configurável via TICKER_CACHE_REFRESH_THRESHOLD")
    print("   • Mantém compatibilidade com o sistema atual")