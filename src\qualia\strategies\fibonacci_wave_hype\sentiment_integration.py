"""
Integração de Sentiment Holográfico para FWH Strategy

Conecta com HolographicFarsightEngine para amplificar sinais
baseados em análise de sentiment do universo holográfico.
"""

from __future__ import annotations

import asyncio
import numpy as np
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

from ...utils.logger import get_logger
from ...farsight.holographic_extension import HolographicFarsightEngine
from ...custom_types import CollectiveMindState

logger = get_logger(__name__)

class HolographicSentimentAnalyzer:
    """Analisador de sentiment holográfico para FWH."""
    
    def __init__(self):
        self.sentiment_cache: Dict[str, Dict] = {}
        self.cache_ttl = 300  # 5 minutos
        
    def get_cached_sentiment(self, symbol: str) -> Optional[Dict]:
        """Recupera sentiment do cache se ainda válido."""
        if symbol in self.sentiment_cache:
            cache_entry = self.sentiment_cache[symbol]
            if datetime.now().timestamp() - cache_entry["timestamp"] < self.cache_ttl:
                return cache_entry["data"]
        return None
    
    def cache_sentiment(self, symbol: str, sentiment_data: Dict) -> None:
        """Armazena sentiment no cache."""
        self.sentiment_cache[symbol] = {
            "data": sentiment_data,
            "timestamp": datetime.now().timestamp()
        }

# Instância global do analisador
_sentiment_analyzer = HolographicSentimentAnalyzer()

def integrate_holographic_sentiment(
    holographic_engine: HolographicFarsightEngine,
    symbol: str,
    use_cache: bool = True
) -> float:
    """
    Integra sentiment holográfico para amplificar sinais FWH.
    
    Args:
        holographic_engine: Engine holográfico do QUALIA
        symbol: Símbolo do ativo (ex: "BTC/USDT")
        use_cache: Se deve usar cache de sentiment
        
    Returns:
        Fator de amplificação (0.5 - 2.0)
    """
    try:
        # Verifica cache primeiro
        if use_cache:
            cached = _sentiment_analyzer.get_cached_sentiment(symbol)
            if cached:
                logger.debug(f"[FWH-Sentiment] Usando cache para {symbol}")
                return cached["boost_factor"]
        
        # Gera estado mental coletivo
        collective_state = holographic_engine.generate_collective_mind_state()
        
        # Analisa sentiment específico do símbolo
        symbol_sentiment = _analyze_symbol_sentiment(collective_state, symbol)
        
        # Calcula fator de amplificação
        boost_factor = _calculate_boost_factor(symbol_sentiment, collective_state)
        
        # Armazena no cache
        if use_cache:
            sentiment_data = {
                "symbol_sentiment": symbol_sentiment,
                "boost_factor": boost_factor,
                "collective_state": _serialize_collective_state(collective_state)
            }
            _sentiment_analyzer.cache_sentiment(symbol, sentiment_data)
        
        logger.info(f"[FWH-Sentiment] {symbol}: boost={boost_factor:.3f}")
        return boost_factor
        
    except Exception as e:
        logger.error(f"[FWH-Sentiment] Erro ao integrar sentiment para {symbol}: {e}")
        return 1.0  # Neutro em caso de erro

def _analyze_symbol_sentiment(
    collective_state: CollectiveMindState,
    symbol: str
) -> Dict[str, float]:
    """Analisa sentiment específico do símbolo com métricas quânticas."""

    # Extrai base do símbolo (BTC de BTC/USDT)
    base_symbol = symbol.split('/')[0].upper()

    sentiment_score = 0.0
    confidence = 0.0
    quantum_coherence = 0.0

    # Analisa clusters relevantes com pesos quânticos
    for cluster_name, cluster_data in collective_state.persona_impact.items():
        cluster_sentiment = cluster_data.get("sentiment", 0.0)
        cluster_confidence = cluster_data.get("confidence_boost", 0.0)

        # Peso baseado na relevância do cluster para o símbolo
        relevance_weight = _calculate_cluster_relevance(cluster_name, base_symbol)

        if relevance_weight > 0:
            # Aplica transformação quântica ao sentiment
            quantum_sentiment = _apply_quantum_transformation(
                cluster_sentiment, cluster_confidence, relevance_weight
            )

            sentiment_score += quantum_sentiment * relevance_weight * cluster_confidence
            confidence += cluster_confidence * relevance_weight
            quantum_coherence += abs(quantum_sentiment) * relevance_weight

    # Normaliza com correção quântica
    if confidence > 0:
        sentiment_score /= confidence
        quantum_coherence /= confidence

    # Aplica fator de coerência holográfica
    holographic_factor = _calculate_holographic_coherence(
        collective_state.dominant_narrative, base_symbol
    )

    return {
        "sentiment": sentiment_score * holographic_factor,
        "confidence": min(confidence, 1.0),
        "quantum_coherence": quantum_coherence,
        "holographic_factor": holographic_factor,
        "symbol": base_symbol
    }

def _calculate_cluster_relevance(cluster_name: str, symbol: str) -> float:
    """Calcula relevância do cluster para o símbolo."""
    
    # Mapeamento de clusters para símbolos
    cluster_symbol_map = {
        "RetailCluster": ["BTC", "ETH", "DOGE", "SHIB"],  # Retail favorites
        "InstitutionalCluster": ["BTC", "ETH"],  # Institutional focus
        "MomentumQuant": ["BTC", "ETH", "BNB", "SOL"],  # High volume
        "DeFiCluster": ["ETH", "UNI", "AAVE", "COMP"],  # DeFi tokens
        "AICluster": ["FET", "AGIX", "OCEAN"],  # AI tokens
    }
    
    # Relevância direta
    if cluster_name in cluster_symbol_map:
        if symbol in cluster_symbol_map[cluster_name]:
            return 1.0
    
    # Relevância por categoria
    if "BTC" in symbol and cluster_name in ["RetailCluster", "InstitutionalCluster"]:
        return 0.8
    elif "ETH" in symbol and cluster_name in ["DeFiCluster", "InstitutionalCluster"]:
        return 0.8
    elif cluster_name == "MomentumQuant":
        return 0.6  # Momentum é relevante para todos
    
    return 0.2  # Relevância mínima

def _calculate_boost_factor(
    symbol_sentiment: Dict[str, float],
    collective_state: CollectiveMindState
) -> float:
    """Calcula fator de amplificação baseado no sentiment quântico-holográfico."""

    sentiment = symbol_sentiment["sentiment"]
    confidence = symbol_sentiment["confidence"]
    quantum_coherence = symbol_sentiment.get("quantum_coherence", 0.0)
    holographic_factor = symbol_sentiment.get("holographic_factor", 1.0)

    # Sentiment global do mercado com correção quântica
    global_sentiment = getattr(collective_state, 'global_sentiment', 0.0)
    market_fear_greed = getattr(collective_state, 'market_fear_greed_index', 50.0)

    # Fator base do sentiment com amplificação quântica
    if sentiment > 0.3:  # Bullish
        base_factor = 1.2 + (sentiment - 0.3) * quantum_coherence * 1.5  # Amplificado
    elif sentiment < -0.3:  # Bearish
        base_factor = 0.8 + (sentiment + 0.3) * quantum_coherence * 1.2  # Suavizado
    else:  # Neutro
        base_factor = 1.0

    # Ajuste por confiança com coerência quântica
    confidence_adjustment = 0.8 + (confidence * quantum_coherence * 0.6)

    # Ajuste por sentiment global holográfico
    global_adjustment = 0.9 + (global_sentiment * holographic_factor * 0.3)

    # Ajuste por fear/greed com modulação temporal
    if market_fear_greed:
        fg_adjustment = 0.8 + (market_fear_greed / 100.0 * holographic_factor * 0.5)
    else:
        fg_adjustment = 1.0
    
    # Combina fatores
    boost_factor = base_factor * confidence_adjustment * global_adjustment * fg_adjustment
    
    # Limita entre 0.5 e 2.0
    return max(0.5, min(2.0, boost_factor))

def _serialize_collective_state(collective_state: CollectiveMindState) -> Dict:
    """Serializa estado coletivo para cache."""
    return {
        "global_sentiment": getattr(collective_state, 'global_sentiment', 0.0),
        "market_fear_greed_index": getattr(collective_state, 'market_fear_greed_index', 50.0),
        "dominant_narrative": collective_state.dominant_narrative,
        "cluster_count": len(getattr(collective_state, 'persona_impact', {})),
        "timestamp": datetime.now().isoformat()
    }

async def get_holographic_market_context(
    holographic_engine: HolographicFarsightEngine
) -> Dict[str, Any]:
    """
    Obtém contexto holográfico completo do mercado.
    Função assíncrona para análises mais profundas.
    """
    try:
        # Executa análise holográfica completa
        insights = holographic_engine.run_holographic_analysis()
        
        # Extrai padrões emergentes
        emerging_patterns = []
        for insight in insights:
            if "holographic_analysis" in insight:
                holo_data = insight["holographic_analysis"]
                if holo_data.get("confidence", 0) > 0.7:
                    emerging_patterns.append({
                        "pattern_type": holo_data.get("pattern_type"),
                        "confidence": holo_data.get("confidence"),
                        "position": holo_data.get("position"),
                        "topic": insight.get("topic", "unknown")
                    })
        
        return {
            "total_insights": len(insights),
            "emerging_patterns": emerging_patterns,
            "high_confidence_patterns": len(emerging_patterns),
            "analysis_timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"[FWH-Sentiment] Erro ao obter contexto holográfico: {e}")
        return {}

def _apply_quantum_transformation(
    sentiment: float,
    confidence: float,
    relevance: float
) -> float:
    """Aplica transformação quântica ao sentiment."""
    # Superposição quântica do sentiment
    quantum_state = sentiment * np.sqrt(confidence * relevance)

    # Interferência construtiva/destrutiva
    interference = np.sin(sentiment * np.pi) * confidence

    return quantum_state + interference * 0.3

def _calculate_holographic_coherence(
    dominant_narrative: str,
    symbol: str
) -> float:
    """Calcula coerência holográfica entre narrativa e símbolo."""

    # Mapeamento narrativa-símbolo
    narrative_map = {
        "AI": ["BTC", "ETH", "FET", "AGIX"],
        "DEFI": ["ETH", "UNI", "AAVE", "COMP"],
        "INSTITUTIONAL": ["BTC", "ETH"],
        "RETAIL": ["DOGE", "SHIB", "BTC"],
        "QUANTUM": ["BTC", "ETH", "ATOM"],
    }

    # Coerência base
    coherence = 1.0

    for narrative, symbols in narrative_map.items():
        if narrative.upper() in dominant_narrative.upper():
            if symbol in symbols:
                coherence *= 1.3  # Amplifica coerência
            else:
                coherence *= 0.9  # Reduz levemente

    return min(coherence, 1.5)

def _calculate_temporal_decay(timestamp: float) -> float:
    """Calcula fator de decaimento temporal."""
    import time

    current_time = time.time()
    age_seconds = current_time - timestamp
    age_minutes = age_seconds / 60.0

    # Decaimento exponencial suave (meia-vida de 30 minutos)
    decay_factor = np.exp(-age_minutes / 30.0)

    return max(0.5, decay_factor)  # Mínimo de 50%