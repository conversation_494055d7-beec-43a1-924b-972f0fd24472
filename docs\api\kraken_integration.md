# KrakenIntegration

Implementação original do `CryptoDataFetcher` voltada à exchange Kraken. O
módulo serve como base para integrações com outras exchanges e suporta
operações via REST e WebSocket.

Principais funcionalidades:

- Carregamento de mercados e normalização de símbolos.
- Consulta de saldo, ordens e histórico através da API `ccxt`.
- Envio e cancelamento de ordens *market* e *limit*.
- Recuperação de preços em tempo real com `watch_ticker` quando disponível,
  fazendo fallback para `fetch_ticker` em caso de falhas.
- Descoberta do primeiro candle disponível com `get_earliest_timestamp` e
  cálculo da profundidade histórica via `max_history_candles`.

Desde a versão atual, `get_earliest_timestamp` também verifica arquivos no
diretório `CACHE_DIR` quando a API da exchange não retorna dados. Ao
encontrar caches válidos com prefixo `<symbol>_<timeframe>`, o método utiliza o
timestamp mais antigo disponível, contabilizando o número total de candles
detectados. Apenas na ausência de caches ou quando corrompidos o timestamp
atual é retornado. O valor obtido é então empregado por `max_history_candles`
para estimar quantos candles podem ser recuperados.

## Configuração

Informe as credenciais obrigatórias via variáveis de ambiente:

- `KRAKEN_API_KEY`
- `KRAKEN_SECRET_KEY` ou `KRAKEN_API_SECRET`

Parâmetros opcionais como `EXCHANGE_CONN_TIMEOUT`, `EXCHANGE_CONN_RETRIES` e
`TICKER_TIMEOUT` controlam limites de requisições e tempo de conexão, conforme
mostrado no README do projeto. Defina `QUALIA_MARKET_METRICS_ENABLED=true` para
emitir métricas de latência e erros via DogStatsd.

## Uso básico

```python
from src.qualia.market.kraken_integration import KrakenIntegration

kraken = KrakenIntegration(use_websocket=True)
await kraken.initialize_connection()
ticker = await kraken.fetch_ticker("ETH/USD")
```

A classe herda toda a lógica de `CryptoDataFetcher`. Chame
`initialize_connection` antes de enviar ordens ou obter cotações.
