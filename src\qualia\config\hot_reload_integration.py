"""
QUALIA Hot-reload Integration
============================

D-05: Integração do sistema de hot-reload com componentes QUALIA.
Permite atualizações dinâmicas de configurações sem reinicialização.

YAA IMPLEMENTATION: Integração com BayesianOptimizer, sistema de trading,
live feed e outros componentes críticos.
"""

from __future__ import annotations

import asyncio
import logging
from typing import Dict, Any, Optional, TYPE_CHECKING
from pathlib import Path

from ..utils.logger import get_logger
from .hot_reload import ConfigurationHotReloader, get_global_hot_reloader

if TYPE_CHECKING:
    from ..optimization.bayesian_optimizer import BayesianOptimizer
    from ..consciousness.live_feed_integration import LiveFeedIntegration

logger = get_logger(__name__)

class BayesianOptimizerHotReload:
    """Integração de hot-reload para BayesianOptimizer."""
    
    def __init__(self, optimizer: 'BayesianOptimizer'):
        self.optimizer = optimizer
        self.hot_reloader = get_global_hot_reloader()
        self.config_path = "config/bayesian_optimization.yaml"
        
        # Registrar callback
        self.hot_reloader.register_callback(
            self.config_path,
            self._on_config_reload
        )
        
        logger.info("🔄 Hot-reload integrado com BayesianOptimizer")
        
    async def _on_config_reload(self, config_path: str, config_data: Dict[str, Any]):
        """Callback chamado quando configuração do Bayesian é recarregada."""
        try:
            logger.info("🔄 Recarregando configuração do BayesianOptimizer...")
            
            # Atualizar configuração do otimizador
            await self._update_optimizer_config(config_data)
            
            # Atualizar configuração de pruning
            await self._update_pruning_config(config_data)
            
            # Atualizar configuração multi-fidelity
            await self._update_multi_fidelity_config(config_data)
            
            logger.info("✅ BayesianOptimizer reconfigurado com sucesso")
            
        except Exception as e:
            logger.error(f"❌ Erro ao recarregar configuração do BayesianOptimizer: {e}")
            raise
            
    async def _update_optimizer_config(self, config_data: Dict[str, Any]):
        """Atualiza configuração principal do otimizador."""
        opt_config = config_data.get('optimization', {})
        
        # Atualizar parâmetros básicos
        if 'n_trials_per_cycle' in opt_config:
            self.optimizer.config.n_trials_per_cycle = opt_config['n_trials_per_cycle']
            
        if 'optimization_interval_cycles' in opt_config:
            self.optimizer.config.optimization_interval_cycles = opt_config['optimization_interval_cycles']
            
        if 'lookback_hours' in opt_config:
            self.optimizer.config.lookback_hours = opt_config['lookback_hours']
            
        if 'objective_metric' in opt_config:
            self.optimizer.config.objective_metric = opt_config['objective_metric']
            
        # Atualizar ranges de parâmetros
        if 'parameter_ranges' in opt_config:
            ranges = opt_config['parameter_ranges']
            
            if 'price_amplification' in ranges:
                price_range = ranges['price_amplification']
                self.optimizer.config.price_amp_range = (
                    price_range.get('min', 1.0),
                    price_range.get('max', 10.0)
                )
                
            if 'news_amplification' in ranges:
                news_range = ranges['news_amplification']
                self.optimizer.config.news_amp_range = (
                    news_range.get('min', 1.0),
                    news_range.get('max', 15.0)
                )
                
            if 'min_confidence' in ranges:
                conf_range = ranges['min_confidence']
                self.optimizer.config.min_conf_range = (
                    conf_range.get('min', 0.2),
                    conf_range.get('max', 0.8)
                )
                
        # Atualizar pesos multi-objetivo
        if 'weights' in opt_config:
            weights = opt_config['weights']
            if 'sharpe_weight' in weights:
                self.optimizer.config.sharpe_weight = weights['sharpe_weight']
            if 'pnl_weight' in weights:
                self.optimizer.config.pnl_weight = weights['pnl_weight']
                
        logger.debug("📊 Configuração principal do otimizador atualizada")
        
    async def _update_pruning_config(self, config_data: Dict[str, Any]):
        """Atualiza configuração de pruning."""
        pruning_config = config_data.get('pruning', {})
        
        if self.optimizer.advanced_pruner and pruning_config:
            # Atualizar estratégia de pruning
            if 'strategy' in pruning_config:
                from ..optimization.advanced_pruning import PruningStrategy
                strategy_name = pruning_config['strategy'].upper()
                try:
                    new_strategy = PruningStrategy[strategy_name]
                    self.optimizer.advanced_pruner.config.strategy = new_strategy
                    logger.debug(f"🔧 Estratégia de pruning atualizada: {new_strategy}")
                except KeyError:
                    logger.warning(f"⚠️ Estratégia de pruning inválida: {strategy_name}")
                    
            # Atualizar thresholds
            if 'thresholds' in pruning_config:
                thresholds = pruning_config['thresholds']
                for key, value in thresholds.items():
                    if hasattr(self.optimizer.advanced_pruner.config, key):
                        setattr(self.optimizer.advanced_pruner.config, key, value)
                        
        logger.debug("✂️ Configuração de pruning atualizada")
        
    async def _update_multi_fidelity_config(self, config_data: Dict[str, Any]):
        """Atualiza configuração multi-fidelity."""
        mf_config = config_data.get('multi_fidelity', {})
        
        if self.optimizer.multi_fidelity_evaluator and mf_config:
            # Atualizar níveis de fidelidade
            if 'fidelity_levels' in mf_config:
                levels = mf_config['fidelity_levels']
                self.optimizer.config.multi_fidelity_levels = levels
                
                # Recriar evaluator com novos níveis
                from ..optimization.multi_fidelity import MultiFidelityEvaluator, FidelityConfig, FidelityLevel
                new_config = FidelityConfig(
                    fidelity_levels=[FidelityLevel(h) for h in levels]
                )
                self.optimizer.multi_fidelity_evaluator = MultiFidelityEvaluator(new_config)
                
            # Atualizar critérios de promoção
            if 'promotion_criteria' in mf_config:
                criteria = mf_config['promotion_criteria']
                if self.optimizer.multi_fidelity_evaluator:
                    for key, value in criteria.items():
                        if hasattr(self.optimizer.multi_fidelity_evaluator.config, key):
                            setattr(self.optimizer.multi_fidelity_evaluator.config, key, value)
                            
        logger.debug("🎯 Configuração multi-fidelity atualizada")

class TradingSystemHotReload:
    """Integração de hot-reload para sistema de trading."""
    
    def __init__(self, trading_system):
        self.trading_system = trading_system
        self.hot_reloader = get_global_hot_reloader()
        self.config_paths = [
            "config/holographic_trading_config.yaml",
            "config/strategy_parameters.yaml"
        ]
        
        # Registrar callbacks
        for config_path in self.config_paths:
            self.hot_reloader.register_callback(
                config_path,
                self._on_config_reload
            )
            
        logger.info("🔄 Hot-reload integrado com TradingSystem")
        
    async def _on_config_reload(self, config_path: str, config_data: Dict[str, Any]):
        """Callback chamado quando configuração de trading é recarregada."""
        try:
            logger.info(f"🔄 Recarregando configuração de trading: {config_path}")
            
            if "holographic_trading" in config_path:
                await self._update_trading_config(config_data)
            elif "strategy_parameters" in config_path:
                await self._update_strategy_config(config_data)
                
            logger.info("✅ Sistema de trading reconfigurado com sucesso")
            
        except Exception as e:
            logger.error(f"❌ Erro ao recarregar configuração de trading: {e}")
            raise
            
    async def _update_trading_config(self, config_data: Dict[str, Any]):
        """Atualiza configuração de trading holográfico."""
        # Atualizar parâmetros de amplificação
        if 'amplification' in config_data:
            amp_config = config_data['amplification']
            
            if hasattr(self.trading_system, 'amplification_calibrator'):
                calibrator = self.trading_system.amplification_calibrator
                
                # Atualizar valores iniciais
                if 'initial' in amp_config:
                    initial = amp_config['initial']
                    for param, value in initial.items():
                        if hasattr(calibrator, param):
                            setattr(calibrator, param, value)
                            
                # Atualizar limites
                if 'limits' in amp_config:
                    limits = amp_config['limits']
                    for param, value in limits.items():
                        if hasattr(calibrator, param):
                            setattr(calibrator, param, value)
                            
        # Atualizar configuração de confiança
        if 'confidence' in config_data:
            conf_config = config_data['confidence']
            if hasattr(self.trading_system, 'min_confidence'):
                self.trading_system.min_confidence = conf_config.get('min_confidence', 0.3)
                
        logger.debug("📈 Configuração de trading atualizada")
        
    async def _update_strategy_config(self, config_data: Dict[str, Any]):
        """Atualiza configuração de estratégia."""
        # Atualizar parâmetros de estratégia
        if hasattr(self.trading_system, 'strategy_config'):
            self.trading_system.strategy_config.update(config_data)
            
        logger.debug("🎯 Configuração de estratégia atualizada")

class LiveFeedHotReload:
    """Integração de hot-reload para live feed."""
    
    def __init__(self, live_feed: 'LiveFeedIntegration'):
        self.live_feed = live_feed
        self.hot_reloader = get_global_hot_reloader()
        self.config_path = "config/holographic_trading_config.yaml"
        
        # Registrar callback
        self.hot_reloader.register_callback(
            self.config_path,
            self._on_config_reload
        )
        
        logger.info("🔄 Hot-reload integrado com LiveFeed")
        
    async def _on_config_reload(self, config_path: str, config_data: Dict[str, Any]):
        """Callback chamado quando configuração do live feed é recarregada."""
        try:
            logger.info("🔄 Recarregando configuração do LiveFeed...")
            
            # Atualizar configuração do live feed
            if 'live_feed' in config_data:
                feed_config = config_data['live_feed']
                
                # Atualizar frequência de atualização
                if 'update_frequency' in feed_config:
                    self.live_feed.config.update_frequency = feed_config['update_frequency']
                    
                # Atualizar modo de operação
                if 'mode' in feed_config:
                    self.live_feed.config.mode = feed_config['mode']
                    
                # Atualizar configurações de validação
                if 'enable_data_validation' in feed_config:
                    self.live_feed.config.enable_data_validation = feed_config['enable_data_validation']
                    
                if 'price_variance_threshold' in feed_config:
                    self.live_feed.config.price_variance_threshold = feed_config['price_variance_threshold']
                    
            logger.info("✅ LiveFeed reconfigurado com sucesso")
            
        except Exception as e:
            logger.error(f"❌ Erro ao recarregar configuração do LiveFeed: {e}")
            raise

def setup_hot_reload_integrations(
    bayesian_optimizer: Optional['BayesianOptimizer'] = None,
    trading_system = None,
    live_feed: Optional['LiveFeedIntegration'] = None
) -> Dict[str, Any]:
    """
    Configura integrações de hot-reload para componentes QUALIA.
    
    Args:
        bayesian_optimizer: Instância do BayesianOptimizer
        trading_system: Instância do sistema de trading
        live_feed: Instância do LiveFeedIntegration
        
    Returns:
        Dict com instâncias das integrações criadas
    """
    integrations = {}
    
    try:
        if bayesian_optimizer:
            integrations['bayesian'] = BayesianOptimizerHotReload(bayesian_optimizer)
            logger.info("✅ Integração BayesianOptimizer configurada")
            
        if trading_system:
            integrations['trading'] = TradingSystemHotReload(trading_system)
            logger.info("✅ Integração TradingSystem configurada")
            
        if live_feed:
            integrations['live_feed'] = LiveFeedHotReload(live_feed)
            logger.info("✅ Integração LiveFeed configurada")
            
        logger.info(f"🎉 {len(integrations)} integrações de hot-reload configuradas")
        return integrations
        
    except Exception as e:
        logger.error(f"❌ Erro ao configurar integrações de hot-reload: {e}")
        raise
