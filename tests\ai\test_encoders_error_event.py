import importlib
import pytest
from tests.stub_utils import install_stubs

install_stubs()

from qualia.config import encoder_registry
from qualia.memory.event_bus import SimpleEventBus


def reload_plugin(monkeypatch):
    monkeypatch.delenv("QUALIA_ENCODERS_CONFIG", raising=False)
    import src.ai.encoders_plugin as plugin

    importlib.reload(encoder_registry)
    return importlib.reload(plugin)


def test_encode_failure_event(monkeypatch):
    plugin = reload_plugin(monkeypatch)

    bus = SimpleEventBus()
    events = []
    bus.subscribe("encoder.failed", lambda p: events.append(p))

    def raise_error(self, _snapshot):
        raise ValueError("boom")

    monkeypatch.setattr(plugin.CoreRSIPhaseEncoder, "encode", raise_error)

    enc = encoder_registry.create_encoder("RSIPhaseEncoder", name="rsi", event_bus=bus)
    with pytest.raises(ValueError):
        enc.encode({"rsi": 50})

    assert events
    assert events[0]["class"] == "RSIPhaseEncoder"
    assert "boom" in events[0]["error"]
