#!/usr/bin/env python3
"""
Script de debug para identificar erro no WarmupManager
"""

import asyncio
import logging
import sys
from pathlib import Path

# Adicionar o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.utils.logger import get_logger
from qualia.exchanges.kucoin_client import KuCoinClient
from qualia.core.warmup_manager import WarmupManager

# Configurar logging detalhado
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = get_logger(__name__)


async def debug_warmup():
    """Debug do processo de warmup"""
    try:
        logger.info("🔍 Iniciando debug do warmup...")
        
        # Configuração simples
        config = {
            "api_key": "your_api_key",
            "api_secret": "your_api_secret", 
            "passphrase": "your_passphrase",
            "sandbox": True
        }
        
        # Criar cliente KuCoin
        logger.info("📡 Criando cliente KuCoin...")
        kucoin_client = KuCoinClient(config)
        await kucoin_client.initialize()
        
        # Criar WarmupManager com requisitos mínimos
        min_candles = {
            "5m": 10,  # Apenas 10 candles para teste rápido
        }
        
        logger.info("🔧 Criando WarmupManager...")
        warmup_manager = WarmupManager(
            kucoin_client=kucoin_client,
            min_candles_required=min_candles,
            cache_dir="data/debug_cache"
        )
        
        # Testar com apenas um símbolo e timeframe
        symbols = ["BTC-USDT"]
        timeframes = ["5m"]
        
        logger.info("🚀 Iniciando carregamento de dados...")
        success = await warmup_manager.load_historical_data(
            symbols=symbols,
            timeframes=timeframes,
            force_reload=True  # Forçar reload para debug
        )
        
        if success:
            logger.info("✅ Warmup concluído com sucesso!")
            
            # Verificar dados carregados
            data = warmup_manager.get_data("BTC-USDT", "5m")
            if data is not None:
                logger.info(f"📊 Dados carregados: {len(data)} candles")
                logger.info(f"📊 Primeiras linhas:\n{data.head()}")
            else:
                logger.error("❌ Nenhum dado foi carregado!")
        else:
            logger.error("❌ Falha no warmup!")
            
    except Exception as e:
        logger.error(f"💥 Erro durante debug: {e}", exc_info=True)
    finally:
        # Fechar cliente
        if 'kucoin_client' in locals():
            await kucoin_client.close()


if __name__ == "__main__":
    asyncio.run(debug_warmup()) 