#!/usr/bin/env python3
"""
QUANTUM MOMENTUM FINE-TUNED - Ajustes Mínimos e Precisos
Estratégia: Manter o que funciona, ajustar apenas o essencial
"""

import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any

import pandas as pd
import numpy as np
import requests


class QuantumMomentumFineTuned:
    """Versão fine-tuned com ajustes mínimos e precisos."""
    
    def __init__(self):
        self.session = requests.Session()
    
    def fetch_data(self, symbol: str, days: int = 90) -> pd.DataFrame:
        """Busca dados históricos."""
        try:
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': symbol.replace('/', ''),
                'interval': '1h',
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index().dropna()
            
            # Indicadores básicos (mantém os mesmos)
            df['returns'] = df['close'].pct_change()
            df['sma_20'] = df['close'].rolling(20).mean()
            df['sma_50'] = df['close'].rolling(50).mean()
            df['rsi'] = self._calculate_rsi(df['close'], 14)
            df['volatility'] = df['returns'].rolling(20).std()
            df['atr'] = self._calculate_atr(df, 14)
            
            return df
            
        except Exception as e:
            print(f"❌ Erro ao buscar dados para {symbol}: {e}")
            return pd.DataFrame()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calcula RSI."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calcula Average True Range."""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        return true_range.rolling(period).mean()
    
    def quantum_momentum_current(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Versão ATUAL (baseline)."""
        signals = []
        
        for i in range(50, len(df)):
            # Filtros atuais (MANTÉM EXATAMENTE IGUAIS)
            vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-20:i].quantile(0.7)
            trend_filter = abs(df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i] > 0.02
            rsi_filter = 35 < df['rsi'].iloc[i] < 65
            
            if not (vol_filter and trend_filter and rsi_filter):
                signals.append(0)
                continue
            
            # Sinais atuais (MANTÉM EXATAMENTE IGUAIS)
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
            long_momentum = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            
            signal = (
                price_momentum * 0.4 +
                vol_momentum * 0.2 +
                rsi_momentum * 0.2 +
                long_momentum * 0.2
            )
            
            if abs(signal) > 0.03:
                signals.append(np.clip(signal * 6, -1, 1))
            else:
                signals.append(0)
        
        return self._calculate_performance_current(df.iloc[50:], signals, "CURRENT")
    
    def quantum_momentum_fine_tuned_v1(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Versão 1: Apenas melhora take-profit e stop-loss."""
        signals = []
        
        for i in range(50, len(df)):
            # MANTÉM FILTROS EXATAMENTE IGUAIS
            vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-20:i].quantile(0.7)
            trend_filter = abs(df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i] > 0.02
            rsi_filter = 35 < df['rsi'].iloc[i] < 65
            
            if not (vol_filter and trend_filter and rsi_filter):
                signals.append(0)
                continue
            
            # MANTÉM SINAIS EXATAMENTE IGUAIS
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
            long_momentum = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            
            signal = (
                price_momentum * 0.4 +
                vol_momentum * 0.2 +
                rsi_momentum * 0.2 +
                long_momentum * 0.2
            )
            
            if abs(signal) > 0.03:
                signals.append(np.clip(signal * 6, -1, 1))
            else:
                signals.append(0)
        
        return self._calculate_performance_v1(df.iloc[50:], signals, "FINE_TUNED_V1")
    
    def quantum_momentum_fine_tuned_v2(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Versão 2: V1 + threshold ligeiramente mais baixo."""
        signals = []
        
        for i in range(50, len(df)):
            # MANTÉM FILTROS EXATAMENTE IGUAIS
            vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-20:i].quantile(0.7)
            trend_filter = abs(df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i] > 0.02
            rsi_filter = 35 < df['rsi'].iloc[i] < 65
            
            if not (vol_filter and trend_filter and rsi_filter):
                signals.append(0)
                continue
            
            # MANTÉM SINAIS EXATAMENTE IGUAIS
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
            long_momentum = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            
            signal = (
                price_momentum * 0.4 +
                vol_momentum * 0.2 +
                rsi_momentum * 0.2 +
                long_momentum * 0.2
            )
            
            # 🔧 ÚNICA MUDANÇA: Threshold ligeiramente mais baixo
            if abs(signal) > 0.027:  # 0.03 → 0.027 (10% mais baixo)
                signals.append(np.clip(signal * 6, -1, 1))
            else:
                signals.append(0)
        
        return self._calculate_performance_v1(df.iloc[50:], signals, "FINE_TUNED_V2")
    
    def quantum_momentum_fine_tuned_v3(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Versão 3: V2 + RSI filter ligeiramente mais amplo."""
        signals = []
        
        for i in range(50, len(df)):
            # Filtros com RSI ligeiramente mais amplo
            vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-20:i].quantile(0.7)
            trend_filter = abs(df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i] > 0.02
            rsi_filter = 32 < df['rsi'].iloc[i] < 68  # 35-65 → 32-68 (ligeiramente mais amplo)
            
            if not (vol_filter and trend_filter and rsi_filter):
                signals.append(0)
                continue
            
            # MANTÉM SINAIS EXATAMENTE IGUAIS
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
            long_momentum = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            
            signal = (
                price_momentum * 0.4 +
                vol_momentum * 0.2 +
                rsi_momentum * 0.2 +
                long_momentum * 0.2
            )
            
            if abs(signal) > 0.027:
                signals.append(np.clip(signal * 6, -1, 1))
            else:
                signals.append(0)
        
        return self._calculate_performance_v1(df.iloc[50:], signals, "FINE_TUNED_V3")
    
    def _calculate_performance_current(self, df: pd.DataFrame, signals: List[float], strategy_name: str) -> Dict[str, Any]:
        """Performance atual (baseline)."""
        returns = []
        trades = 0
        winning_trades = 0
        total_wins = 0
        total_losses = 0
        
        for i in range(1, len(signals)):
            if abs(signals[i-1]) > 0.1:
                price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
                raw_return = signals[i-1] * price_return
                
                # Gestão de risco atual (MANTÉM IGUAL)
                if raw_return < -0.005:
                    final_return = -0.005
                elif raw_return > 0.008:
                    final_return = 0.008
                else:
                    final_return = raw_return
                
                returns.append(final_return)
                trades += 1
                
                if final_return > 0:
                    winning_trades += 1
                    total_wins += final_return
                else:
                    total_losses += abs(final_return)
        
        if not returns:
            return {'error': 'Nenhum trade'}
        
        returns_series = pd.Series(returns)
        
        total_return = returns_series.sum()
        volatility = returns_series.std() * np.sqrt(252)
        sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
        
        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        win_rate = winning_trades / trades if trades > 0 else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        return {
            'strategy': strategy_name,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown * 100,
            'win_rate': win_rate * 100,
            'total_trades': trades,
            'volatility': volatility * 100,
            'profit_factor': profit_factor
        }
    
    def _calculate_performance_v1(self, df: pd.DataFrame, signals: List[float], strategy_name: str) -> Dict[str, Any]:
        """Performance com ajustes mínimos."""
        returns = []
        trades = 0
        winning_trades = 0
        total_wins = 0
        total_losses = 0
        
        for i in range(1, len(signals)):
            if abs(signals[i-1]) > 0.1:
                price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
                raw_return = signals[i-1] * price_return
                
                # 🔧 AJUSTES MÍNIMOS na gestão de risco
                
                # Stop-loss ligeiramente mais generoso (para melhorar win rate)
                stop_loss = -0.0048  # -0.005 → -0.0048 (4% mais generoso)
                
                # Take-profit ligeiramente mais agressivo (para melhorar returns)
                take_profit = 0.0095  # 0.008 → 0.0095 (19% mais agressivo)
                
                if raw_return < stop_loss:
                    final_return = stop_loss
                elif raw_return > take_profit:
                    final_return = take_profit
                else:
                    final_return = raw_return
                
                returns.append(final_return)
                trades += 1
                
                if final_return > 0:
                    winning_trades += 1
                    total_wins += final_return
                else:
                    total_losses += abs(final_return)
        
        if not returns:
            return {'error': 'Nenhum trade'}
        
        returns_series = pd.Series(returns)
        
        total_return = returns_series.sum()
        volatility = returns_series.std() * np.sqrt(252)
        sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
        
        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        win_rate = winning_trades / trades if trades > 0 else 0
        avg_win = total_wins / winning_trades if winning_trades > 0 else 0
        avg_loss = total_losses / (trades - winning_trades) if (trades - winning_trades) > 0 else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        return {
            'strategy': strategy_name,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown * 100,
            'win_rate': win_rate * 100,
            'total_trades': trades,
            'volatility': volatility * 100,
            'profit_factor': profit_factor,
            'avg_win_pct': avg_win * 100,
            'avg_loss_pct': avg_loss * 100,
            'win_loss_ratio': avg_win / avg_loss if avg_loss > 0 else 0
        }


def run_fine_tuning_test():
    """Testa versões fine-tuned incrementalmente."""
    print("🎯 QUANTUM MOMENTUM FINE-TUNED - Ajustes Incrementais")
    print("=" * 60)
    print("🎯 Objetivo: Win Rate >60%, Sharpe >0.5, Return >3%, Max DD <2%")
    print("=" * 60)
    
    tuner = QuantumMomentumFineTuned()
    
    symbols = ["BTCUSDT", "ETHUSDT"]
    
    for symbol in symbols:
        print(f"\n📈 Testando {symbol}...")
        
        df = tuner.fetch_data(symbol, days=90)
        if df.empty or len(df) < 100:
            print(f"❌ Dados insuficientes para {symbol}")
            continue
        
        # Testa todas as versões
        current = tuner.quantum_momentum_current(df)
        v1 = tuner.quantum_momentum_fine_tuned_v1(df)
        v2 = tuner.quantum_momentum_fine_tuned_v2(df)
        v3 = tuner.quantum_momentum_fine_tuned_v3(df)
        
        versions = [
            ("ATUAL", current),
            ("V1 (TP/SL)", v1),
            ("V2 (+ Threshold)", v2),
            ("V3 (+ RSI)", v3)
        ]
        
        print(f"\n📊 COMPARAÇÃO DE VERSÕES:")
        print(f"{'Versão':<15} {'Return':<8} {'Sharpe':<8} {'Win%':<6} {'Trades':<7} {'Obj':<4}")
        print("-" * 50)
        
        best_version = None
        best_score = -999
        
        for name, result in versions:
            if 'error' not in result:
                # Score baseado nos objetivos
                win_score = 1 if result['win_rate'] >= 60 else 0
                sharpe_score = 1 if result['sharpe_ratio'] >= 0.5 else 0
                return_score = 1 if result['total_return_pct'] >= 3 else 0
                dd_score = 1 if result['max_drawdown_pct'] <= 2 else 0
                total_score = win_score + sharpe_score + return_score + dd_score
                
                print(f"{name:<15} {result['total_return_pct']:>6.2f}% {result['sharpe_ratio']:>6.3f} {result['win_rate']:>5.1f}% {result['total_trades']:>6} {total_score}/4")
                
                if total_score > best_score or (total_score == best_score and result['sharpe_ratio'] > best_version[1]['sharpe_ratio']):
                    best_score = total_score
                    best_version = (name, result)
        
        if best_version:
            print(f"\n🏆 MELHOR VERSÃO: {best_version[0]}")
            result = best_version[1]
            print(f"   🎯 STATUS VS OBJETIVOS:")
            print(f"      Win Rate: {result['win_rate']:.1f}% {'✅' if result['win_rate'] >= 60 else '❌'} (>60%)")
            print(f"      Sharpe: {result['sharpe_ratio']:.3f} {'✅' if result['sharpe_ratio'] >= 0.5 else '❌'} (>0.5)")
            print(f"      Return: {result['total_return_pct']:.2f}% {'✅' if result['total_return_pct'] >= 3 else '❌'} (>3%)")
            print(f"      Max DD: {result['max_drawdown_pct']:.2f}% {'✅' if result['max_drawdown_pct'] <= 2 else '❌'} (<2%)")
            
            if 'win_loss_ratio' in result:
                print(f"      Win/Loss Ratio: {result['win_loss_ratio']:.2f}")
    
    print(f"\n💡 CONCLUSÕES:")
    print(f"   • Ajustes mínimos preservam a estrutura que funciona")
    print(f"   • Take-profit e stop-loss são os ajustes mais impactantes")
    print(f"   • Threshold mais baixo pode aumentar oportunidades")
    print(f"   • RSI mais amplo pode melhorar entrada em trends")


if __name__ == "__main__":
    run_fine_tuning_test()
