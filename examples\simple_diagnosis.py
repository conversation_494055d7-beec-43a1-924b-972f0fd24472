#!/usr/bin/env python3
"""
Diagnóstico Simples - Identifica problema raiz rapidamente
"""

import pandas as pd
import numpy as np
import requests
from datetime import datetime, timedelta

def get_btc_data():
    """Busca dados BTC simples."""
    try:
        end_time = int(datetime.now().timestamp() * 1000)
        start_time = int((datetime.now() - timedelta(days=30)).timestamp() * 1000)
        
        url = "https://api.binance.com/api/v3/klines"
        params = {
            'symbol': 'BTCUSDT',
            'interval': '1h',
            'startTime': start_time,
            'endTime': end_time,
            'limit': 500
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        prices = [float(candle[4]) for candle in data]  # Close prices
        return prices
        
    except Exception as e:
        print(f"Erro: {e}")
        return []

def main():
    print("🔬 DIAGNÓSTICO SIMPLES - PROBLEMA RAIZ")
    print("=" * 50)
    
    # Busca dados
    prices = get_btc_data()
    
    if not prices or len(prices) < 100:
        print("❌ Erro ao obter dados")
        return
    
    print(f"✅ {len(prices)} preços obtidos")
    
    # Análise básica
    start_price = prices[50]  # Mesmo ponto das estratégias
    end_price = prices[-1]
    
    buy_hold_return = (end_price / start_price - 1) * 100
    
    print(f"\n📊 ANÁLISE BÁSICA:")
    print(f"   💲 Preço inicial: ${start_price:.2f}")
    print(f"   💲 Preço final: ${end_price:.2f}")
    print(f"   📈 Buy & Hold: {buy_hold_return:.2f}%")
    
    # Calcula retornos horários
    returns = []
    for i in range(1, len(prices)):
        ret = (prices[i] / prices[i-1] - 1) * 100
        returns.append(ret)
    
    avg_return = np.mean(returns)
    volatility = np.std(returns)
    
    print(f"   📊 Retorno médio/hora: {avg_return:.4f}%")
    print(f"   📊 Volatilidade/hora: {volatility:.4f}%")
    
    # Teste de sinal simples
    print(f"\n🎯 TESTE DE SINAL SIMPLES:")
    
    # RSI básico
    deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
    gains = [max(0, d) for d in deltas]
    losses = [max(0, -d) for d in deltas]
    
    # RSI para últimos 100 pontos
    rsi_values = []
    for i in range(14, len(gains)):
        avg_gain = np.mean(gains[i-14:i])
        avg_loss = np.mean(losses[i-14:i])
        
        if avg_loss == 0:
            rsi = 100
        else:
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
        
        rsi_values.append(rsi)
    
    # Testa correlação RSI com retornos futuros
    if len(rsi_values) > 50:
        signals = []
        future_returns = []
        
        for i in range(len(rsi_values) - 1):
            rsi = rsi_values[i]
            
            # Sinal RSI
            if rsi > 70:
                signal = -1  # Sell
            elif rsi < 30:
                signal = 1   # Buy
            else:
                signal = 0   # Hold
            
            signals.append(signal)
            
            # Retorno futuro (próxima hora)
            current_idx = 50 + 14 + i  # Ajusta índices
            if current_idx + 1 < len(prices):
                future_ret = (prices[current_idx + 1] / prices[current_idx] - 1) * 100
                future_returns.append(future_ret)
            else:
                future_returns.append(0)
        
        # Correlação
        if len(signals) > 10 and len(future_returns) > 10:
            correlation = np.corrcoef(signals, future_returns)[0, 1]
            
            print(f"   🔗 Correlação RSI vs Retorno Futuro: {correlation:.4f}")
            
            if correlation < -0.1:
                print(f"   ❌ PROBLEMA: Correlação NEGATIVA!")
                print(f"   💡 SOLUÇÃO: INVERTER sinais RSI")
            elif correlation > 0.1:
                print(f"   ✅ Correlação positiva - RSI funciona")
            else:
                print(f"   ⚠️ Correlação fraca - RSI não funciona")
    
    # Diagnóstico final
    print(f"\n" + "="*50)
    print(f"🎯 DIAGNÓSTICO FINAL")
    print(f"="*50)
    
    if buy_hold_return < -2:
        print(f"❌ MERCADO EM QUEDA ({buy_hold_return:.2f}%)")
        print(f"💡 Estratégias long falham em bear market")
        print(f"🎯 SOLUÇÃO: Implementar proteção ou short")
    
    elif buy_hold_return > 2:
        print(f"✅ MERCADO EM ALTA ({buy_hold_return:.2f}%)")
        print(f"❌ Nossas estratégias perdem em bull market!")
        print(f"💡 CAUSA: Overtrading ou sinais errados")
        print(f"🎯 SOLUÇÃO: Reduzir trades ou corrigir sinais")
    
    else:
        print(f"📊 MERCADO LATERAL ({buy_hold_return:.2f}%)")
        print(f"💡 Mean reversion pode funcionar")
    
    # Recomendação específica
    print(f"\n🚀 RECOMENDAÇÃO ESPECÍFICA:")
    
    if buy_hold_return > 0:
        print(f"   1. 📈 Mercado subiu {buy_hold_return:.2f}%")
        print(f"   2. 🎯 Usar estratégia SIMPLES: Buy quando RSI < 30")
        print(f"   3. 💰 Evitar overtrading (máximo 5 trades/mês)")
        print(f"   4. 🛡️ Stop loss em -2%")
    else:
        print(f"   1. 📉 Mercado caiu {buy_hold_return:.2f}%")
        print(f"   2. 🎯 Usar proteção: Short quando RSI > 70")
        print(f"   3. 💰 Posições pequenas (máximo 50%)")
        print(f"   4. 🛡️ Stop loss em -1%")
    
    print(f"\n✅ Diagnóstico concluído!")

if __name__ == "__main__":
    main()
