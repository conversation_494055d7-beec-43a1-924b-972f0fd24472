#!/usr/bin/env python3
"""
Test script to validate QASTCore market integration fix
"""
import asyncio
import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from qualia.core.qast_core import TradingQASTCore
from qualia.market.base_integration import CryptoData<PERSON><PERSON><PERSON>

async def test_qast_market_integration():
    """Test QASTCore market integration fix"""
    print("🧪 Testing QASTCore market integration fix...")
    
    try:
        # Create market integration
        print("📡 Creating market integration...")
        market_integration = CryptoDataFetcher('kucoin')
        await market_integration.initialize_connection()
        print("✅ Market integration initialized")
        
        # Test fetch_ticker method directly
        print("🔍 Testing fetch_ticker method...")
        try:
            ticker = await market_integration.fetch_ticker('BTC-USDT')
            price = ticker.get('last', 'N/A')
            print(f"✅ fetch_ticker works: BTC-USDT @ ${price}")
        except Exception as e:
            print(f"❌ fetch_ticker error: {e}")
            return False
        
        # Create QASTCore with market_integration
        print("🧠 Creating QASTCore with market_integration...")
        config = {
            'consciousness_threshold': 0.5, 
            'qualia': {},
            'symbols': ['BTC/USDT']
        }
        qast_core = TradingQASTCore(config, market_integration=market_integration)
        print("✅ QASTCore created with market_integration")
        
        # Test market data collection
        print("📊 Testing QASTCore market data collection...")
        try:
            market_data = await qast_core._gather_market_data_from_integration()
            if market_data:
                symbols = list(market_data.keys())
                print(f"✅ QASTCore market integration working: {len(symbols)} symbols")
                for symbol in symbols:
                    data = market_data[symbol]
                    price = data.get('ticker', {}).get('last', 'N/A')
                    print(f"   📈 {symbol}: ${price}")
                success = True
            else:
                print("❌ QASTCore market integration returned None")
                success = False
        except Exception as e:
            print(f"❌ QASTCore market integration error: {e}")
            success = False
        
        # Cleanup
        await market_integration.close()
        print("🧹 Cleanup completed")
        
        return success
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_qast_market_integration())
    if success:
        print("\n🎉 QASTCore market integration fix SUCCESSFUL!")
        print("✅ Ready for full system validation")
    else:
        print("\n💥 QASTCore market integration fix FAILED!")
        print("❌ Needs further debugging")
    
    sys.exit(0 if success else 1)
