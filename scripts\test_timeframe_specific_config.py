#!/usr/bin/env python3
"""
Script de teste para validar configuração específica por timeframe
Testa se os parâmetros são carregados corretamente para cada timeframe
"""

import sys
import os
import yaml
import logging
from pathlib import Path

# Adicionar o diretório raiz ao path
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from src.qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy
    from src.qualia.config.config_manager import ConfigManager
    QUALIA_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ QUALIA não disponível: {e}")
    QUALIA_AVAILABLE = False

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_timeframe_config_loading():
    """Testa carregamento de configuração específica por timeframe"""
    logger.info("🧪 Testando carregamento de configuração por timeframe...")
    
    if not QUALIA_AVAILABLE:
        logger.error("❌ QUALIA não disponível para teste")
        return False
    
    # Timeframes para testar
    timeframes = ["1m", "5m", "15m", "1h"]
    
    # Valores esperados por timeframe (baseados na configuração)
    expected_values = {
        "1m": {
            "hype_threshold": 0.42,
            "wave_min_strength": 0.32,
            "quantum_boost_factor": 1.02,
            "holographic_weight": 0.4,
            "tsvf_validation_threshold": 0.65
        },
        "5m": {
            "hype_threshold": 0.36,
            "wave_min_strength": 0.26,
            "quantum_boost_factor": 1.05,
            "holographic_weight": 0.5,
            "tsvf_validation_threshold": 0.55
        },
        "15m": {
            "hype_threshold": 0.28,
            "wave_min_strength": 0.22,
            "quantum_boost_factor": 1.08,
            "holographic_weight": 0.6,
            "tsvf_validation_threshold": 0.45
        },
        "1h": {
            "hype_threshold": 0.22,
            "wave_min_strength": 0.18,
            "quantum_boost_factor": 1.12,
            "holographic_weight": 0.7,
            "tsvf_validation_threshold": 0.35
        }
    }
    
    results = {}
    
    for timeframe in timeframes:
        try:
            logger.info(f"📊 Testando timeframe: {timeframe}")
            
            # Criar contexto com timeframe específico
            context = {"timeframe": timeframe}
            
            # Inicializar estratégia
            strategy = FibonacciWaveHypeStrategy(
                symbol="BTC/USDT",
                timeframe=timeframe,
                context=context
            )
            
            # Verificar se os parâmetros foram carregados corretamente
            actual_values = {
                "hype_threshold": strategy.hype_threshold,
                "wave_min_strength": strategy.wave_min_strength,
                "quantum_boost_factor": strategy.quantum_boost_factor,
                "holographic_weight": strategy.holographic_weight,
                "tsvf_validation_threshold": strategy.tsvf_validation_threshold
            }
            
            # Comparar com valores esperados
            expected = expected_values[timeframe]
            matches = True
            
            for param, expected_val in expected.items():
                actual_val = actual_values[param]
                if abs(actual_val - expected_val) > 0.001:  # Tolerância para float
                    logger.error(f"❌ {timeframe} - {param}: esperado {expected_val}, obtido {actual_val}")
                    matches = False
                else:
                    logger.info(f"✅ {timeframe} - {param}: {actual_val}")
            
            results[timeframe] = {
                "success": matches,
                "values": actual_values
            }
            
        except Exception as e:
            logger.error(f"❌ Erro ao testar {timeframe}: {e}")
            results[timeframe] = {"success": False, "error": str(e)}
    
    # Resumo dos resultados
    logger.info("\n📋 RESUMO DOS TESTES:")
    all_passed = True
    
    for timeframe, result in results.items():
        if result["success"]:
            logger.info(f"✅ {timeframe}: PASSOU")
        else:
            logger.error(f"❌ {timeframe}: FALHOU")
            all_passed = False
    
    return all_passed


def test_config_file_structure():
    """Testa se a estrutura do arquivo de configuração está correta"""
    logger.info("🔍 Testando estrutura do arquivo de configuração...")
    
    config_path = "config/fwh_scalp_config.yaml"
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Verificar estrutura básica
        assert 'fibonacci_wave_hype_config' in config
        assert 'params' in config['fibonacci_wave_hype_config']
        
        params = config['fibonacci_wave_hype_config']['params']
        
        # Verificar se timeframe_specific existe
        assert 'timeframe_specific' in params
        
        timeframe_specific = params['timeframe_specific']
        
        # Verificar se todos os timeframes estão presentes
        required_timeframes = ["1m", "5m", "15m", "1h"]
        for tf in required_timeframes:
            assert tf in timeframe_specific, f"Timeframe {tf} não encontrado"
        
        # Verificar se todos os parâmetros estão presentes em cada timeframe
        required_params = [
            "hype_threshold",
            "wave_min_strength", 
            "quantum_boost_factor",
            "holographic_weight",
            "tsvf_validation_threshold"
        ]
        
        for tf in required_timeframes:
            tf_params = timeframe_specific[tf]
            for param in required_params:
                assert param in tf_params, f"Parâmetro {param} não encontrado em {tf}"
                assert isinstance(tf_params[param], (int, float)), f"Parâmetro {param} em {tf} deve ser numérico"
        
        logger.info("✅ Estrutura do arquivo de configuração está correta")
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro na estrutura do arquivo: {e}")
        return False


def main():
    """Função principal de teste"""
    logger.info("🚀 Iniciando testes de configuração por timeframe...")
    
    # Teste 1: Estrutura do arquivo
    test1_passed = test_config_file_structure()
    
    # Teste 2: Carregamento por timeframe
    test2_passed = test_timeframe_config_loading()
    
    # Resultado final
    if test1_passed and test2_passed:
        logger.info("🎉 TODOS OS TESTES PASSARAM!")
        return True
    else:
        logger.error("💥 ALGUNS TESTES FALHARAM!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
