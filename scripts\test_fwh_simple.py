#!/usr/bin/env python3
"""
Script de teste simplificado para a estratégia Fibonacci Wave Hype (FWH).
"""

import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path

# Adiciona o diretório src ao path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

print("🚀 QUALIA - Teste Simplificado da Estratégia FWH")
print("=" * 50)

def test_fibonacci_calculations():
    """Testa cálculos básicos de Fibonacci."""
    print("🔢 Testando cálculos de Fibonacci...")
    
    try:
        from qualia.strategies.fibonacci_wave_hype.indicators import calculate_fibonacci_levels
        
        # Dados de teste
        highs = pd.Series([52000, 53000, 54000, 53500, 52800])
        lows = pd.Series([50000, 50500, 51000, 50800, 50200])
        
        levels = calculate_fibonacci_levels(highs, lows)
        
        print(f"📊 Níveis de Fibonacci:")
        for level, price in levels.items():
            print(f"   {level}: ${price:,.2f}")
        
        # Validações básicas
        assert levels["0.0"] > levels["0.618"]
        assert levels["0.618"] > levels["1.0"]
        
        print("✅ Cálculos de Fibonacci OK")
        return True
        
    except Exception as e:
        print(f"❌ Erro nos cálculos de Fibonacci: {e}")
        return False


def test_wave_detection():
    """Testa detecção de padrões de ondas."""
    print("\n🌊 Testando detecção de ondas...")
    
    try:
        from qualia.strategies.fibonacci_wave_hype.indicators import (
            calculate_fibonacci_levels, 
            detect_wave_patterns
        )
        
        # Gera dados sintéticos
        dates = pd.date_range("2024-01-01", periods=50, freq="1H")
        prices = [50000 + i * 100 + np.sin(i * 0.1) * 1000 for i in range(50)]
        
        market_data = pd.DataFrame({
            "close": prices,
            "high": [p * 1.01 for p in prices],
            "low": [p * 0.99 for p in prices],
            "volume": [1000 + np.random.uniform(0, 500) for _ in prices]
        }, index=dates)
        
        # Calcula níveis e detecta padrões
        fib_levels = calculate_fibonacci_levels(
            market_data["high"].tail(20),
            market_data["low"].tail(20)
        )
        
        patterns = detect_wave_patterns(market_data, fib_levels)
        
        print(f"🔍 Padrões detectados:")
        print(f"   Nível Fibonacci: {patterns['fib_level']}")
        print(f"   Direção: {patterns['direction']}")
        print(f"   Força: {patterns['trend_strength']:.4f}")
        
        # Validações
        assert patterns["direction"] in [-1, 1]
        assert patterns["trend_strength"] >= 0
        
        print("✅ Detecção de ondas OK")
        return True
        
    except Exception as e:
        print(f"❌ Erro na detecção de ondas: {e}")
        return False


def test_strategy_initialization():
    """Testa inicialização da estratégia."""
    print("\n⚙️ Testando inicialização da estratégia...")
    
    try:
        from qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy
        
        # Parâmetros de teste
        parameters = {
            "fib_lookback": 30,
            "hype_threshold": 0.618,
            "wave_min_strength": 0.4,
        }
        
        # Inicializa estratégia
        strategy = FibonacciWaveHypeStrategy(
            symbol="BTC/USDT",
            timeframe="1h",
            parameters=parameters
        )
        
        print(f"📋 Estratégia inicializada:")
        print(f"   Símbolo: {strategy.symbol}")
        print(f"   Timeframe: {strategy.timeframe}")
        print(f"   Lookback: {strategy.fib_lookback}")
        print(f"   Threshold: {strategy.hype_threshold}")
        
        # Validações
        assert strategy.symbol == "BTC/USDT"
        assert strategy.timeframe == "1h"
        assert strategy.fib_lookback == 30
        
        print("✅ Inicialização da estratégia OK")
        return True
        
    except Exception as e:
        print(f"❌ Erro na inicialização: {e}")
        return False


def test_sentiment_integration():
    """Testa integração básica de sentiment."""
    print("\n🌌 Testando integração de sentiment...")
    
    try:
        from unittest.mock import Mock
        from qualia.strategies.fibonacci_wave_hype.sentiment_integration import (
            integrate_holographic_sentiment,
            _apply_quantum_transformation,
            _calculate_holographic_coherence
        )
        
        # Testa transformação quântica
        quantum_result = _apply_quantum_transformation(0.5, 0.8, 0.9)
        print(f"🔬 Transformação quântica: {quantum_result:.4f}")
        
        # Testa coerência holográfica
        coherence = _calculate_holographic_coherence("AI_SURGE", "BTC")
        print(f"🌐 Coerência holográfica: {coherence:.4f}")
        
        # Mock engine para teste de integração
        mock_engine = Mock()
        
        # Simula CollectiveMindState
        from dataclasses import dataclass
        from typing import Dict, Any
        
        @dataclass
        class CollectiveMindState:
            timestamp: float
            dominant_narrative: str
            persona_impact: Dict[str, Any]
        
        collective_state = CollectiveMindState(
            timestamp=datetime.now().timestamp(),
            dominant_narrative="QUANTUM_FIBONACCI",
            persona_impact={
                "RetailCluster": {"sentiment": 0.7, "confidence_boost": 0.8},
                "MomentumQuant": {"sentiment": 0.6, "confidence_boost": 0.7}
            }
        )
        
        mock_engine.generate_collective_mind_state.return_value = collective_state
        
        # Testa integração
        boost_factor = integrate_holographic_sentiment(
            mock_engine, "BTC/USDT", use_cache=False
        )
        
        print(f"🚀 Fator de boost: {boost_factor:.3f}")
        
        # Validações
        assert isinstance(quantum_result, float)
        assert coherence > 0
        assert 0.3 <= boost_factor <= 2.5
        
        print("✅ Integração de sentiment OK")
        return True
        
    except Exception as e:
        print(f"❌ Erro na integração de sentiment: {e}")
        print(f"   Detalhes: {str(e)}")
        return False


def main():
    """Função principal de teste."""
    print("Executando testes da estratégia FWH...\n")
    
    results = []
    
    # Executa testes
    results.append(("Fibonacci", test_fibonacci_calculations()))
    results.append(("Ondas", test_wave_detection()))
    results.append(("Estratégia", test_strategy_initialization()))
    results.append(("Sentiment", test_sentiment_integration()))
    
    # Resumo
    print("\n" + "=" * 50)
    print("🎯 RESUMO DOS TESTES")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSOU" if result else "❌ FALHOU"
        print(f"{test_name:12}: {status}")
        if result:
            passed += 1
    
    print(f"\nResultado: {passed}/{len(results)} testes passaram")
    
    if passed == len(results):
        print("\n🌟 Todos os testes passaram! Estratégia FWH está funcionando!")
        return 0
    else:
        print(f"\n⚠️ {len(results) - passed} teste(s) falharam. Verifique os erros acima.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
