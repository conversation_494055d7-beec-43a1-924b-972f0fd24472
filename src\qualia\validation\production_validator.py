"""
QUALIA Production Environment Validator
Comprehensive validation of production environment before deployment
"""

import os
import sys
import json
import time
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
import yaml
import requests
import psutil
import subprocess
from pathlib import Path
import importlib.util
import socket
import ssl

logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """Validation test result"""
    test_name: str
    category: str
    status: str  # 'PASS', 'FAIL', 'WARNING', 'SKIP'
    message: str
    details: Dict[str, Any]
    execution_time_ms: float
    timestamp: datetime

@dataclass
class ValidationReport:
    """Complete validation report"""
    validation_id: str
    timestamp: datetime
    overall_status: str  # 'PASS', 'FAIL', 'WARNING'
    total_tests: int
    passed_tests: int
    failed_tests: int
    warning_tests: int
    skipped_tests: int
    execution_time_ms: float
    results: List[ValidationResult]
    environment_info: Dict[str, Any]

class ProductionValidator:
    """
    Production environment validator
    Performs comprehensive validation of all system components
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.validation_config = config.get('validation', {})

        # Validation categories
        self.validation_categories = [
            'system_requirements',
            'configuration',
            'security',
            'connectivity',
            'dependencies',
            'file_system',
            'performance',
            'integration'
        ]

        # Results storage
        self.results = []
        self.start_time = None

    async def validate_production_environment(self) -> ValidationReport:
        """
        Perform complete production environment validation

        Returns:
            ValidationReport with all test results
        """
        validation_id = f"validation_{int(datetime.utcnow().timestamp())}"
        self.start_time = datetime.utcnow()

        logger.info(f"Starting production environment validation: {validation_id}")

        try:
            # Clear previous results
            self.results = []

            # Run validation categories
            await self._validate_system_requirements()
            await self._validate_configuration()
            await self._validate_security()
            await self._validate_connectivity()
            await self._validate_dependencies()
            await self._validate_file_system()
            await self._validate_performance()
            await self._validate_integration()

            # Generate report
            report = self._generate_validation_report(validation_id)

            # Save report
            await self._save_validation_report(report)

            logger.info(f"Validation completed: {report.overall_status} "
                       f"({report.passed_tests}/{report.total_tests} passed)")

            return report

        except Exception as e:
            logger.error(f"Validation failed: {e}")

            # Create error report
            error_report = ValidationReport(
                validation_id=validation_id,
                timestamp=self.start_time,
                overall_status='FAIL',
                total_tests=0,
                passed_tests=0,
                failed_tests=1,
                warning_tests=0,
                skipped_tests=0,
                execution_time_ms=0,
                results=[ValidationResult(
                    test_name='validation_framework',
                    category='system',
                    status='FAIL',
                    message=f'Validation framework error: {e}',
                    details={'error': str(e)},
                    execution_time_ms=0,
                    timestamp=datetime.utcnow()
                )],
                environment_info={}
            )

            return error_report

    async def _validate_system_requirements(self):
        """Validate system requirements"""
        category = 'system_requirements'

        # Python version check
        await self._run_test(
            'python_version',
            category,
            self._check_python_version,
            'Validate Python version compatibility'
        )

        # Operating system check
        await self._run_test(
            'operating_system',
            category,
            self._check_operating_system,
            'Validate operating system compatibility'
        )

        # Memory check
        await self._run_test(
            'memory_requirements',
            category,
            self._check_memory_requirements,
            'Validate memory requirements'
        )

        # CPU check
        await self._run_test(
            'cpu_requirements',
            category,
            self._check_cpu_requirements,
            'Validate CPU requirements'
        )

        # Disk space check
        await self._run_test(
            'disk_space',
            category,
            self._check_disk_space,
            'Validate disk space requirements'
        )

    async def _validate_configuration(self):
        """Validate configuration files"""
        category = 'configuration'

        # Production config validation
        await self._run_test(
            'production_config',
            category,
            self._check_production_config,
            'Validate production configuration file'
        )

        # Environment variables
        await self._run_test(
            'environment_variables',
            category,
            self._check_environment_variables,
            'Validate required environment variables'
        )

        # Configuration integrity
        await self._run_test(
            'config_integrity',
            category,
            self._check_config_integrity,
            'Validate configuration file integrity'
        )

    async def _validate_security(self):
        """Validate security components"""
        category = 'security'

        # Credentials validation
        await self._run_test(
            'credentials_security',
            category,
            self._check_credentials_security,
            'Validate credentials security setup'
        )

        # File permissions
        await self._run_test(
            'file_permissions',
            category,
            self._check_file_permissions,
            'Validate file permissions'
        )

        # SSL/TLS configuration
        await self._run_test(
            'ssl_configuration',
            category,
            self._check_ssl_configuration,
            'Validate SSL/TLS configuration'
        )

    async def _validate_connectivity(self):
        """Validate network connectivity"""
        category = 'connectivity'

        # Internet connectivity
        await self._run_test(
            'internet_connectivity',
            category,
            self._check_internet_connectivity,
            'Validate internet connectivity'
        )

        # Exchange API connectivity
        await self._run_test(
            'exchange_api',
            category,
            self._check_exchange_api_connectivity,
            'Validate exchange API connectivity'
        )

        # DNS resolution
        await self._run_test(
            'dns_resolution',
            category,
            self._check_dns_resolution,
            'Validate DNS resolution'
        )

    async def _validate_dependencies(self):
        """Validate Python dependencies"""
        category = 'dependencies'

        # Required packages
        await self._run_test(
            'required_packages',
            category,
            self._check_required_packages,
            'Validate required Python packages'
        )

        # Package versions
        await self._run_test(
            'package_versions',
            category,
            self._check_package_versions,
            'Validate package version compatibility'
        )

        # Import tests
        await self._run_test(
            'import_tests',
            category,
            self._check_imports,
            'Validate module imports'
        )

    async def _validate_file_system(self):
        """Validate file system setup"""
        category = 'file_system'

        # Required directories
        await self._run_test(
            'required_directories',
            category,
            self._check_required_directories,
            'Validate required directories exist'
        )

        # File permissions
        await self._run_test(
            'directory_permissions',
            category,
            self._check_directory_permissions,
            'Validate directory permissions'
        )

        # Log file rotation
        await self._run_test(
            'log_rotation',
            category,
            self._check_log_rotation,
            'Validate log rotation setup'
        )

    async def _validate_performance(self):
        """Validate performance requirements"""
        category = 'performance'

        # System load
        await self._run_test(
            'system_load',
            category,
            self._check_system_load,
            'Validate system load levels'
        )

        # Network latency
        await self._run_test(
            'network_latency',
            category,
            self._check_network_latency,
            'Validate network latency'
        )

        # Disk I/O performance
        await self._run_test(
            'disk_io_performance',
            category,
            self._check_disk_io_performance,
            'Validate disk I/O performance'
        )

    async def _validate_integration(self):
        """Validate system integration"""
        category = 'integration'

        # Component integration
        await self._run_test(
            'component_integration',
            category,
            self._check_component_integration,
            'Validate component integration'
        )

        # Database connectivity (if applicable)
        await self._run_test(
            'database_connectivity',
            category,
            self._check_database_connectivity,
            'Validate database connectivity'
        )

        # Monitoring integration
        await self._run_test(
            'monitoring_integration',
            category,
            self._check_monitoring_integration,
            'Validate monitoring system integration'
        )

    async def _run_test(self, test_name: str, category: str,
                       test_func, description: str):
        """Run individual validation test"""
        start_time = datetime.utcnow()

        try:
            logger.debug(f"Running test: {test_name}")

            # Execute test function
            result = await test_func() if asyncio.iscoroutinefunction(test_func) else test_func()

            # Calculate execution time
            execution_time = (datetime.utcnow() - start_time).total_seconds() * 1000

            # Create result
            validation_result = ValidationResult(
                test_name=test_name,
                category=category,
                status=result['status'],
                message=result['message'],
                details=result.get('details', {}),
                execution_time_ms=execution_time,
                timestamp=start_time
            )

            self.results.append(validation_result)

        except Exception as e:
            execution_time = (datetime.utcnow() - start_time).total_seconds() * 1000

            validation_result = ValidationResult(
                test_name=test_name,
                category=category,
                status='FAIL',
                message=f'Test execution failed: {e}',
                details={'error': str(e)},
                execution_time_ms=execution_time,
                timestamp=start_time
            )

            self.results.append(validation_result)
            logger.error(f"Test {test_name} failed: {e}")

    def _check_python_version(self) -> Dict[str, Any]:
        """Check Python version"""
        try:
            version = sys.version_info

            if version >= (3, 8):
                return {
                    'status': 'PASS',
                    'message': f'Python version {version.major}.{version.minor}.{version.micro} is supported',
                    'details': {'version': f'{version.major}.{version.minor}.{version.micro}'}
                }
            else:
                return {
                    'status': 'FAIL',
                    'message': f'Python version {version.major}.{version.minor}.{version.micro} is not supported (requires 3.8+)',
                    'details': {'version': f'{version.major}.{version.minor}.{version.micro}'}
                }
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check Python version: {e}',
                'details': {'error': str(e)}
            }

    def _check_operating_system(self) -> Dict[str, Any]:
        """Check operating system compatibility"""
        try:
            import platform

            os_name = platform.system()
            os_version = platform.release()

            # QUALIA supports Linux, macOS, and Windows
            supported_os = ['Linux', 'Darwin', 'Windows']

            if os_name in supported_os:
                return {
                    'status': 'PASS',
                    'message': f'Operating system {os_name} {os_version} is supported',
                    'details': {'os': os_name, 'version': os_version}
                }
            else:
                return {
                    'status': 'FAIL',
                    'message': f'Operating system {os_name} is not supported',
                    'details': {'os': os_name, 'version': os_version}
                }
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check operating system: {e}',
                'details': {'error': str(e)}
            }

    def _check_memory_requirements(self) -> Dict[str, Any]:
        """Check memory requirements"""
        try:
            memory = psutil.virtual_memory()
            available_gb = memory.available / (1024**3)
            total_gb = memory.total / (1024**3)

            # For staging, require at least 512MB available memory (more realistic)
            if available_gb >= 1.0:
                return {
                    'status': 'PASS',
                    'message': f'Sufficient memory available: {available_gb:.1f}GB of {total_gb:.1f}GB',
                    'details': {'available_gb': available_gb, 'total_gb': total_gb}
                }
            elif available_gb >= 0.5:
                return {
                    'status': 'WARNING',
                    'message': f'Low memory available: {available_gb:.1f}GB of {total_gb:.1f}GB',
                    'details': {'available_gb': available_gb, 'total_gb': total_gb}
                }
            else:
                return {
                    'status': 'FAIL',
                    'message': f'Insufficient memory: {available_gb:.1f}GB available (requires 512MB+)',
                    'details': {'available_gb': available_gb, 'total_gb': total_gb}
                }
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check memory requirements: {e}',
                'details': {'error': str(e)}
            }

    def _check_cpu_requirements(self) -> Dict[str, Any]:
        """Check CPU requirements"""
        try:
            cpu_count = psutil.cpu_count()
            cpu_percent = psutil.cpu_percent(interval=1)

            # Require at least 2 CPU cores
            if cpu_count >= 2:
                if cpu_percent < 80:
                    return {
                        'status': 'PASS',
                        'message': f'CPU requirements met: {cpu_count} cores, {cpu_percent}% usage',
                        'details': {'cpu_count': cpu_count, 'cpu_percent': cpu_percent}
                    }
                else:
                    return {
                        'status': 'WARNING',
                        'message': f'High CPU usage: {cpu_count} cores, {cpu_percent}% usage',
                        'details': {'cpu_count': cpu_count, 'cpu_percent': cpu_percent}
                    }
            else:
                return {
                    'status': 'FAIL',
                    'message': f'Insufficient CPU cores: {cpu_count} (requires 2+)',
                    'details': {'cpu_count': cpu_count, 'cpu_percent': cpu_percent}
                }
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check CPU requirements: {e}',
                'details': {'error': str(e)}
            }

    def _check_disk_space(self) -> Dict[str, Any]:
        """Check disk space requirements"""
        try:
            disk_usage = psutil.disk_usage('.')
            free_gb = disk_usage.free / (1024**3)
            total_gb = disk_usage.total / (1024**3)

            # Require at least 5GB free space
            if free_gb >= 5.0:
                return {
                    'status': 'PASS',
                    'message': f'Sufficient disk space: {free_gb:.1f}GB of {total_gb:.1f}GB free',
                    'details': {'free_gb': free_gb, 'total_gb': total_gb}
                }
            elif free_gb >= 2.0:
                return {
                    'status': 'WARNING',
                    'message': f'Low disk space: {free_gb:.1f}GB of {total_gb:.1f}GB free',
                    'details': {'free_gb': free_gb, 'total_gb': total_gb}
                }
            else:
                return {
                    'status': 'FAIL',
                    'message': f'Insufficient disk space: {free_gb:.1f}GB free (requires 5GB+)',
                    'details': {'free_gb': free_gb, 'total_gb': total_gb}
                }
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check disk space: {e}',
                'details': {'error': str(e)}
            }

    def _check_production_config(self) -> Dict[str, Any]:
        """Check production configuration"""
        try:
            config_path = 'config/production_config.yaml'

            if not os.path.exists(config_path):
                return {
                    'status': 'FAIL',
                    'message': f'Production configuration file not found: {config_path}',
                    'details': {'config_path': config_path}
                }

            # Load and validate configuration
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)

            # Check required sections
            required_sections = ['environment', 'capital', 'risk_management', 'trading', 'exchange']
            missing_sections = []

            for section in required_sections:
                if section not in config:
                    missing_sections.append(section)

            if missing_sections:
                return {
                    'status': 'FAIL',
                    'message': f'Missing required configuration sections: {missing_sections}',
                    'details': {'missing_sections': missing_sections}
                }

            # Validate environment setting
            if config.get('environment', {}).get('name') != 'production':
                return {
                    'status': 'FAIL',
                    'message': 'Configuration is not set for production environment',
                    'details': {'environment': config.get('environment', {})}
                }

            return {
                'status': 'PASS',
                'message': 'Production configuration is valid',
                'details': {'config_sections': list(config.keys())}
            }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to validate production configuration: {e}',
                'details': {'error': str(e)}
            }

    def _check_environment_variables(self) -> Dict[str, Any]:
        """Check required environment variables"""
        try:
            # Check for required environment variables
            required_vars = ['QUALIA_ENV']
            optional_vars = ['QUALIA_CONFIG', 'QUALIA_LOG_LEVEL']

            missing_vars = []
            present_vars = []

            for var in required_vars:
                if var in os.environ:
                    present_vars.append(var)
                else:
                    missing_vars.append(var)

            for var in optional_vars:
                if var in os.environ:
                    present_vars.append(var)

            if missing_vars:
                return {
                    'status': 'WARNING',
                    'message': f'Missing optional environment variables: {missing_vars}',
                    'details': {'missing': missing_vars, 'present': present_vars}
                }
            else:
                return {
                    'status': 'PASS',
                    'message': 'All environment variables are set',
                    'details': {'present': present_vars}
                }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check environment variables: {e}',
                'details': {'error': str(e)}
            }

    def _check_config_integrity(self) -> Dict[str, Any]:
        """Check configuration file integrity"""
        try:
            config_path = 'config/production_config.yaml'

            if not os.path.exists(config_path):
                return {
                    'status': 'FAIL',
                    'message': 'Configuration file not found',
                    'details': {}
                }

            # Calculate checksum
            import hashlib
            with open(config_path, 'rb') as f:
                checksum = hashlib.sha256(f.read()).hexdigest()

            return {
                'status': 'PASS',
                'message': 'Configuration file integrity verified',
                'details': {'checksum': checksum}
            }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check configuration integrity: {e}',
                'details': {'error': str(e)}
            }

    def _check_credentials_security(self) -> Dict[str, Any]:
        """Check credentials security setup"""
        try:
            # Check if credentials manager files exist
            cred_files = ['config/.credentials.enc', 'config/.master.key']
            missing_files = []

            for file_path in cred_files:
                if not os.path.exists(file_path):
                    missing_files.append(file_path)

            if missing_files:
                return {
                    'status': 'WARNING',
                    'message': f'Credentials security files not found: {missing_files}',
                    'details': {'missing_files': missing_files}
                }

            # Check file permissions
            for file_path in cred_files:
                if os.path.exists(file_path):
                    file_stat = os.stat(file_path)
                    file_mode = oct(file_stat.st_mode)[-3:]

                    if file_mode != '600':
                        return {
                            'status': 'WARNING',
                            'message': f'Insecure file permissions on {file_path}: {file_mode}',
                            'details': {'file': file_path, 'permissions': file_mode}
                        }

            return {
                'status': 'PASS',
                'message': 'Credentials security setup is valid',
                'details': {'credential_files': cred_files}
            }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check credentials security: {e}',
                'details': {'error': str(e)}
            }

    def _check_file_permissions(self) -> Dict[str, Any]:
        """Check critical file permissions"""
        try:
            critical_files = {
                'config/production_config.yaml': '644',
                'scripts/production_deploy.py': '755',
                'logs/': '755'
            }

            permission_issues = []

            for file_path, expected_perm in critical_files.items():
                if os.path.exists(file_path):
                    file_stat = os.stat(file_path)
                    actual_perm = oct(file_stat.st_mode)[-3:]

                    if actual_perm != expected_perm:
                        permission_issues.append({
                            'file': file_path,
                            'expected': expected_perm,
                            'actual': actual_perm
                        })

            if permission_issues:
                return {
                    'status': 'WARNING',
                    'message': f'File permission issues found: {len(permission_issues)}',
                    'details': {'issues': permission_issues}
                }
            else:
                return {
                    'status': 'PASS',
                    'message': 'File permissions are correct',
                    'details': {'checked_files': list(critical_files.keys())}
                }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check file permissions: {e}',
                'details': {'error': str(e)}
            }

    def _check_ssl_configuration(self) -> Dict[str, Any]:
        """Check SSL/TLS configuration"""
        try:
            # Test SSL context creation
            ssl_context = ssl.create_default_context()

            return {
                'status': 'PASS',
                'message': 'SSL/TLS configuration is valid',
                'details': {'ssl_version': ssl.OPENSSL_VERSION}
            }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'SSL/TLS configuration error: {e}',
                'details': {'error': str(e)}
            }

    def _check_internet_connectivity(self) -> Dict[str, Any]:
        """Check internet connectivity"""
        try:
            # Test connectivity to multiple endpoints
            test_urls = [
                'https://api.kucoin.com/api/v1/timestamp',
                'https://www.google.com',
                'https://httpbin.org/get'
            ]

            successful_connections = 0
            failed_connections = []

            for url in test_urls:
                try:
                    response = requests.get(url, timeout=10)
                    if response.status_code == 200:
                        successful_connections += 1
                    else:
                        failed_connections.append(f"{url}: HTTP {response.status_code}")
                except Exception as e:
                    failed_connections.append(f"{url}: {str(e)}")

            if successful_connections == len(test_urls):
                return {
                    'status': 'PASS',
                    'message': 'Internet connectivity is working',
                    'details': {'successful_connections': successful_connections}
                }
            elif successful_connections > 0:
                return {
                    'status': 'WARNING',
                    'message': f'Partial connectivity: {successful_connections}/{len(test_urls)} successful',
                    'details': {'failed_connections': failed_connections}
                }
            else:
                return {
                    'status': 'FAIL',
                    'message': 'No internet connectivity',
                    'details': {'failed_connections': failed_connections}
                }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check internet connectivity: {e}',
                'details': {'error': str(e)}
            }

    def _check_exchange_api_connectivity(self) -> Dict[str, Any]:
        """Check exchange API connectivity"""
        try:
            # Test KuCoin API connectivity
            kucoin_endpoints = [
                'https://api.kucoin.com/api/v1/timestamp',
                'https://api.kucoin.com/api/v1/status',
                'https://api.kucoin.com/api/v1/symbols'
            ]

            successful_calls = 0
            failed_calls = []

            for endpoint in kucoin_endpoints:
                try:
                    response = requests.get(endpoint, timeout=10)
                    if response.status_code == 200:
                        successful_calls += 1
                    else:
                        failed_calls.append(f"{endpoint}: HTTP {response.status_code}")
                except Exception as e:
                    failed_calls.append(f"{endpoint}: {str(e)}")

            if successful_calls == len(kucoin_endpoints):
                return {
                    'status': 'PASS',
                    'message': 'Exchange API connectivity is working',
                    'details': {'successful_calls': successful_calls}
                }
            elif successful_calls > 0:
                return {
                    'status': 'WARNING',
                    'message': f'Partial API connectivity: {successful_calls}/{len(kucoin_endpoints)} successful',
                    'details': {'failed_calls': failed_calls}
                }
            else:
                return {
                    'status': 'FAIL',
                    'message': 'Exchange API connectivity failed',
                    'details': {'failed_calls': failed_calls}
                }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check exchange API connectivity: {e}',
                'details': {'error': str(e)}
            }

    def _check_dns_resolution(self) -> Dict[str, Any]:
        """Check DNS resolution"""
        try:
            test_domains = ['api.kucoin.com', 'google.com', 'github.com']
            resolution_results = []

            for domain in test_domains:
                try:
                    socket.gethostbyname(domain)
                    resolution_results.append({'domain': domain, 'status': 'success'})
                except Exception as e:
                    resolution_results.append({'domain': domain, 'status': 'failed', 'error': str(e)})

            failed_resolutions = [r for r in resolution_results if r['status'] == 'failed']

            if not failed_resolutions:
                return {
                    'status': 'PASS',
                    'message': 'DNS resolution is working',
                    'details': {'resolution_results': resolution_results}
                }
            else:
                return {
                    'status': 'WARNING',
                    'message': f'DNS resolution issues: {len(failed_resolutions)} failed',
                    'details': {'failed_resolutions': failed_resolutions}
                }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check DNS resolution: {e}',
                'details': {'error': str(e)}
            }

    def _check_required_packages(self) -> Dict[str, Any]:
        """Check required Python packages"""
        try:
            required_packages = [
                'asyncio', 'aiohttp', 'numpy', 'pandas', 'yaml',
                'requests', 'psutil', 'schedule', 'optuna'
            ]

            missing_packages = []
            installed_packages = []

            for package in required_packages:
                try:
                    __import__(package)
                    installed_packages.append(package)
                except ImportError:
                    missing_packages.append(package)

            if missing_packages:
                return {
                    'status': 'FAIL',
                    'message': f'Missing required packages: {missing_packages}',
                    'details': {'missing': missing_packages, 'installed': installed_packages}
                }
            else:
                return {
                    'status': 'PASS',
                    'message': 'All required packages are installed',
                    'details': {'installed_packages': installed_packages}
                }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check required packages: {e}',
                'details': {'error': str(e)}
            }

    def _check_package_versions(self) -> Dict[str, Any]:
        """Check package version compatibility"""
        try:
            # This would check specific version requirements
            # For now, just return success if packages are importable
            return {
                'status': 'PASS',
                'message': 'Package versions are compatible',
                'details': {}
            }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check package versions: {e}',
                'details': {'error': str(e)}
            }

    def _check_imports(self) -> Dict[str, Any]:
        """Check critical module imports"""
        try:
            critical_modules = [
                'src.qualia.logging.production_logger',
                'src.qualia.security.credentials_manager',
                'src.qualia.monitoring.production_monitor',
                'src.qualia.backup.backup_manager'
            ]

            import_results = []
            failed_imports = []

            for module in critical_modules:
                try:
                    spec = importlib.util.find_spec(module)
                    if spec is not None:
                        import_results.append({'module': module, 'status': 'success'})
                    else:
                        failed_imports.append({'module': module, 'error': 'Module not found'})
                except Exception as e:
                    failed_imports.append({'module': module, 'error': str(e)})

            if failed_imports:
                return {
                    'status': 'FAIL',
                    'message': f'Failed to import critical modules: {len(failed_imports)}',
                    'details': {'failed_imports': failed_imports}
                }
            else:
                return {
                    'status': 'PASS',
                    'message': 'All critical modules can be imported',
                    'details': {'import_results': import_results}
                }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check imports: {e}',
                'details': {'error': str(e)}
            }

    def _check_required_directories(self) -> Dict[str, Any]:
        """Check required directories exist"""
        try:
            required_dirs = [
                'config', 'data', 'logs', 'backups', 'run',
                'src/qualia', 'scripts'
            ]

            missing_dirs = []
            existing_dirs = []

            for directory in required_dirs:
                if os.path.exists(directory) and os.path.isdir(directory):
                    existing_dirs.append(directory)
                else:
                    missing_dirs.append(directory)

            if missing_dirs:
                return {
                    'status': 'WARNING',
                    'message': f'Missing directories: {missing_dirs}',
                    'details': {'missing': missing_dirs, 'existing': existing_dirs}
                }
            else:
                return {
                    'status': 'PASS',
                    'message': 'All required directories exist',
                    'details': {'existing_directories': existing_dirs}
                }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check required directories: {e}',
                'details': {'error': str(e)}
            }

    def _check_directory_permissions(self) -> Dict[str, Any]:
        """Check directory permissions"""
        try:
            # Check write permissions on critical directories
            write_dirs = ['data', 'logs', 'backups', 'run']
            permission_issues = []

            for directory in write_dirs:
                if os.path.exists(directory):
                    if not os.access(directory, os.W_OK):
                        permission_issues.append(f"No write permission: {directory}")

            if permission_issues:
                return {
                    'status': 'FAIL',
                    'message': f'Directory permission issues: {permission_issues}',
                    'details': {'permission_issues': permission_issues}
                }
            else:
                return {
                    'status': 'PASS',
                    'message': 'Directory permissions are correct',
                    'details': {'checked_directories': write_dirs}
                }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check directory permissions: {e}',
                'details': {'error': str(e)}
            }

    def _check_log_rotation(self) -> Dict[str, Any]:
        """Check log rotation setup"""
        try:
            # Check if log files exist and are writable
            log_files = ['logs/qualia_production.log', 'logs/trading.log']
            log_status = []

            for log_file in log_files:
                if os.path.exists(log_file):
                    if os.access(log_file, os.W_OK):
                        log_status.append({'file': log_file, 'status': 'writable'})
                    else:
                        log_status.append({'file': log_file, 'status': 'not_writable'})
                else:
                    log_status.append({'file': log_file, 'status': 'missing'})

            return {
                'status': 'PASS',
                'message': 'Log rotation setup is ready',
                'details': {'log_status': log_status}
            }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check log rotation: {e}',
                'details': {'error': str(e)}
            }

    def _check_system_load(self) -> Dict[str, Any]:
        """Check system load levels"""
        try:
            # Check CPU and memory load
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent

            if cpu_percent > 90 or memory_percent > 90:
                return {
                    'status': 'WARNING',
                    'message': f'High system load: CPU {cpu_percent}%, Memory {memory_percent}%',
                    'details': {'cpu_percent': cpu_percent, 'memory_percent': memory_percent}
                }
            elif cpu_percent > 70 or memory_percent > 70:
                return {
                    'status': 'WARNING',
                    'message': f'Moderate system load: CPU {cpu_percent}%, Memory {memory_percent}%',
                    'details': {'cpu_percent': cpu_percent, 'memory_percent': memory_percent}
                }
            else:
                return {
                    'status': 'PASS',
                    'message': f'System load is acceptable: CPU {cpu_percent}%, Memory {memory_percent}%',
                    'details': {'cpu_percent': cpu_percent, 'memory_percent': memory_percent}
                }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check system load: {e}',
                'details': {'error': str(e)}
            }

    def _check_network_latency(self) -> Dict[str, Any]:
        """Check network latency to critical endpoints"""
        try:
            import time

            # Test latency to KuCoin API
            start_time = time.time()
            response = requests.get('https://api.kucoin.com/api/v1/timestamp', timeout=10)
            latency_ms = (time.time() - start_time) * 1000

            if response.status_code == 200:
                if latency_ms < 100:
                    return {
                        'status': 'PASS',
                        'message': f'Network latency is excellent: {latency_ms:.1f}ms',
                        'details': {'latency_ms': latency_ms}
                    }
                elif latency_ms < 500:
                    return {
                        'status': 'PASS',
                        'message': f'Network latency is acceptable: {latency_ms:.1f}ms',
                        'details': {'latency_ms': latency_ms}
                    }
                else:
                    return {
                        'status': 'WARNING',
                        'message': f'High network latency: {latency_ms:.1f}ms',
                        'details': {'latency_ms': latency_ms}
                    }
            else:
                return {
                    'status': 'FAIL',
                    'message': f'Network latency test failed: HTTP {response.status_code}',
                    'details': {'status_code': response.status_code}
                }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check network latency: {e}',
                'details': {'error': str(e)}
            }

    def _check_disk_io_performance(self) -> Dict[str, Any]:
        """Check disk I/O performance"""
        try:
            # Simple disk I/O test
            test_file = 'test_io_performance.tmp'
            test_data = b'0' * 1024 * 1024  # 1MB test data

            # Write test
            start_time = time.time()
            with open(test_file, 'wb') as f:
                f.write(test_data)
                f.flush()
                os.fsync(f.fileno())
            write_time = time.time() - start_time

            # Read test
            start_time = time.time()
            with open(test_file, 'rb') as f:
                f.read()
            read_time = time.time() - start_time

            # Cleanup
            os.remove(test_file)

            # Avoid division by zero
            write_speed_mbps = 1.0 / max(write_time, 0.001)
            read_speed_mbps = 1.0 / max(read_time, 0.001)

            if write_speed_mbps > 10 and read_speed_mbps > 10:
                return {
                    'status': 'PASS',
                    'message': f'Disk I/O performance is good: Write {write_speed_mbps:.1f}MB/s, Read {read_speed_mbps:.1f}MB/s',
                    'details': {'write_speed_mbps': write_speed_mbps, 'read_speed_mbps': read_speed_mbps}
                }
            else:
                return {
                    'status': 'WARNING',
                    'message': f'Slow disk I/O: Write {write_speed_mbps:.1f}MB/s, Read {read_speed_mbps:.1f}MB/s',
                    'details': {'write_speed_mbps': write_speed_mbps, 'read_speed_mbps': read_speed_mbps}
                }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check disk I/O performance: {e}',
                'details': {'error': str(e)}
            }

    def _check_component_integration(self) -> Dict[str, Any]:
        """Check component integration"""
        try:
            # Test if all major components can be initialized
            components = [
                'ProductionLogger',
                'ProductionCredentialsManager',
                'ProductionMonitor',
                'ProductionBackupManager'
            ]

            integration_results = []

            for component in components:
                try:
                    # This would test actual component initialization
                    # For now, just check if the module exists
                    integration_results.append({'component': component, 'status': 'available'})
                except Exception as e:
                    integration_results.append({'component': component, 'status': 'failed', 'error': str(e)})

            failed_components = [r for r in integration_results if r['status'] == 'failed']

            if failed_components:
                return {
                    'status': 'FAIL',
                    'message': f'Component integration issues: {len(failed_components)} failed',
                    'details': {'failed_components': failed_components}
                }
            else:
                return {
                    'status': 'PASS',
                    'message': 'All components are available for integration',
                    'details': {'integration_results': integration_results}
                }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check component integration: {e}',
                'details': {'error': str(e)}
            }

    def _check_database_connectivity(self) -> Dict[str, Any]:
        """Check database connectivity (if applicable)"""
        try:
            # QUALIA doesn't use a traditional database, so this is a placeholder
            return {
                'status': 'SKIP',
                'message': 'Database connectivity check skipped (not applicable)',
                'details': {}
            }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check database connectivity: {e}',
                'details': {'error': str(e)}
            }

    def _check_monitoring_integration(self) -> Dict[str, Any]:
        """Check monitoring system integration"""
        try:
            # Check if monitoring components are available
            monitoring_files = [
                'src/qualia/monitoring/production_monitor.py',
                'config/production_config.yaml'
            ]

            missing_files = []
            for file_path in monitoring_files:
                if not os.path.exists(file_path):
                    missing_files.append(file_path)

            if missing_files:
                return {
                    'status': 'FAIL',
                    'message': f'Monitoring integration files missing: {missing_files}',
                    'details': {'missing_files': missing_files}
                }
            else:
                return {
                    'status': 'PASS',
                    'message': 'Monitoring system integration is ready',
                    'details': {'monitoring_files': monitoring_files}
                }

        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Failed to check monitoring integration: {e}',
                'details': {'error': str(e)}
            }

    def _generate_validation_report(self, validation_id: str) -> ValidationReport:
        """Generate comprehensive validation report"""
        end_time = datetime.utcnow()
        execution_time_ms = (end_time - self.start_time).total_seconds() * 1000

        # Count results by status
        passed_tests = len([r for r in self.results if r.status == 'PASS'])
        failed_tests = len([r for r in self.results if r.status == 'FAIL'])
        warning_tests = len([r for r in self.results if r.status == 'WARNING'])
        skipped_tests = len([r for r in self.results if r.status == 'SKIP'])
        total_tests = len(self.results)

        # Determine overall status
        if failed_tests > 0:
            overall_status = 'FAIL'
        elif warning_tests > 0:
            overall_status = 'WARNING'
        else:
            overall_status = 'PASS'

        # Gather environment information
        environment_info = self._gather_environment_info()

        return ValidationReport(
            validation_id=validation_id,
            timestamp=self.start_time,
            overall_status=overall_status,
            total_tests=total_tests,
            passed_tests=passed_tests,
            failed_tests=failed_tests,
            warning_tests=warning_tests,
            skipped_tests=skipped_tests,
            execution_time_ms=execution_time_ms,
            results=self.results,
            environment_info=environment_info
        )

    def _gather_environment_info(self) -> Dict[str, Any]:
        """Gather environment information"""
        try:
            import platform

            return {
                'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                'platform': platform.platform(),
                'architecture': platform.architecture(),
                'processor': platform.processor(),
                'hostname': socket.gethostname(),
                'cpu_count': psutil.cpu_count(),
                'memory_total_gb': psutil.virtual_memory().total / (1024**3),
                'disk_total_gb': psutil.disk_usage('.').total / (1024**3),
                'working_directory': os.getcwd(),
                'environment_variables': {
                    'QUALIA_ENV': os.environ.get('QUALIA_ENV'),
                    'QUALIA_CONFIG': os.environ.get('QUALIA_CONFIG'),
                    'QUALIA_LOG_LEVEL': os.environ.get('QUALIA_LOG_LEVEL')
                }
            }
        except Exception as e:
            return {'error': f'Failed to gather environment info: {e}'}

    async def _save_validation_report(self, report: ValidationReport):
        """Save validation report to file"""
        try:
            # Create reports directory
            reports_dir = Path('reports')
            reports_dir.mkdir(exist_ok=True)

            # Save JSON report
            report_file = reports_dir / f"{report.validation_id}.json"

            # Convert report to dict for JSON serialization
            report_dict = asdict(report)

            # Convert datetime objects to strings
            report_dict['timestamp'] = report.timestamp.isoformat()
            for result in report_dict['results']:
                result['timestamp'] = result['timestamp'].isoformat() if isinstance(result['timestamp'], datetime) else result['timestamp']

            with open(report_file, 'w') as f:
                json.dump(report_dict, f, indent=2)

            # Save human-readable report
            readable_report_file = reports_dir / f"{report.validation_id}_readable.txt"
            with open(readable_report_file, 'w') as f:
                f.write(self._format_readable_report(report))

            logger.info(f"Validation report saved: {report_file}")

        except Exception as e:
            logger.error(f"Failed to save validation report: {e}")

    def _format_readable_report(self, report: ValidationReport) -> str:
        """Format validation report for human reading"""
        lines = []
        lines.append("=" * 80)
        lines.append(f"QUALIA PRODUCTION ENVIRONMENT VALIDATION REPORT")
        lines.append("=" * 80)
        lines.append(f"Validation ID: {report.validation_id}")
        lines.append(f"Timestamp: {report.timestamp}")
        lines.append(f"Overall Status: {report.overall_status}")
        lines.append(f"Execution Time: {report.execution_time_ms:.1f}ms")
        lines.append("")

        lines.append("SUMMARY:")
        lines.append(f"  Total Tests: {report.total_tests}")
        lines.append(f"  Passed: {report.passed_tests}")
        lines.append(f"  Failed: {report.failed_tests}")
        lines.append(f"  Warnings: {report.warning_tests}")
        lines.append(f"  Skipped: {report.skipped_tests}")
        lines.append("")

        # Group results by category
        categories = {}
        for result in report.results:
            if result.category not in categories:
                categories[result.category] = []
            categories[result.category].append(result)

        for category, results in categories.items():
            lines.append(f"{category.upper().replace('_', ' ')}:")
            lines.append("-" * 40)

            for result in results:
                status_symbol = {
                    'PASS': '✓',
                    'FAIL': '✗',
                    'WARNING': '⚠',
                    'SKIP': '-'
                }.get(result.status, '?')

                lines.append(f"  {status_symbol} {result.test_name}: {result.message}")
                if result.status in ['FAIL', 'WARNING'] and result.details:
                    for key, value in result.details.items():
                        if key != 'error':
                            lines.append(f"    {key}: {value}")
            lines.append("")

        lines.append("ENVIRONMENT INFORMATION:")
        lines.append("-" * 40)
        for key, value in report.environment_info.items():
            if isinstance(value, dict):
                lines.append(f"  {key}:")
                for sub_key, sub_value in value.items():
                    lines.append(f"    {sub_key}: {sub_value}")
            else:
                lines.append(f"  {key}: {value}")

        lines.append("")
        lines.append("=" * 80)

        return "\n".join(lines)

# Global validator instance
_validator = None

def initialize_production_validator(config: Dict[str, Any]) -> ProductionValidator:
    """Initialize global production validator"""
    global _validator
    _validator = ProductionValidator(config)
    return _validator

def get_production_validator() -> Optional[ProductionValidator]:
    """Get global production validator instance"""
    return _validator