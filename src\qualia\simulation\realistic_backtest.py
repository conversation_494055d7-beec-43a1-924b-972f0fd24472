#!/usr/bin/env python3
"""
QUALIA Realistic Backtest Simulator
Implementa simulação realista com slippage, fees, latency-jitter para calibrar expectativas
antes de "money in the loop". Parte da tarefa D-01 do roadmap de produção.
"""

import json
import time
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import numpy as np
import pandas as pd

from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class TransactionCosts:
    """Estrutura de custos de transação realistas."""
    
    # Fees de exchange (baseado em KuCoin/Binance)
    maker_fee_bps: float = 10.0      # 0.10% maker fee
    taker_fee_bps: float = 20.0      # 0.20% taker fee
    
    # Slippage baseado em volume e spread
    base_slippage_bps: float = 2.5   # 0.025% slippage base
    volume_impact_factor: float = 0.1 # Impacto do volume no slippage
    
    # Latency e jitter de rede
    base_latency_ms: float = 50.0    # 50ms latência base
    latency_jitter_ms: float = 20.0  # ±20ms jitter
    
    # Custos de funding para posições overnight
    funding_rate_daily_bps: float = 1.0  # 0.01% ao dia


@dataclass
class MarketConditions:
    """Condições de mercado que afetam custos de execução."""
    
    volatility: float = 0.02         # Volatilidade atual (2%)
    spread_bps: float = 5.0          # Spread bid-ask (0.05%)
    volume_ratio: float = 1.0        # Ratio do volume atual vs médio
    market_impact: float = 0.1       # Impacto de mercado estimado


@dataclass
class RealisticBacktestResult:
    """Resultado de backtest realista com custos detalhados."""
    
    # Métricas básicas
    initial_capital: float
    final_capital: float
    total_return_pct: float
    
    # Métricas de risco-retorno
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    max_drawdown_pct: float
    volatility: float
    
    # Métricas de trading
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    profit_factor: float
    
    # Custos detalhados
    total_fees: float
    total_slippage: float
    total_latency_cost: float
    total_funding_cost: float
    total_transaction_costs: float
    
    # Performance ajustada por custos
    gross_pnl: float
    net_pnl: float
    cost_ratio_pct: float  # % do PnL perdido em custos
    
    # Metadados
    backtest_duration_hours: int
    avg_trade_duration_minutes: float
    execution_timestamp: str


class RealisticBacktester:
    """
    Simulador de backtest realista com custos de transação completos.
    Calibra expectativas antes de aplicar dinheiro real.
    """
    
    def __init__(self, transaction_costs: Optional[TransactionCosts] = None):
        self.costs = transaction_costs or TransactionCosts()
        self.trade_history: List[Dict] = []
        
        logger.info("🎯 RealisticBacktester inicializado")
        logger.info(f"   📊 Maker fee: {self.costs.maker_fee_bps:.1f} bps")
        logger.info(f"   📊 Taker fee: {self.costs.taker_fee_bps:.1f} bps")
        logger.info(f"   📊 Base slippage: {self.costs.base_slippage_bps:.1f} bps")
        logger.info(f"   📊 Base latency: {self.costs.base_latency_ms:.1f} ms")
    
    def calculate_slippage(self, trade_size: float, market_conditions: MarketConditions) -> float:
        """
        Calcula slippage realista baseado em tamanho do trade e condições de mercado.
        """
        # Slippage base
        base_slippage = self.costs.base_slippage_bps / 10000.0
        
        # Ajuste por volatilidade
        volatility_multiplier = 1.0 + (market_conditions.volatility / 0.02)  # Normalizado em 2%
        
        # Ajuste por volume (menos volume = mais slippage)
        volume_multiplier = 1.0 + (1.0 / max(market_conditions.volume_ratio, 0.1))
        
        # Ajuste por tamanho do trade
        size_multiplier = 1.0 + (abs(trade_size) * self.costs.volume_impact_factor)
        
        # Slippage final
        total_slippage = base_slippage * volatility_multiplier * volume_multiplier * size_multiplier
        
        return min(total_slippage, 0.01)  # Cap em 1%
    
    def calculate_latency_cost(self, trade_size: float, price_change_during_latency: float) -> float:
        """
        Calcula custo de latência baseado na mudança de preço durante o delay.
        """
        # Simular latência real
        actual_latency_ms = self.costs.base_latency_ms + np.random.normal(0, self.costs.latency_jitter_ms)
        actual_latency_ms = max(10.0, actual_latency_ms)  # Mínimo 10ms
        
        # Custo = mudança de preço adversa durante latência
        latency_cost = abs(trade_size) * abs(price_change_during_latency) * (actual_latency_ms / 1000.0)
        
        return latency_cost
    
    def execute_trade(
        self, 
        position_change: float, 
        price: float, 
        market_conditions: MarketConditions,
        is_maker: bool = False
    ) -> Dict[str, float]:
        """
        Executa trade com custos realistas.
        
        Args:
            position_change: Mudança na posição (-1 a 1)
            price: Preço atual
            market_conditions: Condições de mercado
            is_maker: Se é ordem maker (menor fee)
        
        Returns:
            Dict com custos detalhados
        """
        if abs(position_change) < 0.001:  # Trade muito pequeno
            return {
                'fee_cost': 0.0,
                'slippage_cost': 0.0,
                'latency_cost': 0.0,
                'total_cost': 0.0,
                'effective_price': price
            }
        
        # Calcular fee
        fee_bps = self.costs.maker_fee_bps if is_maker else self.costs.taker_fee_bps
        fee_cost = abs(position_change) * price * (fee_bps / 10000.0)
        
        # Calcular slippage
        slippage_rate = self.calculate_slippage(position_change, market_conditions)
        slippage_cost = abs(position_change) * price * slippage_rate
        
        # Simular mudança de preço durante latência (baseado em volatilidade)
        price_change_during_latency = np.random.normal(0, market_conditions.volatility * 0.1)
        latency_cost = self.calculate_latency_cost(position_change, price_change_during_latency)
        
        # Preço efetivo após slippage
        slippage_direction = 1 if position_change > 0 else -1  # Slippage sempre adverso
        effective_price = price * (1 + slippage_direction * slippage_rate)
        
        total_cost = fee_cost + slippage_cost + latency_cost
        
        return {
            'fee_cost': fee_cost,
            'slippage_cost': slippage_cost,
            'latency_cost': latency_cost,
            'total_cost': total_cost,
            'effective_price': effective_price,
            'fee_bps': fee_bps,
            'slippage_bps': slippage_rate * 10000,
            'latency_ms': self.costs.base_latency_ms + np.random.normal(0, self.costs.latency_jitter_ms)
        }
    
    def run_realistic_backtest(
        self,
        positions: np.ndarray,
        prices: np.ndarray,
        timestamps: Optional[np.ndarray] = None,
        initial_capital: float = 10000.0,
        market_conditions: Optional[MarketConditions] = None
    ) -> RealisticBacktestResult:
        """
        Executa backtest realista com custos completos.
        
        Args:
            positions: Array de posições (-1 a 1)
            prices: Array de preços correspondentes
            timestamps: Timestamps opcionais
            initial_capital: Capital inicial
            market_conditions: Condições de mercado (usa padrão se None)
        
        Returns:
            RealisticBacktestResult com métricas detalhadas
        """
        start_time = time.time()
        
        if market_conditions is None:
            market_conditions = MarketConditions()
        
        if len(positions) != len(prices):
            raise ValueError("positions e prices devem ter o mesmo tamanho")
        
        if len(positions) < 2:
            raise ValueError("Necessário pelo menos 2 pontos de dados")
        
        logger.info(f"🚀 Iniciando backtest realista com {len(positions)} pontos")
        logger.info(f"   💰 Capital inicial: ${initial_capital:,.2f}")
        
        # Inicializar variáveis
        capital = initial_capital
        current_position = 0.0
        trade_count = 0
        winning_trades = 0
        losing_trades = 0
        
        # Arrays para métricas
        capital_history = [capital]
        returns = []
        trade_pnls = []
        
        # Custos acumulados
        total_fees = 0.0
        total_slippage = 0.0
        total_latency_cost = 0.0
        total_funding_cost = 0.0
        
        # Histórico de trades
        self.trade_history = []
        
        # Loop principal do backtest
        for i in range(1, len(positions)):
            target_position = positions[i]
            current_price = prices[i]
            position_change = target_position - current_position
            
            # Executar trade se houver mudança significativa
            if abs(position_change) > 0.001:
                trade_costs = self.execute_trade(
                    position_change, 
                    current_price, 
                    market_conditions,
                    is_maker=np.random.random() > 0.7  # 30% maker orders
                )
                
                # Atualizar custos
                total_fees += trade_costs['fee_cost']
                total_slippage += trade_costs['slippage_cost']
                total_latency_cost += trade_costs['latency_cost']
                
                # Registrar trade
                trade_record = {
                    'timestamp': timestamps[i] if timestamps is not None else i,
                    'position_change': position_change,
                    'price': current_price,
                    'effective_price': trade_costs['effective_price'],
                    'costs': trade_costs,
                    'capital_before': capital
                }
                
                self.trade_history.append(trade_record)
                trade_count += 1
                
                # Aplicar custos ao capital
                capital -= trade_costs['total_cost']
                current_position = target_position
            
            # Calcular retorno do período
            if i > 0:
                price_return = (prices[i] - prices[i-1]) / prices[i-1]
                strategy_return = current_position * price_return
                
                # Aplicar retorno ao capital
                capital *= (1 + strategy_return)
                
                # Custo de funding para posições overnight (simplificado)
                if abs(current_position) > 0.1:
                    funding_cost = abs(current_position) * capital * (self.costs.funding_rate_daily_bps / 10000.0) / 24
                    capital -= funding_cost
                    total_funding_cost += funding_cost
                
                returns.append(strategy_return)
                capital_history.append(capital)
                
                # Registrar PnL do trade se posição fechou
                if abs(current_position) < 0.1 and len(self.trade_history) > 0:
                    trade_pnl = capital - self.trade_history[-1]['capital_before']
                    trade_pnls.append(trade_pnl)
                    
                    if trade_pnl > 0:
                        winning_trades += 1
                    else:
                        losing_trades += 1
        
        # Calcular métricas finais
        execution_time = time.time() - start_time
        
        result = self._calculate_final_metrics(
            initial_capital=initial_capital,
            final_capital=capital,
            returns=np.array(returns),
            capital_history=np.array(capital_history),
            trade_pnls=trade_pnls,
            total_trades=trade_count,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            total_fees=total_fees,
            total_slippage=total_slippage,
            total_latency_cost=total_latency_cost,
            total_funding_cost=total_funding_cost,
            backtest_duration_hours=len(positions),
            execution_time=execution_time
        )
        
        logger.info("✅ Backtest realista concluído")
        logger.info(f"   📈 Retorno total: {result.total_return_pct:.2f}%")
        logger.info(f"   💸 Custos totais: ${result.total_transaction_costs:.2f} ({result.cost_ratio_pct:.2f}%)")
        logger.info(f"   📊 Sharpe ratio: {result.sharpe_ratio:.3f}")
        logger.info(f"   🎯 Win rate: {result.win_rate:.1%}")
        
        return result

    def _calculate_final_metrics(
        self,
        initial_capital: float,
        final_capital: float,
        returns: np.ndarray,
        capital_history: np.ndarray,
        trade_pnls: List[float],
        total_trades: int,
        winning_trades: int,
        losing_trades: int,
        total_fees: float,
        total_slippage: float,
        total_latency_cost: float,
        total_funding_cost: float,
        backtest_duration_hours: int,
        execution_time: float
    ) -> RealisticBacktestResult:
        """Calcula métricas finais do backtest."""

        # Métricas básicas
        total_return_pct = ((final_capital - initial_capital) / initial_capital) * 100

        # Métricas de risco-retorno
        if len(returns) > 1:
            returns_std = np.std(returns)
            returns_mean = np.mean(returns)

            sharpe_ratio = (returns_mean / returns_std) * np.sqrt(8760) if returns_std > 0 else 0  # Anualizado

            # Sortino ratio (apenas downside deviation)
            downside_returns = returns[returns < 0]
            downside_std = np.std(downside_returns) if len(downside_returns) > 0 else returns_std
            sortino_ratio = (returns_mean / downside_std) * np.sqrt(8760) if downside_std > 0 else 0

            volatility = returns_std * np.sqrt(8760)  # Anualizada
        else:
            sharpe_ratio = sortino_ratio = volatility = 0.0

        # Max drawdown
        peak = np.maximum.accumulate(capital_history)
        drawdown = (capital_history - peak) / peak
        max_drawdown_pct = abs(np.min(drawdown)) * 100

        # Calmar ratio
        calmar_ratio = (total_return_pct / max_drawdown_pct) if max_drawdown_pct > 0 else 0

        # Métricas de trading
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        # Profit factor
        winning_pnl = sum(pnl for pnl in trade_pnls if pnl > 0)
        losing_pnl = abs(sum(pnl for pnl in trade_pnls if pnl < 0))
        profit_factor = winning_pnl / losing_pnl if losing_pnl > 0 else float('inf')

        # Custos detalhados
        total_transaction_costs = total_fees + total_slippage + total_latency_cost + total_funding_cost
        gross_pnl = final_capital - initial_capital + total_transaction_costs
        net_pnl = final_capital - initial_capital
        cost_ratio_pct = (total_transaction_costs / abs(gross_pnl)) * 100 if gross_pnl != 0 else 0

        # Duração média dos trades
        avg_trade_duration_minutes = (backtest_duration_hours * 60) / total_trades if total_trades > 0 else 0

        return RealisticBacktestResult(
            initial_capital=initial_capital,
            final_capital=final_capital,
            total_return_pct=total_return_pct,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            calmar_ratio=calmar_ratio,
            max_drawdown_pct=max_drawdown_pct,
            volatility=volatility,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            profit_factor=profit_factor,
            total_fees=total_fees,
            total_slippage=total_slippage,
            total_latency_cost=total_latency_cost,
            total_funding_cost=total_funding_cost,
            total_transaction_costs=total_transaction_costs,
            gross_pnl=gross_pnl,
            net_pnl=net_pnl,
            cost_ratio_pct=cost_ratio_pct,
            backtest_duration_hours=backtest_duration_hours,
            avg_trade_duration_minutes=avg_trade_duration_minutes,
            execution_timestamp=datetime.now().isoformat()
        )

    def export_results_json(self, result: RealisticBacktestResult, filepath: str) -> None:
        """Exporta resultados para JSON."""
        result_dict = {
            'metadata': {
                'execution_timestamp': result.execution_timestamp,
                'backtest_duration_hours': result.backtest_duration_hours,
                'total_trades': result.total_trades,
                'avg_trade_duration_minutes': result.avg_trade_duration_minutes
            },
            'performance': {
                'initial_capital': result.initial_capital,
                'final_capital': result.final_capital,
                'total_return_pct': result.total_return_pct,
                'gross_pnl': result.gross_pnl,
                'net_pnl': result.net_pnl
            },
            'risk_metrics': {
                'sharpe_ratio': result.sharpe_ratio,
                'sortino_ratio': result.sortino_ratio,
                'calmar_ratio': result.calmar_ratio,
                'max_drawdown_pct': result.max_drawdown_pct,
                'volatility': result.volatility
            },
            'trading_metrics': {
                'total_trades': result.total_trades,
                'winning_trades': result.winning_trades,
                'losing_trades': result.losing_trades,
                'win_rate': result.win_rate,
                'profit_factor': result.profit_factor
            },
            'cost_breakdown': {
                'total_fees': result.total_fees,
                'total_slippage': result.total_slippage,
                'total_latency_cost': result.total_latency_cost,
                'total_funding_cost': result.total_funding_cost,
                'total_transaction_costs': result.total_transaction_costs,
                'cost_ratio_pct': result.cost_ratio_pct
            },
            'trade_history': self.trade_history[-100:]  # Últimos 100 trades
        }

        with open(filepath, 'w') as f:
            json.dump(result_dict, f, indent=2, default=str)

        logger.info(f"📄 Resultados exportados para {filepath}")


def run_backtest_with_realistic_costs(
    strategy_positions: np.ndarray,
    market_prices: np.ndarray,
    initial_capital: float = 10000.0,
    transaction_costs: Optional[TransactionCosts] = None,
    market_conditions: Optional[MarketConditions] = None,
    export_json: Optional[str] = None
) -> Dict[str, Any]:
    """
    Função de conveniência para executar backtest realista.

    Args:
        strategy_positions: Posições da estratégia (-1 a 1)
        market_prices: Preços de mercado
        initial_capital: Capital inicial
        transaction_costs: Custos de transação customizados
        market_conditions: Condições de mercado customizadas
        export_json: Caminho para exportar JSON (opcional)

    Returns:
        Dict com métricas em formato JSON-compatível
    """
    backtester = RealisticBacktester(transaction_costs)

    result = backtester.run_realistic_backtest(
        positions=strategy_positions,
        prices=market_prices,
        initial_capital=initial_capital,
        market_conditions=market_conditions
    )

    # Exportar se solicitado
    if export_json:
        backtester.export_results_json(result, export_json)

    # Retornar em formato compatível com sistema existente
    return {
        'final_capital': result.final_capital,
        'total_pnl_pct': result.total_return_pct,
        'sharpe_ratio': result.sharpe_ratio,
        'max_drawdown_pct': result.max_drawdown_pct,
        'win_rate': result.win_rate,
        'total_trades': result.total_trades,
        'volatility': result.volatility,
        'sortino_ratio': result.sortino_ratio,
        'calmar_ratio': result.calmar_ratio,
        'profit_factor': result.profit_factor,

        # Custos detalhados (novo)
        'total_transaction_costs': result.total_transaction_costs,
        'cost_breakdown': {
            'fees': result.total_fees,
            'slippage': result.total_slippage,
            'latency': result.total_latency_cost,
            'funding': result.total_funding_cost
        },
        'cost_ratio_pct': result.cost_ratio_pct,
        'gross_pnl': result.gross_pnl,
        'net_pnl': result.net_pnl,

        # Metadados
        'realistic_simulation': True,
        'execution_timestamp': result.execution_timestamp
    }
