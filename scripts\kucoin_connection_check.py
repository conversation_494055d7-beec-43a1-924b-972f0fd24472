#!/usr/bin/env python
"""Quick connectivity check for <PERSON><PERSON><PERSON><PERSON> using ccxt.

Set KUCOIN_API_KEY, KUCOIN_SECRET_KEY and KUCOIN_PASSPHRASE in the environment.
"""
import ccxt.async_support as ccxt
import asyncio
import os


async def main():
    exchange = ccxt.kucoin(
        {
            "apiKey": os.environ.get("KUCOIN_API_KEY"),
            "secret": os.environ.get("KUCOIN_SECRET_KEY"),
            "password": os.environ.get("KUCOIN_PASSPHRASE"),
            "enableRateLimit": True,
        }
    )
    try:
        print("Tentando carregar mercados Kucoin...")
        markets = await exchange.load_markets()
        print("Mercados carregados:", list(markets.keys())[:5])
    except Exception as e:
        print("Erro ao conectar:", e)
    finally:
        await exchange.close()


if __name__ == "__main__":
    asyncio.run(main())
