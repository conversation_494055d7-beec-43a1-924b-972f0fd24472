#!/usr/bin/env python3
"""
QUALIA A/B Testing Framework - D-07.6 Simple Integration Test

Teste simplificado para validar componentes principais do D-07.6
sem dependências complexas.
"""

import asyncio
import sys
import logging
from pathlib import Path
from datetime import datetime
import json
import tempfile

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)


class SimpleD07_6_Tester:
    """Tester simplificado para D-07.6."""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp(prefix="d07_6_simple_")
        self.test_results = []
        
        logger.info(f"🧪 SimpleD07_6_Tester inicializado (temp: {self.temp_dir})")
    
    async def run_simple_tests(self) -> dict:
        """Executa testes simplificados."""
        logger.info("🚀 Iniciando testes simplificados D-07.6...")
        
        start_time = datetime.now()
        
        # Lista de testes simplificados
        simple_tests = [
            ("Import Tests", self._test_imports),
            ("Basic Logging", self._test_basic_logging),
            ("Configuration", self._test_configuration),
            ("File Creation", self._test_file_creation),
            ("Error Handling", self._test_error_handling)
        ]
        
        passed_tests = 0
        failed_tests = 0
        
        for test_name, test_func in simple_tests:
            try:
                logger.info(f"🧪 Executando: {test_name}")
                result = await test_func()
                
                if result["success"]:
                    logger.info(f"✅ {test_name}: PASSOU - {result['details']}")
                    passed_tests += 1
                else:
                    logger.error(f"❌ {test_name}: FALHOU - {result['details']}")
                    failed_tests += 1
                
                self.test_results.append({
                    "test_name": test_name,
                    "success": result["success"],
                    "details": result["details"],
                    "timestamp": datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"💥 {test_name}: ERRO CRÍTICO - {e}")
                failed_tests += 1
                self.test_results.append({
                    "test_name": test_name,
                    "success": False,
                    "details": f"Erro crítico: {e}",
                    "timestamp": datetime.now().isoformat()
                })
        
        total_duration = (datetime.now() - start_time).total_seconds()
        success_rate = (passed_tests / len(simple_tests)) * 100
        
        # Gerar relatório final
        final_report = {
            "test_suite": "D-07.6 Simple Integration Tests",
            "total_tests": len(simple_tests),
            "passed": passed_tests,
            "failed": failed_tests,
            "success_rate": success_rate,
            "total_duration_seconds": total_duration,
            "timestamp": datetime.now().isoformat(),
            "test_results": self.test_results,
            "production_ready": success_rate >= 80.0,
            "temp_directory": self.temp_dir
        }
        
        logger.info(f"📊 Testes simplificados finalizados: {passed_tests}/{len(simple_tests)} passaram ({success_rate:.1f}%)")
        
        return final_report
    
    async def _test_imports(self) -> dict:
        """Testa importação dos módulos principais."""
        try:
            # Testar imports principais
            from qualia.ab_testing.system_integration import QualiaABTestingIntegration, SystemIntegrationConfig
            from qualia.ab_testing.end_to_end_testing import EndToEndTestFramework
            from qualia.ab_testing.production_logging import ProductionLogger, ProductionErrorHandler
            
            return {
                "success": True,
                "details": "Todos os módulos importados com sucesso"
            }
            
        except Exception as e:
            return {
                "success": False,
                "details": f"Erro na importação: {e}"
            }
    
    async def _test_basic_logging(self) -> dict:
        """Testa sistema básico de logging."""
        try:
            from qualia.ab_testing.production_logging import ProductionLogger
            
            # Criar logger
            prod_logger = ProductionLogger(
                log_dir=f"{self.temp_dir}/logs",
                enable_console=False,  # Desabilitar console para teste
                log_level=logging.INFO
            )
            
            # Testar logs
            prod_logger.info("Teste de log INFO")
            prod_logger.warning("Teste de log WARNING")
            
            # Verificar se arquivos foram criados
            log_dir = Path(f"{self.temp_dir}/logs")
            log_files = list(log_dir.glob("*.log"))
            
            success = len(log_files) >= 1
            
            return {
                "success": success,
                "details": f"Arquivos de log criados: {len(log_files)}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "details": f"Erro no logging: {e}"
            }
    
    async def _test_configuration(self) -> dict:
        """Testa sistema de configuração."""
        try:
            from qualia.ab_testing.system_integration import SystemIntegrationConfig
            
            # Criar configuração
            config = SystemIntegrationConfig(
                ab_test_duration_hours=1,
                ab_test_symbols=["BTC/USDT"],
                monitoring_interval_seconds=10,
                report_storage_path=self.temp_dir
            )
            
            # Verificar atributos
            success = (
                config.ab_test_duration_hours == 1 and
                "BTC/USDT" in config.ab_test_symbols and
                config.monitoring_interval_seconds == 10
            )
            
            return {
                "success": success,
                "details": f"Configuração criada: {config.ab_test_symbols}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "details": f"Erro na configuração: {e}"
            }
    
    async def _test_file_creation(self) -> dict:
        """Testa criação de arquivos."""
        try:
            # Criar arquivo de teste
            test_file = Path(self.temp_dir) / "test_file.json"
            
            test_data = {
                "test": "D-07.6 Integration",
                "timestamp": datetime.now().isoformat(),
                "status": "testing"
            }
            
            with open(test_file, 'w') as f:
                json.dump(test_data, f, indent=2)
            
            # Verificar se arquivo foi criado
            success = test_file.exists() and test_file.stat().st_size > 0
            
            return {
                "success": success,
                "details": f"Arquivo criado: {test_file.name} ({test_file.stat().st_size} bytes)"
            }
            
        except Exception as e:
            return {
                "success": False,
                "details": f"Erro na criação de arquivo: {e}"
            }
    
    async def _test_error_handling(self) -> dict:
        """Testa tratamento básico de erros."""
        try:
            from qualia.ab_testing.production_logging import ProductionLogger, ProductionErrorHandler, ErrorSeverity
            
            # Criar logger e error handler
            prod_logger = ProductionLogger(
                log_dir=f"{self.temp_dir}/logs",
                enable_console=False,
                log_level=logging.INFO
            )
            
            error_handler = ProductionErrorHandler(prod_logger)
            
            # Simular erro
            test_error = ValueError("Erro de teste")
            
            error_context = await error_handler.handle_error(
                component="test_component",
                operation="test_operation",
                error=test_error,
                severity=ErrorSeverity.LOW
            )
            
            # Verificar se erro foi processado
            success = (
                error_context.component == "test_component" and
                error_context.error_type == "ValueError" and
                len(error_handler.error_history) > 0
            )
            
            return {
                "success": success,
                "details": f"Erro processado: {error_context.error_type}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "details": f"Erro no error handling: {e}"
            }


async def main():
    """Função principal."""
    print("🚀 QUALIA A/B TESTING FRAMEWORK - D-07.6 SIMPLE INTEGRATION TESTS")
    print("=" * 70)
    
    tester = SimpleD07_6_Tester()
    
    try:
        # Executar testes simplificados
        results = await tester.run_simple_tests()
        
        # Exibir resultados
        print("\n" + "=" * 70)
        print("📋 RESUMO DOS TESTES SIMPLIFICADOS D-07.6")
        print("=" * 70)
        
        for result in results["test_results"]:
            status = "✅ PASSOU" if result["success"] else "❌ FALHOU"
            print(f"{status}: {result['test_name']}")
            print(f"   {result['details']}")
        
        print(f"\nTaxa de Sucesso: {results['passed']}/{results['total_tests']} ({results['success_rate']:.1f}%)")
        
        if results["production_ready"]:
            print("🎉 D-07.6 System Integration & Testing - Componentes básicos funcionando!")
        else:
            print("⚠️ D-07.6 precisa de ajustes nos componentes básicos")
        
        print(f"📁 Arquivos temporários: {results['temp_directory']}")
        
        # Salvar relatório
        report_file = Path(results['temp_directory']) / "simple_test_report.json"
        with open(report_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"📊 Relatório salvo em: {report_file}")
        
    except Exception as e:
        print(f"💥 Erro crítico nos testes: {e}")
        return False
    
    return results["production_ready"]


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
