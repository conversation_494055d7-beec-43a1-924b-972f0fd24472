"""
QUALIA Advanced Pruning Configuration Loader - D-04

Carregador de configurações para o sistema de pruning avançado,
incluindo presets por regime de mercado e configurações multi-fidelidade.
"""

from __future__ import annotations

import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, field

from ..utils.logger import get_logger
from .advanced_pruning import PruningConfig, PruningStrategy, MarketRegime, MultiFidelityConfig

logger = get_logger(__name__)


@dataclass
class PruningConfigPresets:
    """Presets de configuração de pruning por regime de mercado."""
    
    bull: Dict[str, Any] = field(default_factory=dict)
    bear: Dict[str, Any] = field(default_factory=dict)
    volatile: Dict[str, Any] = field(default_factory=dict)
    stable: Dict[str, Any] = field(default_factory=dict)
    sideways: Dict[str, Any] = field(default_factory=dict)


class PruningConfigLoader:
    """
    Carregador de configurações para o sistema de pruning avançado.
    
    Carrega configurações do arquivo YAML e cria objetos de configuração
    apropriados para diferentes regimes de mercado.
    """
    
    def __init__(self, config_path: str = "config/bayesian_optimization.yaml"):
        self.config_path = Path(config_path)
        self.config_data: Dict[str, Any] = {}
        self.presets: PruningConfigPresets = PruningConfigPresets()
        
        self._load_config()
        self._parse_presets()
        
        logger.info(f"📋 PruningConfigLoader inicializado: {self.config_path}")
    
    def _load_config(self) -> None:
        """Carrega configuração do arquivo YAML."""
        
        try:
            if not self.config_path.exists():
                logger.warning(f"Arquivo de configuração não encontrado: {self.config_path}")
                self.config_data = self._get_default_config()
                return
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f) or {}
                
            logger.info(f"✅ Configuração carregada: {self.config_path}")
            
        except Exception as e:
            logger.error(f"Erro ao carregar configuração: {e}")
            self.config_data = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Retorna configuração padrão."""
        
        return {
            "pruning": {
                "strategy": "ADAPTIVE",
                "median_n_startup_trials": 10,
                "median_n_warmup_steps": 5,
                "median_interval_steps": 1,
                "median_percentile": 50.0,
                "sh_min_resource": 1,
                "sh_reduction_factor": 3,
                "sh_min_early_stopping_rate": 0,
                "min_sharpe_threshold": 0.3,
                "max_drawdown_threshold": 0.20,
                "min_trades_threshold": 10
            },
            "multi_fidelity": {
                "enabled": True,
                "fidelity_levels": [1, 6, 24],
                "budget_ratios": [0.5, 0.3, 0.2],
                "promotion_percentile": 0.3,
                "min_trials_for_promotion": 5
            }
        }
    
    def _parse_presets(self) -> None:
        """Parseia presets de configuração por regime."""
        
        pruning_config = self.config_data.get("pruning", {})
        regime_presets = pruning_config.get("regime_presets", {})
        
        # Mapear regimes
        regime_mapping = {
            "BULL": "bull",
            "BEAR": "bear", 
            "VOLATILE": "volatile",
            "STABLE": "stable",
            "SIDEWAYS": "sideways"
        }
        
        for regime_key, preset_key in regime_mapping.items():
            if regime_key in regime_presets:
                setattr(self.presets, preset_key, regime_presets[regime_key])
                logger.debug(f"📋 Preset carregado para regime {regime_key}")
    
    def get_pruning_config(self, market_regime: MarketRegime = MarketRegime.SIDEWAYS) -> PruningConfig:
        """
        Retorna configuração de pruning para um regime específico.
        
        Args:
            market_regime: Regime de mercado atual
            
        Returns:
            Configuração de pruning ajustada para o regime
        """
        
        base_config = self.config_data.get("pruning", {})
        
        # Aplicar preset do regime se disponível
        regime_preset = self._get_regime_preset(market_regime)
        if regime_preset:
            # Merge preset com configuração base
            merged_config = {**base_config, **regime_preset}
        else:
            merged_config = base_config
        
        # Converter strategy string para enum
        strategy_str = merged_config.get("strategy", "ADAPTIVE")
        try:
            strategy = PruningStrategy(strategy_str)
        except ValueError:
            logger.warning(f"Estratégia inválida: {strategy_str}, usando ADAPTIVE")
            strategy = PruningStrategy.ADAPTIVE
        
        # Criar configuração
        config = PruningConfig(
            strategy=strategy,
            median_n_startup_trials=merged_config.get("median_n_startup_trials", 10),
            median_n_warmup_steps=merged_config.get("median_n_warmup_steps", 5),
            median_interval_steps=merged_config.get("median_interval_steps", 1),
            median_percentile=merged_config.get("median_percentile", 50.0),
            sh_min_resource=merged_config.get("sh_min_resource", 1),
            sh_reduction_factor=merged_config.get("sh_reduction_factor", 3),
            sh_min_early_stopping_rate=merged_config.get("sh_min_early_stopping_rate", 0),
            min_sharpe_threshold=merged_config.get("min_sharpe_threshold", 0.3),
            max_drawdown_threshold=merged_config.get("max_drawdown_threshold", 0.20),
            min_trades_threshold=merged_config.get("min_trades_threshold", 10)
        )
        
        logger.info(f"🎯 Configuração de pruning criada para regime: {market_regime.value}")
        logger.debug(f"📊 Estratégia: {strategy.value}, Startup trials: {config.median_n_startup_trials}")
        
        return config
    
    def get_multi_fidelity_config(self, market_regime: MarketRegime = MarketRegime.SIDEWAYS) -> MultiFidelityConfig:
        """
        Retorna configuração multi-fidelidade para um regime específico.
        
        Args:
            market_regime: Regime de mercado atual
            
        Returns:
            Configuração multi-fidelidade ajustada para o regime
        """
        
        base_config = self.config_data.get("multi_fidelity", {})
        
        # Aplicar ajustes do regime se disponível
        regime_adjustments = base_config.get("regime_adjustments", {}).get(market_regime.value, {})
        merged_config = {**base_config, **regime_adjustments}
        
        config = MultiFidelityConfig(
            fidelity_levels=merged_config.get("fidelity_levels", [1, 6, 24]),
            budget_ratios=merged_config.get("budget_ratios", [0.5, 0.3, 0.2]),
            promotion_percentile=merged_config.get("promotion_percentile", 0.3),
            min_trials_for_promotion=merged_config.get("min_trials_for_promotion", 5),
            metrics_weights=merged_config.get("metrics_weights", {
                "sharpe_ratio": [0.4, 0.5, 0.6],
                "pnl": [0.3, 0.3, 0.3],
                "drawdown": [0.3, 0.2, 0.1]
            })
        )
        
        logger.info(f"🎯 Configuração multi-fidelidade criada para regime: {market_regime.value}")
        logger.debug(f"📊 Níveis: {config.fidelity_levels}, Promoção: {config.promotion_percentile:.1%}")
        
        return config
    
    def _get_regime_preset(self, market_regime: MarketRegime) -> Optional[Dict[str, Any]]:
        """Retorna preset para um regime específico."""
        
        regime_map = {
            MarketRegime.BULL: self.presets.bull,
            MarketRegime.BEAR: self.presets.bear,
            MarketRegime.VOLATILE: self.presets.volatile,
            MarketRegime.STABLE: self.presets.stable,
            MarketRegime.SIDEWAYS: self.presets.sideways
        }
        
        return regime_map.get(market_regime, {})
    
    def reload_config(self) -> None:
        """Recarrega configuração do arquivo."""
        
        logger.info("🔄 Recarregando configuração de pruning...")
        self._load_config()
        self._parse_presets()
        logger.info("✅ Configuração recarregada")
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Retorna resumo da configuração atual."""
        
        return {
            "config_path": str(self.config_path),
            "config_loaded": bool(self.config_data),
            "available_presets": {
                "bull": bool(self.presets.bull),
                "bear": bool(self.presets.bear),
                "volatile": bool(self.presets.volatile),
                "stable": bool(self.presets.stable),
                "sideways": bool(self.presets.sideways)
            },
            "pruning_strategy": self.config_data.get("pruning", {}).get("strategy", "ADAPTIVE"),
            "multi_fidelity_enabled": self.config_data.get("multi_fidelity", {}).get("enabled", True)
        }


# Singleton instance
_config_loader: Optional[PruningConfigLoader] = None


def get_pruning_config_loader() -> PruningConfigLoader:
    """Retorna instância singleton do carregador de configuração."""
    
    global _config_loader
    if _config_loader is None:
        _config_loader = PruningConfigLoader()
    
    return _config_loader
