"""
QUALIA Live Feed - Módulo Principal

Permite execução do sistema de live feed como módulo Python.
"""

import asyncio
import sys
from .run_tests import main as run_tests_main


async def main():
    """Função principal do módulo."""
    if len(sys.argv) > 1 and sys.argv[1] == 'setup':
        # Executar setup de credenciais
        from .setup_credentials import main as setup_main
        await setup_main()
    elif len(sys.argv) > 1 and sys.argv[1] == 'example':
        # Executar exemplo
        from .example_usage import main as example_main
        await example_main()
    else:
        # Executar testes por padrão
        await run_tests_main()


if __name__ == "__main__":
    asyncio.run(main())
