#!/usr/bin/env python3
"""
Teste de Integração das Otimizações no Sistema de Trading Real
YAA IMPLEMENTATION: Valida se todas as otimizações foram integradas corretamente.
"""

import sys
import json
import asyncio
from pathlib import Path

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

from scripts.start_optimized_trading import OptimizedQUALIATradingSystem
from scripts.start_real_trading import QUALIATradingSystem
from src.production_optimizer import ProductionOptimizer
from qualia.config.config_loader import load_env_and_json

def test_configuration_loading():
    """Testa se a configuração otimizada é carregada corretamente."""
    
    print("🔍 TESTE 1: Carregamento de Configuração Otimizada")
    print("=" * 60)
    
    try:
        # Carregar configuração otimizada
        config = load_env_and_json(json_path="config/production_config.json")
        
        # Verificar parâmetros otimizados
        base_params = config.get("base_params", {})
        
        tests = [
            ("news_amplification", 11.3, "Fator mais impactante (+463.2%)"),
            ("price_amplification", 1.0, "Otimizado (-51.4%)"),
            ("min_confidence", 0.37, "Ajustado (-7.0%)"),
        ]
        
        all_passed = True
        for param, expected, description in tests:
            actual = base_params.get(param)
            if actual == expected:
                print(f"   ✅ {param}: {actual} - {description}")
            else:
                print(f"   ❌ {param}: {actual} (esperado: {expected}) - {description}")
                all_passed = False
        
        # Verificar configurações de produção
        opt_settings = config.get("optimization_settings", {})
        trials = opt_settings.get("n_trials_per_cycle", 0)
        symbols = len(config.get("symbols", []))
        
        if trials == 25:
            print(f"   ✅ n_trials_per_cycle: {trials} (produção)")
        else:
            print(f"   ❌ n_trials_per_cycle: {trials} (esperado: 25)")
            all_passed = False
        
        if symbols == 8:
            print(f"   ✅ símbolos: {symbols} (diversificação)")
        else:
            print(f"   ❌ símbolos: {symbols} (esperado: 8)")
            all_passed = False
        
        if all_passed:
            print("🏆 TESTE 1 PASSOU: Configuração otimizada carregada corretamente!")
        else:
            print("❌ TESTE 1 FALHOU: Problemas na configuração otimizada")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ TESTE 1 FALHOU: Erro ao carregar configuração: {e}")
        return False

def test_production_optimizer_integration():
    """Testa se o ProductionOptimizer pode ser integrado."""
    
    print("\n🔍 TESTE 2: Integração do ProductionOptimizer")
    print("=" * 60)
    
    try:
        # Tentar importar e instanciar ProductionOptimizer
        optimizer = ProductionOptimizer()
        
        if optimizer:
            print("   ✅ ProductionOptimizer importado com sucesso")
            print("   ✅ Instância criada corretamente")
            print("🏆 TESTE 2 PASSOU: ProductionOptimizer pode ser integrado!")
            return True
        else:
            print("   ❌ Falha ao criar instância do ProductionOptimizer")
            return False
            
    except Exception as e:
        print(f"❌ TESTE 2 FALHOU: Erro na integração: {e}")
        return False

async def test_optimized_system_initialization():
    """Testa se o sistema otimizado inicializa corretamente."""
    
    print("\n🔍 TESTE 3: Inicialização do Sistema Otimizado")
    print("=" * 60)
    
    try:
        # Carregar configuração otimizada
        config = load_env_and_json(json_path="config/production_config.json")
        
        # Criar sistema otimizado (sem inicializar completamente)
        system = OptimizedQUALIATradingSystem(
            config=config,
            config_path="config/production_config.json",
            mode="paper_trading"  # Usar paper trading para teste
        )
        
        # Verificar se atributos foram configurados
        tests = [
            (hasattr(system, 'production_optimizer'), "ProductionOptimizer attribute"),
            (hasattr(system, 'optimization_discoveries_applied'), "Optimization discoveries flag"),
            (system.config is not None, "Configuration loaded"),
            (system.config_path.endswith("production_config.json"), "Optimized config path"),
        ]
        
        all_passed = True
        for test_result, description in tests:
            if test_result:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description}")
                all_passed = False
        
        # Verificar se descobertas podem ser aplicadas
        system._apply_optimization_discoveries()
        
        if system.optimization_discoveries_applied:
            print("   ✅ Descobertas aplicadas com sucesso")
        else:
            print("   ❌ Falha ao aplicar descobertas")
            all_passed = False
        
        if all_passed:
            print("🏆 TESTE 3 PASSOU: Sistema otimizado inicializa corretamente!")
        else:
            print("❌ TESTE 3 FALHOU: Problemas na inicialização")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ TESTE 3 FALHOU: Erro na inicialização: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_original_system_auto_optimization():
    """Testa se o sistema original detecta e carrega otimizações automaticamente."""
    
    print("\n🔍 TESTE 4: Auto-detecção de Otimizações no Sistema Original")
    print("=" * 60)
    
    try:
        # Criar sistema original sem configuração específica
        system = QUALIATradingSystem(
            mode="paper_trading"
        )
        
        # Verificar se detectou configuração otimizada
        if system.use_optimized_config:
            print("   ✅ Configuração otimizada detectada automaticamente")
        else:
            print("   ❌ Configuração otimizada não detectada")
            return False
        
        # Verificar se parâmetros otimizados foram carregados
        base_params = system.config.get("base_params", {})
        
        tests = [
            (base_params.get("news_amplification") == 11.3, "news_amplification otimizado"),
            (base_params.get("price_amplification") == 1.0, "price_amplification otimizado"),
            (base_params.get("min_confidence") == 0.37, "min_confidence otimizado"),
        ]
        
        all_passed = True
        for test_result, description in tests:
            if test_result:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description}")
                all_passed = False
        
        if all_passed:
            print("🏆 TESTE 4 PASSOU: Sistema original detecta otimizações automaticamente!")
        else:
            print("❌ TESTE 4 FALHOU: Auto-detecção não funcionou")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ TESTE 4 FALHOU: Erro na auto-detecção: {e}")
        return False

def test_integration_completeness():
    """Testa se a integração está completa e funcional."""
    
    print("\n🔍 TESTE 5: Completude da Integração")
    print("=" * 60)
    
    try:
        # Verificar arquivos necessários
        required_files = [
            "config/production_config.json",
            "src/production_optimizer.py",
            "scripts/start_optimized_trading.py",
            "docs/optimization_discoveries.md",
        ]
        
        all_files_exist = True
        for file_path in required_files:
            if Path(file_path).exists():
                print(f"   ✅ {file_path}")
            else:
                print(f"   ❌ {file_path} (não encontrado)")
                all_files_exist = False
        
        # Verificar se scripts são executáveis
        scripts_executable = True
        try:
            # Verificar imports dos scripts principais
            from scripts.start_optimized_trading import OptimizedQUALIATradingSystem
            from scripts.start_real_trading import QUALIATradingSystem
            print("   ✅ Scripts principais importáveis")
        except Exception as e:
            print(f"   ❌ Erro ao importar scripts: {e}")
            scripts_executable = False
        
        if all_files_exist and scripts_executable:
            print("🏆 TESTE 5 PASSOU: Integração completa e funcional!")
            return True
        else:
            print("❌ TESTE 5 FALHOU: Integração incompleta")
            return False
        
    except Exception as e:
        print(f"❌ TESTE 5 FALHOU: Erro na verificação: {e}")
        return False

async def main():
    """Executa todos os testes de integração."""
    
    print("🧪 TESTES DE INTEGRAÇÃO DAS OTIMIZAÇÕES")
    print("=" * 80)
    print("Validando se todas as otimizações foram integradas corretamente...")
    print("=" * 80)
    
    # Executar todos os testes
    tests = [
        ("Configuração Otimizada", test_configuration_loading()),
        ("ProductionOptimizer", test_production_optimizer_integration()),
        ("Sistema Otimizado", await test_optimized_system_initialization()),
        ("Auto-detecção", test_original_system_auto_optimization()),
        ("Completude", test_integration_completeness()),
    ]
    
    # Resumo dos resultados
    print("\n" + "=" * 80)
    print("📊 RESUMO DOS TESTES")
    print("=" * 80)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in tests:
        if result:
            print(f"✅ {test_name}: PASSOU")
            passed += 1
        else:
            print(f"❌ {test_name}: FALHOU")
    
    print(f"\n📈 RESULTADO FINAL: {passed}/{total} testes passaram")
    
    if passed == total:
        print("🏆 SUCESSO COMPLETO: Todas as otimizações foram integradas!")
        print("\n🚀 COMANDOS PARA USAR O SISTEMA OTIMIZADO:")
        print("   # Sistema otimizado dedicado")
        print("   python scripts/start_optimized_trading.py --mode paper_trading")
        print("   ")
        print("   # Sistema original com auto-detecção")
        print("   python scripts/start_real_trading.py --mode paper_trading")
        print("   ")
        print("   # Produção com otimizações")
        print("   python scripts/start_optimized_trading.py --mode live")
    else:
        print("❌ FALHAS DETECTADAS: Algumas otimizações não foram integradas corretamente")
        print("   Verifique os erros acima e corrija antes de usar em produção")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
