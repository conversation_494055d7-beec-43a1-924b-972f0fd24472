# KrakenIntegration Ticker Strategy

A `CryptoDataFetcher` agora possui o método `watch_ticker` para recuperar
cotações via WebSocket quando a exchange disponibiliza o recurso. O
método aplica um timeout configurável e valida a presença de campos como
`last` e `close`. Caso o WebSocket esteja indisponível ou ocorra algum
erro, o código faz fallback para `fetch_ticker` usando a API REST,
respeitando o limite de requisições e utilizando backoff exponencial.

Essa abordagem reduz a latência na obtenção de preços em tempo real e
permite chamadas assíncronas concorrentes, evitando atrasos
sequenciais quando vários pares de negociação são monitorados.
