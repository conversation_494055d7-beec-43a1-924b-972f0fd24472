#!/usr/bin/env python3
"""
Quantum Momentum Strategy Performance Monitor
Tracks optimized strategy performance and alerts on deviations.
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path

import pandas as pd
import numpy as np

from qualia.utils.logger import get_logger


@dataclass
class PerformanceMetrics:
    """Performance metrics for monitoring."""
    timestamp: datetime
    total_return_pct: float
    sharpe_ratio: float
    win_rate: float
    max_drawdown_pct: float
    total_trades: int
    profit_factor: float
    avg_win_pct: float
    avg_loss_pct: float
    win_loss_ratio: float


@dataclass
class AlertThresholds:
    """Alert thresholds for monitoring."""
    min_sharpe_ratio: float = 0.3
    min_win_rate: float = 50.0
    max_drawdown: float = 1.5
    min_trades_per_day: float = 0.5
    max_days_without_trades: int = 2


class QuantumMomentumMonitor:
    """Monitor for optimized Quantum Momentum strategy performance."""
    
    def __init__(
        self,
        strategy_name: str = "enhanced_quantum_momentum",
        alert_thresholds: Optional[AlertThresholds] = None,
        log_level: str = "INFO"
    ):
        self.strategy_name = strategy_name
        self.alert_thresholds = alert_thresholds or AlertThresholds()
        self.logger = get_logger(__name__, log_level)
        
        # Performance tracking
        self.performance_history: List[PerformanceMetrics] = []
        self.alerts_sent: List[Dict[str, Any]] = []
        
        # Target metrics (from optimization)
        self.target_metrics = {
            'win_rate': 60.0,
            'sharpe_ratio': 0.5,
            'total_return_pct': 3.0,
            'max_drawdown_pct': 2.0
        }
        
        # Optimized baseline (from testing)
        self.optimized_baseline = {
            'win_rate': 56.6,
            'sharpe_ratio': 2.254,
            'total_return_pct': 2.01,
            'max_drawdown_pct': 0.92
        }
    
    def calculate_performance_metrics(
        self,
        trades_data: pd.DataFrame,
        current_time: Optional[datetime] = None
    ) -> PerformanceMetrics:
        """Calculate current performance metrics."""
        
        if current_time is None:
            current_time = datetime.now()
        
        if trades_data.empty:
            self.logger.warning("No trades data available for performance calculation")
            return PerformanceMetrics(
                timestamp=current_time,
                total_return_pct=0.0,
                sharpe_ratio=0.0,
                win_rate=0.0,
                max_drawdown_pct=0.0,
                total_trades=0,
                profit_factor=0.0,
                avg_win_pct=0.0,
                avg_loss_pct=0.0,
                win_loss_ratio=0.0
            )
        
        # Calculate returns
        returns = trades_data['pnl_pct'].values
        returns_series = pd.Series(returns)
        
        # Basic metrics
        total_return = returns_series.sum()
        total_trades = len(returns)
        
        # Win/Loss analysis
        winning_trades = returns_series[returns_series > 0]
        losing_trades = returns_series[returns_series < 0]
        
        win_rate = len(winning_trades) / total_trades * 100 if total_trades > 0 else 0
        avg_win = winning_trades.mean() if len(winning_trades) > 0 else 0
        avg_loss = abs(losing_trades.mean()) if len(losing_trades) > 0 else 0
        win_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0
        
        # Profit factor
        total_wins = winning_trades.sum()
        total_losses = abs(losing_trades.sum())
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        # Risk metrics
        if len(returns) > 1:
            volatility = returns_series.std() * np.sqrt(252)
            sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
            
            # Drawdown calculation
            cumulative = (1 + returns_series).cumprod()
            rolling_max = cumulative.expanding().max()
            drawdowns = (cumulative - rolling_max) / rolling_max
            max_drawdown = abs(drawdowns.min()) * 100
        else:
            sharpe_ratio = 0.0
            max_drawdown = 0.0
        
        return PerformanceMetrics(
            timestamp=current_time,
            total_return_pct=total_return * 100,
            sharpe_ratio=sharpe_ratio,
            win_rate=win_rate,
            max_drawdown_pct=max_drawdown,
            total_trades=total_trades,
            profit_factor=profit_factor,
            avg_win_pct=avg_win * 100,
            avg_loss_pct=avg_loss * 100,
            win_loss_ratio=win_loss_ratio
        )
    
    def check_alerts(self, metrics: PerformanceMetrics) -> List[Dict[str, Any]]:
        """Check for alert conditions."""
        alerts = []
        
        # Sharpe ratio alert
        if metrics.sharpe_ratio < self.alert_thresholds.min_sharpe_ratio:
            alerts.append({
                'type': 'SHARPE_RATIO_LOW',
                'severity': 'HIGH',
                'message': f"Sharpe ratio {metrics.sharpe_ratio:.3f} below threshold {self.alert_thresholds.min_sharpe_ratio}",
                'current_value': metrics.sharpe_ratio,
                'threshold': self.alert_thresholds.min_sharpe_ratio,
                'timestamp': metrics.timestamp
            })
        
        # Win rate alert
        if metrics.win_rate < self.alert_thresholds.min_win_rate:
            alerts.append({
                'type': 'WIN_RATE_LOW',
                'severity': 'MEDIUM',
                'message': f"Win rate {metrics.win_rate:.1f}% below threshold {self.alert_thresholds.min_win_rate}%",
                'current_value': metrics.win_rate,
                'threshold': self.alert_thresholds.min_win_rate,
                'timestamp': metrics.timestamp
            })
        
        # Drawdown alert
        if metrics.max_drawdown_pct > self.alert_thresholds.max_drawdown:
            alerts.append({
                'type': 'DRAWDOWN_HIGH',
                'severity': 'CRITICAL',
                'message': f"Max drawdown {metrics.max_drawdown_pct:.2f}% exceeds threshold {self.alert_thresholds.max_drawdown}%",
                'current_value': metrics.max_drawdown_pct,
                'threshold': self.alert_thresholds.max_drawdown,
                'timestamp': metrics.timestamp
            })
        
        # Trade frequency alert
        if len(self.performance_history) > 0:
            days_since_last = (metrics.timestamp - self.performance_history[-1].timestamp).days
            if days_since_last > self.alert_thresholds.max_days_without_trades:
                alerts.append({
                    'type': 'NO_TRADES',
                    'severity': 'MEDIUM',
                    'message': f"No trades for {days_since_last} days (threshold: {self.alert_thresholds.max_days_without_trades})",
                    'current_value': days_since_last,
                    'threshold': self.alert_thresholds.max_days_without_trades,
                    'timestamp': metrics.timestamp
                })
        
        return alerts
    
    def generate_performance_report(self, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        
        # Compare to targets
        target_comparison = {}
        for key, target in self.target_metrics.items():
            current = getattr(metrics, key)
            target_comparison[key] = {
                'current': current,
                'target': target,
                'achievement_pct': (current / target * 100) if target > 0 else 0,
                'status': 'ACHIEVED' if current >= target else 'BELOW_TARGET'
            }
        
        # Compare to optimized baseline
        baseline_comparison = {}
        for key, baseline in self.optimized_baseline.items():
            current = getattr(metrics, key)
            baseline_comparison[key] = {
                'current': current,
                'baseline': baseline,
                'change_pct': ((current - baseline) / baseline * 100) if baseline != 0 else 0,
                'status': 'IMPROVED' if current >= baseline else 'DEGRADED'
            }
        
        # Performance trend (if history available)
        trend_analysis = {}
        if len(self.performance_history) >= 2:
            recent_metrics = self.performance_history[-5:]  # Last 5 measurements
            for key in ['sharpe_ratio', 'win_rate', 'total_return_pct']:
                values = [getattr(m, key) for m in recent_metrics]
                if len(values) >= 2:
                    trend = 'IMPROVING' if values[-1] > values[0] else 'DECLINING'
                    trend_analysis[key] = {
                        'trend': trend,
                        'change': values[-1] - values[0],
                        'values': values
                    }
        
        return {
            'timestamp': metrics.timestamp.isoformat(),
            'current_metrics': {
                'total_return_pct': metrics.total_return_pct,
                'sharpe_ratio': metrics.sharpe_ratio,
                'win_rate': metrics.win_rate,
                'max_drawdown_pct': metrics.max_drawdown_pct,
                'total_trades': metrics.total_trades,
                'profit_factor': metrics.profit_factor,
                'win_loss_ratio': metrics.win_loss_ratio
            },
            'target_comparison': target_comparison,
            'baseline_comparison': baseline_comparison,
            'trend_analysis': trend_analysis,
            'overall_status': self._determine_overall_status(metrics, target_comparison, baseline_comparison)
        }
    
    def _determine_overall_status(
        self,
        metrics: PerformanceMetrics,
        target_comparison: Dict,
        baseline_comparison: Dict
    ) -> str:
        """Determine overall strategy status."""
        
        # Critical checks
        if metrics.max_drawdown_pct > self.alert_thresholds.max_drawdown:
            return 'CRITICAL'
        
        if metrics.sharpe_ratio < self.alert_thresholds.min_sharpe_ratio:
            return 'WARNING'
        
        # Performance checks
        targets_achieved = sum(1 for comp in target_comparison.values() if comp['status'] == 'ACHIEVED')
        total_targets = len(target_comparison)
        
        baseline_improved = sum(1 for comp in baseline_comparison.values() if comp['status'] == 'IMPROVED')
        total_baseline = len(baseline_comparison)
        
        if targets_achieved >= total_targets * 0.75:  # 75% of targets achieved
            return 'EXCELLENT'
        elif baseline_improved >= total_baseline * 0.75:  # 75% improved from baseline
            return 'GOOD'
        elif targets_achieved >= total_targets * 0.5:  # 50% of targets achieved
            return 'ACCEPTABLE'
        else:
            return 'NEEDS_ATTENTION'
    
    def update_performance(self, trades_data: pd.DataFrame) -> Dict[str, Any]:
        """Update performance metrics and check for alerts."""
        
        # Calculate current metrics
        metrics = self.calculate_performance_metrics(trades_data)
        
        # Add to history
        self.performance_history.append(metrics)
        
        # Check for alerts
        alerts = self.check_alerts(metrics)
        
        # Log alerts
        for alert in alerts:
            self.logger.warning(f"ALERT [{alert['type']}]: {alert['message']}")
            self.alerts_sent.append(alert)
        
        # Generate report
        report = self.generate_performance_report(metrics)
        
        # Log performance summary
        self.logger.info(
            f"Performance Update - "
            f"Return: {metrics.total_return_pct:.2f}%, "
            f"Sharpe: {metrics.sharpe_ratio:.3f}, "
            f"Win Rate: {metrics.win_rate:.1f}%, "
            f"Status: {report['overall_status']}"
        )
        
        return {
            'metrics': metrics,
            'alerts': alerts,
            'report': report
        }
    
    def save_performance_history(self, filepath: str):
        """Save performance history to file."""
        if not self.performance_history:
            self.logger.warning("No performance history to save")
            return
        
        # Convert to DataFrame
        data = []
        for metrics in self.performance_history:
            data.append({
                'timestamp': metrics.timestamp,
                'total_return_pct': metrics.total_return_pct,
                'sharpe_ratio': metrics.sharpe_ratio,
                'win_rate': metrics.win_rate,
                'max_drawdown_pct': metrics.max_drawdown_pct,
                'total_trades': metrics.total_trades,
                'profit_factor': metrics.profit_factor,
                'win_loss_ratio': metrics.win_loss_ratio
            })
        
        df = pd.DataFrame(data)
        df.to_csv(filepath, index=False)
        self.logger.info(f"Performance history saved to {filepath}")


def main():
    """Example usage of the monitor."""
    monitor = QuantumMomentumMonitor()
    
    # Example trades data (replace with actual data source)
    example_trades = pd.DataFrame({
        'timestamp': pd.date_range('2025-01-01', periods=50, freq='H'),
        'pnl_pct': np.random.normal(0.001, 0.01, 50)  # Example returns
    })
    
    # Update performance
    result = monitor.update_performance(example_trades)
    
    print("Performance Report:")
    print(f"Status: {result['report']['overall_status']}")
    print(f"Alerts: {len(result['alerts'])}")
    
    # Save history
    monitor.save_performance_history("quantum_momentum_performance.csv")


if __name__ == "__main__":
    main()
