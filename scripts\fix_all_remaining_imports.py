#!/usr/bin/env python3
"""
Script para corrigir TODOS os imports incorretos restantes do tipo 'import src.qualia'
"""

import os
import re
import sys
from pathlib import Path


def fix_import_statements(file_path):
    """Corrige imports incorretos em um arquivo específico"""
    
    changes_made = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Padrões de import incorretos e suas correções
        patterns = [
            # from qualia.module import module as alias
            (r'import src\.qualia\.([^\s]+) as ([^\s\n]+)', r'from qualia.\1 import \1 as \2'),
            
            # from qualia import module
            (r'import src\.qualia\.([^\s\n]+)', r'from qualia import \1'),
            
            # from qualia.module import something
            (r'from src\.qualia\.([^\s]+) import', r'from qualia.\1 import'),
            
            # Casos especiais para imports diretos
            (r'import src\.qualia\.config as config_pkg', r'from qualia import config as config_pkg'),
            (r'import src\.qualia\.config as config_module', r'from qualia import config as config_module'),
            (r'import src\.qualia\.init_utils as init_utils', r'from qualia import init_utils'),
            (r'import src\.qualia\.utils\.([^\s]+) as ([^\s\n]+)', r'from qualia.utils.\1 import \1 as \2'),
            (r'import src\.qualia\.core\.([^\s]+) as ([^\s\n]+)', r'from qualia.core.\1 import \1 as \2'),
            (r'import src\.qualia\.memory\.([^\s]+) as ([^\s\n]+)', r'from qualia.memory.\1 import \1 as \2'),
            (r'import src\.qualia\.trader\.([^\s]+) as ([^\s\n]+)', r'from qualia.trader.\1 import \1 as \2'),
            (r'import src\.qualia\.ui\.([^\s]+) as ([^\s\n]+)', r'from qualia.ui.\1 import \1 as \2'),
            (r'import src\.qualia\.metacognition as ([^\s\n]+)', r'from qualia import metacognition as \1'),
        ]
        
        # Aplicar todas as correções
        for pattern, replacement in patterns:
            new_content = re.sub(pattern, replacement, content)
            if new_content != content:
                matches = re.findall(pattern, content)
                changes_made.extend([f"  {pattern} -> {replacement} ({len(matches)} occurrences)"])
                content = new_content
        
        # Salvar apenas se houve mudanças
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return changes_made
        
        return []
        
    except Exception as e:
        print(f"❌ Erro ao processar {file_path}: {e}")
        return []


def main():
    """Função principal"""
    
    print("🔧 CORREÇÃO DE IMPORTS INCORRETOS RESTANTES")
    print("=" * 70)
    
    # Diretórios para processar
    directories = ['src', 'tests', '.']
    
    total_files = 0
    total_changes = 0
    
    for directory in directories:
        if not os.path.exists(directory):
            continue
            
        print(f"\n📁 Processando diretório: {directory}")
        
        # Encontrar todos os arquivos Python
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    
                    # Verificar se o arquivo contém imports incorretos
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        if 'import src.qualia' in content:
                            print(f"  🔄 Corrigindo: {file_path}")
                            changes = fix_import_statements(file_path)
                            
                            if changes:
                                total_files += 1
                                total_changes += len(changes)
                                
                                for change in changes:
                                    print(f"    ✅ {change}")
                            else:
                                print("    ⚠️ Nenhuma mudança aplicada")
                                
                    except Exception as e:
                        print(f"    ❌ Erro ao ler {file_path}: {e}")
    
    print("\n🏁 RESULTADO FINAL")
    print("=" * 70)
    print(f"✅ Arquivos corrigidos: {total_files}")
    print(f"✅ Total de mudanças: {total_changes}")
    
    if total_changes > 0:
        print("\n💡 Execute novamente o sistema para verificar se os erros foram corrigidos!")
    else:
        print("\n💡 Nenhum import incorreto encontrado!")


if __name__ == "__main__":
    main() 
