# Gerenciamento de Estado

Este documento descreve o contêiner `QualiaState` utilizado pela aplicação Flask.
A classe encapsula objetos como o núcleo consciente,
o processador simbólico e o universo de trading.

## Segurança de Threads

`QualiaState` expõe um gerenciador de contexto por meio do método `locked()`.
Ele utiliza um `threading.RLock` para serializar o acesso quando o estado é alterado.
Embora a aplicação web seja predominantemente de leitura, o uso desse contexto
em operações de escrita previne condições de corrida em implantações multi-thread.

## Snapshotting state

`QualiaState` fornece um método `to_dict()` que retorna um dicionário raso
com os valores atuais. Esse recurso facilita a depuração e integrações com
ferramentas de monitoramento que esperam dados JSON-serializáveis.

