import logging
import argparse

from qualia.utils.logger import get_logger
from qiskit import qasm3
from qualia.core.universe import QUALIAQuantumUniverse

logging.basicConfig(level=logging.INFO)
logger = get_logger(__name__)
logger.setLevel(logging.INFO)


def run_low_diversity_example(post_randomize_layers: int = 1):
    """Run ``QUALIAQuantumUniverse`` to illustrate low diversity scenario.

    Parameters
    ----------
    post_randomize_layers
        Number of randomization layers applied after trimming.
    """

    u = QUALIAQuantumUniverse(
        n_qubits=5,
        scr_depth=1,
        base_lambda=0.1,
        alpha=0.1,
        retro_strength=0.0,
        num_ctc_qubits=0,
        measure_frequency=1,
        thermal_coefficient=0.05,
        shots=128,
        qpu_steps=1,
        thermal_noise_enabled=False,
        backend_name="aer_simulator_statevector",
        initial_state_type="hadamard",
        entanglement_style="full",
    )
    counts, metrics = u.run(steps=1, post_randomize_layers=post_randomize_layers)
    logger.info("Counts diversity ratio: %.4f", metrics["counts_diversity_ratio"])
    logger.info("Circuit depth: %d", u.qc.depth())
    try:
        qasm_snippet = qasm3.dumps(u.qc).splitlines()[:10]
        logger.info("QASM snippet:\n%s", "\n".join(qasm_snippet))
    except Exception as exc:  # pragma: no cover - qasm export may fail
        logger.warning("Failed to export QASM: %s", exc)
    return counts, metrics


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Reproduz exemplo de baixa diversidade"
    )
    parser.add_argument(
        "--post-randomize-layers",
        type=int,
        default=1,
        help="Número de camadas de randomização pós-trim",
    )
    args = parser.parse_args()

    run_low_diversity_example(post_randomize_layers=args.post_randomize_layers)
