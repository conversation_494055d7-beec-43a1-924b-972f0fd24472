# Interface de Desenvolvimento

Este guia resume a API do `CryptoDataFetcher`, principal ponto de acesso à camada de mercado do QUALIA.
Ele detalha os parâmetros `since`, `limit` e `ohlcv_timeout` utilizados em `fetch_ohlcv`.

## Parâmetros da Função `fetch_ohlcv`

| Nome           | Tipo   | Descrição |
|----------------|--------|-----------|
| `since`        | `int` opcional | Timestamp em milissegundos a partir do qual as velas devem ser coletadas. Se `None`, a exchange retorna os dados mais recentes disponíveis. |
| `limit`        | `int` | Número máximo de candles solicitados na chamada. Valores acima do permitido pela exchange são automaticamente ajustados. |
| `ohlcv_timeout`| `float` opcional | Tempo máximo, em segundos, para aguardar a resposta da exchange. Quando excedido, um `DataFrame` vazio é retornado e o timeout é ampliado progressivamente a cada falha consecutiva. |

Exemplo de uso:

```python
from src.qualia.market.base_integration import CryptoDataFetcher, MarketSpec

fetcher = CryptoDataFetcher(ohlcv_timeout=10)
spec = MarketSpec(symbol="BTC/USD", timeframe="1m")

ohlcv = await fetcher.fetch_ohlcv(spec, since=1700000000000, limit=500)
```

## Versionamento e Compatibilidade

A interface do `CryptoDataFetcher` evolui seguindo o versionamento semântico. A partir da versão **0.1.50**, os parâmetros `since`, `limit` e `ohlcv_timeout` tornaram-se estáveis e são mantidos em todas as versões `0.1.x`. Alterações futuras em versões `0.2` ou superiores preservarão compatibilidade retroativa sempre que possível. Ajustes de comportamento serão documentados no `CHANGELOG.md`.

Quando novas opções forem adicionadas, valores padrão continuarão compatíveis com scripts existentes, garantindo transições suaves entre versões.
