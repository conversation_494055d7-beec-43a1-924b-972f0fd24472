"""Helpers for loading QUALIA configuration files."""

from __future__ import annotations

import json
from pathlib import Path
from typing import Any, Dict

import yaml

from .logger import get_logger

__all__ = ["load_config_file"]

logger = get_logger(__name__)


def load_config_file(file_path: str) -> Dict[str, Any]:
    """Return parsed configuration from ``file_path``.

    Parameters
    ----------
    file_path : str
        Path to the configuration file. The format is inferred from the
        extension and must be JSON (``.json``) or YAML (``.yaml``/``.yml``).

    Returns
    -------
    dict
        Parsed configuration as a dictionary.

    Raises
    ------
    FileNotFoundError
        When the file does not exist.
    ValueError
        If the extension is unsupported.
    json.JSONDecodeError
        When a JSON file cannot be parsed.
    yaml.YAMLError
        When a YAML file cannot be parsed.
    """

    path = Path(file_path)
    if not path.exists():
        raise FileNotFoundError(f"File not found: {path}")

    suffix = path.suffix.lower()
    try:
        if suffix == ".json":
            with path.open("r", encoding="utf-8") as fh:
                data = json.load(fh)
        elif suffix in {".yaml", ".yml"}:
            with path.open("r", encoding="utf-8") as fh:
                data = yaml.safe_load(fh)
        else:
            raise ValueError(f"Unsupported config format: {suffix}")
    except Exception as exc:
        logger.error("Failed to load config from %s: %s", path, exc)
        raise

    logger.info("Loaded configuration from %s", path)
    return data
