#!/usr/bin/env python3
"""
Criar configuração balanceada da estratégia FWH baseada nos resultados do backtest.
"""

import sys
import os
import yaml
from pathlib import Path

def create_balanced_config():
    """Cria configuração balanceada baseada na análise do backtest."""
    print("🎯 CRIANDO CONFIGURAÇÃO BALANCEADA FWH")
    print("=" * 60)
    print("📊 Baseado na análise: 266 sinais, 26.1% win rate")
    print("🔧 Objetivo: Reduzir sinais falsos, manter atividade")
    print()
    
    # Configuração balanceada (menos agressiva, mais precisa)
    balanced_config = {
        'fibonacci_wave_hype_config': {
            'name': 'FibonacciWaveHypeStrategy',
            'enabled': True,
            'params': {
                'fib_lookback': 30,  # AUMENTADO de 20 para 30 (mais est<PERSON><PERSON>)
                'sentiment_cache_ttl': 300,
                
                # Configurações específicas por timeframe (BALANCEADAS)
                'timeframe_specific': {
                    '1m': {
                        'hype_threshold': 0.08,      # AUMENTADO de 0.03 para 0.08
                        'wave_min_strength': 0.02,   # AUMENTADO de 0.005 para 0.02
                        'quantum_boost_factor': 1.05, # REDUZIDO de 1.08 para 1.05
                        'holographic_weight': 0.4,
                        'tsvf_validation_threshold': 0.4  # AUMENTADO de 0.3 para 0.4
                    },
                    '5m': {
                        'hype_threshold': 0.12,      # AUMENTADO de 0.05 para 0.12
                        'wave_min_strength': 0.03,   # AUMENTADO de 0.01 para 0.03
                        'quantum_boost_factor': 1.05, # REDUZIDO de 1.08 para 1.05
                        'holographic_weight': 0.5,
                        'tsvf_validation_threshold': 0.4  # AUMENTADO de 0.3 para 0.4
                    },
                    '15m': {
                        'hype_threshold': 0.15,      # AUMENTADO de 0.08 para 0.15
                        'wave_min_strength': 0.05,   # AUMENTADO de 0.02 para 0.05
                        'quantum_boost_factor': 1.03, # REDUZIDO de 1.05 para 1.03
                        'holographic_weight': 0.6,
                        'tsvf_validation_threshold': 0.45 # AUMENTADO de 0.35 para 0.45
                    },
                    '1h': {
                        'hype_threshold': 0.18,      # AUMENTADO de 0.10 para 0.18
                        'wave_min_strength': 0.08,   # AUMENTADO de 0.03 para 0.08
                        'quantum_boost_factor': 1.02, # REDUZIDO de 1.03 para 1.02
                        'holographic_weight': 0.7,
                        'tsvf_validation_threshold': 0.45 # AUMENTADO de 0.35 para 0.45
                    }
                },
                
                # Configuração multi-timeframe (BALANCEADA)
                'multi_timeframe_config': {
                    'otoc_config': {
                        'enabled': True,
                        'max_threshold': 0.18,  # AUMENTADO de 0.12 para 0.18
                        'window': 40,           # AUMENTADO de 30 para 40
                        'method': 'correlation',
                        'adaptive_threshold': {
                            'enabled': True,
                            'beta': 1.0,        # REDUZIDO de 1.2 para 1.0
                            'vol_window': 20    # AUMENTADO de 15 para 20
                        }
                    },
                    'timeframe_weights': {
                        '1m': 0.25,   # REDUZIDO de 0.30 para 0.25
                        '5m': 0.35,   # REDUZIDO de 0.40 para 0.35
                        '15m': 0.65,  # REDUZIDO de 0.70 para 0.65
                        '1h': 0.85    # REDUZIDO de 0.90 para 0.85
                    },
                    'consolidation_method': 'weighted_average',
                    'min_timeframes_required': 2,  # AUMENTADO de 1 para 2
                    'confidence_threshold': 0.18,  # AUMENTADO de 0.10 para 0.18
                    'convergence_threshold': 0.75   # AUMENTADO de 0.6 para 0.75
                },
                
                # Níveis Fibonacci (BALANCEADOS)
                'fibonacci_levels': {
                    'primary': [0.236, 0.382, 0.618],
                    'secondary': [0.146, 0.5, 0.786],
                    'extensions': [1.272, 1.618, 2.618],
                    'sensitivity': 1.0  # REDUZIDO de 1.2 para 1.0
                },
                
                # Detecção de ondas (BALANCEADA)
                'wave_detection': {
                    'min_wave_bars': 5,      # AUMENTADO de 3 para 5
                    'max_wave_bars': 20,     # AUMENTADO de 15 para 20
                    'volume_weight': 0.6,    # REDUZIDO de 0.7 para 0.6
                    'price_weight': 0.4,     # AUMENTADO de 0.3 para 0.4
                    'momentum_threshold': 0.025  # AUMENTADO de 0.015 para 0.025
                },
                
                # Gestão de risco (BALANCEADA)
                'risk_management': {
                    'max_position_size': 0.12,     # REDUZIDO de 0.15 para 0.12
                    'stop_loss_fib_level': 0.786,  # AUMENTADO de 0.618 para 0.786
                    'take_profit_fib_level': 1.618, # AUMENTADO de 1.272 para 1.618
                    'dynamic_sizing': True,
                    'confidence_scaling': True,
                    'min_confidence_for_trade': 0.25  # NOVO: confidence mínima mais alta
                }
            },
            
            # Integração com componentes (BALANCEADA)
            'integration': {
                'holographic_engine': True,
                'tsvf_calculator': True,
                'quantum_metrics': True,
                'sentiment_analysis': True,
                'adaptive_thresholds': True,
                'signal_filtering': True  # NOVO: filtro adicional de sinais
            },
            
            # Configuração de backtesting (BALANCEADA)
            'backtesting': {
                'lookback_days': 14,  # AUMENTADO de 7 para 14
                'benchmark_strategies': ['QualiaTSVFStrategy'],
                'performance_metrics': ['sharpe_ratio', 'max_drawdown', 'win_rate', 'profit_factor'],
                'min_confidence_for_trade': 0.25,  # AUMENTADO de 0.15 para 0.25
                'max_signals_per_hour': 5  # NOVO: limite de sinais por hora
            }
        },
        
        # Configuração do sistema de trading (BALANCEADA)
        'trading_system': {
            'limits': {
                'max_position_size_usd': 1200.0,  # REDUZIDO de 1500 para 1200
                'max_daily_trades': 8,            # REDUZIDO de 10 para 8
                'min_trade_interval_minutes': 30, # AUMENTADO de 15 para 30
                'max_signals_per_day': 50         # NOVO: limite diário de sinais
            },
            'execution': {
                'slippage_tolerance': 0.001,
                'partial_fill_enabled': True,
                'signal_quality_filter': True  # NOVO: filtro de qualidade
            }
        },
        
        # Configuração de backtesting (BALANCEADA)
        'backtesting': {
            'initial_capital': 10000.0,
            'commission': 0.001,
            'slippage': 0.0005,
            'realistic_execution': True,
            'signal_delay_ms': 100  # NOVO: delay realista de execução
        }
    }
    
    return balanced_config

def create_conservative_config():
    """Cria configuração conservadora para produção."""
    print(f"\n🛡️ CRIANDO CONFIGURAÇÃO CONSERVADORA PARA PRODUÇÃO")
    print("=" * 60)
    
    # Configuração conservadora (alta precisão, baixa frequência)
    conservative_config = {
        'fibonacci_wave_hype_config': {
            'name': 'FibonacciWaveHypeStrategy',
            'enabled': True,
            'params': {
                'fib_lookback': 50,  # Mais estável
                'sentiment_cache_ttl': 300,
                
                # Configurações conservadoras
                'timeframe_specific': {
                    '1m': {
                        'hype_threshold': 0.15,      # Muito mais alto
                        'wave_min_strength': 0.05,   # Muito mais alto
                        'quantum_boost_factor': 1.02, # Mais conservador
                        'holographic_weight': 0.4,
                        'tsvf_validation_threshold': 0.5
                    },
                    '5m': {
                        'hype_threshold': 0.20,
                        'wave_min_strength': 0.08,
                        'quantum_boost_factor': 1.02,
                        'holographic_weight': 0.5,
                        'tsvf_validation_threshold': 0.5
                    },
                    '15m': {
                        'hype_threshold': 0.25,
                        'wave_min_strength': 0.10,
                        'quantum_boost_factor': 1.01,
                        'holographic_weight': 0.6,
                        'tsvf_validation_threshold': 0.5
                    },
                    '1h': {
                        'hype_threshold': 0.30,
                        'wave_min_strength': 0.15,
                        'quantum_boost_factor': 1.01,
                        'holographic_weight': 0.7,
                        'tsvf_validation_threshold': 0.5
                    }
                },
                
                # Multi-timeframe conservador
                'multi_timeframe_config': {
                    'otoc_config': {
                        'enabled': True,
                        'max_threshold': 0.25,
                        'window': 50,
                        'method': 'correlation'
                    },
                    'timeframe_weights': {
                        '1m': 0.20,
                        '5m': 0.30,
                        '15m': 0.60,
                        '1h': 0.80
                    },
                    'min_timeframes_required': 3,  # Mais confirmação
                    'confidence_threshold': 0.30,  # Muito mais alto
                    'convergence_threshold': 0.85   # Muito mais alto
                },
                
                # Gestão de risco conservadora
                'risk_management': {
                    'max_position_size': 0.08,     # Muito menor
                    'stop_loss_fib_level': 0.786,
                    'take_profit_fib_level': 2.618, # Mais amplo
                    'min_confidence_for_trade': 0.35  # Muito mais alto
                }
            }
        },
        
        # Sistema conservador
        'trading_system': {
            'limits': {
                'max_position_size_usd': 800.0,   # Menor
                'max_daily_trades': 3,            # Muito menor
                'min_trade_interval_minutes': 60, # Muito maior
                'max_signals_per_day': 20         # Muito menor
            }
        }
    }
    
    return conservative_config

def apply_configurations():
    """Aplica as configurações balanceada e conservadora."""
    try:
        # Configuração balanceada
        balanced_config = create_balanced_config()
        
        config_path = Path(__file__).parent.parent / 'config' / 'strategies_balanced.yaml'
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(balanced_config, f, default_flow_style=False, indent=2, allow_unicode=True)
        
        print(f"✅ Configuração balanceada salva: {config_path}")
        
        # Configuração conservadora
        conservative_config = create_conservative_config()
        
        prod_config_path = Path(__file__).parent.parent / 'config' / 'strategies_conservative.yaml'
        
        with open(prod_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(conservative_config, f, default_flow_style=False, indent=2, allow_unicode=True)
        
        print(f"✅ Configuração conservadora salva: {prod_config_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao aplicar configurações: {e}")
        return False

def main():
    """Executa criação das configurações."""
    print("🎯 CRIAÇÃO DE CONFIGURAÇÕES BALANCEADAS FWH")
    print("=" * 70)
    print("📊 Baseado na análise do backtest real:")
    print("   • 266 sinais gerados (muito ativo)")
    print("   • 26.1% win rate (baixa precisão)")
    print("   • -0.42% retorno (negativo)")
    print("🔧 Objetivo: Balancear atividade vs precisão")
    print()
    
    success = apply_configurations()
    
    if success:
        print(f"\n🎉 CONFIGURAÇÕES CRIADAS COM SUCESSO!")
        print(f"📊 CONFIGURAÇÃO BALANCEADA:")
        print(f"   • Thresholds aumentados para reduzir sinais falsos")
        print(f"   • Confidence mínima: 0.25 (era 0.15)")
        print(f"   • Min timeframes: 2 (era 1)")
        print(f"   • Objetivo: ~50-100 sinais/dia, >40% win rate")
        print(f"🛡️ CONFIGURAÇÃO CONSERVADORA:")
        print(f"   • Thresholds muito altos para máxima precisão")
        print(f"   • Confidence mínima: 0.35")
        print(f"   • Min timeframes: 3")
        print(f"   • Objetivo: ~10-20 sinais/dia, >60% win rate")
        print(f"\n📋 PRÓXIMOS PASSOS:")
        print(f"   1. Testar configuração balanceada")
        print(f"   2. Validar em simulação")
        print(f"   3. Usar conservadora em produção")
        return True
    else:
        print(f"\n❌ FALHA NA CRIAÇÃO DAS CONFIGURAÇÕES")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ CONFIGURAÇÕES BALANCEADAS CRIADAS!")
    else:
        print(f"\n🔧 REVISAR PROCESSO")
