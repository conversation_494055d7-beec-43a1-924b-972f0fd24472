# ============================================================================
# QUALIA HYPERPARAMETERS - FONTE ÚNICA DE VERDADE
# ============================================================================
# YAA REFINEMENT: Consolidação de todos os parâmetros críticos de amplificação,
# confiança e thresholds em um único arquivo para eliminar redundância e
# inconsistências entre configurações.
#
# IMPORTANTE: Este arquivo é a fonte única de verdade para:
# - price_amplification / price_amp
# - news_amplification / news_amp  
# - min_confidence / confidence_threshold / pattern_threshold
# - Todos os thresholds relacionados a detecção de padrões
# ============================================================================

# Amplificação Adaptativa - Parâmetros Principais
amplification:
  # Valores iniciais (podem ser sobrescritos por env vars ou CLI)
  initial:
    price_amplification: 5.0      # Multiplicador para eventos de preço
    news_amplification: 4.0       # Multiplicador para eventos de notícias
    pattern_threshold: 0.3        # Threshold base para detecção de padrões
    
  # Limites de segurança CRÍTICOS (NUNCA podem ser ultrapassados)
  limits:
    min_amplification: 1.0        # Amplificação mínima permitida (fail-safe)
    max_amplification: 10.0       # Amplificação máxima permitida (fail-safe)
    min_pattern_threshold: 0.2    # Threshold mínimo para padrões
    max_pattern_threshold: 0.8    # Threshold máximo para padrões

  # Limites de alerta (geram warnings mas não bloqueiam)
  warning_limits:
    high_price_amp_threshold: 8.0     # Alerta se price_amp > 8.0
    high_news_amp_threshold: 12.0     # Alerta se news_amp > 12.0
    low_confidence_threshold: 0.3     # Alerta se min_confidence < 0.3
    high_confidence_threshold: 0.9    # Alerta se min_confidence > 0.9
    
  # Parâmetros de aprendizado adaptativo
  learning:
    rate: 0.1                     # Taxa de aprendizado para calibração
    history_size: 100             # Tamanho do histórico para análise
    calibration_interval: 300     # Intervalo de calibração (segundos)
    min_samples_for_calibration: 10  # Mínimo de amostras para calibrar

# Confiança e Thresholds - Unificação de todos os min_confidence
confidence:
  # Threshold principal para decisões do sistema
  min_confidence: 0.6             # Confiança mínima para executar sinais
  
  # Thresholds específicos por componente
  temporal_detector:
    min_confidence: 0.6           # TemporalPatternDetector
    pattern_recognition_threshold: 0.45
    anomaly_detection_threshold: 1.8
    trend_detection_sensitivity: 0.25
    reversal_detection_sensitivity: 0.35
    
  metacognition:
    min_confidence: 0.6           # Metacognição
    buy_score_threshold: 0.38
    sell_score_threshold: 0.32
    cooldown_confidence_step: 0.03
    
  holographic_universe:
    significance_threshold: 0.5   # Detecção de padrões holográficos
    confidence_threshold: 0.6     # Confiança para sinais holográficos
    min_strength: 0.7             # Força mínima de sinais de trading
    
  quantum_encoding:
    confidence_boost: 0.15        # Boost de confiança para RSS sentiment
    quantum_weight: 1.2           # Peso quântico para encoding

# Parâmetros de Risco - Consolidação dos perfis
risk_profiles:
  conservative:
    price_amplification: 2.0
    news_amplification: 1.5
    min_confidence: 0.7
    pattern_threshold: 0.4
    
  moderate:
    price_amplification: 5.0
    news_amplification: 4.0
    min_confidence: 0.6
    pattern_threshold: 0.3
    
  aggressive:
    price_amplification: 8.0
    news_amplification: 7.0
    min_confidence: 0.4
    pattern_threshold: 0.2

# Configurações de Override - Precedência de configuração
override_precedence:
  # Ordem de precedência (maior número = maior prioridade):
  # 1. Valores padrão deste arquivo
  # 2. Variáveis de ambiente (QUALIA_*)
  # 3. Argumentos de linha de comando (--price-amp, --news-amp, etc.)
  # 4. Configuração dinâmica via API/gRPC
  
  environment_variables:
    price_amplification: "QUALIA_PRICE_AMP"
    news_amplification: "QUALIA_NEWS_AMP"
    min_confidence: "QUALIA_MIN_CONFIDENCE"
    pattern_threshold: "QUALIA_PATTERN_THRESHOLD"
    risk_profile: "QUALIA_RISK_PROFILE"
    
  cli_arguments:
    price_amplification: "--price-amp"
    news_amplification: "--news-amp"
    min_confidence: "--min-confidence"
    pattern_threshold: "--pattern-threshold"
    risk_profile: "--risk-profile"

# Validação e Constraints
validation:
  # Regras de validação que devem ser aplicadas
  rules:
    - name: "amplification_bounds"
      condition: "1.0 <= price_amplification <= 10.0"
      error: "price_amplification deve estar entre 1.0 e 10.0"
      
    - name: "news_amplification_bounds"
      condition: "1.0 <= news_amplification <= 10.0"
      error: "news_amplification deve estar entre 1.0 e 10.0"
      
    - name: "confidence_bounds"
      condition: "0.0 <= min_confidence <= 1.0"
      error: "min_confidence deve estar entre 0.0 e 1.0"
      
    - name: "pattern_threshold_bounds"
      condition: "0.0 <= pattern_threshold <= 1.0"
      error: "pattern_threshold deve estar entre 0.0 e 1.0"
      
    - name: "logical_consistency"
      condition: "price_amplification >= news_amplification * 0.5"
      error: "price_amplification não pode ser muito menor que news_amplification"

# Metadados para auditoria
metadata:
  version: "1.0.0"
  created_by: "YAA_QUALIA_CONSOLIDATION"
  created_at: "2025-01-05"
  description: "Consolidação de hiperparâmetros QUALIA para eliminar duplicação"
  replaces:
    - "config/holographic_enhanced_config.yaml:amplification"
    - "config/strategy_parameters.yaml:temporal_detector_config.min_confidence"
    - "config/strategy_parameters.json:metacognition_config.metacognition_min_confidence"
    - "config/holographic_universe.yaml:pattern_detection"
    - "config/permissive_config.yaml:holographic_universe"
    - "src/qualia/consciousness/amplification_calibrator.py:hardcoded_values"
    - "src/qualia/consciousness/enhanced_data_collector.py:hardcoded_values"
