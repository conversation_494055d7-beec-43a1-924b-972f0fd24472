
# QUALIA PERMISSIVE Configuration for Signal Generation
# Configuração otimizada para geração de sinais com dados limitados

# Sistema principal
system:
  symbols: ["BTC/USDT", "ETH/USDT", "ADA/USDT", "SOL/USDT"]
  timeframes: ["5m", "15m", "1h"]
  base_currency: "USDT"
  quote_currencies: ["BTC", "ETH", "ADA", "SOL"]
  capital: 10000.0

# Configuração de exchanges
exchanges:
  default: "kucoin"
  timeout: 30.0
  kucoin:
    api_key: "${KUCOIN_API_KEY}"
    api_secret: "${KUCOIN_SECRET_KEY}"
    api_passphrase: "${KUCOIN_PASSPHRASE}"
    sandbox: false
    timeout: 20.0
    rate_limit: 4.0
    retry_attempts: 3
    retry_delay: 1.0

# Trading configuration
trading:
  base_currency: "USDT"
  quote_currencies: ["BTC", "ETH", "ADA", "SOL"]
  market_data_timeout: 30.0
  enable_live_data: true
  data_source_priority: ["kucoin", "fallback"]

# Market data optimization - usar limite máximo da KuCoin
market:
  kucoin_batch_size: 1500  # Limite máximo da KuCoin (otimizado)
  max_candles_per_request: 1500
  enable_batch_optimization: true

# Holographic Universe - muito permissivo
holographic_universe:
  pattern_detection:
    significance_threshold: 0.1
    confidence_threshold: 0.1
    min_history_length: 5
    analysis_window: 10
  trading_signals:
    min_strength: 0.05
    proximity_threshold: 100
    confidence_threshold: 0.1

# Strategy config - thresholds muito baixos
strategy_config:
  name: "NovaEstrategiaQUALIA"
  params:
    s1_strength_threshold: 0.05
    s3_strength_threshold: 0.05
    meta_decision_threshold: 0.01
    coherence_threshold: 0.0
    entropy_threshold: 1.0

# Signal approval - extremamente permissivo
signal_approval:
  min_confidence: 0.01
  min_volume_ratio: 0.01
  max_volatility: 50.0
  enable_aggressive_mode: true
  enable_adaptive_threshold: true
  volatility_confidence_factor: 0.01
  volatility_history_window: 20
  threshold_decay_factor: 0.9
  min_trades_for_adjustment: 1
  performance_sensitivity_factor: 0.1

# Holographic trading - muito permissivo
holographic_trading:
  min_confidence: 0.01
  max_concurrent_positions: 10
  enable_holographic_risk_override: true
  volume_threshold: 0.01
  aggressive_mode: true

# Risk management - permissivo para testes
risk_management:
  risk_per_trade: 0.02
  max_drawdown: 0.15
  max_volatility: 20.0
  safety:
    max_open_positions: 5
    max_daily_loss_pct: 0.05
    max_drawdown_pct: 0.15

# QPM config
qpm_config:
  risk_manager:
    alias: "aggressive"
    initial_capital: 10000.0
    risk_profile: "aggressive"

# Unification weights
unification_weights:
  strategy: 0.2
  holographic: 0.6
  metacognition: 0.2
  decision_threshold: 0.005

# Data collection - muito permissivo
data_collection:
  min_data_completeness: 0.2
  allow_partial_data: true
  fallback_enabled: true
  min_candles_required: 10
