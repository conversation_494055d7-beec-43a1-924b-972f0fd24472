#!/usr/bin/env python
"""Download OHLCV data from public exchanges and save as CSV.

This helper retrieves historical candles using ``ccxt``. It is mainly
intended for the metaestratégia examples where a CSV file with columns
``timestamp``, ``open``, ``high``, ``low``, ``close`` and ``volume`` is
required by ``QUALIARealTimeTrader`` via ``qast_historical_data_path``.
"""
from __future__ import annotations

import argparse
import os
import time
from datetime import datetime, timezone
from typing import List

import ccxt
import pandas as pd


def _fetch_range(
    exchange: ccxt.Exchange,
    symbol: str,
    timeframe: str,
    start: datetime,
    end: datetime,
) -> List[List[float]]:
    """Iteratively fetch candles within the given date range."""
    since = int(start.replace(tzinfo=timezone.utc).timestamp() * 1000)
    end_ms = int(end.replace(tzinfo=timezone.utc).timestamp() * 1000)
    step_ms = exchange.parse_timeframe(timeframe) * 1000
    all_data: List[List[float]] = []
    limit = 1000

    while since < end_ms:
        candles = exchange.fetch_ohlcv(
            symbol, timeframe=timeframe, since=since, limit=limit
        )
        if not candles:
            break
        all_data.extend(candles)
        last_ts = candles[-1][0]
        since = last_ts + step_ms
        time.sleep(exchange.rateLimit / 1000)
        if last_ts >= end_ms or len(candles) < limit:
            break
    return [row for row in all_data if row[0] <= end_ms]


def main() -> None:
    parser = argparse.ArgumentParser(description="Download public OHLCV data")
    parser.add_argument("--symbol", default="BTC/USDT", help="Trading pair symbol")
    parser.add_argument("--timeframe", default="1h", help="Candlestick timeframe")
    parser.add_argument("--start", required=True, help="Start date YYYY-MM-DD")
    parser.add_argument("--end", required=True, help="End date YYYY-MM-DD")
    parser.add_argument(
        "--output", default="data/historical/ohlcv.csv", help="Path to output CSV"
    )
    parser.add_argument(
        "--exchange", default="binance", help="Exchange id supported by ccxt"
    )
    args = parser.parse_args()

    exchange_class = getattr(ccxt, args.exchange)
    exchange = exchange_class({"enableRateLimit": True})
    start_dt = datetime.fromisoformat(args.start)
    end_dt = datetime.fromisoformat(args.end)

    data = _fetch_range(exchange, args.symbol, args.timeframe, start_dt, end_dt)
    df = pd.DataFrame(
        data, columns=["timestamp", "open", "high", "low", "close", "volume"]
    )
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    df.to_csv(args.output, index=False)
    print(f"Saved {len(df)} rows to {args.output}")


if __name__ == "__main__":
    main()
