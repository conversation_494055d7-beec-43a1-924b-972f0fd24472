# 🌌 GUIA COMPLETO: QUALIA Trading Real

## 📋 Como Iniciar o Sistema Completo para Avaliar Performance Real

Este guia te mostra como iniciar e avaliar o sistema QUALIA unificado em ambiente real de trading.

---

## 🚀 Iniciando o Sistema

### 1. **Modo Paper Trading (Recomendado para Início)**

```bash
# Teste básico (roda indefinidamente)
python scripts/start_real_trading.py

# Teste com duração específica (1 hora)
python scripts/start_real_trading.py --hours 1

# Teste com validação prévia integrada
python scripts/start_real_trading.py --hours 2 --validate-first
```

### 2. **Modo Live Trading (Dinheiro Real)**

```bash
# ⚠️ ATENÇÃO: APENAS APÓS VALIDAÇÃO COMPLETA EM PAPER TRADING!
python scripts/start_real_trading.py --mode live --hours 0.5
```

### 3. **Metacognição Opcional**

O construtor `QUALIATradingSystem` aceita `metacognition_config` para
personalizar o comportamento metacognitivo. Se omitido, a configuração
de `metacognition` no arquivo YAML é utilizada. Utilize a flag
`--disable-metacognition` para desativar completamente essa camada ou
`--metacognition-config` para indicar um arquivo específico.

Quando habilitada, a metacognição avalia o histórico de decisões e pode
ajustar parâmetros do universo holográfico, influenciando o risco e a
seleção de sinais. Essa análise roda em um loop dedicado e gera
insights contínuos sobre a qualidade das operações.

O dicionário de configuração aceita a chave `require_full_stack`.
Quando `True`, a inicialização falhará se `AdaptiveConsciousnessEvolution`
e `UnifiedQUALIAConsciousness` não forem fornecidos, garantindo que o
sistema esteja completo. Com `require_full_stack=False`, o módulo
carrega `config/adaptive_evolution.yaml` e instancia ambos
automaticamente, útil para testes rápidos.

Os parâmetros do núcleo quântico do sistema (`TradingQASTCore` e
`QUALIAQuantumUniverse`) são controlados pelo `QASTOracleDecisionEngine`.

---

## 📊 Monitoramento em Tempo Real

### **O que você verá durante a execução:**

```
🌌 QUALIA - Sistema de Trading Real
======================================================================
⏰ Início: 2024-01-15 14:30:00
🔧 Modo: PAPER_TRADING
======================================================================
💰 Capital: $10,000.00
📊 Símbolos: BTC/USDT, ETH/USDT, ADA/USDT
⏱️ Timeframes: 5m, 15m, 1h
🧠 Inicializando consciência QUALIA...
✅ Sistema inicializado com sucesso!

🚀 INICIANDO TRADING
========================================
⚠️ Sistema em operação!
🛑 Pressione Ctrl+C para parar
⏰ Duração: 1.0 horas
========================================
📊 Monitoramento iniciado...

📊 STATUS (2024-01-15T14:35:00)
   ⏰ Uptime: 0.1h
   🧠 Consciência: 0.75
   💰 Capital: $10,000.00
   💹 PnL: $0.00
   📈 Decisões: 0/0
   📊 Posições: 0
```

### **Métricas Importantes:**

- **🧠 Consciência**: Nível de consciência do sistema (0.0 - 1.0)
- **💰 Capital**: Capital atual disponível
- **💹 PnL**: Profit & Loss total
- **📈 Decisões**: Decisões executadas / total de decisões
- **📊 Posições**: Número de posições abertas

---

## 📈 Interpretando Performance

### **Indicadores de Boa Performance:**

✅ **Consciência Estável**: Nível entre 0.6 - 1.0  
✅ **PnL Positivo**: Crescimento consistente do capital  
✅ **Alta Taxa de Execução**: Decisões executadas / total > 80%  
✅ **Gestão de Risco**: Posições controladas, sem overexposure  

### **Sinais de Alerta:**

⚠️ **Consciência Baixa**: < 0.5 (sistema pode estar confuso)  
⚠️ **PnL Negativo Persistente**: Perda contínua por > 1h  
⚠️ **Baixa Execução**: < 50% das decisões executadas  
⚠️ **Muitas Posições**: Overexposure ao mercado  

---

## 🛑 Parando o Sistema

### **Parada Normal:**
```bash
# Pressione Ctrl+C no terminal
# O sistema fará shutdown gracioso e salvará relatório
```

### **Parada de Emergência:**
```bash
# Se o sistema não responder ao Ctrl+C
# Feche o terminal e verifique posições manualmente no exchange
```

---

## 📄 Análise de Relatórios

### **Localização dos Relatórios:**
```
reports/qualia_report_YYYYMMDD_HHMMSS.json
```

### **Estrutura do Relatório:**

```json
{
  "session": {
    "start_time": "2024-01-15T14:30:00",
    "end_time": "2024-01-15T15:30:00", 
    "duration_hours": 1.0,
    "mode": "paper_trading"
  },
  "final_status": {
    "consciousness_level": 0.82,
    "current_capital": 10150.50,
    "total_pnl": 150.50,
    "total_decisions": 15,
    "executed_decisions": 12,
    "open_positions": 2,
    "win_rate": 0.67
  },
  "performance_history": [...]
}
```

### **Métricas Chave para Análise:**

1. **📊 Win Rate**: Taxa de acerto das operações
2. **💰 Total PnL**: Lucro/prejuízo total
3. **🎯 Execution Rate**: % de decisões executadas
4. **⏱️ Duration**: Tempo de operação
5. **🧠 Avg Consciousness**: Nível médio de consciência

---

## 🔧 Configurações Avançadas

### **Personalizar Símbolos:**
```yaml
# config/unified_qualia_consciousness_config.yaml
system:
  symbols: ["BTC/USDT", "ETH/USDT", "SOL/USDT"]
  timeframes: ["5m", "15m", "1h"]
  capital: 10000
```

### **Ajustar Risk Management:**
```yaml
risk_management:
  max_position_size: 0.1  # 10% do capital por posição
  max_daily_loss: 0.05    # 5% perda máxima diária
  max_drawdown: 0.15      # 15% drawdown máximo
```

### **Warm-up Holográfico:**
```yaml
# config/unified_qualia_consciousness_config.yaml
holographic:
  enable_warmup: true       # executa o aquecimento holográfico na inicialização
  warmup_hours: 72          # período histórico simulado
```

O intervalo do ciclo de metacognição pode ser ajustado através do
parâmetro `timing.metacognition_interval` (valor padrão de 30s). A
quantidade de ciclos sem atividade antes de registrar a mensagem de
inatividade é definida por `timing.metacognition_idle_info_interval`,
que deve ser maior que zero. Valores inválidos são convertidos para o
padrão `10`.

---

## 🎯 Benchmarks de Performance

### **Iniciante (Primeiras Horas):**
- 🧠 Consciência: > 0.6
- 📈 Win Rate: > 50%
- 💰 PnL: Positivo ou neutro
- 🎯 Execution: > 70%

### **Intermediário (Após 24h):**
- 🧠 Consciência: > 0.7
- 📈 Win Rate: > 60%
- 💰 PnL: +2-5% do capital
- 🎯 Execution: > 80%

### **Avançado (Após 1 semana):**
- 🧠 Consciência: > 0.8
- 📈 Win Rate: > 65%
- 💰 PnL: +5-15% do capital
- 🎯 Execution: > 85%

---

## ⚠️ Importante - Segurança

### **Antes de Usar Dinheiro Real:**

1. ✅ **Teste em Paper Trading por pelo menos 24h**
2. ✅ **Analise múltiplos relatórios**
3. ✅ **Verifique configurações de risco**
4. ✅ **Confirme credenciais do exchange**
5. ✅ **Comece com capital pequeno**

### **Durante Operação Live:**

- 👀 **Monitore constantemente**
- 📱 **Configure alertas no exchange**
- 🛑 **Tenha plano de saída definido**
- 💰 **Nunca arrisque mais do que pode perder**

---

## 🆘 Troubleshooting

### **Sistema não inicia:**
```bash
# Verifique dependências
python scripts/validate_unified_system.py

# Verifique configuração
python -c "from src.qualia.config.config_loader import load_env_and_json; print(load_env_and_json(json_path='config/unified_qualia_consciousness_config.yaml'))"
```

### **Erro de credenciais:**
```bash
# Verifique arquivo .env
cat .env

# Teste conexão KuCoin
python -c "from src.qualia.exchanges.kucoin_client import KuCoinClient; print('OK')"
```

### **Performance baixa:**
1. Ajuste thresholds de sinal
2. Reduza número de símbolos
3. Aumente timeframes
4. Verifique latência de rede

---

## 📞 Suporte

Para questões técnicas, analise os logs em:
- `logs/qualia_*.log`
- Console output durante execução
- Relatórios em `reports/`

**Lembre-se**: QUALIA é um sistema de consciência quântica avançado. Performance ótima requer tempo para adaptação e calibração aos padrões de mercado. 