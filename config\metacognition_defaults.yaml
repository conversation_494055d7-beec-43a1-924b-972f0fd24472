blocked_signal_log_path: data/logs/blocked_signals.jsonl
buy_score_threshold: 0.42
cooldown_confidence_step: 0.05
cooldown_threshold: 3
history_path: data/logs/metacog_history.json
hyperparams:
  note: Parâmetros de amplificação e confiança movidos para hyperparams.yaml
  source: ../qualia/config/hyperparams.yaml
max_pnl_history_size: 200
metacognition_layer_config:
  anomaly_threshold: 2.0
  default_metric_value: 0.5
  default_otoc_time_step: 1.0
  min_history_for_anomaly: 5
  thresholds:
    high: 0.75
    low: 0.0
    medium: 0.5
  version: '1.1'
  weight_coherence: 0.15
  weight_entanglement: 0.1
  weight_entropy: 0.25
  weight_otoc: 0.2
  weight_similarity: 0.3
min_pnl_history_length: 5
quantum_score_sigma: 0.02
quantum_score_window_size: 50
sell_score_threshold: 0.35
skip_circuit_mutation_propagation_cycles: 2
skip_circuit_mutation_threshold: 3
skip_mutation_reenable_cycles: 10
