#!/usr/bin/env python3
"""
Teste para verificar se as correções dos warnings do multi-timeframe consolidator funcionam.

Este script testa as melhorias implementadas para resolver os warnings:
- Dados insuficientes após resample
- Span temporal muito pequeno
- Períodos insuficientes
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Adiciona o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from qualia.strategies.fibonacci_wave_hype.multi_timeframe_consolidator import MultiTimeframeSignalConsolidator
    from qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy
    from qualia.utils.logger import get_logger
except ImportError as e:
    print(f"Erro ao importar módulos: {e}")
    print("Certifique-se de que o diretório src está no PYTHONPATH")
    sys.exit(1)

logger = get_logger(__name__)

def create_minimal_ohlcv_data(periods: int = 3, start_time: datetime = None) -> pd.DataFrame:
    """Cria dados OHLCV mínimos para teste."""
    if start_time is None:
        start_time = datetime.now() - timedelta(minutes=periods)
    
    timestamps = [start_time + timedelta(minutes=i) for i in range(periods)]
    
    # Dados sintéticos simples
    base_price = 100.0
    data = []
    
    for i, ts in enumerate(timestamps):
        price = base_price + np.random.uniform(-1, 1)
        data.append({
            'open': price,
            'high': price + np.random.uniform(0, 0.5),
            'low': price - np.random.uniform(0, 0.5),
            'close': price + np.random.uniform(-0.3, 0.3),
            'volume': np.random.uniform(1000, 5000)
        })
    
    df = pd.DataFrame(data, index=pd.DatetimeIndex(timestamps))
    return df

def create_single_period_data() -> pd.DataFrame:
    """Cria dados com apenas 1 período para testar edge case."""
    now = datetime.now()
    data = {
        'open': [100.0],
        'high': [100.5],
        'low': [99.5],
        'close': [100.2],
        'volume': [1000.0]
    }
    return pd.DataFrame(data, index=pd.DatetimeIndex([now]))

def create_zero_span_data() -> pd.DataFrame:
    """Cria dados com span temporal zero (mesmo timestamp)."""
    now = datetime.now()
    data = {
        'open': [100.0, 100.1],
        'high': [100.5, 100.6],
        'low': [99.5, 99.4],
        'close': [100.2, 100.3],
        'volume': [1000.0, 1100.0]
    }
    # Mesmo timestamp para ambos os períodos
    return pd.DataFrame(data, index=pd.DatetimeIndex([now, now]))

def test_minimal_data_scenarios():
    """Testa cenários com dados mínimos que causavam warnings."""
    print("\n=== Teste de Cenários com Dados Mínimos ===")
    
    consolidator = MultiTimeframeSignalConsolidator({
        "cache_enabled": False,  # Desabilita cache para teste
        "min_confidence_threshold": 0.1
    })
    
    # Teste 1: Dados com apenas 3 períodos
    print("\n--- Teste 1: 3 períodos de dados ---")
    data_3p = create_minimal_ohlcv_data(3)
    print(f"Dados criados: {len(data_3p)} períodos")
    print(f"Span temporal: {(data_3p.index.max() - data_3p.index.min()).total_seconds() / 60:.1f} minutos")
    
    for timeframe in ['5m', '15m', '1h']:
        result = consolidator.resample_data(data_3p, timeframe)
        print(f"  {timeframe}: {len(result)} períodos após resample")
    
    # Teste 2: Dados com apenas 1 período
    print("\n--- Teste 2: 1 período de dados ---")
    data_1p = create_single_period_data()
    print(f"Dados criados: {len(data_1p)} períodos")
    
    for timeframe in ['5m', '15m', '1h']:
        result = consolidator.resample_data(data_1p, timeframe)
        print(f"  {timeframe}: {len(result)} períodos após resample")
    
    # Teste 3: Dados com span temporal zero
    print("\n--- Teste 3: Span temporal zero ---")
    data_zero = create_zero_span_data()
    print(f"Dados criados: {len(data_zero)} períodos")
    print(f"Span temporal: {(data_zero.index.max() - data_zero.index.min()).total_seconds() / 60:.1f} minutos")
    
    for timeframe in ['5m', '15m', '1h']:
        result = consolidator.resample_data(data_zero, timeframe)
        print(f"  {timeframe}: {len(result)} períodos após resample")
    
    print("\n✅ Teste de cenários mínimos concluído")

def test_diagnosis_function():
    """Testa a função de diagnóstico."""
    print("\n=== Teste da Função de Diagnóstico ===")
    
    consolidator = MultiTimeframeSignalConsolidator()
    
    # Teste com diferentes tipos de dados problemáticos
    test_cases = [
        ("Dados normais", create_minimal_ohlcv_data(10)),
        ("Dados mínimos", create_minimal_ohlcv_data(2)),
        ("Um período", create_single_period_data()),
        ("Span zero", create_zero_span_data()),
        ("Dados vazios", pd.DataFrame())
    ]
    
    for name, data in test_cases:
        print(f"\n--- {name} ---")
        diagnosis = consolidator._diagnose_data_issues(data, '5m')
        print(f"  Períodos: {diagnosis['total_periods']}")
        print(f"  Span temporal: {diagnosis['time_span_minutes']:.1f} min")
        print(f"  Timestamps válidos: {diagnosis['has_valid_timestamps']}")
        print(f"  Duplicados: {diagnosis['duplicate_timestamps']}")
        if diagnosis['issues']:
            print(f"  Problemas: {'; '.join(diagnosis['issues'])}")
        else:
            print("  ✅ Nenhum problema detectado")
    
    print("\n✅ Teste de diagnóstico concluído")

def test_flexible_validation():
    """Testa as validações mais flexíveis."""
    print("\n=== Teste de Validações Flexíveis ===")
    
    # Cria dados que antes causariam warnings
    data_limited = create_minimal_ohlcv_data(5)  # Apenas 5 períodos
    
    # Configuração flexível
    config = {
        "cache_enabled": False,
        "min_confidence_threshold": 0.05,
        "convergence_threshold": 0.6
    }
    
    consolidator = MultiTimeframeSignalConsolidator(config)
    
    print(f"Testando com {len(data_limited)} períodos de dados")
    print(f"Span: {(data_limited.index.max() - data_limited.index.min()).total_seconds() / 60:.1f} min")
    
    results = {}
    for timeframe in ['5m', '15m', '1h']:
        result = consolidator.resample_data(data_limited, timeframe)
        results[timeframe] = len(result)
        status = "✅ Sucesso" if not result.empty else "⚠️ Vazio"
        print(f"  {timeframe}: {len(result)} períodos - {status}")
    
    # Verifica se pelo menos alguns timeframes funcionaram
    successful = sum(1 for count in results.values() if count > 0)
    print(f"\nTimeframes com sucesso: {successful}/3")
    
    if successful > 0:
        print("✅ Validações flexíveis funcionando")
    else:
        print("❌ Validações ainda muito restritivas")

def main():
    """Função principal de teste."""
    print("Testando correções dos warnings do Multi-Timeframe Consolidator")
    print("=" * 70)
    
    try:
        test_minimal_data_scenarios()
        test_diagnosis_function()
        test_flexible_validation()
        
        print("\n" + "=" * 70)
        print("✅ Todos os testes concluídos com sucesso!")
        print("\nAs correções implementadas devem resolver os warnings:")
        print("- Validações mais flexíveis para dados limitados")
        print("- Modo fallback para timeframes pequenos")
        print("- Diagnóstico melhorado de problemas")
        print("- Logs mais informativos e menos verbosos")
        
    except Exception as e:
        print(f"\n❌ Erro durante os testes: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)