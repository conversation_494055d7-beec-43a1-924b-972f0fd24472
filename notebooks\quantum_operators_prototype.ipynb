{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Protótipos dos Operadores Quânticos\n", "Demonstração simples de uso dos operadores presentes em `src/qualia/core`."]}, {"cell_type": "code", "metadata": {}, "execution_count": null, "outputs": [], "source": ["from qualia.core.folding import FoldingOperator\n", "import numpy as np\n", "fold = FoldingOperator({})\n", "state = await fold.fold(np.eye(2), timestamp=0.0)\n", "state.coherence"]}, {"cell_type": "code", "metadata": {}, "execution_count": null, "outputs": [], "source": ["from qualia.core.resonance import ResonanceOperator\n", "data = np.sin(np.linspace(0, 2*np.pi, 128))\n", "res = ResonanceOperator({})\n", "state = await res.analyze_resonance(data, timestamp=0.0)\n", "state.resonance_strength"]}, {"cell_type": "code", "metadata": {}, "execution_count": null, "outputs": [], "source": ["from qualia.core.emergence import EmergenceOperator\n", "components = np.random.rand(5, 3)\n", "eme = EmergenceOperator({})\n", "state = await eme.detect_emergence(components, timestamp=0.0)\n", "len(state.active_patterns)"]}, {"cell_type": "code", "metadata": {}, "execution_count": null, "outputs": [], "source": ["from qualia.core.retrocausality import RetrocausalityOperator\n", "retro = RetrocausalityOperator({})\n", "future = np.array([0.0])\n", "state = await retro.analyze_retrocausality(np.array([0.0]), future, timestamp=0.0)\n", "state.prediction_confidence"]}, {"cell_type": "code", "metadata": {}, "execution_count": null, "outputs": [], "source": ["from qualia.core.observer import ObserverOperator, ObservationType\n", "obs = ObserverOperator()\n", "result = await obs.observe(np.array([1.0, 0.0]), ObservationType.ENERGY, timestamp=0.0)\n", "result.observation_effect"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.10"}}, "nbformat": 4, "nbformat_minor": 5}