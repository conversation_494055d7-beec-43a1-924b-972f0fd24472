#!/usr/bin/env python3
"""
Teste final direto do sistema QUALIA
"""

import os
import sys
import subprocess
import time

def setup_robust_environment():
    """Configura ambiente robusto"""
    # Configurações ultra conservadoras
    env_vars = {
        'TICKER_TIMEOUT': '60',
        'OHLCV_TIMEOUT': '180', 
        'RATE_LIMIT': '6.0',
        'TICKER_CACHE_TTL': '60',
        'API_FAIL_THRESHOLD': '1',
        'TICKER_RETRIES': '1',
        'MAX_CONCURRENT_REQUESTS': '1'
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
    
    print("🔧 Ambiente configurado com timeouts robustos")

def test_qualia_system():
    """Testa o sistema QUALIA diretamente"""
    print("🚀 TESTE FINAL DO SISTEMA QUALIA")
    print("=" * 60)
    
    setup_robust_environment()
    
    # Comando direto
    cmd = [
        sys.executable, "-m", "src.qualia.qualia_trading_system",
        "--symbols", "BTC/USDT",
        "--timeframes", "5m",
        "--capital", "1000",
        "--mode", "paper_trading", 
        "--duration_seconds", "20",
        "--log_level", "INFO",
        "--data_source", "kucoin",
        "--risk_profile", "aggressive",
        "--strategy_config_path", "config/strategy_parameters.json"
    ]
    
    print("📋 Executando:")
    print("   • Símbolo: BTC/USDT")
    print("   • Duração: 20 segundos")
    print("   • Timeouts robustos aplicados")
    print("   • Rate limit: 6s")
    
    start_time = time.time()
    
    try:
        print("\n🔄 Iniciando QUALIA...")
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=120,  # 2 minutos máximo
            cwd=os.getcwd()
        )
        
        duration = time.time() - start_time
        
        # Analisar resultado
        stdout = result.stdout
        stderr = result.stderr
        
        print(f"\n⏱️ Execução completada em {duration:.1f}s")
        
        # Verificar sucessos nos logs
        success_indicators = [
            "QUALIAConsciousness inicializada",
            "QUALIAQuantumUniverse",
            "QuantumPatternMemory",
            "Conexão assíncrona com kucoin estabelecida",
            "Sistema principal de trading"
        ]
        
        failures = [
            "TimeoutError",
            "TickerFetchError", 
            "ModuleNotFoundError",
            "ImportError"
        ]
        
        successes = sum(1 for indicator in success_indicators if indicator in stdout)
        errors = sum(1 for failure in failures if failure in stdout or failure in stderr)
        
        print("\n📊 Análise:")
        print(f"   ✅ Componentes inicializados: {successes}/{len(success_indicators)}")
        print(f"   ❌ Erros encontrados: {errors}")
        
        if successes >= 3 and errors == 0:
            print("\n🎉 SISTEMA FUNCIONANDO PERFEITAMENTE!")
            print("✅ Todos os componentes principais inicializados")
            print("✅ Sem erros críticos")
            return True
        elif successes >= 2:
            print("\n⚠️ SISTEMA FUNCIONANDO COM LIMITAÇÕES")
            print("✅ Componentes principais OK")
            if errors > 0:
                print(f"⚠️ {errors} erros encontrados (possivelmente de conectividade)")
            return True
        else:
            print("\n❌ SISTEMA COM PROBLEMAS")
            print("❌ Poucos componentes inicializados")
            if stderr:
                print(f"Erro: {stderr[:200]}...")
            return False
            
    except subprocess.TimeoutExpired:
        duration = time.time() - start_time
        print(f"\n⏰ TIMEOUT após {duration:.1f}s")
        print("⚠️ Sistema pode estar funcionando mas lento")
        return False
        
    except Exception as e:
        duration = time.time() - start_time
        print(f"\n❌ ERRO após {duration:.1f}s: {e}")
        return False

def main():
    """Função principal"""
    success = test_qualia_system()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 RESULTADO: QUALIA ESTÁ FUNCIONAL!")
        print("📈 Pronto para trading (com configurações conservadoras)")
        print("🔥 Use timeouts altos para melhor estabilidade")
    else:
        print("🔧 RESULTADO: QUALIA PRECISA DE AJUSTES")
        print("💡 Tente executar em horários de menor tráfego")
        print("🌐 Verifique conexão de internet")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 
