#!/usr/bin/env python3
"""
Análise <PERSON>alhada da Estratégia Quantum Momentum
Investigando onde está "perdendo" apesar do alto win rate.
"""

import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Tuple

import pandas as pd
import numpy as np
import requests
import matplotlib.pyplot as plt


class DetailedAnalyzer:
    """Analisador detalhado de performance."""
    
    def __init__(self):
        self.session = requests.Session()
    
    def fetch_data(self, symbol: str, days: int = 90) -> pd.DataFrame:
        """Busca dados históricos da Binance."""
        try:
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': symbol.replace('/', ''),
                'interval': '1h',
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index().dropna()
            
            # Adiciona indicadores
            df['returns'] = df['close'].pct_change()
            df['sma_20'] = df['close'].rolling(20).mean()
            df['rsi'] = self._calculate_rsi(df['close'], 14)
            df['volatility'] = df['returns'].rolling(20).std()
            
            return df
            
        except Exception as e:
            print(f"❌ Erro ao buscar dados para {symbol}: {e}")
            return pd.DataFrame()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calcula RSI."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def analyze_quantum_momentum(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Análise detalhada da estratégia Quantum Momentum."""
        signals = []
        trade_details = []
        
        print("🔍 Analisando Quantum Momentum em detalhes...")
        
        for i in range(50, len(df)):
            # Momentum clássico
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            
            # Momentum de volume
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            
            # RSI momentum
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
            
            # Combinação quantum
            signal = (
                price_momentum * 0.5 +
                vol_momentum * 0.3 +
                rsi_momentum * 0.2
            )
            
            if abs(signal) > 0.015:  # 1.5% threshold
                final_signal = np.clip(signal * 8, -1, 1)
                signals.append(final_signal)
                
                # Registra detalhes do trade
                trade_details.append({
                    'timestamp': df.index[i],
                    'price': df['close'].iloc[i],
                    'signal': final_signal,
                    'price_momentum': price_momentum,
                    'vol_momentum': vol_momentum,
                    'rsi_momentum': rsi_momentum,
                    'rsi': df['rsi'].iloc[i],
                    'volatility': df['volatility'].iloc[i]
                })
            else:
                signals.append(0)
        
        # Calcula retornos detalhados
        returns = []
        winning_trades = []
        losing_trades = []
        
        for i in range(1, len(signals)):
            if abs(signals[i-1]) > 0.1:  # Trade significativo
                price_return = (df['close'].iloc[i+50] - df['close'].iloc[i+49]) / df['close'].iloc[i+49]
                position_return = signals[i-1] * price_return
                returns.append(position_return)
                
                trade_info = {
                    'return': position_return,
                    'position': signals[i-1],
                    'price_change': price_return,
                    'timestamp': df.index[i+49]
                }
                
                if position_return > 0:
                    winning_trades.append(trade_info)
                else:
                    losing_trades.append(trade_info)
        
        # Análise estatística
        returns_series = pd.Series(returns)
        
        # Métricas básicas
        total_return = returns_series.sum()
        win_rate = len(winning_trades) / len(returns) if returns else 0
        
        # Análise de ganhos vs perdas
        avg_win = np.mean([t['return'] for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t['return'] for t in losing_trades]) if losing_trades else 0
        
        # Profit factor
        total_wins = sum([t['return'] for t in winning_trades])
        total_losses = abs(sum([t['return'] for t in losing_trades]))
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        # Análise de drawdown
        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        # Análise de volatilidade dos trades
        volatility = returns_series.std()
        sharpe = returns_series.mean() / volatility if volatility > 0 else 0
        
        return {
            'total_return_pct': total_return * 100,
            'win_rate_pct': win_rate * 100,
            'total_trades': len(returns),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'avg_win_pct': avg_win * 100,
            'avg_loss_pct': avg_loss * 100,
            'profit_factor': profit_factor,
            'max_drawdown_pct': max_drawdown * 100,
            'sharpe_ratio': sharpe,
            'volatility': volatility,
            'largest_win': max([t['return'] for t in winning_trades]) * 100 if winning_trades else 0,
            'largest_loss': min([t['return'] for t in losing_trades]) * 100 if losing_trades else 0,
            'win_loss_ratio': abs(avg_win / avg_loss) if avg_loss != 0 else 0,
            'trade_details': trade_details[:10],  # Primeiros 10 trades para análise
            'winning_trades_sample': winning_trades[:5],
            'losing_trades_sample': losing_trades[:5]
        }
    
    def compare_with_composite(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Compara com a estratégia Composite para contraste."""
        signals = []
        returns = []
        
        for i in range(50, len(df)):
            # Trend following
            trend_signal = (df['sma_20'].iloc[i] - df['sma_20'].iloc[i-20]) / df['sma_20'].iloc[i-20]
            
            # Mean reversion
            mean_rev = -(df['close'].iloc[i] - df['sma_20'].iloc[i]) / df['sma_20'].iloc[i]
            
            # Momentum
            momentum = df['close'].iloc[i-5:i].pct_change().mean()
            
            # Volatility adjustment
            vol_adj = 1 / (1 + df['volatility'].iloc[i] * 100)
            
            # Combinação adaptativa
            composite_signal = (
                trend_signal * 0.3 +
                mean_rev * 0.3 +
                momentum * 0.4
            ) * vol_adj
            
            if abs(composite_signal) > 0.01:
                signals.append(np.clip(composite_signal * 12, -1, 1))
            else:
                signals.append(0)
        
        # Calcula retornos
        for i in range(1, len(signals)):
            if abs(signals[i-1]) > 0.1:
                price_return = (df['close'].iloc[i+50] - df['close'].iloc[i+49]) / df['close'].iloc[i+49]
                position_return = signals[i-1] * price_return
                returns.append(position_return)
        
        if returns:
            returns_series = pd.Series(returns)
            win_rate = (returns_series > 0).mean()
            avg_win = returns_series[returns_series > 0].mean() if any(returns_series > 0) else 0
            avg_loss = returns_series[returns_series < 0].mean() if any(returns_series < 0) else 0
            
            return {
                'total_return_pct': returns_series.sum() * 100,
                'win_rate_pct': win_rate * 100,
                'total_trades': len(returns),
                'avg_win_pct': avg_win * 100,
                'avg_loss_pct': avg_loss * 100,
                'win_loss_ratio': abs(avg_win / avg_loss) if avg_loss != 0 else 0
            }
        
        return {'error': 'Sem trades'}


def run_detailed_analysis():
    """Executa análise detalhada."""
    print("🔍 ANÁLISE DETALHADA: QUANTUM MOMENTUM vs COMPOSITE")
    print("=" * 60)
    
    analyzer = DetailedAnalyzer()
    
    symbols = ["BTCUSDT", "ETHUSDT"]
    
    for symbol in symbols:
        print(f"\n📈 Analisando {symbol}...")
        
        df = analyzer.fetch_data(symbol, days=90)
        if df.empty:
            continue
        
        # Análise Quantum Momentum
        qm_analysis = analyzer.analyze_quantum_momentum(df)
        
        # Análise Composite
        comp_analysis = analyzer.compare_with_composite(df)
        
        print(f"\n🧠 QUANTUM MOMENTUM - {symbol}:")
        print(f"   📊 Total Return: {qm_analysis['total_return_pct']:.2f}%")
        print(f"   🎯 Win Rate: {qm_analysis['win_rate_pct']:.1f}%")
        print(f"   📈 Trades: {qm_analysis['total_trades']}")
        print(f"   💚 Avg Win: {qm_analysis['avg_win_pct']:.3f}%")
        print(f"   💔 Avg Loss: {qm_analysis['avg_loss_pct']:.3f}%")
        print(f"   ⚖️  Win/Loss Ratio: {qm_analysis['win_loss_ratio']:.2f}")
        print(f"   📉 Profit Factor: {qm_analysis['profit_factor']:.2f}")
        print(f"   🔥 Largest Win: {qm_analysis['largest_win']:.2f}%")
        print(f"   💥 Largest Loss: {qm_analysis['largest_loss']:.2f}%")
        print(f"   📊 Sharpe: {qm_analysis['sharpe_ratio']:.3f}")
        
        print(f"\n🔄 COMPOSITE - {symbol}:")
        if 'error' not in comp_analysis:
            print(f"   📊 Total Return: {comp_analysis['total_return_pct']:.2f}%")
            print(f"   🎯 Win Rate: {comp_analysis['win_rate_pct']:.1f}%")
            print(f"   📈 Trades: {comp_analysis['total_trades']}")
            print(f"   💚 Avg Win: {comp_analysis['avg_win_pct']:.3f}%")
            print(f"   💔 Avg Loss: {comp_analysis['avg_loss_pct']:.3f}%")
            print(f"   ⚖️  Win/Loss Ratio: {comp_analysis['win_loss_ratio']:.2f}")
        
        print(f"\n🔍 DIAGNÓSTICO QUANTUM MOMENTUM:")
        
        # Diagnóstico principal
        if qm_analysis['win_rate_pct'] > 45 and qm_analysis['total_return_pct'] < 0:
            print(f"   ⚠️  PROBLEMA IDENTIFICADO: Alto win rate mas retorno negativo!")
            print(f"   🔍 Win/Loss Ratio: {qm_analysis['win_loss_ratio']:.2f}")
            
            if qm_analysis['win_loss_ratio'] < 1.0:
                print(f"   💡 CAUSA: Perdas médias MAIORES que ganhos médios")
                print(f"      • Ganho médio: {qm_analysis['avg_win_pct']:.3f}%")
                print(f"      • Perda média: {qm_analysis['avg_loss_pct']:.3f}%")
                print(f"   🎯 SOLUÇÃO: Precisa melhorar stop-loss ou take-profit")
            
            if qm_analysis['profit_factor'] < 1.0:
                print(f"   💡 Profit Factor < 1.0: Perdas totais > Ganhos totais")
            
            if abs(qm_analysis['largest_loss']) > qm_analysis['largest_win']:
                print(f"   ⚠️  Maior perda ({qm_analysis['largest_loss']:.2f}%) > Maior ganho ({qm_analysis['largest_win']:.2f}%)")
        
        # Salva análise detalhada
        output_dir = Path("results/quantum_momentum_analysis")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        with open(output_dir / f"analysis_{symbol}_{int(time.time())}.json", 'w') as f:
            json.dump({
                'symbol': symbol,
                'quantum_momentum': qm_analysis,
                'composite': comp_analysis,
                'timestamp': datetime.now().isoformat()
            }, f, indent=2, default=str)


if __name__ == "__main__":
    run_detailed_analysis()
