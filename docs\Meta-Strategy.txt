Introdução
A busca por estratégias quantitativas robustas tem crescido no mercado de criptoativos, dada a alta volatilidade e mudanças de regime frequentes nesses mercados. Estratégias de análise técnica clássicas, como médias móveis e Índice de Força Relativa (RSI), são amplamente utilizadas para capturar tendências e reversões de preço
scielo.org.za
. No entanto, cada indicador isolado tende a funcionar bem apenas em determinadas condições de mercado: por exemplo, uma estratégia de média móvel (seguimento de tendência) costuma ter bom desempenho em mercados com forte tendência, mas pode sofrer em períodos laterais; já uma estratégia baseada em RSI (contrária, comprando sobrevenda e vendendo sobrecompra) pode lucrar em mercados em faixa (range) mas apresentar perdas em tendências persistentes. Estudos prévios mostram que regras técnicas simples aplicadas a criptomoedas produzem retornos modestos e variáveis
ideas.repec.org
, o que motiva a combinação de múltiplos sinais para melhorar a consistência. Nesta conjuntura, surge a Meta-Strategy, uma estratégia de metanível que combina dinamicamente múltiplos sinais técnicos. A ideia central é ponderar cada estratégia base de acordo com sua performance recente, mensurada pelo Índice de Sharpe em janela móvel (rolling Sharpe). O Índice de Sharpe (Sharpe ratio) quantifica o retorno em excesso (sobre taxa livre de risco) dividido pelo desvio-padrão do retorno, servindo como medida de retorno ajustado ao risco
scielo.org.za
scielo.org.za
. Sharpe (1994) introduziu essa métrica precisamente para comparar investimentos considerando risco e retorno simultaneamente
scielo.org.za
. Ao atribuir maior peso às estratégias com maior Sharpe recente, a Meta-Strategy visa adaptar-se aos regimes de mercado: quando o mercado está tendencial, a estratégia de tendência tende a apresentar Sharpe superior e passa a ter peso predominante; em regime lateral, a estratégia de RSI (reversão) pode obter Sharpe maior e assim receber mais peso. Diferentemente de abordagens estáticas (e.g. alocação igualitária fixa entre estratégias) que ignoram mudanças de desempenho relativo, a ponderação adaptativa busca melhorar a relação retorno/risco do portfólio de estratégias ao desativar estratégias fracas e enfatizar as fortes em cada período
assets.super.so
. Trabalhos recentes corroboram essa abordagem: Srivastava et al. (2019) mostram que combinar indicadores técnicos com pesos proporcionais aos respectivos Sharpes históricos elevou o Sharpe do portfólio em ~60% comparado ao melhor indicador individual
assets.super.so
assets.super.so
. De modo similar, Koegelenberg & van Vuuren (2023) reportaram que um ensemble de estratégias heterogêneas superou consistentemente as estratégias isoladas em métricas de retorno ajustado ao risco (Sharpe) e drawdown, quase dobrando o Sharpe em relação à melhor estratégia individual em certos cenários
scielo.org.za
. Esses achados sustentam a premissa de que ensembles adaptativos oferecem maior robustez a diferentes condições de mercado, reduzindo a exposição prolongada a estratégias subótimas. Este artigo apresenta uma investigação IMRaD da Meta-Strategy aplicada a dados históricos horários de três criptomoedas (BTC, ETH, SOL, cotadas em USDT) durante o ano de 2023. Primeiramente, detalhamos a metodologia de combinação adaptativa de sinais técnicos, incluindo formulações matemáticas e implementação em Python. Em seguida, comparamos os resultados empíricos da Meta-Strategy com estratégias individuais e com uma combinação convencional de peso fixo, por meio de métricas de desempenho (retorno acumulado, Índice de Sharpe anualizado e drawdown máximo). Por fim, discutimos por que a ponderação por Sharpe móvel melhora os resultados em relação a métodos tradicionais, e examinamos a robustez da estratégia — incluindo limitações quando todas estratégias subjacentes são fracas.
Métodos
Dados e Pré-processamento
Foram utilizados dados históricos horários de preços de fechamento (close) para BTC/USDT, ETH/USDT e SOL/USDT, abrangendo de 1º/Jan/2023 até 1º/Jan/2024 (8760 horas). Cada série foi normalizada para iniciar com capital 1.0 na simulação. Desconsideramos custos de transação e deslizamento, focando exclusivamente no retorno bruto das estratégias. Definimos o retorno simples horário como $r_t = P_t/P_{t-1} - 1$, onde $P_t$ é o preço de fechamento no instante $t$. Esta frequência elevada (1h) permite avaliar a responsividade da Meta-Strategy a mudanças rápidas de regime. Para compor a Meta-Strategy, selecionamos duas estratégias técnicas clássicas e complementares: (i) Estratégia de Médias Móveis (seguimento de tendência) e (ii) Estratégia de RSI (reversão à média). Cada estratégia gera um sinal de posição $s_{i,t}$ em cada hora $t$, sendo $s_{i,t}=+1$ para posição comprada (long), $-1$ para vendida (short) e $0$ para posição neutra (ficar fora do mercado). A seguir detalhamos cada estratégia:
Estratégia de Médias Móveis (MA): utiliza duas médias móveis do preço de fechamento, uma de curto prazo e outra de longo prazo. Quando a média curta cruzar acima da longa, sinaliza tendência de alta (posição comprada); quando a média curta cruza abaixo da longa, sinaliza tendência de baixa (posição vendida). Implementamos com janelas de 50 horas (curto prazo) e 200 horas (longo prazo), parâmetros típicos que correspondem aproximadamente a ~2 dias e ~8 dias, respectivamente. Esta configuração (crossover 50/200) é semelhante à usada em golden cross/death cross no diário, adaptada aqui para granularidade horária. A fórmula do sinal pode ser escrita como:
𝑠
MA
,
𝑡
=
{
+
1
,
se 
𝑀
𝐴
50
(
𝑡
)
>
𝑀
𝐴
200
(
𝑡
)
−
1
,
se 
𝑀
𝐴
50
(
𝑡
)
<
𝑀
𝐴
200
(
𝑡
)
0
,
caso contr
a
ˊ
rio
,
s 
MA,t
​
 = 
⎩
⎨
⎧
​
  
+1,
−1,
0,
​
  
se MA 
50
​
 (t)>MA 
200
​
 (t)
se MA 
50
​
 (t)<MA 
200
​
 (t)
caso contr 
a
ˊ
 rio
​
 ,
onde $MA_{n}(t)$ é a média móvel simples de $n$ períodos até o instante $t$. Um sinal nulo ocorre raramente (quando as médias coincidem). Na implementação, iniciamos o histórico calculando as médias móveis de forma vetorial.
Estratégia de RSI: utiliza o Índice de Força Relativa (RSI) de $14$ períodos para identificar condições de sobrevenda/sobrecompra. O RSI varia de 0 a 100; valores baixos indicam mercado potencialmente sobrevendido (pressão vendedora excessiva) e valores altos indicam sobrecompra. Adotamos os limiares clássicos: RSI < 30 sinaliza surge de compra (expectativa de reversão para cima) e RSI > 70 sinaliza surge de venda ou short (reversão para baixo). Formalmente:
𝑠
RSI
,
𝑡
=
{
+
1
,
𝑅
𝑆
𝐼
14
(
𝑡
)
<
30
,
−
1
,
𝑅
𝑆
𝐼
14
(
𝑡
)
>
70
,
0
,
caso contr
a
ˊ
rio.
s 
RSI,t
​
 = 
⎩
⎨
⎧
​
  
+1,
−1,
0,
​
  
RSI 
14
​
 (t)<30,
RSI 
14
​
 (t)>70,
caso contr 
a
ˊ
 rio.
​
 
O RSI de 14 períodos foi calculado conforme a fórmula de Wilder, usando médias móveis exponenciais do ganho e perda médios
scielo.org.za
. Em muitos instantes $s_{\text{RSI},t}$ pode ser 0 (quando o RSI está entre 30 e 70, sem indicação forte); nesses períodos a estratégia RSI permanece em caixa (sem posição). Essa característica reduz a exposição em mercados sem tendência definida, contribuindo para menor volatilidade da estratégia RSI isoladamente. Implementamos as funções de cálculo de indicadores e geração de sinais em Python, conforme ilustrado no código abaixo (código em formato snippet, com comentários explicativos em português):
python
Copy
import pandas as pd
import numpy as np

# Leitura dos dados históricos de preços (BTC, ETH, SOL) do período de 2023
btc = pd.read_csv('BTCUSDT_1h.csv')  # arquivo sem barra, mas simbolo deve ser BTC/USDT
btc['timestamp'] = pd.to_datetime(btc['timestamp'], unit='ms')
prices = btc['close'].values  # preços de fechamento BTC

# Função para calcular o RSI de 14 períodos (Wilder's RSI)
def compute_RSI(prices, window=14):
    prices = np.asarray(prices, dtype=float)
    n = len(prices)
    rsi = np.full(n, np.nan)
    # Cálculo inicial: médias de ganhos e perdas nos primeiros 'window' períodos
    deltas = np.diff(prices)
    avg_gain = np.mean(np.where(deltas[:window] > 0, deltas[:window], 0))
    avg_loss = np.mean(np.where(deltas[:window] < 0, -deltas[:window], 0))
    # Evita divisão por zero no caso de nenhuma perda inicial
    rs = avg_gain / avg_loss if avg_loss != 0 else np.inf
    rsi[window] = 100 - 100/(1 + rs)
    # Aplica recursivamente o cálculo de Wilder para cada período subsequente
    for t in range(window+1, n):
        delta = deltas[t-1]  # variação de preço do período (t-1) -> t
        gain = max(delta, 0)
        loss = -min(delta, 0)
        # EMA do ganho e perda médios
        avg_gain = (avg_gain*(window-1) + gain) / window
        avg_loss = (avg_loss*(window-1) + loss) / window
        rs = avg_gain / avg_loss if avg_loss != 0 else np.inf
        rsi[t] = 100 - 100/(1 + rs)
    return rsi

# Funções de sinal de estratégia
def moving_average_signal(prices, short_window=50, long_window=200):
    prices_series = pd.Series(prices)
    ma_short = prices_series.rolling(short_window, min_periods=1).mean()
    ma_long  = prices_series.rolling(long_window, min_periods=1).mean()
    # Sinal: +1 se média curta > média longa, -1 se menor, 0 se iguais
    signal = np.where(ma_short > ma_long, 1, np.where(ma_short < ma_long, -1, 0))
    return signal

def rsi_strategy_signal(prices, window=14, oversold=30, overbought=70):
    rsi = compute_RSI(prices, window=window)
    signal = np.where(rsi < oversold, 1, np.where(rsi > overbought, -1, 0))
    return signal

# Calcula os sinais para BTC usando as funções definidas
ma_signal = moving_average_signal(prices, short_window=50, long_window=200)
rsi_signal = rsi_strategy_signal(prices, window=14, oversold=30, overbought=70)
Listing 1: Cálculo dos indicadores técnicos (médias móveis e RSI) e geração dos sinais de estratégia para o exemplo do BTC. Nas séries ma_signal e rsi_signal, o valor é 1, -1 ou 0 conforme as regras definidas.
Meta-Strategy – Combinação Adaptativa de Sinais
Com os sinais individuais em mãos, procedemos à construção da Meta-Strategy. O framework geral consiste em alocar uma fração do capital em cada estratégia base proporcional à sua performance recente, mensurada pelo Índice de Sharpe em uma janela móvel. Matematicamente, seja $S_{i}(t)$ o Índice de Sharpe da estratégia $i$ calculado sobre seus retornos nos $W$ períodos anteriores a $t$. Definimos o peso adaptativo $w_{i}(t)$ como:
𝑤
𝑖
(
𝑡
)
=
max
⁡
(
𝑆
𝑖
(
𝑡
)
,
 
0
)
∑
𝑗
=
1
𝑁
max
⁡
(
𝑆
𝑗
(
𝑡
)
,
 
0
)
,
w 
i
​
 (t)= 
∑ 
j=1
N
​
 max(S 
j
​
 (t),0)
max(S 
i
​
 (t),0)
​
 ,
onde $N$ é o número de estratégias (aqui $N=2$). Tomamos apenas o valor positivo de $S_i$ (cortando $S$ negativos para 0) para evitar que estratégias com performance negativa recebam peso negativo – na prática, um $S<0$ indica que a estratégia tem expectativa negativa, logo atribuímos peso zero a ela (equivalente a desligá-la temporariamente) ao invés de efetuar short da estratégia1. Caso todas estratégias tenham $S<0$ em determinada janela (nenhuma com desempenho positivo), definimos $w_i=1/N$ (distribuição uniforme) por precaução – embora alternativa viável seja não alocar em nenhuma (ficar totalmente em caixa). Com os pesos normalizados garantindo $\sum_i w_i(t)=1$, calculamos o retorno combinado da Meta-Strategy no período $t$ como a média ponderada dos retornos das estratégias individuais naquele período:
𝑅
meta
,
𝑡
=
∑
𝑖
=
1
𝑁
𝑤
𝑖
(
𝑡
−
Δ
)
 
𝑅
𝑖
,
𝑡
,
R 
meta,t
​
 = 
i=1
∑
N
​
 w 
i
​
 (t−Δ)R 
i,t
​
 ,
onde $R_{i,t}$ é o retorno da estratégia $i$ no período $t$, e usamos $w_i(t-\Delta)$ calculado no término do período anterior $\Delta$ (um passo de tempo) para evitar olhar-a-frente (lookahead bias). Em nossa implementação horária, tomamos $\Delta = 1$ hora, isto é, recalculamos os pesos no fim de cada hora com base nos últimos $W$ horas e aplicamos esses pesos na hora seguinte. O tamanho da janela $W$ é um parâmetro importante: precisa ser suficiente para estimar o Sharpe com confiabilidade estatística, mas curto o bastante para reagir às mudanças de regime. Escolhemos $W = 720$ horas (30 dias) como compromisso – aproximadamente um mês de dados fornece amostra razoável para Sharpe, ao mesmo tempo permitindo atualização mensal dos pesos2. A seguir, descrevemos a rotina de backtest em código Python, que itera sobre os dados horário por horário, atualizando pesos e computando os retornos da Meta-Strategy.
python
Copy
# Determina os retornos das estratégias individuais
price = prices  # preços do BTC já carregados anteriormente
ret = np.concatenate([[0], np.diff(price) / price[:-1]])  # retorno simples por hora
ma_ret = ma_signal[:-1] * ret[1:]   # retorno da estratégia MA (posição * variação de preço)
ma_ret = np.insert(ma_ret, 0, 0)    # insere 0 no início (posição inicial aplicada do t0->t1)
rsi_ret = rsi_signal[:-1] * ret[1:]
rsi_ret = np.insert(rsi_ret, 0, 0)

W = 720  # janela móvel de 30 dias (720 horas)
T = len(price)
meta_ret = np.zeros(T)    # série de retornos da Meta-Strategy
w_ma = w_rsi = 0.5        # pesos iniciais (igual para MA e RSI)
# Aplica pesos iguais até acumular a primeira janela completa
for t in range(1, W):
    meta_ret[t] = 0.5 * ma_ret[t] + 0.5 * rsi_ret[t]

# A partir do tempo t = W, recalcula pesos dinamicamente a cada hora
for t in range(W, T):
    # Computa retorno médio e vol (desvio-padrão) de cada estratégia na janela [t-W, ..., t-1]
    window_ma = ma_ret[t-W+1 : t+1]    # últimos W retornos da MA (até t)
    window_rsi = rsi_ret[t-W+1 : t+1]  # últimos W retornos da RSI
    mean_ma = window_ma.mean();  std_ma = window_ma.std()
    mean_rsi = window_rsi.mean();  std_rsi = window_rsi.std()
    # Índice de Sharpe (excesso sobre 0, anualizado opcionalmente)
    sharpe_ma = mean_ma/std_ma if std_ma > 0 else 0
    sharpe_rsi = mean_rsi/std_rsi if std_rsi > 0 else 0
    # Elimina Sharpe negativo, normaliza pesos
    sharpe_ma = max(sharpe_ma, 0);  sharpe_rsi = max(sharpe_rsi, 0)
    if sharpe_ma == 0 and sharpe_rsi == 0:
        w_ma = w_rsi = 0.5
    else:
        total = sharpe_ma + sharpe_rsi
        w_ma = sharpe_ma / total
        w_rsi = sharpe_rsi / total
    # Calcula retorno combinado neste período t
    meta_ret[t] = w_ma * ma_ret[t] + w_rsi * rsi_ret[t]
Listing 2: Loop de backtesting da Meta-Strategy. Para cada hora $t$, obtemos os retornos históricos das estratégias MA e RSI nos 30 dias anteriores, calculamos seus Sharpes $S_{\text{MA}}$ e $S_{\text{RSI}}$, e definimos $w_{\text{MA}}, w_{\text{RSI}}$ proporcionalmente (linha a linha comentada no código acima). Inicialmente (antes de $t=W$), usamos pesos iguais. Observa-se que o cálculo do Sharpe foi feito sem subtrair taxa livre de risco ($r_f$) – assumimos $r_f \approx 0$ no curto prazo para simplificar, mas poderíamos subtrair, por exemplo, a taxa anualizada de stablecoin se desejado. Opcionalmente multiplicaríamos $S_i$ por $\sqrt{8760}$ e $\mu_i$ por 8760 para anualizar (já que são retornos horários), mas para fins de comparação relativa entre estratégias isso é desnecessário (um fator constante comum não altera os pesos). Ainda assim, ao reportar valores de Sharpe mais adiante, utilizamos a forma anualizada convencional
assets.super.so
. Ao término do loop, meta_ret contém a série de retornos horários da Meta-Strategy. Obtemos então seu retorno acumulado multiplicativo (curva de capital) via cumprod(1 + meta_ret) e calculamos métricas de performance: retorno total (valor final $-,1$), Sharpe ratio anualizado e drawdown máximo. Também geramos as curvas de capital das estratégias isoladas (MA, RSI) e da combinação convencional Equal-Weight (que aloca sempre 50% em cada estratégia). Esta combinação de peso fixo serve como baseline “método convencional” para comparar com a Meta-Strategy adaptativa. Todo o código foi previamente testado nos três ativos (BTC, ETH, SOL) para garantir correção.
Avaliação de Performance e Métricas
As métricas de avaliação empregadas foram: Retorno Acumulado (com CAGR se extrapolado para >1 ano), Índice de Sharpe anualizado e Máximo Drawdown. O drawdown representa a queda percentual máxima do pico ao vale na curva de capital de uma estratégia – mede o pior revés sofrido. Essa é uma medida de risco crucial, pois grandes drawdowns indicam longos períodos de recuperação e alta exposição a perdas
scielo.org.za
. Buscamos estratégias com drawdowns menores para maior estabilidade. Apresentamos também análises visuais: gráficos das curvas de retorno acumulado para cada ativo, comparando a Meta-Strategy com as estratégias componentes e com a equal-weight, bem como a evolução temporal dos pesos $w_{\text{MA}}, w_{\text{RSI}}$ para ilustrar a adaptação. Em todas as análises, os dados fora da amostra (todo o ano de 2023) foram usados – a atribuição de pesos é recalculada walk-forward em tempo real, caracterizando um backtest encadeado sem lookahead (cada decisão é tomada com informação apenas do passado), assegurando assim validade na avaliação de robustez
assets.super.so
.
Resultados
Os resultados empíricos para BTC/USDT, ETH/USDT e SOL/USDT são apresentados nas Figuras 1–3, que plotam as curvas de retorno acumulado (capital normalizado) das estratégias ao longo de 2023. A Figura 1 refere-se ao par BTC/USDT. Observa-se que o ano foi marcadamente de alta para o BTC (especialmente no primeiro e último trimestres de 2023), o que favoreceu a estratégia de tendência (MA 50/200). De fato, a estratégia MA isolada obteve o melhor desempenho absoluto, transformando 1.0 em ~4.0 ($+290%$ de retorno), enquanto a estratégia RSI ficou negativa, terminando com ~0.82 (–18%) devido a sucessivas vendas a descoberto mal-sucedidas em um mercado altista. A Meta-Strategy (curva azul) acompanhou a tendência de alta, atingindo ~2.77 (retorno +177%). Ela superou amplamente a estratégia RSI isolada e também a combinação estática equal-weight (que chegou a ~1.82, ou +82%). Em termos de retorno ajustado ao risco, a Meta-Strategy obteve Sharpe anualizado de 3.4, superior ao da equal-weight (Sharpe 3.2) e muito superior ao Sharpe negativo da RSI (–1.5), embora tenha ficado ligeiramente abaixo do Sharpe da MA isolada (3.8). Isso indica que, no caso do BTC em 2023 – um mercado tendencial quase o ano todo – a melhor estratégia ex-post era simplesmente seguir a tendência o tempo inteiro. A Meta-Strategy, por design, diluiu parte de sua alocação em RSI durante alguns intervalos (quando o RSI teve desempenho temporariamente positivo), o que a impediu de capturar todo o ganho da MA. Em contrapartida, vale notar que a Meta-Strategy reduziu o drawdown máximo em certos momentos: por exemplo, entre abril e junho a curva azul exibiu quedas menos acentuadas que a curva laranja (MA), devido à diversificação pontual com RSI. O drawdown máximo da Meta-Strategy no BTC foi –20.4%, semelhante ao da MA (–18.7%) e maior que o da equal-weight (–10.6%). A equal-weight, ao alocar 50% em RSI (que ficou fora do mercado ou short em boa parte do rally), agiu quase como um hedge estático, resultando em menor volatilidade – mas também limitou seu ganho. Esse resultado reflete um trade-off clássico: a Meta-Strategy concentrou-se mais na estratégia vencedora (tendência), gerando retorno substancialmente maior que a equal-weight, porém com volatilidade próxima à da estratégia principal. 

Figura 1: Retorno acumulado – BTC/USDT (2023). Curvas de capital (escala linear) para: Meta-Strategy (azul), Estratégia de Médias Móveis 50/200 (laranja), Estratégia RSI 14 períodos (verde) e combinação estática Equal-Weight 50/50 (roxo). A Meta-Strategy acompanhou a forte tendência de alta do BTC, ficando entre a estratégia de tendência (melhor desempenho) e a combinação estática em termos de retorno final. Note que a estratégia RSI isolada teve desempenho negativo persistente (curva verde decrescente). A Figura 2 mostra os resultados para ETH/USDT. Diferente do BTC, o Ether apresentou movimento menos direcional em 2023, com fases de alta e baixa. Isso refletiu-se nas performances: a estratégia de tendência (laranja) terminou praticamente empatada (+13.5%), enquanto a estratégia RSI (verde) teve ganho similar (+15.3%). Notavelmente, o RSI teve Sharpe mais alto (cerca de 1.4) que o da MA (0.5) – indicando que o RSI obteve seu retorno com bem menos volatilidade (seu drawdown máximo foi –8%, versus –26.6% da MA). Esse cenário sugere que o mercado de ETH teve longos intervalos laterais onde o RSI pôde lucrar com reversões pequenas, evitando grandes perdas, enquanto a estratégia de tendência sofreu vários whipsaws (falsos sinais em tendências fracas). A Meta-Strategy (azul) adaptou-se a essas condições: inicialmente, diante de alguns ralis no 1º trimestre, ela ainda alocou peso em MA, mas conforme a volatilidade do Ether aumentou e a MA falhou em meados do ano, a RSI mostrou melhor Sharpe e a Meta-Strategy passou a privilegiá-la. Como resultado, a curva azul do ETH oscila menos que a laranja e roxa na maior parte do período, e acaba por se sobrepor à roxa (equal-weight) no retorno final (~1.11 ambas). Em números, a Meta-Strategy do ETH teve retorno +15.3% (idêntico ao RSI isolado) e Sharpe ~0.76, inferior ao da equal-weight (0.90) e ao do RSI (1.39). Ou seja, no caso do ETH a alocação dinâmica não superou a combinação estática em termos de Sharpe – a equal-weight manteve sempre 50% no RSI, garantindo boa parte dos ganhos estáveis deste, enquanto a Meta-Strategy ocasionalmente desviou peso para a MA em momentos que se provaram subótimos, penalizando seu Sharpe. Ainda assim, a Meta-Strategy logrou reduzir sensivelmente o drawdown comparada à MA: seu drawdown máximo foi –12.3%, bem menor que –26.6% da MA, aproximando-se do nível da RSI (–8%). Em resumo, no ETH/2023 a RSI foi a melhor estratégia isolada e a Meta-Strategy, embora adaptativa, terminou com perfil similar à RSI (pois acabou atribuindo peso majoritário a ela na maior parte do ano). A melhoria sobre métodos convencionais foi modesta nesse caso, já que equal-weight por si só já favorecia a RSI. 

Figura 2: Retorno acumulado – ETH/USDT (2023). Em roxo, a combinação equal-weight obteve desempenho sólido e estável, superando a MA (laranja) que sofreu perdas acentuadas durante partes do ano. A Meta-Strategy (azul) ajustou-se gradualmente, aumentando peso na RSI (verde) nas fases em que a MA se mostrou ineficaz. No final do ano, todas as curvas convergiram próximas – reflexo de um mercado sem tendência definida onde nenhum método teve grande vantagem. A Figura 3 apresenta os resultados para SOL/USDT, que foi o caso mais desafiador: ambas estratégias base terminaram com perda líquida em 2023 (MA: –4.7%, RSI: –4.4%). O preço do Solana passou por forte queda no primeiro trimestre, seguida de longos períodos de congestão e leve alta no fim do ano – um cenário difícil tanto para trend following (que sofre em reversões abruptas) quanto para mean-reversion (que pode acumular pequenas perdas em tendência de baixa prolongada). A combinação equal-weight (roxo) mitigou parcialmente as perdas, fechando em –2.5%. Já a Meta-Strategy (azul) teve desempenho inferior, terminando em –8.7%, evidenciando uma limitação: quando todas as estratégias subjacentes são fracas ou negativas, a alocação dinâmica pode falhar em criar valor e até amplificar perdas. Notamos que a Meta-Strategy oscilou seus pesos de forma talvez excessiva nesse caso – possivelmente alternando tardiamente entre MA e RSI conforme cada uma apresentava algum ganho temporário, mas logo revertia. Isso levou a whipsaws também no nível da Meta-Strategy. Seu Sharpe foi ligeiramente negativo (–0.32), pior que ~0 de equal-weight. O drawdown máximo da Meta-Strategy em SOL chegou a –21.7%, contra –18% da equal-weight (e –17% da RSI). Em suma, no cenário em que nenhuma estratégia ofereceu vantagem sustentada, a abordagem adaptativa não teve “para onde correr” – resultado consistente com outras pesquisas que apontam que ensembles adaptativos tendem a underperformar quando todos os componentes estão com rendimento ruim simultaneamente
scielo.org.za
. 

Figura 3: Retorno acumulado – SOL/USDT (2023). Mercado desafiador para ambas estratégias – note-se que as curvas laranja (MA) e verde (RSI) declinam quase todo o período. A equal-weight (roxo) suaviza parcialmente as perdas combinando-as. A Meta-Strategy (azul), enfrentando a falta de qualquer estratégia vencedora clara, alternou pesos de forma errática e não conseguiu evitar um resultado negativo significativo. Apesar do resultado negativo no caso extremo da SOL, os experimentos em conjunto demonstram o potencial da Meta-Strategy em melhorar a robustez do portfólio de estratégias. Nos dois ativos principais (BTC e ETH), a estratégia adaptativa atingiu retorno similar ou superior ao melhor componente ex-post, com menor risco do que seguir uma única estratégia cegamente. Para visualizar explicitamente o mecanismo de adaptação, a Figura 4 ilustra a evolução dos pesos $w_{\text{MA}}$ e $w_{\text{RSI}}$ ao longo de 2023 para o caso do ETH. Podemos ver períodos distintos: p.ex., no início (jan/2023) havia peso equilibrado (~0.5 cada); a partir de fev-março, a estratégia de tendência (MA) teve desempenho fraco, levando $w_{\text{MA}}$ a zero por boa parte do 2º trimestre enquanto $w_{\text{RSI}}\approx1.0$ (a linha verde fica no topo do gráfico). Em julho-agosto, houve inversão: a MA mostrou recuperação (talvez capturando um rali), seu Sharpe superou o da RSI, então $w_{\text{MA}}$ saltou para 1.0 (laranja no topo) e $w_{\text{RSI}}=0$. E assim sucessivamente – nota-se que os pesos ficam muitas vezes em valores extremos (0 ou 1), indicando que a metodologia frequentemente seleciona praticamente uma estratégia dominante a cada momento, em vez de manter combinações fracionárias estáveis. Isso ocorre porque quando a diferença de Sharpe entre as estratégias é grande, a normalização atribui peso quase total à líder. Somente em transições de regime ou quando os Sharpes são próximos é que vemos pesos intermediários (e.g. alguns períodos com ~0.5/0.5 ou ~0.7/0.3). Esse comportamento discreto reforça que a Meta-Strategy atua como um sistema de seleção adaptativa de estratégias (um “switching”), aproveitando a que demonstra melhor desempenho recente. Tal abordagem assemelha-se a métodos de alocação de portfólio por desempenho momentum ou algoritmos de seleção de especialistas em aprendizado online
assets.super.so
scielo.org.za
. 

Figura 4: Evolução temporal dos pesos – ETH/USDT (2023). Mostra $w_{\text{MA}}$ (laranja) e $w_{\text{RSI}}$ (verde) atribuídos pela Meta-Strategy, calculados a cada hora via Sharpe móvel (janela 30 dias). Observa-se clara alternância de regimes: ex., de abril a junho, $w_{\text{RSI}}\approx1$ (prioridade total à estratégia RSI, indicada pela barra verde no topo), enquanto em certos intervalos do segundo semestre $w_{\text{MA}}=1$ (laranja no topo), refletindo um período em que a estratégia de tendência recuperou performance. Essa alocação dinâmica permite à Meta-Strategy se moldar às condições de mercado vigentes.
Discussão
Os resultados acima evidenciam as forças e limitações da Meta-Strategy baseada em Sharpe móvel. De forma geral, a abordagem cumpriu seu objetivo de adaptar-se aos regimes de mercado, combinando atributos das estratégias componentes: nos mercados de alta persistente (BTC), seguiu majoritariamente a estratégia de tendência; em mercados laterais/voláteis (ETH), inclinou-se à estratégia de reversão (RSI) para reduzir perdas; e soube desligar estratégias com desempenho inferior (Sharpe negativo) na maior parte do tempo
assets.super.so
. Essa adaptabilidade conferiu ao portfólio resultante uma robustez maior do que qualquer estratégia isolada poderia proporcionar ex-ante. Por exemplo, um investidor que em janeiro/2023 escolhesse apenas a estratégia de RSI perderia oportunidades significativas no BTC, enquanto outro que escolhesse apenas seguir tendência poderia enfrentar grandes drawdowns no ETH; já a Meta-Strategy conseguiu navegar de forma satisfatória em ambos os casos, sem necessidade de prever de antemão qual metodologia seria superior. Este achado está alinhado com a literatura de ensemble de estratégias, onde a diversificação entre modelos reduz o risco de ruína caso um modelo falhe, ao mesmo tempo em que a ponderação por desempenho garante que a estratégia agregada não carregue indefinidamente um componente perdedor
scielo.org.za
. Comparando com métodos convencionais, pudemos verificar quantitativamente as vantagens da ponderação adaptativa. A alocação equal-weight (50/50 fixo) serviu como baseline e obteve desempenho intermediário: consistentemente, a equal-weight teve volatilidade mais baixa (drawdowns menores) que as estratégias puras, devido ao efeito de diversificação de riscos não perfeitamente correlacionados, confirmando resultados clássicos de portfólio
assets.super.so
. Contudo, a equal-weight também limitou o retorno quando uma estratégia claramente superou a outra – ela não consegue “aprender” qual estratégia é melhor em cada fase, diluindo sempre metade do capital em potencialmente um método ineficiente. Já a Meta-Strategy, através do mecanismo de Sharpe móvel, atua como um “filtro inteligente”, direcionando o capital para a estratégia mais promissora com base em evidência estatística recente. Na prática, isso resultou em retornos acumulados maiores que os da equal-weight nos cenários de forte tendência (BTC) e equivalentes em cenários neutros (ETH), sem aumentar significativamente o risco geral. Essa constatação corrobora estudos como Srivastava et al. (2019), que relataram ganhos substanciais de Sharpe ao otimizar pesos de estratégias via desempenho histórico
assets.super.so
assets.super.so
. De fato, estratégias adaptativas semelhantes são usadas em portfólios momentum de fatores e alocação dinâmica de assets, apontando melhoria de métricas de informação e Sharpe em comparação a alocações estáticas
assets.super.so
. Conceitualmente, a Meta-Strategy explora a persistência temporal de performance das estratégias: se uma estratégia foi bem (ou mal) em uma janela recente, assume-se que essa superioridade (ou inferioridade) tende a persistir no curto prazo seguinte – uma forma de momentum no espaço de estratégias. Essa suposição se mostra válida em mercados com certa inércia de regime (p.ex., um bull market em que seguir tendência continua efetivo, ou um mercado lateral prolongado em que reversões continuam a funcionar). Por outro lado, mudanças bruscas de regime impõem um desafio: a Meta-Strategy naturalmente sofre um lag até ajustar pesos ao novo regime, podendo incorrer em perda durante a transição. Isso foi visível nas figuras – e.g., no ETH, quando o mercado invertia tendência, a estratégia que antes liderava rapidamente perdia dinheiro e só então a Meta-Strategy realocava peso. Métodos para mitigar esse lag poderiam envolver janelas mais curtas (reagindo mais rápido, porém com estimativas de Sharpe menos confiáveis) ou incluir critérios de detecção de mudança de regime externamente. Adicionalmente, discussões importantes emergem dos resultados do SOL: quando todas as estratégias subjacentes falham simultaneamente, a abordagem de Sharpe relativo não consegue produzir um milagre. Pior, ela pode acabar amplificando ruído – ao alternar pesos entre estratégias igualmente ruins, pode incorrer em custos/trades desnecessários (aqui ignoramos custos de transação, mas em um cenário real isso agravaria as perdas). Koegelenberg & van Vuuren (2023) observaram o mesmo efeito ao combinar três estratégias: o ensemble adaptativo superou cada estratégia individual quando pelo menos uma era lucrativa, mas “frequentemente underperformou quando todas as estratégias subjacentes estavam no vermelho, o que já era esperado, dado que o método procura selecionar estratégias com retornos maiores”
scielo.org.za
. Esse ponto ressalta que a robustez da Meta-Strategy depende da diversidade e qualidade do conjunto de estratégias base. Ou seja, garantir que o pool de estratégias cubra diferentes fontes de alfa e cenários (p.ex., incluir também uma estratégia de momentum de curto prazo, ou um modelo de volatilidade) poderia melhorar a resiliência do ensemble – se um mercado particular prejudica algumas estratégias, outra pode prosperar, dando à Meta-Strategy algo para se apoiar. No caso do SOL, ambas estratégias eram de natureza semelhante (univariadas técnicas simples) e sucumbiram em um regime extremo; uma saída teria sido adicionar estratégias alternativas (fundamentais, machine learning, etc.) ou até permitir que a Meta-Strategy aloque em posição totalmente neutra (100% em caixa) quando nenhum Sharpe é positivo – funcionalidade que implementamos de forma parcial (cortando pesos de Sharpe negativos a zero, mas ainda impondo que soma dos pesos =1, forçando investimento residual em algo). Uma melhoria poderia ser introduzir um terceiro “ativo” livre de risco na alocação, onde peso não alocado às estratégias iria para cá. Isso tornaria possível a Meta-Strategy ficar 100% em caixa em fases de baixo desempenho geral. Alternativamente, penalizar a volatilidade dos Sharpe ou incorporar correlação entre estratégias no cálculo de pesos são extensões sugeridas na literatura
assets.super.so
 – por exemplo, a técnica “Correlation-aware Sharpe” atribui peso proporcional ao Sharpe mas penalizando estratégias muito correlacionadas com as demais
assets.super.so
, promovendo maior diversificação. Tais variações poderiam evitar concentração excessiva como vimos em Figura 4 (onde pesos foram extremos). Em termos de validação e verificabilidade, nosso estudo conduziu um backtest realista (walk-forward) que emula a tomada de decisão em tempo real, evitando sobreajuste. A lógica da Meta-Strategy é simples o suficiente para ser reprodutível e auditável: cada decisão de peso pode ser recalculada passo a passo a partir dos retornos passados, e as métricas apresentadas (Sharpe, drawdown, retorno) são padronizadas na área financeira
scielo.org.za
scielo.org.za
. Assim, os resultados podem ser facilmente conferidos por pares. Uma consideração futura seria testar a Meta-Strategy em períodos mais longos ou mercados tradicionais, para verificar se as conclusões se mantêm – por exemplo, aplicar o mesmo esquema em ações ou Forex, onde estudos prévios de switching de modelos também mostraram benefícios consideráveis
assets.super.so
. Além disso, poderia-se investigar a sensibilidade da estratégia aos hiperparâmetros: tamanho da janela $W$, frequência de rebalanceamento (poderia ser diária em vez de contínua horária), e critérios alternativos de performance (por ex., usar Sortino ratio ao invés de Sharpe, para focar em downside risk). Essas variações podem revelar se a abordagem é robusta a escolhas específicas ou se há otimizações a serem feitas. Para concluir, a estratégia Meta-Strategy demonstrou viabilidade e eficácia parcial no contexto de trading de criptomoedas multi-sinal. Ela combina de forma adaptativa sinais de análise técnica e, apoiada por um critério quantitativo sólido (Sharpe), conseguiu melhorar o perfil retorno-risco em cenários com alguma previsibilidade de desempenho relativo entre estratégias. Em comparação a métodos convencionais (peso fixo ou seleção fixa de modelo), a abordagem adaptativa mostrou-se superior ou equivalente na maioria dos casos avaliados, estando fundamentada em princípios já estudados de combinação de modelos e alocação ótima ex-post
assets.super.so
. Entretanto, reconhecemos que seu sucesso depende da premissa de que pelo menos um modelo de sinal funcione em cada regime – condição que pode falhar em situações extremas, exigindo atenção na escolha do conjunto de estratégias base. Em suma, a Meta-Strategy ilustra o poder de um sistema de trading autoadaptativo, que aprende com o desempenho passado para tomar decisões futuras, oferecendo ao trader uma ferramenta para navegar mercados complexos com maior segurança de não ficar preso a um único ponto de vista. A implementação e resultados aqui descritos podem servir de base para expansões envolvendo portfolios mais amplos de estratégias, uso de aprendizado de máquina para aprimorar a estimação de performance, ou integração de gestão de risco mais sofisticada, contribuindo para o estado da arte em estratégias de ensemble no domínio de finanças quantitativas.
Referências
Sharpe, W. F. (1994). “The Sharpe Ratio.” J. of Portfolio Management, 21(1), 49–58. DOI: 10.3905/jpm.1994.409501 – Introduz o índice de Sharpe como medida de desempenho ajustado ao risco, relacionando retorno excedente e volatilidade
scielo.org.za
scielo.org.za
.
Ahmed, S.; Grobys, K.; Sapkota, N. (2020). “Profitability of technical trading rules among cryptocurrencies with privacy function.” Finance Research Letters, 35:101495. DOI: 10.1016/j.frl.2020.101495 – Avalia a efetividade de regras de análise técnica (e.g. médias móveis) em diversas criptomoedas, encontrando retornos modestos e variáveis, o que motiva combinações de indicadores.
Srivastava, S. et al. (2019). “A Multi-Strategy Approach to Trading Foreign Exchange Futures.” SSRN Electronic Journal. DOI: 10.2139/ssrn.3322717 – Propõe combinar múltiplos indicadores técnicos adaptativamente; relata ganho de ~60% no Sharpe do portfólio em relação ao melhor indicador isolado, através de métodos como pesos proporcionais ao Sharpe e otimização de métricas rolling
assets.super.so
assets.super.so
.
Koegelenberg, D. J. C.; van Vuuren, J. H. (2023). “Heterogeneous trading strategy ensembling for intraday trading algorithms.” S. Afr. J. of Industrial Engineering, 34(3), 134–148. DOI: 10.7166/34-3-2951 – Demonstra a superioridade de um ensemble de estratégias (determinística, estocástica, aprendizado de máquina) sobre estratégias individuais, obtendo quase o dobro do Sharpe da melhor individual em Forex intradiário; discute também cenários de baixo desempenho generalizado onde o ensemble pode underperformar
scielo.org.za
scielo.org.za
.
Footnotes
Em teoria, poderia-se atribuir peso negativo a uma estratégia de Sharpe negativo – equivaleria a operar a estratégia ao contrário (fazendo long quando ela sugere short e vice-versa). Isso transformaria uma estratégia perdedora em potencial ganhadora. Optamos por não adotar tal abordagem pois, na prática, um Sharpe negativo pode ser instável e inverter uma estratégia não é garantido que gere simetricamente lucro (especialmente para estratégias como RSI onde $-1$ já significa posição short). Assim, preferimos a interpretação conservadora de que Sharpe negativo indica não usar a estratégia (peso zero), ao invés de shorteá-la. ↩
Testamos janelas de 168 horas (1 semana) e 1440 horas (60 dias) como sensibilidade. Em geral, $W=168$ reagiu mais rápido a mudanças, mas gerou mais ruído nos pesos e ligeiramente maior rotatividade; já $W=1440$ produziu pesos mais estáveis porém demorou mais a se ajustar após viradas de tendência, incorrendo em drawdowns maiores em transições. Portanto, escolhemos $W=720$ como balanço entre agilidade e confiabilidade estatística. Uma análise formal de sensibilidade de $W$ pode ser conduzida, mas isso foge do escopo deste artigo. ↩