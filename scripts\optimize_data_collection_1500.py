#!/usr/bin/env python3
"""
QUALIA Data Collection Optimization - Using <PERSON><PERSON>oin's Maximum 1500 Candles
Otimiza coleta de dados usando o limite máximo da KuCoin
"""

import asyncio
import json
import os
import time
from datetime import datetime, timedelta
from pathlib import Path
import ccxt.async_support as ccxt

async def main():
    print("🚀 QUALIA Data Collection Optimization - 1500 Candles")
    print("=" * 60)
    
    # Configurar exchange com limite máximo
    exchange = ccxt.kucoin({
        'apiKey': os.getenv('KUCOIN_API_KEY'),
        'secret': os.getenv('KUCOIN_SECRET_KEY'),
        'password': os.getenv('KUCOIN_PASSPHRASE'),
        'sandbox': False,
        'enableRateLimit': True,
        'timeout': 60000,  # 60 segundos
        'rateLimit': 100,  # 100ms entre requests
    })
    
    symbols = ["BTC/USDT", "ETH/USDT", "ADA/USDT", "SOL/USDT"]
    timeframes = ["5m", "15m", "1h"]
    cache_dir = Path("data/cache")
    cache_dir.mkdir(parents=True, exist_ok=True)
    
    # Estatísticas
    total_requests = 0
    total_candles = 0
    start_time = time.time()
    
    try:
        await exchange.load_markets()
        print(f"✅ Mercados carregados: {len(exchange.markets)} símbolos")
        
        for symbol in symbols:
            for timeframe in timeframes:
                try:
                    print(f"\n📊 Coletando {symbol}@{timeframe} com limite 1500...")
                    
                    request_start = time.time()
                    
                    # USAR LIMITE MÁXIMO DA KUCOIN: 1500 CANDLES
                    ohlcv = await exchange.fetch_ohlcv(
                        symbol=symbol, 
                        timeframe=timeframe, 
                        limit=1500  # MÁXIMO PERMITIDO PELA KUCOIN
                    )
                    
                    request_duration = time.time() - request_start
                    total_requests += 1
                    
                    if ohlcv and len(ohlcv) > 0:
                        candles_count = len(ohlcv)
                        total_candles += candles_count
                        
                        print(f"✅ {symbol}@{timeframe}: {candles_count} candles em {request_duration:.2f}s")
                        
                        # Calcular período coberto
                        if candles_count > 1:
                            first_time = datetime.fromtimestamp(ohlcv[0][0] / 1000)
                            last_time = datetime.fromtimestamp(ohlcv[-1][0] / 1000)
                            period = last_time - first_time
                            print(f"   📅 Período: {first_time.strftime('%Y-%m-%d %H:%M')} -> {last_time.strftime('%Y-%m-%d %H:%M')}")
                            print(f"   ⏱️ Cobertura: {period}")
                        
                        # Salvar dados otimizados
                        symbol_clean = symbol.replace("/", "")
                        json_path = cache_dir / f"{symbol_clean}_{timeframe}.json"
                        
                        cache_data = {
                            "symbol": symbol,
                            "timeframe": timeframe,
                            "data": ohlcv,
                            "last_update": datetime.now().isoformat(),
                            "count": candles_count,
                            "collection_method": "optimized_1500_limit",
                            "request_duration_seconds": request_duration,
                            "metadata": {
                                "first_timestamp": ohlcv[0][0] if ohlcv else None,
                                "last_timestamp": ohlcv[-1][0] if ohlcv else None,
                                "source": "kucoin_optimized_1500",
                                "efficiency_score": candles_count / request_duration if request_duration > 0 else 0
                            }
                        }
                        
                        with open(json_path, 'w') as f:
                            json.dump(cache_data, f, indent=2)
                            
                        print(f"   💾 Salvo: {json_path}")
                        
                        # Verificar eficiência
                        efficiency = candles_count / request_duration if request_duration > 0 else 0
                        if efficiency > 50:
                            print(f"   🚀 Alta eficiência: {efficiency:.1f} candles/segundo")
                        elif efficiency < 10:
                            print(f"   ⚠️ Baixa eficiência: {efficiency:.1f} candles/segundo")
                        
                    else:
                        print(f"❌ {symbol}@{timeframe}: Nenhum dado retornado")
                        
                    # Rate limiting respeitoso
                    await asyncio.sleep(0.2)  # 200ms entre requests
                    
                except Exception as e:
                    print(f"❌ Erro coletando {symbol}@{timeframe}: {e}")
                    await asyncio.sleep(1)  # Pausa maior em caso de erro
                    
    except Exception as e:
        print(f"❌ Erro geral: {e}")
    finally:
        await exchange.close()
    
    # Estatísticas finais
    total_duration = time.time() - start_time
    print(f"\n🎉 COLETA OTIMIZADA CONCLUÍDA!")
    print("=" * 60)
    print(f"📊 Total de requests: {total_requests}")
    print(f"📈 Total de candles: {total_candles}")
    print(f"⏱️ Tempo total: {total_duration:.2f}s")
    print(f"🚀 Eficiência geral: {total_candles/total_duration:.1f} candles/segundo")
    print(f"📊 Média por request: {total_candles/total_requests:.1f} candles/request")
    
    # Comparação com método anterior
    old_method_candles = total_requests * 300  # Método anterior ~300 candles/request
    improvement = (total_candles / old_method_candles - 1) * 100 if old_method_candles > 0 else 0
    print(f"📈 Melhoria vs método anterior: +{improvement:.1f}% mais candles")
    
    print(f"\n💡 PRÓXIMOS PASSOS:")
    print(f"   1. Execute: python scripts/start_real_trading.py --config config/permissive_config.yaml")
    print(f"   2. Sistema agora tem {total_candles} candles para análise")
    print(f"   3. Geração de sinais deve ser mais eficiente")

if __name__ == "__main__":
    asyncio.run(main())
