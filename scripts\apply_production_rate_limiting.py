#!/usr/bin/env python3
"""
Script para aplicar configurações de rate limiting otimizadas para produção
"""

import os
import sys
from pathlib import Path


def apply_base_integration_patch():
    """Aplica patch na classe base de integração"""
    
    file_path = Path(__file__).parent.parent / "src" / "qualia" / "market" / "base_integration.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Patches específicos para melhorar rate limiting
        patches = [
            # 1. Aumentar timeout padrão do ticker
            ('ticker_timeout = float(get_env("TICKER_TIMEOUT", "15", warn=False))', 
             'ticker_timeout = float(get_env("TICKER_TIMEOUT", "20", warn=False))'),
            
            # 2. Aumentar timeout padrão do OHLCV
            ('ohlcv_timeout = float(get_env("OHLCV_TIMEOUT", "20", warn=False))', 
             'ohlcv_timeout = float(get_env("OHLCV_TIMEOUT", "60", warn=False))'),
            
            # 3. Aumentar TTL do cache
            ('self.ticker_cache_ttl = float(get_env("TICKER_CACHE_TTL", "5", warn=False))', 
             'self.ticker_cache_ttl = float(get_env("TICKER_CACHE_TTL", "10", warn=False))'),
            
            # 4. Reduzir concurrent requests
            ('max_concurrent_requests: int = 5', 'max_concurrent_requests: int = 3'),
        ]
        
        changes_made = []
        original_content = content
        
        for old_pattern, new_pattern in patches:
            if old_pattern in content:
                content = content.replace(old_pattern, new_pattern)
                changes_made.append(f"  ✅ {old_pattern[:50]}... → {new_pattern[:50]}...")
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("📝 Aplicado patch em base_integration.py:")
            for change in changes_made:
                print(change)
            return True
        else:
            print("⚠️ Nenhuma mudança aplicada em base_integration.py")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao aplicar patch em base_integration.py: {e}")
        return False


def create_env_file():
    """Cria arquivo .env com configurações otimizadas"""
    
    env_path = Path(__file__).parent.parent / ".env.production"
    
    env_content = """# Configurações de Rate Limiting para Produção
# QUALIA Trading System

# === RATE LIMITING ===
RATE_LIMIT=2.0
TICKER_TIMEOUT=20.0
OHLCV_TIMEOUT=60.0
TICKER_CACHE_TTL=10.0
OHLCV_CACHE_TTL=120.0

# === RETRIES E BACKOFF ===
TICKER_RETRIES=2
OHLCV_RETRIES=2
TICKER_BACKOFF_BASE=2.0
OHLCV_BACKOFF_BASE=5.0

# === CIRCUIT BREAKER ===
API_FAIL_THRESHOLD=3
API_RECOVERY_TIMEOUT=30.0

# === KUCOIN ESPECÍFICO ===
KUCOIN_RATE_LIMIT=2.5
KUCOIN_MAX_CANDLES=500

# === CONCURRENT REQUESTS ===
MAX_CONCURRENT_REQUESTS=3

# === LOGGING ===
QUALIA_LOG_LEVEL=INFO
VERBOSE_RATE_LIMITING=false

# === MÉTRICAS ===
METRICS_ENABLED=true
STATSD_ENABLED=false

# === MODO DE PRODUÇÃO ===
PRODUCTION_MODE=true
CONSERVATIVE_MODE=true
GRACEFUL_DEGRADATION=true
"""
    
    try:
        with open(env_path, 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print(f"📄 Criado arquivo de configuração: {env_path}")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar arquivo .env: {e}")
        return False


def create_production_test():
    """Cria script de teste otimizado para produção"""
    
    file_path = Path(__file__).parent / "test_production_optimized.py"
    
    content = """#!/usr/bin/env python3
# Teste do sistema com configurações otimizadas para produção

import asyncio
import sys
import os
from datetime import datetime

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Aplicar configurações de produção via variáveis de ambiente
os.environ.update({
    'RATE_LIMIT': '2.0',
    'TICKER_TIMEOUT': '20.0',
    'OHLCV_TIMEOUT': '60.0',
    'TICKER_CACHE_TTL': '10.0',
    'OHLCV_CACHE_TTL': '120.0',
    'TICKER_RETRIES': '2',
    'OHLCV_RETRIES': '2',
    'API_FAIL_THRESHOLD': '3',
    'API_RECOVERY_TIMEOUT': '30.0',
    'MAX_CONCURRENT_REQUESTS': '3',
})


async def test_production_optimized():
    print("🚀 TESTE DE PRODUÇÃO OTIMIZADO")
    print("=" * 60)
    
    try:
        from qualia.market.kucoin_integration import KucoinIntegration
        print("✅ KucoinIntegration importado")
        
        # Criar integração com configurações otimizadas
        kucoin = KucoinIntegration(
            api_key=None,
            api_secret=None,
            password=None,
            use_websocket=False,
            ticker_timeout=20.0,
            ohlcv_timeout=60.0,
            ticker_retries=2,
            ticker_backoff_base=2.0,
            fail_threshold=3,
            recovery_timeout=30.0,
        )
        print("✅ Integração criada com configurações otimizadas")
        
        # Inicializar conexão
        await kucoin.initialize_connection()
        print("✅ Conexão inicializada")
        
        # Teste conservador: uma requisição por vez com intervalo
        print("\\n📊 TESTE CONSERVADOR")
        success_count = 0
        
        for i in range(3):
            try:
                print(f"🔄 Requisição {i+1}/3...")
                ticker = await kucoin.fetch_ticker("BTC/USDT")
                
                if ticker and 'last' in ticker:
                    success_count += 1
                    print(f"✅ Sucesso - ${ticker['last']:,.2f}")
                else:
                    print("❌ Ticker vazio")
                
                if i < 2:
                    await asyncio.sleep(5.0)
                
            except Exception as e:
                print(f"❌ Erro: {e}")
        
        # Fechar conexão
        await kucoin.close()
        print("✅ Conexão fechada")
        
        # Resultado final
        print(f"\\n🏁 Sucessos: {success_count}/3")
        
        if success_count >= 2:
            print("🎉 SISTEMA OTIMIZADO FUNCIONANDO!")
            return True
        else:
            print("⚠️ Sistema ainda precisa de ajustes")
            return False
        
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        return False


if __name__ == "__main__":
    result = asyncio.run(test_production_optimized())
    
    if result:
        print("🚀 PRONTO PARA PRODUÇÃO!")
    else:
        print("🔧 PRECISA DE MAIS AJUSTES!")
"""
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"🧪 Criado teste de produção: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar teste: {e}")
        return False


def main():
    """Função principal"""
    
    print("🔧 APLICANDO CONFIGURAÇÕES DE RATE LIMITING PARA PRODUÇÃO")
    print("=" * 70)
    
    # Aplicar patches
    success_count = 0
    
    if apply_base_integration_patch():
        success_count += 1
    
    if create_env_file():
        success_count += 1
    
    if create_production_test():
        success_count += 1
    
    # Resultado final
    print("\n🏁 RESULTADO FINAL")
    print("=" * 70)
    print(f"✅ Configurações aplicadas: {success_count}/3")
    
    if success_count >= 2:
        print("\n🎉 CONFIGURAÇÕES DE PRODUÇÃO APLICADAS!")
        print("\n💡 PRÓXIMOS PASSOS:")
        print("1. Execute: python scripts/test_production_optimized.py")
        print("2. Monitore as métricas de performance")
        print("3. Ajuste conforme necessário")
        
        print("\n📋 CONFIGURAÇÕES PRINCIPAIS:")
        print("   • Rate limit: 2.0s entre requisições")
        print("   • Ticker timeout: 20s")
        print("   • OHLCV timeout: 60s")
        print("   • Máximo 3 requisições simultâneas")
        print("   • Circuit breaker: 3 falhas")
        print("   • Cache TTL: 10s (ticker), 120s (OHLCV)")
        
        return True
    else:
        print("\n⚠️ Algumas configurações não foram aplicadas")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 
