from __future__ import annotations

from dataclasses import dataclass
from typing import Any, Dict, Sequence

from ..memory.event_bus import Simple<PERSON>ventBus
from ..memory.quantum_pattern_memory import Quantum<PERSON>atternMemory
from ..common_types import QuantumSignaturePacket
from ..utils.logger import get_logger

from ..events import SystemEvent

MARKET_PATTERN_EVENT = "market.pattern_detected"
MARKET_PATTERN_RECORDED = "market.pattern_recorded"


@dataclass(kw_only=True)
class MarketPatternDetected(SystemEvent):
    """Payload for :data:`MARKET_PATTERN_EVENT`."""

    vector: Sequence[float]
    metadata: Dict[str, Any] | None = None


@dataclass(kw_only=True)
class MarketPatternRecorded(SystemEvent):
    """Payload for :data:`MARKET_PATTERN_RECORDED`."""

    pattern_id: str


def register_pattern_memory_handler(
    qpm: QuantumPatternMemory,
    event_bus: SimpleEventBus,
    event_name: str = MARKET_PATTERN_EVENT,
) -> None:
    """Subscribe ``qpm`` to ``market.pattern_detected`` events.

    Parameters
    ----------
    qpm : QuantumPatternMemory
        Memory instance used to persist patterns.
    event_bus : SimpleEventBus
        Event bus responsible for delivering events.
    event_name : str, optional
        Name of the event to listen for. Defaults to
        :data:`MARKET_PATTERN_EVENT`.
    """

    logger = get_logger(__name__)

    def _handler(payload: Any) -> None:
        if isinstance(payload, MarketPatternDetected):
            vector = payload.vector
            metadata = payload.metadata or {}
        elif isinstance(payload, dict):
            vector = payload.get("vector")
            metadata = payload.get("metadata", {})
        else:
            vector = getattr(payload, "vector", None)
            metadata = getattr(payload, "metadata", {}) or {}
        if vector is None:
            logger.warning("MarketPatternHandler: evento sem vetor recebido")
            return
        packet = QuantumSignaturePacket(vector=list(vector), metrics={})
        try:
            qpm.store_pattern(
                packet,
                metadata.get("market_snapshot", {}),
                metadata.get("outcome", {}),
                metadata.get("decision_context"),
                extra_metadata=metadata.get("extra_metadata"),
            )
        except Exception:  # pragma: no cover - defensive
            logger.exception("MarketPatternHandler: falha ao armazenar padrão")

    event_bus.subscribe(event_name, _handler)


__all__ = [
    "MARKET_PATTERN_EVENT",
    "MarketPatternDetected",
    "MARKET_PATTERN_RECORDED",
    "MarketPatternRecorded",
    "register_pattern_memory_handler",
]
