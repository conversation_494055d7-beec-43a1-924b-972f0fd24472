#!/usr/bin/env python3
"""
QUALIA Offline Benchmark System
Implementa backtest de 90 dias com grid search para otimização de parâmetros.

Grid Search:
- price_amp: 1-10 (10 valores)
- news_amp: 1-10 (10 valores) 
- min_conf: 0.3-0.8 (6 valores)
Total: ~600 combinações × múltiplos símbolos = ~2000+ runs

Métricas avaliadas:
- Sharpe Ratio
- Max Drawdown
- Total Return
- Win Rate
- Volatility
"""

import sys
import os
import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json
from pathlib import Path
import itertools
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp

# Adicionar paths do projeto
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from qualia.config.config_loader import ConfigLoader
from qualia.market.kucoin_integration import KucoinIntegration
from qualia.strategies.strategy_factory import StrategyFactory
from qualia.consciousness.enhanced_data_collector import EnhancedDataCollector
from qualia.utils.logger import get_logger, setup_logging

# Setup logging
setup_logging()
logger = get_logger(__name__)

class OfflineBenchmark:
    """Sistema de benchmark offline para otimização de parâmetros QUALIA."""
    
    def __init__(self, config_path: str = "config/production_config.json"):
        """Inicializar benchmark system."""
        self.config_path = config_path
        self.config = None
        self.results = []
        self.data_cache = {}
        
        # Parâmetros do grid search
        self.price_amp_range = np.linspace(1.0, 10.0, 10)  # 1, 2, 3, ..., 10
        self.news_amp_range = np.linspace(1.0, 10.0, 10)   # 1, 2, 3, ..., 10
        self.min_conf_range = np.linspace(0.3, 0.8, 6)     # 0.3, 0.4, 0.5, 0.6, 0.7, 0.8
        
        # Período de backtest (90 dias)
        self.end_date = datetime.now()
        self.start_date = self.end_date - timedelta(days=90)
        
        logger.info(f"🔬 OfflineBenchmark inicializado")
        logger.info(f"📅 Período: {self.start_date.date()} → {self.end_date.date()}")
        logger.info(f"🎯 Grid: {len(self.price_amp_range)} × {len(self.news_amp_range)} × {len(self.min_conf_range)} = {len(self.price_amp_range) * len(self.news_amp_range) * len(self.min_conf_range)} combinações")

    async def load_config(self):
        """Carregar configuração do sistema."""
        try:
            config_loader = ConfigLoader(self.config_path)
            self.config = config_loader.load()
            logger.info(f"✅ Configuração carregada: {len(self.config.get('symbols', []))} símbolos")
            return True
        except Exception as e:
            logger.error(f"❌ Erro ao carregar configuração: {e}")
            return False

    async def collect_historical_data(self, symbol: str, timeframe: str = "1h") -> Optional[pd.DataFrame]:
        """Coletar dados históricos para backtesting (simulados para demonstração)."""
        cache_key = f"{symbol}_{timeframe}"

        if cache_key in self.data_cache:
            logger.debug(f"📊 Usando dados em cache para {cache_key}")
            return self.data_cache[cache_key]

        try:
            logger.info(f"📊 Gerando dados simulados para {symbol} ({timeframe})")

            # Gerar dados simulados para demonstração
            # Em produção, isso seria substituído pela coleta real de dados

            # Calcular número de candles necessários
            if timeframe == "1h":
                candles_needed = 90 * 24  # 90 dias × 24 horas
            elif timeframe == "15m":
                candles_needed = 90 * 24 * 4  # 90 dias × 96 candles/dia
            elif timeframe == "5m":
                candles_needed = 90 * 24 * 12  # 90 dias × 288 candles/dia
            else:
                candles_needed = 2160  # Default para 90 dias em 1h

            # Limitar para demonstração
            candles_needed = min(candles_needed, 1000)

            # Gerar dados simulados
            np.random.seed(42)  # Para reprodutibilidade

            # Preço base baseado no símbolo
            if 'BTC' in symbol:
                base_price = 50000
            elif 'ETH' in symbol:
                base_price = 3000
            elif 'BNB' in symbol:
                base_price = 400
            else:
                base_price = 1.0

            # Gerar série temporal com random walk
            returns = np.random.normal(0, 0.02, candles_needed)  # 2% volatilidade diária
            prices = [base_price]

            for ret in returns:
                new_price = prices[-1] * (1 + ret)
                prices.append(new_price)

            prices = prices[1:]  # Remover preço inicial

            # Criar DataFrame
            timestamps = pd.date_range(
                start=self.start_date,
                periods=candles_needed,
                freq='1H' if timeframe == '1h' else '15T' if timeframe == '15m' else '5T'
            )

            # Simular OHLCV
            data = []
            for i, (ts, close) in enumerate(zip(timestamps, prices)):
                # Simular high/low/open baseado no close
                volatility = 0.01  # 1% intraday volatility
                high = close * (1 + np.random.uniform(0, volatility))
                low = close * (1 - np.random.uniform(0, volatility))
                open_price = prices[i-1] if i > 0 else close
                volume = np.random.uniform(1000, 10000)

                data.append({
                    'timestamp': ts,
                    'open': open_price,
                    'high': high,
                    'low': low,
                    'close': close,
                    'volume': volume
                })

            df = pd.DataFrame(data)
            df.set_index('timestamp', inplace=True)

            # Cache dos dados
            self.data_cache[cache_key] = df

            logger.info(f"✅ Gerados {len(df)} candles simulados para {symbol} ({timeframe})")
            return df

        except Exception as e:
            logger.error(f"❌ Erro ao gerar dados simulados para {symbol}: {e}")
            return None

    def calculate_metrics(self, returns: pd.Series) -> Dict:
        """Calcular métricas de performance."""
        if len(returns) == 0 or returns.isna().all():
            return {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'volatility': 0.0,
                'win_rate': 0.0,
                'num_trades': 0
            }
        
        # Remover NaN
        returns = returns.dropna()
        
        if len(returns) == 0:
            return {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'volatility': 0.0,
                'win_rate': 0.0,
                'num_trades': 0
            }
        
        # Calcular métricas
        total_return = (1 + returns).prod() - 1
        volatility = returns.std() * np.sqrt(252)  # Anualizado
        
        # Sharpe Ratio (assumindo risk-free rate = 0)
        if volatility > 0:
            sharpe_ratio = (returns.mean() * 252) / volatility
        else:
            sharpe_ratio = 0.0
        
        # Max Drawdown
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # Win Rate
        positive_returns = returns[returns > 0]
        win_rate = len(positive_returns) / len(returns) if len(returns) > 0 else 0.0
        
        return {
            'total_return': float(total_return),
            'sharpe_ratio': float(sharpe_ratio),
            'max_drawdown': float(max_drawdown),
            'volatility': float(volatility),
            'win_rate': float(win_rate),
            'num_trades': len(returns)
        }

    async def run_single_backtest(self, symbol: str, price_amp: float, news_amp: float, min_conf: float) -> Dict:
        """Executar um único backtest com parâmetros específicos."""
        try:
            # Coletar dados históricos
            data = await self.collect_historical_data(symbol, "1h")
            if data is None or len(data) < 100:
                logger.warning(f"⚠️ Dados insuficientes para {symbol}")
                return None
            
            # Simular estratégia com parâmetros
            # Nota: Esta é uma simulação simplificada
            # Em produção, usaríamos a estratégia QUALIA completa
            
            # Calcular retornos simples baseados em volatilidade e parâmetros
            price_changes = data['close'].pct_change().dropna()
            
            # Simular sinais baseados nos parâmetros
            # price_amp influencia sensibilidade a movimentos de preço
            # news_amp seria usado com dados de notícias (simulado aqui)
            # min_conf define threshold para entrada
            
            volatility_threshold = price_changes.std() * (1 / price_amp)
            confidence_threshold = min_conf
            
            # Gerar sinais simulados
            signals = []
            for i in range(len(price_changes)):
                if abs(price_changes.iloc[i]) > volatility_threshold:
                    # Simular confiança baseada em news_amp
                    simulated_confidence = min(0.9, 0.5 + (news_amp / 20))
                    
                    if simulated_confidence >= confidence_threshold:
                        signal = 1 if price_changes.iloc[i] > 0 else -1
                        signals.append(signal * price_changes.iloc[i])
                    else:
                        signals.append(0)
                else:
                    signals.append(0)
            
            # Converter para Series
            strategy_returns = pd.Series(signals, index=price_changes.index)
            
            # Calcular métricas
            metrics = self.calculate_metrics(strategy_returns)
            
            # Adicionar parâmetros ao resultado
            result = {
                'symbol': symbol,
                'price_amp': price_amp,
                'news_amp': news_amp,
                'min_conf': min_conf,
                'timestamp': datetime.now().isoformat(),
                **metrics
            }
            
            logger.debug(f"📊 Backtest {symbol}: Sharpe={metrics['sharpe_ratio']:.3f}, Return={metrics['total_return']:.3f}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Erro no backtest {symbol} (p={price_amp}, n={news_amp}, c={min_conf}): {e}")
            return None

    async def run_grid_search(self) -> List[Dict]:
        """Executar grid search completo."""
        logger.info("🚀 Iniciando grid search offline...")
        
        if not await self.load_config():
            logger.error("❌ Falha ao carregar configuração")
            return []
        
        symbols = self.config.get('symbols', ['BTC/USDT', 'ETH/USDT'])
        logger.info(f"🎯 Testando {len(symbols)} símbolos")
        
        # Gerar todas as combinações de parâmetros
        param_combinations = list(itertools.product(
            self.price_amp_range,
            self.news_amp_range,
            self.min_conf_range
        ))
        
        total_runs = len(symbols) * len(param_combinations)
        logger.info(f"📊 Total de runs: {total_runs}")
        
        results = []
        completed = 0
        
        # Executar backtests
        for symbol in symbols:
            logger.info(f"🔍 Processando {symbol}...")
            
            for price_amp, news_amp, min_conf in param_combinations:
                result = await self.run_single_backtest(symbol, price_amp, news_amp, min_conf)
                
                if result:
                    results.append(result)
                
                completed += 1
                
                if completed % 50 == 0:
                    logger.info(f"📈 Progresso: {completed}/{total_runs} ({completed/total_runs*100:.1f}%)")
        
        logger.info(f"✅ Grid search concluído: {len(results)} resultados válidos")
        return results

    def analyze_results(self, results: List[Dict]) -> Dict:
        """Analisar resultados do grid search."""
        if not results:
            logger.warning("⚠️ Nenhum resultado para analisar")
            return {}
        
        df = pd.DataFrame(results)
        
        # Estatísticas gerais
        analysis = {
            'total_runs': len(df),
            'best_sharpe': {
                'value': df['sharpe_ratio'].max(),
                'params': df.loc[df['sharpe_ratio'].idxmax()][['price_amp', 'news_amp', 'min_conf']].to_dict()
            },
            'best_return': {
                'value': df['total_return'].max(),
                'params': df.loc[df['total_return'].idxmax()][['price_amp', 'news_amp', 'min_conf']].to_dict()
            },
            'lowest_drawdown': {
                'value': df['max_drawdown'].max(),  # Menos negativo = melhor
                'params': df.loc[df['max_drawdown'].idxmax()][['price_amp', 'news_amp', 'min_conf']].to_dict()
            },
            'summary_stats': {
                'avg_sharpe': df['sharpe_ratio'].mean(),
                'avg_return': df['total_return'].mean(),
                'avg_drawdown': df['max_drawdown'].mean(),
                'avg_win_rate': df['win_rate'].mean()
            }
        }
        
        logger.info("📊 Análise de resultados:")
        logger.info(f"   🏆 Melhor Sharpe: {analysis['best_sharpe']['value']:.3f}")
        logger.info(f"   💰 Melhor Return: {analysis['best_return']['value']:.3f}")
        logger.info(f"   🛡️ Menor Drawdown: {analysis['lowest_drawdown']['value']:.3f}")
        
        return analysis

    def save_results(self, results: List[Dict], analysis: Dict):
        """Salvar resultados do benchmark."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Salvar resultados detalhados
        results_file = f"data/benchmark_results_{timestamp}.json"
        os.makedirs("data", exist_ok=True)
        
        with open(results_file, 'w') as f:
            json.dump({
                'metadata': {
                    'timestamp': timestamp,
                    'start_date': self.start_date.isoformat(),
                    'end_date': self.end_date.isoformat(),
                    'total_runs': len(results)
                },
                'results': results,
                'analysis': analysis
            }, f, indent=2)
        
        logger.info(f"💾 Resultados salvos em: {results_file}")
        
        # Salvar CSV para análise
        if results:
            df = pd.DataFrame(results)
            csv_file = f"data/benchmark_results_{timestamp}.csv"
            df.to_csv(csv_file, index=False)
            logger.info(f"📊 CSV salvo em: {csv_file}")

async def main():
    """Função principal do benchmark."""
    logger.info("🚀 Iniciando QUALIA Offline Benchmark...")
    
    benchmark = OfflineBenchmark()
    
    # Executar grid search
    results = await benchmark.run_grid_search()
    
    if results:
        # Analisar resultados
        analysis = benchmark.analyze_results(results)
        
        # Salvar resultados
        benchmark.save_results(results, analysis)
        
        logger.info("🏆 Benchmark offline concluído com sucesso!")
        
        # Mostrar melhores parâmetros
        if 'best_sharpe' in analysis:
            best_params = analysis['best_sharpe']['params']
            logger.info(f"🎯 Melhores parâmetros (Sharpe): price_amp={best_params['price_amp']:.1f}, news_amp={best_params['news_amp']:.1f}, min_conf={best_params['min_conf']:.2f}")
    else:
        logger.error("❌ Nenhum resultado válido obtido")

if __name__ == "__main__":
    asyncio.run(main())
