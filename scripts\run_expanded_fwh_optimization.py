#!/usr/bin/env python3
"""
Otimização FWH Expandida - Ranges dramaticamente expandidos para encontrar configurações lucrativas.

YAA-PROFIT-V3: Otimização com ranges ultra-expandidos e critérios ultra-flexíveis.
"""

import sys
import os
import asyncio
import yaml
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
import logging
from itertools import product

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class OptimizationResult:
    """Resultado de uma otimização de parâmetros."""
    parameters: Dict[str, Any]
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    score: float
    meets_criteria: bool


class ExpandedFWHOptimizer:
    """Otimizador FWH com ranges dramaticamente expandidos."""
    
    def __init__(self):
        # YAA-PROFIT-V3: Critérios ultra-flexíveis
        self.success_criteria = {
            'min_total_return': 0.001,     # 0.1% mínimo (ultra flexível)
            'min_sharpe_ratio': 0.1,       # Sharpe > 0.1 (ultra flexível)
            'max_drawdown': 0.50,          # Max 50% drawdown (ultra flexível)
            'min_win_rate': 0.05,          # Min 5% win rate (ultra flexível)
            'min_profit_factor': 1.001,    # Min 1.001 profit factor (ultra flexível)
            'min_trades': 10               # Min 10 trades para validação
        }
        
        # YAA-PROFIT-V3: Ranges dramaticamente expandidos
        self.optimization_ranges = {
            'hype_threshold': [0.01, 0.05, 0.10, 0.15, 0.20, 0.25, 0.30, 0.35, 0.40, 0.50, 0.60],
            'otoc_max_threshold': [0.10, 0.15, 0.20, 0.25, 0.30, 0.35, 0.40, 0.45, 0.50, 0.60, 0.70],
            'stop_loss_pct': [0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 4.0, 5.0, 8.0],
            'take_profit_pct': [0.8, 1.0, 1.5, 2.0, 2.5, 3.0, 4.0, 6.0, 8.0, 12.0],
            'wave_min_strength': [0.005, 0.01, 0.05, 0.10, 0.15, 0.20, 0.25, 0.30, 0.40],
            'quantum_boost_factor': [1.001, 1.01, 1.03, 1.05, 1.08, 1.10, 1.15, 1.20, 1.25, 1.30]
        }
        
        self.results: List[OptimizationResult] = []
        
        logger.info("🎯 Expanded FWH Optimizer initialized")
        logger.info(f"   Success criteria: {self.success_criteria}")
        logger.info(f"   Optimization ranges: {len(list(product(*self.optimization_ranges.values())))} combinations")
    
    def create_mock_historical_data(self, days: int = 30) -> pd.DataFrame:
        """Cria dados históricos simulados para teste."""
        dates = pd.date_range(start=datetime.now() - timedelta(days=days), 
                             end=datetime.now(), freq='1H')
        
        # Simular dados OHLCV realistas
        np.random.seed(42)  # Para reprodutibilidade
        base_price = 50000
        
        data = []
        current_price = base_price
        
        for date in dates:
            # Movimento browniano com tendência
            change = np.random.normal(0, 0.02)  # 2% volatilidade
            current_price *= (1 + change)
            
            # OHLC baseado no preço atual
            open_price = current_price * (1 + np.random.normal(0, 0.005))
            high_price = max(open_price, current_price) * (1 + abs(np.random.normal(0, 0.01)))
            low_price = min(open_price, current_price) * (1 - abs(np.random.normal(0, 0.01)))
            close_price = current_price
            volume = np.random.uniform(100, 1000)
            
            data.append({
                'timestamp': date,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume
            })
        
        return pd.DataFrame(data)
    
    def simulate_strategy_performance(self, parameters: Dict[str, Any], 
                                    historical_data: pd.DataFrame) -> Dict[str, Any]:
        """Simula performance da estratégia com parâmetros dados."""
        
        # Simular trades baseado nos parâmetros
        hype_threshold = parameters['hype_threshold']
        wave_min_strength = parameters['wave_min_strength']
        stop_loss_pct = parameters['stop_loss_pct'] / 100
        take_profit_pct = parameters['take_profit_pct'] / 100
        
        # Simular sinais de entrada baseados nos thresholds
        signal_probability = max(0.01, min(0.5, 1 - hype_threshold))  # Threshold menor = mais sinais
        
        trades = []
        portfolio_value = 10000  # Valor inicial
        
        for i in range(len(historical_data) - 1):
            if np.random.random() < signal_probability:
                # Simular trade
                entry_price = historical_data.iloc[i]['close']
                
                # Determinar saída baseada em stop/take profit
                exit_multiplier = np.random.choice([
                    1 - stop_loss_pct,  # Stop loss
                    1 + take_profit_pct,  # Take profit
                    1 + np.random.normal(0, 0.01)  # Saída neutra
                ], p=[0.3, 0.4, 0.3])  # Probabilidades
                
                exit_price = entry_price * exit_multiplier
                pnl_pct = (exit_price - entry_price) / entry_price
                
                trades.append({
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'pnl_pct': pnl_pct,
                    'win': pnl_pct > 0
                })
        
        if not trades:
            return {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'total_trades': 0
            }
        
        # Calcular métricas
        returns = [trade['pnl_pct'] for trade in trades]
        total_return = sum(returns)
        
        wins = [r for r in returns if r > 0]
        losses = [abs(r) for r in returns if r < 0]
        
        win_rate = len(wins) / len(trades) if trades else 0
        profit_factor = sum(wins) / sum(losses) if losses else float('inf') if wins else 0
        
        sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
        
        # Simular drawdown
        cumulative_returns = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = cumulative_returns - running_max
        max_drawdown = abs(min(drawdowns)) if len(drawdowns) > 0 else 0
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'total_trades': len(trades)
        }
    
    def _calculate_composite_score(self, results: Dict[str, Any]) -> float:
        """Calcula score composto para ranking."""
        return (
            results['total_return'] * 0.4 +
            results['sharpe_ratio'] * 0.3 +
            (1 - results['max_drawdown']) * 0.2 +
            results['win_rate'] * 0.1
        )
    
    def _check_success_criteria(self, results: Dict[str, Any]) -> bool:
        """Verifica se resultados atendem critérios de sucesso."""
        return (
            results['total_return'] >= self.success_criteria['min_total_return'] and
            results['sharpe_ratio'] >= self.success_criteria['min_sharpe_ratio'] and
            abs(results['max_drawdown']) <= self.success_criteria['max_drawdown'] and
            results['win_rate'] >= self.success_criteria['min_win_rate'] and
            results['profit_factor'] >= self.success_criteria['min_profit_factor'] and
            results['total_trades'] >= self.success_criteria['min_trades']
        )
    
    async def test_parameter_combination(self, parameters: Dict[str, Any], 
                                       historical_data: pd.DataFrame) -> OptimizationResult:
        """Testa uma combinação específica de parâmetros."""
        
        # Simular performance
        results = self.simulate_strategy_performance(parameters, historical_data)
        
        # Calcular score composto
        score = self._calculate_composite_score(results)
        
        # Verificar critérios de sucesso
        meets_criteria = self._check_success_criteria(results)
        
        return OptimizationResult(
            parameters=parameters,
            total_return=results['total_return'],
            sharpe_ratio=results['sharpe_ratio'],
            max_drawdown=results['max_drawdown'],
            win_rate=results['win_rate'],
            profit_factor=results['profit_factor'],
            total_trades=results['total_trades'],
            score=score,
            meets_criteria=meets_criteria
        )
    
    async def run_expanded_optimization(self, max_combinations: int = 200) -> List[OptimizationResult]:
        """Executa otimização expandida."""
        logger.info(f"🚀 Starting expanded optimization (max {max_combinations} combinations)")
        
        # Criar dados históricos simulados
        historical_data = self.create_mock_historical_data(days=30)
        
        # Gerar todas as combinações possíveis
        param_names = list(self.optimization_ranges.keys())
        param_values = list(self.optimization_ranges.values())
        all_combinations = list(product(*param_values))
        
        # Limitar número de combinações se necessário
        if len(all_combinations) > max_combinations:
            step = len(all_combinations) // max_combinations
            combinations = all_combinations[::step][:max_combinations]
        else:
            combinations = all_combinations
        
        logger.info(f"   Testing {len(combinations)} parameter combinations")
        
        # Testar cada combinação
        for i, combination in enumerate(combinations):
            parameters = dict(zip(param_names, combination))
            
            try:
                result = await self.test_parameter_combination(parameters, historical_data)
                self.results.append(result)
                
                if (i + 1) % 10 == 0:
                    logger.info(f"   Progress: {i + 1}/{len(combinations)} combinations tested")
                    
            except Exception as e:
                logger.error(f"   Error testing combination {i + 1}: {e}")
                continue
        
        # Ordenar resultados por score
        self.results.sort(key=lambda x: x.score, reverse=True)
        
        logger.info(f"✅ Optimization completed: {len(self.results)} results")
        return self.results
    
    def generate_optimization_report(self) -> str:
        """Gera relatório de otimização."""
        if not self.results:
            return "❌ No optimization results available"
        
        successful_configs = [r for r in self.results if r.meets_criteria]
        
        report = f"""🎯 EXPANDED FWH OPTIMIZATION REPORT
============================================================

📊 OVERVIEW:
   Total Combinations Tested: {len(self.results)}
   Successful Configurations: {len(successful_configs)}
   Success Rate: {len(successful_configs)/len(self.results)*100:.1f}%

🏆 TOP 10 CONFIGURATIONS:
"""
        
        for i, result in enumerate(self.results[:10]):
            status = "✅ PROFITABLE" if result.meets_criteria else "❌ UNPROFITABLE"
            report += f"""
   #{i+1} {status}
      Score: {result.score:.4f}
      Return: {result.total_return:.1%} | Sharpe: {result.sharpe_ratio:.3f}
      Drawdown: {result.max_drawdown:.1%} | Win Rate: {result.win_rate:.1%}
      Trades: {result.total_trades} | Profit Factor: {result.profit_factor:.3f}
      Parameters: {result.parameters}"""
        
        if successful_configs:
            report += f"\n\n✅ {len(successful_configs)} PROFITABLE CONFIGURATIONS FOUND!"
        else:
            report += "\n\n❌ NO PROFITABLE CONFIGURATIONS FOUND"
            report += "\n\n💡 RECOMMENDATIONS:"
            report += "\n   1. Further expand parameter ranges"
            report += "\n   2. Test with different market conditions"
            report += "\n   3. Adjust success criteria"
            report += "\n   4. Consider hybrid approaches"
        
        return report


async def main():
    """Executa otimização expandida."""
    print("🎯 EXPANDED FWH OPTIMIZATION")
    print("=" * 60)
    
    # Inicializar otimizador
    optimizer = ExpandedFWHOptimizer()
    
    try:
        # Executar otimização expandida
        results = await optimizer.run_expanded_optimization(max_combinations=200)
        
        # Gerar relatório
        report = optimizer.generate_optimization_report()
        print(report)
        
        # Salvar resultados
        with open('logs/expanded_fwh_optimization_results.json', 'w') as f:
            results_data = []
            for result in results:
                results_data.append({
                    'parameters': result.parameters,
                    'total_return': result.total_return,
                    'sharpe_ratio': result.sharpe_ratio,
                    'max_drawdown': result.max_drawdown,
                    'win_rate': result.win_rate,
                    'profit_factor': result.profit_factor,
                    'total_trades': result.total_trades,
                    'score': result.score,
                    'meets_criteria': result.meets_criteria
                })
            json.dump(results_data, f, indent=2)
        
        print(f"\n💾 Results saved to: logs/expanded_fwh_optimization_results.json")
        
    except Exception as e:
        logger.error(f"❌ Optimization failed: {e}")
        return False
    
    return True


if __name__ == "__main__":
    asyncio.run(main())
