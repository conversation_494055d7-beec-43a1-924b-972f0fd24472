"""
QUALIA Live Feed Integration - Integração do Live Feed com Sistema de Trading

Este módulo fornece a integração completa do live feed system com o sistema
de trading QUALIA existente, substituindo ou complementando fontes de dados
tradicionais com dados em tempo real.
"""

from __future__ import annotations

import asyncio
import time
from typing import Dict, List, Any, Optional, TYPE_CHECKING
from dataclasses import dataclass

from .live_feed_data_collector import LiveFeedData<PERSON>ollector, LiveFeedConfig
from .enhanced_data_collector import EnhancedDataCollector
from .amplification_calibrator import AmplificationCalibrator
from ..utils.logging_config import get_qualia_logger
from ..event_bus import SimpleEventBus
from ..config.data_sources import load_data_sources_defaults

if TYPE_CHECKING:
    from ..qualia_trading_system import QUALIATradingSystem
    from .holographic_universe import HolographicMarketUniverse

logger = get_qualia_logger(__name__)


@dataclass
class LiveFeedIntegrationConfig:
    """Configuração da integração do live feed."""
    
    # Modo de operação
    mode: str = "hybrid"  # "live_only", "traditional_only", "hybrid"
    
    # Configuração do live feed
    enable_live_feed: bool = True
    live_feed_priority: int = 1  # Prioridade dos dados do live feed
    
    # Configuração de fallback
    enable_fallback: bool = True
    fallback_timeout: float = 30.0  # Timeout para fallback
    
    # Configuração de validação
    enable_data_validation: bool = True
    price_variance_threshold: float = 0.01  # 1% de variação máxima
    
    # Configuração de performance
    update_frequency: float = 1.0  # Frequência de updates em segundos
    batch_processing: bool = True
    
    # Configuração de paper trading
    paper_trading_mode: bool = True
    paper_trading_symbols: List[str] = None
    
    # Parâmetros otimizados (conforme especificado na tarefa)
    news_amp: float = 11.3
    price_amp: float = 1.0
    min_conf: float = 0.37


class LiveFeedIntegration:
    """
    Classe principal para integração do live feed com o sistema de trading QUALIA.
    
    Funcionalidades:
    - Integra LiveFeedDataCollector com sistema existente
    - Gerencia fallback para fontes tradicionais
    - Valida dados em tempo real
    - Configura parâmetros otimizados
    - Suporte a paper trading
    """
    
    def __init__(self, 
                 trading_system: Optional["QUALIATradingSystem"] = None,
                 config: Optional[Dict[str, Any]] = None):
        
        self.trading_system = trading_system
        self.config = LiveFeedIntegrationConfig(**(config or {}))
        
        # Componentes principais
        self.live_feed_collector: Optional[LiveFeedDataCollector] = None
        self.enhanced_data_collector: Optional[EnhancedDataCollector] = None
        self.amplification_calibrator: Optional[AmplificationCalibrator] = None
        self.event_bus: Optional[SimpleEventBus] = None
        
        # Estado da integração
        self.is_initialized = False
        self.is_running = False
        self.live_feed_active = False
        self.fallback_active = False
        
        # Estatísticas
        self.stats = {
            "live_feed_updates": 0,
            "fallback_activations": 0,
            "data_validation_failures": 0,
            "total_processed": 0,
            "last_update_time": 0.0
        }
        
        logger.info("LiveFeedIntegration inicializada")
    
    async def initialize(self, 
                        symbols: List[str], 
                        timeframes: List[str] = None) -> bool:
        """Inicializa a integração do live feed."""
        try:
            logger.info("🚀 Inicializando Live Feed Integration...")
            
            # Configurar símbolos padrão se não fornecidos
            if not symbols:
                symbols = ["BTC/USDT", "ETH/USDT", "ADA/USDT"]
            
            if not timeframes:
                timeframes = ["5m", "15m", "1h"]
            
            # Inicializar event bus se não existir
            if not self.event_bus:
                self.event_bus = SimpleEventBus()
            
            # Inicializar AmplificationCalibrator com parâmetros otimizados
            await self._initialize_amplification_calibrator()
            
            # Inicializar EnhancedDataCollector
            await self._initialize_enhanced_data_collector(symbols, timeframes)
            
            # Inicializar LiveFeedDataCollector
            await self._initialize_live_feed_collector(symbols)
            
            # Configurar integração com trading system se disponível
            if self.trading_system:
                await self._integrate_with_trading_system()
            
            self.is_initialized = True
            logger.info("✅ Live Feed Integration inicializada com sucesso")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar Live Feed Integration: {e}")
            return False
    
    async def _initialize_amplification_calibrator(self):
        """Inicializa o calibrador de amplificação com parâmetros otimizados."""
        try:
            logger.info("🎛️ Inicializando AmplificationCalibrator com parâmetros otimizados...")

            # Criar calibrador com parâmetros otimizados
            self.amplification_calibrator = AmplificationCalibrator(
                initial_price_amp=self.config.price_amp,
                initial_news_amp=self.config.news_amp,
                learning_rate=0.01
            )

            # Parâmetros já configurados no construtor
            
            logger.info(f"✅ AmplificationCalibrator configurado: "
                       f"price_amp={self.config.price_amp}, "
                       f"news_amp={self.config.news_amp}, "
                       f"min_conf={self.config.min_conf}")
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar AmplificationCalibrator: {e}")
            raise
    
    async def _initialize_enhanced_data_collector(self, symbols: List[str], timeframes: List[str]):
        """Inicializa o Enhanced Data Collector."""
        try:
            logger.info("📊 Inicializando EnhancedDataCollector...")

            self.enhanced_data_collector = EnhancedDataCollector(
                symbols=symbols,
                timeframes=timeframes,
                event_bus=self.event_bus
            )
            
            # Inicializar o collector
            await self.enhanced_data_collector.__aenter__()
            
            logger.info("✅ EnhancedDataCollector inicializado")
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar EnhancedDataCollector: {e}")
            raise
    
    async def _initialize_live_feed_collector(self, symbols: List[str]):
        """Inicializa o Live Feed Data Collector."""
        try:
            logger.info("📡 Inicializando LiveFeedDataCollector...")
            
            live_feed_config = {
                "enable_kucoin": True,
                "enable_binance": False,
                "enable_coinbase": False,
                "aggregation_enabled": True,
                "enable_technical_indicators": True,
                "enable_holographic_conversion": True,
                "update_interval": self.config.update_frequency,
                "confidence_threshold": self.config.min_conf
            }
            
            self.live_feed_collector = LiveFeedDataCollector(
                symbols=symbols,
                config=live_feed_config,
                event_bus=self.event_bus,
                amplification_calibrator=self.amplification_calibrator,
                enhanced_data_collector=self.enhanced_data_collector
            )
            
            # Inicializar o collector
            await self.live_feed_collector.initialize()
            
            logger.info("✅ LiveFeedDataCollector inicializado")
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar LiveFeedDataCollector: {e}")
            raise
    
    async def _integrate_with_trading_system(self):
        """Integra com o sistema de trading existente."""
        try:
            logger.info("🔗 Integrando com QUALIATradingSystem...")
            
            # Substituir ou complementar data collectors existentes
            if hasattr(self.trading_system, 'enhanced_data_collector'):
                # Backup do collector original
                self.trading_system._original_enhanced_data_collector = self.trading_system.enhanced_data_collector
                
                # Substituir pelo nosso collector integrado
                self.trading_system.enhanced_data_collector = self.enhanced_data_collector
                
                logger.info("✅ EnhancedDataCollector substituído no trading system")
            
            # Configurar event bus se não existir
            if not hasattr(self.trading_system, 'event_bus') or not self.trading_system.event_bus:
                self.trading_system.event_bus = self.event_bus
                logger.info("✅ Event bus configurado no trading system")
            
            # Configurar amplification calibrator
            if hasattr(self.trading_system, 'amplification_calibrator'):
                self.trading_system.amplification_calibrator = self.amplification_calibrator
                logger.info("✅ AmplificationCalibrator configurado no trading system")
            
            logger.info("✅ Integração com trading system concluída")
            
        except Exception as e:
            logger.error(f"❌ Erro ao integrar com trading system: {e}")
            raise
    
    async def start(self) -> bool:
        """Inicia a integração do live feed."""
        try:
            if not self.is_initialized:
                logger.error("Integração não foi inicializada")
                return False
            
            logger.info("🚀 Iniciando Live Feed Integration...")
            
            # Iniciar live feed collector
            if self.config.enable_live_feed and self.live_feed_collector:
                success = await self.live_feed_collector.start()
                if success:
                    self.live_feed_active = True
                    logger.info("✅ Live feed ativo")
                else:
                    logger.warning("⚠️ Falha ao iniciar live feed, usando fallback")
                    if self.config.enable_fallback:
                        self.fallback_active = True
            
            # Iniciar monitoramento
            asyncio.create_task(self._monitoring_loop())
            
            self.is_running = True
            self.stats["last_update_time"] = time.time()
            
            logger.info("✅ Live Feed Integration iniciada")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao iniciar Live Feed Integration: {e}")
            return False
    
    async def stop(self):
        """Para a integração do live feed."""
        try:
            logger.info("🛑 Parando Live Feed Integration...")
            
            self.is_running = False
            
            # Parar live feed collector
            if self.live_feed_collector:
                await self.live_feed_collector.stop()
                self.live_feed_active = False
            
            # Parar enhanced data collector
            if self.enhanced_data_collector:
                await self.enhanced_data_collector.__aexit__(None, None, None)
            
            # Restaurar collectors originais no trading system se necessário
            if (self.trading_system and 
                hasattr(self.trading_system, '_original_enhanced_data_collector')):
                self.trading_system.enhanced_data_collector = self.trading_system._original_enhanced_data_collector
                delattr(self.trading_system, '_original_enhanced_data_collector')
            
            logger.info("✅ Live Feed Integration parada")
            
        except Exception as e:
            logger.error(f"❌ Erro ao parar Live Feed Integration: {e}")
    
    async def _monitoring_loop(self):
        """Loop de monitoramento da integração."""
        logger.info("🔍 Iniciando loop de monitoramento")
        
        while self.is_running:
            try:
                # Verificar status do live feed
                if self.live_feed_collector and self.live_feed_active:
                    status = self.live_feed_collector.get_status()
                    
                    if not status["is_running"]:
                        logger.warning("⚠️ Live feed não está rodando, ativando fallback")
                        self.live_feed_active = False
                        if self.config.enable_fallback:
                            self.fallback_active = True
                            self.stats["fallback_activations"] += 1
                
                # Atualizar estatísticas
                self.stats["last_update_time"] = time.time()
                
                # Log de status periodicamente
                if int(time.time()) % 60 == 0:  # A cada minuto
                    await self._log_status()
                
                await asyncio.sleep(5.0)  # Verificar a cada 5 segundos
                
            except Exception as e:
                logger.error(f"Erro no loop de monitoramento: {e}")
                await asyncio.sleep(10.0)
        
        logger.info("🛑 Loop de monitoramento finalizado")
    
    async def _log_status(self):
        """Log do status atual da integração."""
        try:
            status_msg = (
                f"📊 Live Feed Integration Status: "
                f"live_feed={'✅' if self.live_feed_active else '❌'}, "
                f"fallback={'✅' if self.fallback_active else '❌'}, "
                f"processed={self.stats['total_processed']}, "
                f"failures={self.stats['data_validation_failures']}"
            )
            logger.info(status_msg)
            
        except Exception as e:
            logger.error(f"Erro ao fazer log de status: {e}")
    
    def get_integration_status(self) -> Dict[str, Any]:
        """Retorna status completo da integração."""
        return {
            "is_initialized": self.is_initialized,
            "is_running": self.is_running,
            "live_feed_active": self.live_feed_active,
            "fallback_active": self.fallback_active,
            "config": {
                "mode": self.config.mode,
                "news_amp": self.config.news_amp,
                "price_amp": self.config.price_amp,
                "min_conf": self.config.min_conf,
                "paper_trading": self.config.paper_trading_mode
            },
            "stats": self.stats.copy(),
            "components": {
                "live_feed_collector": self.live_feed_collector.get_status() if self.live_feed_collector else None,
                "amplification_calibrator": self.amplification_calibrator.get_calibration_report() if self.amplification_calibrator else None
            }
        }
    
    async def validate_integration(self) -> Dict[str, bool]:
        """Valida se a integração está funcionando corretamente."""
        validation_results = {
            "live_feed_collector_initialized": self.live_feed_collector is not None,
            "enhanced_data_collector_initialized": self.enhanced_data_collector is not None,
            "amplification_calibrator_initialized": self.amplification_calibrator is not None,
            "event_bus_configured": self.event_bus is not None,
            "live_feed_running": False,
            "data_flow_active": False
        }
        
        try:
            # Verificar se live feed está rodando
            if self.live_feed_collector:
                status = self.live_feed_collector.get_status()
                validation_results["live_feed_running"] = status.get("is_running", False)
            
            # Verificar fluxo de dados
            if self.live_feed_collector:
                latest_data = self.live_feed_collector.get_latest_data()
                validation_results["data_flow_active"] = any(data is not None for data in latest_data.values())
            
        except Exception as e:
            logger.error(f"Erro na validação da integração: {e}")
        
        return validation_results
