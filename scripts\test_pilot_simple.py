#!/usr/bin/env python3
"""
Simple Pilot Test
P-02.3: Deploy Piloto com Capital Limitado

Simple test to verify pilot system functionality
"""

import os
import sys
import json
import time
from pathlib import Path

def test_pilot_setup():
    """Test pilot setup"""
    print("🧪 TESTING PILOT SETUP")
    print("=" * 30)
    
    # Test 1: Configuration file
    config_file = Path("config/pilot_config.yaml")
    if config_file.exists():
        print("✅ Pilot configuration exists")
    else:
        print("❌ Pilot configuration missing")
        return False
    
    # Test 2: Credentials files
    cred_files = [
        "config/.pilot_credentials",
        "config/.pilot_master.key", 
        "config/.pilot_env"
    ]
    
    for cred_file in cred_files:
        if Path(cred_file).exists():
            print(f"✅ {cred_file} exists")
        else:
            print(f"❌ {cred_file} missing")
            return False
    
    # Test 3: Directories
    pilot_dirs = [
        "logs/pilot",
        "data/pilot",
        "reports/pilot",
        "backups/pilot"
    ]
    
    for pilot_dir in pilot_dirs:
        dir_path = Path(pilot_dir)
        if dir_path.exists() and dir_path.is_dir():
            print(f"✅ {pilot_dir} directory exists")
        else:
            print(f"❌ {pilot_dir} directory missing")
            return False
    
    # Test 4: Import test
    try:
        import yaml
        print("✅ YAML module available")
    except ImportError:
        print("❌ YAML module missing")
        return False
    
    try:
        from cryptography.fernet import Fernet
        print("✅ Cryptography module available")
    except ImportError:
        print("❌ Cryptography module missing")
        return False
    
    # Test 5: Load configuration
    try:
        import yaml
        with open("config/pilot_config.yaml", 'r') as f:
            config = yaml.safe_load(f)
        
        if config.get('environment') == 'pilot':
            print("✅ Configuration loaded successfully")
        else:
            print("❌ Invalid configuration environment")
            return False
            
    except Exception as e:
        print(f"❌ Configuration load failed: {e}")
        return False
    
    # Test 6: Decrypt credentials test
    try:
        import base64
        from cryptography.fernet import Fernet
        
        # Load master key
        with open("config/.pilot_master.key", 'rb') as f:
            master_key = f.read()
        
        # Load encrypted credentials
        with open("config/.pilot_credentials", 'r') as f:
            encrypted_data = f.read()
        
        # Decrypt
        f = Fernet(master_key)
        encrypted_bytes = base64.b64decode(encrypted_data.encode())
        decrypted = f.decrypt(encrypted_bytes)
        credentials = json.loads(decrypted.decode())
        
        if credentials.get('environment') == 'pilot':
            print("✅ Credentials decryption successful")
        else:
            print("❌ Invalid credentials environment")
            return False
            
    except Exception as e:
        print(f"❌ Credentials decryption failed: {e}")
        return False
    
    print("=" * 30)
    print("🎉 ALL PILOT TESTS PASSED!")
    return True

def test_pilot_trading_simulation():
    """Test pilot trading simulation"""
    print("\n🚀 TESTING PILOT TRADING SIMULATION")
    print("=" * 40)
    
    # Simulate trading system initialization
    print("📊 Initializing trading components...")
    time.sleep(1)
    
    # Simulate safety checks
    print("🔒 Running safety checks...")
    capital_limit = 1000.0
    max_position = 20.0
    max_daily_loss = 50.0
    
    print(f"   Capital limit: ${capital_limit}")
    print(f"   Max position: ${max_position}")
    print(f"   Max daily loss: ${max_daily_loss}")
    
    # Simulate trading cycles
    print("🔄 Running trading cycles...")
    total_pnl = 0.0
    
    for cycle in range(5):
        print(f"   Cycle {cycle + 1}/5...")
        
        # Simulate small profit
        cycle_pnl = 0.25  # $0.25 per cycle
        total_pnl += cycle_pnl
        
        print(f"   Cycle PnL: ${cycle_pnl:.2f}, Total: ${total_pnl:.2f}")
        time.sleep(0.5)
    
    # Safety validation
    if total_pnl < max_daily_loss:
        print(f"✅ Safety check passed: PnL ${total_pnl:.2f} < limit ${max_daily_loss}")
    else:
        print(f"❌ Safety check failed: PnL ${total_pnl:.2f} >= limit ${max_daily_loss}")
        return False
    
    print("=" * 40)
    print(f"🎉 PILOT TRADING SIMULATION COMPLETED!")
    print(f"📈 Total simulated PnL: ${total_pnl:.2f}")
    return True

def main():
    """Main test function"""
    print("🧪 QUALIA P-02.3: PILOT SYSTEM TEST")
    print("=" * 50)
    print("⚠️  Testing pilot setup and functionality")
    print("⚠️  This is a DRY RUN - no real trading")
    print("=" * 50)
    
    # Test setup
    if not test_pilot_setup():
        print("\n❌ PILOT SETUP TEST FAILED")
        sys.exit(1)
    
    # Test trading simulation
    if not test_pilot_trading_simulation():
        print("\n❌ PILOT TRADING SIMULATION FAILED")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 ALL PILOT TESTS COMPLETED SUCCESSFULLY!")
    print("✅ Pilot system is ready for deployment")
    print("=" * 50)

if __name__ == '__main__':
    main()
