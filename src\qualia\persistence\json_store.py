"""
JSON Store

Implements JSON-based data persistence for system state, configuration,
and trading history with compression and backup capabilities.
"""

import asyncio
import json
import os
import gzip
import shutil
import contextlib
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
import logging
from pathlib import Path

from ..memory.event_bus import SimpleEventBus
from ..events import JsonStoreDataSaved, JsonStoreDataLoaded

try:  # portalocker may fail to import optional redis extras on Python 3.12
    import portalocker
except Exception:  # pragma: no cover - fallback when optional deps are missing
    portalocker = None


class JsonStore:
    """
    JSON-based data persistence system

    Provides atomic file operations, compression, backup management,
    and concurrent access protection for JSON data storage.
    """

    def __init__(self, config: Dict, event_bus: Optional[SimpleEventBus] = None):
        """Inicializar ``JsonStore`` com as configurações fornecidas."""

        self.config = config
        self.event_bus = event_bus
        self.logger = logging.getLogger(__name__)

        # Storage configuration
        self.data_dir = Path(config.get("data_dir", "data"))
        self.backup_interval = config.get("backup_interval", 3600)  # seconds
        self.compression = config.get("compression", True)
        self.max_backups = config.get("max_backups", 10)

        # File locks for concurrent access protection
        self.file_locks: Dict[str, Any] = {}
        # Lock to protect file_locks dictionary access
        self._locks_lock = asyncio.Lock()

        # Backup tracking
        self.last_backup_time: Dict[str, datetime] = {}

        # Background backup task
        self._backup_task: Optional[asyncio.Task] = None

        # Ensure data directory exists
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # Initialize subdirectories
        self._initialize_directories()

    def _initialize_directories(self):
        """Initialize required subdirectories"""
        subdirs = [
            "system",
            "trading",
            "memory",
            "metacognition",
            "risk",
            "signals",
            "backups",
        ]

        for subdir in subdirs:
            (self.data_dir / subdir).mkdir(exist_ok=True)

    async def initialize(self):
        """Initialize the JSON store"""
        try:
            self.logger.info(f"Initializing JSON store at {self.data_dir}")

            # Verify directory permissions
            if not os.access(self.data_dir, os.W_OK):
                raise PermissionError(
                    f"No write permission for data directory: {self.data_dir}"
                )

            # Start background backup task
            self._backup_task = asyncio.create_task(self._backup_loop())

            self.logger.info("JSON store initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize JSON store: {e}")
            raise

    async def save_data(self, key: str, data: Any, category: str = "system") -> bool:
        """
        Save data to JSON file

        Args:
            key: Unique identifier for the data
            data: Data to save (must be JSON serializable)
            category: Category/subdirectory for organization

        Returns:
            True if successful, False otherwise
        """
        try:
            ext = ".json.gz" if self.compression else ".json"
            file_path = self.data_dir / category / f"{key}{ext}"

            # Acquire file lock
            await self._acquire_lock(str(file_path))

            try:
                # Prepare data with metadata
                json_data = {
                    "data": data,
                    "metadata": {
                        "key": key,
                        "category": category,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "version": "1.0",
                    },
                }

                # Write to temporary file first (atomic operation)
                temp_path = file_path.with_name(file_path.name + ".tmp")

                if self.compression:
                    await self._save_compressed(temp_path, json_data)
                else:
                    await self._save_uncompressed(temp_path, json_data)

                # Atomic move to final location
                shutil.move(str(temp_path), str(file_path))

                self.logger.debug(
                    f"Saved data for key '{key}' in category '{category}'"
                )

                if self.event_bus:
                    size = file_path.stat().st_size if file_path.exists() else 0
                    self.event_bus.publish(
                        "persistence.data.saved",
                        JsonStoreDataSaved(
                            key=key,
                            category=category,
                            path=str(file_path),
                            size_bytes=size,
                        ),
                    )

                # Check if backup is needed
                await self._check_backup_needed(key, category)

                return True

            finally:
                await self._release_lock(str(file_path))

        except Exception as e:
            self.logger.error(f"Error saving data for key '{key}': {e}")
            return False

    async def load_data(self, key: str, category: str = "system") -> Optional[Any]:
        """
        Load data from JSON file

        Args:
            key: Unique identifier for the data
            category: Category/subdirectory to search in

        Returns:
            Loaded data or None if not found
        """
        try:
            ext = ".json.gz" if self.compression else ".json"
            file_path = self.data_dir / category / f"{key}{ext}"

            if not file_path.exists():
                self.logger.debug(
                    f"Data file not found for key '{key}' in category '{category}'"
                )
                return None

            # Acquire file lock
            await self._acquire_lock(str(file_path))

            try:
                if self.compression:
                    json_data = await self._load_compressed(file_path)
                else:
                    json_data = await self._load_uncompressed(file_path)

                if json_data and "data" in json_data:
                    self.logger.debug(
                        f"Loaded data for key '{key}' from category '{category}'"
                    )
                    if self.event_bus:
                        self.event_bus.publish(
                            "persistence.data.loaded",
                            JsonStoreDataLoaded(
                                key=key,
                                category=category,
                                path=str(file_path),
                            ),
                        )
                    return json_data["data"]
                else:
                    self.logger.warning(f"Invalid data format for key '{key}'")
                    return None

            finally:
                await self._release_lock(str(file_path))

        except Exception as e:
            self.logger.error(f"Error loading data for key '{key}': {e}")
            return None

    async def delete_data(self, key: str, category: str = "system") -> bool:
        """
        Delete data file

        Args:
            key: Unique identifier for the data
            category: Category/subdirectory

        Returns:
            True if successful, False otherwise
        """
        try:
            ext = ".json.gz" if self.compression else ".json"
            file_path = self.data_dir / category / f"{key}{ext}"

            if not file_path.exists():
                return True  # Already deleted

            # Acquire file lock
            await self._acquire_lock(str(file_path))

            try:
                # Create backup before deletion
                await self._create_backup(key, category)

                # Delete file
                file_path.unlink()

                self.logger.debug(
                    f"Deleted data for key '{key}' from category '{category}'"
                )
                return True

            finally:
                await self._release_lock(str(file_path))

        except Exception as e:
            self.logger.error(f"Error deleting data for key '{key}': {e}")
            return False

    async def list_keys(self, category: str = "system") -> List[str]:
        """
        List all keys in a category

        Args:
            category: Category/subdirectory to search

        Returns:
            List of available keys
        """
        try:
            category_path = self.data_dir / category

            if not category_path.exists():
                return []

            ext = ".json.gz" if self.compression else ".json"
            keys = []
            for file_path in category_path.glob(f"*{ext}"):
                if not file_path.name.endswith(".tmp"):
                    key = file_path.name[: -len(ext)]
                    keys.append(key)

            return sorted(keys)

        except Exception as e:
            self.logger.error(f"Error listing keys in category '{category}': {e}")
            return []

    async def get_data_info(self, key: str, category: str = "system") -> Optional[Dict]:
        """
        Get metadata information about stored data

        Args:
            key: Unique identifier for the data
            category: Category/subdirectory

        Returns:
            Metadata dictionary or None if not found
        """
        try:
            ext = ".json.gz" if self.compression else ".json"
            file_path = self.data_dir / category / f"{key}{ext}"

            if not file_path.exists():
                return None

            # Get file stats
            stat = file_path.stat()

            # Load metadata
            await self._acquire_lock(str(file_path))
            try:
                if self.compression:
                    json_data = await self._load_compressed(file_path)
                else:
                    json_data = await self._load_uncompressed(file_path)

                metadata = json_data.get("metadata", {}) if json_data else {}

            finally:
                await self._release_lock(str(file_path))

            return {
                "key": key,
                "category": category,
                "file_size": stat.st_size,
                "modified_time": datetime.fromtimestamp(
                    stat.st_mtime, timezone.utc
                ).isoformat(),
                "metadata": metadata,
            }

        except Exception as e:
            self.logger.error(f"Error getting data info for key '{key}': {e}")
            return None

    async def _save_compressed(self, file_path: Path, data: Dict):
        """Save data with gzip compression"""
        json_str = json.dumps(data, indent=2, default=str)

        def _write() -> None:
            with gzip.open(file_path, "wt", encoding="utf-8") as f:
                f.write(json_str)

        await asyncio.to_thread(_write)

    async def _save_uncompressed(self, file_path: Path, data: Dict):
        """Save data without compression"""

        def _write() -> None:
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, default=str)

        await asyncio.to_thread(_write)

    async def _load_compressed(self, file_path: Path) -> Optional[Dict]:
        """Load compressed data"""

        def _read() -> Optional[Dict]:
            gz_path = (
                file_path
                if file_path.suffix == ".gz"
                else file_path.with_suffix(file_path.suffix + ".gz")
            )
            try:
                with gzip.open(gz_path, "rt", encoding="utf-8") as f:
                    return json.load(f)
            except (gzip.BadGzipFile, OSError, FileNotFoundError) as exc:
                self.logger.warning(
                    "_load_compressed: falha ao ler %s: %s", gz_path, exc
                )
                return None

        result = await asyncio.to_thread(_read)
        if result is None:
            alt_path = (
                file_path.with_suffix("") if file_path.suffix == ".gz" else file_path
            )
            return await self._load_uncompressed(alt_path)
        return result

    async def _load_uncompressed(self, file_path: Path) -> Optional[Dict]:
        """Load uncompressed data"""

        def _read() -> Optional[Dict]:
            with open(file_path, "r", encoding="utf-8") as f:
                return json.load(f)

        return await asyncio.to_thread(_read)

    async def _acquire_lock(self, file_path: str):
        """Acquire file lock for concurrent access protection"""
        if portalocker is None:
            return
        try:
            # Use async lock to prevent race condition when checking/creating locks
            async with self._locks_lock:
                if file_path not in self.file_locks:
                    lock_path = f"{file_path}.lock"
                    lock = portalocker.Lock(lock_path, "a", timeout=5)
                    lock.acquire()
                    self.file_locks[file_path] = lock
        except Exception as e:
            # If locking fails, continue without lock (best effort)
            self.logger.warning(f"Could not acquire lock for {file_path}: {e}")

    async def _release_lock(self, file_path: str):
        """Release file lock"""
        if portalocker is None:
            return
        try:
            # Use async lock to prevent race condition when checking/removing locks
            async with self._locks_lock:
                if file_path in self.file_locks:
                    lock = self.file_locks[file_path]
                    lock.release()
                    del self.file_locks[file_path]

                    # Remove lock file
                    lock_path = f"{file_path}.lock"
                    if os.path.exists(lock_path):
                        os.unlink(lock_path)
        except Exception as e:
            self.logger.warning(f"Could not release lock for {file_path}: {e}")

    async def _check_backup_needed(self, key: str, category: str):
        """Check if backup is needed for this data"""
        try:
            backup_key = f"{category}:{key}"
            current_time = datetime.now(timezone.utc)

            last_backup = self.last_backup_time.get(backup_key)

            if (
                last_backup is None
                or (current_time - last_backup).total_seconds() > self.backup_interval
            ):
                await self._create_backup(key, category)
                self.last_backup_time[backup_key] = current_time

        except Exception as e:
            self.logger.error(f"Error checking backup for {key}: {e}")

    async def _create_backup(self, key: str, category: str):
        """Create backup of data file"""
        try:
            ext = ".json.gz" if self.compression else ".json"
            source_path = self.data_dir / category / f"{key}{ext}"

            if not source_path.exists():
                return

            # Create backup directory for this key
            backup_dir = self.data_dir / "backups" / category / key
            backup_dir.mkdir(parents=True, exist_ok=True)

            # Generate backup filename with timestamp
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            backup_path = backup_dir / f"{key}_{timestamp}{ext}"

            # Copy file
            await asyncio.to_thread(shutil.copy2, str(source_path), str(backup_path))

            # Cleanup old backups
            await self._cleanup_old_backups(backup_dir)

            self.logger.debug(f"Created backup for {key} in {category}")

        except Exception as e:
            self.logger.error(f"Error creating backup for {key}: {e}")

    async def _cleanup_old_backups(self, backup_dir: Path):
        """Remove old backup files, keeping only the most recent ones"""
        try:
            # Get all backup files
            ext = ".json.gz" if self.compression else ".json"
            backup_files = list(backup_dir.glob(f"*{ext}"))

            if len(backup_files) > self.max_backups:
                # Sort by modification time
                backup_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)

                # Remove old files
                for old_file in backup_files[self.max_backups :]:
                    await asyncio.to_thread(old_file.unlink)
                    self.logger.debug(f"Removed old backup: {old_file}")

        except Exception as e:
            self.logger.error(f"Error cleaning up backups: {e}")

    async def _backup_loop(self):
        """Background task for periodic backup management"""
        try:
            while True:
                await asyncio.sleep(3600)  # Run every hour
                await self._maintain_backups()

        except asyncio.CancelledError:
            self.logger.info("Backup loop cancelled")
        except Exception as e:
            self.logger.error(f"Error in backup loop: {e}")

    async def _maintain_backups(self):
        """Maintain backup system"""
        try:
            backup_root = self.data_dir / "backups"

            if not backup_root.exists():
                return

            # Cleanup old backups across all categories
            for category_dir in backup_root.iterdir():
                if category_dir.is_dir():
                    for key_dir in category_dir.iterdir():
                        if key_dir.is_dir():
                            await self._cleanup_old_backups(key_dir)

            self.logger.debug("Backup maintenance completed")

        except Exception as e:
            self.logger.error(f"Error maintaining backups: {e}")

    async def restore_from_backup(
        self, key: str, category: str, backup_timestamp: Optional[str] = None
    ) -> bool:
        """
        Restore data from backup

        Args:
            key: Unique identifier for the data
            category: Category/subdirectory
            backup_timestamp: Specific backup timestamp, or None for latest

        Returns:
            True if successful, False otherwise
        """
        try:
            backup_dir = self.data_dir / "backups" / category / key

            if not backup_dir.exists():
                self.logger.error(f"No backups found for {key} in {category}")
                return False

            # Find backup file
            ext = ".json.gz" if self.compression else ".json"
            if backup_timestamp:
                backup_file = backup_dir / f"{key}_{backup_timestamp}{ext}"
                if not backup_file.exists():
                    self.logger.error(f"Backup not found: {backup_file}")
                    return False
            else:
                # Get latest backup
                backup_files = list(backup_dir.glob(f"*{ext}"))
                if not backup_files:
                    self.logger.error(f"No backup files found for {key}")
                    return False

                backup_file = max(backup_files, key=lambda f: f.stat().st_mtime)

            # Restore file
            target_path = self.data_dir / category / f"{key}{ext}"
            await asyncio.to_thread(shutil.copy2, str(backup_file), str(target_path))

            self.logger.info(f"Restored {key} from backup: {backup_file.name}")
            return True

        except Exception as e:
            self.logger.error(f"Error restoring from backup: {e}")
            return False

    async def export_data(
        self, export_path: str, categories: Optional[List[str]] = None
    ) -> bool:
        """
        Export data to external location

        Args:
            export_path: Path to export directory
            categories: List of categories to export, or None for all

        Returns:
            True if successful, False otherwise
        """
        try:
            export_dir = Path(export_path)
            export_dir.mkdir(parents=True, exist_ok=True)

            ext = ".json.gz" if self.compression else ".json"

            if categories is None:
                categories = [
                    d.name
                    for d in self.data_dir.iterdir()
                    if d.is_dir() and d.name != "backups"
                ]

            for category in categories:
                category_dir = self.data_dir / category
                if not category_dir.exists():
                    continue

                export_category_dir = export_dir / category
                export_category_dir.mkdir(exist_ok=True)

                for json_file in category_dir.glob(f"*{ext}"):
                    if not json_file.name.endswith(".tmp"):
                        await asyncio.to_thread(
                            shutil.copy2, str(json_file), str(export_category_dir)
                        )

            # Create export manifest
            manifest = {
                "export_time": datetime.now(timezone.utc).isoformat(),
                "categories": categories,
                "total_files": sum(1 for _ in export_dir.rglob(f"*{ext}")),
            }

            def _write_manifest() -> None:
                with open(export_dir / "manifest.json", "w") as f:
                    json.dump(manifest, f, indent=2)

            await asyncio.to_thread(_write_manifest)

            self.logger.info(f"Data exported to {export_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error exporting data: {e}")
            return False

    async def import_data(self, import_path: str, overwrite: bool = False) -> bool:
        """
        Import data from external location

        Args:
            import_path: Path to import directory
            overwrite: Whether to overwrite existing files

        Returns:
            True if successful, False otherwise
        """
        try:
            import_dir = Path(import_path)

            if not import_dir.exists():
                self.logger.error(f"Import directory not found: {import_path}")
                return False

            # Check for manifest
            manifest_path = import_dir / "manifest.json"
            if manifest_path.exists():

                def _read_manifest() -> Dict:
                    with open(manifest_path, "r") as f:
                        return json.load(f)

                manifest = await asyncio.to_thread(_read_manifest)
                self.logger.info(
                    f"Importing data from {manifest.get('export_time', 'unknown time')}"
                )

            ext = ".json.gz" if self.compression else ".json"

            # Import each category
            for category_dir in import_dir.iterdir():
                if category_dir.is_dir():
                    category = category_dir.name
                    target_dir = self.data_dir / category
                    target_dir.mkdir(exist_ok=True)

                    for json_file in category_dir.glob(f"*{ext}"):
                        target_file = target_dir / json_file.name

                        if target_file.exists() and not overwrite:
                            self.logger.warning(
                                f"Skipping existing file: {target_file}"
                            )
                            continue

                        await asyncio.to_thread(
                            shutil.copy2, str(json_file), str(target_file)
                        )

            self.logger.info(f"Data imported from {import_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error importing data: {e}")
            return False

    async def get_storage_stats(self) -> Dict:
        """Get storage statistics"""
        try:

            def _collect() -> Dict:
                stats = {
                    "total_files": 0,
                    "total_size": 0,
                    "categories": {},
                    "backup_files": 0,
                    "backup_size": 0,
                }

                ext = ".json.gz" if self.compression else ".json"

                for category_dir in self.data_dir.iterdir():
                    if category_dir.is_dir() and category_dir.name != "backups":
                        category = category_dir.name
                        category_stats = {"files": 0, "size": 0}

                        for json_file in category_dir.glob(f"*{ext}"):
                            if not json_file.name.endswith(".tmp"):
                                file_size = json_file.stat().st_size
                                category_stats["files"] += 1
                                category_stats["size"] += file_size
                                stats["total_files"] += 1
                                stats["total_size"] += file_size

                        stats["categories"][category] = category_stats

                backup_dir = self.data_dir / "backups"
                if backup_dir.exists():
                    for backup_file in backup_dir.rglob(f"*{ext}"):
                        file_size = backup_file.stat().st_size
                        stats["backup_files"] += 1
                        stats["backup_size"] += file_size

                return stats

            return await asyncio.to_thread(_collect)

        except Exception as e:
            self.logger.error(f"Error getting storage stats: {e}")
            return {}

    async def close(self):
        """Close the JSON store and cleanup resources"""
        try:
            if self._backup_task is not None:
                self._backup_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._backup_task
                self._backup_task = None

            # Release all file locks
            for file_path in list(self.file_locks.keys()):
                await self._release_lock(file_path)

            self.logger.info("JSON store closed")

        except Exception as exc:
            self.logger.exception("Error closing JSON store", exc_info=exc)
            raise
