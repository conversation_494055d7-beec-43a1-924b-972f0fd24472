# Rastreador de Issues do QUALIA

Atualizado em: 2025-07-01

Este documento registra as tarefas pendentes para alinhar o código às melhorias documentadas em `src/qualia/core/UNIVERSE_IMPROVEMENTS.md` e no arquivo de resumo [UNIVERSE_IMPROVEMENTS_SUMMARY.md](UNIVERSE_IMPROVEMENTS_SUMMARY.md).

| ID | Área | Descrição | Origem | Status |
|----|------|-----------|--------|--------|
| 1 | Performance | Refatorar `update_last_statevector` (função grande) | UNIVERSE_IMPROVEMENTS.md | Concluído |
| 2 | Performance | Implementar cache para operações custosas | UNIVERSE_IMPROVEMENTS.md | Concluído |
| 3 | Performance | Otimizar cópias de Statevector | UNIVERSE_IMPROVEMENTS.md | Concluído |
| 4 | Qualidade de Código | Adicionar type hints completos em todo o módulo | UNIVERSE_IMPROVEMENTS.md | Concluído |
| 5 | Testes | Aumentar a cobertura de testes unitários | UNIVERSE_IMPROVEMENTS.md | Parcial |
| 6 | Qualidade de Código | Quebrar lógica complexa em métodos menores | UNIVERSE_IMPROVEMENTS.md | Parcial |
| 7 | Funcionalidade | Implementar controle LQR completo com cálculo de ganho | UNIVERSE_IMPROVEMENTS.md | Concluído |
| 8 | Funcionalidade | Adicionar métodos adicionais de assinatura quântica | UNIVERSE_IMPROVEMENTS.md | Concluído |
| 9 | Funcionalidade | Expandir integração com Quantum Pattern Memory | UNIVERSE_IMPROVEMENTS.md | Concluído |
| 10 | Refatoração | Dividir `run_with_qast_feedback` em subfunções menores | UNIVERSE_IMPROVEMENTS_SUMMARY.md | Concluído |

| 11 | Testes | Criar testes para validação de parâmetros e configuração dinâmica | UNIVERSE_IMPROVEMENTS_SUMMARY.md | Concluído |
| 12 | Performance | Otimizar loops críticos | UNIVERSE_IMPROVEMENTS_SUMMARY.md | Concluído |
| 13 | Integração | Finalizar integração do AdaptiveConsciousnessEvolution | UNIVERSE_IMPROVEMENTS_SUMMARY.md | Concluído |
| 14 | Estratégias | Calcular dinamicamente o período mínimo de lookback na CompositeStrategy | Código (CompositeStrategy.backtest) | Concluído |
| 15 | Estratégias | Integrar stop-loss e take-profit das subestratégias no backtest da CompositeStrategy | Código (CompositeStrategy.backtest) | Concluído |
| 16 | Estratégias | Remover dependências das funções legadas do módulo `registry` e migrar para `StrategyFactory` | Código (`__init__.py` e `registry.py`) | Concluído |
| 17 | Integração | Implementar Event Bus e feature flags no módulo Memory | analysis/integration_memory_double_check.md | Concluído |
| 18 | Integração | Instrumentar métricas e YAML nos encoders, emitir eventos | analysis/integration_ai_double_check.md | Concluído |
| 19 | Integração | Adicionar eventos e feature flags no módulo Market | analysis/integration_market_double_check.md | Concluído |
| 20 | Integração | Eventos `risk.update` e benchmarks de sizing | analysis/integration_risk_management_double_check.md | Concluído |
| 21 | Integração | Ajustes de evento e tracing já aplicados no Adaptive Evolution | analysis/integration_adaptive_evolution_double_check.md | Concluído |
| 22 | Integração | Habilitar eventos e feature flag na Nova Estratégia | analysis/integration_nova_estrategia_qualia_double_check.md | Concluído |

| 23 | Integração | Instrumentar Event Bus, configs YAML e tracing no módulo Metacognition | DoubleCheck_Metacognition.md | Concluído |
| 24 | Integração | Instrumentar eventos, YAML e benchmarks no módulo Utils | analysis/integration_utils_double_check.md | Concluído |
| 25 | Integração | Integrar fontes de sentimento social e sinais on-chain ao RealDataCollector | docs/roadmap.md | Concluído |
| 26 | Funcionalidade | Criar módulo RetrocausalInsightCollector para feedback temporal | docs/roadmap.md | Concluído |
| 27 | Observabilidade | Instrumentar QUALIATradingSystem com OpenTelemetry | docs/roadmap.md | Concluído |
| 28 | Performance | Criar pipeline de benchmarks contínuos | docs/roadmap.md | Concluído |
| 29 | Risco | Desenvolver RiskStressSimulator para cenários extremos | docs/roadmap.md | Concluído |
| 30 | Estratégias | Implementar sistema adaptativo de liquidez | docs/roadmap.md | Concluído |
| 31 | Infraestrutura | Explorar aceleração em hardware dedicado (FPGA/GPU) | docs/roadmap.md | Concluído |
| 32 | Arquitetura | Implementar barramento de eventos central (`qualia/event_bus.py`) e registrar assinantes | docs/event_bus.md | Concluído |
| 33 | Arquitetura | Publicar `MarketDataUpdated` em `_data_collection_loop` e ajustar assinantes | docs/event_bus.md | Concluído |
| 34 | Arquitetura | Publicar `TradingSignalGenerated` e vincular o executor via barramento | docs/event_bus.md | Concluído |
| 35 | Arquitetura | Propagar o barramento para risco, monitoramento e persistência | docs/event_bus.md | Concluído |
| 36 | Estratégias | Implementar Consciência de Enxame consolidando votos das estratégias | docs/roadmap.md | Concluído |
| 37 | Funcionalidade | Criar `ProspectiveSimulationAgent` para canal retrocausal ativo | docs/roadmap.md | Concluído |
| 38 | Observabilidade | Desenvolver `ContextRelevanceAgent` para percepção multimodal | docs/roadmap.md | Concluído |

## Itens Pendentes

- **Aumentar a cobertura de testes unitários** – diversos módulos ainda carecem de testes adequados. (Issue 5)
- **Quebrar lógicas complexas em métodos menores** – várias áreas contêm funções extensas. (Issue 6)

## Itens Resolvidos

- **Refatorar `update_last_statevector`** – função dividida em métodos menores. (Issue 1)
- **Implementar cache para operações custosas** – otimiza cálculos repetidos. (Issue 2)
- **Otimizar cópias de Statevector** – utiliza cópias rasas para economia de memória. (Issue 3)
- **Adicionar type hints completos em todo o módulo** – melhora a qualidade do código. (Issue 4)
- **Implementar controle LQR completo com cálculo de ganho** – controlador atualizado com recomputação de ganho. (Issue 7)
- **Adicionar métodos adicionais de assinatura quântica** – incorpora `phase_vector` e `cumulative_probabilities`. (Issue 8)
- **Expandir integração com Quantum Pattern Memory** – interoperações finalizadas. (Issue 9)
- **Dividir `run_with_qast_feedback` em subfunções menores** – melhora legibilidade. (Issue 10)
- **Criar testes para validação de parâmetros e configuração dinâmica** – garante segurança robusta em tempo de execução. (Issue 11)
- **Otimizar loops críticos** – aprimora performance em rotinas sensíveis. (Issue 12)
- **Finalizar integração do AdaptiveConsciousnessEvolution** – ajustes dinâmicos durante o trading ao vivo. (Issue 13)
- **Calcular dinamicamente o período mínimo de lookback na CompositeStrategy** – otimiza backtests. (Issue 14)
- **Integrar stop-loss e take-profit das subestratégias na CompositeStrategy** – controle unificado de risco. (Issue 15)
- **Remover dependências das funções legadas do módulo `registry`** – migração para `StrategyFactory`. (Issue 16)
- **Implementar Event Bus e feature flags no módulo Memory** – rastreio centralizado. (Issue 17)
- **Instrumentar métricas e YAML nos encoders, emitir eventos** – telemetria refinada. (Issue 18)
- **Adicionar eventos e feature flags no módulo Market** – integração completa. (Issue 19)
- **Eventos `risk.update` e benchmarks de sizing** – fortalecimento do controle de risco. (Issue 20)
- **Ajustes de evento e tracing no Adaptive Evolution** – consistência de fluxo. (Issue 21)
- **Habilitar eventos e feature flag na Nova Estratégia** – instrumentação padronizada. (Issue 22)
- **Instrumentar Event Bus, configs YAML e tracing no módulo Metacognition** – rastreabilidade total. (Issue 23)
- **Instrumentar eventos, YAML e benchmarks no módulo Utils** – uniformidade de monitoramento. (Issue 24)
- **Integrar fontes de sentimento social e sinais on-chain ao RealDataCollector** – visão ampliada de mercado. (Issue 25)
- **Criar módulo RetrocausalInsightCollector para feedback temporal** – suporte a análises retrocausais. (Issue 26)
- **Instrumentar QUALIATradingSystem com OpenTelemetry** – observabilidade expandida. (Issue 27)
- **Criar pipeline de benchmarks contínuos** – medições de performance automatizadas. (Issue 28)
- **Desenvolver RiskStressSimulator para cenários extremos** – validação de resiliência. (Issue 29)
- **Implementar sistema adaptativo de liquidez** – adequação dinâmica ao mercado. (Issue 30)
- **Explorar aceleração em hardware dedicado (FPGA/GPU)** – ganhos de desempenho. (Issue 31)
- **Implementar barramento de eventos central (`qualia/event_bus.py`) e registrar assinantes** – arquitetura unificada. (Issue 32)
- **Publicar `MarketDataUpdated` em `_data_collection_loop` e ajustar assinantes** – comunicação padronizada. (Issue 33)
- **Publicar `TradingSignalGenerated` e vincular o executor via barramento** – integração plena com o executor. (Issue 34)
- **Propagar o barramento para risco, monitoramento e persistência** – eventos distribuídos. (Issue 35)
- **Implementar Consciência de Enxame consolidando votos das estratégias** – decisão colaborativa. (Issue 36)
- **Criar `ProspectiveSimulationAgent` para canal retrocausal ativo** – antecipação de cenários. (Issue 37)
- **Desenvolver `ContextRelevanceAgent` para percepção multimodal** – correlação de sinais. (Issue 38)

Cada item deve ser acompanhado como uma issue separada no sistema de gerenciamento do projeto. Conforme as tarefas forem concluídas, marque-as como feitas e atualize a documentação de referência.

Para mais detalhes sobre o barramento de eventos, consulte o arquivo [docs/event_bus.md](event_bus.md), introduzido no commit `3bcfdd4b`.

## Logs de Testes
O arquivo de saída de testes `result_testes.txt` agora é gerado diretamente em
UTF-8, permitindo o uso de ferramentas como `grep` e `pytest -vv` sem qualquer
conversão. Logs antigos em UTF-16 LE ainda podem ser convertidos com `iconv` se
necessário:

```bash
iconv -f UTF-16LE -t UTF-8 result_testes.txt > result_testes_utf8.txt
```
Última execução em 2025-07-02: todos os testes unitários passaram e a cobertura global manteve-se acima de 80%.
