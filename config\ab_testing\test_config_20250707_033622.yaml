config_id: test_config_20250707_033622
config_name: Execution Settings Test
created_at: '2025-07-07T03:36:22.845903'
description: ''
execution_config:
  allow_partial_fills: true
  default_order_type: market
  expected_slippage_pct: 0.05
  limit_order_offset_pct: 0.1
  max_order_age_seconds: 300
  max_orders_per_minute: 5
  max_slippage_pct: 0.1
  max_spread_pct: 0.1
  min_fill_pct: 50.0
  min_volume_usd: 1000000.0
  order_retry_attempts: 3
  order_retry_delay_seconds: 5
  order_timeout_seconds: 30
  paper_trading: true
  trading_fees_pct: 0.1
live_config:
  enable_live_feed: true
  enable_order_management: true
  exchange: kucoin
  max_orders_per_minute: 10
  mode: paper_trading
  risk_checks_enabled: true
  strategy: Execution Test
  symbols:
  - BTC/USDT
risk_config:
  avoid_news_events: true
  initial_capital: 10000.0
  max_capital_risk_pct: 2.0
  max_consecutive_losses: 5
  max_correlation_exposure: 0.7
  max_daily_loss_pct: 5.0
  max_position_size_pct: 10.0
  max_total_exposure_pct: 50.0
  min_position_size_usd: 10.0
  news_blackout_minutes: 30
  position_sizing_method: fixed_pct
  stop_loss_pct: 2.0
  take_profit_pct: 4.0
  trading_hours_end: '23:59'
  trading_hours_start: 00:00
  trailing_stop_enabled: false
  trailing_stop_pct: 1.0
simulator_config:
  data_source: historical
  enable_market_impact: true
  enable_realistic_fills: true
  fee_model: percentage
  latency_ms: 50
  latency_simulation: true
  mode: backtesting
  slippage_model: linear
  strategy: Execution Test
  symbols:
  - BTC/USDT
strategy_config:
  entry_conditions:
  - rsi < oversold
  - price > ema_fast
  - volume > avg_volume * 1.2
  exit_conditions:
  - rsi > overbought
  - price < ema_slow
  - stop_loss_hit
  - take_profit_hit
  indicators:
    ema:
      fast: 12
      slow: 26
    rsi:
      overbought: 70
      oversold: 30
      period: 14
    volume:
      period: 20
  name: Execution Test
  parameters: {}
  primary_timeframe: 1h
  secondary_timeframes:
  - 5m
  - 15m
  type: momentum
symbols:
- BTC/USDT
tags:
- ab_test
- simulator_vs_live
test_duration_hours: 24.0
version: '1.0'
warmup_period_minutes: 15.0
