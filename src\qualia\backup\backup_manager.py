"""
QUALIA Production Backup and Recovery Manager
Automated backup, versioning, and disaster recovery for production data
"""

import os
import json
import shutil
import gzip
import hashlib
import asyncio
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import yaml
import tarfile
import threading
from dataclasses import dataclass, asdict
import schedule
import time

logger = logging.getLogger(__name__)

@dataclass
class BackupInfo:
    """Backup metadata information"""
    backup_id: str
    timestamp: datetime
    backup_type: str  # 'full', 'incremental', 'differential'
    size_bytes: int
    file_count: int
    checksum: str
    retention_days: int
    status: str  # 'completed', 'failed', 'in_progress'
    files_backed_up: List[str]
    error_message: Optional[str] = None

@dataclass
class RecoveryPoint:
    """Recovery point information"""
    recovery_id: str
    timestamp: datetime
    backup_id: str
    description: str
    system_state: Dict[str, Any]
    verified: bool = False

class ProductionBackupManager:
    """
    Production backup and recovery system
    Features:
    - Automated scheduled backups
    - Full, incremental, and differential backups
    - Data integrity verification
    - Point-in-time recovery
    - Disaster recovery procedures
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.backup_config = config.get('backup', {})
        
        # Backup settings
        self.backup_dir = Path(self.backup_config.get('storage', {}).get('local', {}).get('path', 'backups'))
        self.retention_days = self.backup_config.get('storage', {}).get('local', {}).get('retention_days', 30)
        self.backup_frequency = self.backup_config.get('frequency', 3600)  # 1 hour default
        
        # Backup targets
        self.backup_targets = self.backup_config.get('targets', [])
        
        # State
        self.backup_history = []
        self.recovery_points = []
        self.is_running = False
        self.backup_thread = None
        
        # Create backup directory
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Load backup history
        self._load_backup_history()
        
        # Setup scheduler
        self._setup_backup_schedule()
    
    def _setup_backup_schedule(self):
        """Setup automated backup scheduling"""
        if self.backup_config.get('enabled', True):
            # Schedule regular backups
            schedule.every(self.backup_frequency).seconds.do(self._scheduled_backup)
            
            # Schedule daily full backup at 2 AM
            schedule.every().day.at("02:00").do(self._scheduled_full_backup)
            
            # Schedule weekly cleanup
            schedule.every().sunday.at("03:00").do(self._cleanup_old_backups)
    
    async def start_backup_service(self):
        """Start the backup service"""
        if self.is_running:
            logger.warning("Backup service is already running")
            return
        
        self.is_running = True
        logger.info("Starting QUALIA backup service")
        
        # Start scheduler thread
        self.backup_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.backup_thread.start()
        
        # Perform initial backup
        await self.create_backup('full', 'Initial production backup')
    
    def stop_backup_service(self):
        """Stop the backup service"""
        if not self.is_running:
            return
        
        self.is_running = False
        logger.info("Stopping QUALIA backup service")
        
        # Clear scheduled jobs
        schedule.clear()
    
    def _run_scheduler(self):
        """Run the backup scheduler"""
        while self.is_running:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
    
    def _scheduled_backup(self):
        """Scheduled incremental backup"""
        try:
            asyncio.run(self.create_backup('incremental', 'Scheduled incremental backup'))
        except Exception as e:
            logger.error(f"Scheduled backup failed: {e}")
    
    def _scheduled_full_backup(self):
        """Scheduled full backup"""
        try:
            asyncio.run(self.create_backup('full', 'Scheduled full backup'))
        except Exception as e:
            logger.error(f"Scheduled full backup failed: {e}")
    
    async def create_backup(self, backup_type: str = 'incremental', 
                           description: str = '') -> Optional[BackupInfo]:
        """
        Create a backup of the system
        
        Args:
            backup_type: Type of backup ('full', 'incremental', 'differential')
            description: Backup description
            
        Returns:
            BackupInfo object or None if failed
        """
        backup_id = f"backup_{backup_type}_{int(time.time())}"
        logger.info(f"Creating {backup_type} backup: {backup_id}")
        
        try:
            # Create backup info
            backup_info = BackupInfo(
                backup_id=backup_id,
                timestamp=datetime.utcnow(),
                backup_type=backup_type,
                size_bytes=0,
                file_count=0,
                checksum='',
                retention_days=self.retention_days,
                status='in_progress',
                files_backed_up=[]
            )
            
            # Create backup directory
            backup_path = self.backup_dir / backup_id
            backup_path.mkdir(exist_ok=True)
            
            # Determine files to backup
            files_to_backup = await self._get_files_to_backup(backup_type)
            
            # Backup files
            total_size = 0
            backed_up_files = []
            
            for source_file in files_to_backup:
                if not os.path.exists(source_file):
                    continue
                
                # Calculate relative path
                rel_path = os.path.relpath(source_file)
                backup_file_path = backup_path / rel_path
                
                # Create directory structure
                backup_file_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Copy and compress file
                if source_file.endswith(('.json', '.yaml', '.yml', '.log', '.txt')):
                    # Compress text files
                    with open(source_file, 'rb') as f_in:
                        with gzip.open(f"{backup_file_path}.gz", 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    file_size = os.path.getsize(f"{backup_file_path}.gz")
                    backed_up_files.append(f"{rel_path}.gz")
                else:
                    # Copy binary files as-is
                    shutil.copy2(source_file, backup_file_path)
                    file_size = os.path.getsize(backup_file_path)
                    backed_up_files.append(rel_path)
                
                total_size += file_size
            
            # Create backup archive
            archive_path = self.backup_dir / f"{backup_id}.tar.gz"
            with tarfile.open(archive_path, 'w:gz') as tar:
                tar.add(backup_path, arcname=backup_id)
            
            # Calculate checksum
            checksum = await self._calculate_checksum(archive_path)
            
            # Update backup info
            backup_info.size_bytes = os.path.getsize(archive_path)
            backup_info.file_count = len(backed_up_files)
            backup_info.checksum = checksum
            backup_info.status = 'completed'
            backup_info.files_backed_up = backed_up_files
            
            # Clean up temporary directory
            shutil.rmtree(backup_path)
            
            # Save backup metadata
            await self._save_backup_metadata(backup_info)
            
            # Add to history
            self.backup_history.append(backup_info)
            
            # Create recovery point
            await self._create_recovery_point(backup_info, description)
            
            logger.info(f"Backup completed: {backup_id} ({backup_info.size_bytes} bytes, {backup_info.file_count} files)")
            return backup_info
            
        except Exception as e:
            logger.error(f"Backup creation failed: {e}")
            
            # Update backup info with error
            backup_info.status = 'failed'
            backup_info.error_message = str(e)
            
            return None
    
    async def _get_files_to_backup(self, backup_type: str) -> List[str]:
        """Get list of files to backup based on backup type"""
        files_to_backup = []
        
        # Always backup these critical files
        critical_files = [
            'config/production_config.yaml',
            'data/positions.json',
            'data/trades.json',
            'data/optimization_results.json',
            'logs/qualia_production.log',
            'logs/trading.log',
            'logs/optimization.log',
            'logs/errors.log'
        ]
        
        # Add configured backup targets
        for target in self.backup_targets:
            if os.path.exists(target):
                if os.path.isfile(target):
                    critical_files.append(target)
                elif os.path.isdir(target):
                    # Add all files in directory
                    for root, dirs, files in os.walk(target):
                        for file in files:
                            critical_files.append(os.path.join(root, file))
        
        if backup_type == 'full':
            # Full backup includes all files
            files_to_backup = critical_files
            
        elif backup_type == 'incremental':
            # Incremental backup includes files changed since last backup
            last_backup_time = self._get_last_backup_time()
            
            for file_path in critical_files:
                if os.path.exists(file_path):
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if not last_backup_time or file_mtime > last_backup_time:
                        files_to_backup.append(file_path)
                        
        elif backup_type == 'differential':
            # Differential backup includes files changed since last full backup
            last_full_backup_time = self._get_last_full_backup_time()
            
            for file_path in critical_files:
                if os.path.exists(file_path):
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if not last_full_backup_time or file_mtime > last_full_backup_time:
                        files_to_backup.append(file_path)
        
        return files_to_backup
    
    def _get_last_backup_time(self) -> Optional[datetime]:
        """Get timestamp of last backup"""
        if not self.backup_history:
            return None
        
        completed_backups = [b for b in self.backup_history if b.status == 'completed']
        if not completed_backups:
            return None
        
        return max(b.timestamp for b in completed_backups)
    
    def _get_last_full_backup_time(self) -> Optional[datetime]:
        """Get timestamp of last full backup"""
        full_backups = [b for b in self.backup_history 
                       if b.backup_type == 'full' and b.status == 'completed']
        
        if not full_backups:
            return None
        
        return max(b.timestamp for b in full_backups)
    
    async def _calculate_checksum(self, file_path: Path) -> str:
        """Calculate SHA-256 checksum of file"""
        sha256_hash = hashlib.sha256()
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        
        return sha256_hash.hexdigest()
    
    async def _save_backup_metadata(self, backup_info: BackupInfo):
        """Save backup metadata to file"""
        metadata_file = self.backup_dir / f"{backup_info.backup_id}_metadata.json"
        
        # Convert datetime objects to strings for JSON serialization
        metadata = asdict(backup_info)
        metadata['timestamp'] = backup_info.timestamp.isoformat()
        
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)
    
    async def _create_recovery_point(self, backup_info: BackupInfo, description: str):
        """Create a recovery point"""
        recovery_point = RecoveryPoint(
            recovery_id=f"recovery_{int(time.time())}",
            timestamp=backup_info.timestamp,
            backup_id=backup_info.backup_id,
            description=description,
            system_state=await self._capture_system_state()
        )
        
        self.recovery_points.append(recovery_point)
        
        # Save recovery point
        recovery_file = self.backup_dir / f"{recovery_point.recovery_id}.json"
        recovery_data = asdict(recovery_point)
        recovery_data['timestamp'] = recovery_point.timestamp.isoformat()
        
        with open(recovery_file, 'w') as f:
            json.dump(recovery_data, f, indent=2)
    
    async def _capture_system_state(self) -> Dict[str, Any]:
        """Capture current system state"""
        try:
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'config_checksum': await self._get_config_checksum(),
                'running_processes': self._get_running_processes(),
                'system_metrics': self._get_system_metrics(),
                'disk_usage': self._get_disk_usage(),
                'environment_variables': dict(os.environ)
            }
        except Exception as e:
            logger.error(f"Failed to capture system state: {e}")
            return {'error': str(e)}
    
    async def _get_config_checksum(self) -> str:
        """Get checksum of configuration file"""
        try:
            config_file = self.config.get('config_path', 'config/production_config.yaml')
            if os.path.exists(config_file):
                return await self._calculate_checksum(Path(config_file))
            return ''
        except Exception:
            return ''
    
    def _get_running_processes(self) -> List[Dict[str, Any]]:
        """Get list of running processes"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'qualia' in ' '.join(proc.info['cmdline']).lower():
                        processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            return processes
        except Exception:
            return []
    
    def _get_system_metrics(self) -> Dict[str, Any]:
        """Get current system metrics"""
        try:
            return {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('.').percent,
                'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else None
            }
        except Exception:
            return {}
    
    def _get_disk_usage(self) -> Dict[str, Any]:
        """Get disk usage information"""
        try:
            usage = psutil.disk_usage('.')
            return {
                'total_gb': usage.total / (1024**3),
                'used_gb': usage.used / (1024**3),
                'free_gb': usage.free / (1024**3),
                'percent_used': (usage.used / usage.total) * 100
            }
        except Exception:
            return {}
    
    async def restore_from_backup(self, backup_id: str, 
                                 target_dir: str = '.') -> bool:
        """
        Restore system from backup
        
        Args:
            backup_id: ID of backup to restore from
            target_dir: Target directory for restoration
            
        Returns:
            bool: Restoration success
        """
        logger.info(f"Restoring from backup: {backup_id}")
        
        try:
            # Find backup
            backup_info = self._find_backup(backup_id)
            if not backup_info:
                logger.error(f"Backup not found: {backup_id}")
                return False
            
            # Verify backup integrity
            if not await self._verify_backup_integrity(backup_info):
                logger.error(f"Backup integrity check failed: {backup_id}")
                return False
            
            # Create restoration directory
            restore_dir = Path(target_dir) / f"restore_{int(time.time())}"
            restore_dir.mkdir(parents=True, exist_ok=True)
            
            # Extract backup
            archive_path = self.backup_dir / f"{backup_id}.tar.gz"
            with tarfile.open(archive_path, 'r:gz') as tar:
                tar.extractall(restore_dir)
            
            # Restore files
            backup_content_dir = restore_dir / backup_id
            
            for backed_up_file in backup_info.files_backed_up:
                source_path = backup_content_dir / backed_up_file
                
                # Handle compressed files
                if backed_up_file.endswith('.gz'):
                    target_file = backed_up_file[:-3]  # Remove .gz extension
                    target_path = Path(target_dir) / target_file
                    
                    # Create target directory
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # Decompress file
                    with gzip.open(source_path, 'rb') as f_in:
                        with open(target_path, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                else:
                    target_path = Path(target_dir) / backed_up_file
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(source_path, target_path)
            
            # Clean up restoration directory
            shutil.rmtree(restore_dir)
            
            logger.info(f"Restoration completed successfully: {backup_id}")
            return True
            
        except Exception as e:
            logger.error(f"Restoration failed: {e}")
            return False
    
    def _find_backup(self, backup_id: str) -> Optional[BackupInfo]:
        """Find backup by ID"""
        for backup in self.backup_history:
            if backup.backup_id == backup_id:
                return backup
        return None
    
    async def _verify_backup_integrity(self, backup_info: BackupInfo) -> bool:
        """Verify backup file integrity"""
        try:
            archive_path = self.backup_dir / f"{backup_info.backup_id}.tar.gz"
            
            if not archive_path.exists():
                logger.error(f"Backup file not found: {archive_path}")
                return False
            
            # Verify checksum
            current_checksum = await self._calculate_checksum(archive_path)
            if current_checksum != backup_info.checksum:
                logger.error(f"Backup checksum mismatch: expected {backup_info.checksum}, got {current_checksum}")
                return False
            
            # Verify archive can be opened
            with tarfile.open(archive_path, 'r:gz') as tar:
                tar.getnames()  # This will raise an exception if archive is corrupted
            
            return True
            
        except Exception as e:
            logger.error(f"Backup integrity verification failed: {e}")
            return False
    
    def _cleanup_old_backups(self):
        """Clean up old backups based on retention policy"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=self.retention_days)
            
            backups_to_remove = []
            for backup in self.backup_history:
                if backup.timestamp < cutoff_date and backup.backup_type != 'full':
                    backups_to_remove.append(backup)
            
            for backup in backups_to_remove:
                # Remove backup file
                archive_path = self.backup_dir / f"{backup.backup_id}.tar.gz"
                if archive_path.exists():
                    archive_path.unlink()
                
                # Remove metadata file
                metadata_path = self.backup_dir / f"{backup.backup_id}_metadata.json"
                if metadata_path.exists():
                    metadata_path.unlink()
                
                # Remove from history
                self.backup_history.remove(backup)
                
                logger.info(f"Removed old backup: {backup.backup_id}")
            
        except Exception as e:
            logger.error(f"Backup cleanup failed: {e}")
    
    def _load_backup_history(self):
        """Load backup history from metadata files"""
        try:
            for metadata_file in self.backup_dir.glob("*_metadata.json"):
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                # Convert timestamp back to datetime
                metadata['timestamp'] = datetime.fromisoformat(metadata['timestamp'])
                
                backup_info = BackupInfo(**metadata)
                self.backup_history.append(backup_info)
            
            # Sort by timestamp
            self.backup_history.sort(key=lambda x: x.timestamp)
            
        except Exception as e:
            logger.error(f"Failed to load backup history: {e}")
    
    def get_backup_history(self) -> List[BackupInfo]:
        """Get backup history"""
        return self.backup_history.copy()
    
    def get_recovery_points(self) -> List[RecoveryPoint]:
        """Get available recovery points"""
        return self.recovery_points.copy()
    
    def get_backup_status(self) -> Dict[str, Any]:
        """Get backup system status"""
        return {
            'service_running': self.is_running,
            'total_backups': len(self.backup_history),
            'successful_backups': len([b for b in self.backup_history if b.status == 'completed']),
            'failed_backups': len([b for b in self.backup_history if b.status == 'failed']),
            'last_backup': self.backup_history[-1].timestamp.isoformat() if self.backup_history else None,
            'next_scheduled_backup': 'Every hour',
            'retention_days': self.retention_days,
            'backup_directory': str(self.backup_dir),
            'total_backup_size_mb': sum(b.size_bytes for b in self.backup_history) / (1024*1024)
        }

# Global backup manager instance
_backup_manager = None

def initialize_backup_manager(config: Dict[str, Any]) -> ProductionBackupManager:
    """Initialize global backup manager"""
    global _backup_manager
    _backup_manager = ProductionBackupManager(config)
    return _backup_manager

def get_backup_manager() -> Optional[ProductionBackupManager]:
    """Get global backup manager instance"""
    return _backup_manager
