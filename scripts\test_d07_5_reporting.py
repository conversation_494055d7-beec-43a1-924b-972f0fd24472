#!/usr/bin/env python3
"""
Teste D-07.5: Statistical Analysis & Reporting Engine

Testa o sistema de análise estatística e geração de relatórios.
"""

import asyncio
import sys
import tempfile
from pathlib import Path
from datetime import datetime, timedelta
from dataclasses import dataclass

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.ab_testing.reporting_engine import ReportingEngine, ABTestReport
from qualia.ab_testing.ab_test_framework import ABTestResult, ABTestConfig
from qualia.ab_testing.performance_comparator import PerformanceMetrics
from qualia.ab_testing.data_quality_validator import DataQualityMetrics
from qualia.ab_testing.statistical_analyzer import StatisticalResult


@dataclass
class TestResult:
    """Resultado de um teste."""
    name: str
    passed: bool
    details: str = ""
    error: str = ""


class D07_5_ReportingTester:
    """Testador para D-07.5 Reporting Engine."""
    
    def __init__(self):
        """Inicializa o testador."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.reporting_engine = ReportingEngine(output_dir=self.temp_dir / "reports")
        self.results = []
    
    async def run_all_tests(self):
        """Executa todos os testes."""
        
        print("🚀 Testando D-07.5 Statistical Analysis & Reporting Engine\n")
        
        tests = [
            ("Report Generation", self.test_report_generation),
            ("Executive Summary", self.test_executive_summary),
            ("Performance Analysis", self.test_performance_analysis),
            ("Statistical Analysis", self.test_statistical_analysis),
            ("Quality Analysis", self.test_quality_analysis),
            ("Risk Analysis", self.test_risk_analysis),
            ("Recommendations", self.test_recommendations),
            ("HTML Export", self.test_html_export),
            ("JSON Export", self.test_json_export),
            ("Overall Scoring", self.test_overall_scoring)
        ]
        
        for test_name, test_func in tests:
            print(f"🧪 Executando: {test_name}")
            try:
                result = await test_func()
                if result.passed:
                    print(f"✅ {test_name}: PASSOU")
                    if result.details:
                        print(f"   {result.details}")
                else:
                    print(f"❌ {test_name}: FALHOU - {result.error}")
                self.results.append(result)
            except Exception as e:
                print(f"❌ {test_name}: FALHOU - {str(e)}")
                self.results.append(TestResult(test_name, False, error=str(e)))
            print()
        
        # Resumo
        passed = sum(1 for r in self.results if r.passed)
        total = len(self.results)
        
        print("=" * 60)
        print("📋 RESUMO DOS TESTES D-07.5")
        print("=" * 60)
        
        for result in self.results:
            status = "✅ PASSOU" if result.passed else "❌ FALHOU"
            print(f"{status}: {result.name}")
            if not result.passed and result.error:
                print(f"    Erro: {result.error}")
        
        print(f"\nTaxa de Sucesso: {passed}/{total} ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 D-07.5 Statistical Analysis & Reporting implementado com sucesso!")
        else:
            print(f"❌ {total - passed} testes falharam.")
        
        return passed == total
    
    def create_mock_test_result(self) -> ABTestResult:
        """Cria resultado de teste mock."""

        from qualia.ab_testing.ab_test_framework import TestType, TestStatus

        result = ABTestResult(
            test_id="test_d07_5",
            test_name="Mock A/B Test",
            test_type=TestType.SIMULATOR_VS_LIVE,
            status=TestStatus.COMPLETED,
            start_time=datetime.now() - timedelta(hours=24),
            end_time=datetime.now(),
            duration_seconds=24.0 * 3600
        )

        # Adicionar propriedade duration_hours para compatibilidade
        result.duration_hours = result.duration_seconds / 3600.0

        return result
    
    def create_mock_performance_metrics(self) -> PerformanceMetrics:
        """Cria métricas de performance mock."""

        return PerformanceMetrics(
            session_id="mock_session",
            session_type="simulator",
            start_time=datetime.now() - timedelta(hours=24),
            end_time=datetime.now(),
            total_trades=45,
            winning_trades=28,
            losing_trades=17,
            total_pnl=1250.75,
            total_pnl_pct=12.51,
            gross_profit=2100.50,
            gross_loss=-849.75,
            sharpe_ratio=1.85,
            max_drawdown_pct=-8.0,
            win_rate=0.62,
            avg_trade_duration_minutes=252.0  # 4.2 hours
        )
    
    def create_mock_quality_metrics(self) -> DataQualityMetrics:
        """Cria métricas de qualidade mock."""

        return DataQualityMetrics(
            session_id="mock_quality_session",
            start_time=datetime.now() - timedelta(hours=24),
            end_time=datetime.now(),
            total_price_comparisons=1440,  # 24h * 60min
            avg_price_difference_pct=0.0025,
            max_price_difference_pct=0.015,
            price_correlation=0.995,
            avg_timestamp_lag_ms=12.5,
            total_executions=45,
            simulator_fill_rate=0.98,
            live_fill_rate=0.94,
            avg_slippage_difference=0.008,
            avg_latency_difference_ms=8.2,
            price_accuracy_score=0.92,
            execution_accuracy_score=0.88
        )
    
    def create_mock_statistical_results(self) -> list[StatisticalResult]:
        """Cria resultados estatísticos mock."""

        from qualia.ab_testing.statistical_analyzer import StatisticalTest

        return [
            StatisticalResult(
                analysis_id="mock_analysis_1",
                timestamp=datetime.now(),
                confidence_level=0.95,
                significance_threshold=0.05,
                tests_performed=[
                    StatisticalTest(
                        test_name="T-Test Returns",
                        test_statistic=2.15,
                        p_value=0.032,
                        is_significant=True,
                        confidence_level=0.95,
                        effect_size=0.45
                    )
                ],
                is_significant=True,
                overall_p_value=0.032,
                confidence_interval=(0.12, 0.78),
                mean_difference=70.25
            )
        ]
    
    async def test_report_generation(self) -> TestResult:
        """Testa geração básica de relatório."""
        
        test_result = self.create_mock_test_result()
        performance_metrics = self.create_mock_performance_metrics()
        quality_metrics = self.create_mock_quality_metrics()
        statistical_results = self.create_mock_statistical_results()
        
        report = await self.reporting_engine.generate_comprehensive_report(
            test_result, performance_metrics, quality_metrics, statistical_results
        )
        
        # Verificações
        if not report.report_id:
            return TestResult("Report Generation", False, error="Report ID não gerado")
        
        if report.test_id != "test_d07_5":
            return TestResult("Report Generation", False, error="Test ID incorreto")
        
        if not report.executive_summary.content:
            return TestResult("Report Generation", False, error="Executive summary vazio")
        
        details = f"Report ID: {report.report_id}, Score: {report.overall_score:.3f}"
        return TestResult("Report Generation", True, details=details)
    
    async def test_executive_summary(self) -> TestResult:
        """Testa geração do resumo executivo."""

        from qualia.ab_testing.reporting_engine import CompatibilityMetrics

        test_result = self.create_mock_test_result()
        performance_metrics = self.create_mock_performance_metrics()
        quality_metrics = self.create_mock_quality_metrics()

        # Converter para CompatibilityMetrics
        compat_metrics = CompatibilityMetrics.from_performance_metrics(performance_metrics)

        summary = await self.reporting_engine._generate_executive_summary(
            test_result, compat_metrics, quality_metrics
        )
        
        if not summary.content:
            return TestResult("Executive Summary", False, error="Conteúdo vazio")
        
        if not summary.metrics:
            return TestResult("Executive Summary", False, error="Métricas não geradas")
        
        required_metrics = ["simulator_sharpe", "live_sharpe", "correlation", "data_quality"]
        for metric in required_metrics:
            if metric not in summary.metrics:
                return TestResult("Executive Summary", False, error=f"Métrica {metric} ausente")

        details = f"Métricas: {len(summary.metrics)}, Sharpe Sim: {summary.metrics['simulator_sharpe']:.3f}"
        return TestResult("Executive Summary", True, details=details)
    
    async def test_performance_analysis(self) -> TestResult:
        """Testa análise de performance."""

        from qualia.ab_testing.reporting_engine import CompatibilityMetrics

        test_result = self.create_mock_test_result()
        performance_metrics = self.create_mock_performance_metrics()

        # Converter para CompatibilityMetrics
        compat_metrics = CompatibilityMetrics.from_performance_metrics(performance_metrics)

        analysis = await self.reporting_engine._generate_performance_analysis(
            compat_metrics, test_result
        )
        
        if not analysis.content:
            return TestResult("Performance Analysis", False, error="Conteúdo vazio")
        
        if "PnL Simulador" not in analysis.content:
            return TestResult("Performance Analysis", False, error="PnL não encontrado no conteúdo")
        
        if "Max Drawdown" not in analysis.content:
            return TestResult("Performance Analysis", False, error="Drawdown não encontrado")
        
        details = f"PnL Diff: ${compat_metrics.simulator_total_pnl - compat_metrics.live_total_pnl:.2f}"
        return TestResult("Performance Analysis", True, details=details)
    
    async def test_statistical_analysis(self) -> TestResult:
        """Testa análise estatística."""
        
        test_result = self.create_mock_test_result()
        statistical_results = self.create_mock_statistical_results()
        
        analysis = await self.reporting_engine._generate_statistical_analysis(
            statistical_results, test_result
        )
        
        if not analysis.content:
            return TestResult("Statistical Analysis", False, error="Conteúdo vazio")
        
        if "Testes Realizados" not in analysis.content:
            return TestResult("Statistical Analysis", False, error="Contagem de testes não encontrada")
        
        significant_tests = len([r for r in statistical_results if r.is_significant])
        if str(significant_tests) not in analysis.content:
            return TestResult("Statistical Analysis", False, error="Testes significativos incorretos")
        
        details = f"Testes: {len(statistical_results)}, Significativos: {significant_tests}"
        return TestResult("Statistical Analysis", True, details=details)
    
    async def test_quality_analysis(self) -> TestResult:
        """Testa análise de qualidade."""
        
        quality_metrics = self.create_mock_quality_metrics()
        
        analysis = await self.reporting_engine._generate_quality_analysis(quality_metrics)
        
        if not analysis.content:
            return TestResult("Quality Analysis", False, error="Conteúdo vazio")
        
        if "Score Geral de Qualidade" not in analysis.content:
            return TestResult("Quality Analysis", False, error="Score geral não encontrado")
        
        if not analysis.metrics.get("overall_quality"):
            return TestResult("Quality Analysis", False, error="Métrica overall_quality ausente")
        
        overall_quality = analysis.metrics["overall_quality"]
        details = f"Quality Score: {overall_quality:.3f}, Comparisons: {quality_metrics.total_price_comparisons}"
        return TestResult("Quality Analysis", True, details=details)

    async def test_risk_analysis(self) -> TestResult:
        """Testa análise de risco."""

        from qualia.ab_testing.reporting_engine import CompatibilityMetrics

        test_result = self.create_mock_test_result()
        performance_metrics = self.create_mock_performance_metrics()

        # Converter para CompatibilityMetrics
        compat_metrics = CompatibilityMetrics.from_performance_metrics(performance_metrics)

        analysis = await self.reporting_engine._generate_risk_analysis(
            test_result, compat_metrics
        )

        if not analysis.content:
            return TestResult("Risk Analysis", False, error="Conteúdo vazio")

        if "Nível de Risco" not in analysis.content:
            return TestResult("Risk Analysis", False, error="Nível de risco não encontrado")

        if not analysis.metrics.get("risk_score"):
            return TestResult("Risk Analysis", False, error="Risk score ausente")

        risk_score = analysis.metrics["risk_score"]
        details = f"Risk Score: {risk_score:.3f}, Recommendations: {len(analysis.recommendations)}"
        return TestResult("Risk Analysis", True, details=details)

    async def test_recommendations(self) -> TestResult:
        """Testa geração de recomendações."""

        from qualia.ab_testing.reporting_engine import CompatibilityMetrics

        test_result = self.create_mock_test_result()
        performance_metrics = self.create_mock_performance_metrics()
        quality_metrics = self.create_mock_quality_metrics()
        statistical_results = self.create_mock_statistical_results()

        # Converter para CompatibilityMetrics
        compat_metrics = CompatibilityMetrics.from_performance_metrics(performance_metrics)

        recommendations = await self.reporting_engine._generate_recommendations(
            test_result, compat_metrics, quality_metrics, statistical_results
        )

        if not recommendations.content:
            return TestResult("Recommendations", False, error="Conteúdo vazio")

        if not recommendations.recommendations:
            return TestResult("Recommendations", False, error="Lista de recomendações vazia")

        if len(recommendations.recommendations) == 0:
            return TestResult("Recommendations", False, error="Nenhuma recomendação gerada")

        details = f"Recomendações: {len(recommendations.recommendations)}"
        return TestResult("Recommendations", True, details=details)

    async def test_html_export(self) -> TestResult:
        """Testa exportação HTML."""

        test_result = self.create_mock_test_result()
        performance_metrics = self.create_mock_performance_metrics()
        quality_metrics = self.create_mock_quality_metrics()
        statistical_results = self.create_mock_statistical_results()

        report = await self.reporting_engine.generate_comprehensive_report(
            test_result, performance_metrics, quality_metrics, statistical_results
        )

        html_file = await self.reporting_engine.save_report(report, format="html")

        if not html_file.exists():
            return TestResult("HTML Export", False, error="Arquivo HTML não criado")

        # Verificar conteúdo básico
        content = html_file.read_text(encoding='utf-8')
        if "QUALIA A/B Test Report" not in content:
            return TestResult("HTML Export", False, error="Título não encontrado no HTML")

        if report.report_id not in content:
            return TestResult("HTML Export", False, error="Report ID não encontrado no HTML")

        file_size = html_file.stat().st_size
        details = f"File: {html_file.name}, Size: {file_size} bytes"
        return TestResult("HTML Export", True, details=details)

    async def test_json_export(self) -> TestResult:
        """Testa exportação JSON."""

        test_result = self.create_mock_test_result()
        performance_metrics = self.create_mock_performance_metrics()
        quality_metrics = self.create_mock_quality_metrics()
        statistical_results = self.create_mock_statistical_results()

        report = await self.reporting_engine.generate_comprehensive_report(
            test_result, performance_metrics, quality_metrics, statistical_results
        )

        json_file = await self.reporting_engine.save_report(report, format="json")

        if not json_file.exists():
            return TestResult("JSON Export", False, error="Arquivo JSON não criado")

        # Verificar se é JSON válido
        import json
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            return TestResult("JSON Export", False, error=f"JSON inválido: {e}")

        # Verificar estrutura básica
        if "report_id" not in data:
            return TestResult("JSON Export", False, error="report_id ausente no JSON")

        if "overall_score" not in data:
            return TestResult("JSON Export", False, error="overall_score ausente no JSON")

        file_size = json_file.stat().st_size
        details = f"File: {json_file.name}, Size: {file_size} bytes"
        return TestResult("JSON Export", True, details=details)

    async def test_overall_scoring(self) -> TestResult:
        """Testa sistema de pontuação geral."""

        from qualia.ab_testing.reporting_engine import CompatibilityMetrics

        performance_metrics = self.create_mock_performance_metrics()
        quality_metrics = self.create_mock_quality_metrics()
        statistical_results = self.create_mock_statistical_results()

        # Converter para CompatibilityMetrics
        compat_metrics = CompatibilityMetrics.from_performance_metrics(performance_metrics)

        score = self.reporting_engine._calculate_overall_score(
            compat_metrics, quality_metrics, statistical_results
        )

        if not isinstance(score, (int, float)):
            return TestResult("Overall Scoring", False, error="Score não é numérico")

        if score < 0 or score > 1:
            return TestResult("Overall Scoring", False, error=f"Score fora do range [0,1]: {score}")

        # Testar confidence level
        confidence = self.reporting_engine._calculate_confidence_level(statistical_results)

        if not isinstance(confidence, (int, float)):
            return TestResult("Overall Scoring", False, error="Confidence não é numérico")

        if confidence < 0 or confidence > 1:
            return TestResult("Overall Scoring", False, error=f"Confidence fora do range [0,1]: {confidence}")

        details = f"Score: {score:.3f}, Confidence: {confidence:.3f}"
        return TestResult("Overall Scoring", True, details=details)


async def main():
    """Função principal."""

    tester = D07_5_ReportingTester()
    success = await tester.run_all_tests()

    return 0 if success else 1


if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
