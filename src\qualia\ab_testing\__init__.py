"""
QUALIA A/B Testing Framework - D-07 Implementation

Sistema completo de A/B testing para comparar performance do simulador vs trading ao vivo.
Valida a qualidade e precisão do simulador QUALIA através de comparações estatísticas rigorosas.

Componentes principais:
- ABTestFramework: Coordenador principal dos testes A/B
- PerformanceComparator: Comparação de métricas de performance
- DataQualityValidator: Validação de qualidade de dados
- StatisticalAnalyzer: Análise estatística e significância
- TestConfigManager: Gerenciamento de configurações de teste
"""

from .ab_test_framework import ABTestFramework, ABTestConfig, ABTestResult
from .performance_comparator import PerformanceComparator, PerformanceMetrics
from .data_quality_validator import DataQualityValidator, DataQualityReport
from .statistical_analyzer import StatisticalAnalyzer, StatisticalResult
from .test_config_manager import TestConfigManager, TestConfiguration

__all__ = [
    "ABTestFramework",
    "ABTestConfig", 
    "ABTestResult",
    "PerformanceComparator",
    "PerformanceMetrics",
    "DataQualityValidator",
    "DataQualityReport",
    "StatisticalAnalyzer",
    "StatisticalResult",
    "TestConfigManager",
    "TestConfiguration",
]
