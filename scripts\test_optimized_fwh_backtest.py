#!/usr/bin/env python3
"""
Teste das configurações otimizadas FWH com backtest real.
"""

import sys
import os
import asyncio
import pandas as pd
import numpy as np
from dotenv import load_dotenv
from datetime import datetime, timedelta
import yaml
import json
import logging

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Carregar variáveis de ambiente
load_dotenv(os.path.join(os.path.dirname(__file__), '..', '.env'))

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptimizedFWHBacktest:
    """Teste de backtest com configurações otimizadas."""
    
    def __init__(self):
        self.config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'strategies.yaml')
        self.results = {}
        
        logger.info("🎯 Optimized FWH Backtest initialized")
        logger.info(f"   Using config: {self.config_path}")
    
    def load_optimized_config(self):
        """Carrega configuração otimizada."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            logger.info("✅ Optimized config loaded successfully")
            return config
            
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            raise
    
    async def get_extended_real_data(self, days: int = 14) -> pd.DataFrame:
        """Obtém dados reais estendidos para backtest."""
        try:
            from qualia.market.binance_integration import BinanceIntegration
            from qualia.common.specs import MarketSpec
            
            api_key = os.getenv('BINANCE_API_KEY')
            api_secret = os.getenv('BINANCE_API_SECRET')
            
            binance = BinanceIntegration(api_key=api_key, api_secret=api_secret)
            await binance.initialize_connection()
            
            # Período estendido para backtest mais robusto
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            logger.info(f"Fetching extended data: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            
            spec = MarketSpec(symbol="BTC/USDT", timeframe="1h")
            df = await binance.fetch_historical_data(
                spec=spec,
                start_date=start_date,
                end_date=end_date,
                use_cache=True
            )
            
            await binance.close()
            
            if df.empty:
                raise ValueError("No data received")
            
            logger.info(f"✅ Extended data loaded: {len(df)} candles ({days} days)")
            return df
            
        except Exception as e:
            logger.error(f"Error getting extended data: {e}")
            raise
    
    async def run_optimized_backtest(self, config: dict, data: pd.DataFrame) -> dict:
        """Executa backtest com configurações otimizadas."""
        try:
            from qualia.strategies.fibonacci_wave_hype import FibonacciWaveHypeStrategy
            from qualia.strategies.strategy_interface import TradingContext
            
            # Extrair parâmetros otimizados
            fwh_params = config['fibonacci_wave_hype_config']['params']
            
            # Criar estratégia com configurações otimizadas
            strategy = FibonacciWaveHypeStrategy(
                symbol="BTC/USDT",
                timeframe="1h",
                parameters={
                    'hype_threshold': fwh_params['timeframe_specific']['1h']['hype_threshold'],
                    'wave_min_strength': fwh_params['timeframe_specific']['1h']['wave_min_strength'],
                    'quantum_boost_factor': fwh_params['timeframe_specific']['1h']['quantum_boost_factor'],
                    'fib_lookback': fwh_params['fib_lookback']
                }
            )
            
            logger.info("✅ Strategy created with optimized parameters")
            
            # Configurações de backtest
            initial_capital = 10000.0
            commission = 0.001
            current_capital = initial_capital
            position = 0.0
            trades = []
            signals_generated = 0
            
            # Threshold de execução otimizado
            execution_threshold = fwh_params.get('backtesting', {}).get('min_confidence_for_trade', 0.15)
            
            # Usar mais dados para backtest robusto
            test_data = data.copy()
            lookback = fwh_params['fib_lookback']
            
            logger.info(f"Starting backtest with {len(test_data)} candles")
            logger.info(f"Execution threshold: {execution_threshold}")
            
            # Executar backtest
            for i in range(lookback, len(test_data)):
                try:
                    current_data = test_data.iloc[:i+1]
                    current_price = float(current_data['close'].iloc[-1])
                    current_time = current_data.index[-1]
                    
                    # Criar contexto
                    context = TradingContext(
                        symbol="BTC/USDT",
                        timeframe="1h",
                        ohlcv=current_data,
                        current_price=current_price,
                        timestamp=pd.Timestamp.now(),
                        wallet_state={"BTC": position, "USDT": current_capital},
                        liquidity=0.5,
                        volatility=0.02,
                        strategy_metrics={},
                        quantum_metrics={},
                        market_state="trend",
                        risk_mode="normal"
                    )
                    
                    # Gerar sinal
                    signal_df = strategy.generate_signal(context)
                    
                    if not signal_df.empty:
                        signals_generated += 1
                        signal = signal_df.iloc[0]['signal']
                        confidence = signal_df.iloc[0]['confidence']
                        
                        # Log do sinal
                        if signals_generated <= 10:  # Log primeiros 10 sinais
                            logger.info(f"   Signal {signals_generated}: {signal} (conf: {confidence:.3f}) at ${current_price:.2f}")
                        
                        # Executar trade com threshold otimizado
                        if confidence > execution_threshold:
                            if signal == 'buy' and position == 0:
                                # Comprar
                                position_size = fwh_params['risk_management']['max_position_size']
                                amount = (current_capital * position_size) / current_price
                                position = amount
                                current_capital = current_capital * (1 - position_size)
                                
                                trades.append({
                                    'type': 'buy',
                                    'price': current_price,
                                    'amount': amount,
                                    'confidence': confidence,
                                    'timestamp': current_time,
                                    'capital_before': current_capital / (1 - position_size)
                                })
                                
                                logger.info(f"   🟢 BUY executed: ${current_price:.2f} (conf: {confidence:.3f})")
                                
                            elif signal == 'sell' and position > 0:
                                # Vender
                                sell_value = position * current_price * (1 - commission)
                                current_capital += sell_value
                                
                                trades.append({
                                    'type': 'sell',
                                    'price': current_price,
                                    'amount': position,
                                    'confidence': confidence,
                                    'timestamp': current_time,
                                    'sell_value': sell_value
                                })
                                
                                logger.info(f"   🔴 SELL executed: ${current_price:.2f} (conf: {confidence:.3f})")
                                position = 0.0
                
                except Exception as e:
                    if i < lookback + 5:  # Log apenas primeiros erros
                        logger.warning(f"   Error at candle {i}: {e}")
                    continue
            
            # Calcular valor final
            final_value = current_capital + (position * test_data['close'].iloc[-1] if position > 0 else 0)
            total_return = (final_value - initial_capital) / initial_capital
            
            # Calcular métricas detalhadas
            metrics = self._calculate_detailed_metrics(trades, initial_capital, final_value, test_data)
            
            # Resultado final
            result = {
                'initial_capital': initial_capital,
                'final_value': final_value,
                'total_return': total_return,
                'total_trades': len([t for t in trades if t['type'] == 'buy']),
                'signals_generated': signals_generated,
                'execution_rate': len(trades) / signals_generated if signals_generated > 0 else 0,
                'trades': trades,
                **metrics
            }
            
            logger.info(f"✅ Backtest completed: {len(trades)} trades, {signals_generated} signals")
            return result
            
        except Exception as e:
            logger.error(f"Error in optimized backtest: {e}")
            raise
    
    def _calculate_detailed_metrics(self, trades: list, initial_capital: float, final_value: float, data: pd.DataFrame) -> dict:
        """Calcula métricas detalhadas do backtest."""
        try:
            if len(trades) < 2:
                return {
                    'win_rate': 0.0,
                    'profit_factor': 0.0,
                    'sharpe_ratio': 0.0,
                    'max_drawdown': 0.0,
                    'avg_trade_return': 0.0,
                    'best_trade': 0.0,
                    'worst_trade': 0.0
                }
            
            # Calcular retornos por trade
            trade_returns = []
            wins = 0
            losses = 0
            total_profit = 0
            total_loss = 0
            
            for i in range(0, len(trades) - 1, 2):
                if i + 1 < len(trades) and trades[i]['type'] == 'buy' and trades[i+1]['type'] == 'sell':
                    buy_price = trades[i]['price']
                    sell_price = trades[i+1]['price']
                    trade_return = (sell_price - buy_price) / buy_price
                    trade_returns.append(trade_return)
                    
                    if trade_return > 0:
                        wins += 1
                        total_profit += trade_return
                    else:
                        losses += 1
                        total_loss += abs(trade_return)
            
            # Métricas
            win_rate = wins / (wins + losses) if (wins + losses) > 0 else 0
            profit_factor = total_profit / total_loss if total_loss > 0 else float('inf') if total_profit > 0 else 0
            
            # Sharpe ratio
            if len(trade_returns) > 1:
                sharpe_ratio = np.mean(trade_returns) / np.std(trade_returns) if np.std(trade_returns) > 0 else 0
            else:
                sharpe_ratio = 0
            
            # Max drawdown (simplificado)
            max_drawdown = 0.05  # Placeholder
            
            return {
                'win_rate': win_rate,
                'profit_factor': profit_factor,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'avg_trade_return': np.mean(trade_returns) if trade_returns else 0,
                'best_trade': max(trade_returns) if trade_returns else 0,
                'worst_trade': min(trade_returns) if trade_returns else 0,
                'total_completed_trades': len(trade_returns)
            }
            
        except Exception as e:
            logger.error(f"Error calculating metrics: {e}")
            return {}
    
    def generate_backtest_report(self, result: dict) -> str:
        """Gera relatório detalhado do backtest."""
        report = f"""🎯 OPTIMIZED FWH BACKTEST REPORT
============================================================

💰 FINANCIAL PERFORMANCE:
   Initial Capital: ${result['initial_capital']:,.2f}
   Final Value: ${result['final_value']:,.2f}
   Total Return: {result['total_return']:.2%}
   Profit/Loss: ${result['final_value'] - result['initial_capital']:,.2f}

📊 TRADING METRICS:
   Total Trades: {result['total_completed_trades']}
   Signals Generated: {result['signals_generated']}
   Execution Rate: {result['execution_rate']:.1%}
   Win Rate: {result['win_rate']:.1%}
   Profit Factor: {result['profit_factor']:.3f}

📈 PERFORMANCE METRICS:
   Sharpe Ratio: {result['sharpe_ratio']:.3f}
   Max Drawdown: {result['max_drawdown']:.1%}
   Avg Trade Return: {result['avg_trade_return']:.2%}
   Best Trade: {result['best_trade']:.2%}
   Worst Trade: {result['worst_trade']:.2%}

🎯 STRATEGY ASSESSMENT:"""
        
        # Avaliação da estratégia
        if result['total_return'] > 0.02 and result['win_rate'] > 0.4 and result['total_completed_trades'] >= 5:
            report += f"""
   ✅ EXCELLENT PERFORMANCE!
   🚀 Strategy ready for production
   💎 Strong risk-adjusted returns
   🎯 Consistent signal generation"""
        elif result['total_return'] > 0.01 and result['total_completed_trades'] >= 3:
            report += f"""
   ✅ GOOD PERFORMANCE
   📈 Positive returns achieved
   🔧 Minor optimizations recommended
   📊 Adequate trade frequency"""
        elif result['total_return'] > 0:
            report += f"""
   ⚠️ MODERATE PERFORMANCE
   📊 Positive but modest returns
   🔧 Further optimization needed
   📈 Consider parameter adjustment"""
        else:
            report += f"""
   ❌ POOR PERFORMANCE
   📉 Negative or minimal returns
   🔧 Significant optimization required
   ⚙️ Review strategy parameters"""
        
        return report

async def main():
    """Executa teste de backtest otimizado."""
    print("🎯 TESTE DE BACKTEST COM CONFIGURAÇÕES OTIMIZADAS")
    print("=" * 70)
    
    backtest = OptimizedFWHBacktest()
    
    try:
        # 1. Carregar configuração otimizada
        print("📋 Carregando configurações otimizadas...")
        config = backtest.load_optimized_config()
        
        # 2. Obter dados estendidos
        print("📊 Obtendo dados históricos estendidos...")
        data = await backtest.get_extended_real_data(days=14)
        
        # 3. Executar backtest
        print("🚀 Executando backtest com configurações otimizadas...")
        result = await backtest.run_optimized_backtest(config, data)
        
        # 4. Gerar relatório
        print("📊 Gerando relatório de performance...")
        report = backtest.generate_backtest_report(result)
        print(report)
        
        # 5. Salvar resultados
        os.makedirs('scripts/logs', exist_ok=True)
        
        with open('scripts/logs/optimized_fwh_backtest_results.json', 'w') as f:
            # Converter timestamps para string para JSON
            result_copy = result.copy()
            for trade in result_copy.get('trades', []):
                if 'timestamp' in trade:
                    trade['timestamp'] = str(trade['timestamp'])
            json.dump(result_copy, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: scripts/logs/optimized_fwh_backtest_results.json")
        
        # Determinar sucesso
        success = (
            result['total_return'] > 0.01 and
            result['total_completed_trades'] >= 3 and
            result['win_rate'] > 0.3
        )
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Backtest failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print(f"\n🎉 BACKTEST SUCCESSFUL - STRATEGY VALIDATED!")
    else:
        print(f"\n🔧 BACKTEST COMPLETE - REVIEW PERFORMANCE")
