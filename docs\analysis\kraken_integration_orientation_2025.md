# Carta de Orientação Técnica - Kraken Integration (Roadmap 2025-H2)

Esta orientação sintetiza os principais aspectos a serem observados no módulo `src/qualia/market/kraken_integration.py` para alinhamento com o roadmap QUALIA 2025-H2.

## 1. Papel do Módulo
- **Data-fetch**: provê acesso unificado a dados de mercado via REST/WebSocket.
- **Feature Engineering**: alimenta indicadores e cálculos quânticos com OHLCV e tickers.
- **Execução**: expõe métodos de criação e gerenciamento de ordens.

## 2. Dependências e Pontos de Acoplamento
- Bibliotecas externas: `ccxt`, `websockets`, `pandas`, `numpy`, `datadog`.
- Internas: utilidades `get_logger`, `CircuitBreaker`, `normalize_symbol`, configurações em `src.qualia.config`.
- Acoplamentos principais: lógica de cache de OHLCV em `CACHE_DIR` e parametrização de timeouts via variáveis de ambiente.

## 3. Débitos Técnicos Identificados
1. **Funções extensas**: métodos como `_paginate_ohlcv` misturam múltiplas responsabilidades.
2. **Tratamento genérico de exceções**: diversos `except Exception` dificultam testes e logging específico.
3. **Configuração dispersa**: parâmetros lidos de variáveis de ambiente sem centralização formal.
4. **Observabilidade limitada**: falta de métricas de latência em vários pontos, uso reduzido de `statsd`.
5. **Cache em arquivo acoplado**: lógica de leitura/gravação de CSV poderia ser reutilizada por outras integrações.

## 4. Recomendações para 2025-H2
1. **Modularização**: extrair utilitários de cache e de retry/backoff para módulos dedicados, facilitando testes unitários.
2. **Config unificada**: mover parâmetros padrão para arquivos em `config/` e ler valores via `config_manager`.
3. **Observabilidade**: instrumentar tempo de chamadas REST/WebSocket e contagem de erros com `DogStatsd` e `trace_id` propagado.
4. **Tipagem estrita**: revisar tipos retornados e remover usos desnecessários de `Any`.
5. **Async otimizado**: avaliar uso de `asyncio.TaskGroup` ou pools para fan-out de múltiplos pares, respeitando `MAX_DEPTH` do roadmap.

Estas medidas direcionam a evolução do módulo para maior confiabilidade e manutenibilidade, contribuindo com os objetivos de performance e qualidade estabelecidos para o QUALIA em 2025-H2.

