#!/usr/bin/env python3
"""
Teste do Production Optimizer - Etapa D.1
YAA IMPLEMENTATION: Valida melhorias de produção implementadas.
"""

import sys
import asyncio
import json
import time
from datetime import datetime
from pathlib import Path

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

from src.production_optimizer import ProductionOptimizer
from src.bayesian_optimizer import OptimizationConfig

def test_production_config():
    """Testa configuração de produção."""
    
    print("🧪 Testando Configuração de Produção...")
    
    try:
        config_file = "config/production_config.json"
        
        # Verifica se arquivo existe
        if not Path(config_file).exists():
            print(f"   ❌ Arquivo de configuração não encontrado: {config_file}")
            return False
        
        # Carrega configuração
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        # Validações
        required_keys = ['study_name', 'n_trials_per_cycle', 'symbols', 'worker_settings']
        for key in required_keys:
            if key not in config:
                print(f"   ❌ Chave obrigatória não encontrada: {key}")
                return False
        
        # Verifica melhorias implementadas
        print(f"   ✅ Trials por ciclo: {config['n_trials_per_cycle']} (melhorado de 8)")
        print(f"   ✅ Símbolos: {len(config['symbols'])} (diversificação)")
        print(f"   ✅ Worker settings: {len(config['worker_settings'])} configurações")
        
        # Verifica símbolos
        expected_symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT']
        for symbol in expected_symbols:
            if symbol not in config['symbols']:
                print(f"   ⚠️ Símbolo esperado não encontrado: {symbol}")
        
        # Verifica worker settings
        worker_settings = config['worker_settings']
        expected_settings = ['cycle_interval_minutes', 'max_runtime_hours', 'memory_limit_mb']
        for setting in expected_settings:
            if setting not in worker_settings:
                print(f"   ⚠️ Setting não encontrado: {setting}")
            else:
                print(f"   ✅ {setting}: {worker_settings[setting]}")
        
        print("   ✅ Configuração de produção válida")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro ao testar configuração: {e}")
        return False

def test_production_optimizer_init():
    """Testa inicialização do otimizador de produção."""
    
    print("🧪 Testando Inicialização do Production Optimizer...")
    
    try:
        optimizer = ProductionOptimizer()
        
        # Verifica atributos
        assert hasattr(optimizer, 'config'), "Optimizer não tem config"
        assert hasattr(optimizer, 'stats'), "Optimizer não tem stats"
        assert hasattr(optimizer, 'is_running'), "Optimizer não tem is_running"
        
        # Verifica configuração carregada
        assert optimizer.config is not None, "Config não foi carregada"
        assert len(optimizer.config.symbols) > 1, "Não há múltiplos símbolos"
        assert optimizer.config.n_trials_per_cycle >= 20, "Trials por ciclo não aumentados"
        
        print(f"   ✅ Símbolos configurados: {optimizer.config.symbols}")
        print(f"   ✅ Trials por ciclo: {optimizer.config.n_trials_per_cycle}")
        print(f"   ✅ Worker settings: {len(optimizer.config.worker_settings)} configurações")
        
        # Verifica estatísticas iniciais
        stats = optimizer.stats
        expected_stats = ['total_trials', 'best_pnl_ever', 'cycles_completed']
        for stat in expected_stats:
            if stat not in stats:
                print(f"   ❌ Estatística não encontrada: {stat}")
                return False
        
        print("   ✅ Production Optimizer inicializado corretamente")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro ao inicializar optimizer: {e}")
        return False

def test_resource_monitoring():
    """Testa monitoramento de recursos."""
    
    print("🧪 Testando Monitoramento de Recursos...")
    
    try:
        optimizer = ProductionOptimizer()
        
        # Testa verificação de recursos
        resources = optimizer.check_system_resources()
        
        expected_keys = ['memory_usage_mb', 'cpu_percent']
        for key in expected_keys:
            if key not in resources:
                print(f"   ❌ Métrica de recurso não encontrada: {key}")
                return False
        
        print(f"   ✅ Memória: {resources['memory_usage_mb']:.1f}MB")
        print(f"   ✅ CPU: {resources['cpu_percent']:.1f}%")
        
        # Testa verificação de limites
        within_limits = optimizer.check_resource_limits()
        print(f"   ✅ Recursos dentro dos limites: {within_limits}")
        
        # Testa status
        status = optimizer.get_status()
        assert 'resources' in status, "Status não contém recursos"
        assert 'stats' in status, "Status não contém stats"
        
        print("   ✅ Monitoramento de recursos funcionando")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro no monitoramento: {e}")
        return False

def test_multi_symbol_support():
    """Testa suporte a múltiplos símbolos."""
    
    print("🧪 Testando Suporte Multi-Símbolo...")
    
    try:
        optimizer = ProductionOptimizer()
        
        # Verifica múltiplos símbolos
        symbols = optimizer.config.symbols
        if len(symbols) < 4:
            print(f"   ⚠️ Poucos símbolos configurados: {len(symbols)}")
        
        print(f"   ✅ Símbolos configurados: {symbols}")
        
        # Verifica pesos dos símbolos
        if hasattr(optimizer.config, 'symbol_weights'):
            weights = optimizer.config.symbol_weights
            print(f"   ✅ Pesos configurados: {len(weights)} símbolos")
            
            for symbol, weight in weights.items():
                print(f"      • {symbol}: {weight}")
        else:
            print("   ⚠️ Pesos dos símbolos não configurados")
        
        # Verifica se BayesianOptimizer suporta múltiplos símbolos
        from src.bayesian_optimizer import BayesianOptimizer
        
        test_config = OptimizationConfig(
            study_name="test_multi_symbol",
            n_trials_per_cycle=2,
            optimization_cycles=1,
            symbols=symbols[:3]  # Usa primeiros 3 símbolos
        )
        
        bayesian_opt = BayesianOptimizer(test_config)
        
        # Verifica se aceita múltiplos símbolos
        assert len(bayesian_opt.config.symbols) > 1, "BayesianOptimizer não suporta múltiplos símbolos"
        
        print("   ✅ Suporte multi-símbolo implementado")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro no teste multi-símbolo: {e}")
        return False

async def test_production_cycle():
    """Testa ciclo de produção (simulado)."""
    
    print("🧪 Testando Ciclo de Produção...")
    
    try:
        optimizer = ProductionOptimizer()
        
        # Inicializa otimizador Bayesiano
        from src.bayesian_optimizer import BayesianOptimizer
        optimizer.optimizer = BayesianOptimizer(optimizer.config)
        
        print("   🔄 Simulando ciclo de produção...")
        
        # Simula ciclo (sem executar realmente para economizar tempo)
        cycle_start = time.time()
        
        # Verifica recursos
        resources_ok = optimizer.check_resource_limits()
        print(f"   ✅ Recursos verificados: {resources_ok}")
        
        # Simula resultado de ciclo
        cycle_result = {
            'success': True,
            'cycle_time': time.time() - cycle_start,
            'best_pnl': 0.15,
            'trials': optimizer.config.n_trials_per_cycle
        }
        
        # Atualiza estatísticas
        optimizer.stats['cycles_completed'] += 1
        optimizer.stats['total_trials'] += cycle_result['trials']
        
        print(f"   ✅ Ciclo simulado: {cycle_result['trials']} trials em {cycle_result['cycle_time']:.2f}s")
        
        # Testa logging de métricas
        optimizer.log_metrics()
        print("   ✅ Métricas registradas")
        
        # Testa status
        status = optimizer.get_status()
        print(f"   ✅ Status obtido: {status['stats']['cycles_completed']} ciclos")
        
        print("   ✅ Ciclo de produção testado com sucesso")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro no teste de ciclo: {e}")
        return False

def test_configuration_improvements():
    """Testa melhorias específicas implementadas."""
    
    print("🧪 Testando Melhorias Implementadas...")
    
    try:
        config_file = "config/production_config.json"
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        improvements = []
        
        # 1. Ajuste n_trials_per_cycle para mais precisão
        trials_per_cycle = config['n_trials_per_cycle']
        if trials_per_cycle >= 20:
            improvements.append(f"✅ Trials aumentados: {trials_per_cycle} (era 8)")
        else:
            improvements.append(f"❌ Trials não aumentados: {trials_per_cycle}")
        
        # 2. Múltiplos símbolos para diversificação
        symbols_count = len(config['symbols'])
        if symbols_count >= 4:
            improvements.append(f"✅ Diversificação: {symbols_count} símbolos")
        else:
            improvements.append(f"❌ Pouca diversificação: {symbols_count} símbolos")
        
        # 3. Worker settings para produção
        worker_settings = config.get('worker_settings', {})
        production_settings = [
            'cycle_interval_minutes',
            'max_runtime_hours', 
            'memory_limit_mb',
            'cpu_limit_percent',
            'restart_on_failure'
        ]
        
        configured_settings = sum(1 for setting in production_settings if setting in worker_settings)
        if configured_settings >= 4:
            improvements.append(f"✅ Worker settings: {configured_settings}/{len(production_settings)} configurados")
        else:
            improvements.append(f"❌ Worker settings incompletos: {configured_settings}/{len(production_settings)}")
        
        # 4. Configurações avançadas
        advanced_features = ['risk_management', 'monitoring', 'symbol_specific_settings']
        advanced_count = sum(1 for feature in advanced_features if feature in config)
        if advanced_count >= 2:
            improvements.append(f"✅ Recursos avançados: {advanced_count} implementados")
        else:
            improvements.append(f"⚠️ Recursos avançados limitados: {advanced_count}")
        
        # Exibe melhorias
        print("   📊 Melhorias Implementadas:")
        for improvement in improvements:
            print(f"      {improvement}")
        
        # Verifica se todas as melhorias foram implementadas
        success_count = sum(1 for imp in improvements if imp.startswith("✅"))
        total_count = len(improvements)
        
        if success_count >= 3:
            print(f"   🎉 Melhorias implementadas com sucesso: {success_count}/{total_count}")
            return True
        else:
            print(f"   ⚠️ Algumas melhorias pendentes: {success_count}/{total_count}")
            return False
        
    except Exception as e:
        print(f"   ❌ Erro ao testar melhorias: {e}")
        return False

async def main():
    """Executa todos os testes de produção."""
    
    print("🧪 TESTE COMPLETO - PRODUCTION OPTIMIZER")
    print("=" * 60)
    
    tests = [
        ("Configuração de Produção", test_production_config),
        ("Inicialização do Optimizer", test_production_optimizer_init),
        ("Monitoramento de Recursos", test_resource_monitoring),
        ("Suporte Multi-Símbolo", test_multi_symbol_support),
        ("Ciclo de Produção", test_production_cycle),
        ("Melhorias Implementadas", test_configuration_improvements)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        start_time = time.time()
        
        if asyncio.iscoroutinefunction(test_func):
            success = await test_func()
        else:
            success = test_func()
        
        test_time = time.time() - start_time
        results.append((test_name, success, test_time))
        
        if success:
            print(f"✅ {test_name}: PASSOU ({test_time:.2f}s)")
        else:
            print(f"❌ {test_name}: FALHOU ({test_time:.2f}s)")
    
    # Resumo final
    print("\n" + "=" * 60)
    print("📊 RESUMO DOS TESTES DE PRODUÇÃO")
    print("=" * 60)
    
    passed = sum(1 for _, success, _ in results if success)
    total = len(results)
    total_time = sum(test_time for _, _, test_time in results)
    
    for test_name, success, test_time in results:
        status = "✅ PASSOU" if success else "❌ FALHOU"
        print(f"   {test_name:<30} {status} ({test_time:.2f}s)")
    
    print(f"\n🎯 RESULTADO FINAL: {passed}/{total} testes passaram")
    print(f"⏱️ Tempo total: {total_time:.2f}s")
    
    if passed == total:
        print("🎉 TODAS AS MELHORIAS DE PRODUÇÃO IMPLEMENTADAS!")
        print("✅ Sistema pronto para ambiente de produção!")
    else:
        print("⚠️ Algumas melhorias precisam de ajustes")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
