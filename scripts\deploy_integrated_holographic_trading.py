#!/usr/bin/env python3
"""
Deploy Integrated Holographic Trading System

Sistema completo que integra:
1. Enhanced Data Collector (OHLCV + Quantum Encoders)
2. Holographic Universe
3. HolographicTradingAdapter 
4. QUALIARealTimeTrader (execução real)

Este script demonstra como usar toda a infraestrutura QUALIA
para trading holográfico com execução automática de posições.
"""

import os
import sys
import asyncio
import time
from typing import Dict, Any, List
from dotenv import load_dotenv

# Adiciona o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from qualia.consciousness.enhanced_data_collector import EnhancedDataCollector
from qualia.consciousness.holographic_universe import HolographicMarketUniverse
from qualia.consciousness.holographic_trading_adapter import (
    HolographicTradingAdapter, 
    HolographicSignal,
    create_holographic_trading_adapter
)
from qualia.qualia_trading_system import QUALIARealTimeTrader
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

class IntegratedHolographicTradingSystem:
    """
    Sistema integrado de trading holográfico com execução real.
    
    Combina:
    - Enhanced Data Collection
    - Holographic Universe Simulation  
    - Trading Signal Generation
    - Real Trade Execution via QUALIARealTimeTrader
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.running = False
        
        # Componentes principais
        self.data_collector = None
        self.holographic_universe = None
        self.trading_adapter = None
        self.trader = None
        
        # Métricas
        self.cycles_completed = 0
        self.signals_generated = 0
        self.trades_executed = 0
        
    async def initialize(self):
        """Inicializa todos os componentes do sistema."""
        
        logger.info("🚀 Inicializando Sistema Integrado de Trading Holográfico")
        
        try:
            # 1. Enhanced Data Collector
            logger.info("📊 Inicializando Enhanced Data Collector...")
            self.data_collector = EnhancedDataCollector()
            await self.data_collector.__aenter__()
            
            # 2. Holographic Universe
            logger.info("🌌 Inicializando Holographic Universe...")
            universe_config = self.config.get('holographic_universe', {})
            self.holographic_universe = HolographicMarketUniverse(
                field_size=universe_config.get('field_size', (200, 200)),
                diffusion_rate=universe_config.get('diffusion_rate', 0.35),
                feedback_strength=universe_config.get('feedback_strength', 0.08)
            )
            
            # 3. QUALIARealTimeTrader
            logger.info("🤖 Inicializando QUALIARealTimeTrader...")
            trader_config = self.config.get('trader', {})
            
            self.trader = QUALIARealTimeTrader(
                symbols=trader_config.get('symbols', ['BTC/USDT', 'ETH/USDT']),
                timeframes=trader_config.get('timeframes', ['5m', '15m', '1h']),
                capital=trader_config.get('capital', 10000.0),
                risk_profile=trader_config.get('risk_profile', 'moderate'),
                mode=trader_config.get('mode', 'paper_trading'),
                data_source=trader_config.get('data_source', 'kucoin'),
                # Credenciais KuCoin
                kucoin_api_key=os.getenv('KUCOIN_API_KEY'),
                kucoin_secret_key=os.getenv('KUCOIN_SECRET_KEY'),
                kucoin_passphrase=os.getenv('KUCOIN_PASSPHRASE'),
                # Configurações de risco
                risk_per_trade_pct=trader_config.get('risk_per_trade_pct', 2.0),
                trading_fee_pct=trader_config.get('trading_fee_pct', 0.001),
                # Timeouts (s): market data 90s, ticker 20s, OHLCV 60s
                market_data_timeout=90.0,
                ticker_fetch_timeout=20.0,
                ohlcv_fetch_timeout=60.0
            )
            
            await self.trader.initialize()
            
            # 4. Holographic Trading Adapter
            logger.info("🌀 Inicializando Holographic Trading Adapter...")
            adapter_config = self.config.get('adapter', {})
            
            self.trading_adapter = create_holographic_trading_adapter(
                trader=self.trader,
                config={
                    'min_confidence': adapter_config.get('min_confidence', 0.5),
                    'max_concurrent_positions': adapter_config.get('max_concurrent_positions', 2),
                    'enable_holographic_risk_override': adapter_config.get('enable_risk_override', True)
                }
            )
            
            logger.info("✅ Sistema integrado inicializado com sucesso!")
            
        except Exception as e:
            logger.error(f"❌ Erro na inicialização: {e}", exc_info=True)
            raise
    
    async def run_integrated_cycle(self) -> Dict[str, Any]:
        """Executa um ciclo completo integrado."""
        
        cycle_start = time.time()
        cycle_results = {
            "cycle_number": self.cycles_completed + 1,
            "timestamp": cycle_start,
            "data_collected": False,
            "holographic_events": 0,
            "signals_generated": 0,
            "trades_executed": 0,
            "trades_rejected": 0,
            "execution_time": 0.0,
            "errors": []
        }
        
        try:
            # 1. Coleta dados enhanced
            logger.info(f"📊 Ciclo {cycle_results['cycle_number']}: Coletando dados...")
            
            market_data = await self.data_collector.collect_market_data()
            news_events = await self.data_collector.collect_news_events()
            sentiment_data = await self.data_collector.collect_fear_greed_index()
            
            cycle_results["data_collected"] = True
            
            # 2. Processa dados no universo holográfico
            logger.info("🌌 Processando dados no universo holográfico...")
            
            # Injeta eventos de mercado
            for symbol, data in market_data.items():
                if data and 'close' in data:
                    # Calcula amplitude baseada na volatilidade
                    volatility = data.get('volatility', 1.0)
                    price_change = data.get('price_change_pct', 0.0)
                    amplitude = min(abs(price_change) * 3.0 + volatility * 0.5, 8.0)
                    
                    self.holographic_universe.inject_market_event(
                        symbol=symbol,
                        event_type='price_movement',
                        amplitude=amplitude,
                        metadata={
                            'price': data['close'],
                            'volume': data.get('volume', 0),
                            'volatility': volatility,
                            'rsi': data.get('rsi', 50.0),
                            'volume_ratio': data.get('volume_ratio', 1.0)
                        }
                    )
                    cycle_results["holographic_events"] += 1
            
            # Injeta eventos de notícias
            for event in news_events:
                amplitude = min(abs(event.get('sentiment_score', 0.0)) * 4.0 + 1.0, 6.0)
                if amplitude > 0.5:
                    self.holographic_universe.inject_market_event(
                        symbol='NEWS',
                        event_type='news',
                        amplitude=amplitude,
                        metadata=event
                    )
                    cycle_results["holographic_events"] += 1
            
            # 3. Evolui universo holográfico
            self.holographic_universe.evolve()
            
            # 4. Detecta padrões e gera sinais
            logger.info("🔍 Detectando padrões e gerando sinais...")
            
            detected_patterns = self.holographic_universe.detect_patterns()
            
            # Converte padrões em sinais holográficos
            holographic_signals = []
            
            for pattern in detected_patterns:
                if pattern.get('strength', 0) > 0.3 and pattern.get('confidence', 0) > 0.4:
                    
                    # Determina ação baseada no padrão
                    field_energy = pattern.get('field_energy', 0.0)
                    entropy = pattern.get('entropy', 1.0)
                    
                    if field_energy > 0.6 and entropy < 0.7:
                        action = 'BUY'
                    elif field_energy < -0.6 and entropy < 0.7:
                        action = 'SELL'
                    else:
                        continue  # HOLD - não gera sinal
                    
                    # Obtém dados de mercado para o símbolo
                    symbol = pattern.get('symbol', 'BTC/USDT')
                    symbol_data = market_data.get(symbol, {})
                    
                    if symbol_data:
                        signal = HolographicSignal(
                            symbol=symbol,
                            action=action,
                            confidence=pattern['confidence'],
                            timeframe='5m',  # Timeframe principal
                            timestamp=time.time(),
                            # Dados técnicos
                            rsi=symbol_data.get('rsi'),
                            volume_ratio=symbol_data.get('volume_ratio'),
                            volatility=symbol_data.get('volatility'),
                            price_change_pct=symbol_data.get('price_change_pct'),
                            # Estados quânticos
                            quantum_rsi_encoded=symbol_data.get('quantum_rsi_encoded', False),
                            quantum_volume_encoded=symbol_data.get('quantum_volume_encoded', False),
                            # Contexto holográfico
                            pattern_strength=pattern['strength'],
                            field_energy=field_energy,
                            entropy=entropy
                        )
                        
                        holographic_signals.append(signal)
                        cycle_results["signals_generated"] += 1
            
            # 5. Executa trades através do adapter
            logger.info(f"⚡ Executando {len(holographic_signals)} sinais holográficos...")
            
            for signal in holographic_signals:
                try:
                    trade_result = await self.trading_adapter.process_holographic_signal(signal)
                    
                    if trade_result.trade_executed:
                        cycle_results["trades_executed"] += 1
                        self.trades_executed += 1
                        
                        logger.info(
                            f"✅ Trade executado: {signal.symbol} {signal.action} "
                            f"@ {trade_result.execution_price:.4f} "
                            f"confidence={signal.confidence:.2f}"
                        )
                    else:
                        cycle_results["trades_rejected"] += 1
                        logger.info(
                            f"❌ Trade rejeitado: {signal.symbol} {signal.action} "
                            f"- {trade_result.rejection_reason}"
                        )
                        
                except Exception as e:
                    error_msg = f"Erro executando sinal {signal.symbol}: {e}"
                    cycle_results["errors"].append(error_msg)
                    logger.error(error_msg)
            
            self.signals_generated += cycle_results["signals_generated"]
            
        except Exception as e:
            error_msg = f"Erro no ciclo integrado: {e}"
            cycle_results["errors"].append(error_msg)
            logger.error(error_msg, exc_info=True)
        
        finally:
            cycle_results["execution_time"] = time.time() - cycle_start
            self.cycles_completed += 1
        
        return cycle_results
    
    async def run_continuous(self, max_cycles: int = 100, cycle_interval: float = 30.0):
        """Executa o sistema continuamente."""
        
        logger.info(f"🔄 Iniciando execução contínua: {max_cycles} ciclos, intervalo {cycle_interval}s")
        
        self.running = True
        
        try:
            for cycle in range(max_cycles):
                if not self.running:
                    break
                
                logger.info(f"\n{'='*60}")
                logger.info(f"🔄 CICLO {cycle + 1}/{max_cycles}")
                logger.info(f"{'='*60}")
                
                # Executa ciclo
                cycle_result = await self.run_integrated_cycle()
                
                # Log do resultado
                logger.info(f"📊 Resultado do Ciclo {cycle + 1}:")
                logger.info(f"   - Eventos holográficos: {cycle_result['holographic_events']}")
                logger.info(f"   - Sinais gerados: {cycle_result['signals_generated']}")
                logger.info(f"   - Trades executados: {cycle_result['trades_executed']}")
                logger.info(f"   - Trades rejeitados: {cycle_result['trades_rejected']}")
                logger.info(f"   - Tempo execução: {cycle_result['execution_time']:.2f}s")
                
                if cycle_result['errors']:
                    logger.warning(f"   - Erros: {len(cycle_result['errors'])}")
                
                # Métricas acumuladas
                if self.cycles_completed > 0:
                    success_rate = (self.trades_executed / max(self.signals_generated, 1)) * 100
                    logger.info("📈 Métricas Acumuladas:")
                    logger.info(f"   - Ciclos completados: {self.cycles_completed}")
                    logger.info(f"   - Total sinais: {self.signals_generated}")
                    logger.info(f"   - Total trades: {self.trades_executed}")
                    logger.info(f"   - Taxa execução: {success_rate:.1f}%")
                
                # Relatório do adapter
                if self.trading_adapter:
                    adapter_report = self.trading_adapter.get_holographic_performance_report()
                    logger.info("🌀 Adapter Performance:")
                    logger.info(f"   - Execution rate: {adapter_report['execution_rate']:.1f}%")
                    logger.info(f"   - Quantum encoding rate: {adapter_report['quantum_encoding_rate']:.1f}%")
                    logger.info(f"   - Posições abertas: {adapter_report['positions']['open']}")
                
                # Aguarda próximo ciclo
                if cycle < max_cycles - 1:
                    logger.info(f"⏰ Aguardando {cycle_interval}s para próximo ciclo...")
                    await asyncio.sleep(cycle_interval)
                
        except KeyboardInterrupt:
            logger.info("🛑 Interrupção solicitada pelo usuário")
        except Exception as e:
            logger.error(f"❌ Erro na execução contínua: {e}", exc_info=True)
        finally:
            self.running = False
            await self.shutdown()
    
    async def shutdown(self):
        """Encerra o sistema graciosamente."""
        
        logger.info("🛑 Encerrando sistema integrado...")
        
        try:
            # Fecha posições holográficas
            if self.trading_adapter:
                await self.trading_adapter.close_all_holographic_positions("System shutdown")
            
            # Encerra trader
            if self.trader:
                await self.trader.close_exchange()
            
            # Encerra data collector
            if self.data_collector:
                await self.data_collector.__aexit__(None, None, None)
            
            logger.info("✅ Sistema encerrado graciosamente")
            
        except Exception as e:
            logger.error(f"❌ Erro no shutdown: {e}", exc_info=True)

async def main():
    """Função principal."""
    
    # Carrega variáveis de ambiente
    load_dotenv()
    
    # Configuração do sistema
    config = {
        'holographic_universe': {
            'field_size': (200, 200),
            'diffusion_rate': 0.35,
            'feedback_strength': 0.08
        },
        'trader': {
            'symbols': ['BTC/USDT', 'ETH/USDT', 'BNB/USDT'],
            'timeframes': ['5m', '15m', '1h'],
            'capital': 10000.0,
            'risk_profile': 'moderate',
            'mode': 'paper_trading',  # Mude para 'live' para trading real
            'data_source': 'kucoin',
            'risk_per_trade_pct': 2.0,
            'trading_fee_pct': 0.001
        },
        'adapter': {
            'min_confidence': 0.5,
            'max_concurrent_positions': 2,
            'enable_risk_override': True
        }
    }
    
    # Verifica credenciais (opcional para paper trading)
    if not os.getenv('KUCOIN_API_KEY'):
        logger.warning("⚠️  Credenciais KuCoin não encontradas - rodando em modo demo")
    
    # Cria e executa sistema
    system = IntegratedHolographicTradingSystem(config)
    
    try:
        await system.initialize()
        await system.run_continuous(max_cycles=20, cycle_interval=60.0)  # 20 ciclos, 1 min cada
        
    except Exception as e:
        logger.error(f"❌ Erro no sistema principal: {e}", exc_info=True)
    finally:
        await system.shutdown()

if __name__ == "__main__":
    print("🌀 QUALIA Integrated Holographic Trading System")
    print("=" * 60)
    print("Sistema integrado de trading holográfico com:")
    print("• Enhanced Data Collection (OHLCV + Quantum Encoders)")
    print("• Holographic Universe Simulation")
    print("• Real Trade Execution via QUALIARealTimeTrader")
    print("• Advanced Risk Management")
    print("=" * 60)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Sistema interrompido pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro fatal: {e}")
        sys.exit(1)
