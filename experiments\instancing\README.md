# Experimento de Instancing

Este experimento utiliza Three.js em ambiente Node.js para comparar a performance de 50 cubos renderizados individualmente com `Mesh` e com `InstancedMesh`.

Execute `npm install` dentro desta pasta para baixar as dependências. Em seguida, rode `node instancing_experiment.js`. O script abre um browser headless via Puppeteer, mede o FPS médio de cada abordagem por dois segundos e imprime o ganho percentual.

A execução pode falhar em ambientes sem aceleração gráfica ou sem suporte ao Chromium headless.
