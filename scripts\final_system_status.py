#!/usr/bin/env python3
"""
Script para verificar o status final do sistema QUALIA
"""

import sys
import os
import subprocess
import time

# Adicionar src ao path
sys.path.insert(0, 'src')

def test_imports():
    """Testa imports críticos"""
    print("🔍 TESTANDO IMPORTS CRÍTICOS")
    print("-" * 40)
    
    tests = [
        ("qualia.config", "Configuração"),
        ("qualia.core.qast_core", "QAST Core"),
        ("qualia.qualia_trading_system", "Sistema Principal"),
        ("qualia.market.kucoin_integration", "KuCoin Integration")
    ]
    
    success_count = 0
    for module, desc in tests:
        try:
            __import__(module)
            print(f"✅ {desc}")
            success_count += 1
        except Exception as e:
            print(f"❌ {desc}: {e}")
    
    return success_count, len(tests)

def test_api_connectivity():
    """Testa conectividade API"""
    print("\n🌐 TESTANDO CONECTIVIDADE API")
    print("-" * 40)
    
    try:
        import ccxt
        exchange = ccxt.kucoin({'timeout': 5000})
        ticker = exchange.fetch_ticker('BTC/USDT')
        print(f"✅ API KuCoin: BTC/USDT = ${ticker['last']}")
        return True
    except Exception as e:
        print(f"❌ API KuCoin: {e}")
        return False

def test_system_startup():
    """Testa inicialização do sistema"""
    print("\n🚀 TESTANDO INICIALIZAÇÃO DO SISTEMA")
    print("-" * 40)
    
    cmd = [
        sys.executable, "-c",
        "import sys; sys.path.insert(0, 'src'); "
        "from qualia.qualia_trading_system import QUALIARealTimeTrader; "
        "print('✅ Sistema pode ser instanciado')"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0 and "✅" in result.stdout:
            print("✅ Sistema inicializa corretamente")
            return True
        else:
            print(f"❌ Erro na inicialização: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

def main():
    """Função principal"""
    print("🎯 VERIFICAÇÃO FINAL DO SISTEMA QUALIA")
    print("=" * 60)
    
    # Teste 1: Imports
    import_success, import_total = test_imports()
    
    # Teste 2: API
    api_success = test_api_connectivity()
    
    # Teste 3: Sistema
    system_success = test_system_startup()
    
    # Resumo final
    print("\n" + "=" * 60)
    print("📊 RESUMO FINAL")
    print("-" * 60)
    print(f"Imports: {import_success}/{import_total} ({'✅' if import_success == import_total else '❌'})")
    print(f"API KuCoin: {'✅' if api_success else '❌'}")
    print(f"Inicialização: {'✅' if system_success else '❌'}")
    
    overall_success = (import_success == import_total) and api_success and system_success
    
    if overall_success:
        print("\n🎉 SISTEMA QUALIA TOTALMENTE FUNCIONAL!")
        print("✅ Pronto para trading em tempo real")
        print("✅ Todos os componentes operacionais")
        print("✅ Conectividade com KuCoin estabelecida")
        
        print("\n📋 PRÓXIMOS PASSOS:")
        print("1. Execute: python -m src.qualia.qualia_trading_system --help")
        print("2. Configure parâmetros de trading desejados")
        print("3. Execute em modo paper_trading para testes")
        print("4. Migre para modo live quando pronto")
        
    else:
        print("\n⚠️ SISTEMA COM PROBLEMAS")
        print("Verifique os erros acima e corrija antes de usar")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 