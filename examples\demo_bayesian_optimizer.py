#!/usr/bin/env python3
"""
Demo do Bayesian Optimizer - Etapa D
YAA IMPLEMENTATION: Demonstração funcional do sistema de otimização.
"""

import sys
import asyncio
import time
import json
from datetime import datetime
from pathlib import Path

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

from src.bayesian_optimizer import BayesianOptimizer, OptimizationConfig

async def demo_quick_optimization():
    """Demonstração rápida de otimização."""
    
    print("🚀 DEMO BAYESIAN OPTIMIZER - OTIMIZAÇÃO RÁPIDA")
    print("=" * 60)
    
    # Configuração para demo (rápida)
    config = OptimizationConfig(
        study_name="qualia_demo_optimization",
        n_trials_per_cycle=8,  # Poucos trials para demo
        optimization_cycles=3,  # Poucos ciclos para demo
        symbols=["BTCUSDT"],    # Apenas BTC para velocidade
        base_params={
            "price_amplification": 2.0,  # Parâmetros vencedores
            "news_amplification": 7.0,
            "min_confidence": 0.4
        }
    )
    
    print(f"📊 Configuração da Demo:")
    print(f"   • Símbolo: {config.symbols[0]}")
    print(f"   • Trials por ciclo: {config.n_trials_per_cycle}")
    print(f"   • Ciclos totais: {config.optimization_cycles}")
    print(f"   • Parâmetros base: {config.base_params}")
    
    # Inicializa otimizador
    print(f"\n🔧 Inicializando otimizador...")
    optimizer = BayesianOptimizer(config)
    
    # Executa otimização
    print(f"\n🎯 Iniciando otimização (isso pode levar alguns minutos)...")
    
    start_time = time.time()
    
    try:
        # Executa alguns ciclos
        for cycle in range(config.optimization_cycles):
            print(f"\n📈 Executando ciclo {cycle + 1}/{config.optimization_cycles}...")
            
            cycle_start = time.time()
            cycle_result = optimizer.run_optimization_cycle()
            cycle_time = time.time() - cycle_start
            
            print(f"   ✅ Ciclo {cycle + 1} concluído em {cycle_time:.1f}s")
            print(f"   📊 Melhor PnL: {cycle_result.get('best_pnl_24h', 0):.4f}%")
            print(f"   🎛️ Melhores parâmetros:")
            
            best_params = cycle_result.get('best_params', {})
            for param, value in best_params.items():
                print(f"      • {param}: {value:.3f}")
            
            # Pequena pausa entre ciclos
            if cycle < config.optimization_cycles - 1:
                print(f"   ⏳ Aguardando próximo ciclo (10s)...")
                await asyncio.sleep(10)
    
    except KeyboardInterrupt:
        print(f"\n🛑 Demo interrompida pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro na demo: {e}")
    
    total_time = time.time() - start_time
    
    # Resultados finais
    print(f"\n" + "=" * 60)
    print(f"📊 RESULTADOS DA DEMO")
    print(f"=" * 60)
    
    if optimizer.study and optimizer.study.best_trial:
        best_params = optimizer.study.best_params
        best_value = optimizer.study.best_value
        n_trials = len(optimizer.study.trials)
        
        print(f"🏆 Melhor resultado encontrado:")
        print(f"   • PnL 24h: {best_value:.4f}%")
        print(f"   • Trials executados: {n_trials}")
        print(f"   • Tempo total: {total_time:.1f}s")
        
        print(f"\n🎛️ Melhores parâmetros otimizados:")
        for param, value in best_params.items():
            original = config.base_params.get(param, 0)
            change = ((value / original) - 1) * 100 if original > 0 else 0
            print(f"   • {param}: {value:.3f} (original: {original:.3f}, mudança: {change:+.1f}%)")
        
        # Comparação com parâmetros originais
        print(f"\n📈 Melhoria vs Parâmetros Originais:")
        if best_value > 0:
            print(f"   ✅ Otimização bem-sucedida: +{best_value:.4f}% PnL")
        else:
            print(f"   ⚠️ Resultado negativo: {best_value:.4f}% PnL")
            print(f"   💡 Pode precisar de mais trials ou ajuste de range")
    
    else:
        print(f"❌ Nenhum resultado válido obtido")
        print(f"⏱️ Tempo total: {total_time:.1f}s")
    
    # Salva resultados da demo
    optimizer.save_results()
    
    print(f"\n💾 Resultados salvos em: results/bayesian_optimization/")
    print(f"📋 Logs disponíveis em: logs/bayesian_optimizer.log")
    
    return optimizer

def demo_parameter_analysis():
    """Demonstra análise de parâmetros."""
    
    print(f"\n🔍 ANÁLISE DE PARÂMETROS")
    print(f"=" * 40)
    
    # Carrega resultados mais recentes
    results_dir = Path("results/bayesian_optimization")
    
    if not results_dir.exists():
        print(f"❌ Diretório de resultados não encontrado")
        return
    
    result_files = list(results_dir.glob("optimization_results_*.json"))
    
    if not result_files:
        print(f"❌ Nenhum arquivo de resultado encontrado")
        return
    
    # Carrega arquivo mais recente
    latest_file = max(result_files, key=lambda x: x.stat().st_mtime)
    
    try:
        with open(latest_file, 'r') as f:
            data = json.load(f)
        
        print(f"📄 Analisando: {latest_file.name}")
        
        # Estatísticas gerais
        config = data.get('config', {})
        history = data.get('optimization_history', [])
        best_params = data.get('current_best_params', {})
        best_pnl = data.get('current_best_pnl', 0)
        total_trials = data.get('total_trials', 0)
        
        print(f"\n📊 Estatísticas Gerais:")
        print(f"   • Total de trials: {total_trials}")
        print(f"   • Ciclos executados: {len(history)}")
        print(f"   • Melhor PnL: {best_pnl:.4f}%")
        
        if history:
            print(f"\n📈 Evolução por Ciclo:")
            for i, cycle in enumerate(history):
                pnl = cycle.get('best_pnl_24h', 0)
                trials = cycle.get('n_trials', 0)
                time_s = cycle.get('cycle_time_seconds', 0)
                print(f"   Ciclo {i+1}: PnL={pnl:.4f}%, Trials={trials}, Tempo={time_s:.1f}s")
        
        if best_params:
            print(f"\n🎛️ Parâmetros Finais Otimizados:")
            base_params = config.get('base_params', {})
            
            for param, value in best_params.items():
                original = base_params.get(param, 0)
                if original > 0:
                    change = ((value / original) - 1) * 100
                    print(f"   • {param}: {value:.3f} (mudança: {change:+.1f}%)")
                else:
                    print(f"   • {param}: {value:.3f}")
    
    except Exception as e:
        print(f"❌ Erro ao analisar resultados: {e}")

async def main():
    """Função principal da demo."""
    
    print("🎮 QUALIA BAYESIAN OPTIMIZER - DEMO INTERATIVA")
    print("=" * 60)
    
    try:
        # Executa demo de otimização
        optimizer = await demo_quick_optimization()
        
        # Análise de resultados
        demo_parameter_analysis()
        
        print(f"\n" + "=" * 60)
        print(f"🎉 DEMO CONCLUÍDA COM SUCESSO!")
        print(f"=" * 60)
        
        print(f"\n🚀 Próximos Passos:")
        print(f"   1. 🔧 Executar: python src/parameter_tuner.py")
        print(f"   2. 🌐 Iniciar: python src/grpc_service.py")
        print(f"   3. 📊 Monitorar: logs/parameter_tuner.log")
        print(f"   4. 📈 Resultados: results/bayesian_optimization/")
        
        print(f"\n💡 Dicas:")
        print(f"   • Ajuste n_trials_per_cycle para mais precisão")
        print(f"   • Use mais símbolos para diversificação")
        print(f"   • Configure worker_settings para produção")
        
    except KeyboardInterrupt:
        print(f"\n🛑 Demo interrompida pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro na demo: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n👋 Demo finalizada!")

if __name__ == "__main__":
    asyncio.run(main())
