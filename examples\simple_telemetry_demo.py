#!/usr/bin/env python3
"""
Demonstração simples do sistema de telemetria de hiperparâmetros QUALIA.

YAA REFINEMENT: Exemplo básico focado apenas na telemetria sem dependências complexas.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import time
import random
from unittest.mock import Mock

from src.qualia.config.hyperparams_loader import HyperParams
from src.qualia.metrics.hyperparams_telemetry import HyperParamsTelemetryCollector


def main():
    """Demonstração principal do sistema de telemetria."""
    
    print("🚀 Demonstração Simples de Telemetria QUALIA")
    print("=" * 50)
    
    # 1. Inicializa coletor de telemetria
    print("\n📊 1. Inicializando coletor de telemetria...")
    
    # Mock do StatsD para evitar dependências externas
    mock_statsd = Mock()
    mock_metrics = Mock()
    
    collector = HyperParamsTelemetryCollector(
        statsd_client=mock_statsd,
        metrics_collector=mock_metrics
    )
    
    print("   ✅ Coletor inicializado")
    
    # 2. Cria hiperparâmetros de exemplo
    print("\n⚙️ 2. Configurando hiperparâmetros...")
    
    hyperparams = HyperParams(
        price_amplification=6.0,
        news_amplification=5.0,
        min_confidence=0.7,
        pattern_threshold=0.4
    )
    
    print(f"   Price Amplification: {hyperparams.price_amplification}")
    print(f"   News Amplification: {hyperparams.news_amplification}")
    print(f"   Min Confidence: {hyperparams.min_confidence}")
    print(f"   Pattern Threshold: {hyperparams.pattern_threshold}")
    
    # 3. Simula eventos de decisão
    print("\n🎯 3. Simulando eventos de decisão...")
    
    decisions = ["buy", "sell", "hold"]
    symbols = ["BTC/USDT", "ETH/USDT", "ADA/USDT"]
    trends = ["bull", "bear", "sideways"]
    
    for i in range(10):
        # Simula dados de mercado
        symbol = random.choice(symbols)
        trend = random.choice(trends)
        volatility = random.uniform(0.01, 0.05)
        
        # Simula decisão
        raw_score = random.uniform(-1.0, 1.0)
        confidence = random.uniform(0.3, 0.9)
        decision = random.choice(decisions)
        
        # Registra evento
        collector.record_decision_event(
            hyperparams=hyperparams,
            raw_score=raw_score,
            confidence=confidence,
            decision=decision,
            component="SimpleDemo",
            symbol=symbol,
            market_volatility=volatility,
            market_trend=trend
        )
        
        print(f"   Evento {i+1}: {decision} {symbol} (score: {raw_score:.3f}, conf: {confidence:.3f})")
        
        time.sleep(0.1)  # Pequena pausa
    
    # 4. Simula eventos de calibração
    print("\n🔧 4. Simulando eventos de calibração...")
    
    for i in range(3):
        patterns_detected = random.randint(5, 20)
        signals_generated = random.randint(2, patterns_detected)
        signals_executed = random.randint(1, signals_generated)
        success_rate = random.uniform(0.5, 0.9)
        false_positive_rate = random.uniform(0.1, 0.3)
        
        collector.record_calibration_event(
            hyperparams=hyperparams,
            patterns_detected=patterns_detected,
            signals_generated=signals_generated,
            signals_executed=signals_executed,
            execution_success_rate=success_rate,
            false_positive_rate=false_positive_rate,
            component="SimpleCalibrator"
        )
        
        print(f"   Calibração {i+1}: {patterns_detected} padrões, {success_rate:.1%} sucesso")
    
    # 5. Demonstra context manager
    print("\n🎯 5. Demonstrando context manager...")
    
    with collector.decision_context(hyperparams, "ContextDemo", symbol="DEMO/USDT") as ctx:
        # Simula processamento
        time.sleep(0.05)
        
        # Define resultados
        ctx["raw_score"] = 0.75
        ctx["confidence"] = 0.85
        ctx["decision"] = "buy"
        ctx["market_trend"] = "bull"
        
        print(f"   Contexto: {ctx['decision']} (score: {ctx['raw_score']}, conf: {ctx['confidence']})")
    
    print("   ✅ Telemetria capturada automaticamente")
    
    # 6. Exibe estatísticas
    print("\n📈 6. Estatísticas de telemetria:")
    
    stats = collector.get_summary_stats()
    
    print(f"   Total de eventos: {stats['total_events']}")
    print(f"   Eventos recentes: {stats['recent_events']}")
    print(f"   Decisões por tipo: {stats['decision_counts']}")
    print(f"   Eventos por componente: {stats['component_counts']}")
    
    if stats['hyperparams_stats']:
        hp_stats = stats['hyperparams_stats']
        print(f"   Price Amplification: {hp_stats['price_amplification']['min']:.1f} - {hp_stats['price_amplification']['max']:.1f}")
        print(f"   News Amplification: {hp_stats['news_amplification']['min']:.1f} - {hp_stats['news_amplification']['max']:.1f}")
        print(f"   Min Confidence: {hp_stats['min_confidence']['min']:.2f} - {hp_stats['min_confidence']['max']:.2f}")
    
    # 7. Verifica integração com sistemas externos
    print("\n🔌 7. Verificando integração com sistemas externos...")
    
    # Verifica chamadas para StatsD
    statsd_calls = mock_statsd.gauge.call_count + mock_statsd.increment.call_count
    print(f"   Chamadas StatsD: {statsd_calls}")
    
    # Verifica chamadas para MetricsCollector interno
    metrics_calls = mock_metrics.record_metric.call_count
    print(f"   Chamadas MetricsCollector: {metrics_calls}")
    
    if statsd_calls > 0:
        print("   ✅ Integração StatsD funcionando")
    
    if metrics_calls > 0:
        print("   ✅ Integração MetricsCollector funcionando")
    
    # 8. Demonstra diferentes tipos de eventos
    print("\n🎨 8. Demonstrando diferentes tipos de eventos...")
    
    # Evento com contexto rico
    collector.record_decision_event(
        hyperparams=hyperparams,
        raw_score=0.8,
        confidence=0.9,
        decision="buy",
        component="RichContextDemo",
        symbol="BTC/USDT",
        market_volatility=0.025,
        market_trend="bull",
        # Contexto adicional
        portfolio_exposure=0.3,
        risk_score=0.2,
        sentiment_score=0.7,
        technical_indicators={
            "rsi": 65,
            "macd": 0.15,
            "bollinger_position": 0.8
        }
    )
    
    print("   ✅ Evento com contexto rico registrado")
    
    # Evento de calibração com métricas avançadas
    collector.record_calibration_event(
        hyperparams=hyperparams,
        patterns_detected=25,
        signals_generated=18,
        signals_executed=15,
        execution_success_rate=0.87,
        false_positive_rate=0.13,
        component="AdvancedCalibrator",
        # Métricas adicionais
        pattern_quality_score=0.82,
        signal_strength_avg=0.75,
        execution_latency_ms=45.2,
        market_regime="trending"
    )
    
    print("   ✅ Evento de calibração avançado registrado")
    
    # 9. Estatísticas finais
    print("\n📊 9. Estatísticas finais:")
    
    final_stats = collector.get_summary_stats()
    print(f"   Total final de eventos: {final_stats['total_events']}")
    print(f"   Componentes únicos: {len(final_stats['component_counts'])}")
    print(f"   Tipos de decisão: {len([k for k, v in final_stats['decision_counts'].items() if v > 0])}")
    
    print("\n✅ Demonstração concluída!")
    print("=" * 50)
    print("💡 O sistema de telemetria está capturando e processando eventos")
    print("💡 Métricas estão sendo enviadas para sistemas de monitoramento")
    print("💡 Use ferramentas como Grafana para visualizar em tempo real")


if __name__ == "__main__":
    main()
