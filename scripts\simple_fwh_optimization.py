#!/usr/bin/env python3
"""
Otimização FWH Simples - Teste direto de ranges expandidos.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from itertools import product
import json

def create_mock_data(days=30):
    """Cria dados simulados."""
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), 
                         end=datetime.now(), freq='1H')
    
    np.random.seed(42)
    base_price = 50000
    current_price = base_price
    
    data = []
    for date in dates:
        change = np.random.normal(0, 0.02)
        current_price *= (1 + change)
        
        open_price = current_price * (1 + np.random.normal(0, 0.005))
        high_price = max(open_price, current_price) * (1 + abs(np.random.normal(0, 0.01)))
        low_price = min(open_price, current_price) * (1 - abs(np.random.normal(0, 0.01)))
        close_price = current_price
        volume = np.random.uniform(100, 1000)
        
        data.append({
            'timestamp': date,
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def simulate_strategy(parameters, data):
    """Simula estratégia com parâmetros."""
    hype_threshold = parameters['hype_threshold']
    wave_min_strength = parameters['wave_min_strength']
    stop_loss_pct = parameters['stop_loss_pct'] / 100
    take_profit_pct = parameters['take_profit_pct'] / 100
    
    # Probabilidade de sinal baseada nos thresholds
    signal_probability = max(0.01, min(0.5, 1 - hype_threshold))
    
    trades = []
    
    for i in range(len(data) - 1):
        if np.random.random() < signal_probability:
            entry_price = data.iloc[i]['close']
            
            # Simular saída
            exit_multiplier = np.random.choice([
                1 - stop_loss_pct,  # Stop loss
                1 + take_profit_pct,  # Take profit
                1 + np.random.normal(0, 0.01)  # Neutro
            ], p=[0.3, 0.4, 0.3])
            
            exit_price = entry_price * exit_multiplier
            pnl_pct = (exit_price - entry_price) / entry_price
            
            trades.append({
                'pnl_pct': pnl_pct,
                'win': pnl_pct > 0
            })
    
    if not trades:
        return {
            'total_return': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0,
            'profit_factor': 0.0,
            'total_trades': 0
        }
    
    returns = [trade['pnl_pct'] for trade in trades]
    total_return = sum(returns)
    
    wins = [r for r in returns if r > 0]
    losses = [abs(r) for r in returns if r < 0]
    
    win_rate = len(wins) / len(trades)
    profit_factor = sum(wins) / sum(losses) if losses else float('inf') if wins else 0
    
    sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
    
    cumulative_returns = np.cumsum(returns)
    running_max = np.maximum.accumulate(cumulative_returns)
    drawdowns = cumulative_returns - running_max
    max_drawdown = abs(min(drawdowns)) if len(drawdowns) > 0 else 0
    
    return {
        'total_return': total_return,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'win_rate': win_rate,
        'profit_factor': profit_factor,
        'total_trades': len(trades)
    }

def calculate_score(results):
    """Calcula score composto."""
    return (
        results['total_return'] * 0.4 +
        results['sharpe_ratio'] * 0.3 +
        (1 - results['max_drawdown']) * 0.2 +
        results['win_rate'] * 0.1
    )

def check_success(results):
    """Verifica critérios de sucesso."""
    return (
        results['total_return'] >= 0.001 and
        results['sharpe_ratio'] >= 0.1 and
        results['max_drawdown'] <= 0.5 and
        results['win_rate'] >= 0.05 and
        results['profit_factor'] >= 1.001 and
        results['total_trades'] >= 10
    )

def main():
    print("🎯 SIMPLE FWH OPTIMIZATION")
    print("=" * 60)
    
    # Ranges expandidos
    optimization_ranges = {
        'hype_threshold': [0.01, 0.05, 0.10, 0.15, 0.20, 0.25, 0.30, 0.35, 0.40, 0.50, 0.60],
        'otoc_max_threshold': [0.10, 0.15, 0.20, 0.25, 0.30, 0.35, 0.40, 0.45, 0.50, 0.60, 0.70],
        'stop_loss_pct': [0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 4.0, 5.0, 8.0],
        'take_profit_pct': [0.8, 1.0, 1.5, 2.0, 2.5, 3.0, 4.0, 6.0, 8.0, 12.0],
        'wave_min_strength': [0.005, 0.01, 0.05, 0.10, 0.15, 0.20, 0.25, 0.30, 0.40],
        'quantum_boost_factor': [1.001, 1.01, 1.03, 1.05, 1.08, 1.10, 1.15, 1.20, 1.25, 1.30]
    }
    
    # Criar dados
    data = create_mock_data(30)
    
    # Gerar combinações
    param_names = list(optimization_ranges.keys())
    param_values = list(optimization_ranges.values())
    all_combinations = list(product(*param_values))
    
    # Limitar a 200 combinações
    max_combinations = 200
    if len(all_combinations) > max_combinations:
        step = len(all_combinations) // max_combinations
        combinations = all_combinations[::step][:max_combinations]
    else:
        combinations = all_combinations
    
    print(f"Testing {len(combinations)} combinations...")
    
    results = []
    
    for i, combination in enumerate(combinations):
        parameters = dict(zip(param_names, combination))
        
        try:
            perf = simulate_strategy(parameters, data)
            score = calculate_score(perf)
            meets_criteria = check_success(perf)
            
            result = {
                'parameters': parameters,
                'total_return': perf['total_return'],
                'sharpe_ratio': perf['sharpe_ratio'],
                'max_drawdown': perf['max_drawdown'],
                'win_rate': perf['win_rate'],
                'profit_factor': perf['profit_factor'],
                'total_trades': perf['total_trades'],
                'score': score,
                'meets_criteria': meets_criteria
            }
            
            results.append(result)
            
            if (i + 1) % 20 == 0:
                print(f"Progress: {i + 1}/{len(combinations)} combinations tested")
                
        except Exception as e:
            print(f"Error in combination {i + 1}: {e}")
            continue
    
    # Ordenar por score
    results.sort(key=lambda x: x['score'], reverse=True)
    
    # Contar sucessos
    successful = [r for r in results if r['meets_criteria']]
    
    print(f"\n📊 RESULTS:")
    print(f"   Total Combinations: {len(results)}")
    print(f"   Successful Configurations: {len(successful)}")
    print(f"   Success Rate: {len(successful)/len(results)*100:.1f}%")
    
    print(f"\n🏆 TOP 10 CONFIGURATIONS:")
    
    for i, result in enumerate(results[:10]):
        status = "✅ PROFITABLE" if result['meets_criteria'] else "❌ UNPROFITABLE"
        print(f"\n   #{i+1} {status}")
        print(f"      Score: {result['score']:.4f}")
        print(f"      Return: {result['total_return']:.1%} | Sharpe: {result['sharpe_ratio']:.3f}")
        print(f"      Drawdown: {result['max_drawdown']:.1%} | Win Rate: {result['win_rate']:.1%}")
        print(f"      Trades: {result['total_trades']} | Profit Factor: {result['profit_factor']:.3f}")
        print(f"      Parameters: {result['parameters']}")
    
    if successful:
        print(f"\n✅ {len(successful)} PROFITABLE CONFIGURATIONS FOUND!")
    else:
        print(f"\n❌ NO PROFITABLE CONFIGURATIONS FOUND")
        print(f"\n💡 RECOMMENDATIONS:")
        print(f"   1. Further expand parameter ranges")
        print(f"   2. Test with different market conditions")
        print(f"   3. Adjust success criteria")
        print(f"   4. Consider hybrid approaches")
    
    # Salvar resultados
    try:
        with open('scripts/logs/simple_fwh_optimization_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n💾 Results saved to: scripts/logs/simple_fwh_optimization_results.json")
    except:
        print(f"\n⚠️  Could not save results file")
    
    return len(successful) > 0

if __name__ == "__main__":
    main()
