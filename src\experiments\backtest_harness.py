import argparse
from qualia.utils.logger import get_logger
from qualia.utils.path_utils import ensure_parent_dirs
from qualia.utils.config_loader import load_config_file
from pathlib import Path
from typing import Any, Dict

from qualia.qualia_trading_system import QUALIARealTimeTrader

logger = get_logger(__name__)


def load_strategy_parameters(config_path: str) -> Dict[str, Any]:
    """Load strategy parameters from a JSON or YAML file."""

    return load_config_file(config_path)


async def run_backtest(args: argparse.Namespace) -> Dict[str, Any]:
    ensure_parent_dirs(
        getattr(args, "log_file", None), getattr(args, "output_report_path", None)
    )

    if not getattr(args, "strategy_config_json", None):
        logger.error("strategy_config_json is required")
        return {}

    if not Path(args.historical_data_path).exists():
        raise SystemExit(1)

    if not Path(args.strategy_config_json).exists():
        raise SystemExit(1)

    strategy_params = load_strategy_parameters(args.strategy_config_json)

    config = {
        "enable_qast_evolution": not getattr(args, "disable_qast_evolution", False),
        "risk_profile_settings": strategy_params.get("risk_profile_settings", {}),
    }

    QUALIARealTimeTrader(
        symbols=[args.symbol],
        timeframes=[args.timeframe],
        capital=args.initial_capital,
        risk_profile=args.risk_profile,
        risk_per_trade_pct=(
            args.risk_per_trade_pct if args.risk_profile == "custom" else None
        ),
        trading_fee_pct=args.trading_fee_pct,
        mode="simulation",
        data_source="simulation",
        duration_seconds=None,
        disable_metacognition=args.disable_metacognition,
        config=config,
        strategy_config_path=args.strategy_config_json,
    )

    return {}
