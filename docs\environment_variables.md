# Variáveis de Ambiente QUALIA

Este documento compila as variáveis iniciadas por `QUALIA_` mencionadas na documentação. Para detalhes sobre cada uso consulte também os arquivos indicados.

| Variável | Descrição/Default | Onde é usada |
|-----------|-------------------|--------------|
| `QUALIA_CACHE_DIR` | Diretório para persistência em JSON. Padrão: `data/cache`. | [json_persistence.md](api/json_persistence.md) | 
| `QUALIA_DYNAMIC_RISK_DEFAULTS` | Caminho para o YAML de parâmetros de risco dinâmico (via `load_dynamic_risk_defaults`). | [risk_management.md](risk_management.md) |
| `QUALIA_DYN_RISK_*` | Sobrescrevem campos individuais do `DynamicRiskController`. | [risk_management.md](risk_management.md) |
| `QUALIA_SELF_EVOLVING_DEFAULTS` | YAML de limiares usados no `SelfEvolvingTradingSystem`. | [api/self_evolving_config.md](api/self_evolving_config.md) |
| `QUALIA_CONSCIOUSNESS_DEFAULTS` | YAML com parâmetros da `QUALIAConsciousness`. | [api/consciousness_config.md](api/consciousness_config.md) |
| `QUALIA_LOG_DEDUP_INTERVAL` | Intervalo para suprimir logs duplicados. Padrão: 5.0 s. | [logging.md](logging.md) |
| `QUALIA_MARKET_METRICS_ENABLED` | Habilita métricas de latência das integrações. | [api/kraken_integration.md](api/kraken_integration.md) |
| `QUALIA_PID_METRICS_ENABLED` | Envia métricas do PIDOptimizer via DogStatsd. | [api/qast_core.md](api/qast_core.md) |
| `QUALIA_PID_COEFFS_FILE` | Caminho do arquivo `pid_coeffs.json`. | [README.md](../README.md) / [api/qast_core.md](api/qast_core.md) |
| `QUALIA_PID_PERFORMANCE_FILE` | Armazena desempenho do controlador PID. | [README.md](../README.md) |
| `QUALIA_LOGS_DIR` | Diretório de logs. Padrão: `logs`. | [README.md](../README.md) |
| `QUALIA_LOG_LEVEL` | Nível global de logs (ex.: `DEBUG`). Padrão: `INFO`. | [utils/logger.py](../src/qualia/utils/logger.py) |
| `QUALIA_RESULTS_DIR` | Diretório de resultados gerais. | [README.md](../README.md) |
| `QUALIA_QAST_BACKTEST_RESULTS_DIR` | Subdiretório para backtests do QAST. | [README.md](../README.md) |
| `QUALIA_BACKEND_DEVICE` | Define o dispositivo do backend Qiskit (`CPU` ou `GPU`). | [utils/backend.py](../src/qualia/utils/backend.py) |
| `QUALIA_USE_GPU` | Força o uso de GPU e habilita aceleradores como Numba (`true`/`false`). | [core/universe.py](../src/qualia/core/universe.py) |
| `QUALIA_USE_GPU_MEMORY` | Ativa normalização vetorial em GPU via CuPy ou qiskit-aer quando disponível. | [memory/similarity.py](../src/qualia/memory/similarity.py) |
| `QUALIA_STRATEGY_CONFIG` | YAML de configuração das estratégias. | [README.md](../README.md) |
| `QUALIA_MODULE_LEVELS` | JSON com níveis específicos por módulo (ex.: `{"src.qualia.memory": "DEBUG", "src.qualia.core.universe": "DEBUG"}`). | [README.md](../README.md) |
| `QUALIA_AUTO_INIT` | Tenta inicializar trading automaticamente. | [README.md](../README.md) |
| `QUALIA_ENV_PATH` | Caminho para o `.env` customizado. | [README.md](../README.md) |
| `QUALIA_SECRET_KEY` | Chave para criptografia e inicialização da interface web. | [README.md](../README.md), [api/kucoin_integration.md](api/kucoin_integration.md) |
| `QUALIA_SECRET_KEY_PATH` | Caminho para chave persistente usada pelo SecurityManager. | [README.md](../README.md), [api/kucoin_integration.md](api/kucoin_integration.md) |
| `QUALIA_RISK_HISTORY_LIMIT` | Número máximo de entradas de histórico mantidas pelos gerenciadores de risco. | [analysis/integration_risk_management_double_check.md](analysis/integration_risk_management_double_check.md) |
| `QUALIA_MAX_CIRCUIT_DEPTH` | Limite máximo de profundidade dos circuitos quânticos. Padrão lido de `universe_config.max_circuit_depth`. | [utils/quantum_utils.py](../src/qualia/utils/quantum_utils.py) |
| `QUALIA_MAX_CIRCUIT_OPERATIONS` | Número máximo de operações permitidas em cada circuito. Padrão lido de `universe_config.max_circuit_operations`. | [utils/quantum_utils.py](../src/qualia/utils/quantum_utils.py) |
| `QUALIA_MAX_EXACT_QFT_QUBITS` | Número máximo de qubits para tentativa de QFT exata. Acima disso, usa estado aproximado. Padrão lido de `universe_config.max_exact_qft_qubits`. | [core/universe.py](../src/qualia/core/universe.py) |
| `QUALIA_MIN_CX_RATIO` | Razão mínima de portas CX por qubit ao aparar circuitos. Padrão: `0.8` (ajustável automaticamente quando não definido). | [utils/quantum_utils.py](../src/qualia/utils/quantum_utils.py) |

| `QUALIA_NEXUS_HISTORY` | Caminho para o arquivo JSONL que armazena o histórico de coerência e alertas do NEXUS. Padrão: `nexus_coherence_history.jsonl`. | [coherence_store.py](../src/qualia/persistence/coherence_store.py) |


| `QUALIA_ORDER_JOURNAL_FILE` | Caminho do diário persistente de ordens. Padrão: `data/cache/order_journal.jsonl`. | `order_journal` |
| `QUALIA_SIGNAL_ARBITER_DEFAULTS` | YAML de configuração do SignalArbiter (via `load_signal_arbiter_defaults`). | [signal_arbiter_defaults.py](../src/qualia/config/signal_arbiter_defaults.py) |
| `QUALIA_METACOGNITION_DEFAULTS` | YAML com parâmetros de metacognição (via `load_metacognition_defaults`). | [metacognition_defaults.py](../src/qualia/config/metacognition_defaults.py) |
| `QUALIA_QUANTUM_RISK_DEFAULTS` | YAML com ajustes do QuantumRiskAnalyzer (via `load_quantum_risk_defaults`). | [quantum_risk_defaults.py](../src/qualia/config/quantum_risk_defaults.py) |
| `QUALIA_TRADING_DEFAULTS` | Valores padrão do QualiaTradingSystem (via `load_trading_defaults`). | [trading_defaults.py](../src/qualia/config/trading_defaults.py) |
| `QUALIA_CTC_DEFAULTS` | Parâmetros do módulo CTC Utilities (via `load_ctc_defaults`). | [ctc_defaults.py](../src/qualia/config/ctc_defaults.py) |
| `QUALIA_UTILS_CONFIG` | Caminho opcional para `config/utils.yaml` (via `load_utils_defaults`). | [utils_defaults.py](../src/qualia/config/utils_defaults.py) |
| `QUALIA_KRAKEN_FEE_TTL` | TTL em segundos do cache de taxas da Kraken. Padrão: `3600`. | [paper_broker.py](../src/qualia/market/paper_broker.py) |
| `QUALIA_DEFAULT_FEE_PAIR` | Par usado por padrão para estimar taxas no Kraken. Padrão: `ETHUSDT`. | [paper_broker.py](../src/qualia/market/paper_broker.py) |
