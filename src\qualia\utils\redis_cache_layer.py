"""Redis-based L2 cache layer implementation.

This module provides Redis integration for the multi-layer cache architecture,
offering persistent caching with advanced features like:
- Distributed caching across multiple instances
- Atomic operations and transactions
- Pub/Sub for cache invalidation
- Compression and serialization
- Connection pooling and failover
"""

from __future__ import annotations

import asyncio
import json
import pickle
import gzip
import time
from typing import Any, Dict, List, Optional, Union, Callable
from dataclasses import asdict
import hashlib

try:
    import redis.asyncio as redis
    from redis.asyncio import ConnectionPool
    REDIS_AVAILABLE = True
except ImportError:
    redis = None
    ConnectionPool = None
    REDIS_AVAILABLE = False

from qualia.utils.logger import get_logger
from qualia.utils.adaptive_cache import CacheEntry, DataQuality

logger = get_logger(__name__)


class SerializationMethod:
    """Serialization methods for cache data."""
    JSON = "json"
    PICKLE = "pickle"
    COMPRESSED_PICKLE = "compressed_pickle"


class RedisL2Cache:
    """Redis-based L2 cache implementation with advanced features.
    
    Features:
    - Connection pooling and failover
    - Multiple serialization methods
    - Compression for large values
    - Atomic operations
    - Pub/Sub for distributed invalidation
    - Comprehensive error handling
    """
    
    def __init__(
        self,
        redis_url: str = "redis://localhost:6379",
        key_prefix: str = "qualia_cache:",
        default_ttl: int = 3600,
        max_connections: int = 20,
        serialization_method: str = SerializationMethod.COMPRESSED_PICKLE,
        compression_threshold: int = 1024,
        enable_pubsub: bool = True,
        retry_attempts: int = 3,
        retry_delay: float = 0.1,
    ):
        if not REDIS_AVAILABLE:
            raise ImportError("Redis is not available. Install with: pip install redis")
        
        self.redis_url = redis_url
        self.key_prefix = key_prefix
        self.default_ttl = default_ttl
        self.serialization_method = serialization_method
        self.compression_threshold = compression_threshold
        self.enable_pubsub = enable_pubsub
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay
        
        # Connection management
        self.pool = ConnectionPool.from_url(
            redis_url,
            max_connections=max_connections,
            retry_on_timeout=True,
            socket_keepalive=True,
            socket_keepalive_options={},
        )
        
        self.redis_client: Optional[redis.Redis] = None
        self.pubsub_client: Optional[redis.Redis] = None
        self.pubsub_task: Optional[asyncio.Task] = None
        
        # Statistics
        self.stats = {
            'gets': 0,
            'sets': 0,
            'deletes': 0,
            'hits': 0,
            'misses': 0,
            'errors': 0,
            'serialization_errors': 0,
            'compression_saves': 0,
            'pubsub_messages': 0
        }
        
        # Callbacks for distributed invalidation
        self.invalidation_callbacks: List[Callable[[str], None]] = []
        
        self._is_connected = False

    async def connect(self) -> bool:
        """Establish connection to Redis."""
        try:
            self.redis_client = redis.Redis(connection_pool=self.pool)
            
            # Test connection
            await self.redis_client.ping()
            
            # Setup pub/sub for distributed invalidation
            if self.enable_pubsub:
                self.pubsub_client = redis.Redis(connection_pool=self.pool)
                await self._setup_pubsub()
            
            self._is_connected = True
            logger.info("Connected to Redis L2 cache")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self.stats['errors'] += 1
            return False

    async def disconnect(self) -> None:
        """Disconnect from Redis."""
        if self.pubsub_task:
            self.pubsub_task.cancel()
            try:
                await self.pubsub_task
            except asyncio.CancelledError:
                pass
        
        if self.redis_client:
            await self.redis_client.close()
        
        if self.pubsub_client:
            await self.pubsub_client.close()
        
        if self.pool:
            await self.pool.disconnect()
        
        self._is_connected = False
        logger.info("Disconnected from Redis L2 cache")

    async def get(self, key: str) -> Optional[CacheEntry]:
        """Get cache entry from Redis.
        
        Parameters
        ----------
        key : str
            Cache key
            
        Returns
        -------
        CacheEntry or None
            Cached entry if found and valid
        """
        if not self._is_connected:
            return None
        
        self.stats['gets'] += 1
        redis_key = self._make_redis_key(key)
        
        for attempt in range(self.retry_attempts):
            try:
                # Get data and metadata
                pipe = self.redis_client.pipeline()
                pipe.hgetall(redis_key)
                pipe.ttl(redis_key)
                
                result, ttl = await pipe.execute()
                
                if not result:
                    self.stats['misses'] += 1
                    return None
                
                # Deserialize cache entry
                entry = await self._deserialize_entry(result)
                if entry is None:
                    self.stats['misses'] += 1
                    return None
                
                # Check if expired (double-check with Redis TTL)
                if ttl <= 0 or entry.is_expired:
                    await self.delete(key)
                    self.stats['misses'] += 1
                    return None
                
                self.stats['hits'] += 1
                logger.debug(f"L2 cache hit for key '{key}'")
                return entry
                
            except Exception as e:
                logger.warning(f"Redis get attempt {attempt + 1} failed for key '{key}': {e}")
                self.stats['errors'] += 1
                
                if attempt < self.retry_attempts - 1:
                    await asyncio.sleep(self.retry_delay * (2 ** attempt))
                else:
                    self.stats['misses'] += 1
                    return None

    async def set(self, key: str, entry: CacheEntry) -> bool:
        """Set cache entry in Redis.
        
        Parameters
        ----------
        key : str
            Cache key
        entry : CacheEntry
            Cache entry to store
            
        Returns
        -------
        bool
            True if successfully stored
        """
        if not self._is_connected:
            return False
        
        self.stats['sets'] += 1
        redis_key = self._make_redis_key(key)
        
        for attempt in range(self.retry_attempts):
            try:
                # Serialize entry
                serialized_data = await self._serialize_entry(entry)
                if serialized_data is None:
                    return False
                
                # Calculate TTL
                ttl = max(1, int(entry.ttl - entry.age))
                
                # Store with pipeline for atomicity
                pipe = self.redis_client.pipeline()
                pipe.hset(redis_key, mapping=serialized_data)
                pipe.expire(redis_key, ttl)
                
                await pipe.execute()
                
                logger.debug(f"L2 cache set for key '{key}' with TTL {ttl}s")
                return True
                
            except Exception as e:
                logger.warning(f"Redis set attempt {attempt + 1} failed for key '{key}': {e}")
                self.stats['errors'] += 1
                
                if attempt < self.retry_attempts - 1:
                    await asyncio.sleep(self.retry_delay * (2 ** attempt))
        
        return False

    async def delete(self, key: str) -> bool:
        """Delete cache entry from Redis.
        
        Parameters
        ----------
        key : str
            Cache key to delete
            
        Returns
        -------
        bool
            True if key was deleted
        """
        if not self._is_connected:
            return False
        
        self.stats['deletes'] += 1
        redis_key = self._make_redis_key(key)
        
        try:
            result = await self.redis_client.delete(redis_key)
            
            # Publish invalidation message
            if self.enable_pubsub:
                await self._publish_invalidation(key)
            
            logger.debug(f"L2 cache delete for key '{key}' (existed: {result > 0})")
            return result > 0
            
        except Exception as e:
            logger.warning(f"Redis delete failed for key '{key}': {e}")
            self.stats['errors'] += 1
            return False

    async def exists(self, key: str) -> bool:
        """Check if key exists in Redis cache."""
        if not self._is_connected:
            return False
        
        redis_key = self._make_redis_key(key)
        
        try:
            return await self.redis_client.exists(redis_key) > 0
        except Exception as e:
            logger.warning(f"Redis exists check failed for key '{key}': {e}")
            self.stats['errors'] += 1
            return False

    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching a pattern.
        
        Parameters
        ----------
        pattern : str
            Key pattern (supports wildcards)
            
        Returns
        -------
        int
            Number of keys deleted
        """
        if not self._is_connected:
            return 0
        
        redis_pattern = self._make_redis_key(pattern)
        
        try:
            keys = await self.redis_client.keys(redis_pattern)
            if keys:
                deleted = await self.redis_client.delete(*keys)
                logger.info(f"Cleared {deleted} keys matching pattern '{pattern}'")
                return deleted
            return 0
            
        except Exception as e:
            logger.error(f"Failed to clear pattern '{pattern}': {e}")
            self.stats['errors'] += 1
            return 0

    def _make_redis_key(self, key: str) -> str:
        """Create Redis key with prefix."""
        return f"{self.key_prefix}{key}"

    async def _serialize_entry(self, entry: CacheEntry) -> Optional[Dict[str, bytes]]:
        """Serialize cache entry for Redis storage."""
        try:
            # Convert entry to dict
            entry_dict = asdict(entry)
            
            # Serialize value based on method
            if self.serialization_method == SerializationMethod.JSON:
                value_data = json.dumps(entry.value).encode('utf-8')
            elif self.serialization_method == SerializationMethod.PICKLE:
                value_data = pickle.dumps(entry.value)
            elif self.serialization_method == SerializationMethod.COMPRESSED_PICKLE:
                pickled_data = pickle.dumps(entry.value)
                if len(pickled_data) > self.compression_threshold:
                    value_data = gzip.compress(pickled_data)
                    entry_dict['compressed'] = True
                    self.stats['compression_saves'] += 1
                else:
                    value_data = pickled_data
                    entry_dict['compressed'] = False
            else:
                raise ValueError(f"Unknown serialization method: {self.serialization_method}")
            
            # Remove value from dict and serialize metadata
            del entry_dict['value']
            metadata = json.dumps(entry_dict).encode('utf-8')
            
            return {
                'metadata': metadata,
                'value': value_data,
                'serialization_method': self.serialization_method.encode('utf-8')
            }
            
        except Exception as e:
            logger.error(f"Serialization failed: {e}")
            self.stats['serialization_errors'] += 1
            return None

    async def _deserialize_entry(self, data: Dict[bytes, bytes]) -> Optional[CacheEntry]:
        """Deserialize cache entry from Redis data."""
        try:
            # Get serialization method
            serialization_method = data.get(b'serialization_method', b'').decode('utf-8')
            
            # Deserialize metadata
            metadata_bytes = data.get(b'metadata', b'{}')
            metadata = json.loads(metadata_bytes.decode('utf-8'))
            
            # Deserialize value
            value_data = data.get(b'value', b'')
            
            if serialization_method == SerializationMethod.JSON:
                value = json.loads(value_data.decode('utf-8'))
            elif serialization_method == SerializationMethod.PICKLE:
                value = pickle.loads(value_data)
            elif serialization_method == SerializationMethod.COMPRESSED_PICKLE:
                if metadata.get('compressed', False):
                    decompressed_data = gzip.decompress(value_data)
                    value = pickle.loads(decompressed_data)
                else:
                    value = pickle.loads(value_data)
            else:
                logger.warning(f"Unknown serialization method: {serialization_method}")
                return None
            
            # Reconstruct cache entry
            metadata['value'] = value
            
            # Convert quality back to enum
            if 'quality' in metadata:
                metadata['quality'] = DataQuality(metadata['quality'])
            
            return CacheEntry(**metadata)
            
        except Exception as e:
            logger.error(f"Deserialization failed: {e}")
            self.stats['serialization_errors'] += 1
            return None

    async def _setup_pubsub(self) -> None:
        """Setup Redis pub/sub for distributed cache invalidation."""
        try:
            pubsub = self.pubsub_client.pubsub()
            await pubsub.subscribe(f"{self.key_prefix}invalidation")
            
            self.pubsub_task = asyncio.create_task(self._pubsub_listener(pubsub))
            logger.info("Redis pub/sub setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup pub/sub: {e}")
            self.stats['errors'] += 1

    async def _pubsub_listener(self, pubsub) -> None:
        """Listen for pub/sub invalidation messages."""
        try:
            async for message in pubsub.listen():
                if message['type'] == 'message':
                    try:
                        key = message['data'].decode('utf-8')
                        self.stats['pubsub_messages'] += 1
                        
                        # Notify callbacks
                        for callback in self.invalidation_callbacks:
                            try:
                                callback(key)
                            except Exception as e:
                                logger.warning(f"Invalidation callback failed: {e}")
                        
                        logger.debug(f"Processed invalidation message for key: {key}")
                        
                    except Exception as e:
                        logger.warning(f"Failed to process pub/sub message: {e}")
                        
        except asyncio.CancelledError:
            await pubsub.unsubscribe()
            await pubsub.close()
        except Exception as e:
            logger.error(f"Pub/sub listener error: {e}")
            self.stats['errors'] += 1

    async def _publish_invalidation(self, key: str) -> None:
        """Publish cache invalidation message."""
        try:
            await self.pubsub_client.publish(f"{self.key_prefix}invalidation", key)
        except Exception as e:
            logger.warning(f"Failed to publish invalidation for key '{key}': {e}")

    def add_invalidation_callback(self, callback: Callable[[str], None]) -> None:
        """Add callback for distributed cache invalidation."""
        self.invalidation_callbacks.append(callback)

    def remove_invalidation_callback(self, callback: Callable[[str], None]) -> None:
        """Remove invalidation callback."""
        if callback in self.invalidation_callbacks:
            self.invalidation_callbacks.remove(callback)

    async def get_info(self) -> Dict[str, Any]:
        """Get Redis server information."""
        if not self._is_connected:
            return {}
        
        try:
            info = await self.redis_client.info()
            return {
                'redis_version': info.get('redis_version'),
                'used_memory': info.get('used_memory'),
                'used_memory_human': info.get('used_memory_human'),
                'connected_clients': info.get('connected_clients'),
                'total_commands_processed': info.get('total_commands_processed'),
                'keyspace_hits': info.get('keyspace_hits'),
                'keyspace_misses': info.get('keyspace_misses'),
            }
        except Exception as e:
            logger.error(f"Failed to get Redis info: {e}")
            return {}

    def get_stats(self) -> Dict[str, Any]:
        """Get L2 cache statistics."""
        hit_rate = self.stats['hits'] / max(1, self.stats['hits'] + self.stats['misses'])
        
        return {
            'connected': self._is_connected,
            'hit_rate': hit_rate,
            'stats': self.stats,
            'serialization_method': self.serialization_method,
            'compression_threshold': self.compression_threshold,
            'pubsub_enabled': self.enable_pubsub,
            'invalidation_callbacks': len(self.invalidation_callbacks)
        }
