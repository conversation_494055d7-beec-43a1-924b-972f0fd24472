#!/usr/bin/env python3
"""
Demo do Grid Search QUALIA com Dados Reais - Etapa C
YAA IMPLEMENTATION: Demonstração do sistema de grid search usando dados históricos reais.
"""

import asyncio
import sys
from pathlib import Path

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

from src.qualia.backtest.hyperparams_grid_search import (
    GridSearchParams,
    HyperParamsGridSearch,
)
from src.qualia.utils.logger import get_logger

logger = get_logger(__name__)


async def demo_grid_search_real_data():
    """Demonstra grid search com dados reais."""
    
    print("🚀 QUALIA Grid Search com Dados Reais - Etapa C")
    print("=" * 60)
    
    # Opções de demonstração
    print("\nEscolha o tipo de grid search:")
    print("1. Grid Search Pequeno (64 combinações) - Rápido")
    print("2. Grid Search Médio (150 combinações) - Balanceado") 
    print("3. Grid Search Completo (600 combinações) - Completo")
    print("4. Grid Search Personalizado")
    
    choice = input("\nDigite sua escolha (1-4): ").strip()
    
    if choice == "1":
        # Grid pequeno para teste rápido
        params = GridSearchParams(
            price_amp_range=(2.0, 8.0, 4),      # 4 valores: 2.0, 4.0, 6.0, 8.0
            news_amp_range=(1.5, 6.5, 4),       # 4 valores: 1.5, 3.5, 5.5, 6.5
            min_conf_range=(0.4, 0.8, 4),       # 4 valores: 0.4, 0.53, 0.67, 0.8
            symbols=["BTC/USDT", "ETH/USDT"],
            backtest_days=60,                    # 60 dias para teste rápido
            max_workers=1,                       # Sequencial para estabilidade
            min_trades_required=5,
            max_drawdown_limit=0.6
        )
        print(f"\n📊 Configuração: Grid Pequeno (64 combinações)")
        
    elif choice == "2":
        # Grid médio balanceado
        params = GridSearchParams(
            price_amp_range=(1.0, 10.0, 5),     # 5 valores: 1.0, 3.25, 5.5, 7.75, 10.0
            news_amp_range=(1.0, 10.0, 5),      # 5 valores
            min_conf_range=(0.3, 0.8, 6),       # 6 valores: 0.3 a 0.8
            symbols=["BTC/USDT", "ETH/USDT"],
            backtest_days=90,                    # 90 dias padrão
            max_workers=1,                       # Sequencial
            min_trades_required=10,
            max_drawdown_limit=0.5
        )
        print(f"\n📊 Configuração: Grid Médio (150 combinações)")
        
    elif choice == "3":
        # Grid completo
        params = GridSearchParams(
            price_amp_range=(1.0, 10.0, 10),    # 10 valores
            news_amp_range=(1.0, 10.0, 10),     # 10 valores  
            min_conf_range=(0.3, 0.8, 6),       # 6 valores
            symbols=["BTC/USDT", "ETH/USDT"],
            backtest_days=120,                   # 120 dias para robustez
            max_workers=1,                       # Sequencial
            min_trades_required=15,
            max_drawdown_limit=0.5
        )
        print(f"\n📊 Configuração: Grid Completo (600 combinações)")
        
    elif choice == "4":
        # Grid personalizado
        print("\n🔧 Configuração Personalizada:")
        
        price_min = float(input("Price Amplification Min (1.0-10.0): ") or "2.0")
        price_max = float(input("Price Amplification Max (1.0-10.0): ") or "8.0")
        price_steps = int(input("Price Amplification Steps (2-10): ") or "4")
        
        news_min = float(input("News Amplification Min (1.0-10.0): ") or "1.5")
        news_max = float(input("News Amplification Max (1.0-10.0): ") or "6.5")
        news_steps = int(input("News Amplification Steps (2-10): ") or "4")
        
        conf_min = float(input("Min Confidence Min (0.1-0.9): ") or "0.4")
        conf_max = float(input("Min Confidence Max (0.1-0.9): ") or "0.8")
        conf_steps = int(input("Min Confidence Steps (2-10): ") or "4")
        
        days = int(input("Backtest Days (30-180): ") or "90")
        
        params = GridSearchParams(
            price_amp_range=(price_min, price_max, price_steps),
            news_amp_range=(news_min, news_max, news_steps),
            min_conf_range=(conf_min, conf_max, conf_steps),
            symbols=["BTC/USDT", "ETH/USDT"],
            backtest_days=days,
            max_workers=1,
            min_trades_required=10,
            max_drawdown_limit=0.5
        )
        
        total_combinations = price_steps * news_steps * conf_steps
        print(f"\n📊 Configuração Personalizada ({total_combinations} combinações)")
        
    else:
        print("❌ Escolha inválida. Usando configuração padrão.")
        params = GridSearchParams()
    
    # Confirma execução
    total_combinations = (
        params.price_amp_range[2] * 
        params.news_amp_range[2] * 
        params.min_conf_range[2]
    )
    
    print(f"\n📋 Resumo da Configuração:")
    print(f"   • Símbolos: {', '.join(params.symbols)}")
    print(f"   • Período: {params.backtest_days} dias")
    print(f"   • Combinações: {total_combinations}")
    print(f"   • Execução: {'Paralela' if params.max_workers > 1 else 'Sequencial'}")
    print(f"   • Min Trades: {params.min_trades_required}")
    print(f"   • Max Drawdown: {params.max_drawdown_limit:.1%}")
    
    confirm = input(f"\n🚀 Executar grid search com DADOS REAIS? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes', 's', 'sim']:
        print("❌ Execução cancelada.")
        return
    
    # Executa grid search
    print(f"\n🔄 Iniciando execução...")
    print("⚠️  IMPORTANTE: Usando dados históricos REAIS da KuCoin")
    print("⏱️  Tempo estimado: 2-10 minutos dependendo da configuração")
    
    try:
        # Cria e executa grid search
        grid_search = HyperParamsGridSearch(params)
        results = await grid_search.run_grid_search()
        
        # Exibe resultados
        print_results_summary(results)
        
        # Salva resultados
        output_dir = Path("results/grid_search_real_data")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = asyncio.get_event_loop().time()
        output_file = output_dir / f"grid_search_real_{int(timestamp)}.json"
        
        grid_search.save_results(results, str(output_file))
        print(f"\n💾 Resultados salvos em: {output_file}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Erro durante execução: {e}")
        print(f"❌ Erro: {e}")
        return None


def print_results_summary(results):
    """Imprime resumo dos resultados."""
    print(f"\n" + "="*60)
    print(f"📊 RESULTADOS DO GRID SEARCH COM DADOS REAIS")
    print(f"="*60)
    
    print(f"\n📈 Estatísticas Gerais:")
    print(f"   • Total de Combinações: {results.total_combinations}")
    print(f"   • Backtests Bem-sucedidos: {results.successful_backtests}")
    print(f"   • Backtests Falharam: {results.failed_backtests}")
    print(f"   • Taxa de Sucesso: {results.successful_backtests/results.total_combinations:.1%}")
    print(f"   • Tempo Total: {results.total_execution_time:.1f}s")
    
    if results.best_sharpe:
        print(f"\n🏆 MELHOR SHARPE RATIO: {results.best_sharpe.sharpe_ratio:.3f}")
        print(f"   • Parâmetros: price_amp={results.best_sharpe.price_amplification:.1f}, "
              f"news_amp={results.best_sharpe.news_amplification:.1f}, "
              f"min_conf={results.best_sharpe.min_confidence:.2f}")
        print(f"   • Return: {results.best_sharpe.total_return_pct:.2%}")
        print(f"   • Max Drawdown: {results.best_sharpe.max_drawdown_pct:.2%}")
        print(f"   • Win Rate: {results.best_sharpe.win_rate:.2%}")
        print(f"   • Total Trades: {results.best_sharpe.total_trades}")
    
    if results.best_return:
        print(f"\n💰 MELHOR RETORNO: {results.best_return.total_return_pct:.2%}")
        print(f"   • Parâmetros: price_amp={results.best_return.price_amplification:.1f}, "
              f"news_amp={results.best_return.news_amplification:.1f}, "
              f"min_conf={results.best_return.min_confidence:.2f}")
        print(f"   • Sharpe: {results.best_return.sharpe_ratio:.3f}")
        print(f"   • Max Drawdown: {results.best_return.max_drawdown_pct:.2%}")
    
    if results.best_drawdown:
        print(f"\n🛡️ MENOR DRAWDOWN: {results.best_drawdown.max_drawdown_pct:.2%}")
        print(f"   • Parâmetros: price_amp={results.best_drawdown.price_amplification:.1f}, "
              f"news_amp={results.best_drawdown.news_amplification:.1f}, "
              f"min_conf={results.best_drawdown.min_confidence:.2f}")
        print(f"   • Return: {results.best_drawdown.total_return_pct:.2%}")
        print(f"   • Sharpe: {results.best_drawdown.sharpe_ratio:.3f}")
    
    print(f"\n✅ Grid Search com dados reais concluído com sucesso!")


if __name__ == "__main__":
    asyncio.run(demo_grid_search_real_data())
