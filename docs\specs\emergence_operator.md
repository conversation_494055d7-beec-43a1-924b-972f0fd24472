# Emergence Operator

O operador de *Emergence* busca padrões complexos que surgem da interação entre componentes simples. Usa buffers temporais para analisar subsequências recorrentes, correlações e cascatas de eventos.

Principais características
-------------------------
- Detecção de padrões com base em similaridade de Hamming e cosseno.
- Métricas de complexidade e persistência para ranquear padrões emergentes.
- Registro de interações entre padrões e manutenção de histórico de estados.
- `average_pattern_persistence` em `qualia.core.emergence` retorna a
  persistência média dos padrões registrados.
- <PERSON><PERSON> `apply_emergence` que realiza amplificação linear simples usada em testes unitários.

## Exemplo de Uso
```python
import numpy as np
from qualia.core.emergence import EmergenceOperator

config = {"complexity_threshold": 0.6}
operator = EmergenceOperator(config)

components = np.random.rand(5, 3)
state = asyncio.run(operator.detect_emergence(components, timestamp=0.0))
print(len(state.active_patterns))
```
