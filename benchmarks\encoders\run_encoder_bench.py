import time
import numpy as np
import sys
import os
import gc
import psutil
import click
from importlib import import_module
from typing import List, Dict, Any, Callable, Type, Union
import json

# Caminho para fixtures
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
src_path = os.path.join(project_root, "src")
fixtures_path = os.path.join(project_root, "fixtures")

from qualia.core.encoders import (
    OBIEncoder,
    PriceMomentumEncoder,
    VolatilityEncoder,
    LiquidityVelocityEncoder,
    FundingRateDeviationEncoder,
    QuantumEncoder,
    OrderBookSnapshot,
    LiquidityVelocityData,
    FundingRateDeviationData,
    PriceMomentumData,
    VolatilityData,
)

# --- Configurações do Benchmark ---
NUM_ITEMS_FOR_SLA = 1000
SLA_TOTAL_TIME_SECONDS = 0.5  # SLA: <0.5s para NUM_ITEMS_FOR_SLA
N_REPETITIONS = 5


# --- <PERSON><PERSON> de Teste (Geradores) ---
def generate_obi_snapshot_data(num_items: int) -> List[OrderBookSnapshot]:
    data: List[OrderBookSnapshot] = []
    for i in range(num_items):
        data.append(
            {
                "bids": [(100.0 + i * 0.01, 10.0), (99.0 - i * 0.01, 5.0)],
                "asks": [(101.0 + i * 0.01, 8.0), (102.0 - i * 0.01, 6.0)],
                "timestamp": time.time() * 1000 + i,
            }
        )
    return data


def generate_lv_snapshot_data(num_items: int) -> List[LiquidityVelocityData]:
    data: List[LiquidityVelocityData] = []
    for i in range(num_items):
        data.append(
            {"lv_metric": np.random.rand(), "timestamp": time.time() * 1000 + i}
        )
    return data


def generate_frd_snapshot_data(num_items: int) -> List[FundingRateDeviationData]:
    data: List[FundingRateDeviationData] = []
    for i in range(num_items):
        data.append(
            {
                "frd_metric": (np.random.rand() - 0.5) * 0.002,
                "timestamp": time.time() * 1000 + i,
            }
        )
    return data


def generate_price_momentum_data(num_items: int) -> List[PriceMomentumData]:
    data: List[PriceMomentumData] = []
    for i in range(num_items):
        data.append(
            {
                "price_percentage_change": (np.random.rand() - 0.5) * 0.02,
                "timestamp": time.time() * 1000 + i,
            }
        )
    return data


def generate_volatility_data(num_items: int) -> List[VolatilityData]:
    data: List[VolatilityData] = []
    for i in range(num_items):
        data.append(
            {
                "volatility_metric": np.random.rand() * 0.1,
                "timestamp": time.time() * 1000 + i,
            }
        )
    return data


ENCODERS_TO_BENCHMARK: List[Dict[str, Any]] = [
    {
        "name": "OBIEncoder",
        "class": OBIEncoder,
        "data_generator": generate_obi_snapshot_data,
        "params": {},
    },
    {
        "name": "LiquidityVelocityEncoder",
        "class": LiquidityVelocityEncoder,
        "data_generator": generate_lv_snapshot_data,
        "params": {},
    },
    {
        "name": "FundingRateDeviationEncoder",
        "class": FundingRateDeviationEncoder,
        "data_generator": generate_frd_snapshot_data,
        "params": {"scaling_factor": 10.0},
    },
    {
        "name": "PriceMomentumEncoder",
        "class": PriceMomentumEncoder,
        "data_generator": generate_price_momentum_data,
        "params": {"scaling_factor": 1.0},
    },
    {
        "name": "VolatilityEncoder",
        "class": VolatilityEncoder,
        "data_generator": generate_volatility_data,
        "params": {"scaling_factor": 10.0},
    },
]


def _get_encoder_instance(
    encoder_path_or_class: Union[str, Type[QuantumEncoder]],
    params: Dict[str, Any],
) -> QuantumEncoder:
    if isinstance(encoder_path_or_class, str):
        # Assume que o path é relativo ao módulo 'qualia.core.encoders' se não for completo
        if not "." in encoder_path_or_class:  # Se for só o nome da classe
            cls_name = encoder_path_or_class
            module_path = "qualia.core.encoders"
        elif encoder_path_or_class.startswith("qualia."):  # Path completo já
            module_path, cls_name = encoder_path_or_class.rsplit(".", 1)
        else:  # Tentar como path relativo a src
            module_path, cls_name = encoder_path_or_class.rsplit(".", 1)

        try:
            EncoderCls = getattr(import_module(module_path), cls_name)
        except ImportError:  # Tentar caminho completo se falhar
            module_path, cls_name = (
                ("qualia.core.encoders." + encoder_path_or_class).rsplit(".", 1)
                if not encoder_path_or_class.startswith("qualia.")
                else encoder_path_or_class.rsplit(".", 1)
            )
            EncoderCls = getattr(import_module(module_path), cls_name)

        return EncoderCls(**params)
    elif isinstance(encoder_path_or_class, type) and issubclass(
        encoder_path_or_class, QuantumEncoder
    ):
        return encoder_path_or_class(**params)
    else:
        raise ValueError(f"Formato de encoder inválido: {encoder_path_or_class}")


def perform_single_encoder_benchmark(
    encoder_name: str,
    encoder_class_or_path: Union[str, Type[QuantumEncoder]],
    data_source: Union[Callable[[int], List[Dict[str, Any]]], str],
    num_items: int,
    repetitions: int,
    encoder_params: Dict[str, Any],
) -> Dict[str, Any]:
    print(f"\n--- Benchmarking {encoder_name} ---")
    try:
        encoder = _get_encoder_instance(encoder_class_or_path, encoder_params)
    except Exception as e:
        print(f"Erro ao instanciar {encoder_name}: {e}")
        return {"encoder": encoder_name, "error": str(e), "passed_sla": False}

    batch_data: List[Dict[str, Any]] = []
    if callable(data_source):
        print(f"Gerando {num_items} itens de dados sintéticos...")
        batch_data = data_source(num_items)
    elif isinstance(data_source, str):  # Path da fixture
        fixture_file_path = os.path.join(fixtures_path, os.path.basename(data_source))
        if not os.path.exists(
            fixture_file_path
        ):  # Tenta no diretório atual se não achar em fixtures/
            fixture_file_path = data_source

        print(f"Carregando dados de {fixture_file_path}...")
        try:
            loaded_data = np.load(fixture_file_path, allow_pickle=True)
            if not isinstance(loaded_data, np.ndarray) or loaded_data.ndim == 0:
                raise ValueError("Fixture não é um array numpy ou está vazia.")

            # Checa se o array carregado é de objetos (dicts)
            if (
                loaded_data.dtype == "object"
                and len(loaded_data) > 0
                and isinstance(loaded_data[0], dict)
            ):
                if len(loaded_data) >= num_items:
                    batch_data = list(loaded_data[:num_items])
                else:
                    raise ValueError(
                        f"Dados insuficientes na fixture: {len(loaded_data)} encontrados, {num_items} necessários."
                    )
            else:
                raise ValueError(
                    f"Formato de dados da fixture não é uma lista de dicionários. dtype: {loaded_data.dtype}"
                )

        except FileNotFoundError:
            print(f"Erro: Arquivo de fixture '{fixture_file_path}' não encontrado.")
            return {
                "encoder": encoder_name,
                "error": "Fixture not found",
                "passed_sla": False,
            }
        except Exception as e:
            print(f"Erro ao carregar ou processar fixture '{fixture_file_path}': {e}")
            return {
                "encoder": encoder_name,
                "error": f"Fixture error: {e}",
                "passed_sla": False,
            }
    else:
        raise ValueError("data_source inválido.")

    if not batch_data or len(batch_data) < num_items:
        print(
            f"Erro: Não foi possível gerar ou carregar dados suficientes para {encoder_name}. Esperado: {num_items}, Obtido: {len(batch_data if batch_data else [])}"
        )
        return {
            "encoder": encoder_name,
            "error": "Data generation/loading failed",
            "passed_sla": False,
        }

    latencies_s = []
    mem_increments_bytes = []
    proc = psutil.Process(os.getpid())

    for rep in range(repetitions):
        gc.collect()
        mem_before_bytes = proc.memory_info().rss
        time_start_s = time.perf_counter()
        encoded_output = encoder.encode(batch_data)
        time_end_s = time.perf_counter()
        mem_after_bytes = proc.memory_info().rss

        expected_output_dim = getattr(
            encoder, "output_dim", 2
        )  # Default para 2 se não definido
        if (
            not isinstance(encoded_output, np.ndarray)
            or (encoded_output.ndim == 2 and encoded_output.shape[0] != len(batch_data))
            or (
                encoded_output.ndim == 1
                and len(batch_data) == 1
                and encoded_output.shape[0] != expected_output_dim
                and encoded_output.size != 0
            )
            or (len(batch_data) == 0 and encoded_output.size != 0)
        ):  # size !=0 para encoded_output.shape = (0,)
            print(
                f"  Rep {rep+1}/{repetitions}: Aviso! Output do encode tem formato inesperado. Shape: {encoded_output.shape if isinstance(encoded_output, np.ndarray) else type(encoded_output)}. Input size: {len(batch_data)}"
            )

        total_latency_s = time_end_s - time_start_s
        latencies_s.append(total_latency_s)
        mem_increment = mem_after_bytes - mem_before_bytes
        mem_increments_bytes.append(mem_increment)
        print(
            f"  Rep {rep+1}/{repetitions}: Latency: {total_latency_s:.4f} s, Mem Increment: {mem_increment / (1024**2):.2f} MB"
        )

    mean_latency_s = float(np.mean(latencies_s)) if latencies_s else 0.0
    p95_latency_s = float(np.percentile(latencies_s, 95)) if latencies_s else 0.0
    mean_mem_increment_bytes = (
        float(np.mean(mem_increments_bytes)) if mem_increments_bytes else 0.0
    )

    sla_check_latency = mean_latency_s < SLA_TOTAL_TIME_SECONDS
    print(f"  Resultados para {encoder_name} ({num_items} items):")
    print(f"    Latência Média Total: {mean_latency_s:.4f} s")
    print(f"    Latência P95 Total:   {p95_latency_s:.4f} s")
    print(
        f"    Incremento Médio de Memória: {mean_mem_increment_bytes / (1024**2):.2f} MB"
    )
    print(
        f"    SLA de Latência (< {SLA_TOTAL_TIME_SECONDS:.3f}s): {'PASSOU' if sla_check_latency else 'FALHOU'}"
    )

    return {
        "encoder": encoder_name,
        "num_items": num_items,
        "repetitions": repetitions,
        "mean_latency_s": mean_latency_s,
        "p95_latency_s": p95_latency_s,
        "mean_mem_increment_mb": mean_mem_increment_bytes / (1024**2),
        "sla_target_s": SLA_TOTAL_TIME_SECONDS,
        "passed_sla": sla_check_latency,
        "error": None,
    }


@click.command()
@click.option(
    "--encoder",
    "encoder_path_cli",
    default=None,
    help="Nome da classe do Encoder (ex: OBIEncoder) ou caminho pontilhado.",
)
@click.option(
    "--all",
    "test_all",
    is_flag=True,
    help="Testar todos os encoders definidos em ENCODERS_TO_BENCHMARK.",
)
@click.option(
    "--fixture",
    "fixture_param",
    default="order_book_snapshots.npy",
    help="Nome do arquivo de fixture .npy (procurado em ./fixtures/ e no diretório atual).",
)
@click.option(
    "--num-items",
    default=NUM_ITEMS_FOR_SLA,
    show_default=True,
    help="Número de itens para processar no benchmark.",
)
@click.option(
    "--repetitions",
    default=N_REPETITIONS,
    show_default=True,
    help="Número de repetições para o benchmark.",
)
@click.option(
    "--output-file",
    default=None,
    type=str,
    help="Arquivo JSON para salvar os resultados do benchmark.",
)
def main(
    encoder_path_cli: str,
    test_all: bool,
    fixture_param: str,
    num_items: int,
    repetitions: int,
    output_file: str,
) -> None:
    """Executa benchmarks de performance para QuantumEncoders."""
    overall_pass = True
    results_summary = []

    # Define o path base da fixture que é ./fixtures/order_book_snapshots.npy
    # O fixture_param pode ser apenas o nome do arquivo.
    default_fixture_full_path = os.path.join(fixtures_path, fixture_param)

    if test_all:
        print(
            f"Executando benchmarks para TODOS os encoders ({num_items} itens, {repetitions} repetições)."
        )
        for config in ENCODERS_TO_BENCHMARK:
            data_src_all: Any = config["data_generator"]
            current_encoder_name_all = config["name"]
            encoder_params_all = config.get("params", {})

            if current_encoder_name_all == "OBIEncoder":
                print(
                    f"Para {current_encoder_name_all}, tentando usar fixture: {default_fixture_full_path}"
                )
                # Se a fixture padrão (order_book_snapshots.npy) existir, use-a para OBIEncoder.
                # Senão, o data_source continua sendo o gerador.
                if os.path.exists(default_fixture_full_path):
                    data_src_all = default_fixture_full_path
                else:
                    print(
                        f"Fixture {default_fixture_full_path} não encontrada para {current_encoder_name_all}, usando gerador de dados."
                    )

            result = perform_single_encoder_benchmark(
                encoder_name=current_encoder_name_all,
                encoder_class_or_path=config["class"],
                data_source=data_src_all,
                num_items=num_items,
                repetitions=repetitions,
                encoder_params=encoder_params_all,
            )
            results_summary.append(result)
            if not result.get("passed_sla", False) or result.get("error") is not None:
                overall_pass = False
    elif encoder_path_cli:
        print(
            f"Executando benchmark para {encoder_path_cli} ({num_items} itens, {repetitions} repetições)."
        )

        # Tenta encontrar na lista pré-definida para obter classe, gerador e params
        found_config = next(
            (
                e
                for e in ENCODERS_TO_BENCHMARK
                if e["name"] == encoder_path_cli
                or e["class"].__name__ == encoder_path_cli
                or f"qualia.core.encoders.{e['name']}" == encoder_path_cli
            ),
            None,
        )

        encoder_class_single: Any
        data_src_single: Any
        encoder_params_single = {}

        if found_config:
            encoder_class_single = found_config["class"]
            encoder_params_single = found_config.get("params", {})
            if found_config["name"] == "OBIEncoder":
                print(
                    f"Para {encoder_path_cli} (OBIEncoder), tentando usar fixture: {default_fixture_full_path}"
                )
                if os.path.exists(default_fixture_full_path):
                    data_src_single = default_fixture_full_path
                else:
                    print(
                        f"Fixture {default_fixture_full_path} não encontrada para OBIEncoder, usando gerador de dados."
                    )
                    data_src_single = found_config["data_generator"]
            else:
                data_src_single = found_config["data_generator"]
        elif (
            isinstance(encoder_path_cli, str) and "." in encoder_path_cli
        ):  # Se for um path completo não listado, tenta importar
            encoder_class_single = encoder_path_cli
            # Para encoders não listados, não temos gerador padrão, então é preciso uma fixture.
            # A fixture_param aqui será usada.
            print(
                f"Para encoder não listado {encoder_path_cli}, usando fixture especificada: {default_fixture_full_path}"
            )
            data_src_single = default_fixture_full_path
            if not os.path.exists(default_fixture_full_path):
                print(
                    f"Erro: Fixture {default_fixture_full_path} não encontrada para encoder {encoder_path_cli}"
                )
                sys.exit(1)
        else:
            print(
                f"Erro: Encoder '{encoder_path_cli}' não reconhecido na lista interna e não é um caminho completo válido, ou falta fixture."
            )
            sys.exit(1)

        result = perform_single_encoder_benchmark(
            encoder_name=encoder_path_cli,  # Usa o nome/path fornecido pelo usuário
            encoder_class_or_path=encoder_class_single,
            data_source=data_src_single,
            num_items=num_items,
            repetitions=repetitions,
            encoder_params=encoder_params_single,
        )
        results_summary.append(result)
        if not result.get("passed_sla", False) or result.get("error") is not None:
            overall_pass = False
    else:
        print(
            "Erro: Especifique --encoder NOME_CLASSE ou --encoder qualia.core.encoders.NOME_CLASSE OU --all."
        )
        sys.exit(1)

    if output_file:
        # Garante que o diretório de output exista
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(
            output_dir
        ):  # Verifica se output_dir não é uma string vazia
            os.makedirs(output_dir, exist_ok=True)

        with open(output_file, "w") as f:
            json.dump(results_summary, f, indent=2)
        print(f"\nResultados do benchmark salvos em: {output_file}")

    if not overall_pass:
        print("\nAlguns benchmarks FALHARAM o SLA de performance ou encontraram erros.")
        sys.exit(1)
    else:
        print(
            "\nTodos os benchmarks executados passaram no SLA de performance e não tiveram erros."
        )
        sys.exit(0)


if __name__ == "__main__":
    main()
