"""
Performance Validation Integration for QUALIA Trading System.

This module integrates the Real-Time Performance Validator with the existing
QUALIA trading infrastructure, providing seamless performance monitoring.
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path

from ..utils.logger import get_logger
from .realtime_performance_validator import (
    RealTimePerformanceValidator, 
    PerformanceAlert, 
    PerformanceSnapshot,
    create_performance_validator
)
from ..monitoring.performance_metrics import PerformanceMetricsCollector
from ..monitoring.integrated_monitoring_system import IntegratedMonitoringSystem

logger = get_logger(__name__)


class PerformanceValidationIntegration:
    """
    Integration layer for performance validation in QUALIA trading system.
    
    This class provides:
    - Seamless integration with existing trading loops
    - Automatic trade and position tracking
    - Performance alert handling
    - Parameter adjustment recommendations
    - Integration with monitoring dashboard
    """
    
    def __init__(self, 
                 config_path: Optional[str] = None,
                 monitoring_system: Optional[IntegratedMonitoringSystem] = None):
        self.config = self._load_config(config_path)
        self.monitoring_system = monitoring_system
        
        # Initialize performance validator
        self.validator = create_performance_validator(
            min_sharpe_ratio=self.config.get('min_sharpe_ratio', 3.0),
            max_drawdown_pct=self.config.get('max_drawdown_pct', 15.0),
            min_win_rate=self.config.get('min_win_rate', 45.0),
            performance_window_hours=self.config.get('performance_window_hours', 24)
        )
        
        # Integration state
        self.is_integrated = False
        self.trading_system = None
        self.execution_engine = None
        self.signal_generator = None
        
        # Performance tracking
        self.last_performance_report = None
        self.parameter_adjustment_history = []
        
        # Setup callbacks
        self._setup_callbacks()
        
        logger.info("[INTEGRATION] Performance Validation Integration initialized")
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """Load configuration for performance validation."""
        default_config = {
            "min_sharpe_ratio": 3.0,
            "max_drawdown_pct": 15.0,
            "min_win_rate": 45.0,
            "min_profit_factor": 1.2,
            "max_consecutive_losses": 5,
            "performance_window_hours": 24,
            "alert_cooldown_minutes": 15,
            "auto_parameter_adjustment": True,
            "emergency_stop_enabled": True,
            "emergency_stop_drawdown": 25.0,
            "monitoring_interval": 60.0,
            "report_interval_hours": 6
        }
        
        if config_path and Path(config_path).exists():
            try:
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
                logger.info(f"[INTEGRATION] Loaded config from {config_path}")
            except Exception as e:
                logger.warning(f"[INTEGRATION] Failed to load config: {e}, using defaults")
        
        return default_config
    
    def _setup_callbacks(self):
        """Setup performance validation callbacks."""
        # Alert callback
        self.validator.add_alert_callback(self._handle_performance_alert)
        
        # Performance update callback
        self.validator.add_performance_callback(self._handle_performance_update)
    
    async def integrate_with_trading_system(self, trading_system):
        """Integrate with QUALIA trading system."""
        self.trading_system = trading_system
        
        # Get references to key components
        if hasattr(trading_system, 'execution_engine'):
            self.execution_engine = trading_system.execution_engine
        
        if hasattr(trading_system, 'signal_generator'):
            self.signal_generator = trading_system.signal_generator
        
        # Hook into trading events
        await self._setup_trading_hooks()
        
        # Start performance monitoring
        await self.start_monitoring()
        
        self.is_integrated = True
        logger.info("[INTEGRATION] Successfully integrated with trading system")
    
    async def _setup_trading_hooks(self):
        """Setup hooks into trading system events."""
        # Hook into trade execution
        if self.execution_engine:
            original_execute_trade = getattr(self.execution_engine, 'execute_trade', None)
            if original_execute_trade:
                async def hooked_execute_trade(*args, **kwargs):
                    result = await original_execute_trade(*args, **kwargs)
                    if result:
                        await self._on_trade_executed(result)
                    return result
                
                setattr(self.execution_engine, 'execute_trade', hooked_execute_trade)
        
        # Hook into position updates
        if self.execution_engine:
            original_update_positions = getattr(self.execution_engine, '_monitor_positions', None)
            if original_update_positions:
                async def hooked_update_positions(*args, **kwargs):
                    result = await original_update_positions(*args, **kwargs)
                    await self._on_positions_updated()
                    return result
                
                setattr(self.execution_engine, '_monitor_positions', hooked_update_positions)
    
    async def start_monitoring(self):
        """Start performance monitoring."""
        monitoring_interval = self.config.get('monitoring_interval', 60.0)
        
        # Start validator monitoring
        monitoring_task = asyncio.create_task(
            self.validator.start_monitoring(monitoring_interval)
        )
        
        # Start periodic reporting
        reporting_task = asyncio.create_task(self._periodic_reporting())
        
        logger.info(f"[INTEGRATION] Performance monitoring started (interval: {monitoring_interval}s)")
        
        return monitoring_task, reporting_task
    
    async def _on_trade_executed(self, trade_result: Dict[str, Any]):
        """Handle trade execution event."""
        try:
            # Extract trade data
            trade_data = {
                'symbol': trade_result.get('symbol', 'UNKNOWN'),
                'side': trade_result.get('side', 'UNKNOWN'),
                'quantity': trade_result.get('quantity', 0),
                'entry_price': trade_result.get('entry_price', 0),
                'exit_price': trade_result.get('exit_price', 0),
                'realized_pnl': trade_result.get('realized_pnl', 0),
                'unrealized_pnl': trade_result.get('unrealized_pnl', 0),
                'entry_time': trade_result.get('entry_time', datetime.now()),
                'exit_time': trade_result.get('exit_time'),
                'timestamp': datetime.now()
            }
            
            # Update validator
            self.validator.update_trade(trade_data)
            
            logger.debug(f"[INTEGRATION] Trade tracked: {trade_data['symbol']} "
                        f"PnL: {trade_data['realized_pnl']:.2f}")
            
        except Exception as e:
            logger.error(f"[INTEGRATION] Error tracking trade: {e}")
    
    async def _on_positions_updated(self):
        """Handle position update event."""
        try:
            if self.execution_engine and hasattr(self.execution_engine, 'open_positions'):
                for position in self.execution_engine.open_positions:
                    position_data = {
                        'symbol': position.get('symbol', 'UNKNOWN'),
                        'side': position.get('side', 'UNKNOWN'),
                        'quantity': position.get('quantity', 0),
                        'entry_price': position.get('entry_price', 0),
                        'current_price': position.get('current_price', 0),
                        'unrealized_pnl': position.get('unrealized_pnl', 0),
                        'timestamp': datetime.now()
                    }
                    
                    self.validator.update_position(position_data)
            
        except Exception as e:
            logger.error(f"[INTEGRATION] Error updating positions: {e}")
    
    def _handle_performance_alert(self, alert: PerformanceAlert):
        """Handle performance alerts."""
        logger.warning(f"[INTEGRATION] PERFORMANCE ALERT: {alert.message}")
        
        # Emergency stop if enabled
        if (self.config.get('emergency_stop_enabled', True) and 
            alert.alert_type == 'drawdown_high' and 
            alert.current_value >= self.config.get('emergency_stop_drawdown', 25.0)):
            
            asyncio.create_task(self._emergency_stop())
        
        # Auto parameter adjustment if enabled
        if self.config.get('auto_parameter_adjustment', True):
            asyncio.create_task(self._suggest_parameter_adjustments(alert))
        
        # Integrate with monitoring system
        if self.monitoring_system:
            self.monitoring_system.handle_external_alert({
                'type': 'performance_validation',
                'severity': alert.severity,
                'message': alert.message,
                'timestamp': alert.timestamp.isoformat(),
                'suggested_action': alert.suggested_action
            })
    
    def _handle_performance_update(self, performance: PerformanceSnapshot):
        """Handle performance updates."""
        # Store latest performance
        self.last_performance_report = performance
        
        # Log significant changes
        if len(self.validator.performance_history) > 1:
            previous = self.validator.performance_history[-2]
            
            sharpe_change = performance.sharpe_ratio - previous.sharpe_ratio
            if abs(sharpe_change) > 0.5:
                logger.info(f"[INTEGRATION] Significant Sharpe change: "
                           f"{previous.sharpe_ratio:.3f} → {performance.sharpe_ratio:.3f} "
                           f"({sharpe_change:+.3f})")
    
    async def _emergency_stop(self):
        """Execute emergency stop procedure."""
        logger.critical("[INTEGRATION] EMERGENCY STOP TRIGGERED - High drawdown detected")
        
        try:
            # Stop trading system if possible
            if self.trading_system and hasattr(self.trading_system, 'stop'):
                await self.trading_system.stop()
            
            # Close all positions if possible
            if self.execution_engine and hasattr(self.execution_engine, 'close_all_positions'):
                await self.execution_engine.close_all_positions()
            
            logger.critical("[INTEGRATION] Emergency stop executed successfully")
            
        except Exception as e:
            logger.error(f"[INTEGRATION] Error during emergency stop: {e}")
    
    async def _suggest_parameter_adjustments(self, alert: PerformanceAlert):
        """Suggest parameter adjustments based on alert."""
        suggestions = []
        
        if alert.alert_type == 'sharpe_ratio_low':
            suggestions.extend([
                "Reduce position sizes by 20-30%",
                "Increase signal confidence threshold",
                "Review and tighten entry criteria",
                "Consider reducing trading frequency"
            ])
        
        elif alert.alert_type == 'drawdown_high':
            suggestions.extend([
                "Implement stricter stop-losses",
                "Reduce maximum position size",
                "Increase risk-off threshold",
                "Consider temporary trading pause"
            ])
        
        elif alert.alert_type == 'win_rate_low':
            suggestions.extend([
                "Review signal generation parameters",
                "Adjust entry timing criteria",
                "Optimize take-profit levels",
                "Consider market regime filters"
            ])
        
        elif alert.alert_type == 'consecutive_losses':
            suggestions.extend([
                "Pause trading temporarily",
                "Reduce position sizes significantly",
                "Review recent market conditions",
                "Consider strategy recalibration"
            ])
        
        # Log suggestions
        logger.info(f"[INTEGRATION] Parameter adjustment suggestions for {alert.alert_type}:")
        for i, suggestion in enumerate(suggestions, 1):
            logger.info(f"[INTEGRATION]   {i}. {suggestion}")
        
        # Store in history
        self.parameter_adjustment_history.append({
            'timestamp': datetime.now(),
            'alert_type': alert.alert_type,
            'suggestions': suggestions,
            'performance_snapshot': alert.performance_snapshot
        })
    
    async def _periodic_reporting(self):
        """Generate periodic performance reports."""
        report_interval = self.config.get('report_interval_hours', 6) * 3600  # Convert to seconds
        
        while self.validator.is_monitoring:
            try:
                await asyncio.sleep(report_interval)
                
                # Generate comprehensive report
                summary = self.validator.get_performance_summary()
                
                logger.info("[INTEGRATION] === PERFORMANCE REPORT ===")
                logger.info(f"[INTEGRATION] Sharpe Ratio: {summary['current_performance']['sharpe_ratio']:.3f}")
                logger.info(f"[INTEGRATION] Total Return: {summary['current_performance']['total_return_pct']:.2f}%")
                logger.info(f"[INTEGRATION] Max Drawdown: {summary['current_performance']['max_drawdown_pct']:.2f}%")
                logger.info(f"[INTEGRATION] Win Rate: {summary['current_performance']['win_rate']:.1f}%")
                logger.info(f"[INTEGRATION] Total Trades: {summary['current_performance']['total_trades']}")
                logger.info(f"[INTEGRATION] Active Alerts: {summary['alerts']['active_alerts']}")
                logger.info(f"[INTEGRATION] Backtest Deviation: {summary['backtest_comparison']['deviation_pct']:.1f}%")
                logger.info("[INTEGRATION] ========================")
                
            except Exception as e:
                logger.error(f"[INTEGRATION] Error in periodic reporting: {e}")
    
    def get_integration_status(self) -> Dict[str, Any]:
        """Get integration status and performance summary."""
        return {
            "integration_status": {
                "is_integrated": self.is_integrated,
                "trading_system_connected": self.trading_system is not None,
                "execution_engine_connected": self.execution_engine is not None,
                "monitoring_active": self.validator.is_monitoring
            },
            "performance_summary": self.validator.get_performance_summary(),
            "recent_adjustments": self.parameter_adjustment_history[-5:] if self.parameter_adjustment_history else [],
            "config": self.config
        }
    
    async def shutdown(self):
        """Shutdown performance validation integration."""
        logger.info("[INTEGRATION] Shutting down performance validation integration")
        
        # Stop validator monitoring
        self.validator.stop_monitoring()
        
        # Export final performance data
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        export_path = f"performance_data_{timestamp}.json"
        self.validator.export_performance_data(export_path)
        
        logger.info("[INTEGRATION] Performance validation integration shutdown complete")


# Factory function for easy integration
def create_performance_integration(
    config_path: Optional[str] = None,
    monitoring_system: Optional[IntegratedMonitoringSystem] = None
) -> PerformanceValidationIntegration:
    """Create a configured performance validation integration."""
    return PerformanceValidationIntegration(
        config_path=config_path,
        monitoring_system=monitoring_system
    )
