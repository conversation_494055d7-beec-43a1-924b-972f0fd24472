"""
QUALIA Data Normalizer - Normalização de dados de feeds para formato padrão.

Este módulo padroniza dados de diferentes exchanges para o formato interno do QUALIA,
garantindo consistência e compatibilidade com o resto do sistema.
"""

from __future__ import annotations

import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime
import numpy as np

from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class NormalizedTicker:
    """Ticker normalizado para formato QUALIA."""
    
    symbol: str
    price: float
    bid: float
    ask: float
    volume_24h: float
    change_24h: float
    change_24h_percent: float
    high_24h: float
    low_24h: float
    timestamp: float
    source: str
    raw_data: Optional[Dict[str, Any]] = None


@dataclass
class NormalizedOrderBook:
    """Order book normalizado para formato QUALIA."""
    
    symbol: str
    bids: List[List[float]]  # [[price, size], ...]
    asks: List[List[float]]  # [[price, size], ...]
    timestamp: float
    source: str
    raw_data: Optional[Dict[str, Any]] = None


@dataclass
class NormalizedTrade:
    """Trade normalizado para formato QUALIA."""
    
    symbol: str
    price: float
    size: float
    side: str  # 'buy' or 'sell'
    timestamp: float
    trade_id: str
    source: str
    raw_data: Optional[Dict[str, Any]] = None


class DataNormalizer:
    """Normalizador de dados de feeds para formato QUALIA."""
    
    def __init__(self):
        self.symbol_mappings = {
            # Mapeamento de símbolos específicos por exchange
            'kucoin': {
                'BTC-USDT': 'BTCUSDT',
                'ETH-USDT': 'ETHUSDT',
                'ADA-USDT': 'ADAUSDT',
                'SOL-USDT': 'SOLUSDT',
            }
        }
    
    def normalize_symbol(self, symbol: str, source: str) -> str:
        """Normaliza símbolo para formato QUALIA padrão."""
        if source in self.symbol_mappings:
            return self.symbol_mappings[source].get(symbol, symbol)
        
        # Normalização genérica: remover separadores
        return symbol.replace('-', '').replace('/', '').upper()
    
    def normalize_kucoin_ticker(self, data: Dict[str, Any]) -> NormalizedTicker:
        """Normaliza ticker do KuCoin para formato QUALIA."""
        try:
            symbol = self.normalize_symbol(data.get('symbol', ''), 'kucoin')
            
            return NormalizedTicker(
                symbol=symbol,
                price=float(data.get('price', 0) or data.get('last', 0)),
                bid=float(data.get('bestBid', 0) or data.get('bid', 0)),
                ask=float(data.get('bestAsk', 0) or data.get('ask', 0)),
                volume_24h=float(data.get('vol', 0) or data.get('baseVolume', 0)),
                change_24h=float(data.get('changePrice', 0) or 0),
                change_24h_percent=float(data.get('changeRate', 0) or 0) * 100,
                high_24h=float(data.get('high', 0) or 0),
                low_24h=float(data.get('low', 0) or 0),
                timestamp=float(data.get('time', time.time() * 1000)) / 1000,
                source='kucoin',
                raw_data=data
            )
        except Exception as e:
            logger.error(f"Erro ao normalizar ticker KuCoin: {e}")
            raise
    
    def normalize_kucoin_orderbook(self, data: Dict[str, Any], symbol: str) -> NormalizedOrderBook:
        """Normaliza order book do KuCoin para formato QUALIA."""
        try:
            normalized_symbol = self.normalize_symbol(symbol, 'kucoin')
            
            # Converter bids/asks para formato padrão
            bids = []
            asks = []
            
            if 'bids' in data:
                bids = [[float(price), float(size)] for price, size in data['bids']]
            
            if 'asks' in data:
                asks = [[float(price), float(size)] for price, size in data['asks']]
            
            return NormalizedOrderBook(
                symbol=normalized_symbol,
                bids=bids,
                asks=asks,
                timestamp=float(data.get('time', time.time() * 1000)) / 1000,
                source='kucoin',
                raw_data=data
            )
        except Exception as e:
            logger.error(f"Erro ao normalizar order book KuCoin: {e}")
            raise
    
    def normalize_kucoin_trade(self, data: Dict[str, Any]) -> NormalizedTrade:
        """Normaliza trade do KuCoin para formato QUALIA."""
        try:
            symbol = self.normalize_symbol(data.get('symbol', ''), 'kucoin')
            
            return NormalizedTrade(
                symbol=symbol,
                price=float(data.get('price', 0)),
                size=float(data.get('size', 0)),
                side=data.get('side', '').lower(),
                timestamp=float(data.get('time', time.time() * 1000)) / 1000,
                trade_id=str(data.get('tradeId', '')),
                source='kucoin',
                raw_data=data
            )
        except Exception as e:
            logger.error(f"Erro ao normalizar trade KuCoin: {e}")
            raise
    
    def normalize_generic_ticker(self, data: Dict[str, Any], source: str) -> NormalizedTicker:
        """Normaliza ticker genérico para formato QUALIA."""
        try:
            symbol = self.normalize_symbol(data.get('symbol', ''), source)
            
            return NormalizedTicker(
                symbol=symbol,
                price=float(data.get('last', 0) or data.get('price', 0)),
                bid=float(data.get('bid', 0)),
                ask=float(data.get('ask', 0)),
                volume_24h=float(data.get('baseVolume', 0) or data.get('volume', 0)),
                change_24h=float(data.get('change', 0)),
                change_24h_percent=float(data.get('percentage', 0)),
                high_24h=float(data.get('high', 0)),
                low_24h=float(data.get('low', 0)),
                timestamp=float(data.get('timestamp', time.time() * 1000)) / 1000,
                source=source,
                raw_data=data
            )
        except Exception as e:
            logger.error(f"Erro ao normalizar ticker genérico de {source}: {e}")
            raise
    
    def validate_normalized_data(self, data: Any) -> bool:
        """Valida dados normalizados."""
        if isinstance(data, NormalizedTicker):
            return (
                data.symbol and
                data.price > 0 and
                data.timestamp > 0 and
                data.source
            )
        elif isinstance(data, NormalizedOrderBook):
            return (
                data.symbol and
                data.timestamp > 0 and
                data.source and
                (data.bids or data.asks)
            )
        elif isinstance(data, NormalizedTrade):
            return (
                data.symbol and
                data.price > 0 and
                data.size > 0 and
                data.timestamp > 0 and
                data.source
            )
        
        return False
