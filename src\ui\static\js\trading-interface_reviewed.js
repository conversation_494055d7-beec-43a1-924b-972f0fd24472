// Revisado em 2025-06-13 por Codex
/**
 * QUALIA Trading System - Interface JavaScript
 * 
 * Este arquivo implementa as funcionalidades de frontend para o sistema de trading,
 * incluindo interações com a API, visualizações de dados e controles da interface.
 */

// Flag global de depuração
window.DEBUG = window.DEBUG || false;

// Estado global da interface
const tradingUI = {
    // Configurações
    config: {
        apiKey: '',
        apiSecret: '',
        initialCapital: 10000,
        riskProfile: 'balanced',
        liveMode: false,
        drawdownThreshold: 0.1,
        retrocausalityEnabled: false,
        retrocausalCoefficient: 0.5,
        selectedPairs: ['BTC/USD', 'ETH/USD'],
        refreshInterval: 5000  // 5 segundos
    },

    // Obter token CSRF do formulário oculto
    getCsrfToken: function() {
        const tokenInput = document.querySelector('input[name="csrf_token"]');
        return tokenInput ? tokenInput.value : '';
    },

    // Estado do sistema
    state: {
        systemStatus: 'stopped',  // 'stopped', 'running', 'paused', 'error'
        lastUpdate: null,
        errorMessage: null,
        apiConfigured: false,
        activeTab: 'BTC/USD',
        refreshTimer: null,
        dataLoaded: false,
        alerts: {
            drawdown: false,
            regime: false
        }
    },

    // Dados
    data: {
        marketData: {},
        positions: [],
        tradeHistory: [],
        performanceMetrics: {
            pnl: 0,
            pnl_percentage: 0,
            win_rate: 0,
            drawdown: 0,
            sharpe: 0,
            trades: 0
        },
        quantumMetrics: {
            entropy: 0,
            coherence: 0,
            active_states: 0
        },
        charts: {}
    },

    // Garantir que as credenciais só sejam enviadas via HTTPS
    ensureSecureConnection: function() {
        const url = new URL(window.location.origin);
        const isSecure = url.protocol === 'https:' || url.hostname === 'localhost';
        if (!isSecure) {
            this.showNotification('error', 'Conexão Insegura', 'Credenciais somente podem ser enviadas por HTTPS.');
        }
        return isSecure;
    },

    // Enviar e armazenar credenciais de forma segura
    storeCredentials: async function(apiKey, apiSecret) {
        if (!this.ensureSecureConnection()) {
            throw new Error('Conexão insegura');
        }
        const response = await fetch(window.location.origin + '/trading/api/store-credentials', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCsrfToken()
            },
            body: JSON.stringify({ api_key: apiKey, api_secret: apiSecret })
        });
        return response.json();
    },

    // Enviar configurações avançadas para o backend
    updateConfig: function() {
        fetch('/trading/api/update-config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCsrfToken()
            },
            body: JSON.stringify({
                drawdown_threshold: this.config.drawdownThreshold,
                retrocausal_enabled: this.config.retrocausalityEnabled,
                retrocausal_coefficient: this.config.retrocausalCoefficient
            })
        })
            .then((res) => res.json())
            .then((data) => {
                if (data.success) {
                    this.showNotification('success', 'Configuração Atualizada', data.message || 'Configurações salvas.');
                } else {
                    this.showNotification('error', 'Erro', data.message || 'Falha ao atualizar configurações.');
                }
            })
            .catch((err) => {
                console.error('Erro ao atualizar configuração:', err);
                this.showNotification('error', 'Erro', 'Não foi possível enviar as configurações.');
            });
    },

    // Inicialização da interface
    init: function() {
        this.setupEventListeners();
        this.setupQuantumCircuitEvents();
        this.loadAvailablePairs();
        this.updateSystemStatus();
        this.createPlaceholderCharts();
    },
    
    // Configurar eventos para visualização de circuito quântico
    setupQuantumCircuitEvents: function() {
        const refreshCircuitBtn = document.getElementById('refresh-circuit-btn');
        const exportQasmBtn = document.getElementById('export-qasm-btn');
        
        if (refreshCircuitBtn) {
            refreshCircuitBtn.addEventListener('click', () => this.refreshQuantumCircuit());
        }
        
        if (exportQasmBtn) {
            exportQasmBtn.addEventListener('click', () => this.exportQuantumCircuit());
        }
    },

    // Configurar listeners de eventos
    setupEventListeners: function() {
        // Botões de controle do sistema
        document.getElementById('test-api-btn').addEventListener('click', () => this.testApiConnection());
        document.getElementById('initialize-btn').addEventListener('click', () => this.initializeSystem());
        document.getElementById('start-btn').addEventListener('click', () => this.startSystem());
        document.getElementById('stop-btn').addEventListener('click', () => this.stopSystem());
        
        // Toggle de modo live
        document.getElementById('live-mode-toggle').addEventListener('change', (e) => {
            this.config.liveMode = e.target.checked;
            document.getElementById('mode-label').textContent = this.config.liveMode ? 'Trading Real' : 'Simulação';
            document.getElementById('footer-mode-label').textContent = this.config.liveMode ? 'Trading Real' : 'Simulação';
            
            // Mostrar alerta se ativar modo live
            if (this.config.liveMode) {
                this.showNotification('warning', 'Modo de Trading Real', 'ATENÇÃO: O sistema executará ordens reais na Kraken com seu capital.');
            }
        });
        
        // Seleção de risco
        document.getElementById('risk-profile').addEventListener('change', (e) => {
            this.config.riskProfile = e.target.value;
        });
        
        // Capital inicial
        document.getElementById('initial-capital').addEventListener('change', (e) => {
            this.config.initialCapital = parseFloat(e.target.value);
        });

        // Configurações avançadas
        document.getElementById('drawdown-threshold').addEventListener('change', (e) => {
            this.config.drawdownThreshold = parseFloat(e.target.value) / 100;
        });
        document.getElementById('retrocausality-toggle').addEventListener('change', (e) => {
            this.config.retrocausalityEnabled = e.target.checked;
        });
        document.getElementById('retrocausal-coefficient').addEventListener('change', (e) => {
            this.config.retrocausalCoefficient = parseFloat(e.target.value);
        });
        document.getElementById('update-config-btn').addEventListener('click', () => this.updateConfig());
        
        // Credenciais API
        document.getElementById('api-key').addEventListener('change', (e) => {
            this.config.apiKey = e.target.value.trim();
        });
        document.getElementById('api-secret').addEventListener('change', (e) => {
            this.config.apiSecret = e.target.value.trim();
        });
        
        // Tabs de mercado
        document.getElementById('market-tab-headers').addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-header')) {
                const symbol = e.target.dataset.symbol;
                this.changeMarketTab(symbol);
            }
        });
        
        // Tipo de ordem manual
        document.getElementById('manual-type').addEventListener('change', (e) => {
            const isLimit = e.target.value === 'limit';
            document.getElementById('price-container').style.display = isLimit ? 'block' : 'none';
        });
        
        // Botão de ordem manual
        document.getElementById('manual-order-btn').addEventListener('click', () => this.submitManualOrder());
        
        // Botões de modal
        document.getElementById('modal-close').addEventListener('click', () => this.closeModal());
        document.getElementById('modal-cancel').addEventListener('click', () => this.closeModal());
        
        // Inicialmente esconder campo de preço se for ordem de mercado
        document.getElementById('price-container').style.display = 'none';
    },

    // Carregar pares disponíveis
    loadAvailablePairs: function() {
        fetch('/trading/api/pairs')
            .then(response => response.json())
            .then(data => {
                if (data.pairs && Array.isArray(data.pairs)) {
                    // Limpar container de pares
                    const pairsContainer = document.getElementById('trading-pairs-container');
                    pairsContainer.innerHTML = '';
                    
                    // Adicionar pares disponíveis
                    data.pairs.forEach(pair => {
                        const pairId = pair.replace('/', '');
                        const isChecked = this.config.selectedPairs.includes(pair);
                        
                        const pairElement = document.createElement('div');
                        pairElement.className = 'pair-checkbox';
                        pairElement.innerHTML = `
                            <input type="checkbox" id="pair-${pairId}" value="${pair}" ${isChecked ? 'checked' : ''}>
                            <label for="pair-${pairId}">${pair}</label>
                        `;
                        
                        // Adicionar evento para atualizar pares selecionados
                        pairElement.querySelector('input').addEventListener('change', (e) => {
                            if (e.target.checked) {
                                if (!this.config.selectedPairs.includes(pair)) {
                                    this.config.selectedPairs.push(pair);
                                }
                            } else {
                                this.config.selectedPairs = this.config.selectedPairs.filter(p => p !== pair);
                            }
                        });
                        
                        pairsContainer.appendChild(pairElement);
                    });
                    
                    // Criar tabs de mercado para pares iniciais
                    this.updateMarketTabs();
                }
            })
            .catch(error => {
                console.error('Erro ao carregar pares:', error);
                this.showNotification('error', 'Erro', 'Falha ao carregar pares de trading disponíveis.');
            });
    },

    // Verificar status do sistema
    updateSystemStatus: function() {
        if (window.DEBUG) {
            console.log("Buscando status do sistema de trading... URL absoluta:", window.location.origin + "/trading/api/status");
        }
        fetch(window.location.origin + '/trading/api/status')
            .then(response => {
                if (window.DEBUG) {
                    console.log("Resposta de status recebida:", response.status, response.statusText);
                }
                if (!response.ok) {
                    throw new Error(`Erro na resposta (${response.status}): ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (window.DEBUG) {
                    console.log("Dados de status recebidos:", data);
                }
                this.state.systemStatus = data.status;
                this.state.lastUpdate = data.last_update;
                this.state.errorMessage = data.error_message;
                this.state.apiConfigured = data.api_configured;
                
                // Atualizar métricas de desempenho
                if (data.performance_metrics) {
                    this.data.performanceMetrics = data.performance_metrics;
                    this.updatePerformanceUI();
                }
                
                // Atualizar status na UI
                this.updateStatusUI();
                
                // Configurar atualização periódica de dados se sistema estiver rodando
                if (this.state.systemStatus === 'running') {
                    this.startDataRefresh();
                    this.loadAllData();
                } else {
                    this.stopDataRefresh();
                }
            })
            .catch(error => {
                console.error('Erro ao verificar status:', error);
                this.showNotification('error', 'Erro de Conexão', 'Não foi possível verificar o status do sistema.');
            });
    },

    // Testar conexão com a API
    testApiConnection: async function() {
        const apiKey = document.getElementById('api-key').value.trim();
        const apiSecret = document.getElementById('api-secret').value.trim();
        
        if (!apiKey || !apiSecret) {
            this.showNotification('warning', 'Dados Incompletos', 'Por favor, forneça sua API Key e Secret da Kraken.');
            return;
        }
        
        this.showNotification('info', 'Testando Conexão', 'Verificando credenciais da API Kraken...');
        
        try {
            await this.storeCredentials(apiKey, apiSecret);
        } catch (err) {
            return;
        }

        if (window.DEBUG) {
            console.log("Testando conexão com a API Kraken...");
        }
        fetch(window.location.origin + '/trading/api/test-connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCsrfToken()
            },
            body: JSON.stringify({})
        })
        .then(response => {
            if (window.DEBUG) {
                console.log("Resposta do teste de conexão recebida:", response.status, response.statusText);
            }
            if (!response.ok) {
                throw new Error(`Erro na resposta (${response.status}): ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (window.DEBUG) {
                console.log("Dados do teste de conexão recebidos:", data);
            }
            if (data.success) {
                this.showNotification('success', 'Conexão Bem Sucedida', data.message);
                
                // Salvar credenciais na configuração
                this.config.apiKey = apiKey;
                this.config.apiSecret = apiSecret;
                
                // Atualizar UI
                document.getElementById('api-connection-led').classList.add('active');
            } else {
                this.showNotification('error', 'Falha na Conexão', data.message);
                document.getElementById('api-connection-led').classList.remove('active');
            }
        })
        .catch(error => {
            console.error('Erro ao testar conexão:', error);
            this.showNotification('error', 'Erro de Conexão', 'Não foi possível testar a conexão com a Kraken.');
        });
    },

    // Inicializar sistema de trading
    initializeSystem: async function() {
        const apiKey = this.config.apiKey;
        const apiSecret = this.config.apiSecret;
        
        if (!apiKey || !apiSecret) {
            this.showNotification('warning', 'Dados Incompletos', 'Por favor, configure suas credenciais da API Kraken.');
            return;
        }
        
        if (this.config.selectedPairs.length === 0) {
            this.showNotification('warning', 'Configuração Incompleta', 'Selecione pelo menos um par de trading.');
            return;
        }
        
        this.showNotification('info', 'Inicializando', 'Inicializando sistema de trading...');

        try {
            await this.storeCredentials(apiKey, apiSecret);
        } catch (err) {
            return;
        }

        fetch(window.location.origin + '/trading/api/initialize', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCsrfToken()
            },
            body: JSON.stringify({
                initial_capital: this.config.initialCapital,
                symbols: this.config.selectedPairs,
                risk_profile: this.config.riskProfile,
                live_mode: this.config.liveMode
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification('success', 'Sistema Inicializado', data.message);
                this.state.systemStatus = data.status;
                this.updateStatusUI();
                
                // Habilitar botão de iniciar
                document.getElementById('start-btn').disabled = false;
                document.getElementById('trading-system-led').classList.add('active');
            } else {
                this.showNotification('error', 'Falha na Inicialização', data.message);
            }
        })
        .catch(error => {
            console.error('Erro ao inicializar sistema:', error);
            this.showNotification('error', 'Erro de Inicialização', 'Não foi possível inicializar o sistema de trading.');
        });
    },

    // Iniciar sistema de trading
    startSystem: function() {
        this.showNotification('info', 'Iniciando Sistema', 'Iniciando o sistema de trading...');
        
        fetch(window.location.origin + '/trading/api/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCsrfToken()
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification('success', 'Sistema Iniciado', data.message);
                this.state.systemStatus = data.status;
                this.updateStatusUI();
                
                // Iniciar atualização de dados
                this.startDataRefresh();
                this.loadAllData();
                
                // Atualizar botões
                document.getElementById('start-btn').disabled = true;
                document.getElementById('stop-btn').disabled = false;
            } else {
                this.showNotification('error', 'Falha ao Iniciar', data.message);
            }
        })
        .catch(error => {
            console.error('Erro ao iniciar sistema:', error);
            this.showNotification('error', 'Erro ao Iniciar', 'Não foi possível iniciar o sistema de trading.');
        });
    },

    // Parar sistema de trading
    stopSystem: function() {
        if (this.config.liveMode) {
            // Confirmar parada em modo live
            this.showModal(
                'Confirmar Parada em Modo Real',
                'Você está prestes a parar o sistema em modo de trading real. Todas as operações automáticas serão interrompidas, mas posições abertas continuarão ativas. Deseja continuar?',
                () => this.executeStopSystem()
            );
        } else {
            this.executeStopSystem();
        }
    },

    executeStopSystem: function() {
        this.showNotification('info', 'Parando Sistema', 'Parando o sistema de trading...');
        
        fetch(window.location.origin + '/trading/api/stop', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCsrfToken()
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification('success', 'Sistema Parado', data.message);
                this.state.systemStatus = data.status;
                this.updateStatusUI();
                
                // Parar atualização de dados
                this.stopDataRefresh();
                
                // Atualizar botões
                document.getElementById('start-btn').disabled = false;
                document.getElementById('stop-btn').disabled = true;
            } else {
                this.showNotification('error', 'Falha ao Parar', data.message);
            }
        })
        .catch(error => {
            console.error('Erro ao parar sistema:', error);
            this.showNotification('error', 'Erro ao Parar', 'Não foi possível parar o sistema de trading.');
        });
    },

    // Atualizar UI de status
    updateStatusUI: function() {
        const statusIndicator = document.getElementById('system-status-indicator');
        const statusText = document.getElementById('status-text');
        const systemLed = document.getElementById('trading-system-led');
        const startBtn = document.getElementById('start-btn');
        const stopBtn = document.getElementById('stop-btn');
        
        // Limpar classes anteriores
        statusIndicator.querySelector('.indicator-led').className = 'indicator-led';
        
        // Definir status
        switch (this.state.systemStatus) {
            case 'running':
                statusIndicator.querySelector('.indicator-led').classList.add('active');
                statusText.textContent = 'Sistema em Execução';
                systemLed.classList.add('active');
                startBtn.disabled = true;
                stopBtn.disabled = false;
                break;
                
            case 'initialized':
                statusIndicator.querySelector('.indicator-led').classList.add('warning');
                statusText.textContent = 'Sistema Inicializado';
                systemLed.classList.add('warning');
                startBtn.disabled = false;
                stopBtn.disabled = true;
                break;
                
            case 'stopping':
                statusIndicator.querySelector('.indicator-led').classList.add('warning');
                statusText.textContent = 'Sistema Parando...';
                systemLed.classList.add('warning');
                startBtn.disabled = true;
                stopBtn.disabled = true;
                break;
                
            case 'error':
                statusIndicator.querySelector('.indicator-led').classList.add('error');
                statusText.textContent = 'Erro no Sistema';
                systemLed.classList.add('error');
                startBtn.disabled = false;
                stopBtn.disabled = true;
                break;
                
            case 'stopped':
            default:
                statusIndicator.querySelector('.indicator-led').classList.remove('active');
                statusText.textContent = 'Sistema Parado';
                systemLed.classList.remove('active');
                startBtn.disabled = !this.state.apiConfigured;
                stopBtn.disabled = true;
                break;
        }
        
        // Atualizar indicador de modo
        const liveLed = document.getElementById('live-mode-led');
        if (this.config.liveMode) {
            liveLed.classList.add(this.state.systemStatus === 'running' ? 'error' : 'warning');
        } else {
            liveLed.classList.remove('error', 'warning');
        }
        
        // Atualizar timestamp
        if (this.state.lastUpdate) {
            const formattedTime = new Date(this.state.lastUpdate).toLocaleTimeString();
            document.getElementById('update-timestamp').textContent = formattedTime;
        }
    },

    // Iniciar atualização periódica de dados
    startDataRefresh: function() {
        if (this.state.refreshTimer) {
            clearInterval(this.state.refreshTimer);
        }
        
        this.state.refreshTimer = setInterval(() => {
            this.refreshData();
        }, this.config.refreshInterval);
    },

    // Parar atualização periódica de dados
    stopDataRefresh: function() {
        if (this.state.refreshTimer) {
            clearInterval(this.state.refreshTimer);
            this.state.refreshTimer = null;
        }
    },

    // Atualizar todos os dados
    refreshData: function() {
        // Somente atualizar se sistema estiver ativo
        if (this.state.systemStatus === 'running') {
            // Verificar status do sistema
            if (window.DEBUG) {
                console.log("Verificando status do sistema...");
            }
            fetch(window.location.origin + '/trading/api/status')
                .then(response => {
                    if (window.DEBUG) {
                        console.log("Resposta de status recebida:", response.status, response.statusText);
                    }
                    return response.json();
                })
                .then(data => {
                    this.state.systemStatus = data.status;
                    this.state.lastUpdate = data.last_update;
                    this.updateStatusUI();
                    
                    // Se status mudou para não-running, parar atualizações
                    if (data.status !== 'running') {
                        this.stopDataRefresh();
                        return;
                    }
                    
                    // Atualizar dados de mercado e posições
                    if (window.DEBUG) {
                        console.log("Buscando dados de mercado, posições e histórico...");
                    }
                    Promise.all([
                        fetch(window.location.origin + '/trading/api/market-data').then(res => res.json()),
                        fetch(window.location.origin + '/trading/api/positions').then(res => res.json()),
                        fetch(window.location.origin + '/trading/api/trade-history').then(res => res.json()),
                        fetch(window.location.origin + '/trading/api/quantum_metrics').then(res => res.json())
                    ])
                    .then(([marketData, positions, tradeHistory, quantumMetrics]) => {
                        // Atualizar dados
                        if (marketData.market_data) {
                            this.data.marketData = marketData.market_data;
                            this.updateMarketUI();
                        }
                        
                        if (positions.positions) {
                            this.data.positions = positions.positions;
                            this.updatePositionsUI();
                        }
                        
                        if (tradeHistory.trade_history) {
                            this.data.tradeHistory = tradeHistory.trade_history;
                            this.updateTradeHistoryUI();
                        }
                        
                        if (data.performance_metrics) {
                            this.data.performanceMetrics = data.performance_metrics;
                            this.updatePerformanceUI();
                        }
                        
                        // Atualizar métricas quânticas, se disponíveis
                        if (quantumMetrics && quantumMetrics.success && quantumMetrics.metrics) {
                            this.data.quantumMetrics = quantumMetrics.metrics;
                            this.data.metricHistory = quantumMetrics.history || [];
                            this.updateQuantumMetricsUI();
                        }
                        
                        // Definir flag de dados carregados
                        if (!this.state.dataLoaded) {
                            this.state.dataLoaded = true;
                        }
                    })
                    .catch(error => {
                        console.error('Erro ao atualizar dados:', error);
                    });
                })
                .catch(error => {
                    console.error('Erro ao verificar status:', error);
                });
        }
    },

    // Carregar todos os dados (primeira carga)
    loadAllData: function() {
        if (window.DEBUG) {
            console.log("Carregando dados iniciais...");
        }
        Promise.all([
            fetch(window.location.origin + '/trading/api/market-data').then(res => res.json()),
            fetch(window.location.origin + '/trading/api/positions').then(res => res.json()),
            fetch(window.location.origin + '/trading/api/trade-history').then(res => res.json()),
            fetch(window.location.origin + '/trading/api/quantum_metrics').then(res => res.json())
        ])
        .then(([marketData, positions, tradeHistory, quantumMetrics]) => {
            // Inicializar dados
            if (marketData.market_data) {
                this.data.marketData = marketData.market_data;
                this.updateMarketUI();
                this.updateMarketTabs();
            }
            
            if (positions.positions) {
                this.data.positions = positions.positions;
                this.updatePositionsUI();
            }
            
            if (tradeHistory.trade_history) {
                this.data.tradeHistory = tradeHistory.trade_history;
                this.updateTradeHistoryUI();
            }
            
            // Adicionar métricas quânticas
            if (quantumMetrics.success && quantumMetrics.metrics) {
                this.data.quantumMetrics = quantumMetrics.metrics;
                this.data.metricHistory = quantumMetrics.history || [];
                this.updateQuantumMetricsUI();
            }
            
            // Definir flag de dados carregados
            this.state.dataLoaded = true;
        })
        .catch(error => {
            console.error('Erro ao carregar dados iniciais:', error);
            this.showNotification('error', 'Erro de Dados', 'Não foi possível carregar os dados iniciais.');
        });
    },

    // Atualizar UI de mercado
    updateMarketUI: function() {
        // Verificar se temos dados de mercado
        if (!this.data.marketData || Object.keys(this.data.marketData).length === 0) {
            return;
        }
        
        // Atualizar tab ativa
        const activeSymbol = this.state.activeTab;
        if (this.data.marketData[activeSymbol]) {
            const tfKeys = Object.keys(this.data.marketData[activeSymbol]);
            if (tfKeys.length > 0) {
                const tf = tfKeys[0];  // Usar primeiro timeframe disponível
                const data = this.data.marketData[activeSymbol][tf];
                
                // Atualizar dados de mercado
                if (data) {
                    // Atualizar preço e variação
                    const lastPrice = data.close[data.close.length - 1];
                    const prevPrice = data.close.length > 1 ? data.close[data.close.length - 2] : lastPrice;
                    const priceChange = ((lastPrice - prevPrice) / prevPrice) * 100;
                    
                    const priceElement = document.getElementById(`price-${activeSymbol.replace('/', '-')}`);
                    const changeElement = document.getElementById(`change-${activeSymbol.replace('/', '-')}`);
                    const volumeElement = document.getElementById(`volume-${activeSymbol.replace('/', '-')}`);
                    
                    if (priceElement) priceElement.textContent = `$${lastPrice.toFixed(2)}`;
                    
                    if (changeElement) {
                        changeElement.textContent = `${priceChange >= 0 ? '+' : ''}${priceChange.toFixed(2)}%`;
                        changeElement.className = `item-value ${priceChange >= 0 ? 'positive' : 'negative'}`;
                    }
                    
                    if (volumeElement) {
                        // Calcular volume total das últimas 24 candles (assumindo 1h = 24h)
                        const recentVolume = data.volume.slice(-24).reduce((sum, vol) => sum + vol, 0);
                        volumeElement.textContent = recentVolume.toFixed(2);
                    }
                    
                    // Atualizar gráfico
                    this.updateMarketChart(activeSymbol, data);
                }
            }
        }
    },

    // Atualizar tabs de mercado
    updateMarketTabs: function() {
        // Verificar se temos dados de mercado ou usar selectedPairs
        const symbols = Object.keys(this.data.marketData).length > 0 ? 
            Object.keys(this.data.marketData) : this.config.selectedPairs;
        
        if (symbols.length === 0) {
            return;
        }
        
        // Limpar headers de tabs
        const tabHeaders = document.getElementById('market-tab-headers');
        tabHeaders.innerHTML = '';
        
        // Adicionar tab para cada símbolo
        symbols.forEach(symbol => {
            const isActive = symbol === this.state.activeTab;
            
            const tabHeader = document.createElement('div');
            tabHeader.className = `tab-header${isActive ? ' active' : ''}`;
            tabHeader.dataset.symbol = symbol;
            tabHeader.textContent = symbol;
            
            tabHeaders.appendChild(tabHeader);
            
            // Verificar se já existe conteúdo para esta tab
            const tabId = `market-${symbol.replace('/', '-')}`;
            if (!document.getElementById(tabId)) {
                this.createMarketTabContent(symbol);
            }
        });
        
        // Se a tab ativa não está na lista, selecionar a primeira
        if (!symbols.includes(this.state.activeTab) && symbols.length > 0) {
            this.changeMarketTab(symbols[0]);
        }
    },

    // Criar conteúdo para tab de mercado
    createMarketTabContent: function(symbol) {
        const symbolId = symbol.replace('/', '-');
        const tabContent = document.getElementById('market-tab-content');
        
        // Criar elemento da tab
        const tabPane = document.createElement('div');
        tabPane.className = `tab-pane${symbol === this.state.activeTab ? ' active' : ''}`;
        tabPane.id = `market-${symbolId}`;
        
        tabPane.innerHTML = `
            <div class="market-chart-container">
                <div id="chart-${symbolId}" class="market-chart"></div>
            </div>
            <div class="market-data-grid">
                <div class="market-data-item">
                    <div class="item-label">Último</div>
                    <div class="item-value" id="price-${symbolId}">$0.00</div>
                </div>
                <div class="market-data-item">
                    <div class="item-label">24h Δ</div>
                    <div class="item-value" id="change-${symbolId}">0.00%</div>
                </div>
                <div class="market-data-item">
                    <div class="item-label">Volume</div>
                    <div class="item-value" id="volume-${symbolId}">0.00</div>
                </div>
                <div class="market-data-item">
                    <div class="item-label">Análise</div>
                    <div class="item-value" id="signal-${symbolId}">-</div>
                </div>
            </div>
        `;
        
        tabContent.appendChild(tabPane);
        
        // Criar gráfico placeholder
        this.createPlaceholderChart(symbol);
    },

    // Mudar tab de mercado
    changeMarketTab: function(symbol) {
        // Atualizar estado
        this.state.activeTab = symbol;
        
        // Atualizar UI
        const tabHeaders = document.querySelectorAll('#market-tab-headers .tab-header');
        const tabPanes = document.querySelectorAll('#market-tab-content .tab-pane');
        
        // Desativar todos
        tabHeaders.forEach(header => header.classList.remove('active'));
        tabPanes.forEach(pane => pane.classList.remove('active'));
        
        // Ativar selecionado
        const selectedHeader = document.querySelector(`#market-tab-headers .tab-header[data-symbol="${symbol}"]`);
        const selectedPane = document.getElementById(`market-${symbol.replace('/', '-')}`);
        
        if (selectedHeader) selectedHeader.classList.add('active');
        if (selectedPane) selectedPane.classList.add('active');
        
        // Atualizar dados
        this.updateMarketUI();
    },

    // Criar gráficos placeholder
    createPlaceholderCharts: function() {
        // Criar gráfico para cada par inicial
        this.config.selectedPairs.forEach(symbol => {
            this.createPlaceholderChart(symbol);
        });
        
        // Criar gráfico de equity
        const equityDiv = document.getElementById('equity-curve');
        if (equityDiv) {
            const equityData = {
                x: Array.from({length: 10}, (_, i) => new Date(Date.now() - (9-i) * 86400000)),
                y: Array.from({length: 10}, () => this.config.initialCapital),
                type: 'scatter',
                mode: 'lines',
                name: 'Equity',
                line: {
                    color: '#485bc4',
                    width: 2
                }
            };
            
            const layout = {
                title: 'Curva de Equity',
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)',
                font: {
                    family: 'Space Grotesk, sans-serif',
                    color: '#333'
                },
                margin: {
                    l: 50,
                    r: 20,
                    t: 30,
                    b: 40
                },
                xaxis: {
                    title: '',
                    showgrid: false
                },
                yaxis: {
                    title: 'Valor ($)',
                    showgrid: true,
                    gridcolor: 'rgba(72, 91, 196, 0.1)'
                }
            };
            
            Plotly.newPlot(equityDiv, [equityData], layout, {responsive: true});
            this.data.charts.equity = equityDiv;
        }
        
        // Criar gráfico de métricas quânticas
        const quantumDiv = document.getElementById('quantum-metrics-line-chart');
        if (quantumDiv) {
            const timestamps = Array.from({length: 20}, (_, i) => new Date(Date.now() - (19-i) * 60000));
            
            const entropyData = {
                x: timestamps,
                y: Array.from({length: 20}, () => Math.random() * 0.5),
                type: 'scatter',
                mode: 'lines',
                name: 'Entropia',
                line: {
                    color: '#F44336',
                    width: 2
                }
            };
            
            const coherenceData = {
                x: timestamps,
                y: Array.from({length: 20}, () => 0.2 + Math.random() * 0.6),
                type: 'scatter',
                mode: 'lines',
                name: 'Coerência',
                line: {
                    color: '#4CAF50',
                    width: 2
                }
            };
            
            const layout = {
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)',
                font: {
                    family: 'Space Grotesk, sans-serif',
                    color: '#333'
                },
                margin: {
                    l: 50,
                    r: 20,
                    t: 10,
                    b: 40
                },
                xaxis: {
                    title: '',
                    showgrid: false
                },
                yaxis: {
                    title: 'Valor',
                    range: [0, 1],
                    showgrid: true,
                    gridcolor: 'rgba(72, 91, 196, 0.1)'
                },
                legend: {
                    orientation: 'h',
                    y: 1.1
                }
            };
            
            Plotly.newPlot(quantumDiv, [entropyData, coherenceData], layout, {responsive: true});
            this.data.charts.quantum = quantumDiv;
        }
    },

    // Criar gráfico placeholder para um par
    createPlaceholderChart: function(symbol) {
        const symbolId = symbol.replace('/', '-');
        const chartDiv = document.getElementById(`chart-${symbolId}`);
        
        if (!chartDiv) {
            console.warn(`Elemento de gráfico não encontrado para ${symbol}`);
            return;
        }
        
        // Dados fictícios para o placeholder
        const currentTime = new Date();
        const times = Array.from({length: 50}, (_, i) => {
            const time = new Date(currentTime);
            time.setMinutes(time.getMinutes() - (49-i) * 5);
            return time;
        });
        
        let price = 100 + (symbol.includes('BTC') ? 30000 : (symbol.includes('ETH') ? 2000 : 0));
        
        const ohlc = times.map(() => {
            const change = (Math.random() - 0.5) * price * 0.01;
            price += change;
            const open = price;
            const high = open * (1 + Math.random() * 0.005);
            const low = open * (1 - Math.random() * 0.005);
            const close = (open + high + low) / 3 + (Math.random() - 0.5) * price * 0.005;
            return { open, high, low, close };
        });
        
        const candleData = {
            x: times,
            open: ohlc.map(candle => candle.open),
            high: ohlc.map(candle => candle.high),
            low: ohlc.map(candle => candle.low),
            close: ohlc.map(candle => candle.close),
            type: 'candlestick',
            name: symbol,
            increasing: {line: {color: '#4CAF50'}},
            decreasing: {line: {color: '#F44336'}}
        };
        
        const layout = {
            paper_bgcolor: 'rgba(0,0,0,0)',
            plot_bgcolor: 'rgba(0,0,0,0)',
            font: {
                family: 'Space Grotesk, sans-serif',
                color: '#333'
            },
            margin: {
                l: 50,
                r: 20,
                t: 10,
                b: 40
            },
            xaxis: {
                title: '',
                rangeslider: {
                    visible: false
                },
                showgrid: false
            },
            yaxis: {
                title: 'Preço',
                showgrid: true,
                gridcolor: 'rgba(72, 91, 196, 0.1)'
            }
        };
        
        Plotly.newPlot(chartDiv, [candleData], layout, {responsive: true});
        
        // Armazenar referência ao gráfico
        if (!this.data.charts.markets) {
            this.data.charts.markets = {};
        }
        this.data.charts.markets[symbol] = chartDiv;
    },

    // Atualizar gráfico de mercado com dados reais
    updateMarketChart: function(symbol, data) {
        if (!data || !data.timestamps || data.timestamps.length === 0) {
            return;
        }
        
        const symbolId = symbol.replace('/', '-');
        const chartDiv = document.getElementById(`chart-${symbolId}`);
        
        if (!chartDiv) {
            console.warn(`Elemento de gráfico não encontrado para ${symbol}`);
            return;
        }
        
        // Converter timestamps para objetos Date
        const times = data.timestamps.map(ts => new Date(ts));
        
        const candleData = {
            x: times,
            open: data.open,
            high: data.high,
            low: data.low,
            close: data.close,
            type: 'candlestick',
            name: symbol,
            increasing: {line: {color: '#4CAF50'}},
            decreasing: {line: {color: '#F44336'}}
        };
        
        Plotly.react(chartDiv, [candleData]);
    },

    // Atualizar UI de posições
    updatePositionsUI: function() {
        const table = document.getElementById('positions-table');
        if (!table) return;
        
        const tbody = table.querySelector('tbody');
        
        // Limpar tabela
        tbody.innerHTML = '';
        
        // Verificar se há posições
        if (this.data.positions.length === 0) {
            const emptyRow = document.createElement('tr');
            emptyRow.className = 'empty-row';
            emptyRow.innerHTML = '<td colspan="8">Sem posições abertas</td>';
            tbody.appendChild(emptyRow);
            return;
        }
        
        // Adicionar cada posição
        this.data.positions.forEach(position => {
            const row = document.createElement('tr');
            
            // Calcular cor de P&L
            const pnlColor = position.unrealized_pnl >= 0 ? 'positive' : 'negative';
            
            // Formatar duração
            const duration = new Date(position.duration * 1000).toISOString().substr(11, 8);
            
            // Preencher células
            row.innerHTML = `
                <td>${position.symbol}</td>
                <td class="${position.side === 'buy' ? 'positive' : 'negative'}">${position.side.toUpperCase()}</td>
                <td>${position.quantity.toFixed(8)}</td>
                <td>$${position.entry_price.toFixed(2)}</td>
                <td>$${position.current_price.toFixed(2)}</td>
                <td class="${pnlColor}">$${position.unrealized_pnl.toFixed(2)} (${position.unrealized_pnl_pct >= 0 ? '+' : ''}${position.unrealized_pnl_pct.toFixed(2)}%)</td>
                <td>${duration}</td>
                <td>
                    <button class="quantum-button small table-action-btn" data-action="close" data-symbol="${position.symbol}">Fechar</button>
                </td>
            `;
            
            // Adicionar event listener para botão de fechar
            const closeBtn = row.querySelector('button[data-action="close"]');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => this.closePosition(position.symbol));
            }
            
            tbody.appendChild(row);
        });
    },

    // Fechar posição
    closePosition: function(symbol) {
        this.showModal(
            'Confirmar Fechamento',
            `Você está prestes a fechar manualmente a posição em ${symbol}. Deseja continuar?`,
            () => this.executeClosePosition(symbol)
        );
    },

    executeClosePosition: function(symbol) {
        fetch('/trading/api/close-position', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCsrfToken()
            },
            body: JSON.stringify({
                symbol: symbol,
                reason: 'Fechamento manual'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification('success', 'Posição Fechada', data.message);
                
                // Atualizar dados
                this.refreshData();
            } else {
                this.showNotification('error', 'Erro ao Fechar', data.message);
            }
        })
        .catch(error => {
            console.error('Erro ao fechar posição:', error);
            this.showNotification('error', 'Erro ao Fechar', 'Não foi possível fechar a posição.');
        });
    },

    // Atualizar UI de histórico de trades
    updateTradeHistoryUI: function() {
        const table = document.getElementById('history-table');
        if (!table) return;
        
        const tbody = table.querySelector('tbody');
        
        // Limpar tabela
        tbody.innerHTML = '';
        
        // Verificar se há histórico
        if (this.data.tradeHistory.length === 0) {
            const emptyRow = document.createElement('tr');
            emptyRow.className = 'empty-row';
            emptyRow.innerHTML = '<td colspan="7">Histórico de operações vazio</td>';
            tbody.appendChild(emptyRow);
            return;
        }
        
        // Ordenar por timestamp (mais recente primeiro)
        const sortedHistory = [...this.data.tradeHistory].sort((a, b) => {
            return new Date(b.timestamp) - new Date(a.timestamp);
        });
        
        // Adicionar cada trade (limitado aos 15 mais recentes)
        sortedHistory.slice(0, 15).forEach(trade => {
            const row = document.createElement('tr');
            
            // Calcular cor de P&L
            const pnlColor = trade.realized_pnl >= 0 ? 'positive' : 'negative';
            
            // Formatar timestamp
            const date = new Date(trade.timestamp);
            const formattedDate = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
            
            // Preencher células
            row.innerHTML = `
                <td>${formattedDate}</td>
                <td>${trade.symbol}</td>
                <td class="${trade.side === 'buy' ? 'positive' : 'negative'}">${trade.side.toUpperCase()}</td>
                <td>${trade.quantity.toFixed(8)}</td>
                <td>$${trade.price.toFixed(2)}</td>
                <td class="${pnlColor}">$${trade.realized_pnl.toFixed(2)} (${trade.realized_pnl_pct >= 0 ? '+' : ''}${trade.realized_pnl_pct.toFixed(2)}%)</td>
                <td>${trade.status}</td>
            `;
            
            tbody.appendChild(row);
        });
    },

    // Atualizar gráfico de equity curve
    updateEquityCurve: function() {
        const equityDiv = this.data.charts.equity || document.getElementById('equity-curve');
        if (!equityDiv) {
            return;
        }

        const history = Array.isArray(this.data.tradeHistory) ? [...this.data.tradeHistory] : [];
        history.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

        let equity = this.config.initialCapital;
        const times = [];
        const values = [];

        if (history.length > 0) {
            times.push(new Date(history[0].timestamp));
            values.push(equity);
        }

        history.forEach(trade => {
            equity += trade.realized_pnl || 0;
            times.push(new Date(trade.timestamp));
            values.push(equity);
        });

        if (times.length === 0) {
            times.push(new Date());
            values.push(equity);
        }

        const trace = {
            x: times,
            y: values,
            type: 'scatter',
            mode: 'lines',
            name: 'Equity',
            line: { color: '#485bc4', width: 2 }
        };

        Plotly.react(equityDiv, [trace]);
    },

    // Atualizar UI de performance
    updatePerformanceUI: function() {
        const metrics = this.data.performanceMetrics;
        
        // Atualizar valores
        document.getElementById('pnl-value').textContent = `$${metrics.pnl.toFixed(2)}`;
        document.getElementById('pnl-value').className = `metric-value ${metrics.pnl >= 0 ? 'positive' : 'negative'}`;
        
        document.getElementById('pnl-percent').textContent = `${metrics.pnl_percentage >= 0 ? '+' : ''}${metrics.pnl_percentage.toFixed(2)}%`;
        document.getElementById('pnl-percent').className = `metric-subvalue ${metrics.pnl_percentage >= 0 ? 'positive' : 'negative'}`;
        
        document.getElementById('win-rate').textContent = `${(metrics.win_rate * 100).toFixed(1)}%`;
        document.getElementById('trade-count').textContent = `${metrics.trades} operações`;
        
        document.getElementById('max-drawdown').textContent = `${(metrics.drawdown * 100).toFixed(2)}%`;
        document.getElementById('max-drawdown').className = metrics.drawdown > 0.1 ? 'metric-value negative' : 'metric-value';

        if (!this.state.alerts.drawdown && metrics.drawdown > this.config.drawdownThreshold) {
            this.showNotification('warning', 'Limite de Drawdown Excedido', 'O drawdown máximo ultrapassou o limite definido.');
            this.state.alerts.drawdown = true;
        } else if (this.state.alerts.drawdown && metrics.drawdown <= this.config.drawdownThreshold * 0.9) {
            this.state.alerts.drawdown = false;
        }
        
        document.getElementById('sharpe-ratio').textContent = metrics.sharpe.toFixed(2);
        document.getElementById('sharpe-ratio').className = `metric-value ${metrics.sharpe >= 1 ? 'positive' : metrics.sharpe >= 0 ? 'neutral' : 'negative'}`;

        // Atualizar gráfico de equity curve
        this.updateEquityCurve();
    },
    
    // Atualizar métricas quânticas na UI
    updateQuantumMetricsUI: function() {
        // Verificar se temos dados de métricas quânticas
        if (!this.data.quantumMetrics) {
            if (window.DEBUG) {
                console.log("Dados de métricas quânticas não disponíveis");
            }
            return;
        }

        if (window.DEBUG) {
            console.log("Atualizando métricas quânticas na UI:", this.data.quantumMetrics);
        }
        
        // Extrair métricas quânticas
        const pageEntropy = this.data.quantumMetrics.page_entropy || 0;
        const otoc = this.data.quantumMetrics.otoc || 0;
        const coherence = this.data.quantumMetrics.quantum_coherence || 0;
        const loschmidtEcho = this.data.quantumMetrics.loschmidt_echo || 0;
        const patternRecognition = this.data.quantumMetrics.pattern_recognition_rate || 0;
        const selfReflection = this.data.quantumMetrics.self_reflection_depth || 0;
        const thermalCoefficient = this.data.quantumMetrics.thermal_coefficient || 0;
        
        // Métricas de performance para correlação
        const sharpeRatio = this.data.quantumMetrics.sharpe_ratio || 0;
        const pnl = this.data.quantumMetrics.pnl || 0;
        const winRate = this.data.quantumMetrics.win_rate || 0;
        
        // Atualizar valores nos elementos da UI
        const updateMetricIfExists = (id, value, formatFunc = null) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = formatFunc ? formatFunc(value) : value.toFixed(4);
            } else if (window.DEBUG) {
                console.log(`Elemento ${id} não encontrado na UI`);
            }
        };
        
        // Formatar valores para exibição
        const formatPercent = (value) => (value * 100).toFixed(2) + '%';
        const formatDecimal = (value) => value.toFixed(4);
        
        // Atualizar métricas quânticas individuais
        updateMetricIfExists('page-entropy-value', pageEntropy, formatDecimal);
        updateMetricIfExists('otoc-value', otoc, formatDecimal);
        updateMetricIfExists('quantum-coherence-value', coherence, formatDecimal);
        updateMetricIfExists('loschmidt-echo-value', loschmidtEcho, formatDecimal);
        updateMetricIfExists('pattern-recognition-value', patternRecognition, formatDecimal);
        updateMetricIfExists('self-reflection-value', selfReflection, formatDecimal);

        // Atualizar gráfico de métricas quânticas se existir
        this.updateQuantumMetricsChart();
        this.updateQuantumLineChart();

        const regimeChange = this.data.quantumMetrics.regime_change || 0;
        if (!this.state.alerts.regime && regimeChange > 0) {
            this.showNotification('info', 'Mudança de Regime', 'Foi detectada uma mudança de regime de mercado.');
            this.state.alerts.regime = true;
        } else if (this.state.alerts.regime && regimeChange === 0) {
            this.state.alerts.regime = false;
        }



            }
            
            // Atualizar gráficos de visualização, se existirem
            if (this.data.charts && this.data.charts.quantumMetricsChart) {
                this.updateQuantumMetricsChart();
            }
        }
    },
    
    // Criar painel de métricas quânticas (caso não exista)
    createQuantumMetricsPanel: function() {
        // Verificar se já existe um contêiner de métricas quânticas
        if (document.getElementById('quantum-metrics-container')) {
            return;
        }
        
        // Criar o painel de métricas quânticas
        const mainContent = document.querySelector('.main-panels');
        
        if (!mainContent) {
            console.error("Elemento .main-panels não encontrado!");
            return;
        }
        
        const quantumPanel = document.createElement('div');
        quantumPanel.className = 'panel';
        quantumPanel.innerHTML = `
            <div class="panel-header">
                <span>Métricas Quânticas Interpretáveis</span>
                <div class="panel-actions">
                    <button class="small-btn refresh-btn" id="refresh-quantum-btn" title="Atualizar Métricas">
                        <span>⟳</span>
                    </button>
                </div>
            </div>
            <div class="panel-content">
                <div id="quantum-metrics-container">
                    <div class="quantum-metrics-grid">
                        <div class="metric-box">
                            <div class="metric-title">Entropia de Page</div>
                            <div class="metric-value" id="page-entropy-value">0.0000</div>
                            <div class="metric-desc">Quantifica incerteza quântica</div>
                        </div>
                        <div class="metric-box">
                            <div class="metric-title">OTOC</div>
                            <div class="metric-value" id="otoc-value">0.0000</div>
                            <div class="metric-desc">Scrambling de informação quântica</div>
                        </div>
                        <div class="metric-box">
                            <div class="metric-title">Coerência Quântica</div>
                            <div class="metric-value" id="coherence-value">0.0000</div>
                            <div class="metric-desc">Estabilidade dos estados quânticos</div>
                        </div>
                        <div class="metric-box">
                            <div class="metric-title">Eco Loschmidt</div>
                            <div class="metric-value" id="loschmidt-value">0.0000</div>
                            <div class="metric-desc">Reversibilidade do processo</div>
                        </div>
                        <div class="metric-box">
                            <div class="metric-title">Reconhecimento de Padrões</div>
                            <div class="metric-value" id="pattern-recognition-value">0.00%</div>
                            <div class="metric-desc">Taxa de reconhecimento de padrões</div>
                        </div>
                        <div class="metric-box">
                            <div class="metric-title">Coeficiente Térmico</div>
                            <div class="metric-value" id="thermal-coefficient-value">0.000</div>
                            <div class="metric-desc">Calibrado pela volatilidade do mercado</div>
                        </div>
                    </div>
                    
                    <div class="correlation-section">
                        <h3>Correlação com Performance</h3>
                        <div class="correlation-grid">
                            <div class="correlation-item">
                                <div class="correlation-title">Entropia ↔ PnL</div>
                                <div class="correlation-value" id="entropy-pnl-correlation">0.00</div>
                            </div>
                            <div class="correlation-item">
                                <div class="correlation-title">OTOC ↔ PnL</div>
                                <div class="correlation-value" id="otoc-pnl-correlation">0.00</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="metrics-chart-container">
                        <canvas id="quantum-metrics-chart"></canvas>
                    </div>
                    
                    <div class="quantum-metrics-explanation">
                        <h3>Interpretação das Métricas Quânticas</h3>
                        <div class="explanation-grid">
                            <div class="explanation-item">
                                <div class="explanation-title">Entropia Quântica</div>
                                <div class="explanation-desc">
                                    Mede o nível de incerteza no sistema quântico. Valores altos indicam maior complexidade
                                    e podem sinalizar mudanças no mercado. Esta métrica é essencial para compreender
                                    o comportamento caótico dos mercados financeiros.
                                </div>
                            </div>
                            <div class="explanation-item">
                                <div class="explanation-title">Coerência Quântica</div>
                                <div class="explanation-desc">
                                    Indica a integridade dos estados quânticos e a robustez das previsões. 
                                    Valores elevados sugerem maior precisão nas previsões e estabilidade algorítmica, 
                                    essencial para validação de compliance.
                                </div>
                            </div>
                            <div class="explanation-item">
                                <div class="explanation-title">OTOC (Out-of-Time-Order Correlator)</div>
                                <div class="explanation-desc">
                                    Mede como pequenas perturbações crescem ao longo do tempo no sistema quântico.
                                    Valores elevados indicam maior sensibilidade a mudanças nas condições de mercado,
                                    crucial para avaliação de risco.
                                </div>
                            </div>
                            <div class="explanation-item">
                                <div class="explanation-title">Eco de Loschmidt</div>
                                <div class="explanation-desc">
                                    Quantifica a reversibilidade do processo quântico. Valores baixos indicam
                                    processos mais irreversíveis, o que pode corresponder a movimentos de mercado
                                    mais decisivos ou tendências estabelecidas.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Inserir o painel no DOM
        mainContent.appendChild(quantumPanel);
        
        // Adicionar event listener para o botão de atualização
        const refreshBtn = document.getElementById('refresh-quantum-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshQuantumMetrics();
            });
        }
        
        // Inicializar o gráfico de métricas quânticas
        this.initQuantumMetricsChart();
        
        // Adicionar estilos CSS específicos para as métricas quânticas
        this.addQuantumMetricsStyles();
        
        // Atualizar as métricas para preencher o painel
        this.updateQuantumMetricsUI();
    },
    
    // Adicionar estilos CSS para o painel de métricas quânticas
    addQuantumMetricsStyles: function() {
        // Verificar se os estilos já foram adicionados
        if (document.getElementById('quantum-metrics-styles')) {
            return;
        }
        
        // Criar elemento de estilos
        const style = document.createElement('style');
        style.id = 'quantum-metrics-styles';
        style.textContent = `
            .quantum-metrics-explanation {
                margin: 20px 0;
                background-color: rgba(72, 91, 196, 0.05);
                border-radius: 8px;
                padding: 15px;
                border-left: 4px solid var(--trading-accent);
            }
            
            .quantum-metrics-explanation h3 {
                color: var(--trading-accent);
                font-size: 1.1rem;
                margin-top: 0;
                margin-bottom: 15px;
                font-weight: 500;
            }
            
            .explanation-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
            
            .explanation-item {
                padding: 10px;
                background-color: rgba(255, 255, 255, 0.03);
                border-radius: 6px;
                border: 1px solid rgba(72, 91, 196, 0.1);
            }
            
            .explanation-title {
                font-weight: 500;
                color: var(--trading-text-primary);
                margin-bottom: 8px;
                font-size: 0.9rem;
            }
            
            .explanation-desc {
                font-size: 0.85rem;
                color: var(--trading-text-secondary);
                line-height: 1.5;
            }
            
            .quantum-metrics-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
                margin-bottom: 20px;
            }
            
            .metric-box {
                background-color: rgba(0,0,0,0.1);
                padding: 15px;
                border-radius: 5px;
                text-align: center;
                transition: all 0.3s ease;
            }
            
            .metric-box:hover {
                background-color: rgba(0,0,0,0.2);
                transform: translateY(-2px);
            }
            
            .metric-title {
                font-size: 14px;
                color: var(--trading-text-secondary);
                margin-bottom: 8px;
            }
            
            .metric-value {
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 5px;
            }
            
            .metric-desc {
                font-size: 12px;
                color: var(--trading-text-secondary);
                opacity: 0.8;
            }
            
            .correlation-section {
                margin-top: 20px;
                margin-bottom: 20px;
            }
            
            .correlation-section h3 {
                font-size: 16px;
                margin-bottom: 15px;
                color: var(--trading-accent);
            }
            
            .correlation-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
            
            .correlation-item {
                background-color: rgba(0,0,0,0.1);
                padding: 15px;
                border-radius: 5px;
                text-align: center;
            }
            
            .correlation-title {
                font-size: 14px;
                color: var(--trading-text-secondary);
                margin-bottom: 10px;
            }
            
            .correlation-value {
                font-size: 22px;
                font-weight: 600;
            }
            
            .correlation-value.positive {
                color: var(--trading-success);
            }
            
            .correlation-value.negative {
                color: var(--trading-danger);
            }
            
            .correlation-value.neutral {
                color: var(--trading-text-primary);
            }
            
            .metrics-chart-container {
                margin-top: 20px;
                height: 300px;
            }
            
            @media (max-width: 768px) {
                .quantum-metrics-grid {
                    grid-template-columns: repeat(2, 1fr);
                }
                
                .correlation-grid {
                    grid-template-columns: 1fr;
                }
            }
        `;
        
        // Adicionar ao head
        document.head.appendChild(style);
    },
    
    // Inicializar gráfico de métricas quânticas
    initQuantumMetricsChart: function() {
        const chartCanvas = document.getElementById('quantum-metrics-chart');
        if (!chartCanvas) {
            console.error("Canvas para gráfico de métricas quânticas não encontrado!");
            return;
        }
        
        // Verificar se o Chart.js está disponível
        if (typeof Chart === 'undefined') {
            // Carregar Chart.js dinamicamente se não estiver disponível
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js';
            script.onload = () => this.createQuantumMetricsBarChart();
            document.head.appendChild(script);
        } else {
            this.createQuantumMetricsBarChart();
        }
    },
    

    
    // Atualizar gráfico de métricas quânticas com novos dados
    updateQuantumMetricsChart: function() {
        // Verificar se temos dados de métricas quânticas
        if (!this.data.quantumMetrics) {
            if (window.DEBUG) {
                console.log("Dados de métricas quânticas não disponíveis");
            }
            return;
        }
        
        // Se não temos o gráfico ainda, criar um novo
        if (!this.data.charts || !this.data.charts.quantumMetricsChart) {
            this.createQuantumMetricsBarChart();
            return;
        }
        
        const chart = this.data.charts.quantumMetricsChart;
        
        // Atualizar dados do gráfico com valores reais (sem simulação)
        const metrics = this.data.quantumMetrics;
        
        // Extrair métricas quânticas para visualização
        chart.data.datasets[0].data = [
            metrics.page_entropy || 0,            // Entropia Quântica
            metrics.quantum_coherence || 0,       // Coerência Quântica
            metrics.otoc || 0,                    // OTOC
            metrics.loschmidt_echo || 0           // Eco de Loschmidt
        ];
        
        // Atualizar o gráfico
        chart.update();
    },

    updateQuantumLineChart: function() {
        const chartDiv = this.data.charts.quantum || document.getElementById('quantum-metrics-line-chart');
        if (!chartDiv) {
            return;
        }

        const history = this.data.metricHistory || [];
        if (history.length === 0) {
            return;
        }

        const times = history.map((h) => new Date(h.timestamp));
        const entropySeries = history.map((h) => h.page_entropy || 0);
        const coherenceSeries = history.map((h) => h.quantum_coherence || 0);

        const entropyTrace = { x: times, y: entropySeries, type: 'scatter', mode: 'lines', name: 'Entropia', line: { color: '#F44336', width: 2 } };
        const coherenceTrace = { x: times, y: coherenceSeries, type: 'scatter', mode: 'lines', name: 'Coerência', line: { color: '#4CAF50', width: 2 } };

        Plotly.react(chartDiv, [entropyTrace, coherenceTrace]);
        this.data.charts.quantum = chartDiv;
    },
    
    // Criar gráfico de barras para métricas quânticas
    createQuantumMetricsBarChart: function() {
        const chartCanvas = document.getElementById('quantum-metrics-chart');
        if (!chartCanvas || typeof Chart === 'undefined') {
            console.error("Canvas para gráfico ou Chart.js não encontrado");
            return;
        }
        
        // Inicializar objeto de charts se não existir
        if (!this.data.charts) {
            this.data.charts = {};
        }
        
        // Dados para o gráfico
        const metrics = this.data.quantumMetrics || {};
        
        // Criar o gráfico de barras
        this.data.charts.quantumMetricsChart = new Chart(chartCanvas, {
            type: 'bar',
            data: {
                labels: ['Entropia Quântica', 'Coerência Quântica', 'OTOC', 'Eco de Loschmidt'],
                datasets: [{
                    label: 'Métricas Quânticas',
                    data: [
                        metrics.page_entropy || 0,
                        metrics.quantum_coherence || 0,
                        metrics.otoc || 0,
                        metrics.loschmidt_echo || 0
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 1.0,
                        title: {
                            display: true,
                            text: 'Valor'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw.toFixed(4);
                                return `Valor: ${value}`;
                            }
                        }
                    },
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Métricas Quânticas Interpretáveis'
                    }
                }
            }
        });
    },
    
    // Atualizar métricas quânticas manualmente
    refreshQuantumMetrics: function() {
        if (window.DEBUG) {
            console.log("Atualizando métricas quânticas manualmente...");
        }
        fetch(window.location.origin + '/trading/api/quantum_metrics')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.metrics) {
                    this.data.quantumMetrics = data.metrics;
                    this.data.metricHistory = data.history || [];
                    this.updateQuantumMetricsUI();
                    this.showNotification('info', 'Métricas Atualizadas', 'Métricas quânticas atualizadas com sucesso.');
                } else {
                    this.showNotification('error', 'Erro', data.message || 'Não foi possível atualizar as métricas quânticas.');
                }
            })
            .catch(error => {
                console.error('Erro ao atualizar métricas quânticas:', error);
                this.showNotification('error', 'Erro de Conexão', 'Não foi possível atualizar as métricas quânticas.');
            });
    },

    // Enviar ordem manual
    submitManualOrder: function() {
        const symbol = document.getElementById('manual-symbol').value;
        const side = document.getElementById('manual-side').value;
        const orderType = document.getElementById('manual-type').value;
        const quantity = parseFloat(document.getElementById('manual-quantity').value);
        const price = orderType === 'limit' ? parseFloat(document.getElementById('manual-price').value) : null;
        
        // Validar entrada
        if (!symbol || !side || isNaN(quantity) || quantity <= 0) {
            this.showNotification('warning', 'Dados Incompletos', 'Por favor, preencha todos os campos corretamente.');
            return;
        }
        
        if (orderType === 'limit' && (isNaN(price) || price <= 0)) {
            this.showNotification('warning', 'Preço Inválido', 'Por favor, informe um preço válido para a ordem limit.');
            return;
        }
        
        // Confirmar ordem
        const details = `Tipo: ${orderType.toUpperCase()}\nQuantidade: ${quantity}\n${orderType === 'limit' ? `Preço: $${price}` : ''}`;
        
        this.showModal(
            `Confirmar Ordem ${side.toUpperCase()} ${symbol}`,
            `Você está prestes a enviar a seguinte ordem:\n\n${details}\n\nDeseja continuar?`,
            () => this.executeManualOrder(symbol, side, orderType, quantity, price)
        );
    },

    executeManualOrder: function(symbol, side, orderType, quantity, price) {
        fetch('/trading/api/manual-order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCsrfToken()
            },
            body: JSON.stringify({
                symbol: symbol,
                side: side,
                order_type: orderType,
                quantity: quantity,
                price: price
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification('success', 'Ordem Enviada', data.message);
                
                // Limpar campos
                document.getElementById('manual-quantity').value = '';
                document.getElementById('manual-price').value = '';
                
                // Atualizar dados
                this.refreshData();
            } else {
                this.showNotification('error', 'Erro na Ordem', data.message);
            }
        })
        .catch(error => {
            console.error('Erro ao enviar ordem:', error);
            this.showNotification('error', 'Erro na Ordem', 'Não foi possível enviar a ordem.');
        });
    },

    // Funções de UI utilitárias
    
    // Mostrar notificação
    showNotification: function(type, title, message) {
        const container = document.getElementById('notifications-container');
        
        // Criar elemento de notificação
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close">&times;</button>
        `;
        
        // Adicionar ao container
        container.appendChild(notification);
        
        // Adicionar evento de fechar
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });
        
        // Remover após 5 segundos
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    },

    // Mostrar modal
    showModal: function(title, content, onConfirm) {
        const modalOverlay = document.getElementById('modal-overlay');
        const modalTitle = document.getElementById('modal-title');
        const modalContent = document.getElementById('modal-content');
        const modalConfirm = document.getElementById('modal-confirm');
        
        // Configurar modal
        modalTitle.textContent = title;
        modalContent.textContent = content;
        
        // Evento de confirmar
        const confirmHandler = () => {
            onConfirm();
            this.closeModal();
            modalConfirm.removeEventListener('click', confirmHandler);
        };
        
        modalConfirm.addEventListener('click', confirmHandler);
        
        // Mostrar modal
        modalOverlay.classList.add('active');
    },

    // Fechar modal
    closeModal: function() {
        const modalOverlay = document.getElementById('modal-overlay');
        modalOverlay.classList.remove('active');
    },
    
    // Funções para visualização e exportação de circuitos quânticos
    refreshQuantumCircuit: function() {
        // Obter parâmetros da interface
        const steps = document.getElementById('circuit-steps').value;
        const visualizationType = document.getElementById('visualization-type').value;
        const useRealCircuit = document.getElementById('use-real-quantum-circuit').checked;
        
        // Exibir indicador de carregamento
        const visualizationArea = document.getElementById('circuit-visualization-area');
        visualizationArea.innerHTML = '<div class="loading-indicator">Carregando visualização do circuito...</div>';
        
        // Construir URL da API
        let apiUrl;
        
        // Verificar se devemos usar o circuito real gerado pelo trading ou o circuito simulado
        if (useRealCircuit) {
            // Usar o endpoint que executa o trading e gera o circuito real
            apiUrl = `/api/trading/execute_and_visualize?output_type=${visualizationType}`;
            if (window.DEBUG) {
                console.log("Executando trading para gerar circuito quântico real...");
            }
        } else {
            // Usar o endpoint de visualização simulada do circuito
            apiUrl = `/api/circuit/visualize?steps=${steps}&output_type=${visualizationType}`;
        }
        
        // Fazer solicitação com base no tipo de visualização
        if (visualizationType === 'text' || visualizationType === 'latex') {
            // Para visualizações de texto, obter resposta JSON
            fetch(apiUrl)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Exibir visualização de texto
                        visualizationArea.innerHTML = `<pre class="circuit-text">${data.circuit_visualization}</pre>`;
                        
                        // Se temos resultados da análise de trading, mostrar também
                        if (data.analysis_result) {
                            const tradingInfo = document.createElement('div');
                            tradingInfo.className = 'trading-analysis-result';
                            tradingInfo.innerHTML = `
                                <h4>Resultado da Análise de Trading:</h4>
                                <pre>${JSON.stringify(data.analysis_result, null, 2)}</pre>
                            `;
                            visualizationArea.appendChild(tradingInfo);
                        }
                    } else {
                        // Exibir mensagem de erro
                        visualizationArea.innerHTML = `<div class="error-message">Erro: ${data.message}</div>`;
                    }
                    
                    // Obter informações de compatibilidade
                    this.checkCircuitCompatibility();
                })
                .catch(error => {
                    console.error("Erro ao obter visualização do circuito:", error);
                    visualizationArea.innerHTML = `<div class="error-message">Erro ao carregar visualização do circuito: ${error}</div>`;
                });
        } else {
            // Para visualizações gráficas (mpl ou bloch), carregar imagem diretamente
            const timestamp = new Date().getTime(); // Evitar cache
            const imgUrl = `${apiUrl}&_=${timestamp}`;
            
            // Criar elemento de imagem
            const img = new Image();
            img.className = 'circuit-image';
            img.alt = 'Visualização do Circuito Quântico';
            
            // Configurar handlers de eventos
            img.onload = () => {
                visualizationArea.innerHTML = '';
                visualizationArea.appendChild(img);
                
                // Obter informações de compatibilidade
                this.checkCircuitCompatibility();
            };
            
            img.onerror = () => {
                visualizationArea.innerHTML = `<div class="error-message">Erro ao carregar imagem do circuito</div>`;
            };
            
            // Iniciar carregamento da imagem
            img.src = imgUrl;
        }
    },
    
    checkCircuitCompatibility: function() {
        // Fazer solicitação API para validar compatibilidade com hardware
        fetch('/api/circuit/export_qasm3')
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    console.error("Erro ao validar compatibilidade do circuito:", data.message);
                    return;
                }
                
                // Atualizar informações do circuito
                document.getElementById('circuit-qubits').textContent = 8; // Padrão do QUALIA
                
                // Se temos informações detalhadas do circuito
                if (data.qasm_content) {
                    // Contar gates aproximadamente (número de linhas com operações)
                    const lines = data.qasm_content.split('\n');
                    let gateCount = 0;
                    
                    for (const line of lines) {
                        // Contar linhas que contêm operações quânticas típicas
                        if (line.includes('x ') || line.includes('h ') || 
                            line.includes('cx ') || line.includes('cz ') || 
                            line.includes('rz(') || line.includes('measure ')) {
                            gateCount++;
                        }
                    }
                    
                    document.getElementById('circuit-gates').textContent = gateCount;
                }
                
                // Atualizar status de compatibilidade
                const compatibilityStatus = document.getElementById('circuit-compatibility-status');
                const compatibilityMessage = document.getElementById('compatibility-message');
                
                if (data.hardware_compatible) {
                    compatibilityStatus.className = 'compatibility-status compatible';
                    compatibilityMessage.innerHTML = `<span style="color: var(--trading-success);">✓</span> Circuito compatível com hardware quântico real`;
                } else {
                    compatibilityStatus.className = 'compatibility-status incompatible';
                    
                    // Listar problemas de compatibilidade
                    let issuesHtml = `<span style="color: var(--trading-danger);">✗</span> Circuito não compatível com hardware real:<br>`;
                    
                    if (data.compatibility_issues) {
                        const issues = data.compatibility_issues;
                        
                        if (issues.depth) {
                            issuesHtml += `• Profundidade excessiva: ${issues.depth} camadas<br>`;
                            document.getElementById('circuit-depth').textContent = issues.depth;
                        }
                        
                        if (issues.total_operations) {
                            issuesHtml += `• Operações excessivas: ${issues.total_operations} gates<br>`;
                        }
                        
                        // Listar gates não suportados
                        for (const gate in issues) {
                            if (gate !== 'depth' && gate !== 'total_operations' && gate !== 'limited_support') {
                                issuesHtml += `• Gate não suportado: ${gate} (${issues[gate]}x)<br>`;
                            }
                        }
                        
                        // Listar gates com suporte limitado
                        if (issues.limited_support) {
                            for (const gate in issues.limited_support) {
                                issuesHtml += `• Gate com suporte limitado: ${gate} (${issues.limited_support[gate]}x)<br>`;
                            }
                        }
                    }
                    
                    compatibilityMessage.innerHTML = issuesHtml;
                }
            })
            .catch(error => {
                console.error("Erro ao verificar compatibilidade:", error);
            });
    },
    
    exportQuantumCircuit: function() {
        // Obter valor de steps
        const steps = document.getElementById('circuit-steps').value;
        
        // Mostrar modal de confirmação
        this.showModal({
            title: 'Exportar Circuito Quântico',
            content: `
                <p>Selecione o formato de exportação:</p>
                <div style="display: flex; flex-direction: column; gap: 10px; margin-top: 15px;">
                    <button id="download-qasm" class="quantum-button">Download QASM 3.0</button>
                    <button id="view-qasm" class="quantum-button">Visualizar QASM 3.0</button>
                </div>
            `,
            showCancel: true,
            onOpen: () => {
                // Configurar event listeners para os botões
                document.getElementById('download-qasm').addEventListener('click', () => {
                    window.location.href = `/api/circuit/export_qasm3?steps=${steps}&download=true`;
                    this.closeModal();
                });
                
                document.getElementById('view-qasm').addEventListener('click', () => {
                    fetch(`/api/circuit/export_qasm3?steps=${steps}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.qasm_content) {
                                this.closeModal();
                                
                                // Mostrar novo modal com o conteúdo QASM
                                this.showModal({
                                    title: 'Código QASM 3.0',
                                    content: `
                                        <div style="position: relative;">
                                            <pre style="background-color: rgba(0,0,0,0.2); padding: 15px; border-radius: 4px; max-height: 400px; overflow: auto; white-space: pre-wrap; font-family: monospace; font-size: 12px;">${data.qasm_content}</pre>
                                            <button id="copy-qasm" class="quantum-button small" style="position: absolute; top: 5px; right: 5px;">Copiar</button>
                                        </div>
                                    `,
                                    showCancel: false,
                                    onOpen: () => {
                                        document.getElementById('copy-qasm').addEventListener('click', () => {
                                            navigator.clipboard.writeText(data.qasm_content).then(() => {
                                                alert('Código QASM copiado para a área de transferência!');
                                            });
                                        });
                                    }
                                });
                            } else {
                                alert('Erro ao obter código QASM: ' + (data.message || 'Erro desconhecido'));
                            }
                        })
                        .catch(error => {
                            console.error("Erro ao obter QASM:", error);
                            alert('Erro ao obter código QASM.');
                        });
                });
            }
        });
    }
};

// Inicializar quando documento estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    tradingUI.init();
});