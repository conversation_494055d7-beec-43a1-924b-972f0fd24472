#!/usr/bin/env bash
# scripts/install_test_deps.sh
#
# Instala apenas as dependências necessárias para rodar a suíte de testes.
# Útil para ambientes de CI ou para desenvolvedores que já possuem o projeto
# clonado e querem executar os testes rapidamente. O arquivo
# `requirements-test.txt` contém todas as bibliotecas usadas nos testes,
# como pandas, scipy, scikit-learn, pydantic e pytest-mock.
set -euo pipefail

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

python -m pip install --upgrade pip
pip install -r requirements.txt
pip install -r requirements-test.txt
pip install -e ".[dev]"
