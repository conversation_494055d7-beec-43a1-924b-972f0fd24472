#!/usr/bin/env python3
"""
YAA: Script para auditoria de thresholds e pesos do sistema QUALIA.

Executa análise completa de distribuições de raw_score vs. decision
e otimização automática de parâmetros.

Uso:
    python scripts/audit_thresholds.py --days 7 --output reports/audit_report.json
    python scripts/audit_thresholds.py --optimize-weights --grid-search
"""

import argparse
import asyncio
import json
import sys
from pathlib import Path

# Adiciona o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.analysis.threshold_auditor import ThresholdAuditor
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


async def main():
    parser = argparse.ArgumentParser(description="Auditoria de thresholds e pesos QUALIA")
    parser.add_argument(
        "--days", 
        type=float, 
        default=1.0,
        help="Período de análise em dias (padrão: 1.0)"
    )
    parser.add_argument(
        "--output", 
        type=str,
        default="data/analysis/audit_report.json",
        help="Arquivo de saída do relatório"
    )
    parser.add_argument(
        "--optimize-weights",
        action="store_true",
        help="Executa otimização de pesos"
    )
    parser.add_argument(
        "--grid-search",
        action="store_true", 
        help="Usa grid search para otimização (padrão: análise simples)"
    )
    parser.add_argument(
        "--threshold-only",
        action="store_true",
        help="Executa apenas análise de threshold"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Logging detalhado"
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        import logging
        logging.getLogger("qualia").setLevel(logging.DEBUG)
    
    logger.info("🔍 Iniciando auditoria de thresholds e pesos QUALIA")
    
    # Inicializa auditor
    auditor = ThresholdAuditor(data_path="data/analysis")
    
    # Carrega dados do período especificado
    records = auditor.load_decision_records(max_age_hours=args.days * 24)
    
    if not records:
        logger.error("❌ Nenhum registro de decisão encontrado para análise")
        return 1
        
    logger.info(f"📊 Analisando {len(records)} registros de decisão")
    
    # Análise de distribuições
    logger.info("📈 Analisando distribuições de score...")
    score_analysis = auditor.analyze_score_distributions(records)
    
    print("\n" + "="*60)
    print("📊 DISTRIBUIÇÕES DE SCORE")
    print("="*60)
    
    actions_dist = score_analysis.get("actions_distribution", {})
    for action, count in actions_dist.items():
        percentage = (count / score_analysis["total_decisions"]) * 100
        print(f"{action:>6}: {count:>4} ({percentage:>5.1f}%)")
        
    print(f"\nTotal de decisões: {score_analysis['total_decisions']}")
    
    # Estatísticas por ação
    score_stats = score_analysis.get("score_statistics", {})
    if score_stats:
        print("\n📈 Estatísticas de Score por Ação:")
        for action, stats in score_stats.items():
            print(f"\n{action}:")
            print(f"  Média: {stats['mean']:.3f} ± {stats['std']:.3f}")
            print(f"  Min/Max: {stats['min']:.3f} / {stats['max']:.3f}")
            print(f"  P50/P90: {stats['percentiles']['50']:.3f} / {stats['percentiles']['90']:.3f}")
    
    # Análise de threshold
    if not args.optimize_weights or args.threshold_only:
        logger.info("🎯 Otimizando threshold...")
        threshold_analysis = auditor.optimize_threshold(records)
        
        if threshold_analysis:
            print("\n" + "="*60)
            print("🎯 OTIMIZAÇÃO DE THRESHOLD")
            print("="*60)
            print(f"Threshold atual:  {threshold_analysis.current_threshold:.3f}")
            print(f"Threshold ótimo:  {threshold_analysis.optimal_threshold:.3f}")
            print(f"Precisão:         {threshold_analysis.precision:.3f}")
            print(f"Recall:           {threshold_analysis.recall:.3f}")
            print(f"F1-Score:         {threshold_analysis.f1_score:.3f}")
            print(f"Acurácia:         {threshold_analysis.accuracy:.3f}")
            
            improvement = threshold_analysis.optimal_threshold - threshold_analysis.current_threshold
            if abs(improvement) > 0.05:
                print(f"\n💡 RECOMENDAÇÃO: Ajustar threshold em {improvement:+.3f}")
            else:
                print(f"\n✅ Threshold atual está bem calibrado")
    
    # Otimização de pesos
    weight_optimization = None
    if args.optimize_weights and not args.threshold_only:
        logger.info("⚖️ Otimizando pesos de unificação...")
        
        if args.grid_search:
            weight_optimization = auditor.grid_search_weights(records)
        else:
            # Análise simples de pesos
            import pandas as pd
            df = pd.DataFrame([{
                "strategy": r.strategy_weight,
                "holographic": r.holographic_weight, 
                "metacognition": r.metacognition_weight,
                "action": r.action,
                "confidence": r.confidence
            } for r in records])
            
            print("\n" + "="*60)
            print("⚖️ ANÁLISE DE PESOS ATUAIS")
            print("="*60)
            
            current_weights = {
                "strategy": df["strategy"].mean(),
                "holographic": df["holographic"].mean(),
                "metacognition": df["metacognition"].mean()
            }
            
            for component, weight in current_weights.items():
                print(f"{component:>12}: {weight:.3f}")
                
            # Correlação com confiança
            trade_df = df[df["action"].isin(["BUY", "SELL"])]
            if len(trade_df) > 10:
                print(f"\n📊 Correlação com confiança (trades):")
                for component in ["strategy", "holographic", "metacognition"]:
                    corr = trade_df[component].corr(trade_df["confidence"])
                    print(f"{component:>12}: {corr:>6.3f}")
        
        if weight_optimization:
            print("\n" + "="*60)
            print("⚖️ OTIMIZAÇÃO DE PESOS")
            print("="*60)
            
            orig = weight_optimization.original_weights
            opt = weight_optimization.optimal_weights
            
            print("Pesos atuais:")
            for component, weight in orig.items():
                print(f"  {component:>12}: {weight:.3f}")
                
            print("\nPesos otimizados:")
            for component, weight in opt.items():
                print(f"  {component:>12}: {weight:.3f}")
                
            print(f"\nMelhoria esperada: {weight_optimization.performance_improvement:+.3f}")
            ci = weight_optimization.confidence_interval
            print(f"Intervalo de confiança: [{ci[0]:.3f}, {ci[1]:.3f}]")
    
    # Gera relatório completo
    logger.info("📋 Gerando relatório completo...")
    report = {
        "metadata": {
            "analysis_period_days": args.days,
            "total_records": len(records),
            "generated_at": score_analysis.get("timestamp")
        },
        "score_distributions": score_analysis,
        "threshold_analysis": threshold_analysis.__dict__ if threshold_analysis else None,
        "weight_optimization": weight_optimization.__dict__ if weight_optimization else None
    }
    
    # Salva relatório
    output_path = Path(args.output)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, default=str)
        
    logger.info(f"✅ Relatório salvo em: {output_path}")
    
    # Recomendações finais
    print("\n" + "="*60)
    print("💡 RECOMENDAÇÕES")
    print("="*60)
    
    if threshold_analysis and abs(threshold_analysis.optimal_threshold - threshold_analysis.current_threshold) > 0.05:
        print(f"1. Ajustar decision_threshold: {threshold_analysis.current_threshold:.3f} → {threshold_analysis.optimal_threshold:.3f}")
        
    if weight_optimization and weight_optimization.performance_improvement > 0.02:
        print("2. Aplicar pesos otimizados:")
        for component, weight in weight_optimization.optimal_weights.items():
            print(f"   {component}: {weight:.3f}")
            
    hold_rate = actions_dist.get("HOLD", 0) / score_analysis["total_decisions"]
    if hold_rate > 0.8:
        print(f"3. Taxa de HOLD muito alta ({hold_rate:.1%}) - considerar reduzir thresholds")
    elif hold_rate < 0.3:
        print(f"3. Taxa de HOLD muito baixa ({hold_rate:.1%}) - considerar aumentar thresholds")
        
    print(f"\n📊 Análise completa disponível em: {output_path}")
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
