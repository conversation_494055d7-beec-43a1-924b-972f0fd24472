# QUALIA Staging Environment Configuration
# P-02.1: Deploy em Ambiente de Staging
# Configuração para validação completa antes de produção

# Environment Settings
environment: "staging"
debug_mode: true
verbose_logging: true
log_level: "DEBUG"

# Trading Configuration - Identical to Production for Validation
trading:
  # Optimized Parameters (Validated Sharpe 5.340)
  price_amplification: 1.0
  news_amplification: 11.3
  min_confidence: 0.37
  
  # Risk Management - Conservative for Staging
  max_position_size_percent: 1.0  # Even more conservative than production (2%)
  max_drawdown_percent: 10.0      # More conservative than production (15%)
  stop_loss_percent: 5.0
  take_profit_percent: 10.0
  
  # Capital Management - Staging with Minimal Capital
  initial_capital: 100.0          # $100 for staging tests
  max_daily_trades: 10
  max_concurrent_positions: 3
  
  # Symbols for Staging Testing
  symbols:
    - "BTC/USDT"
    - "ETH/USDT"
    - "POL/USDT"    # Validated symbol from production tests
    - "ADA/USDT"
  
  # Timeframes
  timeframes:
    - "1m"
    - "5m"
    - "15m"
    - "1h"

# Exchange Configuration - KuCoin Sandbox
exchange:
  name: "kucoin"
  environment: "sandbox"  # Use sandbox for staging
  api_base_url: "https://openapi-sandbox.kucoin.com"
  websocket_url: "wss://ws-api-sandbox.kucoin.com"
  
  # Rate Limiting - Conservative for Staging
  rate_limit:
    requests_per_second: 5
    burst_limit: 10
    cooldown_seconds: 1
  
  # Timeouts
  timeouts:
    connect_timeout: 10
    read_timeout: 30
    total_timeout: 60

# Live Feed Configuration
live_feed:
  enabled: true
  provider: "kucoin"
  environment: "sandbox"
  
  # Feed Settings
  buffer_size: 1000
  update_interval_ms: 100
  reconnect_attempts: 5
  reconnect_delay_seconds: 5
  
  # Data Quality
  data_validation: true
  outlier_detection: true
  latency_monitoring: true
  max_latency_ms: 200  # More lenient for staging

# Bayesian Optimization - Reduced for Staging
bayesian_optimization:
  enabled: true
  n_trials_per_cycle: 10        # Reduced from production (25)
  optimization_interval: 1800   # 30 minutes (vs 1 hour in production)
  
  # Optimization Targets
  primary_metric: "sharpe_ratio"
  secondary_metrics:
    - "total_pnl"
    - "max_drawdown"
    - "win_rate"
  
  # Parameter Bounds
  parameter_bounds:
    price_amplification: [0.5, 2.0]
    news_amplification: [5.0, 15.0]
    min_confidence: [0.2, 0.6]

# Monitoring Configuration - Enhanced for Staging
monitoring:
  enabled: true
  environment: "staging"
  
  # Metrics Collection
  collect_system_metrics: true
  collect_trading_metrics: true
  collect_performance_metrics: true
  
  # Monitoring Intervals
  system_check_interval: 30      # 30 seconds
  trading_check_interval: 60     # 1 minute
  performance_check_interval: 300 # 5 minutes
  
  # Alerting - Email only for staging
  alerts:
    enabled: true
    channels: ["email"]  # No SMS/Slack for staging
    
    # Alert Thresholds - More sensitive for staging
    thresholds:
      max_drawdown_percent: 8.0    # Lower than production (12%)
      min_sharpe_ratio: 1.0        # Higher than production (0.5)
      max_consecutive_losses: 3    # Lower than production (5)
      system_cpu_percent: 80       # Same as production
      system_memory_percent: 80    # Same as production
      api_error_rate_percent: 10   # Lower than production (15%)

# Logging Configuration - Detailed for Staging
logging:
  level: "DEBUG"
  format: "json"
  
  # Log Files
  files:
    main: "logs/staging_qualia.log"
    trading: "logs/staging_trading.log"
    errors: "logs/staging_errors.log"
    performance: "logs/staging_performance.log"
  
  # Log Rotation
  rotation:
    max_size_mb: 50
    backup_count: 10
    interval: "daily"
  
  # Log Correlation
  correlation_tracking: true
  request_id_header: "X-Request-ID"

# Backup Configuration - Simplified for Staging
backup:
  enabled: true
  environment: "staging"
  
  # Backup Schedule - More frequent for staging validation
  schedule:
    full_backup_interval: "6h"     # Every 6 hours
    incremental_interval: "1h"     # Every hour
    retention_days: 7              # 1 week retention
  
  # Backup Locations
  local_backup_dir: "backups/staging"
  
  # Backup Validation
  verify_backups: true
  test_restore: true

# Security Configuration - Same as Production
security:
  encryption:
    algorithm: "AES-256-GCM"
    key_derivation: "PBKDF2"
    iterations: 100000
  
  # Credential Management
  credentials:
    rotation_interval_days: 7      # More frequent than production (30)
    validation_interval_minutes: 30
    audit_logging: true
  
  # File Permissions
  file_permissions:
    config_files: "644"
    secret_files: "600"
    log_files: "644"
    backup_files: "600"

# Performance Configuration
performance:
  # System Resources
  max_cpu_percent: 70
  max_memory_percent: 70
  max_disk_usage_percent: 80
  
  # Trading Performance
  target_latency_ms: 100
  max_order_processing_time_ms: 500
  
  # Data Processing
  max_data_processing_time_ms: 1000
  buffer_flush_interval_ms: 5000

# Validation Configuration - Comprehensive for Staging
validation:
  enabled: true
  
  # Pre-deployment Validation
  pre_deployment_checks: true
  system_requirements_check: true
  configuration_validation: true
  connectivity_tests: true
  
  # Runtime Validation
  runtime_checks: true
  data_quality_validation: true
  performance_monitoring: true
  
  # Validation Intervals
  system_validation_interval: 300    # 5 minutes
  data_validation_interval: 60       # 1 minute
  performance_validation_interval: 180 # 3 minutes

# Testing Configuration - Staging Specific
testing:
  enabled: true
  
  # Test Types
  unit_tests: true
  integration_tests: true
  end_to_end_tests: true
  performance_tests: true
  
  # Test Data
  use_historical_data: true
  historical_data_days: 30
  
  # Test Execution
  parallel_execution: false  # Sequential for staging clarity
  test_timeout_seconds: 300
  
  # Test Reporting
  generate_reports: true
  report_format: "json"
  report_location: "reports/staging"

# Development Configuration - Staging Specific
development:
  hot_reload: true
  debug_endpoints: true
  profiling: true
  
  # Development Tools
  enable_debugger: true
  enable_profiler: true
  enable_metrics_dashboard: true
  
  # Development Ports
  debug_port: 5678
  metrics_port: 8080
  dashboard_port: 3000
