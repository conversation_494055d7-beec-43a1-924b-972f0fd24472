# src/qualia/personas/base.py

from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Dict, Any

class BasePersona(ABC):
    """
    Classe base abstrata para todas as Personas no Simulador de Ecossistemas de Agentes.

    Cada Persona representa um arquétipo de ator no mercado, com seu próprio estado interno,
    função de utilidade e processo de tomada de decisão.
    """

    def __init__(self, persona_id: str, config: Dict[str, Any]):
        """
        Inicializa a Persona.

        Args:
            persona_id: Identificador único para esta instância da persona.
            config: Dicionário de configuração específico para esta persona.
        """
        self.persona_id = persona_id
        self.config = config
        self.state: Dict[str, Any] = self._initialize_state()

    @abstractmethod
    def _initialize_state(self) -> Dict[str, Any]:
        """
        Inicializa o estado interno da persona. Deve ser implementado por subclasses.
        Ex: {"confidence": 0.5, "capital": 100000, "action_bias": "NEUTRAL"}
        """
        raise NotImplementedError

    @abstractmethod
    def update_state(self, market_data: Dict[str, Any], external_data: Dict[str, Any]) -> None:
        """
        Atualiza o estado interno da persona com base em novos dados de mercado e externos (farsight).
        Deve ser implementado por subclasses.

        Args:
            market_data: Dicionário com os dados de mercado mais recentes.
            external_data: Dicionário com os insights do `farsight` (ex: `CollectiveMindState`).
        """
        raise NotImplementedError

    @abstractmethod
    def get_probable_action(self, market_state: Dict[str, Any]) -> Dict[str, float]:
        """
        Calcula e retorna as probabilidades das próximas ações da persona.
        Deve ser implementado por subclasses.

        Args:
            market_state: Dicionário que representa o estado atual do mercado.

        Returns:
            Um dicionário (DecisionVector) com as probabilidades de ação.
            Ex: {"BUY": 0.7, "SELL": 0.1, "HOLD": 0.2}
        """
        raise NotImplementedError

    def get_persona_id(self) -> str:
        """
        Retorna o ID da persona.
        """
        return self.persona_id

    def get_current_state(self) -> Dict[str, Any]:
        """
        Retorna uma cópia do estado interno atual da persona.
        """
        return self.state.copy()
