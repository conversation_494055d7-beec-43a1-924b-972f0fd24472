import pytest

pytest.importorskip("flask")
import types
import sys
import numpy as np
from flask import Flask

import pytest


class DummyUniverseBase:
    pass


sys.modules.setdefault(
    "qualia.core.universe", types.ModuleType("qualia.core.universe")
).QUALIAQuantumUniverse = DummyUniverseBase
sys.modules.setdefault(
    "qualia.core.consciousness", types.ModuleType("qualia.core.consciousness")
).QUALIAConsciousness = type("DummyConsciousness", (), {})

from qualia.visualization import consciousness_visualization as cv


class DummyUniverse(DummyUniverseBase):
    def __init__(self) -> None:
        self.n_qubits = 2
        self.last_statevector = types.SimpleNamespace(data=np.array([1, 0, 0, 0]))

    def get_metrics(self):
        class M:
            def get_metrics_dict(self):
                return {"dummy": 1}

        return M()


def test_get_dashboard_data(monkeypatch):
    monkeypatch.setattr(cv, "QUALIAQuantumUniverse", DummyUniverse)
    viz = cv.QUALIAConsciousnessVisualization(data_source=DummyUniverse())
    with viz.flask_app.app_context():
        resp, status = cv.get_dashboard_data()
        assert status == 503
        viz.state.active = True
        resp2 = cv.get_dashboard_data()
        assert resp2.status_code == 200
        data = resp2.get_json()
        assert data["universe_metrics"]["dummy"] == 1


def test_start_stop_single_thread():
    viz = cv.QUALIAConsciousnessVisualization(
        data_source=DummyUniverse(),
        port=5051,
    )
    viz.start()
    first_thread = viz.thread
    assert first_thread is not None and first_thread.is_alive()

    viz.start()
    assert viz.thread is first_thread

    viz.stop()
    assert viz.thread is None
    assert viz._server is None
    assert not first_thread.is_alive()
