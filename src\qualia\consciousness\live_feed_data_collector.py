"""
QUALIA Live Feed Data Collector - Integração do Live Feed com Sistema de Trading

Este módulo integra o sistema de live feed (D-03) com o sistema de trading existente,
convertendo dados em tempo real para o formato esperado pelos componentes QUALIA.
"""

from __future__ import annotations

import asyncio
import time
import numpy as np
from typing import Dict, List, Any, Optional, Callable, TYPE_CHECKING
from dataclasses import dataclass
from datetime import datetime

from ..live_feed import (
    FeedManager,
    NormalizedTicker,
    NormalizedOrderBook,
    NormalizedTrade,
)
from .enhanced_data_collector import EnhancedMarketData, EnhancedDataCollector
from .holographic_universe import HolographicEvent
from .amplification_calibrator import AmplificationCalibrator
from ..utils.logging_config import get_qualia_logger
from ..event_bus import SimpleEventBus
from ..utils.event_bus import MarketDataUpdated
from ..config.data_sources import load_data_sources_defaults

if TYPE_CHECKING:
    from .holographic_universe import HolographicMarketUniverse

logger = get_qualia_logger(__name__)


@dataclass
class LiveFeedConfig:
    """Configuração do Live Feed Data Collector."""

    # Configuração do feed manager
    enable_kucoin: bool = True
    enable_binance: bool = False  # Para futuras expansões
    enable_coinbase: bool = False

    # Configuração de agregação
    aggregation_enabled: bool = True
    price_variance_threshold: float = 0.005  # 0.5%
    max_age_seconds: float = 30.0

    # Configuração de conversão para EnhancedMarketData
    enable_technical_indicators: bool = True
    rsi_period: int = 14
    volume_lookback: int = 20

    # Configuração de eventos holográficos
    enable_holographic_conversion: bool = True
    confidence_threshold: float = 0.8

    # Configuração de performance
    update_interval: float = 1.0  # Intervalo mínimo entre updates
    batch_size: int = 10  # Tamanho do batch para processamento


class LiveFeedDataCollector:
    """
    Coletor de dados que integra o live feed system com o sistema de trading QUALIA.

    Funcionalidades:
    - Coleta dados em tempo real via live feed system
    - Converte para formato EnhancedMarketData
    - Integra com event bus existente
    - Mantém compatibilidade com AmplificationCalibrator
    - Gera eventos holográficos
    """

    def __init__(
        self,
        symbols: List[str],
        config: Optional[Dict[str, Any]] = None,
        event_bus: Optional[SimpleEventBus] = None,
        amplification_calibrator: Optional[AmplificationCalibrator] = None,
        enhanced_data_collector: Optional[EnhancedDataCollector] = None,
    ):

        self.symbols = symbols
        self.config = LiveFeedConfig(**(config or {}))
        self.event_bus = event_bus
        self.amplification_calibrator = amplification_calibrator
        self.enhanced_data_collector = enhanced_data_collector

        # Estado interno
        self.is_running = False
        self.feed_manager: Optional[FeedManager] = None
        self.last_update_time = 0.0
        self.data_buffer: Dict[str, List[NormalizedTicker]] = {}
        self.processed_count = 0

        # Cache para indicadores técnicos
        self.price_history: Dict[str, List[float]] = {}
        self.volume_history: Dict[str, List[float]] = {}

        # Histórico de orderbooks e trades
        self.orderbook_history: Dict[str, List[NormalizedOrderBook]] = {}
        self.trade_history: Dict[str, List[NormalizedTrade]] = {}

        # Configuração do feed manager
        self.feed_config = self._load_feed_config()

        logger.info(f"LiveFeedDataCollector inicializado para símbolos: {symbols}")

    def _load_feed_config(self) -> Dict[str, Any]:
        """Carrega configuração do feed manager."""
        try:
            import os

            # Carregar configuração base
            base_config = load_data_sources_defaults()

            # Configuração específica para live feed com credenciais das variáveis de ambiente
            feed_config = {
                "exchanges": {
                    "kucoin": {
                        "api_key": os.getenv("KUCOIN_API_KEY", ""),
                        "api_secret": os.getenv("KUCOIN_SECRET_KEY", ""),
                        "password": os.getenv("KUCOIN_PASSPHRASE", ""),
                        "sandbox": False,  # KuCoin não tem sandbox - usar produção
                        "timeout": 60.0,  # Timeout aumentado para melhor tolerância
                        "rate_limit": 5.0,  # Rate limit muito conservador (5 segundos entre requests)
                    }
                }
            }

            # Debug: Verificar se as credenciais foram carregadas
            logger.info(f"🔍 Credenciais carregadas no feed_config:")
            logger.info(
                f"   KUCOIN_API_KEY: {'✅ Presente' if feed_config['exchanges']['kucoin']['api_key'] else '❌ Ausente'}"
            )
            logger.info(
                f"   KUCOIN_SECRET_KEY: {'✅ Presente' if feed_config['exchanges']['kucoin']['api_secret'] else '❌ Ausente'}"
            )
            logger.info(
                f"   KUCOIN_PASSPHRASE: {'✅ Presente' if feed_config['exchanges']['kucoin']['password'] else '❌ Ausente'}"
            )

            # Merge com configuração base se disponível
            if base_config and "exchanges" in base_config:
                # Preservar credenciais das variáveis de ambiente
                kucoin_creds = feed_config["exchanges"]["kucoin"].copy()
                feed_config["exchanges"].update(base_config.get("exchanges", {}))
                # Restaurar credenciais das variáveis de ambiente
                if "kucoin" in feed_config["exchanges"]:
                    feed_config["exchanges"]["kucoin"].update(kucoin_creds)

            return feed_config

        except Exception as e:
            logger.warning(f"Erro ao carregar configuração do feed: {e}")
            # Fallback com credenciais das variáveis de ambiente
            import os

            return {
                "exchanges": {
                    "kucoin": {
                        "api_key": os.getenv("KUCOIN_API_KEY", ""),
                        "api_secret": os.getenv("KUCOIN_SECRET_KEY", ""),
                        "password": os.getenv("KUCOIN_PASSPHRASE", ""),
                    }
                }
            }

    async def initialize(self) -> bool:
        """Inicializa o live feed data collector."""
        try:
            logger.info("🚀 Inicializando Live Feed Data Collector...")

            # Criar feed manager
            self.feed_manager = FeedManager(
                config=self.feed_config,
                symbols=self._convert_symbols_to_exchange_format(),
                enable_kucoin=self.config.enable_kucoin,
                aggregation_enabled=self.config.aggregation_enabled,
            )

            # Configurar callbacks
            self.feed_manager.set_ticker_callback(self._on_ticker_update)
            self.feed_manager.set_orderbook_callback(self._on_orderbook_update)
            self.feed_manager.set_system_alert_callback(self._on_system_alert)

            # Inicializar buffers
            for symbol in self.symbols:
                self.data_buffer[symbol] = []
                self.price_history[symbol] = []
                self.volume_history[symbol] = []
                self.orderbook_history[symbol] = []
                self.trade_history[symbol] = []

            logger.info("✅ Live Feed Data Collector inicializado")
            return True

        except Exception as e:
            logger.error(f"❌ Erro ao inicializar Live Feed Data Collector: {e}")
            return False

    def _convert_symbols_to_exchange_format(self) -> List[str]:
        """Converte símbolos do formato QUALIA para formato da exchange."""
        converted = []
        for symbol in self.symbols:
            # Converter de BTC/USDT para BTC-USDT (formato KuCoin)
            if "/" in symbol:
                converted.append(symbol.replace("/", "-"))
            else:
                converted.append(symbol)
        return converted

    def _convert_symbol_from_exchange_format(self, symbol: str) -> str:
        """Converte símbolo do formato da exchange para formato QUALIA."""
        # Converter de BTC-USDT para BTC/USDT
        if "-" in symbol:
            return symbol.replace("-", "/")
        return symbol

    async def start(self) -> bool:
        """Inicia a coleta de dados em tempo real."""
        try:
            if not self.feed_manager:
                logger.error("Feed manager não inicializado")
                return False

            logger.info("🔄 Iniciando coleta de dados em tempo real...")

            # Iniciar feed manager
            success = await self.feed_manager.start()
            if not success:
                logger.error("Falha ao iniciar feed manager")
                return False

            self.is_running = True
            self.last_update_time = time.time()

            # Iniciar task de processamento
            asyncio.create_task(self._processing_loop())

            logger.info("✅ Live Feed Data Collector iniciado")
            return True

        except Exception as e:
            logger.error(f"❌ Erro ao iniciar Live Feed Data Collector: {e}")
            return False

    async def stop(self):
        """Para a coleta de dados."""
        try:
            logger.info("🛑 Parando Live Feed Data Collector...")

            self.is_running = False

            if self.feed_manager:
                await self.feed_manager.stop()

            logger.info("✅ Live Feed Data Collector parado")

        except Exception as e:
            logger.error(f"❌ Erro ao parar Live Feed Data Collector: {e}")

    def _on_ticker_update(self, ticker: NormalizedTicker):
        """Callback para atualizações de ticker."""
        try:
            symbol = self._convert_symbol_from_exchange_format(ticker.symbol)

            if symbol in self.data_buffer:
                self.data_buffer[symbol].append(ticker)

                # Manter histórico para indicadores técnicos
                self.price_history[symbol].append(ticker.price)
                self.volume_history[symbol].append(ticker.volume_24h or 0.0)

                # Limitar tamanho do histórico
                max_history = (
                    max(self.config.rsi_period, self.config.volume_lookback) + 10
                )
                if len(self.price_history[symbol]) > max_history:
                    self.price_history[symbol] = self.price_history[symbol][
                        -max_history:
                    ]
                    self.volume_history[symbol] = self.volume_history[symbol][
                        -max_history:
                    ]

                logger.debug(f"Ticker recebido: {symbol} @ ${ticker.price:.4f}")

        except Exception as e:
            logger.error(f"Erro ao processar ticker: {e}")

    def _on_orderbook_update(self, orderbook: NormalizedOrderBook):
        """Callback para atualizações de orderbook."""
        try:
            symbol = self._convert_symbol_from_exchange_format(orderbook.symbol)
            logger.debug(f"Orderbook recebido: {symbol}")

            if symbol in self.orderbook_history:
                self.orderbook_history[symbol].append(orderbook)
                self.orderbook_history[symbol] = self.orderbook_history[symbol][
                    -self.config.batch_size :
                ]

        except Exception as e:
            logger.error(f"Erro ao processar orderbook: {e}")

    def _on_trade_update(self, trade: NormalizedTrade):
        """Callback para atualizações de trade."""
        try:
            symbol = self._convert_symbol_from_exchange_format(trade.symbol)
            logger.debug(
                f"Trade recebido: {symbol} - {trade.size} @ ${trade.price:.4f}"
            )

            if symbol in self.trade_history:
                self.trade_history[symbol].append(trade)
                self.trade_history[symbol] = self.trade_history[symbol][
                    -self.config.batch_size :
                ]

        except Exception as e:
            logger.error(f"Erro ao processar trade: {e}")

    def _on_system_alert(self, alert_type: str, alert_data: Dict[str, Any]):
        """Callback para alertas do sistema."""
        try:
            logger.warning(f"🚨 Sistema Alert: {alert_type} - {alert_data}")

            if self.event_bus is not None:
                payload = {"type": alert_type, **alert_data}
                self.event_bus.publish("system.alert", payload)

        except Exception as e:
            logger.error(f"Erro ao processar alerta: {e}")

    async def _processing_loop(self):
        """Loop principal de processamento de dados."""
        logger.info("🔄 Iniciando loop de processamento de dados")

        while self.is_running:
            try:
                current_time = time.time()

                # Verificar se é hora de processar
                if current_time - self.last_update_time >= self.config.update_interval:
                    await self._process_buffered_data()
                    self.last_update_time = current_time

                # Aguardar próximo ciclo
                await asyncio.sleep(0.1)

            except Exception as e:
                logger.error(f"Erro no loop de processamento: {e}")
                await asyncio.sleep(1.0)

        logger.info("🛑 Loop de processamento finalizado")

    async def _process_buffered_data(self):
        """Processa dados em buffer e gera eventos."""
        try:
            enhanced_data_list = []

            for symbol, ticker_list in self.data_buffer.items():
                if not ticker_list:
                    continue

                # Pegar o ticker mais recente
                latest_ticker = ticker_list[-1]

                # Converter para EnhancedMarketData
                enhanced_data = await self._convert_to_enhanced_market_data(
                    symbol, latest_ticker
                )

                if enhanced_data:
                    enhanced_data_list.append(enhanced_data)

                # Limpar buffer (manter apenas os últimos N itens)
                self.data_buffer[symbol] = ticker_list[-self.config.batch_size :]

            if enhanced_data_list:
                # Publicar evento no event bus
                await self._publish_market_data_event(enhanced_data_list)

                # Gerar eventos holográficos se habilitado
                if self.config.enable_holographic_conversion:
                    await self._generate_holographic_events(enhanced_data_list)

                self.processed_count += len(enhanced_data_list)
                logger.debug(f"Processados {len(enhanced_data_list)} dados de mercado")

        except Exception as e:
            logger.error(f"Erro ao processar dados em buffer: {e}")

    async def _convert_to_enhanced_market_data(
        self, symbol: str, ticker: NormalizedTicker
    ) -> Optional[EnhancedMarketData]:
        """Converte NormalizedTicker para EnhancedMarketData."""
        try:
            # Calcular indicadores técnicos se habilitado
            rsi = None
            volume_ratio = None
            price_change_pct = ticker.change_24h_percent
            volatility = None

            if self.config.enable_technical_indicators:
                rsi = self._calculate_rsi(symbol)
                volume_ratio = self._calculate_volume_ratio(symbol)
                volatility = self._calculate_volatility(symbol)

            # Criar EnhancedMarketData
            enhanced_data = EnhancedMarketData(
                symbol=symbol,
                timeframe="realtime",
                timestamp=ticker.timestamp,
                open=ticker.price,  # Para dados em tempo real, usar preço atual
                high=ticker.high_24h or ticker.price,
                low=ticker.low_24h or ticker.price,
                close=ticker.price,
                volume=ticker.volume_24h or 0.0,
                rsi=rsi,
                volume_ratio=volume_ratio,
                price_change_pct=price_change_pct,
                volatility=volatility,
                source="live_feed",
            )

            return enhanced_data

        except Exception as e:
            logger.error(f"Erro ao converter ticker para EnhancedMarketData: {e}")
            return None

    def _calculate_rsi(self, symbol: str) -> Optional[float]:
        """Calcula RSI para o símbolo."""
        try:
            prices = self.price_history.get(symbol, [])
            if len(prices) < self.config.rsi_period + 1:
                return None

            # Implementação simples de RSI
            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)

            avg_gain = np.mean(gains[-self.config.rsi_period :])
            avg_loss = np.mean(losses[-self.config.rsi_period :])

            if avg_loss == 0:
                return 100.0

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))

            return float(rsi)

        except Exception as e:
            logger.error(f"Erro ao calcular RSI para {symbol}: {e}")
            return None

    def _calculate_volume_ratio(self, symbol: str) -> Optional[float]:
        """Calcula ratio de volume para o símbolo."""
        try:
            volumes = self.volume_history.get(symbol, [])
            if len(volumes) < self.config.volume_lookback:
                return None

            current_volume = volumes[-1]
            avg_volume = np.mean(volumes[-self.config.volume_lookback :])

            if avg_volume == 0:
                return 1.0

            return float(current_volume / avg_volume)

        except Exception as e:
            logger.error(f"Erro ao calcular volume ratio para {symbol}: {e}")
            return None

    def _calculate_volatility(self, symbol: str) -> Optional[float]:
        """Calcula volatilidade para o símbolo."""
        try:
            prices = self.price_history.get(symbol, [])
            if len(prices) < 10:
                return None

            returns = np.diff(np.log(prices))
            volatility = np.std(returns) * np.sqrt(len(returns))

            return float(volatility)

        except Exception as e:
            logger.error(f"Erro ao calcular volatilidade para {symbol}: {e}")
            return None

    async def _publish_market_data_event(
        self, enhanced_data_list: List[EnhancedMarketData]
    ):
        """Publica evento de dados de mercado no event bus."""
        try:
            if not self.event_bus:
                return

            # Criar evento MarketDataUpdated
            event = MarketDataUpdated(
                market_data=enhanced_data_list, timestamp=time.time()
            )

            # Publicar no event bus
            self.event_bus.publish("market.data.updated", event)

            logger.debug(
                f"Evento MarketDataUpdated publicado com {len(enhanced_data_list)} itens"
            )

        except Exception as e:
            logger.error(f"Erro ao publicar evento de dados de mercado: {e}")

    async def _generate_holographic_events(
        self, enhanced_data_list: List[EnhancedMarketData]
    ):
        """Gera eventos holográficos a partir dos dados de mercado."""
        try:
            if not self.enhanced_data_collector:
                return

            # Usar o enhanced_data_collector para converter para eventos holográficos
            holographic_events = (
                self.enhanced_data_collector.convert_to_holographic_events(
                    enhanced_data_list,
                    [],  # Sem eventos de notícias por enquanto
                    (100, 100),  # Tamanho do campo holográfico (configurável)
                )
            )

            if holographic_events and self.event_bus:
                # Publicar eventos holográficos
                for event in holographic_events:
                    self.event_bus.publish("holographic.event.received", event)

            logger.debug(f"Gerados {len(holographic_events)} eventos holográficos")

        except Exception as e:
            logger.error(f"Erro ao gerar eventos holográficos: {e}")

    def get_status(self) -> Dict[str, Any]:
        """Retorna status atual do coletor."""
        return {
            "is_running": self.is_running,
            "symbols": self.symbols,
            "processed_count": self.processed_count,
            "last_update_time": self.last_update_time,
            "buffer_sizes": {
                symbol: len(data) for symbol, data in self.data_buffer.items()
            },
            "feed_manager_status": (
                self.feed_manager.get_system_status() if self.feed_manager else None
            ),
        }

    def get_latest_data(self) -> Dict[str, Optional[NormalizedTicker]]:
        """Retorna os dados mais recentes para cada símbolo."""
        latest_data = {}
        for symbol, ticker_list in self.data_buffer.items():
            latest_data[symbol] = ticker_list[-1] if ticker_list else None
        return latest_data

    def get_latest_orderbook(self, symbol: str) -> Optional[NormalizedOrderBook]:
        """Retorna o orderbook mais recente para o símbolo fornecido.

        Parameters
        ----------
        symbol
            Símbolo no formato ``BTC/USDT``.

        Returns
        -------
        Optional[NormalizedOrderBook]
            Último orderbook disponível ou ``None`` se não houver dados.
        """
        return (
            self.orderbook_history.get(symbol, [])[-1]
            if self.orderbook_history.get(symbol)
            else None
        )

    def get_recent_trades(self, symbol: str, limit: int = 50) -> List[NormalizedTrade]:
        """Retorna o histórico recente de trades para o símbolo.

        Parameters
        ----------
        symbol
            Símbolo no formato ``BTC/USDT``.
        limit
            Quantidade máxima de trades a retornar.

        Returns
        -------
        List[NormalizedTrade]
            Lista de trades mais recentes, vazia se não houver histórico.
        """
        trades = self.trade_history.get(symbol, [])
        return trades[-limit:] if trades else []

    async def __aenter__(self):
        """Context manager entry."""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        await self.stop()
