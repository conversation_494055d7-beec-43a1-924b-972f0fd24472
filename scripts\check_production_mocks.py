#!/usr/bin/env python3
"""
Script para verificar se o sistema está usando mocks em produção
"""

import sys
import os

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def check_ccxt_classes():
    """Verifica se as classes ccxt são reais ou mocks"""
    
    print("🔍 VERIFICAÇÃO DE MOCKS EM PRODUÇÃO")
    print("=" * 50)
    
    try:
        import ccxt.async_support as ccxt
        print("✅ ccxt.async_support importado")
        
        # Verificar classe KuCoin
        kucoin_class = getattr(ccxt, 'kucoin', None)
        if kucoin_class is None:
            print("❌ ccxt.kucoin não encontrado")
            return False
        
        print(f"📋 Classe KuCoin: {kucoin_class}")
        print(f"📋 Módulo: {kucoin_class.__module__}")
        print(f"📋 Tipo: {type(kucoin_class)}")
        
        # Tentar criar instância
        try:
            exchange = kucoin_class({
                'sandbox': False,
                'timeout': 30000,
                'enableRateLimit': True,
            })
            print(f"✅ Instância criada: {type(exchange)}")
            
            # Verificar métodos essenciais
            methods = ['load_markets', 'fetch_ticker', 'fetch_ohlcv']
            for method in methods:
                if hasattr(exchange, method):
                    print(f"✅ Método {method}: {getattr(exchange, method)}")
                else:
                    print(f"❌ Método {method}: AUSENTE")
            
            # Verificar se é mock
            if hasattr(exchange, '__class__'):
                class_name = exchange.__class__.__name__
                if 'Dummy' in class_name or 'Mock' in class_name:
                    print(f"🚨 MOCK DETECTADO: {class_name}")
                    return False
                else:
                    print(f"✅ Classe real: {class_name}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erro ao criar instância: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ Erro ao importar ccxt: {e}")
        return False


def check_qualia_integration():
    """Verifica se a integração QUALIA está usando mocks"""
    
    print("\n🔍 VERIFICAÇÃO DA INTEGRAÇÃO QUALIA")
    print("=" * 50)
    
    try:
        # Usar importação correta sem 'src'
        from qualia.market.kucoin_integration import KucoinIntegration
        print("✅ KucoinIntegration importado")
        
        # Criar instância
        integration = KucoinIntegration(
            api_key=None,
            api_secret=None,
            password=None,
            use_websocket=False
        )
        
        print(f"📋 Exchange: {integration.exchange}")
        print(f"📋 Tipo: {type(integration.exchange)}")
        
        if hasattr(integration.exchange, '__class__'):
            class_name = integration.exchange.__class__.__name__
            if 'Dummy' in class_name or 'Mock' in class_name:
                print(f"🚨 MOCK DETECTADO NA INTEGRAÇÃO: {class_name}")
                return False
            else:
                print(f"✅ Classe real na integração: {class_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na integração QUALIA: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Função principal"""
    
    print("🚀 DIAGNÓSTICO DE MOCKS EM PRODUÇÃO")
    print("=" * 60)
    
    # Verificar se estamos em ambiente de teste
    if 'pytest' in sys.modules:
        print("⚠️  AVISO: Executando em ambiente de teste")
    
    if 'PYTEST_CURRENT_TEST' in os.environ:
        print("⚠️  AVISO: Variável PYTEST_CURRENT_TEST detectada")
    
    # Verificar classes ccxt
    ccxt_ok = check_ccxt_classes()
    
    # Verificar integração QUALIA
    qualia_ok = check_qualia_integration()
    
    # Resultado final
    print("\n🏁 RESULTADO FINAL")
    print("=" * 60)
    
    if ccxt_ok and qualia_ok:
        print("✅ SISTEMA USANDO CLASSES REAIS")
        print("   - ccxt.kucoin é classe real")
        print("   - KucoinIntegration usa classe real")
    else:
        print("🚨 SISTEMA USANDO MOCKS EM PRODUÇÃO!")
        if not ccxt_ok:
            print("   ❌ ccxt.kucoin é mock")
        if not qualia_ok:
            print("   ❌ KucoinIntegration usa mock")
        
        print("\n💡 POSSÍVEIS CAUSAS:")
        print("   - Arquivo tests/fixtures/stubs.py sendo carregado")
        print("   - Mocks não foram limpos após testes")
        print("   - Importação incorreta em produção")


if __name__ == "__main__":
    main() 