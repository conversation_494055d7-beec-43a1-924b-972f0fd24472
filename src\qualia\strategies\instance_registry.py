"""
YAA TASK 5: Registry Pattern para Gestão de Instâncias de Estratégia

Este módulo implementa um Registry pattern para centralizar a gestão de instâncias
de estratégias, evitando duplicação e garantindo reutilização eficiente.
"""

from __future__ import annotations

import threading
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from datetime import datetime, timezone

from ..utils.logging_config import get_qualia_logger
from .strategy_factory import StrategyFactory
from .strategy_interface import TradingStrategy

logger = get_qualia_logger("strategies.instance_registry")


@dataclass
class StrategyInstanceKey:
    """
    Chave única para identificar uma instância de estratégia.
    
    Combina alias da estratégia, símbolo, timeframe e hash dos parâmetros
    para criar uma identificação única.
    """
    alias: str
    symbol: str
    timeframe: str
    params_hash: str
    
    def __str__(self) -> str:
        return f"{self.alias}:{self.symbol}:{self.timeframe}:{self.params_hash[:8]}"


@dataclass
class StrategyInstanceInfo:
    """Informações sobre uma instância de estratégia registrada."""
    
    key: StrategyInstanceKey
    instance: TradingStrategy
    created_at: datetime
    last_accessed: datetime
    access_count: int
    is_active: bool


class StrategyInstanceRegistry:
    """
    Registry centralizado para gestão de instâncias de estratégias.
    
    Implementa padrão Singleton para garantir uma única instância do registry
    em todo o sistema QUALIA.
    """
    
    _instance: Optional['StrategyInstanceRegistry'] = None
    _lock = threading.Lock()
    
    def __new__(cls) -> 'StrategyInstanceRegistry':
        """Implementa padrão Singleton thread-safe."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Inicializa o registry (apenas uma vez devido ao Singleton)."""
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._instances: Dict[str, StrategyInstanceInfo] = {}
        self._access_lock = threading.RLock()
        self._initialized = True
        
        logger.info("🏭 StrategyInstanceRegistry inicializado")
    
    def get_or_create_strategy(
        self,
        alias: str,
        symbol: str,
        timeframe: str,
        params: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None,
        config_manager: Optional[Any] = None,
        force_new: bool = False
    ) -> TradingStrategy:
        """
        Obtém uma instância existente ou cria uma nova estratégia.
        
        Args:
            alias: Alias da estratégia
            symbol: Símbolo de trading
            timeframe: Timeframe da estratégia
            params: Parâmetros da estratégia
            context: Contexto de inicialização
            config_manager: Gerenciador de configuração
            force_new: Se True, força criação de nova instância
            
        Returns:
            Instância da estratégia (existente ou nova)
        """
        with self._access_lock:
            # Criar chave única para a estratégia
            key = self._create_strategy_key(alias, symbol, timeframe, params or {})
            key_str = str(key)
            
            # Verificar se já existe uma instância (e não é forçada nova criação)
            if not force_new and key_str in self._instances:
                instance_info = self._instances[key_str]
                
                # Atualizar estatísticas de acesso
                instance_info.last_accessed = datetime.now(timezone.utc)
                instance_info.access_count += 1
                
                logger.debug(f"♻️ Reutilizando estratégia existente: {key_str}")
                logger.debug(f"   Acessos: {instance_info.access_count}, Criada em: {instance_info.created_at}")
                
                return instance_info.instance
            
            # Criar nova instância
            logger.info(f"🏗️ Criando nova instância de estratégia: {key_str}")
            
            try:
                # Preparar contexto completo
                full_context = {
                    "symbol": symbol,
                    "timeframe": timeframe,
                    **(context or {})
                }
                
                # Criar instância via StrategyFactory
                instance = StrategyFactory.create_strategy(
                    alias=alias,
                    params=params,
                    context=full_context,
                    config_manager=config_manager
                )
                
                # Registrar a nova instância
                instance_info = StrategyInstanceInfo(
                    key=key,
                    instance=instance,
                    created_at=datetime.now(timezone.utc),
                    last_accessed=datetime.now(timezone.utc),
                    access_count=1,
                    is_active=True
                )
                
                self._instances[key_str] = instance_info
                
                logger.info(f"✅ Estratégia registrada: {key_str}")
                logger.info(f"   Total de instâncias no registry: {len(self._instances)}")
                
                return instance
                
            except Exception as e:
                logger.error(f"❌ Erro ao criar estratégia {key_str}: {e}")
                raise
    
    def _create_strategy_key(
        self, 
        alias: str, 
        symbol: str, 
        timeframe: str, 
        params: Dict[str, Any]
    ) -> StrategyInstanceKey:
        """
        Cria uma chave única para identificar a estratégia.
        
        Args:
            alias: Alias da estratégia
            symbol: Símbolo de trading
            timeframe: Timeframe
            params: Parâmetros da estratégia
            
        Returns:
            Chave única da estratégia
        """
        import hashlib
        import json
        
        # Criar hash dos parâmetros para identificação única
        params_str = json.dumps(params, sort_keys=True, default=str)
        params_hash = hashlib.md5(params_str.encode()).hexdigest()
        
        return StrategyInstanceKey(
            alias=alias,
            symbol=symbol,
            timeframe=timeframe,
            params_hash=params_hash
        )
    
    def get_strategy_info(self, key_str: str) -> Optional[StrategyInstanceInfo]:
        """Obtém informações sobre uma estratégia registrada."""
        with self._access_lock:
            return self._instances.get(key_str)
    
    def list_registered_strategies(self) -> List[StrategyInstanceInfo]:
        """Lista todas as estratégias registradas."""
        with self._access_lock:
            return list(self._instances.values())
    
    def deactivate_strategy(self, key_str: str) -> bool:
        """
        Desativa uma estratégia (marca como inativa mas mantém no registry).
        
        Args:
            key_str: Chave da estratégia
            
        Returns:
            True se desativada com sucesso
        """
        with self._access_lock:
            if key_str in self._instances:
                self._instances[key_str].is_active = False
                logger.info(f"🔒 Estratégia desativada: {key_str}")
                return True
            return False
    
    def remove_strategy(self, key_str: str) -> bool:
        """
        Remove completamente uma estratégia do registry.
        
        Args:
            key_str: Chave da estratégia
            
        Returns:
            True se removida com sucesso
        """
        with self._access_lock:
            if key_str in self._instances:
                instance_info = self._instances.pop(key_str)
                logger.info(f"🗑️ Estratégia removida do registry: {key_str}")
                logger.info(f"   Estava ativa por: {datetime.now(timezone.utc) - instance_info.created_at}")
                logger.info(f"   Total de acessos: {instance_info.access_count}")
                return True
            return False
    
    def cleanup_inactive_strategies(self) -> int:
        """
        Remove estratégias inativas do registry.
        
        Returns:
            Número de estratégias removidas
        """
        with self._access_lock:
            inactive_keys = [
                key for key, info in self._instances.items() 
                if not info.is_active
            ]
            
            for key in inactive_keys:
                self.remove_strategy(key)
            
            if inactive_keys:
                logger.info(f"🧹 Limpeza concluída: {len(inactive_keys)} estratégias inativas removidas")
            
            return len(inactive_keys)
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """Obtém estatísticas do registry."""
        with self._access_lock:
            total_strategies = len(self._instances)
            active_strategies = sum(1 for info in self._instances.values() if info.is_active)
            total_accesses = sum(info.access_count for info in self._instances.values())

            # Estratégias por alias
            by_alias = {}
            for info in self._instances.values():
                alias = info.key.alias
                by_alias[alias] = by_alias.get(alias, 0) + 1

            return {
                "total_strategies": total_strategies,
                "active_strategies": active_strategies,
                "inactive_strategies": total_strategies - active_strategies,
                "total_accesses": total_accesses,
                "strategies_by_alias": by_alias,
                "registry_id": id(self)
            }

    def shutdown(self) -> None:
        """
        Realiza limpeza completa do registry durante shutdown do sistema.

        Desativa todas as estratégias e limpa o registry.
        """
        with self._access_lock:
            logger.info("🔄 Iniciando shutdown do StrategyInstanceRegistry...")

            # Obter estatísticas finais
            final_stats = self.get_registry_stats()
            logger.info(f"📊 Estatísticas finais do Registry:")
            logger.info(f"   Total de estratégias: {final_stats['total_strategies']}")
            logger.info(f"   Estratégias ativas: {final_stats['active_strategies']}")
            logger.info(f"   Total de acessos: {final_stats['total_accesses']}")

            # Desativar todas as estratégias
            for key_str, instance_info in self._instances.items():
                if instance_info.is_active:
                    instance_info.is_active = False
                    logger.debug(f"🔒 Estratégia desativada: {key_str}")

            # Limpar o registry
            cleared_count = len(self._instances)
            self._instances.clear()

            logger.info(f"✅ Registry shutdown concluído: {cleared_count} estratégias removidas")

    def force_cleanup_all(self) -> int:
        """
        Força limpeza completa de todas as estratégias (ativas e inativas).

        Returns:
            Número de estratégias removidas
        """
        with self._access_lock:
            count = len(self._instances)
            self._instances.clear()
            logger.info(f"🧹 Limpeza forçada: {count} estratégias removidas")
            return count


# Instância global do registry (Singleton)
_strategy_registry = StrategyInstanceRegistry()


def get_strategy_registry() -> StrategyInstanceRegistry:
    """Obtém a instância global do registry de estratégias."""
    return _strategy_registry


def get_or_create_strategy(
    alias: str,
    symbol: str,
    timeframe: str,
    params: Optional[Dict[str, Any]] = None,
    context: Optional[Dict[str, Any]] = None,
    config_manager: Optional[Any] = None,
    force_new: bool = False
) -> TradingStrategy:
    """
    Função de conveniência para obter ou criar uma estratégia.
    
    Args:
        alias: Alias da estratégia
        symbol: Símbolo de trading
        timeframe: Timeframe da estratégia
        params: Parâmetros da estratégia
        context: Contexto de inicialização
        config_manager: Gerenciador de configuração
        force_new: Se True, força criação de nova instância
        
    Returns:
        Instância da estratégia
    """
    return _strategy_registry.get_or_create_strategy(
        alias=alias,
        symbol=symbol,
        timeframe=timeframe,
        params=params,
        context=context,
        config_manager=config_manager,
        force_new=force_new
    )


__all__ = [
    "StrategyInstanceRegistry",
    "StrategyInstanceKey", 
    "StrategyInstanceInfo",
    "get_strategy_registry",
    "get_or_create_strategy"
]
