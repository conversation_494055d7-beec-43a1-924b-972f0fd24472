#!/usr/bin/env python3
"""CLI simples para configurar o logging do QUALIA."""
import argparse
import json
from pathlib import Path
from qualia.utils.logging_config import init_logging
from qualia.utils.logger import get_logger

logger = get_logger("configure_logging")


def main() -> None:
    parser = argparse.ArgumentParser(description="Configura o logging do QUALIA")
    parser.add_argument(
        "--detail",
        choices=["summary", "debug"],
        default="summary",
        help="Nível de detalhe desejado",
    )
    parser.add_argument(
        "--config",
        type=str,
        default=None,
        help="Arquivo JSON opcional com configuracao de logging",
    )
    parser.add_argument(
        "--log-file",
        type=str,
        default=None,
        help="Arquivo de log a ser utilizado",
    )
    args = parser.parse_args()

    level_map = {"summary": "INFO", "debug": "DEBUG"}

    module_levels = None
    allowed_modules = None
    if args.config:
        try:
            with open(args.config) as f:
                cfg = json.load(f)
        except json.JSONDecodeError as e:
            logger.error("Invalid logging config: %s", e)
            raise
        module_levels = cfg.get("module_levels")
        allowed_modules = cfg.get("allowed_modules")
    init_logging(
        log_level=level_map[args.detail],
        log_file=Path(args.log_file) if args.log_file else None,
        module_levels=module_levels,
        allowed_modules=allowed_modules,
        config_file=Path(args.config) if args.config else None,
    )
    print(f"Logging configurado para {args.detail}")


if __name__ == "__main__":
    main()
