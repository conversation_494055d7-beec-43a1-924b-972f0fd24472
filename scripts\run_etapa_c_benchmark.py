#!/usr/bin/env python3
"""
QUALIA Etapa C: Benchmark Offline - Executor Principal

Executa o grid search completo de 90 dias conforme especificado na Etapa C:
- price_amp: 1-10 (10 valores)
- news_amp: 1-10 (10 valores) 
- min_conf: 0.3-0.8 (6 valores)
- Total: ~600 combinações × múltiplos símbolos = ~2000+ runs

Avalia Sharpe ratio e max drawdown usando dados reais de mercado.
"""

import sys
import os
import asyncio
import logging
from pathlib import Path
from datetime import datetime
import json

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.backtest.hyperparams_grid_search import (
    run_hyperparams_grid_search,
    GridSearchParams,
    HyperParamsGridSearch
)
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class EtapaCBenchmarkRunner:
    """
    Executor principal para Etapa C: Benchmark Offline.
    
    Coordena a execução do grid search completo com dados reais
    e gera relatórios detalhados de performance.
    """
    
    def __init__(self, output_dir: str = "results/etapa_c_benchmark"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Configurações específicas da Etapa C
        self.params = GridSearchParams(
            price_amp_range=(1.0, 10.0, 10),    # 1, 2, 3, ..., 10
            news_amp_range=(1.0, 10.0, 10),     # 1, 2, 3, ..., 10
            min_conf_range=(0.3, 0.8, 6),       # 0.3, 0.4, 0.5, 0.6, 0.7, 0.8
            backtest_days=90,                    # 90 dias conforme especificação
            initial_capital=10000.0,
            symbols=["BTC/USDT", "ETH/USDT", "ADA/USDT", "DOT/USDT"],  # 4 símbolos
            timeframe="1h",
            max_workers=1,  # Sequencial para estabilidade
            cache_results=True,
            save_detailed_results=True
        )
        
        logger.info(f"🎯 EtapaCBenchmarkRunner inicializado")
        logger.info(f"📊 Configuração: {len(self.params.symbols)} símbolos, {self.params.backtest_days} dias")
        
        # Calcular total de runs esperados
        price_steps = self.params.price_amp_range[2]
        news_steps = self.params.news_amp_range[2]
        conf_steps = self.params.min_conf_range[2]
        total_combinations = price_steps * news_steps * conf_steps
        total_runs = total_combinations * len(self.params.symbols)
        
        logger.info(f"🔢 Total esperado: {total_combinations} combinações × {len(self.params.symbols)} símbolos = {total_runs} runs")
    
    async def run_benchmark(self) -> dict:
        """Executa o benchmark completo da Etapa C."""
        
        logger.info("🚀 Iniciando Etapa C: Benchmark Offline...")
        start_time = datetime.now()
        
        try:
            # Executar grid search
            grid_search = HyperParamsGridSearch(self.params)
            results = await grid_search.run_grid_search()
            
            # Salvar resultados
            timestamp = start_time.strftime("%Y%m%d_%H%M%S")
            results_file = self.output_dir / f"etapa_c_results_{timestamp}.json"
            
            # Converter resultados para formato serializável
            results_dict = {
                "metadata": {
                    "execution_time": start_time.isoformat(),
                    "duration_minutes": (datetime.now() - start_time).total_seconds() / 60,
                    "total_results": len(results.results),
                    "successful_runs": len(results.results),  # Todos são considerados sucessos se chegaram aqui
                    "failed_runs": 0,
                    "parameters": {
                        "price_amp_range": self.params.price_amp_range,
                        "news_amp_range": self.params.news_amp_range,
                        "min_conf_range": self.params.min_conf_range,
                        "backtest_days": self.params.backtest_days,
                        "symbols": self.params.symbols
                    }
                },
                "summary": {
                    "best_sharpe": results.best_sharpe.__dict__ if results.best_sharpe else None,
                    "best_return": results.best_return.__dict__ if results.best_return else None,
                    "best_drawdown": results.best_drawdown.__dict__ if results.best_drawdown else None,
                    "best_calmar": results.best_calmar.__dict__ if results.best_calmar else None
                },
                "detailed_results": [r.__dict__ for r in results.results]
            }
            
            # Salvar arquivo JSON
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results_dict, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"💾 Resultados salvos em: {results_file}")
            
            # Gerar relatório resumido
            await self._generate_summary_report(results, timestamp)
            
            # Log final
            duration = (datetime.now() - start_time).total_seconds() / 60
            logger.info(f"✅ Etapa C concluída em {duration:.1f} minutos")
            logger.info(f"📊 {len(results.results)} resultados obtidos")
            
            if results.best_sharpe:
                best = results.best_sharpe
                logger.info(f"🏆 Melhor Sharpe: {best.sharpe_ratio:.3f} (price_amp={best.price_amplification:.1f}, news_amp={best.news_amplification:.1f}, min_conf={best.min_confidence:.2f})")
            
            return results_dict
            
        except Exception as e:
            logger.error(f"❌ Erro na execução da Etapa C: {e}")
            raise
    
    async def _generate_summary_report(self, results, timestamp: str):
        """Gera relatório resumido dos resultados."""
        
        report_file = self.output_dir / f"etapa_c_summary_{timestamp}.md"
        
        # Calcular estatísticas
        successful_results = results.results  # Todos os resultados são considerados válidos

        if not successful_results:
            logger.warning("⚠️ Nenhum resultado válido para gerar relatório")
            return
        
        # Estatísticas por símbolo
        symbol_stats = {}
        for result in successful_results:
            # Usar symbols_tested se disponível, senão usar um símbolo padrão
            symbols = getattr(result, 'symbols_tested', ['Unknown'])
            symbol = symbols[0] if symbols else 'Unknown'

            if symbol not in symbol_stats:
                symbol_stats[symbol] = {
                    'count': 0,
                    'sharpe_ratios': [],
                    'returns': [],
                    'drawdowns': []
                }

            symbol_stats[symbol]['count'] += 1
            symbol_stats[symbol]['sharpe_ratios'].append(result.sharpe_ratio)
            symbol_stats[symbol]['returns'].append(result.total_return)
            symbol_stats[symbol]['drawdowns'].append(result.max_drawdown)
        
        # Gerar relatório markdown
        report_content = f"""# QUALIA Etapa C: Benchmark Offline - Relatório

**Data de Execução:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Período de Backtest:** {self.params.backtest_days} dias
**Total de Resultados:** {len(successful_results)}

## Configuração do Grid Search

- **Price Amplification:** {self.params.price_amp_range[0]} - {self.params.price_amp_range[1]} ({self.params.price_amp_range[2]} valores)
- **News Amplification:** {self.params.news_amp_range[0]} - {self.params.news_amp_range[1]} ({self.params.news_amp_range[2]} valores)
- **Min Confidence:** {self.params.min_conf_range[0]} - {self.params.min_conf_range[1]} ({self.params.min_conf_range[2]} valores)
- **Símbolos:** {', '.join(self.params.symbols)}

## Melhores Resultados

### Melhor Sharpe Ratio
"""
        
        if results.best_sharpe:
            best_sharpe = results.best_sharpe
            symbols = getattr(best_sharpe, 'symbols_tested', ['Unknown'])
            symbol = symbols[0] if symbols else 'Unknown'
            report_content += f"""
- **Símbolo:** {symbol}
- **Sharpe Ratio:** {best_sharpe.sharpe_ratio:.4f}
- **Retorno Total:** {best_sharpe.total_return:.2%}
- **Max Drawdown:** {best_sharpe.max_drawdown:.2%}
- **Parâmetros:** price_amp={best_sharpe.price_amplification:.1f}, news_amp={best_sharpe.news_amplification:.1f}, min_conf={best_sharpe.min_confidence:.2f}
"""
        
        report_content += "\n### Melhor Retorno Total\n"
        
        if results.best_return:
            best_return = results.best_return
            symbols = getattr(best_return, 'symbols_tested', ['Unknown'])
            symbol = symbols[0] if symbols else 'Unknown'
            report_content += f"""
- **Símbolo:** {symbol}
- **Retorno Total:** {best_return.total_return:.2%}
- **Sharpe Ratio:** {best_return.sharpe_ratio:.4f}
- **Max Drawdown:** {best_return.max_drawdown:.2%}
- **Parâmetros:** price_amp={best_return.price_amplification:.1f}, news_amp={best_return.news_amplification:.1f}, min_conf={best_return.min_confidence:.2f}
"""
        
        report_content += "\n## Estatísticas por Símbolo\n"
        
        for symbol, stats in symbol_stats.items():
            avg_sharpe = sum(stats['sharpe_ratios']) / len(stats['sharpe_ratios'])
            avg_return = sum(stats['returns']) / len(stats['returns'])
            avg_drawdown = sum(stats['drawdowns']) / len(stats['drawdowns'])
            
            report_content += f"""
### {symbol}
- **Runs:** {stats['count']}
- **Sharpe Médio:** {avg_sharpe:.4f}
- **Retorno Médio:** {avg_return:.2%}
- **Drawdown Médio:** {avg_drawdown:.2%}
"""
        
        # Salvar relatório
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"📋 Relatório resumido salvo em: {report_file}")


async def main():
    """Função principal."""
    
    # Configurar logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("🎯 Iniciando execução da Etapa C: Benchmark Offline")
    
    try:
        # Executar benchmark
        runner = EtapaCBenchmarkRunner()
        results = await runner.run_benchmark()
        
        logger.info("🏆 Etapa C executada com sucesso!")
        
        # Mostrar resumo final
        metadata = results.get('metadata', {})
        summary = results.get('summary', {})
        
        logger.info(f"📊 Resumo Final:")
        logger.info(f"   - Total de runs: {metadata.get('total_results', 0)}")
        logger.info(f"   - Runs bem-sucedidos: {metadata.get('successful_runs', 0)}")
        logger.info(f"   - Duração: {metadata.get('duration_minutes', 0):.1f} minutos")
        
        if summary.get('best_sharpe'):
            best = summary['best_sharpe']
            logger.info(f"   - Melhor Sharpe: {best.get('sharpe_ratio', 0):.3f}")
        
    except Exception as e:
        logger.error(f"❌ Erro na execução: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
