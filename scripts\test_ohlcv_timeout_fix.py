#!/usr/bin/env python3
"""
Teste específico para resolver o problema de timeout do OHLCV
"""

import asyncio
import sys
import os
from datetime import datetime

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


async def test_ohlcv_with_different_limits():
    """Testa OHLCV com diferentes limites para encontrar o ideal"""
    
    print("🔍 TESTE: OHLCV COM DIFERENTES LIMITES")
    print("=" * 60)
    
    try:
        from qualia.market.kucoin_integration import KucoinIntegration
        
        # Criar integração com timeout maior
        kucoin = KucoinIntegration(
            api_key=None,
            api_secret=None,
            password=None,
            use_websocket=False,
            conn_timeout=30.0,
            ohlcv_timeout=90.0  # 90 segundos
        )
        
        await kucoin.initialize_connection()
        print("✅ Conexão inicializada")
        
        # Testar diferentes limites
        limits = [1, 5, 10, 20, 50, 100]
        
        for limit in limits:
            try:
                print(f"\n🔄 Testando OHLCV com limite {limit}...")
                start_time = datetime.now()
                
                df = await kucoin.fetch_ohlcv("BTC/USDT", "5m", limit=limit)
                
                duration = (datetime.now() - start_time).total_seconds()
                
                if df is not None and not df.empty:
                    print(f"✅ Sucesso em {duration:.2f}s - {len(df)} candles")
                    
                    # Se conseguiu rapidamente, este é o limite ideal
                    if duration < 30:
                        print(f"🎯 LIMITE IDEAL ENCONTRADO: {limit} (tempo: {duration:.2f}s)")
                        break
                else:
                    print(f"❌ OHLCV vazio para limite {limit}")
                    
            except asyncio.TimeoutError:
                print(f"⏱️ TIMEOUT para limite {limit}")
            except Exception as e:
                print(f"❌ Erro para limite {limit}: {e}")
        
        await kucoin.close()
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False


async def test_direct_ccxt_ohlcv():
    """Testa OHLCV diretamente com ccxt para comparação"""
    
    print("\n🔍 TESTE: CCXT DIRETO PARA OHLCV")
    print("=" * 60)
    
    try:
        import ccxt.async_support as ccxt
        
        exchange = ccxt.kucoin({
            'sandbox': False,
            'timeout': 90000,  # 90 segundos
            'enableRateLimit': True,
        })
        
        await exchange.load_markets()
        print("✅ Mercados carregados")
        
        # Testar diferentes limites
        limits = [1, 5, 10, 20]
        
        for limit in limits:
            try:
                print(f"\n🔄 CCXT direto com limite {limit}...")
                start_time = datetime.now()
                
                ohlcv = await exchange.fetch_ohlcv("BTC/USDT", "5m", limit=limit)
                
                duration = (datetime.now() - start_time).total_seconds()
                
                if ohlcv and len(ohlcv) > 0:
                    print(f"✅ Sucesso em {duration:.2f}s - {len(ohlcv)} candles")
                    print(f"   Último preço: ${ohlcv[-1][4]:,.2f}")
                    
                    if duration < 30:
                        print(f"🎯 CCXT FUNCIONA BEM com limite {limit}")
                        break
                else:
                    print("❌ OHLCV vazio")
                    
            except asyncio.TimeoutError:
                print(f"⏱️ TIMEOUT no CCXT para limite {limit}")
            except Exception as e:
                print(f"❌ Erro no CCXT: {e}")
        
        await exchange.close()
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste CCXT: {e}")
        return False


async def test_ohlcv_different_timeframes():
    """Testa OHLCV com diferentes timeframes"""
    
    print("\n🔍 TESTE: DIFERENTES TIMEFRAMES")
    print("=" * 60)
    
    try:
        from qualia.market.kucoin_integration import KucoinIntegration
        
        kucoin = KucoinIntegration(
            api_key=None,
            api_secret=None,
            password=None,
            use_websocket=False,
            ohlcv_timeout=60.0
        )
        
        await kucoin.initialize_connection()
        
        # Testar diferentes timeframes
        timeframes = ["1m", "5m", "15m", "1h"]
        
        for tf in timeframes:
            try:
                print(f"\n🔄 Testando timeframe {tf}...")
                start_time = datetime.now()
                
                df = await kucoin.fetch_ohlcv("BTC/USDT", tf, limit=5)
                
                duration = (datetime.now() - start_time).total_seconds()
                
                if df is not None and not df.empty:
                    print(f"✅ {tf}: Sucesso em {duration:.2f}s - {len(df)} candles")
                else:
                    print(f"❌ {tf}: OHLCV vazio")
                    
            except asyncio.TimeoutError:
                print(f"⏱️ {tf}: TIMEOUT")
            except Exception as e:
                print(f"❌ {tf}: Erro - {e}")
        
        await kucoin.close()
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste de timeframes: {e}")
        return False


async def main():
    """Função principal"""
    
    print("🚀 DIAGNÓSTICO COMPLETO DO OHLCV")
    print("=" * 70)
    print(f"Iniciado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Teste 1: Diferentes limites
    test1 = await test_ohlcv_with_different_limits()
    
    # Teste 2: CCXT direto
    test2 = await test_direct_ccxt_ohlcv()
    
    # Teste 3: Diferentes timeframes
    test3 = await test_ohlcv_different_timeframes()
    
    # Resultado final
    print("\n🏁 RESULTADO DO DIAGNÓSTICO")
    print("=" * 70)
    
    if test1 and test2 and test3:
        print("✅ TODOS OS TESTES PASSARAM")
        print("💡 OHLCV está funcionando corretamente")
    else:
        print("⚠️  ALGUNS TESTES FALHARAM")
        print("💡 Pode ser necessário ajustar timeouts ou limites")
    
    print(f"\nConcluído em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    asyncio.run(main()) 
