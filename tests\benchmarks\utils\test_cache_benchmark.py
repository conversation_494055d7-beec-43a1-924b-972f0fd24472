import pytest
import sys
import types

pytest.importorskip("pytest_benchmark")

from qualia.utils.cache import MemoryCache


@pytest.mark.benchmark(group="utils-cache")
def test_cache_get_set_benchmark(benchmark, tmp_path):
    sys.modules.setdefault("pandas", types.SimpleNamespace(DataFrame=object))
    cache = MemoryCache(expiry_seconds=30)
    cache.collect_stats = True
    cache.reset_stats()
    cache.set("foo", 1)

    def run():
        return cache.get("foo")

    result = benchmark(run)
    assert result == 1
