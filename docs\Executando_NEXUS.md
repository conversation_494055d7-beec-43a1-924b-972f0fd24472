# Executando o NEXUS – Guia de Observação

Este documento complementa `Compreendendo_QUALIA.md` e descreve, passo-a-passo, **o que você verá** ao colocar o subsistema NEXUS (coerência multimodal + GA de limiar) em execução. A métrica de coerência é calculada pela função [`cross_modal_coherence`](api/metrics.md#coerencia-cross-modal-com-cross_modal_coherence).

---
## 1. Pré-requisitos resumidos

1. Ambiente Python 3.9+ e virtual-env ativado.
2. Dependências instaladas:
   ```bash
   pip install -r requirements.txt  # ou
   pip install python-dotenv feedparser "tweepy>=4.14" yt_dlp soundfile librosa dash plotly requests
   ```
3. Arquivo `.env` na raiz com:
   ```env
   TWITTER_BEARER_TOKEN=AAAAAAAAA...  # token v2
   ```
4. Executar:
   ```bash
   python scripts/run_nexus.py
   ```

---
## 2. Lin<PERSON> do Tempo Esperada

| Minuto | O que ocorre                                           | Evidência no **terminal**                               | Evidência nos **dashboards** |
|-------:|--------------------------------------------------------|---------------------------------------------------------|------------------------------|
| 0      | Aplicação inicia, Dash abre portas 8050 e 8060         | "Dash is running on ..."                                   | Páginas carregam vazias      |
| 0-1    | RSSIngestor baixa primeiros *feeds*                    | `RSSIngestor publicou headline '...'`                   | 1º ponto na curva de coerência (8050) |
| 1-3    | CoherenceMonitor publica valores a cada headline       | `CoherenceMonitor publicou coerência 0.23`              | Curva vai sendo traçada      |
| 3-5    | ThresholdCalibrator calcula limiar dinâmico            | `AdaptiveThresholdCalibrator threshold=0.35`            | Linha horizontal no gráfico  |
| 5-20   | (dependendo do volume) GA recebe **window_events**     | Buffer atinge 500 coerências → `GA geração concluída`   | Dashboard GA (8060) exibe Gen 1: pontos α/offset e F1 |
| 20-∞   | GA publica overrides a cada geração                    | `nexus.threshold.override α=0.18 off=0.06`              | Gráficos atualizam a cada geração |

> Observação: se quiser ver resultados mais rápido em ambiente de teste, edite `scripts/run_nexus.py` e instancie `GAThresholdEvolver(bus, window_events=100)`.

---
## 3. O que é "normal" e o que indica problema?

### Normal
* Curva de coerência (8050) começa perto de zero e oscila.
* Limiar (linha horizontal) ajusta-se gradualmente.
* GA (8060) mostra pontos após ~1ª geração.
* No terminal, mensagens de headline/tweet/audio aparecem periodicamente.

### Problemas comuns
| Sintoma | Causa provável | Ação sugerida |
|---------|----------------|---------------|
| Dash 8050 fica vazio | Falha de rede, RSS bloqueado | Teste ping em feeds RSS ou diminua `poll_seconds` |
| "TwitterIngestor disabled" | Tweepy < 4.14 ou token ausente | `pip install --upgrade tweepy` e verifique `.env` |
| GA não gera | Poucos eventos ou `window_events` muito grande | Reduza `window_events` ou aumente fontes de dados |
| YouTube Ingestor desabilitado | Falta yt_dlp ou soundfile | `pip install yt_dlp soundfile` |

---
## 4. APIs úteis

* Última coerência: `GET /api/nexus/coherence`
* Limiar atual: `GET /api/nexus/threshold`
* Estado GA: `GET /api/nexus/ga/state`
* Pausar GA: `POST /api/nexus/ga/pause`
* Retomar GA: `POST /api/nexus/ga/resume`

---
## 5. Logs

Todos os módulos usam o logger do QUALIA (`src/qualia/utils/logger.py`). Para log em arquivo:
```bash
set QUALIA_LOG_FILE=logs/nexus.log
set QUALIA_LOG_LEVEL=DEBUG  # opcional
```

---
## 6. Encerramento seguro

Pressione `CTRL+C` no terminal que executa `run_nexus.py`. O loop assíncrono será encerrado e as threads dos dashboards finalizadas. 