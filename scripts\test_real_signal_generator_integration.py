#!/usr/bin/env python3
"""
Test Real Signal Generator Integration - Task 5 Validation
P-02.3 Phase 2: Real QUALIA Components Implementation

This test validates the integration of the real SignalGenerator
with ultra-conservative safety mechanisms.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

async def test_real_signal_generator_integration():
    """Test real signal generator integration with comprehensive validation"""
    
    print("\n" + "="*80)
    print("🧪 TASK 5: REAL SIGNAL GENERATOR INTEGRATION - VALIDATION TESTS")
    print("="*80)
    
    test_results = []
    
    try:
        # Import the pilot system
        from qualia_pilot_trading_system import QUALIAPilotTradingSystem, RealSignalGenerator
        
        # Test 1: Real Signal Generator Initialization
        print("\n📋 Test 1: Real Signal Generator Initialization")
        try:
            signal_config = {
                'symbol': 'BTCUSDT',
                'min_confidence': 0.8,
                'max_signals_per_symbol': 1
            }
            
            real_signal_generator = RealSignalGenerator(config=signal_config)
            
            # Validate initialization
            assert hasattr(real_signal_generator, 'signal_generator'), "Real SignalGenerator not initialized"
            assert hasattr(real_signal_generator, 'ultra_conservative_min_confidence'), "Ultra-conservative config missing"
            assert real_signal_generator.ultra_conservative_min_confidence == 0.85, "Ultra-conservative threshold incorrect"
            
            print("✅ Test 1 PASSED: Real SignalGenerator initialized successfully")
            test_results.append(("Test 1: Initialization", True))
            
        except Exception as e:
            print(f"❌ Test 1 FAILED: {e}")
            test_results.append(("Test 1: Initialization", False))
        
        # Test 2: Ultra-Conservative Configuration Validation
        print("\n📋 Test 2: Ultra-Conservative Configuration Validation")
        try:
            # Validate ultra-conservative parameters
            assert real_signal_generator.ultra_conservative_min_confidence >= 0.85, "Min confidence too low"
            assert real_signal_generator.ultra_conservative_signal_threshold >= 0.8, "Signal threshold too low"
            assert real_signal_generator.max_signals_per_symbol == 1, "Too many signals allowed"
            assert real_signal_generator.quantum_confidence_threshold >= 0.8, "Quantum threshold too low"
            
            print("✅ Test 2 PASSED: Ultra-conservative configuration validated")
            test_results.append(("Test 2: Ultra-Conservative Config", True))
            
        except Exception as e:
            print(f"❌ Test 2 FAILED: {e}")
            test_results.append(("Test 2: Ultra-Conservative Config", False))
        
        # Test 3: Signal Generation with High Confidence Data
        print("\n📋 Test 3: Signal Generation with High Confidence Data")
        try:
            # Prepare high-confidence test data
            market_data = {
                'symbol': 'BTCUSDT',
                'prices': [50000, 50100, 50200, 50300, 50400],
                'volumes': [100, 110, 120, 130, 140],
                'timestamp': '2024-01-01T12:00:00Z'
            }
            
            quantum_analysis = {
                'consciousness_level': 0.9,  # High consciousness
                'quantum_coherence': 0.85,   # High coherence
                'decision_confidence': 0.88, # High decision confidence
                'temporal_analysis': {
                    'patterns': [{'type': 'bullish', 'confidence': 0.9}]
                }
            }
            
            strategy_decision = {
                'signal': 'buy',
                'confidence': 0.87,  # High confidence
                'position_size': 0.005
            }
            
            # Generate signals
            signals = await real_signal_generator.generate_signals(
                market_data, quantum_analysis, strategy_decision
            )
            
            # Validate signals (may be empty due to ultra-conservative filtering)
            assert isinstance(signals, list), "Signals should be a list"
            
            if signals:
                # If signals generated, validate their structure
                signal = signals[0]
                assert 'symbol' in signal, "Signal missing symbol"
                assert 'action' in signal, "Signal missing action"
                assert 'confidence' in signal, "Signal missing confidence"
                assert signal['confidence'] >= 0.85, "Signal confidence too low"
                assert signal['position_size'] <= 0.005, "Position size too large"
            
            print(f"✅ Test 3 PASSED: Signal generation completed ({len(signals)} signals generated)")
            test_results.append(("Test 3: Signal Generation", True))
            
        except Exception as e:
            print(f"❌ Test 3 FAILED: {e}")
            test_results.append(("Test 3: Signal Generation", False))
        
        # Test 4: Ultra-Conservative Filtering
        print("\n📋 Test 4: Ultra-Conservative Filtering")
        try:
            # Test with low confidence data (should be filtered out)
            low_confidence_quantum = {
                'consciousness_level': 0.7,  # Below threshold
                'quantum_coherence': 0.75,   # Below threshold
                'decision_confidence': 0.7   # Below threshold
            }
            
            low_confidence_strategy = {
                'signal': 'buy',
                'confidence': 0.7,  # Below ultra-conservative threshold
                'position_size': 0.01
            }
            
            # Should return empty signals due to filtering
            filtered_signals = await real_signal_generator.generate_signals(
                market_data, low_confidence_quantum, low_confidence_strategy
            )
            
            # Should be empty due to ultra-conservative filtering
            assert len(filtered_signals) == 0, "Ultra-conservative filtering failed"
            
            print("✅ Test 4 PASSED: Ultra-conservative filtering working correctly")
            test_results.append(("Test 4: Ultra-Conservative Filtering", True))
            
        except Exception as e:
            print(f"❌ Test 4 FAILED: {e}")
            test_results.append(("Test 4: Ultra-Conservative Filtering", False))
        
        # Test 5: Error Handling
        print("\n📋 Test 5: Error Handling")
        try:
            # Test with invalid data
            invalid_signals = await real_signal_generator.generate_signals(
                {}, {}, {}  # Empty data
            )
            
            # Should return empty list, not crash
            assert isinstance(invalid_signals, list), "Error handling failed"
            assert len(invalid_signals) == 0, "Should return empty signals for invalid data"
            
            print("✅ Test 5 PASSED: Error handling returns safe defaults")
            test_results.append(("Test 5: Error Handling", True))
            
        except Exception as e:
            print(f"❌ Test 5 FAILED: {e}")
            test_results.append(("Test 5: Error Handling", False))
        
        # Test 6: Integration with Pilot System
        print("\n📋 Test 6: Integration with Pilot System")
        try:
            # Test pilot system initialization with real signal generator
            config_path = project_root / "config" / "qualia_config.yaml"
            
            if config_path.exists():
                pilot_system = QUALIAPilotTradingSystem(str(config_path))
                
                # Check if real signal generator was initialized
                has_real_signal_generator = hasattr(pilot_system, 'signal_generator_type')
                
                if has_real_signal_generator:
                    signal_generator_type = getattr(pilot_system, 'signal_generator_type', 'unknown')
                    print(f"Signal Generator Type: {signal_generator_type}")
                
                print("✅ Test 6 PASSED: Pilot system integration successful")
                test_results.append(("Test 6: Pilot Integration", True))
            else:
                print("⚠️  Test 6 SKIPPED: Config file not found")
                test_results.append(("Test 6: Pilot Integration", "SKIPPED"))
            
        except Exception as e:
            print(f"❌ Test 6 FAILED: {e}")
            test_results.append(("Test 6: Pilot Integration", False))
        
    except ImportError as e:
        print(f"❌ IMPORT ERROR: {e}")
        print("Make sure the pilot system is properly configured")
        return False
    
    # Summary
    print("\n" + "="*80)
    print("📊 TASK 5 VALIDATION SUMMARY")
    print("="*80)
    
    passed_tests = sum(1 for _, result in test_results if result is True)
    total_tests = len([r for r in test_results if r[1] != "SKIPPED"])
    skipped_tests = sum(1 for _, result in test_results if result == "SKIPPED")
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result is True else "❌ FAILED" if result is False else "⚠️  SKIPPED"
        print(f"{status}: {test_name}")
    
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    print(f"\n📈 SUCCESS RATE: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
    if skipped_tests > 0:
        print(f"⚠️  SKIPPED: {skipped_tests} tests skipped")
    
    if success_rate >= 80:
        print("\n🎉 Task 5: Real Signal Generator Integration - VALIDATION SUCCESSFUL")
        return True
    else:
        print("\n❌ Task 5: Real Signal Generator Integration - VALIDATION FAILED")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_real_signal_generator_integration())
    sys.exit(0 if success else 1)
