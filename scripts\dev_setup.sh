#!/usr/bin/env bash
# scripts/dev_setup.sh
#
# Configura o ambiente de desenvolvimento instalando as dependências
# necessárias para execução dos testes.

set -euo pipefail

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

python -m pip install --upgrade pip
pip install -r requirements.txt
pip install -e ".[dev]"

mkdir -p data/cache logs results

echo "Setup concluído. Execute 'pytest tests/' para rodar a suíte de testes."
