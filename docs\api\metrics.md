# Métricas do QUALIA

Este documento descreve o contêiner `QUALIAMetrics` utilizado para armazenar as métricas geradas durante a execução do `QUALIAQuantumUniverse`.

## Armazenamento controlado por `max_history_size`

`QUALIAMetrics` mantém listas históricas para cada métrica. O argumento `max_history_size` define o número máximo de entradas preservadas para cada lista. Quando o limite é ultrapassado, a entrada mais antiga é descartada automaticamente.

```python
from src.qualia.metrics.metrics_model import QUALIAMetrics

metrics = QUALIAMetrics(max_history_size=200)
metrics.add_entry("page_entropy", 0.42)
```

Definir `max_history_size` evita consumo excessivo de memória em execuções prolongadas.

## Recuperando os valores

Utilize `get_metrics_dict()` para obter um dicionário com todas as listas de métricas atualizadas.

```python
latest = metrics.get_metrics_dict()
page_entropy_history = latest["page_entropy"]
```

## Funções de métricas quânticas

O pacote `src.qualia.metrics` fornece utilitários para cálculo de métricas quânticas, como `renyi_entropy` e `mutual_information`.

```python
from src.qualia.metrics import renyi_entropy, mutual_information

renyi = renyi_entropy(statevector)
mi = mutual_information(density_matrix, [0], [1])
```

## Coerência cross-modal com `cross_modal_coherence`

O método `cross_modal_coherence` calcula a similaridade média entre vetores de
modalidades diferentes. Cada vetor deve estar L2-normalizado e possuir dimensão
igual ou inferior ao maior vetor da lista. Vetores menores são preenchidos com
zeros para alinhar as dimensões antes do cálculo da similaridade coseno.

```python
from qualia.metrics.performance_metrics import cross_modal_coherence
import numpy as np

v_news = np.array([1.0, 0.0])
v_audio = np.array([0.8, 0.6])

coh = cross_modal_coherence([v_news, v_audio])
print(coh)  # valor entre 0 e 1
```

Essa métrica retorna `0.0` quando menos de dois vetores são fornecidos e é
utilizada pelo `CoherenceMonitor` para publicar o nível de coerência no tópico
`nexus.cross_modal_coherence`.

## Métricas fractais

O módulo `qualia.metrics.fractal` implementa utilitários para análise fractal
e cálculo de entropia mórfica via wavelets.

```python
from qualia.metrics.fractal import compute_fractal_metrics
import numpy as np

data = np.random.rand(8, 8)
results = compute_fractal_metrics(data)
print(results["fractal_dimension"], results["morphic_entropy"])
```

`compute_fractal_metrics` retorna um dicionário com `fractal_dimension`, os
coeficientes wavelet e `morphic_entropy`.

## Métrica `lambda_gravity`

O método interno `_lambda_gravity()` do `QUALIAQuantumUniverse` calcula um fator
de gravidade informacional relacionado à volatilidade do sistema. A chamada a
`get_latest_metrics()` inclui o valor atual desse indicador no campo
`lambda_gravity`, possibilitando que as estratégias ajustem a confiança nos
sinais conforme as variações desse parâmetro.

## Métricas de construção e execução

`build_circuit_ms` e `run_ms` registram o tempo gasto (em milissegundos) na
construção do circuito e na execução do universo, respectivamente. Os campos
`build_circuit_count` e `run_count` acumulam o número de chamadas a cada
método. Essas métricas ficam disponíveis no painel Grafana "Universe Ops" para
análise de desempenho e correlação com `trace_id`.

## Coerência simbólica

A função `calculate_symbolic_coherence` compara a entropia atual do sistema com
a distribuição de tokens observados. O valor, registrado como
`symbolic_coherence`, varia de `0` (dissonância completa) a `1` (alinhamento
total entre complexidade e frequência simbólica). `compute_symbolic_coherence`
pode ser usada para atualizar automaticamente as métricas do universo a cada
ciclo.
