# Exchange Clients

Este documento descreve a hierarquia de clientes de exchange utilizada pelo QUALIA.
São camadas simples que encapsulam a integração principal (`CryptoDataFetcher`) e
expõem uma interface assíncrona unificada para consultar preços, enviar ordens e
obter balanços.

## BaseExchange

`BaseExchange` define os métodos essenciais que qualquer integração deve
implementar:

- `initialize` – preparação do cliente e abertura de conexões.
- `get_ticker(symbol)` – obtém informações do par de mercado.
- `place_order(symbol, order_type, side, amount, price)` – envia ordens.
- `get_balance()` – retorna os saldos disponíveis.
- `get_open_orders(symbol)` – lista ordens pendentes.
- `cancel_order(order_id, symbol)` – cancela ordens enviadas.
- `shutdown` – encerra conexões e libera recursos.

Os métodos `get_order_book` e `get_trades` possuem implementações padrão que
retornam `None` ou uma lista vazia e podem ser sobrescritos por subclasses.

## CCXTExchangeClient

`CCXTExchangeClient` estende `BaseExchange` e simplifica integrações baseadas em
[CCXT](https://github.com/ccxt/ccxt). Ele utiliza a classe
`CryptoDataFetcher` como `integration_cls` para realizar as chamadas reais. As
responsabilidades principais incluem:

- Criar a instância de `integration_cls` a partir de `config` filtrando apenas os
  parâmetros suportados.
- Gerenciar o ciclo de vida com `initialize`, `initialize_connection`,
  `reconnect` e `shutdown`.
- Expor um gerenciador de contexto assíncrono para uso com `async with`.
- Encapsular chamadas comuns da API, propagando erros via log e retornando
  valores seguros em falhas.

Subclasses precisam apenas definir `integration_cls` apontando para a
implementação específica do `CryptoDataFetcher`.

## Implementações Específicas

### KrakenClient

`KrakenClient` define `integration_cls = KrakenIntegration` e herda toda a lógica
padrão do `CCXTExchangeClient`. Serve como ponto de acesso à exchange Kraken.

### KuCoinClient

`KuCoinClient` utiliza `integration_cls = KucoinIntegration`, replicando o mesmo
comportamento para a exchange KuCoin.

## Exemplos de Uso

### Kraken

```python
from qualia.exchanges import KrakenClient

config = {
    "api_key": "SUA_API_KEY",
    "api_secret": "SEU_SEGREDO",
    "use_websocket": True,
}

client = KrakenClient(config)
await client.initialize()
ticker = await client.get_ticker("ETH/USD")
# ...
await client.shutdown()
```

Também é possível utilizar o gerenciador de contexto:

```python
async with KrakenClient(config) as client:
    ticker = await client.get_ticker("ETH/USD")
    # conexao sera fechada automaticamente
```

### KuCoin

```python
from qualia.exchanges import KuCoinClient

config = {
    "api_key": "SUA_API_KEY",
    "api_secret": "SEU_SEGREDO",
    "password": "SUA_PASSPHRASE",
    "use_websocket": True,
}

kucoin = KuCoinClient(config)
await kucoin.initialize()
# chamadas da API ...
await kucoin.shutdown()
```
