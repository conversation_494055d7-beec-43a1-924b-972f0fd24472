#!/usr/bin/env python3
"""Demo de integração do HolographicUniverse com dashboard em tempo real."""

from __future__ import annotations

import asyncio

from src.qualia.consciousness.holographic_universe import HolographicMarketUniverse
from src.qualia.consciousness.real_data_collectors import create_real_data_collector
from src.qualia.ui.dashboards.holographic_universe import init_app
from src.qualia.utils.event_bus import (
    EventBus,
    HolographicSignalsUpdated,
)
from src.qualia.utils.logger import get_logger

logger = get_logger(__name__)


async def main() -> None:
    """Executa coleta de dados e inicia dashboard."""
    bus = EventBus()
    universe = HolographicMarketUniverse(event_bus=bus)
    app = init_app(bus)

    async with create_real_data_collector(event_bus=bus) as collector:

        async def _collect() -> None:
            async for events in collector.start_real_time_collection(universe, 60.0):
                logger.info("Eventos coletados: %d", len(events))
                for event in events:
                    await universe.inject_holographic_event(event)
                await universe.step_evolution(events[-1].time if events else 0)
                patterns = universe.analyze_holographic_patterns()
                signals = universe.generate_trading_signals(patterns)
                if signals:
                    payload = HolographicSignalsUpdated(
                        signals={s.symbol: s for s in signals}, timestamp=0.0
                    )
                    bus.publish("holographic.signals.updated", payload)

        loop = asyncio.get_event_loop()
        loop.create_task(_collect())
        app.run_server(debug=False, port=8070)


if __name__ == "__main__":
    asyncio.run(main())
