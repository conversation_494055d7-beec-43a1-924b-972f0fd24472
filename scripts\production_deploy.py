#!/usr/bin/env python3
"""
QUALIA Production Deployment Script
Automated deployment, initialization, and management for production environment
"""

import os
import sys
import json
import time
import signal
import subprocess
import argparse
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
import yaml
import psutil
import asyncio

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.qualia.logging.production_logger import initialize_production_logging
from src.qualia.security.credentials_manager import production_credentials
from src.qualia.monitoring.production_monitor import initialize_production_monitoring
from src.qualia.backup.backup_manager import ProductionBackupManager

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProductionDeployment:
    """
    Production deployment and management system
    Handles deployment, startup, shutdown, restart, and health checks
    """
    
    def __init__(self, config_path: str = "config/production_config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.pid_file = "run/qualia_production.pid"
        self.log_file = "logs/deployment.log"
        self.status_file = "run/qualia_status.json"
        
        # Create necessary directories
        os.makedirs("run", exist_ok=True)
        os.makedirs("logs", exist_ok=True)
        os.makedirs("backups", exist_ok=True)
        
        # Deployment state
        self.deployment_id = f"deploy_{int(time.time())}"
        self.start_time = None
        
    def _load_config(self) -> Dict[str, Any]:
        """Load production configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load config from {self.config_path}: {e}")
            sys.exit(1)
    
    def deploy(self, force: bool = False) -> bool:
        """
        Deploy QUALIA to production environment
        
        Args:
            force: Force deployment even if system is running
            
        Returns:
            bool: Deployment success
        """
        logger.info(f"Starting QUALIA production deployment: {self.deployment_id}")
        
        try:
            # Pre-deployment checks
            if not self._pre_deployment_checks(force):
                return False
            
            # Backup current state
            if not self._backup_current_state():
                return False
            
            # Validate configuration
            if not self._validate_configuration():
                return False
            
            # Setup environment
            if not self._setup_environment():
                return False
            
            # Install/update dependencies
            if not self._install_dependencies():
                return False
            
            # Initialize security
            if not self._initialize_security():
                return False
            
            # Deploy application
            if not self._deploy_application():
                return False
            
            # Post-deployment validation
            if not self._post_deployment_validation():
                return False
            
            logger.info(f"QUALIA production deployment completed successfully: {self.deployment_id}")
            return True
            
        except Exception as e:
            logger.error(f"Deployment failed: {e}")
            self._rollback_deployment()
            return False
    
    def start(self, daemon: bool = True) -> bool:
        """
        Start QUALIA production system
        
        Args:
            daemon: Run as daemon process
            
        Returns:
            bool: Start success
        """
        logger.info("Starting QUALIA production system")
        
        try:
            # Check if already running
            if self._is_running():
                logger.warning("QUALIA is already running")
                return False
            
            # Pre-start validation
            if not self._pre_start_validation():
                return False
            
            # Start system
            if daemon:
                return self._start_daemon()
            else:
                return self._start_foreground()
                
        except Exception as e:
            logger.error(f"Failed to start QUALIA: {e}")
            return False
    
    def stop(self, timeout: int = 30) -> bool:
        """
        Stop QUALIA production system
        
        Args:
            timeout: Timeout in seconds for graceful shutdown
            
        Returns:
            bool: Stop success
        """
        logger.info("Stopping QUALIA production system")
        
        try:
            if not self._is_running():
                logger.info("QUALIA is not running")
                return True
            
            # Get process ID
            pid = self._get_pid()
            if not pid:
                logger.error("Could not determine QUALIA process ID")
                return False
            
            # Graceful shutdown
            logger.info(f"Sending SIGTERM to process {pid}")
            os.kill(pid, signal.SIGTERM)
            
            # Wait for graceful shutdown
            for i in range(timeout):
                if not self._is_running():
                    logger.info("QUALIA stopped gracefully")
                    self._cleanup_pid_file()
                    return True
                time.sleep(1)
            
            # Force shutdown
            logger.warning(f"Graceful shutdown timeout, sending SIGKILL to process {pid}")
            os.kill(pid, signal.SIGKILL)
            
            # Wait for force shutdown
            for i in range(10):
                if not self._is_running():
                    logger.info("QUALIA stopped forcefully")
                    self._cleanup_pid_file()
                    return True
                time.sleep(1)
            
            logger.error("Failed to stop QUALIA")
            return False
            
        except Exception as e:
            logger.error(f"Failed to stop QUALIA: {e}")
            return False
    
    def restart(self, timeout: int = 30) -> bool:
        """
        Restart QUALIA production system
        
        Args:
            timeout: Timeout for shutdown
            
        Returns:
            bool: Restart success
        """
        logger.info("Restarting QUALIA production system")
        
        # Stop system
        if not self.stop(timeout):
            return False
        
        # Wait a moment
        time.sleep(2)
        
        # Start system
        return self.start()
    
    def status(self) -> Dict[str, Any]:
        """
        Get QUALIA system status
        
        Returns:
            Dict with system status information
        """
        try:
            status = {
                'timestamp': datetime.utcnow().isoformat(),
                'running': self._is_running(),
                'pid': self._get_pid(),
                'uptime_seconds': self._get_uptime(),
                'config_file': self.config_path,
                'deployment_id': self.deployment_id
            }
            
            # Add process information if running
            if status['running'] and status['pid']:
                try:
                    process = psutil.Process(status['pid'])
                    status['process_info'] = {
                        'memory_mb': process.memory_info().rss / 1024 / 1024,
                        'cpu_percent': process.cpu_percent(),
                        'num_threads': process.num_threads(),
                        'create_time': process.create_time()
                    }
                except Exception as e:
                    status['process_info'] = {'error': str(e)}
            
            # Save status to file
            with open(self.status_file, 'w') as f:
                json.dump(status, f, indent=2)
            
            return status
            
        except Exception as e:
            logger.error(f"Failed to get status: {e}")
            return {'error': str(e)}
    
    def health_check(self) -> Dict[str, Any]:
        """
        Perform comprehensive health check
        
        Returns:
            Dict with health check results
        """
        logger.info("Performing QUALIA health check")
        
        health = {
            'timestamp': datetime.utcnow().isoformat(),
            'overall_status': 'UNKNOWN',
            'checks': {}
        }
        
        try:
            # System running check
            health['checks']['system_running'] = {
                'status': 'PASS' if self._is_running() else 'FAIL',
                'message': 'System is running' if self._is_running() else 'System is not running'
            }
            
            # Configuration check
            health['checks']['configuration'] = {
                'status': 'PASS' if self._validate_configuration() else 'FAIL',
                'message': 'Configuration is valid' if self._validate_configuration() else 'Configuration is invalid'
            }
            
            # Credentials check
            health['checks']['credentials'] = self._check_credentials_health()
            
            # Network connectivity check
            health['checks']['connectivity'] = self._check_connectivity()
            
            # Disk space check
            health['checks']['disk_space'] = self._check_disk_space()
            
            # Memory check
            health['checks']['memory'] = self._check_memory()
            
            # Determine overall status
            failed_checks = [check for check in health['checks'].values() 
                           if check['status'] == 'FAIL']
            warning_checks = [check for check in health['checks'].values() 
                            if check['status'] == 'WARNING']
            
            if failed_checks:
                health['overall_status'] = 'CRITICAL'
            elif warning_checks:
                health['overall_status'] = 'WARNING'
            else:
                health['overall_status'] = 'HEALTHY'
            
            return health
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            health['overall_status'] = 'ERROR'
            health['error'] = str(e)
            return health
    
    def _pre_deployment_checks(self, force: bool) -> bool:
        """Perform pre-deployment checks"""
        logger.info("Performing pre-deployment checks")
        
        # Check if system is running
        if self._is_running() and not force:
            logger.error("QUALIA is currently running. Use --force to override")
            return False
        
        # Check Python version
        if sys.version_info < (3, 8):
            logger.error("Python 3.8+ is required")
            return False
        
        # Check disk space
        disk_check = self._check_disk_space()
        if disk_check['status'] == 'FAIL':
            logger.error(f"Insufficient disk space: {disk_check['message']}")
            return False
        
        # Check memory
        memory_check = self._check_memory()
        if memory_check['status'] == 'FAIL':
            logger.error(f"Insufficient memory: {memory_check['message']}")
            return False
        
        return True
    
    def _backup_current_state(self) -> bool:
        """Backup current system state"""
        logger.info("Backing up current state")
        
        try:
            backup_dir = f"backups/backup_{int(time.time())}"
            os.makedirs(backup_dir, exist_ok=True)
            
            # Backup configuration
            if os.path.exists(self.config_path):
                subprocess.run(['cp', self.config_path, f"{backup_dir}/"], check=True)
            
            # Backup data files
            data_files = ['data/positions.json', 'data/trades.json', 'data/optimization_results.json']
            for data_file in data_files:
                if os.path.exists(data_file):
                    subprocess.run(['cp', data_file, f"{backup_dir}/"], check=True)
            
            logger.info(f"Backup completed: {backup_dir}")
            return True
            
        except Exception as e:
            logger.error(f"Backup failed: {e}")
            return False
    
    def _validate_configuration(self) -> bool:
        """Validate production configuration"""
        try:
            # Check required sections
            required_sections = ['environment', 'capital', 'risk_management', 'trading', 'exchange']
            for section in required_sections:
                if section not in self.config:
                    logger.error(f"Missing required configuration section: {section}")
                    return False
            
            # Validate environment
            env = self.config.get('environment', {})
            if env.get('name') != 'production':
                logger.error("Configuration is not set for production environment")
                return False
            
            # Validate risk management
            risk = self.config.get('risk_management', {})
            if risk.get('max_position_size', 0) > 0.05:  # 5% max
                logger.error("Position size limit too high for production")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False
    
    def _setup_environment(self) -> bool:
        """Setup production environment"""
        logger.info("Setting up production environment")
        
        try:
            # Set environment variables
            os.environ['QUALIA_ENV'] = 'production'
            os.environ['QUALIA_CONFIG'] = self.config_path
            
            # Create necessary directories
            directories = ['logs', 'data', 'run', 'backups', 'config']
            for directory in directories:
                os.makedirs(directory, exist_ok=True)
            
            return True
            
        except Exception as e:
            logger.error(f"Environment setup failed: {e}")
            return False
    
    def _install_dependencies(self) -> bool:
        """Install/update Python dependencies"""
        logger.info("Installing/updating dependencies")
        
        try:
            # Install requirements
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                         check=True, capture_output=True)
            
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Dependency installation failed: {e}")
            return False
    
    def _initialize_security(self) -> bool:
        """Initialize security components"""
        logger.info("Initializing security components")
        
        try:
            # This would initialize credentials manager
            # For now, just check if credentials exist
            return True
            
        except Exception as e:
            logger.error(f"Security initialization failed: {e}")
            return False
    
    def _deploy_application(self) -> bool:
        """Deploy application components"""
        logger.info("Deploying application components")
        
        try:
            # This would copy/update application files
            # For now, assume files are already in place
            return True
            
        except Exception as e:
            logger.error(f"Application deployment failed: {e}")
            return False
    
    def _post_deployment_validation(self) -> bool:
        """Validate deployment"""
        logger.info("Validating deployment")
        
        try:
            # Check if all required files exist
            required_files = [
                'src/qualia/logging/production_logger.py',
                'src/qualia/security/credentials_manager.py',
                'src/qualia/monitoring/production_monitor.py'
            ]
            
            for file_path in required_files:
                if not os.path.exists(file_path):
                    logger.error(f"Required file missing: {file_path}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Deployment validation failed: {e}")
            return False
    
    def _rollback_deployment(self):
        """Rollback failed deployment"""
        logger.warning("Rolling back deployment")
        # Implementation would restore from backup
        pass
    
    def _pre_start_validation(self) -> bool:
        """Validate system before starting"""
        # Check configuration
        if not self._validate_configuration():
            return False
        
        # Check credentials
        cred_check = self._check_credentials_health()
        if cred_check['status'] == 'FAIL':
            logger.error(f"Credentials check failed: {cred_check['message']}")
            return False
        
        return True
    
    def _start_daemon(self) -> bool:
        """Start QUALIA as daemon process"""
        try:
            # Fork process
            pid = os.fork()
            if pid > 0:
                # Parent process
                with open(self.pid_file, 'w') as f:
                    f.write(str(pid))
                logger.info(f"QUALIA started as daemon with PID {pid}")
                return True
            else:
                # Child process
                self._run_main_loop()
                return True
                
        except Exception as e:
            logger.error(f"Failed to start daemon: {e}")
            return False
    
    def _start_foreground(self) -> bool:
        """Start QUALIA in foreground"""
        try:
            pid = os.getpid()
            with open(self.pid_file, 'w') as f:
                f.write(str(pid))
            
            logger.info(f"QUALIA starting in foreground with PID {pid}")
            self._run_main_loop()
            return True
            
        except Exception as e:
            logger.error(f"Failed to start in foreground: {e}")
            return False
    
    def _run_main_loop(self):
        """Main QUALIA execution loop"""
        try:
            self.start_time = time.time()
            
            # Initialize logging
            prod_logger = initialize_production_logging(self.config)
            prod_logger.log_system_startup(self.config)
            
            # Initialize monitoring
            asyncio.run(initialize_production_monitoring(self.config))
            
            # Main loop would go here
            logger.info("QUALIA main loop started")
            
            # For now, just sleep (in production this would be the actual trading loop)
            while True:
                time.sleep(60)
                logger.info("QUALIA heartbeat")
                
        except KeyboardInterrupt:
            logger.info("QUALIA shutdown requested")
        except Exception as e:
            logger.error(f"QUALIA main loop error: {e}")
        finally:
            self._cleanup_pid_file()
    
    def _is_running(self) -> bool:
        """Check if QUALIA is running"""
        try:
            pid = self._get_pid()
            if not pid:
                return False
            
            # Check if process exists
            return psutil.pid_exists(pid)
            
        except Exception:
            return False
    
    def _get_pid(self) -> Optional[int]:
        """Get QUALIA process ID"""
        try:
            if os.path.exists(self.pid_file):
                with open(self.pid_file, 'r') as f:
                    return int(f.read().strip())
            return None
        except Exception:
            return None
    
    def _get_uptime(self) -> Optional[float]:
        """Get system uptime in seconds"""
        try:
            pid = self._get_pid()
            if pid and psutil.pid_exists(pid):
                process = psutil.Process(pid)
                return time.time() - process.create_time()
            return None
        except Exception:
            return None
    
    def _cleanup_pid_file(self):
        """Clean up PID file"""
        try:
            if os.path.exists(self.pid_file):
                os.remove(self.pid_file)
        except Exception as e:
            logger.error(f"Failed to cleanup PID file: {e}")
    
    def _check_credentials_health(self) -> Dict[str, Any]:
        """Check credentials health"""
        try:
            # This would check actual credentials
            return {
                'status': 'PASS',
                'message': 'Credentials are valid'
            }
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Credentials check failed: {e}'
            }
    
    def _check_connectivity(self) -> Dict[str, Any]:
        """Check network connectivity"""
        try:
            # Test internet connectivity
            import urllib.request
            urllib.request.urlopen('https://api.kucoin.com/api/v1/timestamp', timeout=10)
            
            return {
                'status': 'PASS',
                'message': 'Network connectivity is good'
            }
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Network connectivity failed: {e}'
            }
    
    def _check_disk_space(self) -> Dict[str, Any]:
        """Check available disk space"""
        try:
            disk_usage = psutil.disk_usage('.')
            free_gb = disk_usage.free / (1024**3)
            
            if free_gb < 1.0:  # Less than 1GB
                return {
                    'status': 'FAIL',
                    'message': f'Insufficient disk space: {free_gb:.1f}GB available'
                }
            elif free_gb < 5.0:  # Less than 5GB
                return {
                    'status': 'WARNING',
                    'message': f'Low disk space: {free_gb:.1f}GB available'
                }
            else:
                return {
                    'status': 'PASS',
                    'message': f'Sufficient disk space: {free_gb:.1f}GB available'
                }
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Disk space check failed: {e}'
            }
    
    def _check_memory(self) -> Dict[str, Any]:
        """Check available memory"""
        try:
            memory = psutil.virtual_memory()
            available_gb = memory.available / (1024**3)
            
            if available_gb < 0.5:  # Less than 500MB
                return {
                    'status': 'FAIL',
                    'message': f'Insufficient memory: {available_gb:.1f}GB available'
                }
            elif available_gb < 1.0:  # Less than 1GB
                return {
                    'status': 'WARNING',
                    'message': f'Low memory: {available_gb:.1f}GB available'
                }
            else:
                return {
                    'status': 'PASS',
                    'message': f'Sufficient memory: {available_gb:.1f}GB available'
                }
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Memory check failed: {e}'
            }

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='QUALIA Production Deployment')
    parser.add_argument('command', choices=['deploy', 'start', 'stop', 'restart', 'status', 'health'],
                       help='Command to execute')
    parser.add_argument('--config', default='config/production_config.yaml',
                       help='Configuration file path')
    parser.add_argument('--force', action='store_true',
                       help='Force deployment even if system is running')
    parser.add_argument('--daemon', action='store_true', default=True,
                       help='Run as daemon (default: True)')
    parser.add_argument('--timeout', type=int, default=30,
                       help='Timeout for stop/restart operations')
    
    args = parser.parse_args()
    
    # Initialize deployment manager
    deployment = ProductionDeployment(args.config)
    
    # Execute command
    if args.command == 'deploy':
        success = deployment.deploy(args.force)
        sys.exit(0 if success else 1)
    
    elif args.command == 'start':
        success = deployment.start(args.daemon)
        sys.exit(0 if success else 1)
    
    elif args.command == 'stop':
        success = deployment.stop(args.timeout)
        sys.exit(0 if success else 1)
    
    elif args.command == 'restart':
        success = deployment.restart(args.timeout)
        sys.exit(0 if success else 1)
    
    elif args.command == 'status':
        status = deployment.status()
        print(json.dumps(status, indent=2))
        sys.exit(0)
    
    elif args.command == 'health':
        health = deployment.health_check()
        print(json.dumps(health, indent=2))
        sys.exit(0 if health['overall_status'] in ['HEALTHY', 'WARNING'] else 1)

if __name__ == '__main__':
    main()
