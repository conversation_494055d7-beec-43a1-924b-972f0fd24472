#!/usr/bin/env python3
"""
Test script for unified configuration manager.
"""

import sys
import os
import tempfile
import json
import yaml
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_basic_config_loading():
    """Test basic configuration loading"""
    print("🧪 Testing Basic Configuration Loading")
    print("=" * 50)
    
    try:
        from src.qualia.config.unified_config_manager import UnifiedConfigManager, ConfigSource
        
        # Create temporary config files
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create test YAML config
            yaml_config = {
                'test_section': {
                    'value1': 'from_yaml',
                    'value2': 42
                },
                'shared_key': 'yaml_value'
            }
            
            yaml_file = temp_path / "test.yaml"
            with open(yaml_file, 'w') as f:
                yaml.dump(yaml_config, f)
            
            # Create test JSON config
            json_config = {
                'test_section': {
                    'value3': 'from_json'
                },
                'shared_key': 'json_value',  # Should override YAML
                'json_only': True
            }
            
            json_file = temp_path / "test.json"
            with open(json_file, 'w') as f:
                json.dump(json_config, f)
            
            # Create config manager
            manager = UnifiedConfigManager()
            
            # Clear default sources for clean test
            manager.sources.clear()
            
            # Add test sources
            manager.add_source("yaml_source", yaml_file, priority=10, format="yaml")
            manager.add_source("json_source", json_file, priority=20, format="json")  # Higher priority
            
            # Load configuration
            config = manager.load_all()
            
            # Verify merged configuration
            if config['shared_key'] == 'json_value':
                print("✅ Priority-based merging working correctly")
            else:
                print("❌ Priority-based merging failed")
                return False
            
            if config['test_section']['value1'] == 'from_yaml':
                print("✅ YAML configuration loaded correctly")
            else:
                print("❌ YAML configuration not loaded correctly")
                return False
            
            if config['test_section']['value3'] == 'from_json':
                print("✅ JSON configuration loaded correctly")
            else:
                print("❌ JSON configuration not loaded correctly")
                return False
            
            if config.get('json_only') is True:
                print("✅ JSON-only values preserved")
            else:
                print("❌ JSON-only values not preserved")
                return False
            
            return True
            
    except Exception as e:
        print(f"❌ Error in basic config loading: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_environment_overrides():
    """Test environment variable overrides"""
    print("\n🧪 Testing Environment Variable Overrides")
    print("=" * 50)
    
    try:
        from src.qualia.config.unified_config_manager import UnifiedConfigManager
        
        # Set test environment variables
        os.environ['QUALIA_PRICE_AMP'] = '7.5'
        os.environ['QUALIA_NEWS_AMP'] = '3.2'
        os.environ['QUALIA_TEST_BOOL'] = 'true'
        os.environ['QUALIA_TEST_INT'] = '123'
        
        try:
            # Create temporary config file
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                base_config = {
                    'price_amplification': 5.0,
                    'news_amplification': 4.0,
                    'test_bool': False,
                    'test_int': 456
                }
                
                config_file = temp_path / "base.yaml"
                with open(config_file, 'w') as f:
                    yaml.dump(base_config, f)
                
                # Create config manager
                manager = UnifiedConfigManager()
                manager.sources.clear()
                manager.add_source("base", config_file, priority=10, format="yaml")
                
                # Load configuration (should apply env overrides)
                config = manager.load_all()
                
                # Check overrides
                if config.get('price_amplification') == 7.5:
                    print("✅ Environment override for price_amplification working")
                else:
                    print(f"❌ Environment override failed: {config.get('price_amplification')}")
                    return False
                
                if config.get('news_amplification') == 3.2:
                    print("✅ Environment override for news_amplification working")
                else:
                    print(f"❌ Environment override failed: {config.get('news_amplification')}")
                    return False
                
                if config.get('test_bool') is True:
                    print("✅ Boolean environment override working")
                else:
                    print(f"❌ Boolean environment override failed: {config.get('test_bool')}")
                    return False
                
                if config.get('test_int') == 123:
                    print("✅ Integer environment override working")
                else:
                    print(f"❌ Integer environment override failed: {config.get('test_int')}")
                    return False
                
                return True
                
        finally:
            # Clean up environment variables
            for key in ['QUALIA_PRICE_AMP', 'QUALIA_NEWS_AMP', 'QUALIA_TEST_BOOL', 'QUALIA_TEST_INT']:
                if key in os.environ:
                    del os.environ[key]
            
    except Exception as e:
        print(f"❌ Error in environment overrides test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_dot_notation_access():
    """Test dot notation configuration access"""
    print("\n🧪 Testing Dot Notation Access")
    print("=" * 50)
    
    try:
        from src.qualia.config.unified_config_manager import UnifiedConfigManager
        
        # Create temporary config file
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            nested_config = {
                'level1': {
                    'level2': {
                        'level3': {
                            'deep_value': 'found_it'
                        },
                        'array': [1, 2, 3]
                    },
                    'simple_value': 'level1_value'
                },
                'root_value': 'root'
            }
            
            config_file = temp_path / "nested.yaml"
            with open(config_file, 'w') as f:
                yaml.dump(nested_config, f)
            
            # Create config manager
            manager = UnifiedConfigManager()
            manager.sources.clear()
            manager.add_source("nested", config_file, priority=10, format="yaml")
            
            # Load configuration
            manager.load_all()
            
            # Test dot notation access
            if manager.get('root_value') == 'root':
                print("✅ Root level access working")
            else:
                print("❌ Root level access failed")
                return False
            
            if manager.get('level1.simple_value') == 'level1_value':
                print("✅ Two-level dot notation working")
            else:
                print("❌ Two-level dot notation failed")
                return False
            
            if manager.get('level1.level2.level3.deep_value') == 'found_it':
                print("✅ Deep dot notation working")
            else:
                print("❌ Deep dot notation failed")
                return False
            
            if manager.get('nonexistent.key', 'default') == 'default':
                print("✅ Default value for missing key working")
            else:
                print("❌ Default value for missing key failed")
                return False
            
            return True
            
    except Exception as e:
        print(f"❌ Error in dot notation test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_runtime_configuration():
    """Test runtime configuration changes"""
    print("\n🧪 Testing Runtime Configuration Changes")
    print("=" * 50)
    
    try:
        from src.qualia.config.unified_config_manager import UnifiedConfigManager
        
        # Create config manager
        manager = UnifiedConfigManager()
        manager.sources.clear()
        
        # Load empty configuration
        manager.load_all()
        
        # Set runtime values
        manager.set('runtime.test_value', 'runtime_set')
        manager.set('nested.deep.value', 42)
        
        # Test retrieval
        if manager.get('runtime.test_value') == 'runtime_set':
            print("✅ Runtime configuration setting working")
        else:
            print("❌ Runtime configuration setting failed")
            return False
        
        if manager.get('nested.deep.value') == 42:
            print("✅ Runtime nested configuration working")
        else:
            print("❌ Runtime nested configuration failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in runtime configuration test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_status():
    """Test configuration manager status"""
    print("\n🧪 Testing Configuration Manager Status")
    print("=" * 50)
    
    try:
        from src.qualia.config.unified_config_manager import UnifiedConfigManager
        
        # Create config manager with default sources
        manager = UnifiedConfigManager()
        
        # Get status
        status = manager.get_status()
        
        # Check status structure
        required_keys = ['sources', 'hot_reload_enabled', 'schemas_count', 'callbacks_count', 'config_keys']
        
        for key in required_keys:
            if key not in status:
                print(f"❌ Missing status key: {key}")
                return False
        
        if len(status['sources']) > 0:
            print(f"✅ Status shows {len(status['sources'])} sources")
        else:
            print("❌ No sources in status")
            return False
        
        # Check source details
        for source_name, source_info in status['sources'].items():
            required_source_keys = ['path', 'priority', 'format', 'hot_reload', 'required', 'loaded']
            for key in required_source_keys:
                if key not in source_info:
                    print(f"❌ Missing source info key {key} for {source_name}")
                    return False
        
        print("✅ Configuration status working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Error in status test: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests"""
    print("🚀 Starting Unified Configuration Manager Tests")
    print("=" * 80)
    
    tests = [
        test_basic_config_loading,
        test_environment_overrides,
        test_dot_notation_access,
        test_runtime_configuration,
        test_config_status
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ FAIL: {test.__name__} raised exception: {e}")
            failed += 1
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 TEST SUMMARY")
    print("=" * 80)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 All configuration manager tests passed!")
        return True
    else:
        print(f"\n💥 {failed} test(s) failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
