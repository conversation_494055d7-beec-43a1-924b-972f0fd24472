"""Auxiliary risk functions for QUALIA market module."""

from __future__ import annotations

import os
from dataclasses import dataclass, fields
from typing import Any, Dict, List, Tuple, Union

import numpy as np

from ..utils.logger import get_logger
from ..indicators import atr
from ..config.dynamic_risk_defaults import load_dynamic_risk_defaults
from ..market.volatility_utils import calculate_implied_volatility


logger = get_logger(__name__)


@dataclass
class DynamicRiskParameters:
    """Container for dynamic risk control parameters."""

    atr_period: int = 14
    atr_multiplier_base: float = 0.8
    atr_multiplier_volatility_factor: float = 0.5
    take_profit_base_ratio: float = 2.5
    take_profit_volatility_adjustment: float = 0.3
    volatility_lookback_periods: int = 20
    volatility_threshold_low: float = 0.08
    volatility_threshold_high: float = 0.25
    recalibration_frequency_minutes: int = 5
    min_adjustment_threshold: float = 0.05
    max_adjustment_factor: float = 2.0
    regime_calm_multiplier: float = 0.8
    regime_volatile_multiplier: float = 1.3
    regime_normal_multiplier: float = 1.0


def validate_dynamic_risk_parameters(params: Dict[str, Any]) -> None:
    """Validate a mapping of dynamic risk parameters."""

    valid_fields = {f.name for f in fields(DynamicRiskParameters)}
    for key, value in params.items():
        if key not in valid_fields:
            raise ValueError(f"Par\u00e2metro desconhecido: {key}")
        if isinstance(value, (int, float)) and value < 0:
            raise ValueError(f"{key} deve ser n\u00e3o-negativo")


def load_risk_parameters(
    profile: str, config: Dict[str, Any], defaults: Dict[str, Any]
) -> DynamicRiskParameters:
    """Load parameters based on risk profile, config and defaults."""

    base_dict = defaults.get("defaults", defaults)
    try:
        params = DynamicRiskParameters(**base_dict)
    except TypeError:
        params = DynamicRiskParameters()
        for key, value in base_dict.items():
            if hasattr(params, key):
                setattr(params, key, value)

    profile_overrides = defaults.get("profiles", {}).get(profile, {})
    overrides = {**profile_overrides, **config.get("dynamic_risk_parameters", {})}
    validate_dynamic_risk_parameters(overrides)

    for key, value in overrides.items():
        if hasattr(params, key):
            setattr(params, key, value)

    for field in fields(DynamicRiskParameters):
        env_name = f"QUALIA_DYN_RISK_{field.name.upper()}"
        env_value = os.getenv(env_name)
        if env_value is not None:
            try:
                cast = int(env_value) if field.type is int else float(env_value)
            except ValueError:
                logger.error("Valor inv\u00e1lido para %s: %s", env_name, env_value)
                continue
            setattr(params, field.name, cast)

    return params


def calculate_atr_levels(
    high_data: Union[List[float], np.ndarray],
    low_data: Union[List[float], np.ndarray],
    close_data: Union[List[float], np.ndarray],
    current_price: float,
    params: DynamicRiskParameters,
    *,
    use_gpu: bool | None = None,
) -> Tuple[float, float, float]:
    """Return ATR value and SL/TP distances."""

    atr_values = atr(
        high=high_data, low=low_data, close=close_data, period=params.atr_period
    )
    current_atr = (
        atr_values[-1] if not np.isnan(atr_values[-1]) else np.nanmean(atr_values[-5:])
    )
    if np.isnan(current_atr) or current_atr <= 0:
        current_atr = current_price * 0.01
        logger.warning("ATR inv\u00e1lido, usando fallback: %s", current_atr)

    implied_vol = calculate_implied_volatility(close_data, use_gpu=use_gpu)
    vol_adjustment = 1.0
    if implied_vol < params.volatility_threshold_low:
        vol_adjustment = 0.8
    elif implied_vol > params.volatility_threshold_high:
        vol_adjustment = 1.3

    effective_multiplier = (
        params.atr_multiplier_base
        * vol_adjustment
        * (1 + implied_vol * params.atr_multiplier_volatility_factor)
    )
    effective_multiplier = np.clip(
        effective_multiplier, 0.5, params.max_adjustment_factor * 2
    )

    stop_distance = current_atr * effective_multiplier
    take_profit_distance = stop_distance * params.take_profit_base_ratio
    tp_vol_adjustment = 1 + (implied_vol * params.take_profit_volatility_adjustment)
    take_profit_distance *= tp_vol_adjustment
    return float(current_atr), float(stop_distance), float(take_profit_distance)


def load_default_parameters() -> Dict[str, Any]:
    """Helper to fetch defaults from YAML configuration."""

    return load_dynamic_risk_defaults()


__all__ = [
    "DynamicRiskParameters",
    "validate_dynamic_risk_parameters",
    "load_risk_parameters",
    "calculate_atr_levels",
    "load_default_parameters",
]
