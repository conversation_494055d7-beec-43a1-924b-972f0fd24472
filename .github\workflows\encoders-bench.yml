name: Encoders Benchmark CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  benchmark:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    - name: Install dependencies
      run: |
        ./scripts/install_test_deps.sh
        pip install -e .[dev]
    - name: Generate Fixture (if not committed)
      id: fixture_check
      run: |
        if [ ! -f fixtures/order_book_snapshots.npy ]; then
          echo "Fixture not found, generating..."
          python fixtures/generate_fixture.py
          echo "fixture_generated=true" >> $GITHUB_OUTPUT
        else
          echo "Fixture found."
          echo "fixture_generated=false" >> $GITHUB_OUTPUT
        fi
    - name: Run Encoders Benchmark
      run: python benchmarks/encoders/run_encoder_bench.py --all --output-file benchmark_results.json
    - name: Verify Benchmark Results
      run: |
        if [ -f benchmark_results.json ]; then
          echo "Benchmark results file exists."
        else
          echo "Benchmark results file is missing!" >&2
          exit 1
        fi
    - name: Upload Benchmark Results
      uses: actions/upload-artifact@v4
      with:
        name: benchmark-results
        path: benchmark_results.json
