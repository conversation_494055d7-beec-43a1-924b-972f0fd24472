#!/usr/bin/env python3
"""
Test Quantum Decision Engine - P-02.3 Phase 2 Task 3
Validates ultra-conservative quantum decision engine configuration and operation
"""

import asyncio
import logging
import os
import sys
import json
from pathlib import Path

# Add src to path for QUALIA imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_quantum_decision_engine():
    """Test quantum decision engine with ultra-conservative settings"""
    logger.info("🧪 Starting Quantum Decision Engine Test...")
    
    try:
        # Import the pilot system
        from qualia_pilot_trading_system import QUALIAPilotTradingSystem
        
        # Initialize pilot system
        pilot = QUALIAPilotTradingSystem("config/pilot_config.yaml")
        
        # Test 1: Load Configuration
        logger.info("📋 Test 1: Configuration Loading...")
        config_loaded = await pilot.load_configuration()
        if not config_loaded:
            logger.error("❌ Configuration loading failed")
            return False
        logger.info("✅ Configuration loaded successfully")
        
        # Test 2: Initialize Components
        logger.info("🧠 Test 2: Component Initialization...")
        components_initialized = await pilot.initialize_qualia_components()
        if not components_initialized:
            logger.error("❌ Component initialization failed")
            return False
        logger.info("✅ Components initialized successfully")
        
        # Test 3: Validate Ultra-Conservative Configuration
        logger.info("🛡️ Test 3: Ultra-Conservative Configuration Validation...")
        
        # Check consciousness configuration
        consciousness_config = pilot._create_ultra_conservative_consciousness_config()
        oracle_config = pilot._create_ultra_conservative_oracle_config()
        
        # Validate consciousness thresholds
        consciousness_params = consciousness_config.get('consciousness', {})
        assert consciousness_params.get('base_level', 1.0) <= 0.3, "Base consciousness level too high"
        assert consciousness_params.get('coherence_weight', 0.0) >= 0.5, "Coherence weight too low"
        logger.info("✅ Consciousness configuration is ultra-conservative")
        
        # Validate oracle thresholds
        oracle_consciousness = oracle_config.get('consciousness', {})
        assert oracle_consciousness.get('base_level', 1.0) <= 0.3, "Oracle base level too high"
        
        metacognition = oracle_config.get('metacognition', {})
        assert metacognition.get('trade_decision_confidence_threshold', 0.0) >= 0.8, "Confidence threshold too low"
        logger.info("✅ Oracle configuration is ultra-conservative")
        
        # Validate unification weights
        unification = oracle_config.get('unification_weights', {})
        assert unification.get('decision_threshold', 0.0) >= 0.7, "Decision threshold too low"
        assert unification.get('metacognition', 0.0) >= 0.5, "Metacognition weight too low"
        logger.info("✅ Unification weights are ultra-conservative")
        
        # Test 4: Data Collection and Filtering
        logger.info("📊 Test 4: Data Collection and Ultra-Conservative Filtering...")
        
        # Test data collection
        if hasattr(pilot.enhanced_data_collector, 'collect_enhanced_market_data'):
            try:
                enhanced_data = await pilot.enhanced_data_collector.collect_enhanced_market_data()
                logger.info(f"📈 Collected {len(enhanced_data)} enhanced data points")
                
                # Test ultra-conservative filtering
                filtered_data = pilot._apply_ultra_conservative_data_filter(enhanced_data)
                filter_ratio = len(filtered_data) / len(enhanced_data) if enhanced_data else 0
                logger.info(f"🛡️ Ultra-conservative filter ratio: {filter_ratio:.2%}")
                
                if filter_ratio > 0.5:
                    logger.warning("⚠️ Filter may not be conservative enough")
                else:
                    logger.info("✅ Ultra-conservative data filtering working correctly")
                    
            except Exception as e:
                logger.info(f"ℹ️ Real data collection not available: {e}")
        else:
            logger.info("ℹ️ Using mock data collector")
        
        # Test 5: Quantum Decision Generation
        logger.info("🔮 Test 5: Quantum Decision Generation...")
        
        try:
            # Run a complete trading cycle to test decision generation
            cycle_success = await pilot.run_qualia_trading_cycle()
            
            if cycle_success:
                logger.info("✅ Quantum decision cycle completed successfully")
                
                # Check if ultra-conservative overrides were applied
                # This would be logged during the cycle
                logger.info("🛡️ Check logs above for ultra-conservative override indicators")
            else:
                logger.warning("⚠️ Quantum decision cycle completed with warnings")
                
        except Exception as e:
            logger.error(f"❌ Quantum decision generation failed: {e}")
            return False
        
        # Test 6: Default Analysis Validation
        logger.info("🛡️ Test 6: Ultra-Conservative Default Analysis...")
        
        default_analysis = pilot._get_ultra_conservative_default_analysis()
        
        # Validate default analysis is ultra-conservative
        assert default_analysis.get('consciousness_level', 1.0) <= 0.2, "Default consciousness too high"
        assert default_analysis.get('quantum_coherence', 1.0) <= 0.3, "Default coherence too high"
        assert default_analysis.get('decision_confidence', 1.0) <= 0.1, "Default confidence too high"
        assert default_analysis.get('action_recommended') == 'HOLD', "Default action should be HOLD"
        
        risk_assessment = default_analysis.get('risk_assessment', {})
        assert risk_assessment.get('max_position_size', 1.0) <= 0.01, "Default position size too high"
        assert risk_assessment.get('risk_per_trade', 1.0) <= 0.005, "Default risk per trade too high"
        
        logger.info("✅ Ultra-conservative default analysis validated")
        
        # Test Summary
        logger.info("📊 Quantum Decision Engine Test Summary:")
        logger.info("   ✅ Configuration: Ultra-conservative thresholds validated")
        logger.info("   ✅ Components: Real/mock components initialized correctly")
        logger.info("   ✅ Filtering: Ultra-conservative data filtering active")
        logger.info("   ✅ Decisions: Quantum decision generation working")
        logger.info("   ✅ Defaults: Ultra-conservative fallbacks validated")
        
        logger.info("🎉 Quantum Decision Engine Test Completed Successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Quantum Decision Engine test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test execution"""
    success = await test_quantum_decision_engine()
    if success:
        logger.info("🎯 All quantum decision engine tests passed!")
        return 0
    else:
        logger.error("💥 Some quantum decision engine tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
