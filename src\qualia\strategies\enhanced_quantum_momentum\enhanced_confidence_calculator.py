"""
Enhanced Confidence Calculator for Enhanced Quantum Momentum Strategy.

This module provides advanced confidence calculation that considers multiple
market factors, quantum metrics, and dynamic market conditions.
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from ...utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ConfidenceFactors:
    """Factors that contribute to signal confidence."""
    base_score: float
    trend_alignment: float
    volatility_factor: float
    quantum_coherence: float
    volume_confirmation: float
    momentum_strength: float
    market_regime_factor: float
    risk_adjusted_factor: float


@dataclass
class ConfidenceMetrics:
    """Detailed confidence metrics for analysis."""
    final_confidence: float
    factors: ConfidenceFactors
    regime: str
    timestamp: datetime
    explanation: str


class EnhancedConfidenceCalculator:
    """
    Advanced confidence calculator for Enhanced Quantum Momentum Strategy.
    
    Features:
    - Multi-factor confidence calculation
    - Market regime awareness
    - Quantum metrics integration
    - Risk-adjusted confidence scoring
    - Dynamic threshold adjustment
    """
    
    def __init__(self):
        self.confidence_history: List[ConfidenceMetrics] = []
        self.calibration_factor = 1.0
        self.min_confidence = 0.0
        self.max_confidence = 1.0
        
        # Confidence factor weights
        self.factor_weights = {
            "base_score": 0.25,
            "trend_alignment": 0.15,
            "volatility_factor": 0.12,
            "quantum_coherence": 0.18,
            "volume_confirmation": 0.10,
            "momentum_strength": 0.12,
            "market_regime_factor": 0.08
        }
        
        logger.info("[CONFIDENCE] Enhanced Confidence Calculator initialized")
    
    def calculate_confidence(self, 
                           combined_score: float,
                           market_regime: str,
                           analysis_result: Dict[str, Any],
                           quantum_metrics: List[Dict[str, Any]],
                           price_data: pd.DataFrame) -> ConfidenceMetrics:
        """
        Calculate enhanced confidence score with detailed factor analysis.
        
        Args:
            combined_score: Base combined score from strategy
            market_regime: Current market regime
            analysis_result: Technical analysis results
            quantum_metrics: Quantum computation metrics
            price_data: Recent price data for additional analysis
            
        Returns:
            ConfidenceMetrics with detailed breakdown
        """
        # Calculate individual confidence factors
        factors = self._calculate_confidence_factors(
            combined_score, market_regime, analysis_result, quantum_metrics, price_data
        )
        
        # Calculate weighted confidence
        weighted_confidence = (
            factors.base_score * self.factor_weights["base_score"] +
            factors.trend_alignment * self.factor_weights["trend_alignment"] +
            factors.volatility_factor * self.factor_weights["volatility_factor"] +
            factors.quantum_coherence * self.factor_weights["quantum_coherence"] +
            factors.volume_confirmation * self.factor_weights["volume_confirmation"] +
            factors.momentum_strength * self.factor_weights["momentum_strength"] +
            factors.market_regime_factor * self.factor_weights["market_regime_factor"]
        )
        
        # Apply risk adjustment
        risk_adjusted_confidence = weighted_confidence * factors.risk_adjusted_factor
        
        # Apply calibration factor
        final_confidence = risk_adjusted_confidence * self.calibration_factor
        
        # Ensure confidence is in valid range
        final_confidence = max(self.min_confidence, min(self.max_confidence, final_confidence))
        
        # Generate explanation
        explanation = self._generate_confidence_explanation(factors, market_regime, final_confidence)
        
        # Create confidence metrics
        metrics = ConfidenceMetrics(
            final_confidence=final_confidence,
            factors=factors,
            regime=market_regime,
            timestamp=datetime.now(),
            explanation=explanation
        )
        
        # Store in history
        self.confidence_history.append(metrics)
        if len(self.confidence_history) > 100:
            self.confidence_history = self.confidence_history[-100:]
        
        logger.debug(f"[CONFIDENCE] Calculated confidence: {final_confidence:.4f} for regime: {market_regime}")
        
        return metrics
    
    def _calculate_confidence_factors(self, 
                                    combined_score: float,
                                    market_regime: str,
                                    analysis_result: Dict[str, Any],
                                    quantum_metrics: List[Dict[str, Any]],
                                    price_data: pd.DataFrame) -> ConfidenceFactors:
        """Calculate individual confidence factors."""
        
        # 1. Base Score Factor
        base_score = min(1.0, abs(combined_score) * 2.0)  # Scale to 0-1
        
        # 2. Trend Alignment Factor
        trend_alignment = self._calculate_trend_alignment(analysis_result, price_data)
        
        # 3. Volatility Factor
        volatility_factor = self._calculate_volatility_factor(analysis_result)
        
        # 4. Quantum Coherence Factor
        quantum_coherence = self._calculate_quantum_coherence_factor(quantum_metrics)
        
        # 5. Volume Confirmation Factor
        volume_confirmation = self._calculate_volume_confirmation(analysis_result, price_data)
        
        # 6. Momentum Strength Factor
        momentum_strength = self._calculate_momentum_strength(analysis_result, price_data)
        
        # 7. Market Regime Factor
        market_regime_factor = self._calculate_market_regime_factor(market_regime, analysis_result)
        
        # 8. Risk Adjusted Factor
        risk_adjusted_factor = self._calculate_risk_adjustment(analysis_result, market_regime)
        
        return ConfidenceFactors(
            base_score=base_score,
            trend_alignment=trend_alignment,
            volatility_factor=volatility_factor,
            quantum_coherence=quantum_coherence,
            volume_confirmation=volume_confirmation,
            momentum_strength=momentum_strength,
            market_regime_factor=market_regime_factor,
            risk_adjusted_factor=risk_adjusted_factor
        )
    
    def _calculate_trend_alignment(self, analysis_result: Dict[str, Any], price_data: pd.DataFrame) -> float:
        """Calculate trend alignment factor."""
        try:
            # Get EMAs
            ema_short = analysis_result.get("ema_short", pd.Series())
            ema_long = analysis_result.get("ema_long", pd.Series())
            trend_ema = analysis_result.get("trend_ema", pd.Series())
            
            if len(ema_short) < 2 or len(ema_long) < 2 or len(trend_ema) < 2:
                return 0.5  # Neutral if insufficient data
            
            # Check EMA alignment
            current_short = ema_short.iloc[-1]
            current_long = ema_long.iloc[-1]
            current_trend = trend_ema.iloc[-1]
            
            # Perfect alignment: short > long > trend (uptrend) or short < long < trend (downtrend)
            if current_short > current_long > current_trend:
                alignment = 1.0  # Perfect uptrend alignment
            elif current_short < current_long < current_trend:
                alignment = 1.0  # Perfect downtrend alignment
            else:
                # Partial alignment
                short_long_aligned = (current_short > current_long) == (current_long > current_trend)
                alignment = 0.7 if short_long_aligned else 0.3
            
            return alignment
            
        except Exception as e:
            logger.warning(f"[CONFIDENCE] Error calculating trend alignment: {e}")
            return 0.5
    
    def _calculate_volatility_factor(self, analysis_result: Dict[str, Any]) -> float:
        """Calculate volatility factor (optimal volatility gets higher score)."""
        try:
            vol_short = analysis_result.get("vol_short", 0.02)
            
            # Optimal volatility range: 0.01 - 0.04
            if 0.01 <= vol_short <= 0.04:
                # Peak at 0.025
                optimal_distance = abs(vol_short - 0.025) / 0.015
                factor = 1.0 - optimal_distance
            elif vol_short < 0.01:
                # Too low volatility
                factor = vol_short / 0.01 * 0.6
            else:
                # Too high volatility
                factor = max(0.2, 1.0 - (vol_short - 0.04) / 0.06)
            
            return max(0.0, min(1.0, factor))
            
        except Exception as e:
            logger.warning(f"[CONFIDENCE] Error calculating volatility factor: {e}")
            return 0.5
    
    def _calculate_quantum_coherence_factor(self, quantum_metrics: List[Dict[str, Any]]) -> float:
        """Calculate quantum coherence factor."""
        try:
            if not quantum_metrics:
                return 0.5
            
            recent_metrics = quantum_metrics[:3]  # Last 3 measurements
            
            # Extract coherence values
            coherences = [m.get("coherence", 0.0) for m in recent_metrics]
            entropies = [m.get("entropy", 0.5) for m in recent_metrics]
            
            if not coherences:
                return 0.5
            
            # Average coherence (higher is better)
            avg_coherence = np.mean(coherences)
            
            # Entropy stability (lower variance is better)
            entropy_stability = 1.0 - min(1.0, np.std(entropies) * 2.0) if len(entropies) > 1 else 0.5
            
            # Combine factors
            factor = (avg_coherence * 0.7 + entropy_stability * 0.3)
            
            return max(0.0, min(1.0, factor))
            
        except Exception as e:
            logger.warning(f"[CONFIDENCE] Error calculating quantum coherence: {e}")
            return 0.5
    
    def _calculate_volume_confirmation(self, analysis_result: Dict[str, Any], price_data: pd.DataFrame) -> float:
        """Calculate volume confirmation factor."""
        try:
            if len(price_data) < 20:
                return 0.5
            
            # Get recent volume data
            recent_volume = price_data['volume'].iloc[-5:].mean()
            avg_volume = price_data['volume'].iloc[-20:].mean()
            
            if avg_volume == 0:
                return 0.5
            
            volume_ratio = recent_volume / avg_volume
            
            # Optimal volume ratio: 1.2 - 2.0 (above average but not excessive)
            if 1.2 <= volume_ratio <= 2.0:
                factor = 1.0
            elif volume_ratio > 2.0:
                # Too high volume (might be manipulation)
                factor = max(0.6, 1.0 - (volume_ratio - 2.0) / 3.0)
            else:
                # Below average volume
                factor = volume_ratio / 1.2 * 0.8
            
            return max(0.0, min(1.0, factor))
            
        except Exception as e:
            logger.warning(f"[CONFIDENCE] Error calculating volume confirmation: {e}")
            return 0.5
    
    def _calculate_momentum_strength(self, analysis_result: Dict[str, Any], price_data: pd.DataFrame) -> float:
        """Calculate momentum strength factor."""
        try:
            # Get MACD data
            macd = analysis_result.get("macd", pd.Series())
            macd_signal = analysis_result.get("macd_signal", pd.Series())
            macd_histogram = analysis_result.get("macd_histogram", pd.Series())
            
            if len(macd_histogram) < 3:
                return 0.5
            
            # MACD momentum
            current_hist = macd_histogram.iloc[-1]
            prev_hist = macd_histogram.iloc[-2]
            hist_momentum = abs(current_hist - prev_hist)
            
            # Price momentum
            if len(price_data) >= 5:
                recent_returns = price_data['close'].pct_change().iloc[-5:]
                price_momentum = abs(recent_returns.mean())
            else:
                price_momentum = 0.01
            
            # Combine momentum factors
            momentum_factor = min(1.0, (hist_momentum * 20 + price_momentum * 50) / 2)
            
            return max(0.0, min(1.0, momentum_factor))
            
        except Exception as e:
            logger.warning(f"[CONFIDENCE] Error calculating momentum strength: {e}")
            return 0.5
    
    def _calculate_market_regime_factor(self, market_regime: str, analysis_result: Dict[str, Any]) -> float:
        """Calculate market regime factor."""
        regime_factors = {
            "uptrend": 1.0,
            "downtrend": 1.0,
            "range": 0.8,
            "neutral": 0.7,
            "volatile": 0.6
        }
        
        base_factor = regime_factors.get(market_regime, 0.7)
        
        # Adjust based on directional strength
        directional_strength = analysis_result.get("directional_strength", 0.5)
        
        if directional_strength > 0.7:
            base_factor *= 1.1
        elif directional_strength < 0.3:
            base_factor *= 0.9
        
        return max(0.0, min(1.0, base_factor))
    
    def _calculate_risk_adjustment(self, analysis_result: Dict[str, Any], market_regime: str) -> float:
        """Calculate risk adjustment factor."""
        try:
            vol_short = analysis_result.get("vol_short", 0.02)
            
            # Base risk adjustment
            if vol_short > 0.05:
                risk_factor = 0.8  # High volatility = higher risk
            elif vol_short < 0.005:
                risk_factor = 0.9  # Low volatility = some risk (low liquidity)
            else:
                risk_factor = 1.0  # Normal volatility
            
            # Regime-specific adjustments
            if market_regime in ["volatile", "neutral"]:
                risk_factor *= 0.95
            
            return max(0.5, min(1.0, risk_factor))
            
        except Exception as e:
            logger.warning(f"[CONFIDENCE] Error calculating risk adjustment: {e}")
            return 1.0
    
    def _generate_confidence_explanation(self, factors: ConfidenceFactors, 
                                       market_regime: str, final_confidence: float) -> str:
        """Generate human-readable explanation of confidence calculation."""
        explanations = []
        
        if factors.base_score > 0.7:
            explanations.append("Strong base signal")
        elif factors.base_score < 0.3:
            explanations.append("Weak base signal")
        
        if factors.trend_alignment > 0.8:
            explanations.append("Excellent trend alignment")
        elif factors.trend_alignment < 0.4:
            explanations.append("Poor trend alignment")
        
        if factors.quantum_coherence > 0.7:
            explanations.append("High quantum coherence")
        elif factors.quantum_coherence < 0.3:
            explanations.append("Low quantum coherence")
        
        if factors.volume_confirmation > 0.8:
            explanations.append("Strong volume confirmation")
        
        if factors.momentum_strength > 0.7:
            explanations.append("Strong momentum")
        
        explanation = f"Regime: {market_regime}, Confidence: {final_confidence:.3f}"
        if explanations:
            explanation += f" ({', '.join(explanations)})"
        
        return explanation
    
    def get_confidence_statistics(self) -> Dict[str, Any]:
        """Get confidence calculation statistics."""
        if not self.confidence_history:
            return {"message": "No confidence history available"}
        
        recent_confidences = [m.final_confidence for m in self.confidence_history[-20:]]
        
        return {
            "recent_avg_confidence": np.mean(recent_confidences),
            "recent_max_confidence": np.max(recent_confidences),
            "recent_min_confidence": np.min(recent_confidences),
            "confidence_std": np.std(recent_confidences),
            "total_calculations": len(self.confidence_history),
            "calibration_factor": self.calibration_factor
        }
