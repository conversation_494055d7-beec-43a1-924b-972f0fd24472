#!/usr/bin/env python3
"""
Teste do sistema de produção após correções de import
"""

import asyncio
import sys
import os
from datetime import datetime

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


async def test_production_system():
    """Testa o sistema exatamente como em produção"""
    
    print("🚀 TESTE DO SISTEMA DE PRODUÇÃO")
    print("=" * 60)
    print(f"Iniciado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Importar o sistema de trading
        from qualia.market.kucoin_integration import KucoinIntegration
        print("✅ KucoinIntegration importado")
        
        # Criar a integração
        kucoin = KucoinIntegration(
            api_key=None,
            api_secret=None,
            password=None,
            use_websocket=False,
            ticker_timeout=15.0,
            ohlcv_timeout=30.0
        )
        print("✅ Integração criada")
        
        # Inicializar conexão
        print("\n🔄 Inicializando conexão...")
        start_time = datetime.now()
        await kucoin.initialize_connection()
        init_duration = (datetime.now() - start_time).total_seconds()
        print(f"✅ Conexão inicializada em {init_duration:.2f}s")
        
        # Teste 1: Fetch Ticker
        print("\n📊 TESTE 1: FETCH TICKER")
        print("-" * 40)
        
        for attempt in range(3):
            try:
                start_time = datetime.now()
                ticker = await kucoin.fetch_ticker("BTC/USDT")
                duration = (datetime.now() - start_time).total_seconds()
                
                if ticker and 'last' in ticker:
                    print(f"✅ Tentativa {attempt+1}: Sucesso em {duration:.2f}s")
                    print(f"   Preço: ${ticker['last']:,.2f}")
                    print(f"   Bid: ${ticker.get('bid', 'N/A')}")
                    print(f"   Ask: ${ticker.get('ask', 'N/A')}")
                    break
                else:
                    print(f"❌ Tentativa {attempt+1}: Ticker vazio em {duration:.2f}s")
                    
            except asyncio.TimeoutError:
                duration = (datetime.now() - start_time).total_seconds()
                print(f"⏱️ Tentativa {attempt+1}: TIMEOUT após {duration:.2f}s")
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                print(f"❌ Tentativa {attempt+1}: Erro após {duration:.2f}s - {e}")
            
            await asyncio.sleep(1)  # Pausa entre tentativas
        
        # Teste 2: Fetch OHLCV
        print("\n📈 TESTE 2: FETCH OHLCV")
        print("-" * 40)
        
        try:
            start_time = datetime.now()
            df = await kucoin.fetch_ohlcv("BTC/USDT", "5m", limit=10)
            duration = (datetime.now() - start_time).total_seconds()
            
            if df is not None and not df.empty:
                print(f"✅ OHLCV obtido em {duration:.2f}s")
                print(f"   Candles: {len(df)}")
                print(f"   Último preço: ${df.iloc[-1]['close']:,.2f}")
                print(f"   Timeframe: {df.index[-1] - df.index[-2]}")
            else:
                print(f"❌ OHLCV vazio em {duration:.2f}s")
                
        except asyncio.TimeoutError:
            duration = (datetime.now() - start_time).total_seconds()
            print(f"⏱️ OHLCV TIMEOUT após {duration:.2f}s")
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            print(f"❌ OHLCV erro após {duration:.2f}s - {e}")
        
        # Teste 3: Múltiplas requisições rápidas
        print("\n⚡ TESTE 3: MÚLTIPLAS REQUISIÇÕES")
        print("-" * 40)
        
        success_count = 0
        total_time = 0
        
        for i in range(5):
            try:
                start_time = datetime.now()
                ticker = await kucoin.fetch_ticker("BTC/USDT")
                duration = (datetime.now() - start_time).total_seconds()
                total_time += duration
                
                if ticker and 'last' in ticker:
                    success_count += 1
                    print(f"✅ Requisição {i+1}: {duration:.2f}s - ${ticker['last']:,.2f}")
                else:
                    print(f"❌ Requisição {i+1}: Vazio em {duration:.2f}s")
                
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                total_time += duration
                print(f"❌ Requisição {i+1}: Erro em {duration:.2f}s - {e}")
            
            await asyncio.sleep(0.5)  # Rate limiting
        
        # Resultados finais
        print("\n📊 RESULTADOS MÚLTIPLAS REQUISIÇÕES:")
        print(f"   Sucessos: {success_count}/5 ({success_count/5*100:.1f}%)")
        print(f"   Tempo médio: {total_time/5:.2f}s")
        
        # Fechar conexão
        await kucoin.close()
        print("\n✅ Conexão fechada")
        
        # Resultado final
        print("\n🏁 RESULTADO FINAL")
        print("=" * 60)
        
        if success_count >= 4:  # 80% de sucesso
            print("🎉 SISTEMA FUNCIONANDO PERFEITAMENTE!")
            print("💡 Todos os timeouts foram corrigidos!")
            return True
        else:
            print("⚠️ Sistema com problemas intermitentes")
            print("💡 Pode ser problema de rede ou configuração")
            return False
        
    except Exception as e:
        print(f"❌ Erro geral no teste: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    result = asyncio.run(test_production_system())
    
    if result:
        print("\n🚀 SISTEMA PRONTO PARA PRODUÇÃO!")
    else:
        print("\n🔧 SISTEMA PRECISA DE MAIS AJUSTES!") 
