import asyncio
import c<PERSON>rofile
import io
from pstats import Stats
from typing import Any, Dict

import numpy as np
from datetime import datetime, timezone

from qualia.memory.system import MemorySystem


def _generate_experiences(n: int) -> list[Dict[str, Any]]:
    experiences = []
    for i in range(n):
        experiences.append(
            {
                "timestamp": datetime.now(timezone.utc),
                "market_data": {"prices": np.random.rand(20).tolist()},
                "signal": {"action": "buy"},
                "trade_result": {
                    "success": bool(i % 2),
                    "profit_loss": float(np.random.randn()),
                },
                "metadata": {},
            }
        )
    return experiences


def run_profile(num_items: int = 1000) -> str:
    ms = MemorySystem({"holo_max_items": num_items})

    async def _run():
        await ms.store_experiences(_generate_experiences(num_items))
        ms.query_market_patterns(np.random.rand(3), top_n=5)

    profiler = cProfile.Profile()
    profiler.enable()
    asyncio.run(_run())
    profiler.disable()
    output = io.StringIO()
    Stats(profiler, stream=output).sort_stats("cumtime").print_stats(10)
    return output.getvalue()


if __name__ == "__main__":
    print(run_profile())
