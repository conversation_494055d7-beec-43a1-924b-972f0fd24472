#!/usr/bin/env python3
import ccxt.async_support as ccxt
import asyncio
import time

async def test_ohlcv_direct():
    """Teste direto de OHLCV com ccxt"""
    print("🔍 TESTE DIRETO OHLCV KUCOIN")
    
    exchange = ccxt.kucoin({'timeout': 10000})
    
    try:
        print("📊 Buscando 10 candles BTC/USDT 5m...")
        start = time.time()
        ohlcv = await exchange.fetch_ohlcv('BTC/USDT', '5m', limit=10)
        duration = time.time() - start
        
        print(f"✅ Sucesso: {len(ohlcv)} candles em {duration:.2f}s")
        
    except Exception as e:
        duration = time.time() - start
        print(f"❌ Erro após {duration:.2f}s: {e}")
    finally:
        await exchange.close()

if __name__ == "__main__":
    asyncio.run(test_ohlcv_direct()) 