#!/usr/bin/env python3
"""
Test script for enhanced health monitoring system.
"""

import sys
import asyncio
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class MockQASTCore:
    """Mock QAST Core for testing"""
    
    def __init__(self, healthy: bool = True):
        self.healthy = healthy
        self.quantum_universe = "mock_universe" if healthy else None
        self.current_qualia_state = {"mock": "state"} if healthy else None
        self.state_history = [1, 2, 3, 4, 5] if healthy else []
        self.exchanges = {"kucoin": "mock"} if healthy else {}


class MockDataCollector:
    """Mock Data Collector for testing"""
    
    def __init__(self, healthy: bool = True):
        self.healthy = healthy
        self.active_connections = 2 if healthy else 0
        self.last_update_time = time.time() if healthy else time.time() - 1000
    
    def get_data_count(self):
        return 50 if self.healthy else 5


async def test_basic_health_checks():
    """Test basic health check functionality"""
    print("🧪 Testing Basic Health Checks")
    print("=" * 50)
    
    try:
        from src.qualia.monitoring.enhanced_health_system import (
            EnhancedHealthSystem, ComponentType, HealthStatus
        )
        
        # Create health system
        health_system = EnhancedHealthSystem()
        
        # Register mock components
        healthy_qast = MockQASTCore(healthy=True)
        health_system.register_component("qast_core", healthy_qast, ComponentType.CORE)
        
        healthy_collector = MockDataCollector(healthy=True)
        health_system.register_component("data_collector", healthy_collector, ComponentType.DATA)
        
        # Perform health check
        system_health = await health_system.check_system_health()
        
        # Verify results
        if system_health.overall_status == HealthStatus.HEALTHY:
            print("✅ Healthy components correctly identified")
        else:
            print(f"❌ Expected healthy status, got: {system_health.overall_status}")
            return False
        
        if len(system_health.components) >= 3:  # qast_core, data_collector, system_resources
            print(f"✅ All components checked ({len(system_health.components)} components)")
        else:
            print(f"❌ Expected at least 3 components, got: {len(system_health.components)}")
            return False
        
        if system_health.health_score > 0.8:
            print(f"✅ Good health score: {system_health.health_score:.2f}")
        else:
            print(f"❌ Low health score: {system_health.health_score:.2f}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in basic health checks: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_unhealthy_components():
    """Test detection of unhealthy components"""
    print("\n🧪 Testing Unhealthy Component Detection")
    print("=" * 50)
    
    try:
        from src.qualia.monitoring.enhanced_health_system import (
            EnhancedHealthSystem, ComponentType, HealthStatus
        )
        
        # Create health system
        health_system = EnhancedHealthSystem()
        
        # Register unhealthy components
        unhealthy_qast = MockQASTCore(healthy=False)
        health_system.register_component("qast_core", unhealthy_qast, ComponentType.CORE)
        
        unhealthy_collector = MockDataCollector(healthy=False)
        health_system.register_component("data_collector", unhealthy_collector, ComponentType.DATA)
        
        # Perform health check
        system_health = await health_system.check_system_health()
        
        # Verify results
        if system_health.overall_status in [HealthStatus.WARNING, HealthStatus.CRITICAL]:
            print("✅ Unhealthy components correctly detected")
        else:
            print(f"❌ Expected warning/critical status, got: {system_health.overall_status}")
            return False
        
        if len(system_health.critical_issues) > 0 or len(system_health.warnings) > 0:
            print(f"✅ Issues identified: {len(system_health.critical_issues)} critical, {len(system_health.warnings)} warnings")
        else:
            print("❌ No issues identified for unhealthy components")
            return False
        
        if system_health.health_score < 0.7:
            print(f"✅ Low health score for unhealthy system: {system_health.health_score:.2f}")
        else:
            print(f"❌ Health score too high for unhealthy system: {system_health.health_score:.2f}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in unhealthy component test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_system_resources_monitoring():
    """Test system resources monitoring"""
    print("\n🧪 Testing System Resources Monitoring")
    print("=" * 50)
    
    try:
        from src.qualia.monitoring.enhanced_health_system import (
            SystemResourcesHealthChecker, ComponentType
        )
        
        # Create system resources checker
        checker = SystemResourcesHealthChecker("system_resources", ComponentType.INFRASTRUCTURE)
        
        # Perform health check
        health = await checker.check_health()
        
        # Verify results
        expected_metrics = ['cpu_usage', 'memory_usage', 'disk_usage', 'process_count']
        
        for metric_name in expected_metrics:
            if metric_name in health.metrics:
                metric = health.metrics[metric_name]
                print(f"✅ {metric.name}: {metric.value}{metric.unit} ({metric.status.value})")
            else:
                print(f"❌ Missing metric: {metric_name}")
                return False
        
        if health.status in [health.status.HEALTHY, health.status.WARNING]:
            print("✅ System resources check completed successfully")
        else:
            print(f"❌ Unexpected system resources status: {health.status}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in system resources test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_health_history():
    """Test health history tracking"""
    print("\n🧪 Testing Health History Tracking")
    print("=" * 50)
    
    try:
        from src.qualia.monitoring.enhanced_health_system import (
            EnhancedHealthSystem, ComponentType
        )
        
        # Create health system
        health_system = EnhancedHealthSystem()
        
        # Register a component
        mock_component = MockQASTCore(healthy=True)
        health_system.register_component("test_component", mock_component, ComponentType.CORE)
        
        # Perform multiple health checks
        for i in range(5):
            await health_system.check_system_health()
            await asyncio.sleep(0.1)  # Small delay
        
        # Check history
        history = health_system.get_health_history()
        
        if len(history) == 5:
            print("✅ Health history tracking working correctly")
        else:
            print(f"❌ Expected 5 history entries, got: {len(history)}")
            return False
        
        # Check that timestamps are different
        timestamps = [h.timestamp for h in history]
        if len(set(timestamps)) == len(timestamps):
            print("✅ Health check timestamps are unique")
        else:
            print("❌ Health check timestamps are not unique")
            return False
        
        # Test history limit
        latest_health = health_system.get_health_status()
        if latest_health and latest_health.timestamp == timestamps[-1]:
            print("✅ Latest health status correctly retrieved")
        else:
            print("❌ Latest health status not correctly retrieved")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in health history test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_alert_callbacks():
    """Test alert callback functionality"""
    print("\n🧪 Testing Alert Callbacks")
    print("=" * 50)
    
    try:
        from src.qualia.monitoring.enhanced_health_system import (
            EnhancedHealthSystem, ComponentType
        )
        
        # Create health system
        health_system = EnhancedHealthSystem()
        
        # Track callback calls
        callback_calls = []
        
        def test_callback(system_health):
            callback_calls.append(system_health)
        
        # Add callback
        health_system.add_alert_callback(test_callback)
        
        # Register component and perform health check
        mock_component = MockQASTCore(healthy=True)
        health_system.register_component("test_component", mock_component, ComponentType.CORE)
        
        await health_system.check_system_health()
        
        # Verify callback was called
        if len(callback_calls) == 1:
            print("✅ Alert callback called correctly")
        else:
            print(f"❌ Expected 1 callback call, got: {len(callback_calls)}")
            return False
        
        # Verify callback received correct data
        callback_health = callback_calls[0]
        if hasattr(callback_health, 'overall_status') and hasattr(callback_health, 'components'):
            print("✅ Callback received correct health data")
        else:
            print("❌ Callback received incorrect health data")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in alert callbacks test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_health_dashboard():
    """Test health dashboard functionality"""
    print("\n🧪 Testing Health Dashboard")
    print("=" * 50)
    
    try:
        from src.qualia.monitoring.enhanced_health_system import (
            EnhancedHealthSystem, HealthDashboard, ComponentType
        )
        
        # Create health system and dashboard
        health_system = EnhancedHealthSystem()
        dashboard = HealthDashboard(health_system, port=8081)
        
        # Register components
        mock_qast = MockQASTCore(healthy=True)
        health_system.register_component("qast_core", mock_qast, ComponentType.CORE)
        
        mock_collector = MockDataCollector(healthy=True)
        health_system.register_component("data_collector", mock_collector, ComponentType.DATA)
        
        # Perform health check
        await health_system.check_system_health()
        
        # Generate dashboard HTML
        html = dashboard.generate_html_dashboard()
        
        # Verify HTML content
        if "QUALIA Health Dashboard" in html:
            print("✅ Dashboard HTML generated successfully")
        else:
            print("❌ Dashboard HTML missing title")
            return False
        
        if "qast_core" in html and "data_collector" in html:
            print("✅ Dashboard includes component information")
        else:
            print("❌ Dashboard missing component information")
            return False
        
        if "Health Score" in html:
            print("✅ Dashboard includes health metrics")
        else:
            print("❌ Dashboard missing health metrics")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in dashboard test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all tests"""
    print("🚀 Starting Enhanced Health System Tests")
    print("=" * 80)
    
    tests = [
        test_basic_health_checks,
        test_unhealthy_components,
        test_system_resources_monitoring,
        test_health_history,
        test_alert_callbacks,
        test_health_dashboard
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if await test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ FAIL: {test.__name__} raised exception: {e}")
            failed += 1
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 TEST SUMMARY")
    print("=" * 80)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 All health system tests passed!")
        return True
    else:
        print(f"\n💥 {failed} test(s) failed!")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
