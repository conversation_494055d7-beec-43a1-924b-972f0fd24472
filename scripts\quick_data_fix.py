#!/usr/bin/env python3
"""
Quick Data Fix for QUALIA Signal Generation
"""

import asyncio
import json
import os
from datetime import datetime
from pathlib import Path
import ccxt.async_support as ccxt

async def main():
    print("🚀 QUALIA Quick Data Fix")
    
    # Configurar exchange
    exchange = ccxt.kucoin({
        'apiKey': os.getenv('KUCOIN_API_KEY'),
        'secret': os.getenv('KUCOIN_SECRET_KEY'),
        'password': os.getenv('KUCOIN_PASSPHRASE'),
        'sandbox': False,
        'enableRateLimit': True,
        'timeout': 30000,
    })
    
    symbols = ["BTC/USDT", "ETH/USDT", "ADA/USDT", "SOL/USDT"]
    timeframes = ["5m", "15m", "1h"]
    cache_dir = Path("data/cache")
    cache_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        await exchange.load_markets()
        print(f"✅ Mercados carregados")
        
        for symbol in symbols:
            for timeframe in timeframes:
                try:
                    print(f"📊 Coletando {symbol}@{timeframe}")
                    
                    # Usar limite máximo da <PERSON> (1500 candles)
                    limit = 1500  # KuCoin permite até 1500 candles por chamada
                    ohlcv = await exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
                    
                    if ohlcv and len(ohlcv) > 10:
                        # Salvar no formato esperado
                        symbol_clean = symbol.replace("/", "")
                        json_path = cache_dir / f"{symbol_clean}_{timeframe}.json"
                        
                        cache_data = {
                            "symbol": symbol,
                            "timeframe": timeframe,
                            "data": ohlcv,
                            "last_update": datetime.now().isoformat(),
                            "count": len(ohlcv)
                        }
                        
                        with open(json_path, 'w') as f:
                            json.dump(cache_data, f)
                            
                        print(f"✅ {symbol}@{timeframe}: {len(ohlcv)} candles salvos")
                    else:
                        print(f"⚠️ {symbol}@{timeframe}: dados insuficientes")
                        
                    await asyncio.sleep(0.5)  # Rate limiting
                    
                except Exception as e:
                    print(f"❌ Erro {symbol}@{timeframe}: {e}")
                    
    except Exception as e:
        print(f"❌ Erro geral: {e}")
    finally:
        await exchange.close()
    
    # Criar configuração permissiva
    config_content = """
signal_approval:
  min_confidence: 0.01
  min_volume_threshold: 0.01
  
holographic_trading:
  min_confidence: 0.05
  volume_threshold: 0.1
  aggressive_mode: true
  risk_override: true
  
data_collection:
  min_data_completeness: 0.3
  allow_partial_data: true
"""
    
    config_path = Path("config/permissive_config.yaml")
    with open(config_path, 'w') as f:
        f.write(config_content)
    
    print("🎉 Correção concluída!")
    print("📋 Execute: python scripts/start_real_trading.py --config config/permissive_config.yaml")

if __name__ == "__main__":
    asyncio.run(main())
