# Novas Features de Encoding

As interfaces de encoding do QUALIA agora aceitam:

- **order_book_imbalance**: valor normalizado em [-1, 1], mapeado em rotações `RY`.
- **funding_rate**: taxa de funding diária. O encoder aplica `RY` com fator de escala 10.
- **ts_embedding**: vetor numérico representando embedding de séries temporais. O vetor é codificado via preparação de estado.
- **open_interest**: valor de interesse em aberto normalizado em [0, 1], mapeado em uma rotação `RY` dedicada.
- **rsi_phase**: valor do RSI no intervalo [0, 100], convertido em fase com `RZ`.
- **volume_ratio_amplitude**: ratio de volume normalizado convertido em amplitudes [√(1-r), √r].

Essas features podem ser passadas ao `EnhancedQuantumClassicalInterface.encode_market_data` e também a `QuantumEncodingInterface` através de encoders dedicados.

## Configuração dos Encoders

O arquivo `config/encoders.yaml` centraliza parâmetros e flags de cada encoder. Defina a variável
de ambiente `QUALIA_ENCODERS_CONFIG` para apontar para outro caminho, caso necessário.

Exemplo de configuração padrão:

```yaml
RSIPhaseEncoder:
  enabled: true
  emit_events: true
VolumeRatioAmplitudeEncoder:
  enabled: true
  emit_events: true
```

O campo `enabled` controla o registro do encoder via `encoder_registry`. Já `emit_events`
determina se um evento `encoder.processed` será publicado ao final de cada chamada
`encode`.

**Importante**: os encoders ``RSIPhaseEncoder`` e ``VolumeRatioAmplitudeEncoder``
devem estar disponíveis em ``qualia.core.encoders``. Caso esses módulos não
estejam instalados, o plugin ``src.ai.encoders_plugin`` emitirá ``ImportError``
e não poderá ser utilizado.
