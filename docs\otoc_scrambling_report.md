# Relatório: Sweep de OTOC x Scrambling Depth

Este relatório resume a interpretação dos resultados obtidos a partir do
script `generate_otoc_scrambling_data.py`, que por padrão grava o dataset em
`docs/data/otoc_scrambling_data.csv`. É possível especificar outro caminho com
o argumento `--output`. O objetivo é verificar como o valor do
Out-of-Time-Order Correlator (OTOC) se comporta em circuitos de scrambling
aleatório para diferentes quantidades de qubits, profundidades e níveis de
ruído.

## Visão Geral dos Dados

O dataset contém medições de OTOC para circuitos com 2 e 3 qubits, com
profundidades (`scr_depth`) de 1 a 5. Para cada configuração foram avaliados
dois níveis de ruído (0 e 0.05) aplicados via mistura uniforme com a
identidade. O trecho abaixo apresenta parte dos valores coletados:

```
num_qubits,scr_depth,noise,otoc
2,1,0.0,-0.1497
2,2,0.0,-0.7002
2,3,0.0,0.5620
2,4,0.0,0.9327
2,5,0.0,-0.5438
3,1,0.0,-0.7155
3,2,0.0,0.9842
3,3,0.0,-0.0409
3,4,0.0,0.8279
3,5,0.0,0.2972
```

Para todos os pontos amostrados, os valores obtidos com ruído 0.05 coincidem
com os do simulador ideal. Isso indica que a mistura com 5% da identidade não
foi suficiente para alterar o OTOC nessa configuração simplificada.

## Interpretação

1. **Oscilações com a Profundidade**: Os valores de OTOC variam de forma
   não monotônica conforme a profundidade de scrambling aumenta. Em 2 qubits,
   observa-se alternância de sinais, indicando regimes de maior ou menor
   caoticidade do circuito.
2. **Influência do Número de Qubits**: Para 3 qubits os valores absolutos
demonstram amplitudes semelhantes às de 2 qubits, porém com padrões distintos
   de oscilação. Isso sugere que a transição para sistemas maiores preserva a
   sensibilidade do OTOC, mas modifica a frequência das oscilações.
3. **Efeito do Ruído**: A ausência de diferença entre os níveis de ruído
demonstrados aponta que o modelo de decoerência empregado (mistura com a
identidade) gera impacto insignificante em profundidades baixas. Ruídos mais
fortes ou modelos de canal mais realistas podem ser necessários para evidenciar
mudanças.

## Conclusão

O sweep inicial mostra que o comportamento do OTOC depende fortemente da
profundidade do circuito, mas é pouco sensível ao ruído leve considerado. Para
estudos futuros, recomenda-se aumentar a variedade de canais de ruído e explorar
profundidades maiores ou diferentes topologias de circuitos, de modo a analisar
se a tendência persiste em condições mais complexas.

## Observações Recentes

Durante testes adicionais, notou-se que o valor do OTOC permaneceu em `1.0` em
diversas execuções. A investigação apontou que, com apenas um qubit ou com
medições muito frequentes, o comutador entre `X` e `Z` resulta sempre no valor
máximo teórico após a normalização. Essa falta de variação pode mascarar
possíveis desvios de scrambling. Para contornar o problema ajustou-se
`measure_frequency` para valores menores e incrementou-se `scr_depth` quando
detectada estagnação do OTOC. Agora, logs adicionais registram o valor
intermediário do OTOC e a diversidade de circuitos a cada execução, permitindo
diagnóstico mais preciso.

### Estabilização do OTOC em 1.0
Quando os circuitos iniciam em estados computacionais básicos (\|0⟩ ou \|1⟩) e são analisados apenas com operadores de Pauli, o valor do OTOC normalizado tende a fixar-se em `1.0`. Isso ocorre porque o comutador entre os operadores permanece invariável, ocultando possíveis efeitos de scrambling.

Para observar variações significativas no OTOC, recomenda-se:
- preparar qubits em superposição antes da evolução,
- usar múltiplos qubits para aumentar o espaço de Hilbert,
- variar o `time_step` ao longo das simulações.
