"""Fibonacci Wave Hype Strategy - Detecção quântica de ondas de entusiasmo."""

from .core import FibonacciWaveHypeStrategy
from .indicators import (
    calculate_fibonacci_levels,
    detect_wave_patterns,
    calculate_hype_momentum
)
from .sentiment_integration import (
    integrate_holographic_sentiment,
    HolographicSentimentAnalyzer
)
from .backtesting import FWHBacktestEngine, run_fwh_backtest

__all__ = [
    "FibonacciWaveHypeStrategy",
    "calculate_fibonacci_levels",
    "detect_wave_patterns",
    "calculate_hype_momentum",
    "integrate_holographic_sentiment",
    "HolographicSentimentAnalyzer",
    "FWHBacktestEngine",
    "run_fwh_backtest",
]