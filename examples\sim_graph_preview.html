<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8" />
  <title>Preview – QUALIA SIM Graph</title>
  <style>
    html, body { margin: 0; height: 100%; overflow: hidden; background: radial-gradient(#0d0d1a, #000); }
    #graph { width: 100%; height: 100%; }
    #legend { position: absolute; top: 10px; left: 10px; color: #9df; font: 14px sans-serif; z-index: 2; }
  </style>
  <!-- CDN libs -->
  <script src="https://cdn.jsdelivr.net/npm/three@0.152.0/build/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/3d-force-graph@1.70.12/dist/3d-force-graph.min.js"></script>
</head>
<body>
  <div id="legend">QUALIA – Token Transition Graph (preview)</div>
  <div id="graph"></div>

<script>
  const FETCH_INTERVAL_MS = 2000;
  const ENDPOINT = '/api/sim_graph'; // ajuste se backend estiver em host diferente

  // Inicializa ForceGraph3D
  const Graph = ForceGraph3D()(document.getElementById('graph'))
    .backgroundColor('#000000')
    .nodeRelSize(6)
    .nodeOpacity(0.9)
    .linkOpacity(0.4)
    .linkDirectionalParticles(2)
    .linkDirectionalParticleSpeed(0.006)
    .linkDirectionalParticleWidth(l => Math.log2(l.weight + 1) + 0.5)
    .linkWidth(l => Math.log2(l.weight + 1) + 0.5);

  // Ajustes de visualização adicionais podem ser feitos com Graph.cameraPosition(...) if needed.

  // Grafo inicial vazio
  Graph.graphData({ nodes: [], links: [] })
    .nodeAutoColorBy('id')
    .nodeOpacity(0.85)
    .linkColor(() => '#88f');

  async function fetchGraph() {
    try {
      const res = await fetch(ENDPOINT);
      if (!res.ok) throw new Error(res.statusText);
      const data = await res.json();
      Graph.graphData(data);
      Graph.zoomToFit(400);
    } catch (err) {
      console.warn('Erro ao buscar SIM graph', err);
    }
  }

  // Primeiro fetch + intervalo
  fetchGraph();
  setInterval(fetchGraph, FETCH_INTERVAL_MS);
</script>
</body>
</html>
