# Mapeamento de Funcionalidades do `qualia_trading_system.py`

Este documento resume as principais responsabilidades encontradas no arquivo `src/qualia/qualia_trading_system.py`. O objetivo é facilitar a futura divisão em módulos menores.

## 1. Configuração de Exchange
- Carregamento de variáveis de ambiente e validação de credenciais
- Instanciação de objetos de integração (KrakenIntegration, KucoinIntegration)
- Configuração de parâmetros de segurança e limites de conexão

## 2. Estratégias
- Leitura de arquivos JSON com parâmetros de estratégia
- Criação de instâncias via `StrategyFactory`
- Gerenciamento de múltiplas estratégias por símbolo e timeframe

## 3. Monitoramento
- Acompanhamento de posições abertas com atualização de stop-loss/take-profit
- Emissão de métricas via DogStatsD
- Rotinas de verificação de saúde (falhas de API, limites de erro)

## 4. Loops de Trading
- Loop principal assíncrono que busca dados de mercado e executa ordens
- Agendamento de ciclos de metacognição
- Rotinas auxiliares para exibição de status e geração de relatórios

Este levantamento servirá de base para a refatoração descrita na Issue correspondente.
