import logging
import pytest

from tests.stub_utils import install_stubs

install_stubs()

from qualia.core.universe import QUALIAQuantumUniverse
from qualia.adaptive_evolution import AdaptiveConsciousnessEvolution


@pytest.fixture
def simple_universe_params():
    return {
        "n_qubits": 4,
        "scr_depth": 1,
        "base_lambda": 0.1,
        "alpha": 0.0,
        "retro_strength": 0.0,
        "num_ctc_qubits": 0,
        "informational_mass": 0.15,
        "hawking_factor": 0.1,
        "mass_threshold": 0.05,
        "initial_state_type": "hadamard",
    }


@pytest.fixture
def simple_universe(simple_universe_params):
    return QUALIAQuantumUniverse(**simple_universe_params)


def test_qubit_increase_throttle(simple_universe, caplog):
    ace = AdaptiveConsciousnessEvolution(qualia_universe=simple_universe)

    market_obs = {"volatility": 0.8, "non_linearity": 0.8}
    directives = {"override_on_metacognition": True, "qubit_increase": 2}

    with caplog.at_level(logging.INFO):
        assert ace.assess_and_adapt(market_obs, directives)

    increased_qubits = simple_universe.n_qubits

    market_obs2 = {"volatility": 0.82, "non_linearity": 0.82}
    with caplog.at_level(logging.INFO):
        assert not ace.assess_and_adapt(market_obs2, directives)

    assert simple_universe.n_qubits == increased_qubits
    assert (
        "bloqueado pelo throttle" in caplog.text
        or "Adaptação não acionada pelo throttle" in caplog.text
    )
