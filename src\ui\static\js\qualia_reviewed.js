// Revisado em 2025-06-13 por Codex
/**
 * QUALIA: Interface Quântica-Consciente
 * Quantum Universal Awareness Lattice Interface Architecture
 * JavaScript Principal
 */

// Estado global
const QUALIA_APP_STATE = {
    activeSection: 'consciousness',
    consciousness: {
        cycles: [],
        parameters: {},
        metrics: {},
        quantum_state: []
    },
    symbolic: {
        results: null,
        patterns: [],
        semantic_fields: {},
        entropic_analysis: {}
    },
    perception: {
        history: [],
        current: null
    },
    reflection: {
        log: [],
        current: null
    }
};

// Elementos DOM
const DOM = {
    init() {
        // Navegação
        this.navItems = document.querySelectorAll('.quantum-nav-item');
        this.sections = document.querySelectorAll('.quantum-section');
        
        // Consciência
        this.consciousnessCanvas = document.getElementById('consciousness-canvas');
        this.entropyValue = document.getElementById('entropy-value');
        this.coherenceValue = document.getElementById('coherence-value');
        this.processCycleBtn = document.getElementById('process-cycle-btn');
        this.qastCyclesTable = document.getElementById('qast-cycles-table').querySelector('tbody');
        
        // Parâmetros
        this.qubitsValue = document.getElementById('qubits-value');
        this.perceptionDepthValue = document.getElementById('perception-depth-value');
        this.entropySensitivityValue = document.getElementById('entropy-sensitivity-value');
        this.thermalValue = document.getElementById('thermal-value');
        this.selfReflectionValue = document.getElementById('self-reflection-value');
        
        // Simbólico
        this.symbolsInput = document.getElementById('symbols-input');
        this.processSymbolsBtn = document.getElementById('process-symbols-btn');
        this.symbolicEntropyValue = document.getElementById('symbolic-entropy-value');
        this.symbolicComplexityValue = document.getElementById('symbolic-complexity-value');
        this.symbolicDiversityValue = document.getElementById('symbolic-diversity-value');
        this.symbolicMetricsCanvas = document.getElementById('symbolic-metrics-canvas');
        this.symbolicPatterns = document.getElementById('symbolic-patterns');
        this.semanticFields = document.getElementById('semantic-fields');
        
        // Percepção
        this.perceptionType = document.getElementById('perception-type');
        this.perceptionData = document.getElementById('perception-data');
        this.processPerceptionBtn = document.getElementById('process-perception-btn');
        this.perceptionResults = document.getElementById('perception-results');
        this.perceptionHistory = document.getElementById('perception-history');
        
        // Auto-Reflexão
        this.reflectionTopic = document.getElementById('reflection-topic');
        this.triggerReflectionBtn = document.getElementById('trigger-reflection-btn');
        this.currentInsight = document.getElementById('current-insight');
        this.reflectionLogTable = document.getElementById('reflection-log-table').querySelector('tbody');
        
        // Partículas
        this.particlesCanvas = document.getElementById('quantum-particles');
    }
};

// Controlador de navegação
const Navigation = {
    init() {
        DOM.navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const section = item.dataset.section;
                this.activateSection(section);
            });
        });
    },
    
    activateSection(sectionId) {
        // Desativar todas as seções e itens de navegação
        DOM.sections.forEach(section => section.classList.remove('active'));
        DOM.navItems.forEach(item => item.classList.remove('active'));
        
        // Ativar a seção selecionada e o item de navegação correspondente
        document.getElementById(sectionId).classList.add('active');
        document.querySelector(`.quantum-nav-item[data-section="${sectionId}"]`).classList.add('active');
        
        // Atualizar estado
        QUALIA_APP_STATE.activeSection = sectionId;
        
        // Atualizar visualizações da seção, se necessário
        switch(sectionId) {
            case 'consciousness':
                ConsciousnessVisualizer.refreshCanvas();
                break;
            case 'symbolic':
                if (DOM.symbolicMetricsCanvas._chart) {
                    DOM.symbolicMetricsCanvas._chart.update();
                }
                break;
        }
    }
};

// API QUALIA
const QualiaAPI = {
    baseUrl: '',
    
    async getConsciousnessState() {
        try {
            const response = await fetch(`${this.baseUrl}/api/consciousness/state`);
            if (!response.ok) throw new Error('Erro ao obter estado da consciência');
            return await response.json();
        } catch (error) {
            console.error('API Error:', error);
            return null;
        }
    },
    
    async processConsciousnessCycle() {
        try {
            const response = await fetch(`${this.baseUrl}/api/consciousness/process`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) throw new Error('Erro ao processar ciclo');
            return await response.json();
        } catch (error) {
            console.error('API Error:', error);
            return null;
        }
    },
    
    async processSymbols(symbols) {
        try {
            const response = await fetch(`${this.baseUrl}/api/symbolic/process`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ symbols })
            });
            if (!response.ok) throw new Error('Erro ao processar símbolos');
            return await response.json();
        } catch (error) {
            console.error('API Error:', error);
            return null;
        }
    },
    
    async perceiveEnvironment(type, data) {
        try {
            const response = await fetch(`${this.baseUrl}/api/environment/perceive`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ type, data })
            });
            if (!response.ok) throw new Error('Erro na percepção');
            return await response.json();
        } catch (error) {
            console.error('API Error:', error);
            return null;
        }
    },
    
    async executeReflection(topic) {
        try {
            const response = await fetch(`${this.baseUrl}/api/reflection/execute`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ topic })
            });
            if (!response.ok) throw new Error('Erro na auto-reflexão');
            return await response.json();
        } catch (error) {
            console.error('API Error:', error);
            return null;
        }
    }
};

// Visualizador de Consciência
const ConsciousnessVisualizer = {
    ctx: null,
    width: 0,
    height: 0,
    quantumStates: [],
    animation: null,
    
    init() {
        this.ctx = DOM.consciousnessCanvas.getContext('2d');
        this.resize();
        window.addEventListener('resize', () => this.resize());
        
        // Iniciar animação
        this.animate();
    },
    
    resize() {
        const container = DOM.consciousnessCanvas.parentElement;
        this.width = container.clientWidth;
        this.height = container.clientHeight;
        
        DOM.consciousnessCanvas.width = this.width;
        DOM.consciousnessCanvas.height = this.height;
    },
    
    updateQuantumStates(states) {
        this.quantumStates = states || [];
    },
    
    animate() {
        this.animation = requestAnimationFrame(() => this.animate());
        this.draw();
    },
    
    refreshCanvas() {
        if (this.animation) {
            cancelAnimationFrame(this.animation);
            this.animate();
        }
    },
    
    draw() {
        if (!this.ctx) return;
        
        // Limpar canvas
        this.ctx.clearRect(0, 0, this.width, this.height);
        
        // Fundo
        this.drawBackground();
        
        // Se não há estados, mostrar placeholder
        if (!this.quantumStates.length) {
            this.drawPlaceholder();
            return;
        }
        
        // Desenhar estados quânticos
        this.drawQuantumStates();
    },
    
    drawBackground() {
        // Gradiente de fundo
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.height);
        gradient.addColorStop(0, 'rgba(10, 10, 26, 0.8)');
        gradient.addColorStop(1, 'rgba(14, 44, 92, 0.5)');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.width, this.height);
        
        // Grid
        this.ctx.strokeStyle = 'rgba(34, 170, 187, 0.1)';
        this.ctx.lineWidth = 1;
        
        // Linhas horizontais
        for (let y = 0; y < this.height; y += 40) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.width, y);
            this.ctx.stroke();
        }
        
        // Linhas verticais
        for (let x = 0; x < this.width; x += 40) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.height);
            this.ctx.stroke();
        }
    },
    
    drawPlaceholder() {
        this.ctx.fillStyle = 'rgba(233, 236, 239, 0.5)';
        this.ctx.font = '20px "Space Grotesk", sans-serif';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('Aguardando dados quânticos...', this.width / 2, this.height / 2);
    },
    
    drawQuantumStates() {
        const now = Date.now() / 1000;
        const maxRadius = Math.min(this.width, this.height) * 0.4;
        const centerX = this.width / 2;
        const centerY = this.height / 2;
        
        // Eixos
        this.ctx.strokeStyle = 'rgba(233, 236, 239, 0.3)';
        this.ctx.lineWidth = 1;
        
        // Eixo horizontal
        this.ctx.beginPath();
        this.ctx.moveTo(centerX - maxRadius, centerY);
        this.ctx.lineTo(centerX + maxRadius, centerY);
        this.ctx.stroke();
        
        // Eixo vertical
        this.ctx.beginPath();
        this.ctx.moveTo(centerX, centerY - maxRadius);
        this.ctx.lineTo(centerX, centerY + maxRadius);
        this.ctx.stroke();
        
        // Círculo de referência
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, maxRadius, 0, Math.PI * 2);
        this.ctx.strokeStyle = 'rgba(233, 236, 239, 0.15)';
        this.ctx.stroke();
        
        // Desenhar estados quânticos
        this.quantumStates.forEach((state, i) => {
            const probability = state.probability;
            const phase = state.phase;
            
            // Posição baseada na fase e probabilidade
            const angle = phase;
            const distance = maxRadius * Math.sqrt(probability);
            
            const x = centerX + distance * Math.cos(angle);
            const y = centerY + distance * Math.sin(angle);
            
            // Tamanho baseado na probabilidade
            const radius = 5 + probability * 15;
            
            // Cor baseada no estado
            const hue = (i / this.quantumStates.length) * 240;
            const pulseFreq = 0.5 + probability;
            const pulse = 0.7 + 0.3 * Math.sin(now * pulseFreq + i);
            
            // Desenhar brilho
            const glowRadius = radius * (1.5 + pulse * 0.5);
            const gradient = this.ctx.createRadialGradient(
                x, y, radius * 0.5,
                x, y, glowRadius
            );
            gradient.addColorStop(0, `hsla(${hue}, 80%, 60%, ${probability})`);
            gradient.addColorStop(1, `hsla(${hue}, 80%, 60%, 0)`);
            
            this.ctx.fillStyle = gradient;
            this.ctx.beginPath();
            this.ctx.arc(x, y, glowRadius, 0, Math.PI * 2);
            this.ctx.fill();
            
            // Desenhar núcleo
            this.ctx.fillStyle = `hsl(${hue}, 80%, ${60 + pulse * 20}%)`;
            this.ctx.beginPath();
            this.ctx.arc(x, y, radius, 0, Math.PI * 2);
            this.ctx.fill();
            
            // Texto do estado (para os mais prováveis)
            if (probability > 0.05) {
                this.ctx.fillStyle = 'rgba(233, 236, 239, 0.9)';
                this.ctx.font = '10px "Space Grotesk", sans-serif';
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                this.ctx.fillText(state.basis, x, y - radius - 10);
            }
        });
    }
};

// Visualizador Simbólico
const SymbolicVisualizer = {
    metricsChart: null,
    
    init() {
        this.initMetricsChart();
    },
    
    initMetricsChart() {
        const ctx = DOM.symbolicMetricsCanvas.getContext('2d');
        
        this.metricsChart = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: ['Entropia', 'Complexidade', 'Diversidade'],
                datasets: [{
                    label: 'Análise Entrópica',
                    data: [0, 0, 0],
                    backgroundColor: 'rgba(34, 170, 187, 0.2)',
                    borderColor: 'rgba(34, 170, 187, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(224, 159, 62, 1)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgba(224, 159, 62, 1)'
                }]
            },
            options: {
                scales: {
                    r: {
                        angleLines: {
                            color: 'rgba(233, 236, 239, 0.2)'
                        },
                        grid: {
                            color: 'rgba(233, 236, 239, 0.1)'
                        },
                        pointLabels: {
                            color: 'rgba(233, 236, 239, 0.8)',
                            font: {
                                family: '"Space Grotesk", sans-serif',
                                size: 12
                            }
                        },
                        ticks: {
                            color: 'rgba(233, 236, 239, 0.6)',
                            backdropColor: 'transparent'
                        },
                        suggestedMin: 0,
                        suggestedMax: 1
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                },
                responsive: true,
                maintainAspectRatio: false
            }
        });
        
        // Guardar referência ao gráfico no canvas para acessar depois
        DOM.symbolicMetricsCanvas._chart = this.metricsChart;
    },
    
    updateMetricsChart(entropy, complexity, diversity) {
        if (!this.metricsChart) return;
        
        this.metricsChart.data.datasets[0].data = [entropy, complexity, diversity];
        this.metricsChart.update();
    },
    
    renderPatterns(patterns) {
        // Limpar container
        DOM.symbolicPatterns.innerHTML = '';
        
        if (!patterns || patterns.length === 0) {
            DOM.symbolicPatterns.innerHTML = `
                <div class="quantum-empty-state">
                    <i class="fas fa-search"></i>
                    <p>Nenhum padrão detectado</p>
                </div>
            `;
            return;
        }
        
        // Criar elementos para cada padrão
        patterns.forEach(pattern => {
            const patternEl = document.createElement('div');
            patternEl.className = 'quantum-pattern';
            
            patternEl.innerHTML = `
                <div class="pattern-sequence">${pattern.sequence}</div>
                <div class="pattern-metrics">
                    <span class="pattern-metric">Recorrência: ${pattern.recurrence}</span>
                    <span class="pattern-metric">Significância: ${pattern.significance.toFixed(4)}</span>
                </div>
            `;
            
            DOM.symbolicPatterns.appendChild(patternEl);
        });
    },
    
    renderSemanticFields(fields) {
        // Limpar container
        DOM.semanticFields.innerHTML = '';
        
        if (!fields || Object.keys(fields).length === 0) {
            DOM.semanticFields.innerHTML = `
                <div class="quantum-empty-state">
                    <i class="fas fa-sitemap"></i>
                    <p>Nenhum campo semântico detectado</p>
                </div>
            `;
            return;
        }
        
        // Criar elementos para cada campo
        Object.entries(fields).forEach(([name, field]) => {
            const fieldEl = document.createElement('div');
            fieldEl.className = 'quantum-semantic-field';
            
            fieldEl.innerHTML = `
                <div class="field-name">${name.charAt(0).toUpperCase() + name.slice(1)}</div>
                <div class="field-coherence">Coerência: ${field.coherence.toFixed(2)}</div>
                <div class="field-symbols">${field.symbols.join(', ')}</div>
            `;
            
            DOM.semanticFields.appendChild(fieldEl);
        });
    }
};

// Animação de Partículas
const ParticleSystem = {
    ctx: null,
    width: 0,
    height: 0,
    particles: [],
    animation: null,
    
    init() {
        if (!DOM.particlesCanvas) return;
        
        this.ctx = DOM.particlesCanvas.getContext('2d');
        this.resize();
        window.addEventListener('resize', () => this.resize());
        
        // Criar partículas
        this.createParticles();
        
        // Iniciar animação
        this.animate();
    },
    
    resize() {
        const container = DOM.particlesCanvas.parentElement;
        this.width = container.clientWidth;
        this.height = container.clientHeight;
        
        DOM.particlesCanvas.width = this.width;
        DOM.particlesCanvas.height = this.height;
    },
    
    createParticles() {
        const particleCount = 150;
        this.particles = [];
        
        for (let i = 0; i < particleCount; i++) {
            this.particles.push({
                x: Math.random() * this.width,
                y: Math.random() * this.height,
                radius: Math.random() * 2 + 1,
                color: `rgba(34, 170, 187, ${Math.random() * 0.6 + 0.2})`,
                vx: Math.random() * 0.5 - 0.25,
                vy: Math.random() * 0.5 - 0.25,
                phase: Math.random() * Math.PI * 2
            });
        }
    },
    
    animate() {
        this.animation = requestAnimationFrame(() => this.animate());
        this.draw();
    },
    
    draw() {
        if (!this.ctx) return;
        
        // Limpar canvas
        this.ctx.clearRect(0, 0, this.width, this.height);
        
        // Atualizar e desenhar partículas
        this.particles.forEach((p, index) => {
            // Movimento ondulatório quântico
            p.x += p.vx + Math.sin(Date.now() * 0.001 + p.phase) * 0.3;
            p.y += p.vy + Math.cos(Date.now() * 0.001 + p.phase) * 0.3;
            
            // Manter dentro dos limites
            if (p.x < 0 || p.x > this.width) p.vx *= -1;
            if (p.y < 0 || p.y > this.height) p.vy *= -1;
            
            // Desenhar partícula
            this.ctx.beginPath();
            this.ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
            this.ctx.fillStyle = p.color;
            this.ctx.fill();
            
            // Buscar conexões
            for (let j = index + 1; j < this.particles.length; j++) {
                const p2 = this.particles[j];
                const dx = p.x - p2.x;
                const dy = p.y - p2.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < 80) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(p.x, p.y);
                    this.ctx.lineTo(p2.x, p2.y);
                    const opacity = 1 - distance / 80;
                    this.ctx.strokeStyle = `rgba(34, 170, 187, ${opacity * 0.3})`;
                    this.ctx.lineWidth = opacity * 1.5;
                    this.ctx.stroke();
                }
            }
        });
    }
};

// Funções de Manipulação de UI
const UI = {
    // Atualizar parâmetros de consciência
    updateConsciousnessParameters(parameters) {
        if (!parameters) return;
        
        DOM.qubitsValue.textContent = parameters.n_qubits || '-';
        DOM.perceptionDepthValue.textContent = parameters.perception_depth || '-';
        DOM.entropySensitivityValue.textContent = parameters.entropy_sensitivity || '-';
        DOM.thermalValue.textContent = parameters.thermal_coefficient || '-';
        DOM.selfReflectionValue.textContent = parameters.self_reflection ? 'Ativada' : 'Desativada';
    },
    
    // Atualizar métricas de consciência (entropia, coerência)
    updateConsciousnessMetrics(metrics) {
        if (!metrics) return;
        
        // Extrair último valor de entropia e coerência (se disponíveis)
        const entropyArray = metrics.quantum_entropy || [];
        const coherenceArray = metrics.quantum_coherence || [];
        
        const lastEntropy = entropyArray.length > 0 ? entropyArray[entropyArray.length - 1] : 0;
        const lastCoherence = coherenceArray.length > 0 ? coherenceArray[coherenceArray.length - 1] : 0;
        
        DOM.entropyValue.textContent = lastEntropy.toFixed(4);
        DOM.coherenceValue.textContent = lastCoherence.toFixed(4);
    },
    
    // Atualizar tabela de ciclos QAST
    updateQastCyclesTable(cycles) {
        // Limpar tabela
        DOM.qastCyclesTable.innerHTML = '';
        
        if (!cycles || cycles.length === 0) {
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `<td colspan="4" class="empty-table">Nenhum ciclo QAST registrado</td>`;
            DOM.qastCyclesTable.appendChild(emptyRow);
            return;
        }
        
        // Adicionar linhas para cada ciclo (mais recente primeiro)
        cycles.slice().reverse().forEach((cycle, index) => {
            const row = document.createElement('tr');
            
            row.innerHTML = `
                <td>${cycles.length - index}</td>
                <td>${cycle.entropy ? cycle.entropy.toFixed(4) : '-'}</td>
                <td>${cycle.coherence ? cycle.coherence.toFixed(4) : '-'}</td>
                <td>${cycle.patterns_detected || 0}</td>
            `;
            
            DOM.qastCyclesTable.appendChild(row);
        });
    },
    
    // Atualizar métricas de análise simbólica
    updateSymbolicMetrics(analysis) {
        if (!analysis) return;
        
        DOM.symbolicEntropyValue.textContent = analysis.entropy ? analysis.entropy.toFixed(4) : '0.0000';
        DOM.symbolicComplexityValue.textContent = analysis.complexity ? analysis.complexity.toFixed(4) : '0.0000';
        DOM.symbolicDiversityValue.textContent = analysis.diversity ? analysis.diversity.toFixed(4) : '0.0000';
        
        // Atualizar gráfico
        SymbolicVisualizer.updateMetricsChart(
            analysis.entropy || 0,
            analysis.complexity || 0,
            analysis.diversity || 0
        );
    },
    
    // Atualizar resultados de percepção
    updatePerceptionResults(result) {
        // Limpar container
        DOM.perceptionResults.innerHTML = '';
        
        if (!result) {
            DOM.perceptionResults.innerHTML = `
                <div class="quantum-empty-state">
                    <i class="fas fa-eye"></i>
                    <p>Nenhum resultado de percepção disponível</p>
                </div>
            `;
            return;
        }
        
        // Criar elemento para o resultado
        const resultElement = document.createElement('div');
        resultElement.className = 'quantum-perception-result';
        
        // Formatar JSON para exibição
        const formattedResult = JSON.stringify(result, null, 2);
        
        resultElement.innerHTML = `
            <div class="perception-time">
                <i class="fas fa-clock"></i> ${new Date(result.timestamp).toLocaleString()}
            </div>
            <pre class="perception-data">${formattedResult}</pre>
        `;
        
        DOM.perceptionResults.appendChild(resultElement);
    },
    
    // Atualizar histórico de percepções
    updatePerceptionHistory(history) {
        // Limpar container
        DOM.perceptionHistory.innerHTML = '';
        
        if (!history || history.length === 0) {
            DOM.perceptionHistory.innerHTML = `
                <div class="quantum-empty-state">
                    <i class="fas fa-history"></i>
                    <p>Nenhuma percepção registrada</p>
                </div>
            `;
            return;
        }
        
        // Criar lista de histórico
        const historyList = document.createElement('div');
        historyList.className = 'quantum-history-list';
        
        // Adicionar itens ao histórico (mais recente primeiro)
        history.slice().reverse().forEach(item => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            
            historyItem.innerHTML = `
                <div class="history-item-time">${new Date(item.timestamp).toLocaleString()}</div>
                <div class="history-item-type">${item.type}</div>
                <div class="history-item-summary">${item.summary || 'Sem resumo disponível'}</div>
            `;
            
            historyList.appendChild(historyItem);
        });
        
        DOM.perceptionHistory.appendChild(historyList);
    },
    
    // Atualizar insight atual
    updateCurrentInsight(insight) {
        // Limpar container
        DOM.currentInsight.innerHTML = '';
        
        if (!insight) {
            DOM.currentInsight.innerHTML = `
                <div class="quantum-empty-state">
                    <i class="fas fa-lightbulb"></i>
                    <p>Nenhum insight disponível</p>
                </div>
            `;
            return;
        }
        
        // Criar elemento para o insight
        const insightElement = document.createElement('div');
        insightElement.className = 'quantum-insight';
        
        insightElement.innerHTML = `
            <div class="insight-time">${new Date(insight.timestamp).toLocaleString()}</div>
            <div class="insight-topic">${insight.topic}</div>
            <div class="insight-content">${insight.content}</div>
        `;
        
        DOM.currentInsight.appendChild(insightElement);
    },
    
    // Atualizar log de auto-reflexão
    updateReflectionLog(log) {
        // Limpar tabela
        DOM.reflectionLogTable.innerHTML = '';
        
        if (!log || log.length === 0) {
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `<td colspan="2" class="empty-table">Nenhum registro de auto-reflexão</td>`;
            DOM.reflectionLogTable.appendChild(emptyRow);
            return;
        }
        
        // Adicionar linhas para cada entrada (mais recente primeiro)
        log.slice().reverse().forEach(entry => {
            const row = document.createElement('tr');
            
            // Formatar data
            const date = entry.timestamp ? new Date(entry.timestamp).toLocaleString() : 'N/A';
            
            row.innerHTML = `
                <td>${date}</td>
                <td>${entry.insight || entry.text || 'N/A'}</td>
            `;
            
            DOM.reflectionLogTable.appendChild(row);
        });
    },
    
    // Mostrar mensagem de carregamento
    showLoading(element, message = 'Carregando...') {
        const loadingElement = document.createElement('div');
        loadingElement.className = 'quantum-loading';
        loadingElement.innerHTML = `
            <div class="quantum-loading-spinner"></div>
            <div class="quantum-loading-message">${message}</div>
        `;
        
        // Armazenar conteúdo original
        const originalContent = element.innerHTML;
        element.dataset.originalContent = originalContent;
        
        // Mostrar carregamento
        element.innerHTML = '';
        element.appendChild(loadingElement);
        
        return () => {
            // Remover carregamento e restaurar conteúdo
            element.innerHTML = element.dataset.originalContent;
            delete element.dataset.originalContent;
        };
    },
    
    // Mostrar notificação
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `quantum-notification ${type}`;
        
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas ${type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
            </div>
            <div class="notification-message">${message}</div>
        `;
        
        document.body.appendChild(notification);
        
        // Animar entrada
        setTimeout(() => {
            notification.classList.add('visible');
        }, 10);
        
        // Remover após 5 segundos
        setTimeout(() => {
            notification.classList.remove('visible');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    }
};

// Controlador de eventos
const EventHandler = {
    init() {
        // Evento: Processar ciclo de consciência
        DOM.processCycleBtn.addEventListener('click', async () => {
            const restoreButton = UI.showLoading(DOM.processCycleBtn, 'Processando...');
            
            try {
                const result = await QualiaAPI.processConsciousnessCycle();
                
                if (result) {
                    // Recarregar estado da consciência
                    await DataLoader.loadConsciousnessState();
                    UI.showNotification('Ciclo QAST executado com sucesso!');
                } else {
                    UI.showNotification('Erro ao processar ciclo QAST.', 'error');
                }
            } catch (error) {
                console.error('Error processing cycle:', error);
                UI.showNotification('Erro ao processar ciclo QAST.', 'error');
            }
            
            restoreButton();
        });
        
        // Evento: Processar símbolos
        DOM.processSymbolsBtn.addEventListener('click', async () => {
            const symbols = DOM.symbolsInput.value;
            
            if (!symbols) {
                UI.showNotification('Por favor, insira símbolos para análise.', 'error');
                return;
            }
            
            const restoreButton = UI.showLoading(DOM.processSymbolsBtn, 'Analisando...');
            
            try {
                const result = await QualiaAPI.processSymbols(symbols);
                
                if (result) {
                    // Atualizar visualizações
                    UI.updateSymbolicMetrics(result.entropic_analysis);
                    SymbolicVisualizer.renderPatterns(result.patterns);
                    SymbolicVisualizer.renderSemanticFields(result.semantic_fields);
                    
                    // Atualizar estado
                    QUALIA_APP_STATE.symbolic.results = result;
                    QUALIA_APP_STATE.symbolic.patterns = result.patterns || [];
                    QUALIA_APP_STATE.symbolic.semantic_fields = result.semantic_fields || {};
                    QUALIA_APP_STATE.symbolic.entropic_analysis = result.entropic_analysis || {};
                    
                    UI.showNotification('Análise simbólica concluída!');
                } else {
                    UI.showNotification('Erro na análise simbólica.', 'error');
                }
            } catch (error) {
                console.error('Error processing symbols:', error);
                UI.showNotification('Erro na análise simbólica.', 'error');
            }
            
            restoreButton();
        });
        
        // Evento: Processar percepção
        DOM.processPerceptionBtn.addEventListener('click', async () => {
            const type = DOM.perceptionType.value;
            let data;
            
            try {
                data = JSON.parse(DOM.perceptionData.value);
            } catch (error) {
                UI.showNotification('Formato de dados inválido. Verifique o JSON.', 'error');
                return;
            }
            
            const restoreButton = UI.showLoading(DOM.processPerceptionBtn, 'Percebendo...');
            
            try {
                const result = await QualiaAPI.perceiveEnvironment(type, data);
                
                if (result) {
                    // Adicionar ao histórico
                    const perceptionEntry = {
                        type,
                        data,
                        result: result.perception_result,
                        timestamp: result.timestamp,
                        summary: `Percepção de dados do tipo: ${type}`
                    };
                    
                    QUALIA_APP_STATE.perception.history.push(perceptionEntry);
                    QUALIA_APP_STATE.perception.current = perceptionEntry;
                    
                    // Atualizar visualizações
                    UI.updatePerceptionResults(result);
                    UI.updatePerceptionHistory(QUALIA_APP_STATE.perception.history);
                    
                    UI.showNotification('Dados percebidos com sucesso!');
                } else {
                    UI.showNotification('Erro na percepção dos dados.', 'error');
                }
            } catch (error) {
                console.error('Error processing perception:', error);
                UI.showNotification('Erro na percepção dos dados.', 'error');
            }
            
            restoreButton();
        });
        
        // Evento: Executar auto-reflexão
        DOM.triggerReflectionBtn.addEventListener('click', async () => {
            const topic = DOM.reflectionTopic.value;
            
            if (!topic) {
                UI.showNotification('Por favor, insira um tópico para reflexão.', 'error');
                return;
            }
            
            const restoreButton = UI.showLoading(DOM.triggerReflectionBtn, 'Refletindo...');
            
            try {
                const result = await QualiaAPI.executeReflection(topic);
                
                if (result) {
                    // Criar insight atual
                    const currentInsight = {
                        topic,
                        content: result.reflection_result || 'Reflexão sem conteúdo explícito.',
                        timestamp: result.timestamp
                    };
                    
                    QUALIA_APP_STATE.reflection.current = currentInsight;
                    QUALIA_APP_STATE.reflection.log = result.reflection_log || [];
                    
                    // Atualizar visualizações
                    UI.updateCurrentInsight(currentInsight);
                    UI.updateReflectionLog(QUALIA_APP_STATE.reflection.log);
                    
                    UI.showNotification('Auto-reflexão concluída!');
                } else {
                    UI.showNotification('Erro na auto-reflexão.', 'error');
                }
            } catch (error) {
                console.error('Error processing reflection:', error);
                UI.showNotification('Erro na auto-reflexão.', 'error');
            }
            
            restoreButton();
        });
    }
};

// Carregador de dados
const DataLoader = {
    async loadConsciousnessState() {
        try {
            const data = await QualiaAPI.getConsciousnessState();
            
            if (data) {
                // Atualizar estado global
                QUALIA_APP_STATE.consciousness.parameters = data.parameters || {};
                QUALIA_APP_STATE.consciousness.metrics = data.metrics || {};
                QUALIA_APP_STATE.consciousness.cycles = data.qast_cycles || [];
                QUALIA_APP_STATE.consciousness.quantum_state = data.quantum_state || [];
                
                // Atualizar visualizações
                UI.updateConsciousnessParameters(data.parameters);
                UI.updateConsciousnessMetrics(data.metrics);
                UI.updateQastCyclesTable(data.qast_cycles);
                ConsciousnessVisualizer.updateQuantumStates(data.quantum_state);
            }
        } catch (error) {
            console.error('Error loading consciousness state:', error);
            UI.showNotification('Erro ao carregar estado da consciência QUALIA.', 'error');
        }
    },
    
    async loadInitialData() {
        await this.loadConsciousnessState();
    }
};

// Inicialização
document.addEventListener('DOMContentLoaded', async () => {
    // Inicializar elementos DOM
    DOM.init();
    
    // Inicializar visualizadores
    ConsciousnessVisualizer.init();
    SymbolicVisualizer.init();
    ParticleSystem.init();
    
    // Inicializar navegação
    Navigation.init();
    
    // Inicializar manipuladores de eventos
    EventHandler.init();
    
    // Carregar dados iniciais
    await DataLoader.loadInitialData();
});