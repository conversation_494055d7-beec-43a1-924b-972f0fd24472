from __future__ import annotations

"""Simple social media data provider.

Fetches sentiment data from public APIs and publishes normalized metrics
on the :class:`~qualia.memory.event_bus.SimpleEventBus`.
"""

from dataclasses import dataclass
from typing import List, Optional
import time
import aiohttp

from ..memory.event_bus import SimpleEventBus, SOCIAL_SENTIMENT_STREAM
from ..core.rss_encoder import RSSTextSentimentEncoder
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class SocialMetric:
    """Normalized social sentiment metric."""

    platform: str
    symbol: str
    sentiment_score: float
    volume_mentions: int
    timestamp: float
    keywords: List[str]


class SocialMediaProvider:
    """Coleta dados de sentimento de redes sociais."""

    def __init__(self, event_bus: Optional[SimpleEventBus] = None) -> None:
        self.event_bus = event_bus
        self.session: aiohttp.ClientSession | None = None
        self.encoder = RSSTextSentimentEncoder()

    async def __aenter__(self) -> "SocialMediaProvider":
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=10),
            headers={"User-Agent": "QUALIA-SocialProvider/1.0"},
        )
        return self

    async def __aexit__(self, exc_type, exc, tb) -> None:
        if self.session:
            await self.session.close()
            self.session = None

    # ------------------------------------------------------------------
    async def _fetch_twitter(self, keyword: str) -> List[SocialMetric]:
        if not self.session:
            return []
        url = f"https://api.twitter.com/2/tweets/search/recent?query={keyword}&max_results=5"
        try:
            async with self.session.get(url) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    metrics: List[SocialMetric] = []
                    for entry in data.get("data", []):
                        text = entry.get("text", "")
                        score = self.encoder._get_sentiment({"text": text})
                        metrics.append(
                            SocialMetric(
                                platform="twitter",
                                symbol=keyword,
                                sentiment_score=score,
                                volume_mentions=1,
                                timestamp=time.time(),
                                keywords=[keyword],
                            )
                        )
                    return metrics
        except Exception as exc:  # pragma: no cover - network errors not in tests
            logger.debug("Twitter API error: %s", exc)
        return []

    async def _fetch_reddit(self, keyword: str) -> List[SocialMetric]:
        if not self.session:
            return []
        url = f"https://www.reddit.com/search.json?q={keyword}&limit=5&sort=new"
        headers = {"User-Agent": "QUALIA-Reddit/1.0"}
        try:
            async with self.session.get(url, headers=headers) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    metrics: List[SocialMetric] = []
                    for child in data.get("data", {}).get("children", []):
                        text = child.get("data", {}).get("title", "")
                        score = self.encoder._get_sentiment({"text": text})
                        metrics.append(
                            SocialMetric(
                                platform="reddit",
                                symbol=keyword,
                                sentiment_score=score,
                                volume_mentions=1,
                                timestamp=time.time(),
                                keywords=[keyword],
                            )
                        )
                    return metrics
        except Exception as exc:  # pragma: no cover - network errors not in tests
            logger.debug("Reddit API error: %s", exc)
        return []

    async def collect_metrics(self, keyword: str) -> List[SocialMetric]:
        """Collect metrics for ``keyword`` and publish them."""

        metrics: List[SocialMetric] = []
        metrics.extend(await self._fetch_twitter(keyword))
        metrics.extend(await self._fetch_reddit(keyword))

        if self.event_bus:
            for metric in metrics:
                self.event_bus.publish(SOCIAL_SENTIMENT_STREAM, metric)

        logger.debug("SocialMediaProvider coletou %d métricas", len(metrics))
        return metrics
