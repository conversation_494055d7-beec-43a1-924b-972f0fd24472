"""
QUALIA Live Feed - Setup e Validação de Credenciais KuCoin

Script para configurar e validar credenciais do KuCoin antes de executar os testes.
"""

import os
import asyncio
import getpass
from typing import Dict, Any, Optional

from ..market.kucoin_integration import KucoinIntegration
from ..utils.logger import get_logger

logger = get_logger(__name__)


class CredentialsSetup:
    """Configuração e validação de credenciais KuCoin."""
    
    def __init__(self):
        self.credentials = {}
    
    def check_environment_variables(self) -> Dict[str, bool]:
        """Verifica se as variáveis de ambiente estão configuradas."""
        env_vars = {
            'KUCOIN_API_KEY': bool(os.getenv('KUCOIN_API_KEY')),
            'KUCOIN_API_SECRET': bool(os.getenv('KUCOIN_API_SECRET')),
            'KUCOIN_PASSPHRASE': bool(os.getenv('KUCOIN_PASSPHRASE')),
        }
        
        logger.info("🔍 Verificando variáveis de ambiente:")
        for var, present in env_vars.items():
            status = "✅ Configurada" if present else "❌ Ausente"
            logger.info(f"   {var}: {status}")
        
        return env_vars
    
    def prompt_for_credentials(self) -> Dict[str, str]:
        """Solicita credenciais do usuário interativamente."""
        logger.info("🔑 Configuração de Credenciais KuCoin")
        logger.info("   Você pode obter suas credenciais em: https://www.kucoin.com/account/api")
        logger.info("")
        
        credentials = {}
        
        # API Key
        api_key = input("Digite sua KuCoin API Key: ").strip()
        if not api_key:
            raise ValueError("API Key é obrigatória")
        credentials['api_key'] = api_key
        
        # API Secret
        api_secret = getpass.getpass("Digite sua KuCoin API Secret: ").strip()
        if not api_secret:
            raise ValueError("API Secret é obrigatória")
        credentials['api_secret'] = api_secret
        
        # Passphrase
        passphrase = getpass.getpass("Digite sua KuCoin Passphrase: ").strip()
        if not passphrase:
            raise ValueError("Passphrase é obrigatória")
        credentials['passphrase'] = passphrase
        
        return credentials
    
    def save_to_env_file(self, credentials: Dict[str, str], filename: str = ".env"):
        """Salva credenciais em arquivo .env."""
        try:
            env_content = f"""# KuCoin API Credentials
KUCOIN_API_KEY={credentials['api_key']}
KUCOIN_API_SECRET={credentials['api_secret']}
KUCOIN_PASSPHRASE={credentials['passphrase']}

# Optional: Set to true for sandbox testing
# KUCOIN_SANDBOX=true
"""
            
            with open(filename, 'w') as f:
                f.write(env_content)
            
            logger.info(f"✅ Credenciais salvas em {filename}")
            logger.info("   Lembre-se de adicionar .env ao .gitignore!")
            
        except Exception as e:
            logger.error(f"❌ Erro ao salvar credenciais: {e}")
            raise
    
    async def validate_credentials(self, 
                                 api_key: str, 
                                 api_secret: str, 
                                 passphrase: str, 
                                 sandbox: bool = True) -> Dict[str, Any]:
        """Valida credenciais fazendo uma chamada de teste à API."""
        logger.info(f"🧪 Validando credenciais ({'SANDBOX' if sandbox else 'PRODUÇÃO'})...")
        
        try:
            # Criar integração KuCoin
            kucoin = KucoinIntegration(
                api_key=api_key,
                api_secret=api_secret,
                password=passphrase,
                sandbox=sandbox
            )
            
            # Inicializar conexão
            await kucoin.initialize_connection()
            
            # Testes básicos
            results = {}
            
            # 1. Teste de tempo do servidor
            try:
                server_time = await kucoin.get_server_time()
                results['server_time'] = {
                    'success': True,
                    'time': server_time,
                    'message': 'Conexão com servidor OK'
                }
                logger.info("   ✅ Conexão com servidor: OK")
            except Exception as e:
                results['server_time'] = {
                    'success': False,
                    'error': str(e),
                    'message': 'Falha na conexão com servidor'
                }
                logger.error(f"   ❌ Conexão com servidor: {e}")
            
            # 2. Teste de autenticação (buscar informações da conta)
            try:
                # Tentar buscar informações básicas da conta
                account_info = await kucoin.get_account_info()
                results['authentication'] = {
                    'success': True,
                    'account_info': account_info,
                    'message': 'Autenticação bem-sucedida'
                }
                logger.info("   ✅ Autenticação: OK")
            except Exception as e:
                results['authentication'] = {
                    'success': False,
                    'error': str(e),
                    'message': 'Falha na autenticação'
                }
                logger.error(f"   ❌ Autenticação: {e}")
            
            # 3. Teste de acesso a dados de mercado
            try:
                ticker = await kucoin.fetch_ticker("BTC/USDT")
                results['market_data'] = {
                    'success': True,
                    'ticker': ticker,
                    'message': 'Acesso a dados de mercado OK'
                }
                logger.info("   ✅ Dados de mercado: OK")
            except Exception as e:
                results['market_data'] = {
                    'success': False,
                    'error': str(e),
                    'message': 'Falha no acesso a dados de mercado'
                }
                logger.error(f"   ❌ Dados de mercado: {e}")
            
            # 4. Teste de WebSocket (se disponível)
            try:
                # Teste básico de WebSocket
                ws_available = hasattr(kucoin, 'websocket_manager') and kucoin.websocket_manager is not None
                results['websocket'] = {
                    'success': ws_available,
                    'available': ws_available,
                    'message': 'WebSocket disponível' if ws_available else 'WebSocket não disponível'
                }
                status = "✅" if ws_available else "⚠️"
                logger.info(f"   {status} WebSocket: {'Disponível' if ws_available else 'Não disponível'}")
            except Exception as e:
                results['websocket'] = {
                    'success': False,
                    'error': str(e),
                    'message': 'Erro ao verificar WebSocket'
                }
                logger.error(f"   ❌ WebSocket: {e}")
            
            # Fechar conexão
            await kucoin.close_connection()
            
            # Resumo
            successful_tests = sum(1 for r in results.values() if r.get('success', False))
            total_tests = len(results)
            
            results['summary'] = {
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'success_rate': (successful_tests / total_tests) * 100,
                'overall_success': successful_tests >= 3,  # Pelo menos 3 de 4 testes
            }
            
            logger.info(f"📊 Resumo da validação:")
            logger.info(f"   Testes executados: {total_tests}")
            logger.info(f"   Testes bem-sucedidos: {successful_tests}")
            logger.info(f"   Taxa de sucesso: {results['summary']['success_rate']:.1f}%")
            
            if results['summary']['overall_success']:
                logger.info("✅ Credenciais validadas com sucesso!")
            else:
                logger.warning("⚠️ Credenciais com problemas - verifique os erros acima")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Erro crítico na validação: {e}")
            return {
                'summary': {
                    'total_tests': 0,
                    'successful_tests': 0,
                    'success_rate': 0,
                    'overall_success': False,
                    'critical_error': str(e)
                }
            }
    
    async def setup_and_validate(self) -> bool:
        """Processo completo de setup e validação."""
        try:
            logger.info("🚀 QUALIA Live Feed - Setup de Credenciais")
            logger.info("="*50)
            
            # 1. Verificar variáveis de ambiente
            env_vars = self.check_environment_variables()
            
            # 2. Se não estão configuradas, solicitar do usuário
            if not all(env_vars.values()):
                logger.info("\n📝 Credenciais não encontradas nas variáveis de ambiente")
                
                choice = input("\nDeseja configurar credenciais agora? (s/n): ").lower().strip()
                if choice not in ['s', 'sim', 'y', 'yes']:
                    logger.info("Setup cancelado pelo usuário")
                    return False
                
                credentials = self.prompt_for_credentials()
                
                # Salvar em arquivo .env
                save_choice = input("\nDeseja salvar credenciais em arquivo .env? (s/n): ").lower().strip()
                if save_choice in ['s', 'sim', 'y', 'yes']:
                    self.save_to_env_file(credentials)
                
                # Definir variáveis de ambiente para esta sessão
                os.environ['KUCOIN_API_KEY'] = credentials['api_key']
                os.environ['KUCOIN_API_SECRET'] = credentials['api_secret']
                os.environ['KUCOIN_PASSPHRASE'] = credentials['passphrase']
            
            # 3. Validar credenciais em sandbox
            logger.info("\n🧪 Validando credenciais em SANDBOX...")
            sandbox_results = await self.validate_credentials(
                api_key=os.getenv('KUCOIN_API_KEY'),
                api_secret=os.getenv('KUCOIN_API_SECRET'),
                passphrase=os.getenv('KUCOIN_PASSPHRASE'),
                sandbox=True
            )
            
            if not sandbox_results['summary']['overall_success']:
                logger.error("❌ Validação em sandbox falhou!")
                return False
            
            # 4. Perguntar se quer validar em produção
            prod_choice = input("\nDeseja validar credenciais em PRODUÇÃO também? (s/n): ").lower().strip()
            if prod_choice in ['s', 'sim', 'y', 'yes']:
                logger.info("\n🚀 Validando credenciais em PRODUÇÃO...")
                prod_results = await self.validate_credentials(
                    api_key=os.getenv('KUCOIN_API_KEY'),
                    api_secret=os.getenv('KUCOIN_API_SECRET'),
                    passphrase=os.getenv('KUCOIN_PASSPHRASE'),
                    sandbox=False
                )
                
                if not prod_results['summary']['overall_success']:
                    logger.warning("⚠️ Validação em produção falhou!")
                    logger.info("   Você ainda pode executar testes em sandbox")
            
            logger.info("\n✅ Setup concluído com sucesso!")
            logger.info("   Agora você pode executar os testes com:")
            logger.info("   python -m qualia.live_feed.test_suite")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro no setup: {e}")
            return False


async def main():
    """Função principal do setup."""
    setup = CredentialsSetup()
    success = await setup.setup_and_validate()
    
    if success:
        print("\n🎉 Setup concluído! Próximos passos:")
        print("   1. Execute os testes: python -m qualia.live_feed.test_suite")
        print("   2. Ou execute o exemplo: python -m qualia.live_feed.example_usage")
    else:
        print("\n❌ Setup falhou. Verifique as credenciais e tente novamente.")


if __name__ == "__main__":
    asyncio.run(main())
