"""
QUALIA Prometheus Metrics Exporter

YAA REFINEMENT: Exporta métricas QUALIA para Prometheus via endpoint HTTP.
Integra com sistema de telemetria existente para observabilidade completa.
"""

from __future__ import annotations

import time
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from threading import Thread, Lock
from http.server import HTTPServer, BaseHTTPRequestHandler
import json

from prometheus_client import (
    Gauge, Counter, Histogram, Info, 
    generate_latest, CONTENT_TYPE_LATEST,
    CollectorRegistry, REGISTRY
)

from ..metrics.hyperparams_telemetry import get_global_hyperparams_telemetry
from ..monitoring.metrics import get_collector
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class PrometheusMetric:
    """Definição de uma métrica Prometheus."""
    name: str
    help_text: str
    metric_type: str  # 'gauge', 'counter', 'histogram', 'info'
    labels: List[str] = None
    
    def __post_init__(self):
        if self.labels is None:
            self.labels = []


class QualiaPrometheusExporter:
    """
    Exportador de métricas QUALIA para Prometheus.
    
    Coleta métricas do sistema QUALIA e as expõe via endpoint HTTP
    no formato compatível com Prometheus.
    """
    
    def __init__(
        self,
        port: int = 8080,
        host: str = "0.0.0.0",
        registry: Optional[CollectorRegistry] = None
    ):
        self.port = port
        self.host = host
        self.registry = registry or REGISTRY
        self._lock = Lock()
        self._server: Optional[HTTPServer] = None
        self._server_thread: Optional[Thread] = None
        self._running = False
        
        # Métricas Prometheus
        self._metrics: Dict[str, Any] = {}
        
        # Coletores QUALIA
        self.hyperparams_telemetry = get_global_hyperparams_telemetry()
        self.metrics_collector = get_collector()
        
        self._setup_metrics()
        logger.info(f"✅ QualiaPrometheusExporter inicializado em {host}:{port}")
    
    def _setup_metrics(self):
        """Configura métricas Prometheus baseadas no sistema QUALIA."""
        
        # Métricas de Performance
        self._metrics['total_return_pct'] = Gauge(
            'qualia_hyperparams_total_return_pct',
            'Total return percentage',
            ['timeframe', 'symbol'],
            registry=self.registry
        )
        
        self._metrics['sharpe_ratio'] = Gauge(
            'qualia_hyperparams_sharpe_ratio',
            'Sharpe ratio',
            ['timeframe', 'symbol'],
            registry=self.registry
        )
        
        self._metrics['max_drawdown_pct'] = Gauge(
            'qualia_hyperparams_max_drawdown_pct',
            'Maximum drawdown percentage',
            ['timeframe', 'symbol'],
            registry=self.registry
        )
        
        self._metrics['decision_rate'] = Gauge(
            'qualia_hyperparams_decision_rate',
            'Decision rate (decisions per hour)',
            ['timeframe', 'symbol'],
            registry=self.registry
        )
        
        # Métricas de Hiperparâmetros
        self._metrics['price_amplification'] = Gauge(
            'qualia_hyperparams_price_amplification',
            'Price amplification parameter',
            ['component'],
            registry=self.registry
        )
        
        self._metrics['news_amplification'] = Gauge(
            'qualia_hyperparams_news_amplification',
            'News amplification parameter',
            ['component'],
            registry=self.registry
        )
        
        self._metrics['min_confidence'] = Gauge(
            'qualia_hyperparams_min_confidence',
            'Minimum confidence threshold',
            ['component'],
            registry=self.registry
        )
        
        self._metrics['final_confidence'] = Gauge(
            'qualia_hyperparams_final_confidence',
            'Final confidence score',
            ['component', 'symbol'],
            registry=self.registry
        )
        
        # Métricas de Decisões
        self._metrics['decisions_total'] = Counter(
            'qualia_hyperparams_decisions_total',
            'Total number of decisions made',
            ['decision_type', 'component'],
            registry=self.registry
        )
        
        # Métricas de Calibração
        self._metrics['calibrations_total'] = Counter(
            'qualia_hyperparams_calibrations_total',
            'Total number of calibrations performed',
            ['component'],
            registry=self.registry
        )
        
        self._metrics['patterns_detected'] = Gauge(
            'qualia_hyperparams_patterns_detected',
            'Number of patterns detected',
            ['component'],
            registry=self.registry
        )
        
        self._metrics['signals_generated'] = Gauge(
            'qualia_hyperparams_signals_generated',
            'Number of signals generated',
            ['component'],
            registry=self.registry
        )
        
        # Métricas de Sistema
        self._metrics['processing_latency_ms'] = Histogram(
            'qualia_hyperparams_processing_latency_ms',
            'Processing latency in milliseconds',
            ['component'],
            registry=self.registry
        )
        
        self._metrics['system_uptime_seconds'] = Gauge(
            'qualia_system_uptime_seconds',
            'System uptime in seconds',
            registry=self.registry
        )
        
        logger.info("📊 Métricas Prometheus configuradas")
    
    def update_metrics(self):
        """Atualiza métricas Prometheus com dados atuais do QUALIA."""
        try:
            with self._lock:
                # Obter eventos recentes de telemetria
                recent_events = list(self.hyperparams_telemetry.events)[-100:]  # Últimos 100 eventos
                
                if not recent_events:
                    return
                
                # Calcular métricas agregadas
                latest_event = recent_events[-1]
                
                # Atualizar métricas de hiperparâmetros
                if latest_event.price_amplification is not None:
                    self._metrics['price_amplification'].labels(
                        component=latest_event.component
                    ).set(latest_event.price_amplification)
                
                if latest_event.news_amplification is not None:
                    self._metrics['news_amplification'].labels(
                        component=latest_event.component
                    ).set(latest_event.news_amplification)
                
                if latest_event.min_confidence is not None:
                    self._metrics['min_confidence'].labels(
                        component=latest_event.component
                    ).set(latest_event.min_confidence)
                
                if latest_event.confidence is not None:
                    self._metrics['final_confidence'].labels(
                        component=latest_event.component,
                        symbol=latest_event.symbol or "unknown"
                    ).set(latest_event.confidence)
                
                # Contar decisões por tipo
                decision_counts = {"buy": 0, "sell": 0, "hold": 0}
                for event in recent_events:
                    if event.decision in decision_counts:
                        decision_counts[event.decision] += 1
                
                for decision_type, count in decision_counts.items():
                    self._metrics['decisions_total'].labels(
                        decision_type=decision_type,
                        component=latest_event.component
                    )._value._value = count
                
                # Métricas de sistema
                self._metrics['system_uptime_seconds'].set(time.time() - self._start_time)
                
                logger.debug("📊 Métricas Prometheus atualizadas")
                
        except Exception as e:
            logger.error(f"❌ Erro atualizando métricas Prometheus: {e}")
    
    def start(self):
        """Inicia o servidor HTTP de métricas."""
        if self._running:
            logger.warning("⚠️ Servidor Prometheus já está rodando")
            return
        
        self._start_time = time.time()
        
        class MetricsHandler(BaseHTTPRequestHandler):
            def __init__(self, exporter):
                self.exporter = exporter
                super().__init__()
            
            def __call__(self, *args, **kwargs):
                self.exporter = self.exporter
                super().__init__(*args, **kwargs)
            
            def do_GET(self):
                if self.path == '/metrics':
                    # Atualizar métricas antes de servir
                    self.exporter.update_metrics()
                    
                    # Gerar resposta Prometheus
                    output = generate_latest(self.exporter.registry)
                    
                    self.send_response(200)
                    self.send_header('Content-Type', CONTENT_TYPE_LATEST)
                    self.end_headers()
                    self.wfile.write(output)
                elif self.path == '/health':
                    self.send_response(200)
                    self.send_header('Content-Type', 'application/json')
                    self.end_headers()
                    health_data = {
                        "status": "healthy",
                        "uptime": time.time() - self.exporter._start_time,
                        "metrics_count": len(self.exporter._metrics)
                    }
                    self.wfile.write(json.dumps(health_data).encode())
                else:
                    self.send_response(404)
                    self.end_headers()
            
            def log_message(self, format, *args):
                # Suprimir logs HTTP padrão
                pass
        
        # Criar handler com referência ao exporter
        handler = lambda *args, **kwargs: MetricsHandler(self)(*args, **kwargs)
        
        try:
            self._server = HTTPServer((self.host, self.port), handler)
            self._server_thread = Thread(target=self._server.serve_forever, daemon=True)
            self._server_thread.start()
            self._running = True
            
            logger.info(f"🚀 Servidor Prometheus iniciado em http://{self.host}:{self.port}/metrics")
            
        except Exception as e:
            logger.error(f"❌ Erro iniciando servidor Prometheus: {e}")
            raise
    
    def stop(self):
        """Para o servidor HTTP de métricas."""
        if not self._running:
            return
        
        if self._server:
            self._server.shutdown()
            self._server.server_close()
        
        if self._server_thread:
            self._server_thread.join(timeout=5)
        
        self._running = False
        logger.info("🛑 Servidor Prometheus parado")
    
    def is_running(self) -> bool:
        """Verifica se o servidor está rodando."""
        return self._running


# Instância global
_global_prometheus_exporter: Optional[QualiaPrometheusExporter] = None


def get_prometheus_exporter(port: int = 8080) -> QualiaPrometheusExporter:
    """Retorna instância global do exportador Prometheus."""
    global _global_prometheus_exporter
    if _global_prometheus_exporter is None:
        _global_prometheus_exporter = QualiaPrometheusExporter(port=port)
    return _global_prometheus_exporter


__all__ = [
    "QualiaPrometheusExporter",
    "get_prometheus_exporter"
]
