# Double-Check de Integração do módulo `src/qualia/memory`

Este documento avalia como o pacote `memory` se integra ao ecossistema QUALIA e apresenta oportunidades de melhoria.

## Integração Sistêmica
- **Event-driven**: não há publicação ou consumo de eventos via `qualia.events`.
- **Configuração**: dependência de arquivos YAML (`warmstart.yaml`, `strategy_parameters.json`) sem suporte a feature flags.
- **Observabilidade**: há métrica StatsD opcional (`qpm_metrics_enabled`), mas falta tracing OpenTelemetry.
- **Segurança**: não manipula chaves de API diretamente; persistência não criptografada.

## Performance & Escalabilidade
- **Benchmark**: não existem testes `pytest-benchmark` dedicados aos principais métodos.
- **Paralelismo**: operações CPU‑bound; suporte opcional a `asyncio.Lock`, porém sem off‑load para GPU/QPU.

## Riscos Identificados
| Gravidade | Esforço | Descrição |
|-----------|--------|-----------|
| Alta | Médio | Falta de integração com o Event Bus reduz acoplamento com o restante do pipeline. |
| Média | Baixo | Configurações em JSON dificultam override por ambiente e rollout progressivo. |
| Média | Médio | Ausência de tracing torna difícil depurar gargalos de acesso à memória. |
| Baixa | Baixo | Persistência local pode expor dados sensíveis sem criptografia. |

## Quick Wins ⚡
- [x] #17 Adicionar publicações de evento `memory.update` a cada armazenamento ou limpeza de padrões.
- [x] #17 Converter `warmstart.json` para YAML e permitir override via `QPM_WARMSTART_CONFIG`. (concluído em 2025-06-15)
- [x] #17 Implementar benchmark simples para `retrieve_similar_patterns` usando `pytest-benchmark`.

## Features de Valor
1. **Integração com QuantumPatternMemory adaptativa**
   - *User Story*: Como estrategista, desejo ajustar dinamicamente o `similarity_threshold` via eventos para otimizar a recuperação de padrões.
   - *Estimativa*: 5 dias.
2. **Hooks de Off-loading para GPU/QPU**
   - *User Story*: Como desenvolvedor, quero processar a normalização de vetores em hardware acelerado quando disponível.
   - *Estimativa*: 7 dias.
3. **Tracing completo em OpenTelemetry**
   - *User Story*: Como analista, preciso visualizar o caminho `symbol → store_pattern → retrieve` no Grafana.
   - *Estimativa*: 3 dias.

Todos os riscos de Alta Gravidade devem gerar tickets com responsável e prazo definido.
