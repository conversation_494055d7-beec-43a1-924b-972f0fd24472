#!/usr/bin/env python3
"""
Teste das Descobertas de Otimização - Validação
YAA IMPLEMENTATION: Valida se as descobertas importantes foram incorporadas.
"""

import sys
import json
from pathlib import Path

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

from src.production_optimizer import ProductionOptimizer

def test_optimization_discoveries():
    """Testa se as descobertas de otimização foram incorporadas."""
    
    print("🔍 VALIDANDO DESCOBERTAS DE OTIMIZAÇÃO")
    print("=" * 60)
    
    # Carrega configuração
    optimizer = ProductionOptimizer()
    config_file = "config/production_config.json"
    
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    # Valores esperados baseados nas descobertas
    expected_values = {
        'news_amplification': 11.3,      # +463.2% descoberto
        'price_amplification': 1.0,      # -51.4% descoberto  
        'min_confidence': 0.37           # -7.0% desco<PERSON>
    }
    
    print("📊 VALIDAÇÃO DOS VALORES OTIMIZADOS:")
    print("-" * 40)
    
    base_params = config['base_params']
    all_correct = True
    
    for param, expected in expected_values.items():
        actual = base_params[param]
        tolerance = 0.1  # 10% de tolerância
        
        if abs(actual - expected) <= tolerance:
            status = "✅"
            all_correct = True
        else:
            status = "❌"
            all_correct = False
        
        print(f"   {status} {param}:")
        print(f"      Esperado: {expected}")
        print(f"      Atual: {actual}")
        print(f"      Diferença: {abs(actual - expected):.3f}")
        print()
    
    # Verifica ranges focados
    print("🎯 VALIDAÇÃO DOS RANGES FOCADOS:")
    print("-" * 40)
    
    param_ranges = config['parameter_ranges']
    
    # news_amplification deve ter range focado ao redor de 11.3
    news_range = param_ranges['news_amplification']
    if 8.0 <= news_range['min'] <= 10.0 and 13.0 <= news_range['max'] <= 16.0:
        print("   ✅ news_amplification: Range focado corretamente")
        print(f"      Range: {news_range['min']} - {news_range['max']}")
    else:
        print("   ❌ news_amplification: Range não focado")
        all_correct = False
    
    # price_amplification deve ter range focado ao redor de 1.0
    price_range = param_ranges['price_amplification']
    if 0.4 <= price_range['min'] <= 0.7 and 1.5 <= price_range['max'] <= 2.5:
        print("   ✅ price_amplification: Range focado corretamente")
        print(f"      Range: {price_range['min']} - {price_range['max']}")
    else:
        print("   ❌ price_amplification: Range não focado")
        all_correct = False
    
    # min_confidence deve ter range focado ao redor de 0.37
    conf_range = param_ranges['min_confidence']
    if 0.25 <= conf_range['min'] <= 0.35 and 0.40 <= conf_range['max'] <= 0.50:
        print("   ✅ min_confidence: Range focado corretamente")
        print(f"      Range: {conf_range['min']} - {conf_range['max']}")
    else:
        print("   ❌ min_confidence: Range não focado")
        all_correct = False
    
    # Verifica configuração de convergência rápida
    print("\n⚡ VALIDAÇÃO DA CONVERGÊNCIA RÁPIDA:")
    print("-" * 40)
    
    opt_settings = config['optimization_settings']
    
    # Startup trials reduzido para convergência rápida
    startup_trials = opt_settings['n_startup_trials']
    if startup_trials <= 5:
        print(f"   ✅ n_startup_trials reduzido: {startup_trials}")
    else:
        print(f"   ❌ n_startup_trials não reduzido: {startup_trials}")
        all_correct = False
    
    # Warmup steps reduzido
    warmup_steps = opt_settings['n_warmup_steps']
    if warmup_steps <= 3:
        print(f"   ✅ n_warmup_steps reduzido: {warmup_steps}")
    else:
        print(f"   ❌ n_warmup_steps não reduzido: {warmup_steps}")
        all_correct = False
    
    # Verifica documentação das descobertas
    print("\n📚 VALIDAÇÃO DA DOCUMENTAÇÃO:")
    print("-" * 40)
    
    if 'optimization_discoveries' in config:
        discoveries = config['optimization_discoveries']
        print("   ✅ Descobertas documentadas na configuração:")
        for key, value in discoveries.items():
            print(f"      • {key}: {value}")
    else:
        print("   ❌ Descobertas não documentadas na configuração")
        all_correct = False
    
    # Verifica arquivo de documentação
    docs_file = Path("docs/optimization_discoveries.md")
    if docs_file.exists():
        print("   ✅ Documentação detalhada criada: docs/optimization_discoveries.md")
    else:
        print("   ❌ Documentação detalhada não encontrada")
        all_correct = False
    
    # Resultado final
    print("\n" + "=" * 60)
    print("🎯 RESULTADO DA VALIDAÇÃO:")
    print("=" * 60)
    
    if all_correct:
        print("🎉 TODAS AS DESCOBERTAS FORAM INCORPORADAS CORRETAMENTE!")
        print("\n✅ Validações Aprovadas:")
        print("   • Valores otimizados aplicados nos base_params")
        print("   • Ranges focados ao redor dos valores ótimos")
        print("   • Configuração para convergência rápida")
        print("   • Documentação completa das descobertas")
        
        print("\n🚀 IMPACTO ESPERADO:")
        print("   • news_amplification: +463.2% de impacto")
        print("   • price_amplification: -51.4% otimizado")
        print("   • min_confidence: -7.0% ajustado")
        print("   • Convergência rápida implementada")
        
        return True
    else:
        print("❌ ALGUMAS DESCOBERTAS NÃO FORAM INCORPORADAS")
        print("\n⚠️ Verificar:")
        print("   • Valores base_params")
        print("   • Ranges de parâmetros")
        print("   • Configurações de otimização")
        print("   • Documentação")
        
        return False

def main():
    """Executa validação das descobertas."""
    
    print("🧪 TESTE DE VALIDAÇÃO - DESCOBERTAS DE OTIMIZAÇÃO")
    print("=" * 70)
    
    try:
        success = test_optimization_discoveries()
        
        if success:
            print("\n🏆 SISTEMA OTIMIZADO COM BASE NAS DESCOBERTAS!")
            print("✅ Pronto para produção com valores otimizados")
        else:
            print("\n⚠️ AJUSTES NECESSÁRIOS")
            print("❌ Revisar incorporação das descobertas")
        
        return success
        
    except Exception as e:
        print(f"\n❌ Erro na validação: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
