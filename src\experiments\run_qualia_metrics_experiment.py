"""Helper utilities for exploring Qualia metrics."""

from __future__ import annotations

import numpy as np
import pandas as pd


def correlation_with_vol_future(
    df: pd.DataFrame, target_column: str = "vol_future"
) -> pd.DataFrame:
    """Return correlations with ``target_column`` for non-constant numeric columns.

    Parameters
    ----------
    df
        DataFrame containing feature columns and ``target_column``.
    target_column
        Name of the target volatility column. Defaults to ``"vol_future"``.

    Returns
    -------
    pandas.DataFrame
        A DataFrame with correlations to ``target_column`` or an empty DataFrame
        if the column has no variance.

    Raises
    ------
    KeyError
        If ``target_column`` does not exist in ``df``.
    """

    numeric_df = df.select_dtypes(include=[np.number])
    if target_column not in numeric_df.columns:
        raise KeyError(f"{target_column} column is missing")

    non_constant_cols = [
        col for col in numeric_df.columns if numeric_df[col].nunique() > 1
    ]

    if target_column not in non_constant_cols:
        return pd.DataFrame()

    return numeric_df[non_constant_cols].corr()[[target_column]]
