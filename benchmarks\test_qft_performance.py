import pytest

pytest.importorskip("pytest_benchmark")
from qualia.core.universe import QUALIAQuantumUniverse

# Parâmetros default para QUALIAQuantumUniverse, consistentes com outros testes
# Alguns podem não impactar diretamente o tempo de criação do estado inicial QFT,
# mas são necessários para a instanciação.
DEFAULT_SCR_DEPTH = 1
DEFAULT_BASE_LAMBDA = 0.5
DEFAULT_ALPHA = 0.1
DEFAULT_RETRO_STRENGTH = 0.0
DEFAULT_NUM_CTC_QUBITS = 0
ALLOW_QFT_FALLBACK = False  # Queremos testar o QFT puro

# Valores de n_qubits para o benchmark
# Incluindo valores menores para referência e maiores para testar os limites.
N_QUBITS_VALUES = [5, 8, 10, 12]


@pytest.mark.parametrize("n_qubits", N_QUBITS_VALUES)
def test_qft_initial_state_creation_performance(benchmark, n_qubits):
    """
    Benchmark para a criação do estado inicial QFT em QUALIAQuantumUniverse
    para diferentes números de qubits.
    """

    # A função a ser benchmarkada é a instanciação de QUALIAQuantumUniverse
    # com initial_state_type="qft".
    # O pytest-benchmark chamará isso múltiplas vezes.

    # Usamos benchmark.pedantic para passar argumentos para a função alvo
    # e para ter um controle mais fino, como número de rounds.
    # Se for apenas benchmark(callable_func, *args, **kwargs), ele executa um número
    # auto-ajustado de iterações.

    result = benchmark(
        QUALIAQuantumUniverse,
        n_qubits=n_qubits,
        scr_depth=DEFAULT_SCR_DEPTH,
        base_lambda=DEFAULT_BASE_LAMBDA,
        alpha=DEFAULT_ALPHA,
        retro_strength=DEFAULT_RETRO_STRENGTH,
        num_ctc_qubits=DEFAULT_NUM_CTC_QUBITS,
        initial_state_type="qft",
        allow_qft_fallback=ALLOW_QFT_FALLBACK,
        # Outros parâmetros que podem ser relevantes ou são obrigatórios
        # com seus valores default, se houver.
        # Ex: backend_type, qpu_config, etc., se QUALIAQuantumUniverse os exigir.
        # Por ora, os parâmetros acima são os identificados como principais
        # no construtor pela análise anterior.
    )

    # Opcionalmente, podemos fazer um assert simples para garantir que algo foi criado,
    # embora o foco do benchmark não seja a correção funcional (já coberta por outros testes).
    assert result is not None
    assert result.n_qubits == n_qubits
    assert result.initial_state_type == "qft"


# Nota: Se a inicialização de QUALIAQuantumUniverse em si tiver efeitos colaterais
# que não deveriam ser repetidos a cada chamada do benchmark (ex: logging global,
# criação de arquivos uma única vez), pode ser necessário usar benchmark.pedantic
# com uma função wrapper e setup/teardown, mas para a instanciação de classe,
# benchmark(ClassName, *init_args) é geralmente adequado.
