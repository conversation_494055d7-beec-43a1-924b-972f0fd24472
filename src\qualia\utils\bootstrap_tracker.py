"""Bootstrap progress tracking with granular metrics and convergence analysis.

This module provides comprehensive tracking and monitoring of the QUALIA system
bootstrap process, including:
- Granular progress tracking across bootstrap phases
- Convergence metrics and analysis
- Consciousness level progression monitoring
- Adaptive threshold calibration
- Bootstrap performance analytics
- Early termination detection
"""

from __future__ import annotations

import asyncio
import time
import statistics
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from collections import deque, defaultdict

from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class BootstrapPhase(Enum):
    """Bootstrap phases for granular tracking."""
    INITIALIZATION = "initialization"
    DATA_WARMUP = "data_warmup"
    STRATEGY_CREATION = "strategy_creation"
    CONSCIOUSNESS_AWAKENING = "consciousness_awakening"
    DECISION_GENERATION = "decision_generation"
    CONVERGENCE_TESTING = "convergence_testing"
    STABILIZATION = "stabilization"
    COMPLETION = "completion"


class ConvergenceStatus(Enum):
    """Convergence status indicators."""
    NOT_STARTED = "not_started"
    PROGRESSING = "progressing"
    CONVERGING = "converging"
    CONVERGED = "converged"
    DIVERGING = "diverging"
    STALLED = "stalled"


@dataclass
class BootstrapMetrics:
    """Comprehensive bootstrap metrics."""
    phase: BootstrapPhase
    progress_percentage: float
    consciousness_level: float
    quantum_coherence: float
    temporal_coherence: float
    decision_quality: float
    convergence_rate: float
    stability_score: float
    timestamp: float
    
    # Phase-specific metrics
    data_completeness: float = 0.0
    strategy_readiness: float = 0.0
    memory_coherence: float = 0.0
    risk_calibration: float = 0.0


@dataclass
class ConvergenceAnalysis:
    """Analysis of bootstrap convergence patterns."""
    status: ConvergenceStatus
    convergence_rate: float
    stability_trend: float
    oscillation_amplitude: float
    time_to_convergence_estimate: float
    confidence_level: float
    
    # Convergence criteria
    target_consciousness: float = 0.65
    target_coherence: float = 0.55
    target_stability: float = 0.8
    max_oscillation: float = 0.1


class BootstrapProgressTracker:
    """Advanced bootstrap progress tracker with convergence analysis.
    
    This tracker provides:
    - Granular phase-by-phase progress monitoring
    - Real-time convergence analysis
    - Adaptive threshold calibration
    - Performance trend analysis
    - Early termination detection
    - Comprehensive bootstrap analytics
    """
    
    def __init__(
        self,
        name: str = "bootstrap_tracker",
        target_bootstrap_cycles: int = 10,
        convergence_window: int = 20,
        stability_threshold: float = 0.8,
        min_consciousness_level: float = 0.65,
        enable_early_termination: bool = True,
        adaptive_thresholds: bool = True,
    ):
        self.name = name
        self.target_bootstrap_cycles = target_bootstrap_cycles
        self.convergence_window = convergence_window
        self.stability_threshold = stability_threshold
        self.min_consciousness_level = min_consciousness_level
        self.enable_early_termination = enable_early_termination
        self.adaptive_thresholds = adaptive_thresholds
        
        # Current state
        self.current_phase = BootstrapPhase.INITIALIZATION
        self.current_cycle = 0
        self.is_active = False
        self.start_time: Optional[float] = None
        self.completion_time: Optional[float] = None
        
        # Progress tracking
        self.phase_progress: Dict[BootstrapPhase, float] = {
            phase: 0.0 for phase in BootstrapPhase
        }
        self.phase_completion_times: Dict[BootstrapPhase, float] = {}
        
        # Metrics history
        self.metrics_history: deque = deque(maxlen=1000)
        self.consciousness_history: deque = deque(maxlen=100)
        self.coherence_history: deque = deque(maxlen=100)
        self.decision_quality_history: deque = deque(maxlen=100)
        
        # Convergence analysis
        self.convergence_analyzer = ConvergenceAnalyzer(
            window_size=convergence_window,
            stability_threshold=stability_threshold
        )
        
        # Adaptive thresholds
        self.dynamic_thresholds = {
            'consciousness_target': min_consciousness_level,
            'coherence_target': 0.55,
            'stability_target': stability_threshold,
            'quality_target': 0.7
        }
        
        # Performance callbacks
        self.phase_callbacks: Dict[BootstrapPhase, List[Callable]] = defaultdict(list)
        self.convergence_callbacks: List[Callable] = []
        
        # Adaptive learning rate system
        self.learning_rate_manager = AdaptiveLearningRateManager(
            initial_rate=0.1,
            min_rate=0.01,
            max_rate=0.5,
            adaptation_window=10
        )

        # Statistics
        self.stats = {
            'total_cycles': 0,
            'successful_cycles': 0,
            'failed_cycles': 0,
            'early_terminations': 0,
            'threshold_adaptations': 0,
            'convergence_detections': 0,
            'learning_rate_adaptations': 0
        }

    def start_bootstrap(self) -> None:
        """Start bootstrap tracking."""
        if self.is_active:
            logger.warning("Bootstrap tracking already active")
            return
        
        self.is_active = True
        self.start_time = time.time()
        self.current_cycle = 0
        self.current_phase = BootstrapPhase.INITIALIZATION
        
        # Reset state
        self.phase_progress = {phase: 0.0 for phase in BootstrapPhase}
        self.phase_completion_times.clear()
        self.metrics_history.clear()
        self.consciousness_history.clear()
        self.coherence_history.clear()
        self.decision_quality_history.clear()
        
        logger.info(f"Bootstrap tracking started - target cycles: {self.target_bootstrap_cycles}")

    def update_phase(self, phase: BootstrapPhase, progress: float = 0.0) -> None:
        """Update current bootstrap phase and progress.
        
        Parameters
        ----------
        phase : BootstrapPhase
            Current bootstrap phase
        progress : float
            Progress within the phase (0.0 to 1.0)
        """
        if not self.is_active:
            return
        
        # Mark previous phase as complete if moving to new phase
        if phase != self.current_phase and self.current_phase not in self.phase_completion_times:
            self.phase_completion_times[self.current_phase] = time.time()
            self.phase_progress[self.current_phase] = 1.0
            
            # Trigger phase completion callbacks
            for callback in self.phase_callbacks[self.current_phase]:
                try:
                    callback(self.current_phase, self.get_phase_metrics(self.current_phase))
                except Exception as e:
                    logger.warning(f"Phase callback failed: {e}")
        
        self.current_phase = phase
        self.phase_progress[phase] = max(0.0, min(1.0, progress))
        
        logger.debug(f"Bootstrap phase updated: {phase.value} ({progress:.1%})")

    def record_metrics(
        self,
        consciousness_level: float,
        quantum_coherence: float,
        temporal_coherence: float,
        decision_quality: float = 0.0,
        **kwargs
    ) -> None:
        """Record bootstrap metrics for analysis.
        
        Parameters
        ----------
        consciousness_level : float
            Current consciousness level
        quantum_coherence : float
            Quantum coherence measurement
        temporal_coherence : float
            Temporal coherence measurement
        decision_quality : float
            Quality of decisions being generated
        **kwargs
            Additional phase-specific metrics
        """
        if not self.is_active:
            return
        
        current_time = time.time()
        
        # Calculate derived metrics
        convergence_rate = self._calculate_convergence_rate()
        stability_score = self._calculate_stability_score()
        overall_progress = self._calculate_overall_progress()
        
        # Create metrics record
        metrics = BootstrapMetrics(
            phase=self.current_phase,
            progress_percentage=overall_progress,
            consciousness_level=consciousness_level,
            quantum_coherence=quantum_coherence,
            temporal_coherence=temporal_coherence,
            decision_quality=decision_quality,
            convergence_rate=convergence_rate,
            stability_score=stability_score,
            timestamp=current_time,
            **kwargs
        )
        
        # Store metrics
        self.metrics_history.append(metrics)
        self.consciousness_history.append(consciousness_level)
        self.coherence_history.append(quantum_coherence)
        self.decision_quality_history.append(decision_quality)
        
        # Update convergence analyzer
        self.convergence_analyzer.add_measurement(
            consciousness_level, quantum_coherence, temporal_coherence, stability_score
        )
        
        # Check for convergence
        convergence_analysis = self.convergence_analyzer.analyze_convergence()
        
        if convergence_analysis.status == ConvergenceStatus.CONVERGED:
            self._handle_convergence_detected(convergence_analysis)
        
        # Adapt thresholds if enabled
        if self.adaptive_thresholds:
            self._adapt_thresholds(metrics)

        # Update learning rate based on performance
        self.learning_rate_manager.update_performance(
            consciousness_level, quantum_coherence, stability_score
        )

        logger.debug(
            f"Bootstrap metrics recorded: consciousness={consciousness_level:.3f}, "
            f"coherence={quantum_coherence:.3f}, stability={stability_score:.3f}, "
            f"learning_rate={self.learning_rate_manager.current_rate:.4f}"
        )

    def increment_cycle(self) -> bool:
        """Increment bootstrap cycle and check for completion.
        
        Returns
        -------
        bool
            True if bootstrap should continue, False if complete
        """
        if not self.is_active:
            return False
        
        self.current_cycle += 1
        self.stats['total_cycles'] += 1
        
        # Check for early termination
        if self.enable_early_termination and self._should_terminate_early():
            self.stats['early_terminations'] += 1
            logger.info(f"Early termination detected at cycle {self.current_cycle}")
            self._complete_bootstrap(early_termination=True)
            return False
        
        # Check for normal completion
        if self.current_cycle >= self.target_bootstrap_cycles:
            logger.info(f"Bootstrap completed after {self.current_cycle} cycles")
            self._complete_bootstrap(early_termination=False)
            return False
        
        logger.debug(f"Bootstrap cycle {self.current_cycle}/{self.target_bootstrap_cycles}")
        return True

    def _calculate_convergence_rate(self) -> float:
        """Calculate current convergence rate."""
        if len(self.consciousness_history) < 5:
            return 0.0
        
        recent_values = list(self.consciousness_history)[-5:]
        if len(set(recent_values)) == 1:  # All values are the same
            return 1.0
        
        # Calculate trend slope
        x_values = list(range(len(recent_values)))
        try:
            # Simple linear regression slope
            n = len(recent_values)
            sum_x = sum(x_values)
            sum_y = sum(recent_values)
            sum_xy = sum(x * y for x, y in zip(x_values, recent_values))
            sum_x2 = sum(x * x for x in x_values)
            
            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
            
            # Normalize slope to 0-1 range
            return max(0.0, min(1.0, slope + 0.5))
        except:
            return 0.0

    def _calculate_stability_score(self) -> float:
        """Calculate current stability score."""
        if len(self.consciousness_history) < 3:
            return 0.0
        
        recent_values = list(self.consciousness_history)[-10:]
        
        # Calculate coefficient of variation (lower = more stable)
        if len(recent_values) > 1:
            mean_val = statistics.mean(recent_values)
            if mean_val > 0:
                std_val = statistics.stdev(recent_values)
                cv = std_val / mean_val
                # Convert to stability score (higher = more stable)
                return max(0.0, 1.0 - cv)
        
        return 0.5

    def _calculate_overall_progress(self) -> float:
        """Calculate overall bootstrap progress percentage."""
        # Weight phases by importance
        phase_weights = {
            BootstrapPhase.INITIALIZATION: 0.05,
            BootstrapPhase.DATA_WARMUP: 0.15,
            BootstrapPhase.STRATEGY_CREATION: 0.15,
            BootstrapPhase.CONSCIOUSNESS_AWAKENING: 0.20,
            BootstrapPhase.DECISION_GENERATION: 0.20,
            BootstrapPhase.CONVERGENCE_TESTING: 0.15,
            BootstrapPhase.STABILIZATION: 0.10,
            BootstrapPhase.COMPLETION: 0.0
        }
        
        total_progress = 0.0
        for phase, weight in phase_weights.items():
            total_progress += self.phase_progress[phase] * weight
        
        # Add cycle progress
        cycle_progress = (self.current_cycle / self.target_bootstrap_cycles) * 0.3
        total_progress += cycle_progress
        
        return min(1.0, total_progress)

    def _should_terminate_early(self) -> bool:
        """Check if bootstrap should terminate early due to convergence."""
        if len(self.metrics_history) < 10:
            return False
        
        convergence_analysis = self.convergence_analyzer.analyze_convergence()
        
        # Check convergence criteria
        if convergence_analysis.status == ConvergenceStatus.CONVERGED:
            recent_metrics = list(self.metrics_history)[-5:]
            
            # All recent metrics should meet thresholds
            meets_consciousness = all(
                m.consciousness_level >= self.dynamic_thresholds['consciousness_target']
                for m in recent_metrics
            )
            meets_coherence = all(
                m.quantum_coherence >= self.dynamic_thresholds['coherence_target']
                for m in recent_metrics
            )
            meets_stability = all(
                m.stability_score >= self.dynamic_thresholds['stability_target']
                for m in recent_metrics
            )
            
            return meets_consciousness and meets_coherence and meets_stability
        
        return False

    def _adapt_thresholds(self, metrics: BootstrapMetrics) -> None:
        """Adapt thresholds based on system performance."""
        if len(self.metrics_history) < 20:
            return
        
        recent_metrics = list(self.metrics_history)[-20:]
        
        # Calculate performance trends
        consciousness_trend = statistics.mean([m.consciousness_level for m in recent_metrics])
        coherence_trend = statistics.mean([m.quantum_coherence for m in recent_metrics])
        stability_trend = statistics.mean([m.stability_score for m in recent_metrics])
        
        # Adapt thresholds based on achievable performance
        adaptation_rate = 0.05
        
        if consciousness_trend > self.dynamic_thresholds['consciousness_target'] * 1.1:
            # Performance is consistently above target, can raise threshold
            self.dynamic_thresholds['consciousness_target'] += adaptation_rate
            self.stats['threshold_adaptations'] += 1
        elif consciousness_trend < self.dynamic_thresholds['consciousness_target'] * 0.9:
            # Performance is consistently below target, lower threshold
            self.dynamic_thresholds['consciousness_target'] -= adaptation_rate
            self.stats['threshold_adaptations'] += 1
        
        # Similar logic for other thresholds
        if coherence_trend > self.dynamic_thresholds['coherence_target'] * 1.1:
            self.dynamic_thresholds['coherence_target'] += adaptation_rate
        elif coherence_trend < self.dynamic_thresholds['coherence_target'] * 0.9:
            self.dynamic_thresholds['coherence_target'] -= adaptation_rate
        
        # Ensure thresholds stay within reasonable bounds
        self.dynamic_thresholds['consciousness_target'] = max(0.3, min(0.9, self.dynamic_thresholds['consciousness_target']))
        self.dynamic_thresholds['coherence_target'] = max(0.2, min(0.8, self.dynamic_thresholds['coherence_target']))
        self.dynamic_thresholds['stability_target'] = max(0.5, min(0.95, self.dynamic_thresholds['stability_target']))

    def _handle_convergence_detected(self, analysis: ConvergenceAnalysis) -> None:
        """Handle convergence detection."""
        self.stats['convergence_detections'] += 1
        
        logger.info(
            f"Convergence detected: rate={analysis.convergence_rate:.3f}, "
            f"stability={analysis.stability_trend:.3f}, confidence={analysis.confidence_level:.3f}"
        )
        
        # Trigger convergence callbacks
        for callback in self.convergence_callbacks:
            try:
                callback(analysis)
            except Exception as e:
                logger.warning(f"Convergence callback failed: {e}")

    def _complete_bootstrap(self, early_termination: bool = False) -> None:
        """Complete bootstrap process."""
        self.is_active = False
        self.completion_time = time.time()
        self.current_phase = BootstrapPhase.COMPLETION
        self.phase_progress[BootstrapPhase.COMPLETION] = 1.0
        
        if early_termination:
            self.stats['successful_cycles'] += 1
            logger.info("Bootstrap completed successfully via early termination")
        else:
            self.stats['successful_cycles'] += 1
            logger.info("Bootstrap completed successfully after full cycle count")

    def get_current_status(self) -> Dict[str, Any]:
        """Get current bootstrap status."""
        if not self.is_active and self.completion_time is None:
            return {'status': 'not_started'}
        
        convergence_analysis = self.convergence_analyzer.analyze_convergence()
        
        return {
            'status': 'active' if self.is_active else 'completed',
            'current_phase': self.current_phase.value,
            'current_cycle': self.current_cycle,
            'target_cycles': self.target_bootstrap_cycles,
            'overall_progress': self._calculate_overall_progress(),
            'phase_progress': {phase.value: progress for phase, progress in self.phase_progress.items()},
            'convergence_status': convergence_analysis.status.value,
            'convergence_confidence': convergence_analysis.confidence_level,
            'time_to_convergence_estimate': convergence_analysis.time_to_convergence_estimate,
            'dynamic_thresholds': self.dynamic_thresholds.copy(),
            'elapsed_time': (time.time() - self.start_time) if self.start_time else 0,
            'recent_metrics': {
                'consciousness': list(self.consciousness_history)[-5:] if self.consciousness_history else [],
                'coherence': list(self.coherence_history)[-5:] if self.coherence_history else [],
                'decision_quality': list(self.decision_quality_history)[-5:] if self.decision_quality_history else []
            }
        }

    def get_phase_metrics(self, phase: BootstrapPhase) -> Dict[str, Any]:
        """Get metrics for a specific phase."""
        phase_metrics = [m for m in self.metrics_history if m.phase == phase]
        
        if not phase_metrics:
            return {'phase': phase.value, 'metrics_count': 0}
        
        return {
            'phase': phase.value,
            'metrics_count': len(phase_metrics),
            'average_consciousness': statistics.mean([m.consciousness_level for m in phase_metrics]),
            'average_coherence': statistics.mean([m.quantum_coherence for m in phase_metrics]),
            'average_stability': statistics.mean([m.stability_score for m in phase_metrics]),
            'completion_time': self.phase_completion_times.get(phase),
            'progress': self.phase_progress[phase]
        }

    def add_phase_callback(self, phase: BootstrapPhase, callback: Callable) -> None:
        """Add callback for phase completion."""
        self.phase_callbacks[phase].append(callback)

    def add_convergence_callback(self, callback: Callable) -> None:
        """Add callback for convergence detection."""
        self.convergence_callbacks.append(callback)

    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get comprehensive bootstrap statistics."""
        duration = (self.completion_time or time.time()) - (self.start_time or time.time())
        
        return {
            'name': self.name,
            'is_active': self.is_active,
            'duration': duration,
            'stats': self.stats,
            'phase_completion_times': {
                phase.value: completion_time 
                for phase, completion_time in self.phase_completion_times.items()
            },
            'convergence_analysis': self.convergence_analyzer.analyze_convergence().__dict__,
            'dynamic_thresholds': self.dynamic_thresholds,
            'metrics_collected': len(self.metrics_history)
        }


class ConvergenceAnalyzer:
    """Analyzer for bootstrap convergence patterns."""
    
    def __init__(self, window_size: int = 20, stability_threshold: float = 0.8):
        self.window_size = window_size
        self.stability_threshold = stability_threshold
        self.measurements: deque = deque(maxlen=window_size)
    
    def add_measurement(self, consciousness: float, coherence: float, temporal: float, stability: float) -> None:
        """Add measurement for convergence analysis."""
        self.measurements.append({
            'timestamp': time.time(),
            'consciousness': consciousness,
            'coherence': coherence,
            'temporal': temporal,
            'stability': stability
        })
    
    def analyze_convergence(self) -> ConvergenceAnalysis:
        """Analyze convergence status and patterns."""
        if len(self.measurements) < 5:
            return ConvergenceAnalysis(
                status=ConvergenceStatus.NOT_STARTED,
                convergence_rate=0.0,
                stability_trend=0.0,
                oscillation_amplitude=0.0,
                time_to_convergence_estimate=float('inf'),
                confidence_level=0.0
            )
        
        recent_measurements = list(self.measurements)
        
        # Calculate trends
        consciousness_values = [m['consciousness'] for m in recent_measurements]
        stability_values = [m['stability'] for m in recent_measurements]
        
        # Determine convergence status
        status = self._determine_convergence_status(consciousness_values, stability_values)
        
        # Calculate metrics
        convergence_rate = self._calculate_convergence_rate(consciousness_values)
        stability_trend = statistics.mean(stability_values[-5:]) if len(stability_values) >= 5 else 0.0
        oscillation_amplitude = self._calculate_oscillation_amplitude(consciousness_values)
        time_estimate = self._estimate_time_to_convergence(consciousness_values, convergence_rate)
        confidence = self._calculate_confidence(status, stability_trend, oscillation_amplitude)

        return ConvergenceAnalysis(
            status=status,
            convergence_rate=convergence_rate,
            stability_trend=stability_trend,
            oscillation_amplitude=oscillation_amplitude,
            time_to_convergence_estimate=time_estimate,
            confidence_level=confidence
        )

    def get_learning_rate(self) -> float:
        """Get current adaptive learning rate."""
        return self.learning_rate_manager.current_rate

    def get_learning_rate_stats(self) -> Dict[str, Any]:
        """Get learning rate adaptation statistics."""
        return self.learning_rate_manager.get_stats()


class AdaptiveLearningRateManager:
    """Manages adaptive learning rate during bootstrap based on performance and stability.

    The learning rate adapts based on:
    - Performance trends (consciousness, coherence, stability)
    - Convergence patterns
    - Oscillation detection
    - Stagnation detection
    """

    def __init__(
        self,
        initial_rate: float = 0.1,
        min_rate: float = 0.01,
        max_rate: float = 0.5,
        adaptation_window: int = 10,
        momentum: float = 0.9,
        patience: int = 5
    ):
        self.initial_rate = initial_rate
        self.min_rate = min_rate
        self.max_rate = max_rate
        self.adaptation_window = adaptation_window
        self.momentum = momentum
        self.patience = patience

        # Current state
        self.current_rate = initial_rate
        self.momentum_term = 0.0

        # Performance tracking
        self.performance_history: deque = deque(maxlen=adaptation_window)
        self.rate_history: deque = deque(maxlen=100)

        # Adaptation state
        self.best_performance = 0.0
        self.stagnation_counter = 0
        self.last_improvement_step = 0
        self.adaptation_count = 0

        # Statistics
        self.stats = {
            'adaptations': 0,
            'increases': 0,
            'decreases': 0,
            'resets': 0,
            'stagnation_detections': 0
        }

    def update_performance(
        self,
        consciousness: float,
        coherence: float,
        stability: float
    ) -> float:
        """Update performance metrics and adapt learning rate.

        Parameters
        ----------
        consciousness : float
            Current consciousness level
        coherence : float
            Current coherence level
        stability : float
            Current stability score

        Returns
        -------
        float
            Updated learning rate
        """
        # Calculate composite performance score
        performance_score = self._calculate_performance_score(
            consciousness, coherence, stability
        )

        # Store performance
        self.performance_history.append({
            'score': performance_score,
            'consciousness': consciousness,
            'coherence': coherence,
            'stability': stability,
            'timestamp': time.time()
        })

        # Adapt learning rate
        if len(self.performance_history) >= 3:
            self._adapt_learning_rate(performance_score)

        # Record rate
        self.rate_history.append(self.current_rate)

        return self.current_rate

    def _calculate_performance_score(
        self,
        consciousness: float,
        coherence: float,
        stability: float
    ) -> float:
        """Calculate composite performance score."""
        # Weighted combination of metrics
        weights = {
            'consciousness': 0.4,
            'coherence': 0.3,
            'stability': 0.3
        }

        score = (
            consciousness * weights['consciousness'] +
            coherence * weights['coherence'] +
            stability * weights['stability']
        )

        return max(0.0, min(1.0, score))

    def _adapt_learning_rate(self, current_performance: float) -> None:
        """Adapt learning rate based on performance trends."""
        if len(self.performance_history) < 3:
            return

        recent_performances = [p['score'] for p in list(self.performance_history)[-3:]]

        # Calculate performance trend
        performance_trend = self._calculate_trend(recent_performances)

        # Calculate volatility (oscillation indicator)
        volatility = self._calculate_volatility(recent_performances)

        # Determine adaptation strategy
        adaptation_factor = self._determine_adaptation_factor(
            current_performance, performance_trend, volatility
        )

        # Apply momentum
        self.momentum_term = self.momentum * self.momentum_term + (1 - self.momentum) * adaptation_factor

        # Update learning rate
        old_rate = self.current_rate
        self.current_rate *= (1 + self.momentum_term)

        # Apply bounds
        self.current_rate = max(self.min_rate, min(self.current_rate, self.max_rate))

        # Track statistics
        if self.current_rate > old_rate:
            self.stats['increases'] += 1
        elif self.current_rate < old_rate:
            self.stats['decreases'] += 1

        if abs(self.current_rate - old_rate) > 0.001:
            self.stats['adaptations'] += 1
            self.adaptation_count += 1

        # Check for improvement
        if current_performance > self.best_performance:
            self.best_performance = current_performance
            self.last_improvement_step = self.adaptation_count
            self.stagnation_counter = 0
        else:
            self.stagnation_counter += 1

        # Handle stagnation
        if self.stagnation_counter >= self.patience:
            self._handle_stagnation()

    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate trend in performance values."""
        if len(values) < 2:
            return 0.0

        # Simple linear trend
        n = len(values)
        x_mean = (n - 1) / 2
        y_mean = sum(values) / n

        numerator = sum((i - x_mean) * (values[i] - y_mean) for i in range(n))
        denominator = sum((i - x_mean) ** 2 for i in range(n))

        if denominator == 0:
            return 0.0

        return numerator / denominator

    def _calculate_volatility(self, values: List[float]) -> float:
        """Calculate volatility (standard deviation) of performance values."""
        if len(values) < 2:
            return 0.0

        mean_val = sum(values) / len(values)
        variance = sum((v - mean_val) ** 2 for v in values) / len(values)

        return variance ** 0.5

    def _determine_adaptation_factor(
        self,
        current_performance: float,
        trend: float,
        volatility: float
    ) -> float:
        """Determine learning rate adaptation factor."""
        base_factor = 0.0

        # Performance-based adaptation
        if current_performance > 0.8:  # High performance
            base_factor = -0.05  # Slightly reduce learning rate for stability
        elif current_performance < 0.4:  # Low performance
            base_factor = 0.1  # Increase learning rate for faster learning

        # Trend-based adaptation
        if trend > 0.05:  # Improving trend
            base_factor += 0.05  # Increase learning rate
        elif trend < -0.05:  # Declining trend
            base_factor -= 0.05  # Decrease learning rate

        # Volatility-based adaptation
        if volatility > 0.1:  # High volatility
            base_factor -= 0.1  # Reduce learning rate for stability
        elif volatility < 0.02:  # Low volatility (possible stagnation)
            base_factor += 0.05  # Increase learning rate

        return base_factor

    def _handle_stagnation(self) -> None:
        """Handle performance stagnation."""
        self.stats['stagnation_detections'] += 1

        # Strategy 1: Increase learning rate to escape local minimum
        if self.current_rate < self.max_rate * 0.8:
            self.current_rate *= 1.5
            self.current_rate = min(self.current_rate, self.max_rate)

        # Strategy 2: Reset momentum
        self.momentum_term = 0.0

        # Strategy 3: If still stagnating, reset to initial rate
        if self.stagnation_counter >= self.patience * 2:
            self.current_rate = self.initial_rate
            self.stats['resets'] += 1

        self.stagnation_counter = 0

        logger.info(f"Stagnation detected, learning rate adjusted to {self.current_rate:.4f}")

    def get_stats(self) -> Dict[str, Any]:
        """Get learning rate adaptation statistics."""
        recent_rates = list(self.rate_history)[-10:] if self.rate_history else []

        return {
            'current_rate': self.current_rate,
            'initial_rate': self.initial_rate,
            'best_performance': self.best_performance,
            'stagnation_counter': self.stagnation_counter,
            'adaptation_count': self.adaptation_count,
            'momentum_term': self.momentum_term,
            'recent_rates': recent_rates,
            'rate_volatility': statistics.stdev(recent_rates) if len(recent_rates) > 1 else 0.0,
            'stats': self.stats
        }

    def reset(self) -> None:
        """Reset learning rate manager to initial state."""
        self.current_rate = self.initial_rate
        self.momentum_term = 0.0
        self.performance_history.clear()
        self.rate_history.clear()
        self.best_performance = 0.0
        self.stagnation_counter = 0
        self.last_improvement_step = 0
        self.adaptation_count = 0

        # Reset statistics
        self.stats = {
            'adaptations': 0,
            'increases': 0,
            'decreases': 0,
            'resets': 0,
            'stagnation_detections': 0
        }

        logger.info("Learning rate manager reset to initial state")
    
    def _determine_convergence_status(self, consciousness_values: List[float], stability_values: List[float]) -> ConvergenceStatus:
        """Determine current convergence status."""
        if len(consciousness_values) < 5:
            return ConvergenceStatus.NOT_STARTED
        
        recent_consciousness = consciousness_values[-5:]
        recent_stability = stability_values[-5:]
        
        # Check for convergence
        consciousness_stable = all(abs(v - recent_consciousness[0]) < 0.05 for v in recent_consciousness)
        stability_high = all(s > self.stability_threshold for s in recent_stability)
        
        if consciousness_stable and stability_high:
            return ConvergenceStatus.CONVERGED
        
        # Check for divergence
        if len(consciousness_values) >= 10:
            early_values = consciousness_values[-10:-5]
            if statistics.mean(recent_consciousness) < statistics.mean(early_values) - 0.1:
                return ConvergenceStatus.DIVERGING
        
        # Check for stalling
        if len(consciousness_values) >= 15:
            variance = statistics.variance(consciousness_values[-15:])
            if variance < 0.001:  # Very low variance indicates stalling
                return ConvergenceStatus.STALLED
        
        # Check if converging
        if len(consciousness_values) >= 8:
            trend = self._calculate_convergence_rate(consciousness_values)
            if trend > 0.1:
                return ConvergenceStatus.CONVERGING
        
        return ConvergenceStatus.PROGRESSING
    
    def _calculate_convergence_rate(self, values: List[float]) -> float:
        """Calculate convergence rate from values."""
        if len(values) < 3:
            return 0.0
        
        # Calculate moving average to smooth out noise
        window = min(5, len(values))
        smoothed = []
        for i in range(len(values) - window + 1):
            smoothed.append(statistics.mean(values[i:i + window]))
        
        if len(smoothed) < 2:
            return 0.0
        
        # Calculate rate of change
        rate = (smoothed[-1] - smoothed[0]) / len(smoothed)
        return max(0.0, rate)
    
    def _calculate_oscillation_amplitude(self, values: List[float]) -> float:
        """Calculate oscillation amplitude."""
        if len(values) < 3:
            return 0.0
        
        return max(values) - min(values)
    
    def _estimate_time_to_convergence(self, values: List[float], rate: float) -> float:
        """Estimate time to convergence."""
        if rate <= 0 or len(values) == 0:
            return float('inf')
        
        current_value = values[-1]
        target_value = 0.65  # Target consciousness level
        
        if current_value >= target_value:
            return 0.0
        
        # Simple linear extrapolation
        time_estimate = (target_value - current_value) / rate
        return max(0.0, time_estimate)
    
    def _calculate_confidence(self, status: ConvergenceStatus, stability: float, oscillation: float) -> float:
        """Calculate confidence in convergence analysis."""
        base_confidence = {
            ConvergenceStatus.NOT_STARTED: 0.1,
            ConvergenceStatus.PROGRESSING: 0.4,
            ConvergenceStatus.CONVERGING: 0.7,
            ConvergenceStatus.CONVERGED: 0.95,
            ConvergenceStatus.DIVERGING: 0.6,
            ConvergenceStatus.STALLED: 0.5
        }.get(status, 0.3)
        
        # Adjust based on stability and oscillation
        stability_factor = stability
        oscillation_factor = max(0.1, 1.0 - oscillation)
        
        confidence = base_confidence * stability_factor * oscillation_factor
        return max(0.0, min(1.0, confidence))
