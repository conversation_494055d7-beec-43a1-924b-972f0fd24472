#!/usr/bin/env python3
"""
QUALIA RSS Feed Test para Universo Holográfico

Este script testa a funcionalidade de coleta de feeds RSS
e sua injeção e processamento no universo holográfico.
"""

import os
import sys
import asyncio
import time
from typing import Dict, List, Any
import logging

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qualia.consciousness.enhanced_data_collector import (
    EnhancedDataCollector, EnhancedNewsEvent
)
from src.qualia.consciousness.holographic_universe import (
    HolographicMarketUniverse, HolographicEvent
)
from src.qualia.utils.logger import get_logger

# Configuração manual de logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
                   datefmt='%H:%M:%S')

logger = get_logger(__name__)


async def test_rss_collection():
    """
    Testa a coleta de feeds RSS e mostra os resultados.
    """
    logger.info("🔍 Iniciando teste de coleta RSS")
    
    collector = EnhancedDataCollector(
        symbols=["BTCUSDT", "ETHUSDT", "ADAUSDT", "SOLUSDT"],
        timeframes=["5m", "15m", "1h"]
    )
    
    async with collector:
        logger.info("📰 Buscando feeds RSS...")
        news_events = await collector.collect_news_events()
        
        logger.info(f"✅ Coletados {len(news_events)} eventos de notícias")
        
        for i, event in enumerate(news_events[:5]):  # Mostra até 5 eventos
            logger.info(f"  Notícia #{i+1}: {event.title[:50]}...")
            logger.info(f"    Sentiment: {event.sentiment_score:.3f}")
            logger.info(f"    Fonte: {event.source}")
            if event.quantum_sentiment_state is not None:
                logger.info(f"    Estado quântico: {event.quantum_sentiment_state}")
    
    return news_events


async def test_holographic_integration(news_events: List[EnhancedNewsEvent]):
    """
    Testa a integração das notícias coletadas no universo holográfico.
    """
    logger.info("🌌 Inicializando universo holográfico")
    
    # Cria universo holográfico
    universe = HolographicMarketUniverse(
        field_size=(200, 200),
        diffusion_rate=0.35,
        feedback_strength=0.08
    )
    
    await universe.initialize()
    logger.info(f"✅ Universo holográfico inicializado: {universe.field_size}")
    
    # Cria coletor de dados
    collector = EnhancedDataCollector(
        symbols=["BTCUSDT", "ETHUSDT", "ADAUSDT", "SOLUSDT"],
        timeframes=["5m", "15m", "1h"]
    )
    
    # Converte notícias para eventos holográficos
    holographic_events = collector.convert_to_holographic_events(
        enhanced_data=[],  # Sem dados de mercado para este teste
        news_events=news_events,
        universe_field_size=universe.field_size
    )
    
    logger.info(f"🔄 Convertidos {len(holographic_events)} eventos holográficos a partir das notícias")
    
    # Injeta eventos no universo
    for event in holographic_events:
        await universe.inject_holographic_event(event)
        logger.info(f"💉 Evento injetado: {event.event_type} ({event.position}) amp={event.amplitude:.2f}")
    
    # Evolui o universo
    current_time = time.time()
    for step in range(5):
        logger.info(f"🔄 Evoluindo universo - passo {step+1}/5")
        await universe.step_evolution(current_time + step)
    
    # Detecta padrões
    logger.info("🔍 Analisando padrões holográficos...")
    patterns = universe.analyze_holographic_patterns()
    
    logger.info(f"✅ Detectados {len(patterns)} padrões")
    for i, pattern in enumerate(patterns[:3]):  # Mostra até 3 padrões
        logger.info(f"  Padrão #{i+1}: {pattern.pattern_type} ({pattern.position})")
        logger.info(f"    Força: {pattern.strength:.3f}")
        logger.info(f"    Confiança: {pattern.confidence:.3f}")
    
    # Gera sinais
    logger.info("🎯 Gerando sinais holográficos...")
    signals = universe.generate_trading_signals(patterns)
    
    logger.info(f"✅ Gerados {len(signals)} sinais")
    for i, signal in enumerate(signals[:3]):  # Mostra até 3 sinais
        logger.info(f"  Sinal #{i+1}: {signal.symbol} {signal.action}")
        logger.info(f"    Força: {signal.strength:.3f}")
        logger.info(f"    Confiança: {signal.confidence:.3f}")


async def main():
    """Função principal."""
    print("🌟 QUALIA RSS Feed Test para Universo Holográfico 🌟")
    print("=" * 70)
    
    try:
        news_events = await test_rss_collection()
        
        if news_events:
            print("\n" + "=" * 70)
            await test_holographic_integration(news_events)
        else:
            logger.error("❌ Nenhuma notícia coletada - abortando teste de integração")
    
    except Exception as e:
        logger.error(f"❌ Erro durante o teste: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main()) 