# Path: src/qualia/custom_types.py
# Este trecho será adicionado ao final do arquivo existente, ou o arquivo será criado.

from dataclasses import dataclass, field
from typing import Dict, Any

@dataclass
class CollectiveMindState:
    """
    Representa o estado mental coletivo inferido pelo Farsight a partir de dados externos.
    """
    timestamp: float
    dominant_narrative: str  # Ex: "AI_optimism", "inflation_fear", "crypto_winter"
    # Mapeia o nome de uma Persona para o impacto que a narrativa atual tem sobre ela.
    persona_impact: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    # Ex: {"RetailCluster": {"confidence_boost": 0.25, "action_bias": "BUY"}}