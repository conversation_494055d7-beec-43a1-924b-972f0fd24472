#!/usr/bin/env python3
"""
QUALIA KuCoin + Holographic Integration Demo

Demonstração prática da integração entre:
- Sistema holográfico de análise de mercado
- Execução automática via KuCoin
- Risk management integrado
- Monitoramento em tempo real

Este demo mostra como conectar sinais holográficos com execução real.
"""

import asyncio
import time
import logging
import os
import sys
from pathlib import Path
from typing import Dict, List, Any

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from qualia.consciousness.holographic_universe import (
    HolographicMarketUniverse, 
    HolographicEvent, 
    TradingSignal
)
from qualia.consciousness.real_data_collectors import RealDataCollector
from qualia.risk_management.advanced_risk_manager import AdvancedRiskManager
from qualia.exchanges.kucoin_client import KuCoinClient
from qualia.utils.logger import get_logger

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = get_logger(__name__)

class KuCoinHolographicBridge:
    """
    Ponte simplificada entre sinais holográficos e KuCoin.
    Versão demo que demonstra a integração prática.
    """
    
    def __init__(self, risk_manager: AdvancedRiskManager, kucoin_client: KuCoinClient):
        self.risk_manager = risk_manager
        self.kucoin_client = kucoin_client
        self.signals_processed = 0
        self.trades_executed = 0
        self.active_positions = {}
        
        # Configuration
        self.min_confidence = 0.75  # Confidence mínima para execução
        self.allowed_symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']
        self.max_positions = 3
        
        logger.info("🌉 KuCoin Holographic Bridge inicializada")
    
    async def process_holographic_signal(self, signal: TradingSignal) -> Dict[str, Any]:
        """Processa sinal holográfico com validação e execução."""
        
        self.signals_processed += 1
        
        # 1. Validação básica
        if not self._validate_signal(signal):
            return {'executed': False, 'reason': 'validation_failed'}
        
        # 2. Risk management
        risk_result = await self._validate_risk(signal)
        if not risk_result['allowed']:
            return {'executed': False, 'reason': f"risk_{risk_result['reason']}"}
        
        # 3. Execução (simulada para demo)
        execution_result = await self._execute_signal_demo(signal, risk_result)
        
        if execution_result['success']:
            self.trades_executed += 1
            
        return execution_result
    
    def _validate_signal(self, signal: TradingSignal) -> bool:
        """Validação básica do sinal."""
        
        # Confidence check
        if signal.confidence < self.min_confidence:
            logger.debug(f"Sinal rejeitado: confidence {signal.confidence:.2f} < {self.min_confidence}")
            return False
        
        # Symbol check
        if signal.symbol not in self.allowed_symbols:
            logger.debug(f"Sinal rejeitado: symbol {signal.symbol} não permitido")
            return False
        
        # Action check
        if signal.action not in ['BUY', 'SELL']:
            logger.debug(f"Sinal rejeitado: action {signal.action} inválida")
            return False
        
        # Position limit check
        if len(self.active_positions) >= self.max_positions:
            logger.debug(f"Sinal rejeitado: limite de posições atingido ({self.max_positions})")
            return False
        
        return True
    
    async def _validate_risk(self, signal: TradingSignal) -> Dict[str, Any]:
        """Validação via risk manager."""
        
        try:
            # Simula preço atual (em produção viria do KuCoin)
            current_price = signal.metadata.get('current_price', 50000.0)
            
            # Calcula stop loss (2% para demo)
            if signal.action == 'BUY':
                stop_loss = current_price * 0.98
            else:
                stop_loss = current_price * 1.02
            
            # Validação via risk manager
            risk_result = self.risk_manager.calculate_position_size(
                symbol=signal.symbol,
                current_price=current_price,
                stop_loss_price=stop_loss,
                confidence=signal.confidence,
                volatility=0.02  # 2% volatility para demo
            )
            
            return {
                'allowed': risk_result['position_allowed'],
                'reason': risk_result.get('reason', 'approved'),
                'position_size': risk_result.get('position_size', 0),
                'quantity': risk_result.get('quantity', 0)
            }
            
        except Exception as e:
            logger.error(f"Erro na validação de risco: {e}")
            return {'allowed': False, 'reason': 'risk_calculation_error'}
    
    async def _execute_signal_demo(self, signal: TradingSignal, risk_result: Dict[str, Any]) -> Dict[str, Any]:
        """Execução simulada do sinal (para demo)."""
        
        try:
            # Em produção, aqui faria a chamada real para KuCoin
            # order_result = await self.kucoin_client.create_order(...)
            
            # Para demo, simula execução bem-sucedida
            order_id = f"demo_order_{int(time.time())}"
            execution_price = signal.metadata.get('current_price', 50000.0)
            
            # Registra posição ativa
            self.active_positions[order_id] = {
                'signal': signal,
                'order_id': order_id,
                'execution_price': execution_price,
                'quantity': risk_result['quantity'],
                'opened_at': time.time()
            }
            
            logger.info(
                f"🎯 DEMO TRADE EXECUTED: {signal.symbol} {signal.action} "
                f"quantity={risk_result['quantity']:.6f} "
                f"price={execution_price:.2f} "
                f"confidence={signal.confidence:.2f} "
                f"order_id={order_id}"
            )
            
            return {
                'success': True,
                'executed': True,
                'order_id': order_id,
                'execution_price': execution_price,
                'quantity': risk_result['quantity'],
                'reason': 'demo_execution_successful'
            }
            
        except Exception as e:
            logger.error(f"Erro na execução demo: {e}")
            return {
                'success': False,
                'executed': False,
                'reason': f'execution_error_{str(e)[:30]}'
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas da bridge."""
        
        return {
            'signals_processed': self.signals_processed,
            'trades_executed': self.trades_executed,
            'active_positions': len(self.active_positions),
            'execution_rate': self.trades_executed / max(self.signals_processed, 1)
        }

async def demo_kucoin_holographic_integration():
    """Demo principal da integração KuCoin + Holographic."""
    
    logger.info("🌌 QUALIA KuCoin + Holographic Integration Demo")
    logger.info("=" * 60)
    
    try:
        # 1. Inicializar componentes
        logger.info("🔧 Inicializando componentes...")
        
        # Data collector
        data_sources_config = {
            'binance': {
                'enabled': True,
                'symbols': ['BTCUSDT', 'ETHUSDT', 'ADAUSDT'],
                'interval': 30
            },
            'news': {
                'enabled': True,
                'sources': ['https://cointelegraph.com/rss'],
                'interval': 60
            },
            'sentiment': {
                'enabled': True,
                'fear_greed_index': True,
                'interval': 300
            }
        }
        
        data_collector = RealDataCollector(data_sources_config)
        await data_collector.initialize()
        
        # Holographic universe
        holographic_universe = HolographicMarketUniverse(
            field_size=(200, 200),
            time_steps=500,
            diffusion_rate=0.25,
            feedback_strength=0.06
        )
        
        # Risk manager
        risk_manager = AdvancedRiskManager(
            initial_capital=10000.0,
            risk_per_trade_pct=1.0,
            max_drawdown_pct=15.0,
            max_volatility=5.0
        )
        
        # KuCoin client (configuração demo)
        kucoin_config = {
            'api_key': os.getenv('KUCOIN_API_KEY', 'demo_key'),
            'api_secret': os.getenv('KUCOIN_API_SECRET', 'demo_secret'),
            'password': os.getenv('KUCOIN_PASSPHRASE', 'demo_pass'),
            'sandbox': True,
            'timeout': 30.0
        }
        
        # Para demo, não inicializa conexão real se não tiver credenciais
        kucoin_client = None
        if all([kucoin_config['api_key'] != 'demo_key', 
                kucoin_config['api_secret'] != 'demo_secret',
                kucoin_config['password'] != 'demo_pass']):
            kucoin_client = KuCoinClient(kucoin_config)
            await kucoin_client.initialize()
            logger.info("✅ KuCoin client conectado")
        else:
            logger.info("⚠️ Usando modo demo (sem credenciais KuCoin reais)")
        
        # Trading bridge
        trading_bridge = KuCoinHolographicBridge(risk_manager, kucoin_client)
        
        logger.info("✅ Todos os componentes inicializados")
        
        # 2. Loop de demonstração
        logger.info("\n🚀 Iniciando loop de demonstração...")
        
        for iteration in range(5):  # 5 iterações para demo
            logger.info(f"\n📊 Iteração {iteration + 1}/5 - {time.strftime('%H:%M:%S')}")
            
            # Coleta de dados
            logger.info("📡 Coletando dados...")
            market_data = await data_collector.collect_market_data()
            news_data = await data_collector.collect_news_data()
            
            # Injeção de eventos no universo holográfico
            events_injected = 0
            
            # Market events
            for symbol, data in market_data.items():
                if symbol.replace('USDT', '/USDT') in holographic_universe.symbol_positions:
                    symbol_formatted = symbol.replace('USDT', '/USDT')
                    position = holographic_universe.symbol_positions[symbol_formatted.split('/')[0]]
                    
                    # Amplitude baseada em volume
                    volume = data.get('volume', 0)
                    amplitude = min(volume / 1000000, 5.0)  # Normaliza volume
                    
                    event = HolographicEvent(
                        position=position,
                        time=time.time(),
                        amplitude=amplitude,
                        spatial_sigma=15.0,
                        temporal_sigma=30.0,
                        event_type='market',
                        source_data=data,
                        confidence=0.9
                    )
                    
                    await holographic_universe.inject_holographic_event(event)
                    events_injected += 1
            
            # News events
            for article in news_data[:3]:  # Primeiros 3 artigos
                position = holographic_universe.modality_regions['news']
                amplitude = abs(article.get('sentiment_score', 0)) * 2.0
                
                event = HolographicEvent(
                    position=position,
                    time=time.time(),
                    amplitude=amplitude,
                    spatial_sigma=25.0,
                    temporal_sigma=60.0,
                    event_type='news',
                    source_data=article,
                    confidence=article.get('confidence', 0.7)
                )
                
                await holographic_universe.inject_holographic_event(event)
                events_injected += 1
            
            logger.info(f"  • Eventos injetados: {events_injected}")
            
            # Evolução do universo
            await holographic_universe.step_evolution(time.time())
            
            # Análise de padrões
            patterns = holographic_universe.analyze_holographic_patterns()
            logger.info(f"  • Padrões detectados: {len(patterns)}")
            
            # Geração de sinais
            if patterns:
                signals = holographic_universe.generate_trading_signals(patterns)
                logger.info(f"  • Sinais gerados: {len(signals)}")
                
                # Processamento de sinais via bridge
                for signal in signals:
                    if signal.confidence >= 0.7:  # Filtro adicional
                        result = await trading_bridge.process_holographic_signal(signal)
                        
                        if result['executed']:
                            logger.info(
                                f"    ✅ SINAL EXECUTADO: {signal.symbol} {signal.action} "
                                f"confidence={signal.confidence:.2f}"
                            )
                        else:
                            logger.debug(
                                f"    ❌ Sinal rejeitado: {signal.symbol} - {result['reason']}"
                            )
            
            # Estatísticas da bridge
            stats = trading_bridge.get_stats()
            logger.info(
                f"  • Bridge stats: processed={stats['signals_processed']}, "
                f"executed={stats['trades_executed']}, "
                f"rate={stats['execution_rate']:.1%}"
            )
            
            # Field summary
            field_summary = holographic_universe.get_field_summary()
            logger.info(
                f"  • Field: energy={field_summary.get('total_energy', 0):.2f}, "
                f"entropy={field_summary.get('entropy', 0):.2f}"
            )
            
            # Pausa entre iterações
            await asyncio.sleep(10)
        
        # 3. Relatório final
        logger.info("\n📈 RELATÓRIO FINAL")
        logger.info("=" * 40)
        
        final_stats = trading_bridge.get_stats()
        logger.info(f"Sinais processados: {final_stats['signals_processed']}")
        logger.info(f"Trades executados: {final_stats['trades_executed']}")
        logger.info(f"Taxa de execução: {final_stats['execution_rate']:.1%}")
        logger.info(f"Posições ativas: {final_stats['active_positions']}")
        
        final_field_summary = holographic_universe.get_field_summary()
        logger.info(f"Energia do campo: {final_field_summary.get('total_energy', 0):.2f}")
        logger.info(f"Entropia do campo: {final_field_summary.get('entropy', 0):.2f}")
        
        # Risk manager stats
        logger.info(f"Capital atual: ${risk_manager.current_capital:.2f}")
        logger.info(f"Drawdown atual: {risk_manager.current_drawdown_pct:.2f}%")
        
        logger.info("\n🎉 Demo concluída com sucesso!")
        
        # 4. Próximos passos
        logger.info("\n💡 PRÓXIMOS PASSOS PARA PRODUÇÃO:")
        logger.info("1. Configurar credenciais KuCoin reais")
        logger.info("2. Implementar HolographicTradingBridge completa")
        logger.info("3. Adicionar monitoramento de posições ativas")
        logger.info("4. Configurar alertas e notificações")
        logger.info("5. Implementar backtesting histórico")
        logger.info("6. Configurar paper trading para validação")
        
    except Exception as e:
        logger.error(f"❌ Erro na demo: {e}", exc_info=True)
    
    finally:
        # Cleanup
        try:
            await data_collector.shutdown()
            await holographic_universe.shutdown()
            if kucoin_client:
                # await kucoin_client.close()
                pass
        except Exception as e:
            logger.error(f"Erro no cleanup: {e}")

async def main():
    """Função principal."""
    
    print("🌌 QUALIA KuCoin + Holographic Integration Demo")
    print("=" * 60)
    print("Este demo mostra como integrar:")
    print("• Sistema holográfico de análise")
    print("• Execução automática via KuCoin")
    print("• Risk management integrado")
    print("• Monitoramento em tempo real")
    print("=" * 60)
    
    # Verificar se quer continuar
    try:
        response = input("\nPressione ENTER para continuar ou 'q' para sair: ")
        if response.lower() == 'q':
            print("Demo cancelada.")
            return
    except KeyboardInterrupt:
        print("\nDemo cancelada.")
        return
    
    # Executar demo
    await demo_kucoin_holographic_integration()

if __name__ == "__main__":
    asyncio.run(main()) 
