import cProfile
import io
import threading
import time
from typing import Any, Dict

import numpy as np
import pandas as pd
from pstats import Stats

from qualia.market.trading_engine import TradingEngine


class DummyStrategy:
    def generate_signal(
        self, df: pd.DataFrame, quantum_metrics: Dict[str, Any] | None = None
    ):
        return "buy", 0.6


def _prepare_engine() -> TradingEngine:
    TradingEngine._initialize_components = lambda self: None  # type: ignore
    eng = TradingEngine("k", "s", ["BTC/USD"], live_mode=False)
    eng.timeframes = ["1m"]
    now = pd.Timestamp.now()
    df = pd.DataFrame(
        {
            "timestamp": pd.date_range(
                now - pd.Timedelta(minutes=4), periods=5, freq="min"
            ),
            "open": np.random.rand(5),
            "high": np.random.rand(5),
            "low": np.random.rand(5),
            "close": np.random.rand(5),
            "volume": np.random.rand(5),
        }
    )
    eng.market_data["BTC/USD"]["1m"] = TradingEngine._ohlcv_df_to_dict_of_lists(df)
    eng.strategy = DummyStrategy()
    eng._generate_quantum_enhanced_signal = lambda s, d: (
        {
            "action": "buy",
            "confidence": 0.6,
            "reasons": [],
            "price": df["close"].iloc[-1],
        },
        {},
    )
    eng.analysis_interval = 0
    return eng


def run_profile(duration: float = 0.1) -> str:
    engine = _prepare_engine()
    profiler = cProfile.Profile()
    thread = threading.Thread(target=engine._analysis_loop)
    profiler.enable()
    thread.start()
    time.sleep(duration)
    engine.stop_event.set()
    thread.join()
    profiler.disable()
    output = io.StringIO()
    Stats(profiler, stream=output).sort_stats("cumtime").print_stats(10)
    return output.getvalue()


if __name__ == "__main__":
    result = run_profile(0.1)
    print(result)
