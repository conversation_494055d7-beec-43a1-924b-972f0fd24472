# MemoryService

O `MemoryService` unifica as diferentes formas de armazenamento do QUALIA, oferecendo uma interface assíncrona para gravação e consulta de padrões.
Ele combina o `QuantumPatternMemory` com o `HolographicMemory` e emite eventos pelo `SimpleEventBus` quando padrões são escritos ou removidos.

## Exemplo de uso

```python
import asyncio
from src.qualia.memory.service import MemoryService
from src.qualia.common_types import QuantumSignaturePacket

service = MemoryService()

async def main() -> None:
    packet = QuantumSignaturePacket(vector=[0.1, 0.2, 0.3, 0.4], metrics={})
    await service.store(packet)
    resultado = service.retrieve(packet.id)
    print(resultado)

asyncio.run(main())
```

## Diagrama de sequência

```plantuml
@startuml
Client -> MemoryService : store(packet)
MemoryService -> QuantumPatternMemory : store_pattern(packet)
QuantumPatternMemory --> MemoryService : sucesso
MemoryService -> HolographicMemory : store(vector)
MemoryService -> EventBus : publish(memory.written)
MemoryService --> Client : resultado
@enduml
```

## Integração no start_real_trading.py

O script `scripts/start_real_trading.py` inicializa o `MemoryService` combinando
`HolographicMemory` e `QuantumPatternMemory`. A cada decisão validada é gerado
um `QuantumSignaturePacket` contendo métricas do sinal. O pacote é gravado via
`memory_service.store` juntamente com um *snapshot* do mercado e o contexto da
operação.

Desde a versão atual, esse script cria a `QuantumPatternMemory` usando o caminho
definido em `qualia.config.settings.qpm_memory_file` quando nenhum
`persistence_path` é informado no `qpm_config`. A instância resultante é
injetada no contexto das estratégias através da chave `qpm_instance`, permitindo
acesso direto à memória durante a execução.

### Exemplo extra

```python
import asyncio
from qualia.memory.service import MemoryService
from qualia.memory.holographic_memory import HolographicMemory
from qualia.memory.quantum_pattern_memory import QuantumPatternMemory
from qualia.common_types import QuantumSignaturePacket

qpm = QuantumPatternMemory(persistence_path="patterns.db")
service = MemoryService(holographic=HolographicMemory(), qpm=qpm)

async def main() -> None:
    pkt = QuantumSignaturePacket(vector=[0.5, 0.6], metrics={"symbol": "BTCUSDT"})
    await service.store(pkt, market_snapshot={"close": 44120.0}, decision_context={"strategy": "holo"})

asyncio.run(main())
```

## Ativando o IntentMemory

Defina `QUALIA_FT_INTENT_MEMORY` para que o `MemoryService` habilite
automaticamente o `IntentMemory`, permitindo armazenar sequências de tokens e
prever o próximo elemento.

Exemplo de `.env`:

```yaml
QUALIA_FT_INTENT_MEMORY: "true"
```

## Async persistence

O parâmetro `async_persist` do construtor permite offloadar a persistência do
`QuantumPatternMemory` para uma *task* assíncrona. Assim, a chamada a
`store` retorna imediatamente e o loop principal não fica bloqueado.

```python
from qualia.memory.service import MemoryService
from qualia.common_types import QuantumSignaturePacket

service = MemoryService(async_persist=True)
pkt = QuantumSignaturePacket(vector=[1.0], metrics={})
await service.store(pkt)
```

Quando habilitado, eventuais erros de gravação são registrados de forma
assíncrona no logger do serviço.

