from datetime import datetime
from types import SimpleNamespace
from unittest.mock import MagicMock

from tests.stub_utils import install_stubs

install_stubs()

from qualia.adaptive_evolution import AdaptiveConsciousnessEvolution


class DummyController:
    def __init__(self, ret=None):
        self.ret = ret

    def get_current_risk_levels(self, symbol):
        return self.ret

    def get_performance_metrics(self):
        return {"metric": 1}


def test_dynamic_risk_enabled_and_metrics():
    ace = AdaptiveConsciousnessEvolution(qualia_universe=MagicMock())
    ace.dynamic_risk_controller = DummyController()
    assert ace.is_dynamic_risk_enabled()
    assert ace.get_risk_controller_metrics() == {"metric": 1}


def test_get_dynamic_risk_levels_from_cache():
    ace = AdaptiveConsciousnessEvolution(qualia_universe=MagicMock())
    ace.risk_calibration_results = {
        "BTC": {"stop_loss_price": 1.0, "calibration_timestamp": "x"}
    }
    res = ace.get_dynamic_risk_levels("BTC")
    assert res["stop_loss_price"] == 1.0


def test_get_dynamic_risk_levels_via_controller():
    ret = SimpleNamespace(
        stop_loss_price=1.0,
        take_profit_price=2.0,
        atr_value=3.0,
        market_regime="bull",
        timestamp=datetime(2020, 1, 1),
        adjustment_reason="volatility",
    )
    ace = AdaptiveConsciousnessEvolution(qualia_universe=MagicMock())
    ace.dynamic_risk_controller = DummyController(ret)
    res = ace.get_dynamic_risk_levels("ETH")
    assert res["take_profit_price"] == 2.0


def test_get_dynamic_risk_levels_none():
    ace = AdaptiveConsciousnessEvolution(qualia_universe=MagicMock())
    ace.dynamic_risk_controller = DummyController(None)
    assert ace.get_dynamic_risk_levels("ETH") is None
