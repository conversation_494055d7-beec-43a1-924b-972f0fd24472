#!/usr/bin/env python3
"""
Teste direto da conectividade KuCoin para diagnosticar problemas de rede.
"""

import asyncio
import os
import sys
from pathlib import Path

# Adicionar o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from dotenv import load_dotenv
import ccxt.async_support as ccxt

async def test_kucoin_direct():
    """Teste direto da API KuCoin."""
    
    # Carregar variáveis de ambiente
    env_path = Path(__file__).parent.parent / ".env"
    load_dotenv(env_path)
    
    print(f"✅ Arquivo .env carregado: {env_path}")
    
    # Verificar credenciais
    api_key = os.getenv('KUCOIN_API_KEY', '')
    api_secret = os.getenv('KUCOIN_SECRET_KEY', '')
    passphrase = os.getenv('KUCOIN_PASSPHRASE', '')
    
    print(f"🔍 Verificando credenciais:")
    print(f"   KUCOIN_API_KEY: {'✅ Presente' if api_key else '❌ Ausente'}")
    print(f"   KUCOIN_SECRET_KEY: {'✅ Presente' if api_secret else '❌ Ausente'}")
    print(f"   KUCOIN_PASSPHRASE: {'✅ Presente' if passphrase else '❌ Ausente'}")
    
    if not all([api_key, api_secret, passphrase]):
        print("❌ Credenciais incompletas!")
        return False
    
    # Teste 1: Sandbox
    print("\n🧪 TESTE 1: KuCoin Sandbox")
    try:
        exchange = ccxt.kucoin({
            'apiKey': api_key,
            'secret': api_secret,
            'password': passphrase,
            'sandbox': True,
            'timeout': 30000,  # 30 segundos
            'rateLimit': 500,  # 500ms entre requests
        })
        
        print("   Carregando mercados...")
        await exchange.load_markets()
        print("   ✅ Mercados carregados com sucesso!")
        
        print("   Testando ticker BTC/USDT...")
        ticker = await exchange.fetch_ticker('BTC/USDT')
        print(f"   ✅ Ticker recebido: ${ticker['last']:,.2f}")
        
        await exchange.close()
        print("   ✅ SANDBOX FUNCIONANDO!")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro no sandbox: {e}")
        try:
            await exchange.close()
        except:
            pass
    
    # Teste 2: Produção
    print("\n🏭 TESTE 2: KuCoin Produção")
    try:
        exchange = ccxt.kucoin({
            'apiKey': api_key,
            'secret': api_secret,
            'password': passphrase,
            'sandbox': False,
            'timeout': 30000,  # 30 segundos
            'rateLimit': 500,  # 500ms entre requests
        })
        
        print("   Carregando mercados...")
        await exchange.load_markets()
        print("   ✅ Mercados carregados com sucesso!")
        
        print("   Testando ticker BTC/USDT...")
        ticker = await exchange.fetch_ticker('BTC/USDT')
        print(f"   ✅ Ticker recebido: ${ticker['last']:,.2f}")
        
        await exchange.close()
        print("   ✅ PRODUÇÃO FUNCIONANDO!")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro na produção: {e}")
        try:
            await exchange.close()
        except:
            pass
    
    # Teste 3: Apenas dados públicos (sem credenciais)
    print("\n🌐 TESTE 3: Dados Públicos (sem credenciais)")
    try:
        exchange = ccxt.kucoin({
            'timeout': 30000,  # 30 segundos
            'rateLimit': 500,  # 500ms entre requests
        })
        
        print("   Carregando mercados...")
        await exchange.load_markets()
        print("   ✅ Mercados carregados com sucesso!")
        
        print("   Testando ticker BTC/USDT...")
        ticker = await exchange.fetch_ticker('BTC/USDT')
        print(f"   ✅ Ticker recebido: ${ticker['last']:,.2f}")
        
        await exchange.close()
        print("   ✅ DADOS PÚBLICOS FUNCIONANDO!")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro nos dados públicos: {e}")
        try:
            await exchange.close()
        except:
            pass
    
    return False

async def main():
    """Função principal."""
    print("🚀 Teste Direto KuCoin - Diagnóstico de Conectividade")
    print("=" * 60)
    
    success = await test_kucoin_direct()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ SUCESSO: Pelo menos um teste passou!")
        print("   O problema pode estar na integração QUALIA, não na conectividade KuCoin.")
    else:
        print("❌ FALHA: Todos os testes falharam!")
        print("   Possíveis causas:")
        print("   - Problemas de rede/firewall")
        print("   - Credenciais inválidas")
        print("   - Rate limiting da KuCoin")
        print("   - Problemas temporários da KuCoin")

if __name__ == "__main__":
    asyncio.run(main())
