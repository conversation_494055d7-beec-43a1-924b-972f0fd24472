"""
QUALIA Test Configuration Manager - D-07.4 Implementation

Gerenciador de configurações de teste com estratégias idênticas, parâmetros de risco
e suporte a múltiplos símbolos para garantir comparações justas entre simulador e live trading.
"""

import yaml
import json
from datetime import datetime
from dataclasses import dataclass, field, asdict
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import logging

from ..utils.logging_config import get_qualia_logger

logger = get_qualia_logger(__name__)


@dataclass
class StrategyConfig:
    """Configuração de estratégia de trading."""
    
    name: str
    type: str  # "momentum", "mean_reversion", "arbitrage", etc.
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    # Indicadores técnicos
    indicators: Dict[str, Any] = field(default_factory=dict)
    
    # Sinais de entrada/saída
    entry_conditions: List[str] = field(default_factory=list)
    exit_conditions: List[str] = field(default_factory=list)
    
    # Timeframes
    primary_timeframe: str = "1h"
    secondary_timeframes: List[str] = field(default_factory=list)


@dataclass
class RiskManagementConfig:
    """Configuração de gerenciamento de risco."""
    
    # Capital
    initial_capital: float = 10000.0
    max_capital_risk_pct: float = 2.0  # 2% do capital por trade
    
    # Position sizing
    position_sizing_method: str = "fixed_pct"  # "fixed_pct", "kelly", "volatility_adjusted"
    max_position_size_pct: float = 10.0  # 10% do capital
    min_position_size_usd: float = 10.0
    
    # Stop loss / Take profit
    stop_loss_pct: float = 2.0  # 2%
    take_profit_pct: float = 4.0  # 4%
    trailing_stop_enabled: bool = False
    trailing_stop_pct: float = 1.0
    
    # Drawdown protection
    max_daily_loss_pct: float = 5.0  # 5% do capital
    max_consecutive_losses: int = 5
    
    # Exposure limits
    max_total_exposure_pct: float = 50.0  # 50% do capital
    max_correlation_exposure: float = 0.7
    
    # Time-based rules
    trading_hours_start: str = "00:00"
    trading_hours_end: str = "23:59"
    avoid_news_events: bool = True
    news_blackout_minutes: int = 30


@dataclass
class ExecutionConfig:
    """Configuração de execução de ordens."""
    
    # Order types
    default_order_type: str = "market"  # "market", "limit", "stop"
    limit_order_offset_pct: float = 0.1  # 0.1% do preço atual
    
    # Slippage e fees
    expected_slippage_pct: float = 0.05  # 0.05%
    trading_fees_pct: float = 0.1  # 0.1%
    
    # Timing
    max_order_age_seconds: int = 300  # 5 minutos
    order_retry_attempts: int = 3
    order_retry_delay_seconds: int = 5
    
    # Partial fills
    allow_partial_fills: bool = True
    min_fill_pct: float = 50.0  # 50% da quantidade
    
    # Market conditions
    min_volume_usd: float = 100000.0  # Volume mínimo para trading
    max_spread_pct: float = 0.5  # 0.5% spread máximo

    # Trading mode
    paper_trading: bool = True  # Modo paper trading por padrão

    # Slippage and timeout settings
    max_slippage_pct: float = 0.2  # 0.2% slippage máximo
    order_timeout_seconds: int = 60  # Timeout de ordem
    max_orders_per_minute: int = 10  # Limite de ordens por minuto


@dataclass
class TestConfiguration:
    """Configuração completa de um teste A/B."""
    
    # Identificação
    config_id: str
    config_name: str
    created_at: datetime = field(default_factory=datetime.now)
    
    # Símbolos
    symbols: List[str] = field(default_factory=list)
    
    # Configurações
    strategy_config: StrategyConfig = field(default_factory=lambda: StrategyConfig("default", "momentum"))
    risk_config: RiskManagementConfig = field(default_factory=RiskManagementConfig)
    execution_config: ExecutionConfig = field(default_factory=ExecutionConfig)
    
    # Configurações específicas do teste
    test_duration_hours: float = 24.0
    warmup_period_minutes: float = 15.0
    
    # Configurações de ambiente
    simulator_config: Dict[str, Any] = field(default_factory=dict)
    live_config: Dict[str, Any] = field(default_factory=dict)
    
    # Metadados
    description: str = ""
    tags: List[str] = field(default_factory=list)
    version: str = "1.0"


class TestConfigManager:
    """Gerenciador de configurações de teste."""
    
    def __init__(self, config_dir: str = "config/ab_testing"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Cache de configurações
        self._config_cache: Dict[str, TestConfiguration] = {}
        
        # Templates de estratégias
        self._strategy_templates = self._load_strategy_templates()
        
        logger.info(f"📋 TestConfigManager inicializado - Dir: {self.config_dir}")
    
    async def create_test_configuration(
        self,
        symbols: List[str],
        strategy_config: Dict[str, Any],
        risk_params: Dict[str, Any],
        config_name: Optional[str] = None,
        description: str = "",
        execution_settings: Optional[Dict[str, Any]] = None
    ) -> TestConfiguration:
        """Cria uma nova configuração de teste."""
        
        config_id = f"test_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        config_name = config_name or f"A/B Test Configuration {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        logger.info(f"🔧 Criando configuração de teste: {config_name}")
        
        # Criar configuração de estratégia
        strategy = self._create_strategy_config(strategy_config)
        
        # Criar configuração de risco
        risk_config = self._create_risk_config(risk_params)
        
        # Criar configuração de execução
        execution_config = self._create_execution_config(symbols, execution_settings)
        
        # Configurações específicas do simulador e live
        simulator_config = self._create_simulator_config(symbols, strategy)
        live_config = self._create_live_config(symbols, strategy)
        
        # Criar configuração completa
        test_config = TestConfiguration(
            config_id=config_id,
            config_name=config_name,
            symbols=symbols.copy(),
            strategy_config=strategy,
            risk_config=risk_config,
            execution_config=execution_config,
            simulator_config=simulator_config,
            live_config=live_config,
            description=description,
            tags=["ab_test", "simulator_vs_live"]
        )
        
        # Salvar configuração
        await self.save_configuration(test_config)
        
        # Adicionar ao cache
        self._config_cache[config_id] = test_config
        
        logger.info(f"✅ Configuração criada: {config_id}")
        return test_config
    
    async def load_configuration(self, config_id_or_path: Union[str, Path]) -> Optional[TestConfiguration]:
        """Carrega uma configuração existente."""

        # Determinar se é um ID ou um path
        if isinstance(config_id_or_path, Path):
            config_file = config_id_or_path
            config_id = config_file.stem  # Nome do arquivo sem extensão
        else:
            config_id = config_id_or_path
            # Verificar cache primeiro
            if config_id in self._config_cache:
                return self._config_cache[config_id]

            # Carregar do arquivo
            config_file = self.config_dir / f"{config_id}.yaml"

        if not config_file.exists():
            logger.warning(f"⚠️ Configuração não encontrada: {config_file}")
            return None
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # Converter para TestConfiguration
            config = self._dict_to_test_configuration(config_data)
            
            # Adicionar ao cache
            self._config_cache[config_id] = config
            
            logger.info(f"📋 Configuração carregada: {config_id}")
            return config
            
        except Exception as e:
            logger.error(f"❌ Erro ao carregar configuração {config_id}: {e}")
            return None
    
    async def save_configuration(self, config: TestConfiguration) -> Path:
        """Salva uma configuração e retorna o path do arquivo."""

        try:
            config_file = self.config_dir / f"{config.config_id}.yaml"

            # Converter para dict
            config_dict = asdict(config)

            # Converter datetime para string
            config_dict['created_at'] = config.created_at.isoformat()

            # Salvar como YAML
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)

            logger.info(f"💾 Configuração salva: {config.config_id}")
            return config_file

        except Exception as e:
            logger.error(f"❌ Erro ao salvar configuração {config.config_id}: {e}")
            raise
    
    def list_configurations(self) -> List[Dict[str, Any]]:
        """Lista todas as configurações disponíveis."""
        
        configs = []
        
        for config_file in self.config_dir.glob("*.yaml"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                configs.append({
                    "config_id": config_data.get("config_id"),
                    "config_name": config_data.get("config_name"),
                    "created_at": config_data.get("created_at"),
                    "symbols": config_data.get("symbols", []),
                    "description": config_data.get("description", ""),
                    "tags": config_data.get("tags", [])
                })
                
            except Exception as e:
                logger.warning(f"⚠️ Erro ao ler {config_file}: {e}")
        
        return sorted(configs, key=lambda x: x.get("created_at", ""), reverse=True)
    
    async def validate_configuration(self, config: TestConfiguration) -> tuple[bool, List[str]]:
        """Valida uma configuração e retorna (is_valid, lista_de_erros)."""

        errors = []

        # Validar símbolos
        if not config.symbols:
            errors.append("Lista de símbolos não pode estar vazia")

        for symbol in config.symbols:
            if "/" not in symbol:
                errors.append(f"Formato de símbolo inválido: {symbol}")

        # Validar configuração de risco
        if config.risk_config.initial_capital <= 0:
            errors.append("Capital inicial deve ser positivo")

        # Validação mais flexível para capital baixo
        if config.risk_config.initial_capital < 100:
            errors.append("Capital inicial muito baixo (mínimo recomendado: $100)")

        if config.risk_config.max_position_size_pct <= 0 or config.risk_config.max_position_size_pct > 100:
            errors.append("Tamanho máximo de posição deve estar entre 0 e 100%")

        if config.risk_config.stop_loss_pct <= 0:
            errors.append("Stop loss deve ser positivo")

        # Validar configuração de estratégia
        if not config.strategy_config.name:
            errors.append("Nome da estratégia é obrigatório")

        # Validar duração do teste (se existir o campo)
        if hasattr(config, 'test_duration_hours') and config.test_duration_hours <= 0:
            errors.append("Duração do teste deve ser positiva")

        is_valid = len(errors) == 0
        return is_valid, errors
    
    def _create_strategy_config(self, strategy_params: Dict[str, Any]) -> StrategyConfig:
        """Cria configuração de estratégia."""

        # Verificar se é um template
        template_name = strategy_params.get("template")
        if template_name and template_name in self._strategy_templates:
            template = self._strategy_templates[template_name].copy()
            # Mesclar parâmetros do template com os fornecidos
            for key, value in strategy_params.items():
                if key != "template":
                    template[key] = value
            strategy_params = template

        strategy_name = strategy_params.get("name", template_name or "default_momentum")
        strategy_type = strategy_params.get("type", "momentum")
        
        return StrategyConfig(
            name=strategy_name,
            type=strategy_type,
            parameters=strategy_params.get("parameters", {}),
            indicators=strategy_params.get("indicators", {
                "rsi": {"period": 14, "overbought": 70, "oversold": 30},
                "ema": {"fast": 12, "slow": 26},
                "volume": {"period": 20}
            }),
            entry_conditions=strategy_params.get("entry_conditions", [
                "rsi < oversold",
                "price > ema_fast",
                "volume > avg_volume * 1.2"
            ]),
            exit_conditions=strategy_params.get("exit_conditions", [
                "rsi > overbought",
                "price < ema_slow",
                "stop_loss_hit",
                "take_profit_hit"
            ]),
            primary_timeframe=strategy_params.get("primary_timeframe", "1h"),
            secondary_timeframes=strategy_params.get("secondary_timeframes", ["5m", "15m"])
        )
    
    def _create_risk_config(self, risk_params: Dict[str, Any]) -> RiskManagementConfig:
        """Cria configuração de risco."""
        
        config = RiskManagementConfig()
        
        # Atualizar com parâmetros fornecidos
        for key, value in risk_params.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        return config
    
    def _create_execution_config(self, symbols: List[str], execution_settings: Optional[Dict[str, Any]] = None) -> ExecutionConfig:
        """Cria configuração de execução."""

        # Configuração padrão otimizada para os símbolos
        config = ExecutionConfig()

        # Ajustar configurações baseadas nos símbolos
        if any("BTC" in symbol for symbol in symbols):
            config.min_volume_usd = 1000000.0  # BTC tem mais volume
            config.max_spread_pct = 0.1

        if any("ETH" in symbol for symbol in symbols):
            config.min_volume_usd = 500000.0
            config.max_spread_pct = 0.2

        # Aplicar configurações customizadas se fornecidas
        if execution_settings:
            for key, value in execution_settings.items():
                if hasattr(config, key):
                    setattr(config, key, value)

        return config
    
    def _create_simulator_config(self, symbols: List[str], strategy: StrategyConfig) -> Dict[str, Any]:
        """Cria configuração específica do simulador."""
        
        return {
            "mode": "backtesting",
            "data_source": "historical",
            "slippage_model": "linear",
            "fee_model": "percentage",
            "latency_simulation": True,
            "latency_ms": 50,
            "symbols": symbols,
            "strategy": strategy.name,
            "enable_realistic_fills": True,
            "enable_market_impact": True
        }
    
    def _create_live_config(self, symbols: List[str], strategy: StrategyConfig) -> Dict[str, Any]:
        """Cria configuração específica do live trading."""
        
        return {
            "mode": "paper_trading",  # Seguro por padrão
            "exchange": "kucoin",
            "symbols": symbols,
            "strategy": strategy.name,
            "enable_live_feed": True,
            "enable_order_management": True,
            "risk_checks_enabled": True,
            "max_orders_per_minute": 10
        }
    
    def _load_strategy_templates(self) -> Dict[str, Dict[str, Any]]:
        """Carrega templates de estratégias."""
        
        templates = {
            "momentum_rsi": {
                "type": "momentum",
                "parameters": {
                    "rsi_period": 14,
                    "rsi_overbought": 70,
                    "rsi_oversold": 30,
                    "volume_multiplier": 1.5
                },
                "indicators": {
                    "rsi": {"period": 14},
                    "volume": {"period": 20},
                    "ema": {"fast": 12, "slow": 26}
                }
            },
            "mean_reversion": {
                "type": "mean_reversion",
                "parameters": {
                    "bollinger_period": 20,
                    "bollinger_std": 2.0,
                    "rsi_period": 14
                },
                "indicators": {
                    "bollinger": {"period": 20, "std": 2.0},
                    "rsi": {"period": 14}
                }
            }
        }
        
        return templates
    
    def _dict_to_test_configuration(self, data: Dict[str, Any]) -> TestConfiguration:
        """Converte dict para TestConfiguration."""
        
        # Converter datetime
        if isinstance(data.get("created_at"), str):
            data["created_at"] = datetime.fromisoformat(data["created_at"])
        
        # Converter sub-configurações
        if "strategy_config" in data:
            data["strategy_config"] = StrategyConfig(**data["strategy_config"])
        
        if "risk_config" in data:
            data["risk_config"] = RiskManagementConfig(**data["risk_config"])
        
        if "execution_config" in data:
            data["execution_config"] = ExecutionConfig(**data["execution_config"])
        
        return TestConfiguration(**data)
