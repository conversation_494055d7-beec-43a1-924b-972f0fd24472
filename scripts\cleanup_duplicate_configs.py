#!/usr/bin/env python3
"""
Script para limpeza de configurações duplicadas após consolidação de hiperparâmetros.

YAA REFINEMENT: Remove parâmetros duplicados de price_amp, news_amp, min_confidence
dos arquivos YAML existentes, mantendo apenas a referência ao hyperparams.yaml.
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, List
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ConfigCleaner:
    """Limpa configurações duplicadas dos arquivos YAML/JSON."""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.backup_dir = self.config_dir / "backups"
        self.backup_dir.mkdir(exist_ok=True)
        
        # Parâmetros que devem ser removidos (agora centralizados)
        self.duplicate_params = {
            "price_amplification",
            "news_amplification", 
            "pattern_threshold",
            "min_confidence",
            "metacognition_min_confidence",
            "confidence_threshold",
            "significance_threshold",
            "min_amplification",
            "max_amplification",
            "min_pattern_threshold",
            "max_pattern_threshold"
        }
        
        # Arquivos que devem ser processados
        self.target_files = [
            "holographic_enhanced_config.yaml",
            "strategy_parameters.yaml", 
            "strategy_parameters.json",
            "holographic_universe.yaml",
            "permissive_config.yaml",
            "metacognition_defaults.yaml"
        ]
    
    def backup_file(self, file_path: Path) -> Path:
        """Cria backup do arquivo antes da modificação."""
        backup_path = self.backup_dir / f"{file_path.name}.backup"
        backup_path.write_text(file_path.read_text(encoding='utf-8'), encoding='utf-8')
        logger.info(f"📁 Backup criado: {backup_path}")
        return backup_path
    
    def clean_yaml_config(self, file_path: Path) -> bool:
        """Limpa configurações duplicadas de um arquivo YAML."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or {}
            
            original_config = config.copy()
            changes_made = False
            
            # Remove seções de amplificação duplicadas
            if 'amplification' in config:
                logger.info(f"🧹 Removendo seção 'amplification' de {file_path.name}")
                del config['amplification']
                changes_made = True
            
            # Remove parâmetros duplicados em várias seções
            changes_made |= self._clean_nested_dict(config, file_path.name)
            
            # Adiciona referência ao hyperparams.yaml
            if changes_made:
                if 'hyperparams' not in config:
                    config['hyperparams'] = {
                        'source': '../qualia/config/hyperparams.yaml',
                        'note': 'Parâmetros de amplificação e confiança movidos para hyperparams.yaml'
                    }
                
                # Salva arquivo limpo
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
                
                logger.info(f"✅ Arquivo limpo: {file_path.name}")
                return True
            else:
                logger.info(f"ℹ️ Nenhuma mudança necessária: {file_path.name}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao processar {file_path}: {e}")
            return False
    
    def clean_json_config(self, file_path: Path) -> bool:
        """Limpa configurações duplicadas de um arquivo JSON."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            changes_made = False
            
            # Remove parâmetros duplicados
            changes_made |= self._clean_nested_dict(config, file_path.name)
            
            if changes_made:
                # Adiciona referência ao hyperparams.yaml
                if 'hyperparams' not in config:
                    config['hyperparams'] = {
                        'source': '../qualia/config/hyperparams.yaml',
                        'note': 'Parâmetros de amplificação e confiança movidos para hyperparams.yaml'
                    }
                
                # Salva arquivo limpo
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                
                logger.info(f"✅ Arquivo JSON limpo: {file_path.name}")
                return True
            else:
                logger.info(f"ℹ️ Nenhuma mudança necessária: {file_path.name}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao processar JSON {file_path}: {e}")
            return False
    
    def _clean_nested_dict(self, config: Dict[str, Any], filename: str) -> bool:
        """Remove parâmetros duplicados de dicionário aninhado."""
        changes_made = False
        
        def clean_recursive(obj: Any, path: str = "") -> bool:
            nonlocal changes_made
            local_changes = False
            
            if isinstance(obj, dict):
                keys_to_remove = []
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    # Remove parâmetros duplicados
                    if key in self.duplicate_params:
                        keys_to_remove.append(key)
                        logger.info(f"🧹 Removendo '{current_path}' de {filename}")
                        local_changes = True
                    else:
                        # Recursão para objetos aninhados
                        local_changes |= clean_recursive(value, current_path)
                
                # Remove as chaves marcadas
                for key in keys_to_remove:
                    del obj[key]
                    
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    local_changes |= clean_recursive(item, f"{path}[{i}]")
            
            return local_changes
        
        changes_made = clean_recursive(config)
        return changes_made
    
    def run_cleanup(self) -> Dict[str, Any]:
        """Executa limpeza completa dos arquivos de configuração."""
        results = {
            "processed": [],
            "cleaned": [],
            "errors": [],
            "skipped": []
        }
        
        logger.info("🚀 Iniciando limpeza de configurações duplicadas...")
        
        for filename in self.target_files:
            file_path = self.config_dir / filename
            
            if not file_path.exists():
                logger.warning(f"⚠️ Arquivo não encontrado: {file_path}")
                results["skipped"].append(str(file_path))
                continue
            
            results["processed"].append(str(file_path))
            
            # Cria backup
            self.backup_file(file_path)
            
            # Processa arquivo baseado na extensão
            try:
                if filename.endswith('.yaml') or filename.endswith('.yml'):
                    if self.clean_yaml_config(file_path):
                        results["cleaned"].append(str(file_path))
                elif filename.endswith('.json'):
                    if self.clean_json_config(file_path):
                        results["cleaned"].append(str(file_path))
                else:
                    logger.warning(f"⚠️ Tipo de arquivo não suportado: {filename}")
                    results["skipped"].append(str(file_path))
                    
            except Exception as e:
                logger.error(f"❌ Erro ao processar {file_path}: {e}")
                results["errors"].append({"file": str(file_path), "error": str(e)})
        
        # Relatório final
        logger.info("📊 Relatório de limpeza:")
        logger.info(f"   Processados: {len(results['processed'])}")
        logger.info(f"   Limpos: {len(results['cleaned'])}")
        logger.info(f"   Erros: {len(results['errors'])}")
        logger.info(f"   Ignorados: {len(results['skipped'])}")
        
        if results["cleaned"]:
            logger.info("✅ Arquivos limpos:")
            for file in results["cleaned"]:
                logger.info(f"   - {file}")
        
        if results["errors"]:
            logger.error("❌ Erros encontrados:")
            for error in results["errors"]:
                logger.error(f"   - {error['file']}: {error['error']}")
        
        return results


def main():
    """Função principal do script."""
    cleaner = ConfigCleaner()
    results = cleaner.run_cleanup()
    
    # Exit code baseado nos resultados
    if results["errors"]:
        exit(1)
    else:
        exit(0)


if __name__ == "__main__":
    main()
