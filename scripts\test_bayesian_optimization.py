#!/usr/bin/env python3
"""
QUALIA Bayesian Optimization Test Suite
Testa o sistema de otimização Bayesiana implementado na Etapa D
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.utils.logger import get_logger, setup_logging
from qualia.optimization.bayesian_optimizer import BayesianOptimizer, OptimizationConfig
from qualia.optimization.parameter_tuner import ParameterTuner, TunerConfig
from qualia.optimization.optimization_grpc_service import OptimizationGRPCService, OptimizationGRPCClient

logger = get_logger(__name__)

class BayesianOptimizationTester:
    """
    Suite de testes para o sistema de otimização Bayesiana.
    """
    
    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()
        
        logger.info("🧪 BayesianOptimizationTester inicializado")
    
    async def run_all_tests(self):
        """Executa todos os testes."""
        logger.info("🚀 Iniciando suite de testes do sistema de otimização Bayesiana")
        logger.info("=" * 60)
        
        tests = [
            ("test_bayesian_optimizer", self.test_bayesian_optimizer),
            ("test_parameter_tuner", self.test_parameter_tuner),
            ("test_grpc_service", self.test_grpc_service),
            ("test_integration", self.test_integration),
            ("test_benchmark_integration", self.test_benchmark_integration)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            logger.info(f"🔬 Executando: {test_name}")
            try:
                result = await test_func()
                if result:
                    logger.info(f"✅ {test_name}: PASSOU")
                    passed += 1
                else:
                    logger.error(f"❌ {test_name}: FALHOU")
                    failed += 1
                self.test_results[test_name] = result
            except Exception as e:
                logger.error(f"💥 {test_name}: ERRO - {e}")
                self.test_results[test_name] = False
                failed += 1
            
            logger.info("-" * 40)
        
        # Resumo final
        total_time = (datetime.now() - self.start_time).total_seconds()
        logger.info("=" * 60)
        logger.info("📊 RESUMO DOS TESTES")
        logger.info(f"✅ Passou: {passed}")
        logger.info(f"❌ Falhou: {failed}")
        logger.info(f"⏱️ Tempo total: {total_time:.1f}s")
        logger.info("=" * 60)
        
        return passed, failed
    
    async def test_bayesian_optimizer(self) -> bool:
        """Testa o BayesianOptimizer."""
        try:
            logger.info("🧠 Testando BayesianOptimizer...")
            
            # Criar configuração de teste
            config = OptimizationConfig(
                n_trials_per_cycle=5,  # Reduzido para teste rápido
                optimization_interval_cycles=10,
                lookback_hours=1
            )
            
            # Criar otimizador
            optimizer = BayesianOptimizer(config)
            
            # Teste 1: Verificar inicialização
            assert optimizer.config.n_trials_per_cycle == 5
            assert len(optimizer.current_parameters) > 0
            logger.info("  ✓ Inicialização OK")
            
            # Teste 2: Otimizar um símbolo
            result = await optimizer.optimize_symbol("BTCUSDT")
            assert result is not None
            assert result.symbol == "BTCUSDT"
            assert "price_amplification" in result.parameters
            assert "news_amplification" in result.parameters
            assert "min_confidence" in result.parameters
            logger.info(f"  ✓ Otimização OK: objetivo={result.objective_value:.4f}")
            
            # Teste 3: Otimizar múltiplos símbolos
            symbols = ["ETHUSDT", "BNBUSDT"]
            results = await optimizer.optimize_all_symbols(symbols)
            assert len(results) == len(symbols)
            logger.info(f"  ✓ Otimização múltipla OK: {len(results)} símbolos")
            
            # Teste 4: Verificar parâmetros atuais
            params = optimizer.get_current_parameters("BTCUSDT")
            assert isinstance(params, dict)
            assert len(params) == 3
            logger.info("  ✓ Parâmetros atuais OK")
            
            # Teste 5: Salvar/carregar estado
            optimizer.save_optimization_state("data/test_optimizer_state.json")
            assert Path("data/test_optimizer_state.json").exists()
            logger.info("  ✓ Persistência OK")
            
            # Cleanup
            await optimizer.cleanup()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro no teste BayesianOptimizer: {e}")
            return False
    
    async def test_parameter_tuner(self) -> bool:
        """Testa o ParameterTuner."""
        try:
            logger.info("🔧 Testando ParameterTuner...")
            
            # Criar configuração de teste
            config = TunerConfig(
                symbols=["BTCUSDT", "ETHUSDT"],
                optimization_interval_cycles=5,  # Reduzido para teste
                n_trials_per_cycle=3,
                grpc_enabled=False  # Desabilitar gRPC para teste simples
            )
            
            # Criar tuner
            tuner = ParameterTuner(config)
            
            # Teste 1: Verificar inicialização
            assert len(tuner.active_symbols) == 2
            assert tuner.config.optimization_interval_cycles == 5
            logger.info("  ✓ Inicialização OK")
            
            # Teste 2: Verificar parâmetros atuais
            params = tuner.get_current_parameters("BTCUSDT")
            assert isinstance(params, dict)
            logger.info("  ✓ Parâmetros atuais OK")
            
            # Teste 3: Verificar status
            status = tuner.get_status()
            assert "is_running" in status
            assert "active_symbols" in status
            logger.info("  ✓ Status OK")
            
            # Teste 4: Simular alguns ciclos
            for i in range(6):  # Mais que optimization_interval_cycles
                tuner.optimizer.cycle_counter += 1
            
            should_optimize = tuner.optimizer.should_optimize()
            assert should_optimize  # Deve ser True após 5+ ciclos
            logger.info("  ✓ Lógica de ciclos OK")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro no teste ParameterTuner: {e}")
            return False
    
    async def test_grpc_service(self) -> bool:
        """Testa o serviço gRPC."""
        try:
            logger.info("🌐 Testando OptimizationGRPCService...")
            
            # Criar serviço
            service = OptimizationGRPCService(port=50052)  # Porta diferente para teste
            
            # Teste 1: Inicializar serviço
            await service.start()
            assert service.is_running
            logger.info("  ✓ Inicialização OK")
            
            # Teste 2: Testar get_current_parameters
            response = await service.get_current_parameters("BTCUSDT")
            assert response.success
            assert "parameters" in response.data
            logger.info("  ✓ Get parameters OK")
            
            # Teste 3: Testar update_parameters
            new_params = {
                "price_amplification": 5.0,
                "news_amplification": 8.0,
                "min_confidence": 0.45
            }
            response = await service.update_parameters("BTCUSDT", new_params)
            assert response.success
            logger.info("  ✓ Update parameters OK")
            
            # Teste 4: Verificar se parâmetros foram atualizados
            response = await service.get_current_parameters("BTCUSDT")
            assert response.success
            updated_params = response.data["parameters"]
            assert updated_params["price_amplification"] == 5.0
            logger.info("  ✓ Verificação de atualização OK")
            
            # Teste 5: Testar get_optimization_status
            response = await service.get_optimization_status()
            assert response.success
            assert "is_running" in response.data
            logger.info("  ✓ Status OK")
            
            # Cleanup
            await service.stop()
            assert not service.is_running
            logger.info("  ✓ Shutdown OK")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro no teste gRPC: {e}")
            return False
    
    async def test_integration(self) -> bool:
        """Testa a integração entre componentes."""
        try:
            logger.info("🔗 Testando integração entre componentes...")
            
            # Criar componentes
            optimizer = BayesianOptimizer(OptimizationConfig(n_trials_per_cycle=3))
            service = OptimizationGRPCService(port=50053)
            
            # Inicializar serviço
            await service.start()
            
            # Teste 1: Otimizar e notificar via gRPC
            result = await optimizer.optimize_symbol("ETHUSDT")
            assert result is not None
            
            # Notificar via gRPC
            await service.notify_parameter_update(result.symbol, result.parameters)
            
            # Verificar se parâmetros foram atualizados no serviço
            response = await service.get_current_parameters(result.symbol)
            assert response.success
            service_params = response.data["parameters"]
            
            # Comparar parâmetros
            for key, value in result.parameters.items():
                assert abs(service_params[key] - value) < 0.001
            
            logger.info("  ✓ Integração otimizador-gRPC OK")
            
            # Cleanup
            await service.stop()
            await optimizer.cleanup()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro no teste de integração: {e}")
            return False
    
    async def test_benchmark_integration(self) -> bool:
        """Testa a integração com resultados do benchmark."""
        try:
            logger.info("📊 Testando integração com benchmark offline...")
            
            # Verificar se arquivo de benchmark existe
            benchmark_file = Path("data/benchmark_results_20250706_141236_best_configs.json")
            
            if not benchmark_file.exists():
                logger.warning("  ⚠️ Arquivo de benchmark não encontrado, criando dados de teste...")
                
                # Criar dados de teste
                test_data = {
                    "metadata": {
                        "generated_at": datetime.now().isoformat(),
                        "total_tested": 100,
                        "top_n": 3
                    },
                    "best_configurations": [
                        {
                            "rank": 1,
                            "symbol": "ETHUSDT",
                            "parameters": {
                                "price_amplification": 10.0,
                                "news_amplification": 1.0,
                                "min_confidence": 0.3
                            },
                            "metrics": {
                                "sharpe_ratio": 20.5,
                                "total_return": 4419482.96,
                                "max_drawdown": 0.0,
                                "win_rate": 0.91
                            }
                        }
                    ]
                }
                
                benchmark_file.parent.mkdir(parents=True, exist_ok=True)
                with open(benchmark_file, 'w', encoding='utf-8') as f:
                    json.dump(test_data, f, indent=2, ensure_ascii=False)
            
            # Criar otimizador que deve carregar dados do benchmark
            optimizer = BayesianOptimizer()
            
            # Verificar se parâmetros foram carregados do benchmark
            assert len(optimizer.current_parameters) > 0
            logger.info(f"  ✓ Parâmetros carregados: {len(optimizer.current_parameters)} símbolos")
            
            # Verificar se ETHUSDT tem parâmetros do benchmark
            if "ETHUSDT" in optimizer.current_parameters:
                eth_params = optimizer.current_parameters["ETHUSDT"]
                # Verificar se tem os parâmetros esperados do benchmark
                assert "price_amplification" in eth_params
                assert "news_amplification" in eth_params
                assert "min_confidence" in eth_params
                logger.info("  ✓ Parâmetros do benchmark carregados para ETHUSDT")
            
            # Verificar estatísticas do benchmark
            if optimizer.optimization_stats.get("best_parameters_per_symbol"):
                logger.info("  ✓ Estatísticas do benchmark disponíveis")
            
            await optimizer.cleanup()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro no teste de integração com benchmark: {e}")
            return False

async def main():
    """Função principal."""
    # Configurar logging
    setup_logging()
    
    logger.info("🧪 QUALIA BAYESIAN OPTIMIZATION TEST SUITE")
    logger.info("=" * 60)
    
    # Executar testes
    tester = BayesianOptimizationTester()
    passed, failed = await tester.run_all_tests()
    
    # Salvar resultados
    results_file = Path("data/test_results_bayesian_optimization.json")
    results_file.parent.mkdir(parents=True, exist_ok=True)
    
    test_summary = {
        "timestamp": datetime.now().isoformat(),
        "total_tests": passed + failed,
        "passed": passed,
        "failed": failed,
        "success_rate": passed / (passed + failed) if (passed + failed) > 0 else 0,
        "test_results": tester.test_results
    }
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(test_summary, f, indent=2, ensure_ascii=False)
    
    logger.info(f"📄 Resultados salvos em: {results_file}")
    
    # Exit code baseado nos resultados
    if failed == 0:
        logger.info("🎉 Todos os testes passaram!")
        sys.exit(0)
    else:
        logger.error(f"💥 {failed} teste(s) falharam!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
