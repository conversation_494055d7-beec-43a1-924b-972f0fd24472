# Qualia Event Bus

O `SimpleEventBus` promove comunicação interna entre os módulos do QUALIA. Cada tópico representa um fluxo de dados específico. A partir da versão atual, existe também o módulo `qualia.event_bus` com uma implementação assíncrona (`AsyncEventBus`) construída sobre `aiopubsub`.

Quando o tracing está habilitado, o `trace_id` ativo é adicionado automaticamente ao campo `metadata` dos eventos, permitindo correlação entre spans e mensagens.
Os eventos `MarketDataUpdated` e `TradingSignalGenerated` definem cargas de dados padronizadas para o ciclo de decisão.

## market.alert
Alerta as estratégias e controladores sobre condições de mercado ou notícias relevantes.

Campos esperados:

- `type`: categoria do alerta, como `"volatility"` ou `"news"`.
- `level`: intensidade numérica ou textual do evento.
- `parameters`: dicionário opcional com ajustes para `QualiaTSVFParams`.
- `drc_parameters`: dicionário opcional para ajustes do `DynamicRiskController`.
  Quando o ``AsyncEventBus`` é utilizado, publique um
  ``DynamicRiskParameterUpdate`` em ``dynamic_risk.update`` para alterar
  parâmetros em tempo real.

Outros eventos estão descritos nos respectivos módulos e relatórios de integração.

## MarketDataUpdated
Indica que novos dados de mercado e notícias foram coletados. Normalmente é publicado a partir do _loop_ de coleta de dados e recebido pelos módulos de análise ou predição.

Campos do `dataclass`:

- `market_data`: lista de `MarketDataPoint` capturados no intervalo.
- `news_events`: lista de `NewsEvent` correlatos.

## TradingSignalGenerated
Sinaliza que o sistema validou decisões de compra ou venda. É o gancho para os módulos de execução ou monitoramento.

Campos do `dataclass`:

- `decisions`: lista de `OracleDecision` avaliadas.

### Registrando assinantes
O exemplo abaixo utiliza o `AsyncEventBus` do módulo `qualia.event_bus`:

```python
from qualia.event_bus import AsyncEventBus, MarketDataUpdated, TradingSignalGenerated

bus = AsyncEventBus()

async def on_data(payload: MarketDataUpdated) -> None:
    print(payload.market_data)

async def on_signal(payload: TradingSignalGenerated) -> None:
    print(payload.decisions)

bus.subscribe("market.data.updated", on_data)
bus.subscribe("trading.signal.generated", on_signal)

bus.publish("market.data.updated", MarketDataUpdated(market_data=[], news_events=[]))
bus.publish("trading.signal.generated", TradingSignalGenerated(decisions=[]))
```

## market.pattern_detected
Evento emitido pelos detectores de padrões de mercado ao identificar correlações
relevantes. O payload segue o dataclass
`MarketPatternDetected` em `qualia.market.event_bus`.

Campos do `dataclass`:

- `vector`: vetor numérico representando o padrão.
- `metadata`: dicionário opcional com detalhes do padrão.

Para gravar esses padrões na memória, registre o manipulador
`register_market_pattern_handler` (ou
`register_market_pattern_handler_async` para o
``AsyncEventBus``) disponível em `qualia.memory.qpm_interface` ou
forneça um ``event_bus`` ao ``MemoryService``.

## OracleDecisionEvent
Evento publicado após o *colapso por coerência* pelo `QASTOracleDecisionEngine`.
Representa a decisão final sobre uma oportunidade de trade.

Campos do `dataclass`:
- `symbol`: par de negociação analisado.
- `timeframe`: intervalo de tempo da decisão.
- `decision`: ação sugerida (`BUY`, `SELL` ou `HOLD`).
- `confidence`: grau de confiança na decisão.
- `details`: metadados adicionais sobre o colapso.

Exemplo de assinatura de manipulador:
```python
from qualia.event_bus import SimpleEventBus, OracleDecisionEvent

bus = SimpleEventBus()

def on_decision(event: OracleDecisionEvent) -> None:
    print(event.symbol, event.decision)

bus.subscribe(OracleDecisionEvent, on_decision)
```

## ParadoxEvent

Evento publicado pelo [`MetacognitiveEngine`](../src/qualia/metacognition/metacognitive_engine.py)
sempre que um paradoxo é detectado ou resolvido durante o ciclo de análise.

Campos do `dataclass`:

- `symbol`: par de negociação relacionado ao paradoxo.
- `coherence`: valor de coerência observado no momento.
- `details`: dicionário com informações adicionais.

Exemplo de assinatura de manipulador:
```python
from qualia.event_bus import SimpleEventBus
from qualia.events import ParadoxEvent

bus = SimpleEventBus()

def on_paradox(event: ParadoxEvent) -> None:
    print(event.symbol, event.coherence)

bus.subscribe("metacognition.paradox", on_paradox)
```
