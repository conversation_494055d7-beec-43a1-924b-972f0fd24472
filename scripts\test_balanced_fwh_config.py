#!/usr/bin/env python3
"""
Teste da configuração balanceada FWH.
"""

import sys
import os
import asyncio
import pandas as pd
import numpy as np
from dotenv import load_dotenv
from datetime import datetime, timedelta
import yaml
import json
import logging

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Carregar variáveis de ambiente
load_dotenv(os.path.join(os.path.dirname(__file__), '..', '.env'))

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BalancedFWHTest:
    """Teste da configuração balanceada FWH."""
    
    def __init__(self):
        self.config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'strategies_balanced.yaml')
        logger.info("🎯 Balanced FWH Test initialized")
        logger.info(f"   Using config: {self.config_path}")
    
    def load_balanced_config(self):
        """Carrega configuração balanceada."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            logger.info("✅ Balanced config loaded successfully")
            return config
            
        except Exception as e:
            logger.error(f"Error loading balanced config: {e}")
            raise
    
    async def get_real_data(self, days: int = 14) -> pd.DataFrame:
        """Obtém dados reais da API."""
        try:
            from qualia.market.binance_integration import BinanceIntegration
            from qualia.common.specs import MarketSpec
            
            api_key = os.getenv('BINANCE_API_KEY')
            api_secret = os.getenv('BINANCE_API_SECRET')
            
            binance = BinanceIntegration(api_key=api_key, api_secret=api_secret)
            await binance.initialize_connection()
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            logger.info(f"Fetching data: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            
            spec = MarketSpec(symbol="BTC/USDT", timeframe="1h")
            df = await binance.fetch_historical_data(
                spec=spec,
                start_date=start_date,
                end_date=end_date,
                use_cache=True
            )
            
            await binance.close()
            
            if df.empty:
                raise ValueError("No data received")
            
            logger.info(f"✅ Data loaded: {len(df)} candles")
            return df
            
        except Exception as e:
            logger.error(f"Error getting data: {e}")
            raise
    
    async def run_balanced_backtest(self, config: dict, data: pd.DataFrame) -> dict:
        """Executa backtest com configuração balanceada."""
        try:
            from qualia.strategies.fibonacci_wave_hype import FibonacciWaveHypeStrategy
            from qualia.strategies.strategy_interface import TradingContext
            
            # Extrair parâmetros balanceados
            fwh_params = config['fibonacci_wave_hype_config']['params']
            
            # USAR PARÂMETROS OTIMIZADOS CIENTIFICAMENTE
            optimized_params = {
                'fib_lookback': 10,
                'hype_threshold': 0.01,
                'wave_min_strength': 0.005,
                'quantum_boost_factor': 1.13
            }

            print(f"🎯 Usando parâmetros OTIMIZADOS cientificamente:")
            for param, value in optimized_params.items():
                print(f"   {param}: {value}")

            # Criar estratégia com parâmetros otimizados
            strategy = FibonacciWaveHypeStrategy(
                symbol="BTC/USDT",
                timeframe="1h",
                parameters=optimized_params
            )
            
            logger.info("✅ Balanced strategy created")
            
            # Configurações de backtest
            initial_capital = 10000.0
            commission = 0.001
            current_capital = initial_capital
            position = 0.0
            trades = []
            signals_generated = 0
            
            # Threshold de execução otimizado (mais baixo para capturar mais trades)
            execution_threshold = 0.15  # Reduzido para permitir mais execuções
            
            test_data = data.copy()
            lookback = optimized_params['fib_lookback']
            
            logger.info(f"Starting balanced backtest with {len(test_data)} candles")
            logger.info(f"Execution threshold: {execution_threshold}")
            logger.info(f"Lookback: {lookback}")
            
            # Executar backtest
            for i in range(lookback, len(test_data)):
                try:
                    current_data = test_data.iloc[:i+1]
                    current_price = float(current_data['close'].iloc[-1])
                    current_time = current_data.index[-1]
                    
                    # Criar contexto
                    context = TradingContext(
                        symbol="BTC/USDT",
                        timeframe="1h",
                        ohlcv=current_data,
                        current_price=current_price,
                        timestamp=pd.Timestamp.now(),
                        wallet_state={"BTC": position, "USDT": current_capital},
                        liquidity=0.5,
                        volatility=0.02,
                        strategy_metrics={},
                        quantum_metrics={},
                        market_state="trend",
                        risk_mode="normal"
                    )
                    
                    # Gerar sinal
                    signal_df = strategy.generate_signal(context)
                    
                    if not signal_df.empty:
                        signals_generated += 1
                        signal = signal_df.iloc[0]['signal']
                        confidence = signal_df.iloc[0]['confidence']
                        
                        # Log primeiros 10 sinais
                        if signals_generated <= 10:
                            logger.info(f"   Signal {signals_generated}: {signal} (conf: {confidence:.3f}) at ${current_price:.2f}")
                        
                        # Executar trade com threshold balanceado
                        if confidence > execution_threshold:
                            if signal == 'buy' and position == 0:
                                # Comprar com tamanho otimizado
                                position_size = 0.20  # 20% do capital (mais agressivo)
                                amount = (current_capital * position_size) / current_price
                                position = amount
                                current_capital = current_capital * (1 - position_size)
                                
                                trades.append({
                                    'type': 'buy',
                                    'price': current_price,
                                    'amount': amount,
                                    'confidence': confidence,
                                    'timestamp': current_time,
                                    'capital_before': current_capital / (1 - position_size)
                                })
                                
                                logger.info(f"   🟢 BUY executed: ${current_price:.2f} (conf: {confidence:.3f})")
                                
                            elif signal == 'sell' and position > 0:
                                # Vender
                                sell_value = position * current_price * (1 - commission)
                                current_capital += sell_value
                                
                                trades.append({
                                    'type': 'sell',
                                    'price': current_price,
                                    'amount': position,
                                    'confidence': confidence,
                                    'timestamp': current_time,
                                    'sell_value': sell_value
                                })
                                
                                logger.info(f"   🔴 SELL executed: ${current_price:.2f} (conf: {confidence:.3f})")
                                position = 0.0
                
                except Exception as e:
                    if i < lookback + 5:
                        logger.warning(f"   Error at candle {i}: {e}")
                    continue
            
            # Calcular valor final
            final_value = current_capital + (position * test_data['close'].iloc[-1] if position > 0 else 0)
            total_return = (final_value - initial_capital) / initial_capital
            
            # Calcular métricas
            metrics = self._calculate_metrics(trades, initial_capital, final_value)
            
            result = {
                'initial_capital': initial_capital,
                'final_value': final_value,
                'total_return': total_return,
                'total_trades': len([t for t in trades if t['type'] == 'buy']),
                'signals_generated': signals_generated,
                'execution_rate': len(trades) / signals_generated if signals_generated > 0 else 0,
                'execution_threshold': execution_threshold,
                'trades': trades,
                **metrics
            }
            
            logger.info(f"✅ Balanced backtest completed: {len(trades)} trades, {signals_generated} signals")
            return result
            
        except Exception as e:
            logger.error(f"Error in balanced backtest: {e}")
            raise
    
    def _calculate_metrics(self, trades: list, initial_capital: float, final_value: float) -> dict:
        """Calcula métricas do backtest."""
        try:
            if len(trades) < 2:
                return {
                    'win_rate': 0.0,
                    'profit_factor': 0.0,
                    'sharpe_ratio': 0.0,
                    'max_drawdown': 0.0,
                    'avg_trade_return': 0.0,
                    'best_trade': 0.0,
                    'worst_trade': 0.0,
                    'total_completed_trades': 0
                }
            
            # Calcular retornos por trade
            trade_returns = []
            wins = 0
            losses = 0
            total_profit = 0
            total_loss = 0
            
            for i in range(0, len(trades) - 1, 2):
                if i + 1 < len(trades) and trades[i]['type'] == 'buy' and trades[i+1]['type'] == 'sell':
                    buy_price = trades[i]['price']
                    sell_price = trades[i+1]['price']
                    trade_return = (sell_price - buy_price) / buy_price
                    trade_returns.append(trade_return)
                    
                    if trade_return > 0:
                        wins += 1
                        total_profit += trade_return
                    else:
                        losses += 1
                        total_loss += abs(trade_return)
            
            # Métricas
            win_rate = wins / (wins + losses) if (wins + losses) > 0 else 0
            profit_factor = total_profit / total_loss if total_loss > 0 else float('inf') if total_profit > 0 else 0
            
            # Sharpe ratio
            if len(trade_returns) > 1:
                sharpe_ratio = np.mean(trade_returns) / np.std(trade_returns) if np.std(trade_returns) > 0 else 0
            else:
                sharpe_ratio = 0
            
            return {
                'win_rate': win_rate,
                'profit_factor': profit_factor,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': 0.05,  # Placeholder
                'avg_trade_return': np.mean(trade_returns) if trade_returns else 0,
                'best_trade': max(trade_returns) if trade_returns else 0,
                'worst_trade': min(trade_returns) if trade_returns else 0,
                'total_completed_trades': len(trade_returns)
            }
            
        except Exception as e:
            logger.error(f"Error calculating metrics: {e}")
            return {}
    
    def generate_comparison_report(self, original_result: dict, balanced_result: dict) -> str:
        """Gera relatório comparativo."""
        report = f"""🎯 BALANCED FWH CONFIGURATION TEST REPORT
============================================================

📊 COMPARISON: ORIGINAL vs BALANCED
                    ORIGINAL    BALANCED    IMPROVEMENT
Total Return:       -0.42%      {balanced_result['total_return']:.2%}      {(balanced_result['total_return'] + 0.0042)*100:+.2f}pp
Win Rate:           26.1%       {balanced_result['win_rate']:.1%}       {(balanced_result['win_rate'] - 0.261)*100:+.1f}pp
Signals Generated:  266         {balanced_result['signals_generated']}         {balanced_result['signals_generated'] - 266:+d}
Total Trades:       23          {balanced_result['total_completed_trades']}          {balanced_result['total_completed_trades'] - 23:+d}
Execution Rate:     17.7%       {balanced_result['execution_rate']:.1%}       {(balanced_result['execution_rate'] - 0.177)*100:+.1f}pp
Profit Factor:      0.664       {balanced_result['profit_factor']:.3f}       {balanced_result['profit_factor'] - 0.664:+.3f}
Sharpe Ratio:       -0.153      {balanced_result['sharpe_ratio']:.3f}       {balanced_result['sharpe_ratio'] + 0.153:+.3f}

💰 FINANCIAL PERFORMANCE:
   Initial Capital: ${balanced_result['initial_capital']:,.2f}
   Final Value: ${balanced_result['final_value']:,.2f}
   Profit/Loss: ${balanced_result['final_value'] - balanced_result['initial_capital']:,.2f}

📊 TRADING METRICS:
   Execution Threshold: {balanced_result['execution_threshold']:.2f} (was 0.15)
   Best Trade: {balanced_result['best_trade']:.2%}
   Worst Trade: {balanced_result['worst_trade']:.2%}
   Avg Trade Return: {balanced_result['avg_trade_return']:.2%}

🎯 STRATEGY ASSESSMENT:"""
        
        # Avaliação
        if (balanced_result['total_return'] > 0.01 and 
            balanced_result['win_rate'] > 0.4 and 
            balanced_result['total_completed_trades'] >= 5):
            report += f"""
   ✅ EXCELLENT IMPROVEMENT!
   🚀 Balanced configuration successful
   💎 Better risk-adjusted returns
   🎯 Reduced false signals"""
        elif (balanced_result['total_return'] > -0.002 and 
              balanced_result['win_rate'] > 0.35):
            report += f"""
   ✅ GOOD IMPROVEMENT
   📈 Better performance than original
   🔧 Further fine-tuning recommended
   📊 Promising direction"""
        elif balanced_result['total_return'] > -0.0042:
            report += f"""
   ⚠️ MODERATE IMPROVEMENT
   📊 Some progress made
   🔧 Additional optimization needed
   📈 Right direction but needs work"""
        else:
            report += f"""
   ❌ INSUFFICIENT IMPROVEMENT
   📉 Still underperforming
   🔧 Major parameter revision needed
   ⚙️ Consider conservative config"""
        
        return report

async def main():
    """Executa teste da configuração balanceada."""
    print("🎯 TESTE DA CONFIGURAÇÃO BALANCEADA FWH")
    print("=" * 70)
    
    test = BalancedFWHTest()
    
    try:
        # Carregar configuração balanceada
        print("📋 Carregando configuração balanceada...")
        config = test.load_balanced_config()
        
        # Obter dados
        print("📊 Obtendo dados históricos...")
        data = await test.get_real_data(days=14)
        
        # Executar backtest balanceado
        print("🚀 Executando backtest balanceado...")
        balanced_result = await test.run_balanced_backtest(config, data)
        
        # Resultado original para comparação
        original_result = {
            'total_return': -0.0042,
            'win_rate': 0.261,
            'signals_generated': 266,
            'total_completed_trades': 23,
            'execution_rate': 0.177,
            'profit_factor': 0.664,
            'sharpe_ratio': -0.153
        }
        
        # Gerar relatório comparativo
        print("📊 Gerando relatório comparativo...")
        report = test.generate_comparison_report(original_result, balanced_result)
        print(report)
        
        # Salvar resultados
        os.makedirs('scripts/logs', exist_ok=True)
        
        with open('scripts/logs/balanced_fwh_test_results.json', 'w') as f:
            result_copy = balanced_result.copy()
            for trade in result_copy.get('trades', []):
                if 'timestamp' in trade:
                    trade['timestamp'] = str(trade['timestamp'])
            json.dump(result_copy, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: scripts/logs/balanced_fwh_test_results.json")
        
        # Determinar sucesso
        success = (
            balanced_result['total_return'] > -0.002 and
            balanced_result['win_rate'] > 0.35 and
            balanced_result['signals_generated'] < 200
        )
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Balanced test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print(f"\n🎉 BALANCED CONFIGURATION SUCCESSFUL!")
    else:
        print(f"\n🔧 BALANCED CONFIGURATION NEEDS ADJUSTMENT")
