# Métricas do CoherenceAggregator

O `CoherenceAggregator` consolida os votos de múltiplas estratégias a partir do Event Bus. Cada vez que um novo sinal é recebido em `strategy.signal`, o agregador calcula métricas que indicam o nível de consenso dos agentes.

## agreement_ratio

Porcentagem de votos (ponderados pelos pesos das estratégias) que apoiam o sinal final. Valores próximos de `1.0` indicam forte concordância entre as estratégias.

## voters

Quantidade de estratégias que enviaram sinal no ciclo corrente. Esse número ajuda a monitorar se alguma estratégia ficou ausente.

As métricas são publicadas junto ao evento `trading.signal.generated`, permitindo avaliação posterior em backtests e painéis de monitoramento.
