import argparse
import json
import types
from unittest.mock import AsyncMock
import pytest

import src.backtest.backtest_harness as backtest_harness


class DummyTrader:
    def __init__(
        self,
        symbols,
        timeframes,
        capital,
        risk_profile,
        mode="simulation",
        data_source="simulation",
        duration_seconds=None,
        disable_metacognition=False,
        config=None,
        strategy_config_path=None,
        trading_fee_pct=0.0,
        risk_per_trade_pct=None,
        **kwargs,
    ):
        DummyTrader.created_config = config
        self.market_data = {}
        self.current_tickers = {}
        self.wallet_state = {"initial_capital": capital, "current_capital": capital}
        self.trade_history = []
        self.hud_manager = types.SimpleNamespace(display_wallet_status=AsyncMock())

    async def _analyze_symbols(self):
        pass


@pytest.mark.asyncio
async def test_disable_qast_evolution_flag(monkeypatch, tmp_path):
    monkeypatch.setenv(
        "QUALIA_SECRET_KEY",
        "MDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDA=",
    )
    csv = tmp_path / "data.csv"
    csv.write_text("timestamp,open,high,low,close,volume\n")
    cfg_file = tmp_path / "strategy.json"
    cfg_file.write_text(json.dumps({"risk_profile_settings": {"custom": {}}}))

    monkeypatch.setattr(backtest_harness, "QUALIARealTimeTrader", DummyTrader)

    args = argparse.Namespace(
        historical_data_path=str(csv),
        symbol="BTC/USDT",
        timeframe="1m",
        initial_capital=1000.0,
        output_report_path=None,
        log_level="INFO",
        log_file=str(tmp_path / "log.txt"),
        disable_metacognition=False,
        disable_qast_evolution=True,
        disable_ace_optimization=False,
        stop_on_error=False,
        risk_profile="custom",
        risk_per_trade_pct=1.0,
        stop_loss_pct=0.02,
        take_profit_pct=0.04,
        trading_fee_pct=0.0026,
        strategy_config_json=str(cfg_file),
        max_position_capital_pct=None,
    )

    metrics = await backtest_harness.run_backtest(args)
    assert DummyTrader.created_config["enable_qast_evolution"] is False


@pytest.mark.asyncio
async def test_enable_qast_evolution_default(monkeypatch, tmp_path):
    monkeypatch.setenv(
        "QUALIA_SECRET_KEY",
        "MDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDA=",
    )
    csv = tmp_path / "data.csv"
    csv.write_text("timestamp,open,high,low,close,volume\n")
    cfg_file = tmp_path / "strategy.json"
    cfg_file.write_text(json.dumps({"risk_profile_settings": {"custom": {}}}))

    monkeypatch.setattr(backtest_harness, "QUALIARealTimeTrader", DummyTrader)

    args = argparse.Namespace(
        historical_data_path=str(csv),
        symbol="BTC/USDT",
        timeframe="1m",
        initial_capital=1000.0,
        output_report_path=None,
        log_level="INFO",
        log_file=str(tmp_path / "log.txt"),
        disable_metacognition=False,
        disable_qast_evolution=False,
        disable_ace_optimization=False,
        stop_on_error=False,
        risk_profile="custom",
        risk_per_trade_pct=1.0,
        stop_loss_pct=0.02,
        take_profit_pct=0.04,
        trading_fee_pct=0.0026,
        strategy_config_json=str(cfg_file),
        max_position_capital_pct=None,
    )

    metrics = await backtest_harness.run_backtest(args)
    assert DummyTrader.created_config["enable_qast_evolution"] is True
