#!/usr/bin/env python
"""
QUALIA Enhanced Holographic Trading System - Deploy Script

Este script demonstra a integração completa de TODOS os recursos QUALIA:
- Encoders quânticos (RSI, Volume, Sentiment)
- Dados OHLCV completos com múltiplos timeframes
- Indicadores técnicos avançados
- Sistema holográfico com padrões quânticos
- Integração completa com KuCoin

Uso:
    python scripts/deploy_enhanced_holographic_trading.py
"""

import asyncio
import logging
import os
import sys
import yaml
import time
from pathlib import Path
from typing import Dict, Any

# Load environment variables from .env file
try:
    from dotenv import load_dotenv

    load_dotenv()
    print("✅ Arquivo .env carregado")
except ImportError:
    # Fallback manual loading if python-dotenv not available
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file) as f:
            for line in f:
                if line.strip() and not line.startswith("#"):
                    key, value = line.strip().split("=", 1)
                    os.environ[key] = value
        print("✅ Arquivo .env carregado manualmente")
    else:
        print("⚠️ Arquivo .env não encontrado")

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from qualia.consciousness.holographic_universe import HolographicMarketUniverse
from qualia.consciousness.enhanced_data_collector import EnhancedDataCollector
from qualia.risk_management.advanced_risk_manager import AdvancedRiskManager
from qualia.exchanges.kucoin_client import KuCoinClient
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class EnhancedHolographicTradingOrchestrator:
    """
    Orquestrador aprimorado que utiliza TODOS os recursos QUALIA.

    Recursos integrados:
    - EnhancedDataCollector com encoders quânticos
    - Dados OHLCV completos com indicadores técnicos
    - Análise holográfica avançada
    - Gestão de risco integrada
    - Execução automática no KuCoin
    """

    def __init__(self, config_path: str = "config/holographic_trading_config.yaml"):
        self.config = self._load_config(config_path)

        # Componentes principais
        self.enhanced_data_collector = None
        self.holographic_universe = None
        self.risk_manager = None
        self.kucoin_client = None

        # Estado do sistema
        self.running = False
        self.cycle_count = 0
        self.total_signals_generated = 0

        logger.info("🌌 Enhanced Holographic Trading Orchestrator inicializado")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Carrega configuração do sistema."""
        try:
            with open(config_path, "r") as f:
                config = yaml.safe_load(f)

            # Override with environment variables
            kucoin_config = config["holographic_trading"]["exchanges"]["kucoin"]
            kucoin_config["api_key"] = os.getenv("KUCOIN_API_KEY", "")
            kucoin_config["api_secret"] = os.getenv("KUCOIN_SECRET_KEY", "")
            kucoin_config["password"] = os.getenv("KUCOIN_PASSPHRASE", "")

            logger.info("✅ Configuração carregada com credenciais do .env")
            return config

        except Exception as e:
            logger.error(f"Erro carregando configuração: {e}")
            raise

    async def initialize_components(self):
        """Inicializa todos os componentes do sistema."""

        try:
            # 1. Enhanced Data Collector com encoders quânticos
            exchange_config = {
                "exchange_id": "binance",  # Para coleta de dados OHLCV
                "api_key": "",  # Binance público não precisa
                "api_secret": "",
            }

            symbols = self.config["holographic_trading"]["data_sources"]["binance"][
                "symbols"
            ]
            timeframes = ["5m", "15m", "1h"]  # Múltiplos timeframes

            self.enhanced_data_collector = EnhancedDataCollector(
                symbols=symbols, timeframes=timeframes, exchange_config=exchange_config
            )

            logger.info(
                f"✅ Enhanced Data Collector: {len(symbols)} símbolos, {len(timeframes)} timeframes"
            )

            # 2. Holographic Universe
            holo_config = self.config["holographic_trading"]["holographic"]
            self.holographic_universe = HolographicMarketUniverse(
                field_size=tuple(holo_config["field_size"]),
                time_steps=holo_config["time_steps"],
                diffusion_rate=holo_config["diffusion_rate"],
                feedback_strength=holo_config["feedback_strength"],
            )

            logger.info("✅ Universo holográfico inicializado")

            # 3. Advanced Risk Manager
            risk_config = self.config["holographic_trading"]["risk"]
            capital_config = self.config["holographic_trading"]["capital"]
            self.risk_manager = AdvancedRiskManager(
                initial_capital=capital_config["initial"],
                risk_per_trade_pct=risk_config["per_trade_pct"],
                max_drawdown_pct=risk_config["max_drawdown_pct"],
                max_volatility=risk_config["max_volatility"],
            )

            logger.info("✅ Advanced Risk Manager inicializado")

            # 4. KuCoin Client
            kucoin_config = self.config["holographic_trading"]["exchanges"]["kucoin"]

            if all(
                [
                    kucoin_config["api_key"],
                    kucoin_config["api_secret"],
                    kucoin_config["password"],
                ]
            ):
                kucoin_client_config = {
                    "exchange_id": "kucoin",
                    "api_key": kucoin_config["api_key"],
                    "api_secret": kucoin_config["api_secret"],
                    "password": kucoin_config["password"],
                    "sandbox": kucoin_config.get("sandbox", False),
                    "timeout": kucoin_config.get("timeout", 30.0),
                    "rate_limit": kucoin_config.get("rate_limit", 4.0),
                }

                self.kucoin_client = KuCoinClient(kucoin_client_config)
                await self.kucoin_client.initialize()
                logger.info("✅ KuCoin client conectado")
            else:
                logger.warning("⚠️ Credenciais KuCoin não encontradas - modo demo")
                self.kucoin_client = None

        except Exception as e:
            logger.error(f"Erro inicializando componentes: {e}")
            raise

    async def run_enhanced_trading_cycle(self):
        """Executa um ciclo completo do sistema aprimorado."""

        cycle_start = time.time()
        self.cycle_count += 1

        try:
            # 1. Coleta dados enriquecidos com encoders quânticos
            enhanced_market_data = (
                await self.enhanced_data_collector.collect_enhanced_market_data()
            )

            logger.info(
                f"📊 Coletados {len(enhanced_market_data)} pontos de dados enriquecidos"
            )

            # Log detalhado dos dados coletados
            for data in enhanced_market_data[:3]:  # Primeiros 3 para debug
                logger.info(
                    f"🔍 {data.symbol}: RSI={f'{data.rsi:.1f}' if data.rsi is not None else 'N/A'}, "
                    f"Vol_Ratio={data.volume_ratio:.2f if data.volume_ratio else 'N/A'}, "
                    f"Change={data.price_change_pct:.2f if data.price_change_pct else 'N/A'}%, "
                    f"Quantum_RSI={'✅' if data.rsi_quantum_state is not None else '❌'}, "
                    f"Quantum_Vol={'✅' if data.volume_quantum_state is not None else '❌'}"
                )

            # 2. Converte para eventos holográficos enriquecidos
            holographic_events = (
                self.enhanced_data_collector.convert_to_holographic_events(
                    enhanced_market_data, [], self.holographic_universe.field_size
                )
            )

            logger.info(
                f"🌀 Convertidos {len(holographic_events)} eventos holográficos enriquecidos"
            )

            # 3. Injeta eventos no universo holográfico
            for event in holographic_events:
                self.holographic_universe.inject_event(event)

            # 4. Evolui o universo
            self.holographic_universe.evolve()

            # 5. Analisa padrões quânticos
            patterns = self.holographic_universe.analyze_patterns()

            # 6. Gera sinais de trading holográficos
            signals = self.holographic_universe.generate_trading_signals()

            # 7. Processa sinais com risk management
            for signal in signals:
                await self._process_enhanced_signal(signal, enhanced_market_data)

            # 8. Log do estado do sistema
            universe_stats = self.holographic_universe.get_field_statistics()

            logger.info(
                f"📈 Ciclo {self.cycle_count}: "
                f"{len(patterns)} padrões, "
                f"{len(signals)} sinais, "
                f"energia={universe_stats.get('energy', 0):.3f}, "
                f"entropia={universe_stats.get('entropy', 0):.3f}"
            )

            self.total_signals_generated += len(signals)

            cycle_time = time.time() - cycle_start
            logger.info(f"⏱️ Ciclo completado em {cycle_time:.2f}s")

        except Exception as e:
            logger.error(f"Erro no ciclo de trading: {e}", exc_info=True)

    async def _process_enhanced_signal(self, signal, enhanced_data):
        """Processa sinal com dados enriquecidos e risk management."""

        try:
            # Encontra dados enriquecidos correspondentes ao símbolo
            symbol_data = None
            for data in enhanced_data:
                if data.symbol.replace("USDT", "") in signal.symbol:
                    symbol_data = data
                    break

            if not symbol_data:
                logger.warning(
                    f"Dados enriquecidos não encontrados para {signal.symbol}"
                )
                return

            # Validação de risco enriquecida
            risk_assessment = self._enhanced_risk_assessment(signal, symbol_data)

            if not risk_assessment["approved"]:
                logger.info(
                    f"🚫 Sinal rejeitado: {signal.symbol} {signal.action} - "
                    f"Razão: {risk_assessment['reason']}"
                )
                return

            # Log detalhado do sinal
            logger.info(
                f"🎯 ENHANCED SIGNAL: {signal.symbol} {signal.action} "
                f"confidence={signal.confidence:.2f} "
                f"RSI={f'{symbol_data.rsi:.1f}' if symbol_data.rsi is not None else 'N/A'} "
                f"Vol_Ratio={symbol_data.volume_ratio:.2f if symbol_data.volume_ratio else 'N/A'} "
                f"Volatility={symbol_data.volatility:.2f if symbol_data.volatility else 'N/A'}%"
            )

            # Execução (modo demo ou live)
            if (
                self.config["holographic_trading"]["mode"] == "live"
                and self.kucoin_client
            ):
                # await self._execute_live_trade(signal, symbol_data, risk_assessment)
                logger.info("🚀 Executaria trade em modo live")
            else:
                logger.info("📝 Modo paper trading - sinal registrado")

        except Exception as e:
            logger.error(f"Erro processando sinal: {e}")

    def _enhanced_risk_assessment(self, signal, symbol_data) -> Dict[str, Any]:
        """Avaliação de risco enriquecida com dados técnicos."""

        # Validação básica de risco usando calculate_position_size
        stop_loss_price = (
            symbol_data.close * 0.98
            if signal.action == "BUY"
            else symbol_data.close * 1.02
        )
        basic_validation = self.risk_manager.calculate_position_size(
            symbol=signal.symbol,
            current_price=symbol_data.close,
            stop_loss_price=stop_loss_price,
            confidence=signal.confidence,
            volatility=symbol_data.volatility,
        )

        if not basic_validation.get("position_allowed", False):
            return {
                "approved": False,
                "reason": f"Risk manager rejection: {basic_validation.get('reason', 'unknown')}",
            }

        # Validações adicionais com dados enriquecidos

        # 1. RSI extremo
        if symbol_data.rsi is not None:
            if signal.action == "BUY" and symbol_data.rsi > 75:
                return {
                    "approved": False,
                    "reason": f"RSI muito alto: {symbol_data.rsi:.1f}",
                }
            elif signal.action == "SELL" and symbol_data.rsi < 25:
                return {
                    "approved": False,
                    "reason": f"RSI muito baixo: {symbol_data.rsi:.1f}",
                }

        # 2. Volatilidade excessiva
        if symbol_data.volatility is not None and symbol_data.volatility > 8.0:
            return {
                "approved": False,
                "reason": f"Volatilidade alta: {symbol_data.volatility:.2f}%",
            }

        # 3. Volume insuficiente
        if symbol_data.volume_ratio is not None and symbol_data.volume_ratio < 0.5:
            return {
                "approved": False,
                "reason": f"Volume baixo: {symbol_data.volume_ratio:.2f}x",
            }

        # 4. Confidence muito baixa
        if signal.confidence < 0.6:
            return {
                "approved": False,
                "reason": f"Confidence baixa: {signal.confidence:.2f}",
            }

        return {
            "approved": True,
            "reason": "All checks passed",
            "risk_score": 1.0 - signal.confidence,
            "position_size": basic_validation,
        }

    async def start(self):
        """Inicia o sistema de trading holográfico aprimorado."""

        logger.info("🚀 Iniciando Enhanced Holographic Trading System")

        try:
            # Inicializa componentes
            await self.initialize_components()

            # Inicializa context manager do data collector
            await self.enhanced_data_collector.__aenter__()

            self.running = True

            # Loop principal
            while self.running:
                await self.run_enhanced_trading_cycle()

                # Intervalo entre ciclos
                interval = self.config["holographic_trading"]["holographic"][
                    "analysis_interval"
                ]
                await asyncio.sleep(interval)

        except KeyboardInterrupt:
            logger.info("🛑 Interrupção solicitada pelo usuário")
        except Exception as e:
            logger.error(f"Erro crítico: {e}", exc_info=True)
        finally:
            await self.shutdown()

    async def shutdown(self):
        """Encerra o sistema graciosamente."""

        logger.info("🔄 Encerrando sistema...")

        self.running = False

        try:
            if self.enhanced_data_collector:
                await self.enhanced_data_collector.__aexit__(None, None, None)

            if self.kucoin_client:
                await self.kucoin_client.close()

        except Exception as e:
            logger.error(f"Erro no shutdown: {e}")

        logger.info(
            f"✅ Sistema encerrado. "
            f"Ciclos executados: {self.cycle_count}, "
            f"Sinais gerados: {self.total_signals_generated}"
        )


async def main():
    """Função principal."""

    # Configuração de logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    # Cria e executa o orquestrador
    orchestrator = EnhancedHolographicTradingOrchestrator()
    await orchestrator.start()


if __name__ == "__main__":
    asyncio.run(main())
