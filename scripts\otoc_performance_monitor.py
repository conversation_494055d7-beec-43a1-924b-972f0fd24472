#!/usr/bin/env python3
"""
OTOC Performance Monitor - Monitoramento em tempo real da eficácia dos parâmetros OTOC.

YAA-MONITORING: Sistema de monitoramento contínuo para validar e ajustar
parâmetros OTOC em ambiente de paper trading.
"""

import sys
import os
import time
import json
import yaml
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import asyncio

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from qualia.utils.otoc_metrics import get_otoc_metrics_collector
    from qualia.utils.otoc_calculator import get_otoc_diagnostics
    QUALIA_AVAILABLE = True
except ImportError:
    print("⚠️ Módulos QUALIA não disponíveis. Usando modo simulação.")
    QUALIA_AVAILABLE = False


@dataclass
class PerformanceSnapshot:
    """Snapshot de performance em um momento específico."""
    timestamp: datetime
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    chaos_rate: float
    avg_otoc: float
    filtered_signals: int
    active_positions: int
    daily_pnl: float


class OTOCPerformanceMonitor:
    """Monitor de performance OTOC em tempo real."""
    
    def __init__(self, config_path: str = "config/fwh_scalp_config_optimized.yaml"):
        """
        Inicializa o monitor de performance.
        
        Parameters
        ----------
        config_path : str
            Caminho para configuração otimizada
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.snapshots: List[PerformanceSnapshot] = []
        self.alerts_sent: List[str] = []
        
        # Thresholds de alerta baseados na configuração
        self.alert_thresholds = {
            'max_drawdown_alert': self.config.get('monitoring', {}).get('alerts', {}).get('max_drawdown_alert', 2.5),
            'low_win_rate_alert': self.config.get('monitoring', {}).get('alerts', {}).get('low_win_rate_alert', 35.0),
            'high_chaos_rate_alert': self.config.get('monitoring', {}).get('alerts', {}).get('high_chaos_rate_alert', 70.0),
            'daily_loss_alert': self.config.get('monitoring', {}).get('alerts', {}).get('daily_loss_alert', 40.0)
        }
        
        print(f"🎯 Monitor OTOC inicializado com configuração: {config_path}")
        print(f"📊 Thresholds de alerta: {self.alert_thresholds}")
    
    def _load_config(self) -> Dict[str, Any]:
        """Carrega configuração."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            print(f"⚠️ Configuração não encontrada: {self.config_path}")
            return {}
    
    def collect_performance_snapshot(self) -> PerformanceSnapshot:
        """
        Coleta snapshot atual de performance.
        
        YAA-MONITORING: Integra com sistema OTOC real ou simula dados
        """
        if QUALIA_AVAILABLE:
            return self._collect_real_snapshot()
        else:
            return self._simulate_snapshot()
    
    def _collect_real_snapshot(self) -> PerformanceSnapshot:
        """Coleta dados reais do sistema QUALIA."""
        try:
            # Obter métricas OTOC
            otoc_collector = get_otoc_metrics_collector()
            otoc_stats = otoc_collector.get_otoc_statistics()
            chaos_rate = otoc_collector.get_chaos_rate(window_minutes=60)
            
            # Simular métricas de trading (em implementação real, viria do sistema)
            # TODO: Integrar com sistema de trading real
            
            return PerformanceSnapshot(
                timestamp=datetime.now(),
                sharpe_ratio=1.2,  # Placeholder
                max_drawdown=0.025,  # Placeholder
                win_rate=otoc_stats.get('count', 0) * 0.6 if otoc_stats else 0.6,
                total_trades=otoc_stats.get('count', 0) if otoc_stats else 0,
                chaos_rate=chaos_rate,
                avg_otoc=otoc_stats.get('otoc_mean', 0.3) if otoc_stats else 0.3,
                filtered_signals=otoc_stats.get('chaos_events', 0) if otoc_stats else 0,
                active_positions=2,  # Placeholder
                daily_pnl=5.5  # Placeholder
            )
        except Exception as e:
            print(f"❌ Erro ao coletar dados reais: {e}")
            return self._simulate_snapshot()
    
    def _simulate_snapshot(self) -> PerformanceSnapshot:
        """Simula snapshot para demonstração."""
        import random
        import numpy as np
        
        # Simular métricas baseadas no tempo (variação realística)
        base_time = time.time()
        noise_factor = 0.1 * np.sin(base_time / 3600)  # Variação horária
        
        return PerformanceSnapshot(
            timestamp=datetime.now(),
            sharpe_ratio=1.2 + noise_factor + random.uniform(-0.2, 0.2),
            max_drawdown=0.025 + abs(noise_factor * 0.01) + random.uniform(0, 0.01),
            win_rate=0.62 + noise_factor * 0.1 + random.uniform(-0.05, 0.05),
            total_trades=random.randint(45, 75),
            chaos_rate=0.35 + abs(noise_factor * 0.2) + random.uniform(0, 0.15),
            avg_otoc=0.32 + noise_factor * 0.1 + random.uniform(-0.05, 0.05),
            filtered_signals=random.randint(8, 25),
            active_positions=random.randint(0, 4),
            daily_pnl=random.uniform(-15, 25)
        )
    
    def check_alerts(self, snapshot: PerformanceSnapshot) -> List[str]:
        """
        Verifica condições de alerta baseadas no snapshot.
        
        Returns
        -------
        List[str]
            Lista de alertas ativados
        """
        alerts = []
        
        # Alerta de drawdown
        if snapshot.max_drawdown * 100 > self.alert_thresholds['max_drawdown_alert']:
            alert_msg = f"🚨 DRAWDOWN ALTO: {snapshot.max_drawdown:.1%} > {self.alert_thresholds['max_drawdown_alert']:.1f}%"
            if alert_msg not in self.alerts_sent:
                alerts.append(alert_msg)
                self.alerts_sent.append(alert_msg)
        
        # Alerta de win rate baixo
        if snapshot.win_rate * 100 < self.alert_thresholds['low_win_rate_alert']:
            alert_msg = f"📉 WIN RATE BAIXO: {snapshot.win_rate:.1%} < {self.alert_thresholds['low_win_rate_alert']:.1f}%"
            if alert_msg not in self.alerts_sent:
                alerts.append(alert_msg)
                self.alerts_sent.append(alert_msg)
        
        # Alerta de taxa de caos alta
        if snapshot.chaos_rate * 100 > self.alert_thresholds['high_chaos_rate_alert']:
            alert_msg = f"🌀 CAOS ALTO: {snapshot.chaos_rate:.1%} > {self.alert_thresholds['high_chaos_rate_alert']:.1f}%"
            if alert_msg not in self.alerts_sent:
                alerts.append(alert_msg)
                self.alerts_sent.append(alert_msg)
        
        # Alerta de perda diária
        if snapshot.daily_pnl < -self.alert_thresholds['daily_loss_alert']:
            alert_msg = f"💸 PERDA DIÁRIA: ${snapshot.daily_pnl:.2f} < -${self.alert_thresholds['daily_loss_alert']:.2f}"
            if alert_msg not in self.alerts_sent:
                alerts.append(alert_msg)
                self.alerts_sent.append(alert_msg)
        
        return alerts
    
    def analyze_performance_trend(self, window_minutes: int = 60) -> Dict[str, Any]:
        """
        Analisa tendência de performance na janela especificada.
        
        Parameters
        ----------
        window_minutes : int
            Janela de análise em minutos
            
        Returns
        -------
        Dict[str, Any]
            Análise de tendência
        """
        if len(self.snapshots) < 2:
            return {"status": "insufficient_data"}
        
        # Filtrar snapshots na janela
        cutoff_time = datetime.now() - timedelta(minutes=window_minutes)
        recent_snapshots = [s for s in self.snapshots if s.timestamp >= cutoff_time]
        
        if len(recent_snapshots) < 2:
            return {"status": "insufficient_recent_data"}
        
        # Calcular tendências
        first_snapshot = recent_snapshots[0]
        last_snapshot = recent_snapshots[-1]
        
        trends = {
            'sharpe_trend': (last_snapshot.sharpe_ratio - first_snapshot.sharpe_ratio) / first_snapshot.sharpe_ratio * 100,
            'drawdown_trend': (last_snapshot.max_drawdown - first_snapshot.max_drawdown) / first_snapshot.max_drawdown * 100,
            'win_rate_trend': (last_snapshot.win_rate - first_snapshot.win_rate) / first_snapshot.win_rate * 100,
            'chaos_rate_trend': (last_snapshot.chaos_rate - first_snapshot.chaos_rate) / first_snapshot.chaos_rate * 100,
            'trades_per_hour': len(recent_snapshots) * (60 / window_minutes),
            'avg_daily_pnl': sum(s.daily_pnl for s in recent_snapshots) / len(recent_snapshots)
        }
        
        # Classificar tendência geral
        positive_indicators = sum(1 for k, v in trends.items() 
                                if k in ['sharpe_trend', 'win_rate_trend'] and v > 0)
        negative_indicators = sum(1 for k, v in trends.items() 
                                if k in ['drawdown_trend', 'chaos_rate_trend'] and v > 0)
        
        if positive_indicators > negative_indicators:
            overall_trend = "improving"
        elif negative_indicators > positive_indicators:
            overall_trend = "deteriorating"
        else:
            overall_trend = "stable"
        
        return {
            "status": "analyzed",
            "overall_trend": overall_trend,
            "trends": trends,
            "window_minutes": window_minutes,
            "snapshots_analyzed": len(recent_snapshots)
        }
    
    def generate_performance_report(self) -> str:
        """Gera relatório de performance formatado."""
        if not self.snapshots:
            return "📊 Nenhum dado de performance disponível."
        
        latest = self.snapshots[-1]
        trend_analysis = self.analyze_performance_trend()
        
        report = f"""
🎯 RELATÓRIO DE PERFORMANCE OTOC - {latest.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
{'='*70}

📈 MÉTRICAS ATUAIS:
   Sharpe Ratio:     {latest.sharpe_ratio:.3f}
   Max Drawdown:     {latest.max_drawdown:.1%}
   Win Rate:         {latest.win_rate:.1%}
   Total Trades:     {latest.total_trades}
   Posições Ativas:  {latest.active_positions}
   P&L Diário:       ${latest.daily_pnl:.2f}

🌀 MÉTRICAS OTOC:
   Taxa de Caos:     {latest.chaos_rate:.1%}
   OTOC Médio:       {latest.avg_otoc:.3f}
   Sinais Filtrados: {latest.filtered_signals}

📊 ANÁLISE DE TENDÊNCIA (1h):
   Status:           {trend_analysis.get('overall_trend', 'N/A').upper()}
   Snapshots:        {trend_analysis.get('snapshots_analyzed', 0)}
"""
        
        if trend_analysis.get('status') == 'analyzed':
            trends = trend_analysis['trends']
            report += f"""   Sharpe Trend:     {trends['sharpe_trend']:+.1f}%
   Drawdown Trend:   {trends['drawdown_trend']:+.1f}%
   Win Rate Trend:   {trends['win_rate_trend']:+.1f}%
   Chaos Rate Trend: {trends['chaos_rate_trend']:+.1f}%
"""
        
        return report
    
    def save_snapshots(self, filepath: str = "logs/otoc_performance_snapshots.json"):
        """Salva snapshots em arquivo JSON."""
        snapshots_data = [asdict(snapshot) for snapshot in self.snapshots]
        
        # Converter datetime para string
        for snapshot_data in snapshots_data:
            snapshot_data['timestamp'] = snapshot_data['timestamp'].isoformat()
        
        with open(filepath, 'w') as f:
            json.dump(snapshots_data, f, indent=2)
        
        print(f"💾 {len(self.snapshots)} snapshots salvos em: {filepath}")
    
    async def run_monitoring_loop(self, interval_seconds: int = 60, duration_minutes: int = 60):
        """
        Executa loop de monitoramento contínuo.
        
        Parameters
        ----------
        interval_seconds : int
            Intervalo entre coletas em segundos
        duration_minutes : int
            Duração total do monitoramento em minutos
        """
        print(f"🔄 Iniciando monitoramento OTOC...")
        print(f"   Intervalo: {interval_seconds}s")
        print(f"   Duração: {duration_minutes}min")
        print(f"   Total snapshots esperados: {duration_minutes * 60 // interval_seconds}")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        iteration = 0
        
        while datetime.now() < end_time:
            iteration += 1
            
            # Coletar snapshot
            snapshot = self.collect_performance_snapshot()
            self.snapshots.append(snapshot)
            
            # Verificar alertas
            alerts = self.check_alerts(snapshot)
            for alert in alerts:
                print(f"🚨 {alert}")
            
            # Log de progresso
            if iteration % 5 == 0:  # A cada 5 iterações
                elapsed = datetime.now() - start_time
                remaining = end_time - datetime.now()
                
                print(f"\n⏱️  Progresso: {elapsed.total_seconds()/60:.1f}min / {duration_minutes}min")
                print(f"📊 Snapshots coletados: {len(self.snapshots)}")
                print(f"🌀 Taxa de caos atual: {snapshot.chaos_rate:.1%}")
                print(f"💰 P&L atual: ${snapshot.daily_pnl:.2f}")
                
                # Relatório de tendência a cada 10 iterações
                if iteration % 10 == 0:
                    print(self.generate_performance_report())
            
            # Aguardar próxima iteração
            await asyncio.sleep(interval_seconds)
        
        print(f"\n✅ Monitoramento concluído!")
        print(f"📊 Total de snapshots coletados: {len(self.snapshots)}")
        
        # Salvar dados
        self.save_snapshots()
        
        # Relatório final
        print(self.generate_performance_report())


async def main():
    """Executa monitoramento de performance OTOC."""
    print("📊 OTOC PERFORMANCE MONITOR")
    print("=" * 50)
    
    # Inicializar monitor
    monitor = OTOCPerformanceMonitor()
    
    # Executar monitoramento
    await monitor.run_monitoring_loop(
        interval_seconds=30,    # Snapshot a cada 30s
        duration_minutes=30     # Monitorar por 30min
    )


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="OTOC Performance Monitor")
    parser.add_argument("--interval", type=int, default=30,
                       help="Intervalo entre snapshots em segundos")
    parser.add_argument("--duration", type=int, default=30,
                       help="Duração do monitoramento em minutos")
    parser.add_argument("--config", type=str, default="config/fwh_scalp_config_optimized.yaml",
                       help="Caminho para configuração")
    
    args = parser.parse_args()
    
    # Executar com parâmetros customizados
    async def custom_main():
        monitor = OTOCPerformanceMonitor(args.config)
        await monitor.run_monitoring_loop(args.interval, args.duration)
    
    asyncio.run(custom_main())
