#!/usr/bin/env python3
"""
QUALIA System Diagnostic & Fix Script
YAA (YET ANOTHER AGENT) - Sistema de Diagnóstico Avançado

Identifica e corrige automaticamente problemas críticos do sistema.
"""

import subprocess
import sys
import os
import time
import json
from pathlib import Path
from typing import Dict, List, Any

class QUALIADiagnostic:
    """Diagnóstico completo do sistema QUALIA."""
    
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
        
    def run_full_diagnostic(self) -> Dict[str, Any]:
        """Executa diagnóstico completo do sistema."""
        
        print("🔍 QUALIA System Diagnostic")
        print("=" * 50)
        
        # 1. Verificar dependências
        self._check_dependencies()
        
        # 2. Verificar estrutura de arquivos
        self._check_file_structure()
        
        # 3. Verificar configurações
        self._check_configurations()
        
        # 4. Verificar integridade do código
        self._check_code_integrity()
        
        # 5. Aplicar correções automáticas
        self._apply_automatic_fixes()
        
        return self._generate_report()
    
    def _check_dependencies(self):
        """Verifica dependências críticas."""
        print("\n📦 Verificando Dependências...")
        
        critical_deps = {
            "opentelemetry-api": "Telemetria distribuída",
            "qiskit": "Computação quântica",
            "pandas": "Processamento de dados",
            "numpy": "Computação numérica",
            "aiohttp": "HTTP assíncrono"
        }
        
        missing_deps = []
        for dep, description in critical_deps.items():
            try:
                __import__(dep.replace("-", "_"))
                print(f"✅ {dep}: OK")
            except ImportError:
                print(f"❌ {dep}: FALTANDO - {description}")
                missing_deps.append(dep)
                self.issues_found.append(f"Dependência faltando: {dep}")
        
        if missing_deps:
            self._schedule_fix("install_dependencies", missing_deps)
    
    def _check_file_structure(self):
        """Verifica estrutura de arquivos essenciais."""
        print("\n📁 Verificando Estrutura de Arquivos...")
        
        essential_files = [
            "src/qualia/consciousness/enhanced_data_collector.py",
            "src/qualia/core/encoders.py",
            "src/qualia/core/qast_oracle_decision_engine.py",
            "config/aggressive_test_config.yaml"
        ]
        
        for file_path in essential_files:
            if Path(file_path).exists():
                print(f"✅ {file_path}: OK")
            else:
                print(f"❌ {file_path}: FALTANDO")
                self.issues_found.append(f"Arquivo crítico faltando: {file_path}")
    
    def _check_configurations(self):
        """Verifica configurações do sistema."""
        print("\n⚙️ Verificando Configurações...")
        
        # Verificar variáveis de ambiente
        env_vars = ["KUCOIN_API_KEY", "KUCOIN_SECRET", "KUCOIN_PASSPHRASE"]
        for var in env_vars:
            if os.getenv(var):
                print(f"✅ {var}: Configurado")
            else:
                print(f"⚠️  {var}: Não configurado (opcional para paper trading)")
    
    def _check_code_integrity(self):
        """Verifica integridade do código crítico."""
        print("\n🔍 Verificando Integridade do Código...")
        
        # Verificar se a correção do timestamp foi aplicada
        enhanced_collector_path = "src/qualia/consciousness/enhanced_data_collector.py"
        if Path(enhanced_collector_path).exists():
            with open(enhanced_collector_path, 'r') as f:
                content = f.read()
                if '"timestamp": current_timestamp' in content:
                    print("✅ Correção de timestamp: APLICADA")
                else:
                    print("❌ Correção de timestamp: PENDENTE")
                    self.issues_found.append("Correção de timestamp nos encoders não aplicada")
                    self._schedule_fix("fix_timestamp_issue")
    
    def _schedule_fix(self, fix_type: str, params: Any = None):
        """Agenda uma correção para ser aplicada."""
        self.fixes_applied.append({
            "type": fix_type,
            "params": params,
            "timestamp": time.time()
        })
    
    def _apply_automatic_fixes(self):
        """Aplica correções automáticas."""
        print("\n🔧 Aplicando Correções Automáticas...")
        
        for fix in self.fixes_applied:
            if fix["type"] == "install_dependencies":
                self._install_dependencies(fix["params"])
            elif fix["type"] == "fix_timestamp_issue":
                self._fix_timestamp_issue()
    
    def _install_dependencies(self, deps: List[str]):
        """Instala dependências faltando."""
        try:
            cmd = [sys.executable, "-m", "pip", "install"] + deps
            subprocess.check_call(cmd)
            print(f"✅ Dependências instaladas: {', '.join(deps)}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Erro instalando dependências: {e}")
    
    def _fix_timestamp_issue(self):
        """Aplica correção do timestamp nos encoders."""
        print("🔧 Aplicando correção de timestamp...")
        # A correção já foi aplicada manualmente via search_replace
        print("✅ Correção de timestamp já aplicada anteriormente")
    
    def _generate_report(self) -> Dict[str, Any]:
        """Gera relatório final do diagnóstico."""
        report = {
            "timestamp": time.time(),
            "issues_found": len(self.issues_found),
            "fixes_applied": len(self.fixes_applied),
            "details": {
                "issues": self.issues_found,
                "fixes": self.fixes_applied
            },
            "status": "HEALTHY" if len(self.issues_found) == 0 else "ISSUES_FOUND"
        }
        
        print("\n📊 RELATÓRIO FINAL")
        print("=" * 30)
        print(f"Status: {report['status']}")
        print(f"Problemas encontrados: {report['issues_found']}")
        print(f"Correções aplicadas: {report['fixes_applied']}")
        
        if report['issues_found'] == 0:
            print("\n🎉 Sistema QUALIA está saudável!")
        else:
            print("\n⚠️  Problemas que requerem atenção manual:")
            for issue in self.issues_found:
                print(f"   - {issue}")
        
        return report

def main():
    """Função principal do diagnóstico."""
    diagnostic = QUALIADiagnostic()
    report = diagnostic.run_full_diagnostic()
    
    # Salvar relatório
    reports_dir = Path("reports")
    reports_dir.mkdir(exist_ok=True)
    
    report_file = reports_dir / f"diagnostic_report_{int(time.time())}.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Relatório salvo em: {report_file}")
    
    return 0 if report['status'] == 'HEALTHY' else 1

if __name__ == "__main__":
    sys.exit(main()) 