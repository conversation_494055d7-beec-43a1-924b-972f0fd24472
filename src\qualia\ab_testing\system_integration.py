"""
QUALIA A/B Testing Framework - D-07.6 System Integration

Sistema de integração completo que conecta o framework A/B Testing
com todos os componentes principais do QUALIA:
- Live Feed Integration (D-03.2)
- BayesianOptimizer
- ParameterTuner
- Sistemas de monitoramento e alertas
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
import json
from pathlib import Path

# QUALIA Core Components
from qualia.consciousness.live_feed_integration import LiveFeedIntegration, LiveFeedIntegrationConfig
from qualia.optimization.bayesian_optimizer import BayesianOptimizer, OptimizationConfig
from qualia.optimization.parameter_tuner import ParameterTuner, TunerConfig
from qualia.memory.event_bus import SimpleEventBus

# A/B Testing Components
from .ab_test_framework import ABTestFramework, ABTestConfig, ABTestResult
from .performance_comparator import PerformanceComparator
from .data_quality_validator import DataQualityValidator
from .statistical_analyzer import StatisticalAnalyzer
from .test_config_manager import TestConfigManager
from .reporting_engine import ReportingEngine

logger = logging.getLogger(__name__)


@dataclass
class SystemIntegrationConfig:
    """Configuração da integração do sistema."""
    
    # Componentes a integrar
    enable_live_feed: bool = True
    enable_bayesian_optimizer: bool = True
    enable_parameter_tuner: bool = True
    enable_monitoring: bool = True
    
    # Configuração de A/B Testing
    ab_test_duration_hours: int = 24
    ab_test_symbols: List[str] = field(default_factory=lambda: ["BTC/USDT", "ETH/USDT"])
    ab_test_confidence_level: float = 0.95
    
    # Configuração de otimização
    optimization_interval_cycles: int = 500
    n_trials_per_optimization: int = 25
    max_concurrent_optimizations: int = 4
    
    # Configuração de monitoramento
    monitoring_interval_seconds: int = 60
    alert_thresholds: Dict[str, float] = field(default_factory=lambda: {
        "performance_difference_pct": 5.0,
        "data_quality_score_min": 0.8,
        "statistical_significance_p": 0.05
    })
    
    # Configuração de relatórios
    auto_report_generation: bool = True
    report_export_formats: List[str] = field(default_factory=lambda: ["html", "json"])
    report_storage_path: str = "reports/ab_testing"


class QualiaABTestingIntegration:
    """
    Sistema de integração completo do framework A/B Testing com QUALIA.
    
    Coordena todos os componentes para execução de testes A/B end-to-end:
    1. Configuração automática de testes
    2. Integração com live feed
    3. Otimização de parâmetros
    4. Monitoramento em tempo real
    5. Geração automática de relatórios
    """
    
    def __init__(self, config: Optional[SystemIntegrationConfig] = None):
        self.config = config or SystemIntegrationConfig()
        
        # Componentes principais
        self.event_bus = SimpleEventBus()
        self.live_feed_integration: Optional[LiveFeedIntegration] = None
        self.bayesian_optimizer: Optional[BayesianOptimizer] = None
        self.parameter_tuner: Optional[ParameterTuner] = None
        
        # A/B Testing Framework
        self.ab_test_framework: Optional[ABTestFramework] = None
        self.test_config_manager: Optional[TestConfigManager] = None
        self.reporting_engine: Optional[ReportingEngine] = None
        
        # Estado da integração
        self.is_initialized = False
        self.is_running = False
        self.active_tests: Dict[str, ABTestResult] = {}
        
        # Callbacks e handlers
        self.alert_callbacks: List[Callable] = []
        self.test_completion_callbacks: List[Callable] = []
        
        # Estatísticas
        self.integration_stats = {
            "tests_completed": 0,
            "tests_failed": 0,
            "optimization_cycles": 0,
            "alerts_generated": 0,
            "reports_generated": 0,
            "uptime_start": datetime.now(),
            "last_activity": datetime.now()
        }
        
        logger.info("🔗 QualiaABTestingIntegration inicializada")
    
    async def initialize(self) -> bool:
        """Inicializa todos os componentes da integração."""
        try:
            logger.info("🚀 Inicializando integração completa do sistema...")
            
            # 1. Inicializar Live Feed Integration
            if self.config.enable_live_feed:
                await self._initialize_live_feed()
            
            # 2. Inicializar Bayesian Optimizer
            if self.config.enable_bayesian_optimizer:
                await self._initialize_bayesian_optimizer()
            
            # 3. Inicializar Parameter Tuner
            if self.config.enable_parameter_tuner:
                await self._initialize_parameter_tuner()
            
            # 4. Inicializar A/B Testing Framework
            await self._initialize_ab_testing_framework()
            
            # 5. Configurar event handlers
            self._setup_event_handlers()
            
            # 6. Configurar monitoramento
            if self.config.enable_monitoring:
                await self._initialize_monitoring()
            
            self.is_initialized = True
            logger.info("✅ Integração do sistema inicializada com sucesso")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar integração: {e}")
            return False
    
    async def _initialize_live_feed(self):
        """Inicializa integração do live feed."""
        logger.info("📡 Inicializando Live Feed Integration...")
        
        live_feed_config = {
            "mode": "hybrid",
            "enable_live_feed": True,
            "paper_trading_mode": True,
            "enable_fallback": True,
            "enable_data_validation": True,
            "news_amp": 11.3,
            "price_amp": 1.0,
            "min_conf": 0.37
        }
        
        self.live_feed_integration = LiveFeedIntegration(config=live_feed_config)
        await self.live_feed_integration.initialize(symbols=self.config.ab_test_symbols)
        
        logger.info("✅ Live Feed Integration inicializada")
    
    async def _initialize_bayesian_optimizer(self):
        """Inicializa otimizador Bayesiano."""
        logger.info("🧠 Inicializando Bayesian Optimizer...")
        
        optimization_config = OptimizationConfig(
            study_name="ab_testing_optimization",
            n_trials_per_cycle=self.config.n_trials_per_optimization,
            optimization_cycles=self.config.optimization_interval_cycles,
            symbols=[symbol.replace("/", "") for symbol in self.config.ab_test_symbols]
        )
        
        self.bayesian_optimizer = BayesianOptimizer(optimization_config)
        self.bayesian_optimizer.initialize_study()
        
        logger.info("✅ Bayesian Optimizer inicializado")
    
    async def _initialize_parameter_tuner(self):
        """Inicializa parameter tuner."""
        logger.info("⚙️ Inicializando Parameter Tuner...")
        
        tuner_config = TunerConfig(
            symbols=[symbol.replace("/", "") for symbol in self.config.ab_test_symbols],
            optimization_interval_cycles=self.config.optimization_interval_cycles,
            n_trials_per_cycle=self.config.n_trials_per_optimization,
            max_concurrent_optimizations=self.config.max_concurrent_optimizations,
            performance_lookback_hours=24,
            min_trades_for_optimization=10,
            grpc_port=50051,
            grpc_enabled=True
        )
        
        self.parameter_tuner = ParameterTuner(tuner_config)
        
        logger.info("✅ Parameter Tuner inicializado")
    
    async def _initialize_ab_testing_framework(self):
        """Inicializa framework de A/B testing."""
        logger.info("🧪 Inicializando A/B Testing Framework...")
        
        # Test Config Manager
        self.test_config_manager = TestConfigManager()
        
        # A/B Test Framework
        ab_config = ABTestConfig(
            test_name="QUALIA_System_Integration_Test",
            duration_hours=self.config.ab_test_duration_hours,
            symbols=self.config.ab_test_symbols,
            confidence_level=self.config.ab_test_confidence_level
        )
        
        self.ab_test_framework = ABTestFramework(ab_config)
        
        # Reporting Engine
        self.reporting_engine = ReportingEngine()
        
        logger.info("✅ A/B Testing Framework inicializado")
    
    def _setup_event_handlers(self):
        """Configura handlers de eventos."""
        logger.info("📡 Configurando event handlers...")
        
        # Handler para alertas de qualidade de dados
        self.event_bus.subscribe("data_quality.alert", self._handle_data_quality_alert)
        
        # Handler para completion de testes
        self.event_bus.subscribe("ab_test.completed", self._handle_test_completion)
        
        # Handler para otimização de parâmetros
        self.event_bus.subscribe("optimization.completed", self._handle_optimization_completion)
        
        logger.info("✅ Event handlers configurados")
    
    async def _initialize_monitoring(self):
        """Inicializa sistema de monitoramento."""
        logger.info("📊 Inicializando sistema de monitoramento...")
        
        # Criar diretório de relatórios
        Path(self.config.report_storage_path).mkdir(parents=True, exist_ok=True)
        
        logger.info("✅ Sistema de monitoramento inicializado")
    
    async def start_integration(self) -> bool:
        """Inicia a integração completa."""
        if not self.is_initialized:
            logger.error("❌ Sistema não inicializado. Chame initialize() primeiro.")
            return False
        
        try:
            logger.info("🚀 Iniciando integração completa...")
            
            # 1. Iniciar Live Feed
            if self.live_feed_integration:
                await self.live_feed_integration.start()
            
            # 2. Iniciar Parameter Tuner
            if self.parameter_tuner:
                asyncio.create_task(self.parameter_tuner.start())
            
            # 3. Iniciar monitoramento
            asyncio.create_task(self._monitoring_loop())
            
            self.is_running = True
            self.integration_stats["uptime_start"] = datetime.now()
            
            logger.info("✅ Integração completa iniciada com sucesso")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao iniciar integração: {e}")
            return False
    
    async def run_ab_test(self, test_config: Optional[Dict[str, Any]] = None) -> Optional[ABTestResult]:
        """Executa um teste A/B completo end-to-end."""
        try:
            logger.info("🧪 Iniciando teste A/B end-to-end...")
            
            # 1. Configurar teste
            if test_config:
                config = await self.test_config_manager.create_test_configuration(test_config)
                await self.test_config_manager.save_configuration(config)
            
            # 2. Executar teste
            test_result = await self.ab_test_framework.run_test()
            
            if test_result:
                # 3. Gerar relatório
                if self.config.auto_report_generation:
                    await self._generate_test_report(test_result)
                
                # 4. Atualizar estatísticas
                self.integration_stats["tests_completed"] += 1
                self.integration_stats["last_activity"] = datetime.now()
                
                # 5. Notificar completion
                self.event_bus.publish("ab_test.completed", {"test_result": test_result})
                
                logger.info(f"✅ Teste A/B completado: {test_result.test_id}")
                return test_result
            else:
                self.integration_stats["tests_failed"] += 1
                logger.error("❌ Teste A/B falhou")
                return None
                
        except Exception as e:
            logger.error(f"❌ Erro ao executar teste A/B: {e}")
            self.integration_stats["tests_failed"] += 1
            return None

    async def _generate_test_report(self, test_result: ABTestResult):
        """Gera relatório completo do teste."""
        try:
            logger.info(f"📊 Gerando relatório para teste {test_result.test_id}...")

            # Coletar dados para o relatório
            performance_metrics = self.ab_test_framework.performance_comparator.get_all_metrics()
            quality_report = self.ab_test_framework.data_quality_validator.generate_report()
            statistical_results = []  # Será preenchido pelo statistical analyzer

            # Gerar relatório
            report = await self.reporting_engine.generate_comprehensive_report(
                test_result=test_result,
                performance_metrics=performance_metrics,
                quality_report=quality_report,
                statistical_results=statistical_results
            )

            # Exportar nos formatos configurados
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            for format_type in self.config.report_export_formats:
                if format_type == "html":
                    filename = f"{self.config.report_storage_path}/ab_test_{test_result.test_id}_{timestamp}.html"
                    await self.reporting_engine.export_html_report(report, filename)
                elif format_type == "json":
                    filename = f"{self.config.report_storage_path}/ab_test_{test_result.test_id}_{timestamp}.json"
                    await self.reporting_engine.export_json_report(report, filename)

            self.integration_stats["reports_generated"] += 1
            logger.info(f"✅ Relatório gerado: {test_result.test_id}")

        except Exception as e:
            logger.error(f"❌ Erro ao gerar relatório: {e}")

    async def _monitoring_loop(self):
        """Loop principal de monitoramento."""
        logger.info("📊 Iniciando loop de monitoramento...")

        while self.is_running:
            try:
                await self._check_system_health()
                await self._check_test_progress()
                await self._check_performance_alerts()

                await asyncio.sleep(self.config.monitoring_interval_seconds)

            except Exception as e:
                logger.error(f"❌ Erro no loop de monitoramento: {e}")
                await asyncio.sleep(self.config.monitoring_interval_seconds)

    async def _check_system_health(self):
        """Verifica saúde geral do sistema."""
        health_status = {
            "live_feed_active": self.live_feed_integration.is_running if self.live_feed_integration else False,
            "ab_testing_active": self.ab_test_framework.is_running if self.ab_test_framework else False,
            "parameter_tuner_active": self.parameter_tuner.is_running if self.parameter_tuner else False,
            "active_tests_count": len(self.active_tests),
            "uptime_hours": (datetime.now() - self.integration_stats["uptime_start"]).total_seconds() / 3600
        }

        # Publicar status no event bus
        self.event_bus.publish("system.health_check", health_status)

    async def _check_test_progress(self):
        """Verifica progresso dos testes ativos."""
        for test_id, test_result in self.active_tests.items():
            if test_result.status.value == "running":
                # Verificar se teste deve ser finalizado
                elapsed_hours = (datetime.now() - test_result.start_time).total_seconds() / 3600
                if elapsed_hours >= self.config.ab_test_duration_hours:
                    logger.info(f"⏰ Finalizando teste por timeout: {test_id}")
                    await self.ab_test_framework.stop_test()

    async def _check_performance_alerts(self):
        """Verifica alertas de performance."""
        if not self.ab_test_framework or not self.ab_test_framework.performance_comparator:
            return

        try:
            comparison = self.ab_test_framework.performance_comparator.calculate_comparison()
            if comparison:
                # Verificar diferença de performance
                pnl_diff_pct = abs(comparison.performance_difference_pct)
                if pnl_diff_pct > self.config.alert_thresholds["performance_difference_pct"]:
                    alert_data = {
                        "type": "performance_difference",
                        "difference_pct": pnl_diff_pct,
                        "threshold": self.config.alert_thresholds["performance_difference_pct"],
                        "timestamp": datetime.now().isoformat()
                    }
                    await self._trigger_alert("performance_difference", alert_data)

        except Exception as e:
            logger.error(f"❌ Erro ao verificar alertas de performance: {e}")

    async def _trigger_alert(self, alert_type: str, alert_data: Dict[str, Any]):
        """Dispara alerta do sistema."""
        logger.warning(f"🚨 ALERTA {alert_type}: {alert_data}")

        # Publicar no event bus
        self.event_bus.publish(f"alert.{alert_type}", alert_data)

        # Chamar callbacks registrados
        for callback in self.alert_callbacks:
            try:
                await callback(alert_type, alert_data)
            except Exception as e:
                logger.error(f"❌ Erro ao executar callback de alerta: {e}")

        self.integration_stats["alerts_generated"] += 1

    # Event Handlers
    async def _handle_data_quality_alert(self, alert_data: Dict[str, Any]):
        """Handler para alertas de qualidade de dados."""
        logger.warning(f"🔍 Alerta de qualidade de dados: {alert_data}")

        # Verificar se qualidade está abaixo do threshold
        if alert_data.get("quality_score", 1.0) < self.config.alert_thresholds["data_quality_score_min"]:
            await self._trigger_alert("data_quality_low", alert_data)

    async def _handle_test_completion(self, event_data: Dict[str, Any]):
        """Handler para completion de testes."""
        test_result = event_data.get("test_result")
        if test_result:
            logger.info(f"✅ Teste completado: {test_result.test_id}")

            # Remover dos testes ativos
            if test_result.test_id in self.active_tests:
                del self.active_tests[test_result.test_id]

            # Chamar callbacks de completion
            for callback in self.test_completion_callbacks:
                try:
                    await callback(test_result)
                except Exception as e:
                    logger.error(f"❌ Erro ao executar callback de completion: {e}")

    async def _handle_optimization_completion(self, event_data: Dict[str, Any]):
        """Handler para completion de otimização."""
        logger.info(f"🧠 Otimização completada: {event_data}")
        self.integration_stats["optimization_cycles"] += 1

    # Public Interface
    def add_alert_callback(self, callback: Callable):
        """Adiciona callback para alertas."""
        self.alert_callbacks.append(callback)

    def add_test_completion_callback(self, callback: Callable):
        """Adiciona callback para completion de testes."""
        self.test_completion_callbacks.append(callback)

    def get_integration_status(self) -> Dict[str, Any]:
        """Retorna status completo da integração."""
        return {
            "is_initialized": self.is_initialized,
            "is_running": self.is_running,
            "active_tests": len(self.active_tests),
            "components": {
                "live_feed": self.live_feed_integration.is_running if self.live_feed_integration else False,
                "bayesian_optimizer": self.bayesian_optimizer is not None,
                "parameter_tuner": self.parameter_tuner.is_running if self.parameter_tuner else False,
                "ab_testing": self.ab_test_framework.is_running if self.ab_test_framework else False
            },
            "statistics": self.integration_stats.copy()
        }

    async def stop_integration(self):
        """Para a integração completa."""
        logger.info("🛑 Parando integração completa...")

        self.is_running = False

        # Parar componentes
        if self.live_feed_integration:
            await self.live_feed_integration.stop()

        if self.parameter_tuner:
            await self.parameter_tuner.stop()

        if self.ab_test_framework:
            await self.ab_test_framework.stop_test()

        logger.info("✅ Integração completa parada")
