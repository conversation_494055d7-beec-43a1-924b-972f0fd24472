from unittest.mock import MagicMock
from tests.strategies.test_adaptive_liquidity import DummyBus
from qualia.adaptive_evolution import AdaptiveConsciousnessEvolution
from qualia.events import RiskAdjustmentRequestedEvent


def test_ace_consumes_adjustment_requested() -> None:
    bus = DummyBus()
    ace = AdaptiveConsciousnessEvolution(qualia_universe=MagicMock(), event_bus=bus)

    bus.publish(
        "risk.adjustment_requested",
        RiskAdjustmentRequestedEvent(
            symbol="BTC",
            stop_loss=90.0,
            take_profit=110.0,
            entropy=0.5,
            otoc=0.3,
        ),
    )

    assert ace.last_risk_adjustment_request.symbol == "BTC"
