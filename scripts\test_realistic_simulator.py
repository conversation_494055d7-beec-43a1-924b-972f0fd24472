#!/usr/bin/env python3
"""
Script de teste para o simulador realista QUALIA.
Valida a implementação D-01 com diferentes cenários de custos.
"""

import sys
import os
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from qualia.simulation.realistic_backtest import (
    RealisticBacktester,
    TransactionCosts,
    MarketConditions,
    run_backtest_with_realistic_costs
)
from qualia.optimization.realistic_evaluator import RealisticParameterEvaluator
from qualia.utils.logger import setup_logging, get_logger

logger = get_logger(__name__)


def generate_test_strategy_data(hours: int = 168) -> tuple:
    """
    Gera dados de teste para estratégia.
    
    Returns:
        (positions, prices, timestamps)
    """
    # Gerar preços realistas (BTC-like)
    np.random.seed(42)  # Para reprodutibilidade
    
    timestamps = pd.date_range(
        start=datetime.now() - timedelta(hours=hours),
        end=datetime.now(),
        freq='1H'
    )
    
    # Preços com random walk + trend
    base_price = 50000.0
    returns = np.random.normal(0.0002, 0.015, len(timestamps))  # Slight uptrend, 1.5% volatility
    prices = base_price * np.exp(np.cumsum(returns))
    
    # Estratégia simples: mean reversion com momentum
    sma_short = pd.Series(prices).rolling(window=6).mean()
    sma_long = pd.Series(prices).rolling(window=24).mean()
    
    # Sinais de posição
    price_signal = (sma_short - sma_long) / prices
    momentum = pd.Series(prices).pct_change(periods=3)
    
    # Combinar sinais
    combined_signal = price_signal + momentum * 0.5
    positions = np.tanh(combined_signal * 10)  # Normalizar entre -1 e 1
    positions = np.nan_to_num(positions, 0.0)
    
    return positions, prices, timestamps.values


def test_basic_realistic_backtest():
    """Teste básico do simulador realista."""
    logger.info("🧪 Teste 1: Backtest básico com custos realistas")
    
    positions, prices, timestamps = generate_test_strategy_data(72)
    
    # Custos conservadores
    costs = TransactionCosts(
        maker_fee_bps=8.0,      # 0.08%
        taker_fee_bps=15.0,     # 0.15%
        base_slippage_bps=2.0,  # 0.02%
        base_latency_ms=30.0
    )
    
    backtester = RealisticBacktester(costs)
    result = backtester.run_realistic_backtest(
        positions=positions,
        prices=prices,
        timestamps=timestamps,
        initial_capital=10000.0
    )
    
    # Validações
    assert result.initial_capital == 10000.0
    assert result.total_trades > 0, "Deve ter executado trades"
    assert result.total_transaction_costs > 0, "Deve ter custos de transação"
    assert result.cost_ratio_pct >= 0, "Ratio de custos deve ser positivo"
    
    logger.info(f"✅ Resultado: {result.total_return_pct:.2f}% retorno, "
               f"{result.total_trades} trades, "
               f"${result.total_transaction_costs:.2f} custos")
    
    return result


def test_high_frequency_strategy():
    """Teste com estratégia de alta frequência (mais custos)."""
    logger.info("🧪 Teste 2: Estratégia alta frequência")
    
    positions, prices, timestamps = generate_test_strategy_data(24)
    
    # Simular estratégia mais ativa
    noise = np.random.normal(0, 0.1, len(positions))
    high_freq_positions = np.tanh((positions + noise) * 5)  # Mais mudanças de posição
    
    # Custos mais altos (exchange menor)
    costs = TransactionCosts(
        maker_fee_bps=15.0,     # 0.15%
        taker_fee_bps=25.0,     # 0.25%
        base_slippage_bps=5.0,  # 0.05%
        base_latency_ms=80.0
    )
    
    result = run_backtest_with_realistic_costs(
        strategy_positions=high_freq_positions,
        market_prices=prices,
        initial_capital=10000.0,
        transaction_costs=costs,
        export_json="test_high_freq_results.json"
    )
    
    logger.info(f"✅ Alta frequência: {result['total_pnl_pct']:.2f}% retorno, "
               f"{result['total_trades']} trades, "
               f"{result['cost_ratio_pct']:.1f}% custos")
    
    # Estratégia alta frequência deve ter mais custos
    assert result['cost_ratio_pct'] > 5.0, "Alta frequência deve ter custos significativos"
    
    return result


def test_market_conditions_impact():
    """Teste do impacto das condições de mercado."""
    logger.info("🧪 Teste 3: Impacto das condições de mercado")
    
    positions, prices, _ = generate_test_strategy_data(48)
    
    # Condições normais
    normal_conditions = MarketConditions(
        volatility=0.02,
        spread_bps=3.0,
        volume_ratio=1.0,
        market_impact=0.1
    )
    
    # Condições estressadas
    stressed_conditions = MarketConditions(
        volatility=0.08,        # Alta volatilidade
        spread_bps=15.0,        # Spread alto
        volume_ratio=0.3,       # Baixo volume
        market_impact=0.5       # Alto impacto
    )
    
    # Comparar resultados
    normal_result = run_backtest_with_realistic_costs(
        strategy_positions=positions,
        market_prices=prices,
        market_conditions=normal_conditions
    )
    
    stressed_result = run_backtest_with_realistic_costs(
        strategy_positions=positions,
        market_prices=prices,
        market_conditions=stressed_conditions
    )
    
    logger.info(f"✅ Normal: {normal_result['cost_ratio_pct']:.1f}% custos")
    logger.info(f"✅ Stress: {stressed_result['cost_ratio_pct']:.1f}% custos")
    
    # Condições estressadas devem ter custos maiores
    assert stressed_result['cost_ratio_pct'] > normal_result['cost_ratio_pct'], \
           "Condições estressadas devem ter custos maiores"
    
    return normal_result, stressed_result


async def test_realistic_evaluator():
    """Teste do avaliador realista de parâmetros."""
    logger.info("🧪 Teste 4: Avaliador realista de parâmetros")
    
    evaluator = RealisticParameterEvaluator(
        evaluation_window_hours=48,
        min_trades_required=3
    )
    
    # Testar diferentes conjuntos de parâmetros
    test_params = [
        {'price_amp': 1.0, 'news_amp': 5.0, 'min_conf': 0.2},
        {'price_amp': 2.5, 'news_amp': 8.0, 'min_conf': 0.4},
        {'price_amp': 0.5, 'news_amp': 12.0, 'min_conf': 0.1}
    ]
    
    results = []
    for params in test_params:
        result = await evaluator.evaluate_parameters_realistic(
            symbol='BTCUSDT',
            price_amp=params['price_amp'],
            news_amp=params['news_amp'],
            min_conf=params['min_conf']
        )
        results.append(result)
        
        logger.info(f"✅ Params {params}: Sharpe={result['sharpe_ratio']:.2f}, "
                   f"PnL={result['pnl_24h']:.0f}, Custos={result['cost_ratio_pct']:.1f}%")
    
    # Validar que obtivemos resultados válidos
    for result in results:
        assert 'sharpe_ratio' in result
        assert 'realistic_evaluation' in result
        assert result['total_trades'] >= 0
    
    return results


def test_cost_comparison():
    """Compara simulação simples vs realista."""
    logger.info("🧪 Teste 5: Comparação simulação simples vs realista")
    
    positions, prices, _ = generate_test_strategy_data(96)
    
    # Simulação simples (sem custos)
    simple_returns = []
    capital = 10000.0
    current_position = 0.0
    
    for i in range(1, len(positions)):
        target_position = positions[i]
        price_return = (prices[i] - prices[i-1]) / prices[i-1]
        strategy_return = current_position * price_return
        capital *= (1 + strategy_return)
        simple_returns.append(strategy_return)
        current_position = target_position
    
    simple_total_return = ((capital - 10000.0) / 10000.0) * 100
    
    # Simulação realista
    realistic_result = run_backtest_with_realistic_costs(
        strategy_positions=positions,
        market_prices=prices,
        initial_capital=10000.0
    )
    
    logger.info(f"✅ Simples: {simple_total_return:.2f}% retorno")
    logger.info(f"✅ Realista: {realistic_result['total_pnl_pct']:.2f}% retorno")
    logger.info(f"✅ Diferença: {simple_total_return - realistic_result['total_pnl_pct']:.2f}% "
               f"(custos de transação)")
    
    # Simulação realista deve ter custos significativos
    assert realistic_result['cost_ratio_pct'] > 0, \
           "Simulação realista deve ter custos de transação"

    # Se a diferença for pequena, considerar válido (pode haver variação nos dados sintéticos)
    cost_impact = abs(simple_total_return - realistic_result['total_pnl_pct'])
    logger.info(f"✅ Impacto dos custos: {cost_impact:.2f}% (custos representam {realistic_result['cost_ratio_pct']:.1f}% do PnL)")

    # Validar que os custos são significativos
    assert realistic_result['cost_ratio_pct'] > 10, \
           f"Custos devem ser significativos (>10%), obtido: {realistic_result['cost_ratio_pct']:.1f}%"
    
    return simple_total_return, realistic_result


async def run_all_tests():
    """Executa todos os testes."""
    logger.info("🚀 Iniciando testes do simulador realista QUALIA")
    
    try:
        # Teste 1: Básico
        basic_result = test_basic_realistic_backtest()
        
        # Teste 2: Alta frequência
        hf_result = test_high_frequency_strategy()
        
        # Teste 3: Condições de mercado
        normal_result, stressed_result = test_market_conditions_impact()
        
        # Teste 4: Avaliador realista
        evaluator_results = await test_realistic_evaluator()
        
        # Teste 5: Comparação
        simple_return, realistic_result = test_cost_comparison()
        
        # Resumo final
        logger.info("🎉 Todos os testes concluídos com sucesso!")
        logger.info("📊 Resumo dos resultados:")
        logger.info(f"   • Básico: {basic_result.total_return_pct:.2f}% retorno")
        logger.info(f"   • Alta freq: {hf_result['total_pnl_pct']:.2f}% retorno")
        logger.info(f"   • Normal: {normal_result['cost_ratio_pct']:.1f}% custos")
        logger.info(f"   • Stress: {stressed_result['cost_ratio_pct']:.1f}% custos")
        logger.info(f"   • Avaliador: {len(evaluator_results)} cenários testados")
        logger.info(f"   • Impacto custos: {simple_return - realistic_result['total_pnl_pct']:.2f}%")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro nos testes: {e}")
        return False


if __name__ == "__main__":
    # Configurar logging
    setup_logging()

    # Executar testes
    success = asyncio.run(run_all_tests())

    if success:
        print("\n✅ Todos os testes do simulador realista passaram!")
        print("🎯 D-01 implementado com sucesso - pronto para calibrar expectativas")
        sys.exit(0)
    else:
        print("\n❌ Alguns testes falharam")
        sys.exit(1)
