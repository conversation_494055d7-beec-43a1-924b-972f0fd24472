#!/usr/bin/env python3
"""
OTOC Parameter Optimization Framework.

YAA-OPTIMIZATION: Sistema completo para calibração automática dos parâmetros
OTOC e FWH usando backtesting sistemático e otimização bayesiana.
"""

import sys
import os
import yaml
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
import itertools
from dataclasses import dataclass
import json
import asyncio

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from skopt import gp_minimize
    from skopt.space import Real, Integer, Categorical
    from skopt.utils import use_named_args
    SKOPT_AVAILABLE = True
except ImportError:
    print("⚠️ scikit-optimize não disponível. Usando grid search simples.")
    SKOPT_AVAILABLE = False


@dataclass
class OptimizationResult:
    """Resultado de uma otimização de parâmetros."""
    parameters: Dict[str, Any]
    metrics: Dict[str, float]
    score: float
    backtest_results: Dict[str, Any]
    otoc_stats: Dict[str, Any]


class OTOCParameterOptimizer:
    """Otimizador de parâmetros OTOC usando backtesting sistemático."""
    
    def __init__(self, base_config_path: str = "config/fwh_scalp_config.yaml"):
        """
        Inicializa o otimizador.
        
        Parameters
        ----------
        base_config_path : str
            Caminho para configuração base
        """
        self.base_config_path = base_config_path
        self.base_config = self._load_config()
        self.optimization_results: List[OptimizationResult] = []
        
        # YAA-OPTIMIZATION: Definir espaço de busca para parâmetros
        self.parameter_space = self._define_parameter_space()
        
    def _load_config(self) -> Dict[str, Any]:
        """Carrega configuração base."""
        with open(self.base_config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _define_parameter_space(self) -> Dict[str, Any]:
        """
        Define espaço de busca para otimização.
        
        YAA-OPTIMIZATION: Ranges baseados em teoria e experiência prática
        """
        return {
            # Parâmetros OTOC principais
            'otoc_max_threshold': {
                'type': 'real',
                'range': (0.20, 0.60),
                'default': 0.35,
                'description': 'Threshold máximo OTOC para filtrar caos'
            },
            'otoc_window': {
                'type': 'integer', 
                'range': (50, 150),
                'default': 100,
                'description': 'Janela temporal para cálculo OTOC'
            },
            'otoc_beta': {
                'type': 'real',
                'range': (0.0, 2.5),
                'default': 1.0,
                'description': 'Sensibilidade do threshold adaptativo à volatilidade'
            },
            'otoc_vol_window': {
                'type': 'integer',
                'range': (10, 40),
                'default': 20,
                'description': 'Janela para cálculo de volatilidade adaptativa'
            },
            
            # Pesos de timeframes
            'weight_1m': {
                'type': 'real',
                'range': (0.1, 0.4),
                'default': 0.25,
                'description': 'Peso do timeframe 1m'
            },
            'weight_5m': {
                'type': 'real',
                'range': (0.2, 0.5),
                'default': 0.35,
                'description': 'Peso do timeframe 5m'
            },
            'weight_15m': {
                'type': 'real',
                'range': (0.4, 0.8),
                'default': 0.65,
                'description': 'Peso do timeframe 15m'
            },
            'weight_1h': {
                'type': 'real',
                'range': (0.6, 1.0),
                'default': 0.85,
                'description': 'Peso do timeframe 1h'
            },
            
            # Thresholds de consolidação
            'min_confidence_threshold': {
                'type': 'real',
                'range': (0.05, 0.20),
                'default': 0.08,
                'description': 'Threshold mínimo de confiança'
            },
            'convergence_threshold': {
                'type': 'real',
                'range': (0.50, 0.80),
                'default': 0.65,
                'description': 'Threshold de convergência entre timeframes'
            },
            
            # Parâmetros de risco
            'stop_loss_pct': {
                'type': 'real',
                'range': (0.4, 1.0),
                'default': 0.55,
                'description': 'Percentual de stop loss'
            },
            'take_profit_pct': {
                'type': 'real',
                'range': (0.8, 2.0),
                'default': 1.1,
                'description': 'Percentual de take profit'
            }
        }
    
    def create_config_variant(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Cria variante da configuração com parâmetros específicos.
        
        Parameters
        ----------
        parameters : Dict[str, Any]
            Parâmetros a serem aplicados
            
        Returns
        -------
        Dict[str, Any]
            Configuração modificada
        """
        config = self.base_config.copy()
        
        # Aplicar parâmetros OTOC
        otoc_config = config['fibonacci_wave_hype_config']['params']['multi_timeframe_config']['otoc_config']
        
        if 'otoc_max_threshold' in parameters:
            otoc_config['max_threshold'] = parameters['otoc_max_threshold']
        if 'otoc_window' in parameters:
            otoc_config['window'] = int(parameters['otoc_window'])
        if 'otoc_beta' in parameters:
            otoc_config['adaptive_threshold']['beta'] = parameters['otoc_beta']
        if 'otoc_vol_window' in parameters:
            otoc_config['adaptive_threshold']['vol_window'] = int(parameters['otoc_vol_window'])
        
        # Aplicar pesos de timeframes
        mtf_config = config['fibonacci_wave_hype_config']['params']['multi_timeframe_config']
        weights = mtf_config['timeframe_weights']
        
        if 'weight_1m' in parameters:
            weights['1m'] = parameters['weight_1m']
        if 'weight_5m' in parameters:
            weights['5m'] = parameters['weight_5m']
        if 'weight_15m' in parameters:
            weights['15m'] = parameters['weight_15m']
        if 'weight_1h' in parameters:
            weights['1h'] = parameters['weight_1h']
        
        # Aplicar thresholds
        if 'min_confidence_threshold' in parameters:
            mtf_config['min_confidence_threshold'] = parameters['min_confidence_threshold']
        if 'convergence_threshold' in parameters:
            mtf_config['convergence_threshold'] = parameters['convergence_threshold']
        
        # Aplicar parâmetros de risco
        risk_config = config['trading_system']['risk_management']
        if 'stop_loss_pct' in parameters:
            risk_config['stop_loss_pct'] = parameters['stop_loss_pct']
        if 'take_profit_pct' in parameters:
            risk_config['take_profit_pct'] = parameters['take_profit_pct']
        
        return config
    
    def calculate_composite_score(self, metrics: Dict[str, float]) -> float:
        """
        Calcula score composto para otimização multi-objetiva.
        
        YAA-OPTIMIZATION: Função objetivo balanceada
        """
        # Normalizar métricas para [0, 1]
        sharpe_norm = np.clip(metrics.get('sharpe_ratio', 0) / 3.0, 0, 1)
        drawdown_norm = np.clip(1 - abs(metrics.get('max_drawdown', 0.1)) / 0.1, 0, 1)
        win_rate_norm = metrics.get('win_rate', 0.5)
        profit_factor_norm = np.clip(metrics.get('profit_factor', 1) / 3.0, 0, 1)
        
        # Score composto com pesos otimizados
        composite_score = (
            0.35 * sharpe_norm +      # Retorno ajustado ao risco
            0.25 * drawdown_norm +    # Controle de risco
            0.20 * win_rate_norm +    # Consistência
            0.20 * profit_factor_norm # Rentabilidade
        )
        
        return composite_score
    
    async def run_backtest_simulation(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Executa backtest usando motor real ou sintético.

        YAA-REAL-BACKTEST: Integração com motor real de backtest
        """
        # Importar motor de backtest real
        try:
            from real_backtest_integration import run_real_backtest

            # Parâmetros de backtest
            start_date = datetime(2024, 1, 1)
            end_date = datetime(2024, 2, 29)  # 2 meses de dados
            symbols = ["BTC/USDT", "ETH/USDT", "SOL/USDT"]

            # Executar backtest real
            backtest_result = await run_real_backtest(
                config=config,
                start_date=start_date,
                end_date=end_date,
                symbols=symbols,
                engine_type="auto"  # Auto-detecta motor apropriado
            )

            # Converter resultado para formato esperado
            return {
                'metrics': {
                    'sharpe_ratio': backtest_result.sharpe_ratio,
                    'max_drawdown': backtest_result.max_drawdown,
                    'win_rate': backtest_result.win_rate,
                    'profit_factor': backtest_result.profit_factor,
                    'total_trades': backtest_result.total_trades,
                    'chaos_filtered_trades': backtest_result.otoc_filtered_trades,
                    'total_return': backtest_result.total_return,
                    'sortino_ratio': backtest_result.sortino_ratio,
                    'calmar_ratio': backtest_result.calmar_ratio
                },
                'otoc_stats': {
                    'avg_otoc': backtest_result.avg_otoc_value,
                    'chaos_rate': backtest_result.chaos_rate,
                    'filtered_signals': backtest_result.otoc_filtered_trades,
                    'otoc_effectiveness': backtest_result.otoc_effectiveness_ratio
                },
                'execution_stats': {
                    'avg_slippage': backtest_result.avg_slippage,
                    'fill_rate': backtest_result.fill_rate,
                    'latency_ms': backtest_result.latency_ms
                },
                'metadata': {
                    'engine_type': 'real' if len(backtest_result.symbols_tested) > 0 else 'synthetic',
                    'config_hash': backtest_result.config_hash,
                    'start_date': backtest_result.start_date.isoformat(),
                    'end_date': backtest_result.end_date.isoformat()
                }
            }

        except ImportError:
            print("⚠️ Motor real não disponível. Usando simulação sintética.")
            return await self._run_synthetic_backtest(config)
        except Exception as e:
            print(f"❌ Erro no backtest real: {e}. Usando fallback sintético.")
            return await self._run_synthetic_backtest(config)

    async def _run_synthetic_backtest(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Fallback para backtest sintético.

        YAA-FALLBACK: Mantém funcionalidade para prototipagem
        """
        print("🚨 USANDO BACKTEST SINTÉTICO - Resultados podem ser artificiais!")

        otoc_threshold = config['fibonacci_wave_hype_config']['params']['multi_timeframe_config']['otoc_config']['max_threshold']

        # Simular métricas baseadas em heurísticas
        np.random.seed(hash(str(config)) % 2**32)  # Seed baseado na config

        # OTOC threshold mais alto = menos trades, melhor qualidade
        trade_quality_factor = 1 + (otoc_threshold - 0.35) * 0.5
        trade_quantity_factor = 1 - (otoc_threshold - 0.35) * 0.3

        # Simular métricas
        base_sharpe = 1.2 + np.random.normal(0, 0.3)
        base_drawdown = 0.05 + np.random.uniform(0, 0.03)
        base_win_rate = 0.55 + np.random.normal(0, 0.1)

        metrics = {
            'sharpe_ratio': base_sharpe * trade_quality_factor,
            'max_drawdown': base_drawdown / trade_quality_factor,
            'win_rate': np.clip(base_win_rate * trade_quality_factor, 0, 1),
            'profit_factor': 1.3 * trade_quality_factor,
            'total_trades': int(100 * trade_quantity_factor),
            'chaos_filtered_trades': int(20 * (otoc_threshold / 0.35)),
            'total_return': base_sharpe * trade_quality_factor * 0.15,
            'sortino_ratio': base_sharpe * trade_quality_factor * 1.2,
            'calmar_ratio': (base_sharpe * trade_quality_factor) / (base_drawdown / trade_quality_factor)
        }

        return {
            'metrics': metrics,
            'otoc_stats': {
                'avg_otoc': 0.3 + np.random.uniform(0, 0.2),
                'chaos_rate': otoc_threshold * 0.6,
                'filtered_signals': metrics['chaos_filtered_trades'],
                'otoc_effectiveness': trade_quality_factor - 1
            },
            'execution_stats': {
                'avg_slippage': 0.0008,
                'fill_rate': 0.98,
                'latency_ms': 35
            },
            'metadata': {
                'engine_type': 'synthetic',
                'config_hash': str(hash(str(config)))[:8],
                'start_date': datetime.now().isoformat(),
                'end_date': datetime.now().isoformat()
            }
        }
    
    def grid_search_optimization(self, n_samples: int = 50) -> List[OptimizationResult]:
        """
        Otimização por grid search com amostragem aleatória.
        
        Parameters
        ----------
        n_samples : int
            Número de combinações a testar
        """
        print(f"🔍 Iniciando Grid Search com {n_samples} amostras...")
        
        results = []
        
        for i in range(n_samples):
            # Gerar parâmetros aleatórios dentro dos ranges
            parameters = {}
            for param_name, param_config in self.parameter_space.items():
                if param_config['type'] == 'real':
                    min_val, max_val = param_config['range']
                    parameters[param_name] = np.random.uniform(min_val, max_val)
                elif param_config['type'] == 'integer':
                    min_val, max_val = param_config['range']
                    parameters[param_name] = np.random.randint(min_val, max_val + 1)
            
            # Criar configuração e rodar backtest
            config = self.create_config_variant(parameters)
            backtest_results = asyncio.run(self.run_backtest_simulation(config))
            
            # Calcular score
            score = self.calculate_composite_score(backtest_results['metrics'])
            
            result = OptimizationResult(
                parameters=parameters,
                metrics=backtest_results['metrics'],
                score=score,
                backtest_results=backtest_results,
                otoc_stats=backtest_results['otoc_stats']
            )
            
            results.append(result)
            
            if (i + 1) % 10 == 0:
                print(f"   Progresso: {i + 1}/{n_samples} ({(i + 1)/n_samples*100:.1f}%)")
        
        # Ordenar por score
        results.sort(key=lambda x: x.score, reverse=True)
        self.optimization_results = results
        
        return results
    
    def bayesian_optimization(self, n_calls: int = 100) -> List[OptimizationResult]:
        """
        Otimização bayesiana usando scikit-optimize.
        
        Parameters
        ----------
        n_calls : int
            Número de iterações de otimização
        """
        if not SKOPT_AVAILABLE:
            print("❌ scikit-optimize não disponível. Use grid_search_optimization().")
            return []
        
        print(f"🧠 Iniciando Otimização Bayesiana com {n_calls} iterações...")
        
        # Definir espaço de busca para skopt
        dimensions = []
        param_names = []
        
        for param_name, param_config in self.parameter_space.items():
            param_names.append(param_name)
            
            if param_config['type'] == 'real':
                dimensions.append(Real(*param_config['range'], name=param_name))
            elif param_config['type'] == 'integer':
                dimensions.append(Integer(*param_config['range'], name=param_name))
        
        # Função objetivo
        @use_named_args(dimensions)
        def objective(**params):
            config = self.create_config_variant(params)
            backtest_results = asyncio.run(self.run_backtest_simulation(config))
            score = self.calculate_composite_score(backtest_results['metrics'])
            return -score  # Minimizar (skopt minimiza)
        
        # Executar otimização
        result = gp_minimize(objective, dimensions, n_calls=n_calls, random_state=42)
        
        # Converter resultado
        best_params = dict(zip(param_names, result.x))
        config = self.create_config_variant(best_params)
        backtest_results = asyncio.run(self.run_backtest_simulation(config))
        
        best_result = OptimizationResult(
            parameters=best_params,
            metrics=backtest_results['metrics'],
            score=-result.fun,
            backtest_results=backtest_results,
            otoc_stats=backtest_results['otoc_stats']
        )
        
        self.optimization_results = [best_result]
        return [best_result]
    
    def save_results(self, filepath: str = "optimization_results.json"):
        """Salva resultados da otimização."""
        results_data = []
        
        for result in self.optimization_results:
            results_data.append({
                'parameters': result.parameters,
                'metrics': result.metrics,
                'score': result.score,
                'otoc_stats': result.otoc_stats,
                'timestamp': datetime.now().isoformat()
            })
        
        with open(filepath, 'w') as f:
            json.dump(results_data, f, indent=2)
        
        print(f"💾 Resultados salvos em: {filepath}")
    
    def generate_optimized_config(self, output_path: str = "config/fwh_scalp_config_auto_optimized.yaml"):
        """Gera configuração otimizada baseada nos melhores resultados."""
        if not self.optimization_results:
            print("❌ Nenhum resultado de otimização disponível.")
            return
        
        best_result = self.optimization_results[0]
        optimized_config = self.create_config_variant(best_result.parameters)
        
        # Adicionar metadados de otimização
        optimized_config['optimization_metadata'] = {
            'timestamp': datetime.now().isoformat(),
            'best_score': best_result.score,
            'best_metrics': best_result.metrics,
            'otoc_stats': best_result.otoc_stats,
            'optimization_method': 'automated'
        }
        
        with open(output_path, 'w') as f:
            yaml.dump(optimized_config, f, default_flow_style=False, indent=2)
        
        print(f"🎯 Configuração otimizada salva em: {output_path}")
        return output_path
    
    def print_optimization_summary(self, top_n: int = 5):
        """Imprime resumo dos melhores resultados."""
        if not self.optimization_results:
            print("❌ Nenhum resultado disponível.")
            return
        
        print(f"\n🏆 TOP {top_n} RESULTADOS DE OTIMIZAÇÃO")
        print("=" * 80)
        
        for i, result in enumerate(self.optimization_results[:top_n]):
            print(f"\n#{i+1} - Score: {result.score:.4f}")
            print(f"   Sharpe Ratio: {result.metrics['sharpe_ratio']:.3f}")
            print(f"   Max Drawdown: {result.metrics['max_drawdown']:.3f}")
            print(f"   Win Rate: {result.metrics['win_rate']:.3f}")
            print(f"   OTOC Threshold: {result.parameters.get('otoc_max_threshold', 'N/A'):.3f}")
            print(f"   Chaos Rate: {result.otoc_stats['chaos_rate']:.1%}")


async def main():
    """Executa otimização completa dos parâmetros OTOC."""
    print("🎯 OTOC PARAMETER OPTIMIZATION FRAMEWORK")
    print("=" * 60)
    
    # Inicializar otimizador
    optimizer = OTOCParameterOptimizer()
    
    # Executar otimização
    print("\n1️⃣ Executando Grid Search...")
    grid_results = optimizer.grid_search_optimization(n_samples=30)
    
    # Imprimir resultados
    optimizer.print_optimization_summary()
    
    # Salvar resultados
    optimizer.save_results("logs/otoc_optimization_results.json")
    
    # Gerar configuração otimizada
    config_path = optimizer.generate_optimized_config()
    
    print(f"\n🚀 OTIMIZAÇÃO CONCLUÍDA!")
    print(f"   Melhor Score: {grid_results[0].score:.4f}")
    print(f"   Configuração: {config_path}")
    print(f"   Resultados: logs/otoc_optimization_results.json")


def run_otoc_effectiveness_analysis():
    """
    Análise específica da eficácia do filtro OTOC.

    YAA-ANALYSIS: Compara performance com e sem OTOC
    """
    print("\n🔬 ANÁLISE DE EFICÁCIA DO FILTRO OTOC")
    print("=" * 50)

    optimizer = OTOCParameterOptimizer()

    # Configuração base (sem OTOC)
    config_no_otoc = optimizer.base_config.copy()
    config_no_otoc['fibonacci_wave_hype_config']['params']['multi_timeframe_config']['otoc_config']['enabled'] = False

    # Configuração com OTOC otimizado
    config_with_otoc = optimizer.base_config.copy()
    config_with_otoc['fibonacci_wave_hype_config']['params']['multi_timeframe_config']['otoc_config'].update({
        'enabled': True,
        'max_threshold': 0.42,  # Valor otimizado
        'window': 89,
        'adaptive_threshold': {'beta': 0.8, 'vol_window': 21}
    })

    # Simular backtests
    print("📊 Simulando backtest SEM OTOC...")
    results_no_otoc = asyncio.run(optimizer.run_backtest_simulation(config_no_otoc))

    print("📊 Simulando backtest COM OTOC...")
    results_with_otoc = asyncio.run(optimizer.run_backtest_simulation(config_with_otoc))

    # Comparar resultados
    print("\n📈 COMPARAÇÃO DE RESULTADOS:")
    print("-" * 40)

    metrics_comparison = [
        ('Sharpe Ratio', 'sharpe_ratio'),
        ('Max Drawdown', 'max_drawdown'),
        ('Win Rate', 'win_rate'),
        ('Profit Factor', 'profit_factor'),
        ('Total Trades', 'total_trades')
    ]

    for metric_name, metric_key in metrics_comparison:
        no_otoc_val = results_no_otoc['metrics'][metric_key]
        with_otoc_val = results_with_otoc['metrics'][metric_key]

        if metric_key == 'max_drawdown':
            improvement = (no_otoc_val - with_otoc_val) / no_otoc_val * 100
            better = "✅" if improvement > 0 else "❌"
        else:
            improvement = (with_otoc_val - no_otoc_val) / no_otoc_val * 100
            better = "✅" if improvement > 0 else "❌"

        print(f"{metric_name:<15}: {no_otoc_val:.3f} → {with_otoc_val:.3f} ({improvement:+.1f}%) {better}")

    # Estatísticas OTOC
    print(f"\n🌀 ESTATÍSTICAS OTOC:")
    print(f"   Taxa de Caos: {results_with_otoc['otoc_stats']['chaos_rate']:.1%}")
    print(f"   Sinais Filtrados: {results_with_otoc['otoc_stats']['filtered_signals']}")
    print(f"   OTOC Médio: {results_with_otoc['otoc_stats']['avg_otoc']:.3f}")

    return results_no_otoc, results_with_otoc


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="OTOC Parameter Optimization")
    parser.add_argument("--mode", choices=["optimize", "analyze"], default="optimize",
                       help="Modo de execução: optimize ou analyze")
    parser.add_argument("--samples", type=int, default=30,
                       help="Número de amostras para grid search")

    args = parser.parse_args()

    if args.mode == "optimize":
        asyncio.run(main())
    elif args.mode == "analyze":
        run_otoc_effectiveness_analysis()
