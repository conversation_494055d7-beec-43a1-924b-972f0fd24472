# Fluxo de Geração de Sinais

Este documento explica como os módulos `QUALIAQuantumUniverse` (`universe.py`) e `QASTCore` (`qast_core.py`) interagem para produzir métricas e sinais de trading no QUALIA.

## 1. Codificação do Snapshot de Mercado

1. **Coleta de dados** – o `QUALIAQuantumUniverse` recebe um dicionário de features e aplica `encode_market_data`, convertendo as entradas em amplitudes quânticas.
2. **Atualização do estado** – após a codificação, `update_last_statevector` registra o novo vetor de estado que será usado no ciclo do QAST.

```python
sv = self.universe.encode_snapshot(snapshot)
new_state = self.universe.update(sv, timestamp=now, trace_id=trace_id)
```

## 2. Extração de Métricas Quânticas

Em seguida, o universo calcula métricas como entropia quântica e coerência:

```python
qm = compute_quantum_metrics(self.universe, new_state["vector"])
C = qm["coherence"]
```

Essas métricas permanecem armazenadas em `self.metrics` e podem ser consultadas via `get_latest_metrics()`.

## 3. Entropia Simbólica e Feedback

O `QASTCore` transforma o vetor quântico em um conjunto fixo de coeficientes com `extract_signal_features` e calcula a entropia simbólica:

```python
features = extract_signal_features(new_state["vector"], num_features=self.params.num_dct_features)
coeffs = self.generate_poly_coeffs(features, cycle)
H_symb = SymbolicEntropy.compute(coeffs)
```

A massa informacional é atualizada e um controlador PID ajusta parâmetros do universo (como `scr_depth` e `temperature`) de acordo com o erro em relação à meta de entropia.

## 4. Retorno do Ciclo

`run_cycle` devolve um registro contendo o novo estado, métricas quânticas e o sinal de controle gerado. O `TradingEngine` utiliza essas informações para compor o score final do sinal e decidir sobre abertura ou fechamento de posições.

## 5. Resumo

- `universe.py` é responsável por preparar o estado quântico e calcular métricas.
- `qast_core.py` coordena cada ciclo do QAST, extraindo características do estado e produzindo entropia simbólica.
- O feedback do `QASTCore` ajusta dinamicamente os parâmetros do universo, influenciando os sinais posteriores.

Consulte também [docs/api/qast_core.md](qast_core.md) para detalhes sobre a função `extract_signal_features`.
