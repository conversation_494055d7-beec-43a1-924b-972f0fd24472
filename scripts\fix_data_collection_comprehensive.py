#!/usr/bin/env python3
"""
QUALIA Data Collection Comprehensive Fix
Resolve problemas de coleta de dados e configure sistema para geração de sinais
"""

import asyncio
import json
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import ccxt.async_support as ccxt

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.utils.logger import get_logger

logger = get_logger(__name__)

class ComprehensiveDataFix:
    def __init__(self):
        self.symbols = ["BTC/USDT", "ETH/USDT", "ADA/USDT", "SOL/USDT"]
        self.timeframes = ["5m", "15m", "1h"]
        self.cache_dir = Path("data/cache")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Configurar exchange
        self.exchange = ccxt.kucoin({
            'apiKey': os.getenv('KUCOIN_API_KEY'),
            'secret': os.getenv('KUCOIN_SECRET_KEY'),
            'password': os.getenv('KUCOIN_PASSPHRASE'),
            'sandbox': False,
            'enableRateLimit': True,
            'timeout': 30000,
        })

    async def collect_comprehensive_data(self):
        """Coleta dados abrangentes para todos os símbolos e timeframes"""
        logger.info("🔄 Iniciando coleta abrangente de dados...")
        
        try:
            await self.exchange.load_markets()
            logger.info(f"✅ Mercados carregados: {len(self.exchange.markets)} símbolos")
            
            for symbol in self.symbols:
                for timeframe in self.timeframes:
                    await self.collect_symbol_data(symbol, timeframe)
                    await asyncio.sleep(1)  # Rate limiting
                    
        except Exception as e:
            logger.error(f"❌ Erro na coleta: {e}")
        finally:
            await self.exchange.close()

    async def collect_symbol_data(self, symbol: str, timeframe: str):
        """Coleta dados para um símbolo específico"""
        try:
            # Determinar quantidade de dados necessária
            limits = {
                "5m": 500,   # ~42 horas
                "15m": 300,  # ~75 horas  
                "1h": 200    # ~8 dias
            }
            
            limit = limits.get(timeframe, 200)
            
            logger.info(f"📊 Coletando {limit} candles para {symbol}@{timeframe}")
            
            # Buscar dados históricos
            ohlcv = await self.exchange.fetch_ohlcv(
                symbol=symbol,
                timeframe=timeframe,
                limit=limit
            )
            
            if not ohlcv:
                logger.warning(f"⚠️ Nenhum dado retornado para {symbol}@{timeframe}")
                return
                
            logger.info(f"✅ Coletados {len(ohlcv)} candles para {symbol}@{timeframe}")
            
            # Converter para DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # Salvar em múltiplos formatos
            await self.save_data(symbol, timeframe, df, ohlcv)
            
        except Exception as e:
            logger.error(f"❌ Erro coletando {symbol}@{timeframe}: {e}")

    async def save_data(self, symbol: str, timeframe: str, df: pd.DataFrame, raw_ohlcv: list):
        """Salva dados em múltiplos formatos para compatibilidade"""
        try:
            symbol_clean = symbol.replace("/", "")
            
            # 1. Formato CSV (para análise técnica)
            csv_path = self.cache_dir / f"{symbol_clean}_{timeframe}.csv"
            df.to_csv(csv_path)
            logger.info(f"💾 CSV salvo: {csv_path}")
            
            # 2. Formato JSON (compatibilidade com cache existente)
            json_path = self.cache_dir / f"{symbol_clean}_{timeframe}.json"
            
            # Formato esperado pelo sistema QUALIA
            cache_data = {
                "symbol": symbol,
                "timeframe": timeframe,
                "data": raw_ohlcv,
                "last_update": datetime.now().isoformat(),
                "count": len(raw_ohlcv),
                "metadata": {
                    "first_timestamp": raw_ohlcv[0][0] if raw_ohlcv else None,
                    "last_timestamp": raw_ohlcv[-1][0] if raw_ohlcv else None,
                    "source": "kucoin_comprehensive_fix"
                }
            }
            
            with open(json_path, 'w') as f:
                json.dump(cache_data, f, indent=2)
            logger.info(f"💾 JSON salvo: {json_path}")
            
            # 3. Verificar qualidade dos dados
            await self.verify_data_quality(symbol, timeframe, df)
            
        except Exception as e:
            logger.error(f"❌ Erro salvando dados para {symbol}@{timeframe}: {e}")

    async def verify_data_quality(self, symbol: str, timeframe: str, df: pd.DataFrame):
        """Verifica qualidade dos dados coletados"""
        try:
            if df.empty:
                logger.warning(f"⚠️ DataFrame vazio para {symbol}@{timeframe}")
                return
                
            # Verificações básicas
            missing_data = df.isnull().sum().sum()
            duplicate_timestamps = df.index.duplicated().sum()
            
            # Verificar continuidade temporal
            time_gaps = df.index.to_series().diff()
            expected_interval = pd.Timedelta(timeframe)
            large_gaps = (time_gaps > expected_interval * 2).sum()
            
            logger.info(f"📊 Qualidade {symbol}@{timeframe}:")
            logger.info(f"   - Registros: {len(df)}")
            logger.info(f"   - Dados faltantes: {missing_data}")
            logger.info(f"   - Timestamps duplicados: {duplicate_timestamps}")
            logger.info(f"   - Gaps temporais grandes: {large_gaps}")
            logger.info(f"   - Período: {df.index.min()} -> {df.index.max()}")
            
            # Verificar se há dados suficientes para análise técnica
            min_required = 50  # Mínimo para indicadores técnicos
            if len(df) >= min_required:
                logger.info(f"✅ Dados suficientes para análise técnica ({len(df)} >= {min_required})")
            else:
                logger.warning(f"⚠️ Dados insuficientes para análise técnica ({len(df)} < {min_required})")
                
        except Exception as e:
            logger.error(f"❌ Erro verificando qualidade: {e}")

    async def create_signal_test_config(self):
        """Cria configuração otimizada para geração de sinais"""
        try:
            config_path = Path("config/signal_generation_test.yaml")
            
            config_content = """
# Configuração otimizada para geração de sinais QUALIA
signal_approval:
  min_confidence: 0.01  # Muito baixo para testes
  min_volume_threshold: 0.01
  max_risk_per_trade: 0.05
  
holographic_trading:
  min_confidence: 0.05  # Muito baixo para testes
  volume_threshold: 0.1
  aggressive_mode: true
  risk_override: true
  
# Configurações de estratégia mais permissivas
strategy_config:
  nova_estrategia_qualia:
    coherence_threshold: 0.1  # Muito baixo
    entropy_threshold: 0.1    # Muito baixo
    min_data_points: 20       # Reduzido
    
# Configurações de dados mais flexíveis
data_collection:
  min_data_completeness: 0.5  # 50% em vez de 80%
  allow_partial_data: true
  fallback_enabled: true
  
# Risk management mais permissivo para testes
risk_management:
  max_portfolio_risk: 0.1
  max_position_size: 0.05
  stop_loss_pct: 0.02
  take_profit_pct: 0.04
"""
            
            with open(config_path, 'w') as f:
                f.write(config_content)
                
            logger.info(f"✅ Configuração de teste criada: {config_path}")
            
        except Exception as e:
            logger.error(f"❌ Erro criando configuração: {e}")

async def main():
    """Função principal"""
    logger.info("🚀 Iniciando correção abrangente de dados QUALIA")
    
    fixer = ComprehensiveDataFix()
    
    # 1. Coletar dados abrangentes
    await fixer.collect_comprehensive_data()
    
    # 2. Criar configuração otimizada
    await fixer.create_signal_test_config()
    
    logger.info("🎉 Correção abrangente concluída!")
    logger.info("📋 Próximos passos:")
    logger.info("   1. Execute: python scripts/start_real_trading.py --config config/signal_generation_test.yaml")
    logger.info("   2. Monitore logs para geração de sinais")
    logger.info("   3. Ajuste thresholds conforme necessário")

if __name__ == "__main__":
    asyncio.run(main())
