"""
QUALIA D-06: Exemplo de Uso Completo
====================================

Este exemplo demonstra como usar o sistema D-06 completo:
- Market Regime Detector
- A/B Testing Framework  
- Regime-aware Configuration Manager
- System Integration

Funcionalidades demonstradas:
1. Inicialização do sistema D-06
2. Detecção automática de regimes de mercado
3. Configuração automática baseada em regimes
4. Execução de testes A/B
5. Monitoramento e status do sistema
"""

import asyncio
import yaml
import sys
from datetime import datetime, timezone, timedelta
from pathlib import Path

# Adicionar diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

from src.qualia.d06_integration.d06_system_integration import (
    D06SystemIntegration,
    D06IntegrationConfig,
    initialize_d06_system
)
from src.qualia.config.hot_reload import ConfigurationHotReloader
from src.qualia.testing.ab_testing_framework import ABTestConfig
from src.qualia.market.regime_detector import MarketRegime
from src.qualia.config.regime_aware_config import RegimePreset


class D06UsageExample:
    """Exemplo completo de uso do sistema D-06."""
    
    def __init__(self):
        self.integration = None
        self.hot_reloader = None
    
    async def setup_system(self):
        """Configura e inicializa o sistema D-06."""
        print("🚀 Configurando sistema D-06...")
        
        # 1. Criar configuração base
        config_dir = Path("config")
        config_dir.mkdir(exist_ok=True)
        
        base_config_file = config_dir / "base_config.yaml"
        base_config = {
            'risk_management': {
                'max_position_size': 0.10,
                'stop_loss_pct': 0.05,
                'take_profit_pct': 0.15,
                'max_daily_loss': 0.02
            },
            'trading_params': {
                'trend_following_weight': 0.6,
                'mean_reversion_weight': 0.4,
                'momentum_threshold': 0.5,
                'volatility_threshold': 0.02
            },
            'optimization': {
                'lookback_period': 24,
                'rebalance_frequency': 4,
                'min_trade_size': 10.0
            }
        }
        
        with open(base_config_file, 'w') as f:
            yaml.dump(base_config, f, default_flow_style=False)
        
        print(f"📁 Configuração base criada: {base_config_file}")
        
        # 2. Criar hot reloader
        self.hot_reloader = ConfigurationHotReloader(str(base_config_file))
        self.hot_reloader.start()
        print("✅ Hot reloader iniciado")
        
        # 3. Configurar D-06
        d06_config = D06IntegrationConfig(
            regime_detection_enabled=True,
            ab_testing_enabled=True,
            regime_aware_config_enabled=True,
            live_feed_integration=False,  # Para exemplo, usar dados simulados
            monitored_symbols=["BTC/USDT", "ETH/USDT"],
            metrics_collection_interval=30,  # 30 segundos
            detailed_logging=True
        )
        
        # 4. Callback de métricas de performance (simulado)
        def performance_callback():
            """Simula métricas de performance do sistema de trading."""
            import random
            return {
                'pnl_24h': random.uniform(-50, 200),
                'sharpe_ratio': random.uniform(0.5, 3.0),
                'max_drawdown': random.uniform(0.01, 0.15),
                'win_rate': random.uniform(0.45, 0.75),
                'total_trades': random.randint(10, 100),
                'avg_trade_duration': random.uniform(15, 120),
                'volatility': random.uniform(0.08, 0.25)
            }
        
        # 5. Inicializar sistema D-06
        self.integration = await initialize_d06_system(
            hot_reloader=self.hot_reloader,
            feed_manager=None,  # Sem live feed para exemplo
            config=d06_config,
            performance_callbacks=[performance_callback]
        )
        
        print("✅ Sistema D-06 inicializado e iniciado!")
        return True
    
    async def demonstrate_regime_detection(self):
        """Demonstra detecção de regimes de mercado."""
        print("\n📈 === DEMONSTRAÇÃO: DETECÇÃO DE REGIMES ===")
        
        # Simular dados de mercado para diferentes regimes
        market_scenarios = [
            {
                'name': 'Mercado em Alta (Bull)',
                'data': {
                    'symbol': 'BTC/USDT',
                    'price': 52000.0,
                    'volume': 1500.0,
                    'timestamp': datetime.now(timezone.utc)
                }
            },
            {
                'name': 'Mercado Volátil',
                'data': {
                    'symbol': 'ETH/USDT', 
                    'price': 3200.0,
                    'volume': 800.0,
                    'timestamp': datetime.now(timezone.utc)
                }
            }
        ]
        
        for scenario in market_scenarios:
            print(f"\n🎯 Testando: {scenario['name']}")
            
            # Processar dados de mercado
            self.integration.regime_detector.on_ticker_update(scenario['data'])
            
            # Aguardar processamento
            await asyncio.sleep(0.5)
            
            # Verificar regime detectado
            symbol = scenario['data']['symbol']
            if symbol in self.integration.regime_detector.current_regimes:
                detection = self.integration.regime_detector.current_regimes[symbol]
                print(f"   📊 Regime detectado: {detection.regime.value}")
                print(f"   🎯 Confiança: {detection.confidence:.2f}")
                print(f"   📈 Força da tendência: {detection.metrics.trend_strength:.2f}")
                print(f"   📊 Percentil de volatilidade: {detection.metrics.volatility_percentile:.2f}")
            else:
                print("   ⏳ Aguardando mais dados para detecção...")
    
    async def demonstrate_ab_testing(self):
        """Demonstra execução de testes A/B."""
        print("\n🧪 === DEMONSTRAÇÃO: TESTES A/B ===")
        
        # Configurar teste A/B
        test_config = ABTestConfig(
            name="Otimização de Risk Management",
            description="Teste A/B para otimizar parâmetros de gestão de risco",
            config_a={
                'risk_management': {
                    'max_position_size': 0.08,
                    'stop_loss_pct': 0.04
                }
            },
            config_b={
                'risk_management': {
                    'max_position_size': 0.12,
                    'stop_loss_pct': 0.06
                }
            },
            duration_minutes=2,  # Curto para demonstração
            switch_interval_minutes=0.5,
            primary_metric="sharpe_ratio",
            significance_level=0.05,
            min_effect_size=0.2
        )
        
        # Criar e iniciar teste
        print("🚀 Criando teste A/B...")
        test_id = await self.integration.create_ab_test(test_config)
        print(f"✅ Teste criado: {test_id}")
        
        success = await self.integration.start_ab_test(test_id)
        if success:
            print("✅ Teste A/B iniciado!")
            
            # Monitorar progresso
            for i in range(5):
                await asyncio.sleep(1)
                
                # Obter status do teste
                active_tests = await self.integration.ab_framework.list_tests(include_completed=False)
                if active_tests:
                    test = active_tests[0]
                    print(f"   📊 Progresso: {test.get('progress', 0):.1f}% | "
                          f"Configuração atual: {test.get('current_config', 'A')}")
                else:
                    print("   ✅ Teste concluído!")
                    break
        else:
            print("❌ Falha ao iniciar teste A/B")
    
    async def demonstrate_regime_aware_config(self):
        """Demonstra configuração automática baseada em regimes."""
        print("\n⚙️ === DEMONSTRAÇÃO: CONFIGURAÇÃO REGIME-AWARE ===")
        
        # Criar presets personalizados para diferentes regimes
        presets = [
            RegimePreset(
                regime=MarketRegime.BULL,
                name="Bull Market Aggressive",
                description="Configuração agressiva para mercado em alta",
                config={
                    'risk_management': {
                        'max_position_size': 0.15,
                        'stop_loss_pct': 0.08
                    },
                    'trading_params': {
                        'trend_following_weight': 0.8,
                        'mean_reversion_weight': 0.2
                    }
                },
                min_confidence=0.7,
                min_duration=5
            ),
            RegimePreset(
                regime=MarketRegime.BEAR,
                name="Bear Market Conservative", 
                description="Configuração conservadora para mercado em baixa",
                config={
                    'risk_management': {
                        'max_position_size': 0.05,
                        'stop_loss_pct': 0.03
                    },
                    'trading_params': {
                        'trend_following_weight': 0.3,
                        'mean_reversion_weight': 0.7
                    }
                },
                min_confidence=0.6,
                min_duration=10
            )
        ]
        
        # Adicionar presets
        for preset in presets:
            await self.integration.regime_config_manager.add_preset(preset)
            print(f"✅ Preset adicionado: {preset.name} para regime {preset.regime.value}")
        
        # Listar presets disponíveis
        print("\n📋 Presets disponíveis:")
        for regime, regime_presets in self.integration.regime_config_manager.presets.items():
            print(f"   {regime.value}:")
            for preset in regime_presets:
                print(f"     - {preset.name}")
    
    async def demonstrate_system_monitoring(self):
        """Demonstra monitoramento do sistema."""
        print("\n📊 === DEMONSTRAÇÃO: MONITORAMENTO DO SISTEMA ===")
        
        # Obter status completo do sistema
        status = await self.integration.get_system_status()
        
        print(f"🕐 Timestamp: {status['timestamp']}")
        print(f"🔄 Sistema rodando: {status['is_running']}")
        print(f"📈 Símbolos monitorados: {', '.join(status['monitored_symbols'])}")
        
        print("\n🔧 Status dos componentes:")
        for component, info in status['components'].items():
            status_icon = "✅" if info['running'] else "❌"
            print(f"   {status_icon} {component}: {'Rodando' if info['running'] else 'Parado'}")
        
        print("\n📊 Métricas atuais:")
        for metric, value in status['current_metrics'].items():
            if isinstance(value, float):
                print(f"   📈 {metric}: {value:.3f}")
            else:
                print(f"   📈 {metric}: {value}")
    
    async def cleanup(self):
        """Limpa recursos do sistema."""
        print("\n🧹 Limpando sistema...")
        
        if self.integration and self.integration.is_running:
            await self.integration.stop()
            print("✅ Sistema D-06 parado")
        
        if self.hot_reloader:
            self.hot_reloader.stop()
            print("✅ Hot reloader parado")
    
    async def run_complete_demo(self):
        """Executa demonstração completa do sistema D-06."""
        try:
            print("🎉 === DEMONSTRAÇÃO COMPLETA DO SISTEMA D-06 ===\n")
            
            # 1. Configurar sistema
            await self.setup_system()
            
            # 2. Demonstrar funcionalidades
            await self.demonstrate_regime_detection()
            await self.demonstrate_regime_aware_config()
            await self.demonstrate_ab_testing()
            await self.demonstrate_system_monitoring()
            
            print("\n🎉 === DEMONSTRAÇÃO CONCLUÍDA COM SUCESSO! ===")
            
        except Exception as e:
            print(f"\n❌ Erro na demonstração: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await self.cleanup()


async def main():
    """Função principal do exemplo."""
    demo = D06UsageExample()
    await demo.run_complete_demo()


if __name__ == "__main__":
    asyncio.run(main())
