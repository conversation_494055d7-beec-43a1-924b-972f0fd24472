import importlib
from contextlib import nullcontext
from unittest.mock import <PERSON><PERSON>

import yaml

from qualia.config import encoder_registry
from qualia.memory.event_bus import SimpleEventBus


def reload_plugin(tmp_path, flags):
    config_file = tmp_path / "encoders.yaml"
    with open(config_file, "w", encoding="utf-8") as fh:
        yaml.safe_dump(flags, fh)

    import src.ai.encoders_plugin as plugin

    importlib.reload(encoder_registry)
    return importlib.reload(plugin), config_file


def test_load_encoder_flags(monkeypatch, tmp_path):
    plugin, config_file = reload_plugin(
        tmp_path, {"RSIPhaseEncoder": {"enabled": False}}
    )
    monkeypatch.setenv("QUALIA_ENCODERS_CONFIG", str(config_file))
    plugin = importlib.reload(plugin)

    encoders = encoder_registry.get_registered_encoders()
    assert "RSIPhaseEncoder" not in encoders


def test_metrics_and_event_emission(monkeypatch, tmp_path):
    plugin, config_file = reload_plugin(
        tmp_path,
        {
            "RSIPhaseEncoder": {"enabled": True, "emit_events": True},
            "VolumeRatioAmplitudeEncoder": {"enabled": False},
        },
    )
    monkeypatch.setenv("QUALIA_ENCODERS_CONFIG", str(config_file))
    plugin = importlib.reload(plugin)

    bus = SimpleEventBus()
    captured = {}
    bus.subscribe("encoder.created", lambda p: captured.setdefault("data", p))

    statsd = Mock()
    statsd.timer.return_value = nullcontext()

    enc = encoder_registry.create_encoder(
        "RSIPhaseEncoder", name="rsi", statsd_client=statsd, event_bus=bus
    )
    assert isinstance(enc, plugin.RSIPhaseEncoder)
    assert statsd.timer.called
    assert statsd.increment.called
    assert statsd.increment.call_args.kwargs["tags"] == ["class:RSIPhaseEncoder"]
    assert captured["data"]["class"] == "RSIPhaseEncoder"


def test_processed_event(monkeypatch, tmp_path):
    plugin, config_file = reload_plugin(
        tmp_path,
        {"RSIPhaseEncoder": {"enabled": True, "emit_events": True}},
    )
    monkeypatch.setenv("QUALIA_ENCODERS_CONFIG", str(config_file))
    plugin = importlib.reload(plugin)

    bus = SimpleEventBus()
    events = []
    bus.subscribe("encoder.processed", lambda p: events.append(p))

    enc = encoder_registry.create_encoder("RSIPhaseEncoder", name="rsi", event_bus=bus)
    enc.encode({"rsi": 50})

    assert events and events[0]["class"] == "RSIPhaseEncoder"
