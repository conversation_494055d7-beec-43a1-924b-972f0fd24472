"""Rotinas de otimização de circuitos quânticos."""

from __future__ import annotations

from qiskit import QuantumCircuit

try:  # pragma: no cover - fallback for test stubs
    from qiskit.compiler import transpile
    from qiskit.transpiler import PassManager
    from qiskit.transpiler.passes import (
        CXCancellation,
        CommutativeCancellation,
        RemoveResetInZeroState,
    )
except Exception:  # pragma: no cover - minimal stub environment
    transpile = None
    PassManager = None


def optimize_circuit(circuit: QuantumCircuit) -> QuantumCircuit:
    """Otimiza um circuito quântico.

    Parameters
    ----------
    circuit:
        Circuito a ser otimizado.

    Returns
    -------
    QuantumCircuit
        Novo circuito com menor profundidade e menos operações.
    """
    if transpile is None or PassManager is None:
        optimized = circuit.copy()
        new_data = []
        last = None
        for inst in optimized.data:
            op_name = (
                inst.operation.name if hasattr(inst, "operation") else inst[0].name
            )
            qargs = inst.qargs if hasattr(inst, "qargs") else inst[1]
            if (
                last is not None
                and (
                    last.operation.name if hasattr(last, "operation") else last[0].name
                )
                == op_name
                and qargs == (last.qargs if hasattr(last, "qargs") else last[1])
                and op_name in {"h", "cx"}
            ):
                new_data.pop()
                last = None
                continue
            new_data.append(inst)
            last = inst
        optimized.data = new_data
        return optimized

    transpiled = transpile(circuit, optimization_level=3)
    pm = PassManager(
        [RemoveResetInZeroState(), CXCancellation(), CommutativeCancellation()]
    )
    optimized = pm.run(transpiled)
    return optimized
