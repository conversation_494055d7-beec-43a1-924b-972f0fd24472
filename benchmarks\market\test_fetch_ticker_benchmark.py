import asyncio
from unittest.mock import AsyncMock

import pytest

pytest.importorskip("pytest_benchmark")

from qualia.market.base_integration import CryptoDataFetcher
from qualia.market.symbol_utils import normalize_symbol_async


@pytest.mark.benchmark(group="market")
def test_fetch_ticker_benchmark(benchmark, monkeypatch):
    monkeypatch.setattr(
        "src.qualia.market.symbol_utils.normalize_symbol_async",
        AsyncMock(return_value="BTC/USD"),
    )
    monkeypatch.setattr("ccxt.async_support.kraken", lambda *a, **k: object())

    fetcher = CryptoDataFetcher(ticker_timeout=0.1, ticker_retries=1)
    fetcher.exchange = AsyncMock()
    fetcher.exchange.has = {"fetchTicker": True}
    fetcher.exchange.markets = {"BTC/USD": {"id": "BTC/USD"}}
    fetcher.exchange.fetch_ticker = AsyncMock(return_value={"last": 1.0})
    fetcher._respect_rate_limit = AsyncMock()

    async def run_once():
        await fetcher.fetch_ticker("BTC/USD")

    benchmark(lambda: asyncio.get_event_loop().run_until_complete(run_once()))
