"""Temporal pattern detection for QUALIA trading system."""

from ..utils.logger import get_logger
import asyncio
import numpy as np
from typing import Dict, Any, List
from dataclasses import dataclass
import time
import json

from ..geometry.sacred_geometry import generate_sri_yantra
from ..metrics.topology import compute_topological_coherence

logger = get_logger(__name__)


@dataclass
class TemporalPattern:
    """Container for a detected temporal pattern.

    Parameters
    ----------
    pattern_type : str
        Nome do padrão identificado.
    strength : float
        Intensidade relativa entre ``0`` e ``1``.
    duration : float
        Duração do padrão em unidades de tempo.
    confidence : float
        Grau de confiança de ``0`` a ``1``.
    metadata : dict
        Metadados adicionais produzidos pela análise.
    """

    pattern_type: str
    strength: float
    duration: float
    confidence: float
    metadata: Dict[str, Any]


class QuantumTemporalPatternDetector:
    """Detector avançado de padrões temporais com análise harmônica quântica.

    Notes
    -----
    YAA REFINEMENT: utiliza transformadas wavelet quânticas adaptativas,
    análise espectral de alta sensibilidade, detecção de padrões fractais
    emergentes e correlações não‑locais temporais.
    """

    def __init__(
        self,
        wavelet_depth: int = 4,
        quantum_shots: int = 1024,
        sensitivity_threshold: float = 0.01,
        geometry_guided: bool = False,
    ):
        """Inicializa o detector.

        Parameters
        ----------
        wavelet_depth : int, optional
            Profundidade máxima da análise wavelet, by default ``4``.
        quantum_shots : int, optional
            Número de execuções simuladas das transformações quânticas, by default ``1024``.
        sensitivity_threshold : float, optional
            Limiar mínimo de sensibilidade para reportar padrões, by default ``0.01``.
        geometry_guided : bool, optional
            Ativa o uso de padrões geométricos para detectar fractais, by default ``False``.
        """

        self.wavelet_depth = wavelet_depth
        self.quantum_shots = quantum_shots
        self.sensitivity_threshold = sensitivity_threshold
        self.geometry_guided = geometry_guided

        self.logger = logger

        # YAA REFINEMENT: Sistema de calibração adaptativa
        self.adaptive_calibration = {
            "phase_sensitivity": 1.0,
            "frequency_resolution": 1.0,
            "amplitude_threshold": 0.05,
            "coherence_decay_rate": 0.95,
            "resonance_detection_threshold": 0.15,
        }

        # Cache para padrões detectados
        self.pattern_cache = {}
        self.harmonic_memory = []
        self.resonance_history = []

        # Configuração de sensibilidade por regime
        self.regime_sensitivities = {
            "high_volatility": {
                "phase_multiplier": 0.8,
                "frequency_multiplier": 1.3,
                "amplitude_threshold": 0.08,
            },
            "low_volatility": {
                "phase_multiplier": 1.4,
                "frequency_multiplier": 0.9,
                "amplitude_threshold": 0.02,
            },
            "normal": {
                "phase_multiplier": 1.0,
                "frequency_multiplier": 1.0,
                "amplitude_threshold": 0.05,
            },
        }

    def detect_quantum_patterns(
        self, time_series: np.ndarray, context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Executa a detecção de padrões temporais com análise quântica.

        Parameters
        ----------
        time_series : ndarray
            Série temporal a ser analisada.
        context : dict, optional
            Informações adicionais sobre o regime de mercado.

        Returns
        -------
        dict
            Estrutura com padrões identificados, assinaturas quânticas e níveis
            de confiança.
        """
        if context is None or not isinstance(context, dict):
            context = {}

        try:
            # Determinar regime e ajustar sensibilidades
            regime = self._determine_market_regime(time_series, context)
            adaptive_config = self._adapt_configuration(regime)

            results = {
                "regime": regime,
                "adaptive_config": adaptive_config,
                "patterns": {},
                "quantum_signatures": {},
                "confidence_scores": {},
                "emergent_phenomena": [],
            }

            # YAA REFINEMENT: Análise multi-modal

            # 1. Análise espectral quântica adaptativa
            spectral_patterns = self._quantum_spectral_analysis(
                time_series, adaptive_config
            )
            results["patterns"]["spectral"] = spectral_patterns

            # 2. Detecção de harmônicos com ressonância quântica
            harmonic_patterns = self._detect_quantum_harmonics(
                time_series, adaptive_config
            )
            results["patterns"]["harmonic"] = harmonic_patterns

            # 3. Análise de correlações não-locais
            nonlocal_correlations = self._analyze_nonlocal_correlations(
                time_series, adaptive_config
            )
            results["patterns"]["nonlocal"] = nonlocal_correlations

            # 4. Detecção de padrões fractais emergentes
            fractal_patterns = self._detect_emergent_fractals(
                time_series, adaptive_config
            )
            results["patterns"]["fractal"] = fractal_patterns

            if self.geometry_guided:
                geom_patterns = self.detect_geometry_guided_fractals(time_series)
                results["patterns"]["geometry_fractal"] = geom_patterns

            # 5. Análise de coerência temporal
            coherence_analysis = self._temporal_coherence_analysis(
                time_series, adaptive_config
            )
            results["quantum_signatures"]["coherence"] = coherence_analysis

            # 6. Detecção de transições de fase
            phase_transitions = self._detect_quantum_phase_transitions(
                time_series, adaptive_config
            )
            results["quantum_signatures"]["phase_transitions"] = phase_transitions

            # YAA REFINEMENT: Síntese final com detecção de emergência
            results["emergent_phenomena"] = self._synthesize_emergent_patterns(
                results["patterns"], results["quantum_signatures"], adaptive_config
            )

            # Calcular scores de confiança adaptativos
            results["confidence_scores"] = self._calculate_adaptive_confidence(
                results, adaptive_config
            )

            # Atualizar memória de padrões
            self._update_pattern_memory(results)

            return results

        except Exception as e:
            logger.error(f"Erro na detecção de padrões quânticos: {e}")
            return {
                "regime": "unknown",
                "patterns": {},
                "quantum_signatures": {},
                "confidence_scores": {"overall": 0.0},
                "emergent_phenomena": [],
                "error": str(e),
            }

    def _determine_market_regime(
        self, time_series: np.ndarray, context: Dict[str, Any]
    ) -> str:
        """Determina o regime de mercado a partir da volatilidade quântica.

        Parameters
        ----------
        time_series : ndarray
            Série temporal utilizada na avaliação.
        context : dict
            Informações externas, como volatilidade observada.

        Returns
        -------
        str
            Um dos valores ``"high_volatility"``, ``"low_volatility"`` ou
            ``"normal"``.
        """
        try:
            # Calcular métricas de volatilidade quântica
            quantum_volatility = self._calculate_quantum_volatility(time_series)

            # Analisar contexto se disponível
            context_volatility = context.get("volatility", quantum_volatility)

            # Determinar regime baseado em thresholds adaptativos
            if context_volatility > 0.7 or quantum_volatility > 0.8:
                return "high_volatility"
            elif context_volatility < 0.3 or quantum_volatility < 0.2:
                return "low_volatility"
            else:
                return "normal"

        except Exception as e:
            self.logger.warning("_determine_market_regime falhou: %s", e)
            return "normal"

    def _calculate_quantum_volatility(self, time_series: np.ndarray) -> float:
        """Calcula volatilidade combinando medidas clássicas e quânticas.

        Parameters
        ----------
        time_series : ndarray
            Série temporal de preços ou indicadores.

        Returns
        -------
        float
            Volatilidade normalizada entre ``0`` e ``1``.
        """
        try:
            if len(time_series) < 2:
                return 0.5

            # Volatilidade clássica
            returns = np.diff(time_series)
            classical_vol = np.std(returns) if len(returns) > 0 else 0.0

            # YAA REFINEMENT: Volatilidade quântica baseada em entropia
            # Normalizar série temporal para amplitude [0,1]
            normalized = (time_series - np.min(time_series)) / (
                np.max(time_series) - np.min(time_series) + 1e-10
            )

            # Calcular entropia de Shannon como proxy para volatilidade quântica
            hist, _ = np.histogram(
                normalized, bins=min(20, len(normalized) // 2), density=True
            )
            hist = hist[hist > 0]  # Remove zeros
            quantum_entropy = -np.sum(hist * np.log2(hist)) if len(hist) > 0 else 0.0

            # Combinar volatilidades clássica e quântica
            combined_volatility = 0.6 * classical_vol + 0.4 * (quantum_entropy / 5.0)

            return float(np.clip(combined_volatility, 0.0, 1.0))

        except Exception as e:
            self.logger.warning("_calculate_quantum_volatility falhou: %s", e)
            return 0.5

    def _adapt_configuration(self, regime: str) -> Dict[str, Any]:
        """Ajusta a configuração conforme o regime de mercado.

        Parameters
        ----------
        regime : str
            Regime identificado (por exemplo, ``"high_volatility"``).

        Returns
        -------
        dict
            Configuração adaptada para o regime informado.
        """
        base_config = self.adaptive_calibration.copy()
        regime_adjustments = self.regime_sensitivities.get(
            regime, self.regime_sensitivities["normal"]
        )

        adapted_config = {}
        for key, value in base_config.items():
            if key == "phase_sensitivity":
                adapted_config[key] = value * regime_adjustments["phase_multiplier"]
            elif key == "frequency_resolution":
                adapted_config[key] = value * regime_adjustments["frequency_multiplier"]
            elif key == "amplitude_threshold":
                adapted_config[key] = regime_adjustments["amplitude_threshold"]
            else:
                adapted_config[key] = value

        return adapted_config

    def _quantum_spectral_analysis(
        self, time_series: np.ndarray, config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Realiza análise espectral por transformadas quânticas.

        Parameters
        ----------
        time_series : ndarray
            Série temporal de entrada.
        config : dict
            Configurações adaptadas para o regime atual.

        Returns
        -------
        dict
            Informações sobre frequências dominantes e coerência espectral.
        """
        try:
            # YAA REFINEMENT: Transformada de Fourier quântica simulada
            fft_data = np.fft.fft(time_series)
            frequencies = np.fft.fftfreq(len(time_series))
            magnitudes = np.abs(fft_data)
            phases = np.angle(fft_data)

            # Filtrar componentes significativos
            threshold = config["amplitude_threshold"] * np.max(magnitudes)
            significant_indices = magnitudes > threshold

            significant_freqs = frequencies[significant_indices]
            significant_mags = magnitudes[significant_indices]
            significant_phases = phases[significant_indices]

            # YAA REFINEMENT: Análise de coerência espectral
            spectral_coherence = self._calculate_spectral_coherence(
                significant_freqs, significant_mags, significant_phases
            )

            return {
                "dominant_frequencies": significant_freqs.tolist(),
                "magnitudes": significant_mags.tolist(),
                "phases": significant_phases.tolist(),
                "spectral_coherence": spectral_coherence,
                "frequency_resolution": config["frequency_resolution"],
                "total_components": len(significant_freqs),
            }

        except Exception as e:
            logger.error(f"Erro na análise espectral quântica: {e}")
            return {"error": str(e)}

    def _calculate_spectral_coherence(
        self, frequencies: np.ndarray, magnitudes: np.ndarray, phases: np.ndarray
    ) -> float:
        """Calcula a coerência espectral dos componentes analisados.

        Parameters
        ----------
        frequencies : ndarray
            Frequências selecionadas.
        magnitudes : ndarray
            Magnitudes associadas às frequências.
        phases : ndarray
            Fases relativas dos componentes.

        Returns
        -------
        float
            Grau de coerência espectral entre ``0`` e ``1``.
        """
        try:
            if len(frequencies) < 2:
                return 0.0

            # YAA REFINEMENT: Medida de coerência baseada na distribuição espectral
            # Calcular entropia espectral
            normalized_mags = magnitudes / (np.sum(magnitudes) + 1e-10)
            spectral_entropy = -np.sum(
                normalized_mags * np.log2(normalized_mags + 1e-10)
            )

            # Calcular coerência de fase
            phase_coherence = np.abs(np.mean(np.exp(1j * phases)))

            # Combinar medidas
            combined_coherence = (
                0.6 * (1.0 - spectral_entropy / np.log2(len(frequencies)))
                + 0.4 * phase_coherence
            )

            return float(np.clip(combined_coherence, 0.0, 1.0))

        except Exception as e:
            self.logger.warning("_calculate_spectral_coherence falhou: %s", e)
            return 0.0

    def _detect_quantum_harmonics(
        self, time_series: np.ndarray, config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Detecta harmônicos utilizando análise de ressonância quântica.

        Parameters
        ----------
        time_series : ndarray
            Série temporal alvo da análise.
        config : dict
            Configurações para ajuste de sensibilidade.

        Returns
        -------
        dict
            Dados sobre séries harmônicas e força de ressonância.
        """
        try:
            # YAA REFINEMENT: Detecção de harmônicos com sensibilidade adaptativa
            fft_data = np.fft.fft(time_series)
            frequencies = np.fft.fftfreq(len(time_series))
            magnitudes = np.abs(fft_data)

            # Encontrar picos espectrais
            from scipy.signal import find_peaks

            peaks, properties = find_peaks(
                magnitudes,
                height=config["amplitude_threshold"] * np.max(magnitudes),
                distance=max(1, len(magnitudes) // 20),
            )

            if len(peaks) == 0:
                return {"harmonic_series": [], "resonance_strength": 0.0}

            peak_frequencies = frequencies[peaks]
            peak_magnitudes = magnitudes[peaks]

            # YAA REFINEMENT: Análise de séries harmônicas
            harmonic_series = self._analyze_harmonic_series(
                peak_frequencies, peak_magnitudes
            )

            # Calcular força de ressonância
            resonance_strength = self._calculate_resonance_strength(
                peak_frequencies, peak_magnitudes, config
            )

            return {
                "harmonic_series": harmonic_series,
                "resonance_strength": resonance_strength,
                "peak_frequencies": peak_frequencies.tolist(),
                "peak_magnitudes": peak_magnitudes.tolist(),
                "harmonic_count": len(harmonic_series),
            }

        except Exception as e:
            logger.error(f"Erro na detecção de harmônicos quânticos: {e}")
            return {"error": str(e)}

    def _analyze_harmonic_series(
        self, frequencies: np.ndarray, magnitudes: np.ndarray
    ) -> List[Dict[str, Any]]:
        """Analisa relações harmônicas presentes nos picos espectrais.

        Parameters
        ----------
        frequencies : ndarray
            Frequências dos picos identificados.
        magnitudes : ndarray
            Intensidades correspondentes.

        Returns
        -------
        list of dict
            Série de harmônicos encontrada para cada fundamental.
        """
        harmonic_series = []

        try:
            if len(frequencies) < 2:
                return harmonic_series

            # Ordenar por magnitude (mais forte primeiro)
            sorted_indices = np.argsort(magnitudes)[::-1]
            sorted_freqs = frequencies[sorted_indices]
            sorted_mags = magnitudes[sorted_indices]

            # YAA REFINEMENT: Detectar relações harmônicas
            for i, fundamental_freq in enumerate(
                sorted_freqs[:3]
            ):  # Top 3 fundamentais
                if np.abs(fundamental_freq) < 1e-6:  # Skip DC component
                    continue

                harmonics = []
                fundamental_mag = sorted_mags[i]

                # Buscar harmônicos (2f, 3f, 4f, etc.)
                for harmonic_order in range(2, 6):  # até 5º harmônico
                    target_freq = fundamental_freq * harmonic_order

                    # Encontrar frequência mais próxima
                    closest_idx = np.argmin(np.abs(sorted_freqs - target_freq))
                    closest_freq = sorted_freqs[closest_idx]

                    # Verificar se é realmente um harmônico (tolerância de 10%)
                    if np.abs(closest_freq - target_freq) / np.abs(target_freq) < 0.1:
                        harmonics.append(
                            {
                                "order": harmonic_order,
                                "frequency": float(closest_freq),
                                "magnitude": float(sorted_mags[closest_idx]),
                                "amplitude_ratio": float(
                                    sorted_mags[closest_idx] / fundamental_mag
                                ),
                            }
                        )

                if len(harmonics) >= 1:  # Pelo menos 1 harmônico encontrado
                    harmonic_series.append(
                        {
                            "fundamental_frequency": float(fundamental_freq),
                            "fundamental_magnitude": float(fundamental_mag),
                            "harmonics": harmonics,
                            "harmonic_strength": float(
                                np.sum([h["magnitude"] for h in harmonics])
                            ),
                        }
                    )

            return harmonic_series

        except Exception as e:
            self.logger.warning("_analyze_harmonic_series falhou: %s", e)
            return []

    def _calculate_resonance_strength(
        self, frequencies: np.ndarray, magnitudes: np.ndarray, config: Dict[str, Any]
    ) -> float:
        """Calcula a força de ressonância das componentes espectrais.

        Parameters
        ----------
        frequencies : ndarray
            Frequências dos picos dominantes.
        magnitudes : ndarray
            Magnitudes correspondentes.
        config : dict
            Configurações utilizadas para ajustar a sensibilidade.

        Returns
        -------
        float
            Valor normalizado entre ``0`` e ``1`` representando a ressonância.
        """
        try:
            if len(frequencies) == 0:
                return 0.0

            # YAA REFINEMENT: Medida de ressonância baseada em coerência espectral
            # Normalizar magnitudes
            normalized_mags = magnitudes / (np.sum(magnitudes) + 1e-10)

            # Calcular concentração espectral (anti-entropia)
            spectral_entropy = -np.sum(
                normalized_mags * np.log2(normalized_mags + 1e-10)
            )
            max_entropy = np.log2(len(frequencies))
            concentration = (
                1.0 - (spectral_entropy / max_entropy) if max_entropy > 0 else 0.0
            )

            # Calcular força relativa dos picos dominantes
            top_3_strength = np.sum(np.sort(magnitudes)[-3:]) / (
                np.sum(magnitudes) + 1e-10
            )

            # Combinar medidas
            resonance_strength = 0.6 * concentration + 0.4 * top_3_strength

            # Aplicar sensibilidade do regime
            final_strength = resonance_strength * config.get("phase_sensitivity", 1.0)

            return float(np.clip(final_strength, 0.0, 1.0))

        except Exception as e:
            self.logger.warning("_calculate_resonance_strength falhou: %s", e)
            return 0.0

    def _analyze_nonlocal_correlations(
        self, time_series: np.ndarray, config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analisa correlações temporais de longo alcance.

        Parameters
        ----------
        time_series : ndarray
            Série temporal analisada.
        config : dict
            Configuração adaptada para sensibilidade do regime.

        Returns
        -------
        dict
            Lista de correlações por ``lag`` e força de não-localidade.
        """
        try:
            # YAA REFINEMENT: Análise de correlações de longo alcance
            if len(time_series) < 10:
                return {"correlations": [], "nonlocality_strength": 0.0}

            correlations = []
            max_lag = min(len(time_series) // 4, 20)  # Limite razoável para lags

            # Calcular autocorrelações para diferentes lags
            for lag in range(1, max_lag + 1):
                if len(time_series) > lag:
                    correlation = np.corrcoef(time_series[:-lag], time_series[lag:])[
                        0, 1
                    ]

                    if not np.isnan(correlation):
                        correlations.append(
                            {
                                "lag": lag,
                                "correlation": float(correlation),
                                "abs_correlation": float(abs(correlation)),
                            }
                        )

            # Calcular força de não-localidade
            if correlations:
                # Força baseada em correlações de longo alcance (lags > 5)
                long_range_corrs = [
                    c["abs_correlation"] for c in correlations if c["lag"] > 5
                ]
                nonlocality_strength = (
                    np.mean(long_range_corrs) if long_range_corrs else 0.0
                )
            else:
                nonlocality_strength = 0.0

            return {
                "correlations": correlations,
                "nonlocality_strength": float(nonlocality_strength),
                "max_lag_analyzed": max_lag,
                "significant_correlations": len(
                    [c for c in correlations if c["abs_correlation"] > 0.3]
                ),
            }

        except Exception as e:
            logger.error(f"Erro na análise de correlações não-locais: {e}")
            return {"error": str(e)}

    def _detect_emergent_fractals(
        self, time_series: np.ndarray, config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Detecta padrões fractais em múltiplas escalas.

        Parameters
        ----------
        time_series : ndarray
            Série temporal sob análise.
        config : dict
            Configurações de sensibilidade atuais.

        Returns
        -------
        dict
            Informações sobre dimensão fractal e auto-similaridade.
        """
        try:
            # YAA REFINEMENT: Análise fractal multi-escala
            if len(time_series) < 8:
                return {"fractal_dimension": 1.0, "self_similarity": 0.0}

            # Calcular dimensão fractal usando método de contagem de caixas
            fractal_dimension = self._calculate_fractal_dimension_boxcount(time_series)

            # Medir auto-similaridade
            self_similarity = self._measure_self_similarity(time_series)

            # Detectar escalas características
            characteristic_scales = self._detect_characteristic_scales(time_series)

            return {
                "fractal_dimension": float(fractal_dimension),
                "self_similarity": float(self_similarity),
                "characteristic_scales": characteristic_scales,
                "fractal_strength": float(max(0.0, fractal_dimension - 1.0)),
            }

        except Exception as e:
            logger.error(f"Erro na detecção de fractais emergentes: {e}")
            return {"error": str(e)}

    def _calculate_fractal_dimension_boxcount(self, time_series: np.ndarray) -> float:
        """Calcula a dimensão fractal por contagem de caixas.

        Parameters
        ----------
        time_series : ndarray
            Série temporal normalizada.

        Returns
        -------
        float
            Dimensão fractal estimada para a série.
        """
        try:
            n = len(time_series)
            if n < 4:
                return 1.0

            # Normalizar série
            normalized = (time_series - np.min(time_series)) / (
                np.max(time_series) - np.min(time_series) + 1e-10
            )

            # Escalas para análise
            scales = np.logspace(0, np.log10(n // 4), 8)
            counts = []

            for scale in scales:
                box_size = max(1, int(scale))
                count = 0

                # Dividir em caixas
                for i in range(0, n, box_size):
                    box_data = normalized[i : i + box_size]
                    if len(box_data) > 1:
                        # Verificar se há variação na caixa
                        if np.max(box_data) - np.min(box_data) > 1e-6:
                            count += 1

                counts.append(max(1, count))  # Evitar log(0)

            # Regressão linear para encontrar dimensão
            if len(counts) > 2:
                log_scales = np.log(scales)
                log_counts = np.log(counts)
                slope = np.polyfit(log_scales, log_counts, 1)[0]
                dimension = 1.0 - slope  # Dimensão fractal
                return float(max(1.0, min(2.0, dimension)))

            return 1.0

        except Exception as e:
            self.logger.warning("_calculate_fractal_dimension_boxcount falhou: %s", e)
            return 1.0

    def _measure_self_similarity(self, time_series: np.ndarray) -> float:
        """Mede a auto-similaridade da série temporal.

        Parameters
        ----------
        time_series : ndarray
            Série temporal de entrada.

        Returns
        -------
        float
            Valor médio de correlação entre escalas.
        """
        try:
            if len(time_series) < 6:
                return 0.0

            # YAA REFINEMENT: Auto-similaridade baseada em correlação multi-escala
            n = len(time_series)
            similarities = []

            # Testar diferentes escalas de sub-amostragem
            for scale in [2, 3, 4]:
                if n // scale >= 3:
                    # Sub-amostrar série
                    subsampled = time_series[::scale]

                    # Redimensionar para comparação
                    if len(subsampled) > 2:
                        # Interpolar de volta ao tamanho original
                        from scipy.interpolate import interp1d

                        x_old = np.linspace(0, 1, len(subsampled))
                        x_new = np.linspace(0, 1, n)
                        f = interp1d(x_old, subsampled, kind="linear")
                        interpolated = f(x_new)

                        # Calcular correlação
                        correlation = np.corrcoef(time_series, interpolated)[0, 1]
                        if not np.isnan(correlation):
                            similarities.append(abs(correlation))

            # Retornar média das similaridades
            return float(np.mean(similarities)) if similarities else 0.0

        except Exception as e:
            self.logger.warning("_measure_self_similarity falhou: %s", e)
            return 0.0

    def detect_geometry_guided_fractals(
        self, time_series: np.ndarray, levels: int = 4
    ) -> Dict[str, Any]:
        """Analisa fractais guiados por geometria sagrada.

        Parameters
        ----------
        time_series : ndarray
            Série temporal alvo da análise.
        levels : int, optional
            Número de camadas do padrão ``Sri Yantra`` utilizado, by default ``4``.

        Returns
        -------
        dict
            Métricas fractais combinadas à coerência topológica do guia geométrico.
        """
        template = generate_sri_yantra(levels)
        coherence = compute_topological_coherence(template)

        fractal_data = self._detect_emergent_fractals(
            time_series, self.adaptive_calibration
        )
        fractal_data["geometry_coherence"] = float(coherence)
        fractal_data["template_levels"] = levels
        return fractal_data

    def _detect_characteristic_scales(
        self, time_series: np.ndarray
    ) -> List[Dict[str, Any]]:
        """Identifica escalas características presentes na série.

        Parameters
        ----------
        time_series : ndarray
            Série temporal analisada.

        Returns
        -------
        list of dict
            Escalas com maior concentração de energia.
        """
        scales = []

        try:
            # YAA REFINEMENT: Detecção de escalas via análise wavelet
            from scipy import signal

            # Usar wavelets para encontrar escalas características
            widths = np.arange(1, min(20, len(time_series) // 2))
            if len(widths) > 0:
                cwt_matrix = signal.cwt(time_series, signal.ricker, widths)

                # Encontrar escalas com maior energia
                energy_per_scale = np.sum(np.abs(cwt_matrix) ** 2, axis=1)

                # Encontrar picos de energia
                if len(energy_per_scale) > 2:
                    peaks, _ = signal.find_peaks(
                        energy_per_scale, height=np.mean(energy_per_scale)
                    )

                    for peak in peaks:
                        if peak < len(widths):
                            scales.append(
                                {
                                    "scale": int(widths[peak]),
                                    "energy": float(energy_per_scale[peak]),
                                    "relative_energy": float(
                                        energy_per_scale[peak]
                                        / np.max(energy_per_scale)
                                    ),
                                }
                            )

            return scales

        except Exception as e:
            self.logger.warning("_detect_characteristic_scales falhou: %s", e)
            return []

    def _temporal_coherence_analysis(
        self, time_series: np.ndarray, config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analisa a coerência temporal do sinal.

        Parameters
        ----------
        time_series : ndarray
            Série temporal para análise.
        config : dict
            Configurações adaptadas ao regime.

        Returns
        -------
        dict
            Métricas de coerência e comprimento de coerência.
        """
        try:
            if len(time_series) < 4:
                return {"temporal_coherence": 0.0, "coherence_length": 1}

            # YAA REFINEMENT: Coerência temporal baseada em estabilidade local
            window_size = max(3, len(time_series) // 10)
            coherences = []

            # Calcular coerência em janelas deslizantes
            for i in range(len(time_series) - window_size + 1):
                window = time_series[i : i + window_size]

                # Medir estabilidade da janela (inverso da variância normalizada)
                if len(window) > 1:
                    variance = np.var(window)
                    mean_val = np.mean(np.abs(window))
                    normalized_variance = variance / (mean_val**2 + 1e-10)
                    coherence = 1.0 / (1.0 + normalized_variance)
                    coherences.append(coherence)

            if not coherences:
                return {"temporal_coherence": 0.0, "coherence_length": 1}

            # Calcular métricas de coerência
            mean_coherence = np.mean(coherences)
            coherence_stability = 1.0 - np.std(coherences)  # Estabilidade da coerência

            # Encontrar comprimento de coerência (quantos pontos mantêm coerência alta)
            high_coherence_threshold = 0.7
            coherence_length = 0
            for c in coherences:
                if c > high_coherence_threshold:
                    coherence_length += 1
                else:
                    break

            return {
                "temporal_coherence": float(mean_coherence),
                "coherence_stability": float(max(0.0, coherence_stability)),
                "coherence_length": int(coherence_length),
                "max_coherence": float(max(coherences)),
                "min_coherence": float(min(coherences)),
            }

        except Exception as e:
            logger.error(f"Erro na análise de coerência temporal: {e}")
            return {"error": str(e)}

    def _detect_quantum_phase_transitions(
        self, time_series: np.ndarray, config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Detecta transições de fase na série temporal.

        Parameters
        ----------
        time_series : ndarray
            Série de dados a ser analisada.
        config : dict
            Configuração atual para análise.

        Returns
        -------
        dict
            Lista de transições detectadas e força média.
        """
        try:
            if len(time_series) < 6:
                return {"phase_transitions": [], "transition_strength": 0.0}

            transitions = []

            # YAA REFINEMENT: Detecção de transições via mudanças de regime
            # Calcular derivada segunda para encontrar pontos de inflexão
            second_derivative = np.diff(time_series, 2)

            # Encontrar mudanças abruptas na curvatura
            if len(second_derivative) > 2:
                threshold = np.std(second_derivative) * 2
                transition_points = np.where(np.abs(second_derivative) > threshold)[0]

                for point in transition_points:
                    # Analisar contexto da transição
                    start_idx = max(0, point - 2)
                    end_idx = min(len(time_series), point + 4)

                    before_segment = time_series[start_idx : point + 2]
                    after_segment = time_series[point + 2 : end_idx]

                    if len(before_segment) > 1 and len(after_segment) > 1:
                        # Calcular mudança de comportamento
                        before_trend = np.polyfit(
                            range(len(before_segment)), before_segment, 1
                        )[0]
                        after_trend = np.polyfit(
                            range(len(after_segment)), after_segment, 1
                        )[0]

                        trend_change = abs(after_trend - before_trend)

                        transitions.append(
                            {
                                "position": int(
                                    point + 2
                                ),  # Ajustar para índice original
                                "strength": float(abs(second_derivative[point])),
                                "trend_change": float(trend_change),
                                "transition_type": "curvature_change",
                            }
                        )

            # Calcular força geral das transições
            if transitions:
                transition_strength = np.mean([t["strength"] for t in transitions])
            else:
                transition_strength = 0.0

            return {
                "phase_transitions": transitions,
                "transition_strength": float(transition_strength),
                "transition_count": len(transitions),
            }

        except Exception as e:
            logger.error(f"Erro na detecção de transições de fase: {e}")
            return {"error": str(e)}

    def _synthesize_emergent_patterns(
        self,
        patterns: Dict[str, Any],
        signatures: Dict[str, Any],
        config: Dict[str, Any],
    ) -> List[Dict[str, Any]]:
        """Combina resultados para identificar fenômenos emergentes.

        Parameters
        ----------
        patterns : dict
            Padrões identificados nas análises parciais.
        signatures : dict
            Assinaturas quânticas complementares.
        config : dict
            Configurações aplicadas no ciclo atual.

        Returns
        -------
        list of dict
            Lista de padrões emergentes detectados.
        """
        emergent_patterns = []

        try:
            # YAA REFINEMENT: Detecção de emergência baseada em correlações entre métricas

            # 1. Emergência Harmônica-Fractal
            harmonic_data = patterns.get("harmonic", {})
            fractal_data = patterns.get("fractal", {})

            if (
                harmonic_data.get("resonance_strength", 0) > 0.5
                and fractal_data.get("fractal_strength", 0) > 0.3
            ):
                emergent_patterns.append(
                    {
                        "type": "harmonic_fractal_resonance",
                        "strength": float(
                            (
                                harmonic_data["resonance_strength"]
                                + fractal_data["fractal_strength"]
                            )
                            / 2
                        ),
                        "description": "Resonância harmônica com estrutura fractal emergente",
                        "components": ["harmonic", "fractal"],
                    }
                )

            # 2. Emergência Não-Local-Coerente
            nonlocal_data = patterns.get("nonlocal", {})
            coherence_data = signatures.get("coherence", {})

            if (
                nonlocal_data.get("nonlocality_strength", 0) > 0.4
                and coherence_data.get("temporal_coherence", 0) > 0.6
            ):
                emergent_patterns.append(
                    {
                        "type": "nonlocal_coherent_pattern",
                        "strength": float(
                            (
                                nonlocal_data["nonlocality_strength"]
                                + coherence_data["temporal_coherence"]
                            )
                            / 2
                        ),
                        "description": "Padrão coerente com correlações não-locais fortes",
                        "components": ["nonlocal", "coherence"],
                    }
                )

            # 3. Emergência de Transição Espectral
            spectral_data = patterns.get("spectral", {})
            phase_data = signatures.get("phase_transitions", {})

            if (
                spectral_data.get("spectral_coherence", 0) > 0.7
                and phase_data.get("transition_strength", 0) > 0.3
            ):
                emergent_patterns.append(
                    {
                        "type": "spectral_phase_transition",
                        "strength": float(
                            (
                                spectral_data["spectral_coherence"]
                                + phase_data["transition_strength"]
                            )
                            / 2
                        ),
                        "description": "Transição de fase com reorganização espectral",
                        "components": ["spectral", "phase_transitions"],
                    }
                )

            # 4. Emergência Multi-Modal Complexa
            total_patterns = len([p for p in patterns.values() if not p.get("error")])
            if total_patterns >= 3:
                # Calcular força de emergência multi-modal
                all_strengths = []

                if harmonic_data.get("resonance_strength"):
                    all_strengths.append(harmonic_data["resonance_strength"])
                if fractal_data.get("fractal_strength"):
                    all_strengths.append(fractal_data["fractal_strength"])
                if nonlocal_data.get("nonlocality_strength"):
                    all_strengths.append(nonlocal_data["nonlocality_strength"])
                if coherence_data.get("temporal_coherence"):
                    all_strengths.append(coherence_data["temporal_coherence"])

                if len(all_strengths) >= 3 and np.mean(all_strengths) > 0.5:
                    emergent_patterns.append(
                        {
                            "type": "multimodal_quantum_emergence",
                            "strength": float(np.mean(all_strengths)),
                            "description": f"Emergência quântica multi-modal com {len(all_strengths)} componentes ativos",
                            "components": list(patterns.keys())
                            + list(signatures.keys()),
                            "complexity_score": float(
                                len(all_strengths) * np.mean(all_strengths)
                            ),
                        }
                    )

            return emergent_patterns

        except Exception as e:
            self.logger.warning("_synthesize_emergent_patterns falhou: %s", e)
            return []

    def _calculate_adaptive_confidence(
        self, results: Dict[str, Any], config: Dict[str, Any]
    ) -> Dict[str, float]:
        """Calcula níveis de confiança com base nos resultados obtidos.

        Parameters
        ----------
        results : dict
            Estrutura gerada por ``detect_quantum_patterns``.
        config : dict
            Configuração usada para ponderação.

        Returns
        -------
        dict
            Mapeamento de domínio para valores de confiança normalizados.
        """
        try:
            confidences = {}

            # Confiança baseada na qualidade dos dados
            patterns = results.get("patterns", {})

            # Confiança espectral
            spectral = patterns.get("spectral", {})
            if not spectral.get("error"):
                spectral_conf = min(1.0, spectral.get("spectral_coherence", 0) * 1.2)
                confidences["spectral"] = spectral_conf

            # Confiança harmônica
            harmonic = patterns.get("harmonic", {})
            if not harmonic.get("error"):
                harmonic_conf = min(1.0, harmonic.get("resonance_strength", 0) * 1.1)
                confidences["harmonic"] = harmonic_conf

            # Confiança fractal
            fractal = patterns.get("fractal", {})
            if not fractal.get("error"):
                fractal_conf = min(1.0, fractal.get("self_similarity", 0) * 1.3)
                confidences["fractal"] = fractal_conf

            # Confiança geral (média ponderada)
            if confidences:
                overall_confidence = np.mean(list(confidences.values()))

                # Bonus por padrões emergentes
                emergent_count = len(results.get("emergent_phenomena", []))
                emergence_bonus = min(0.2, emergent_count * 0.05)

                confidences["overall"] = min(1.0, overall_confidence + emergence_bonus)
            else:
                confidences["overall"] = 0.0

            return confidences

        except Exception as e:
            self.logger.warning("_calculate_adaptive_confidence falhou: %s", e)
            return {"overall": 0.0}

    def _update_pattern_memory(self, results: Dict[str, Any]):
        """Atualiza a memória interna de padrões identificados.

        Parameters
        ----------
        results : dict
            Estrutura de resultados retornada por ``detect_quantum_patterns``.
        """
        try:
            # YAA REFINEMENT: Sistema de memória adaptativa para aprendizado
            current_time = time.time()

            # Armazenar padrões significativos
            overall_confidence = results.get("confidence_scores", {}).get("overall", 0)

            if overall_confidence > 0.6:  # Only store high-confidence patterns
                memory_entry = {
                    "timestamp": current_time,
                    "regime": results.get("regime"),
                    "patterns": results.get("patterns"),
                    "emergent_phenomena": results.get("emergent_phenomena"),
                    "confidence": overall_confidence,
                }

                self.pattern_cache[current_time] = memory_entry

                # Manter apenas últimos N padrões
                max_memory_size = 100
                if len(self.pattern_cache) > max_memory_size:
                    oldest_key = min(self.pattern_cache.keys())
                    del self.pattern_cache[oldest_key]

            # Atualizar histórico de harmônicos e ressonâncias
            harmonic_data = results.get("patterns", {}).get("harmonic", {})
            if harmonic_data.get("resonance_strength", 0) > 0.3:
                self.harmonic_memory.append(
                    {
                        "timestamp": current_time,
                        "resonance_strength": harmonic_data["resonance_strength"],
                        "harmonic_series": harmonic_data.get("harmonic_series", []),
                    }
                )

                # Limitar tamanho da memória harmônica
                if len(self.harmonic_memory) > 50:
                    self.harmonic_memory.pop(0)

        except Exception as e:
            logger.debug(f"Erro na atualização da memória de padrões: {e}")

    def save_memory(self, path: str) -> None:
        """Salva ``pattern_cache`` e ``harmonic_memory`` em formato JSON.

        Parameters
        ----------
        path : str
            Caminho do arquivo de saída.
        """
        try:
            data = {
                "pattern_cache": list(self.pattern_cache.values()),
                "harmonic_memory": self.harmonic_memory,
            }
            with open(path, "w", encoding="utf-8") as f:
                json.dump(data, f)
        except Exception as e:
            logger.debug(f"Erro ao salvar memória: {e}")

    def load_memory(self, path: str) -> None:
        """Carrega ``pattern_cache`` e ``harmonic_memory`` de um arquivo JSON.

        Parameters
        ----------
        path : str
            Caminho do arquivo de origem.
        """
        try:
            with open(path, "r", encoding="utf-8") as f:
                data = json.load(f)

            patterns = data.get("pattern_cache", [])
            self.pattern_cache = {
                entry["timestamp"]: entry for entry in patterns if "timestamp" in entry
            }
            self.harmonic_memory = data.get("harmonic_memory", [])
        except Exception as e:
            logger.debug(f"Erro ao carregar memória: {e}")

    # YAA REFINEMENT: Alias para compatibilidade com imports existentes
    async def detect_patterns(
        self, time_series: np.ndarray, context: Dict[str, Any] | None = None
    ) -> Dict[str, Any]:
        """Async wrapper providing backward-compatible `detect_patterns` method."""
        loop = asyncio.get_event_loop() if asyncio.get_event_loop_policy() else None
        if loop and loop.is_running():
            # run in executor to avoid blocking
            return await loop.run_in_executor(
                None, self.detect_quantum_patterns, time_series, context
            )
        return self.detect_quantum_patterns(time_series, context)


# Alias for compatibility
TemporalPatternDetector = QuantumTemporalPatternDetector
