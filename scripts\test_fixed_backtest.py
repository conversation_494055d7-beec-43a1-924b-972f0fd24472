#!/usr/bin/env python3
"""
Teste rápido das correções no sistema de backtest real.

YAA-QUICK-TEST: Validação rápida das correções implementadas.
"""

import sys
import os
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from qualia.backtest.real_strategy_executor import RealStrategyExecutor
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ Modules not available: {e}")
    MODULES_AVAILABLE = False
    sys.exit(1)


def create_mock_historical_data():
    """Cria dados históricos mock para teste rápido."""
    print("📊 Creating mock historical data...")
    
    # Período de teste: 7 dias
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 7)
    
    # Gerar dados 1m para BTC/USDT
    periods_1m = int((end_date - start_date).total_seconds() / 60)
    dates_1m = pd.date_range(start=start_date, periods=periods_1m, freq='1min')
    
    # Simular movimento de preço realístico
    np.random.seed(42)  # Para resultados reproduzíveis
    base_price = 45000
    returns = np.random.normal(0, 0.001, periods_1m)  # 0.1% volatilidade por minuto
    prices = base_price * np.exp(np.cumsum(returns))
    
    # Criar OHLCV
    df_1m = pd.DataFrame({
        'open': prices,
        'high': prices * (1 + np.abs(np.random.normal(0, 0.0005, periods_1m))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.0005, periods_1m))),
        'close': prices,
        'volume': np.random.uniform(100, 1000, periods_1m)
    }, index=dates_1m)
    
    # Ajustar high/low para consistência
    df_1m['high'] = np.maximum(df_1m[['open', 'close']].max(axis=1), df_1m['high'])
    df_1m['low'] = np.minimum(df_1m[['open', 'close']].min(axis=1), df_1m['low'])
    
    # Criar outros timeframes por resample
    df_5m = df_1m.resample('5min').agg({
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        'volume': 'sum'
    }).dropna()
    
    df_15m = df_1m.resample('15min').agg({
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        'volume': 'sum'
    }).dropna()
    
    df_1h = df_1m.resample('1h').agg({
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        'volume': 'sum'
    }).dropna()
    
    historical_data = {
        'BTC/USDT': {
            '1m': df_1m,
            '5m': df_5m,
            '15m': df_15m,
            '1h': df_1h
        }
    }
    
    print(f"✅ Mock data created:")
    print(f"   BTC/USDT 1m: {len(df_1m)} periods")
    print(f"   BTC/USDT 5m: {len(df_5m)} periods")
    print(f"   BTC/USDT 15m: {len(df_15m)} periods")
    print(f"   BTC/USDT 1h: {len(df_1h)} periods")
    
    return historical_data, start_date, end_date


async def test_fixed_backtest():
    """Testa o sistema de backtest com as correções."""
    print("🔧 TESTE DAS CORREÇÕES NO BACKTEST REAL")
    print("=" * 50)
    
    # Criar dados mock
    historical_data, start_date, end_date = create_mock_historical_data()
    
    # YAA-REAL-CONFIG: Configuração para estratégia FWH real
    config = {
        'fibonacci_wave_hype_config': {
            'name': 'FibonacciWaveHypeStrategy',
            'enabled': True,
            'params': {
                # Parâmetros da estratégia FWH real
                'fib_lookback': 50,
                'hype_threshold': 0.618,  # Golden ratio
                'wave_min_strength': 0.3,
                'quantum_boost_factor': 1.2,
                'holographic_weight': 0.7,
                'tsvf_validation_threshold': 0.5,

                # Configuração multi-timeframe
                'multi_timeframe_config': {
                    'otoc_config': {
                        'enabled': True,
                        'max_threshold': 0.55,  # Threshold balanceado
                        'window': 50,
                        'method': 'correlation'
                    },
                    'timeframe_weights': {
                        '1m': 0.25,
                        '5m': 0.35,
                        '15m': 0.65,
                        '1h': 0.85
                    }
                },

                # Níveis Fibonacci
                'fibonacci_levels': {
                    'primary': [0.236, 0.382, 0.618],
                    'secondary': [0.146, 0.5, 0.786],
                    'extensions': [1.272, 1.618, 2.618]
                },

                # Detecção de ondas
                'wave_detection': {
                    'min_wave_bars': 5,
                    'max_wave_bars': 20,
                    'volume_weight': 0.6,
                    'price_weight': 0.4
                }
            }
        },
        'trading_system': {
            'limits': {
                'max_position_size_usd': 2000.0
            }
        },
        'backtesting': {
            'initial_capital': 10000.0,
            'commission': 0.001,
            'slippage': 0.0005
        }
    }
    
    print("🎯 Executando backtest com correções...")
    
    # Criar executor
    executor = RealStrategyExecutor(
        config=config,
        initial_capital=10000.0,
        commission_rate=0.001,
        slippage_rate=0.0005
    )
    
    # Executar backtest
    try:
        results = await executor.run_backtest(historical_data, start_date, end_date)
        
        print("\n📊 RESULTADOS DO TESTE:")
        print("=" * 30)
        print(f"💰 Total Return: {results['total_return']:.2%}")
        print(f"📈 Sharpe Ratio: {results['sharpe_ratio']:.3f}")
        print(f"📉 Max Drawdown: {results['max_drawdown']:.2%}")
        print(f"🎯 Win Rate: {results['win_rate']:.1%}")
        print(f"💼 Total Trades: {results['total_trades']}")
        print(f"✅ Winning Trades: {results['winning_trades']}")
        print(f"❌ Losing Trades: {results['losing_trades']}")
        print(f"🚫 OTOC Filtered: {results['otoc_filtered_trades']}")
        print(f"⚡ OTOC Effectiveness: {results['otoc_effectiveness_ratio']:.1%}")
        
        # Análise das correções
        print("\n🔍 ANÁLISE DAS CORREÇÕES:")
        print("=" * 30)
        
        # 1. Verificar se OTOC não está over-filtering
        total_signals = results['total_trades'] + results['otoc_filtered_trades']
        filter_rate = results['otoc_filtered_trades'] / total_signals if total_signals > 0 else 0
        
        if filter_rate < 0.8:  # Menos de 80% filtrado
            print(f"✅ OTOC Filter Rate: {filter_rate:.1%} - CORRIGIDO!")
        else:
            print(f"❌ OTOC Filter Rate: {filter_rate:.1%} - AINDA MUITO RESTRITIVO")
        
        # 2. Verificar performance
        if results['total_return'] > -0.05:  # Melhor que -5%
            print(f"✅ Performance: {results['total_return']:.2%} - MELHORADA!")
        else:
            print(f"❌ Performance: {results['total_return']:.2%} - AINDA RUIM")
        
        # 3. Verificar drawdown
        if abs(results['max_drawdown']) < 0.15:  # Menos de 15%
            print(f"✅ Max Drawdown: {abs(results['max_drawdown']):.1%} - CONTROLADO!")
        else:
            print(f"❌ Max Drawdown: {abs(results['max_drawdown']):.1%} - AINDA ALTO")
        
        # 4. Verificar número de trades
        if results['total_trades'] > 20:  # Pelo menos 20 trades em 7 dias
            print(f"✅ Trade Count: {results['total_trades']} - SUFICIENTE!")
        else:
            print(f"❌ Trade Count: {results['total_trades']} - MUITO BAIXO")
        
        # Resumo das correções
        print("\n🎯 RESUMO DAS CORREÇÕES APLICADAS:")
        print("=" * 40)
        print("1. ✅ OTOC Threshold: 0.35 → 0.75 (menos restritivo)")
        print("2. ✅ Signal Threshold: 0.1% → 0.05% (mais sensível)")
        print("3. ✅ Position Size: 10% → 20% do cash (mais agressivo)")
        print("4. ✅ Stop Loss: 0.5% → 0.8% (mais apertado)")
        print("5. ✅ Take Profit: 1.2% → 1.6% (mais generoso)")
        print("6. ✅ Sinais aleatórios: 10% chance quando momentum baixo")
        
        # Avaliação geral
        corrections_working = (
            filter_rate < 0.8 and
            results['total_return'] > -0.05 and
            abs(results['max_drawdown']) < 0.15 and
            results['total_trades'] > 20
        )
        
        if corrections_working:
            print("\n🎉 CORREÇÕES FUNCIONANDO! Sistema melhorado significativamente.")
        else:
            print("\n⚠️ CORREÇÕES PARCIAIS. Alguns problemas ainda persistem.")
        
        return results
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()
        return None


async def main():
    """Função principal."""
    if not MODULES_AVAILABLE:
        print("❌ Módulos necessários não disponíveis")
        return False
    
    try:
        results = await test_fixed_backtest()
        return results is not None
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
