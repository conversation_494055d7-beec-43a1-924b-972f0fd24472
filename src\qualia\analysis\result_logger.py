from __future__ import annotations

"""Utility to persist experiment results as JSON lines."""

from pathlib import Path
import json
from typing import Any, Dict

from ..utils.logger import get_logger

logger = get_logger(__name__)


def log_experiment_results(data: Dict[str, Any], path: str) -> None:
    """Append experiment data to a ``.jsonl`` file.

    Parameters
    ----------
    data
        Dictionary with experiment metrics and metadata.
    path
        Destination file path. Parent directories are created if needed.
    """
    try:
        file_path = Path(path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        with file_path.open("a", encoding="utf-8") as fh:
            json.dump(data, fh)
            fh.write("\n")
        logger.debug("Saved experiment result to %s", file_path)
    except Exception as exc:  # pragma: no cover - best-effort logging
        logger.error("Failed to log experiment results: %s", exc)
