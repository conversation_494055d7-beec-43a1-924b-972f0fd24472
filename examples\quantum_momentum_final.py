#!/usr/bin/env python3
"""
QUANTUM MOMENTUM FINAL - Versão Otimizada para Produção
Combina os melhores elementos das versões testadas
Target: Win Rate >60%, Sharpe >0.5, Return >3%, Max DD <2%
"""

import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any

import pandas as pd
import numpy as np
import requests


class QuantumMomentumFinal:
    """Versão final otimizada para o sistema QUALIA."""
    
    def __init__(self):
        self.session = requests.Session()
    
    def fetch_data(self, symbol: str, days: int = 90) -> pd.DataFrame:
        """Busca dados históricos."""
        try:
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': symbol.replace('/', ''),
                'interval': '1h',
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index().dropna()
            
            # Indicadores necessários
            df['returns'] = df['close'].pct_change()
            df['sma_20'] = df['close'].rolling(20).mean()
            df['sma_50'] = df['close'].rolling(50).mean()
            df['rsi'] = self._calculate_rsi(df['close'], 14)
            df['volatility'] = df['returns'].rolling(20).std()
            df['atr'] = self._calculate_atr(df, 14)
            
            return df
            
        except Exception as e:
            print(f"❌ Erro ao buscar dados para {symbol}: {e}")
            return pd.DataFrame()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calcula RSI."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calcula Average True Range."""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        return true_range.rolling(period).mean()
    
    def quantum_momentum_current(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Versão ATUAL (para comparação)."""
        signals = []
        
        for i in range(50, len(df)):
            # Filtros atuais
            vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-20:i].quantile(0.7)
            trend_filter = abs(df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i] > 0.02
            rsi_filter = 35 < df['rsi'].iloc[i] < 65
            
            if not (vol_filter and trend_filter and rsi_filter):
                signals.append(0)
                continue
            
            # Sinais atuais
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
            long_momentum = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            
            signal = (
                price_momentum * 0.4 +
                vol_momentum * 0.2 +
                rsi_momentum * 0.2 +
                long_momentum * 0.2
            )
            
            if abs(signal) > 0.03:
                signals.append(np.clip(signal * 6, -1, 1))
            else:
                signals.append(0)
        
        return self._calculate_performance_current(df.iloc[50:], signals, "CURRENT")
    
    def quantum_momentum_final(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Versão FINAL - Combina os melhores elementos testados."""
        signals = []
        
        for i in range(50, len(df)):
            # 🏆 FILTROS OTIMIZADOS (baseados nos testes)
            
            # Volatilidade: mantém o filtro que funciona
            vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-20:i].quantile(0.7)
            
            # Trend: mantém o filtro que funciona
            trend_filter = abs(df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i] > 0.02
            
            # 🏆 RSI OTIMIZADO: Mais amplo (melhor para returns)
            rsi_filter = 32 < df['rsi'].iloc[i] < 68  # Baseado na V3 que teve melhor return
            
            if not (vol_filter and trend_filter and rsi_filter):
                signals.append(0)
                continue
            
            # SINAIS: Mantém a combinação que funciona
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
            long_momentum = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            
            signal = (
                price_momentum * 0.4 +
                vol_momentum * 0.2 +
                rsi_momentum * 0.2 +
                long_momentum * 0.2
            )
            
            # 🏆 THRESHOLD OTIMIZADO: Mais baixo (melhor para win rate)
            if abs(signal) > 0.027:  # Baseado na V2 que teve melhor win rate
                signals.append(np.clip(signal * 6, -1, 1))
            else:
                signals.append(0)
        
        return self._calculate_performance_final(df.iloc[50:], signals, "FINAL")
    
    def _calculate_performance_current(self, df: pd.DataFrame, signals: List[float], strategy_name: str) -> Dict[str, Any]:
        """Performance atual (baseline)."""
        returns = []
        trades = 0
        winning_trades = 0
        total_wins = 0
        total_losses = 0
        
        for i in range(1, len(signals)):
            if abs(signals[i-1]) > 0.1:
                price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
                raw_return = signals[i-1] * price_return
                
                # Gestão de risco atual
                if raw_return < -0.005:
                    final_return = -0.005
                elif raw_return > 0.008:
                    final_return = 0.008
                else:
                    final_return = raw_return
                
                returns.append(final_return)
                trades += 1
                
                if final_return > 0:
                    winning_trades += 1
                    total_wins += final_return
                else:
                    total_losses += abs(final_return)
        
        if not returns:
            return {'error': 'Nenhum trade'}
        
        returns_series = pd.Series(returns)
        
        total_return = returns_series.sum()
        volatility = returns_series.std() * np.sqrt(252)
        sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
        
        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        win_rate = winning_trades / trades if trades > 0 else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        return {
            'strategy': strategy_name,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown * 100,
            'win_rate': win_rate * 100,
            'total_trades': trades,
            'volatility': volatility * 100,
            'profit_factor': profit_factor
        }
    
    def _calculate_performance_final(self, df: pd.DataFrame, signals: List[float], strategy_name: str) -> Dict[str, Any]:
        """Performance FINAL com gestão de risco otimizada."""
        returns = []
        trades = 0
        winning_trades = 0
        total_wins = 0
        total_losses = 0
        
        for i in range(1, len(signals)):
            if abs(signals[i-1]) > 0.1:
                price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
                raw_return = signals[i-1] * price_return
                
                # 🏆 GESTÃO DE RISCO OTIMIZADA
                
                # Stop-loss otimizado (ligeiramente mais generoso para melhorar win rate)
                stop_loss = -0.0048  # Baseado nos testes que melhoraram win rate
                
                # Take-profit otimizado (mais agressivo para melhorar returns)
                take_profit = 0.0095  # Baseado nos testes que melhoraram returns
                
                if raw_return < stop_loss:
                    final_return = stop_loss
                elif raw_return > take_profit:
                    final_return = take_profit
                else:
                    final_return = raw_return
                
                returns.append(final_return)
                trades += 1
                
                if final_return > 0:
                    winning_trades += 1
                    total_wins += final_return
                else:
                    total_losses += abs(final_return)
        
        if not returns:
            return {'error': 'Nenhum trade'}
        
        returns_series = pd.Series(returns)
        
        total_return = returns_series.sum()
        volatility = returns_series.std() * np.sqrt(252)
        sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
        
        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        win_rate = winning_trades / trades if trades > 0 else 0
        avg_win = total_wins / winning_trades if winning_trades > 0 else 0
        avg_loss = total_losses / (trades - winning_trades) if (trades - winning_trades) > 0 else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        return {
            'strategy': strategy_name,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown * 100,
            'win_rate': win_rate * 100,
            'total_trades': trades,
            'volatility': volatility * 100,
            'profit_factor': profit_factor,
            'avg_win_pct': avg_win * 100,
            'avg_loss_pct': avg_loss * 100,
            'win_loss_ratio': avg_win / avg_loss if avg_loss > 0 else 0
        }


def run_final_validation():
    """Validação final da estratégia otimizada."""
    print("🏆 QUANTUM MOMENTUM FINAL - Validação para Produção")
    print("=" * 60)
    print("🎯 Objetivo: Win Rate >60%, Sharpe >0.5, Return >3%, Max DD <2%")
    print("=" * 60)
    
    final_strategy = QuantumMomentumFinal()
    
    symbols = ["BTCUSDT", "ETHUSDT"]
    all_results = []
    
    for symbol in symbols:
        print(f"\n📈 Validando {symbol}...")
        
        df = final_strategy.fetch_data(symbol, days=90)
        if df.empty or len(df) < 100:
            print(f"❌ Dados insuficientes para {symbol}")
            continue
        
        # Testa versão atual vs final
        current = final_strategy.quantum_momentum_current(df)
        final = final_strategy.quantum_momentum_final(df)
        
        if 'error' not in current:
            current['symbol'] = symbol
            all_results.append(current)
            
            print(f"\n📊 VERSÃO ATUAL:")
            print(f"   Return: {current['total_return_pct']:.2f}%")
            print(f"   Sharpe: {current['sharpe_ratio']:.3f}")
            print(f"   Win Rate: {current['win_rate']:.1f}%")
            print(f"   Trades: {current['total_trades']}")
            print(f"   Max DD: {current['max_drawdown_pct']:.2f}%")
            print(f"   Profit Factor: {current['profit_factor']:.2f}")
        
        if 'error' not in final:
            final['symbol'] = symbol
            all_results.append(final)
            
            print(f"\n🏆 VERSÃO FINAL:")
            print(f"   Return: {final['total_return_pct']:.2f}%")
            print(f"   Sharpe: {final['sharpe_ratio']:.3f}")
            print(f"   Win Rate: {final['win_rate']:.1f}%")
            print(f"   Trades: {final['total_trades']}")
            print(f"   Max DD: {final['max_drawdown_pct']:.2f}%")
            print(f"   Profit Factor: {final['profit_factor']:.2f}")
            if 'win_loss_ratio' in final:
                print(f"   Win/Loss Ratio: {final['win_loss_ratio']:.2f}")
        
        # Comparação
        if 'error' not in current and 'error' not in final:
            print(f"\n📈 MELHORIAS OBTIDAS:")
            print(f"   Win Rate: {final['win_rate'] - current['win_rate']:+.1f}% pontos")
            print(f"   Sharpe: {final['sharpe_ratio'] - current['sharpe_ratio']:+.3f}")
            print(f"   Return: {final['total_return_pct'] - current['total_return_pct']:+.2f}%")
            
            # Status vs objetivos
            print(f"\n🎯 STATUS VS OBJETIVOS (FINAL):")
            print(f"   Win Rate: {final['win_rate']:.1f}% {'✅' if final['win_rate'] >= 60 else '❌'} (>60%)")
            print(f"   Sharpe: {final['sharpe_ratio']:.3f} {'✅' if final['sharpe_ratio'] >= 0.5 else '❌'} (>0.5)")
            print(f"   Return: {final['total_return_pct']:.2f}% {'✅' if final['total_return_pct'] >= 3 else '❌'} (>3%)")
            print(f"   Max DD: {final['max_drawdown_pct']:.2f}% {'✅' if final['max_drawdown_pct'] <= 2 else '❌'} (<2%)")
    
    # Análise consolidada final
    if all_results:
        final_results = [r for r in all_results if r['strategy'] == 'FINAL']
        
        if final_results:
            print(f"\n" + "="*60)
            print(f"🏆 RESULTADO FINAL CONSOLIDADO")
            print(f"="*60)
            
            # Médias finais
            final_avg = {
                'win_rate': np.mean([r['win_rate'] for r in final_results]),
                'sharpe_ratio': np.mean([r['sharpe_ratio'] for r in final_results]),
                'total_return_pct': np.mean([r['total_return_pct'] for r in final_results]),
                'max_drawdown_pct': np.mean([r['max_drawdown_pct'] for r in final_results]),
                'total_trades': np.mean([r['total_trades'] for r in final_results]),
                'profit_factor': np.mean([r['profit_factor'] for r in final_results])
            }
            
            print(f"\n🎯 PERFORMANCE MÉDIA FINAL:")
            print(f"   Win Rate: {final_avg['win_rate']:.1f}%")
            print(f"   Sharpe Ratio: {final_avg['sharpe_ratio']:.3f}")
            print(f"   Total Return: {final_avg['total_return_pct']:.2f}%")
            print(f"   Max Drawdown: {final_avg['max_drawdown_pct']:.2f}%")
            print(f"   Trades Médios: {final_avg['total_trades']:.0f}")
            print(f"   Profit Factor: {final_avg['profit_factor']:.2f}")
            
            # Verifica se TODOS os objetivos foram atingidos
            objectives_met = (
                final_avg['win_rate'] >= 60 and
                final_avg['sharpe_ratio'] >= 0.5 and
                final_avg['total_return_pct'] >= 3 and
                final_avg['max_drawdown_pct'] <= 2
            )
            
            print(f"\n🎯 AVALIAÇÃO FINAL DOS OBJETIVOS:")
            print(f"   Win Rate >60%: {final_avg['win_rate']:.1f}% {'✅' if final_avg['win_rate'] >= 60 else '❌'}")
            print(f"   Sharpe >0.5: {final_avg['sharpe_ratio']:.3f} {'✅' if final_avg['sharpe_ratio'] >= 0.5 else '❌'}")
            print(f"   Return >3%: {final_avg['total_return_pct']:.2f}% {'✅' if final_avg['total_return_pct'] >= 3 else '❌'}")
            print(f"   Max DD <2%: {final_avg['max_drawdown_pct']:.2f}% {'✅' if final_avg['max_drawdown_pct'] <= 2 else '❌'}")
            
            if objectives_met:
                print(f"\n🎉 SUCESSO TOTAL! TODOS OS OBJETIVOS ATINGIDOS!")
                print(f"🏆 Quantum Momentum Final está PRONTA para ser a estratégia principal do QUALIA!")
                
                print(f"\n🚀 IMPLEMENTAÇÃO RECOMENDADA:")
                print(f"   1. ✅ Substituir estratégia atual pela versão FINAL")
                print(f"   2. ✅ Configurar como estratégia principal")
                print(f"   3. ✅ Monitorar performance em produção")
                print(f"   4. ✅ Documentar melhorias implementadas")
                
                print(f"\n📋 PARÂMETROS FINAIS OTIMIZADOS:")
                print(f"   • RSI Filter: 32 < RSI < 68 (ampliado)")
                print(f"   • Threshold: 0.027 (reduzido)")
                print(f"   • Stop-Loss: -0.48% (otimizado)")
                print(f"   • Take-Profit: +0.95% (otimizado)")
                
            else:
                objectives_missing = []
                if final_avg['win_rate'] < 60:
                    objectives_missing.append(f"Win Rate ({final_avg['win_rate']:.1f}% < 60%)")
                if final_avg['sharpe_ratio'] < 0.5:
                    objectives_missing.append(f"Sharpe ({final_avg['sharpe_ratio']:.3f} < 0.5)")
                if final_avg['total_return_pct'] < 3:
                    objectives_missing.append(f"Return ({final_avg['total_return_pct']:.2f}% < 3%)")
                if final_avg['max_drawdown_pct'] > 2:
                    objectives_missing.append(f"Max DD ({final_avg['max_drawdown_pct']:.2f}% > 2%)")
                
                print(f"\n⚠️  Objetivos não atingidos: {', '.join(objectives_missing)}")
                print(f"🔧 Necessárias otimizações adicionais")
            
            # Salva resultados finais
            output_dir = Path("results/quantum_momentum_final")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = int(time.time())
            with open(output_dir / f"final_validation_{timestamp}.json", 'w') as f:
                json.dump({
                    'timestamp': datetime.now().isoformat(),
                    'objectives_met': objectives_met,
                    'final_avg_performance': final_avg,
                    'all_results': all_results,
                    'optimization_summary': {
                        'rsi_filter': '32 < RSI < 68',
                        'threshold': 0.027,
                        'stop_loss': -0.0048,
                        'take_profit': 0.0095
                    }
                }, f, indent=2, default=str)
            
            print(f"\n💾 Validação final salva em results/quantum_momentum_final/")


if __name__ == "__main__":
    run_final_validation()
