import asyncio
import pytest

pytest.importorskip("pytest_benchmark")

from qualia.utils.network_resilience import CircuitBreaker, call_with_backoff


@pytest.mark.benchmark(group="utils-circuit-breaker")
def test_circuit_breaker_benchmark(benchmark):
    cb = CircuitBreaker(fail_threshold=5, recovery_timeout=0.1)

    async def ok_call():
        return 42

    def run_sync():
        return asyncio.run(call_with_backoff(cb, ok_call))

    result = benchmark(run_sync)
    assert result == 42
