#!/usr/bin/env python3
"""
Teste de Integração OTOC no Paper Trading.

YAA-VALIDATION: Script para validar se o sistema OTOC está funcionando corretamente.
"""

import sys
import os
import asyncio
import yaml
from datetime import datetime

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from qualia.utils.otoc_calculator import calculate_otoc, get_otoc_diagnostics
    from qualia.utils.otoc_metrics import get_otoc_metrics_collector
    from qualia.strategies.fibonacci_wave_hype.multi_timeframe_consolidator import (
        MultiTimeframeSignalConsolidator, TimeframeSignal
    )
    print("✅ Imports OTOC carregados com sucesso")
except ImportError as e:
    print(f"❌ Erro ao importar módulos OTOC: {e}")
    sys.exit(1)

import numpy as np
import pandas as pd


def test_otoc_calculator():
    """Testa o calculador OTOC básico."""
    print("\n🔍 Testando OTOC Calculator...")
    
    # Gerar dados de teste
    np.random.seed(42)
    
    # Série ordenada (baixo OTOC esperado)
    ordered_series = np.cumsum(np.random.normal(0.001, 0.01, 200)) + 100
    otoc_ordered = calculate_otoc(ordered_series, window=50, method="correlation")
    
    # Série caótica (alto OTOC esperado)  
    chaotic_series = np.random.normal(0, 0.05, 200)
    otoc_chaotic = calculate_otoc(chaotic_series, window=50, method="correlation")
    
    print(f"   OTOC série ordenada: {otoc_ordered:.4f}")
    print(f"   OTOC série caótica: {otoc_chaotic:.4f}")
    
    # Validações
    assert not np.isnan(otoc_ordered), "OTOC ordenado não deve ser NaN"
    assert not np.isnan(otoc_chaotic), "OTOC caótico não deve ser NaN"
    assert 0.0 <= otoc_ordered <= 1.0, f"OTOC ordenado fora do range: {otoc_ordered}"
    assert 0.0 <= otoc_chaotic <= 1.0, f"OTOC caótico fora do range: {otoc_chaotic}"
    
    print("   ✅ OTOC Calculator funcionando corretamente")
    return True


def test_otoc_diagnostics():
    """Testa as funções de diagnóstico OTOC."""
    print("\n🔍 Testando OTOC Diagnostics...")
    
    # Gerar série de teste
    test_series = np.random.normal(0, 0.02, 150)
    
    # Obter diagnósticos
    diagnostics = get_otoc_diagnostics(test_series, window=50)
    
    print(f"   Diagnósticos: {list(diagnostics.keys())}")
    
    # Validações
    expected_keys = ['otoc_correlation', 'otoc_financial', 'series_length', 'window_coverage']
    for key in expected_keys:
        assert key in diagnostics, f"Chave {key} faltando nos diagnósticos"
    
    assert diagnostics['series_length'] == 150, "Comprimento da série incorreto"
    
    print("   ✅ OTOC Diagnostics funcionando corretamente")
    return True


def test_metrics_collector():
    """Testa o coletor de métricas OTOC."""
    print("\n🔍 Testando OTOC Metrics Collector...")
    
    collector = get_otoc_metrics_collector()
    
    # Simular algumas decisões OTOC
    collector.record_otoc_decision(
        symbol="BTC/USDT",
        timeframe="1m",
        otoc_value=0.2,
        threshold_used=0.35,
        threshold_base=0.35,
        volatility=0.02,
        signal_original="BUY",
        signal_filtered="BUY",
        confidence_original=0.8,
        confidence_filtered=0.8
    )
    
    collector.record_otoc_decision(
        symbol="BTC/USDT",
        timeframe="5m",
        otoc_value=0.8,  # Alto OTOC - deve ser filtrado
        threshold_used=0.35,
        threshold_base=0.35,
        volatility=0.03,
        signal_original="BUY",
        signal_filtered="HOLD",  # Filtrado
        confidence_original=0.7,
        confidence_filtered=0.0
    )
    
    # Obter estatísticas
    stats = collector.get_otoc_statistics()
    chaos_rate = collector.get_chaos_rate()
    
    print(f"   Estatísticas: {stats}")
    print(f"   Taxa de caos: {chaos_rate:.1%}")
    
    # Validações
    assert stats['count'] >= 2, "Deve ter pelo menos 2 registros"
    assert stats['chaos_events'] >= 1, "Deve ter pelo menos 1 evento de caos"
    assert 0.0 <= chaos_rate <= 1.0, f"Taxa de caos fora do range: {chaos_rate}"
    
    print("   ✅ OTOC Metrics Collector funcionando corretamente")
    return True


def test_consolidator_integration():
    """Testa a integração com o consolidador multi-timeframe."""
    print("\n🔍 Testando MultiTimeframe Consolidator com OTOC...")
    
    # Configuração de teste
    config = {
        "timeframe_weights": {
            "1m": 0.3,
            "5m": 0.4,
            "15m": 0.6,
            "1h": 0.8
        },
        "otoc_config": {
            "enabled": True,
            "max_threshold": 0.35,
            "window": 50,
            "method": "correlation",
            "adaptive_threshold": {
                "enabled": True,
                "beta": 1.0,
                "vol_window": 20
            }
        }
    }
    
    consolidator = MultiTimeframeSignalConsolidator(config)
    
    # Criar sinais de teste
    signals = [
        TimeframeSignal(
            timeframe="1m",
            signal="buy",
            confidence=0.8,
            signal_strength=0.7,
            hype_momentum=0.6,
            holographic_boost=1.1,
            tsvf_validation=0.5,
            timestamp=datetime.now(),
            otoc_value=0.2  # OTOC baixo - deve passar
        ),
        TimeframeSignal(
            timeframe="5m",
            signal="buy",
            confidence=0.7,
            signal_strength=0.6,
            hype_momentum=0.5,
            holographic_boost=1.0,
            tsvf_validation=0.4,
            timestamp=datetime.now(),
            otoc_value=0.8  # OTOC alto - deve ser filtrado
        )
    ]
    
    # Aplicar filtro OTOC
    filtered_signals = consolidator.apply_otoc_filter(signals)
    
    print(f"   Sinais originais: {len(signals)}")
    print(f"   Sinais filtrados: {len(filtered_signals)}")
    
    # Validações
    assert len(filtered_signals) == 2, "Deve manter o mesmo número de sinais"
    
    # Primeiro sinal deve passar (OTOC baixo)
    assert filtered_signals[0].signal == "buy", "Sinal com OTOC baixo deve passar"
    assert filtered_signals[0].confidence == 0.8, "Confiança deve ser mantida"
    
    # Segundo sinal deve ser filtrado (OTOC alto)
    assert filtered_signals[1].signal == "hold", "Sinal com OTOC alto deve ser filtrado"
    assert filtered_signals[1].confidence == 0.0, "Confiança deve ser zerada"
    
    print("   ✅ MultiTimeframe Consolidator com OTOC funcionando corretamente")
    return True


def test_config_loading():
    """Testa se a configuração OTOC está correta no YAML."""
    print("\n🔍 Testando configuração OTOC no YAML...")
    
    config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'fwh_scalp_config.yaml')
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Verificar estrutura OTOC
        fwh_config = config.get('fibonacci_wave_hype_config', {})
        mtf_config = fwh_config.get('multi_timeframe_config', {})
        otoc_config = mtf_config.get('otoc_config', {})
        
        print(f"   Config version: {config.get('config_version', 'N/A')}")
        print(f"   OTOC enabled: {otoc_config.get('enabled', False)}")
        print(f"   OTOC threshold: {otoc_config.get('max_threshold', 'N/A')}")
        
        # Validações
        assert 'config_version' in config, "Config version faltando"

        if not otoc_config:
            print(f"   ⚠️ Configuração OTOC não encontrada. Estrutura MTF: {list(mtf_config.keys())}")
            return False

        assert 'enabled' in otoc_config, "Flag enabled faltando"
        assert 'max_threshold' in otoc_config, "Threshold faltando"
        
        print("   ✅ Configuração OTOC no YAML está correta")
        return True
        
    except FileNotFoundError:
        print(f"   ⚠️ Arquivo de configuração não encontrado: {config_path}")
        return False
    except Exception as e:
        print(f"   ❌ Erro ao carregar configuração: {e}")
        return False


async def main():
    """Executa todos os testes de integração OTOC."""
    print("🌀 TESTE DE INTEGRAÇÃO OTOC NO PAPER TRADING")
    print("=" * 60)
    
    tests = [
        ("OTOC Calculator", test_otoc_calculator),
        ("OTOC Diagnostics", test_otoc_diagnostics),
        ("Metrics Collector", test_metrics_collector),
        ("Consolidator Integration", test_consolidator_integration),
        ("Config Loading", test_config_loading),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result, None))
        except Exception as e:
            results.append((test_name, False, str(e)))
    
    # Relatório final
    print("\n" + "=" * 60)
    print("📊 RELATÓRIO DE TESTES")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, success, error in results:
        if success:
            print(f"✅ {test_name}: PASSOU")
            passed += 1
        else:
            print(f"❌ {test_name}: FALHOU")
            if error:
                print(f"   Erro: {error}")
            failed += 1
    
    print(f"\n📈 RESUMO: {passed} passou, {failed} falhou")
    
    if failed == 0:
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("🚀 Sistema OTOC está pronto para produção!")
        return True
    else:
        print(f"\n⚠️ {failed} teste(s) falharam. Verifique os erros acima.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
