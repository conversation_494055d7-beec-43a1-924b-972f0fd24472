import argparse
import json
import pytest

import src.backtest.backtest_harness as backtest_harness


@pytest.mark.asyncio
async def test_exit_when_historical_data_missing(tmp_path, monkeypatch):
    monkeypatch.setenv(
        "QUALIA_SECRET_KEY",
        "MDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDA=",
    )
    cfg = tmp_path / "strategy.json"
    cfg.write_text(json.dumps({"risk_profile_settings": {"custom": {}}}))

    args = argparse.Namespace(
        historical_data_path=str(tmp_path / "missing.csv"),
        symbol="BTC/USDT",
        timeframe="1m",
        initial_capital=1000.0,
        output_report_path=None,
        log_level="INFO",
        log_file=str(tmp_path / "log.txt"),
        disable_metacognition=False,
        disable_qast_evolution=False,
        disable_ace_optimization=False,
        stop_on_error=False,
        risk_profile="custom",
        risk_per_trade_pct=1.0,
        stop_loss_pct=0.02,
        take_profit_pct=0.04,
        trading_fee_pct=0.0026,
        strategy_config_json=str(cfg),
        max_position_capital_pct=None,
    )

    with pytest.raises(SystemExit):
        await backtest_harness.run_backtest(args)


@pytest.mark.asyncio
async def test_exit_when_strategy_config_missing(tmp_path, monkeypatch):
    monkeypatch.setenv(
        "QUALIA_SECRET_KEY",
        "MDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDA=",
    )
    csv = tmp_path / "data.csv"
    csv.write_text("timestamp,open,high,low,close,volume\n")

    args = argparse.Namespace(
        historical_data_path=str(csv),
        symbol="BTC/USDT",
        timeframe="1m",
        initial_capital=1000.0,
        output_report_path=None,
        log_level="INFO",
        log_file=str(tmp_path / "log.txt"),
        disable_metacognition=False,
        disable_qast_evolution=False,
        disable_ace_optimization=False,
        stop_on_error=False,
        risk_profile="custom",
        risk_per_trade_pct=1.0,
        stop_loss_pct=0.02,
        take_profit_pct=0.04,
        trading_fee_pct=0.0026,
        strategy_config_json=str(tmp_path / "missing.json"),
        max_position_capital_pct=None,
    )

    with pytest.raises(SystemExit):
        await backtest_harness.run_backtest(args)
