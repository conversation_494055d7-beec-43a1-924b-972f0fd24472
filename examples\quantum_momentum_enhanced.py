#!/usr/bin/env python3
"""
QUANTUM MOMENTUM ENHANCED - Versão Final Otimizada
Implementa melhorias específicas para atingir os objetivos:
- Win Rate >60%, Sharpe >0.5, Return >3%, Max DD <2%
"""

import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Tuple

import pandas as pd
import numpy as np
import requests


class QuantumMomentumEnhanced:
    """Versão Enhanced da Quantum Momentum com melhorias avançadas."""
    
    def __init__(self):
        self.session = requests.Session()
    
    def fetch_data(self, symbol: str, days: int = 90) -> pd.DataFrame:
        """Busca dados históricos com indicadores avançados."""
        try:
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': symbol.replace('/', ''),
                'interval': '1h',
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index().dropna()
            
            # Indicadores básicos
            df['returns'] = df['close'].pct_change()
            df['sma_20'] = df['close'].rolling(20).mean()
            df['sma_50'] = df['close'].rolling(50).mean()
            df['ema_12'] = df['close'].ewm(span=12).mean()
            df['ema_26'] = df['close'].ewm(span=26).mean()
            df['rsi'] = self._calculate_rsi(df['close'], 14)
            df['volatility'] = df['returns'].rolling(20).std()
            df['atr'] = self._calculate_atr(df, 14)
            
            # Indicadores avançados
            df['macd'] = df['ema_12'] - df['ema_26']
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']
            df['bb_upper'], df['bb_lower'] = self._calculate_bollinger_bands(df['close'], 20, 2)
            df['volume_sma'] = df['volume'].rolling(20).mean()
            df['price_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            # Market regime indicators
            df['trend_strength'] = abs(df['sma_20'] - df['sma_50']) / df['sma_50']
            df['volatility_regime'] = df['volatility'] / df['volatility'].rolling(50).mean()
            df['volume_strength'] = df['volume'] / df['volume_sma']
            
            # Multi-timeframe confirmation (simulado com médias)
            df['sma_5'] = df['close'].rolling(5).mean()
            df['sma_10'] = df['close'].rolling(10).mean()
            df['short_trend'] = (df['sma_5'] - df['sma_10']) / df['sma_10']
            df['medium_trend'] = (df['sma_20'] - df['sma_50']) / df['sma_50']
            
            return df
            
        except Exception as e:
            print(f"❌ Erro ao buscar dados para {symbol}: {e}")
            return pd.DataFrame()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calcula RSI."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calcula Average True Range."""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        return true_range.rolling(period).mean()
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series]:
        """Calcula Bollinger Bands."""
        sma = prices.rolling(period).mean()
        std = prices.rolling(period).std()
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, lower
    
    def quantum_momentum_current(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Versão ATUAL (para comparação)."""
        signals = []
        
        for i in range(50, len(df)):
            # Filtros atuais
            vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-20:i].quantile(0.7)
            trend_filter = abs(df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i] > 0.02
            rsi_filter = 35 < df['rsi'].iloc[i] < 65
            
            if not (vol_filter and trend_filter and rsi_filter):
                signals.append(0)
                continue
            
            # Sinais atuais
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
            long_momentum = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            
            signal = (
                price_momentum * 0.4 +
                vol_momentum * 0.2 +
                rsi_momentum * 0.2 +
                long_momentum * 0.2
            )
            
            if abs(signal) > 0.03:
                signals.append(np.clip(signal * 6, -1, 1))
            else:
                signals.append(0)
        
        return self._calculate_performance_with_risk_mgmt(df.iloc[50:], signals, "CURRENT")
    
    def quantum_momentum_enhanced(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Versão ENHANCED com melhorias implementadas."""
        signals = []
        
        for i in range(50, len(df)):
            # 🚀 MELHORIA 1: Filtros de entrada mais rigorosos
            
            # Filtro de volatilidade mais restritivo
            vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-20:i].quantile(0.6)  # Reduzido de 0.7
            
            # Filtro de trend mais forte
            trend_filter = df['trend_strength'].iloc[i] > 0.025  # Aumentado de 0.02
            
            # Filtro RSI mais conservador
            rsi_filter = 40 < df['rsi'].iloc[i] < 60  # Reduzido de 35-65
            
            # 🚀 MELHORIA 2: Novos filtros de qualidade
            
            # Filtro de volume (confirma movimento)
            volume_filter = df['volume_strength'].iloc[i] > 0.8
            
            # Filtro de posição no Bollinger Band (evita extremos)
            bb_filter = 0.2 < df['price_position'].iloc[i] < 0.8
            
            # Filtro MACD (confirma momentum)
            macd_filter = (df['macd'].iloc[i] > df['macd_signal'].iloc[i] and df['macd_histogram'].iloc[i] > 0) or \
                         (df['macd'].iloc[i] < df['macd_signal'].iloc[i] and df['macd_histogram'].iloc[i] < 0)
            
            # 🚀 MELHORIA 3: Multi-timeframe confirmation
            timeframe_alignment = (df['short_trend'].iloc[i] * df['medium_trend'].iloc[i]) > 0  # Mesmo direção
            
            # Combina todos os filtros
            if not (vol_filter and trend_filter and rsi_filter and volume_filter and bb_filter and macd_filter and timeframe_alignment):
                signals.append(0)
                continue
            
            # 🚀 MELHORIA 4: Sinais mais robustos
            
            # Momentum de preço com confirmação
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            
            # Volume momentum com peso adaptativo
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            vol_weight = min(1.5, df['volume_strength'].iloc[i])  # Peso baseado na força do volume
            
            # RSI momentum melhorado
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
            
            # Long momentum com confirmação MACD
            long_momentum = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            macd_confirmation = np.sign(df['macd_histogram'].iloc[i])
            
            # 🚀 MELHORIA 5: Pesos adaptativos baseados em condições de mercado
            
            # Ajuste baseado em volatilidade
            vol_adj = 1 / (1 + df['volatility_regime'].iloc[i] * 0.5)
            
            # Ajuste baseado na força do trend
            trend_adj = min(1.5, 1 + df['trend_strength'].iloc[i] * 10)
            
            # Combinação enhanced
            signal = (
                price_momentum * 0.35 +
                vol_momentum * vol_weight * 0.25 +
                rsi_momentum * 0.15 +
                long_momentum * macd_confirmation * 0.25
            ) * vol_adj * trend_adj
            
            # 🚀 MELHORIA 6: Threshold adaptativo
            base_threshold = 0.025  # Reduzido de 0.03 para mais trades
            volatility_adjustment = df['volatility_regime'].iloc[i] * 0.01
            adaptive_threshold = base_threshold + volatility_adjustment
            
            if abs(signal) > adaptive_threshold:
                # Multiplicador baseado na força do sinal
                signal_strength = abs(signal) / adaptive_threshold
                multiplier = 5 * min(signal_strength, 1.5)  # Reduzido de 6
                signals.append(np.clip(signal * multiplier, -1, 1))
            else:
                signals.append(0)
        
        return self._calculate_performance_enhanced_risk_mgmt(df.iloc[50:], signals, "ENHANCED")
    
    def _calculate_performance_with_risk_mgmt(self, df: pd.DataFrame, signals: List[float], strategy_name: str) -> Dict[str, Any]:
        """Performance com gestão de risco atual."""
        returns = []
        trades = 0
        winning_trades = 0
        total_wins = 0
        total_losses = 0
        
        for i in range(1, len(signals)):
            if abs(signals[i-1]) > 0.1:
                price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
                raw_return = signals[i-1] * price_return
                
                # Gestão de risco atual
                if raw_return < -0.005:
                    final_return = -0.005
                elif raw_return > 0.008:
                    final_return = 0.008
                else:
                    final_return = raw_return
                
                returns.append(final_return)
                trades += 1
                
                if final_return > 0:
                    winning_trades += 1
                    total_wins += final_return
                else:
                    total_losses += abs(final_return)
        
        if not returns:
            return {'error': 'Nenhum trade'}
        
        returns_series = pd.Series(returns)
        
        total_return = returns_series.sum()
        volatility = returns_series.std() * np.sqrt(252)
        sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
        
        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        win_rate = winning_trades / trades if trades > 0 else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        return {
            'strategy': strategy_name,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown * 100,
            'win_rate': win_rate * 100,
            'total_trades': trades,
            'volatility': volatility * 100,
            'profit_factor': profit_factor
        }
    
    def _calculate_performance_enhanced_risk_mgmt(self, df: pd.DataFrame, signals: List[float], strategy_name: str) -> Dict[str, Any]:
        """Performance com gestão de risco ENHANCED."""
        returns = []
        trades = 0
        winning_trades = 0
        total_wins = 0
        total_losses = 0
        
        for i in range(1, len(signals)):
            if abs(signals[i-1]) > 0.1:
                price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
                raw_return = signals[i-1] * price_return
                
                # 🚀 MELHORIA 7: Gestão de risco adaptativa baseada em ATR
                atr_pct = df['atr'].iloc[i] / df['close'].iloc[i] if i < len(df) else 0.01
                
                # Stop-loss adaptativo (mais generoso para melhorar win rate)
                stop_loss = -max(0.004, atr_pct * 1.5)  # Reduzido de -0.005
                
                # Take-profit adaptativo (mais agressivo para melhorar returns)
                take_profit = max(0.012, atr_pct * 3)  # Aumentado de 0.008
                
                # 🚀 MELHORIA 8: Trailing stop para maximizar ganhos
                if raw_return > take_profit * 0.6:  # Se já ganhou 60% do target
                    # Trailing stop mais generoso
                    trailing_stop = raw_return - max(0.003, atr_pct * 1.2)
                    final_return = max(trailing_stop, raw_return)
                elif raw_return < stop_loss:
                    final_return = stop_loss
                elif raw_return > take_profit:
                    final_return = take_profit
                else:
                    final_return = raw_return
                
                returns.append(final_return)
                trades += 1
                
                if final_return > 0:
                    winning_trades += 1
                    total_wins += final_return
                else:
                    total_losses += abs(final_return)
        
        if not returns:
            return {'error': 'Nenhum trade'}
        
        returns_series = pd.Series(returns)
        
        total_return = returns_series.sum()
        volatility = returns_series.std() * np.sqrt(252)
        sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
        
        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        win_rate = winning_trades / trades if trades > 0 else 0
        avg_win = total_wins / winning_trades if winning_trades > 0 else 0
        avg_loss = total_losses / (trades - winning_trades) if (trades - winning_trades) > 0 else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        return {
            'strategy': strategy_name,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown * 100,
            'win_rate': win_rate * 100,
            'total_trades': trades,
            'volatility': volatility * 100,
            'profit_factor': profit_factor,
            'avg_win_pct': avg_win * 100,
            'avg_loss_pct': avg_loss * 100,
            'win_loss_ratio': avg_win / avg_loss if avg_loss > 0 else 0
        }


def run_enhanced_comparison():
    """Compara versão atual vs enhanced."""
    print("🚀 QUANTUM MOMENTUM ENHANCED - Teste de Melhorias")
    print("=" * 60)
    print("🎯 Objetivo: Win Rate >60%, Sharpe >0.5, Return >3%, Max DD <2%")
    print("=" * 60)

    enhancer = QuantumMomentumEnhanced()

    symbols = ["BTCUSDT", "ETHUSDT"]
    all_results = []

    for symbol in symbols:
        print(f"\n📈 Testando {symbol}...")

        df = enhancer.fetch_data(symbol, days=90)
        if df.empty or len(df) < 100:
            print(f"❌ Dados insuficientes para {symbol}")
            continue

        # Testa ambas as versões
        current = enhancer.quantum_momentum_current(df)
        enhanced = enhancer.quantum_momentum_enhanced(df)

        if 'error' not in current:
            current['symbol'] = symbol
            all_results.append(current)

            print(f"\n📊 VERSÃO ATUAL:")
            print(f"   Return: {current['total_return_pct']:.2f}%")
            print(f"   Sharpe: {current['sharpe_ratio']:.3f}")
            print(f"   Win Rate: {current['win_rate']:.1f}%")
            print(f"   Trades: {current['total_trades']}")
            print(f"   Max DD: {current['max_drawdown_pct']:.2f}%")
            print(f"   Profit Factor: {current['profit_factor']:.2f}")

        if 'error' not in enhanced:
            enhanced['symbol'] = symbol
            all_results.append(enhanced)

            print(f"\n🚀 VERSÃO ENHANCED:")
            print(f"   Return: {enhanced['total_return_pct']:.2f}%")
            print(f"   Sharpe: {enhanced['sharpe_ratio']:.3f}")
            print(f"   Win Rate: {enhanced['win_rate']:.1f}%")
            print(f"   Trades: {enhanced['total_trades']}")
            print(f"   Max DD: {enhanced['max_drawdown_pct']:.2f}%")
            print(f"   Profit Factor: {enhanced['profit_factor']:.2f}")
            if 'win_loss_ratio' in enhanced:
                print(f"   Win/Loss Ratio: {enhanced['win_loss_ratio']:.2f}")

        # Comparação direta
        if 'error' not in current and 'error' not in enhanced:
            print(f"\n📈 MELHORIAS OBTIDAS:")

            win_rate_improvement = enhanced['win_rate'] - current['win_rate']
            sharpe_improvement = enhanced['sharpe_ratio'] - current['sharpe_ratio']
            return_improvement = enhanced['total_return_pct'] - current['total_return_pct']

            print(f"   Win Rate: {win_rate_improvement:+.1f}% pontos")
            print(f"   Sharpe: {sharpe_improvement:+.3f}")
            print(f"   Return: {return_improvement:+.2f}%")

            # Status vs objetivos
            print(f"\n🎯 STATUS VS OBJETIVOS (Enhanced):")
            print(f"   Win Rate: {enhanced['win_rate']:.1f}% {'✅' if enhanced['win_rate'] >= 60 else '❌'} (>60%)")
            print(f"   Sharpe: {enhanced['sharpe_ratio']:.3f} {'✅' if enhanced['sharpe_ratio'] >= 0.5 else '❌'} (>0.5)")
            print(f"   Return: {enhanced['total_return_pct']:.2f}% {'✅' if enhanced['total_return_pct'] >= 3 else '❌'} (>3%)")
            print(f"   Max DD: {enhanced['max_drawdown_pct']:.2f}% {'✅' if enhanced['max_drawdown_pct'] <= 2 else '❌'} (<2%)")

    # Análise consolidada
    if all_results:
        print(f"\n" + "="*60)
        print(f"📊 ANÁLISE CONSOLIDADA")
        print(f"="*60)

        current_results = [r for r in all_results if r['strategy'] == 'CURRENT']
        enhanced_results = [r for r in all_results if r['strategy'] == 'ENHANCED']

        if current_results and enhanced_results:
            # Médias
            current_avg = {
                'win_rate': np.mean([r['win_rate'] for r in current_results]),
                'sharpe_ratio': np.mean([r['sharpe_ratio'] for r in current_results]),
                'total_return_pct': np.mean([r['total_return_pct'] for r in current_results]),
                'max_drawdown_pct': np.mean([r['max_drawdown_pct'] for r in current_results])
            }

            enhanced_avg = {
                'win_rate': np.mean([r['win_rate'] for r in enhanced_results]),
                'sharpe_ratio': np.mean([r['sharpe_ratio'] for r in enhanced_results]),
                'total_return_pct': np.mean([r['total_return_pct'] for r in enhanced_results]),
                'max_drawdown_pct': np.mean([r['max_drawdown_pct'] for r in enhanced_results])
            }

            print(f"\n📊 COMPARAÇÃO MÉDIA:")
            print(f"                    ATUAL    ENHANCED   MELHORIA")
            print(f"   Win Rate:       {current_avg['win_rate']:6.1f}%   {enhanced_avg['win_rate']:6.1f}%   {enhanced_avg['win_rate']-current_avg['win_rate']:+6.1f}%")
            print(f"   Sharpe:         {current_avg['sharpe_ratio']:6.3f}    {enhanced_avg['sharpe_ratio']:6.3f}    {enhanced_avg['sharpe_ratio']-current_avg['sharpe_ratio']:+6.3f}")
            print(f"   Return:         {current_avg['total_return_pct']:6.2f}%   {enhanced_avg['total_return_pct']:6.2f}%   {enhanced_avg['total_return_pct']-current_avg['total_return_pct']:+6.2f}%")
            print(f"   Max DD:         {current_avg['max_drawdown_pct']:6.2f}%   {enhanced_avg['max_drawdown_pct']:6.2f}%   {enhanced_avg['max_drawdown_pct']-current_avg['max_drawdown_pct']:+6.2f}%")

            # Verifica se objetivos foram atingidos
            objectives_met = (
                enhanced_avg['win_rate'] >= 60 and
                enhanced_avg['sharpe_ratio'] >= 0.5 and
                enhanced_avg['total_return_pct'] >= 3 and
                enhanced_avg['max_drawdown_pct'] <= 2
            )

            print(f"\n🎯 RESULTADO FINAL:")
            if objectives_met:
                print(f"   ✅ TODOS OS OBJETIVOS ATINGIDOS!")
                print(f"   🏆 Quantum Momentum Enhanced está pronta para ser a estratégia principal!")
            else:
                print(f"   ⚠️  Alguns objetivos ainda não foram atingidos")
                print(f"   🔧 Necessárias mais otimizações")

            # Salva resultados
            output_dir = Path("results/quantum_momentum_enhanced")
            output_dir.mkdir(parents=True, exist_ok=True)

            timestamp = int(time.time())
            with open(output_dir / f"enhanced_results_{timestamp}.json", 'w') as f:
                json.dump({
                    'timestamp': datetime.now().isoformat(),
                    'objectives_met': objectives_met,
                    'current_avg': current_avg,
                    'enhanced_avg': enhanced_avg,
                    'all_results': all_results
                }, f, indent=2, default=str)

            print(f"\n💾 Resultados salvos em results/quantum_momentum_enhanced/")

            if objectives_met:
                print(f"\n🚀 PRÓXIMOS PASSOS:")
                print(f"   1. ✅ Implementar no sistema QUALIA")
                print(f"   2. ✅ Configurar como estratégia principal")
                print(f"   3. ✅ Monitorar performance em produção")
                print(f"   4. ✅ Documentar melhorias implementadas")


if __name__ == "__main__":
    run_enhanced_comparison()
