import importlib
import os

import importlib
import os

import numpy as np
import pytest

pytest.importorskip("pytest_benchmark")
pytest.importorskip("numba")


def _reload_folding(use_gpu: bool):
    os.environ["QUALIA_USE_GPU"] = "true" if use_gpu else "false"
    mod = importlib.reload(importlib.import_module("qualia.core.folding"))
    op = mod.FoldingOperator({"dimensions": 8})
    data = np.random.rand(100, 8)
    return op, data


def _reload_resonance(use_gpu: bool):
    os.environ["QUALIA_USE_GPU"] = "true" if use_gpu else "false"
    mod = importlib.reload(importlib.import_module("qualia.core.resonance"))
    op = mod.ResonanceOperator({})
    data = np.sin(np.linspace(0, 2 * np.pi, 128))
    return op, data


@pytest.mark.benchmark(group="numba")
@pytest.mark.parametrize("use_gpu", [False, True])
def test_folding_coherence_benchmark(benchmark, use_gpu):
    op, data = _reload_folding(use_gpu)
    benchmark(op._calculate_coherence, data, data)


@pytest.mark.benchmark(group="numba")
@pytest.mark.parametrize("use_gpu", [False, True])
def test_resonance_strength_benchmark(benchmark, use_gpu):
    op, data = _reload_resonance(use_gpu)
    fft = np.fft.fft(data)
    amps = np.abs(fft)
    phases = np.angle(fft)
    benchmark(op._calculate_resonance_strength, amps, phases)
