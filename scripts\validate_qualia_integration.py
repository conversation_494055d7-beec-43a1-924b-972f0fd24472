#!/usr/bin/env python3
"""
QUALIA Integration Validation Script - P-02.3 Phase 1
Comprehensive validation of QUALIA quantum-computational trading system integration

Validation Categories:
1. Configuration validation
2. Component initialization
3. Trading cycle simulation
4. Safety mechanism testing
5. Paper trading validation
6. Quantum metrics verification
7. Integration completeness
"""

import asyncio
import json
import logging
import os
import sys
import time
import yaml
from datetime import datetime, timezone
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class QUALIAIntegrationValidator:
    """Comprehensive QUALIA integration validator"""
    
    def __init__(self, config_path: str = "config/pilot_config.yaml"):
        self.config_path = config_path
        self.validation_results = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'overall_status': 'pending',
            'tests': {},
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'warnings': 0
            }
        }
    
    async def run_comprehensive_validation(self) -> dict:
        """Run all validation tests"""
        logger.info("🚀 Starting QUALIA Integration Validation - P-02.3 Phase 1")
        logger.info("=" * 60)
        
        # Test categories
        test_categories = [
            ('Configuration Validation', self.validate_configuration),
            ('Component Initialization', self.validate_component_initialization),
            ('Trading Cycle Execution', self.validate_trading_cycle),
            ('Safety Mechanisms', self.validate_safety_mechanisms),
            ('Paper Trading Mode', self.validate_paper_trading),
            ('Quantum Metrics', self.validate_quantum_metrics),
            ('Integration Completeness', self.validate_integration_completeness),
            ('Performance Validation', self.validate_performance)
        ]
        
        for category_name, test_function in test_categories:
            logger.info(f"\n📋 {category_name}")
            logger.info("-" * 40)
            
            try:
                result = await test_function()
                self.validation_results['tests'][category_name] = result
                
                if result['status'] == 'passed':
                    self.validation_results['summary']['passed'] += 1
                    logger.info(f"✅ {category_name}: PASSED")
                elif result['status'] == 'warning':
                    self.validation_results['summary']['warnings'] += 1
                    logger.warning(f"⚠️ {category_name}: WARNING - {result.get('message', '')}")
                else:
                    self.validation_results['summary']['failed'] += 1
                    logger.error(f"❌ {category_name}: FAILED - {result.get('message', '')}")
                
                self.validation_results['summary']['total_tests'] += 1
                
            except Exception as e:
                logger.error(f"❌ {category_name}: ERROR - {str(e)}")
                self.validation_results['tests'][category_name] = {
                    'status': 'failed',
                    'message': f"Exception: {str(e)}",
                    'details': {}
                }
                self.validation_results['summary']['failed'] += 1
                self.validation_results['summary']['total_tests'] += 1
        
        # Calculate overall status
        if self.validation_results['summary']['failed'] == 0:
            if self.validation_results['summary']['warnings'] == 0:
                self.validation_results['overall_status'] = 'passed'
            else:
                self.validation_results['overall_status'] = 'passed_with_warnings'
        else:
            self.validation_results['overall_status'] = 'failed'
        
        # Generate final report
        await self.generate_validation_report()
        
        return self.validation_results
    
    async def validate_configuration(self) -> dict:
        """Validate QUALIA integration configuration"""
        try:
            # Load configuration
            if not os.path.exists(self.config_path):
                return {
                    'status': 'failed',
                    'message': f'Configuration file not found: {self.config_path}',
                    'details': {}
                }
            
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            details = {}
            
            # Check QUALIA integration section
            qualia_config = config.get('qualia_integration', {})
            details['qualia_integration_enabled'] = qualia_config.get('enabled', False)
            details['trading_mode'] = qualia_config.get('mode', 'unknown')
            
            if not details['qualia_integration_enabled']:
                return {
                    'status': 'failed',
                    'message': 'QUALIA integration not enabled in configuration',
                    'details': details
                }
            
            # Check required components
            required_components = ['decision_engine', 'data_collector', 'strategy', 'signals', 'risk_manager']
            for component in required_components:
                component_config = qualia_config.get(component, {})
                details[f'{component}_enabled'] = component_config.get('enabled', False)
                
                if not component_config.get('enabled', False):
                    return {
                        'status': 'failed',
                        'message': f'Required component {component} not enabled',
                        'details': details
                    }
            
            # Check ultra-conservative limits
            trading_limits = config.get('trading', {}).get('limits', {})
            details['max_positions'] = trading_limits.get('max_positions', 0)
            details['max_daily_trades'] = trading_limits.get('max_daily_trades', 0)
            details['max_position_size_usd'] = trading_limits.get('max_position_size_usd', 0)
            
            # Validate ultra-conservative settings
            if details['max_positions'] > 2:
                return {
                    'status': 'warning',
                    'message': f'Max positions ({details["max_positions"]}) higher than ultra-conservative recommendation (≤2)',
                    'details': details
                }
            
            if details['max_position_size_usd'] > 20:
                return {
                    'status': 'warning',
                    'message': f'Max position size (${details["max_position_size_usd"]}) higher than ultra-conservative recommendation (≤$20)',
                    'details': details
                }
            
            return {
                'status': 'passed',
                'message': 'Configuration validation successful',
                'details': details
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'Configuration validation error: {str(e)}',
                'details': {}
            }
    
    async def validate_component_initialization(self) -> dict:
        """Validate QUALIA component initialization"""
        try:
            # Import and initialize QUALIA system
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            from qualia_pilot_trading_system import QUALIAPilotTradingSystem
            
            system = QUALIAPilotTradingSystem(self.config_path)
            
            # Test configuration loading
            config_loaded = await system.load_configuration()
            if not config_loaded:
                return {
                    'status': 'failed',
                    'message': 'Failed to load QUALIA configuration',
                    'details': {}
                }
            
            # Test component initialization
            components_initialized = await system.initialize_qualia_components()
            if not components_initialized:
                return {
                    'status': 'failed',
                    'message': 'Failed to initialize QUALIA components',
                    'details': {}
                }
            
            # Verify all components are initialized
            components = {
                'enhanced_data_collector': system.enhanced_data_collector,
                'oracle_decision_engine': system.oracle_decision_engine,
                'strategy': system.strategy,
                'signal_generator': system.signal_generator,
                'execution_engine': system.execution_engine,
                'risk_manager': system.risk_manager
            }
            
            details = {}
            for name, component in components.items():
                details[f'{name}_initialized'] = component is not None
                if component is None:
                    return {
                        'status': 'failed',
                        'message': f'Component {name} not initialized',
                        'details': details
                    }
            
            return {
                'status': 'passed',
                'message': 'All QUALIA components initialized successfully',
                'details': details
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'Component initialization error: {str(e)}',
                'details': {}
            }
    
    async def validate_trading_cycle(self) -> dict:
        """Validate QUALIA trading cycle execution"""
        try:
            from qualia_pilot_trading_system import QUALIAPilotTradingSystem
            
            system = QUALIAPilotTradingSystem(self.config_path)
            await system.load_configuration()
            await system.initialize_qualia_components()
            
            # Record initial state
            initial_pnl = system.total_pnl
            initial_trade_count = system.trade_count
            
            # Execute trading cycle
            start_time = time.time()
            success = await system.run_qualia_trading_cycle()
            execution_time = time.time() - start_time
            
            details = {
                'cycle_executed': success,
                'execution_time_seconds': round(execution_time, 3),
                'initial_pnl': initial_pnl,
                'final_pnl': system.total_pnl,
                'pnl_change': system.total_pnl - initial_pnl,
                'initial_trade_count': initial_trade_count,
                'final_trade_count': system.trade_count,
                'trades_executed': system.trade_count - initial_trade_count
            }
            
            if not success:
                return {
                    'status': 'failed',
                    'message': 'Trading cycle execution failed',
                    'details': details
                }
            
            if execution_time > 30:  # Should complete within 30 seconds
                return {
                    'status': 'warning',
                    'message': f'Trading cycle took {execution_time:.1f}s (>30s threshold)',
                    'details': details
                }
            
            return {
                'status': 'passed',
                'message': 'Trading cycle executed successfully',
                'details': details
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'Trading cycle validation error: {str(e)}',
                'details': {}
            }
    
    async def validate_safety_mechanisms(self) -> dict:
        """Validate ultra-conservative safety mechanisms"""
        try:
            from qualia_pilot_trading_system import QUALIAPilotTradingSystem
            
            system = QUALIAPilotTradingSystem(self.config_path)
            await system.load_configuration()
            
            details = {}
            
            # Test daily loss limit
            system.daily_pnl = -60.0  # Exceed limit
            details['daily_loss_limit_enforced'] = not system.check_safety_limits()
            
            # Test total loss limit
            system.daily_pnl = 0.0
            system.total_pnl = -60.0  # Exceed limit
            details['total_loss_limit_enforced'] = not system.check_safety_limits()
            
            # Test position limit
            system.total_pnl = 0.0
            system.position_count = 5  # Exceed limit
            details['position_limit_enforced'] = not system.check_safety_limits()
            
            # Test trade limit
            system.position_count = 0
            system.trade_count = 10  # Exceed limit
            details['trade_limit_enforced'] = not system.check_safety_limits()
            
            # Test valid state
            system.trade_count = 0
            system.last_trade_time = 0
            details['valid_state_allowed'] = system.check_safety_limits()
            
            # Check all safety mechanisms
            safety_checks = [
                details['daily_loss_limit_enforced'],
                details['total_loss_limit_enforced'],
                details['position_limit_enforced'],
                details['trade_limit_enforced'],
                details['valid_state_allowed']
            ]
            
            if not all(safety_checks):
                failed_checks = []
                if not details['daily_loss_limit_enforced']:
                    failed_checks.append('daily_loss_limit')
                if not details['total_loss_limit_enforced']:
                    failed_checks.append('total_loss_limit')
                if not details['position_limit_enforced']:
                    failed_checks.append('position_limit')
                if not details['trade_limit_enforced']:
                    failed_checks.append('trade_limit')
                if not details['valid_state_allowed']:
                    failed_checks.append('valid_state')
                
                return {
                    'status': 'failed',
                    'message': f'Safety mechanisms failed: {", ".join(failed_checks)}',
                    'details': details
                }
            
            return {
                'status': 'passed',
                'message': 'All safety mechanisms working correctly',
                'details': details
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'Safety mechanism validation error: {str(e)}',
                'details': {}
            }
    
    async def validate_paper_trading(self) -> dict:
        """Validate paper trading mode"""
        try:
            from qualia_pilot_trading_system import QUALIAPilotTradingSystem
            
            system = QUALIAPilotTradingSystem(self.config_path)
            await system.load_configuration()
            await system.initialize_qualia_components()
            
            details = {
                'paper_trading_mode': system.execution_engine.mode == 'paper_trading'
            }
            
            if not details['paper_trading_mode']:
                return {
                    'status': 'failed',
                    'message': f'Expected paper_trading mode, got: {system.execution_engine.mode}',
                    'details': details
                }
            
            # Test paper trade execution
            signals = [{'symbol': 'BTC/USDT', 'action': 'buy', 'confidence': 0.85}]
            risk_assessment = {'approved': True, 'risk_score': 0.2}
            
            execution_result = await system.execution_engine.execute_signals(signals, risk_assessment)
            
            details['paper_trade_executed'] = execution_result.get('executed', False)
            details['paper_trade_pnl'] = execution_result.get('pnl', 0.0)
            
            if not details['paper_trade_executed']:
                return {
                    'status': 'failed',
                    'message': 'Paper trade execution failed',
                    'details': details
                }
            
            return {
                'status': 'passed',
                'message': 'Paper trading mode validated successfully',
                'details': details
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'Paper trading validation error: {str(e)}',
                'details': {}
            }
    
    async def validate_quantum_metrics(self) -> dict:
        """Validate quantum metrics logging and calculation"""
        try:
            from qualia_pilot_trading_system import QUALIAPilotTradingSystem
            
            system = QUALIAPilotTradingSystem(self.config_path)
            await system.load_configuration()
            await system.initialize_qualia_components()
            
            # Get quantum analysis
            market_data = await system.enhanced_data_collector.collect_market_data()
            quantum_analysis = await system.oracle_decision_engine.analyze_market_state(market_data)
            
            details = {
                'consciousness_level': quantum_analysis.get('consciousness_level', 0.0),
                'quantum_coherence': quantum_analysis.get('quantum_coherence', 0.0),
                'holographic_patterns_count': len(quantum_analysis.get('holographic_patterns', [])),
                'temporal_analysis_present': 'temporal_analysis' in quantum_analysis
            }
            
            # Validate quantum metrics ranges
            if not (0.0 <= details['consciousness_level'] <= 1.0):
                return {
                    'status': 'failed',
                    'message': f'Consciousness level out of range: {details["consciousness_level"]}',
                    'details': details
                }
            
            if not (0.0 <= details['quantum_coherence'] <= 1.0):
                return {
                    'status': 'failed',
                    'message': f'Quantum coherence out of range: {details["quantum_coherence"]}',
                    'details': details
                }
            
            return {
                'status': 'passed',
                'message': 'Quantum metrics validated successfully',
                'details': details
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'Quantum metrics validation error: {str(e)}',
                'details': {}
            }
    
    async def validate_integration_completeness(self) -> dict:
        """Validate integration completeness with main pilot system"""
        try:
            from start_pilot_trading import PilotTradingSystem
            
            # Test main pilot system with QUALIA integration
            pilot = PilotTradingSystem(self.config_path)
            await pilot.load_configuration()
            
            details = {
                'qualia_integration_enabled': pilot.config.get('qualia_integration', {}).get('enabled', False),
                'pilot_system_initialized': True
            }
            
            if not details['qualia_integration_enabled']:
                return {
                    'status': 'failed',
                    'message': 'QUALIA integration not enabled in pilot system',
                    'details': details
                }
            
            # Test QUALIA-enhanced trading cycle
            success = await pilot.run_trading_cycle()
            details['qualia_enhanced_cycle_executed'] = success
            
            # Verify QUALIA system was initialized
            details['qualia_system_initialized'] = hasattr(pilot, 'qualia_system')
            
            if not success:
                return {
                    'status': 'failed',
                    'message': 'QUALIA-enhanced trading cycle failed',
                    'details': details
                }
            
            if not details['qualia_system_initialized']:
                return {
                    'status': 'warning',
                    'message': 'QUALIA system not initialized in pilot',
                    'details': details
                }
            
            return {
                'status': 'passed',
                'message': 'Integration completeness validated successfully',
                'details': details
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'Integration completeness validation error: {str(e)}',
                'details': {}
            }
    
    async def validate_performance(self) -> dict:
        """Validate system performance metrics"""
        try:
            from qualia_pilot_trading_system import QUALIAPilotTradingSystem
            
            system = QUALIAPilotTradingSystem(self.config_path)
            await system.load_configuration()
            await system.initialize_qualia_components()
            
            # Run multiple cycles to test performance
            cycle_times = []
            for i in range(3):
                start_time = time.time()
                await system.run_qualia_trading_cycle()
                cycle_time = time.time() - start_time
                cycle_times.append(cycle_time)
            
            details = {
                'average_cycle_time': round(sum(cycle_times) / len(cycle_times), 3),
                'max_cycle_time': round(max(cycle_times), 3),
                'min_cycle_time': round(min(cycle_times), 3),
                'cycles_tested': len(cycle_times)
            }
            
            if details['average_cycle_time'] > 15:  # Should average under 15 seconds
                return {
                    'status': 'warning',
                    'message': f'Average cycle time {details["average_cycle_time"]}s exceeds 15s threshold',
                    'details': details
                }
            
            if details['max_cycle_time'] > 30:  # Max should be under 30 seconds
                return {
                    'status': 'warning',
                    'message': f'Max cycle time {details["max_cycle_time"]}s exceeds 30s threshold',
                    'details': details
                }
            
            return {
                'status': 'passed',
                'message': 'Performance validation successful',
                'details': details
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'Performance validation error: {str(e)}',
                'details': {}
            }
    
    async def generate_validation_report(self):
        """Generate comprehensive validation report"""
        report_path = f"logs/pilot/qualia_integration_validation_{int(time.time())}.json"
        
        # Ensure logs directory exists
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(self.validation_results, f, indent=2)
        
        logger.info(f"\n📊 VALIDATION SUMMARY")
        logger.info("=" * 40)
        logger.info(f"Overall Status: {self.validation_results['overall_status'].upper()}")
        logger.info(f"Total Tests: {self.validation_results['summary']['total_tests']}")
        logger.info(f"Passed: {self.validation_results['summary']['passed']}")
        logger.info(f"Warnings: {self.validation_results['summary']['warnings']}")
        logger.info(f"Failed: {self.validation_results['summary']['failed']}")
        logger.info(f"Report saved: {report_path}")
        
        if self.validation_results['overall_status'] == 'passed':
            logger.info("✅ QUALIA Integration P-02.3 Phase 1: READY FOR DEPLOYMENT")
        elif self.validation_results['overall_status'] == 'passed_with_warnings':
            logger.warning("⚠️ QUALIA Integration P-02.3 Phase 1: READY WITH WARNINGS")
        else:
            logger.error("❌ QUALIA Integration P-02.3 Phase 1: NOT READY - ISSUES FOUND")


async def main():
    """Main validation execution"""
    validator = QUALIAIntegrationValidator()
    results = await validator.run_comprehensive_validation()
    
    # Exit with appropriate code
    if results['overall_status'] == 'failed':
        sys.exit(1)
    elif results['overall_status'] == 'passed_with_warnings':
        sys.exit(2)  # Warning exit code
    else:
        sys.exit(0)  # Success


if __name__ == '__main__':
    asyncio.run(main())
