"""Signal generation utilities for CompositeStrategy."""

from __future__ import annotations

from typing import Any, Dict, List, Optional, TYPE_CHECKING

import pandas as pd

from ...utils.logger import get_logger
from ..strategy_utils import make_signal_df
from ...signals import SignalType

if TYPE_CHECKING:  # pragma: no cover - for type hints only
    from .strategy import CompositeStrategy

logger = get_logger(__name__)


def generate_signals(
    strategy: "CompositeStrategy",
    analysis_result: Dict[str, Any],
) -> pd.DataFrame:
    """Aggregate sub strategy signals and apply QAST adaptations."""
    if not strategy.is_initialized:
        logger.warning(f"{strategy.name}: Tentativa de gerar sinal sem inicialização.")
        return pd.DataFrame()

    if (
        "error" in analysis_result
        and "Market data is empty" in analysis_result["error"]
    ):
        logger.warning(
            f"{strategy.name}: Erro na análise devido a dados de mercado vazios. Retornando DataFrame vazio."
        )
        return pd.DataFrame()

    if not analysis_result or not analysis_result.get("sub_analyses"):
        logger.warning(
            f"{strategy.name}: Resultado da análise inválido ou sem sub-análises. Retornando DataFrame vazio."
        )
        return pd.DataFrame()

    sub_analyses_results = analysis_result.get("sub_analyses", {})
    scores: List[float] = []
    sub_confidences: List[float] = []
    sub_details: Dict[str, Dict[str, Any]] = {}

    current_timestamp = analysis_result.get(
        "market_data_current_timestamp", pd.Timestamp.utcnow()
    )

    for i, strat_instance in enumerate(strategy.strategies):
        name = strat_instance.name
        sub_res = sub_analyses_results.get(name, {})
        score = 0.0
        signal_details_from_sub: Dict[str, Any] = {}

        if "error" not in sub_res and sub_res:
            try:
                signals_df_from_sub = strat_instance.generate_signals(sub_res)
                if not signals_df_from_sub.empty:
                    last_signal_row = signals_df_from_sub.iloc[-1]
                    signal_details_from_sub = last_signal_row.to_dict()
                    raw_signal = signal_details_from_sub.get("signal", "hold")
                    try:
                        sub_signal_type = SignalType(str(raw_signal).upper())
                    except ValueError:
                        sub_signal_type = SignalType.HOLD
                    sub_confidence = signal_details_from_sub.get("confidence", 0.0)

                    if sub_signal_type is SignalType.BUY:
                        score = sub_confidence
                    elif sub_signal_type is SignalType.SELL:
                        score = -sub_confidence
                    else:
                        score = 0.0
                    sub_confidences.append(sub_confidence)
                else:
                    logger.debug(
                        f"Sub-estratégia {name} não gerou sinais (DataFrame vazio)."
                    )
                    signal_details_from_sub = {"signal": "hold", "confidence": 0.0}
                    sub_confidences.append(0.0)
            except Exception as e:  # pragma: no cover - defensive
                logger.error(
                    f"Erro ao gerar sinal para {name}: {str(e)}. Score definido como 0.0.",
                    exc_info=True,
                )
                signal_details_from_sub = {
                    "signal": "hold",
                    "confidence": 0.0,
                    "error": str(e),
                }
                sub_confidences.append(0.0)
        else:
            logger.warning(
                f"Sub-estratégia {name} teve erro na análise ou resultado vazio. Score será 0.0."
            )
            signal_details_from_sub = {
                "signal": "hold",
                "confidence": 0.0,
                "error": sub_res.get("error", "Resultado de análise vazio"),
            }
            sub_confidences.append(0.0)

        sub_details[name] = {"score": score, "raw_signal": signal_details_from_sub}
        scores.append(score)

    current_weights = strategy.weights
    if strategy.qast_controller and hasattr(strategy.qast_controller, "adapt_weights"):
        try:
            qast_context = {
                **analysis_result.get("common", {}),
                "sub_signals_details": sub_details,
            }
            prev_snapshot = strategy.weights.copy()
            adapted_weights = strategy.qast_controller.adapt_weights(
                strategy.weights, qast_context
            )

            candidate_weights = (
                adapted_weights if adapted_weights is not None else strategy.weights
            )

            if (
                isinstance(candidate_weights, list)
                and len(candidate_weights) == len(prev_snapshot)
                and abs(sum(candidate_weights) - 1.0) < 1e-6
                and all(0 <= w <= 1.0 for w in candidate_weights)
            ):
                strategy.previous_weights = prev_snapshot
                strategy.weights = candidate_weights
                current_weights = strategy.weights
                logger.info(
                    f"{strategy.name}: Pesos adaptados pelo QAST: {strategy.weights}"
                )
            else:
                strategy.weights = prev_snapshot
                current_weights = strategy.weights
                logger.warning(
                    f"{strategy.name}: Adaptação de pesos pelo QAST falhou ou retornou valor inválido. Pesos revertidos para: {strategy.weights}"
                )
        except Exception as e:  # pragma: no cover - defensive
            strategy.weights = prev_snapshot
            current_weights = strategy.weights
            logger.error(
                f"{strategy.name}: Erro na adaptação de pesos pelo QAST: {str(e)}. Usando pesos anteriores.",
                exc_info=True,
            )

    final_signal_type = SignalType.HOLD
    final_confidence = 0.0
    weighted_score = 0.0

    if scores and current_weights and len(scores) == len(current_weights):
        weighted_score = sum(w * s for w, s in zip(current_weights, scores))
        if weighted_score > strategy.signal_threshold:
            final_signal_type = SignalType.BUY
        elif weighted_score < -strategy.signal_threshold:
            final_signal_type = SignalType.SELL
        final_confidence = max(sub_confidences) if sub_confidences else 0.0
        final_confidence = min(final_confidence, 1.0)
    else:
        logger.warning(
            f"{strategy.name}: Não foi possvel calcular o score agregado. Scores: {scores}, Pesos: {current_weights}"
        )

    last_close = analysis_result.get("common", {}).get("last_close")
    last_atr = analysis_result.get("common", {}).get("last_atr")
    stop_loss, take_profit = None, None

    if (
        final_signal_type is not SignalType.HOLD
        and last_close is not None
        and last_atr is not None
        and last_atr > 0
    ):
        sl_multiplier = strategy.common_params.get("composite_sl_atr_multiplier", 1.5)
        tp_multiplier = strategy.common_params.get("composite_tp_atr_multiplier", 2.0)
        if final_signal_type is SignalType.BUY:
            stop_loss = last_close - (last_atr * sl_multiplier)
            take_profit = last_close + (last_atr * tp_multiplier)
        elif final_signal_type is SignalType.SELL:
            stop_loss = last_close + (last_atr * sl_multiplier)
            take_profit = last_close - (last_atr * tp_multiplier)

    signal_output_df = make_signal_df(
        final_signal_type.value,
        final_confidence,
        weight=weighted_score,
        stop_loss=stop_loss,
        take_profit=take_profit,
        weights=current_weights,
        sub_details=sub_details,
    )
    signal_output_df.insert(0, "timestamp", current_timestamp)

    if strategy.metacognition:
        try:
            metacog_observation_data = {
                "timestamp": current_timestamp,
                "signal": final_signal_type.value,
                "confidence": final_confidence,
                "weighted_score": weighted_score,
                "weights": current_weights,
                "signal_threshold": strategy.signal_threshold,
                **analysis_result.get("common", {}),
            }
            strategy.metacognition.observe_signal(metacog_observation_data)
            feedback = strategy.metacognition.get_feedback()
            if feedback and "adjust_threshold" in feedback:
                new_threshold = feedback["adjust_threshold"]
                if 0.05 <= new_threshold <= 0.95:
                    strategy.signal_threshold = new_threshold
                    logger.info(
                        f"{strategy.name}: Signal threshold ajustado pela Metacognição para: {strategy.signal_threshold:.3f}"
                    )
        except Exception as e:  # pragma: no cover - defensive
            logger.error(
                f"{strategy.name}: Erro na observação/feedback da Metacognição: {str(e)}",
                exc_info=True,
            )

    logger.info(
        f"{strategy.name} - Sinal Gerado: {final_signal_type.value} (Conf: {final_confidence:.3f}, Score: {weighted_score:.3f}) @ {current_timestamp}"
    )
    logger.debug(
        f"{strategy.name} - Detalhes do Sinal: Pesos={current_weights}, SubSinais={sub_details}"
    )
    return signal_output_df
