#!/usr/bin/env python3
"""
Over-fitting Detection System - Sistema para detectar e prevenir over-fitting
na otimização de parâmetros OTOC.

YAA-OVERFITTING: Validação cruzada e detecção de over-fitting em otimização
de parâmetros de trading.
"""

import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
import json
import asyncio
from sklearn.model_selection import TimeSeriesSplit
from scipy import stats
import warnings

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


@dataclass
class ValidationResult:
    """Resultado de validação cruzada."""
    train_score: float
    test_score: float
    overfitting_ratio: float
    stability_score: float
    parameters: Dict[str, Any]
    fold_scores: List[float]
    is_overfitted: bool


class OverfittingDetector:
    """
    Detector rigoroso de over-fitting para otimização de parâmetros.

    YAA-RIGOROUS: Implementação com thresholds rigorosos conforme especificação
    """

    def __init__(self, overfitting_threshold: float = 0.2, stability_threshold: float = 0.8):
        """
        Inicializa detector de over-fitting com thresholds rigorosos.

        Parameters
        ----------
        overfitting_threshold : float
            Threshold rigoroso: overfitting_ratio < 0.2 (20%)
        stability_threshold : float
            Threshold rigoroso: stability_score > 0.8 (80%)
        """
        self.overfitting_threshold = overfitting_threshold
        self.stability_threshold = stability_threshold
        self.validation_results: List[ValidationResult] = []

        print(f"🔍 Overfitting Detector initialized with RIGOROUS thresholds:")
        print(f"   Max overfitting ratio: {overfitting_threshold:.1%}")
        print(f"   Min stability score: {stability_threshold:.1%}")
    
    async def cross_validate_parameters(
        self,
        parameters: Dict[str, Any],
        backtest_function,
        start_date: datetime,
        end_date: datetime,
        n_splits: int = 5
    ) -> ValidationResult:
        """
        Executa validação cruzada temporal para detectar over-fitting.
        
        YAA-CROSS-VALIDATION: Validação cruzada específica para séries temporais
        
        Parameters
        ----------
        parameters : Dict[str, Any]
            Parâmetros a serem validados
        backtest_function : callable
            Função de backtest a ser usada
        start_date : datetime
            Data inicial dos dados
        end_date : datetime
            Data final dos dados
        n_splits : int
            Número de folds para validação cruzada
            
        Returns
        -------
        ValidationResult
            Resultado da validação cruzada
        """
        print(f"🔍 Validação cruzada: {n_splits} folds")
        
        # YAA-RIGOROUS: Criar splits temporais de 3 meses cada (conforme especificação)
        total_days = (end_date - start_date).days

        # Garantir que temos pelo menos 6 meses de dados
        if total_days < 180:  # 6 meses
            raise ValueError(f"Insufficient data for rigorous validation: {total_days} days < 180 days required")

        # Splits de 3 meses cada
        fold_size_days = 90  # 3 meses
        actual_splits = min(n_splits, total_days // fold_size_days)

        if actual_splits < 3:
            raise ValueError(f"Need at least 3 folds for rigorous validation, got {actual_splits}")

        print(f"   Using {actual_splits} folds of {fold_size_days} days each")

        fold_scores = []
        train_scores = []

        for fold in range(actual_splits):
            # YAA-TEMPORAL-SPLITS: Janelas temporais não sobrepostas
            fold_start = start_date + timedelta(days=fold * fold_size_days)
            fold_end = min(fold_start + timedelta(days=fold_size_days), end_date)

            # Janela de treino: todos os dados ANTERIORES ao fold atual
            if fold == 0:
                # Primeiro fold: usar período anterior simulado (6 meses antes)
                train_start = start_date - timedelta(days=180)
                train_end = fold_start
            else:
                train_start = start_date
                train_end = fold_start

            # Janela de teste: fold atual (3 meses)
            test_start = fold_start
            test_end = fold_end
            
            print(f"   Fold {fold + 1}: Treino {train_start.date()} - {train_end.date()}, "
                  f"Teste {test_start.date()} - {test_end.date()}")
            
            try:
                # Executar backtest no período de treino
                train_result = await backtest_function(
                    parameters, train_start, train_end
                )
                train_score = self._calculate_composite_score(train_result['metrics'])
                train_scores.append(train_score)
                
                # Executar backtest no período de teste
                test_result = await backtest_function(
                    parameters, test_start, test_end
                )
                test_score = self._calculate_composite_score(test_result['metrics'])
                fold_scores.append(test_score)
                
                print(f"      Treino: {train_score:.3f}, Teste: {test_score:.3f}")
                
            except Exception as e:
                print(f"      ❌ Erro no fold {fold + 1}: {e}")
                fold_scores.append(0.0)
                train_scores.append(0.0)
        
        # Calcular métricas de validação
        avg_train_score = np.mean(train_scores) if train_scores else 0.0
        avg_test_score = np.mean(fold_scores) if fold_scores else 0.0
        
        # Ratio de over-fitting
        if avg_train_score > 0:
            overfitting_ratio = (avg_train_score - avg_test_score) / avg_train_score
        else:
            overfitting_ratio = 0.0
        
        # Score de estabilidade (inverso do CV)
        if len(fold_scores) > 1 and np.mean(fold_scores) > 0:
            cv_score = np.std(fold_scores) / np.mean(fold_scores)
            stability_score = max(0, 1 - cv_score)
        else:
            stability_score = 0.0
        
        # Detectar over-fitting
        is_overfitted = (
            overfitting_ratio > self.overfitting_threshold or
            stability_score < (1 - self.stability_threshold)
        )
        
        result = ValidationResult(
            train_score=avg_train_score,
            test_score=avg_test_score,
            overfitting_ratio=overfitting_ratio,
            stability_score=stability_score,
            parameters=parameters,
            fold_scores=fold_scores,
            is_overfitted=is_overfitted
        )
        
        self.validation_results.append(result)
        
        # Log resultado
        status = "🚨 OVERFITTED" if is_overfitted else "✅ VALID"
        print(f"   {status}: Train={avg_train_score:.3f}, Test={avg_test_score:.3f}, "
              f"Ratio={overfitting_ratio:.1%}, Stability={stability_score:.3f}")
        
        return result
    
    def _calculate_composite_score(self, metrics: Dict[str, float]) -> float:
        """
        Calcula score composto (mesmo do otimizador).
        
        YAA-CONSISTENCY: Usar mesma função objetivo
        """
        # Normalizar métricas para [0, 1]
        sharpe_norm = np.clip(metrics.get('sharpe_ratio', 0) / 3.0, 0, 1)
        drawdown_norm = np.clip(1 - abs(metrics.get('max_drawdown', 0.1)) / 0.1, 0, 1)
        win_rate_norm = metrics.get('win_rate', 0.5)
        profit_factor_norm = np.clip(metrics.get('profit_factor', 1) / 3.0, 0, 1)
        
        # Score composto com pesos otimizados
        composite_score = (
            0.35 * sharpe_norm +      # Retorno ajustado ao risco
            0.25 * drawdown_norm +    # Controle de risco
            0.20 * win_rate_norm +    # Consistência
            0.20 * profit_factor_norm # Rentabilidade
        )
        
        return composite_score
    
    def analyze_parameter_sensitivity(self) -> Dict[str, Any]:
        """
        Analisa sensibilidade dos parâmetros ao over-fitting.
        
        Returns
        -------
        Dict[str, Any]
            Análise de sensibilidade
        """
        if len(self.validation_results) < 5:
            return {"status": "insufficient_data"}
        
        # Extrair dados para análise
        overfitted_results = [r for r in self.validation_results if r.is_overfitted]
        valid_results = [r for r in self.validation_results if not r.is_overfitted]
        
        overfitting_rate = len(overfitted_results) / len(self.validation_results)
        
        # Analisar parâmetros mais propensos ao over-fitting
        parameter_analysis = {}
        
        if overfitted_results and valid_results:
            # Comparar distribuições de parâmetros
            all_param_names = set()
            for result in self.validation_results:
                all_param_names.update(result.parameters.keys())
            
            for param_name in all_param_names:
                overfitted_values = [
                    r.parameters.get(param_name, 0) 
                    for r in overfitted_results 
                    if param_name in r.parameters
                ]
                valid_values = [
                    r.parameters.get(param_name, 0) 
                    for r in valid_results 
                    if param_name in r.parameters
                ]
                
                if overfitted_values and valid_values:
                    # Teste estatístico
                    try:
                        statistic, p_value = stats.mannwhitneyu(
                            overfitted_values, valid_values, alternative='two-sided'
                        )
                        
                        parameter_analysis[param_name] = {
                            'overfitted_mean': np.mean(overfitted_values),
                            'valid_mean': np.mean(valid_values),
                            'p_value': p_value,
                            'significant': p_value < 0.05,
                            'overfitted_std': np.std(overfitted_values),
                            'valid_std': np.std(valid_values)
                        }
                    except Exception as e:
                        parameter_analysis[param_name] = {'error': str(e)}
        
        return {
            'status': 'analyzed',
            'overfitting_rate': overfitting_rate,
            'total_validations': len(self.validation_results),
            'overfitted_count': len(overfitted_results),
            'valid_count': len(valid_results),
            'parameter_analysis': parameter_analysis,
            'recommendations': self._generate_recommendations(parameter_analysis, overfitting_rate)
        }
    
    def _generate_recommendations(
        self, 
        parameter_analysis: Dict[str, Any], 
        overfitting_rate: float
    ) -> List[str]:
        """Gera recomendações baseadas na análise."""
        recommendations = []
        
        if overfitting_rate > 0.5:
            recommendations.append(
                "🚨 ALTA taxa de over-fitting detectada! "
                "Considere usar dados reais em vez de sintéticos."
            )
        
        if overfitting_rate > 0.3:
            recommendations.append(
                "⚠️ Over-fitting moderado. Aumente o período de validação "
                "ou reduza a complexidade dos parâmetros."
            )
        
        # Analisar parâmetros problemáticos
        problematic_params = []
        for param_name, analysis in parameter_analysis.items():
            if isinstance(analysis, dict) and analysis.get('significant', False):
                problematic_params.append(param_name)
        
        if problematic_params:
            recommendations.append(
                f"🎯 Parâmetros sensíveis ao over-fitting: {', '.join(problematic_params)}. "
                "Considere fixar estes valores ou reduzir seus ranges."
            )
        
        if overfitting_rate < 0.2:
            recommendations.append(
                "✅ Baixa taxa de over-fitting. Parâmetros parecem robustos."
            )
        
        return recommendations

    async def walk_forward_analysis(
        self,
        parameters: Dict[str, Any],
        backtest_function,
        start_date: datetime,
        end_date: datetime,
        window_months: int = 6,
        step_months: int = 1
    ) -> Dict[str, Any]:
        """
        Executa walk-forward analysis com janela móvel.

        YAA-WALK-FORWARD: Análise rigorosa com janela móvel de 6 meses

        Parameters
        ----------
        parameters : Dict[str, Any]
            Parâmetros a serem testados
        backtest_function : callable
            Função de backtest
        start_date : datetime
            Data inicial
        end_date : datetime
            Data final
        window_months : int
            Tamanho da janela em meses (padrão: 6)
        step_months : int
            Passo da janela em meses (padrão: 1)

        Returns
        -------
        Dict[str, Any]
            Resultados da análise walk-forward
        """
        print(f"🚶 Walk-Forward Analysis: janela {window_months}m, passo {step_months}m")

        # Calcular janelas
        window_delta = timedelta(days=window_months * 30)
        step_delta = timedelta(days=step_months * 30)

        current_start = start_date
        window_results = []

        while current_start + window_delta <= end_date:
            current_end = current_start + window_delta

            print(f"   Janela: {current_start.date()} - {current_end.date()}")

            try:
                # Executar backtest na janela
                result = await backtest_function(parameters, current_start, current_end)
                score = self._calculate_composite_score(result['metrics'])

                window_results.append({
                    'start_date': current_start,
                    'end_date': current_end,
                    'score': score,
                    'metrics': result['metrics'],
                    'sharpe_ratio': result['metrics'].get('sharpe_ratio', 0),
                    'max_drawdown': result['metrics'].get('max_drawdown', 0),
                    'win_rate': result['metrics'].get('win_rate', 0)
                })

                print(f"      Score: {score:.3f}, Sharpe: {result['metrics'].get('sharpe_ratio', 0):.3f}")

            except Exception as e:
                print(f"      ❌ Erro na janela: {e}")
                window_results.append({
                    'start_date': current_start,
                    'end_date': current_end,
                    'score': 0.0,
                    'error': str(e)
                })

            # Próxima janela
            current_start += step_delta

        if not window_results:
            return {'status': 'no_results'}

        # Analisar resultados
        scores = [r['score'] for r in window_results if 'error' not in r]

        if not scores:
            return {'status': 'all_failed'}

        # Métricas de estabilidade
        mean_score = np.mean(scores)
        std_score = np.std(scores)
        cv_score = std_score / mean_score if mean_score > 0 else float('inf')

        # Tendência temporal
        if len(scores) > 1:
            # Regressão linear simples para detectar tendência
            x = np.arange(len(scores))
            slope = np.polyfit(x, scores, 1)[0]
            trend = 'improving' if slope > 0.01 else 'declining' if slope < -0.01 else 'stable'
        else:
            slope = 0
            trend = 'insufficient_data'

        # Consistência (quantas janelas tiveram performance aceitável)
        acceptable_threshold = mean_score * 0.8  # 80% da performance média
        consistent_windows = sum(1 for s in scores if s >= acceptable_threshold)
        consistency_ratio = consistent_windows / len(scores)

        # Avaliação geral
        is_robust = (
            cv_score < 0.3 and  # Baixa variabilidade
            consistency_ratio >= 0.7 and  # 70% das janelas consistentes
            mean_score > 0.5  # Performance mínima aceitável
        )

        return {
            'status': 'completed',
            'total_windows': len(window_results),
            'successful_windows': len(scores),
            'mean_score': mean_score,
            'std_score': std_score,
            'cv_score': cv_score,
            'min_score': min(scores),
            'max_score': max(scores),
            'trend_slope': slope,
            'trend_direction': trend,
            'consistency_ratio': consistency_ratio,
            'is_robust': is_robust,
            'window_results': window_results,
            'parameters': parameters
        }
    
    def generate_validation_report(self) -> str:
        """Gera relatório de validação formatado."""
        if not self.validation_results:
            return "📊 Nenhum resultado de validação disponível."
        
        analysis = self.analyze_parameter_sensitivity()
        
        report = f"""
🔍 RELATÓRIO DE VALIDAÇÃO ANTI-OVERFITTING
{'='*60}

📊 ESTATÍSTICAS GERAIS:
   Total de Validações: {analysis.get('total_validations', 0)}
   Configurações Válidas: {analysis.get('valid_count', 0)}
   Configurações Overfitted: {analysis.get('overfitted_count', 0)}
   Taxa de Over-fitting: {analysis.get('overfitting_rate', 0):.1%}

🎯 ANÁLISE DE PARÂMETROS:
"""
        
        param_analysis = analysis.get('parameter_analysis', {})
        for param_name, param_data in param_analysis.items():
            if isinstance(param_data, dict) and 'p_value' in param_data:
                significance = "⚠️ SIGNIFICATIVO" if param_data['significant'] else "✅ OK"
                report += f"""   {param_name}:
      Média (Válidos): {param_data['valid_mean']:.3f}
      Média (Overfitted): {param_data['overfitted_mean']:.3f}
      P-value: {param_data['p_value']:.3f} {significance}
"""
        
        report += "\n💡 RECOMENDAÇÕES:\n"
        for i, rec in enumerate(analysis.get('recommendations', []), 1):
            report += f"   {i}. {rec}\n"
        
        return report
    
    def save_validation_results(self, filepath: str = "logs/overfitting_validation.json"):
        """Salva resultados de validação."""
        results_data = []
        
        for result in self.validation_results:
            results_data.append({
                'train_score': result.train_score,
                'test_score': result.test_score,
                'overfitting_ratio': result.overfitting_ratio,
                'stability_score': result.stability_score,
                'parameters': result.parameters,
                'fold_scores': result.fold_scores,
                'is_overfitted': result.is_overfitted,
                'timestamp': datetime.now().isoformat()
            })
        
        with open(filepath, 'w') as f:
            json.dump(results_data, f, indent=2)
        
        print(f"💾 Resultados de validação salvos em: {filepath}")


async def validate_optimization_results(
    optimization_results: List[Dict[str, Any]],
    backtest_function,
    top_n: int = 10
) -> List[ValidationResult]:
    """
    Valida os melhores resultados de otimização para detectar over-fitting.
    
    YAA-VALIDATION: Função de conveniência para validar otimização
    
    Parameters
    ----------
    optimization_results : List[Dict[str, Any]]
        Resultados da otimização ordenados por score
    backtest_function : callable
        Função de backtest
    top_n : int
        Número de melhores resultados a validar
        
    Returns
    -------
    List[ValidationResult]
        Resultados de validação
    """
    print(f"🔍 Validando top {top_n} resultados de otimização...")
    
    detector = OverfittingDetector()
    validation_results = []
    
    # Definir período de validação
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 3, 31)  # 3 meses
    
    for i, result in enumerate(optimization_results[:top_n]):
        print(f"\n📊 Validando resultado #{i+1} (score: {result.get('score', 0):.3f})")
        
        try:
            validation_result = await detector.cross_validate_parameters(
                parameters=result['parameters'],
                backtest_function=backtest_function,
                start_date=start_date,
                end_date=end_date,
                n_splits=4
            )
            validation_results.append(validation_result)
            
        except Exception as e:
            print(f"❌ Erro na validação #{i+1}: {e}")
    
    # Gerar relatório
    print(detector.generate_validation_report())
    
    # Salvar resultados
    detector.save_validation_results()
    
    return validation_results


async def main():
    """Demonstração do sistema de detecção de over-fitting."""
    print("🔍 DEMONSTRAÇÃO: Over-fitting Detection System")
    print("=" * 60)
    
    # Simular resultados de otimização
    mock_results = [
        {
            'parameters': {'otoc_max_threshold': 0.42, 'weight_15m': 0.65},
            'score': 0.85
        },
        {
            'parameters': {'otoc_max_threshold': 0.38, 'weight_15m': 0.70},
            'score': 0.82
        },
        {
            'parameters': {'otoc_max_threshold': 0.45, 'weight_15m': 0.60},
            'score': 0.80
        }
    ]
    
    # Função de backtest mock
    async def mock_backtest(params, start_date, end_date):
        # Simular variabilidade temporal
        days_diff = (end_date - start_date).days
        temporal_factor = 1 - (days_diff / 365) * 0.2  # Performance degrada com tempo
        
        base_score = 0.7 + np.random.normal(0, 0.1)
        return {
            'metrics': {
                'sharpe_ratio': base_score * temporal_factor,
                'max_drawdown': 0.05 / temporal_factor,
                'win_rate': 0.6 * temporal_factor,
                'profit_factor': 1.2 * temporal_factor
            }
        }
    
    # Executar validação
    validation_results = await validate_optimization_results(
        mock_results, mock_backtest, top_n=3
    )
    
    print(f"\n✅ Validação concluída: {len(validation_results)} resultados validados")


if __name__ == "__main__":
    asyncio.run(main())
