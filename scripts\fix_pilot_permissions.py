#!/usr/bin/env python3
"""
Fix Pilot File Permissions
P-02.3: Deploy Piloto com Capital Limitado

Fix file permissions for security (666 → 600)
"""

import os
import stat
from pathlib import Path

def fix_file_permissions():
    """Fix file permissions for pilot files"""
    print("🔒 FIXING PILOT FILE PERMISSIONS")
    print("=" * 40)
    
    config_dir = Path("config")
    
    # Files that need 600 permissions (read/write owner only)
    secure_files = [
        "pilot_config.yaml",
        ".pilot_credentials", 
        ".pilot_master.key",
        ".pilot_env"
    ]
    
    fixed_count = 0
    
    for file_name in secure_files:
        file_path = config_dir / file_name
        
        if file_path.exists():
            # Get current permissions
            current_perms = oct(file_path.stat().st_mode)[-3:]
            
            if current_perms != '600':
                try:
                    # Set 600 permissions (read/write owner only)
                    os.chmod(file_path, stat.S_IRUSR | stat.S_IWUSR)
                    new_perms = oct(file_path.stat().st_mode)[-3:]
                    
                    print(f"✅ {file_name}: {current_perms} → {new_perms}")
                    fixed_count += 1
                    
                except Exception as e:
                    print(f"❌ {file_name}: Failed to fix permissions - {e}")
            else:
                print(f"✓ {file_name}: Already secure (600)")
        else:
            print(f"⚠️ {file_name}: File not found")
    
    print("=" * 40)
    print(f"Fixed {fixed_count} file permissions")
    
    return fixed_count > 0

if __name__ == '__main__':
    fix_file_permissions()
