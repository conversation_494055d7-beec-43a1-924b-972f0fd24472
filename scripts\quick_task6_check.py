#!/usr/bin/env python3
"""Quick Task 6 Check"""

import sys
from pathlib import Path

def quick_check():
    pilot_file = Path("scripts/qualia_pilot_trading_system.py")
    
    try:
        with open(pilot_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        with open(pilot_file, 'r', encoding='latin-1') as f:
            content = f.read()
    
    checks = [
        ("RealExecutionEngine class", "class RealExecutionEngine:" in content),
        ("Ultra-conservative config", "ultra_conservative_min_confidence" in content),
        ("Real ExecutionInterface import", "qualia.core.qualia_execution_interface" in content),
        ("Integration in init", "RealExecutionEngine(" in content),
        ("Fallback mechanism", "MockExecutionEngine as fallback" in content),
        ("Execution validation", "_validate_ultra_conservative_execution_conditions" in content),
        ("Execution filtering", "_apply_ultra_conservative_execution_filtering" in content),
        ("Real interface execution", "_execute_with_real_interface" in content)
    ]
    
    print("Implementation Check:")
    for check_name, check_result in checks:
        status = "✅" if check_result else "❌"
        print(f"  {status} {check_name}")
    
    integration_checks = [
        ("Real ExecutionEngine initialization", "self.execution_engine = RealExecutionEngine" in content),
        ("Execution engine type tracking", "execution_engine_type" in content),
        ("Error handling with fallback", "except Exception as real_error:" in content),
        ("Mock fallback implementation", "MockExecutionEngine as fallback" in content),
        ("Success logging", "[SUCCESS] Real ExecutionEngine initialized" in content)
    ]
    
    print("\nIntegration Points Check:")
    for check_name, check_result in integration_checks:
        status = "✅" if check_result else "❌"
        print(f"  {status} {check_name}")

if __name__ == "__main__":
    quick_check()
