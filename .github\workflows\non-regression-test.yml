name: QUALIA Non-Regression Test

on:
  # Run on pushes to main branch
  push:
    branches: [ main ]
    paths:
      - 'src/qualia/**'
      - 'config/**'
      - 'scripts/**'
      - '.github/workflows/non-regression-test.yml'
  
  # Run on pull requests affecting core trading logic
  pull_request:
    branches: [ main ]
    paths:
      - 'src/qualia/trading/**'
      - 'src/qualia/optimization/**'
      - 'src/qualia/backtest/**'
      - 'config/**'
  
  # Run daily at 2 AM UTC to catch environmental changes
  schedule:
    - cron: '0 2 * * *'
  
  # Allow manual triggering
  workflow_dispatch:
    inputs:
      baseline_update:
        description: 'Update baseline metrics after successful run'
        required: false
        default: 'false'
        type: boolean

env:
  PYTHON_VERSION: '3.11'
  REGRESSION_THRESHOLD: 20  # Fail if performance drops > 20%

jobs:
  non-regression-test:
    runs-on: ubuntu-latest
    timeout-minutes: 60  # Prevent hanging jobs
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Full history for better context
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        # Install additional testing dependencies
        pip install pytest pytest-asyncio pytest-cov
    
    - name: Create results directory
      run: |
        mkdir -p results/non_regression
        mkdir -p logs
    
    - name: Run non-regression backtest
      id: backtest
      run: |
        echo "🚀 Starting QUALIA Non-Regression Test..."
        python scripts/non_regression_backtest.py \
          --output-dir results/non_regression \
          --threshold ${{ env.REGRESSION_THRESHOLD }} \
          --verbose
      env:
        # Ensure clean environment for testing
        PYTHONPATH: ${{ github.workspace }}/src
        QUALIA_ENV: ci_testing
        # Mock API keys for testing (use sandbox/demo data)
        KUCOIN_API_KEY: "mock_key_for_testing"
        KUCOIN_SECRET: "mock_secret_for_testing"
        KUCOIN_PASSPHRASE: "mock_passphrase_for_testing"
        KUCOIN_SANDBOX: "true"
    
    - name: Check regression results
      id: check_results
      run: |
        if [ -f "results/non_regression/regression_detected.flag" ]; then
          echo "❌ Performance regression detected!"
          echo "regression_detected=true" >> $GITHUB_OUTPUT
          exit 1
        else
          echo "✅ No performance regression detected"
          echo "regression_detected=false" >> $GITHUB_OUTPUT
        fi
    
    - name: Update baseline metrics (if requested)
      if: github.event.inputs.baseline_update == 'true' && steps.check_results.outputs.regression_detected == 'false'
      run: |
        echo "📊 Updating baseline metrics..."
        if [ -f "results/non_regression/current_metrics.json" ]; then
          cp results/non_regression/current_metrics.json scripts/baseline_metrics.json
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add scripts/baseline_metrics.json
          git commit -m "🔄 Update baseline metrics from non-regression test" || echo "No changes to commit"
          git push || echo "No changes to push"
        fi
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: non-regression-results-${{ github.run_number }}
        path: |
          results/non_regression/
          logs/
        retention-days: 30
    
    - name: Upload performance report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-report-${{ github.run_number }}
        path: results/non_regression/performance_report.md
        retention-days: 90
    
    - name: Comment PR with results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const path = 'results/non_regression/performance_report.md';
          
          if (fs.existsSync(path)) {
            const report = fs.readFileSync(path, 'utf8');
            const regression = '${{ steps.check_results.outputs.regression_detected }}' === 'true';
            
            const header = regression ? 
              '❌ **Performance Regression Detected**' : 
              '✅ **No Performance Regression**';
            
            const body = `${header}\n\n${report}`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: body
            });
          }
    
    - name: Notify on failure
      if: failure()
      run: |
        echo "🚨 Non-regression test failed!"
        echo "Check the artifacts for detailed logs and performance reports."
        # Here you could add Slack/email notifications
        # curl -X POST -H 'Content-type: application/json' \
        #   --data '{"text":"🚨 QUALIA Non-regression test failed in ${{ github.repository }}"}' \
        #   ${{ secrets.SLACK_WEBHOOK_URL }}

  # Optional: Run additional validation tests
  validate-config:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Validate configuration files
      run: |
        python -c "
        import yaml
        import json
        import sys
        
        # Validate YAML configs
        try:
            with open('config/holographic_trading_config.yaml', 'r') as f:
                yaml.safe_load(f)
            print('✅ holographic_trading_config.yaml is valid')
        except Exception as e:
            print(f'❌ Invalid YAML config: {e}')
            sys.exit(1)
        
        # Validate JSON configs if they exist
        import os
        if os.path.exists('config/production_config.json'):
            try:
                with open('config/production_config.json', 'r') as f:
                    json.load(f)
                print('✅ production_config.json is valid')
            except Exception as e:
                print(f'❌ Invalid JSON config: {e}')
                sys.exit(1)
        
        print('✅ All configuration files are valid')
        "
