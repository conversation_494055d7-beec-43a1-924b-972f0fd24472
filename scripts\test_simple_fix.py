#!/usr/bin/env python3
"""
YAA - Test Simple Fix
Teste simples das correções implementadas.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

import numpy as np
from qualia.strategies.nova_estrategia_qualia.risk import calculate_dynamic_weights
from qualia.strategies.nova_estrategia_qualia.meta_strategy import calculate_s1_position, calculate_s3_position

class MockStrategy:
    def __init__(self):
        self.rolling_sharpe_ratios = {"s1": 0.0, "s2": 0.0, "s3": 0.0}
        self.weights = {"s1": 0.0, "s2": 0.0, "s3": 0.0}

def test_dynamic_weights_fix():
    """Testa a correção de dynamic weights."""
    print("🧪 TESTE: Correção Dynamic Weights")
    print("-" * 40)
    
    # Cenário 1: Sharpe ratios zero (problema original)
    strategy = MockStrategy()
    strategy.rolling_sharpe_ratios = {"s1": 0.0, "s2": 0.0, "s3": 0.0}
    
    print("Cenário 1 - Sharpe ratios zero:")
    print(f"  Antes: {strategy.weights}")
    
    calculate_dynamic_weights(strategy)
    
    print(f"  Depois: {strategy.weights}")
    
    total_weight = sum(strategy.weights.values())
    if total_weight > 0:
        print("  ✅ SUCESSO: Weights não-zero aplicados como fallback")
    else:
        print("  ❌ FALHA: Weights ainda são zero")
    
    # Cenário 2: Sharpe ratios negativos
    strategy2 = MockStrategy()
    strategy2.rolling_sharpe_ratios = {"s1": -0.5, "s2": -0.2, "s3": -0.1}
    
    print("\nCenário 2 - Sharpe ratios negativos:")
    print(f"  Antes: {strategy2.weights}")
    
    calculate_dynamic_weights(strategy2)
    
    print(f"  Depois: {strategy2.weights}")
    
    total_weight2 = sum(strategy2.weights.values())
    if total_weight2 > 0:
        print("  ✅ SUCESSO: Weights não-zero aplicados como fallback")
    else:
        print("  ❌ FALHA: Weights ainda são zero")
    
    return total_weight > 0 and total_weight2 > 0

def test_threshold_logic():
    """Testa a lógica de threshold."""
    print("\n🧪 TESTE: Lógica de Threshold")
    print("-" * 40)
    
    # Cenário do problema original
    strength_s1 = 0.2341
    threshold_original = 0.02
    threshold_dynamic = 0.23412017115237962  # Valor do log
    
    print(f"Strength S1: {strength_s1}")
    print(f"Threshold original: {threshold_original}")
    print(f"Threshold dinâmico (problema): {threshold_dynamic}")
    
    # Teste com threshold original
    pos_original = calculate_s1_position(strength_s1, threshold_original)
    print(f"Position com threshold original: {pos_original}")
    
    # Teste com threshold dinâmico (problema)
    pos_dynamic = calculate_s1_position(strength_s1, threshold_dynamic)
    print(f"Position com threshold dinâmico: {pos_dynamic}")
    
    # Teste com correção (min entre percentil e 2x original)
    threshold_corrected = min(threshold_dynamic, threshold_original * 2.0)
    pos_corrected = calculate_s1_position(strength_s1, threshold_corrected)
    print(f"Threshold corrigido: {threshold_corrected}")
    print(f"Position com threshold corrigido: {pos_corrected}")
    
    if pos_corrected != 0:
        print("  ✅ SUCESSO: Threshold corrigido permite position não-zero")
        return True
    else:
        print("  ❌ FALHA: Position ainda é zero mesmo com correção")
        return False

def test_s3_strength():
    """Testa S3 com strength alta."""
    print("\n🧪 TESTE: S3 Strength Alta")
    print("-" * 40)
    
    strength_s3 = 0.37  # Valor do log
    threshold_s3 = 0.015  # Valor padrão
    
    print(f"Strength S3: {strength_s3}")
    print(f"Threshold S3: {threshold_s3}")
    
    pos_s3 = calculate_s3_position(strength_s3, threshold_s3)
    print(f"Position S3: {pos_s3}")
    
    if pos_s3 != 0:
        print("  ✅ SUCESSO: S3 com strength alta gera position não-zero")
        return True
    else:
        print("  ❌ FALHA: S3 position é zero mesmo com strength alta")
        return False

def main():
    """Função principal."""
    print("🚀 TESTE DAS CORREÇÕES PARA CONFIDENCE ZERO")
    print("=" * 50)
    
    test1 = test_dynamic_weights_fix()
    test2 = test_threshold_logic()
    test3 = test_s3_strength()
    
    print("\n📊 RESUMO DOS TESTES:")
    print(f"  Dynamic Weights Fix: {'✅ PASSOU' if test1 else '❌ FALHOU'}")
    print(f"  Threshold Logic Fix: {'✅ PASSOU' if test2 else '❌ FALHOU'}")
    print(f"  S3 Strength Test: {'✅ PASSOU' if test3 else '❌ FALHOU'}")
    
    all_passed = test1 and test2 and test3
    
    if all_passed:
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("As correções implementadas devem resolver o problema de confidence zero.")
    else:
        print("\n⚠️  ALGUNS TESTES FALHARAM")
        print("Podem ser necessários ajustes adicionais.")
    
    return all_passed

if __name__ == "__main__":
    main()
