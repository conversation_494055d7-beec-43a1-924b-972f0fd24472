# Implementação do Warm-up de Dados - QUALIA

## YAA TASK-01: Refatoração do Pré-Carregamento de Dados Históricos

### Objetivo

Garantir que o sistema QUALIA pré-carregue um volume de dados históricos suficiente para satisfazer a necessidade máxima de todas as estratégias configuradas **antes** de iniciar o primeiro ciclo de `analyze_market()`.

### Problema Identificado

O sistema estava falhando durante a inicialização porque as estratégias tentavam analisar o mercado sem ter dados históricos suficientes:

```
qualia.strategies.nova_estrategia_qualia.core - QualiaTSVFStrategy_BTCUSDT_5m: historico insuficiente (3/4). tentativa 1/3
```

### Solução Implementada

#### 1. Módulo de Warm-up (`src/qualia/core/data_warmup.py`)

Criado um gerenciador dedicado para o warm-up de dados com as seguintes funcionalidades:

- **<PERSON><PERSON><PERSON><PERSON> de Requisitos**: Identifica automaticamente os requisitos de dados de todas as estratégias
- **Coleta de Dados**: Busca dados históricos via integração com exchange
- **Cache**: Armazena dados em memória para uso posterior
- **Injeção**: Injeta dados pré-carregados no contexto compartilhado das estratégias
- **Conversão de Timeframes**: Funções comuns reunidas em `src/qualia/utils/timeframe.py`

#### 2. Integração com o Sistema Principal

##### Modificações em `scripts/start_real_trading.py`:

1. **Importações Adicionadas**:
```python
from qualia.core.data_warmup import DataWarmupManager, perform_system_warmup
from qualia.market.base_integration import BaseMarketIntegration
```

2. **Novos Atributos**:
```python
self.market_integration: Optional[BaseMarketIntegration] = None
self.warmup_manager: Optional[DataWarmupManager] = None
self.data_warmed_up = False
```

3. **Inicialização da Integração de Mercado**:
```python
# Em _initialize_data_collection_layer
self.market_integration = BaseMarketIntegration(
    exchange_name=exchange_name,
    config=self.config.get("exchanges", {}).get(exchange_name, {})
)
await self.market_integration.initialize_connection()
```

4. **Execução do Warm-up**:
```python
# Em start_trading, antes de criar as tasks
if not self.warmup_manager:
    self.warmup_manager = DataWarmupManager(
        symbols=self.symbols,
        timeframes=self.timeframes,
        strategies=self.oracle_engine.strategies,
        market_integration=self.market_integration
    )

warmup_success = await self.warmup_manager.perform_warmup()
self.warmup_manager.inject_data_into_strategies()
```

Durante essa fase, as estratégias temporárias criadas para analisar os
requisitos de dados recebem automaticamente cinco componentes do
`QUALIATradingSystem`:

- o gerenciador de risco (`risk_manager`)
- o controlador de risco dinâmico (`dynamic_risk_controller`)
- a configuração completa (`qualia_config`)
- a integração de mercado (`market_integration`)
- a instância de memória QPM (`qpm_instance`)

Isso garante que o comportamento das estratégias no warm-up reflita de forma
fidedigna o ambiente de produção.

### Funcionalidades Principais

#### 1. Análise Inteligente de Requisitos

O sistema analisa cada estratégia para determinar quantos candles são necessários:

```python
def analyze_data_requirements(self) -> Dict[str, int]:
    # Para cada estratégia
    if hasattr(strategy, 'required_initial_data_length'):
        required = strategy.required_initial_data_length
    # Aplica limite conservador baseado em exchanges reais
    max_allowed = 500
```

#### 2. Coleta Eficiente de Dados

- Calcula timestamps com margem de segurança (20%)
- Busca dados via API da exchange
- Implementa delays para não sobrecarregar a API

#### 3. Tratamento de Erros Robusto

- Continua com warm-up parcial se alguns pares falharem
- Registra todos os erros para análise
- Decide se aborta ou continua baseado na gravidade

#### 4. Injeção Automática de Dados

Os dados são injetados no `shared_context` das estratégias usando a chave esperada:

```python
preload_key = f"preloaded_candles_{timeframe}"
strategy.shared_context[preload_key] = data
```

### Benefícios

1. **Previne Falhas de Inicialização**: Garante que todas as estratégias tenham dados suficientes
2. **Otimização de Performance**: Dados são carregados uma única vez e reutilizados
3. **Flexibilidade**: Suporta diferentes requisitos por estratégia/timeframe
4. **Resiliência**: Continua operando mesmo com falhas parciais
5. **Transparência**: Logs detalhados do processo de warm-up

### Teste

Um script de teste foi criado em `scripts/test_warmup.py` para validar a implementação:

```bash
python scripts/test_warmup.py
```

### Próximos Passos

1. **Otimização de Cache**: Persistência em disco implementada no `DataWarmupManager`
2. **Warm-up Incremental**: Agora busca somente candles ausentes quando habilitado
3. **Configuração Dinâmica**: Timeout e limites lidos de arquivo YAML
4. **Monitoramento**: Métricas de duração e volume de dados carregados registradas

### Conclusão

A implementação do warm-up resolve o problema crítico de falha por histórico insuficiente, garantindo que o sistema QUALIA inicie com todos os dados necessários para operação. Isso torna o sistema mais robusto e confiável, eliminando uma das principais causas de falha durante a inicialização. 
## Warm-up Holográfico

### Propósito

O warm-up holográfico prepara o `HolographicMarketUniverse` com dados históricos
antes do início da coleta em tempo real. Esse aquecimento cria um campo
holográfico "vivo" desde o primeiro ciclo de mercado, permitindo que padrões
importantes sejam reconhecidos de imediato.

### Coletores Necessários

O processo exige dois coletores:

- `EnhancedDataCollector` para recuperar dados de mercado enriquecidos
- `RealDataCollector` para eventos de notícias e sentiment

Se qualquer um estiver ausente, o warm-up é ignorado e um aviso é registrado.

### Flags de Configuração

As opções ficam na seção `holographic` da configuração geral:

- `enable_warmup`: ativa ou desativa o aquecimento holográfico (padrão `True`)
- `warmup_hours`: define o período histórico simulado, em horas (padrão `72`)

### Falha no Warm-up

Caso `HolographicWarmupManager.perform_warmup()` retorne `False`,
`QUALIATradingSystem.initialize()` registra um erro e a inicialização é abortada
retornando `False`.
Quando os coletores estão ausentes, o método apenas emite um aviso e prossegue
com `True`.
