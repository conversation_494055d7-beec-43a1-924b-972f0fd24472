# Camada de Metacognição Quântica

## Introdução

A Metacognição Quântica é um componente essencial do sistema QUALIA, responsável por promover a capacidade de auto-análise e reflexão do sistema sobre os padrões quânticos que ele detecta. Este módulo implementa uma camada básica de Metacognição Quântica que avalia a qualidade e relevância dos padrões detectados, atribuindo-lhes um grau de significância simbólica e fornecendo explicações sobre por que determinados padrões são considerados significativos.

Observação: os nomes de variáveis e funções foram padronizados em inglês no código-fonte, mantendo a documentação em português para facilitar a leitura.

O ``QUALIARealTimeTrader`` opera apenas como interface. As decisões de trading
são tomadas pelo ``TradingQASTCore`` que expõe o método ``run_cycle`` para cada
ciclo de mercado.

## Arquitetura

A camada de Metacognição Quântica é composta pelos seguintes componentes:

1. **QuantumMetacognitionLayer**: Classe principal que avalia padrões quânticos
2. **MetacognitionConfig**: Configuração da camada metacognitiva
3. **QuantumSignaturePacket**: Pacote de assinatura quântica (definido em `common_types.py`)

## Configuração

A configuração da camada de metacognição é feita através da classe `MetacognitionConfig`:

```python
class MetacognitionConfig(BaseModel):
    # Pesos para as diferentes métricas na pontuação final
    weight_similarity: float = 0.30  # Peso da similaridade
    weight_entropy: float = 0.25     # Peso da entropia quântica
    weight_otoc: float = 0.20        # Peso do OTOC (Out-of-Time-Order Correlator)
    weight_coherence: float = 0.15   # Peso da coerência quântica
    weight_entanglement: float = 0.10  # Peso do emaranhamento multipartite
    
    # Limiares para classificação simbólica
    thresholds: Dict[str, float] = {
        "high": 0.75,   # Limite para relevância "high"
        "medium": 0.5,  # Limite para relevância "medium"
        "low": 0.0      # Limite para relevância "low"
    }

    # Parâmetros de detecção de anomalias
    anomaly_threshold: float = 2.0  # Z-score médio mínimo para sinalizar anomalia
    min_history_for_anomaly: int = 5  # Mínimo de padrões no histórico

    version: str = "1.1"  # Versão da configuração
```

## Avaliação de Padrões Quânticos

A camada de metacognição avalia os padrões quânticos com base em cinco métricas principais:

1. **Similaridade**: Compara o padrão com os padrões conhecidos armazenados na memória
2. **Entropia Quântica**: Avalia o grau de emaranhamento e complexidade do estado
3. **OTOC**: Mede o scrambling temporal, importante para sistemas quânticos caóticos
   - Em execuções consecutivas com o mesmo circuito em cache ou com estados iniciais
     determinísticos, é possível que o valor do OTOC permaneça inalterado. Nessas
     situações o logger registrará a informação em nível **DEBUG**, pois trata-se
    de um comportamento esperado.
   - Se a repetição ocorrer apenas por dois ciclos e não houver indício de erro,
     o registro será feito em nível **INFO**.
   - Caso o valor persista inalterado por três ou mais ciclos consecutivos, a
     métrica é marcada como inválida e um aviso de **WARNING** é emitido.
4. **Coerência L1**: Quantifica o nível de sobreposição quântica no estado
5. **Emaranhamento Multipartite**: Mede o entrelaçamento global do estado

O método principal para avaliação é:

```python
def evaluate_pattern(self, packet) -> Dict[str, Any]:
    """
    Avalia um padrão quântico e retorna uma explicação detalhada.
    
    Args:
        packet: QuantumSignaturePacket ou tupla (vetor, metadados)
        
    Returns:
        Dict com score, rótulo e explicação detalhada
    """
```

O resultado da avaliação é um dicionário contendo:

- `quantum_score`: Pontuação numérica (0 a 1) da relevância do padrão
- `symbolic_label`: Rótulo simbólico ('high', 'medium', 'low')
- `explanation`: Explicação textual das razões para a avaliação
- Métricas individuais: `similarity`, `entropy`, `otoc`, `coherence`
- Métricas individuais: `similarity`, `entropy`, `otoc`, `coherence`, `entanglement`

## Aprendizado de Feedback

A camada de metacognição pode aprender com feedback externo através do método:

```python
def learn_from_feedback(self, packet_id: str, correct_label: str) -> None:
    """
    Ajusta os pesos das métricas com base no feedback externo.
    
    Args:
        packet_id: ID do pacote avaliado anteriormente
        correct_label: Rótulo correto ('high', 'medium', 'low')
    """
```

Este método implementa um mecanismo de aprendizado simples que ajusta os pesos das métricas quando a avaliação difere do rótulo correto.

## Integração com o Universo Quântico

A camada de metacognição se integra ao `QUALIAQuantumUniverse` através dos seguintes passos:

1. O universo gera assinaturas quânticas utilizando `generate_quantum_signature()`
2. As assinaturas são avaliadas pela camada de metacognição usando `evaluate_pattern()`
3. Padrões significativos são armazenados na `QuantumPatternMemory`
4. O sistema pode reconhecer padrões semelhantes posteriormente

## Exemplo de Uso

```python
# Inicializar componentes
universe = QUALIAQuantumUniverse(n_qubits=4, scr_depth=2)
pattern_memory = QuantumPatternMemory(max_size=20, similarity_threshold=0.6)

# Configuração personalizada para a camada de metacognição
meta_config = MetacognitionConfig(
    weight_similarity=0.30,
    weight_entropy=0.25,
    weight_otoc=0.20,
    weight_coherence=0.15,
    weight_entanglement=0.10,
    thresholds={"high": 0.75, "medium": 0.5, "low": 0.0}
)

# Criar a camada de metacognição
metacognition_layer = QuantumMetacognitionLayer(
    universe=universe,
    pattern_memory=pattern_memory,
    config=meta_config
)

# Executar o universo quântico e gerar um estado
universe.run_quantum_simulation()

# Gerar assinatura quântica
qsp = universe.generate_quantum_signature(method="amplitude_vector")

# Avaliar o padrão
evaluation = metacognition_layer.evaluate_pattern(qsp)

# Resultados da avaliação
print(f"Score: {evaluation['quantum_score']:.4f}")
print(f"Rótulo: {evaluation['symbolic_label']}")
print(f"Similaridade: {evaluation['similarity']:.4f}")
print(f"Entropia: {evaluation['entropy']:.4f}")
print(f"OTOC: {evaluation['otoc']:.4f}")
print(f"Coerência: {evaluation['coherence']:.4f}")

# Armazenar o padrão se for significativo
if evaluation['symbolic_label'] in ['high', 'medium']:
    qsp.label = evaluation['symbolic_label']
    pattern_memory.store_pattern(qsp)
```

## Documentação dos Métodos Principais

### QuantumMetacognitionLayer

- `__init__(universe, pattern_memory, config)`: Inicializa a camada de metacognição
- `evaluate_pattern(packet)`: Avalia um padrão quântico
- `detect_anomaly(packet)`: Calcula um score de anomalia para o padrão
- `learn_from_feedback(packet_id, correct_label)`: Ajusta os pesos com base em feedback
- `_extract_vector(packet)`: Extrai o vetor e metadados do pacote
- `_similarity_to_memory(vector)`: Calcula a similaridade com padrões na memória
- `_von_neumann_entropy(vector)`: Calcula a entropia de von Neumann
- `_estimate_otoc(meta)`: Estima o OTOC dos metadados
- `_coherence_l1(vector)`: Calcula a coerência L1 do estado
- `_multipartite_entanglement(vector)`: Calcula o emaranhamento multipartite
- `_aggregate(similarity, entropy, otoc, coherence, entanglement)`: Agrega as métricas em um score
- `_label(score)`: Converte o score em um rótulo simbólico
- `_build_explanation(...)`: Constrói uma explicação detalhada da avaliação
- `get_evaluation_log()`: Retorna o histórico de avaliações. Modificações no
  resultado não afetam o log interno, pois é retornada uma cópia profunda.

## Considerações Futuras

Futuras melhorias para a camada de metacognição incluem:

1. Mecanismos de aprendizado mais sofisticados (por exemplo, redes neurais)
2. Métricas quânticas adicionais (por exemplo, emaranhamento multipartite)
3. Avaliação de causação vs. correlação em padrões temporais
4. Integração com módulos de tomada de decisão para feedback ativo
## Fluxo da diretiva `REDUCE_EXPOSURE`

O sistema de metacognição pode gerar a diretiva `REDUCE_EXPOSURE` quando os 
insights apontam baixa confiança ou riscos elevados. O fluxo completo ocorre da 
seguinte forma:

1. **Emissão** – `QUALIAMetacognitionTrading` avalia o contexto e define
   `trade_directive` no `MetacognitiveContext` como `REDUCE_EXPOSURE`. Um
   `TradeSignal` é criado com o mesmo tipo de sinal e pode incluir um
   percentual desejado no campo `suggested_quantity_specifier`.
2. **Propagação** – o sinal é lido pelos engines de trading. O
   `TradingEngine` suporta a diretiva nativamente, enquanto o
   `QUALIARealTimeTrader` apenas registra a ocorrência sem executar a
   redução.
   O campo aceita:
   - Strings como `"50%"`;
   - Valores decimais entre `0` e `1` (ex.: `0.5` equivale a 50%);
   - Valores entre `1` e `100` interpretados como percentual direto.

   Qualquer valor fora do intervalo `(0, 100]` ou formato inválido será
   desconsiderado e o percentual definido por `REDUCE_EXPOSURE_DEFAULT_PCT`
   será aplicado.

   Sinais com `confidence` abaixo de `REDUCE_EXPOSURE_MIN_CONFIDENCE` ou
   vinculados a posições com menos de
   `REDUCE_EXPOSURE_MIN_POSITION_AGE_MINUTES` minutos são ignorados. Para
   estratégias mais reativas, considere reduzir esses parâmetros.

3. **Aplicação** – o `TradingEngine` interpreta o percentual com
   `_parse_reduction_pct` e aciona `_reduce_portfolio_exposure`, fechando
   posições até que o alvo seja atingido. No `QUALIARealTimeTrader` esse
   mecanismo está desativado e a diretiva não resulta em fechamento
   automático de posições.
4. **Atualização de risco** – cada fechamento (total ou parcial) é registrado e
   o `RiskManager` recebe o resultado via `process_trade_result`, mantendo o
   capital e métricas consistentes.

Esse ciclo garante que a diminuição de exposição seja precisa e rastreável em
todos os níveis do sistema.

## Cooldown e Diretivas Estáveis

Quando o mecanismo de metacognição detecta diretivas consecutivas `hold` ou
`NO_SIGNAL`, ele inicia um **cooldown**. Após o número de ciclos definido por
`cooldown_threshold`, a confiança do sistema é incrementada em pequenos passos e
os parâmetros da Adaptive Consciousness Evolution são reiniciados. Esse processo
evita ajustes desnecessários durante mercados estáveis e prepara o sistema para
novos cenários sem perder a capacidade de resposta.

## Limites de Circuito

Para manter a execução eficiente dos circuitos quânticos gerados, a camada de
metacognição aplica limites de complexidade. Após a construção de um circuito,
são verificados dois parâmetros definidos em `universe_config`:

- **max_circuit_depth**: número máximo de camadas de portas lógicas;
- **max_circuit_operations**: total máximo de operações no circuito.

Se algum desses limites for ultrapassado, o circuito é reconstruído com menos
qubits ou com um encoding simplificado, reduzindo sua profundidade.

