# Double-Check de Integração do módulo `src/qualia/utils`

Este relatório avalia como o pacote `utils` se integra ao ecossistema QUALIA e apresenta melhorias de curto e longo prazo.

## Integração Sistêmica
- **Event-driven**: o módulo não publica nem consome eventos pelo `qualia.events`, limitando visibilidade de estados como `circuit_breaker`.
- **Configuração**: variáveis de operação (timeouts, falhas) ficam embutidas no código. Não há feature flags para rollout progressivo.
- **Observabilidade**: há uso de DogStatsd em `network_resilience` e `signature_utils`, porém não existe tracing OpenTelemetry nem propagação de `trace_id`.
- **Segurança**: não lida diretamente com credenciais ou PII. Falta revisão de políticas de retenção nos logs.

## Performance & Escalabilidade
- **Benchmark**: inexistem testes `pytest-benchmark` cobrindo as funções de cache ou circuit breaker.
- **Paralelismo**: utilitários assíncronos podem se beneficiar de `asyncio.TaskGroup` para fan-outs; não há hooks para GPU/QPU.

## Riscos Identificados
| Gravidade | Esforço | Descrição |
|-----------|--------|-----------|
| Alta | Médio | Ausência de eventos dificulta orquestração do pipeline e reduz observabilidade. |
| Média | Baixo | Falta de YAML e feature flags impede ajuste fino em produção. |
| Baixa | Baixo | Logs não propagam `trace_id`, comprometendo rastreabilidade. |

## Quick Wins ⚡
- [x] #24 Emitir `utils.circuit_breaker_state` no `qualia.events` ao abrir/fechar.
- [x] #24 Mover parâmetros de `network_resilience` para `config/utils.yaml` com flag `utils_v2`.
- [x] #24 Adicionar benchmark de `call_with_backoff` com `pytest-benchmark`.

## Atualizações Implementadas
- Flag `QUALIA_FT_UTILS_V2` habilita leitura dos parâmetros acima em `config/utils.yaml`.
- Eventos `utils.circuit_breaker_state` expõem `state` e `name` no Event Bus.
- Métricas `*.call_success`, `*.call_failure`, `*.opened` e `*.half_open` registram o ciclo do circuito via DogStatsd.

## Features de Valor
1. **Encoders/Decoders expandindo uso do `QuantumPatternMemory`**
   - *User Story*: Como desenvolvedor, desejo persistir métricas de sucesso do cache e do circuito em `QuantumPatternMemory` para inferir padrões de falha.
   - *Estimativa*: 4 dias.
2. **Hook de off‑loading opcional**
   - *User Story*: Como operador, quero opcionalmente delegar validações de assinatura a GPU/QPU quando presente, reduzindo a latência de `signature_utils`.
   - *Estimativa*: 5 dias.

Todos os riscos classificados como Alta Gravidade devem gerar tickets com responsável e prazo definido.
