#!/usr/bin/env python3
"""
Script otimizado para execução do QUALIA em produção
Carrega componentes sob demanda para acelerar inicialização
"""

import os
import sys
import asyncio
from pathlib import Path

# Configurar ambiente otimizado
os.environ['TICKER_TIMEOUT'] = '30'
os.environ['OHLCV_TIMEOUT'] = '90'
os.environ['RATE_LIMIT'] = '4.0'
os.environ['TICKER_CACHE_TTL'] = '20'
os.environ['API_FAIL_THRESHOLD'] = '2'

# Adicionar src ao path
sys.path.insert(0, 'src')

def main():
    """Execução otimizada do QUALIA"""
    print("🚀 QUALIA TRADING SYSTEM - MODO OTIMIZADO")
    print("=" * 60)
    
    # Importar apenas quando necessário
    from qualia.qualia_trading_system import main as qualia_main
    
    # Configurar argumentos otimizados
    import argparse
    
    # Simular argumentos CLI
    sys.argv = [
        'qualia_trading_system',
        '--symbols', 'BTC/USDT',
        '--timeframes', '5m',
        '--capital', '1000',
        '--mode', 'paper_trading',
        '--duration_seconds', '30',
        '--log_level', 'INFO',
        '--data_source', 'kucoin',
        '--risk_profile', 'aggressive',
        '--skip_preflight',  # Acelerar inicialização
        '--strategy_config_path', 'config/strategy_parameters.json'
    ]
    
    print("📋 Configurações:")
    print("   • Símbolo: BTC/USDT")
    print("   • Timeframe: 5m")
    print("   • Modo: Paper Trading")
    print("   • Duração: 30s")
    print("   • Exchange: KuCoin")
    print("   • Rate Limit: 4.0s")
    print("   • Timeouts otimizados")
    
    print("\n🔄 Iniciando sistema...")
    
    try:
        # Executar sistema principal com argumentos simulados
        import argparse
        
        # Criar parser e parsear argumentos simulados
        parser = argparse.ArgumentParser()
        parser.add_argument('--symbols', nargs='+', default=['BTC/USDT'])
        parser.add_argument('--timeframes', nargs='+', default=['5m'])
        parser.add_argument('--capital', type=float, default=1000)
        parser.add_argument('--mode', default='paper_trading')
        parser.add_argument('--duration_seconds', type=int, default=30)
        parser.add_argument('--log_level', default='INFO')
        parser.add_argument('--data_source', default='kucoin')
        parser.add_argument('--risk_profile', default='aggressive')
        parser.add_argument('--skip_preflight', action='store_true')
        parser.add_argument('--strategy_config_path', default='config/strategy_parameters.json')
        
        # Simular argumentos
        args = parser.parse_args([
            '--symbols', 'BTC/USDT',
            '--timeframes', '5m',
            '--capital', '1000',
            '--mode', 'paper_trading',
            '--duration_seconds', '30',
            '--log_level', 'INFO',
            '--data_source', 'kucoin',
            '--risk_profile', 'aggressive',
            '--skip_preflight',
            '--strategy_config_path', 'config/strategy_parameters.json'
        ])
        
        # Executar sistema principal
        result = asyncio.run(qualia_main(args))
        
        if result:
            print("\n✅ EXECUÇÃO CONCLUÍDA COM SUCESSO!")
        else:
            print("\n⚠️ Execução com problemas")
            
        return result
        
    except KeyboardInterrupt:
        print("\n🛑 Execução interrompida pelo usuário")
        return True
    except Exception as e:
        print(f"\n❌ Erro durante execução: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 QUALIA EXECUTADO COM SUCESSO!")
        print("📊 Sistema pronto para trading real")
        print("🔥 Use este script para execução em produção")
    else:
        print("\n⚠️ Verifique logs para detalhes")
    
    sys.exit(0 if success else 1) 