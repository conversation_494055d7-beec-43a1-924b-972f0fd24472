"""Estratégia TSVF e meta-combinação adaptativa para o QUALIA.

Este módulo define a :class:`QualiaTSVFStrategy`, responsável por aplicar o
Two-State Vector Formalism (TSVF) aos dados de mercado e combinar
retroativamente os sinais gerados por três sub-estratégias. A estratégia é
registrada automaticamente no framework QUALIA através do decorador
``register_strategy`` e oferece utilidades para cálculo de métricas quânticas,
gestão de risco e avaliação de performance.

Classes
-------
QualiaTSVFStrategy
    Estratégia principal que une TSVF e mecanismos de controle dinâmico.
"""

from __future__ import annotations

from dataclasses import asdict, is_dataclass
from pydantic import BaseModel
from collections import deque
import math
from typing import Any, Dict, List, Optional

import numpy as np
import pandas as pd
import re

from ...utils.logger import get_logger
from ...utils.tracing import get_tracer
from ...constants import MIN_INITIAL_HISTORY
from ...utils.data_helpers import is_data_empty
from ...intentions import IntentionEnvelope
from ...retroselector import RetroSelector
from ...utils.simulator import simulate_returns
from ...config.feature_flags import feature_toggle
from ...memory.event_bus import SimpleEventBus
from ...config.config_manager import ConfigManager
from ...strategies.strategy_interface import (
    TradingStrategy,
    TradingContext,
    register_strategy,
)
from ..params import QualiaTSVFParams

from .tsvf import (
    TSVFCalculator,
)
from .meta_strategy import (
    calculate_s1_position,
    calculate_s2_position,
    calculate_s3_position,
    update_weights_for_step,
    AlphaGammaPolicy,
)
from .signal_generation import generate_signals
from ..strategy_utils import make_signal_df
from .sizing import position_sizing as tsvf_position_sizing
from .backtesting import simulate_portfolio
from .backtest import backtest as run_backtest
from .risk import (
    detect_pulso_transcendencia,
    get_cached_dynamic_risk_controller,
    update_entropy_history,
    update_rolling_sharpe_ratios,
    calculate_dynamic_weights,
)
from .risk_utils import atr_stop_take_profit
from ...utils.timeframe import timeframe_to_minutes


from ...strategies.exceptions import (
    DataRequirementsError,
    InsufficientHistoryError,
)

AnalysisResult = Dict[str, Any]

logger = get_logger(__name__)
logger.debug(f"Módulo {__name__} importado")

# Número máximo de tentativas ao lidar com histórico insuficiente.
MAX_ATTEMPTS: int = 3


@register_strategy(
    name="NovaEstrategiaQUALIA",
    category="quantum_retrocausal",
    description=(
        "Estratégia baseada em Two-State Vector Formalism (TSVF) com meta-"
        "combinação adaptativa de sub-estratégias."
    ),
    version="1.0.0",
    tags=["tsvf", "retrocausal", "meta-strategy", "adaptive", "quantum-inspired"],
)
class QualiaTSVFStrategy(TradingStrategy):
    strategy_alias = "QualiaTSVFStrategy"

    def __init__(
        self,
        symbol: str,
        timeframe: str,
        params: QualiaTSVFParams | dict | None = None,
        shared_context: dict | None = None,
        detailed_population_log: bool = False,
        dynamic_risk_controller: Any | None = None,
        event_bus: "SimpleEventBus" | None = None,
        config_manager: ConfigManager | None = None,
        tsvf_calculator: TSVFCalculator | None = None,
        use_acceleration: bool = False,
        use_quantum_paths: bool = False,
    ) -> None:
        """Inicializa a estratégia TSVF.

        Parameters
        ----------
        symbol
            Par de trading a ser analisado.
        timeframe
            Intervalo de tempo das velas utilizadas.
        params
            Conjunto de parâmetros específicos da estratégia.
        shared_context
            Dicionário opcional para troca de informações entre estratégias.
            Quando inclui ``preload_history_callback``, a função apontada deve
            retornar ao menos ``required_initial_data_length`` candles. Se o
            retorno inicial for menor, a estratégia tentará chamadas adicionais
            até completar o mínimo ou levantará ``DataRequirementsError``.
        detailed_population_log
            Se ``True`` imprime todos os valores de ``params`` no log de
            inicialização. Com ``False`` apenas as chaves são exibidas.
        dynamic_risk_controller
            Instância opcional já inicializada de ``DynamicRiskController``.
        event_bus
            Barramento opcional para publicar eventos da estratégia.
        tsvf_calculator
            Instância reutilizável de :class:`TSVFCalculator`. Se ``None``
            uma nova é criada automaticamente.
        use_acceleration
            Quando ``True``, ativa cálculos do TSVF em GPU ou QPU, se
            disponíveis.
        use_quantum_paths
            Quando ``True`` utiliza gerador quântico de trajetórias,
            se ``qiskit`` estiver disponível.
        """
        if params is None and config_manager is not None:
            try:
                params = config_manager.get_strategy_config().get("params", {})
            except (AttributeError, KeyError, TypeError) as exc:
                logger.error("Falha ao obter parametros do ConfigManager: %s", exc)
                params = {}
            except Exception as exc:  # noqa: BLE001 - unexpected
                logger.exception("Erro inesperado ao acessar ConfigManager: %s", exc)
                raise

        if params is None:
            params_dict = {}
        elif isinstance(params, BaseModel):
            try:
                params_dict = params.model_dump()
            except AttributeError:  # pragma: no cover - pydantic v1
                params_dict = params.dict()
        elif is_dataclass(params):
            params_dict = asdict(params)
        elif isinstance(params, dict):
            params_dict = params
        else:
            raise TypeError("params must be a dict, QualiaTSVFParams or None")

        # Fornecer contexto com symbol/timeframe à classe base
        super().__init__(
            params=params_dict,
            context={"symbol": symbol, "timeframe": timeframe},
            config_manager=config_manager,
        )
        self.symbol = symbol
        self.timeframe = timeframe

        if params is None:
            self.parameters = {}
            self.params_obj = QualiaTSVFParams()
        elif isinstance(params, BaseModel):
            self.parameters = params_dict
            self.params_obj = params  # already a QualiaTSVFParams
        elif is_dataclass(params):
            self.parameters = params_dict
            self.params_obj = params
        else:  # params_dict already validated as dict
            self.parameters = params_dict
            filtered = {k: v for k, v in params_dict.items() if k != "risk_profile"}
            self.params_obj = QualiaTSVFParams(**filtered)
        self.shared_context = shared_context if shared_context is not None else {}
        self.detailed_population_log = detailed_population_log
        self.event_bus = event_bus
        self.publish_events = feature_toggle("nova_estrategia")
        self.s1_enabled = feature_toggle("s1", True)
        self.s2_enabled = feature_toggle("s2", True)
        self.s3_enabled = feature_toggle("s3", True)
        self.tsvf_cache_enabled = feature_toggle("tsvf_cache", True)
        self.last_risk_adjustment = None

        if self.event_bus is not None:
            self.event_bus.subscribe(
                "strategy.update_params", self._handle_update_params
            )
            self.event_bus.subscribe("market.alert", self._handle_market_alert)
            self.event_bus.subscribe(
                "risk.adjustment_requested", self._handle_risk_adjustment
            )

        self.preload_key = f"preloaded_candles_{self.timeframe}"
        self.use_acceleration = use_acceleration
        self.use_quantum_paths = use_quantum_paths
        self.tsvf_calculator = tsvf_calculator or TSVFCalculator(
            use_acceleration=use_acceleration
        )

        # self.name pode usar os atributos self.symbol e self.timeframe agora
        self.name = (
            f"QualiaTSVFStrategy_{self.symbol.replace('/', '_')}_{self.timeframe}"
        )
        params_to_log = (
            self.parameters
            if self.detailed_population_log
            else list(self.parameters.keys())
        )
        logger.debug(
            "Inicializando %s com parâmetros: %s",
            self.name,
            params_to_log,
        )

        # Parâmetros TSVF Globais
        self.tsvf_vector_size = self.parameters.get("tsvf_vector_size", 100)
        self.tsvf_num_subfields = self.parameters.get("tsvf_num_subfields", 4)
        self.tsvf_alpha = self.parameters.get("tsvf_alpha", 0.3)
        self.tsvf_gamma = self.parameters.get(
            "tsvf_gamma", 0.1
        )  # Conforme paper, fixo em 0.1
        self.cE = self.parameters.get("cE", 0.1)  # Coefficient for coherence E
        self.cH = self.parameters.get("cH", 0.05)  # Coefficient for entropy H
        self.coherence_threshold = self.parameters.get("coherence_threshold", 0.55)
        self.entropy_threshold = self.parameters.get("entropy_threshold", 0.4)

        # YAA: Log de verificação para depurar problema de threshold
        logger.info(
            f"🔬 Thresholds Carregados para {self.name}: "
            f"Coherence={self.coherence_threshold}, Entropy={self.entropy_threshold}"
        )

        self.otoc_delta = self.parameters.get("otoc_delta", 1)  # Delta for OTOC
        self.otoc_window = self.parameters.get("otoc_window", 168)  # Window for OTOC

        self.use_resonance = self.parameters.get("use_resonance", False)
        self.use_folding = self.parameters.get("use_folding", False)

        # Parâmetros Sub-Estratégia S1 (Baseline 1h)
        self.s1_tsvf_window = self.parameters.get("s1_tsvf_window", 24)
        self.s1_strength_threshold = self.parameters.get("s1_strength_threshold", 0.02)

        # Parâmetros Sub-Estratégia S2 (Filtro SMA+RSI 1h)
        self.s2_sma_short_period = self.parameters.get("s2_sma_short_period", 50)
        self.s2_sma_long_period = self.parameters.get("s2_sma_long_period", 100)
        self.s2_rsi_period = self.parameters.get("s2_rsi_period", 14)
        self.s2_rsi_oversold = self.parameters.get("s2_rsi_oversold", 30)
        self.s2_rsi_overbought = self.parameters.get("s2_rsi_overbought", 70)

        # Parâmetros Sub-Estratégia S3 (Baseline 4h)
        self.s3_resample_period = self.parameters.get("s3_resample_period", "4h")
        self.s3_tsvf_window = self.parameters.get(
            "s3_tsvf_window", 6
        )  # 6 períodos de 4h = 24h
        self.s3_strength_threshold = self.parameters.get("s3_strength_threshold", 0.015)

        # Parâmetros Meta-Estratégia
        self.meta_sharpe_window_hours = self.parameters.get(
            "meta_sharpe_window_hours", 168
        )  # 1 semana
        self.meta_transaction_cost = self.parameters.get(
            "meta_transaction_cost", 0.0005
        )
        self.meta_decision_threshold = self.parameters.get(
            "meta_decision_threshold", 0.1
        )

        # Estado interno para backtesting da meta-estratégia
        self.sub_strategy_returns = {
            k: deque(maxlen=self.meta_sharpe_window_hours) for k in ["s1", "s2", "s3"]
        }
        self.rolling_sharpe_ratios = {"s1": 0.0, "s2": 0.0, "s3": 0.0}
        self.weights = {"s1": 1 / 3, "s2": 1 / 3, "s3": 1 / 3}

        self.envelope = IntentionEnvelope(
            profit_target=self.parameters.get("profit_target", 5.0),
            max_drawdown=self.parameters.get("max_drawdown", 1.0),
            horizon_hours=self.parameters.get("horizon", 24),
        )
        self.retro_selector = RetroSelector()
        self.alpha_gamma_policy = AlphaGammaPolicy(
            alpha=self.tsvf_alpha,
            gamma=self.tsvf_gamma,
            window=self.parameters.get("h4_window", 20),
            weights=self.weights.copy(),
            risk_manager=self.shared_context.get("risk_manager"),
            replay_capacity=self.parameters.get("experience_replay_capacity", 2000),
        )

        # Histórico para análise de PulsoTranscendência
        self.entropy_history_window = self.parameters.get("entropy_history_window", 20)
        self.entropy_history: List[float] = []

        # Configuração do controlador de risco dinâmico
        self.dynamic_risk_controller = None
        self._dynamic_risk_controller_initialized = False

        if dynamic_risk_controller is not None:
            self.dynamic_risk_controller = dynamic_risk_controller
            self._dynamic_risk_controller_initialized = True
        else:
            try:
                cfg_obj = self.shared_context.get("qualia_config")
                cfg: Dict[str, Any] = {}
                if isinstance(cfg_obj, ConfigManager):
                    cfg = cfg_obj.data
                elif isinstance(cfg_obj, dict):
                    cfg = cfg_obj
                elif cfg_obj is not None:
                    logger.error(
                        "%s: 'qualia_config' inválido para controle de risco dinâmico",
                        self.name,
                    )

                if not cfg and config_manager is not None:
                    logger.warning(
                        "%s: 'qualia_config' ausente; aplicando valores padrão",
                        self.name,
                    )
                    cfg = config_manager.data

                default_ace_cfg = (
                    config_manager.get_ace_config() if config_manager else {}
                )
                ace_cfg = cfg.get("ace_config", {})
                enable_key = "enable_dynamic_risk_control"
                drc_key = "dynamic_risk_config"

                if not isinstance(ace_cfg, dict) or not ace_cfg:
                    logger.warning(
                        "%s: 'ace_config' ausente; utilizando padrão do YAML",
                        self.name,
                    )
                    ace_cfg = default_ace_cfg
                else:
                    missing_fields = [
                        key for key in (enable_key, drc_key) if key not in ace_cfg
                    ]
                    if missing_fields:
                        logger.warning(
                            "%s: ace_config incompleto; complementando com padrão (%s)",
                            self.name,
                            ", ".join(missing_fields),
                        )
                        for m_key in missing_fields:
                            if m_key in default_ace_cfg:
                                ace_cfg[m_key] = default_ace_cfg[m_key]

                if ace_cfg.get(enable_key, False) and drc_key in ace_cfg:
                    risk_profile = self.shared_context.get("risk_profile", "balanced")
                    dr_cfg = ace_cfg.get(drc_key, {})
                    self.dynamic_risk_controller = get_cached_dynamic_risk_controller(
                        dr_cfg,
                        risk_profile,
                    )
                else:
                    self.dynamic_risk_controller = None

                self._dynamic_risk_controller_initialized = True
            except (KeyError, ValueError, TypeError) as exc:
                logger.error(
                    "%s: erro ao inicializar DynamicRiskController: %s",
                    self.name,
                    exc,
                )
                self.dynamic_risk_controller = None
                self._dynamic_risk_controller_initialized = True
            except Exception as exc:  # noqa: BLE001 - unexpected
                logger.exception(
                    "%s: erro inesperado ao inicializar DynamicRiskController: %s",
                    self.name,
                    exc,
                )
                raise

        # --- YAA: Refatoração de Carregamento de Histórico (TASK-003) ---
        # A lógica de pré-carregamento de histórico foi centralizada no
        # DataWarmupManager e no QASTOracleDecisionEngine. Remover a lógica
        # duplicada e propensa a erros do __init__ da estratégia simplifica
        # o código e evita comportamentos conflitantes. A estratégia agora
        # confia que os dados serão injetados através do shared_context.
        # --- Fim da Refatoração ---

        # YAA: Adicionar memória interna para o histórico de dados
        self._historical_data: Optional[pd.DataFrame] = None

        self._exchange_adjusted = False
        self._operating_with_reduced_data = False
        self._actual_available_data = 0

        # Cache para a simulação de caminhos de Monte Carlo
        self._last_mc_paths: np.ndarray | None = None
        self._last_volatility: float | None = None
        self._last_horizon: int | None = None

        # Cache de estados TSVF para reuso entre janelas
        self._psi_cache: Dict[tuple[int, int, int], dict] = {}

        # --- YAA: Correção Proativa de Débito Técnico (TASK-007) ---
        # Valida e corrige o período de resample para evitar FutureWarning com Pandas.
        if hasattr(self, "s3_resample_period"):
            if self.s3_resample_period == "M":
                logger.warning(
                    f"'{self.name}': O período de resample 'M' está obsoleto e será removido "
                    f"em uma futura versão do Pandas. Corrigindo para 'ME' (Month-End). "
                    f"Considere atualizar o arquivo de configuração da sua estratégia."
                )
                self.s3_resample_period = "ME"
            elif re.match(r"^\d+m$", self.s3_resample_period):
                self.s3_resample_period = f"{self.s3_resample_period[:-1]}min"
        # --- Fim da Correção ---

        # Armazena o requisito de dados original antes de qualquer ajuste de emergência
        self._original_required_data_length: int = 0

        # YAA: Proteção de Integridade TSVF - Armazenar parâmetros originais
        self._original_tsvf_params = {
            "s1_tsvf_window": self.s1_tsvf_window,
            "s2_sma_short_period": self.s2_sma_short_period,
            "s2_sma_long_period": self.s2_sma_long_period,
            "s2_rsi_period": self.s2_rsi_period,
            "s3_resample_period": self.s3_resample_period,
            "s3_tsvf_window": self.s3_tsvf_window,
            "s1_strength_threshold": self.s1_strength_threshold,
            "s3_strength_threshold": self.s3_strength_threshold,
            "coherence_threshold": self.coherence_threshold,
            "entropy_threshold": self.entropy_threshold,
        }

        # Armazenar requisito original de dados antes de qualquer degradação
        self._required_initial_data_length_orig = None
        self._tsvf_integrity_protected = False

        # Último requisito final calculado após ajustes de limite de exchange
        self._final_required_initial_data_length: int | None = None

        # Flag para indicar que o requisito deve ser recalculado
        self._requirement_dirty: bool = True

        logger.info(
            f"{self.name}: Parâmetros TSVF originais preservados para integridade do sistema"
        )

    def _prune_psi_cache(self) -> None:
        """Remove entradas do cache fora do intervalo de dados atuais."""
        if (
            getattr(self, "_historical_data", None) is None
            or self._historical_data.empty
            or not self._psi_cache
        ):
            return
        normalized_index = pd.to_datetime(self._historical_data.index, errors="coerce")
        normalized_index = normalized_index.dropna()
        if normalized_index.empty:
            logger.error(
                "%s: Histórico sem índice válido para prune_psi_cache.",
                self.name,
            )
            return
        earliest_ts = int(normalized_index[0].timestamp())
        keys_to_remove = [k for k in self._psi_cache if k[1] < earliest_ts]
        for key in keys_to_remove:
            self._psi_cache.pop(key, None)

    def _normalize_timezone_for_concat(
        self, df1: pd.DataFrame, df2: pd.DataFrame
    ) -> tuple[pd.DataFrame, pd.DataFrame]:
        """
        Normaliza timezone de dois DataFrames antes da concatenação para evitar
        erro 'Cannot compare tz-naive and tz-aware timestamps'.


        A correção YAA-001 exige que o índice de ambos os DataFrames seja
        convertido explicitamente para o fuso horário UTC antes de qualquer
        operação. Essa etapa evita conflitos entre timestamps "naive" e
        timezone-aware durante a concatenação ou comparação de datas.

        Parameters
        ----------
        df1, df2 : pd.DataFrame
            DataFrames para normalizar

        Returns
        -------
        tuple[pd.DataFrame, pd.DataFrame]
            DataFrames com timezones normalizados para UTC
        """

        def _ensure_utc(index: pd.DatetimeIndex) -> pd.DatetimeIndex:
            """Localiza ou converte um índice para UTC."""
            tz = getattr(index, "tz", None)
            if tz is None:
                return index.tz_localize("UTC")
            if str(tz) != "UTC":
                return index.tz_convert("UTC")
            return index

        df1_copy = df1.copy()
        df2_copy = df2.copy()

        # Conversão explícita para timezone UTC
        df1_copy.index = pd.to_datetime(df1_copy.index, errors="coerce", utc=True)
        df2_copy.index = pd.to_datetime(df2_copy.index, errors="coerce", utc=True)

        if isinstance(df1_copy.index, pd.DatetimeIndex):
            df1_copy.index = _ensure_utc(df1_copy.index)
        if isinstance(df2_copy.index, pd.DatetimeIndex):
            df2_copy.index = _ensure_utc(df2_copy.index)

        return df1_copy, df2_copy

    def _consume_preloaded_history(self, preload: pd.DataFrame) -> None:
        """Merge preloaded candles into the internal history."""
        if preload is None or preload.empty:
            return

        if self._historical_data is None or self._historical_data.empty:
            preload_norm, _ = self._normalize_timezone_for_concat(preload, preload)
            self._historical_data = preload_norm
        else:
            self._historical_data, preload_norm = self._normalize_timezone_for_concat(
                self._historical_data, preload
            )
            self._historical_data = pd.concat(
                [self._historical_data, preload_norm]
            ).drop_duplicates()

        logger.info(
            "%s: Consumindo %s velas pré-carregadas do warm-up.",
            self.name,
            len(preload),
        )

    def _calculate_required_initial_data_length(self) -> int:
        """
        Calcula o histórico mínimo necessário de forma dinâmica e consciente do ambiente.
        A lógica agora considera o fator de resampling da S3, meta-indicadores e H4.
        """
        warmup_available = self.shared_context and self.shared_context.get(
            "freeze_requirements", False
        )

        min_len_s1 = self.s1_tsvf_window + 1
        min_len_s2 = max(self.s2_sma_long_period, self.s2_rsi_period)

        try:
            base_tf_minutes = timeframe_to_minutes(self.timeframe)
            if base_tf_minutes == 0:
                raise ValueError("O timeframe base não pode ser zero.")

            # Requisito da S3
            s3_tf_minutes = timeframe_to_minutes(self.s3_resample_period)
            resample_factor_s3 = s3_tf_minutes / base_tf_minutes
            if resample_factor_s3 > 60:
                required_s3 = min(
                    72, math.ceil(resample_factor_s3 * (self.s3_tsvf_window + 1))
                )
            else:
                required_s3 = math.ceil(resample_factor_s3 * (self.s3_tsvf_window + 1))

            # YAA: Requisito do Meta-Sharpe
            meta_sharpe_hours = self.parameters.get("meta_sharpe_window_hours", 168)
            if warmup_available:
                required_meta_sharpe = math.ceil(
                    (meta_sharpe_hours * 60) / base_tf_minutes
                )
            else:
                required_meta_sharpe = 0

            # YAA: Requisito do H4 - é uma janela de candles no timeframe atual
            h4_window = self.parameters.get("h4_window", 20)
            required_h4 = h4_window

            logger.debug(
                f"{self.name}: Requisitos - S3={required_s3}, MetaSharpe={required_meta_sharpe}, H4={required_h4}"
            )

        except ValueError as e:
            logger.error(f"{self.name}: Erro ao calcular requisitos: {e}")
            # Fallback conservador
            required_s3 = 500
            required_meta_sharpe = 500
            required_h4 = 500

        # O requisito final é o maior entre todas as sub-estratégias e meta-componentes
        calculated_requirement = max(
            min_len_s1,
            min_len_s2,
            required_s3,
            required_meta_sharpe,
            required_h4,
            300,
        )

        # YAA: Armazenar requisito original na primeira chamada (proteção TSVF)
        if self._required_initial_data_length_orig is None:
            self._required_initial_data_length_orig = calculated_requirement
            logger.info(
                f"{self.name}: Requisito original TSVF armazenado: {calculated_requirement} candles "
                f"(essencial para integridade do canal retrocausal)"
            )

        # Integração com a paginação do DataWarmupManager
        # Removida a limitação hardcoded: o gerenciador de warm-up fornecerá
        # todo o histórico necessário usando paginação quando disponível
        final_requirement = calculated_requirement

        previous_requirement = self._final_required_initial_data_length

        # Verificar se os dados virão de um warmup paginado
        warmup_available = self.shared_context and self.shared_context.get(
            "freeze_requirements", False
        )

        if not warmup_available:
            practical_cap = 720
            if final_requirement > practical_cap:
                final_requirement = practical_cap

        if warmup_available:
            manager = self.shared_context.get("warmup_manager")
            if manager and hasattr(manager, "max_candles_per_fetch"):
                chunk_limit = manager.max_candles_per_fetch
                estimated_pages = math.ceil(calculated_requirement / chunk_limit)
                logger.info(
                    f"📊 TASK-04: {self.name} operando com warmup paginado!"
                    f"\n   ✅ freeze_requirements=True detectado"
                    f"\n   📈 Requisito total: {calculated_requirement} candles"
                    f"\n   📦 Limite por página: {chunk_limit} candles"
                    f"\n   🔢 Páginas estimadas: {estimated_pages}"
                )
            else:
                logger.info(
                    f"📊 TASK-04: {self.name} operando com warmup paginado!"
                    f"\n   ✅ freeze_requirements=True detectado"
                    f"\n   📈 Requisito total: {calculated_requirement} candles"
                    f"\n   🚀 Dados completos serão fornecidos via paginação"
                )
            final_requirement = calculated_requirement
        else:
            # Fallback para o caso de não haver warmup - aplicar limite conservador
            manager = (
                self.shared_context.get("warmup_manager")
                if self.shared_context
                else None
            )
            exchange_limit = getattr(manager, "max_candles_per_fetch", 1500)
            if final_requirement > exchange_limit:
                if previous_requirement != exchange_limit:
                    logger.warning(
                        f"{self.name}: Sem warmup paginado disponível. Requisito de dados TSVF "
                        f"({calculated_requirement} candles) limitado a {exchange_limit} "
                        f"(limite da exchange). Para dados completos, execute com warmup ativo."
                    )
                final_requirement = exchange_limit

                # Marcar que estamos operando com dados reduzidos
                if not hasattr(self, "_exchange_adjusted"):
                    self._exchange_adjusted = True

        if final_requirement != previous_requirement:
            logger.info(
                f"{self.name}: Requisito de dados calculado: {final_requirement} candles. "
                f"(Detalhes: S1={min_len_s1}, S2={min_len_s2}, S3={required_s3}, Meta={required_meta_sharpe}, H4={required_h4})"
            )

        # Armazenar para comparações futuras
        self._final_required_initial_data_length = final_requirement

        return final_requirement

    @property
    def required_initial_data_length(self) -> int:
        """Retorna o número mínimo de velas de dados históricos."""
        if self._final_required_initial_data_length is None or self._requirement_dirty:
            self._final_required_initial_data_length = (
                self._calculate_required_initial_data_length()
            )
            self._requirement_dirty = False
        return self._final_required_initial_data_length

    def setup(self, candles: pd.DataFrame) -> None:
        """Validate initial candle history for this strategy.

        Parameters
        ----------
        candles
            DataFrame com dados OHLCV pré-carregados.

        Raises
        ------
        DataRequirementsError
            Se a quantidade de velas for insuficiente para inicialização.
        """

        have = len(candles) if isinstance(candles, pd.DataFrame) else 0

        # YAA: Verificar se estamos operando com dados reduzidos
        if (
            hasattr(self, "_operating_with_reduced_data")
            and self._operating_with_reduced_data
        ):
            if have >= self._actual_available_data:
                logger.info(
                    f"{self.name}: Operando com dados reduzidos - {have} candles disponíveis "
                    f"(originalmente precisava de {self.required_initial_data_length})"
                )
                return  # Aceitar os dados disponíveis

        # Verificar se temos dados suficientes para operação normal
        if have < self.required_initial_data_length:
            raise DataRequirementsError(
                f"{self.name}: historico insuficiente ({have}/{self.required_initial_data_length})"
            )

    def _handle_missing_history(self, available: int, required: int) -> None:
        """Signal the engine that more history is required.

        Parameters
        ----------
        available
            Number of candles currently available.
        required
            Minimum number of candles required for analysis.

        Raises
        ------
        InsufficientHistoryError
            Raised when insufficient data and auto-adjustments fail.

        NOTE(YAA-2.1): Implementação melhorada de degradação adaptativa.
        """

        # YAA: Proteção TSVF - Não degradar parâmetros se proteção estiver ativa
        if self._tsvf_integrity_protected:
            # Verificar se temos dados do warm-up no shared_context
            freeze_active = self.shared_context and self.shared_context.get(
                "freeze_requirements", False
            )
            if freeze_active:
                logger.warning(
                    f"{self.name}: Proteção TSVF ativa - mantendo integridade do canal retrocausal. "
                    f"Dados insuficientes ({available}/{required}) mas parâmetros preservados. "
                    f"Aguardando mais dados de mercado..."
                )
                raise InsufficientHistoryError(
                    f"{self.name}: Proteção TSVF ativa - histórico insuficiente mas parâmetros preservados "
                    f"({available}/{required}). Sistema aguarda mais dados."
                )

        # Inicializar contador se necessário
        if not hasattr(self, "_insufficient_history_count"):
            self._insufficient_history_count = 0

        self._insufficient_history_count += 1
        attempt = self._insufficient_history_count

        logger.info(
            f"{self.name}: historico insuficiente ({available}/{required}). tentativa {attempt}/{MAX_ATTEMPTS}"
        )

        # Enquanto não exceder o limite de tentativas, apenas sinalizar a falta de dados
        if attempt <= MAX_ATTEMPTS:
            raise InsufficientHistoryError(
                f"{self.name}: historico insuficiente ({available}/{required}). tentativa {attempt}/{MAX_ATTEMPTS}"
            )

        # Ao exceder o limite, tentar ajustes ou modo de emergência
        logger.warning(
            f"{self.name}: Ativando modo de auto-ajuste adaptativo com {available} candles"
        )
        self._auto_adjust_parameters_for_kraken(available)

        new_requirement = self._calculate_required_initial_data_length()

        if available >= new_requirement:
            logger.info(
                f"{self.name}: ✓ Auto-ajuste bem-sucedido! {available} candles são suficientes "
                f"(novo requisito: {new_requirement}, original: {required})"
            )
            self._operating_with_reduced_data = True
            self._actual_available_data = available
            self._insufficient_history_count = 0
            return

        absolute_minimum = 4
        if available >= absolute_minimum:
            logger.warning(
                f"{self.name}: Máximo de tentativas excedido. Ativando modo de emergência."
            )
            self._apply_emergency_parameters()
            self._operating_with_reduced_data = True
            self._actual_available_data = available
            self._insufficient_history_count = 0
            return

        message = (
            f"{self.name}: historico insuficiente após {attempt} tentativas "
            f"({available}/{required}). Abortando estratégia."
        )
        logger.error(message)
        raise DataRequirementsError(message)

    def _apply_emergency_parameters(self) -> None:
        """Aplicar parâmetros extremamente conservadores para modo de emergência."""
        logger.warning(f"{self.name}: Aplicando parâmetros de emergência")

        # YAA CORREÇÃO CRÍTICA: Reduzir DRASTICAMENTE todos os parâmetros
        self.s1_tsvf_window = 2  # Mínimo absoluto
        self.s2_sma_long_period = 3  # Mínimo para SMA
        self.s2_sma_short_period = 2  # Mínimo para SMA
        self.s2_rsi_period = 2  # Mínimo para RSI
        self.s3_tsvf_window = 1  # Mínimo absoluto

        # YAA: Proteção TSVF - Verificar se resampling S3 continua válido
        try:
            base_tf_minutes = timeframe_to_minutes(self.timeframe)
            emergency_s3_period = "1m"  # Período de emergência
            s3_tf_minutes = timeframe_to_minutes(emergency_s3_period)

            if s3_tf_minutes < base_tf_minutes:
                # Resampling inválido - desabilitar S3 para preservar integridade TSVF
                self.s3_enabled = False
                logger.warning(
                    f"{self.name}: S3 desabilitado em modo emergência - resampling '{emergency_s3_period}' "
                    f"menor que timeframe base '{self.timeframe}' quebraria o canal retrocausal TSVF"
                )
            else:
                self.s3_resample_period = emergency_s3_period
                logger.info(
                    f"{self.name}: S3 mantido com resampling de emergência: {emergency_s3_period}"
                )
        except Exception as e:
            # Em caso de erro, desabilitar S3 por segurança
            self.s3_enabled = False
            logger.error(
                f"{self.name}: Erro verificando resampling S3 - desabilitando S3 por segurança: {e}"
            )

        # Ajustar thresholds para serem mais conservadores
        self.s1_strength_threshold = max(0.001, self.s1_strength_threshold * 0.5)
        self.s3_strength_threshold = max(0.001, self.s3_strength_threshold * 0.5)

        logger.info(
            f"{self.name}: Parâmetros de emergência aplicados - "
            f"S3 {'habilitado' if self.s3_enabled else 'desabilitado'} para preservar TSVF"
        )

        # Forçar recálculo do requisito mínimo de histórico
        self._requirement_dirty = True

    def _make_tsvf_state(self, price_segment: np.ndarray) -> np.ndarray:
        """Return a normalized TSVF state vector."""
        return self.tsvf_calculator.make_state(price_segment, self.tsvf_vector_size)

    def _calculate_tsvf_outputs(
        self,
        prices_in: np.ndarray,
        prices_fin: np.ndarray,
        k_components: int = 5,
        *,
        envelope: IntentionEnvelope | None = None,
        mc_paths: np.ndarray | None = None,
        retro_selector: RetroSelector | None = None,
        anacronism_steps: int | None = None,
        start_ts: int | None = None,
        end_ts: int | None = None,
    ) -> dict:
        """Calcula as saídas do TSVF.

        Inclui strength, Coerência (E), Entropia (H) e Emissão Entrópica.
        O vetor resultante é deslocado por :func:`apply_anacronism`.
        """
        return self.tsvf_calculator.compute_outputs(
            prices_in,
            prices_fin,
            self.tsvf_vector_size,
            self.tsvf_alpha,
            self.tsvf_gamma,
            self.cE,
            self.cH,
            k_components,
            envelope,
            mc_paths,
            retro_selector,
            anacronism_steps,
            use_resonance=self.use_resonance,
            resonance_strength=None,
            use_folding=self.use_folding,
            use_quantum_paths=self.use_quantum_paths,
            start_ts=start_ts,
            end_ts=end_ts,
            psi_cache=self._psi_cache if self.tsvf_cache_enabled else None,
            num_subfields=self.tsvf_num_subfields,
        )

    def _compute_financial_otoc(self, returns: pd.Series) -> float:
        """
        Calcula o análogo de OTOC financeiro.
        C(t) = média sobre a janela de (r(t)*r(t+delta))**2
        Retorna o valor mais recente do OTOC.
        """
        return self.tsvf_calculator.compute_otoc(
            returns, self.otoc_delta, self.otoc_window
        )

    def _calculate_s1_position(self, tsvf_strength: float) -> int:
        """Map TSVF strength to a discrete S1 trading position.

        Args:
            tsvf_strength: Strength metric from TSVF computation.

        Returns:
            ``1`` for long, ``-1`` for short or ``0`` to hold.
        """
        return calculate_s1_position(tsvf_strength, self.s1_strength_threshold)

    def _calculate_s2_position(
        self, s1_position: int, current_price_series: pd.Series
    ) -> int:
        """Determine S2 position using SMA and RSI filters over S1 signal.

        Args:
            s1_position: Position suggested by the S1 sub-strategy.
            current_price_series: Series of prices used to compute indicators.

        Returns:
            ``1`` for long, ``-1`` for short or ``0`` when conditions are not met.
        """
        if s1_position == 0:
            return 0

        return calculate_s2_position(
            s1_position,
            current_price_series,
            self.s2_sma_short_period,
            self.s2_sma_long_period,
            self.s2_rsi_period,
            self.s2_rsi_oversold,
            self.s2_rsi_overbought,
        )

    def _calculate_s3_position(self, tsvf_strength_4h: float) -> int:
        """Compute S3 position based on TSVF strength from 4h data."""
        return calculate_s3_position(tsvf_strength_4h, self.s3_strength_threshold)

    def _update_rolling_sharpe_ratios(self, W_sr_periods: int):
        """Delegate to :func:`update_rolling_sharpe_ratios`."""
        update_rolling_sharpe_ratios(self, W_sr_periods)

    def _calculate_dynamic_weights(self):
        """Delegate to :func:`calculate_dynamic_weights`."""
        calculate_dynamic_weights(self)

    def _update_entropy_history(self, entropy_value: float) -> None:
        """Delegate to :func:`update_entropy_history`."""
        update_entropy_history(self, entropy_value)

    def _detect_pulso_transcendencia(self) -> bool:
        """Delegate to :func:`detect_pulso_transcendencia`."""
        return detect_pulso_transcendencia(self)

    def _generate_signals(self, data_1h: pd.DataFrame) -> dict:
        """Gera sinais e retornos das sub-estratégias em base 1h."""
        return generate_signals(self, data_1h)

    def _update_weights_for_step(self, step: int) -> dict:
        """Atualiza os pesos dinâmicos de cada sub-estratégia."""
        return update_weights_for_step(self, step)

    def _simulate_portfolio(
        self,
        positions_s1: np.ndarray,
        positions_s2: np.ndarray,
        positions_s3: np.ndarray,
        market_returns: np.ndarray,
        initial_capital: float,
    ) -> dict:
        """Simula o portfólio combinado e calcula métricas de performance."""
        return simulate_portfolio(
            self,
            positions_s1,
            positions_s2,
            positions_s3,
            market_returns,
            initial_capital,
        )

    def get_parameters(self) -> dict:
        return {
            "tsvf_vector_size": self.tsvf_vector_size,
            "tsvf_alpha": self.tsvf_alpha,
            "tsvf_gamma": self.tsvf_gamma,
            "s1_tsvf_window": self.s1_tsvf_window,
            "s1_strength_threshold": self.s1_strength_threshold,
            "s2_sma_short_period": self.s2_sma_short_period,
            "s2_sma_long_period": self.s2_sma_long_period,
            "s2_rsi_period": self.s2_rsi_period,
            "s2_rsi_oversold": self.s2_rsi_oversold,
            "s2_rsi_overbought": self.s2_rsi_overbought,
            "s3_resample_period": self.s3_resample_period,
            "s3_tsvf_window": self.s3_tsvf_window,
            "s3_strength_threshold": self.s3_strength_threshold,
            "meta_sharpe_window_hours": self.meta_sharpe_window_hours,
            "meta_transaction_cost": self.meta_transaction_cost,
            "meta_decision_threshold": self.meta_decision_threshold,
            "cE": self.cE,  # Included coherence coefficient
            "cH": self.cH,  # Included entropy coefficient
            "coherence_threshold": self.coherence_threshold,
            "entropy_threshold": self.entropy_threshold,
            "otoc_delta": self.otoc_delta,  # Included OTOC delta
            "otoc_window": self.otoc_window,  # Included OTOC window
            "entropy_history_window": self.entropy_history_window,
        }

    def get_template_config(self) -> dict:
        """
        Retorna a configuração template da estratégia para o QAST.
        """
        return {
            "class_name": self.__class__.__name__,
            "symbol": self.symbol,
            "timeframe": self.timeframe,
            "params": self.get_parameters(),  # Reutiliza o método existente
        }

    # Métodos auxiliares para integração com a QASTEvolutionaryStrategy
    def get_base_strategy(self) -> dict:
        """Retorna o template usado como base para evolução."""
        return self.get_template_config()

    def get_params(self) -> dict:
        """Alias compatível para ``get_parameters``."""
        return self.get_parameters()

    def set_params(self, params: dict) -> None:
        """Alias compatível para ``set_parameters``."""
        self.set_parameters(params)

    def _preprocess_historical_data(
        self, market_data: pd.DataFrame
    ) -> tuple[pd.DataFrame, pd.Series, np.ndarray, np.ndarray]:
        """Realiza o pré-processamento do histórico de mercado.

        Recebe o histórico consolidado e atualizado de `analyze_market` e
        gera os caminhos de Monte Carlo usados pelas subestratégias.

        Parameters
        ----------
        market_data
            DataFrame de velas COMPLETO e já consolidado.

        Returns
        -------
        tuple
            ``(data, close_series, close_values, mc_paths)`` onde ``data`` é o
            histórico consolidado.
        """
        tracer = get_tracer(__name__)
        with tracer.start_as_current_span("strategy.preprocess_historical_data"):
            # --- YAA: Refatoração de Carregamento de Histórico (TASK-003) ---
            # A lógica de carregar/combinar/popping do `shared_context` foi removida.
            # Este método agora confia que `market_data` é o histórico completo.
            data = market_data

            # A validação do histórico agora é feita exclusivamente em `analyze_market`
            # antes desta função ser chamada.
            # --- Fim da Refatoração ---

            if len(data) < MIN_INITIAL_HISTORY:
                self._handle_missing_history(len(data), MIN_INITIAL_HISTORY)

            close_series = data["close"]
            close_values = close_series.values
            returns_hist = close_series.pct_change().dropna()
            vol_window = self.parameters.get("vol_window", 20)
            recent_vol = (
                returns_hist[-vol_window:].std() if len(returns_hist) > 0 else 0.0
            )

            horizon = self.envelope.horizon_hours
            use_cache = (
                self._last_mc_paths is not None
                and self._last_volatility is not None
                and self._last_horizon is not None
            )
            if use_cache:
                vol_change = abs(float(recent_vol) - float(self._last_volatility))
                allowed_vol_diff = abs(self._last_volatility) * 0.05
                horizon_same = horizon == self._last_horizon
                if vol_change <= allowed_vol_diff and horizon_same:
                    mc_paths = self._last_mc_paths
                else:
                    try:
                        mc_paths = simulate_returns(
                            mu=0.0,
                            sigma=float(recent_vol),
                            horizon=horizon,
                            num_paths=100,
                            parallel=self.use_acceleration,
                        )
                    except (ValueError, TypeError) as exc:
                        logger.error(
                            "%s: erro ao gerar caminhos de Monte Carlo: %s",
                            self.name,
                            exc,
                        )
                        raise
                    self._last_mc_paths = mc_paths
                    self._last_volatility = float(recent_vol)
                    self._last_horizon = horizon
            else:
                try:
                    mc_paths = simulate_returns(
                        mu=0.0,
                        sigma=float(recent_vol),
                        horizon=horizon,
                        num_paths=100,
                        parallel=self.use_acceleration,
                    )
                except (ValueError, TypeError) as exc:
                    logger.error(
                        "%s: erro ao gerar caminhos de Monte Carlo: %s",
                        self.name,
                        exc,
                    )
                    raise
                self._last_mc_paths = mc_paths
                self._last_volatility = float(recent_vol)
                self._last_horizon = horizon

            return data, close_series, close_values, mc_paths

    def _run_s1(
        self, close_values: np.ndarray, mc_paths: np.ndarray
    ) -> tuple[int, dict]:
        """Execute a subestratégia S1.

        Parameters
        ----------
        close_values
            Série de preços de fechamento na resolução principal.
        mc_paths
            Caminhos de Monte Carlo para projeção de retornos.

        Returns
        -------
        tuple
            ``(position_s1, tsvf_outputs_s1)`` onde ``position_s1`` é a posição
            sugerida e ``tsvf_outputs_s1`` contém as métricas TSVF calculadas.
        """

        tracer = get_tracer(__name__)
        with tracer.start_as_current_span("strategy.run_s1") as span:
            position_s1 = 0
            tsvf_outputs_s1 = {
                "strength": np.nan,
                "E": np.nan,
                "H": np.nan,
                "emitted": np.nan,
            }
            if span is not None:
                span.set_attribute("window", self.s1_tsvf_window)

            min_len_s1 = self.s1_tsvf_window + 1
            if len(close_values) >= min_len_s1:
                prices_in = close_values[-(self.s1_tsvf_window + 1) : -1]
                prices_fin = close_values[-self.s1_tsvf_window :]
                if (
                    len(prices_in) == self.s1_tsvf_window
                    and len(prices_fin) == self.s1_tsvf_window
                ):
                    start_dt = pd.to_datetime(
                        self._historical_data.index[-(self.s1_tsvf_window + 1)],
                        errors="coerce",
                    )
                    end_dt = pd.to_datetime(
                        self._historical_data.index[-1], errors="coerce"
                    )
                    if start_dt is pd.NaT or end_dt is pd.NaT:
                        logger.error(
                            "%s: Índice inválido (NaT) ao calcular timestamp para TSVF S1.",
                            self.name,
                        )
                    else:
                        start_ts = int(start_dt.timestamp())
                        end_ts = int(end_dt.timestamp())
                        tsvf_outputs_s1 = self._calculate_tsvf_outputs(
                            prices_in,
                            prices_fin,
                            envelope=self.envelope,
                            mc_paths=mc_paths,
                            retro_selector=self.retro_selector,
                            start_ts=start_ts,
                            end_ts=end_ts,
                        )
                    sub_hist_s1 = self.shared_context.setdefault(
                        "tsvf_subfields_s1", []
                    )
                    sub_hist_s1.append(tsvf_outputs_s1.get("subfields", []))

                strength_hist = self.shared_context.setdefault("strength_history", [])
                strength_hist.append(tsvf_outputs_s1["strength"])
                if len(strength_hist) > 0:
                    percentile = self.parameters.get("strength_percentile", 80)
                    pct = np.percentile(np.abs(strength_hist[-1000:]), percentile)
                    # YAA: Correção crítica - threshold dinâmico muito restritivo
                    # Usar o menor entre percentil e threshold original para evitar bloqueio
                    original_threshold = self.parameters.get(
                        "s1_strength_threshold", 0.02
                    )
                    self.s1_strength_threshold = float(
                        min(pct, original_threshold * 2.0)
                    )

                    logger.debug(
                        f"{self.name}: Threshold S1 ajustado: percentil={pct:.6f}, "
                        f"original={original_threshold:.6f}, usado={self.s1_strength_threshold:.6f}"
                    )

                self.alpha_gamma_policy.update(
                    {
                        "eis": float(tsvf_outputs_s1.get("emitted", 0.0)),
                        "reward": float(tsvf_outputs_s1.get("strength", 0.0)),
                    }
                )
                policy_params = self.alpha_gamma_policy.get_params()
                self.tsvf_alpha = policy_params["alpha"]
                self.tsvf_gamma = policy_params["gamma"]
                self.weights = policy_params["weights"]

                h_hist = self.shared_context.setdefault("H_history", [])
                h_hist.append(tsvf_outputs_s1["H"])
                h4_window = self.parameters.get("h4_window", 20)
                if len(h_hist) >= h4_window:
                    recent_h = h_hist[-h4_window:]
                    std_h = np.std(recent_h)
                    if std_h > 1e-9:
                        z = (tsvf_outputs_s1["H"] - np.mean(recent_h)) / std_h
                        if z < -3:
                            _ = True

                # YAA: Debug detalhado para diagnóstico
                logger.debug(
                    f"🔬 S1 Decision - E={tsvf_outputs_s1['E']:.4f} vs threshold={self.coherence_threshold}, "
                    f"H={tsvf_outputs_s1['H']:.4f} vs threshold={self.entropy_threshold}, "
                    f"strength={tsvf_outputs_s1['strength']:.4f}, s1_threshold={self.s1_strength_threshold}"
                )

                if (
                    tsvf_outputs_s1["E"] >= self.coherence_threshold
                    and tsvf_outputs_s1["H"] <= self.entropy_threshold
                ):
                    position_s1 = self._calculate_s1_position(
                        tsvf_outputs_s1["strength"]
                    )
                    logger.debug(
                        f"🎯 S1 Position calculated: {position_s1} from strength {tsvf_outputs_s1['strength']:.4f}"
                    )
                else:
                    position_s1 = 0
                    logger.debug(
                        "❌ S1 Position set to 0 - conditions not met (E or H threshold)"
                    )
                self._update_entropy_history(tsvf_outputs_s1["H"])

            if span is not None:
                span.set_attribute(
                    "strength", float(tsvf_outputs_s1.get("strength", np.nan))
                )
                span.set_attribute("E", float(tsvf_outputs_s1.get("E", np.nan)))
                span.set_attribute("H", float(tsvf_outputs_s1.get("H", np.nan)))
                span.set_attribute(
                    "emitted", float(tsvf_outputs_s1.get("emitted", np.nan))
                )

            return position_s1, tsvf_outputs_s1

    def _run_s2(self, position_s1: int, close_series: pd.Series) -> int:
        """Execute a subestratégia S2.

        Parameters
        ----------
        position_s1
            Posição resultante da subestratégia S1.
        close_series
            Série de preços utilizada para indicadores.

        Returns
        -------
        int
            Posição sugerida pela S2.
        """

        tracer = get_tracer(__name__)
        with tracer.start_as_current_span("strategy.run_s2") as span:
            if span is not None:
                span.set_attribute("sma_short", self.s2_sma_short_period)
                span.set_attribute("sma_long", self.s2_sma_long_period)
                span.set_attribute("rsi_period", self.s2_rsi_period)
                span.set_attribute("input_position", position_s1)

            result = self._calculate_s2_position(position_s1, close_series)
            if span is not None:
                span.set_attribute("position", result)
            return result

    def _run_s3(self, data: pd.DataFrame, mc_paths: np.ndarray) -> tuple[int, dict]:
        """Execute a subestratégia S3.

        Parameters
        ----------
        data
            Histórico de mercado já pré-processado.
        mc_paths
            Caminhos de Monte Carlo para projeção de retornos.

        Returns
        -------
        tuple
            ``(position_s3, tsvf_outputs_s3)`` onde ``position_s3`` é a posição
            sugerida e ``tsvf_outputs_s3`` contém as métricas TSVF da janela de
            4h.
        """

        tracer = get_tracer(__name__)
        with tracer.start_as_current_span("strategy.run_s3") as span:
            position_s3 = 0
            tsvf_outputs_s3 = {
                "strength": np.nan,
                "E": np.nan,
                "H": np.nan,
                "emitted": np.nan,
            }
            if span is not None:
                span.set_attribute("window", self.s3_tsvf_window)
                span.set_attribute("resample_period", self.s3_resample_period)

            # --- YAA: Correção Proativa de Débito Técnico (TASK-007) ---
            # Valida e corrige o período de resample para evitar FutureWarning
            if self.s3_resample_period == "M":
                logger.warning(
                    f"'{self.name}': O período de resample 'M' está obsoleto. "
                    f"Corrigindo para 'ME' (Month-End). "
                    f"Atualize sua configuração."
                )
                self.s3_resample_period = "ME"
            # --- Fim da Correção ---

            if "timestamp" not in data.columns and isinstance(
                data.index, pd.DatetimeIndex
            ):
                data_for_s3 = data
                data_for_s3.loc[:, "timestamp"] = data_for_s3.index
            elif "timestamp" in data.columns:
                data_for_s3 = data
            else:
                logger.warning(
                    f"{self.name}: DataFrame não possui 'timestamp' nem DatetimeIndex para S3."
                )
                data_for_s3 = None

            if data_for_s3 is not None:
                if not isinstance(data_for_s3.index, pd.DatetimeIndex):
                    data_for_s3.index = pd.to_datetime(
                        data_for_s3["timestamp"], errors="coerce"
                    )
                    data_for_s3 = data_for_s3[~data_for_s3.index.isna()]

                resample_factor = pd.Timedelta(self.s3_resample_period) / pd.Timedelta(
                    self.timeframe
                )
                required_candles_for_s3 = math.ceil(
                    resample_factor * (self.s3_tsvf_window + 1)
                )
                num_candles_1h = len(data_for_s3)

                if num_candles_1h < required_candles_for_s3:
                    self._handle_missing_history(
                        num_candles_1h, required_candles_for_s3
                    )
                else:
                    if self.s3_resample_period == self.timeframe:
                        prices_4h_series = data_for_s3["close"]
                        logger.debug(
                            f"{self.name}: Usando dados diretos sem resample (período igual)"
                        )
                    else:
                        prices_4h_series = (
                            data_for_s3["close"]
                            .resample(self.s3_resample_period)
                            .last()
                            .dropna()
                        )

                    # YAA: Garantir que `prices_4h_series` é uma Series para evitar TypeError
                    if not isinstance(prices_4h_series, pd.Series):
                        prices_4h_series = pd.Series(prices_4h_series)

                    if len(prices_4h_series) < self.s3_tsvf_window + 1:
                        self._handle_missing_history(
                            len(prices_4h_series), self.s3_tsvf_window + 1
                        )
                    else:
                        prices_4h_values = prices_4h_series.values
                        prices_in = prices_4h_values[-(self.s3_tsvf_window + 1) : -1]
                        prices_fin = prices_4h_values[-self.s3_tsvf_window :]

                        if (
                            len(prices_in) == self.s3_tsvf_window
                            and len(prices_fin) == self.s3_tsvf_window
                        ):
                            start_dt = pd.to_datetime(
                                prices_4h_series.index[-(self.s3_tsvf_window + 1)],
                                errors="coerce",
                            )
                            end_dt = pd.to_datetime(
                                prices_4h_series.index[-1], errors="coerce"
                            )
                            if start_dt is pd.NaT or end_dt is pd.NaT:
                                logger.error(
                                    f"{self.name}: Índice inválido (NaT) ao calcular timestamp para TSVF S3."
                                )
                            else:
                                start_ts = int(start_dt.timestamp())
                                end_ts = int(end_dt.timestamp())
                                tsvf_outputs_s3 = self._calculate_tsvf_outputs(
                                    prices_in,
                                    prices_fin,
                                    envelope=self.envelope,
                                    mc_paths=mc_paths,
                                    retro_selector=self.retro_selector,
                                    start_ts=start_ts,
                                    end_ts=end_ts,
                                )
                                sub_hist_s3 = self.shared_context.setdefault(
                                    "tsvf_subfields_s3", []
                                )
                                sub_hist_s3.append(tsvf_outputs_s3.get("subfields", []))
                                if (
                                    tsvf_outputs_s3["E"] >= self.coherence_threshold
                                    and tsvf_outputs_s3["H"] <= self.entropy_threshold
                                ):
                                    position_s3 = self._calculate_s3_position(
                                        tsvf_outputs_s3["strength"]
                                    )

            if span is not None:
                span.set_attribute(
                    "strength", float(tsvf_outputs_s3.get("strength", np.nan))
                )
                span.set_attribute("E", float(tsvf_outputs_s3.get("E", np.nan)))
                span.set_attribute("H", float(tsvf_outputs_s3.get("H", np.nan)))
                span.set_attribute(
                    "emitted", float(tsvf_outputs_s3.get("emitted", np.nan))
                )
                span.set_attribute("position", position_s3)

            return position_s3, tsvf_outputs_s3

    def _calculate_substrategies(
        self,
        data: pd.DataFrame,
        close_series: pd.Series,
        close_values: np.ndarray,
        mc_paths: np.ndarray,
    ) -> tuple[int, int, int, dict, dict]:
        """Executa o cálculo das subestratégias S1, S2 e S3."""

        default_metrics = {
            "strength": np.nan,
            "E": np.nan,
            "H": np.nan,
            "emitted": np.nan,
        }

        position_s1, tsvf_outputs_s1 = 0, default_metrics.copy()
        if self.s1_enabled:
            position_s1, tsvf_outputs_s1 = self._run_s1(close_values, mc_paths)

        position_s2 = 0
        if self.s2_enabled:
            position_s2 = self._run_s2(position_s1, close_series)

        position_s3, tsvf_outputs_s3 = 0, default_metrics.copy()
        if self.s3_enabled:
            position_s3, tsvf_outputs_s3 = self._run_s3(data, mc_paths)

        # YAA: Atualizar histórico de retornos das sub-estratégias (movido de _run_s3)
        step_return = 0.0
        if len(close_series) >= 2:
            step_return = float(close_series.iloc[-1] / close_series.iloc[-2] - 1)

        self.sub_strategy_returns["s1"].append(float(position_s1) * step_return)
        self.sub_strategy_returns["s2"].append(float(position_s2) * step_return)
        self.sub_strategy_returns["s3"].append(float(position_s3) * step_return)

        self._update_rolling_sharpe_ratios(self.meta_sharpe_window_hours)
        self._calculate_dynamic_weights()

        return position_s1, position_s2, position_s3, tsvf_outputs_s1, tsvf_outputs_s3

    def _combine_signals(
        self,
        position_s1: int,
        position_s2: int,
        position_s3: int,
        close_values: np.ndarray,
        tsvf_outputs_s1: dict,
        tsvf_outputs_s3: dict,
        data: pd.DataFrame,
    ) -> dict:
        """Combine sub-strategy signals and aplicar controles de risco via ATR.

        Esta rotina usa ``calculate_sl_tp_multipliers`` e
        ``atr_stop_take_profit`` de
        :mod:`qualia.strategies.nova_estrategia_qualia.risk_utils`. Esses
        utilitários definem ``atr_period=14`` e calculam ``sl_multiplier`` como
        ``3.0 * (1.0 - min(confidence, 0.5))``—parâmetros obtidos nos primeiros
        backtests da estratégia.

        Parameters
        ----------
        position_s1, position_s2, position_s3
            Sinais brutos das subestratégias.
        close_values
            Fechamentos mais recentes usados no cálculo do ATR.
        tsvf_outputs_s1, tsvf_outputs_s3
            Métricas do TSVF geradas por S1 e S3.
        data
            ``DataFrame`` consolidado para análise.

        Returns
        -------
        dict
            Informação consolidada com ``signal`` final, ``confidence`` e
            parâmetros de risco.
        """

        market_returns = data["close"].pct_change().fillna(0)
        otoc_value = self._compute_financial_otoc(market_returns)
        if np.isnan(otoc_value):
            otoc_value = 0.0

        w = self.weights
        combined_position_raw = (
            w.get("s1", 0) * position_s1
            + w.get("s2", 0) * position_s2
            + w.get("s3", 0) * position_s3
        )

        # LOG DE OBSERVAÇÃO
        logger.info(
            f"🔬 OBSERVAÇÃO DE SINAL (Estratégia) para {self.symbol}: "
            f"Convicção Bruta = {combined_position_raw:.4f}"
        )

        final_signal_val = 0
        decision_threshold = self.meta_decision_threshold
        if combined_position_raw > decision_threshold:
            final_signal_val = 1
        elif combined_position_raw < -decision_threshold:
            final_signal_val = -1

        decision_signal = "hold"
        if final_signal_val == 1:
            decision_signal = "buy"
        elif final_signal_val == -1:
            decision_signal = "sell"

        confidence = abs(combined_position_raw)

        current_price = close_values[-1] if len(close_values) > 0 else None
        stop_loss_price = None
        take_profit_price = None

        if current_price is not None and decision_signal != "hold":
            stop_loss_price, take_profit_price = atr_stop_take_profit(
                close_values,
                decision_signal,
                confidence,
                self.meta_transaction_cost,
            )

        pulso_transcendencia = self._detect_pulso_transcendencia()

        if current_price is not None and decision_signal != "hold":
            controller = getattr(self, "dynamic_risk_controller", None)
            if controller is not None:
                try:
                    calib = controller.calibrate_risk_levels(
                        self.symbol,
                        data,
                        current_price,
                        position_side="long" if decision_signal == "buy" else "short",
                        confidence_level=float(confidence),
                    )
                    sl = calib.stop_loss_price
                    tp = calib.take_profit_price
                    sl, tp = controller.adjust_stop_take_profit(
                        sl,
                        tp,
                        current_price,
                        tsvf_outputs_s1.get("H", 0.0),
                        otoc_value,
                        symbol=self.symbol,
                    )
                    stop_loss_price = sl
                    take_profit_price = tp
                except (ValueError, KeyError, TypeError) as e:
                    logger.error(
                        "%s: erro ao usar DynamicRiskController: %s", self.name, e
                    )
                except Exception as e:  # noqa: BLE001 - unexpected
                    logger.exception(
                        "%s: erro inesperado ao usar DynamicRiskController: %s",
                        self.name,
                        e,
                    )
                    raise

        reasons = [
            (
                f"S1_pos: {position_s1} "
                f"(str: {tsvf_outputs_s1['strength']:.2f}, "
                f"E: {tsvf_outputs_s1['E']:.2f}, "
                f"H: {tsvf_outputs_s1['H']:.2f}, "
                f"emit: {tsvf_outputs_s1['emitted']:.2f})"
            ),
            f"S2_pos: {position_s2}",
            (
                f"S3_pos: {position_s3} "
                f"(str: {tsvf_outputs_s3['strength']:.2f}, "
                f"E: {tsvf_outputs_s3['E']:.2f}, "
                f"H: {tsvf_outputs_s3['H']:.2f}, "
                f"emit: {tsvf_outputs_s3['emitted']:.2f})"
            ),
            (
                f"Weights: S1({w.get('s1', 0):.2f}), "
                f"S2({w.get('s2', 0):.2f}), "
                f"S3({w.get('s3', 0):.2f})"
            ),
            f"Combined_pos_raw: {combined_position_raw:.3f}",
            f"OTOC: {otoc_value:.4f}",
        ]

        result = {
            "signal": decision_signal,
            "confidence": min(confidence, 1.0),
            "stop_loss": stop_loss_price,
            "take_profit": take_profit_price,
            "price": current_price,
            "reasons": reasons,
            "tsvf_strength_s1": tsvf_outputs_s1["strength"],
            "tsvf_E_s1": tsvf_outputs_s1["E"],
            "tsvf_H_s1": tsvf_outputs_s1["H"],
            "tsvf_emitted_s1": tsvf_outputs_s1["emitted"],
            "tsvf_strength_s3": tsvf_outputs_s3["strength"],
            "tsvf_E_s3": tsvf_outputs_s3["E"],
            "tsvf_H_s3": tsvf_outputs_s3["H"],
            "tsvf_emitted_s3": tsvf_outputs_s3["emitted"],
            "position_s1": position_s1,
            "position_s2": position_s2,
            "position_s3": position_s3,
            "combined_position_raw": combined_position_raw,
            "weights_used": self.weights.copy(),
            "otoc": otoc_value,
            "pulso_transcendencia": pulso_transcendencia,
        }

        if self.event_bus and self.publish_events:
            self.event_bus.publish(
                "strategy.signal",
                {
                    "strategy": self.name,
                    "signal": result["signal"],
                    "confidence": result["confidence"],
                    "price": result.get("price"),
                },
            )

        return result

    def analyze_market(
        self,
        market_data: pd.DataFrame,
        common_indicators: dict | None = None,
        quantum_metrics: dict | None = None,
        trading_context: TradingContext | None = None,
        *,
        context: dict | None = None,
        similar_past_patterns: list | None = None,
    ) -> AnalysisResult:
        """Análise principal do mercado para a estratégia.

        Este método é o coração da estratégia, onde os dados de mercado são
        processados, as sub-estratégias são executadas e os sinais são
        combinados.

        NOTE(YAA-003): Lógica de carregamento de histórico refatorada e
        centralizada no início deste método para garantir um estado consistente
        e eliminar duplicação de código.

        Parameters
        ----------
        market_data
            DataFrame contendo os dados de mercado mais recentes (novas velas).
        common_indicators
            Indicadores técnicos comuns pré-calculados.
        quantum_metrics
            Métricas quânticas (Coerência, Entropia, etc.).
        trading_context
            Contexto de trading, como posições abertas e capital.
        context
            Dicionário de contexto compartilhado entre estratégias.
        similar_past_patterns
            Padrões passados similares da QuantumPatternMemory.

        Returns
        -------
        AnalysisResult
            Dicionário contendo o sinal final, confiança e dados de risco.
        """
        logger.debug(
            f"{self.name}: Iniciando analyze_market com {len(market_data) if market_data is not None else 0} novas velas."
        )

        if self.shared_context:
            logger.info(
                f"{self.name}: DEBUG - shared_context exists: {self.shared_context is not None}"
            )
            if "risk_manager" in self.shared_context:
                logger.info(
                    f"{self.name}: DEBUG - shared_context keys: {list(self.shared_context.keys())}"
                )

        # --- YAA: Refatoração de Carregamento de Histórico (TASK-003) ---
        # 1. Obter dados pré-carregados, se houver
        preloaded_data = None
        if self.shared_context and self.preload_key in self.shared_context:
            logger.debug(
                f"{self.name}: DEBUG - looking for preload_key: '{self.preload_key}'"
            )
            preloaded_data = self.shared_context.pop(self.preload_key, None)
            if preloaded_data is not None:
                logger.debug(
                    f"{self.name}: DEBUG - found preloaded data: {len(preloaded_data)} candles"
                )

        # 2. Inicializar histórico e consumir dados pré-carregados
        if preloaded_data is not None:
            self._consume_preloaded_history(preloaded_data)

        if self._historical_data is None:
            self._historical_data = market_data
            if self._historical_data is not None:
                logger.debug(
                    f"{self.name}: DEBUG - initial historical data set: {len(self._historical_data)} candles"
                )

        # 3. Anexar os dados de mercado mais recentes
        if (
            self._historical_data is not None
            and market_data is not None
            and not market_data.empty
        ):
            self._historical_data, market_data_norm = (
                self._normalize_timezone_for_concat(self._historical_data, market_data)
            )
            self._historical_data = pd.concat(
                [self._historical_data, market_data_norm]
            ).drop_duplicates()
            logger.debug(
                f"{self.name}: DEBUG - Dados anexados ao histórico existente: {len(self._historical_data)} candles"
            )

        # Agora, `self._historical_data` é a fonte da verdade.
        if self._historical_data is None or self._historical_data.empty:
            logger.warning(f"{self.name}: Histórico de dados vazio após consolidação.")
            return {"signal": 0, "confidence": 0.0}

        analysis_df = self._historical_data.copy()

        # Proteção contra requisito original
        original_req = self._required_initial_data_length_orig
        is_protected = self.shared_context.get("freeze_requirements", False)

        logger.debug(
            f"{self.name}: Proteção TSVF ativa - usando requisito máximo: {original_req} (atual: {self.required_initial_data_length}, original: {self._required_initial_data_length_orig})"
        )

        # --- Validação do Histórico ---
        logger.debug(
            f"{self.name}: DEBUG - Validação: dados={len(analysis_df)}, requisito={self.required_initial_data_length}, proteção_TSVF={is_protected}"
        )

        # YAA: Limpar cache TSVF se parâmetros foram alterados para evitar interferência
        if hasattr(self, "_last_tsvf_params"):
            current_params = (self.tsvf_alpha, self.tsvf_gamma, self.tsvf_vector_size)
            if current_params != self._last_tsvf_params:
                if hasattr(self, "psi_cache"):
                    self.psi_cache.clear()
                    logger.debug(
                        f"{self.name}: Cache TSVF limpo devido a mudança de parâmetros"
                    )
        self._last_tsvf_params = (
            self.tsvf_alpha,
            self.tsvf_gamma,
            self.tsvf_vector_size,
        )
        if not self._validate_historical_data_length(analysis_df):
            # A validação agora levanta exceção se falhar. O chamador (loop de execução)
            # deve tratar InsufficientHistoryError e pular esta iteração.
            logger.warning(f"{self.name}: Validação de dados falhou, pulando análise.")
            # Retornar um resultado neutro em vez de deixar a execução falhar
            return {"signal": 0, "confidence": 0.0, "reason": "Insufficient history"}

        logger.debug(
            f"{self.name}: DEBUG - Validação PASSOU: {len(analysis_df)} >= {self.required_initial_data_length}"
        )

        logger.info(f"{self.name}.analyze_market() chamado para análise de mercado.")
        logger.debug(
            f"{self.name}: DEBUG - Após validação, analysis_df tem {len(analysis_df)} candles"
        )

        logger.debug(
            f"{self.name}: DEBUG - Requisito atual: {self.required_initial_data_length}, Requisito original: {original_req}, Proteção TSVF: {is_protected}"
        )

        (
            analysis_df,
            close_series,
            close_values,
            mc_paths,
        ) = self._preprocess_historical_data(analysis_df)

        position_s1, position_s2, position_s3, tsvf_outputs_s1, tsvf_outputs_s3 = (
            self._calculate_substrategies(
                analysis_df, close_series, close_values, mc_paths
            )
        )

        final_signal_details = self._combine_signals(
            position_s1,
            position_s2,
            position_s3,
            close_values,
            tsvf_outputs_s1,
            tsvf_outputs_s3,
            analysis_df,
        )

        return final_signal_details

    def generate_signals(self, analysis_result: AnalysisResult) -> pd.DataFrame:
        """Convert analysis results into a signal dataframe.

        Notes
        -----
        ``analysis_result`` already contains stop and profit levels computed
        using confidence-scaled multipliers. This method simply formats the
        information for downstream processing.
        """
        logger.debug(
            f"{self.name}.generate_signals() chamado com analysis_result: {analysis_result}"
        )

        # A lógica de `analyze_market` já retorna um dicionário com "signal", "confidence", etc.
        # Precisamos converter isso para um DataFrame como esperado pela interface.

        signals_df = make_signal_df(
            analysis_result.get("signal", "hold"),
            analysis_result.get("confidence", 0.0),
            price=analysis_result.get("price", np.nan),
            stop_loss=analysis_result.get("stop_loss"),
            take_profit=analysis_result.get("take_profit"),
            reasons=", ".join(analysis_result.get("reasons", [])),
        )
        signals_df.insert(0, "timestamp", pd.Timestamp.now())

        # Adicionar a coluna 'quantity' se position_sizing não for sobrescrito
        # e se a interface ou o sistema de trading esperar por ela.
        # Por padrão, a interface base adiciona 'quantity' = 0.0 se não existir.
        # signals_df["quantity"] = 0.0

        signal_repr = "N/A"
        from ...trading.data_utils import safe_iloc

        if isinstance(signals_df, pd.DataFrame):
            if not is_data_empty(signals_df):
                signal_repr = safe_iloc(signals_df, 0, "signal")
        elif isinstance(signals_df, np.ndarray) and signals_df.size > 0:
            try:
                signal_repr = signals_df[0]
            except IndexError:
                signal_repr = "N/A"
            except Exception as exc:  # noqa: BLE001 - unexpected
                logger.exception(
                    "%s: erro inesperado ao obter signal_repr: %s", self.name, exc
                )
                raise

        logger.info(f"{self.name} gerou sinal: {signal_repr}")
        return signals_df

    def position_sizing(
        self, signals: pd.DataFrame, capital: float, risk_per_trade: float
    ) -> pd.DataFrame:
        """Delegates to :func:`position_sizing` submodule."""
        return tsvf_position_sizing(self, signals, capital, risk_per_trade)

    def set_parameters(self, params: dict) -> None:
        """
        Define novos parâmetros para a estratégia.
        """
        logger.debug(
            f"{self.name}: Atualizando parâmetros de {self.parameters} para {params}"
        )
        if self.parameters is not None:
            self.parameters.update(params)
        else:
            self.parameters = params.copy()  # type: ignore

        # Reaplicar parâmetros internos que dependem de self.parameters
        # Esta parte é crucial e depende de como os parâmetros são usados internamente.
        # Por exemplo, se self.tsvf_vector_size é lido de self.parameters.
        self.tsvf_vector_size = self.parameters.get(
            "tsvf_vector_size", self.tsvf_vector_size
        )
        self.tsvf_alpha = self.parameters.get("tsvf_alpha", self.tsvf_alpha)
        self.tsvf_gamma = self.parameters.get("tsvf_gamma", self.tsvf_gamma)
        self.s1_tsvf_window = self.parameters.get("s1_tsvf_window", self.s1_tsvf_window)
        self.s1_strength_threshold = self.parameters.get(
            "s1_strength_threshold", self.s1_strength_threshold
        )
        self.s2_sma_short_period = self.parameters.get(
            "s2_sma_short_period", self.s2_sma_short_period
        )
        self.s2_sma_long_period = self.parameters.get(
            "s2_sma_long_period", self.s2_sma_long_period
        )
        self.s2_rsi_period = self.parameters.get("s2_rsi_period", self.s2_rsi_period)
        self.s2_rsi_oversold = self.parameters.get(
            "s2_rsi_oversold", self.s2_rsi_oversold
        )
        self.s2_rsi_overbought = self.parameters.get(
            "s2_rsi_overbought", self.s2_rsi_overbought
        )
        self.s3_resample_period = self.parameters.get(
            "s3_resample_period", self.s3_resample_period
        )
        self.s3_tsvf_window = self.parameters.get("s3_tsvf_window", self.s3_tsvf_window)
        self.s3_strength_threshold = self.parameters.get(
            "s3_strength_threshold", self.s3_strength_threshold
        )

        # --- YAA: Correção Proativa de Débito Técnico (TASK-007) ---
        if self.s3_resample_period == "M":
            logger.warning(
                f"'{self.name}': O período de resample 'M' está obsoleto. "
                f"Corrigindo para 'ME' (Month-End). "
                f"Atualize sua configuração."
            )
            self.s3_resample_period = "ME"
        # --- Fim da Correção ---

        self.meta_sharpe_window_hours = self.parameters.get(
            "meta_sharpe_window_hours", self.meta_sharpe_window_hours
        )
        self.sub_strategy_returns = {
            k: deque(
                list(self.sub_strategy_returns.get(k, [])),
                maxlen=self.meta_sharpe_window_hours,
            )
            for k in ["s1", "s2", "s3"]
        }
        self.meta_transaction_cost = self.parameters.get(
            "meta_transaction_cost", self.meta_transaction_cost
        )
        self.meta_decision_threshold = self.parameters.get(
            "meta_decision_threshold", self.meta_decision_threshold
        )
        self.cE = self.parameters.get("cE", self.cE)
        self.cH = self.parameters.get("cH", self.cH)
        self.coherence_threshold = self.parameters.get(
            "coherence_threshold", self.coherence_threshold
        )
        self.entropy_threshold = self.parameters.get(
            "entropy_threshold", self.entropy_threshold
        )
        self.otoc_delta = self.parameters.get(
            "otoc_delta", self.otoc_delta
        )  # Update OTOC delta parameter
        self.otoc_window = self.parameters.get(
            "otoc_window", self.otoc_window
        )  # Update OTOC window parameter
        self.entropy_history_window = self.parameters.get(
            "entropy_history_window", self.entropy_history_window
        )

        self.envelope = IntentionEnvelope(
            profit_target=self.parameters.get(
                "profit_target", self.envelope.profit_target
            ),
            max_drawdown=self.parameters.get(
                "max_drawdown", self.envelope.max_drawdown
            ),
            horizon_hours=self.parameters.get("horizon", self.envelope.horizon_hours),
        )
        if self.alpha_gamma_policy is None:
            self.alpha_gamma_policy = AlphaGammaPolicy(
                alpha=self.tsvf_alpha,
                gamma=self.tsvf_gamma,
                window=self.parameters.get("h4_window", 20),
                weights=self.weights.copy(),
                risk_manager=self.shared_context.get("risk_manager"),
                replay_capacity=self.parameters.get("experience_replay_capacity", 2000),
            )
        else:
            self.alpha_gamma_policy.alpha = self.tsvf_alpha
            self.alpha_gamma_policy.gamma = self.tsvf_gamma
            self.alpha_gamma_policy.weights = self.weights.copy()

        logger.debug(
            f"{self.name}: Parâmetros atualizados. Novo tsvf_vector_size: {self.tsvf_vector_size}"
        )

        # Marcar para recálculo do requisito mínimo de dados
        self._requirement_dirty = True

    def update_from_evolved(self, evolved_strategy: TradingStrategy) -> None:
        """Update internal state based on an evolved strategy instance."""
        new_params = getattr(evolved_strategy, "parameters", None)
        if new_params is None:
            new_params = getattr(evolved_strategy, "params", {})
        logger.debug(f"{self.name}: Recebendo parâmetros evoluídos: {new_params}")
        self.set_parameters(new_params)

    def backtest(
        self,
        market_data_map: dict,
        initial_capital: float = 10000.0,
        risk_per_trade_pct: float = None,
    ) -> dict:
        return run_backtest(
            self,
            market_data_map,
            initial_capital=initial_capital,
            risk_per_trade_pct=risk_per_trade_pct,
        )

    def _validate_historical_data_length(self, data: pd.DataFrame) -> bool:
        """Validar se temos dados históricos suficientes para análise."""
        # NOTE(YAA-06): Lógica central de dados
        # Prioriza o histórico injetado pelo warmup, se existir.
        if self._initial_history is not None and not self._initial_history.empty:
            logger.info(
                f"TASK-06: {self.name}: Usando {len(self._initial_history)} candles do histórico de warmup."
            )
            # Combina o histórico de warmup com os dados de tempo real mais recentes.
            analysis_df = (
                pd.concat([self._initial_history, data])
                .drop_duplicates(subset=["timestamp"])
                .sort_index()
            )
            # Limpa o histórico para forçar o uso de dados incrementais nos próximos ciclos.
            self._initial_history = None
        else:
            analysis_df = data
        # --- Fim da Lógica TASK-06 ---

        if analysis_df is None:
            return False

        if isinstance(analysis_df, np.ndarray):
            actual_length = len(analysis_df) if analysis_df.size > 0 else 0
        elif hasattr(analysis_df, "empty") and hasattr(analysis_df, "__len__"):
            # É um DataFrame pandas
            if is_data_empty(analysis_df):
                return False
            actual_length = len(analysis_df)
        elif hasattr(analysis_df, "__len__"):
            # É uma estrutura com length
            actual_length = len(analysis_df)
        else:
            logger.warning(
                f"{self.name}: Tipo de dados inesperado: {type(analysis_df)}"
            )
            return False

        required_length = self._calculate_required_initial_data_length()

        # YAA: Debug detalhado da validação
        logger.info(
            f"{self.name}: DEBUG - Validação: dados={actual_length}, "
            f"requisito={required_length}, proteção_TSVF={self._tsvf_integrity_protected}"
        )

        if actual_length < required_length:
            logger.warning(
                f"{self.name}: DEBUG - Validação FALHOU: {actual_length} < {required_length}"
            )
            self._handle_missing_history(actual_length, required_length)

        logger.info(
            f"{self.name}: DEBUG - Validação PASSOU: {actual_length} >= {required_length}"
        )
        return True

    def _auto_adjust_parameters_for_kraken(self, available_limit: int) -> None:
        """Auto-ajustar parâmetros da estratégia para funcionar com limite da exchange.

        YAA CORREÇÃO CRÍTICA: Auto-ajuste extremamente agressivo para funcionar com poucos dados.
        (ESTA FUNCIONALIDADE FOI DESATIVADA PARA GARANTIR A INTEGRIDADE DOS REQUISITOS DE DADOS)
        """
        required = self._calculate_required_initial_data_length()

        if available_limit >= required:
            logger.debug("%s: nenhuma necessidade de ajuste", self.name)
            self._insufficient_history_count = 0
            return

        if available_limit <= 0:
            logger.warning(
                "%s: limite invalido para auto-ajuste: %s", self.name, available_limit
            )
            self._insufficient_history_count = 0
            return

        ratio = max(available_limit / required, 0.1)
        logger.info(
            "%s: auto-ajustando parametros com fator %.2f (limite %d)",
            self.name,
            ratio,
            available_limit,
        )

        def scale_window(attr: str) -> None:
            old_val = getattr(self, attr)
            new_val = max(1, int(math.ceil(old_val * ratio)))
            setattr(self, attr, new_val)
            logger.info(
                "%s: %s ajustado de %s para %s", self.name, attr, old_val, new_val
            )

        for window in [
            "s1_tsvf_window",
            "s2_sma_short_period",
            "s2_sma_long_period",
            "s2_rsi_period",
            "s3_tsvf_window",
        ]:
            scale_window(window)

        def scale_threshold(attr: str) -> None:
            old_val = getattr(self, attr)
            new_val = max(0.001, float(old_val) * ratio)
            setattr(self, attr, new_val)
            logger.info(
                "%s: %s ajustado de %.6f para %.6f", self.name, attr, old_val, new_val
            )

        for thresh in [
            "s1_strength_threshold",
            "s3_strength_threshold",
            "coherence_threshold",
            "entropy_threshold",
        ]:
            scale_threshold(thresh)

        self._exchange_adjusted = True
        self._insufficient_history_count = 0

        # Parâmetros alterados exigem recálculo do histórico mínimo
        self._requirement_dirty = True

    def _handle_update_params(self, payload: dict) -> None:
        """Apply parameter updates received from the event bus.

        Parameters
        ----------
        payload
            Mapping with new parameter values. Only keys present in
            :class:`QualiaTSVFParams` are considered.
        """
        try:
            if not isinstance(payload, dict):
                logger.warning(
                    "%s: Ignorando payload invalido em update_params", self.name
                )
                return

            if hasattr(self.params_obj, "model_fields"):
                valid_keys = set(self.params_obj.model_fields.keys())
            else:  # pragma: no cover - fallback for pydantic v1
                valid_keys = set(getattr(self.params_obj, "__fields__", {}).keys())
            updates = {k: v for k, v in payload.items() if k in valid_keys}

            if not updates:
                logger.warning(
                    "%s: Nenhum parametro valido em update_params: %s",
                    self.name,
                    list(payload.keys()),
                )
                return

            for key, value in updates.items():
                setattr(self.params_obj, key, value)

            self.set_parameters(updates)
            logger.info(
                "%s: Parametros atualizados via event bus: %s", self.name, updates
            )
        except Exception as exc:  # noqa: BLE001 - unexpected
            logger.exception("%s: Erro ao aplicar update_params: %s", self.name, exc)

    def _handle_market_alert(self, payload: dict) -> None:
        """React to market alerts published on the event bus.

        Parameters
        ----------
        payload
            Mapping describing the alert. Expected keys include ``type``,
            ``level``, ``parameters`` and ``drc_parameters``.
        """
        try:
            if not isinstance(payload, dict):
                logger.warning("%s: alerta invalido recebido", self.name)
                return

            if isinstance(payload.get("parameters"), dict):
                for k, v in payload["parameters"].items():
                    if hasattr(self.params_obj, k):
                        setattr(self.params_obj, k, v)
                self.set_parameters(payload["parameters"])

            drc_params = payload.get("drc_parameters")
            if self.dynamic_risk_controller is not None and isinstance(
                drc_params, dict
            ):
                for key, value in drc_params.items():
                    if hasattr(self.dynamic_risk_controller.params, key):
                        setattr(self.dynamic_risk_controller.params, key, value)

            alert_type = payload.get("type")
            level = payload.get("level")
            if (
                self.dynamic_risk_controller is not None
                and alert_type == "volatility"
                and isinstance(level, (int, float))
            ):
                if (
                    level
                    > self.dynamic_risk_controller.params.volatility_threshold_high
                ):
                    self.dynamic_risk_controller.params.regime_volatile_multiplier *= (
                        1.1
                    )
                elif (
                    level < self.dynamic_risk_controller.params.volatility_threshold_low
                ):
                    self.dynamic_risk_controller.params.regime_calm_multiplier *= 0.9
        except Exception as exc:  # noqa: BLE001 - unexpected
            logger.exception("%s: Erro ao processar market.alert: %s", self.name, exc)

    def _handle_risk_adjustment(self, payload: Any) -> None:
        """Store risk adjustment requests from the event bus."""

        self.last_risk_adjustment = payload
        logger.info("%s: recebeu solicitacao de ajuste de risco", self.name)

    def _validate_and_prepare_data(
        self, market_data: pd.DataFrame
    ) -> Optional[pd.DataFrame]:
        """
        Valida se há dados suficientes e prepara o DataFrame.
        TASK-06: Modificado para usar dados de warmup injetados se disponíveis.
        """
        # NOTE(YAA-06): Priorizar dados injetados pelo Warmup
        if self._initial_history is not None and not self._initial_history.empty:
            logger.info(
                f"TASK-06: {self.name}: Usando {len(self._initial_history)} candles do histórico de warmup."
            )
            # Concatena os dados do warmup com os dados de tempo real mais recentes
            # para garantir que a análise seja feita com a informação mais atual.
            combined_data = pd.concat(
                [self._initial_history, market_data]
            ).drop_duplicates(subset=["timestamp"])
            combined_data = combined_data.sort_index()
            # Uma vez que os dados do warmup foram usados, limpamos para evitar reutilização
            # em ciclos de decisão subsequentes, forçando o uso de market_data incremental.
            self._initial_history = None

            final_data = combined_data
        else:
            final_data = market_data
        # --- Fim da modificação da TASK-06 ---

        required_candles = self._get_required_data_length()
        has_enough_data = len(final_data) >= required_candles

        if not has_enough_data:
            logger.warning(
                f"{self.name}: Insufficient data for analysis. Required: {required_candles}, Available: {len(final_data)}"
            )
            self._handle_missing_history(len(final_data), required_candles)
            return None

        return final_data


if __name__ == "__main__":
    rng = np.random.default_rng(42)
    dates_test = pd.to_datetime(
        pd.date_range(start="2023-01-01", periods=1000, freq="h")
    )
    prices_test = pd.Series(
        100 + rng.standard_normal(1000).cumsum() * 0.1, index=dates_test
    )
    data_test_df = pd.DataFrame({"close": prices_test})

    test_params = {
        "tsvf_vector_size": 50,
        "tsvf_alpha": 0.3,
        "s1_tsvf_window": 12,
        "s1_strength_threshold": 0.02,
        "s2_sma_short_period": 10,
        "s2_sma_long_period": 30,
        "s2_rsi_period": 7,
        "s2_rsi_oversold": 35,
        "s2_rsi_overbought": 65,
        "s3_resample_period": "4h",
        "s3_tsvf_window": 3,
        "s3_strength_threshold": 0.015,
        "meta_sharpe_window_hours": 7 * 24,
        "meta_transaction_cost": 0.0007,
    }

    strategy_instance = QualiaTSVFStrategy(
        symbol="BTC/USDT", timeframe="1h", params=test_params
    )
    results = strategy_instance.backtest(
        market_data_map={"BTC/USDT": data_test_df}, initial_capital=100000
    )

    logger.info("--- Resultados do Backtest de Teste ---")
    logger.info("PnL Total (%%): %.2f%%", results["total_pnl_pct"])
    logger.info("Sharpe Ratio Anualizado: %.2f", results["sharpe_ratio"])
    logger.info("Drawdown Máximo (%%): %.2f%%", results["max_drawdown"])
    logger.info("Capital Final: $%.2f", results["final_capital"])
    logger.info(
        "Número Aproximado de Trades/Rebalanceamentos: %s",
        results["num_trades"],
    )

    try:
        import matplotlib.pyplot as plt

        plt.figure(figsize=(12, 6))
        # Usar o índice original para plotar retornos combinados
        ret_series_plot = pd.Series(
            results["combined_returns"],
            index=data_test_df.index[: len(results["combined_returns"])],
        )
        plt.plot(
            ret_series_plot.index,
            (1 + ret_series_plot).cumprod(),
            label="Meta-Estratégia Combinada",
        )

        # Alinhar séries de sub-estratégias também se necessário
        s1_plot = pd.Series(
            results["returns_s1"],
            index=data_test_df.index[: len(results["returns_s1"])],
        )
        s2_plot = pd.Series(
            results["returns_s2"],
            index=data_test_df.index[: len(results["returns_s2"])],
        )
        s3_plot = pd.Series(
            results["returns_s3"],
            index=data_test_df.index[: len(results["returns_s3"])],
        )

        plt.plot(
            s1_plot.index, (1 + s1_plot).cumprod(), label="S1 (TSVF 1h)", alpha=0.7
        )
        plt.plot(
            s2_plot.index,
            (1 + s2_plot).cumprod(),
            label="S2 (TSVF 1h + Filtro)",
            alpha=0.7,
        )
        plt.plot(
            s3_plot.index, (1 + s3_plot).cumprod(), label="S3 (TSVF 4h)", alpha=0.7
        )

        plt.title(f"Performance da QualiaTSVFStrategy - {strategy_instance.symbol}")
        plt.xlabel("Data")
        plt.ylabel("Retorno Acumulado (Normalizado)")
        plt.legend()
        plt.grid(True)
        plt.show()
    except ImportError:
        logger.warning("Matplotlib não instalado. Gráfico não será exibido.")

# Aliases são registrados automaticamente pelo decorador
# ``register_strategy``. Nenhuma chamada adicional é necessária aqui.
