"""Script para gerar fixture de snapshots de book de ordens.

Este utilitário precisa ser executado de forma autônoma em diferentes
ambientes (por exemplo, no CI do GitHub). Para garantir que o pacote
``src`` seja encontrado mesmo quando o script é chamado diretamente a
partir do diretório ``fixtures``, o caminho da raiz do repositório é
adicionado dinamicamente ao ``sys.path`` antes das importações.
"""

import numpy as np
import random
import time
import os
from pathlib import Path

from qualia.utils.logger import get_logger
from qualia.utils.logging_config import init_logging

logger = get_logger(__name__)


def main() -> None:
    """Gera uma fixture de snapshots de order book."""

    init_logging()

    N = 10_000
    snapshots = []
    for _ in range(N):
        mid = random.uniform(25_000, 30_000)
        bids = [(mid - i * 0.5, random.random() * 10) for i in range(10)]
        asks = [(mid + i * 0.5, random.random() * 10) for i in range(10)]
        snapshots.append(
            {
                "bids": bids,
                "asks": asks,
                "timestamp": time.time() * 1000,
            }
        )

    output_dir = "fixtures"
    os.makedirs(output_dir, exist_ok=True)

    output_path = os.path.join(output_dir, "order_book_snapshots.npy")
    np.save(output_path, np.array(snapshots, dtype=object))

    logger.info("Fixture com %s snapshots salvo em: %s", N, output_path)


if __name__ == "__main__":
    main()
