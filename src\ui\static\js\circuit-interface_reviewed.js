// Revisado em 2025-06-13 por Codex
document.addEventListener('DOMContentLoaded', function() {
// Inicializar o campo quântico
if (window.initializeQuantumField) {
    window.initializeQuantumField();
}

// Configurar handlers de eventos
const refreshCircuitBtn = document.getElementById('refresh-circuit-btn');
const exportQasmBtn = document.getElementById('export-qasm-btn');

if (refreshCircuitBtn) {
    refreshCircuitBtn.addEventListener('click', function() {
        if (window.TradingInterface && window.TradingInterface.refreshQuantumCircuit) {
            window.TradingInterface.refreshQuantumCircuit();
        }
    });
}

if (exportQasmBtn) {
    exportQasmBtn.addEventListener('click', function() {
        if (window.TradingInterface && window.TradingInterface.exportQASM) {
            window.TradingInterface.exportQASM();
        }
    });
}

// Atualizar timestamp
const timestampEl = document.querySelector('.timestamp');
if (timestampEl) {
    const now = new Date();
    timestampEl.textContent = now.toLocaleString();
}
});
