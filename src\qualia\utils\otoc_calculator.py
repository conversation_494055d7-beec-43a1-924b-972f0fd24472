"""
OTOC Calculator - Single Source of Truth para cálculos OTOC no QUALIA.

Este módulo implementa o cálculo do Out-of-Time-Order Correlator (OTOC) como
análogo financeiro, servindo como única fonte de verdade para todas as
implementações OTOC no sistema.

YAA-DESIGN: Consolidação de compute_financial_otoc + calculate_market_otoc
em uma implementação única, robusta e numericamente estável.
"""

import numpy as np
import pandas as pd
from typing import Union, Optional
import logging

from .logger import get_logger

logger = get_logger(__name__)


class OTOCCalculationError(Exception):
    """Exceção específica para erros de cálculo OTOC."""
    pass


def calculate_otoc(
    series: Union[np.ndarray, pd.Series], 
    idx: Optional[int] = None,
    window: int = 100,
    delta: int = 1,
    method: str = "correlation"
) -> float:
    """
    Calcula OTOC analógico como medida de scrambling temporal.
    
    O OTOC mede o grau de "embaralhamento" entre passado e futuro:
    - Valor próximo de 0.0: Alta correlação temporal (mercado ordenado)
    - Valor próximo de 1.0: Baixa correlação temporal (mercado caótico)
    
    Parameters
    ----------
    series : np.ndarray or pd.Series
        Série temporal de preços ou retornos
    idx : int, optional
        Índice para cálculo. Se None, usa o último ponto válido
    window : int, default=100
        Tamanho da janela temporal para análise
    delta : int, default=1
        Deslocamento temporal para correlação
    method : str, default="correlation"
        Método de cálculo: "correlation" ou "financial"
        
    Returns
    -------
    float
        Valor OTOC normalizado entre 0.0 e 1.0
        
    Raises
    ------
    OTOCCalculationError
        Se parâmetros inválidos ou dados insuficientes
    """
    
    # YAA-VALIDATION: Sanity checks críticos
    if window < 20:
        raise OTOCCalculationError("OTOC: window must be >= 20 for numerical stability")
    
    if delta < 1:
        raise OTOCCalculationError("OTOC: delta must be >= 1")
    
    # Conversão para numpy array se necessário
    if isinstance(series, pd.Series):
        data = series.values
    else:
        data = np.asarray(series)
    
    # Validação de dados
    if len(data) == 0:
        logger.warning("OTOC: Empty series provided")
        return np.nan
    
    # Determinar índice de cálculo
    # YAA-FIX: Usar índice que permita janela futura
    if idx is None:
        idx = len(data) - window - 1
    
    # Verificar se há dados suficientes
    # YAA-FIX: Verificar se há espaço para janelas passado e futuro
    if idx < window or idx + window >= len(data):
        logger.debug(f"OTOC: Insufficient data at idx={idx}, window={window}, len={len(data)}")
        return np.nan
    
    try:
        if method == "correlation":
            return _calculate_correlation_otoc(data, idx, window)
        elif method == "financial":
            return _calculate_financial_otoc(data, idx, window, delta)
        else:
            raise OTOCCalculationError(f"Unknown OTOC method: {method}")
            
    except Exception as e:
        logger.error(f"OTOC calculation failed: {e}")
        raise OTOCCalculationError(f"OTOC calculation error: {e}")


def _calculate_correlation_otoc(data: np.ndarray, idx: int, window: int) -> float:
    """
    Implementa OTOC baseado em correlação entre passado e futuro.

    OTOC = 1 - |correlation(past, future)|²
    """
    # YAA-ROBUST: Ajustar janelas para dados disponíveis
    past_start = max(0, idx - window)
    past_end = idx
    future_start = idx + 1
    future_end = min(len(data), idx + 1 + window)

    past = data[past_start:past_end]
    future = data[future_start:future_end]

    # Verificar se há dados suficientes
    if len(past) < 10 or len(future) < 10:
        return np.nan

    # Usar o menor tamanho para comparação justa
    min_len = min(len(past), len(future))
    past = past[-min_len:]
    future = future[:min_len]
    
    # Calcular correlação com tratamento robusto
    try:
        correlation_matrix = np.corrcoef(past, future)
        if correlation_matrix.shape == (2, 2):
            corr = correlation_matrix[0, 1]
        else:
            corr = 0.0
        
        # YAA-ROBUSTNESS: Tratar NaN e valores extremos
        if np.isnan(corr) or np.isinf(corr):
            return 0.5  # Valor neutro para casos indefinidos
        
        # OTOC = 1 - |correlation|²
        otoc_value = 1.0 - (corr ** 2)
        
        # Garantir range [0, 1]
        return float(np.clip(otoc_value, 0.0, 1.0))
        
    except Exception as e:
        logger.debug(f"Correlation calculation failed: {e}")
        return 0.5


def _calculate_financial_otoc(
    data: np.ndarray, 
    idx: int, 
    window: int, 
    delta: int
) -> float:
    """
    Implementa OTOC financeiro baseado em retornos defasados.
    
    Compatível com a implementação existente em tsvf.py
    """
    # Converter para retornos se necessário
    if len(data) > 1:
        returns = np.diff(data) / data[:-1]
    else:
        return np.nan
    
    # Ajustar índice para array de retornos
    ret_idx = min(idx - 1, len(returns) - 1)
    
    if ret_idx < delta or ret_idx + window >= len(returns):
        return np.nan
    
    try:
        # Calcular OTOC financeiro: média de (r_t * r_{t+delta})²
        otoc_values = []
        
        for i in range(max(0, ret_idx - window + 1), ret_idx + 1):
            if i + delta < len(returns):
                r_t = returns[i]
                r_t_delta = returns[i + delta]
                otoc_point = (r_t * r_t_delta) ** 2
                
                if not (np.isnan(otoc_point) or np.isinf(otoc_point)):
                    otoc_values.append(otoc_point)
        
        if len(otoc_values) == 0:
            return np.nan
        
        # Média dos valores OTOC
        mean_otoc = np.mean(otoc_values)
        
        # Normalizar para range [0, 1] usando função sigmoide suave
        normalized_otoc = 2.0 / (1.0 + np.exp(-mean_otoc)) - 1.0
        
        return float(np.clip(normalized_otoc, 0.0, 1.0))
        
    except Exception as e:
        logger.debug(f"Financial OTOC calculation failed: {e}")
        return 0.5


def calculate_adaptive_threshold(
    base_threshold: float,
    volatility: float,
    beta: float = 1.0,
    vol_window: int = 20
) -> float:
    """
    Calcula threshold OTOC adaptativo baseado na volatilidade do mercado.
    
    threshold_adaptive = base_threshold * (1 + volatility * beta)
    
    Parameters
    ----------
    base_threshold : float
        Threshold base configurado
    volatility : float
        Volatilidade atual do mercado (rolling std)
    beta : float, default=1.0
        Fator de sensibilidade à volatilidade (0-2)
    vol_window : int, default=20
        Janela para cálculo de volatilidade
        
    Returns
    -------
    float
        Threshold adaptativo ajustado
    """
    if np.isnan(volatility) or volatility <= 0:
        return base_threshold
    
    # YAA-ADAPTIVE: Threshold cresce com volatilidade
    # Em mercados voláteis mas ordenados, permitir mais flexibilidade
    adaptive_factor = 1.0 + (volatility * beta)
    adaptive_threshold = base_threshold * adaptive_factor
    
    # Limitar range razoável
    return float(np.clip(adaptive_threshold, base_threshold * 0.5, base_threshold * 3.0))


# YAA-COMPATIBILITY: Re-export para código legado
def compute_financial_otoc(
    returns: Union[pd.Series, np.ndarray], 
    delta: int, 
    window: int
) -> float:
    """
    Wrapper de compatibilidade para compute_financial_otoc existente.
    
    DEPRECATED: Use calculate_otoc(method="financial") instead.
    """
    logger.warning("compute_financial_otoc is deprecated. Use calculate_otoc(method='financial')")
    
    try:
        return calculate_otoc(
            series=returns,
            window=window,
            delta=delta,
            method="financial"
        )
    except OTOCCalculationError:
        return 0.0


# YAA-METRICS: Função para observabilidade
def get_otoc_diagnostics(
    series: Union[np.ndarray, pd.Series],
    window: int = 100,
    delta: int = 1
) -> dict:
    """
    Retorna métricas diagnósticas para debugging OTOC.
    
    Returns
    -------
    dict
        Dicionário com métricas diagnósticas
    """
    try:
        otoc_corr = calculate_otoc(series, method="correlation", window=window)
        otoc_fin = calculate_otoc(series, method="financial", window=window, delta=delta)
        
        if isinstance(series, pd.Series):
            data = series.values
        else:
            data = np.asarray(series)
        
        return {
            "otoc_correlation": otoc_corr,
            "otoc_financial": otoc_fin,
            "series_length": len(data),
            "series_std": np.std(data) if len(data) > 1 else 0.0,
            "series_mean": np.mean(data) if len(data) > 0 else 0.0,
            "has_nan": bool(np.any(np.isnan(data))),
            "window_coverage": min(1.0, len(data) / (2 * window))
        }
    except Exception as e:
        return {"error": str(e)}
