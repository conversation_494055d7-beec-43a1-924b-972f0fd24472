"""FastAPI route exposing the current SIM token-transition graph."""
from __future__ import annotations

from fastapi import APIRouter, Depends
from starlette.responses import JSONResponse

from ...core.sim_graph import get_snapshot, get_stats

router = APIRouter(prefix="/api", tags=["sim_graph"])


@router.get("/sim_graph")
async def sim_graph_snapshot() -> JSONResponse:  # noqa: D401 simple
    """Return the current token-transition graph used by the Dynamic Logo."""
    return JSONResponse(get_snapshot())


@router.get("/sim_graph/stats")
async def sim_graph_stats() -> JSONResponse:  # noqa: D401 simple
    """Return statistics about cycles and features."""
    return JSONResponse(get_stats())
