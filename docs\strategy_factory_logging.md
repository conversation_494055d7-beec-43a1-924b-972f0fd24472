# Sistema de Logging da StrategyFactory - QUALIA

## Visão Geral

A StrategyFactory do QUALIA foi refatorada para reduzir a verbosidade excessiva dos logs, mantendo a capacidade de debugging detalhado quando necessário.

## Mudanças Implementadas

### 1. Controle de Verbosidade Automático

A StrategyFactory agora verifica automaticamente se o nível DEBUG está habilitado:

```python
_verbose_logging = logger.isEnabledFor(logging.DEBUG)
```

### 2. Logs Simplificados por Padrão

**Antes (DEBUG verboso):**
```
FACTORY: Estratégia 'MinhaEstrategia' (MinhaEstrategiaClass) registrada com sucesso. Registry AGORA: {...}, ID: 140234567890
StrategyFactory: Assinatura __init__ para MinhaEstrategiaClass: ['self', 'params', 'shared_context']
StrategyFactory: Mapeado 'params' (config dict) para __init__.
StrategyFactory: Mapeado 'shared_context' para __init__.
StrategyFactory: Argumentos finais para MinhaEstrategiaClass.__init__: ['params', 'shared_context']
StrategyFactory: Valores de valid_init_params: {...}
```

**Agora (INFO conciso):**
```
Estratégia 'MinhaEstrategia' registrada com sucesso
Estratégia 'MinhaEstrategia' criada com sucesso
```

### 3. Logs Detalhados Condicionais

Todos os logs detalhados agora são condicionais:

```python
if _verbose_logging:
    logger.debug("Detalhes técnicos internos...")
```

## Configuração

### Produção (Logs Limpos)

No arquivo `.env.production`:

```bash
QUALIA_LOG_LEVEL=INFO
QUALIA_MODULE_LEVELS={"src.qualia.strategies.strategy_factory": "INFO"}
```

### Desenvolvimento/Debug (Logs Detalhados)

Para habilitar logs detalhados quando necessário:

```bash
# Opção 1: Nível global DEBUG
QUALIA_LOG_LEVEL=DEBUG

# Opção 2: Apenas StrategyFactory em DEBUG
QUALIA_MODULE_LEVELS={"src.qualia.strategies.strategy_factory": "DEBUG"}

# Opção 3: Via código Python
from src.qualia.utils.logging_config import init_logging

init_logging(
    log_level="INFO",
    module_levels={
        "src.qualia.strategies.strategy_factory": "DEBUG"
    }
)
```

## Benefícios

1. **Logs Limpos em Produção**: Apenas informações essenciais são registradas
2. **Debugging Preservado**: Todos os logs detalhados ainda estão disponíveis quando necessário
3. **Configuração Flexível**: Controle granular via variáveis de ambiente
4. **Performance**: Redução do overhead de logging em produção
5. **Manutenibilidade**: Logs mais legíveis facilitam identificação de problemas

## Logs Mantidos em INFO

- Registro bem-sucedido de estratégias
- Criação bem-sucedida de estratégias
- Erros e warnings (sempre visíveis)

## Logs Movidos para DEBUG Condicional

- Detalhes do registry interno
- Mapeamento individual de parâmetros
- Assinaturas de construtores
- IDs de objetos internos
- Valores completos de configuração
- Logs de injeção de dependência

## Exemplo de Uso

```python
from qualia.strategies.strategy_factory import StrategyFactory

# Em produção: apenas logs INFO limpos
factory = StrategyFactory()
strategy = factory.create_strategy("MinhaEstrategia", params={...})
# Output: "Estratégia 'MinhaEstrategia' criada com sucesso"

# Para debugging: habilitar DEBUG via configuração
# Output completo com todos os detalhes técnicos
```

## Compatibilidade

- ✅ Totalmente compatível com código existente
- ✅ Nenhuma mudança na API pública
- ✅ Configuração via infraestrutura existente do QUALIA
- ✅ Testes existentes continuam funcionando
