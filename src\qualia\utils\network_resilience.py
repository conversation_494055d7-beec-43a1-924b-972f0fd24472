"""Network resilience utilities for API calls.

This module provides a simple asynchronous circuit breaker and helper
function for performing API calls with exponential backoff. It is used
by ``KrakenIntegration`` to harden requests to external exchanges.

Default parameters are loaded from ``config/utils.yaml`` when the feature
flag ``QUALIA_FT_UTILS_V2`` está habilitado. Valores individuais podem
ser sobrescritos pelas variáveis de ambiente ``QUALIA_CB_FAIL_THRESHOLD``
e ``QUALIA_CALL_MAX_RETRIES``.
"""

from __future__ import annotations

import asyncio
import time
from typing import Any, Awaitable, Callable

import os
from datadog import DogStatsd

from ..config.utils_defaults import load_utils_defaults
from ..config.feature_flags import feature_toggle
from ..memory.event_bus import SimpleEventBus
from ..events import MetricRecordedEvent

from ..config.settings import market_metrics_enabled
from .logger import get_logger
from .tracing import instrument_logger, is_tracing_enabled
from .metrics_persistence import persist_metric_to_qpm

"""Event name published when a circuit breaker changes state."""
CIRCUIT_BREAKER_STATE_EVENT = "utils.circuit_breaker_state"

logger = get_logger(__name__)

_DEFAULTS = load_utils_defaults() if feature_toggle("utils_v2") else {}
_TRACE_LOGGING = bool(_DEFAULTS.get("trace_instrumentation_enabled", False))

if is_tracing_enabled():  # pragma: no cover - configuration
    instrument_logger(logger.logger)
_PARAMS = _DEFAULTS.get("network_resilience", {}) if feature_toggle("utils_v2") else {}

CB_FAIL_THRESHOLD = int(
    os.getenv(
        "QUALIA_CB_FAIL_THRESHOLD",
        _PARAMS.get("circuit_breaker_fail_threshold", 8),  # NETWORK FIX: Aumentado para 8 para maior tolerância
    )
)
CB_RECOVERY_TIMEOUT = float(_PARAMS.get("circuit_breaker_recovery_timeout", 120.0))  # NETWORK FIX: Aumentado para 120s
CALL_MAX_RETRIES = int(
    os.getenv(
        "QUALIA_CALL_MAX_RETRIES",
        _PARAMS.get("call_max_retries", 3),
    )
)
CALL_BASE_DELAY = float(_PARAMS.get("call_base_delay", 1.0))
CALL_MAX_DELAY = float(_PARAMS.get("call_max_delay", 30.0))


class CircuitBreakerOpenError(Exception):
    """Raised when the circuit breaker is open."""


class CircuitBreakerState:
    """Circuit breaker states for enhanced state management."""
    CLOSED = "CLOSED"
    OPEN = "OPEN"
    HALF_OPEN = "HALF_OPEN"


class EnhancedCircuitBreaker:
    """Enhanced circuit breaker with sophisticated state management and per-symbol isolation.

    This circuit breaker provides:
    - Three-state management (CLOSED/OPEN/HALF_OPEN)
    - Per-symbol failure tracking and isolation
    - Adaptive recovery timeouts based on failure patterns
    - Comprehensive metrics and event publishing
    - Graceful degradation with fallback mechanisms

    Parameters
    ----------
    fail_threshold
        Number of consecutive failures before opening the breaker.
    recovery_timeout
        Base seconds to wait before allowing a new request when open.
    name
        Metric namespace used when emitting StatsD metrics.
    statsd_client
        Optional ``DogStatsd`` instance for metrics emission.
    event_bus
        Optional event bus for publishing state changes.
    adaptive_recovery
        If True, recovery timeout adapts based on failure patterns.
    max_recovery_timeout
        Maximum recovery timeout when using adaptive recovery.
    symbol_isolation
        If True, maintains separate failure counts per symbol.
    """

    def __init__(
        self,
        fail_threshold: int = CB_FAIL_THRESHOLD,
        recovery_timeout: float = CB_RECOVERY_TIMEOUT,
        *,
        name: str = "enhanced_circuit_breaker",
        statsd_client: DogStatsd | None = None,
        event_bus: SimpleEventBus | None = None,
        adaptive_recovery: bool = True,
        max_recovery_timeout: float = 600.0,
        symbol_isolation: bool = True,
    ) -> None:
        self.fail_threshold = max(1, fail_threshold)
        self.base_recovery_timeout = max(0.01, recovery_timeout)
        self.max_recovery_timeout = max_recovery_timeout
        self.adaptive_recovery = adaptive_recovery
        self.symbol_isolation = symbol_isolation

        # State management
        self._state = CircuitBreakerState.CLOSED
        self._opened_at: float | None = None
        self._last_test_time: float | None = None

        # Failure tracking
        self._global_failure_count = 0
        self._symbol_failures: dict[str, int] = {}
        self._consecutive_failures = 0
        self._total_failures = 0
        self._success_count = 0

        # Metrics and events
        self.name = name
        self.statsd = statsd_client or (DogStatsd() if market_metrics_enabled else None)
        self.event_bus = event_bus
        self.publish_events = feature_toggle("utils_v2")

        # Performance tracking
        self._failure_history: list[float] = []
        self._recovery_attempts = 0

    @property
    def state(self) -> str:
        """Return current circuit breaker state."""
        return self._state

    @property
    def is_open(self) -> bool:
        """Return ``True`` when the circuit breaker is open."""
        return self._state == CircuitBreakerState.OPEN

    @property
    def is_closed(self) -> bool:
        """Return ``True`` when the circuit breaker is closed."""
        return self._state == CircuitBreakerState.CLOSED

    @property
    def is_half_open(self) -> bool:
        """Return ``True`` when the circuit breaker is half-open."""
        return self._state == CircuitBreakerState.HALF_OPEN

    @property
    def failure_rate(self) -> float:
        """Calculate current failure rate."""
        total_requests = self._total_failures + self._success_count
        return self._total_failures / max(1, total_requests)

    @property
    def current_recovery_timeout(self) -> float:
        """Calculate current recovery timeout (adaptive if enabled)."""
        if not self.adaptive_recovery:
            return self.base_recovery_timeout

        # Adaptive timeout based on consecutive failures and failure rate
        multiplier = min(2.0 ** (self._consecutive_failures - self.fail_threshold), 8.0)
        adaptive_timeout = self.base_recovery_timeout * multiplier

        return min(adaptive_timeout, self.max_recovery_timeout)

    def time_until_recovery(self) -> float:
        """Return seconds remaining until the circuit allows a new request."""

        if self._opened_at is None:
            return 0.0
        elapsed = time.time() - self._opened_at
        return max(0.0, self.recovery_timeout - elapsed)

    def allow_request(self, symbol: str = None) -> bool:
        """Return ``True`` if a request should be attempted.

        Parameters
        ----------
        symbol : str, optional
            Symbol to check if symbol isolation is enabled.
        """
        current_time = time.time()

        # Check symbol-specific failures if isolation is enabled
        if self.symbol_isolation and symbol:
            symbol_failures = self._symbol_failures.get(symbol, 0)
            if symbol_failures >= self.fail_threshold:
                logger.debug(
                    "Circuit breaker %s blocked for symbol %s; %d failures",
                    self.name, symbol, symbol_failures
                )
                if self.statsd:
                    self.statsd.increment(f"{self.name}.symbol_blocked", tags=[f"symbol:{symbol}"])
                return False

        if self._state == CircuitBreakerState.CLOSED:
            return True
        elif self._state == CircuitBreakerState.OPEN:
            if self._opened_at and current_time - self._opened_at >= self.current_recovery_timeout:
                self._transition_to_half_open()
                return True
            else:
                if self.statsd:
                    self.statsd.increment(f"{self.name}.blocked")
                remaining = self.time_until_recovery()
                logger.debug(
                    "Circuit breaker %s blocked; %.2fs until recovery", self.name, remaining
                )
                return False
        elif self._state == CircuitBreakerState.HALF_OPEN:
            # Allow one test request in half-open state
            if self._last_test_time is None or current_time - self._last_test_time >= 1.0:
                self._last_test_time = current_time
                logger.debug("Circuit breaker %s half-open; allowing test request", self.name)
                if self.statsd:
                    self.statsd.increment(f"{self.name}.half_open_test")
                return True
            else:
                if self.statsd:
                    self.statsd.increment(f"{self.name}.half_open_blocked")
                return False

        return False

    def time_until_recovery(self) -> float:
        """Return seconds until the circuit breaker allows requests."""
        if self._opened_at is None:
            return 0.0
        elapsed = time.time() - self._opened_at
        return max(0.0, self.current_recovery_timeout - elapsed)

    def record_success(self, symbol: str = None) -> None:
        """Record a successful request.

        Parameters
        ----------
        symbol : str, optional
            Symbol for which to record success if symbol isolation is enabled.
        """
        self._success_count += 1
        self._consecutive_failures = 0
        self._global_failure_count = 0

        # Reset symbol-specific failures if isolation is enabled
        if self.symbol_isolation and symbol and symbol in self._symbol_failures:
            self._symbol_failures[symbol] = 0

        if self._state == CircuitBreakerState.HALF_OPEN:
            self._transition_to_closed()

        if self.statsd:
            tags = [f"symbol:{symbol}"] if symbol else []
            self.statsd.increment(f"{self.name}.success", tags=tags)

    def record_failure(self, symbol: str = None) -> None:
        """Record a failed request.

        Parameters
        ----------
        symbol : str, optional
            Symbol for which to record failure if symbol isolation is enabled.
        """
        self._global_failure_count += 1
        self._consecutive_failures += 1
        self._total_failures += 1

        # Track failure history for adaptive recovery
        current_time = time.time()
        self._failure_history.append(current_time)
        # Keep only recent failures (last hour)
        self._failure_history = [t for t in self._failure_history if current_time - t < 3600]

        # Track symbol-specific failures if isolation is enabled
        if self.symbol_isolation and symbol:
            self._symbol_failures[symbol] = self._symbol_failures.get(symbol, 0) + 1

        if self.statsd:
            tags = [f"symbol:{symbol}"] if symbol else []
            self.statsd.increment(f"{self.name}.failure", tags=tags)

        # Transition to OPEN if threshold exceeded
        if self._global_failure_count >= self.fail_threshold and self._state == CircuitBreakerState.CLOSED:
            self._transition_to_open()
        elif self._state == CircuitBreakerState.HALF_OPEN:
            # Failed test request in half-open state
            self._transition_to_open()
            self._recovery_attempts += 1

    def _transition_to_open(self) -> None:
        """Transition circuit breaker to OPEN state."""
        self._state = CircuitBreakerState.OPEN
        self._opened_at = time.time()
        self._last_test_time = None

        logger.warning(
            "Enhanced circuit breaker %s opened after %d failures (consecutive: %d)",
            self.name, self._total_failures, self._consecutive_failures
        )

        if self.statsd:
            self.statsd.increment(f"{self.name}.opened")
            self.statsd.gauge(f"{self.name}.failure_rate", self.failure_rate)

        if self.event_bus and self.publish_events:
            self.event_bus.publish(
                CIRCUIT_BREAKER_STATE_EVENT,
                {
                    "name": self.name,
                    "state": "open",
                    "failure_count": self._total_failures,
                    "recovery_timeout": self.current_recovery_timeout
                }
            )

    def _transition_to_half_open(self) -> None:
        """Transition circuit breaker to HALF_OPEN state."""
        self._state = CircuitBreakerState.HALF_OPEN
        self._last_test_time = None

        logger.info(
            "Enhanced circuit breaker %s half-open; allowing test requests",
            self.name
        )

        if self.statsd:
            self.statsd.increment(f"{self.name}.half_open")

        if self.event_bus and self.publish_events:
            self.event_bus.publish(
                CIRCUIT_BREAKER_STATE_EVENT,
                {"name": self.name, "state": "half_open"}
            )

    def _transition_to_closed(self) -> None:
        """Transition circuit breaker to CLOSED state."""
        self._state = CircuitBreakerState.CLOSED
        self._opened_at = None
        self._last_test_time = None
        self._recovery_attempts = 0

        logger.info(
            "Enhanced circuit breaker %s closed after successful recovery",
            self.name
        )

        if self.statsd:
            self.statsd.increment(f"{self.name}.closed")
            self.statsd.gauge(f"{self.name}.failure_rate", self.failure_rate)

        if self.event_bus and self.publish_events:
            self.event_bus.publish(
                CIRCUIT_BREAKER_STATE_EVENT,
                {"name": self.name, "state": "closed"}
            )

    def get_stats(self) -> dict:
        """Get comprehensive circuit breaker statistics."""
        return {
            "state": self._state,
            "failure_count": self._global_failure_count,
            "consecutive_failures": self._consecutive_failures,
            "total_failures": self._total_failures,
            "success_count": self._success_count,
            "failure_rate": self.failure_rate,
            "recovery_timeout": self.current_recovery_timeout,
            "time_until_recovery": self.time_until_recovery(),
            "recovery_attempts": self._recovery_attempts,
            "symbol_failures": dict(self._symbol_failures) if self.symbol_isolation else {},
            "recent_failure_count": len(self._failure_history)
        }

    def reset(self) -> None:
        """Reset circuit breaker to initial state."""
        self._state = CircuitBreakerState.CLOSED
        self._opened_at = None
        self._last_test_time = None
        self._global_failure_count = 0
        self._consecutive_failures = 0
        self._total_failures = 0
        self._success_count = 0
        self._symbol_failures.clear()
        self._failure_history.clear()
        self._recovery_attempts = 0

        logger.info("Enhanced circuit breaker %s reset to initial state", self.name)


# Keep the original CircuitBreaker for backward compatibility
class CircuitBreaker:
    """Simple circuit breaker for asynchronous API calls.

    Parameters
    ----------
    fail_threshold
        Number of consecutive failures before opening the breaker.
    recovery_timeout
        Seconds to wait before allowing a new request when open.
    name
        Metric namespace used when emitting StatsD metrics.
    statsd_client
        Optional ``DogStatsd`` instance for metrics emission. When ``None`` and
        :data:`market_metrics_enabled` is ``True`` a new client is created.
    event_bus
        Optional :class:`~src.qualia.memory.event_bus.SimpleEventBus` used to
        publish ``utils.circuit_breaker_state`` events when the circuit opens or
        closes.
    """

    def __init__(
        self,
        fail_threshold: int = CB_FAIL_THRESHOLD,
        recovery_timeout: float = CB_RECOVERY_TIMEOUT,
        *,
        name: str = "circuit_breaker",
        statsd_client: DogStatsd | None = None,
        event_bus: SimpleEventBus | None = None,
    ) -> None:
        self.fail_threshold = max(1, fail_threshold)
        self.recovery_timeout = max(0.01, recovery_timeout)
        self._failure_count = 0
        self._opened_at: float | None = None
        self.name = name
        self.statsd = statsd_client or (DogStatsd() if market_metrics_enabled else None)
        self.event_bus = event_bus
        self.publish_events = feature_toggle("utils_v2")

    @property
    def is_open(self) -> bool:
        """Return ``True`` when the circuit breaker is open."""

        return self._opened_at is not None and (
            time.time() - self._opened_at < self.recovery_timeout
        )

    def time_until_recovery(self) -> float:
        """Return seconds until the circuit breaker allows requests."""
        if self._opened_at is None:
            return 0.0
        elapsed = time.time() - self._opened_at
        return max(0.0, self.recovery_timeout - elapsed)

    def allow_request(self) -> bool:
        """Return ``True`` if a request should be attempted."""
        if self._opened_at is None:
            return True
        if time.time() - self._opened_at >= self.recovery_timeout:
            logger.debug(
                "Circuit breaker %s half-open; allowing test request", self.name
            )
            if self.statsd:
                self.statsd.increment(f"{self.name}.half_open")
            self._opened_at = None
            self._failure_count = 0
            if self.event_bus and self.publish_events:
                self.event_bus.publish(
                    CIRCUIT_BREAKER_STATE_EVENT,
                    {"name": self.name, "state": "closed"},
                )
            return True
        if self.statsd:
            self.statsd.increment(f"{self.name}.blocked")
        remaining = self.time_until_recovery()
        logger.debug(
            "Circuit breaker %s blocked; %.2fs until recovery", self.name, remaining
        )
        return False

    def record_success(self) -> None:
        """Reset failure count after a successful call."""
        was_open = self._opened_at is not None
        self._failure_count = 0
        self._opened_at = None
        if self.statsd:
            self.statsd.increment(f"{self.name}.success")
        persist_metric_to_qpm(f"{self.name}.success", True)
        if self.event_bus and self.publish_events:
            self.event_bus.publish(
                "monitor.metric_recorded",
                MetricRecordedEvent(name=f"{self.name}.success", value=1.0),
            )
        if was_open and self.event_bus and self.publish_events:
            self.event_bus.publish(
                CIRCUIT_BREAKER_STATE_EVENT,
                {"name": self.name, "state": "closed"},
            )

    def record_failure(self) -> None:
        """Record a failure and open the circuit if needed."""
        self._failure_count += 1
        if self.statsd:
            self.statsd.increment(f"{self.name}.failure")
        persist_metric_to_qpm(f"{self.name}.success", False)
        if self.event_bus and self.publish_events:
            self.event_bus.publish(
                "monitor.metric_recorded",
                MetricRecordedEvent(name=f"{self.name}.success", value=0.0),
            )

        should_open = (
            self._failure_count >= self.fail_threshold and self._opened_at is None
        )
        if should_open:
            self._opened_at = time.time()
            logger.warning(
                "Circuit breaker %s opened after %s failures",
                self.name,
                self._failure_count,
            )
            if self.statsd:
                self.statsd.increment(f"{self.name}.opened")
            if self.event_bus and self.publish_events:
                self.event_bus.publish(
                    CIRCUIT_BREAKER_STATE_EVENT,
                    {"name": self.name, "state": "open"},
                )


class APICircuitBreaker(CircuitBreaker):
    """Circuit breaker especializado para chamadas de API."""

    def __init__(
        self,
        fail_threshold: int = CB_FAIL_THRESHOLD,
        recovery_timeout: float = CB_RECOVERY_TIMEOUT,
        *,
        name: str = "api_circuit_breaker",
        statsd_client: DogStatsd | None = None,
        event_bus: SimpleEventBus | None = None,
    ) -> None:
        super().__init__(
            fail_threshold,
            recovery_timeout,
            name=name,
            statsd_client=statsd_client,
            event_bus=event_bus,
        )


async def call_with_backoff(
    circuit_breaker: CircuitBreaker,
    func: Callable[..., Awaitable[Any]],
    *args: Any,
    max_retries: int = CALL_MAX_RETRIES,
    base_delay: float = CALL_BASE_DELAY,
    max_delay: float = CALL_MAX_DELAY,
    statsd_client: DogStatsd | None = None,
    **kwargs: Any,
) -> Any:
    """Execute ``func`` with retries, exponential backoff and a circuit breaker.

    Parameters
    ----------
    circuit_breaker
        ``CircuitBreaker`` guarding the request.
    func
        Awaitable callable to execute.
    max_retries
        Maximum number of attempts before giving up.
    base_delay
        Base delay in seconds for the exponential backoff.
    max_delay
        Maximum delay between retries.
    statsd_client
        Optional metrics client used to emit retry counters.
    **kwargs
        Forwarded to ``func``.

    Notes
    -----
    Default values can be overridden via ``config/utils.yaml`` when the
    feature flag ``QUALIA_FT_UTILS_V2`` is enabled.
    """

    statsd = statsd_client or circuit_breaker.statsd

    attempts = 0
    last_exc: Exception | None = None
    while attempts < max_retries:
        if not circuit_breaker.allow_request():
            raise CircuitBreakerOpenError("Circuit breaker open")
        try:
            result = await func(*args, **kwargs)
            circuit_breaker.record_success()
            if statsd:
                statsd.increment(f"{circuit_breaker.name}.call_success")
            if circuit_breaker.event_bus and circuit_breaker.publish_events:
                circuit_breaker.event_bus.publish(
                    "monitor.metric_recorded",
                    MetricRecordedEvent(
                        name=f"{circuit_breaker.name}.call_success", value=1.0
                    ),
                )
            return result
        except Exception as exc:  # pragma: no cover - network or other error
            last_exc = exc
            circuit_breaker.record_failure()
            if statsd:
                statsd.increment(f"{circuit_breaker.name}.call_failure")
            if circuit_breaker.event_bus and circuit_breaker.publish_events:
                circuit_breaker.event_bus.publish(
                    "monitor.metric_recorded",
                    MetricRecordedEvent(
                        name=f"{circuit_breaker.name}.call_failure", value=1.0
                    ),
                )
            attempts += 1
            if attempts >= max_retries:
                break
            delay = min(base_delay * 2 ** (attempts - 1), max_delay)
            logger.debug("Retrying in %.2fs after failure: %s", delay, exc)
            if statsd:
                statsd.increment(f"{circuit_breaker.name}.retry")
            if circuit_breaker.event_bus and circuit_breaker.publish_events:
                circuit_breaker.event_bus.publish(
                    "monitor.metric_recorded",
                    MetricRecordedEvent(
                        name=f"{circuit_breaker.name}.retry", value=float(attempts)
                    ),
                )
            await asyncio.sleep(delay)
    if last_exc:
        raise last_exc
    raise RuntimeError("call_with_backoff failed without exception")


async def wait_for_recovery(circuit_breaker: CircuitBreaker) -> None:
    """Wait for a circuit breaker to allow requests again."""

    if circuit_breaker.is_open:
        delay = circuit_breaker.time_until_recovery()
        logger.info(
            "Circuit breaker %s open; waiting %.2fs for recovery",
            circuit_breaker.name,
            delay,
        )
        await asyncio.sleep(delay)


__all__ = [
    "CircuitBreaker",
    "APICircuitBreaker",
    "CircuitBreakerOpenError",
    "call_with_backoff",
    "wait_for_recovery",
    "CIRCUIT_BREAKER_STATE_EVENT",
]
