#!/usr/bin/env python3
"""
Grid Search QUALIA Real - Sistema Completo com Dados Reais
YAA IMPLEMENTATION: Teste do sistema QUALIA completo com dados históricos reais.
"""

import asyncio
import json
import sys
from datetime import datetime, timedelta
from pathlib import Path
import time
from typing import Dict, List, Any

import pandas as pd
import numpy as np
import requests

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

from src.qualia.strategies.nova_estrategia_qualia import QualiaTSVFStrategy
from src.qualia.strategies.params import QualiaTSVFParams
from src.qualia.consciousness.amplification_calibrator import AmplificationCalibrator
from src.qualia.config.hyperparams_loader import HyperParamsLoader
from src.qualia.utils.logger import get_logger

logger = get_logger(__name__)


class QualiaRealDataFetcher:
    """Fetcher de dados reais otimizado para QUALIA."""
    
    BASE_URL = "https://api.binance.com/api/v3"
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'QUALIA-RealGridSearch/1.0'
        })
    
    def fetch_historical_data(self, symbol: str, interval: str = "1h", days: int = 90) -> pd.DataFrame:
        """Busca dados históricos formatados para QUALIA."""
        try:
            # Calcula timestamps
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            logger.info(f"🔄 Buscando dados reais para {symbol} ({days} dias)")
            
            # Parâmetros da API
            params = {
                'symbol': symbol.replace('/', ''),
                'interval': interval,
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            # Faz requisição
            response = self.session.get(f"{self.BASE_URL}/klines", params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                logger.warning(f"⚠️ Nenhum dado retornado para {symbol}")
                return pd.DataFrame()
            
            # Converte para DataFrame no formato QUALIA
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Processa dados para formato QUALIA
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']].copy()
            
            # Converte para float
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index()
            
            # Remove dados inválidos
            df = df.dropna()
            
            logger.info(f"✅ Dados obtidos para {symbol}: {len(df)} candles de {df.index[0]} a {df.index[-1]}")
            return df
            
        except Exception as e:
            logger.error(f"❌ Erro ao buscar dados para {symbol}: {e}")
            return pd.DataFrame()


def run_qualia_backtest(
    df: pd.DataFrame, 
    symbol: str,
    price_amp: float, 
    news_amp: float, 
    min_conf: float
) -> Dict[str, Any]:
    """Executa backtest usando o sistema QUALIA real."""
    
    if df.empty or len(df) < 100:
        return {
            'total_return_pct': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown_pct': 0.0,
            'win_rate': 0.0,
            'total_trades': 0,
            'volatility': 0.0,
            'error': 'Dados insuficientes'
        }
    
    try:
        # Carrega hiperparâmetros QUALIA
        hyperparams_loader = HyperParamsLoader()
        hyperparams = hyperparams_loader.load()
        
        # Atualiza parâmetros de amplificação
        hyperparams.price_amplification = price_amp
        hyperparams.news_amplification = news_amp
        hyperparams.min_confidence = min_conf
        
        # Cria calibrador de amplificação
        calibrator = AmplificationCalibrator(hyperparams=hyperparams)
        
        # Configura parâmetros da estratégia QUALIA
        strategy_params = QualiaTSVFParams(
            # Parâmetros TSVF
            s1_tsvf_window=hyperparams.tsvf_window,
            tsvf_vector_size=hyperparams.tsvf_vector_size,
            tsvf_alpha=hyperparams.tsvf_alpha,
            tsvf_gamma=hyperparams.tsvf_gamma,
            cE=hyperparams.tsvf_cE,
            cH=hyperparams.tsvf_cH,
            
            # Parâmetros de estratégia
            s1_strength_threshold=hyperparams.s1_strength_threshold,
            s2_sma_short_period=hyperparams.s2_sma_short_period,
            s2_sma_long_period=hyperparams.s2_sma_long_period,
            s2_rsi_period=hyperparams.s2_rsi_period,
            s3_bb_period=hyperparams.s3_bb_period,
            s3_bb_std_dev=hyperparams.s3_bb_std_dev,
            
            # Parâmetros de risco
            max_position_size=hyperparams.max_position_size,
            stop_loss_pct=hyperparams.stop_loss_pct,
            take_profit_pct=hyperparams.take_profit_pct,
        )
        
        # Cria estratégia QUALIA
        strategy = QualiaTSVFStrategy(symbol, "1h", strategy_params)
        
        # Executa análise de mercado usando QUALIA
        quantum_metrics = {}  # Seria preenchido pelo sistema real
        trading_context = {}  # Seria preenchido pelo sistema real
        similar_patterns = []  # Seria preenchido pelo sistema de memória
        
        # Simula análise QUALIA para cada ponto no tempo
        positions = []
        returns = []
        
        # Janela mínima para TSVF
        min_window = max(strategy_params.s1_tsvf_window, 50)
        
        for i in range(min_window, len(df)):
            try:
                # Dados da janela atual
                window_data = df.iloc[max(0, i-min_window):i+1].copy()
                
                if len(window_data) < min_window:
                    positions.append(0.0)
                    returns.append(0.0)
                    continue
                
                # Análise QUALIA
                decision_dict = strategy.analyze_market(
                    market_data=window_data,
                    quantum_metrics=quantum_metrics,
                    trading_context=trading_context,
                    similar_past_patterns=similar_patterns,
                )
                
                # Extrai posição e confiança
                position = decision_dict.get('position', 0.0)
                confidence = decision_dict.get('confidence', 0.0)
                
                # Aplica filtro de confiança mínima
                if confidence < min_conf:
                    position = 0.0
                
                # Aplica amplificação calibrada
                amplified_position = position * calibrator.get_calibrated_amplification("price")
                
                # Limita posição
                final_position = np.clip(amplified_position, -1.0, 1.0)
                positions.append(final_position)
                
                # Calcula retorno
                if i > 0:
                    market_return = (df['close'].iloc[i] / df['close'].iloc[i-1]) - 1
                    strategy_return = positions[-2] * market_return if len(positions) > 1 else 0.0
                    returns.append(strategy_return)
                else:
                    returns.append(0.0)
                    
            except Exception as e:
                logger.debug(f"Erro no ponto {i}: {e}")
                positions.append(0.0)
                returns.append(0.0)
        
        # Calcula métricas de performance
        if not returns or len(returns) == 0:
            return {
                'total_return_pct': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown_pct': 0.0,
                'win_rate': 0.0,
                'total_trades': 0,
                'volatility': 0.0,
                'error': 'Sem retornos calculados'
            }
        
        returns_series = pd.Series(returns)
        cumulative_returns = (1 + returns_series).cumprod()
        
        # Métricas
        total_return = cumulative_returns.iloc[-1] - 1 if len(cumulative_returns) > 0 else 0.0
        
        # Anualizadas (8760 horas por ano)
        periods_per_year = 8760
        mean_return = returns_series.mean() * periods_per_year
        volatility = returns_series.std() * np.sqrt(periods_per_year)
        sharpe_ratio = mean_return / volatility if volatility > 0 else 0.0
        
        # Max drawdown
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = abs(drawdown.min()) if len(drawdown) > 0 else 0.0
        
        # Trades
        position_changes = pd.Series(positions).diff().abs()
        total_trades = int(position_changes.sum())
        
        # Win rate
        winning_periods = (returns_series > 0).sum()
        win_rate = winning_periods / len(returns_series) if len(returns_series) > 0 else 0.0
        
        return {
            'total_return_pct': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'volatility': volatility,
            'periods_analyzed': len(returns),
            'avg_position': np.mean(np.abs(positions)) if positions else 0.0
        }
        
    except Exception as e:
        logger.error(f"❌ Erro no backtest QUALIA: {e}")
        return {
            'total_return_pct': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown_pct': 0.0,
            'win_rate': 0.0,
            'total_trades': 0,
            'volatility': 0.0,
            'error': str(e)
        }


def run_qualia_real_grid_search():
    """Executa grid search com sistema QUALIA completo e dados reais."""
    print("🚀 QUALIA REAL GRID SEARCH - SISTEMA COMPLETO")
    print("=" * 60)
    
    # Inicializa fetcher
    fetcher = QualiaRealDataFetcher()
    
    # Configuração do grid search
    symbols = ["BTC/USDT", "ETH/USDT"]
    
    # Grid de parâmetros QUALIA
    price_amps = [2.0, 5.0, 8.0]      # Amplificação de preço
    news_amps = [1.5, 4.0, 7.0]       # Amplificação de notícias  
    min_confs = [0.4, 0.6, 0.8]       # Confiança mínima
    
    total_combinations = len(price_amps) * len(news_amps) * len(min_confs) * len(symbols)
    print(f"📊 Total de combinações: {total_combinations}")
    print(f"🧠 Usando sistema QUALIA completo: QualiaTSVFStrategy + AmplificationCalibrator")
    
    results = []
    
    for symbol in symbols:
        print(f"\n📈 Processando {symbol} com sistema QUALIA...")
        
        # Busca dados reais
        df = fetcher.fetch_historical_data(symbol, days=120)  # Mais dados para TSVF
        
        if df.empty:
            logger.error(f"❌ Falha ao obter dados para {symbol}")
            continue
        
        for price_amp in price_amps:
            for news_amp in news_amps:
                for min_conf in min_confs:
                    logger.info(f"🔄 Testando ({price_amp:.1f}, {news_amp:.1f}, {min_conf:.1f})")
                    
                    result = run_qualia_backtest(df, symbol, price_amp, news_amp, min_conf)
                    result.update({
                        'symbol': symbol,
                        'price_amplification': price_amp,
                        'news_amplification': news_amp,
                        'min_confidence': min_conf,
                        'data_points': len(df),
                        'strategy': 'QualiaTSVFStrategy'
                    })
                    results.append(result)
                    
                    if 'error' not in result:
                        print(f"   ✅ Return {result['total_return_pct']:.2%}, "
                              f"Sharpe {result['sharpe_ratio']:.3f}, "
                              f"Trades {result['total_trades']}")
                    else:
                        print(f"   ❌ Erro: {result['error']}")
    
    # Análise dos resultados
    if results:
        print(f"\n" + "="*60)
        print(f"📊 RESULTADOS QUALIA REAL GRID SEARCH")
        print(f"="*60)
        
        # Filtra resultados válidos
        valid_results = [r for r in results if 'error' not in r]
        
        if valid_results:
            df_results = pd.DataFrame(valid_results)
            
            print(f"\n📈 Estatísticas (Sistema QUALIA Real):")
            print(f"   • Testes válidos: {len(valid_results)}/{len(results)}")
            print(f"   • Sharpe médio: {df_results['sharpe_ratio'].mean():.3f}")
            print(f"   • Return médio: {df_results['total_return_pct'].mean():.2%}")
            print(f"   • Drawdown médio: {df_results['max_drawdown_pct'].mean():.2%}")
            print(f"   • Win Rate médio: {df_results['win_rate'].mean():.2%}")
            print(f"   • Trades médios: {df_results['total_trades'].mean():.0f}")
            
            # Top 3 configurações
            top_configs = df_results.nlargest(3, 'sharpe_ratio')
            print(f"\n🏆 TOP 3 CONFIGURAÇÕES QUALIA:")
            for i, (_, row) in enumerate(top_configs.iterrows(), 1):
                print(f"   {i}. {row['symbol']}: ({row['price_amplification']:.1f}, "
                      f"{row['news_amplification']:.1f}, {row['min_confidence']:.1f})")
                print(f"      Return: {row['total_return_pct']:.2%}, "
                      f"Sharpe: {row['sharpe_ratio']:.3f}, "
                      f"Drawdown: {row['max_drawdown_pct']:.2%}, "
                      f"Trades: {row['total_trades']}")
        
        # Salva resultados
        output_dir = Path("results/qualia_real_grid_search")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = int(time.time())
        output_file = output_dir / f"qualia_real_results_{timestamp}.json"
        
        # Salva como JSON
        with open(output_file, 'w') as f:
            json.dump({
                'metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'total_combinations': len(results),
                    'valid_results': len(valid_results) if valid_results else 0,
                    'symbols': symbols,
                    'strategy': 'QualiaTSVFStrategy',
                    'data_source': 'Binance API',
                    'system': 'QUALIA Complete'
                },
                'results': results
            }, f, indent=2)
        
        print(f"\n💾 Resultados salvos em: {output_file}")
        
        # Salva CSV
        if valid_results:
            csv_file = output_dir / f"qualia_real_results_{timestamp}.csv"
            df_results.to_csv(csv_file, index=False)
            print(f"📊 CSV salvo em: {csv_file}")
        
        print(f"\n✅ QUALIA Real Grid Search concluído!")
        
    else:
        print("❌ Nenhum resultado obtido")


if __name__ == "__main__":
    run_qualia_real_grid_search()
