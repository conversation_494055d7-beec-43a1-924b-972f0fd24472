global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'qualia-trading'
    environment: 'production'

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node Exporter (system metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # StatsD Exporter (QUALIA metrics via StatsD)
  - job_name: 'statsd-exporter'
    static_configs:
      - targets: ['statsd-exporter:9102']
    scrape_interval: 5s
    metrics_path: /metrics

  # QUALIA Trading System (direct Prometheus endpoint if available)
  - job_name: 'qualia-trading'
    static_configs:
      - targets: ['host.docker.internal:8080']  # Adjust port as needed
    scrape_interval: 10s
    metrics_path: /metrics
    scrape_timeout: 5s

  # QUALIA Bayesian Optimizer
  - job_name: 'qualia-bayesian-optimizer'
    static_configs:
      - targets: ['host.docker.internal:8001']  # FastAPI service port
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # QUALIA Live Feed (if available)
  - job_name: 'qualia-live-feed'
    static_configs:
      - targets: ['host.docker.internal:8002']
    scrape_interval: 5s
    metrics_path: /metrics
    scrape_timeout: 5s

# Remote write configuration (optional - for long-term storage)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint"
#     basic_auth:
#       username: "user"
#       password: "pass"
