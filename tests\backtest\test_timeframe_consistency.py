import pandas as pd
import pytest
from types import SimpleNamespace
from unittest.mock import AsyncMock

from qualia.qualia_trading_system import QUALIARealTimeTrader
from qualia.market.qast_evolutionary_strategy import QASTEvolutionaryStrategy


@pytest.mark.asyncio
async def test_init_system_preloads_primary_tf(monkeypatch):
    monkeypatch.setenv(
        "QUALIA_SECRET_KEY",
        "MDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDA=",
    )
    async def dummy_fetch(symbol: str, timeframe: str, limit=100):
        freq = {
            "15m": "15min",
            "5m": "5min",
        }.get(timeframe, "1H")
        idx = pd.date_range("2020-01-01", periods=limit, freq=freq)
        return pd.DataFrame(
            {
                "timestamp": idx,
                "open": [1.0] * limit,
                "high": [1.0] * limit,
                "low": [1.0] * limit,
                "close": [1.0] * limit,
                "volume": [1.0] * limit,
            }
        )

    monkeypatch.setenv("KRAKEN_API_KEY", "k")
    monkeypatch.setenv("KRAKEN_SECRET_KEY", "s")

    monkeypatch.setattr(
        "src.qualia.market.kraken_integration.KrakenIntegration.fetch_ohlcv",
        AsyncMock(side_effect=dummy_fetch),
    )
    monkeypatch.setattr(
        "src.qualia.market.kraken_integration.KrakenIntegration.initialize_connection",
        AsyncMock(),
    )
    monkeypatch.setattr(
        "src.qualia.market.kraken_integration.KrakenIntegration.close",
        AsyncMock(),
    )

    def dummy_load(self):
        self.loaded_strategy_configs = {"strategy_config": {"preload_candles_15m": 100}}
        self.risk_profile_configs = {}

    monkeypatch.setattr(QUALIARealTimeTrader, "_load_strategy_parameters", dummy_load)
    monkeypatch.setattr(
        QUALIARealTimeTrader,
        "_setup_risk_managers",
        lambda self: setattr(
            self,
            "risk_managers",
            {s: SimpleNamespace(stop_loss_adjustment=None) for s in self.symbols},
        ),
    )
    monkeypatch.setattr(
        QUALIARealTimeTrader,
        "_setup_qpm",
        lambda self: setattr(
            self, "qpm_memory", SimpleNamespace(warm_start=lambda: None)
        ),
    )
    monkeypatch.setattr(
        QUALIARealTimeTrader,
        "_setup_universe",
        lambda self: setattr(self, "qualia_universe", SimpleNamespace(n_qubits=1)),
    )
    monkeypatch.setattr(QUALIARealTimeTrader, "_setup_analysis_core", lambda self: None)
    monkeypatch.setattr(QUALIARealTimeTrader, "_setup_qmc", lambda self: None)
    monkeypatch.setattr(
        "src.qualia.qualia_trading_system.create_strategy_and_qast_engine",
        lambda *a, **k: (
            SimpleNamespace(required_initial_data_length=0),
            SimpleNamespace(population=[1], initialize_population=lambda: None),
        ),
    )
    monkeypatch.setattr(
        "src.qualia.qualia_trading_system.StrategyFactory",
        lambda: SimpleNamespace(
            create_strategy=lambda alias, params, context: SimpleNamespace()
        ),
    )

    trader = QUALIARealTimeTrader(
        symbols=["BTC/USDT"],
        timeframes=["15m"],
        capital=1000.0,
        risk_profile="moderate",
        data_source="kraken",
        kraken_api_key="k",
        kraken_secret_key="s",
    )

    await trader._init_system_components()

    assert "15m" in trader.market_data["BTC/USDT"]
    assert len(trader.market_data["BTC/USDT"]["15m"]) >= 100


from qualia.strategies.strategy_interface import (
    TradingStrategy,
    TradingContext,
    OrderDecision,
)


class DummyStrategyWithTF(TradingStrategy):
    def __init__(self, timeframe: str = "1h"):
        super().__init__(params={"symbol": "BTC/USDT", "timeframe": timeframe})
        self.symbol = "BTC/USDT"
        self.timeframe = timeframe
        self.backtest_timeframe = None

    def analyze_market(
        self,
        market_data: pd.DataFrame,
        trading_context: TradingContext,
        quantum_metrics=None,
        similar_past_patterns=None,
    ) -> OrderDecision:
        return OrderDecision(signal="HOLD", confidence=0.0)

    def backtest(
        self,
        market_data_map,
        initial_capital=0.0,
        risk_per_trade_pct=0.01,
        timeframe=None,
    ):
        self.backtest_timeframe = timeframe
        return {"trades_details": [], "final_capital": initial_capital}


def test_qast_passes_strategy_timeframe():
    df = pd.DataFrame(
        {
            "open": [1, 2],
            "high": [1, 2],
            "low": [1, 2],
            "close": [1, 2],
            "volume": [1, 2],
        },
        index=pd.date_range("2020-01-01", periods=2, freq="H"),
    )
    strat = DummyStrategyWithTF("5m")
    qast = QASTEvolutionaryStrategy(
        strategy_template=strat, qualia_consciousness=None, population_size=1
    )
    fitness, _ = qast._evaluate_strategy(strat, df)
    assert strat.backtest_timeframe == "5m"
