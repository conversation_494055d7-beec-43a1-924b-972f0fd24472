# Resonance Operator

O operador de *Resonance* detecta frequências harmônicas em séries temporais e avalia a força de ressonância de padrões de mercado. Ele identifica harmônicos relevantes por meio de transformada rápida de Fourier e mede a coerência de fase.

Principais características
-------------------------
- Frequências harmônicas configuráveis e normalizadas.
- Detecção de *phase locking* entre pares harmônicos.
- Cálculo de força de ressonância combinando amplitude, coerência de fase e relações baseadas na razão áurea.
- Integração opcional com `QuantumPatternMemory`, com cache LRU para padrões históricos.
- `phase_lock_ratio` em `qualia.core.resonance` mede a fração de pares
  harmônicos sincronizados em fase.


## Cache LRU de padrões históricos

Quando o operador utiliza o `QuantumPatternMemory`, os padrões recuperados são
armazenados em um cache LRU de até 32 entradas. Esse mecanismo evita
recomputações quando a mesma série temporal é analisada repetidamente. As
entradas do cache podem ser invalidadas pela função `invalidate_pattern_cache()`.

```python
from qualia.core.resonance import identify_dominant_resonances

result = identify_dominant_resonances(
    field,
    qpm,
    top_n=3,
    sampling_rate=128,
    use_cache=False,
)
```

O parâmetro ``use_cache`` controla se o cache deve ser consultado. Definir
``False`` força nova busca na memória holográfica.

A função `apply_resonance` provê uma transformação simplificada para experimentos e testes.

## Exemplo de Uso
```python
import numpy as np
from qualia.core.resonance import ResonanceOperator

config = {"harmonic_frequencies": [1, 2, 3]}
operator = ResonanceOperator(config)

data = np.sin(np.linspace(0, 4 * np.pi, 256))
state = asyncio.run(operator.analyze_resonance(data, timestamp=0.0))
print(state.resonance_strength)
```
