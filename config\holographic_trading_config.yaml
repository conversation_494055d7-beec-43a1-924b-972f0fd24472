# QUALIA Holographic Trading Configuration
# Sistema de trading automatizado baseado em universo holográfico

holographic_trading:
  # Modo de operação
  enabled: true
  mode: "paper"  # paper ou live
  
  # Capital Management
  capital:
    initial: 10000.0
    currency: "USDT"
    
  # Risk Management
  risk:
    per_trade_pct: 1.0        # 1% do capital por trade
    max_drawdown_pct: 20.0    # Stop trading se drawdown > 20%
    max_volatility: 5.0       # Block trades se volatility > 5%
    min_confidence: 0.7       # Confidence mínima para execução
    
  # Trading Parameters
  trading:
    allowed_timeframes: ["5m", "15m", "1h"]
    trading_symbols: ["BTC/USDT", "ETH/USDT", "ADA/USDT", "SOL/USDT", "MATIC/USDT", "DOT/USDT"]
    max_positions: 5
    position_timeout: 3600    # Close position após 1h se não triggered
    signal_cooldown: 300      # 5 min entre sinais do mesmo símbolo
    max_signals_per_hour: 6   # Max sinais por símbolo por hora
    
  # Data Sources
  data_sources:
    binance:
      enabled: true
      symbols: ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'MATICUSDT', 'DOTUSDT', 'LINKUSDT', 'UNIUSDT']
      interval: 30  # seconds
      
    news:
      enabled: true
      sources:
        - "https://cointelegraph.com/rss"
        - "https://decrypt.co/feed"
        - "https://cryptonews.com/news/feed/"
      interval: 60  # seconds
      
    sentiment:
      enabled: true
      fear_greed_index: true
      interval: 300  # seconds
      
  # Live Feed System Configuration (D-03.2 Integration)
  live_feed:
    enabled: true
    mode: "hybrid"  # "live_only", "traditional_only", "hybrid"

    # Live Feed Priority and Fallback
    live_feed_priority: 1
    enable_fallback: true
    fallback_timeout: 30.0

    # Data Validation
    enable_data_validation: true
    price_variance_threshold: 0.01  # 1% max variance

    # Performance Configuration
    update_frequency: 1.0  # seconds
    batch_processing: true

    # Paper Trading Configuration
    paper_trading_mode: true
    paper_trading_symbols: ["BTC/USDT", "ETH/USDT", "ADA/USDT"]

    # Optimized Parameters (from D-03.2 specification)
    optimized_params:
      news_amp: 11.3
      price_amp: 1.0
      min_conf: 0.37

    # Live Feed Data Collector Configuration
    data_collector:
      enable_kucoin: true
      enable_binance: true
      enable_coinbase: false
      aggregation_enabled: true
      enable_technical_indicators: true
      enable_holographic_conversion: true
      confidence_threshold: 0.37

      # Technical Indicators
      rsi_period: 14
      volume_lookback: 20

      # Buffer and Processing
      update_interval: 1.0
      batch_size: 10
      max_age_seconds: 30.0
      price_variance_threshold: 0.005  # 0.5%

  # KuCoin Configuration (Enhanced for Live Feed)
  exchanges:
    kucoin:
      api_key: ""           # Set via environment KUCOIN_API_KEY
      api_secret: ""        # Set via environment KUCOIN_API_SECRET
      password: ""          # Set via environment KUCOIN_PASSPHRASE
      sandbox: false         # Use sandbox for testing
      timeout: 30.0
      rate_limit: 4.0

      # Live Feed Specific Settings
      websocket_enabled: true
      rest_fallback: true
      reconnection_enabled: true
      max_reconnection_attempts: 5
      reconnection_delay: 5.0
      
  # Holographic Universe Parameters (Updated for Live Feed Integration)
  holographic:
    field_size: [200, 200]
    time_steps: 500
    diffusion_rate: 0.35        # Aumentado para mais propagação
    feedback_strength: 0.08     # Aumentado para mais amplificação

    # Pattern Detection
    min_pattern_strength: 0.3
    wavelet_scales: [1, 40]
    analysis_interval: 15  # seconds

    # Optimized parameters for live feed integration
    amplitude_multiplier: 5.0   # Multiplica amplitudes dos eventos
    pattern_threshold: 0.37     # Updated to match optimized min_conf
    confidence_threshold: 0.37  # Updated to match optimized min_conf
    signal_threshold: 0.5       # Mantido para filtragem de sinais

    # Live Feed Integration Parameters
    live_feed_amplification:
      price_events: 1.0         # Matches optimized price_amp
      news_events: 11.3         # Matches optimized news_amp
      volume_events: 2.0        # Additional amplification for volume
      technical_events: 1.5     # Amplification for technical indicators
    
  # Performance Monitoring
  monitoring:
    enabled: true
    log_level: "INFO"
    metrics_interval: 60  # seconds
    save_results: true
    results_path: "results/holographic_trading/"
    
  # Timing Configuration
  timing:
    data_collection_interval: 30     # Reduzido para mais coletas
    universe_evolution_interval: 10  # Mais evolução
    signal_generation_interval: 15   # Mais análise de sinais
    position_monitoring_interval: 60
    performance_update_interval: 60  # seconds 