#!/usr/bin/env python3
"""
QUALIA Simplified Trading Signal Validation System
Foco: Testar geração de sinais e performance básica
"""

import asyncio
import logging
import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import statistics

@dataclass
class TradingSignal:
    """Estrutura de sinal de trading"""
    timestamp: datetime
    symbol: str
    signal_type: str  # 'BUY', 'SELL', 'HOLD'
    strength: float   # 0.0 - 1.0
    confidence: float # 0.0 - 1.0
    entry_price: float
    stop_loss: float
    take_profit: float
    position_size: float
    source: str

@dataclass
class Position:
    """Estrutura de posição"""
    id: str
    signal: TradingSignal
    entry_time: datetime
    entry_price: float
    current_price: float
    quantity: float
    side: str  # 'long', 'short'
    status: str  # 'open', 'closed'
    pnl: float = 0.0
    pnl_percentage: float = 0.0
    exit_time: Optional[datetime] = None
    exit_reason: str = ""

class SimplifiedTradingValidator:
    """Sistema simplificado de validação de trading"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.signals: List[TradingSignal] = []
        self.positions: List[Position] = []
        
        # Configurações
        self.symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT']
        self.initial_balance = 10000.0
        self.current_balance = self.initial_balance
        
        # Simulação de dados de mercado
        self.market_prices = {
            'BTCUSDT': 107000.0,
            'ETHUSDT': 3500.0,
            'ADAUSDT': 0.9,
            'SOLUSDT': 200.0
        }
        
        # Métricas de performance
        self.total_signals = 0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        
    def generate_market_data(self) -> Dict[str, Dict[str, Any]]:
        """Gera dados de mercado simulados"""
        market_data = {}
        
        for symbol in self.symbols:
            # Simula mudança de preço
            price_change = random.uniform(-0.03, 0.03)  # ±3%
            self.market_prices[symbol] *= (1 + price_change)
            
            # Gera indicadores técnicos
            rsi = random.uniform(15, 85)
            volume_ratio = random.uniform(0.5, 3.0)
            volatility = random.uniform(0.01, 0.08)
            
            market_data[symbol] = {
                'symbol': symbol,
                'price': self.market_prices[symbol],
                'change_24h': price_change * 100,
                'rsi': rsi,
                'volume_ratio': volume_ratio,
                'volatility': volatility,
                'timestamp': datetime.now()
            }
            
        return market_data
    
    def analyze_patterns_and_generate_signals(self, market_data: Dict[str, Any]) -> List[TradingSignal]:
        """Analisa dados e gera sinais de trading"""
        signals = []
        
        for symbol, data in market_data.items():
            # Estratégia 1: RSI Extremos
            if data['rsi'] < 25:  # Oversold
                signal = self._create_signal(
                    symbol=symbol,
                    signal_type='BUY',
                    strength=0.8,
                    confidence=0.7,
                    entry_price=data['price'],
                    source='rsi_oversold'
                )
                signals.append(signal)
                
            elif data['rsi'] > 75:  # Overbought
                signal = self._create_signal(
                    symbol=symbol,
                    signal_type='SELL',
                    strength=0.8,
                    confidence=0.7,
                    entry_price=data['price'],
                    source='rsi_overbought'
                )
                signals.append(signal)
            
            # Estratégia 2: Volume + Momentum
            elif data['volume_ratio'] > 2.0 and abs(data['change_24h']) > 2.0:
                signal_type = 'BUY' if data['change_24h'] > 0 else 'SELL'
                signal = self._create_signal(
                    symbol=symbol,
                    signal_type=signal_type,
                    strength=0.6,
                    confidence=0.6,
                    entry_price=data['price'],
                    source='volume_momentum'
                )
                signals.append(signal)
            
            # Estratégia 3: Volatilidade Baixa (Mean Reversion)
            elif data['volatility'] < 0.02 and abs(data['change_24h']) < 0.5:
                # Gera sinal aleatório para baixa volatilidade
                signal_type = random.choice(['BUY', 'SELL'])
                signal = self._create_signal(
                    symbol=symbol,
                    signal_type=signal_type,
                    strength=0.4,
                    confidence=0.5,
                    entry_price=data['price'],
                    source='low_volatility'
                )
                signals.append(signal)
        
        return signals
    
    def _create_signal(self, symbol: str, signal_type: str, strength: float, 
                      confidence: float, entry_price: float, source: str) -> TradingSignal:
        """Cria sinal de trading com níveis de risco"""
        
        # Calcula stop loss e take profit
        if signal_type == 'BUY':
            stop_loss = entry_price * 0.98  # 2% stop loss
            take_profit = entry_price * 1.06  # 6% take profit
        elif signal_type == 'SELL':
            stop_loss = entry_price * 1.02  # 2% stop loss
            take_profit = entry_price * 0.94  # 6% take profit
        else:
            stop_loss = entry_price
            take_profit = entry_price
        
        # Calcula tamanho da posição (2% de risco por trade)
        risk_amount = self.current_balance * 0.02
        price_risk = abs(entry_price - stop_loss)
        position_size = risk_amount / price_risk if price_risk > 0 else 0.01
        
        # Limita a 10% do capital
        max_position = self.current_balance * 0.1 / entry_price
        position_size = min(position_size, max_position)
        
        return TradingSignal(
            timestamp=datetime.now(),
            symbol=symbol,
            signal_type=signal_type,
            strength=strength,
            confidence=confidence,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            position_size=position_size,
            source=source
        )
    
    def execute_signals(self, signals: List[TradingSignal]) -> List[Position]:
        """Executa sinais como posições"""
        new_positions = []
        
        for signal in signals:
            if signal.signal_type == 'HOLD':
                continue
            
            # Validação básica de risco
            position_value = signal.entry_price * signal.position_size
            if position_value > self.current_balance * 0.2:  # Max 20% por posição
                self.logger.warning(f"❌ Posição muito grande rejeitada: {signal.symbol}")
                continue
            
            # Cria posição
            position = Position(
                id=f"pos_{len(self.positions) + len(new_positions) + 1}",
                signal=signal,
                entry_time=signal.timestamp,
                entry_price=signal.entry_price,
                current_price=signal.entry_price,
                quantity=signal.position_size,
                side='long' if signal.signal_type == 'BUY' else 'short',
                status='open'
            )
            
            new_positions.append(position)
            self.logger.info(f"📈 Posição aberta: {position.id} {signal.symbol} {signal.signal_type} @{signal.entry_price:.2f}")
        
        return new_positions
    
    def update_positions(self, market_data: Dict[str, Any]):
        """Atualiza posições abertas"""
        for position in self.positions:
            if position.status != 'open':
                continue
            
            # Atualiza preço atual
            if position.signal.symbol in market_data:
                position.current_price = market_data[position.signal.symbol]['price']
                
                # Calcula PnL
                if position.side == 'long':
                    position.pnl = (position.current_price - position.entry_price) * position.quantity
                else:
                    position.pnl = (position.entry_price - position.current_price) * position.quantity
                
                position.pnl_percentage = (position.pnl / (position.entry_price * position.quantity)) * 100
                
                # Verifica condições de saída
                self._check_exit_conditions(position)
    
    def _check_exit_conditions(self, position: Position):
        """Verifica se deve fechar posição"""
        signal = position.signal
        current_price = position.current_price
        
        should_close = False
        exit_reason = ""
        
        # Stop Loss
        if position.side == 'long' and current_price <= signal.stop_loss:
            should_close = True
            exit_reason = "stop_loss"
        elif position.side == 'short' and current_price >= signal.stop_loss:
            should_close = True
            exit_reason = "stop_loss"
        
        # Take Profit
        elif position.side == 'long' and current_price >= signal.take_profit:
            should_close = True
            exit_reason = "take_profit"
        elif position.side == 'short' and current_price <= signal.take_profit:
            should_close = True
            exit_reason = "take_profit"
        
        # Tempo máximo (1 hora para teste rápido)
        elif (datetime.now() - position.entry_time).total_seconds() > 3600:
            should_close = True
            exit_reason = "max_time"
        
        if should_close:
            self._close_position(position, exit_reason)
    
    def _close_position(self, position: Position, reason: str):
        """Fecha posição"""
        position.status = 'closed'
        position.exit_time = datetime.now()
        position.exit_reason = reason
        
        # Atualiza estatísticas
        self.current_balance += position.pnl
        self.total_trades += 1
        
        if position.pnl > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1
        
        self.logger.info(f"📉 Posição fechada: {position.id} {reason} PnL: ${position.pnl:.2f} ({position.pnl_percentage:.1f}%)")
    
    def print_performance_report(self):
        """Imprime relatório de performance"""
        closed_positions = [p for p in self.positions if p.status == 'closed']
        
        print("\n" + "="*60)
        print("🎯 QUALIA SIMPLIFIED TRADING VALIDATION REPORT")
        print("="*60)
        
        print("\n📊 RESUMO GERAL:")
        print(f"   • Total de Sinais Gerados: {len(self.signals)}")
        print(f"   • Total de Posições: {len(self.positions)}")
        print(f"   • Posições Fechadas: {len(closed_positions)}")
        print(f"   • Posições Abertas: {len([p for p in self.positions if p.status == 'open'])}")
        
        print("\n💰 PERFORMANCE FINANCEIRA:")
        print(f"   • Capital Inicial: ${self.initial_balance:,.2f}")
        print(f"   • Capital Final: ${self.current_balance:,.2f}")
        total_pnl = self.current_balance - self.initial_balance
        pnl_percentage = (total_pnl / self.initial_balance) * 100
        print(f"   • PnL Total: ${total_pnl:,.2f}")
        print(f"   • Retorno Percentual: {pnl_percentage:.2f}%")
        
        if self.total_trades > 0:
            win_rate = (self.winning_trades / self.total_trades) * 100
            print("\n📈 MÉTRICAS DE TRADING:")
            print(f"   • Win Rate: {win_rate:.1f}%")
            print(f"   • Trades Vencedores: {self.winning_trades}")
            print(f"   • Trades Perdedores: {self.losing_trades}")
            
            if closed_positions:
                avg_pnl = statistics.mean([p.pnl for p in closed_positions])
                print(f"   • PnL Médio por Trade: ${avg_pnl:.2f}")
        
        print("\n📋 ANÁLISE POR ESTRATÉGIA:")
        strategies = {}
        for signal in self.signals:
            if signal.source not in strategies:
                strategies[signal.source] = 0
            strategies[signal.source] += 1
        
        for strategy, count in strategies.items():
            print(f"   • {strategy}: {count} sinais")
        
        print("\n🎲 ÚLTIMAS POSIÇÕES:")
        recent_positions = sorted(self.positions, key=lambda p: p.entry_time, reverse=True)[:5]
        for pos in recent_positions:
            status_icon = "✅" if pos.pnl > 0 else "❌" if pos.pnl < 0 else "⏳"
            print(f"   {status_icon} {pos.signal.symbol} {pos.side} | Entry: ${pos.entry_price:.2f} | PnL: ${pos.pnl:.2f} | {pos.status}")
        
        print("\n" + "="*60)
    
    async def run_validation_cycle(self, duration_minutes: int = 5, cycle_seconds: int = 30):
        """Executa ciclo de validação"""
        self.logger.info(f"🚀 Iniciando validação simplificada por {duration_minutes} minutos...")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        cycle = 0
        
        while datetime.now() < end_time:
            cycle += 1
            self.logger.info(f"\n🔄 Ciclo {cycle} - {datetime.now().strftime('%H:%M:%S')}")
            
            try:
                # 1. Gera dados de mercado
                market_data = self.generate_market_data()
                self.logger.info(f"📊 Dados simulados para {len(market_data)} símbolos")
                
                # 2. Analisa e gera sinais
                new_signals = self.analyze_patterns_and_generate_signals(market_data)
                self.signals.extend(new_signals)
                self.total_signals += len(new_signals)
                
                if new_signals:
                    self.logger.info(f"📡 {len(new_signals)} novos sinais gerados")
                    for signal in new_signals:
                        self.logger.info(f"   • {signal.symbol} {signal.signal_type} (força: {signal.strength:.2f}, fonte: {signal.source})")
                
                # 3. Executa sinais
                new_positions = self.execute_signals(new_signals)
                self.positions.extend(new_positions)
                
                # 4. Atualiza posições existentes
                self.update_positions(market_data)
                
                # 5. Status rápido
                open_positions = len([p for p in self.positions if p.status == 'open'])
                closed_positions = len([p for p in self.positions if p.status == 'closed'])
                current_pnl = sum(p.pnl for p in self.positions if p.status == 'closed')
                
                self.logger.info(f"💼 Status: {open_positions} abertas, {closed_positions} fechadas | PnL: ${current_pnl:.2f}")
                
                # Aguarda próximo ciclo
                await asyncio.sleep(cycle_seconds)
                
            except Exception as e:
                self.logger.error(f"❌ Erro no ciclo {cycle}: {e}")
                await asyncio.sleep(5)
        
        self.logger.info("✅ Validação concluída!")
        self.print_performance_report()

async def main():
    """Função principal"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    validator = SimplifiedTradingValidator()
    
    try:
        await validator.run_validation_cycle(duration_minutes=3, cycle_seconds=15)  # 3 minutos, ciclos de 15s
        
    except KeyboardInterrupt:
        print("\n🛑 Validação interrompida pelo usuário")
        validator.print_performance_report()
    except Exception as e:
        print(f"\n❌ Erro durante validação: {e}")
        validator.print_performance_report()

if __name__ == "__main__":
    asyncio.run(main())
