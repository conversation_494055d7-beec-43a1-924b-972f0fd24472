# Revisado em 2025-06-13 por Codex
"""Interface de linha de comando para executar backtests do QAST.

Execute este módulo diretamente para rodar o ``QUALIARealTimeTrader`` em modo
histórico. É necessário indicar pelo menos um arquivo CSV com dados de mercado
e um arquivo de configuração da estratégia (JSON ou YAML).

Uso básico
----------
```
python -m backtest.backtest_harness --config configs/sample.yaml \
    --historical_data_path data/btc_usdt.csv --symbol BTC/USDT --timeframe 1h
```

Argumentos principais
---------------------
--historical_data_path
    Caminho para o CSV contendo ``timestamp``, ``open``, ``high``, ``low``,
    ``close`` e ``volume``.
--symbol
    <PERSON><PERSON><PERSON><PERSON> negociado, ex.: ``BTC/USDT``.
--timeframe
    Timeframe dos candles (``1m``, ``5m``, ``1h``...).
--strategy_config_json / --config
    Arquivo com parâmetros da estratégia e perfis de risco.
--output_report_path
    Local onde o relatório JSON será salvo. Um segundo arquivo contendo o
    histórico de trades é gerado no mesmo diretório.

Use ``--help`` para ver todas as opções disponíveis.
"""
import argparse
import pandas as pd
import numpy as np
import json
import logging
from qualia.utils.logger import get_logger
from qualia.utils.logging_config import init_logging
from qualia.utils.path_utils import ensure_parent_dirs
from qualia.utils.config_loader import load_config_file
from qualia.metrics.performance_metrics import aggregate_trade_performance
from pathlib import Path
import asyncio
from typing import Any, Dict, List

logger = get_logger(__name__)

try:
    from qualia.qualia_trading_system import QUALIARealTimeTrader

    # Importar outras configs ou tipos necessários,
    # e.g. ScalpingDecision, TradeSignal, etc.
    # from qualia.market.decision_context import TradeContext
except ImportError as e:  # pragma: no cover - optional dependency
    logger.error(
        "Erro de importação: %s. Verifique se o script está na raiz do "
        "projeto QualiaExplorer e se o PYTHONPATH está correto.",
        e,
    )
    QUALIARealTimeTrader = None


def load_strategy_parameters(config_path: str) -> Dict:
    """Load strategy parameters from a JSON or YAML file."""

    return load_config_file(config_path)


def setup_logging(log_level_str: str, log_file_path: Path) -> None:
    """Configure o sistema de logging via ``init_logging``.

    Parameters
    ----------
    log_level_str:
        Nível de logging desejado (DEBUG, INFO, ...).
    log_file_path:
        Caminho do arquivo para persistir logs.

    Notes
    -----
    ``init_logging`` emite um aviso caso seja invocado quando os handlers já
    estiverem configurados. Essa função delega toda a configuração a
    ``init_logging`` e, portanto, o aviso apenas indica que a configuração foi
    preservada.
    """

    init_logging(
        log_level=log_level_str,
        log_file=log_file_path,
        enable_console=True,
        enable_structured=True,
    )  # Aviso via ``logger.warning`` caso já exista configuração de logging

    log_level = getattr(logging, log_level_str.upper(), logging.INFO)
    get_logger("qualia_backtester").setLevel(log_level)
    get_logger("qualia_trading").setLevel(log_level)
    logger.info(
        "Logging configurado para nível: %s em %s", log_level_str.upper(), log_file_path
    )


def calculate_performance_metrics(
    trade_history: List[Dict[str, Any]],
    initial_capital: float,
    candles_df: pd.DataFrame,
) -> Dict[str, float]:
    """Compute aggregated performance metrics."""

    metrics = aggregate_trade_performance(trade_history, initial_capital)
    metrics["total_fees"] = sum(tr.get("fees", 0.0) for tr in trade_history)
    return metrics


async def run_backtest(
    args: argparse.Namespace, analysis_timeout: float = 5.0
) -> Dict[str, Any]:
    """Execute a backtest using ``QUALIARealTimeTrader``.

    Parameters
    ----------
    args : argparse.Namespace
        Parsed command-line arguments controlling the backtest.
    analysis_timeout : float, optional
        Tempo máximo (em segundos) para cada ciclo de análise da estratégia.

    Returns
    -------
    Dict[str, Any]
        Dictionary with aggregated performance metrics.
    """

    logger.info("Iniciando backtest com os seguintes argumentos: %s", args)
    logger.info(
        "BACKTEST_HARNESS: Argumentos de linha de comando recebidos: %s",
        args,
    )

    # Resolver caminhos relativos para absolutos
    args.historical_data_path = str(Path(args.historical_data_path).resolve())
    if args.strategy_config_json:
        args.strategy_config_json = str(Path(args.strategy_config_json).resolve())
    logger.info(
        "BACKTEST_HARNESS: Perfil de Risco Selecionado (args.risk_profile): %s",
        args.risk_profile,
    )
    logger.info(
        "BACKTEST_HARNESS: Risco por Trade %% (args.risk_per_trade_pct): %s",
        args.risk_per_trade_pct,
    )
    logger.info(
        "BACKTEST_HARNESS: Stop Loss %% (args.stop_loss_pct): %s",
        args.stop_loss_pct,
    )
    logger.info(
        "BACKTEST_HARNESS: Take Profit %% (args.take_profit_pct): %s",
        args.take_profit_pct,
    )
    logger.info(
        "BACKTEST_HARNESS: Capital Inicial (args.initial_capital): %s",
        args.initial_capital,
    )
    logger.info(
        "BACKTEST_HARNESS: Arquivo de Configuração de Estratégia (args.strategy_config_json): %s",
        args.strategy_config_json,
    )
    logger.info(
        "BACKTEST_HARNESS: Máximo %% de Capital por Posição (args.max_position_capital_pct): %s",
        (
            args.max_position_capital_pct
            if hasattr(args, "max_position_capital_pct")
            else "N/A (Não fornecido ou não aplicável)"
        ),
    )

    ensure_parent_dirs(
        getattr(args, "log_file", None), getattr(args, "output_report_path", None)
    )

    # Verificar se o JSON de configuração da estratégia foi fornecido
    if not args.strategy_config_json:
        logger.error(
            "Parâmetro --strategy_config_json não fornecido. Encerrando backtest."
        )
        return

    cfg_path = Path(args.strategy_config_json)
    if not cfg_path.exists():
        logger.error(
            "Arquivo de configuração de estratégia não encontrado: %s",
            cfg_path.resolve(),
        )
        raise SystemExit(1)

    # 1. Carregar Dados Históricos
    hist_path = Path(args.historical_data_path)
    if not hist_path.exists():
        logger.error(
            f"Arquivo de dados históricos não encontrado: {hist_path.resolve()}"
        )
        raise SystemExit(1)

    try:
        if hasattr(pd, "read_csv"):
            historical_df = pd.read_csv(hist_path)
        else:  # pragma: no cover - pandas stub fallback
            historical_df = pd.DataFrame()
        logger.info(
            f"Carregados {len(historical_df)} registros de {args.historical_data_path}"
        )
    except FileNotFoundError:
        abs_path = hist_path.resolve()
        logger.error(f"Arquivo de dados históricos não encontrado: {abs_path}")
        raise SystemExit(1)
    except Exception as e:
        logger.error(f"Erro ao carregar dados históricos: {e}", exc_info=True)
        return

    # Validar e preparar DataFrame
    required_cols = ["timestamp", "open", "high", "low", "close", "volume"]
    if hasattr(historical_df, "columns"):
        if not all(col in historical_df.columns for col in required_cols):
            logger.error(
                f"Arquivo CSV deve conter as colunas: {required_cols}. Encontradas: {historical_df.columns.tolist()}"
            )
            return

    try:
        if hasattr(historical_df, "__getitem__") and hasattr(historical_df, "columns"):
            if (
                hasattr(pd, "api")
                and hasattr(pd.api, "types")
                and pd.api.types.is_numeric_dtype(historical_df["timestamp"])
            ):
                if historical_df["timestamp"].iloc[0] > 1e12:  # Heurística para ms vs s
                    historical_df["timestamp"] = pd.to_datetime(
                        historical_df["timestamp"], unit="ms", utc=True
                    )
                else:
                    historical_df["timestamp"] = pd.to_datetime(
                        historical_df["timestamp"], unit="s", utc=True
                    )
            else:
                historical_df["timestamp"] = pd.to_datetime(
                    historical_df["timestamp"], utc=True
                )

        if hasattr(historical_df, "set_index"):
            historical_df.set_index("timestamp", inplace=True)
            historical_df.sort_index(inplace=True)
            logger.info(
                f"Dados históricos processados. Período: {historical_df.index.min()} a {historical_df.index.max()}"
            )
    except Exception as e:
        logger.error(f"Erro ao processar coluna 'timestamp': {e}", exc_info=True)
        return

    # 2. Configurar e Inicializar QUALIARealTimeTrader
    # YAA: O QUALIA_CONFIG deve ser um dicionário que pode ser passado para QUALIARealTimeTrader
    # Configurações específicas para o MetacognitionTrading
    metacognition_specific_config = {
        "metacognition_enabled": not args.disable_metacognition,
        # Outras configs podem ser adicionadas aqui
    }

    # Carregar todos os parâmetros do strategy_parameters.yaml
    all_strategy_params = load_strategy_parameters(args.strategy_config_json)
    if not all_strategy_params:
        logger.error(
            f"Não foi possível carregar {args.strategy_config_json}. Encerrando backtest."
        )
        return

    risk_profiles_from_json = all_strategy_params.get("risk_profile_settings", {})
    initial_capital_for_trader = args.initial_capital  # Default para CLI
    final_risk_profile_settings_for_trader = {}

    if args.risk_profile == "custom":
        logger.info(
            "Usando perfil de risco 'custom' com parâmetros da linha de comando."
        )
        # Para perfil 'custom', construir a configuração a partir dos args da CLI
        custom_risk_config = {
            args.symbol: {  # Configuração por símbolo
                "risk_per_trade_percentage": args.risk_per_trade_pct,
                "max_concurrent_trades_symbol": 1,
            },
            "default": {  # Fallback
                "risk_per_trade_percentage": args.risk_per_trade_pct,
            },
        }
        if (
            hasattr(args, "max_position_capital_pct")
            and args.max_position_capital_pct is not None
        ):
            custom_risk_config[args.symbol][
                "max_position_capital_percentage"
            ] = args.max_position_capital_pct
            custom_risk_config["default"][
                "max_position_capital_percentage"
            ] = args.max_position_capital_pct

        # Adicionar outros parâmetros customizáveis se houver args para eles (ex: drawdown, daily_loss)
        # Por agora, apenas os dois acima são explicitamente construídos para 'custom' a partir de args comuns.
        # O trader ainda poderá pegar outros defaults do QUALIARiskManager se não estiverem aqui.
        final_risk_profile_settings_for_trader = {"custom": custom_risk_config}
        # Para 'custom', o capital da CLI tem precedência.
        initial_capital_for_trader = args.initial_capital

    elif args.risk_profile in risk_profiles_from_json:
        logger.info(
            f"Usando perfil de risco '{args.risk_profile}' carregado de {args.strategy_config_json}."
        )
        profile_config_from_json = risk_profiles_from_json[args.risk_profile]

        # Usar a configuração completa do JSON para este perfil
        # O QUALIARealTimeTrader e QUALIARiskManager já sabem como lidar com esta estrutura.
        final_risk_profile_settings_for_trader = {
            args.risk_profile: profile_config_from_json
        }

        # Para perfis carregados do JSON, todos os parâmetros de risco são lidos
        # da seção ``risk_profile_settings``. O arquivo não define
        # ``initial_capital`` e, portanto, esse valor continua sendo o fornecido
        # via CLI, mesmo se o JSON vier a oferecer um campo equivalente.

        # A chave para o QUALIARealTimeTrader é que o `config` recebido contenha
        # a seção correta de `risk_profile_settings`.
        # A correção foca em popular `final_risk_profile_settings_for_trader`
        # corretamente.
        initial_capital_for_trader = (
            args.initial_capital
        )  # Mantém capital da CLI por enquanto

    else:
        logger.warning(
            "Perfil de risco '%s' não encontrado em %s e não é 'custom'. "
            "Backtest pode usar defaults internos do trader.",
            args.risk_profile,
            args.strategy_config_json,
        )
        # Deixar final_risk_profile_settings_for_trader vazio ou com uma estrutura mínima baseada em CLI
        # para que o QUALIARealTimeTrader use seus defaults internos ou o que for passado diretamente via CLI.
        # Para consistência, vamos construir uma config mínima baseada nos args da CLI que já estava sendo feita.
        cli_fallback_risk_config = {
            args.symbol: {
                "risk_per_trade_percentage": args.risk_per_trade_pct,
                "max_concurrent_trades_symbol": 1,
            },
            "default": {
                "risk_per_trade_percentage": args.risk_per_trade_pct,
            },
        }
        if (
            hasattr(args, "max_position_capital_pct")
            and args.max_position_capital_pct is not None
        ):
            cli_fallback_risk_config[args.symbol][
                "max_position_capital_percentage"
            ] = args.max_position_capital_pct
            cli_fallback_risk_config["default"][
                "max_position_capital_percentage"
            ] = args.max_position_capital_pct

        final_risk_profile_settings_for_trader = {
            # Passa a config construída a partir da CLI sob o nome do perfil da CLI
            args.risk_profile: cli_fallback_risk_config,
        }
        initial_capital_for_trader = args.initial_capital

    logger.info(
        "BACKTEST_HARNESS: Capital Inicial a ser usado pelo Trader: %s",
        initial_capital_for_trader,
    )
    logger.info(
        "BACKTEST_HARNESS: Configurações de Perfil de Risco FINAIS a serem passadas para o Trader: %s",
        json.dumps(final_risk_profile_settings_for_trader, indent=2),
    )

    # YAA: Construir o dicionário de configuração geral para QUALIARealTimeTrader
    # Incluir outros args relevantes que o QUALIARealTimeTrader espera em seu 'config'
    enable_qast_evolution = not getattr(args, "disable_qast_evolution", False)
    trader_config = {
        "metacognition_config": metacognition_specific_config,
        "enable_qast_evolution": enable_qast_evolution,
        "risk_profile_settings": final_risk_profile_settings_for_trader,  # <--- USA A CONFIGURAÇÃO CORRIGIDA
    }

    try:
        trader = QUALIARealTimeTrader(
            symbols=[args.symbol],
            timeframes=[args.timeframe],
            capital=initial_capital_for_trader,  # <--- USA O CAPITAL CORRIGIDO (embora ainda da CLI)
            risk_profile=args.risk_profile,
            risk_per_trade_pct=(
                args.risk_per_trade_pct if args.risk_profile == "custom" else None
            ),  # Passar apenas se for custom
            trading_fee_pct=args.trading_fee_pct,
            mode="simulation",
            data_source="simulation",
            duration_seconds=None,
            disable_metacognition=args.disable_metacognition,
            config=trader_config,
            strategy_config_path=args.strategy_config_json,
        )
        logger.info(
            f"QUALIARealTimeTrader inicializado para backtest em {args.symbol}@{args.timeframe}."
        )
    except Exception as e:
        logger.error(f"Erro ao inicializar QUALIARealTimeTrader: {e}", exc_info=True)
        return

    # 3. Loop de Backtest
    # Para simular o fluxo de dados para o trader, vamos alimentá-lo candle a candle.
    # QUALIARealTimeTrader precisaria de um método para processar um único "tick" ou um novo conjunto de dados.
    # Vamos assumir que podemos definir `trader.market_data` e chamar um método de processamento.

    logger.info(f"Iniciando loop de backtest sobre {len(historical_df)} candles...")

    # Initialize the rolling window DataFrame and lookback limit
    current_market_slice = pd.DataFrame()
    min_lookback = trader_config.get("strategy_lookback_period", 200)

    # Criar uma cópia para não modificar o original no trader
    # O trader vai montar seu próprio self.market_data[symbol][timeframe]
    # A estratégia é simular o que _update_market_data faria, mas com dados históricos.

    # Garantir que o trader tenha os componentes inicializados corretamente
    # A inicialização completa já acontece no __init__ do QUALIARealTimeTrader.

    # A estratégia de LONG/SHORT é gerenciada internamente pelas decisões da estratégia e do risk manager.
    # O tamanho da posição (% do capital) é configurado no risk manager via risk_per_trade_pct.

    for i in range(len(historical_df)):
        if (
            i == 0
        ):  # Pular o primeiro candle se as estratégias precisarem de dados anteriores que não existem
            # Algumas estratégias podem precisar de um buffer inicial. Para um backtest simples,
            # podemos começar a operar a partir do segundo candle, ou de um número maior.
            # Ou, alimentar um histórico inicial antes do loop principal.
            # Por agora, vamos assumir que o trader consegue lidar com isso ou que as estratégias têm aquecimento.
            # O ideal seria que o trader.strategies[symbol].warmup_period fosse considerado.

            # Simular o carregamento inicial que _update_market_data faria com um histórico.
            # Apenas para a primeira iteração, fornecer um chunk maior se as estratégias precisarem de lookback.
            # Ex: um lookback de 200 candles.
            initial_chunk_end = min(min_lookback, len(historical_df))
            current_market_slice = historical_df.iloc[:initial_chunk_end].copy()
        else:
            new_row = historical_df.iloc[[i]]
            current_market_slice = pd.concat([current_market_slice, new_row])
            if len(current_market_slice) > min_lookback:
                current_market_slice = current_market_slice.iloc[-min_lookback:].copy()

        # Atualizar os dados de mercado do trader com o slice atual
        # Esta é a parte que mais precisa de adaptação ou de um método específico no trader.
        # Assumindo que trader.market_data pode ser setado externamente para o backtest:
        if args.symbol not in trader.market_data:
            trader.market_data[args.symbol] = {}
        trader.market_data[args.symbol][args.timeframe] = current_market_slice.copy()

        # Atualizar tickers simulados (bid/ask) - poderia ser mais sofisticado
        if not current_market_slice.empty:
            last_close = current_market_slice["close"].iloc[-1]
            trader.current_tickers[args.symbol] = {
                "bid": last_close * 0.9995,
                "ask": last_close * 1.0005,
                "last": last_close,
            }

        # Chamar a lógica de análise e decisão do trader para o estado atual dos dados
        # Esta parte simula um ciclo do _main_loop do trader.
        try:
            # Essas chamadas são normalmente dentro do _main_loop do trader.
            # Para backtesting, precisamos simular um ciclo de processamento.
            # A função _analyze_symbols já contém a lógica de decisão e _process_trading_decision

            # Antes de analisar, garantir que estado interno do universo/ACE/QPM
            # possa ser atualizado se necessário. A lógica de atualização do
            # universo/ACE normalmente ocorre em intervalos no _main_loop.
            # Para um backtest candle-a-candle, podemos optar por chamá-las a
            # cada N candles ou não chamá-las se o foco for apenas na estratégia
            # e metacognição sobre o sinal. Por ora, focar na execução do
            # _analyze_symbols que dispara a estratégia.

            # Se houver posições abertas, primeiro verificar SL/TP com o novo candle.
            # _process_trading_decision já tem essa lógica, mas precisa ser
            # chamada com o `current_price` do novo candle. O `_analyze_symbols`
            # obterá o `current_price` do `current_market_slice`.

            # Simular um ciclo de análise e decisão
            if args.verbose_candles:
                logger.debug(
                    f"Backtest - Ciclo {i+1}/{len(historical_df)} - Timestamp: {current_market_slice.index[-1]}"
                )
            await asyncio.wait_for(
                trader._analyze_symbols(), timeout=analysis_timeout
            )  # Este método deve usar trader.market_data e trader.current_tickers
            # e internamente chamar _process_trading_decision

            # A lógica de _update_wallet_status é chamada internamente por _close_position.
            # _display_wallet_status pode ser chamado para logar o estado.
            if (i + 1) % trader_config.get(
                "backtest_log_interval_candles", 100
            ) == 0 and args.verbose_candles:
                await trader.hud_manager.display_wallet_status(trader)

        except Exception as e:
            logger.error(
                "Erro no ciclo de backtest %s para o timestamp %s: %s",
                i,
                getattr(historical_df.index, "__getitem__", lambda *_: "?")(i),
                e,
                exc_info=True,
            )
            # Decidir se continua ou para o backtest em caso de erro
            if args.stop_on_error:
                logger.critical("Parando backtest devido a erro.")
                break

        # Simular a passagem de tempo para que o trader possa atualizar estados internos se necessário
        # (ex: se alguma lógica depender de datetime.now(timezone.utc) - embora para backtest isso deva ser evitado)
        # Em um backtest candle-a-candle, o "tempo" é o timestamp do candle.

    logger.info("Loop de backtest concluído.")
    await trader.hud_manager.display_wallet_status(
        trader, force_update=True
    )  # Final wallet status

    # 4. Calcular Métricas de Performance
    final_wallet_state = trader.wallet_state
    trade_history = trader.trade_history

    # Salvar trade history para análise
    if args.output_report_path:
        trade_history_path = (
            Path(args.output_report_path).parent
            / f"{Path(args.output_report_path).stem}_trade_history.json"
        )
        with open(trade_history_path, "w") as f:
            json.dump(trade_history, f, indent=2, default=str)
        logger.info(f"Histórico de trades salvo em: {trade_history_path}")

    metrics = calculate_performance_metrics(
        trade_history, args.initial_capital, historical_df
    )

    # 5. Exibir Relatório
    logger.info("--- Relatório de Performance do Backtest ---")
    if hasattr(historical_df, "index"):
        logger.info(
            f"Período Analisado: {historical_df.index.min()} a {historical_df.index.max()}"
        )
    else:
        logger.info("Período Analisado: n/a")
    logger.info(f"Símbolo: {args.symbol} @ {args.timeframe}")
    logger.info(f"Capital Inicial: ${args.initial_capital:,.2f}")
    logger.info(
        f"Capital Final: ${metrics.get('final_capital', args.initial_capital):,.2f}"
    )
    logger.info(
        f"Total PnL: ${metrics.get('total_pnl', 0.0):,.2f} ({metrics.get('total_pnl_pct', 0.0):.2f}%)"
    )
    logger.info(f"Total de Trades: {metrics.get('total_trades', 0)}")
    logger.info(f"Trades Vencedores: {metrics.get('winning_trades', 0)}")
    logger.info(f"Trades Perdedores: {metrics.get('losing_trades', 0)}")
    logger.info(f"Taxa de Acerto (Win Rate): {metrics.get('win_rate', 0.0):.2f}%")
    logger.info(f"Drawdown Máximo: {metrics.get('max_drawdown_pct', 0.0):.2f}%")
    logger.info(f"Índice de Sharpe: {metrics.get('sharpe_ratio', 0.0):.2f}")
    logger.info(f"PnL Médio por Trade: ${metrics.get('average_trade_pnl', 0.0):,.2f}")
    logger.info(
        f"PnL Médio (Trades Vencedores): ${metrics.get('average_winning_trade', 0.0):,.2f}"
    )
    logger.info(
        f"PnL Médio (Trades Perdedores): ${metrics.get('average_losing_trade', 0.0):,.2f}"
    )
    logger.info(f"Profit Factor: {metrics.get('profit_factor', 0.0):.2f}")
    logger.info("------------------------------------------")

    if args.output_report_path:
        report_data = {
            "args": vars(args),
            "period_start": getattr(historical_df.index, "min", lambda: None)(),
            "period_end": getattr(historical_df.index, "max", lambda: None)(),
            "metrics": metrics,
            "final_wallet_state": final_wallet_state,
        }
        try:
            with open(args.output_report_path, "w") as f:
                json.dump(
                    report_data, f, indent=2, default=str
                )  # default=str para lidar com datetime e outros tipos
            logger.info(f"Relatório de backtest salvo em: {args.output_report_path}")
        except Exception as e:
            logger.error(f"Erro ao salvar relatório JSON: {e}", exc_info=True)

    return metrics


async def main() -> None:
    """Entry point for command-line execution.

    Parses command-line options, configures logging and runs the backtest.
    """

    parser = argparse.ArgumentParser(
        description="QUALIARealTimeTrader Backtesting Harness",
    )
    # Argumentos de Dados e Configuração Geral
    parser.add_argument(
        "--historical_data_path",
        type=str,
        required=True,
        help="Caminho para o arquivo CSV de dados históricos (timestamp, open, high, low, close, volume).",
    )
    parser.add_argument(
        "--symbol",
        type=str,
        required=True,
        help="Símbolo a ser negociado (ex: BTC/USDT).",
    )
    parser.add_argument(
        "--timeframe",
        type=str,
        required=True,
        help="Timeframe dos dados (ex: 1h, 5m, 1d).",
    )
    parser.add_argument(
        "--initial_capital",
        type=float,
        default=10000.0,
        help="Capital inicial para o backtest.",
    )
    # ... outros argumentos ...
    parser.add_argument(
        "--output_report_path",
        type=str,
        default=None,
        help="Path para salvar o relatório de backtest em JSON.",
    )
    parser.add_argument(
        "--log_level",
        type=str,
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="Nível de logging para o backtest.",
    )
    parser.add_argument(
        "--log_file",
        type=str,
        default="logs/backtest_harness.log",
        help="Arquivo para salvar os logs.",
    )

    # Argumentos para controlar funcionalidades do Trader
    parser.add_argument(
        "--disable_metacognition",
        action="store_true",
        help="Desabilita o módulo de Metacognição no trader.",
    )
    parser.add_argument(
        "--disable_qast_evolution",
        action="store_true",  # YAA: Corrigido para store_true
        help="Desabilita a evolução do QAST (se aplicável no trader).",
    )

    # YAA: Adicionado argumento para parar em caso de erro no loop de backtest
    parser.add_argument(
        "--stop_on_error",
        action="store_true",
        help="Para o backtest imediatamente se um erro ocorrer em um ciclo de processamento de candle.",
    )
    parser.add_argument(
        "--verbose_candles",
        action="store_true",
        help="Emite logs detalhados a cada candle durante o backtest.",
    )
    parser.add_argument(
        "--analysis_timeout",
        type=float,
        default=5.0,
        help="Timeout (segundos) para cada ciclo de análise da estratégia.",
    )

    # Argumentos de Risco
    parser.add_argument(
        "--risk_profile",
        type=str,
        default="conservative",
        help=(
            "Perfil de risco a ser usado (ex: conservative, " "moderate, aggressive)."
        ),
    )
    parser.add_argument(
        "--risk_per_trade_pct",
        type=float,
        default=None,  # 1% do capital por trade
        help=(
            "Percentual do capital a ser arriscado por trade. Se não "
            "fornecido, usa o valor do perfil de risco do JSON."
        ),
    )
    parser.add_argument(
        "--stop_loss_pct",
        type=float,
        default=0.02,  # Stop loss de 2%
        help="Percentual de stop-loss em relação ao preço de entrada.",
    )
    parser.add_argument(
        "--take_profit_pct",
        type=float,
        default=0.04,  # Take profit de 4%
        help="Percentual de take-profit em relação ao preço de entrada.",
    )
    parser.add_argument(
        "--trading_fee_pct",
        type=float,
        default=0.0026,
        help="Percentual de taxa cobrada por trade (ex: 0.0026 para 0.26%).",
    )

    # Argumentos específicos da Metacognição (se não desabilitada)

    # YAA: Adicionado argumento para config da estratégia
    parser.add_argument(
        "--strategy_config_json",
        type=str,
        default=None,  # Default para None se não for fornecido
        help="Path para o arquivo JSON de configuração da estratégia.",
    )
    # YAA: Novo argumento para max_position_capital_pct
    parser.add_argument(
        "--max_position_capital_pct",
        type=float,
        default=None,
        help=(
            "Percentual máximo do capital total para uma única posição "
            "(para perfil 'custom'). Ex: 25.0 para 25%%. Se não fornecido "
            "para perfil 'custom', o default do RiskManager será usado."
        ),
    )

    args: argparse.Namespace = parser.parse_args()

    # Criar diretórios de log e resultados de forma idempotente
    ensure_parent_dirs(
        getattr(args, "log_file", None), getattr(args, "output_report_path", None)
    )

    setup_logging(args.log_level, Path(args.log_file))

    # Carregar configuração da estratégia antes de iniciar o backtest
    if args.strategy_config_json:
        try:
            load_strategy_parameters(args.strategy_config_json)
        except Exception as exc:  # pragma: no cover - erro reportado no log
            logger.error("Falha ao carregar configuração: %s", exc)
            return

    await run_backtest(args, analysis_timeout=args.analysis_timeout)


def cli() -> None:
    """Entry point for ``console_scripts``."""

    asyncio.run(main())


if __name__ == "__main__":
    cli()
