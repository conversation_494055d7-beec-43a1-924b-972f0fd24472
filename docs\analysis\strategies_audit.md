# Auditoria das Estratégias de Trading

Este documento resume a auditoria realizada no diretório `src/qualia/strategies` e suas subpastas. A análise considera a coerência da implementação, arquitetura, aderência às metodologias do projeto e potenciais melhorias.

## Estruturas Principais

- **QUALIAEnhancedScalpingStrategy** (`scalping_strategies.py`)
- **CompositeStrategy** (`composite/`)
- **EnhancedQuantumMomentumStrategy** (`enhanced_quantum_momentum/`) – alias adicional `quantum_momentum_v2`
- **QuantumTrendReversalStrategy** (`quantum_trend_reversal/`)
- **QualiaTSVFStrategy** (`nova_estrategia_qualia/`)
- **Legacy ScalpingStrategy** (`legacy/` – apenas referência histórica)

Funções auxiliares como `metrics_adapter.py`, `strategy_utils.py` e `params.py` dão suporte às estratégias.

## QUALIAEnhancedScalpingStrategy

- **Descrição:** estratégia de scalping combinando indicadores clássicos (VWAP, EMAs, RSI, ATR) e métricas quânticas. Possui modo piloto baseado em RSI e ajustes para perfis de risco.
- **Fluxo:** inicializa parâmetros conforme perfil, calcula indicadores (com cache), gera sinais por cruzamento de médias e integra métricas quânticas.
- **Avaliação:** código extenso (~1.3k linhas) em um único arquivo dificulta manutenção. Recomendado dividir em submódulos (`indicators`, `signal`, `backtest`) e adicionar testes.

## CompositeStrategy

- **Descrição:** orquestra subestratégias e combina sinais ponderados. Suporta pesos dinâmicos por controlador QAST.
- **Fluxo:** configura subestratégias, executa `analyze_market` centralizado e agrega sinais com stop-loss/take-profit.
- **Avaliação:** arquitetura modular facilita leitura. Alguns métodos têm muitos parâmetros; refatoração pode reduzir complexidade. Carece de testes de integração.

## EnhancedQuantumMomentumStrategy

- **Descrição:** versão aprimorada da estratégia de momentum quântico, utilizando RSI, MACD, EMAs e métricas quânticas para avaliar regime de mercado.
- **Alias:** registrada como `quantum_momentum_v2` e `EnhancedQuantumMomentumStrategy`.
- **Fluxo:** calcula indicadores e métricas quânticas, combina pontuações e aplica filtros de volatilidade. Usa módulo `risk.py` para dimensionamento de posição.
- **Avaliação:** métodos longos em `generate_signals`; sugere-se modularizar cálculo de pontuação e testes de `position_sizing`.

## QuantumTrendReversalStrategy

- **Descrição:** identifica reversões de tendência utilizando indicadores clássicos e análise quântica via Qiskit.
- **Fluxo:** configura parâmetros quânticos, calcula ADX e SMA, chama circuitos do Qiskit e gera sinais de reversão.
- **Avaliação:** dependência direta do Qiskit pode falhar em ambientes sem a biblioteca. Recomendado importar condicionalmente e prever modo degradado.

## QualiaTSVFStrategy

- **Descrição:** aplica o Two-State Vector Formalism (TSVF) e combina três subestratégias com controle de risco dinâmico.
- **Fluxo:** carrega histórico, calcula indicadores auxiliares, detecta "PulsoTranscendência" via entropia e executa backtests integrados.
 - **Avaliação:** o arquivo era longo (~1400 linhas) concentrando muita lógica procedural. Parte da função `analyze_market` foi modularizada em métodos privados, mas ainda há espaço para dividir módulos como TSVF, meta-estratégia e backtest.

## Recomendações Gerais

1. **Importações Opcionais:** tornar dependências pesadas (ex.: Qiskit) opcionais para evitar erros em ambientes sem esses pacotes.
2. **Modularização:** dividir arquivos grandes em submódulos para facilitar leitura e manutenção.
3. **Cobertura de Testes:** criar suites de testes para `analyze_market` e `generate_signals`, garantindo que valores de confiança fiquem em `[0, 1]` e que erros sejam tratados.
4. **Interface Consistente:** padronizar assinatura das estratégias conforme `TradingStrategy` e documentar exceções.
5. **Perfil aggressive atualizado:** o parâmetro `max_position_size_pct` passou a fazer parte do perfil de risco `aggressive` no `strategy_parameters.json`. Defina-o como `25.0` para alinhar com os limites documentados.

