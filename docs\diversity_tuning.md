# Ajustes de Diversidade no QUALIAQuantumUniverse

Este documento explica como a métrica `counts_diversity_ratio` é calculada e descreve parâmetros que influenciam a diversidade dos resultados de medição. O limiar mínimo aceitável é definido em `universe_config.min_counts_diversity_ratio` dentro de `config/strategy_parameters.json`. Aqui apresentamos orientações de como aumentar esse valor acima do limite configurado.

## Cálculo da métrica

A função `_calculate_post_run_metrics` em `src/qualia/core/metrics_helpers.py` calcula `counts_diversity_ratio` da seguinte forma:

```python
unique_outcomes = len(counts) if counts else 0
measured_bits = max(len(bitstr.replace(" ", "")) for bitstr in counts) if counts else self.n_qubits
metrics["counts_diversity_ratio"] = unique_outcomes / float(2 ** measured_bits) if counts else 0.0
```

O valor representa a proporção de resultados únicos observados em relação ao número total de estados possíveis.

## Parâmetros que influenciam a diversidade

Diversos fatores podem limitar a diversidade. Os principais são:

- **Número de shots (`shots`)** – valores muito baixos reduzem as chances de observar todos os estados possíveis.
- **Profundidade do circuito (`scr_depth`, `qpu_steps`)** – circuitos rasos ou limitados via `QUALIA_MAX_CIRCUIT_DEPTH` podem gerar evoluções triviais.
- **Otimização de transpile** – níveis altos de otimização podem remover portas e simplificar o circuito.
- **Frequência de medição (`measure_frequency`)** – medições muito frequentes colapsam o estado antes que a superposição se propague.

## Ajustes recomendados

1. **Aumentar o limite de profundidade**
   - Defina a variável `QUALIA_MAX_CIRCUIT_DEPTH` para um valor maior (ex.: `32`) ou remova valores muito baixos que possam podar o circuito.
2. **Executar mais shots**
   - Valores a partir de 64 ou 128 tendem a gerar amostra suficiente para diversidade maior.
3. **Reduzir otimização durante transpile**
   - Use `optimization_level=0` ao transpilar se o circuito estiver ficando excessivamente simplificado.
4. **Usar menos medições intermediárias**
   - Definir `measure_frequency=0` (lazy measurement) permite que a evolução ocorra antes da leitura final.

Esses ajustes costumam elevar `counts_diversity_ratio` acima do limite configurado (`universe_config.min_counts_diversity_ratio`) na maioria das configurações.

## Experimento proposto

O teste `tests/universe/test_diversity_depth_limit.py` demonstra o efeito do parâmetro `QUALIA_MAX_CIRCUIT_DEPTH`. Com o limite em `1` a diversidade fica abaixo do limiar configurado. Ao restaurar para `32` a diversidade ultrapassa esse valor.
