#!/usr/bin/env python3
"""
Real Backtest Integration - Integração com motor real de backtest.

YAA-REAL-BACKTEST: Sistema para substituir simulação sintética por backtest real,
evitando over-fitting em modelos artificiais.
"""

import sys
import os
import yaml
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional, Union
from dataclasses import dataclass
import asyncio
import json
from abc import ABC, abstractmethod

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy
    from qualia.data.historical_data_manager import HistoricalDataManager
    from qualia.backtest.real_strategy_executor import RealStrategyExecutor
    QUALIA_AVAILABLE = True
except ImportError:
    print("⚠️ Módulos QUALIA não disponíveis. Usando interfaces mock.")
    QUALIA_AVAILABLE = False


@dataclass
class BacktestResult:
    """Resultado estruturado de backtest real."""
    # Métricas de performance
    total_return: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    calmar_ratio: float
    
    # Métricas de trading
    win_rate: float
    profit_factor: float
    avg_trade_duration_minutes: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    
    # Métricas OTOC específicas
    otoc_filtered_trades: int
    chaos_rate: float
    avg_otoc_value: float
    otoc_effectiveness_ratio: float
    
    # Métricas de execução
    avg_slippage: float
    fill_rate: float
    latency_ms: float
    
    # Dados brutos para análise
    equity_curve: List[float]
    trade_log: List[Dict[str, Any]]
    otoc_log: List[Dict[str, Any]]
    
    # Metadados
    start_date: datetime
    end_date: datetime
    symbols_tested: List[str]
    timeframes_used: List[str]
    config_hash: str


class BacktestEngine(ABC):
    """Interface abstrata para motores de backtest."""
    
    @abstractmethod
    async def run_backtest(
        self, 
        config: Dict[str, Any], 
        start_date: datetime, 
        end_date: datetime,
        symbols: List[str]
    ) -> BacktestResult:
        """Executa backtest com configuração específica."""
        pass
    
    @abstractmethod
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Valida se a configuração é válida para backtest."""
        pass


class SyntheticBacktestEngine(BacktestEngine):
    """
    Motor de backtest sintético (atual) - APENAS PARA PROTOTIPAGEM.
    
    YAA-WARNING: Este motor gera dados artificiais e pode causar over-fitting!
    """
    
    def __init__(self):
        self.warning_shown = False
    
    async def run_backtest(
        self, 
        config: Dict[str, Any], 
        start_date: datetime, 
        end_date: datetime,
        symbols: List[str]
    ) -> BacktestResult:
        """
        ATENÇÃO: Gera métricas sintéticas - NÃO usar para otimização final!
        """
        if not self.warning_shown:
            print("🚨 AVISO: Usando backtest SINTÉTICO - resultados podem ser artificiais!")
            print("   Para otimização real, use RealBacktestEngine com dados históricos.")
            self.warning_shown = True
        
        # Extrair parâmetros OTOC
        otoc_config = config.get('fibonacci_wave_hype_config', {}).get(
            'params', {}
        ).get('multi_timeframe_config', {}).get('otoc_config', {})
        
        otoc_threshold = otoc_config.get('max_threshold', 0.35)
        otoc_enabled = otoc_config.get('enabled', True)
        
        # Simular métricas com base nos parâmetros (ARTIFICIAL!)
        np.random.seed(hash(str(config)) % 2**32)  # Seed baseado na config
        
        # Heurísticas artificiais baseadas em parâmetros
        threshold_quality_factor = 1 + (otoc_threshold - 0.35) * 0.4
        threshold_quantity_factor = 1 - (otoc_threshold - 0.35) * 0.2
        
        # Gerar métricas sintéticas
        base_sharpe = 1.0 + np.random.normal(0, 0.3)
        base_drawdown = 0.06 + np.random.uniform(0, 0.04)
        base_win_rate = 0.52 + np.random.normal(0, 0.08)
        
        # Aplicar "efeitos" dos parâmetros (ARTIFICIAL!)
        synthetic_sharpe = np.clip(base_sharpe * threshold_quality_factor, 0.1, 4.0)
        synthetic_drawdown = np.clip(base_drawdown / threshold_quality_factor, 0.01, 0.2)
        synthetic_win_rate = np.clip(base_win_rate * threshold_quality_factor, 0.3, 0.8)
        
        total_trades = int(150 * threshold_quantity_factor * len(symbols))
        otoc_filtered = int(total_trades * 0.3 * (otoc_threshold / 0.35)) if otoc_enabled else 0
        
        return BacktestResult(
            # Performance metrics
            total_return=synthetic_sharpe * 0.15 + np.random.normal(0, 0.05),
            sharpe_ratio=synthetic_sharpe,
            sortino_ratio=synthetic_sharpe * 1.2,
            max_drawdown=synthetic_drawdown,
            calmar_ratio=synthetic_sharpe / synthetic_drawdown if synthetic_drawdown > 0 else 0,
            
            # Trading metrics
            win_rate=synthetic_win_rate,
            profit_factor=1.1 + synthetic_win_rate * 0.8,
            avg_trade_duration_minutes=45 + np.random.uniform(-15, 15),
            total_trades=total_trades,
            winning_trades=int(total_trades * synthetic_win_rate),
            losing_trades=int(total_trades * (1 - synthetic_win_rate)),
            
            # OTOC metrics
            otoc_filtered_trades=otoc_filtered,
            chaos_rate=otoc_threshold * 0.7 + np.random.uniform(0, 0.1),
            avg_otoc_value=0.25 + np.random.uniform(0, 0.2),
            otoc_effectiveness_ratio=threshold_quality_factor - 1,
            
            # Execution metrics
            avg_slippage=0.0008 + np.random.uniform(0, 0.0004),
            fill_rate=0.98 + np.random.uniform(-0.02, 0.02),
            latency_ms=35 + np.random.uniform(-10, 20),
            
            # Raw data (simplified)
            equity_curve=[1000 * (1 + i * 0.001) for i in range(100)],
            trade_log=[],
            otoc_log=[],
            
            # Metadata
            start_date=start_date,
            end_date=end_date,
            symbols_tested=symbols,
            timeframes_used=['1m', '5m', '15m', '1h'],
            config_hash=str(hash(str(config)))[:8]
        )
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validação básica para backtest sintético."""
        return 'fibonacci_wave_hype_config' in config


class RealBacktestEngine(BacktestEngine):
    """
    Motor de backtest REAL usando dados históricos e estratégia QUALIA.
    
    YAA-REAL-BACKTEST: Implementação que usa dados reais de mercado.
    """
    
    def __init__(self, data_path: str = "data/historical"):
        """
        Inicializa motor de backtest real.
        
        Parameters
        ----------
        data_path : str
            Caminho para dados históricos
        """
        self.data_path = data_path
        self.market_data_cache: Dict[str, pd.DataFrame] = {}
        
    async def load_historical_data(
        self, 
        symbol: str, 
        timeframe: str,
        start_date: datetime, 
        end_date: datetime
    ) -> pd.DataFrame:
        """
        Carrega dados históricos reais.
        
        YAA-REAL-DATA: Integração com dados reais de mercado
        """
        cache_key = f"{symbol}_{timeframe}_{start_date.date()}_{end_date.date()}"
        
        if cache_key in self.market_data_cache:
            return self.market_data_cache[cache_key]
        
        # TODO: Implementar carregamento real de dados
        # Por enquanto, gerar dados mais realísticos
        
        print(f"📊 Carregando dados históricos: {symbol} {timeframe}")
        
        # Simular dados mais realísticos (substituir por dados reais)
        date_range = pd.date_range(start=start_date, end=end_date, freq='1min')
        
        # Gerar série de preços com características realísticas
        np.random.seed(hash(symbol) % 2**32)
        
        # Componentes do preço
        n_periods = len(date_range)
        trend = np.cumsum(np.random.normal(0.0001, 0.002, n_periods))
        volatility_regime = np.random.choice([0.5, 1.0, 2.0], n_periods, p=[0.7, 0.25, 0.05])
        returns = np.random.normal(0, 0.01 * volatility_regime)
        
        # Preços
        base_price = 30000 if 'BTC' in symbol else 2000
        prices = base_price * np.exp(np.cumsum(returns + trend))
        
        # OHLCV
        df = pd.DataFrame(index=date_range)
        df['close'] = prices
        df['open'] = df['close'].shift(1).fillna(df['close'].iloc[0])
        df['high'] = np.maximum(df['open'], df['close']) * (1 + np.abs(np.random.normal(0, 0.002, n_periods)))
        df['low'] = np.minimum(df['open'], df['close']) * (1 - np.abs(np.random.normal(0, 0.002, n_periods)))
        df['volume'] = np.random.lognormal(15, 1, n_periods)
        
        # Resample para timeframe solicitado
        if timeframe != '1m':
            df = df.resample(timeframe).agg({
                'open': 'first',
                'high': 'max', 
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()
        
        self.market_data_cache[cache_key] = df
        return df
    
    async def run_backtest(
        self,
        config: Dict[str, Any],
        start_date: datetime,
        end_date: datetime,
        symbols: List[str]
    ) -> BacktestResult:
        """
        Executa backtest REAL com dados históricos e estratégia FWH real.

        YAA-REAL-BACKTEST: Implementação completa eliminando simulações sintéticas
        """
        print(f"🚀 Executando backtest REAL: {len(symbols)} símbolos, {start_date.date()} a {end_date.date()}")

        if not QUALIA_AVAILABLE:
            print("❌ Módulos QUALIA não disponíveis. Usando fallback sintético.")
            synthetic_engine = SyntheticBacktestEngine()
            return await synthetic_engine.run_backtest(config, start_date, end_date, symbols)

        # 1. Carregar dados históricos reais
        print("📥 Carregando dados históricos...")
        data_manager = HistoricalDataManager(self.data_path)

        try:
            historical_data = {}
            timeframes = ['1m', '5m', '15m', '1h']

            for symbol in symbols:
                historical_data[symbol] = {}
                for tf in timeframes:
                    try:
                        df, quality_report = await data_manager.get_historical_data(
                            symbol, tf, start_date, end_date, validate_quality=True
                        )

                        if not df.empty:
                            historical_data[symbol][tf] = df
                            print(f"   ✅ {symbol} {tf}: {len(df)} períodos")

                            if quality_report and not quality_report.is_valid:
                                print(f"      ⚠️ Quality issues: {quality_report.issues}")
                        else:
                            print(f"   ❌ No data for {symbol} {tf}")

                    except Exception as e:
                        print(f"   ❌ Erro carregando {symbol} {tf}: {e}")

            # Verificar se temos dados suficientes
            if not historical_data:
                raise ValueError("No historical data loaded")

            # 2. Executar estratégia FWH real
            print("🎯 Executando estratégia FWH real...")
            executor = RealStrategyExecutor(
                config=config,
                initial_capital=config.get('backtesting', {}).get('initial_capital', 10000.0),
                commission_rate=config.get('backtesting', {}).get('commission', 0.001),
                slippage_rate=config.get('backtesting', {}).get('slippage', 0.0005)
            )

            # Executar backtest com estratégia real
            results = await executor.run_backtest(historical_data, start_date, end_date)

            # 3. Converter para formato BacktestResult
            backtest_result = BacktestResult(
                # Performance metrics
                total_return=results['total_return'],
                sharpe_ratio=results['sharpe_ratio'],
                sortino_ratio=results['sortino_ratio'],
                max_drawdown=results['max_drawdown'],
                calmar_ratio=results['calmar_ratio'],

                # Trading metrics
                win_rate=results['win_rate'],
                profit_factor=results['profit_factor'],
                avg_trade_duration_minutes=results['avg_trade_duration_minutes'],
                total_trades=results['total_trades'],
                winning_trades=results['winning_trades'],
                losing_trades=results['losing_trades'],

                # OTOC metrics
                otoc_filtered_trades=results['otoc_filtered_trades'],
                chaos_rate=results['chaos_rate'],
                avg_otoc_value=results['avg_otoc_value'],
                otoc_effectiveness_ratio=results['otoc_effectiveness_ratio'],

                # Execution metrics
                avg_slippage=results['avg_slippage'],
                fill_rate=results['fill_rate'],
                latency_ms=results['latency_ms'],

                # Raw data
                equity_curve=results['equity_curve'],
                trade_log=results['trade_log'],
                otoc_log=results['otoc_log'],

                # Metadata
                start_date=results['start_date'],
                end_date=results['end_date'],
                symbols_tested=results['symbols_tested'],
                timeframes_used=results['timeframes_used'],
                config_hash=results['config_hash']
            )

            print(f"✅ Backtest real concluído:")
            print(f"   Total Return: {results['total_return']:.2%}")
            print(f"   Sharpe Ratio: {results['sharpe_ratio']:.3f}")
            print(f"   Max Drawdown: {results['max_drawdown']:.2%}")
            print(f"   Total Trades: {results['total_trades']}")
            print(f"   OTOC Filtered: {results['otoc_filtered_trades']}")

            return backtest_result

        finally:
            await data_manager.cleanup()
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validação rigorosa para backtest real."""
        required_keys = [
            'fibonacci_wave_hype_config',
            'trading_system',
            'market_data'
        ]
        
        return all(key in config for key in required_keys)


class BacktestEngineFactory:
    """Factory para criar motores de backtest apropriados."""
    
    @staticmethod
    def create_engine(engine_type: str = "auto", **kwargs) -> BacktestEngine:
        """
        Cria motor de backtest apropriado.
        
        Parameters
        ----------
        engine_type : str
            Tipo de engine: "synthetic", "real", ou "auto"
        """
        if engine_type == "synthetic":
            print("⚠️ Usando motor SINTÉTICO - apenas para prototipagem!")
            return SyntheticBacktestEngine()
        
        elif engine_type == "real":
            print("🚀 Usando motor REAL - dados históricos")
            return RealBacktestEngine(**kwargs)
        
        elif engine_type == "auto":
            # Detectar automaticamente qual usar
            if QUALIA_AVAILABLE and os.path.exists("data/historical"):
                print("🎯 Auto-detectado: Usando motor REAL")
                return RealBacktestEngine(**kwargs)
            else:
                print("⚠️ Auto-detectado: Usando motor SINTÉTICO (dados reais não disponíveis)")
                return SyntheticBacktestEngine()
        
        else:
            raise ValueError(f"Tipo de engine inválido: {engine_type}")


# Função de conveniência para integração fácil
async def run_real_backtest(
    config: Dict[str, Any],
    start_date: datetime,
    end_date: datetime,
    symbols: List[str],
    engine_type: str = "auto"
) -> BacktestResult:
    """
    Função de conveniência para executar backtest real.
    
    YAA-CONVENIENCE: Interface simples para uso em otimização
    """
    engine = BacktestEngineFactory.create_engine(engine_type)
    
    if not engine.validate_config(config):
        raise ValueError("Configuração inválida para backtest")
    
    return await engine.run_backtest(config, start_date, end_date, symbols)


async def main():
    """Demonstração do sistema de backtest real."""
    print("🔬 DEMONSTRAÇÃO: Real Backtest Integration")
    print("=" * 60)
    
    # Carregar configuração
    config_path = "config/fwh_scalp_config_optimized.yaml"
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
    except FileNotFoundError:
        print(f"❌ Configuração não encontrada: {config_path}")
        return
    
    # Definir parâmetros de teste
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 31)
    symbols = ["BTC/USDT", "ETH/USDT"]
    
    # Testar ambos os motores
    print("\n1️⃣ Testando motor SINTÉTICO:")
    synthetic_result = await run_real_backtest(
        config, start_date, end_date, symbols, engine_type="synthetic"
    )
    print(f"   Sharpe: {synthetic_result.sharpe_ratio:.3f}")
    print(f"   Drawdown: {synthetic_result.max_drawdown:.1%}")
    print(f"   Trades: {synthetic_result.total_trades}")
    
    print("\n2️⃣ Testando motor REAL:")
    real_result = await run_real_backtest(
        config, start_date, end_date, symbols, engine_type="real"
    )
    print(f"   Sharpe: {real_result.sharpe_ratio:.3f}")
    print(f"   Drawdown: {real_result.max_drawdown:.1%}")
    print(f"   Trades: {real_result.total_trades}")
    
    print("\n📊 COMPARAÇÃO:")
    print(f"   Diferença Sharpe: {abs(real_result.sharpe_ratio - synthetic_result.sharpe_ratio):.3f}")
    print(f"   Diferença Trades: {abs(real_result.total_trades - synthetic_result.total_trades)}")
    
    if abs(real_result.sharpe_ratio - synthetic_result.sharpe_ratio) > 0.5:
        print("🚨 ALERTA: Grande diferença entre motores - possível over-fitting!")
    else:
        print("✅ Diferenças aceitáveis entre motores")


if __name__ == "__main__":
    asyncio.run(main())
