# QUALIA BayesOpt Microservice Configuration - D-02
# Configuração para otimização Bayesiana distribuída

# Server Configuration
server:
  host: "0.0.0.0"
  port: 8080
  workers: 1
  reload: false  # Set to true for development

# Database Configuration
database:
  url: "sqlite:///data/bayes_service.db"
  # For production, consider PostgreSQL:
  # url: "postgresql://user:password@localhost:5432/qualia_bayes"

# Service Configuration
service:
  max_concurrent_studies: 100
  study_timeout_hours: 24
  auto_cleanup_enabled: true
  cleanup_interval_minutes: 60

# Performance Configuration
performance:
  max_trials_per_study: 10000
  trial_timeout_seconds: 300
  n_startup_trials: 10
  
# Optimization Defaults
optimization:
  default_sampler: "TPE"  # TPE, Random, CmaEs
  default_pruning: true
  default_direction: "maximize"
  
  # Default parameter ranges
  price_amplification_range: [1.0, 10.0]
  news_amplification_range: [1.0, 15.0]
  min_confidence_range: [0.2, 0.8]

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "logs/bayes_service.log"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Security Configuration (optional)
security:
  api_key_required: false
  api_key: ""  # Set for production
  cors_origins: ["*"]  # Restrict for production

# Integration with QUALIA
qualia_integration:
  # BayesianOptimizer configuration
  use_distributed_optimization: true
  bayes_service_url: "http://localhost:8080"
  distributed_fallback_enabled: true
  
  # Optimization cycle settings
  n_trials_per_cycle: 25
  optimization_interval_cycles: 500
  lookback_hours: 24
  
  # Objective function settings
  objective_metric: "sharpe_pnl_combined"  # sharpe_ratio, pnl_24h, sharpe_pnl_combined
  sharpe_weight: 0.6
  pnl_weight: 0.4

# Monitoring and Health
monitoring:
  health_check_interval: 30  # seconds
  metrics_enabled: true
  performance_tracking: true
  
# Production Settings
production:
  # Resource limits
  max_memory_mb: 2048
  max_cpu_percent: 80.0
  
  # Backup and recovery
  backup_enabled: true
  backup_interval_hours: 6
  backup_retention_days: 30
  
  # High availability
  ha_enabled: false
  ha_nodes: []
  
# Development Settings
development:
  debug_mode: false
  verbose_logging: false
  test_mode: false
