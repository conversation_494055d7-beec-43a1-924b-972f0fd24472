from __future__ import annotations

"""Adaptive threshold calibration for NEXUS coherence alerts.

Monitors coherence values and adapts the alert threshold using an
Exponential Moving Average (EMA) plus a configurable offset.  Whenever the
latest coherence exceeds the dynamic threshold, an alert event is published.

Published events:
    * ``nexus.threshold.updated`` – payload {"threshold": float}
    * ``nexus.alert`` – payload {"coherence": float, "threshold": float}
"""

import time
from typing import Optional

from qualia.memory.event_bus import SimpleEventBus
from qualia.events import CoherenceAlertEvent, CrossModalCoherenceEvent
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class AdaptiveThresholdCalibrator:
    """Maintain a coherence alert threshold that adapts over time."""

    def __init__(
        self,
        event_bus: SimpleEventBus,
        *,
        alpha: float | None = None,
        offset: float | None = None,
        min_threshold: float = 0.2,
        max_threshold: float = 0.9,
        config_path: str | None = None,
    ) -> None:
        if config_path:
            import yaml, os

            if os.path.exists(config_path):
                with open(config_path, "r", encoding="utf-8") as fh:
                    data = yaml.safe_load(fh) or {}
                alpha = alpha or data.get("alpha")
                offset = offset or data.get("offset")
                min_threshold = data.get("min_threshold", min_threshold)
                max_threshold = data.get("max_threshold", max_threshold)
        alpha = alpha or 0.1
        offset = offset or 0.1

        if not 0 < alpha <= 1:
            raise ValueError("alpha must be in (0,1]")
        self.event_bus = event_bus
        self.alpha = float(alpha)
        self.offset = float(offset)
        self.min_th = float(min_threshold)
        self.max_th = float(max_threshold)
        self.threshold: Optional[float] = None
        # subscribe
        self.event_bus.subscribe("nexus.cross_modal_coherence", self._on_coherence)
        self.event_bus.subscribe("nexus.threshold.override", self._apply_override)
        logger.info(
            "AdaptiveThresholdCalibrator inicializado alpha=%.2f offset=%.2f",
            self.alpha,
            self.offset,
        )

    # ------------------------------------------------------------------

    def _on_coherence(self, payload):  # type: ignore[override]
        if hasattr(payload, "coherence"):
            val = float(payload.coherence)
        else:
            val = float(payload.get("coherence", 0.0))
        if self.threshold is None:
            # bootstrap
            self.threshold = max(self.min_th, min(self.max_th, val + self.offset))
            self._publish_threshold()
            return
        # EMA update toward current value + offset
        target = max(self.min_th, min(self.max_th, val + self.offset))
        self.threshold = (1 - self.alpha) * self.threshold + self.alpha * target
        self._publish_threshold()
        if val >= self.threshold:
            self.event_bus.publish(
                "nexus.alert",
                CoherenceAlertEvent(coherence=val, threshold=self.threshold),
            )
            logger.info(
                "NEXUS ALERT: coherence %.3f superou threshold %.3f",
                val,
                self.threshold,
            )

    def _publish_threshold(self):
        from qualia.events import ThresholdUpdatedEvent

        self.event_bus.publish(
            "nexus.threshold.updated",
            ThresholdUpdatedEvent(threshold=float(self.threshold), timestamp=time.time()),
        )

    def get_threshold(self) -> Optional[float]:
        return self.threshold

    # ------------------------------------------------------------------

    def _apply_override(self, payload):  # type: ignore[override]
        new_alpha = getattr(payload, "alpha", None)
        new_offset = getattr(payload, "offset", None)
        if isinstance(payload, dict):
            new_alpha = float(payload.get("alpha", new_alpha)) if payload.get("alpha") is not None else new_alpha
            new_offset = float(payload.get("offset", new_offset)) if payload.get("offset") is not None else new_offset
        if new_alpha is not None and 0 < new_alpha <= 1:
            self.alpha = new_alpha  # type: ignore[attr-defined]
        if new_offset is not None:
            self.offset = new_offset  # type: ignore[attr-defined]
        logger.info(
            "ThresholdCalibrator override recebido: alpha=%.3f offset=%.3f",
            self.alpha,
            self.offset,
        )
