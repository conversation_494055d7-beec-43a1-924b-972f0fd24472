# Módulos de Operadores

Os arquivos `folding.py`, `resonance.py`, `emergence.py`, `retrocausality.py` e `observer.py` em `src/qualia/core` possuem implementações **funcionais** desses operadores conceituais. Conforme registrado no `AGENTS.md`, esses módulos são utilizados nos testes automatizados do projeto e representam a base para evoluções futuras.

Esta documentação foi atualizada para refletir as implementações atuais e será revisada sempre que novos recursos forem incorporados.

Cada operador tem um propósito específico:

- **Folding** – reduzir dimensionalidade de séries temporais mantendo relações estruturais para facilitar compressão de padrões.
- **Resonance** – identificar frequências predominantes e sincronizar sinais com ciclos de mercado.
- **Emergence** – detectar formação de padrões complexos e sinalizar comportamentos coletivos.
- **Retrocausality** – retropropagar sinais futuros a fim de ajustar decisões presentes.
- **Observer** – medir estados de mercado e registrar colapsos de decisão para análise posterior.


Para compreender os objetivos desses módulos e as direções planejadas, consulte:

- [docs/specs/QUALIA_Trading_System_VFinal_PT-BR.md](specs/QUALIA_Trading_System_VFinal_PT-BR.md)
- [docs/specs/QUALIA_Trading_System_VFinal_PT-BR_EXT.md](specs/QUALIA_Trading_System_VFinal_PT-BR_EXT.md)
- [docs/specs/Modelo Integrado da Consciência Quântica e da Informação (MICQI).md](specs/Modelo Integrado da Consciência Quântica e da Informação (MICQI).md)
- [docs/specs/folding_operator.md](specs/folding_operator.md)
- [docs/specs/resonance_operator.md](specs/resonance_operator.md)
- [docs/specs/emergence_operator.md](specs/emergence_operator.md)
- [docs/specs/retrocausality_operator.md](specs/retrocausality_operator.md)
- [docs/specs/observer_operator.md](specs/observer_operator.md)

Esses documentos descrevem a visão teórica completa e servirão como guia para implementações futuras dos operadores.

## Atualização de Cache

O módulo `resonance.py` passou a contar com um cache LRU opcional para os
padrões consultados na `QuantumPatternMemory`. Esse cache pode ser limpo
através da função `invalidate_pattern_cache()` sempre que novos padrões forem
adicionados, mantendo a análise coerente com o histórico recente.
