# Double-Check de Integração do módulo `src/ai`

Este relatório avalia como o plugin `src/ai/encoders_plugin.py` se encaixa no ecossistema QUALIA e aponta oportunidades de melhoria.

## Integração Sistêmica
- **Registro de Encoders**: o arquivo expõe `RSIPhaseEncoder` e `VolumeRatioAmplitudeEncoder` via `register_encoder`. Os testes confirmam o registro correto.
- **Event-driven**: não foram encontrados produtores ou consumidores de eventos relacionados ao módulo. A integração com `qualia.events` ainda não existe.
- **Configuração**: os encoders agora podem ser habilitados ou desabilitados via `config/encoders.yaml`. O caminho pode ser sobrescrito pela variável de ambiente `QUALIA_ENCODERS_CONFIG`.

## Performance
- **Complexidade**: os wrappers apenas delegam aos encoders de `src.qualia.core.encoders`, não impactando o desempenho.
- **Métricas**: há contagem de instâncias e tempo de inicialização registrados via StatsD com tags estruturadas.

## Observabilidade
- Utiliza `get_logger` para registrar a criação das instâncias, seguindo o padrão do projeto.
- Não há tracing OpenTelemetry nem integração com dashboards.

## Segurança
- O plugin não manipula dados sensíveis nem chave de API. Não há riscos diretos identificados.

## Riscos Identificados
| Gravidade | Descrição | Esforço |
|-----------|-----------|---------|
| Média | Ausência de integração com o Event Bus impede acoplamento com módulos externos. | Médio |
| Baixa | Falta de configuração externa pode dificultar ajustes finos e rollout progressivo. | Baixo |
| Baixa | Métricas de criação de instâncias são limitadas; não há observabilidade de uso. | Baixo |

## Quick Wins ⚡
- [x] #18 Adicionar contagem e tempo de inicialização dos encoders em métricas estruturadas.
- [x] #18 Incluir parâmetros de configuração em `config/` com schema YAML para habilitar/disable cada encoder.

## Features de Valor
1. **Hook para Offloading em GPU/QPU**
   - *User Story*: Como desenvolvedor, desejo que os encoders possam delegar parte do processamento para GPU/QPU quando disponível, reduzindo latência.
   - *Estimativa*: 3 dias para protótipo de fallback com `qiskit-aer` ou `cupy`.
2. **Integração com Event Bus**
   - *User Story*: Como operador, quero que cada encoder emita evento de conclusão para acompanhar a cadeia de processamento no Grafana.
   - *Estimativa*: 2 dias incluindo testes de fluxo.

Todas as ações devem ser registradas em tickets específicos com responsáveis e ETA.

## Fallback para CPU

Quando `use_hardware_acceleration` estiver desativado ou nenhum acelerador for encontrado,
os encoders e cálculos de risco executam apenas com NumPy. A verificação é feita
via `qualia.utils.hardware_hooks.check_hardware_status` e o resultado é logado.
A funcionalidade permanece a mesma, mas sem ganho de desempenho.

