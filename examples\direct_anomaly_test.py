#!/usr/bin/env python3
"""
Teste Direto da Anomalia ROBUST_NORMALIZED
Replica exatamente a implementação original para confirmar resultados.
"""

import pandas as pd
import numpy as np
import requests
from datetime import datetime, timedelta


def fetch_data(symbol: str, days: int = 90) -> pd.DataFrame:
    """Busca dados históricos."""
    try:
        end_time = int(datetime.now().timestamp() * 1000)
        start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
        
        url = "https://api.binance.com/api/v3/klines"
        params = {
            'symbol': symbol.replace('/', ''),
            'interval': '1h',
            'startTime': start_time,
            'endTime': end_time,
            'limit': 1000
        }
        
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        data = response.json()
        df = pd.DataFrame(data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])
        
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df.set_index('timestamp', inplace=True)
        df = df.sort_index().dropna()
        
        # Indicadores
        df['returns'] = df['close'].pct_change()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['sma_50'] = df['close'].rolling(50).mean()
        df['rsi'] = calculate_rsi(df['close'], 14)
        df['volatility'] = df['returns'].rolling(20).std()
        
        return df
        
    except Exception as e:
        print(f"❌ Erro ao buscar dados para {symbol}: {e}")
        return pd.DataFrame()


def calculate_rsi(prices: pd.Series, period: int = 14) -> pd.Series:
    """Calcula RSI."""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))


def robust_normalized_exact(df: pd.DataFrame) -> dict:
    """Implementação EXATA da estratégia ROBUST_NORMALIZED."""
    signals = []
    
    # NORMALIZAÇÃO POR ATIVO - Calcula estatísticas históricas
    lookback = 200
    start_idx = max(50, lookback)
    
    # Estatísticas de normalização (últimos 200 períodos)
    trend_history = []
    mean_rev_history = []
    momentum_history = []
    rsi_history = []
    
    # Coleta histórico para normalização
    for j in range(start_idx-lookback, start_idx):
        if j >= 50:
            trend_raw = (df['sma_20'].iloc[j] - df['sma_50'].iloc[j]) / df['sma_50'].iloc[j]
            mean_rev_raw = -(df['close'].iloc[j] - df['sma_20'].iloc[j]) / df['sma_20'].iloc[j]
            momentum_raw = df['close'].iloc[j-10:j].pct_change().mean()
            rsi_raw = (df['rsi'].iloc[j] - 50) / 50
            
            trend_history.append(trend_raw)
            mean_rev_history.append(mean_rev_raw)
            momentum_history.append(momentum_raw)
            rsi_history.append(rsi_raw)
    
    # Estatísticas para normalização Z-score
    trend_mean, trend_std = np.mean(trend_history), np.std(trend_history)
    mean_rev_mean, mean_rev_std = np.mean(mean_rev_history), np.std(mean_rev_history)
    momentum_mean, momentum_std = np.mean(momentum_history), np.std(momentum_history)
    rsi_mean, rsi_std = np.mean(rsi_history), np.std(rsi_history)
    
    for i in range(start_idx, len(df)):
        # Componentes RAW
        trend_raw = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
        mean_rev_raw = -(df['close'].iloc[i] - df['sma_20'].iloc[i]) / df['sma_20'].iloc[i]
        momentum_raw = df['close'].iloc[i-10:i].pct_change().mean()
        rsi_raw = (df['rsi'].iloc[i] - 50) / 50
        
        # NORMALIZAÇÃO Z-SCORE (remove viés específico do ativo)
        trend_signal = (trend_raw - trend_mean) / (trend_std + 1e-8)
        mean_rev_signal = (mean_rev_raw - mean_rev_mean) / (mean_rev_std + 1e-8)
        momentum_signal = (momentum_raw - momentum_mean) / (momentum_std + 1e-8)
        rsi_signal = (rsi_raw - rsi_mean) / (rsi_std + 1e-8)
        
        # Clipping para evitar outliers
        trend_signal = np.clip(trend_signal, -3, 3)
        mean_rev_signal = np.clip(mean_rev_signal, -3, 3)
        momentum_signal = np.clip(momentum_signal, -3, 3)
        rsi_signal = np.clip(rsi_signal, -3, 3)
        
        # Combinação NORMALIZADA (pesos iguais para consistência)
        composite_signal = (
            trend_signal * 0.25 +
            mean_rev_signal * 0.25 +
            momentum_signal * 0.25 +
            rsi_signal * 0.25
        )
        
        # Threshold baseado em desvios padrão (consistente entre ativos)
        if abs(composite_signal) > 0.5:  # 0.5 desvios padrão
            signals.append(np.clip(composite_signal * 0.3, -1, 1))  # Sinal suave
        else:
            signals.append(0)
    
    # Calcula performance
    return calculate_detailed_performance(df.iloc[start_idx:], signals)


def calculate_detailed_performance(df: pd.DataFrame, signals: list) -> dict:
    """Calcula performance detalhada."""
    if len(signals) != len(df):
        return {'error': 'Mismatch de dados'}
    
    # Simula trading
    positions = signals
    returns = []
    trades = 0
    winning_trades = 0
    losing_trades = 0
    total_wins = 0
    total_losses = 0
    
    trade_details = []
    
    for i in range(1, len(df)):
        if abs(positions[i-1]) > 0.1:  # Posição significativa
            price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
            position_return = positions[i-1] * price_return
            returns.append(position_return)
            trades += 1
            
            trade_info = {
                'return': position_return,
                'position': positions[i-1],
                'price_change': price_return,
                'timestamp': df.index[i]
            }
            trade_details.append(trade_info)
            
            if position_return > 0:
                winning_trades += 1
                total_wins += position_return
            else:
                losing_trades += 1
                total_losses += abs(position_return)
        else:
            returns.append(0)
    
    if not returns:
        return {'error': 'Nenhum trade executado'}
    
    returns_series = pd.Series(returns)
    
    # Métricas básicas
    total_return = returns_series.sum()
    volatility = returns_series.std() * np.sqrt(252)
    sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
    
    # Drawdown
    cumulative = (1 + returns_series).cumprod()
    rolling_max = cumulative.expanding().max()
    drawdowns = (cumulative - rolling_max) / rolling_max
    max_drawdown = abs(drawdowns.min())
    
    # Win rate e métricas de trading
    win_rate = winning_trades / trades if trades > 0 else 0
    avg_win = total_wins / winning_trades if winning_trades > 0 else 0
    avg_loss = total_losses / losing_trades if losing_trades > 0 else 0
    profit_factor = total_wins / total_losses if total_losses > 0 else 0
    
    # Extremos
    win_returns = [t['return'] for t in trade_details if t['return'] > 0]
    loss_returns = [t['return'] for t in trade_details if t['return'] < 0]
    
    largest_win = max(win_returns) if win_returns else 0
    largest_loss = min(loss_returns) if loss_returns else 0
    
    return {
        'total_return_pct': total_return * 100,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown_pct': max_drawdown * 100,
        'win_rate': win_rate * 100,
        'total_trades': trades,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'volatility': volatility * 100,
        'avg_win_pct': avg_win * 100,
        'avg_loss_pct': avg_loss * 100,
        'profit_factor': profit_factor,
        'win_loss_ratio': avg_win / avg_loss if avg_loss > 0 else 0,
        'largest_win_pct': largest_win * 100,
        'largest_loss_pct': largest_loss * 100,
        'top_5_wins': sorted(win_returns, reverse=True)[:5],
        'top_5_losses': sorted(loss_returns)[:5]
    }


def run_direct_test():
    """Executa teste direto da anomalia."""
    print("🔍 TESTE DIRETO: ROBUST_NORMALIZED Anomaly Investigation")
    print("=" * 60)
    
    symbols = ["BTCUSDT", "ETHUSDT"]
    all_results = []
    
    for symbol in symbols:
        print(f"\n📈 Testando {symbol}...")
        
        df = fetch_data(symbol, days=90)
        if df.empty or len(df) < 300:
            print(f"❌ Dados insuficientes para {symbol}")
            continue
        
        result = robust_normalized_exact(df)
        
        if 'error' not in result:
            result['symbol'] = symbol
            all_results.append(result)
            
            print(f"   📊 Total Return: {result['total_return_pct']:.2f}%")
            print(f"   🎯 Win Rate: {result['win_rate']:.1f}%")
            print(f"   📈 Total Trades: {result['total_trades']}")
            print(f"   💚 Avg Win: {result['avg_win_pct']:.3f}%")
            print(f"   💔 Avg Loss: {result['avg_loss_pct']:.3f}%")
            print(f"   ⚖️  Win/Loss Ratio: {result['win_loss_ratio']:.2f}")
            print(f"   📉 Profit Factor: {result['profit_factor']:.2f}")
            print(f"   🔥 Largest Win: {result['largest_win_pct']:.2f}%")
            print(f"   💥 Largest Loss: {result['largest_loss_pct']:.2f}%")
            print(f"   📊 Sharpe: {result['sharpe_ratio']:.3f}")
            
            # Análise da anomalia
            print(f"\n🔍 ANÁLISE DA ANOMALIA:")
            
            if result['win_rate'] < 30 and result['sharpe_ratio'] > 0.5:
                print(f"   ⚠️  ANOMALIA DETECTADA!")
                print(f"   💡 Win rate baixo ({result['win_rate']:.1f}%) mas Sharpe alto ({result['sharpe_ratio']:.3f})")
            elif result['win_rate'] < 30:
                print(f"   📊 Win rate baixo ({result['win_rate']:.1f}%) com Sharpe {result['sharpe_ratio']:.3f}")
            else:
                print(f"   ✅ Win rate normal ({result['win_rate']:.1f}%) com Sharpe {result['sharpe_ratio']:.3f}")
            
            if result['win_loss_ratio'] > 2.0:
                print(f"   💡 CAUSA: Ganhos médios {result['win_loss_ratio']:.2f}x maiores que perdas")
            
            if result['profit_factor'] > 1.5:
                print(f"   💡 Profit Factor alto ({result['profit_factor']:.2f}): Estratégia lucrativa")
            
            # Top trades
            print(f"   🔥 Top 3 ganhos: {[f'{w*100:.2f}%' for w in result['top_5_wins'][:3]]}")
            print(f"   💥 Top 3 perdas: {[f'{l*100:.2f}%' for l in result['top_5_losses'][:3]]}")
        else:
            print(f"   ❌ {result['error']}")
    
    # Consolidação
    if all_results:
        print(f"\n" + "="*60)
        print(f"📊 CONSOLIDAÇÃO DOS RESULTADOS")
        print(f"="*60)
        
        avg_win_rate = np.mean([r['win_rate'] for r in all_results])
        avg_sharpe = np.mean([r['sharpe_ratio'] for r in all_results])
        avg_return = np.mean([r['total_return_pct'] for r in all_results])
        avg_trades = np.mean([r['total_trades'] for r in all_results])
        avg_win_loss_ratio = np.mean([r['win_loss_ratio'] for r in all_results])
        avg_profit_factor = np.mean([r['profit_factor'] for r in all_results])
        
        print(f"\n🎯 MÉTRICAS MÉDIAS:")
        print(f"   Return: {avg_return:.2f}%")
        print(f"   Sharpe: {avg_sharpe:.3f}")
        print(f"   Win Rate: {avg_win_rate:.1f}%")
        print(f"   Trades: {avg_trades:.0f}")
        print(f"   Win/Loss Ratio: {avg_win_loss_ratio:.2f}")
        print(f"   Profit Factor: {avg_profit_factor:.2f}")
        
        print(f"\n💡 DIAGNÓSTICO FINAL:")
        if avg_win_rate < 20 and avg_sharpe > 0.5:
            print(f"   🚨 ANOMALIA CONFIRMADA: Win rate muito baixo mas Sharpe alto")
            print(f"   💡 Estratégia 'Home Run': Poucos ganhos grandes compensam muitas perdas pequenas")
        elif avg_win_rate < 30:
            print(f"   📊 Win rate baixo mas dentro do esperado para estratégias de momentum")
        else:
            print(f"   ✅ Métricas normais - não há anomalia significativa")
        
        if avg_profit_factor > 1.2:
            print(f"   ✅ Estratégia matematicamente sólida (Profit Factor > 1.2)")
        else:
            print(f"   ⚠️  Estratégia marginal (Profit Factor baixo)")


if __name__ == "__main__":
    run_direct_test()
