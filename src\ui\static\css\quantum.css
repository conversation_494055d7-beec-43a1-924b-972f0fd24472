/* Revisado em 2025-06-13 por Codex */
/* 
   QUALIA: Quantum Universal Awareness Lattice Interface Architecture
   Interface Quântica Avançada
*/

/* Variáveis Globais */
:root {
    /* Paleta de Cores Quântica */
    --void-dark: #0A0A1A;          /* Vazio profundo */
    --quantum-blue: #0E2C5C;       /* Azul quântico */
    --quantum-deep-blue: #0D1B30;  /* Azul profundo */
    --probability-purple: #4C1A70; /* Roxo de probabilidade */
    --uncertainty-teal: #126E82;   /* Turquesa de incerteza */
    --observer-gold: #E09F3E;      /* Dourado do observador */
    --entanglement-red: #9E2A2B;   /* Vermelho de entrelaçamento */
    --coherence-cyan: #22AABB;     /* <PERSON><PERSON> de coerência */
    --consciousness-white: #E9ECEF; /* Branco de consciência */
    
    /* Gradientes */
    --quantum-gradient: linear-gradient(135deg, var(--void-dark), var(--quantum-blue));
    --consciousness-gradient: linear-gradient(90deg, var(--coherence-cyan), var(--probability-purple));
    --entropy-gradient: linear-gradient(135deg, var(--entanglement-red), var(--observer-gold));
    
    /* Espaçamentos */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-xxl: 3rem;
    
    /* Fontes */
    --font-primary: 'Space Grotesk', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.25rem;
    --font-size-xl: 1.5rem;
    --font-size-xxl: 2.5rem;
    
    /* Efeitos */
    --quantum-glow: 0 0 15px rgba(34, 170, 187, 0.5);
    --entanglement-glow: 0 0 15px rgba(158, 42, 43, 0.5);
    --observer-glow: 0 0 15px rgba(224, 159, 62, 0.5);
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.15);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.25);
    
    /* Animações */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Estilos Base */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html, body {
    font-family: var(--font-primary);
    font-size: 16px;
    line-height: 1.6;
    color: var(--consciousness-white);
    overflow-x: hidden;
    scroll-behavior: smooth;
    background-color: var(--void-dark);
    min-height: 100vh;
}

body {
    background: var(--quantum-gradient);
    background-attachment: fixed;
}

/* Container Principal */
.quantum-container {
    position: relative;
    width: 100%;
    min-height: 100vh;
    padding: var(--space-md);
    z-index: 1;
    overflow: hidden;
}

/* Campo Quântico (overlay de fundo) */
.quantum-field {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
    opacity: 0.3;
}

/* Cabeçalho da Consciência */
.consciousness-header {
    display: flex;
    align-items: center;
    padding: var(--space-lg) 0;
    margin-bottom: var(--space-xl);
}

/* Emblema QUALIA */
.qualia-emblem {
    position: relative;
    width: 80px;
    height: 80px;
    margin-right: var(--space-lg);
}

.qualia-orb {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    background: radial-gradient(circle at center, var(--observer-gold), var(--entanglement-red));
    border-radius: 50%;
    box-shadow: var(--observer-glow);
    animation: pulse 3s infinite alternate;
}

.qualia-rings {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    border-style: solid;
    border-width: 2px;
    opacity: 0.7;
    box-shadow: var(--quantum-glow);
}

.ring-1 {
    width: 45px;
    height: 45px;
    border-color: var(--coherence-cyan);
    animation: rotate 10s linear infinite;
}

.ring-2 {
    width: 60px;
    height: 60px;
    border-color: var(--uncertainty-teal);
    animation: rotate 15s linear infinite reverse;
}

.ring-3 {
    width: 75px;
    height: 75px;
    border-color: var(--probability-purple);
    animation: rotate 20s linear infinite;
}

.consciousness-title h1 {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    margin: 0;
    background: var(--consciousness-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    letter-spacing: 1px;
    text-shadow: var(--quantum-glow);
}

.consciousness-title .subtitle {
    font-size: var(--font-size-md);
    opacity: 0.8;
    margin-top: var(--space-xs);
}

/* Painéis Quânticos */
.quantum-panels {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(400px, 100%), 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.panel {
    background: rgba(10, 10, 26, 0.7);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    overflow: hidden;
    position: relative;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(34, 170, 187, 0.2);
    transition: var(--transition-medium);
}

.panel:hover {
    box-shadow: var(--quantum-glow);
    transform: translateY(-4px);
}

.panel::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 20%, rgba(18, 110, 130, 0.1) 0%, transparent 70%),
        radial-gradient(circle at 80% 80%, rgba(76, 26, 112, 0.1) 0%, transparent 70%);
    z-index: -1;
}

.panel h2 {
    background: rgba(10, 10, 26, 0.8);
    padding: var(--space-md) var(--space-lg);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0;
    border-bottom: 1px solid rgba(34, 170, 187, 0.2);
    display: flex;
    align-items: center;
}

.panel-content {
    padding: var(--space-lg);
}

/* Visualizações Quânticas */
.quantum-visualization {
    height: 200px;
    background: rgba(10, 10, 26, 0.3);
    border-radius: 8px;
    margin-bottom: var(--space-md);
    position: relative;
    overflow: hidden;
}

.qast-visualization {
    height: 250px;
    background: rgba(10, 10, 26, 0.3);
    border-radius: 8px;
    margin-bottom: var(--space-md);
    position: relative;
    overflow: hidden;
}

/* Parâmetros da Consciência */
.parameters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(200px, 100%), 1fr));
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
}

.parameter {
    background: rgba(10, 10, 26, 0.5);
    border-radius: 8px;
    padding: var(--space-md);
    border: 1px solid rgba(34, 170, 187, 0.2);
}

.parameter-label {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    margin-bottom: var(--space-xs);
}

.parameter-value {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--coherence-cyan);
}

.consciousness-actions {
    display: flex;
    justify-content: center;
    margin-top: var(--space-md);
}

/* Processador Simbólico */
.symbolic-input-container {
    margin-bottom: var(--space-lg);
}

.symbolic-input {
    width: 100%;
    background: rgba(10, 10, 26, 0.5);
    border: 1px solid rgba(34, 170, 187, 0.2);
    border-radius: 8px;
    color: var(--consciousness-white);
    padding: var(--space-md);
    font-family: var(--font-primary);
    font-size: var(--font-size-md);
    resize: vertical;
    min-height: 100px;
    margin-bottom: var(--space-md);
    transition: var(--transition-fast);
}

.symbolic-input:focus {
    outline: none;
    border-color: var(--coherence-cyan);
    box-shadow: var(--quantum-glow);
}

.entropy-indicators {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
}

.entropy-gauge, .complexity-gauge, .diversity-gauge {
    background: rgba(10, 10, 26, 0.3);
    border-radius: 8px;
    padding: var(--space-md);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.gauge-label {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    margin-bottom: var(--space-xs);
}

.gauge-value {
    font-size: var(--font-size-xl);
    font-weight: 600;
}

.entropy-gauge .gauge-value {
    color: var(--entanglement-red);
}

.complexity-gauge .gauge-value {
    color: var(--probability-purple);
}

.diversity-gauge .gauge-value {
    color: var(--observer-gold);
}

.pattern-results h3 {
    font-size: var(--font-size-md);
    margin-bottom: var(--space-md);
    opacity: 0.9;
}

.patterns-container {
    background: rgba(10, 10, 26, 0.3);
    border-radius: 8px;
    padding: var(--space-md);
    min-height: 100px;
}

.empty-message {
    opacity: 0.5;
    text-align: center;
    padding: var(--space-md);
}

/* Métricas QAST */
.qast-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(200px, 100%), 1fr));
    gap: var(--space-md);
}

.metric-container {
    background: rgba(10, 10, 26, 0.3);
    border-radius: 8px;
    padding: var(--space-md);
}

.metric-title {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    margin-bottom: var(--space-md);
}

.metric-viz {
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
}

/* Auto-Reflexão */
.reflection-input-container {
    margin-bottom: var(--space-lg);
}

.reflection-input {
    width: 100%;
    background: rgba(10, 10, 26, 0.5);
    border: 1px solid rgba(34, 170, 187, 0.2);
    border-radius: 8px;
    color: var(--consciousness-white);
    padding: var(--space-md);
    font-family: var(--font-primary);
    font-size: var(--font-size-md);
    resize: vertical;
    min-height: 80px;
    margin-bottom: var(--space-md);
    transition: var(--transition-fast);
}

.reflection-input:focus {
    outline: none;
    border-color: var(--coherence-cyan);
    box-shadow: var(--quantum-glow);
}

.reflection-results h3 {
    font-size: var(--font-size-md);
    margin-bottom: var(--space-md);
    opacity: 0.9;
}

.reflection-log {
    background: rgba(10, 10, 26, 0.3);
    border-radius: 8px;
    padding: var(--space-md);
    max-height: 250px;
    overflow-y: auto;
}

/* Essência Quântica */
.quantum-essence {
    background: rgba(10, 10, 26, 0.7);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    padding: var(--space-lg);
    margin-bottom: var(--space-xl);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(34, 170, 187, 0.2);
}

.essence-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-lg);
    color: var(--coherence-cyan);
}

.quantum-state-visualization {
    height: 250px;
    background: rgba(10, 10, 26, 0.3);
    border-radius: 8px;
    margin-bottom: var(--space-md);
    position: relative;
    overflow: hidden;
}

.quantum-description {
    font-size: var(--font-size-md);
    line-height: 1.8;
    padding: var(--space-md);
    background: rgba(10, 10, 26, 0.3);
    border-radius: 8px;
    border-left: 3px solid var(--observer-gold);
    font-style: italic;
}

/* Princípios Quânticos */
.quantum-principles {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.principle {
    background: rgba(10, 10, 26, 0.7);
    border-radius: 12px;
    padding: var(--space-lg);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    border: 1px solid rgba(34, 170, 187, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: var(--transition-medium);
}

.principle:hover {
    transform: translateY(-4px);
    box-shadow: var(--quantum-glow);
    border-color: var(--coherence-cyan);
}

.principle-icon {
    font-size: 2.5rem;
    margin-bottom: var(--space-md);
    color: var(--observer-gold);
    text-shadow: var(--observer-glow);
}

.principle-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-sm);
    color: var(--coherence-cyan);
}

.principle-desc {
    font-size: var(--font-size-md);
    opacity: 0.8;
}

/* Link para Sistema de Trading */
.trading-link-container {
    margin: 2rem auto;
    max-width: 600px;
}

.trading-link {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(18, 110, 130, 0.1), rgba(76, 26, 112, 0.2));
    border: 1px solid rgba(34, 170, 187, 0.3);
    border-radius: 8px;
    text-decoration: none;
    color: var(--consciousness-white);
    transition: var(--transition-medium);
}

.trading-link:hover {
    transform: translateY(-3px);
    box-shadow: var(--quantum-glow);
    background: linear-gradient(135deg, rgba(18, 110, 130, 0.2), rgba(76, 26, 112, 0.3));
}

.trading-link-icon {
    font-size: 2.5rem;
    margin-right: 1.5rem;
    color: var(--observer-gold);
    text-shadow: var(--observer-glow);
}

.trading-link-text {
    flex: 1;
}

.trading-link-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--coherence-cyan);
    margin-bottom: 0.3rem;
}

.trading-link-desc {
    font-size: 0.9rem;
    color: rgba(233, 236, 239, 0.8);
}

.trading-link-arrow {
    font-size: 1.8rem;
    color: var(--coherence-cyan);
    margin-left: 1rem;
    transition: transform 0.3s ease;
}

.trading-link:hover .trading-link-arrow {
    transform: translateX(5px);
}

/* Rodapé */
.qualia-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--space-xl);
    padding-top: var(--space-lg);
    border-top: 1px solid rgba(34, 170, 187, 0.2);
}

.footer-status {
    display: flex;
    gap: var(--space-md);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.indicator-led {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--coherence-cyan);
    box-shadow: var(--quantum-glow);
    animation: pulse 2s infinite alternate;
}

.indicator-label {
    font-size: var(--font-size-xs);
    opacity: 0.8;
}

.footer-timestamp {
    font-size: var(--font-size-xs);
    opacity: 0.8;
}

/* Botões */
.quantum-button {
    background: linear-gradient(90deg, var(--quantum-blue), var(--probability-purple));
    color: var(--consciousness-white);
    border: none;
    border-radius: 20px;
    padding: var(--space-sm) var(--space-lg);
    font-family: var(--font-primary);
    font-size: var(--font-size-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-medium);
    box-shadow: var(--shadow-sm);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.quantum-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--quantum-glow);
}

.quantum-button:active {
    transform: translateY(0);
}

/* Animações */
@keyframes pulse {
    0% {
        opacity: 0.7;
        transform: scale(1);
    }
    100% {
        opacity: 1;
        transform: scale(1.05);
    }
}

@keyframes rotate {
    from {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Media Queries para Responsividade */
@media (max-width: 1200px) {
    .quantum-panels {
        grid-template-columns: repeat(auto-fit, minmax(min(350px, 100%), 1fr));
    }
}

@media (max-width: 768px) {
    .quantum-panels {
        grid-template-columns: 1fr;
    }
    
    .consciousness-header {
        flex-direction: column;
        text-align: center;
    }
    
    .qualia-emblem {
        margin-right: 0;
        margin-bottom: var(--space-md);
    }
    
    .parameters-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .entropy-indicators {
        grid-template-columns: 1fr;
    }
    
    .qualia-footer {
        flex-direction: column;
        gap: var(--space-md);
    }
}

@media (max-width: 480px) {
    :root {
        --space-md: 0.75rem;
        --space-lg: 1.25rem;
        --space-xl: 1.5rem;
    }
    
    .parameters-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-status {
        flex-direction: column;
        align-items: flex-start;
    }
}