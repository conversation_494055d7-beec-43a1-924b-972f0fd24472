import logging

from qualia.utils.logger import get_logger
from qiskit import QuantumCircuit
from qiskit_aer import AerSimulator
from qiskit.circuit import ClassicalRegister

# Configura logging básico
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = get_logger(__name__)

print("\n=== Teste de Diversidade Quântica ===")
print("Configurações:")
print("- Número de qubits: 3")
print("- Número de shots: 1024")
print("\nIniciando teste...")

# Cria um circuito quântico simples
qc = QuantumCircuit(3)

# Adiciona portas Hadamard para criar superposição
for qubit in range(3):
    qc.h(qubit)

# Adiciona portas CNOT para emaranhamento
qc.cx(0, 1)
qc.cx(1, 2)

# Adiciona registrador clássico e medições
qc.add_register(ClassicalRegister(3))
qc.measure([0, 1, 2], [0, 1, 2])

# Executa o circuito usando AerSimulator
simulator = AerSimulator()
job = simulator.run(qc, shots=1024)
result = job.result()
counts = result.get_counts()

# Calcula a diversidade
num_outcomes = len(counts)
total_outcomes = 2**3
diversity_ratio = num_outcomes / total_outcomes

print("\n=== Resultados do teste ===")
print(f"Razão de diversidade: {diversity_ratio:.4f}")
print(f"Número de resultados únicos: {num_outcomes}")
print(f"Total de possíveis resultados: {total_outcomes}")
print("\n=== Fim do teste ===")

# Registra no logger
logger.info(f"Counts diversity ratio: {diversity_ratio:.4f}")
logger.info(f"Number of unique outcomes: {num_outcomes}")
logger.info(f"Total possible outcomes: {total_outcomes}")
