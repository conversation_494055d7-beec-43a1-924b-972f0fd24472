from __future__ import annotations

"""Meta-strategy utilities for adaptive weight allocation."""

import pandas as pd
import numpy as np
from typing import Any, Dict

from ...memory import ExperienceReplay
from ...risk_management.risk_manager_base import QUALIARiskManagerBase

from ...utils.logger import get_logger
from ...utils.data_helpers import is_data_empty
from .indicators import compute_sma, compute_rsi
from .risk import update_rolling_sharpe_ratios, calculate_dynamic_weights

logger = get_logger(__name__)


def calculate_s1_position(tsvf_strength: float, threshold: float) -> int:
    """Map TSVF strength to a discrete S1 trading position."""
    return int(np.sign(tsvf_strength)) if abs(tsvf_strength) > threshold else 0


def calculate_s2_position(
    s1_position: int,
    price_series: pd.Series,
    sma_short_period: int,
    sma_long_period: int,
    rsi_period: int,
    rsi_oversold: float,
    rsi_overbought: float,
) -> int:
    """Determine S2 position using SMA and RSI filters over S1 signal."""
    if s1_position == 0:
        return 0

    sma_short_values = compute_sma(price_series, sma_short_period)
    sma_long_values = compute_sma(price_series, sma_long_period)
    rsi_values = compute_rsi(price_series, rsi_period)

    if (
        is_data_empty(sma_short_values)
        or is_data_empty(sma_long_values)
        or is_data_empty(rsi_values)
    ):
        return 0

    from ...trading.data_utils import safe_iloc

    sma_short = safe_iloc(sma_short_values, -1)
    sma_long = safe_iloc(sma_long_values, -1)
    rsi = safe_iloc(rsi_values, -1)

    # Verificar se valores foram obtidos com sucesso
    if sma_short is None or sma_long is None or rsi is None:
        return 0

    if pd.isna(sma_short) or pd.isna(sma_long) or pd.isna(rsi):
        return 0

    if s1_position > 0:
        if sma_short > sma_long and rsi < rsi_overbought:
            return 1
    elif s1_position < 0:
        if sma_short < sma_long and rsi > rsi_oversold:
            return -1
    return 0


def calculate_s3_position(tsvf_strength_4h: float, threshold: float) -> int:
    """Compute S3 position based on TSVF strength from 4h data."""
    return int(np.sign(tsvf_strength_4h)) if abs(tsvf_strength_4h) > threshold else 0


def update_weights_for_step(strategy: Any, step: int) -> dict:
    """Update dynamic weights for each sub-strategy."""
    if step >= strategy.meta_sharpe_window_hours:
        update_rolling_sharpe_ratios(strategy, strategy.meta_sharpe_window_hours)
        calculate_dynamic_weights(strategy)
    return strategy.weights.copy()


class AlphaGammaPolicy:
    """Simple Q-learning policy to adapt alpha, gamma and window parameters."""

    def __init__(
        self,
        alpha: float,
        gamma: float,
        window: int,
        weights: Dict[str, float] | None = None,
        replay_capacity: int = 500,
        learning_rate: float = 0.1,
        discount_factor: float = 0.95,
        epsilon: float = 0.1,
        risk_manager: QUALIARiskManagerBase | None = None,
        max_updates_per_session: int = -1,
        convergence_threshold: float = 0.005,
        epsilon_decay: float = 0.995,
        epsilon_min: float = 0.01,
        update_rate_limit_per_minute: int = 5,
    ) -> None:
        self.alpha = alpha
        self.gamma = gamma
        self.window = window
        self.weights = (
            weights if weights is not None else {"s1": 1 / 3, "s2": 1 / 3, "s3": 1 / 3}
        )

        self._lr = learning_rate
        self._discount = discount_factor
        self._epsilon = epsilon
        self._replay = ExperienceReplay(replay_capacity, risk_manager=risk_manager)

        self._state_bins = 10
        self._q_table: Dict[int, np.ndarray] = {}
        self._prev_state: int | None = None
        self._prev_action: int | None = None

        self.max_updates_per_session = max_updates_per_session
        self.convergence_threshold = convergence_threshold
        self.epsilon_decay = epsilon_decay
        self.epsilon_min = epsilon_min
        self.update_rate_limit_per_minute = update_rate_limit_per_minute
        self.updates_count = 0
        self.last_params = self.get_params()
        self.convergence_count = 0
        self.converged = False

        # Rate limiting para atualizações
        from collections import deque
        import time

        self._update_timestamps = deque(maxlen=update_rate_limit_per_minute)
        self._last_log_time = 0

        logger.info(
            "AlphaGammaPolicy initialized: alpha=%s gamma=%s window=%s",
            alpha,
            gamma,
            window,
        )

    # Internal utilities -----------------------------------------------------
    def _discretize_state(self, eis: float) -> int:
        eis_clamped = max(0.0, min(1.0, eis))
        return int(round(eis_clamped * (self._state_bins - 1)))

    def _select_action(self, state: int) -> int:
        if np.random.random() < self._epsilon:
            action = np.random.randint(0, 9)
        else:
            q_values = self._q_table.setdefault(state, np.zeros(9))
            action = int(np.argmax(q_values))
        return action

    def _apply_action(self, action: int) -> None:
        if action == 0:
            self.alpha = min(1.0, self.alpha + 0.05)
        elif action == 1:
            self.alpha = max(0.0, self.alpha - 0.05)
        elif action == 2:
            self.gamma = min(1.0, self.gamma + 0.05)
        elif action == 3:
            self.gamma = max(0.0, self.gamma - 0.05)
        elif action == 4:
            self.window += 1
        elif action == 5:
            self.window = max(1, self.window - 1)
        elif action in (6, 7, 8):
            self._shift_weights(action - 6)

    def _shift_weights(self, index: int) -> None:
        keys = list(self.weights.keys())
        target = keys[index]
        self.weights[target] = min(1.0, self.weights[target] + 0.1)
        remain = 1.0 - self.weights[target]
        other_keys = [k for k in keys if k != target]
        share = remain / len(other_keys)
        for k in other_keys:
            self.weights[k] = share

    # Public API -------------------------------------------------------------
    def update(self, policy_inputs: Dict[str, float]) -> None:
        import time

        current_time = time.time()

        # Rate limiting check
        if len(self._update_timestamps) >= self.update_rate_limit_per_minute:
            oldest_update = self._update_timestamps[0]
            if current_time - oldest_update < 60:  # Menos de 1 minuto
                if current_time - self._last_log_time > 30:  # Log apenas a cada 30s
                    logger.debug(
                        "AlphaGammaPolicy: Rate limit atingido (%s updates/min), aguardando",
                        self.update_rate_limit_per_minute,
                    )
                    self._last_log_time = current_time
                return

        if self.converged or (
            self.max_updates_per_session != -1
            and self.updates_count >= self.max_updates_per_session
        ):
            if (
                not self.converged and current_time - self._last_log_time > 30
            ):  # Evita spam de logs
                logger.debug(
                    "AlphaGammaPolicy: Limite de %s atualizações por sessão atingido",
                    self.max_updates_per_session,
                )
                self._last_log_time = current_time
            return

        eis = float(policy_inputs.get("eis", 0.0))
        reward = float(policy_inputs.get("reward", 0.0))

        state = self._discretize_state(eis)

        if self._prev_state is not None and self._prev_action is not None:
            q_values = self._q_table.setdefault(self._prev_state, np.zeros(9))
            next_q = self._q_table.setdefault(state, np.zeros(9))
            td_target = reward + self._discount * np.max(next_q)
            q_values[self._prev_action] += self._lr * (
                td_target - q_values[self._prev_action]
            )
            self._replay.store((self._prev_state, self._prev_action, reward, state))

        action = self._select_action(state)

        prev_params = self.get_params()

        self._apply_action(action)

        current_params = self.get_params()
        param_change = sum(
            abs(current_params[k] - prev_params[k])
            for k in current_params
            if isinstance(current_params[k], (int, float))
        )

        if param_change < self.convergence_threshold:
            self.convergence_count += 1
            if self.convergence_count >= 5:
                self.converged = True
                logger.info(
                    "AlphaGammaPolicy convergiu após %s atualizações (mudança=%.6f)",
                    self.updates_count,
                    param_change,
                )
        else:
            self.convergence_count = 0

        self._epsilon = max(self.epsilon_min, self._epsilon * self.epsilon_decay)

        # Registrar timestamp da atualização
        self._update_timestamps.append(current_time)

        self.updates_count += 1
        self._prev_state = state
        self._prev_action = action

    def get_params(self) -> Dict[str, Any]:
        return {
            "alpha": self.alpha,
            "gamma": self.gamma,
            "window": self.window,
            "weights": self.weights.copy(),
        }
