import numpy as np
import pytest

pytest.importorskip("pytest_benchmark")


def loops_initialization(dim: int, ct: float) -> np.ndarray:
    primary_matrix = np.random.randn(dim, dim)
    for i in range(dim):
        for j in range(dim):
            if i != j:
                primary_matrix[i, j] *= ct ** abs(i - j)
    U, _, Vt = np.linalg.svd(primary_matrix)
    return U @ Vt


def vectorized_initialization(dim: int, ct: float) -> np.ndarray:
    primary_matrix = np.random.randn(dim, dim)
    indices = np.arange(dim)
    scaling = ct ** np.abs(indices[:, None] - indices[None, :])
    np.fill_diagonal(scaling, 1.0)
    primary_matrix *= scaling
    U, _, Vt = np.linalg.svd(primary_matrix)
    return U @ Vt


@pytest.mark.benchmark(group="folding")
def test_folding_initialization_loop(benchmark):
    dim = 20
    ct = 0.618
    result = benchmark(loops_initialization, dim, ct)
    assert result.shape == (dim, dim)


@pytest.mark.benchmark(group="folding")
def test_folding_initialization_vectorized(benchmark):
    dim = 20
    ct = 0.618
    result = benchmark(vectorized_initialization, dim, ct)
    assert result.shape == (dim, dim)
