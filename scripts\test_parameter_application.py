#!/usr/bin/env python3
"""
Teste rápido para validar se os parâmetros estão sendo aplicados corretamente.

YAA-TEST: Validação da aplicação de parâmetros na otimização
"""

import sys
import os
import asyncio
import yaml
import json
from datetime import datetime

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from systematic_fwh_optimization import SystematicFWHOptimizer
    from test_fixed_backtest import RealStrategyExecutor, create_mock_historical_data
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ Required modules not available: {e}")
    MODULES_AVAILABLE = False
    sys.exit(1)


async def test_parameter_application():
    """Testa se os parâmetros estão sendo aplicados corretamente."""
    print("🧪 TESTING PARAMETER APPLICATION")
    print("=" * 50)
    
    # Inicializar otimizador
    optimizer = SystematicFWHOptimizer()
    
    # Testar diferentes conjuntos de parâmetros
    test_parameters = [
        {
            'hype_threshold': 0.15,
            'otoc_max_threshold': 0.25,
            'stop_loss_pct': 1.5,
            'take_profit_pct': 2.5,
            'wave_min_strength': 0.15,
            'quantum_boost_factor': 1.10
        },
        {
            'hype_threshold': 0.45,
            'otoc_max_threshold': 0.50,
            'stop_loss_pct': 4.0,
            'take_profit_pct': 5.0,
            'wave_min_strength': 0.35,
            'quantum_boost_factor': 1.30
        }
    ]
    
    results = []
    
    for i, params in enumerate(test_parameters, 1):
        print(f"\n🔧 Testing Parameter Set {i}:")
        print(f"   Parameters: {params}")
        
        # Criar configuração com parâmetros
        config = optimizer._create_config_variant(params)
        
        # Verificar se parâmetros foram aplicados
        fwh_params = config.get('fibonacci_wave_hype_config', {}).get('params', {})
        trading_params = config.get('trading_system', {}).get('risk_management', {})
        
        print(f"   Applied hype_threshold: {fwh_params.get('hype_threshold', 'NOT FOUND')}")
        print(f"   Applied wave_min_strength: {fwh_params.get('wave_min_strength', 'NOT FOUND')}")
        print(f"   Applied quantum_boost_factor: {fwh_params.get('quantum_boost_factor', 'NOT FOUND')}")
        print(f"   Applied stop_loss_pct: {trading_params.get('stop_loss_pct', 'NOT FOUND')}")
        print(f"   Applied take_profit_pct: {trading_params.get('take_profit_pct', 'NOT FOUND')}")
        
        # Executar backtest rápido
        try:
            historical_data, start_date, end_date = create_mock_historical_data()
            
            executor = RealStrategyExecutor(
                config=config,
                initial_capital=10000.0,
                commission_rate=0.001,
                slippage_rate=0.0005
            )
            
            # Executar apenas uma pequena parte dos dados para teste rápido
            small_data = {}
            for symbol, timeframes in historical_data.items():
                small_data[symbol] = {}
                for tf, df in timeframes.items():
                    small_data[symbol][tf] = df.head(100)  # Apenas 100 primeiras linhas
            
            backtest_results = await executor.run_backtest(small_data, start_date, end_date)
            
            print(f"   ✅ Backtest Result:")
            print(f"      Return: {backtest_results['total_return']:.2%}")
            print(f"      Win Rate: {backtest_results['win_rate']:.1%}")
            print(f"      Trades: {backtest_results['total_trades']}")
            print(f"      Sharpe: {backtest_results['sharpe_ratio']:.3f}")
            
            results.append({
                'parameters': params,
                'results': backtest_results
            })
            
        except Exception as e:
            print(f"   ❌ Backtest Error: {e}")
            results.append({
                'parameters': params,
                'error': str(e)
            })
    
    # Verificar se os resultados são diferentes
    print(f"\n📊 RESULTS COMPARISON:")
    print("=" * 50)
    
    if len(results) >= 2:
        result1 = results[0].get('results', {})
        result2 = results[1].get('results', {})
        
        if result1 and result2:
            return1 = result1.get('total_return', 0)
            return2 = result2.get('total_return', 0)
            
            trades1 = result1.get('total_trades', 0)
            trades2 = result2.get('total_trades', 0)
            
            if return1 != return2 or trades1 != trades2:
                print("✅ SUCCESS: Parameters are being applied correctly!")
                print(f"   Set 1 Return: {return1:.2%}, Trades: {trades1}")
                print(f"   Set 2 Return: {return2:.2%}, Trades: {trades2}")
                return True
            else:
                print("❌ PROBLEM: Results are identical - parameters not being applied!")
                print(f"   Both sets: Return: {return1:.2%}, Trades: {trades1}")
                return False
        else:
            print("❌ PROBLEM: One or both backtests failed")
            return False
    else:
        print("❌ PROBLEM: Not enough results to compare")
        return False


async def main():
    """Executa teste de aplicação de parâmetros."""
    success = await test_parameter_application()
    
    if success:
        print("\n🎉 PARAMETER APPLICATION TEST PASSED!")
        print("   Ready to run full optimization")
    else:
        print("\n❌ PARAMETER APPLICATION TEST FAILED!")
        print("   Need to fix parameter application before optimization")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
