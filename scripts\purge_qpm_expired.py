#!/usr/bin/env python
"""Purge expired patterns from the Quantum Pattern Memory.

This utility loads ``QuantumPatternMemory`` using the persistence path
configured in ``config/strategy_parameters.json`` under
``qpm_config.persistence_path``. It removes patterns older than the
configured ``pattern_ttl_seconds`` by invoking ``qpm._purge_expired()``
and saves the cleaned memory back to disk.

Schedule this script via cron to keep the persistence file compact. For
example, to run it every day at 03:00:

    0 3 * * * /usr/bin/python /path/to/repo/scripts/purge_qpm_expired.py
"""

from __future__ import annotations

import argparse
import json
from pathlib import Path

from qualia.memory.quantum_pattern_memory import QuantumPatternMemory

DEFAULT_CONFIG_PATH = Path("config") / "strategy_parameters.json"
DEFAULT_PERSISTENCE_PATH = Path("data") / "cache" / "qpm_memory.json"


def _get_persistence_path(config_path: Path) -> str:
    """Return persistence path from strategy configuration."""
    if not config_path.exists():
        return DEFAULT_PERSISTENCE_PATH
    with config_path.open("r", encoding="utf-8") as fh:
        try:
            data = json.load(fh)
        except json.JSONDecodeError:
            return DEFAULT_PERSISTENCE_PATH
    qpm_cfg = data.get("qpm_config", {}) if isinstance(data, dict) else {}
    return qpm_cfg.get("persistence_path", DEFAULT_PERSISTENCE_PATH)


def main() -> None:
    parser = argparse.ArgumentParser(description="Purge expired QPM patterns")
    parser.add_argument(
        "--config",
        type=Path,
        default=DEFAULT_CONFIG_PATH,
        help="Path to strategy_parameters.json",
    )
    args = parser.parse_args()

    persistence_path = _get_persistence_path(args.config)
    qpm = QuantumPatternMemory(
        persistence_path=persistence_path, enable_warmstart=False
    )
    qpm._purge_expired()
    qpm.save_to_file()
    print(f"Purged expired patterns and saved to {persistence_path}.")


if __name__ == "__main__":
    main()
