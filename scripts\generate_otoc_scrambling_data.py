"""Geração de dataset de OTOC para circuitos de scrambling aleatório.

Este script executa uma varredura sobre quantidades de qubits, profundidades de
scrambling e níveis de ruído, armazenando o valor do Out-of-Time-Order
Correlator (OTOC) em um arquivo CSV. Pode ser invocado pela linha de comando:

```
python generate_otoc_scrambling_data.py [--output CAMINHO]
```

O parâmetro ``--output`` define o caminho do CSV gerado. Caso não seja
informado, o arquivo ``otoc_scrambling_data.csv`` é criado no diretório atual.
"""

import argparse
import csv
from typing import Iterable

import numpy as np
from numpy.random import Generator, default_rng
from qiskit import QuantumCircuit
from qiskit.quantum_info import Operator, Pauli, Statevector


def _z_on(index: int, num_qubits: int) -> Pauli:
    label = ["I"] * num_qubits
    label[num_qubits - 1 - index] = "Z"
    return Pauli("".join(label))


def random_scrambling_circuit(
    num_qubits: int, depth: int, rng: Generator
) -> QuantumCircuit:
    circuit = QuantumCircuit(num_qubits)
    for _ in range(depth):
        for qubit in range(num_qubits):
            theta = rng.random() * 2 * np.pi
            circuit.rx(theta, qubit)
        for qubit in range(num_qubits - 1):
            circuit.cx(qubit, qubit + 1)
    return circuit


def otoc_value(
    circuit: QuantumCircuit, noise: float = 0.0, qubit_a: int = 0, qubit_b: int = 1
) -> float:
    num_qubits = circuit.num_qubits
    operator_w = Operator(_z_on(qubit_a, num_qubits))
    operator_v = Operator(_z_on(qubit_b, num_qubits))
    state = Statevector.from_instruction(circuit)
    density = np.outer(state.data, state.data.conj())
    if noise > 0:
        dimension = 2**num_qubits
        density = (1 - noise) * density + noise * np.eye(dimension) / dimension
    unitary = Operator(circuit)
    w_t = unitary.adjoint().compose(operator_w).compose(unitary)
    commutator = (
        w_t.data.conj().T @ operator_v.data.conj().T @ w_t.data @ operator_v.data
    )
    value = np.trace(commutator @ density)
    return float(np.real(value))


def generate_dataset(
    depths: Iterable[int],
    qubits: Iterable[int],
    noise_levels: Iterable[float],
    output_csv: str,
    seed: int = 0,
) -> None:
    rng = default_rng(seed)
    rows = []
    for num_qubits in qubits:
        for depth in depths:
            circuit = random_scrambling_circuit(num_qubits, depth, rng)
            for noise in noise_levels:
                value = otoc_value(circuit, noise=noise)
                rows.append(
                    {
                        "num_qubits": num_qubits,
                        "scr_depth": depth,
                        "noise": noise,
                        "otoc": value,
                    }
                )
    with open(output_csv, "w", newline="") as csvfile:
        writer = csv.DictWriter(
            csvfile, fieldnames=["num_qubits", "scr_depth", "noise", "otoc"]
        )
        writer.writeheader()
        writer.writerows(rows)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Gera dataset de OTOC em função da profundidade de scrambling"
    )
    parser.add_argument(
        "--output",
        default="otoc_scrambling_data.csv",
        help="Caminho do arquivo CSV a ser gerado",
    )
    args = parser.parse_args()

    DEPTHS = range(1, 6)
    QUBITS = [2, 3]
    NOISE_LEVELS = [0.0, 0.05]
    generate_dataset(DEPTHS, QUBITS, NOISE_LEVELS, args.output)
