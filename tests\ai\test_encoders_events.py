import importlib
import yaml
from tests.stub_utils import install_stubs

install_stubs()

from qualia.config import encoder_registry
from qualia.memory.event_bus import SimpleEventBus


def reload_plugin(tmp_path):
    config_file = tmp_path / "encoders.yaml"
    with open(config_file, "w", encoding="utf-8") as fh:
        yaml.safe_dump(
            {
                "RSIPhaseEncoder": {"enabled": True},
                "VolumeRatioAmplitudeEncoder": {"enabled": True},
            },
            fh,
        )

    import src.ai.encoders_plugin as plugin

    importlib.reload(encoder_registry)
    return importlib.reload(plugin), config_file


def _capture_events(bus: SimpleEventBus):
    events = []
    bus.subscribe("encoder.created", lambda p: events.append(("created", p)))
    bus.subscribe("encoder.finished", lambda p: events.append(("finished", p)))
    return events


def test_encoder_init_events(monkeypatch, tmp_path):
    plugin, config_file = reload_plugin(tmp_path)
    monkeypatch.setenv("QUALIA_ENCODERS_CONFIG", str(config_file))
    plugin = importlib.reload(plugin)

    bus = SimpleEventBus()
    events = _capture_events(bus)

    encoder_registry.create_encoder("RSIPhaseEncoder", name="rsi", event_bus=bus)
    encoder_registry.create_encoder(
        "VolumeRatioAmplitudeEncoder", name="vra", event_bus=bus
    )

    assert [e[0] for e in events] == ["created", "finished", "created", "finished"]
    for _, payload in events:
        assert payload["class"] in [
            "RSIPhaseEncoder",
            "VolumeRatioAmplitudeEncoder",
        ]
