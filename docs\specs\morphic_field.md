# Morphic Field

As classes `MorphicField` e `QuantumMorphicField` representam um campo
mórfico simplificado utilizado nos experimentos do QUALIA. Ambas mantêm
uma matriz interna e aplicam transformações inspiradas nos operadores de
*folding* e *resonance*.

Principais características
-------------------------
- <PERSON><PERSON><PERSON><PERSON> configurável da matriz do campo.
- Coeficiente de força que controla a ressonância aplicada.
- Histórico interno de estados limitado por ``history_maxlen``.
- `QuantumMorphicField` utiliza `apply_folding` antes da ressonância.

Exemplo rápido
--------------
```python
from qualia.core.morphic_field import QuantumMorphicField

qmf = QuantumMorphicField(dimension=2, field_strength=0.1, alpha=0.2)
qmf.evolve_field(np.eye(2))
print(qmf.state)
```
