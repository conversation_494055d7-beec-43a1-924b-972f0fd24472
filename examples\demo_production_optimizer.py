#!/usr/bin/env python3
"""
Demo do Production Optimizer - Etapa D.1
YAA IMPLEMENTATION: Demonstração das melhorias de produção implementadas.
"""

import sys
import asyncio
import json
import time
from datetime import datetime
from pathlib import Path

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

from src.production_optimizer import ProductionOptimizer
from src.bayesian_optimizer import BayesianOptimizer

async def demo_production_improvements():
    """Demonstra as melhorias de produção implementadas."""
    
    print("🏭 DEMO PRODUCTION OPTIMIZER - MELHORIAS IMPLEMENTADAS")
    print("=" * 70)
    
    # Carrega configuração de produção
    print("📊 Carregando configuração de produção...")
    optimizer = ProductionOptimizer()
    
    print(f"\n🎯 MELHORIAS IMPLEMENTADAS:")
    print("=" * 50)
    
    # 1. Ajuste n_trials_per_cycle para mais precisão
    trials_original = 8
    trials_production = optimizer.config.n_trials_per_cycle
    improvement_trials = ((trials_production / trials_original) - 1) * 100
    
    print(f"1️⃣ PRECISÃO AUMENTADA:")
    print(f"   • Trials por ciclo: {trials_original} → {trials_production}")
    print(f"   • Melhoria: +{improvement_trials:.0f}% mais trials")
    print(f"   • Impacto: Maior precisão na otimização")
    
    # 2. Múltiplos símbolos para diversificação
    symbols_original = 1
    symbols_production = len(optimizer.config.symbols)
    
    print(f"\n2️⃣ DIVERSIFICAÇÃO IMPLEMENTADA:")
    print(f"   • Símbolos: {symbols_original} → {symbols_production}")
    print(f"   • Ativos: {', '.join(optimizer.config.symbols)}")
    print(f"   • Pesos configurados:")
    
    for symbol, weight in optimizer.config.symbol_weights.items():
        print(f"      • {symbol}: {weight*100:.1f}%")
    
    # 3. Worker settings para produção
    worker_settings = optimizer.config.worker_settings
    
    print(f"\n3️⃣ CONFIGURAÇÕES DE PRODUÇÃO:")
    print(f"   • Intervalo entre ciclos: {worker_settings['cycle_interval_minutes']} minutos")
    print(f"   • Runtime máximo: {worker_settings['max_runtime_hours']} horas")
    print(f"   • Limite de memória: {worker_settings['memory_limit_mb']} MB")
    print(f"   • Limite de CPU: {worker_settings['cpu_limit_percent']}%")
    print(f"   • Restart automático: {worker_settings['restart_on_failure']}")
    print(f"   • Max falhas consecutivas: {worker_settings['max_consecutive_failures']}")
    
    return optimizer

async def demo_production_cycle():
    """Demonstra um ciclo de produção otimizado."""
    
    print(f"\n🔄 DEMONSTRAÇÃO DE CICLO DE PRODUÇÃO")
    print("=" * 50)
    
    optimizer = ProductionOptimizer()
    
    # Configura para demo rápida
    demo_config = optimizer.config
    demo_config.n_trials_per_cycle = 5  # Reduzido para demo
    demo_config.symbols = demo_config.symbols[:3]  # Primeiros 3 símbolos
    
    print(f"📊 Configuração da Demo:")
    print(f"   • Símbolos: {demo_config.symbols}")
    print(f"   • Trials por ciclo: {demo_config.n_trials_per_cycle}")
    
    # Inicializa otimizador Bayesiano
    bayesian_opt = BayesianOptimizer(demo_config)
    optimizer.optimizer = bayesian_opt
    
    print(f"\n🚀 Executando ciclo de produção otimizado...")
    
    start_time = time.time()
    
    try:
        # Verifica recursos do sistema
        print(f"🔍 Verificando recursos do sistema...")
        resources = optimizer.check_system_resources()
        print(f"   • Memória: {resources['memory_usage_mb']:.1f}MB")
        print(f"   • CPU: {resources['cpu_percent']:.1f}%")
        
        within_limits = optimizer.check_resource_limits()
        if within_limits:
            print(f"   ✅ Recursos dentro dos limites")
        else:
            print(f"   ⚠️ Recursos próximos aos limites (continuando demo)")
        
        # Executa ciclo de otimização
        print(f"\n⚙️ Executando otimização multi-símbolo...")
        cycle_result = optimizer.run_production_cycle()
        
        cycle_time = time.time() - start_time
        
        if cycle_result['success']:
            print(f"   ✅ Ciclo concluído com sucesso!")
            print(f"   📊 Resultados:")
            print(f"      • Tempo: {cycle_time:.1f}s")
            print(f"      • Trials: {cycle_result.get('trials', 0)}")
            print(f"      • Melhor PnL: {cycle_result.get('best_pnl', 0):.4f}%")
        else:
            print(f"   ⚠️ Ciclo com problemas: {cycle_result.get('reason', 'unknown')}")
        
        # Mostra estatísticas
        print(f"\n📈 Estatísticas do Sistema:")
        stats = optimizer.stats
        for key, value in stats.items():
            if isinstance(value, float):
                print(f"   • {key}: {value:.2f}")
            else:
                print(f"   • {key}: {value}")
        
        # Log de métricas
        print(f"\n📋 Registrando métricas de produção...")
        optimizer.log_metrics()
        print(f"   ✅ Métricas salvas em: logs/production_metrics.log")
        
    except Exception as e:
        print(f"   ❌ Erro no ciclo: {e}")
    
    return cycle_time

def demo_configuration_comparison():
    """Compara configurações demo vs produção."""
    
    print(f"\n📊 COMPARAÇÃO: DEMO vs PRODUÇÃO")
    print("=" * 50)
    
    # Configuração demo (original)
    demo_config = {
        'n_trials_per_cycle': 8,
        'symbols': ['BTCUSDT'],
        'cycle_interval': '10 segundos',
        'monitoring': 'básico'
    }
    
    # Configuração produção
    with open('config/production_config.json', 'r') as f:
        prod_config = json.load(f)
    
    comparisons = [
        ('Trials por Ciclo', demo_config['n_trials_per_cycle'], prod_config['n_trials_per_cycle']),
        ('Símbolos', len(demo_config['symbols']), len(prod_config['symbols'])),
        ('Intervalo', demo_config['cycle_interval'], f"{prod_config['worker_settings']['cycle_interval_minutes']} minutos"),
        ('Runtime Máximo', 'ilimitado', f"{prod_config['worker_settings']['max_runtime_hours']} horas"),
        ('Monitoramento', demo_config['monitoring'], 'avançado com métricas'),
        ('Gestão de Recursos', 'não', 'sim (CPU/Memória)'),
        ('Restart Automático', 'não', prod_config['worker_settings']['restart_on_failure']),
        ('Risk Management', 'não', 'sim (drawdown, stop-loss)')
    ]
    
    print(f"{'Aspecto':<20} {'Demo':<15} {'Produção':<20} {'Melhoria'}")
    print("-" * 70)
    
    for aspect, demo_val, prod_val in comparisons:
        if isinstance(demo_val, (int, float)) and isinstance(prod_val, (int, float)):
            if prod_val > demo_val:
                improvement = f"+{((prod_val/demo_val)-1)*100:.0f}%"
            else:
                improvement = "="
        else:
            improvement = "✅" if str(prod_val) != str(demo_val) else "="
        
        print(f"{aspect:<20} {str(demo_val):<15} {str(prod_val):<20} {improvement}")

async def main():
    """Função principal da demo de produção."""
    
    print("🎮 QUALIA PRODUCTION OPTIMIZER - DEMO COMPLETA")
    print("=" * 70)
    
    try:
        # 1. Demonstra melhorias implementadas
        optimizer = await demo_production_improvements()
        
        # 2. Executa ciclo de produção
        cycle_time = await demo_production_cycle()
        
        # 3. Compara configurações
        demo_configuration_comparison()
        
        # Resumo final
        print(f"\n" + "=" * 70)
        print(f"🎉 DEMO DE PRODUÇÃO CONCLUÍDA!")
        print("=" * 70)
        
        print(f"\n✅ MELHORIAS IMPLEMENTADAS COM SUCESSO:")
        print(f"   1️⃣ Precisão: 25 trials/ciclo (+212% vs demo)")
        print(f"   2️⃣ Diversificação: 8 símbolos (+700% vs demo)")
        print(f"   3️⃣ Produção: 12 configurações avançadas")
        print(f"   4️⃣ Monitoramento: Recursos e métricas")
        print(f"   5️⃣ Robustez: Restart automático e limites")
        
        print(f"\n🚀 SISTEMA PRONTO PARA PRODUÇÃO:")
        print(f"   • Comando: python src/production_optimizer.py")
        print(f"   • Configuração: config/production_config.json")
        print(f"   • Logs: logs/production_optimizer.log")
        print(f"   • Métricas: logs/production_metrics.log")
        
        print(f"\n💡 PRÓXIMOS PASSOS:")
        print(f"   • Ajustar limites de recursos conforme servidor")
        print(f"   • Configurar notificações (email/webhook)")
        print(f"   • Monitorar performance em produção")
        print(f"   • Ajustar pesos dos símbolos conforme mercado")
        
        print(f"\n🏆 PERFORMANCE ESPERADA:")
        print(f"   • Precisão: +212% mais trials por ciclo")
        print(f"   • Diversificação: 8x mais ativos")
        print(f"   • Robustez: Monitoramento 24/7")
        print(f"   • Eficiência: Gestão automática de recursos")
        
    except KeyboardInterrupt:
        print(f"\n🛑 Demo interrompida pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro na demo: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n👋 Demo de produção finalizada!")


if __name__ == "__main__":
    asyncio.run(main())
