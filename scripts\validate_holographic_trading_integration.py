#!/usr/bin/env python3
"""
QUALIA Holographic Trading Integration Validator
Integra o sistema de validação com o universo holográfico real
"""

import asyncio
import logging
import sys
import os
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import statistics

# Add QUALIA path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from qualia.consciousness.holographic_universe import HolographicMarketUniverse, HolographicEvent
    from qualia.consciousness.real_data_collectors import RealDataCollector
    QUALIA_AVAILABLE = True
except ImportError as e:
    logging.warning(f"QUALIA modules not available: {e}")
    QUALIA_AVAILABLE = False

@dataclass
class EnhancedTradingSignal:
    """Sinal de trading integrado com dados holográficos"""
    timestamp: datetime
    symbol: str
    signal_type: str
    strength: float
    confidence: float
    entry_price: float
    stop_loss: float
    take_profit: float
    position_size: float
    source: str
    holographic_pattern: Optional[Dict[str, Any]] = None
    quantum_state: Optional[List[float]] = None

class HolographicTradingValidator:
    """Validador integrado com universo holográfico"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.signals: List[EnhancedTradingSignal] = []
        self.positions = []
        
        # Configurações
        self.symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT']
        self.initial_balance = 10000.0
        self.current_balance = self.initial_balance
        
        # Componentes QUALIA
        self.holographic_universe = None
        self.data_collector = None
        
        # Simulação de preços
        self.market_prices = {
            'BTCUSDT': 107000.0,
            'ETHUSDT': 3500.0,
            'ADAUSDT': 0.9,
            'SOLUSDT': 200.0
        }
        
        # Métricas aprimoradas
        self.holographic_signals = 0
        self.traditional_signals = 0
        self.pattern_strength_history = []
        
    async def initialize_qualia_components(self):
        """Inicializa componentes QUALIA se disponíveis"""
        if not QUALIA_AVAILABLE:
            self.logger.warning("⚠️ QUALIA não disponível, usando simulação")
            return False
            
        try:
            # Inicializa universo holográfico
            self.holographic_universe = HolographicMarketUniverse()
            self.logger.info("✅ Universo holográfico inicializado")
            
            # Inicializa coletor de dados
            self.data_collector = RealDataCollector()
            self.logger.info("✅ Coletor de dados inicializado")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Erro inicializando QUALIA: {e}")
            return False
    
    def generate_enhanced_market_data(self) -> Dict[str, Dict[str, Any]]:
        """Gera dados de mercado com informações holográficas"""
        market_data = {}
        
        for symbol in self.symbols:
            # Simula mudança de preço
            price_change = random.uniform(-0.04, 0.04)  # ±4%
            self.market_prices[symbol] *= (1 + price_change)
            
            # Indicadores técnicos aprimorados
            rsi = random.uniform(10, 90)
            volume_ratio = random.uniform(0.3, 4.0)
            volatility = random.uniform(0.005, 0.12)
            
            # Simula dados holográficos
            holographic_amplitude = random.uniform(0.1, 1.0)
            pattern_coherence = random.uniform(0.2, 0.9)
            quantum_interference = random.uniform(-0.5, 0.5)
            
            market_data[symbol] = {
                'symbol': symbol,
                'price': self.market_prices[symbol],
                'change_24h': price_change * 100,
                'rsi': rsi,
                'volume_ratio': volume_ratio,
                'volatility': volatility,
                'timestamp': datetime.now(),
                # Dados holográficos simulados
                'holographic_amplitude': holographic_amplitude,
                'pattern_coherence': pattern_coherence,
                'quantum_interference': quantum_interference,
                'field_strength': holographic_amplitude * pattern_coherence
            }
            
        return market_data
    
    async def inject_holographic_events(self, market_data: Dict[str, Any]):
        """Injeta eventos no universo holográfico"""
        if not self.holographic_universe:
            return
            
        for symbol, data in market_data.items():
            try:
                # Calcula amplitude baseada em múltiplos fatores
                price_factor = abs(data.get('change_24h', 0)) / 100
                volume_factor = min(data.get('volume_ratio', 1.0) / 2.0, 1.0)
                volatility_factor = data.get('volatility', 0.02) * 10
                
                amplitude = (price_factor + volume_factor + volatility_factor) / 3.0
                amplitude = max(amplitude, 0.1)
                
                # Injeta evento holográfico
                
                # Obtém posição do símbolo no campo
                symbol_key = symbol.replace('USDT', '')
                position = self.holographic_universe.symbol_positions.get(symbol_key, (100, 100))
                
                event = HolographicEvent(
                    position=position,
                    time=time.time(),
                    amplitude=amplitude,
                    spatial_sigma=15.0,
                    temporal_sigma=10.0,
                    event_type=f"enhanced_market_{symbol.lower()}",
                    source_data=data,
                    confidence=0.8
                )
                
                await self.holographic_universe.inject_holographic_event(event)
                
            except Exception as e:
                self.logger.error(f"Erro injetando evento holográfico {symbol}: {e}")
    
    async def analyze_holographic_patterns(self) -> List[Dict[str, Any]]:
        """Analisa padrões holográficos"""
        if not self.holographic_universe:
            return []
            
        try:
            patterns = self.holographic_universe.analyze_holographic_patterns()
            self.logger.info(f"🔍 {len(patterns)} padrões holográficos detectados")
            
            # Armazena força dos padrões
            for pattern in patterns:
                strength = pattern.strength if hasattr(pattern, 'strength') else 0
                self.pattern_strength_history.append(strength)
                
            return patterns
            
        except Exception as e:
            self.logger.error(f"Erro analisando padrões holográficos: {e}")
            return []
    
    def generate_enhanced_signals(self, market_data: Dict[str, Any], 
                                holographic_patterns: List[Dict[str, Any]]) -> List[EnhancedTradingSignal]:
        """Gera sinais integrados com dados holográficos"""
        signals = []
        
        # Estratégias tradicionais aprimoradas
        traditional_signals = self._generate_traditional_signals(market_data)
        
        # Estratégias holográficas
        holographic_signals = self._generate_holographic_signals(market_data, holographic_patterns)
        
        # Combina sinais
        all_signals = traditional_signals + holographic_signals
        
        # Filtra e otimiza sinais
        filtered_signals = self._filter_and_optimize_signals(all_signals)
        
        return filtered_signals
    
    def _generate_traditional_signals(self, market_data: Dict[str, Any]) -> List[EnhancedTradingSignal]:
        """Gera sinais tradicionais aprimorados"""
        signals = []
        
        for symbol, data in market_data.items():
            # Estratégia 1: RSI Dinâmico com Volatilidade
            if data['rsi'] < 30 and data['volatility'] > 0.02:
                signal = self._create_enhanced_signal(
                    symbol=symbol,
                    signal_type='BUY',
                    strength=0.7 + (30 - data['rsi']) / 100,  # Força dinâmica
                    confidence=0.6 + data['volatility'] * 5,   # Confiança baseada em volatilidade
                    entry_price=data['price'],
                    source='rsi_dynamic_oversold'
                )
                signals.append(signal)
                self.traditional_signals += 1
                
            elif data['rsi'] > 70 and data['volatility'] > 0.02:
                signal = self._create_enhanced_signal(
                    symbol=symbol,
                    signal_type='SELL',
                    strength=0.7 + (data['rsi'] - 70) / 100,
                    confidence=0.6 + data['volatility'] * 5,
                    entry_price=data['price'],
                    source='rsi_dynamic_overbought'
                )
                signals.append(signal)
                self.traditional_signals += 1
            
            # Estratégia 2: Volume Breakout Aprimorado
            elif data['volume_ratio'] > 2.5 and abs(data['change_24h']) > 3.0:
                signal_type = 'BUY' if data['change_24h'] > 0 else 'SELL'
                strength = min(0.8, 0.5 + (data['volume_ratio'] - 2.0) / 10)
                
                signal = self._create_enhanced_signal(
                    symbol=symbol,
                    signal_type=signal_type,
                    strength=strength,
                    confidence=0.7,
                    entry_price=data['price'],
                    source='volume_breakout_enhanced'
                )
                signals.append(signal)
                self.traditional_signals += 1
        
        return signals
    
    def _generate_holographic_signals(self, market_data: Dict[str, Any], 
                                    patterns: List[Dict[str, Any]]) -> List[EnhancedTradingSignal]:
        """Gera sinais baseados em padrões holográficos"""
        signals = []
        
        for pattern in patterns:
            try:
                # Extrai símbolo do padrão
                symbol = self._extract_symbol_from_pattern(pattern)
                if not symbol or symbol not in market_data:
                    continue
                
                data = market_data[symbol]
                pattern_strength = pattern.strength if hasattr(pattern, 'strength') else 0.3
                pattern_coherence = pattern.confidence if hasattr(pattern, 'confidence') else 0.5
                
                # Só gera sinal se padrão for forte o suficiente
                if pattern_strength < 0.25:
                    continue
                
                # Determina direção baseada em múltiplos fatores
                signal_type = self._determine_holographic_signal_direction(pattern, data)
                
                if signal_type != 'HOLD':
                    # Calcula força baseada em padrão holográfico + dados tradicionais
                    combined_strength = (pattern_strength * 0.6) + (self._calculate_traditional_strength(data) * 0.4)
                    combined_confidence = (pattern_coherence * 0.7) + (data.get('field_strength', 0.5) * 0.3)
                    
                    signal = self._create_enhanced_signal(
                        symbol=symbol,
                        signal_type=signal_type,
                        strength=min(combined_strength, 1.0),
                        confidence=min(combined_confidence, 1.0),
                        entry_price=data['price'],
                        source='holographic_pattern',
                        holographic_pattern=pattern,
                        quantum_state=[pattern_strength, pattern_coherence]
                    )
                    signals.append(signal)
                    self.holographic_signals += 1
                    
            except Exception as e:
                self.logger.error(f"Erro gerando sinal holográfico: {e}")
        
        return signals
    
    def _extract_symbol_from_pattern(self, pattern) -> Optional[str]:
        """Extrai símbolo do padrão holográfico"""
        # Busca referências de símbolos no padrão
        pattern_str = str(pattern).upper()
        for symbol in self.symbols:
            if symbol.replace('USDT', '') in pattern_str:
                return symbol
        
        # Se não encontrar, usa posição do padrão para mapear símbolo
        if hasattr(pattern, 'position'):
            # Encontra símbolo mais próximo da posição do padrão
            min_distance = float('inf')
            closest_symbol = None
            
            for symbol in self.symbols:
                symbol_key = symbol.replace('USDT', '')
                if hasattr(self.holographic_universe, 'symbol_positions'):
                    symbol_pos = self.holographic_universe.symbol_positions.get(symbol_key, (100, 100))
                    distance = ((pattern.position[0] - symbol_pos[0]) ** 2 + 
                              (pattern.position[1] - symbol_pos[1]) ** 2) ** 0.5
                    if distance < min_distance:
                        min_distance = distance
                        closest_symbol = symbol
            
            if closest_symbol:
                return closest_symbol
        
        # Fallback: escolhe baseado em hash do padrão
        pattern_hash = hash(str(pattern)) % len(self.symbols)
        return self.symbols[pattern_hash]
    
    def _determine_holographic_signal_direction(self, pattern, 
                                              market_data: Dict[str, Any]) -> str:
        """Determina direção do sinal holográfico"""
        # Analisa tipo de padrão
        pattern_type = pattern.pattern_type if hasattr(pattern, 'pattern_type') else 'unknown'
        pattern_strength = pattern.strength if hasattr(pattern, 'strength') else 0.5
        
        # Combina informações do padrão com dados de mercado
        rsi = market_data.get('rsi', 50)
        quantum_interference = market_data.get('quantum_interference', 0)
        price_momentum = market_data.get('change_24h', 0)
        
        # Lógica combinada baseada em tipo de padrão
        if pattern_type in ['bullish', 'ascending'] or (pattern_strength > 0.6 and price_momentum > 0):
            return 'BUY'
        elif pattern_type in ['bearish', 'descending'] or (pattern_strength > 0.6 and price_momentum < 0):
            return 'SELL'
        elif rsi < 35 and pattern_strength > 0.4 and quantum_interference > 0:
            return 'BUY'
        elif rsi > 65 and pattern_strength > 0.4 and quantum_interference < 0:
            return 'SELL'
        elif pattern_strength > 0.7:  # Padrão muito forte
            return 'BUY' if quantum_interference > 0 else 'SELL'
        else:
            return 'HOLD'
    
    def _calculate_traditional_strength(self, data: Dict[str, Any]) -> float:
        """Calcula força baseada em indicadores tradicionais"""
        rsi = data.get('rsi', 50)
        volume_ratio = data.get('volume_ratio', 1.0)
        volatility = data.get('volatility', 0.02)
        
        # RSI extremos
        rsi_strength = 0
        if rsi < 30:
            rsi_strength = (30 - rsi) / 30
        elif rsi > 70:
            rsi_strength = (rsi - 70) / 30
        
        # Volume
        volume_strength = min((volume_ratio - 1.0) / 2.0, 1.0)
        
        # Volatilidade
        volatility_strength = min(volatility / 0.05, 1.0)
        
        return (rsi_strength + volume_strength + volatility_strength) / 3.0
    
    def _create_enhanced_signal(self, symbol: str, signal_type: str, strength: float,
                              confidence: float, entry_price: float, source: str,
                              holographic_pattern: Optional[Dict[str, Any]] = None,
                              quantum_state: Optional[List[float]] = None) -> EnhancedTradingSignal:
        """Cria sinal aprimorado"""
        
        # Stop loss dinâmico baseado em volatilidade
        volatility = random.uniform(0.01, 0.08)  # Simula volatilidade
        stop_loss_pct = max(0.015, min(0.035, volatility * 1.5))  # 1.5% a 3.5%
        
        # Take profit baseado em força do sinal
        take_profit_pct = max(0.04, min(0.12, strength * 0.15))  # 4% a 12%
        
        if signal_type == 'BUY':
            stop_loss = entry_price * (1 - stop_loss_pct)
            take_profit = entry_price * (1 + take_profit_pct)
        elif signal_type == 'SELL':
            stop_loss = entry_price * (1 + stop_loss_pct)
            take_profit = entry_price * (1 - take_profit_pct)
        else:
            stop_loss = entry_price
            take_profit = entry_price
        
        # Position sizing baseado em confiança
        base_risk = 0.02  # 2% base
        adjusted_risk = base_risk * confidence
        risk_amount = self.current_balance * adjusted_risk
        price_risk = abs(entry_price - stop_loss)
        position_size = risk_amount / price_risk if price_risk > 0 else 0.01
        
        # Limita posição
        max_position = self.current_balance * 0.15 / entry_price
        position_size = min(position_size, max_position)
        
        return EnhancedTradingSignal(
            timestamp=datetime.now(),
            symbol=symbol,
            signal_type=signal_type,
            strength=strength,
            confidence=confidence,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            position_size=position_size,
            source=source,
            holographic_pattern=holographic_pattern,
            quantum_state=quantum_state
        )
    
    def _filter_and_optimize_signals(self, signals: List[EnhancedTradingSignal]) -> List[EnhancedTradingSignal]:
        """Filtra e otimiza sinais"""
        # Remove sinais fracos
        strong_signals = [s for s in signals if s.strength > 0.4 and s.confidence > 0.5]
        
        # Remove sinais conflitantes para o mesmo símbolo
        symbol_signals = {}
        for signal in strong_signals:
            if signal.symbol not in symbol_signals:
                symbol_signals[signal.symbol] = signal
            else:
                # Mantém o sinal mais forte
                if signal.strength > symbol_signals[signal.symbol].strength:
                    symbol_signals[signal.symbol] = signal
        
        return list(symbol_signals.values())
    
    def print_enhanced_performance_report(self):
        """Relatório de performance aprimorado"""
        print("\n" + "="*70)
        print("🌌 QUALIA HOLOGRAPHIC TRADING VALIDATION REPORT")
        print("="*70)
        
        print("\n📊 SINAIS GERADOS:")
        print(f"   • Total de Sinais: {len(self.signals)}")
        print(f"   • Sinais Tradicionais: {self.traditional_signals}")
        print(f"   • Sinais Holográficos: {self.holographic_signals}")
        
        if self.pattern_strength_history:
            avg_pattern_strength = statistics.mean(self.pattern_strength_history)
            max_pattern_strength = max(self.pattern_strength_history)
            print(f"   • Força Média dos Padrões: {avg_pattern_strength:.3f}")
            print(f"   • Força Máxima dos Padrões: {max_pattern_strength:.3f}")
        
        print("\n🎯 ANÁLISE POR FONTE:")
        sources = {}
        for signal in self.signals:
            if signal.source not in sources:
                sources[signal.source] = {'count': 0, 'avg_strength': 0}
            sources[signal.source]['count'] += 1
            sources[signal.source]['avg_strength'] += signal.strength
        
        for source, data in sources.items():
            avg_strength = data['avg_strength'] / data['count']
            print(f"   • {source}: {data['count']} sinais (força média: {avg_strength:.2f})")
        
        print("\n" + "="*70)
    
    async def run_enhanced_validation(self, duration_minutes: int = 5, cycle_seconds: int = 20):
        """Executa validação aprimorada"""
        self.logger.info(f"🌌 Iniciando validação holográfica por {duration_minutes} minutos...")
        
        # Inicializa componentes QUALIA
        qualia_initialized = await self.initialize_qualia_components()
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        cycle = 0
        
        while datetime.now() < end_time:
            cycle += 1
            self.logger.info(f"\n🔄 Ciclo Holográfico {cycle} - {datetime.now().strftime('%H:%M:%S')}")
            
            try:
                # 1. Gera dados de mercado aprimorados
                market_data = self.generate_enhanced_market_data()
                self.logger.info(f"📊 Dados holográficos para {len(market_data)} símbolos")
                
                # 2. Injeta eventos holográficos
                if qualia_initialized:
                    await self.inject_holographic_events(market_data)
                
                # 3. Analisa padrões holográficos
                patterns = await self.analyze_holographic_patterns()
                
                # 4. Gera sinais integrados
                new_signals = self.generate_enhanced_signals(market_data, patterns)
                self.signals.extend(new_signals)
                
                if new_signals:
                    self.logger.info(f"📡 {len(new_signals)} sinais integrados gerados")
                    for signal in new_signals:
                        quantum_info = f" [Quantum: {signal.quantum_state}]" if signal.quantum_state else ""
                        self.logger.info(f"   • {signal.symbol} {signal.signal_type} "
                                       f"(força: {signal.strength:.2f}, confiança: {signal.confidence:.2f}, "
                                       f"fonte: {signal.source}){quantum_info}")
                
                # Status
                total_traditional = self.traditional_signals
                total_holographic = self.holographic_signals
                self.logger.info(f"🎯 Status: {total_traditional} tradicionais, {total_holographic} holográficos")
                
                await asyncio.sleep(cycle_seconds)
                
            except Exception as e:
                self.logger.error(f"❌ Erro no ciclo holográfico {cycle}: {e}")
                await asyncio.sleep(5)
        
        self.logger.info("✅ Validação holográfica concluída!")
        self.print_enhanced_performance_report()

async def main():
    """Função principal"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    validator = HolographicTradingValidator()
    
    try:
        await validator.run_enhanced_validation(duration_minutes=4, cycle_seconds=20)
        
    except KeyboardInterrupt:
        print("\n🛑 Validação holográfica interrompida")
        validator.print_enhanced_performance_report()
    except Exception as e:
        print(f"\n❌ Erro durante validação holográfica: {e}")
        validator.print_enhanced_performance_report()

if __name__ == "__main__":
    asyncio.run(main())
