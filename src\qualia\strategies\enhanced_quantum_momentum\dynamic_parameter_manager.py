"""
Dynamic Parameter Manager for Enhanced Quantum Momentum Strategy.

This module provides real-time parameter optimization and market-regime-aware
parameter adjustment for the Enhanced Quantum Momentum Strategy.
"""

import json
import time
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

from ...utils.logger import get_logger
from ...config.config_loader import ConfigLoader

logger = get_logger(__name__)


@dataclass
class MarketRegimeParameters:
    """Parameters optimized for specific market regimes."""
    signal_threshold: float
    quantum_weight: float
    rsi_overbought: float
    rsi_oversold: float
    min_volatility: float
    max_volatility: float
    confidence_multiplier: float = 1.0


@dataclass
class DynamicParameterState:
    """Current state of dynamic parameters."""
    current_regime: str = "neutral"
    last_update: datetime = field(default_factory=datetime.now)
    performance_window: List[float] = field(default_factory=list)
    parameter_history: List[Dict[str, Any]] = field(default_factory=list)
    confidence_history: List[float] = field(default_factory=list)


class DynamicParameterManager:
    """
    Manages dynamic parameter optimization for Enhanced Quantum Momentum Strategy.
    
    Features:
    - Loads optimized parameters from Bayesian optimization results
    - Adapts parameters based on market regime
    - Provides real-time parameter adjustment
    - Enhances confidence calculation
    """
    
    def __init__(self, symbol: str = "BTCUSDT", config_manager: Optional[ConfigLoader] = None):
        self.symbol = symbol
        self.config_manager = config_manager or ConfigLoader()
        
        # Parameter state
        self.state = DynamicParameterState()
        
        # Market regime parameters
        self.regime_parameters = {
            "uptrend": MarketRegimeParameters(
                signal_threshold=0.45,
                quantum_weight=0.60,
                rsi_overbought=72.0,
                rsi_oversold=28.0,
                min_volatility=0.008,
                max_volatility=0.04,
                confidence_multiplier=1.1
            ),
            "downtrend": MarketRegimeParameters(
                signal_threshold=0.50,
                quantum_weight=0.65,
                rsi_overbought=75.0,
                rsi_oversold=25.0,
                min_volatility=0.010,
                max_volatility=0.05,
                confidence_multiplier=1.2
            ),
            "range": MarketRegimeParameters(
                signal_threshold=0.35,
                quantum_weight=0.50,
                rsi_overbought=68.0,
                rsi_oversold=32.0,
                min_volatility=0.005,
                max_volatility=0.03,
                confidence_multiplier=0.9
            ),
            "neutral": MarketRegimeParameters(
                signal_threshold=0.40,
                quantum_weight=0.55,
                rsi_overbought=70.0,
                rsi_oversold=30.0,
                min_volatility=0.007,
                max_volatility=0.035,
                confidence_multiplier=1.0
            )
        }
        
        # Load optimized parameters from Bayesian optimization
        self.optimized_parameters = self._load_optimized_parameters()
        
        # Performance tracking
        self.performance_window_size = 50
        self.min_confidence_threshold = 0.20
        self.max_confidence_threshold = 0.85
        
        logger.info(f"[DYNAMIC] DynamicParameterManager initialized for {symbol}")
    
    def _load_optimized_parameters(self) -> Dict[str, Any]:
        """Load optimized parameters from Bayesian optimization results."""
        try:
            # Try to load from benchmark results
            benchmark_file = Path("data/benchmark_results_20250706_141236_best_configs.json")
            if benchmark_file.exists():
                with open(benchmark_file, 'r') as f:
                    data = json.load(f)
                    
                best_configs = data.get("best_configurations", [])
                for config in best_configs:
                    if config.get("symbol") == self.symbol:
                        params = config.get("parameters", {})
                        logger.info(f"[DYNAMIC] Loaded optimized parameters for {self.symbol}: {params}")
                        return params
            
            # Fallback to default optimized parameters
            return {
                "price_amplification": 5.0,
                "news_amplification": 11.3,  # From recent optimization
                "min_confidence": 0.45
            }
            
        except Exception as e:
            logger.warning(f"[DYNAMIC] Failed to load optimized parameters: {e}")
            return {
                "price_amplification": 1.0,
                "news_amplification": 1.0,
                "min_confidence": 0.60
            }
    
    def get_dynamic_parameters(self, market_regime: str, volatility: float, 
                             trend_strength: float, quantum_coherence: float) -> Dict[str, Any]:
        """
        Get dynamically adjusted parameters based on current market conditions.
        
        Args:
            market_regime: Current market regime (uptrend, downtrend, range, neutral)
            volatility: Current market volatility
            trend_strength: Strength of current trend (0-1)
            quantum_coherence: Quantum coherence level (0-1)
            
        Returns:
            Dictionary of adjusted parameters
        """
        # Get base parameters for regime
        base_params = self.regime_parameters.get(market_regime, self.regime_parameters["neutral"])
        
        # Apply dynamic adjustments
        adjusted_params = {
            "signal_threshold": self._adjust_signal_threshold(
                base_params.signal_threshold, volatility, trend_strength, quantum_coherence
            ),
            "quantum_weight": self._adjust_quantum_weight(
                base_params.quantum_weight, quantum_coherence, trend_strength
            ),
            "rsi_overbought": base_params.rsi_overbought,
            "rsi_oversold": base_params.rsi_oversold,
            "min_volatility": base_params.min_volatility,
            "max_volatility": base_params.max_volatility,
            "confidence_multiplier": base_params.confidence_multiplier
        }
        
        # Add optimized parameters from Bayesian optimization
        adjusted_params.update(self.optimized_parameters)
        
        # Update state
        self.state.current_regime = market_regime
        self.state.last_update = datetime.now()
        self.state.parameter_history.append({
            "timestamp": datetime.now().isoformat(),
            "regime": market_regime,
            "parameters": adjusted_params.copy()
        })
        
        # Keep history limited
        if len(self.state.parameter_history) > 100:
            self.state.parameter_history = self.state.parameter_history[-100:]
        
        logger.debug(f"[DYNAMIC] Parameters for {market_regime}: threshold={adjusted_params['signal_threshold']:.3f}, "
                    f"quantum_weight={adjusted_params['quantum_weight']:.3f}")
        
        return adjusted_params
    
    def _adjust_signal_threshold(self, base_threshold: float, volatility: float, 
                               trend_strength: float, quantum_coherence: float) -> float:
        """Adjust signal threshold based on market conditions."""
        threshold = base_threshold
        
        # Lower threshold in high volatility (more opportunities)
        if volatility > 0.03:
            threshold *= 0.85
        elif volatility < 0.01:
            threshold *= 1.15
        
        # Lower threshold in strong trends
        if trend_strength > 0.7:
            threshold *= 0.90
        elif trend_strength < 0.3:
            threshold *= 1.10
        
        # Adjust based on quantum coherence
        if quantum_coherence > 0.6:
            threshold *= 0.95  # More confident in quantum signals
        elif quantum_coherence < 0.3:
            threshold *= 1.05  # Less confident in quantum signals
        
        return max(self.min_confidence_threshold, min(self.max_confidence_threshold, threshold))
    
    def _adjust_quantum_weight(self, base_weight: float, quantum_coherence: float, 
                             trend_strength: float) -> float:
        """Adjust quantum weight based on quantum coherence and market conditions."""
        weight = base_weight
        
        # Increase quantum weight when coherence is high
        if quantum_coherence > 0.7:
            weight = min(0.75, weight * 1.1)
        elif quantum_coherence < 0.3:
            weight = max(0.35, weight * 0.9)
        
        # Adjust based on trend strength
        if trend_strength > 0.6:
            weight = min(0.70, weight * 1.05)  # Quantum helps in trends
        
        return weight
    
    def calculate_enhanced_confidence(self, combined_score: float, market_regime: str,
                                    volatility: float, trend_strength: float,
                                    quantum_coherence: float, volume_ratio: float = 1.0) -> float:
        """
        Calculate enhanced confidence score considering multiple market factors.
        
        Args:
            combined_score: Base combined score from strategy
            market_regime: Current market regime
            volatility: Market volatility
            trend_strength: Trend strength (0-1)
            quantum_coherence: Quantum coherence (0-1)
            volume_ratio: Volume ratio vs average (default 1.0)
            
        Returns:
            Enhanced confidence score (0-1)
        """
        base_confidence = abs(combined_score)
        
        # Get regime-specific multiplier
        regime_params = self.regime_parameters.get(market_regime, self.regime_parameters["neutral"])
        confidence = base_confidence * regime_params.confidence_multiplier
        
        # Volatility adjustment
        if 0.01 <= volatility <= 0.04:  # Optimal volatility range
            confidence *= 1.1
        elif volatility > 0.06:  # Too volatile
            confidence *= 0.8
        elif volatility < 0.005:  # Too quiet
            confidence *= 0.9
        
        # Trend strength boost
        if trend_strength > 0.6:
            confidence *= (1.0 + trend_strength * 0.2)
        
        # Quantum coherence boost
        if quantum_coherence > 0.5:
            confidence *= (1.0 + quantum_coherence * 0.15)
        
        # Volume confirmation
        if volume_ratio > 1.2:  # Above average volume
            confidence *= 1.05
        elif volume_ratio < 0.8:  # Below average volume
            confidence *= 0.95
        
        # Ensure confidence is in valid range
        confidence = max(0.0, min(1.0, confidence))
        
        # Track confidence history
        self.state.confidence_history.append(confidence)
        if len(self.state.confidence_history) > 100:
            self.state.confidence_history = self.state.confidence_history[-100:]
        
        return confidence
    
    def update_performance_feedback(self, pnl: float, trade_success: bool):
        """Update performance feedback for parameter adjustment."""
        self.state.performance_window.append(pnl)
        
        # Keep window size limited
        if len(self.state.performance_window) > self.performance_window_size:
            self.state.performance_window = self.state.performance_window[-self.performance_window_size:]
        
        # Adjust parameters based on recent performance
        if len(self.state.performance_window) >= 10:
            recent_performance = np.mean(self.state.performance_window[-10:])
            
            # If performance is poor, make parameters more conservative
            if recent_performance < -0.5:
                for regime in self.regime_parameters:
                    self.regime_parameters[regime].signal_threshold *= 1.05
                    self.regime_parameters[regime].confidence_multiplier *= 0.95
                
                logger.info(f"[DYNAMIC] Adjusting parameters more conservatively due to poor performance")
            
            # If performance is good, make parameters slightly more aggressive
            elif recent_performance > 1.0:
                for regime in self.regime_parameters:
                    self.regime_parameters[regime].signal_threshold *= 0.98
                    self.regime_parameters[regime].confidence_multiplier *= 1.02
                
                logger.info(f"[DYNAMIC] Adjusting parameters more aggressively due to good performance")
    
    def get_current_state(self) -> Dict[str, Any]:
        """Get current state for monitoring and debugging."""
        return {
            "symbol": self.symbol,
            "current_regime": self.state.current_regime,
            "last_update": self.state.last_update.isoformat(),
            "recent_confidence": self.state.confidence_history[-10:] if self.state.confidence_history else [],
            "recent_performance": self.state.performance_window[-10:] if self.state.performance_window else [],
            "optimized_parameters": self.optimized_parameters,
            "regime_parameters": {k: {
                "signal_threshold": v.signal_threshold,
                "quantum_weight": v.quantum_weight,
                "confidence_multiplier": v.confidence_multiplier
            } for k, v in self.regime_parameters.items()}
        }
