#!/usr/bin/env python3
"""
Test Real Strategy Integration - Task 4 Validation
Tests the integration of real QualiaTSVFStrategy with ultra-conservative parameters
"""

import asyncio
import logging
import sys
import os
import traceback
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_real_strategy_integration():
    """Test real QualiaTSVFStrategy integration with ultra-conservative parameters"""
    logger.info("[TEST] Starting Real Strategy Integration Test - Task 4")
    
    try:
        # Import the real strategy class
        from scripts.qualia_pilot_trading_system import RealQualiaTSVFStrategy
        
        # Test 1: Strategy Initialization
        logger.info("[TEST] Test 1: Strategy Initialization")
        strategy_config = {
            'risk_per_trade': 0.01,
            'confidence_threshold': 0.85,
            'ultra_conservative_mode': True,
            'paper_trading': True
        }
        
        strategy = RealQualiaTSVFStrategy(strategy_config)
        logger.info("[PASS] Test 1 PASSED: Real strategy initialized successfully")
        
        # Test 2: Strategy Configuration Validation
        logger.info("[TEST] Test 2: Ultra-Conservative Configuration Validation")
        
        # Validate ultra-conservative thresholds
        assert strategy.ultra_conservative_confidence_threshold >= 0.8, "Confidence threshold too low"
        assert strategy.ultra_conservative_signal_threshold >= 0.8, "Signal threshold too low"
        assert strategy.max_position_size_pct <= 0.02, "Position size too high"
        
        logger.info("[PASS] Test 2 PASSED: Ultra-conservative configuration validated")

        # Test 3: Market Analysis with Mock Data
        logger.info("[TEST] Test 3: Market Analysis with Mock Data")
        
        mock_market_data = {
            'price': 45000.0,
            'high': 45100.0,
            'low': 44900.0,
            'open': 45050.0,
            'volume': 1500.0,
            'timestamp': '2024-01-01T12:00:00Z'
        }
        
        mock_quantum_analysis = {
            'consciousness_level': 0.9,
            'decision_confidence': 0.85,
            'quantum_coherence': 0.8,
            'holographic_strength': 0.7
        }
        
        analysis_result = await strategy.analyze_market(mock_market_data, mock_quantum_analysis)
        
        # Validate analysis result structure
        required_keys = ['signal', 'confidence', 'position_size']
        for key in required_keys:
            assert key in analysis_result, f"Missing key: {key}"
        
        logger.info(f"   - Signal: {analysis_result['signal']}")
        logger.info(f"   - Confidence: {analysis_result['confidence']:.3f}")
        logger.info(f"   - Position Size: {analysis_result['position_size']:.4f}")
        logger.info(f"   - Override: {analysis_result.get('ultra_conservative_override', False)}")
        
        logger.info("[PASS] Test 3 PASSED: Market analysis completed successfully")

        # Test 4: Ultra-Conservative Override Testing
        logger.info("[TEST] Test 4: Ultra-Conservative Override Testing")
        
        # Test with low confidence quantum analysis (should trigger override)
        low_confidence_quantum = {
            'consciousness_level': 0.5,  # Below 0.8 threshold
            'decision_confidence': 0.6,  # Below 0.8 threshold
            'quantum_coherence': 0.5,    # Below 0.8 threshold
            'holographic_strength': 0.4
        }
        
        override_result = await strategy.analyze_market(mock_market_data, low_confidence_quantum)
        
        # Should trigger ultra-conservative override
        assert override_result['signal'] == 'hold', "Override should force HOLD signal"
        assert override_result.get('ultra_conservative_override', False), "Override flag should be True"
        assert override_result['position_size'] == 0.0, "Override should set position size to 0"
        
        logger.info(f"   - Override Triggered: {override_result.get('ultra_conservative_override')}")
        logger.info(f"   - Override Reason: {override_result.get('override_reason', 'N/A')}")
        
        logger.info("[PASS] Test 4 PASSED: Ultra-conservative override working correctly")

        # Test 5: Position Size Calculation
        logger.info("[TEST] Test 5: Position Size Calculation")
        
        # Test with high confidence (should allow small position)
        high_confidence_quantum = {
            'consciousness_level': 0.95,
            'decision_confidence': 0.9,
            'quantum_coherence': 0.85,
            'holographic_strength': 0.8
        }
        
        position_result = await strategy.analyze_market(mock_market_data, high_confidence_quantum)
        
        # Validate position size constraints
        position_size = position_result['position_size']
        assert position_size <= 0.005, f"Position size {position_size} exceeds maximum 0.5%"
        
        if position_result['signal'] != 'hold':
            assert position_size > 0, "Non-hold signal should have positive position size"
        
        logger.info(f"   - Position Size: {position_size:.4f} ({position_size*100:.2f}%)")
        logger.info(f"   - Within Limits: {position_size <= 0.005}")
        
        logger.info("[PASS] Test 5 PASSED: Position size calculation within ultra-conservative limits")

        # Test 6: Error Handling
        logger.info("[TEST] Test 6: Error Handling")
        
        # Test with invalid market data
        invalid_market_data = {}
        error_result = await strategy.analyze_market(invalid_market_data, mock_quantum_analysis)
        
        # Should return safe default
        assert error_result['signal'] == 'hold', "Error should default to HOLD"
        assert error_result['confidence'] <= 0.2, "Error should have low confidence"
        assert error_result['position_size'] == 0.0, "Error should have zero position"
        
        logger.info("[PASS] Test 6 PASSED: Error handling returns safe defaults")

        logger.info("[SUCCESS] ALL TESTS PASSED: Real Strategy Integration validated successfully")
        return True

    except Exception as e:
        logger.error(f"[ERROR] Test failed: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def test_strategy_factory_integration():
    """Test StrategyFactory integration"""
    logger.info("[TEST] Testing StrategyFactory Integration")
    
    try:
        from qualia.strategies.strategy_factory import StrategyFactory
        from qualia.strategies.params import QualiaTSVFParams
        
        # Test strategy creation via factory
        params = QualiaTSVFParams(
            tsvf_vector_size=50,
            tsvf_alpha=0.2,
            tsvf_gamma=0.05,
            coherence_threshold=0.8,
            s1_strength_threshold=0.05
        )
        
        strategy = StrategyFactory.create_strategy(
            alias="NovaEstrategiaQUALIA",
            params=params,
            context={
                "symbol": "BTCUSDT",
                "timeframe": "1h",
                "ultra_conservative_mode": True
            }
        )
        
        logger.info("[PASS] StrategyFactory integration test passed")
        return True

    except Exception as e:
        logger.error(f"[ERROR] StrategyFactory test failed: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def main():
    """Run all tests"""
    logger.info("🚀 Starting Task 4: Real Strategy Integration Tests")
    
    # Test 1: Real Strategy Integration
    test1_result = await test_real_strategy_integration()
    
    # Test 2: StrategyFactory Integration
    test2_result = await test_strategy_factory_integration()
    
    # Summary
    total_tests = 2
    passed_tests = sum([test1_result, test2_result])
    
    logger.info(f"[SUMMARY] Test Summary: {passed_tests}/{total_tests} tests passed")

    if passed_tests == total_tests:
        logger.info("[SUCCESS] Task 4: Real Strategy Integration - ALL TESTS PASSED")
        return True
    else:
        logger.error("[ERROR] Task 4: Real Strategy Integration - SOME TESTS FAILED")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
