#!/usr/bin/env python3
"""
QUALIA Pilot Credentials Setup
P-02.3: Deploy Piloto com Capital Limitado

Setup secure credentials for production pilot deployment
"""

import os
import json
import getpass
from pathlib import Path
from cryptography.fernet import Fernet
import base64
import hashlib

def generate_master_key():
    """Generate master encryption key"""
    return Fernet.generate_key()

def encrypt_data(data: str, key: bytes) -> str:
    """Encrypt data with master key"""
    f = Fernet(key)
    encrypted = f.encrypt(data.encode())
    return base64.b64encode(encrypted).decode()

def create_pilot_credentials():
    """Create encrypted pilot credentials"""
    print("🔐 QUALIA PILOT CREDENTIALS SETUP")
    print("=" * 50)
    print("⚠️  WARNING: This is for PRODUCTION PILOT with REAL MONEY")
    print("⚠️  Capital Limit: $1,000 USD")
    print("⚠️  Ultra-Conservative Risk Management Active")
    print("=" * 50)
    
    # Confirm production setup
    confirm = input("\n🚨 Are you sure you want to setup PRODUCTION credentials? (yes/no): ")
    if confirm.lower() != 'yes':
        print("❌ Setup cancelled for safety")
        return False
    
    # Get KuCoin Production API credentials
    print("\n📡 KuCoin Production API Credentials")
    print("⚠️  These must be PRODUCTION (not sandbox) credentials")
    print("⚠️  Ensure API key has SPOT TRADING permissions only")
    print("⚠️  Ensure API key has IP restrictions if possible")
    
    api_key = getpass.getpass("Enter KuCoin API Key: ")
    api_secret = getpass.getpass("Enter KuCoin API Secret: ")
    api_passphrase = getpass.getpass("Enter KuCoin API Passphrase: ")
    
    if not all([api_key, api_secret, api_passphrase]):
        print("❌ All credentials are required")
        return False
    
    # Validate credentials format
    if len(api_key) < 20 or len(api_secret) < 20:
        print("❌ Invalid credential format")
        return False
    
    # Create credentials structure
    credentials = {
        "environment": "pilot",
        "exchange": "kucoin",
        "api_url": "https://api.kucoin.com",
        "ws_url": "wss://ws-api.kucoin.com/endpoint",
        "credentials": {
            "api_key": api_key,
            "api_secret": api_secret,
            "api_passphrase": api_passphrase
        },
        "limits": {
            "max_capital_usd": 1000.0,
            "max_position_usd": 20.0,
            "max_daily_trades": 10
        },
        "created_at": "2025-07-07T14:00:00Z",
        "version": "P-02.3"
    }
    
    # Generate master key
    master_key = generate_master_key()
    
    # Encrypt credentials
    credentials_json = json.dumps(credentials, indent=2)
    encrypted_credentials = encrypt_data(credentials_json, master_key)
    
    # Save encrypted credentials
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    # Save master key
    master_key_file = config_dir / ".pilot_master.key"
    with open(master_key_file, 'wb') as f:
        f.write(master_key)
    
    # Save encrypted credentials
    credentials_file = config_dir / ".pilot_credentials"
    with open(credentials_file, 'w') as f:
        f.write(encrypted_credentials)
    
    # Set restrictive permissions
    os.chmod(master_key_file, 0o600)
    os.chmod(credentials_file, 0o600)
    
    print("\n✅ Pilot credentials created successfully!")
    print(f"📁 Master key: {master_key_file}")
    print(f"📁 Credentials: {credentials_file}")
    print("🔒 Files secured with 600 permissions")
    
    # Create environment file
    env_file = config_dir / ".pilot_env"
    env_content = f"""# QUALIA Pilot Environment Variables
# P-02.3: Deploy Piloto com Capital Limitado

QUALIA_ENV=pilot
QUALIA_CONFIG=config/pilot_config.yaml
QUALIA_CREDENTIALS=config/.pilot_credentials
QUALIA_MASTER_KEY=config/.pilot_master.key
QUALIA_LOG_LEVEL=INFO
QUALIA_CAPITAL_LIMIT=1000
QUALIA_RISK_LEVEL=ULTRA_CONSERVATIVE

# Trading Limits
QUALIA_MAX_POSITION_USD=20
QUALIA_MAX_DAILY_TRADES=10
QUALIA_EMERGENCY_STOP_USD=50

# Monitoring
QUALIA_MONITORING_LEVEL=INTENSIVE
QUALIA_ALERT_LEVEL=HIGH
"""
    
    with open(env_file, 'w') as f:
        f.write(env_content)
    
    os.chmod(env_file, 0o600)
    
    print(f"📁 Environment: {env_file}")
    
    return True

def validate_pilot_setup():
    """Validate pilot setup"""
    print("\n🔍 VALIDATING PILOT SETUP")
    print("=" * 30)
    
    config_dir = Path("config")
    required_files = [
        "pilot_config.yaml",
        ".pilot_credentials",
        ".pilot_master.key",
        ".pilot_env"
    ]
    
    missing_files = []
    for file_name in required_files:
        file_path = config_dir / file_name
        if not file_path.exists():
            missing_files.append(file_name)
        else:
            print(f"✅ {file_name}")
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    print("\n✅ All pilot files present")
    
    # Validate configuration
    try:
        import yaml
        with open(config_dir / "pilot_config.yaml", 'r') as f:
            config = yaml.safe_load(f)
        
        # Check critical settings
        checks = [
            ("environment", "pilot"),
            ("capital.total_capital_usd", 1000.0),
            ("capital.max_position_size_pct", 2.0),
            ("risk_management.max_drawdown_pct", 3.0),
            ("circuit_breakers.enabled", True),
            ("exchange.environment", "production")
        ]
        
        for key, expected in checks:
            keys = key.split('.')
            value = config
            for k in keys:
                value = value.get(k)
            
            if value == expected:
                print(f"✅ {key}: {value}")
            else:
                print(f"❌ {key}: {value} (expected {expected})")
                return False
        
        print("\n✅ Configuration validation passed")
        return True
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 QUALIA P-02.3: PILOT CREDENTIALS SETUP")
    print("=" * 50)
    
    # Create credentials
    if not create_pilot_credentials():
        print("❌ Credential setup failed")
        return
    
    # Validate setup
    if not validate_pilot_setup():
        print("❌ Setup validation failed")
        return
    
    print("\n🎉 PILOT SETUP COMPLETED SUCCESSFULLY!")
    print("=" * 50)
    print("📋 NEXT STEPS:")
    print("1. Load environment: source config/.pilot_env")
    print("2. Run pilot validation: python scripts/validate_pilot_setup.py")
    print("3. Start pilot trading: python scripts/start_pilot_trading.py")
    print("=" * 50)
    print("⚠️  REMEMBER: This is REAL MONEY trading with $1,000 limit")
    print("⚠️  Monitor closely and be ready to stop if needed")

if __name__ == '__main__':
    main()
