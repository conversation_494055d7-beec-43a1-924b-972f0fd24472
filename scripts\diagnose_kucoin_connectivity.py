#!/usr/bin/env python3
"""
Script para diagnosticar problemas de conectividade com a KuCoin
Executa testes reais de conectividade sem mocks
"""

import asyncio
import sys
import time
from datetime import datetime

# Importar ccxt diretamente
try:
    import ccxt.async_support as ccxt
    print("✅ ccxt importado com sucesso")
except ImportError:
    print("❌ Erro: ccxt não está instalado")
    sys.exit(1)


async def test_basic_connectivity():
    """Teste básico de conectividade"""
    print("\n🔍 TESTE 1: Conectividade Básica")
    print("=" * 50)
    
    try:
        # Criar exchange KuCoin real
        exchange = ccxt.kucoin({
            'sandbox': False,
            'timeout': 10000,  # 10 segundos
            'enableRateLimit': True,
        })
        
        print("✅ Exchange KuCoin criada")
        
        # Testar load_markets
        print("🔄 Carregando mercados...")
        start_time = time.time()
        markets = await exchange.load_markets()
        load_time = time.time() - start_time
        
        print(f"✅ Mercados carregados: {len(markets)} em {load_time:.2f}s")
        
        if "BTC/USDT" in markets:
            print("✅ BTC/USDT disponível")
        else:
            print("❌ BTC/USDT não encontrado")
        
        await exchange.close()
        return True
        
    except Exception as e:
        print(f"❌ Erro na conectividade básica: {e}")
        return False


async def test_ticker_performance():
    """Teste de performance do ticker"""
    print("\n🔍 TESTE 2: Performance do Ticker")
    print("=" * 50)
    
    timeouts = [5000, 10000, 15000, 30000]  # 5s, 10s, 15s, 30s
    
    for timeout_ms in timeouts:
        try:
            exchange = ccxt.kucoin({
                'sandbox': False,
                'timeout': timeout_ms,
                'enableRateLimit': True,
            })
            
            await exchange.load_markets()
            
            print(f"🔄 Testando ticker com timeout de {timeout_ms/1000}s...")
            start_time = time.time()
            
            ticker = await exchange.fetch_ticker("BTC/USDT")
            
            duration = time.time() - start_time
            
            if ticker and "last" in ticker:
                print(f"✅ Sucesso em {duration:.2f}s - Preço: ${ticker['last']:,.2f}")
                await exchange.close()
                return timeout_ms, duration
            else:
                print("❌ Ticker inválido")
                
            await exchange.close()
            
        except asyncio.TimeoutError:
            print(f"⏱️ TIMEOUT com {timeout_ms/1000}s")
            await exchange.close()
        except Exception as e:
            print(f"❌ Erro com timeout {timeout_ms/1000}s: {e}")
            await exchange.close()
    
    return None, None


async def test_ohlcv_performance():
    """Teste de performance do OHLCV"""
    print("\n🔍 TESTE 3: Performance do OHLCV")
    print("=" * 50)
    
    try:
        exchange = ccxt.kucoin({
            'sandbox': False,
            'timeout': 60000,  # 60 segundos para OHLCV
            'enableRateLimit': True,
        })
        
        await exchange.load_markets()
        
        # Testar diferentes limites
        limits = [1, 5, 10, 50]
        
        for limit in limits:
            try:
                print(f"🔄 Testando OHLCV com limite {limit}...")
                start_time = time.time()
                
                ohlcv = await exchange.fetch_ohlcv("BTC/USDT", "5m", limit=limit)
                
                duration = time.time() - start_time
                
                if ohlcv and len(ohlcv) > 0:
                    print(f"✅ {len(ohlcv)} candles em {duration:.2f}s")
                    if duration < 30:  # Se conseguiu em menos de 30s
                        await exchange.close()
                        return limit, duration
                else:
                    print("❌ OHLCV vazio")
                    
            except asyncio.TimeoutError:
                print(f"⏱️ TIMEOUT com limite {limit}")
            except Exception as e:
                print(f"❌ Erro com limite {limit}: {e}")
        
        await exchange.close()
        
    except Exception as e:
        print(f"❌ Erro geral no OHLCV: {e}")
    
    return None, None


async def test_multiple_requests():
    """Teste de múltiplas requisições"""
    print("\n🔍 TESTE 4: Múltiplas Requisições")
    print("=" * 50)
    
    try:
        exchange = ccxt.kucoin({
            'sandbox': False,
            'timeout': 15000,  # 15 segundos (igual ao sistema)
            'enableRateLimit': True,
        })
        
        await exchange.load_markets()
        
        results = {
            'success': 0,
            'timeout': 0,
            'error': 0,
            'times': []
        }
        
        print("🔄 Fazendo 10 requisições de ticker...")
        
        for i in range(10):
            try:
                start_time = time.time()
                ticker = await exchange.fetch_ticker("BTC/USDT")
                duration = time.time() - start_time
                
                if ticker and "last" in ticker:
                    results['success'] += 1
                    results['times'].append(duration)
                    print(f"   Req {i+1}: ✅ {duration:.2f}s")
                else:
                    results['error'] += 1
                    print(f"   Req {i+1}: ❌ Ticker inválido")
                    
            except asyncio.TimeoutError:
                results['timeout'] += 1
                print(f"   Req {i+1}: ⏱️ TIMEOUT")
            except Exception as e:
                results['error'] += 1
                print(f"   Req {i+1}: ❌ {str(e)[:50]}")
            
            # Pequena pausa entre requisições
            await asyncio.sleep(0.5)
        
        # Análise dos resultados
        print("\n📊 RESULTADOS:")
        print(f"   Sucessos: {results['success']}/10 ({results['success']*10}%)")
        print(f"   Timeouts: {results['timeout']}/10 ({results['timeout']*10}%)")
        print(f"   Erros: {results['error']}/10 ({results['error']*10}%)")
        
        if results['times']:
            avg_time = sum(results['times']) / len(results['times'])
            max_time = max(results['times'])
            min_time = min(results['times'])
            print(f"   Tempo médio: {avg_time:.2f}s")
            print(f"   Tempo máximo: {max_time:.2f}s")
            print(f"   Tempo mínimo: {min_time:.2f}s")
        
        await exchange.close()
        return results
        
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        return None


async def main():
    """Função principal de diagnóstico"""
    print("🚀 DIAGNÓSTICO DE CONECTIVIDADE KUCOIN")
    print("=" * 60)
    print(f"Iniciado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Teste 1: Conectividade básica
    basic_ok = await test_basic_connectivity()
    
    if not basic_ok:
        print("\n🚨 FALHA CRÍTICA: Conectividade básica falhou")
        print("   Possíveis causas:")
        print("   - Problema de internet")
        print("   - KuCoin API indisponível")
        print("   - Firewall/proxy bloqueando")
        return
    
    # Teste 2: Performance do ticker
    ticker_timeout, ticker_time = await test_ticker_performance()
    
    # Teste 3: Performance do OHLCV
    ohlcv_limit, ohlcv_time = await test_ohlcv_performance()
    
    # Teste 4: Múltiplas requisições
    multi_results = await test_multiple_requests()
    
    # Diagnóstico final
    print("\n🏁 DIAGNÓSTICO FINAL")
    print("=" * 60)
    
    if ticker_timeout is None:
        print("🚨 PROBLEMA CRÍTICO: Ticker sempre falha")
        print("   Recomendação: Aumentar timeout para 30-60s")
    elif ticker_time > 10:
        print(f"⚠️  PROBLEMA: Ticker lento ({ticker_time:.2f}s)")
        print("   Recomendação: Usar timeout de pelo menos 20s")
    else:
        print(f"✅ Ticker OK ({ticker_time:.2f}s)")
    
    if ohlcv_limit is None:
        print("🚨 PROBLEMA CRÍTICO: OHLCV sempre falha")
        print("   Recomendação: Usar limite menor (1-5 candles)")
    elif ohlcv_time > 30:
        print(f"⚠️  PROBLEMA: OHLCV lento ({ohlcv_time:.2f}s)")
        print("   Recomendação: Usar timeout de pelo menos 60s")
    else:
        print(f"✅ OHLCV OK ({ohlcv_time:.2f}s com {ohlcv_limit} candles)")
    
    if multi_results:
        success_rate = multi_results['success'] / 10 * 100
        if success_rate < 50:
            print(f"🚨 PROBLEMA CRÍTICO: Taxa de sucesso baixa ({success_rate}%)")
        elif success_rate < 80:
            print(f"⚠️  PROBLEMA: Taxa de sucesso moderada ({success_rate}%)")
        else:
            print(f"✅ Taxa de sucesso boa ({success_rate}%)")
    
    print(f"\nConcluído em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    asyncio.run(main()) 
