import numpy as np
from qualia.farsight.analyzer import <PERSON><PERSON><PERSON>
from qualia.farsight.embedder import Embedder


def test_analyzer_caches_embeddings(monkeypatch):
    calls = {"count": 0}

    def fake_encode(self, documents):
        calls["count"] += 1
        return np.ones((1, 3))

    monkeypatch.setattr(Embedder, "encode", fake_encode)
    analyzer = Analyzer(embedder=Embedder())
    text = "some text"
    vec1 = analyzer.embed_text(text)
    vec2 = analyzer.embed_text(text)
    assert calls["count"] == 1
    assert np.array_equal(vec1, vec2)


def test_analyzer_evicts_old_embeddings(monkeypatch):
    calls = []

    def fake_encode(self, documents):
        calls.append(documents[0])
        return np.ones((1, 3))

    monkeypatch.setattr(Embedder, "encode", fake_encode)
    analyzer = Analyzer(embedder=Embedder(), cache_size=2)
    analyzer.embed_text("a")
    analyzer.embed_text("b")
    analyzer.embed_text("a")
    analyzer.embed_text("c")
    analyzer.embed_text("b")  # should trigger recomputation for 'b'
    assert calls.count("b") == 2
