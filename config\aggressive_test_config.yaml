# QUALIA AGGRESSIVE TEST Configuration
# ATENÇÃO: Use apenas para testes. Parâmetros não são seguros para trading real.

holographic_universe:
  # Thresholds rebaixados para facilitar detecção de padrões
  pattern_detection:
    significance_threshold: 0.2
    confidence_threshold: 0.3
    min_history_length: 10
    analysis_window: 20
  
  # Configurações de sinais de trading (mais permissivas para teste)
  trading_signals:
    min_strength: 0.1
    proximity_threshold: 50
    confidence_threshold: 0.2

# Configuração da estratégia com thresholds mais baixos
strategy_config:
  name: "NovaEstrategiaQUALIA"
  params:
    s1_strength_threshold: 0.12  # YAA: Ajuste fino de 0.15 para 0.12, baseado em logs
    s3_strength_threshold: 0.10 # Mantido em 0.10
    meta_decision_threshold: 0.025 
    coherence_threshold: 0.0 # YAA: Revertido para 0.0 para respeitar validação
    entropy_threshold: 1.0 # Mantido MAX para permitir sinais

# Risk Management mais permissivo para testes
risk_management:
  risk_per_trade: 0.05 # Risco de 5% por trade (ALTO)
  max_drawdown: 0.25 # Drawdown de 25% (ALTO)
  max_volatility: 15.0  # Permite volatilidade até 15% (muito alto)
  safety:
    max_open_positions: 10
    max_daily_loss_pct: 0.10 # Perda de 10% (ALTO)
    max_drawdown_pct: 0.25

# ---------------------------------------------------------------------------
# QPM configuration for aggressive testing
# ---------------------------------------------------------------------------
qpm_config:
  risk_manager:
    alias: "advanced"
    initial_capital: 10000.0
    risk_profile: "aggressive"

    # Pesos para unificação da decisão final
unification_weights:
  strategy: 0.2      # REDUZIDO de 0.3 para 0.2 - Menor peso da estratégia
  holographic: 0.6   # AUMENTADO de 0.5 para 0.6 - Maior peso holográfico
  metacognition: 0.2 # Mantido - A metacognição mantém o peso
  decision_threshold: 0.01 # YAA FIX-DATA-PIPELINE: Reduzido drasticamente para 0.01

# Critérios de aprovação de sinais para teste agressivo - TASK-003: Thresholds adaptativos
signal_approval:
  min_confidence: 0.01        # YAA FIX-DATA-PIPELINE: Reduzido drasticamente para 0.01
  min_volume_ratio: 0.05     # YAA FIX-DATA-PIPELINE: Volume mínimo muito baixo (5% da média)
  max_volatility: 25.0       # YAA FIX-DATA-PIPELINE: Volatilidade máxima aumentada para 25%
  enable_aggressive_mode: true # Flag para identificar modo agressivo
  enable_adaptive_threshold: true  # NOVO: Habilita thresholds adaptativos
  volatility_confidence_factor: 0.05 # NOVO: Fator de ajuste de confiança pela volatilidade
  volatility_history_window: 100   # NOVO: Janela para cálculo da volatilidade histórica
  threshold_decay_factor: 0.95
  min_trades_for_adjustment: 5     # NOVO: Mínimo de trades para iniciar o ajuste
  performance_sensitivity_factor: 0.25 # NOVO: Influência do win rate no ajuste

# Configurações específicas do holographic trading adapter
holographic_trading:
  min_confidence: 0.05 # YAA FIX-DATA-PIPELINE: Reduzido para 0.05 para permitir mais sinais
  max_concurrent_positions: 10
  enable_holographic_risk_override: false  # Desabilita override de risco
  volume_threshold: 0.05     # YAA FIX-DATA-PIPELINE: Volume mínimo reduzido para 5%
  aggressive_mode: true # Mantém o modo agressivo para teste

# Adaptive Coherence Engine (ACE) - Configuração para controle dinâmico
ace_config:
  enable_dynamic_risk_control: true  # Habilita ou desabilita o controle de risco dinâmico globalmente
  dynamic_risk_config:
    base_risk_per_trade: 0.02  # Risco base aumentado para testes
    volatility_adjustment_factor: 0.3 # Reduzido para ser menos conservador
    max_risk_multiplier: 3.0  # Multiplicador máximo aumentado
    min_risk_multiplier: 0.3  # Multiplicador mínimo reduzido
    volatility_lookback_period: 10 # Período reduzido para ser mais reativo
    risk_profile:
      conservative:
        volatility_adjustment_factor: 0.2
        max_risk_multiplier: 2.0
      balanced:
        volatility_adjustment_factor: 0.3
        max_risk_multiplier: 3.0
      aggressive:
        volatility_adjustment_factor: 0.5
        max_risk_multiplier: 5.0

# TASK-005: Configuração de Exchanges - Resolve "Market data is None"
exchanges:
  default: "kucoin"
  timeout: 30.0
  kucoin:
    api_key: "${KUCOIN_API_KEY}"
    api_secret: "${KUCOIN_SECRET_KEY}"
    api_passphrase: "${KUCOIN_PASSPHRASE}"
    sandbox: false
    timeout: 20.0  # Reduzido para consistência
    rate_limit: 4.0  # Atualizado para o mínimo recomendado
    retry_attempts: 3
    retry_delay: 1.0

# TASK-005: Configuração de Trading - Define pares e moeda base
trading:
  base_currency: "USDT"
  quote_currencies: ["BTC", "ETH", "ADA", "SOL"]
  market_data_timeout: 30.0
  enable_live_data: true
  data_source_priority: ["kucoin", "fallback"]

# Configurações para a evolução adaptativa da consciência (ACE)
adaptive_evolution:
  adaptation_aggressiveness: 0.95 # Suaviza a adaptação, valor anterior: 1.1
  # ...outros parâmetros agressivos podem ser mantidos
