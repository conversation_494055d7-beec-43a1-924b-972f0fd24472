# FWH Scalp Calibrator - Sistema de Calibração com Dados Reais

## 🎯 Visão Geral

O **FWH Scalp Calibrator** é um sistema avançado de calibração automática para a estratégia FWH Scalp que utiliza **dados reais de mercado** da Binance para otimizar parâmetros e maximizar a performance em cenários reais de trading.

## 🚀 Características Principais

- ✅ **Dados Reais**: Integração direta com a API da Binance
- ✅ **Validação de Qualidade**: Verificação contínua da qualidade dos dados
- ✅ **Métricas Avançadas**: Latência, spread, liquidez e condições de mercado
- ✅ **Otimização Inteligente**: Grid search e algoritmos adaptativos
- ✅ **Relatórios Detalhados**: Análise completa dos resultados
- ✅ **Backup Automático**: Preservação das configurações originais

## 📋 Pré-requisitos

### Dependências Python
```bash
pip install aiohttp websockets python-binance numpy pyyaml asyncio
```

### Credenciais da Binance
1. Crie uma conta na [Binance](https://www.binance.com)
2. Gere uma API Key e Secret no painel de API
3. **IMPORTANTE**: Use apenas permissões de leitura (não é necessário trading)

## 🔧 Configuração

### 1. Arquivo de Configuração Base
Certifique-se de ter um arquivo de configuração base em:
```
config/fwh_scalp_config_optimized.yaml
```

### 2. Estrutura de Diretórios
```
QualiaExplorer/
├── scripts/
│   ├── fwh_scalp_calibrator.py
│   └── README_FWH_CALIBRATOR.md
├── config/
│   └── fwh_scalp_config_optimized.yaml
└── calibration_results/
    └── (resultados serão salvos aqui)
```

## 🎮 Como Usar

### 1. Modos de Calibração

#### ⚡ Modo RÁPIDO (Recomendado)
```bash
# Calibração rápida - 27 combinações (5-15 min)
python fwh_scalp_calibrator.py --mode fast --demo-mode
```

#### ⚖️ Modo BALANCEADO
```bash
# Calibração balanceada - 243 combinações (30-60 min)
python fwh_scalp_calibrator.py --mode balanced --demo-mode
```

#### 🔬 Modo COMPLETO
```bash
# Calibração completa - 3125 combinações (3-8 horas)
python fwh_scalp_calibrator.py --mode full --demo-mode
```

### 2. Com Dados Reais da Binance
```bash
# Calibração rápida com dados reais
python fwh_scalp_calibrator.py \
    --mode fast \
    --api-key YOUR_BINANCE_API_KEY \
    --api-secret YOUR_BINANCE_API_SECRET \
    --test-duration 30
```

### 3. Modo Demonstração (sem dados reais)

```bash
# Modo demonstração para testes
python fwh_scalp_calibrator.py --demo-mode
```

### Modo 3: Uso Programático

```python
import asyncio
from fwh_scalp_calibrator import FWHScalpCalibrator

async def calibrar_estrategia():
    # Configuração
    calibrator = FWHScalpCalibrator(
        config_path="config/fwh_scalp_config_optimized.yaml",
        output_dir="meus_resultados"
    )
    
    # Executar calibração
    result = await calibrator.calibrate(
        strategy='grid_search',
        max_combinations=100,
        test_duration_minutes=10,
        api_key="sua_api_key",
        api_secret="seu_api_secret"
    )
    
    if result:
        print(f"✅ Score: {result.score:.4f}")
        print(f"📈 Win Rate: {result.metrics['win_rate']:.1f}%")
        print(f"💰 Profit Factor: {result.metrics['profit_factor']:.2f}")

# Executar
asyncio.run(calibrar_estrategia())
```

## ⚙️ Parâmetros de Linha de Comando

| Parâmetro | Descrição | Padrão |
|-----------|-----------|--------|
| `--config` | Caminho da configuração base | `config/fwh_scalp_config_optimized.yaml` |
| `--output` | Diretório de saída | `calibration_results` |
| `--mode` | Modo de calibração | `fast` |
| `--strategy` | Estratégia de calibração | `grid_search` |
| `--max-combinations` | Máximo de combinações | `1000` |
| `--test-duration` | Duração de cada teste (min) | `15` |
| `--parallel` | Testes paralelos | `1` |
| `--api-key` | Binance API Key | - |
| `--api-secret` | Binance API Secret | - |
| `--demo-mode` | Modo demonstração | `false` |

## ⚡ Modos de Calibração

| Modo | Combinações | Tempo Estimado | Uso Recomendado |
|------|-------------|----------------|------------------|
| `fast` | ~27 | 5-15 min | ✅ Testes rápidos, desenvolvimento |
| `balanced` | ~243 | 30-60 min | ⚖️ Calibração normal, produção |
| `full` | ~3125 | 3-8 horas | 🔬 Calibração completa, pesquisa |

## 📊 Métricas de Avaliação

O sistema avalia as configurações usando um score ponderado:

- **Win Rate** (25%): Taxa de acerto dos sinais
- **Profit Factor** (30%): Relação lucro/prejuízo
- **Sinais/Hora** (20%): Frequência de geração de sinais
- **Max Drawdown** (15%): Máximo rebaixamento (invertido)
- **Sharpe Ratio** (10%): Relação risco/retorno

### Filtros Mínimos
- Win Rate > 45%
- Profit Factor > 1.2
- Sinais > 2/hora

## 📈 Parâmetros Otimizados

O sistema otimiza os seguintes parâmetros críticos:

| Parâmetro | Range | Descrição |
|-----------|-------|----------|
| `hype_threshold` | 0.15 - 0.45 | Limiar de detecção de hype |
| `wave_min_strength` | 0.20 - 0.40 | Força mínima da onda |
| `quantum_boost_factor` | 0.95 - 1.25 | Fator de boost quântico |
| `holographic_weight` | 0.30 - 0.80 | Peso holográfico |
| `otoc_max_threshold` | 0.25 - 0.65 | Limiar máximo OTOC |
| `stop_loss_pct` | 0.4 - 0.8 | Percentual de stop loss |
| `take_profit_pct` | 0.8 - 1.5 | Percentual de take profit |

## 📁 Arquivos de Saída

Após a calibração, os seguintes arquivos são gerados:

```
calibration_results/
├── calibration_results_YYYYMMDD_HHMMSS.json
├── calibration_analysis_YYYYMMDD_HHMMSS.md
├── fwh_scalp_config_optimized_YYYYMMDD_HHMMSS.yaml
├── real_data_metrics_report.json
└── config_backup_YYYYMMDD_HHMMSS.yaml
```

### Descrição dos Arquivos

- **calibration_results_*.json**: Resultados completos da calibração
- **calibration_analysis_*.md**: Relatório de análise em Markdown
- **fwh_scalp_config_optimized_*.yaml**: Configuração otimizada final
- **real_data_metrics_report.json**: Métricas específicas dos dados reais
- **config_backup_*.yaml**: Backup da configuração original

## 🔍 Validação de Dados Reais

O sistema implementa validação rigorosa dos dados:

### Verificações de Conexão
- ✅ Conectividade com API da Binance
- ✅ Latência da conexão (< 500ms)
- ✅ Disponibilidade dos símbolos

### Validação de Qualidade
- ✅ Atualizações de preço em tempo real
- ✅ Dados de volume consistentes
- ✅ Spreads dentro dos limites aceitáveis
- ✅ Liquidez adequada no orderbook

### Score de Qualidade
O sistema calcula um score de qualidade (0-1) baseado em:
- Frequência de atualizações
- Consistência dos dados
- Latência da conexão
- Qualidade do spread

## ⚠️ Considerações Importantes

### Segurança
- **NUNCA** compartilhe suas credenciais da Binance
- Use apenas permissões de leitura na API
- Mantenha suas chaves em variáveis de ambiente

### Performance
- Cada teste consome dados da API
- Respeite os limites de rate da Binance
- Use `--parallel 1` para evitar sobrecarga

### Horários de Mercado
- O sistema funciona 24/7 (crypto)
- Melhor performance durante horários de alta liquidez
- Evite períodos de manutenção da Binance

## 🐛 Solução de Problemas

### Erro de Conexão
```
❌ Erro: Falha na conexão com dados reais
```
**Solução**: Verifique suas credenciais e conexão com internet

### Dados de Baixa Qualidade
```
⚠️ Qualidade dos dados abaixo do mínimo
```
**Solução**: Aguarde melhor condição de mercado ou ajuste `min_data_quality_score`

### Rate Limit Excedido
```
❌ Rate limit da API excedido
```
**Solução**: Reduza `--parallel` ou aumente `--test-duration`

## 📞 Suporte

Para suporte técnico:
1. Verifique os logs de erro
2. Consulte a documentação da API Binance
3. Teste primeiro em modo demonstração

## 🔄 Atualizações

O sistema é atualizado regularmente com:
- Novos algoritmos de otimização
- Melhorias na validação de dados
- Suporte a novos exchanges
- Otimizações de performance

---

**🧠 QUALIA Quantum Consciousness**  
**⚡ Sistema de Calibração Automática FWH Scalp**  
**📡 Powered by Real Market Data**