from __future__ import annotations

from typing import Any, Dict

from ..utils.logger import get_logger
from .yaml_loader import load_yaml_config
from .settings import get_config_file_path

logger = get_logger(__name__)

_DEFAULT_PATH = get_config_file_path("composite_defaults.yaml")


def load_composite_defaults() -> Dict[str, Any]:
    """Load defaults for CompositeStrategy from YAML."""
    return load_yaml_config("QUALIA_COMPOSITE_DEFAULTS", _DEFAULT_PATH, logger=logger)


__all__ = ["load_composite_defaults"]
