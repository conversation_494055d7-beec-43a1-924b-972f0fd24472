import pandas as pd
import pytest
from types import SimpleNamespace
from qualia.qualia_trading_system import QUALIARealTimeTrader


@pytest.mark.asyncio
async def test_init_skips_fallback_when_history_sufficient(monkeypatch):
    monkeypatch.setenv(
        "QUALIA_SECRET_KEY",
        "MDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDA=",
    )
    monkeypatch.setenv("KRAKEN_API_KEY", "dummy")
    monkeypatch.setenv("KRAKEN_SECRET_KEY", "dummy")

    def load_params(self):
        self.loaded_strategy_configs = {
            "strategy_config": {"max_cache_age_minutes": 60},
            "risk_profile_settings": {"moderate": {}},
        }
        self.risk_profile_configs = {"moderate": {}}

    async def init_stub(self):
        df = pd.DataFrame(
            {
                "timestamp": [1, 2, 3, 4, 5, 6],
                "open": [1, 2, 3, 4, 5, 6],
                "high": [1, 2, 3, 4, 5, 6],
                "low": [1, 2, 3, 4, 5, 6],
                "close": [1, 2, 3, 4, 5, 6],
                "volume": [10, 20, 30, 40, 50, 60],
            }
        )
        self.market_data = {"BTC/USDT": {"1h": df}}
        strategy = SimpleNamespace(
            required_initial_data_length=5,
            s3_resample_period="4h",
            timeframe="1h",
            shared_context={"auto_download_history": True},
        )
        self.strategies = {"BTC/USDT": strategy}
        await self._validate_initial_history()

    monkeypatch.setattr(QUALIARealTimeTrader, "_init_system_components", init_stub)
    monkeypatch.setattr(QUALIARealTimeTrader, "_load_strategy_parameters", load_params)
    monkeypatch.setattr(QUALIARealTimeTrader, "_load_configs", lambda self: None)
    monkeypatch.setattr(QUALIARealTimeTrader, "_load_open_positions", lambda self: None)
    monkeypatch.setattr(
        QUALIARealTimeTrader,
        "_load_risk_profile_settings",
        lambda self: setattr(self, "risk_profile_configs", {"moderate": {}}),
    )
    async def dummy_fee(self):
        return None

    monkeypatch.setattr(QUALIARealTimeTrader, "_fetch_dynamic_fee", dummy_fee)

    trader = QUALIARealTimeTrader(
        symbols=["BTC/USDT"],
        timeframes=["1h"],
        capital=1000.0,
        risk_profile="moderate",
    )

    await trader.initialize()

    assert isinstance(trader, QUALIARealTimeTrader)
