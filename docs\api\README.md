# Referência Geral de APIs

Este diretório reúne documentação das principais APIs públicas do QUALIA. Cada arquivo detalha um componente específico do sistema.

- [consciousness_config.md](consciousness_config.md) – configurações do módulo de consciência.
- [encoder_timeframe_alignment.md](encoder_timeframe_alignment.md) – sincronia de encoders e timeframes.
- [encoding_features.md](encoding_features.md) – esquema de extração de features.
- [exchange_clients.md](exchange_clients.md) – camada de clientes para exchanges suportadas.
- [health_check_system.md](health_check_system.md) – verificação de saúde do ecossistema.
- [json_persistence.md](json_persistence.md) – persistência de configurações e logs em JSON.
- [kraken_integration.md](kraken_integration.md) – integração com a exchange Kraken.
- [kraken_ticker_strategy.md](kraken_ticker_strategy.md) – estratégia simplificada baseada em tickers.
- [kucoin_integration.md](kucoin_integration.md) – integração com a exchange Kucoin.
- [memory.md](memory.md) – acesso à HolographicMemory e sistemas de cache.
- [metacognition.md](metacognition.md) – camada de metacognição quântica.
- [metrics.md](metrics.md) – métricas de evolução e indicadores de desempenho.
- [news_processing_system.md](news_processing_system.md) – ingestão de notícias e filtros de relevância.
- [qast_core.md](qast_core.md) – núcleo do sistema de evolução QAST.
- [qast_evolution_metrics.md](qast_evolution_metrics.md) – métricas geradas pelo QAST.
- [quantum_signatures.md](quantum_signatures.md) – geração de assinaturas quânticas.
- [circuit_optimization.md](circuit_optimization.md) – otimização de circuitos quânticos.
- [risk_metacognition.md](risk_metacognition.md) – engines de metacognição de risco.
- [self_evolving_config.md](self_evolving_config.md) – parâmetros de autoevolução.
- [self_evolving_metrics.md](self_evolving_metrics.md) – métricas de autoevolução do sistema.
- [signal_generation_flow.md](signal_generation_flow.md) – fluxo de geração de sinais.
- [sim_graph.md](sim_graph.md) – grafo de simulação de estratégias.
- [simulation_core.md](simulation_core.md) – núcleo de simulação fora do tempo real.
- [state_management.md](state_management.md) – gerenciamento de estados.
- [system_manager.md](system_manager.md) – orquestração de inicialização e encerramento.
- [temporal_detector.md](temporal_detector.md) – detectores de padrões temporais.
- [temporal_pattern_config.md](temporal_pattern_config.md) – formatação de padrões de tempo.
- [trading_engine.md](trading_engine.md) – motor principal do sistema de trading.
- [trading_loop_metrics.md](trading_loop_metrics.md) – métricas emitidas durante o loop de trading.
