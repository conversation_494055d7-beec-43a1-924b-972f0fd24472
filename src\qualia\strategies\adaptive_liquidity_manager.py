"""Adaptive Liquidity Manager for dynamic order sizing.

This manager tracks order book depth, slippage and traded volume to
scale order quantities in real time.
"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Any, Dict, Optional

import numpy as np
import pandas as pd

from ..memory.event_bus import SimpleEventBus
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class LiquidityParameters:
    """Configuration for :class:`AdaptiveLiquidityManager`."""

    base_order_pct: float = 0.1
    depth_threshold: float = 1.0
    slippage_threshold: float = 0.5
    volume_threshold: float = 1000.0


class AdaptiveLiquidityManager:
    """Manage order sizing using market depth and slippage.

    The manager listens to ``market.data.updated`` and ``risk.update`` events
    when an :class:`~qualia.memory.event_bus.SimpleEventBus` is provided. Order
    sizes are scaled based on the latest depth and slippage information.
    """

    def __init__(
        self,
        *,
        params: Optional[LiquidityParameters] = None,
        event_bus: Optional[SimpleEventBus] = None,
    ) -> None:
        self.params = params or LiquidityParameters()
        self.event_bus = event_bus
        self.last_depth: float | None = None
        self.last_slippage: float | None = None
        self.last_volume: float | None = None
        if self.event_bus is not None:
            self._subscribe_events()
        logger.info("AdaptiveLiquidityManager initialized")

    def _subscribe_events(self) -> None:
        assert self.event_bus is not None
        try:
            from ..event_bus import MarketDataUpdated
        except Exception:  # pragma: no cover - defensive for optional deps
            MarketDataUpdated = object  # type: ignore
        self.event_bus.subscribe(MarketDataUpdated, self._on_market_event)
        self.event_bus.subscribe("market.data.updated", self._on_market_event)
        self.event_bus.subscribe("risk.update", self._on_risk_event)

    def _on_market_event(self, payload: Any) -> None:
        """Update internal depth and slippage using market data."""

        depth_arr: np.ndarray | None = None
        slip_arr: np.ndarray | None = None
        vol_arr: np.ndarray | None = None

        if hasattr(payload, "market_data") and payload.market_data:
            data = payload.market_data
            if isinstance(data, pd.DataFrame):
                depth_arr = data.get("depth").to_numpy(dtype=float)
                slip_arr = data.get("slippage").to_numpy(dtype=float)
                if "volume" in data:
                    vol_arr = data.get("volume").to_numpy(dtype=float)
            else:
                depth_arr = np.array(
                    [
                        (
                            float(item.get("depth"))
                            if isinstance(item, dict)
                            else float(getattr(item, "depth", np.nan))
                        )
                        for item in data
                    ],
                    dtype=float,
                )
                slip_arr = np.array(
                    [
                        (
                            float(item.get("slippage"))
                            if isinstance(item, dict)
                            else float(getattr(item, "slippage", np.nan))
                        )
                        for item in data
                    ],
                    dtype=float,
                )
                vol_arr = np.array(
                    [
                        (
                            float(item.get("volume"))
                            if isinstance(item, dict)
                            else float(getattr(item, "volume", np.nan))
                        )
                        for item in data
                    ],
                    dtype=float,
                )
        elif isinstance(payload, dict):
            if payload.get("depth") is not None:
                depth_arr = np.array([float(payload["depth"])], dtype=float)
            if payload.get("slippage") is not None:
                slip_arr = np.array([float(payload["slippage"])], dtype=float)
            if payload.get("volume") is not None:
                vol_arr = np.array([float(payload["volume"])], dtype=float)

        if depth_arr is not None:
            depth_arr = depth_arr[~np.isnan(depth_arr)]
            if depth_arr.size:
                self.last_depth = float(np.mean(depth_arr))

        if slip_arr is not None:
            slip_arr = slip_arr[~np.isnan(slip_arr)]
            if slip_arr.size:
                self.last_slippage = float(np.mean(slip_arr))

        if vol_arr is not None:
            vol_arr = vol_arr[~np.isnan(vol_arr)]
            if vol_arr.size:
                self.last_volume = float(np.mean(vol_arr))

        logger.debug(
            "Market update received: depth=%s slippage=%s volume=%s",
            self.last_depth,
            self.last_slippage,
            self.last_volume,
        )

    def _on_risk_event(self, payload: Any) -> None:
        """Proxy to :meth:`apply_risk_update` for event bus integration."""

        if hasattr(payload, "params"):
            self.apply_risk_update(payload.params)
        else:
            self.apply_risk_update(payload)

    def apply_risk_update(self, payload: Dict[str, Any]) -> None:
        """Adjust parameters in response to risk manager updates."""

        # ``risk.update`` events may provide a new ``order_pct`` or a full
        # ``liquidity_params`` mapping. This public method allows external
        # components to reconfigure the manager on the fly.

        new_pct = payload.get("order_pct")
        if new_pct is not None:
            logger.info(
                "Adjusting base_order_pct from %s to %s",
                self.params.base_order_pct,
                new_pct,
            )
            self.params.base_order_pct = float(new_pct)

        params_data = payload.get("liquidity_params")
        if isinstance(params_data, dict):
            self.set_params(LiquidityParameters(**params_data))

    def set_params(self, params: LiquidityParameters) -> None:
        """Replace :class:`LiquidityParameters` at runtime."""

        logger.info("Updating liquidity parameters: %s", params)
        self.params = params

    def calculate_order_size(
        self,
        capital: float,
        current_price: float,
        depth: Optional[float] = None,
        slippage_pct: Optional[float] = None,
        volume: Optional[float] = None,
    ) -> float:
        """Return quantity sized according to depth, slippage and volume."""

        depth = depth if depth is not None else self.last_depth or 1.0
        slippage_pct = (
            slippage_pct if slippage_pct is not None else self.last_slippage or 0.0
        )
        volume = (
            volume
            if volume is not None
            else self.last_volume or self.params.volume_threshold
        )

        pct = self.params.base_order_pct
        depth_factor = min(1.0, depth / self.params.depth_threshold)
        slip_factor = max(0.0, 1 - slippage_pct / self.params.slippage_threshold)
        vol_factor = min(1.0, volume / self.params.volume_threshold)
        pct *= depth_factor * slip_factor * vol_factor
        position_size = capital * pct
        quantity = position_size / current_price if current_price > 0 else 0.0
        logger.debug(
            "Order size calculated: capital=%.2f price=%.2f depth=%.2f slippage=%.3f volume=%.2f qty=%.6f",
            capital,
            current_price,
            depth,
            slippage_pct,
            volume,
            quantity,
        )
        return quantity

    def backtest(
        self, market_data: pd.DataFrame, initial_capital: float = 10000.0
    ) -> Dict[str, float]:
        """Run a simple backtest using depth and slippage columns.

        This implementation uses vectorized NumPy operations for speed.
        """
        prices = market_data["price"].to_numpy(dtype=float)
        if len(prices) < 2:
            return {"final_capital": float(initial_capital)}

        depth = market_data.get("depth", 1.0)
        depth_arr = np.asarray(depth, dtype=float)
        slip = market_data.get("slippage", 0.0)
        slip_arr = np.asarray(slip, dtype=float)
        vol = market_data.get("volume", self.params.volume_threshold)
        vol_arr = np.asarray(vol, dtype=float)

        depth_factor = np.minimum(1.0, depth_arr[:-1] / self.params.depth_threshold)
        slip_factor = np.maximum(
            0.0, 1.0 - slip_arr[:-1] / self.params.slippage_threshold
        )
        vol_factor = np.minimum(1.0, vol_arr[:-1] / self.params.volume_threshold)
        pct = self.params.base_order_pct * depth_factor * slip_factor * vol_factor
        returns = pct * (prices[1:] - prices[:-1]) / prices[:-1]
        final_capital = float(initial_capital * np.prod(1.0 + returns))
        return {"final_capital": final_capital}

    def _backtest_loop(
        self, market_data: pd.DataFrame, initial_capital: float
    ) -> float:
        """Naive backtest loop kept for benchmarking."""
        capital = initial_capital
        for i in range(1, len(market_data)):
            prev = market_data.iloc[i - 1]
            curr = market_data.iloc[i]
            qty = self.calculate_order_size(
                capital,
                prev["price"],
                prev.get("depth", 1.0),
                prev.get("slippage", 0.0),
                prev.get("volume", self.params.volume_threshold),
            )
            capital += qty * (curr["price"] - prev["price"])
        return capital
