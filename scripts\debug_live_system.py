#!/usr/bin/env python3
"""
Script para diagnosticar problemas em tempo real no sistema QUALIA
"""

import asyncio
import sys
import os
from datetime import datetime
import traceback

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


async def debug_live_system():
    """Diagnostica o sistema exatamente como está sendo usado em produção"""
    
    print("🚨 DIAGNÓSTICO DO SISTEMA EM PRODUÇÃO")
    print("=" * 70)
    print(f"Iniciado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Importar exatamente como o sistema faz
        from qualia.qualia_trading_system import QUALIATradingSystem
        print("✅ QUALIATradingSystem importado")
        
        # Verificar se está usando mocks
        import ccxt.async_support as ccxt
        kucoin_class = getattr(ccxt, 'kucoin', None)
        print(f"📋 Classe KuCoin em uso: {kucoin_class}")
        print(f"📋 Módulo: {kucoin_class.__module__}")
        
        if 'Dummy' in str(kucoin_class):
            print("🚨 PROBLEMA: Sistema ainda está usando DummyExchange!")
            return False
        else:
            print("✅ Sistema usando classe real")
        
        # Tentar criar o sistema de trading
        print("\n🔄 Criando sistema de trading...")
        
        # Usar configuração mínima como em produção
        trading_system = QUALIATradingSystem(
            exchange_id="kucoin",
            symbol="BTC/USDT",
            timeframe="5m",
            strategy_name="QualiaTSVF",
            initial_capital=1000.0,
            risk_per_trade=0.01,
            mode="paper_trading"
        )
        
        print("✅ Sistema de trading criado")
        
        # Verificar a integração específica
        integration = trading_system.integration
        print(f"📋 Integração: {type(integration)}")
        print(f"📋 Exchange da integração: {type(integration.exchange)}")
        
        # Testar conexão como o sistema faz
        print("\n🔄 Testando inicialização como em produção...")
        start_time = datetime.now()
        
        try:
            await integration.initialize_connection()
            init_duration = (datetime.now() - start_time).total_seconds()
            print(f"✅ Inicialização OK em {init_duration:.2f}s")
        except Exception as e:
            print(f"❌ Falha na inicialização: {e}")
            traceback.print_exc()
            return False
        
        # Testar ticker exatamente como o sistema faz
        print("\n🔄 Testando fetch_ticker como em produção...")
        
        for attempt in range(3):
            try:
                start_time = datetime.now()
                ticker = await integration.fetch_ticker("BTC/USDT")
                duration = (datetime.now() - start_time).total_seconds()
                
                if ticker is not None:
                    print(f"✅ Tentativa {attempt+1}: Ticker OK em {duration:.2f}s")
                    print(f"   Preço: ${ticker.get('last', 'N/A')}")
                    break
                else:
                    print(f"❌ Tentativa {attempt+1}: Ticker retornou None em {duration:.2f}s")
                    
            except asyncio.TimeoutError:
                duration = (datetime.now() - start_time).total_seconds()
                print(f"⏱️ Tentativa {attempt+1}: TIMEOUT após {duration:.2f}s")
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                print(f"❌ Tentativa {attempt+1}: Erro após {duration:.2f}s - {e}")
            
            await asyncio.sleep(2)  # Pausa entre tentativas
        
        # Testar OHLCV
        print("\n🔄 Testando fetch_ohlcv como em produção...")
        
        try:
            start_time = datetime.now()
            df = await integration.fetch_ohlcv("BTC/USDT", "5m", limit=10)
            duration = (datetime.now() - start_time).total_seconds()
            
            if df is not None and not df.empty:
                print(f"✅ OHLCV OK em {duration:.2f}s - {len(df)} candles")
            else:
                print(f"❌ OHLCV vazio em {duration:.2f}s")
                
        except asyncio.TimeoutError:
            duration = (datetime.now() - start_time).total_seconds()
            print(f"⏱️ OHLCV TIMEOUT após {duration:.2f}s")
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            print(f"❌ OHLCV erro após {duration:.2f}s - {e}")
        
        # Verificar configurações
        print("\n📊 CONFIGURAÇÕES ATUAIS:")
        print(f"   Ticker timeout: {getattr(integration, 'ticker_timeout', 'N/A')}s")
        print(f"   OHLCV timeout: {getattr(integration, 'ohlcv_timeout', 'N/A')}s")
        print(f"   Rate limit: {getattr(integration, 'rate_limit', 'N/A')}s")
        print(f"   Exchange ID: {getattr(integration, 'exchange_id', 'N/A')}")
        
        # Verificar circuit breakers
        if hasattr(integration, 'ticker_circuit'):
            circuit = integration.ticker_circuit
            print(f"   Circuit breaker ticker: {circuit.is_open()}")
            print(f"   Falhas ticker: {circuit.failure_count}")
        
        await integration.close()
        print("✅ Conexão fechada")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro geral no diagnóstico: {e}")
        traceback.print_exc()
        return False


async def test_direct_comparison():
    """Compara o comportamento direto vs sistema QUALIA"""
    
    print("\n🔍 COMPARAÇÃO: CCXT DIRETO vs QUALIA")
    print("=" * 70)
    
    # Teste 1: CCXT direto
    print("1️⃣ TESTANDO CCXT DIRETO...")
    try:
        import ccxt.async_support as ccxt
        
        exchange = ccxt.kucoin({
            'sandbox': False,
            'timeout': 15000,  # Mesmo timeout que QUALIA
            'enableRateLimit': True,
        })
        
        await exchange.load_markets()
        
        start_time = datetime.now()
        ticker = await exchange.fetch_ticker("BTC/USDT")
        duration = (datetime.now() - start_time).total_seconds()
        
        if ticker:
            print(f"✅ CCXT direto: Sucesso em {duration:.2f}s - ${ticker['last']:,.2f}")
        else:
            print(f"❌ CCXT direto: Ticker vazio em {duration:.2f}s")
        
        await exchange.close()
        
    except Exception as e:
        print(f"❌ CCXT direto falhou: {e}")
    
    # Teste 2: Integração QUALIA
    print("\n2️⃣ TESTANDO INTEGRAÇÃO QUALIA...")
    try:
        from qualia.market.kucoin_integration import KucoinIntegration
        
        kucoin = KucoinIntegration(
            api_key=None,
            api_secret=None,
            password=None,
            use_websocket=False,
            ticker_timeout=15.0  # Mesmo timeout
        )
        
        await kucoin.initialize_connection()
        
        start_time = datetime.now()
        ticker = await kucoin.fetch_ticker("BTC/USDT")
        duration = (datetime.now() - start_time).total_seconds()
        
        if ticker:
            print(f"✅ QUALIA: Sucesso em {duration:.2f}s - ${ticker['last']:,.2f}")
        else:
            print(f"❌ QUALIA: Ticker vazio em {duration:.2f}s")
        
        await kucoin.close()
        
    except Exception as e:
        print(f"❌ QUALIA falhou: {e}")
        traceback.print_exc()


async def main():
    """Função principal"""
    
    # Diagnóstico completo
    system_ok = await debug_live_system()
    
    # Comparação direta
    await test_direct_comparison()
    
    # Resultado final
    print("\n🏁 RESULTADO DO DIAGNÓSTICO")
    print("=" * 70)
    
    if system_ok:
        print("✅ Sistema funcionando corretamente")
        print("💡 O problema pode ser temporário ou específico do ambiente")
    else:
        print("❌ Sistema com problemas identificados")
        print("💡 Verificar configurações e conectividade")
    
    print(f"\nConcluído em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    asyncio.run(main()) 
