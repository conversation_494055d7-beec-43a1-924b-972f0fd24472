# Visão Geral da Arquitetura

O QUALIA_QAST é organizado em módulos independentes para facilitar a manutenção e evolução do sistema.

- **core** – implementa o núcleo quântico e as funcionalidades de consciência.
- **market** – integração com exchanges, cálculo de métricas e orquestração de trades.
- **memory** – componentes de armazenamento volátil e persistente de padrões.
- **metacognition** – camadas de avaliação e ajuste automático de estratégias.
- **strategies** – coleções de estratégias de trading baseadas no ciclo QAST.

### Sistemas de memória

O módulo `memory` contém duas implementações principais:

1. **LegacyMemorySystem** – registrado em `legacy_memory_system.py`, foi o primeiro protótipo de armazenamento. Mantém uma lista de experiências em memória e recupera os itens mais recentes sem técnicas de similaridade.
2. **MemorySystem** – definido em `system.py`, inclui persistência em disco, rotinas assíncronas de consolidação e aprendizado de padrões. É o sistema utilizado nas execuções avançadas do QUALIA.
   A partir da versão atual, esse componente integra uma `HolographicMemory` para comparação temporal e uma instância interna de `QuantumPatternMemory`.
   As funções `store_market_pattern` e `query_market_patterns` foram mantidas como wrappers de compatibilidade e direcionam as operações para a `QuantumPatternMemory`.

Ambos compartilham a variável de ambiente `QUALIA_MEMORY_FILE` para determinar o caminho de persistência quando aplicável.

O caminho de persistência utilizado pelo `MemorySystem` pode ser configurado
pela variável de ambiente `QUALIA_MEMORY_FILE`. Quando relativo, o valor é
resolvido a partir de `QUALIA_CACHE_DIR`.

A separação de responsabilidades permite testes direcionados e reutilização dos componentes. Alterações estruturais devem ser documentadas neste arquivo.

Para um resumo dos operadores conceituais propostos, consulte [docs/specs/QUALIA_Trading_System_VFinal_PT-BR.md](specs/QUALIA_Trading_System_VFinal_PT-BR.md) e o documento complementar [docs/specs/QUALIA_Trading_System_VFinal_PT-BR_EXT.md](specs/QUALIA_Trading_System_VFinal_PT-BR_EXT.md).

## Módulos Experimentais

Os arquivos `folding.py`, `resonance.py`, `emergence.py`, `retrocausality.py` e `observer.py` em `src/qualia/core` trazem implementações **parciais e experimentais** dos operadores. Embora ainda estejam distantes da visão completa, esses módulos executam transformações iniciais. O comportamento planejado pode ser consultado em [docs/specs/QUALIA_Trading_System_VFinal_PT-BR.md](specs/QUALIA_Trading_System_VFinal_PT-BR.md) e [docs/specs/QUALIA_Trading_System_VFinal_PT-BR_EXT.md](specs/QUALIA_Trading_System_VFinal_PT-BR_EXT.md). A fundamentação teórica continua no documento [Modelo Integrado da Consciência Quântica e da Informação (MICQI).md](specs/Modelo Integrado da Consciência Quântica e da Informação (MICQI).md).

## Novos módulos

O diretório `src/qualia/intentions` introduz o `IntentionEnvelope`, um dataclass que define metas de lucro e limites de drawdown para cada intenção de trading. Já em `src/qualia/retro` o módulo `RetroSelector` implementa um filtro simples de trajetórias de PnL baseado nesses limites. As estratégias que utilizam pós-seleção podem aplicar esse filtro para descartar caminhos inconsistentes.

## Operadores quânticos e métricas derivadas

O núcleo do QUALIA calcula duas métricas principais de estado: `coherence_C` e
`entanglement_E`. Elas são produzidas no `QASTCore` a partir dos estados
intermediários dos módulos em `src/qualia/core`:

- `coherence_C` é determinado em
  [`qast_core._calculate_coherence_level`](../src/qualia/core/qast_core.py), que
  combina a coerência do `folding`, a força de `resonance` e a complexidade de
  `emergence` usando pesos fixos. O resultado expressa o grau de alinhamento
  entre as transformações quânticas em curso.
- `entanglement_E` provém de
  [`qast_core._calculate_entanglement_measure`](../src/qualia/core/qast_core.py),
  multiplicando o `observer_effect` ao `emergence_complexity`. Essa métrica
  estima o nível de correlação global do sistema.

Outras métricas expostas na HUD incluem:

- **Liquidez** – calculada por
  [`market.decision_context._check_liquidity`](../src/qualia/market/decision_context.py).
  A função avalia volume recente e "spread" para fornecer um indicador de
  profundeza de mercado de 0 a 1.
- **Tendência** – derivada de
  [`market.risk_management.QUALIARiskManager`](../src/qualia/market/risk_management.py)
  calcula estabilidade de tendência usando regressão linear e o coeficiente
  de determinação (R²) como medida de direção.
- **Δ-entropia** – obtida em
  [`consciousness._compute_entropy_metrics`](../src/qualia/core/consciousness.py)
  pela diferença absoluta entre as entropias de páginas consecutivas. Valores
  altos podem disparar a detecção de padrões.

Essas métricas são enviadas ao `QualiaHUD`, onde controlam atributos de cor e
brilho no `DynamicLogoEngine`, oferecendo feedback visual contínuo sobre a
evolução do sistema.
