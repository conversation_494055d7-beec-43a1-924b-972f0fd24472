#!/usr/bin/env python3
"""
YAA - Test Zero Confidence Fix
Testa as correções implementadas para resolver o problema de confidence zero.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

import asyncio
import logging
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

from qualia.utils.logger import get_logger
from qualia.strategies.nova_estrategia_qualia.core import QualiaTSVFStrategy
from qualia.market.data_providers.kucoin_provider import KuCoinDataProvider

logger = get_logger(__name__)

async def test_zero_confidence_fix():
    """Testa as correções para o problema de confidence zero."""
    
    print("🧪 TESTE: Correções para Confidence Zero")
    print("=" * 50)
    
    try:
        # 1. Configurar estratégia com parâmetros de teste
        strategy_params = {
            "s1_strength_threshold": 0.02,  # Threshold original
            "s3_strength_threshold": 0.015,
            "strength_percentile": 80,
            "meta_sharpe_window_hours": 10,  # Reduzido para teste
            "tsvf_vector_size": 50,
            "tsvf_alpha": 0.3,
            "tsvf_gamma": 0.1,
        }
        
        strategy = QualiaTSVFStrategy(
            name="TestStrategy_BTCUSDT_1h",
            symbol="BTCUSDT",
            timeframe="1h",
            parameters=strategy_params
        )
        
        # 2. Configurar shared_context para simular warm-up
        strategy.shared_context = {
            "freeze_requirements": True,
            "strength_history": []  # Histórico vazio para forçar threshold original
        }
        strategy._tsvf_integrity_protected = True
        
        print(f"✅ Estratégia configurada: {strategy.name}")
        print(f"   - S1 Threshold: {strategy.s1_strength_threshold}")
        print(f"   - S3 Threshold: {strategy.s3_strength_threshold}")
        
        # 3. Obter dados reais de mercado
        data_provider = KuCoinDataProvider()
        
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=48)  # 48h de dados
        
        print(f"📊 Obtendo dados de mercado...")
        print(f"   - Período: {start_time} até {end_time}")
        
        market_data = await data_provider.get_historical_data(
            symbol="BTCUSDT",
            timeframe="1h",
            start_time=start_time,
            end_time=end_time,
            limit=48
        )
        
        if market_data.empty:
            print("❌ Falha ao obter dados de mercado")
            return False
            
        print(f"✅ Dados obtidos: {len(market_data)} candles")
        print(f"   - Período real: {market_data.index[0]} até {market_data.index[-1]}")
        
        # 4. Injetar dados na estratégia
        strategy._historical_data = market_data.copy()
        
        # 5. Simular histórico de returns para evitar weights zero
        strategy.sub_strategy_returns = {
            "s1": [0.001, 0.002, -0.001, 0.003, 0.001],  # Pequenos returns positivos
            "s2": [0.002, -0.001, 0.001, 0.002, 0.001],
            "s3": [0.001, 0.001, 0.002, -0.001, 0.002]
        }
        
        print("✅ Dados injetados na estratégia")
        
        # 6. Executar análise de mercado
        print("\n🔬 Executando análise de mercado...")
        
        result = strategy.analyze_market()
        
        print(f"\n📊 RESULTADOS DA ANÁLISE:")
        print(f"   - Signal: {result.get('signal', 'N/A')}")
        print(f"   - Confidence: {result.get('confidence', 'N/A')}")
        print(f"   - Position Size: {result.get('position_size', 'N/A')}")
        
        # 7. Verificar detalhes da estratégia
        strategy_analysis = result.get('strategy_analysis', {})
        if strategy_analysis:
            print(f"\n🔍 DETALHES DA ESTRATÉGIA:")
            print(f"   - S1 Position: {strategy_analysis.get('position_s1', 'N/A')}")
            print(f"   - S2 Position: {strategy_analysis.get('position_s2', 'N/A')}")
            print(f"   - S3 Position: {strategy_analysis.get('position_s3', 'N/A')}")
            print(f"   - Combined Position Raw: {strategy_analysis.get('combined_position_raw', 'N/A')}")
            
            weights = strategy_analysis.get('weights_used', {})
            print(f"   - Weights: S1={weights.get('s1', 'N/A')}, S2={weights.get('s2', 'N/A')}, S3={weights.get('s3', 'N/A')}")
            
            print(f"   - TSVF Strength S1: {strategy_analysis.get('tsvf_strength_s1', 'N/A')}")
            print(f"   - TSVF Strength S3: {strategy_analysis.get('tsvf_strength_s3', 'N/A')}")
            print(f"   - OTOC: {strategy_analysis.get('otoc', 'N/A')}")
        
        # 8. Verificar se as correções funcionaram
        confidence = result.get('confidence', 0.0)
        
        if confidence > 0.0:
            print(f"\n✅ SUCESSO: Confidence não-zero detectada ({confidence:.3f})")
            print("🎯 As correções resolveram o problema!")
            return True
        else:
            print(f"\n⚠️  ATENÇÃO: Confidence ainda é zero")
            print("🔧 Pode ser necessário ajustes adicionais")
            
            # Verificar possíveis causas
            if strategy_analysis:
                weights = strategy_analysis.get('weights_used', {})
                total_weight = sum(weights.values())
                
                if total_weight == 0:
                    print("❌ CAUSA: Todos os weights são zero")
                    print(f"   - Rolling Sharpe Ratios: {getattr(strategy, 'rolling_sharpe_ratios', 'N/A')}")
                
                positions = [
                    strategy_analysis.get('position_s1', 0),
                    strategy_analysis.get('position_s2', 0),
                    strategy_analysis.get('position_s3', 0)
                ]
                
                if all(p == 0 for p in positions):
                    print("❌ CAUSA: Todas as positions são zero")
                    print(f"   - S1 Threshold atual: {getattr(strategy, 's1_strength_threshold', 'N/A')}")
                    print(f"   - S3 Threshold atual: {getattr(strategy, 's3_strength_threshold', 'N/A')}")
            
            return False
            
    except Exception as e:
        print(f"❌ ERRO durante teste: {e}")
        logger.error("Erro durante teste de correção", exc_info=True)
        return False

async def main():
    """Função principal."""
    success = await test_zero_confidence_fix()
    
    if success:
        print("\n🎉 TESTE CONCLUÍDO COM SUCESSO!")
        print("As correções implementadas resolveram o problema de confidence zero.")
    else:
        print("\n⚠️  TESTE INDICA PROBLEMAS REMANESCENTES")
        print("Podem ser necessários ajustes adicionais.")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
