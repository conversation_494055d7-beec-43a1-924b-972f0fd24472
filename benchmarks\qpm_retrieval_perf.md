# Benchmark de Performance: <PERSON><PERSON><PERSON><PERSON><PERSON> (QuantumPatternMemory)

Data da Execucao: 2025-06-16

## Configuracao

- **Python**: 3.12.10
- **pytest-benchmark**: 5.1.0
- **Numero de padroes**: 1000
- **<PERSON>mensa<PERSON> do vetor**: 8
- **top_n**: 5

## Resultados

### <PERSON><PERSON>

```
Name (time in ms)                      Min    <PERSON>    Mean  StdDev  Median  IQR  Outliers   OPS  Rounds  Iterations
------------------------------------  -----  -----  -----  ------  ------  ---- --------- ----- ------- ----------
test_qpm_retrieval_performance     19.01  33.45  21.18   3.18   19.91 1.89 5;5   47.21     38          1
```

### Depo<PERSON> <PERSON>

```
Name (time in ms)                      Min    <PERSON>   Mean  StdDev  Median  IQR  Outliers    OPS  Rounds  Iterations
------------------------------------  -----  ----- -----  ------  ------  ---- --------- ------ ------- ----------
test_qpm_retrieval_performance     1.97   2.40   2.08   0.07   2.05 0.08 12;1  481.73     51          1
```

A otimizacao reduz a media de tempo de recuperacao de ~21 ms para ~2 ms, um ganho de desempenho superior a 10x.
