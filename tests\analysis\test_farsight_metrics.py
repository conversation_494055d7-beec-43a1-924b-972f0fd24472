import numpy as np
from unittest.mock import Mock
from qualia.analysis.farsight_engine import FarsightEngine


def test_farsight_engine_emits_stats(monkeypatch):
    clusters = [
        {
            "topic": "A",
            "curvature": 0.3,
            "velocity": 0.2,
            "prediction_window": "na",
            "sources": [],
        },
        {
            "topic": "B",
            "curvature": 0.5,
            "velocity": 0.1,
            "prediction_window": "na",
            "sources": [],
        },
    ]

    def fake_fetch(self):
        self._papers = [object()] * 10

    def fake_cluster(self):
        self._clusters = clusters

    mock_statsd = Mock()
    monkeypatch.setattr(FarsightEngine, "_fetch_papers", fake_fetch)
    monkeypatch.setattr(FarsightEngine, "_cluster_papers", fake_cluster)

    engine = FarsightEngine(statsd_client=mock_statsd)
    result = engine.run()
    assert result == clusters

    expected_curv = float(np.mean([0.3, 0.5]))
    expected_burst = float(np.mean([0.2, 0.1]))
    expected_conf = len(clusters) / engine.max_results

    mock_statsd.gauge.assert_any_call("metacog.curvature_avg", expected_curv)
    mock_statsd.gauge.assert_any_call("metacog.burst_eta", expected_burst)
    mock_statsd.gauge.assert_any_call("metacog.confidence", expected_conf)


def test_engine_uses_env_categories(monkeypatch):
    monkeypatch.setenv("FAR_CATEGORIES", "quant-ph")
    engine = FarsightEngine()
    assert engine.categories == "quant-ph"
