#!/usr/bin/env python3
"""
QUALIA Bayesian Optimizer - Etapa D
YAA IMPLEMENTATION: Sistema de otimização contínua usando Optuna para maximizar PnL 24h rolling.
"""

import sys
import asyncio
import time
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import pandas as pd
import numpy as np
import optuna
from optuna.samplers import TPESampler
from optuna.pruners import MedianPruner
import requests
from concurrent.futures import ThreadPoolExecutor
import threading

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

@dataclass
class OptimizationConfig:
    """Configuração do otimizador Bayesiano."""
    study_name: str = "qualia_bayesian_optimization"
    n_trials_per_cycle: int = 20
    optimization_cycles: int = 500
    pnl_window_hours: int = 24
    symbols: List[str] = None
    base_params: Dict[str, float] = None
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = ["BTCUSDT", "ETHUSDT"]
        if self.base_params is None:
            # Configurações vencedoras como ponto de partida
            self.base_params = {
                "price_amplification": 2.0,
                "news_amplification": 7.0,
                "min_confidence": 0.4
            }

class QualiaDataProvider:
    """Provedor de dados para otimização."""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'QUALIA-BayesianOptimizer/1.0'})
        self.cache = {}
        self.cache_ttl = 300  # 5 minutos
    
    def fetch_market_data(self, symbol: str, hours: int = 48) -> pd.DataFrame:
        """Busca dados de mercado com cache."""
        cache_key = f"{symbol}_{hours}"
        current_time = time.time()
        
        # Verifica cache
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if current_time - timestamp < self.cache_ttl:
                return cached_data
        
        try:
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(hours=hours)).timestamp() * 1000)
            
            params = {
                'symbol': symbol,
                'interval': '1h',
                'startTime': start_time,
                'endTime': end_time,
                'limit': min(1000, hours + 10)
            }
            
            response = self.session.get("https://api.binance.com/api/v3/klines", params=params)
            response.raise_for_status()
            data = response.json()
            
            if not data:
                return pd.DataFrame()
            
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']].copy()
            
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index().dropna()
            
            # Adiciona indicadores otimizados
            df = self._add_indicators(df)
            
            # Atualiza cache
            self.cache[cache_key] = (df, current_time)
            
            return df
            
        except Exception as e:
            logging.error(f"Erro ao buscar dados para {symbol}: {e}")
            return pd.DataFrame()
    
    def _add_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Adiciona indicadores técnicos otimizados."""
        
        # Retornos
        df['returns'] = df['close'].pct_change().fillna(0)
        
        # EMAs otimizadas
        df['ema_8'] = df['close'].ewm(span=8).mean()
        df['ema_21'] = df['close'].ewm(span=21).mean()
        
        # RSI otimizado
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=21).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=21).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD otimizado
        df['macd'] = df['ema_8'] - df['ema_21']
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(21).mean()
        bb_std = df['close'].rolling(21).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2.5)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2.5)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # Volatilidade e volume
        df['volatility'] = df['returns'].rolling(21).std()
        df['volume_sma'] = df['volume'].rolling(21).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        # Momentum
        df['momentum'] = df['close'] / df['close'].shift(21) - 1
        
        return df

class QualiaStrategy:
    """Estratégia QUALIA otimizada para Bayesian Optimization."""
    
    def __init__(self, params: Dict[str, float]):
        self.price_amplification = params.get('price_amplification', 2.0)
        self.news_amplification = params.get('news_amplification', 7.0)
        self.min_confidence = params.get('min_confidence', 0.4)
        self.transaction_cost = 0.0001  # 0.01% - ultra baixo
    
    def generate_signal(self, current_data: pd.Series, symbol: str) -> Dict[str, float]:
        """Gera sinal usando parâmetros otimizados."""
        
        # Ajuste específico por símbolo
        if symbol == "ETHUSDT":
            min_confidence = max(self.min_confidence, 0.6)  # ETH mais conservador
        else:
            min_confidence = self.min_confidence
        
        # RSI Signal (Mean Reversion)
        rsi = current_data.get('rsi', 50)
        if pd.isna(rsi):
            rsi_signal = 0.0
        elif rsi > 75:
            rsi_signal = -0.8
        elif rsi < 25:
            rsi_signal = 0.8
        else:
            rsi_signal = (50 - rsi) / 25
            rsi_signal = np.clip(rsi_signal, -0.8, 0.8)
        
        # MACD Signal (Momentum)
        macd = current_data.get('macd', 0)
        macd_signal = current_data.get('macd_signal', 0)
        macd_histogram = current_data.get('macd_histogram', 0)
        
        if pd.isna(macd) or pd.isna(macd_signal):
            macd_momentum = 0.0
        else:
            macd_cross = np.tanh((macd - macd_signal) * 500)
            macd_hist = np.tanh(macd_histogram * 1000) if not pd.isna(macd_histogram) else 0
            macd_momentum = (macd_cross + macd_hist) / 2
            macd_momentum = np.clip(macd_momentum, -0.8, 0.8)
        
        # Bollinger Signal
        bb_position = current_data.get('bb_position', 0.5)
        if pd.isna(bb_position):
            bb_signal = 0.0
        else:
            if bb_position > 0.9:
                bb_signal = -0.6
            elif bb_position < 0.1:
                bb_signal = 0.6
            else:
                bb_signal = (0.5 - bb_position) * 1.2
                bb_signal = np.clip(bb_signal, -0.6, 0.6)
        
        # Trend Signal
        ema_8 = current_data.get('ema_8', 0)
        ema_21 = current_data.get('ema_21', 0)
        momentum = current_data.get('momentum', 0)
        
        if pd.isna(ema_8) or pd.isna(ema_21) or pd.isna(momentum):
            trend_signal = 0.0
        else:
            ema_trend = np.tanh((ema_8 - ema_21) / ema_21 * 20)
            momentum_trend = np.tanh(momentum * 5)
            trend_signal = (ema_trend + momentum_trend) / 2
            trend_signal = np.clip(trend_signal, -0.6, 0.6)
        
        # Confiança
        volatility = current_data.get('volatility', 0.01)
        volume_ratio = current_data.get('volume_ratio', 1.0)
        
        if pd.isna(volatility) or pd.isna(volume_ratio):
            confidence = 0.5
        else:
            vol_factor = 1.0 / (1.0 + volatility * 50)
            volume_factor = np.clip(volume_ratio / 2.0, 0.4, 1.0)
            confidence = 0.2 + 0.5 * vol_factor + 0.3 * (volume_factor - 0.4) / 0.6
            confidence = np.clip(confidence, 0.2, 0.9)
        
        # Combina sinais
        if confidence < min_confidence:
            position = 0.0
        else:
            price_signals = (rsi_signal + bb_signal) / 2.0
            momentum_signals = (macd_momentum + trend_signal) / 2.0
            
            combined_signal = (
                (self.price_amplification / 10.0) * price_signals + 
                (self.news_amplification / 10.0) * momentum_signals
            )
            
            position = combined_signal * confidence
            position = np.clip(position, -0.8, 0.8)
        
        return {
            'position': position,
            'confidence': confidence,
            'rsi_signal': rsi_signal,
            'macd_signal': macd_momentum,
            'bb_signal': bb_signal,
            'trend_signal': trend_signal
        }
    
    def calculate_pnl_24h(self, df: pd.DataFrame, symbol: str) -> float:
        """Calcula PnL rolling 24h."""
        
        if df.empty or len(df) < 48:  # Precisa de pelo menos 48h de dados
            return -999.0  # Penalidade para dados insuficientes
        
        try:
            positions = []
            returns = []
            
            # Janela mínima
            min_window = 30
            
            for i in range(min_window, len(df)):
                current_data = df.iloc[i]
                signals = self.generate_signal(current_data, symbol)
                position = signals['position']
                positions.append(position)
                
                # Calcula retorno
                if i > min_window:
                    market_return = (df['close'].iloc[i] / df['close'].iloc[i-1]) - 1
                    strategy_return = positions[-2] * market_return
                    
                    # Custos de transação
                    if len(positions) > 1:
                        position_change = abs(positions[-1] - positions[-2])
                        transaction_cost = position_change * self.transaction_cost
                        strategy_return -= transaction_cost
                    
                    returns.append(strategy_return)
                else:
                    returns.append(0.0)
            
            if not returns or len(returns) < 24:
                return -999.0
            
            # PnL rolling 24h (últimas 24 horas)
            recent_returns = returns[-24:]
            pnl_24h = np.sum(recent_returns) * 100  # Em percentual
            
            return pnl_24h
            
        except Exception as e:
            logging.error(f"Erro no cálculo PnL: {e}")
            return -999.0

class BayesianOptimizer:
    """Otimizador Bayesiano principal."""
    
    def __init__(self, config: OptimizationConfig):
        self.config = config
        self.data_provider = QualiaDataProvider()
        self.study = None
        self.best_params = config.base_params.copy()
        self.optimization_history = []
        self.is_running = False
        self.current_cycle = 0
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/bayesian_optimizer.log'),
                logging.StreamHandler()
            ]
        )
        
        # Cria diretórios
        Path("logs").mkdir(exist_ok=True)
        Path("results/bayesian_optimization").mkdir(parents=True, exist_ok=True)
    
    def objective(self, trial: optuna.Trial) -> float:
        """Função objetivo para otimização."""
        
        # Define espaço de busca baseado nos parâmetros vencedores
        params = {
            'price_amplification': trial.suggest_float(
                'price_amplification', 
                self.best_params['price_amplification'] * 0.5,
                self.best_params['price_amplification'] * 2.0
            ),
            'news_amplification': trial.suggest_float(
                'news_amplification',
                self.best_params['news_amplification'] * 0.5,
                self.best_params['news_amplification'] * 2.0
            ),
            'min_confidence': trial.suggest_float(
                'min_confidence',
                max(0.1, self.best_params['min_confidence'] * 0.5),
                min(0.9, self.best_params['min_confidence'] * 2.0)
            )
        }
        
        # Testa estratégia com parâmetros propostos
        strategy = QualiaStrategy(params)
        total_pnl = 0.0
        valid_symbols = 0
        
        for symbol in self.config.symbols:
            try:
                df = self.data_provider.fetch_market_data(symbol, hours=72)
                
                if df.empty:
                    continue
                
                pnl_24h = strategy.calculate_pnl_24h(df, symbol)
                
                if pnl_24h > -999.0:  # Válido
                    total_pnl += pnl_24h
                    valid_symbols += 1
                
            except Exception as e:
                logging.error(f"Erro ao testar {symbol}: {e}")
                continue
        
        if valid_symbols == 0:
            return -999.0
        
        # Média ponderada do PnL
        avg_pnl = total_pnl / valid_symbols
        
        # Log do trial
        logging.info(f"Trial {trial.number}: PnL={avg_pnl:.4f}%, Params={params}")
        
        return avg_pnl
    
    def initialize_study(self):
        """Inicializa o estudo Optuna."""
        
        sampler = TPESampler(seed=42)
        pruner = MedianPruner(n_startup_trials=5, n_warmup_steps=10)
        
        self.study = optuna.create_study(
            study_name=self.config.study_name,
            direction='maximize',
            sampler=sampler,
            pruner=pruner,
            storage=f"sqlite:///results/bayesian_optimization/{self.config.study_name}.db",
            load_if_exists=True
        )
        
        logging.info(f"Estudo inicializado: {self.config.study_name}")
    
    def run_optimization_cycle(self) -> Dict[str, Any]:
        """Executa um ciclo de otimização."""

        cycle_start = time.time()

        logging.info(f"Iniciando ciclo {self.current_cycle + 1}/{self.config.optimization_cycles}")

        # Inicializa estudo se necessário
        if self.study is None:
            self.initialize_study()

        # Executa trials
        self.study.optimize(
            self.objective,
            n_trials=self.config.n_trials_per_cycle,
            timeout=1800  # 30 minutos máximo por ciclo
        )
        
        # Atualiza melhores parâmetros
        if self.study.best_trial:
            self.best_params = self.study.best_params.copy()
            best_value = self.study.best_value
        else:
            best_value = -999.0
        
        cycle_time = time.time() - cycle_start
        
        cycle_result = {
            'cycle': self.current_cycle + 1,
            'best_params': self.best_params.copy(),
            'best_pnl_24h': best_value,
            'n_trials': len(self.study.trials),
            'cycle_time_seconds': cycle_time,
            'timestamp': datetime.now().isoformat()
        }
        
        self.optimization_history.append(cycle_result)
        
        logging.info(f"Ciclo {self.current_cycle + 1} concluído: PnL={best_value:.4f}%, Tempo={cycle_time:.1f}s")
        logging.info(f"Melhores parâmetros: {self.best_params}")
        
        self.current_cycle += 1
        
        return cycle_result
    
    async def start_optimization(self):
        """Inicia otimização contínua."""
        
        if self.is_running:
            logging.warning("Otimização já está rodando")
            return
        
        self.is_running = True
        
        logging.info("🚀 Iniciando Bayesian Optimizer QUALIA")
        logging.info(f"Configuração: {self.config}")
        
        # Inicializa estudo
        self.initialize_study()
        
        try:
            while self.is_running and self.current_cycle < self.config.optimization_cycles:
                
                # Executa ciclo de otimização
                cycle_result = self.run_optimization_cycle()
                
                # Salva resultados
                self.save_results()
                
                # Aguarda próximo ciclo (5 minutos)
                if self.current_cycle < self.config.optimization_cycles:
                    logging.info("Aguardando próximo ciclo (5 minutos)...")
                    await asyncio.sleep(300)
        
        except KeyboardInterrupt:
            logging.info("Otimização interrompida pelo usuário")
        except Exception as e:
            logging.error(f"Erro na otimização: {e}")
        finally:
            self.is_running = False
            logging.info("Otimização finalizada")
    
    def save_results(self):
        """Salva resultados da otimização."""

        results = {
            'config': {
                'study_name': self.config.study_name,
                'n_trials_per_cycle': self.config.n_trials_per_cycle,
                'optimization_cycles': self.config.optimization_cycles,
                'symbols': self.config.symbols,
                'base_params': self.config.base_params
            },
            'current_best_params': self.best_params,
            'current_best_pnl': self.study.best_value if self.study and self.study.best_trial else None,
            'optimization_history': self.optimization_history,
            'total_trials': len(self.study.trials) if self.study else 0,
            'current_cycle': self.current_cycle,
            'timestamp': datetime.now().isoformat()
        }
        
        # Salva JSON
        output_file = Path(f"results/bayesian_optimization/optimization_results_{int(time.time())}.json")
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        # Salva parâmetros atuais
        params_file = Path("results/bayesian_optimization/current_best_params.json")
        with open(params_file, 'w') as f:
            json.dump(self.best_params, f, indent=2)
        
        logging.info(f"Resultados salvos em: {output_file}")
    
    def stop_optimization(self):
        """Para a otimização."""
        self.is_running = False
        logging.info("Parando otimização...")
    
    def get_current_best_params(self) -> Dict[str, float]:
        """Retorna os melhores parâmetros atuais."""
        return self.best_params.copy()


async def main():
    """Função principal."""
    
    print("🚀 QUALIA BAYESIAN OPTIMIZER - ETAPA D")
    print("=" * 60)
    
    # Configuração
    config = OptimizationConfig(
        study_name="qualia_production_optimization",
        n_trials_per_cycle=15,
        optimization_cycles=500,
        symbols=["BTCUSDT", "ETHUSDT"]
    )
    
    # Inicializa otimizador
    optimizer = BayesianOptimizer(config)
    
    print(f"📊 Configuração:")
    print(f"   • Símbolos: {config.symbols}")
    print(f"   • Trials por ciclo: {config.n_trials_per_cycle}")
    print(f"   • Ciclos totais: {config.optimization_cycles}")
    print(f"   • Parâmetros base: {config.base_params}")
    
    # Inicia otimização
    await optimizer.start_optimization()


if __name__ == "__main__":
    asyncio.run(main())
