from __future__ import annotations

"""Prototypes for conceptual explorations of QUALIA."""

import json
from pathlib import Path
from typing import Any, Dict

import numpy as np

from qualia.memory.holographic_memory import HolographicMemory
from qualia.core.retrocausality import apply_retrocausality
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


def holographic_memory_prototype(size: int = 50) -> float:
    """Store and query random vectors using :class:`HolographicMemory`.

    Parameters
    ----------
    size
        Number of vectors to generate and store.

    Returns
    -------
    float
        Mean similarity for the top matches to the last vector.
    """
    memory = HolographicMemory(max_items=size, half_life=5.0)
    rng = np.random.default_rng(42)
    for _ in range(size):
        vec = rng.normal(size=4)
        memory.store(vec, metadata={"source": "synthetic"})
    last_vec = rng.normal(size=4)
    memory.store(last_vec)
    results = memory.query(last_vec, top_n=5)
    mean_sim = float(np.mean([r["similarity"] for r in results])) if results else 0.0
    logger.info("Mean holographic similarity: %f", mean_sim)
    return mean_sim


def retrocausal_trading_prototype(length: int = 100) -> float:
    """Apply retrocausality to a synthetic price series.

    Parameters
    ----------
    length
        Number of points in the generated price series.

    Returns
    -------
    float
        Correlation between original and retrocausality-adjusted series.
    """
    rng = np.random.default_rng(123)
    prices = rng.normal(loc=100.0, scale=1.0, size=length)
    future = np.roll(prices, -1)
    adjusted = apply_retrocausality(prices, future, gamma=0.2)
    corr = float(np.corrcoef(prices[:-1], adjusted[:-1])[0, 1])
    logger.info("Retrocausal correlation: %f", corr)
    return corr


def evaluate_strategy_impact() -> Dict[str, float]:
    """Run both prototypes and return their metrics."""
    memory_score = holographic_memory_prototype()
    retro_score = retrocausal_trading_prototype()
    summary = {
        "memory_similarity": memory_score,
        "retrocausal_correlation": retro_score,
    }
    logger.info("Prototype summary: %s", summary)
    return summary


def save_results(results: Dict[str, Any], path: str) -> None:
    """Persist results for later analysis."""
    Path(path).parent.mkdir(parents=True, exist_ok=True)
    with open(path, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2)
    logger.info("Results saved to %s", path)


if __name__ == "__main__":
    res = evaluate_strategy_impact()
    save_results(res, "experiments/concept_results.json")
