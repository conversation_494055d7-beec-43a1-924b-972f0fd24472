# Caminho padrão para dados de cache
QUALIA_CACHE_DIR=data/cache
# Chave secreta base64 de 32 bytes (ex.: python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())")
QUALIA_SECRET_KEY=F1HZX81kN5k_pFY_wPnIJizkY7pwOxn5M82CAxIapH4=

# Diretório para arquivos de log
QUALIA_LOGS_DIR=logs

# Diretório de resultados do sistema
QUALIA_RESULTS_DIR=results

# Subdiretório para backtests do QAST
QUALIA_QAST_BACKTEST_RESULTS_DIR=results/qast_backtests
QUALIA_ENABLE_BAYES_OPT=False

# Caminho do arquivo de parâmetros de estratégia
QUALIA_STRATEGY_CONFIG=config/strategy_parameters.json

# Exemplo de níveis específicos por módulo (JSON)
# QUALIA_MODULE_LEVELS={"src.qualia.memory": "DEBUG", "src.qualia.core.universe": "DEBUG"}

# Intervalo para suprimir mensagens de log duplicadas consecutivas (segundos)
# QUALIA_LOG_DEDUP_INTERVAL=1.0
