"""Integrated monitoring system for QUALIA.

This module provides a unified interface to all monitoring components,
creating a comprehensive monitoring ecosystem that includes:
- Health monitoring dashboard
- Performance metrics collection
- Automated parameter tuning
- Multi-exchange failover management
- Signal pipeline monitoring
- Bootstrap progress tracking
"""

from __future__ import annotations

import asyncio
import time
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass
import json

from qualia.utils.logger import get_logger
from qualia.monitoring.health_dashboard import HealthMonitoringDashboard
from qualia.monitoring.performance_metrics import PerformanceMetricsCollector
from qualia.monitoring.parameter_tuner import AutomatedParameterTuner
from qualia.monitoring.multi_exchange_failover import MultiExchangeFailoverManager
from qualia.utils.signal_pipeline_monitor import SignalPipelineMonitor
from qualia.utils.bootstrap_tracker import BootstrapProgressTracker

logger = get_logger(__name__)


@dataclass
class MonitoringConfig:
    """Configuration for integrated monitoring system."""
    enable_health_monitoring: bool = True
    enable_performance_metrics: bool = True
    enable_parameter_tuning: bool = True
    enable_failover_management: bool = True
    enable_signal_monitoring: bool = True
    enable_bootstrap_tracking: bool = True
    
    # Update intervals
    health_check_interval: float = 30.0
    metrics_collection_interval: float = 10.0
    parameter_tuning_interval: float = 3600.0  # 1 hour
    
    # Storage and persistence
    enable_persistent_storage: bool = True
    storage_path: str = "data/monitoring"
    
    # Alerting
    enable_alerting: bool = True
    alert_channels: List[str] = None


class IntegratedMonitoringSystem:
    """Comprehensive integrated monitoring system for QUALIA.
    
    This system orchestrates all monitoring components to provide:
    - Unified monitoring interface
    - Cross-component correlation
    - Centralized alerting
    - Performance optimization
    - System health management
    """
    
    def __init__(
        self,
        config: Optional[MonitoringConfig] = None,
        name: str = "qualia_integrated_monitoring"
    ):
        self.config = config or MonitoringConfig()
        self.name = name
        
        # Initialize monitoring components
        self.health_dashboard: Optional[HealthMonitoringDashboard] = None
        self.performance_collector: Optional[PerformanceMetricsCollector] = None
        self.parameter_tuner: Optional[AutomatedParameterTuner] = None
        self.failover_manager: Optional[MultiExchangeFailoverManager] = None
        self.signal_monitor: Optional[SignalPipelineMonitor] = None
        self.bootstrap_tracker: Optional[BootstrapProgressTracker] = None
        
        # System state
        self.is_running = False
        self.start_time: Optional[float] = None
        
        # Cross-component correlation
        self.correlation_data: Dict[str, Any] = {}
        self.system_alerts: List[Dict] = []
        
        # Background tasks
        self.monitoring_tasks: List[asyncio.Task] = []
        
        # Statistics
        self.stats = {
            'total_alerts': 0,
            'system_restarts': 0,
            'optimization_cycles': 0,
            'failover_events': 0,
            'uptime': 0.0
        }

    async def initialize(self) -> None:
        """Initialize all monitoring components."""
        logger.info(f"Initializing integrated monitoring system '{self.name}'")
        
        try:
            # Initialize health monitoring
            if self.config.enable_health_monitoring:
                self.health_dashboard = HealthMonitoringDashboard(
                    name=f"{self.name}_health",
                    update_interval=self.config.health_check_interval,
                    enable_auto_alerts=self.config.enable_alerting
                )
                await self.health_dashboard.start_monitoring()
                logger.info("Health monitoring dashboard initialized")
            
            # Initialize performance metrics
            if self.config.enable_performance_metrics:
                self.performance_collector = PerformanceMetricsCollector(
                    name=f"{self.name}_performance",
                    collection_interval=self.config.metrics_collection_interval,
                    enable_system_metrics=True,
                    enable_trend_analysis=True
                )
                await self.performance_collector.start_collection()
                logger.info("Performance metrics collector initialized")
            
            # Initialize parameter tuning
            if self.config.enable_parameter_tuning:
                self.parameter_tuner = AutomatedParameterTuner(
                    name=f"{self.name}_tuner",
                    enable_ab_testing=True
                )
                logger.info("Automated parameter tuner initialized")
            
            # Initialize failover management
            if self.config.enable_failover_management:
                self.failover_manager = MultiExchangeFailoverManager(
                    name=f"{self.name}_failover",
                    health_check_interval=self.config.health_check_interval,
                    enable_load_balancing=True
                )
                await self.failover_manager.start_monitoring()
                logger.info("Multi-exchange failover manager initialized")
            
            # Initialize signal monitoring
            if self.config.enable_signal_monitoring:
                self.signal_monitor = SignalPipelineMonitor(
                    name=f"{self.name}_signals",
                    enable_detailed_logging=True
                )
                logger.info("Signal pipeline monitor initialized")
            
            # Initialize bootstrap tracking
            if self.config.enable_bootstrap_tracking:
                self.bootstrap_tracker = BootstrapProgressTracker(
                    name=f"{self.name}_bootstrap",
                    enable_early_termination=True,
                    adaptive_thresholds=True
                )
                logger.info("Bootstrap progress tracker initialized")
            
            # Setup cross-component callbacks
            self._setup_cross_component_integration()
            
            logger.info("Integrated monitoring system initialization completed")
            
        except Exception as e:
            logger.error(f"Failed to initialize monitoring system: {e}")
            raise

    async def start(self) -> None:
        """Start the integrated monitoring system."""
        if self.is_running:
            logger.warning("Monitoring system is already running")
            return
        
        await self.initialize()
        
        self.is_running = True
        self.start_time = time.time()
        
        # Start background monitoring tasks
        self.monitoring_tasks = [
            asyncio.create_task(self._correlation_loop()),
            asyncio.create_task(self._optimization_loop()),
            asyncio.create_task(self._health_check_loop())
        ]
        
        logger.info(f"Integrated monitoring system '{self.name}' started")

    async def stop(self) -> None:
        """Stop the integrated monitoring system."""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Cancel background tasks
        for task in self.monitoring_tasks:
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        # Stop individual components
        if self.health_dashboard:
            await self.health_dashboard.stop_monitoring()
        
        if self.performance_collector:
            await self.performance_collector.stop_collection()
        
        if self.failover_manager:
            await self.failover_manager.stop_monitoring()
        
        logger.info(f"Integrated monitoring system '{self.name}' stopped")

    def _setup_cross_component_integration(self) -> None:
        """Setup integration between monitoring components."""
        # Health dashboard callbacks
        if self.health_dashboard:
            self.health_dashboard.add_alert_callback(self._handle_health_alert)
        
        # Performance collector callbacks
        if self.performance_collector:
            self.performance_collector.add_metric_callback(self._handle_performance_metric)
            self.performance_collector.add_bottleneck_callback(self._handle_bottleneck)
        
        # Failover manager callbacks
        if self.failover_manager:
            self.failover_manager.add_failover_callback(self._handle_failover_event)
            self.failover_manager.add_recovery_callback(self._handle_recovery_event)
        
        # Signal monitor callbacks
        if self.signal_monitor:
            self.signal_monitor.add_rejection_callback(self._handle_signal_rejection)
        
        # Bootstrap tracker callbacks
        if self.bootstrap_tracker:
            self.bootstrap_tracker.add_convergence_callback(self._handle_convergence)

    def _handle_health_alert(self, alert) -> None:
        """Handle health monitoring alerts."""
        self.stats['total_alerts'] += 1
        
        alert_data = {
            'type': 'health',
            'timestamp': time.time(),
            'component': alert.component,
            'severity': alert.severity.value,
            'message': alert.message
        }
        
        self.system_alerts.append(alert_data)
        logger.warning(f"Health alert: {alert.message}")
        
        # Trigger optimization if critical
        if alert.severity.value == 'critical':
            asyncio.create_task(self._trigger_emergency_optimization())

    def _handle_performance_metric(self, metric) -> None:
        """Handle performance metric updates."""
        # Store for correlation analysis
        self.correlation_data[f"performance_{metric.name}"] = {
            'value': metric.value,
            'timestamp': metric.timestamp,
            'type': metric.metric_type.value
        }

    def _handle_bottleneck(self, bottlenecks) -> None:
        """Handle bottleneck detection."""
        for bottleneck_name, bottleneck_info in bottlenecks.items():
            alert_data = {
                'type': 'bottleneck',
                'timestamp': time.time(),
                'bottleneck': bottleneck_name,
                'severity': bottleneck_info['severity'],
                'description': bottleneck_info['description']
            }
            
            self.system_alerts.append(alert_data)
            logger.warning(f"Bottleneck detected: {bottleneck_info['description']}")

    def _handle_failover_event(self, event) -> None:
        """Handle failover events."""
        self.stats['failover_events'] += 1
        
        alert_data = {
            'type': 'failover',
            'timestamp': event.timestamp,
            'from_exchange': event.from_exchange,
            'to_exchange': event.to_exchange,
            'reason': event.reason
        }
        
        self.system_alerts.append(alert_data)
        logger.warning(f"Failover: {event.from_exchange} -> {event.to_exchange}")

    def _handle_recovery_event(self, exchange_name) -> None:
        """Handle exchange recovery events."""
        alert_data = {
            'type': 'recovery',
            'timestamp': time.time(),
            'exchange': exchange_name,
            'message': f"Exchange {exchange_name} recovered"
        }
        
        self.system_alerts.append(alert_data)
        logger.info(f"Exchange recovery: {exchange_name}")

    def _handle_signal_rejection(self, metrics, reason, details) -> None:
        """Handle signal rejection events."""
        # Track signal rejection patterns
        rejection_key = f"signal_rejection_{reason.value}"
        if rejection_key not in self.correlation_data:
            self.correlation_data[rejection_key] = []
        
        self.correlation_data[rejection_key].append({
            'timestamp': time.time(),
            'symbol': metrics.symbol,
            'confidence': metrics.confidence,
            'details': details
        })

    def _handle_convergence(self, analysis) -> None:
        """Handle bootstrap convergence events."""
        alert_data = {
            'type': 'convergence',
            'timestamp': time.time(),
            'status': analysis.status.value,
            'confidence': analysis.confidence_level,
            'convergence_rate': analysis.convergence_rate
        }
        
        self.system_alerts.append(alert_data)
        logger.info(f"Bootstrap convergence: {analysis.status.value}")

    async def _correlation_loop(self) -> None:
        """Background loop for cross-component correlation analysis."""
        while self.is_running:
            try:
                await self._analyze_correlations()
                await asyncio.sleep(60.0)  # Run every minute
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in correlation loop: {e}")
                await asyncio.sleep(60.0)

    async def _optimization_loop(self) -> None:
        """Background loop for system optimization."""
        while self.is_running:
            try:
                await self._perform_optimization()
                await asyncio.sleep(self.config.parameter_tuning_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in optimization loop: {e}")
                await asyncio.sleep(self.config.parameter_tuning_interval)

    async def _health_check_loop(self) -> None:
        """Background loop for overall system health checks."""
        while self.is_running:
            try:
                await self._check_system_health()
                await asyncio.sleep(self.config.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(self.config.health_check_interval)

    async def _analyze_correlations(self) -> None:
        """Analyze correlations between different monitoring components."""
        # This would implement sophisticated correlation analysis
        # For now, just log basic correlation insights
        
        current_time = time.time()
        recent_cutoff = current_time - 300  # Last 5 minutes
        
        # Count recent alerts by type
        recent_alerts = [
            alert for alert in self.system_alerts
            if alert['timestamp'] > recent_cutoff
        ]
        
        if len(recent_alerts) > 5:
            logger.warning(f"High alert frequency: {len(recent_alerts)} alerts in last 5 minutes")
            
            # Trigger optimization if too many alerts
            await self._trigger_emergency_optimization()

    async def _perform_optimization(self) -> None:
        """Perform system optimization based on collected data."""
        self.stats['optimization_cycles'] += 1
        
        # Get current system status
        system_status = await self.get_system_status()
        
        # Identify optimization opportunities
        if system_status['overall_health_score'] < 0.7:
            logger.info("System health below threshold, triggering optimization")
            
            # Trigger parameter tuning if available
            if self.parameter_tuner:
                # This would trigger actual parameter optimization
                logger.info("Parameter optimization triggered")

    async def _check_system_health(self) -> None:
        """Check overall system health."""
        if self.start_time:
            self.stats['uptime'] = time.time() - self.start_time
        
        # Check component health
        unhealthy_components = []
        
        if self.health_dashboard and not self.health_dashboard.is_monitoring:
            unhealthy_components.append("health_dashboard")
        
        if self.performance_collector and not self.performance_collector.is_collecting:
            unhealthy_components.append("performance_collector")
        
        if self.failover_manager and not self.failover_manager.is_monitoring:
            unhealthy_components.append("failover_manager")
        
        if unhealthy_components:
            logger.warning(f"Unhealthy monitoring components: {unhealthy_components}")

    async def _trigger_emergency_optimization(self) -> None:
        """Trigger emergency optimization procedures."""
        logger.warning("Triggering emergency optimization")
        
        # This would implement emergency optimization procedures
        # such as parameter resets, cache clearing, etc.
        
        if self.performance_collector:
            # Reset performance statistics
            self.performance_collector.reset_statistics()
        
        if self.signal_monitor:
            # Reset signal monitoring statistics
            self.signal_monitor.reset_statistics()

    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        status = {
            'name': self.name,
            'is_running': self.is_running,
            'uptime': self.stats['uptime'],
            'start_time': self.start_time,
            'components': {},
            'recent_alerts': self.system_alerts[-10:],  # Last 10 alerts
            'statistics': self.stats,
            'overall_health_score': 0.0
        }
        
        # Collect component statuses
        health_scores = []
        
        if self.health_dashboard:
            dashboard_status = self.health_dashboard.get_dashboard_status()
            status['components']['health_dashboard'] = dashboard_status
            health_scores.append(dashboard_status['system_metrics']['overall_health_score'])
        
        if self.performance_collector:
            perf_status = self.performance_collector.get_performance_summary()
            status['components']['performance_collector'] = perf_status
            health_scores.append(perf_status['current_performance']['overall_score'])
        
        if self.failover_manager:
            failover_status = self.failover_manager.get_failover_status()
            status['components']['failover_manager'] = failover_status
            # Calculate health score based on active exchanges
            if failover_status['total_exchanges'] > 0:
                health_scores.append(len(failover_status['active_exchanges']) / failover_status['total_exchanges'])
        
        if self.signal_monitor:
            signal_status = self.signal_monitor.get_pipeline_status()
            status['components']['signal_monitor'] = signal_status
            health_scores.append(signal_status['pass_rate'])
        
        if self.bootstrap_tracker:
            bootstrap_status = self.bootstrap_tracker.get_current_status()
            status['components']['bootstrap_tracker'] = bootstrap_status
            if bootstrap_status['status'] == 'completed':
                health_scores.append(1.0)
            elif bootstrap_status['status'] == 'active':
                health_scores.append(bootstrap_status['overall_progress'])
            else:
                health_scores.append(0.5)
        
        # Calculate overall health score
        if health_scores:
            status['overall_health_score'] = sum(health_scores) / len(health_scores)
        
        return status

    def register_exchange(self, exchange_name: str, exchange_instance: Any, **kwargs) -> None:
        """Register an exchange with the failover manager."""
        if self.failover_manager:
            self.failover_manager.register_exchange(exchange_name, exchange_instance, **kwargs)

    def register_health_component(self, component_name: str, health_checker: Callable, **kwargs) -> None:
        """Register a component with the health dashboard."""
        if self.health_dashboard:
            self.health_dashboard.register_component(component_name, health_checker, **kwargs)

    def record_performance_metric(self, name: str, value: float, **kwargs) -> None:
        """Record a performance metric."""
        if self.performance_collector:
            self.performance_collector.record_metric(name, value, **kwargs)

    def start_signal_tracking(self, signal_id: str, **kwargs) -> str:
        """Start tracking a signal through the pipeline."""
        if self.signal_monitor:
            return self.signal_monitor.start_signal_tracking(signal_id, **kwargs)
        return signal_id

    def start_bootstrap_tracking(self) -> None:
        """Start bootstrap progress tracking."""
        if self.bootstrap_tracker:
            self.bootstrap_tracker.start_bootstrap()

    async def execute_with_failover(self, operation: str, *args, **kwargs) -> Any:
        """Execute operation with automatic failover."""
        if self.failover_manager:
            return await self.failover_manager.execute_with_failover(operation, args, kwargs)
        else:
            raise RuntimeError("Failover manager not available")

    def get_comprehensive_report(self) -> Dict[str, Any]:
        """Generate comprehensive monitoring report."""
        return {
            'timestamp': time.time(),
            'system_status': asyncio.create_task(self.get_system_status()),
            'config': {
                'enable_health_monitoring': self.config.enable_health_monitoring,
                'enable_performance_metrics': self.config.enable_performance_metrics,
                'enable_parameter_tuning': self.config.enable_parameter_tuning,
                'enable_failover_management': self.config.enable_failover_management,
                'enable_signal_monitoring': self.config.enable_signal_monitoring,
                'enable_bootstrap_tracking': self.config.enable_bootstrap_tracking
            },
            'correlation_data': dict(self.correlation_data),
            'recent_optimizations': self.stats['optimization_cycles'],
            'system_alerts_summary': {
                'total': len(self.system_alerts),
                'by_type': self._summarize_alerts_by_type()
            }
        }

    def _summarize_alerts_by_type(self) -> Dict[str, int]:
        """Summarize alerts by type."""
        summary = {}
        for alert in self.system_alerts:
            alert_type = alert.get('type', 'unknown')
            summary[alert_type] = summary.get(alert_type, 0) + 1
        return summary
