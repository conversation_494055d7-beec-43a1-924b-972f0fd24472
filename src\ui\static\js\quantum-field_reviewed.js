// Revisado em 2025-06-13 por Codex
/**
 * QUALIA: Campo Quântico
 * 
 * Visualização do campo quântico subjacente ao QUALIA usando Canvas 2D
 * para criar um sistema de partículas que representa o tecido quântico-informacional.
 * 
 * Esta versão usa Canvas 2D nativo em vez de Three.js para maior compatibilidade.
 */

// Esperar pelo carregamento do DOM
document.addEventListener('DOMContentLoaded', function() {
    // Selecionar o container
    const container = document.getElementById('quantum-field');
    if (!container) return;
    
    // Criar canvas
    const canvas = document.createElement('canvas');
    canvas.width = container.clientWidth;
    canvas.height = container.clientHeight;
    container.appendChild(canvas);
    
    // Contexto 2D
    const ctx = canvas.getContext('2d');
    
    // Configuração das partículas
    const particleCount = 100;
    const particleDistance = 80;
    const particles = [];
    
    // Cores
    const particleColor = '#22AABB';
    const connectionColor = 'rgba(34, 170, 187, 0.2)';
    
    // Inicializar partículas
    function initParticles() {
        for (let i = 0; i < particleCount; i++) {
            particles.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                radius: Math.random() * 2 + 1,
                vx: (Math.random() - 0.5) * 0.5,
                vy: (Math.random() - 0.5) * 0.5,
                phase: Math.random() * Math.PI * 2
            });
        }
    }
    
    // Desenhar partículas e conexões
    function draw(time) {
        // Limpar canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Atualizar e desenhar partículas
        for (let i = 0; i < particles.length; i++) {
            const p = particles[i];
            
            // Movimento ondulatório quântico
            p.x += p.vx + Math.sin(time * 0.001 + p.phase) * 0.2;
            p.y += p.vy + Math.cos(time * 0.001 + p.phase) * 0.2;
            
            // Manter dentro dos limites
            if (p.x < 0) p.x = canvas.width;
            if (p.x > canvas.width) p.x = 0;
            if (p.y < 0) p.y = canvas.height;
            if (p.y > canvas.height) p.y = 0;
            
            // Desenhar partícula
            ctx.beginPath();
            ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
            ctx.fillStyle = particleColor;
            ctx.fill();
            
            // Desenhar conexões
            for (let j = i + 1; j < particles.length; j++) {
                const p2 = particles[j];
                const dx = p.x - p2.x;
                const dy = p.y - p2.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < particleDistance) {
                    // Opacidade baseada na distância
                    const opacity = 1 - distance / particleDistance;
                    
                    // Desenhar linha de conexão
                    ctx.beginPath();
                    ctx.moveTo(p.x, p.y);
                    ctx.lineTo(p2.x, p2.y);
                    ctx.strokeStyle = `rgba(34, 170, 187, ${opacity * 0.2})`;
                    ctx.lineWidth = opacity * 1;
                    ctx.stroke();
                }
            }
        }
    }
    
    // Loop de animação
    function animate(time) {
        requestAnimationFrame(animate);
        draw(time);
    }
    
    // Lidar com redimensionamento
    function handleResize() {
        canvas.width = container.clientWidth;
        canvas.height = container.clientHeight;
    }
    
    // Adicionar evento de redimensionamento
    window.addEventListener('resize', handleResize);
    
    // Inicializar e iniciar animação
    initParticles();
    animate(0);
});