from tests.stub_utils import install_stubs

install_stubs()

from qualia.config import get_encoder_class, create_encoder, encoder_registry
import src.ai.encoders_plugin as plugin
import importlib


def _reload_default_plugin(monkeypatch):
    """Reload the plugin with the default configuration."""
    monkeypatch.delenv("QUALIA_ENCODERS_CONFIG", raising=False)
    importlib.reload(encoder_registry)
    return importlib.reload(plugin)


def test_rsi_encoder_registered(monkeypatch):
    _reload_default_plugin(monkeypatch)
    cls = get_encoder_class("RSIPhaseEncoder")
    assert cls is plugin.RSIPhaseEncoder


def test_create_encoder_instance(monkeypatch):
    _reload_default_plugin(monkeypatch)
    enc = create_encoder("VolumeRatioAmplitudeEncoder", name="test")
    assert isinstance(enc, plugin.VolumeRatioAmplitudeEncoder)
