#!/usr/bin/env python
"""Generate Quantum Pattern Memory cache using warm-start patterns.

This utility populates :class:`QuantumPatternMemory` with synthetic
examples and persists the result to disk. Use it to precompute the
``qpm_memory.json`` cache offline so that runtime components can load
patterns without triggering warm-start on first use.
"""
from __future__ import annotations

import argparse
import os

from qualia.memory.quantum_pattern_memory import QuantumPatternMemory
from qualia.risk.manager import create_risk_manager

DEFAULT_PERSISTENCE_PATH = os.path.join("data", "cache", "qpm_memory.json")


def main() -> None:
    parser = argparse.ArgumentParser(
        description="Warm up Quantum Pattern Memory with synthetic patterns"
    )
    parser.add_argument(
        "--path", default=DEFAULT_PERSISTENCE_PATH, help="Output persistence file"
    )
    parser.add_argument(
        "--min-patterns",
        type=int,
        default=50,
        help="Minimum number of patterns to generate",
    )
    parser.add_argument(
        "--variations",
        type=int,
        default=15,
        help="Number of variations per warm-start scenario",
    )
    args = parser.parse_args()

    risk_manager = create_risk_manager(
        initial_capital=1000.0,
        risk_profile="balanced",
    )

    qpm = QuantumPatternMemory(
        persistence_path=args.path,
        enable_warmstart=True,
        warmstart_min_patterns=args.min_patterns,
        warmstart_variations=args.variations,
        auto_persist=False,
        risk_manager=risk_manager,
    )

    qpm.warm_start(risk_manager=risk_manager)
    qpm.save_to_file(args.path)
    total = sum(len(lst) for lst in qpm.memory.values())
    print(f"Generated {total} patterns and saved to {args.path}.")


if __name__ == "__main__":
    main()
