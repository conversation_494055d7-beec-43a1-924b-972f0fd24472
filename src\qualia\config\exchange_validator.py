"""
Exchange Configuration Validator for QUALIA System
Validates exchange configurations and credentials before system initialization.
"""

import os
import logging
from typing import Dict, Any, Optional, Tuple
import asyncio
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """Result of exchange validation"""
    is_valid: bool
    exchange_name: str
    error_message: Optional[str] = None
    warnings: list = None
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []


class ExchangeConfigValidator:
    """Validates exchange configurations and credentials"""
    
    def __init__(self):
        self.supported_exchanges = ['kucoin', 'kraken']
    
    def validate_config(self, config: Dict[str, Any]) -> ValidationResult:
        """
        Validate exchange configuration structure
        
        Args:
            config: Exchange configuration dictionary
            
        Returns:
            ValidationResult with validation status and details
        """
        try:
            # Check if exchanges config exists
            if 'exchanges' not in config:
                return ValidationResult(
                    is_valid=False,
                    exchange_name='unknown',
                    error_message="No 'exchanges' configuration found in config"
                )
            
            exchanges_config = config['exchanges']
            
            # Validate each configured exchange
            for exchange_name, exchange_config in exchanges_config.items():
                if exchange_name not in self.supported_exchanges:
                    logger.warning(f"Unsupported exchange: {exchange_name}")
                    continue
                
                result = self._validate_exchange_config(exchange_name, exchange_config)
                if not result.is_valid:
                    return result
            
            return ValidationResult(
                is_valid=True,
                exchange_name='all',
                warnings=[]
            )
            
        except Exception as e:
            logger.error(f"Error validating exchange config: {e}")
            return ValidationResult(
                is_valid=False,
                exchange_name='unknown',
                error_message=f"Configuration validation error: {str(e)}"
            )
    
    def _validate_exchange_config(self, exchange_name: str, config: Dict[str, Any]) -> ValidationResult:
        """Validate specific exchange configuration"""
        
        if exchange_name == 'kucoin':
            return self._validate_kucoin_config(config)
        elif exchange_name == 'kraken':
            return self._validate_kraken_config(config)
        else:
            return ValidationResult(
                is_valid=False,
                exchange_name=exchange_name,
                error_message=f"Unsupported exchange: {exchange_name}"
            )
    
    def _validate_kucoin_config(self, config: Dict[str, Any]) -> ValidationResult:
        """Validate KuCoin specific configuration"""
        warnings = []
        
        # Check if enabled
        if not config.get('enabled', False):
            return ValidationResult(
                is_valid=False,
                exchange_name='kucoin',
                error_message="KuCoin is not enabled in configuration"
            )
        
        # Check required credentials
        required_fields = ['api_key', 'api_secret', 'passphrase']
        missing_fields = []
        
        for field in required_fields:
            value = config.get(field)
            if not value:
                missing_fields.append(field)
            elif isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                # Environment variable reference
                env_var = value[2:-1]  # Remove ${ and }
                if not os.getenv(env_var):
                    missing_fields.append(f"{field} (env: {env_var})")
        
        if missing_fields:
            return ValidationResult(
                is_valid=False,
                exchange_name='kucoin',
                error_message=f"Missing KuCoin credentials: {', '.join(missing_fields)}"
            )
        
        # Check optional settings and provide warnings
        if config.get('sandbox', False):
            warnings.append("KuCoin is configured for sandbox mode")
        
        if config.get('rate_limit', 4.0) > 10.0:
            warnings.append("KuCoin rate limit is set higher than recommended (>10.0)")
        
        return ValidationResult(
            is_valid=True,
            exchange_name='kucoin',
            warnings=warnings
        )
    
    def _validate_kraken_config(self, config: Dict[str, Any]) -> ValidationResult:
        """Validate Kraken specific configuration"""
        warnings = []
        
        # Check if enabled
        if not config.get('enabled', False):
            return ValidationResult(
                is_valid=False,
                exchange_name='kraken',
                error_message="Kraken is not enabled in configuration"
            )
        
        # Check required credentials
        required_fields = ['api_key', 'api_secret']
        missing_fields = []
        
        for field in required_fields:
            value = config.get(field)
            if not value:
                missing_fields.append(field)
            elif isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                # Environment variable reference
                env_var = value[2:-1]  # Remove ${ and }
                if not os.getenv(env_var):
                    missing_fields.append(f"{field} (env: {env_var})")
        
        if missing_fields:
            return ValidationResult(
                is_valid=False,
                exchange_name='kraken',
                error_message=f"Missing Kraken credentials: {', '.join(missing_fields)}"
            )
        
        return ValidationResult(
            is_valid=True,
            exchange_name='kraken',
            warnings=warnings
        )
    
    async def validate_credentials(self, exchange_name: str, config: Dict[str, Any]) -> ValidationResult:
        """
        Validate exchange credentials by making a test API call
        
        Args:
            exchange_name: Name of the exchange
            config: Exchange configuration
            
        Returns:
            ValidationResult with credential validation status
        """
        try:
            if exchange_name == 'kucoin':
                return await self._validate_kucoin_credentials(config)
            elif exchange_name == 'kraken':
                return await self._validate_kraken_credentials(config)
            else:
                return ValidationResult(
                    is_valid=False,
                    exchange_name=exchange_name,
                    error_message=f"Credential validation not implemented for {exchange_name}"
                )
        except Exception as e:
            logger.error(f"Error validating {exchange_name} credentials: {e}")
            return ValidationResult(
                is_valid=False,
                exchange_name=exchange_name,
                error_message=f"Credential validation error: {str(e)}"
            )
    
    async def _validate_kucoin_credentials(self, config: Dict[str, Any]) -> ValidationResult:
        """Validate KuCoin credentials with API call"""
        try:
            import ccxt
            
            # Resolve environment variables
            api_key = self._resolve_env_var(config.get('api_key'))
            api_secret = self._resolve_env_var(config.get('api_secret'))
            passphrase = self._resolve_env_var(config.get('passphrase'))
            
            if not all([api_key, api_secret, passphrase]):
                return ValidationResult(
                    is_valid=False,
                    exchange_name='kucoin',
                    error_message="Missing KuCoin credentials after environment variable resolution"
                )
            
            # Create exchange instance
            exchange = ccxt.kucoin({
                'apiKey': api_key,
                'secret': api_secret,
                'password': passphrase,
                'sandbox': config.get('sandbox', False),
                'enableRateLimit': True,
                'timeout': config.get('timeout', 30.0) * 1000,  # ccxt expects milliseconds
            })
            
            # Test API call - get account info
            try:
                await exchange.fetch_balance()
                logger.info("✅ KuCoin credentials validated successfully")
                
                return ValidationResult(
                    is_valid=True,
                    exchange_name='kucoin',
                    warnings=[]
                )
                
            finally:
                await exchange.close()
                
        except Exception as e:
            logger.error(f"❌ KuCoin credential validation failed: {e}")
            return ValidationResult(
                is_valid=False,
                exchange_name='kucoin',
                error_message=f"KuCoin API validation failed: {str(e)}"
            )
    
    async def _validate_kraken_credentials(self, config: Dict[str, Any]) -> ValidationResult:
        """Validate Kraken credentials with API call"""
        try:
            import ccxt
            
            # Resolve environment variables
            api_key = self._resolve_env_var(config.get('api_key'))
            api_secret = self._resolve_env_var(config.get('api_secret'))
            
            if not all([api_key, api_secret]):
                return ValidationResult(
                    is_valid=False,
                    exchange_name='kraken',
                    error_message="Missing Kraken credentials after environment variable resolution"
                )
            
            # Create exchange instance
            exchange = ccxt.kraken({
                'apiKey': api_key,
                'secret': api_secret,
                'enableRateLimit': True,
                'timeout': config.get('timeout', 30.0) * 1000,  # ccxt expects milliseconds
            })
            
            # Test API call
            try:
                await exchange.fetch_balance()
                logger.info("✅ Kraken credentials validated successfully")
                
                return ValidationResult(
                    is_valid=True,
                    exchange_name='kraken',
                    warnings=[]
                )
                
            finally:
                await exchange.close()
                
        except Exception as e:
            logger.error(f"❌ Kraken credential validation failed: {e}")
            return ValidationResult(
                is_valid=False,
                exchange_name='kraken',
                error_message=f"Kraken API validation failed: {str(e)}"
            )
    
    def _resolve_env_var(self, value: str) -> Optional[str]:
        """Resolve environment variable reference"""
        if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
            env_var = value[2:-1]  # Remove ${ and }
            return os.getenv(env_var)
        return value


# Convenience function for quick validation
async def validate_exchange_config(config: Dict[str, Any], validate_credentials: bool = True) -> Tuple[bool, list]:
    """
    Validate exchange configuration and optionally credentials
    
    Args:
        config: Full system configuration
        validate_credentials: Whether to validate credentials with API calls
        
    Returns:
        Tuple of (is_valid, list_of_results)
    """
    validator = ExchangeConfigValidator()
    results = []
    
    # Validate configuration structure
    config_result = validator.validate_config(config)
    results.append(config_result)
    
    if not config_result.is_valid:
        return False, results
    
    # Validate credentials if requested
    if validate_credentials and 'exchanges' in config:
        for exchange_name, exchange_config in config['exchanges'].items():
            if exchange_config.get('enabled', False):
                cred_result = await validator.validate_credentials(exchange_name, exchange_config)
                results.append(cred_result)
                
                if not cred_result.is_valid:
                    return False, results
    
    return True, results
