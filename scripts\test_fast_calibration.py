#!/usr/bin/env python3
"""
Teste Rápido do FWH Scalp Calibrator
Demonstra o novo modo 'fast' com apenas 27 combinações
"""

import asyncio
import sys
from pathlib import Path

# Adicionar o diretório scripts ao path
sys.path.append(str(Path(__file__).parent))

from fwh_scalp_calibrator import FWHScalpCalibrator, ParameterSpace

async def test_fast_mode():
    """Testa o modo rápido de calibração."""
    
    print("🚀 TESTE RÁPIDO - FWH Scalp Calibrator")
    print("=" * 50)
    
    # Testar o espaço de parâmetros em modo fast
    print("\n📊 Testando espaço de parâmetros...")
    
    # Modo fast
    param_space_fast = ParameterSpace(mode="fast")
    combinations_fast = param_space_fast.generate_combinations()
    print(f"⚡ Modo FAST: {len(combinations_fast)} combinações")
    
    # Modo balanced
    param_space_balanced = ParameterSpace(mode="balanced")
    combinations_balanced = param_space_balanced.generate_combinations()
    print(f"⚖️ Modo BALANCED: {len(combinations_balanced)} combinações")
    
    # Modo full (apenas contar, não gerar)
    param_space_full = ParameterSpace(mode="full")
    total_full = param_space_full.get_total_combinations()
    print(f"🔬 Modo FULL: {total_full} combinações (não geradas)")
    
    print("\n✅ Teste de espaço de parâmetros concluído!")
    print(f"\n💡 Recomendação: Use modo 'fast' para testes rápidos (5-15 min)")
    print(f"💡 Use modo 'balanced' para calibração normal (30-60 min)")
    print(f"💡 Use modo 'full' apenas para calibração completa (3-8 horas)")
    
    # Mostrar algumas combinações do modo fast
    print("\n📋 Primeiras 5 combinações do modo FAST:")
    for i, combo in enumerate(combinations_fast[:5]):
        print(f"  {i+1}. {combo}")
    
    print("\n🎯 Sistema otimizado e pronto para uso!")
    print("\n📝 Para executar calibração completa:")
    print("   python fwh_scalp_calibrator.py --mode fast --demo-mode")
    print("   python fwh_scalp_calibrator.py --mode balanced --demo-mode")
    print("   python fwh_scalp_calibrator.py --mode full --demo-mode")

if __name__ == "__main__":
    asyncio.run(test_fast_mode())