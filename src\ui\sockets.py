from __future__ import annotations

"""WebSocket utilities for QUALIA Flask apps."""

from flask import Flask, request
from flask_socketio import Namespace, SocketIO
import uuid

from qualia.utils.config import load_config
from qualia.utils.statsd_client import get_statsd_client
from qualia.common_types import SnapshotPayload
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

_ui_cfg = load_config("config/ui.yaml")
_socket_cfg = _ui_cfg.get("socketio", {})
socketio = SocketIO(**_socket_cfg)
_statsd = get_statsd_client()
_connections: dict[str, str] = {}


class HolographicNamespace(Namespace):
    """Namespace for streaming holographic snapshots."""

    def on_connect(self) -> None:
        trace_id = uuid.uuid4().hex
        _connections[request.sid] = trace_id
        logger.info(
            "TraceID: %s - Cliente conectado ao namespace /ws/holographic",
            trace_id,
        )
        if _statsd:
            tags = [f"trace_id:{trace_id}"]
            _statsd.increment("ui.socketio.connected", tags=tags)
            _statsd.gauge("ui.socketio.active_connections", len(_connections))

    def on_disconnect(self, sid=None) -> None:
        trace_id = _connections.pop(sid or request.sid, uuid.uuid4().hex)
        logger.info(
            "TraceID: %s - Cliente %s desconectado do namespace /ws/holographic",
            trace_id,
            sid or "",
        )
        if _statsd:
            tags = [f"trace_id:{trace_id}"]
            _statsd.increment("ui.socketio.disconnected", tags=tags)
            _statsd.gauge("ui.socketio.active_connections", len(_connections))

    def on_request_initial_data(self) -> None:
        """Handler for when client requests the initial state."""
        from qualia.ui.initialize import get_qualia_state

        trace_id = _connections.get(request.sid) or uuid.uuid4().hex
        logger.debug(
            "TraceID: %s - Cliente solicitou dados iniciais do snapshot.", trace_id
        )
        state = get_qualia_state()
        if state and state.consciousness:
            snapshot = state.consciousness.get_metrics_dict()
            publish_snapshot(snapshot, trace_id=trace_id)


def init_socketio(app: Flask) -> SocketIO:
    """Initialize ``SocketIO`` on *app* and register the holographic namespace."""

    socketio.init_app(app)
    socketio.on_namespace(HolographicNamespace("/ws/holographic"))
    return socketio


def publish_snapshot(
    snapshot: SnapshotPayload | dict, trace_id: str | None = None
) -> None:
    """Emit *snapshot* to connected clients."""

    if isinstance(snapshot, SnapshotPayload):
        payload = snapshot.model_dump()
    else:
        payload = snapshot

    if getattr(socketio, "server", None):  # pragma: no cover - runtime check
        socketio.emit("snapshot", payload, namespace="/ws/holographic")
        if _statsd:
            tags = [f"trace_id:{trace_id}"] if trace_id else None
            _statsd.increment("ui.socketio.snapshot_sent", tags=tags)
        logger.debug("TraceID: %s - Snapshot enviado", trace_id)
