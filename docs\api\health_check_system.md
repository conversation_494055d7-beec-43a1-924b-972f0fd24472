# Sistema de Health Check do QUALIA

## Visão Geral

O Sistema de Health Check do QUALIA é um componente crítico que monitora a saúde e readiness de todos os componentes do sistema em tempo real. Ele fornece relatórios detalhados, detecta problemas automaticamente e pode tomar ações corretivas.

## Funcionalidades Principais

### 🔍 Monitoramento de Componentes
- **Data Collector**: Verifica conexões ativas, atualização de dados e quantidade de dados disponíveis
- **Quantum Layer**: Monitora inicialização e processamento de padrões quânticos
- **Holographic Universe**: Analisa energia do campo e padrões holográficos detectados
- **Trading Strategies**: Verifica status de readiness de cada estratégia por símbolo
- **Risk Manager**: Monitora configurações de risco, capital disponível e posições abertas

### 📊 Relatórios e Métricas
- **Readiness Score**: Pontuação de 0-100% indicando prontidão do sistema
- **Component Status**: Status detalhado de cada componente (HEALTHY, WARNING, ERROR, etc.)
- **Critical Issues**: Lista de problemas críticos que impedem operação
- **Warnings**: Alertas sobre problemas não críticos
- **Recommendations**: Sugestões automáticas para correção de problemas

### 🔄 Monitoramento Contínuo
- Verificações automáticas em intervalos configuráveis
- Callbacks customizáveis para eventos de saúde
- Histórico de verificações para análise de tendências
- Detecção de mudanças de estado em tempo real

## Arquitetura

### Classes Principais

#### `SystemHealthChecker`
Classe principal responsável pela verificação de saúde dos componentes.

```python
from qualia.monitoring.health_check import SystemHealthChecker

# Criar checker
checker = SystemHealthChecker()

# Registrar componentes
checker.register_component("data_collector", data_collector_instance)
checker.register_component("strategies", strategies_dict)

# Executar verificação
report = await checker.get_readiness_report()
```

#### `ComponentHealthInfo`
Estrutura que contém informações detalhadas sobre a saúde de um componente.

```python
@dataclass
class ComponentHealthInfo:
    name: str
    status: ComponentStatus
    message: str
    details: Dict[str, Any]
    last_check: datetime
    uptime_seconds: Optional[float]
    error_count: int
    warning_count: int
```

#### `ReadinessReport`
Relatório completo de readiness do sistema.

```python
@dataclass
class ReadinessReport:
    overall_status: SystemReadiness
    readiness_score: float  # 0.0 a 1.0
    components: Dict[str, ComponentHealthInfo]
    critical_issues: List[str]
    warnings: List[str]
    recommendations: List[str]
    timestamp: datetime
```

### Enums de Status

#### `ComponentStatus`
```python
class ComponentStatus(Enum):
    HEALTHY = "healthy"
    WARNING = "warning"
    ERROR = "error"
    NOT_INITIALIZED = "not_initialized"
    INITIALIZING = "initializing"
    DEGRADED = "degraded"
    OFFLINE = "offline"
```

#### `SystemReadiness`
```python
class SystemReadiness(Enum):
    READY = "ready"
    NOT_READY = "not_ready"
    DEGRADED_READY = "degraded_ready"
    INITIALIZING = "initializing"
    CRITICAL_ERROR = "critical_error"
```

## Uso Básico

### Verificação Standalone

```python
import asyncio
from qualia.monitoring.health_check import SystemHealthChecker

async def basic_health_check():
    # Criar checker
    checker = SystemHealthChecker()
    
    # Registrar componentes
    checker.register_component("my_component", component_instance)
    
    # Executar verificação
    report = await checker.get_readiness_report()
    
    # Imprimir relatório
    checker.print_health_report(report)
    
    # Verificar se está pronto para trading
    if report.is_ready_for_trading:
        print("✅ Sistema pronto para trading!")
    else:
        print("❌ Sistema não está pronto")
        print(f"Problemas: {report.critical_issues}")

# Executar
asyncio.run(basic_health_check())
```

### Monitoramento Contínuo

```python
from qualia.monitoring.health_check import continuous_health_monitoring

async def health_callback(report):
    if report.overall_status == SystemReadiness.CRITICAL_ERROR:
        print("🚨 ALERTA: Sistema em estado crítico!")
        # Tomar ação corretiva
        await handle_critical_error(report)

# Iniciar monitoramento
await continuous_health_monitoring(
    system_components=components,
    interval_seconds=60,
    callback=health_callback
)
```

### Integração com Sistema Manager

```python
from qualia.core.system_manager import QUALIASystemManager

async def integrated_system():
    config = {
        "health_check_interval": 30,
        "auto_corrections_enabled": True,
        "require_all_healthy_for_trading": False
    }
    
    # Criar system manager
    manager = QUALIASystemManager(config)
    
    # Registrar callback de saúde
    async def health_callback(report):
        if len(report.critical_issues) > 0:
            print(f"⚠️ {len(report.critical_issues)} problemas detectados")
    
    manager.register_health_callback(health_callback)
    
    # Inicializar sistema
    if await manager.initialize_system():
        print("✅ Sistema inicializado com sucesso!")
        
        # Verificar readiness
        if manager.is_ready_for_trading():
            print("🚀 Pronto para trading!")
        
        # Obter relatório atual
        report = await manager.get_current_health_report()
        manager.health_checker.print_health_report(report)
```

## Verificações Específicas por Componente

### Data Collector
- **Conexões Ativas**: Verifica se há conexões com fontes de dados
- **Última Atualização**: Monitora tempo desde a última atualização
- **Quantidade de Dados**: Verifica se há dados suficientes disponíveis
- **Qualidade dos Dados**: Valida integridade dos dados coletados

### Quantum Layer
- **Inicialização**: Verifica se a camada quântica foi inicializada
- **Processamento**: Monitora avaliações quânticas recentes
- **Cache de Padrões**: Verifica tamanho do cache de padrões quânticos
- **Assinatura Quântica**: Valida disponibilidade de assinaturas válidas

### Holographic Universe
- **Energia do Campo**: Monitora energia do campo holográfico
- **Padrões Detectados**: Conta padrões holográficos ativos
- **Última Análise**: Verifica tempo desde última análise
- **Coerência**: Valida coerência do universo holográfico

### Trading Strategies
- **Status Individual**: Verifica readiness de cada estratégia
- **Dados Históricos**: Confirma disponibilidade de dados necessários
- **Parâmetros**: Valida configurações das estratégias
- **Performance**: Monitora performance recente

### Risk Manager
- **Configurações**: Verifica configurações de risco válidas
- **Capital Disponível**: Monitora capital disponível para trading
- **Posições Abertas**: Conta e valida posições abertas
- **Limites**: Verifica se limites de risco estão sendo respeitados

## Configuração

### Pesos dos Componentes
```python
component_weights = {
    "data_collector": 0.25,      # 25% - Crítico para dados
    "quantum_layer": 0.15,       # 15% - Importante mas opcional
    "holographic_universe": 0.15, # 15% - Importante mas opcional
    "strategies": 0.25,          # 25% - Crítico para trading
    "risk_manager": 0.20         # 20% - Crítico para segurança
}
```

### Thresholds de Readiness
- **READY**: Score ≥ 90%
- **DEGRADED_READY**: Score ≥ 60%
- **NOT_READY**: Score < 60%
- **CRITICAL_ERROR**: Componentes críticos com erro

### Intervalos de Verificação
- **Padrão**: 60 segundos
- **Modo Teste**: 30 segundos
- **Modo Produção**: 120 segundos

## Tratamento de Erros

### Correções Automáticas
O sistema pode tentar correções automáticas para problemas comuns:

```python
async def _attempt_auto_corrections(self, report):
    for name, info in report.components.items():
        if info.status == ComponentStatus.NOT_INITIALIZED:
            try:
                await self._reinitialize_component(name)
            except Exception as e:
                logger.warning(f"Falha ao reinicializar {name}: {e}")
```

### Fallbacks
- **Modo Degradado**: Operação com funcionalidade reduzida
- **Componentes Opcionais**: Continuar sem componentes não críticos
- **Restart Automático**: Reinicialização automática em casos críticos

## Métricas e Logging

### Métricas Coletadas
- **Readiness Score histórico**
- **Tempo de resposta das verificações**
- **Frequência de problemas por componente**
- **Tempo de recuperação de falhas**

### Níveis de Log
- **INFO**: Status normal e mudanças de estado
- **WARNING**: Problemas não críticos detectados
- **ERROR**: Problemas críticos que impedem operação
- **DEBUG**: Detalhes de verificação para debugging

## Callbacks e Eventos

### Registrar Callbacks
```python
async def my_health_callback(report: ReadinessReport):
    if report.overall_status == SystemReadiness.CRITICAL_ERROR:
        # Enviar alerta
        await send_alert("Sistema em estado crítico!")
    
    # Log mudanças significativas
    if hasattr(my_health_callback, 'last_score'):
        score_change = report.readiness_score - my_health_callback.last_score
        if abs(score_change) > 0.1:  # Mudança > 10%
            logger.info(f"Score mudou significativamente: {score_change:+.2%}")
    
    my_health_callback.last_score = report.readiness_score

# Registrar callback
checker.register_health_callback(my_health_callback)
```

### Eventos Disponíveis
- **Component Status Change**: Mudança de status de componente
- **Readiness Score Change**: Mudança significativa no score
- **Critical Error Detected**: Detecção de erro crítico
- **System Recovery**: Recuperação de problema crítico

## Boas Práticas

### 1. Verificação Regular
```python
# Verificar saúde antes de operações críticas
async def safe_trading_operation():
    report = await health_checker.get_readiness_report()
    
    if not report.is_ready_for_trading:
        logger.warning("Sistema não está pronto para trading")
        return False
    
    # Proceder com operação
    return await execute_trading_operation()
```

### 2. Monitoramento Proativo
```python
# Monitorar tendências de degradação
def analyze_health_trends(checker):
    recent_reports = checker.check_history[-10:]  # Últimos 10 reports
    
    scores = [r.readiness_score for r in recent_reports]
    if len(scores) >= 5:
        trend = (scores[-1] - scores[0]) / len(scores)
        if trend < -0.02:  # Degradação > 2% por verificação
            logger.warning("Tendência de degradação detectada!")
```

### 3. Alertas Inteligentes
```python
# Evitar spam de alertas
class SmartAlerting:
    def __init__(self):
        self.last_alert = {}
        self.alert_cooldown = 300  # 5 minutos
    
    async def send_alert_if_needed(self, issue_type, message):
        now = time.time()
        last = self.last_alert.get(issue_type, 0)
        
        if now - last > self.alert_cooldown:
            await send_alert(message)
            self.last_alert[issue_type] = now
```

## Troubleshooting

### Problemas Comuns

#### "QuantumMetacognitionLayer não encontrada"
```python
# Solução: Verificar inicialização da metacognição
if not hasattr(metacognition, 'meta_layer') or metacognition.meta_layer is None:
    metacognition.meta_layer = QuantumMetacognitionLayer(config)
```

#### "Data collector não encontrado"
```python
# Solução: Registrar data collector corretamente
checker.register_component("data_collector", real_data_collector)
```

#### "Score de readiness muito baixo"
```python
# Solução: Verificar componentes críticos
critical_components = ["data_collector", "strategies", "risk_manager"]
for comp in critical_components:
    if comp not in report.components or not report.components[comp].is_operational:
        logger.error(f"Componente crítico {comp} não operacional")
```

### Debug Mode
```python
# Ativar logging detalhado
import logging
logging.getLogger("qualia.monitoring.health_check").setLevel(logging.DEBUG)

# Verificar detalhes de cada componente
for name, info in report.components.items():
    print(f"{name}: {info.status.value} - {info.message}")
    print(f"  Detalhes: {info.details}")
```

## Extensibilidade

### Adicionar Novos Componentes
```python
class MyCustomComponent:
    def is_healthy(self):
        return self.status == "operational"
    
    def get_status(self):
        return {"status": "ok", "custom_metric": 42}

# Registrar componente customizado
checker.register_component("my_component", MyCustomComponent())
```

### Verificações Customizadas
```python
class CustomHealthChecker(SystemHealthChecker):
    async def _check_custom_component(self):
        # Implementar verificação específica
        return ComponentHealthInfo(
            name="custom",
            status=ComponentStatus.HEALTHY,
            message="Componente customizado OK"
        )
```

## Conclusão

O Sistema de Health Check do QUALIA fornece monitoramento robusto e confiável de todos os componentes críticos do sistema. Com relatórios detalhados, correções automáticas e monitoramento contínuo, ele garante que o sistema opere de forma segura e eficiente.

Para mais informações, consulte os exemplos em `scripts/` e os testes em `tests/monitoring/`. 