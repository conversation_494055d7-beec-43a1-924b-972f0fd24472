import time

from qualia.memory.event_bus import SimpleEventBus
from qualia.analysis.threshold_calibrator import AdaptiveThresholdCalibrator
from qualia.events import CrossModalCoherenceEvent, CoherenceAlertEvent


def test_threshold_calibration_and_alert(monkeypatch):
    bus = SimpleEventBus()
    alerts = []

    def _collect(payload):
        alerts.append(payload)

    bus.subscribe("nexus.alert", _collect)
    calib = AdaptiveThresholdCalibrator(
        bus, alpha=1.0, offset=0.05
    )  # instant update for test

    # publish coherence below threshold bootstrap (0.3 + 0.05 = 0.35) → no alert
    bus.publish("nexus.cross_modal_coherence", CrossModalCoherenceEvent(coherence=0.3))
    assert not alerts
    # next coherence above new threshold (0.8 + 0.05 clipped to 0.85) but calib threshold is 0.35 *done*
    bus.publish("nexus.cross_modal_coherence", CrossModalCoherenceEvent(coherence=0.9))
    assert alerts, "Alert should be triggered when coherence exceeds threshold"
    assert isinstance(alerts[-1], CoherenceAlertEvent)
    assert alerts[-1].coherence >= alerts[-1].threshold
