"""Plugin wrappers for QuantumEncoder classes.

This module exposes select encoders from :mod:`src.qualia.core.encoders` so they
can be discovered via configuration without importing the entire core module.
The classes here do not change behaviour; they merely re-export the base
implementations.

Additionally, encoder registration can be toggled via ``config/encoders.yaml``
and the initialization time of each instance is recorded using ``statsd.timer``
and ``statsd.increment`` for structured metrics. When an encoder is created,
the event ``encoder.created`` is published to an optional
:class:`~src.qualia.memory.event_bus.SimpleEventBus`.
"""

# Revisado em 2025-06-13 por Codex

from __future__ import annotations

import os
from pathlib import Path
from typing import Any, Dict, Optional, TYPE_CHECKING
from contextlib import nullcontext

import yaml

try:
    from qualia.core.encoders import RSIPhaseEncoder as CoreRSIPhaseEncoder
    from qualia.core.encoders import (
        VolumeRatioAmplitudeEncoder as CoreVolumeRatioAmplitudeEncoder,
    )
except Exception as exc:  # pragma: no cover - core package missing
    raise ImportError(
        "RSIPhaseEncoder and VolumeRatioAmplitudeEncoder must be available from"
        " 'qualia.core.encoders'. Ensure QUALIA core modules are installed."
    ) from exc


from qualia.config import register_encoder, unregister_encoder
from qualia.utils.logger import get_logger
from qualia.events import (
    EncoderStartedEvent,
    EncoderFinishedEvent,
)
from .hardware_hooks import check_hardware_status

if TYPE_CHECKING:  # pragma: no cover - imported for type hints only
    from datadog import DogStatsd
    from qualia.memory.event_bus import SimpleEventBus

logger = get_logger(__name__)

# Simple metrics: count how many encoder instances are created locally and
# emit StatsD increments when possible.
_INSTANCE_COUNTER: dict[str, int] = {}


_DEFAULT_CONFIG_PATH = Path(__file__).resolve().parents[2] / "config" / "encoders.yaml"


def _load_encoder_config(path: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
    """Load encoder parameters and feature flags from YAML."""

    config_path = path or os.getenv("QUALIA_ENCODERS_CONFIG", str(_DEFAULT_CONFIG_PATH))
    try:
        with open(config_path, "r", encoding="utf-8") as fh:
            data = yaml.safe_load(fh) or {}
        if not isinstance(data, dict):
            logger.error("Arquivo de encoders invalido: %s", config_path)
            return {}
        normalized: Dict[str, Dict[str, Any]] = {}
        for key, value in data.items():
            if isinstance(value, dict):
                normalized[key] = value
            else:
                normalized[key] = {"enabled": bool(value)}
        return normalized
    except FileNotFoundError:
        logger.warning("Arquivo de encoders nao encontrado em %s", config_path)
    except Exception as exc:  # pragma: no cover - unexpected errors
        logger.error(
            "Falha ao carregar configuracao de encoders %s: %s", config_path, exc
        )
    return {}


_ENCODER_CONFIG = _load_encoder_config()


def _encoder_enabled(name: str) -> bool:
    config = _ENCODER_CONFIG.get(name, {})
    return bool(config.get("enabled", True))


def _feature_enabled(name: str, feature: str, default: bool = False) -> bool:
    config = _ENCODER_CONFIG.get(name, {})
    return bool(config.get(feature, default))


def _INCREMENT_COUNTER(name: str) -> None:
    _INSTANCE_COUNTER[name] = _INSTANCE_COUNTER.get(name, 0) + 1


class RSIPhaseEncoder(CoreRSIPhaseEncoder):
    """Wrapper around :class:`~src.qualia.core.encoders.RSIPhaseEncoder`.

    Examples
    --------
    >>> from ai.encoders_plugin import RSIPhaseEncoder
    >>> enc = RSIPhaseEncoder(name="rsi")
    >>> vector = enc.encode({"rsi": 55.0})
    """

    def __init__(
        self,
        *args: Any,
        statsd_client: Optional["DogStatsd"] = None,
        event_bus: Optional["SimpleEventBus"] = None,
        use_hardware_acceleration: Optional[bool] = None,
        **kwargs: Any,
    ) -> None:
        logger.debug("Creating RSIPhaseEncoder wrapper instance")
        self.statsd = statsd_client
        self.event_bus = event_bus
        config_flag = _feature_enabled(
            self.__class__.__name__, "use_hardware_acceleration", False
        )
        if use_hardware_acceleration is None:
            hw = check_hardware_status()
            self.use_hardware_acceleration = config_flag and (
                hw.get("gpu") or hw.get("qpu")
            )
        else:
            self.use_hardware_acceleration = bool(use_hardware_acceleration)
        timer_cm = (
            self.statsd.timer(
                "encoder.init_ms", tags=[f"class:{self.__class__.__name__}"]
            )
            if self.statsd
            else nullcontext()
        )
        with timer_cm:
            super().__init__(
                *args,
                statsd_client=statsd_client,
                event_bus=event_bus,
                use_hardware_acceleration=self.use_hardware_acceleration,
                **kwargs,
            )
        _INCREMENT_COUNTER("RSIPhaseEncoder")
        if self.statsd:
            self.statsd.increment(
                "encoder.instances", tags=[f"class:{self.__class__.__name__}"]
            )
        if self.event_bus:
            payload = EncoderStartedEvent(name=self.name, cls=self.__class__.__name__)
            self.event_bus.publish("encoder.created", payload)
            # Signal that initialization completed successfully so listeners can
            # react immediately after construction.
            self.event_bus.publish(
                "encoder.finished",
                EncoderFinishedEvent(name=self.name, cls=self.__class__.__name__),
            )

    def encode(self, snapshot_or_batch: Any) -> Any:
        try:
            result = super().encode(snapshot_or_batch)
        except Exception as exc:  # pragma: no cover - error path tested separately
            if self.event_bus:
                self.event_bus.publish(
                    "encoder.failed",
                    {
                        "name": self.name,
                        "class": self.__class__.__name__,
                        "error": str(exc),
                    },
                )
            raise
        else:
            if self.event_bus and _feature_enabled(
                self.__class__.__name__, "emit_events", True
            ):
                self.event_bus.publish(
                    "encoder.processed",
                    {"name": self.name, "class": self.__class__.__name__},
                )
        finally:
            if self.event_bus:
                self.event_bus.publish(
                    "encoder.finished",
                    EncoderFinishedEvent(name=self.name, cls=self.__class__.__name__),
                )
        return result


class VolumeRatioAmplitudeEncoder(CoreVolumeRatioAmplitudeEncoder):
    """Wrapper around :class:`~src.qualia.core.encoders.VolumeRatioAmplitudeEncoder`.

    Examples
    --------
    >>> from ai.encoders_plugin import VolumeRatioAmplitudeEncoder
    >>> enc = VolumeRatioAmplitudeEncoder(name="vra")
    >>> vector = enc.encode({"volume_ratio": 0.8})
    """

    def __init__(
        self,
        *args: Any,
        statsd_client: Optional["DogStatsd"] = None,
        event_bus: Optional["SimpleEventBus"] = None,
        use_hardware_acceleration: Optional[bool] = None,
        **kwargs: Any,
    ) -> None:
        logger.debug("Creating VolumeRatioAmplitudeEncoder wrapper instance")
        self.statsd = statsd_client
        self.event_bus = event_bus
        config_flag = _feature_enabled(
            self.__class__.__name__, "use_hardware_acceleration", False
        )
        if use_hardware_acceleration is None:
            hw = check_hardware_status()
            self.use_hardware_acceleration = config_flag and (
                hw.get("gpu") or hw.get("qpu")
            )
        else:
            self.use_hardware_acceleration = bool(use_hardware_acceleration)
        timer_cm = (
            self.statsd.timer(
                "encoder.init_ms", tags=[f"class:{self.__class__.__name__}"]
            )
            if self.statsd
            else nullcontext()
        )
        with timer_cm:
            super().__init__(
                *args,
                statsd_client=statsd_client,
                event_bus=event_bus,
                use_hardware_acceleration=self.use_hardware_acceleration,
                **kwargs,
            )
        _INCREMENT_COUNTER("VolumeRatioAmplitudeEncoder")
        if self.statsd:
            self.statsd.increment(
                "encoder.instances", tags=[f"class:{self.__class__.__name__}"]
            )
        if self.event_bus:
            payload = EncoderStartedEvent(name=self.name, cls=self.__class__.__name__)
            self.event_bus.publish("encoder.created", payload)
            # Publish a completion event to enable external tracking of encoder
            # readiness immediately after instantiation.
            self.event_bus.publish(
                "encoder.finished",
                EncoderFinishedEvent(name=self.name, cls=self.__class__.__name__),
            )

    def encode(self, snapshot_or_batch: Any) -> Any:
        try:
            result = super().encode(snapshot_or_batch)
        except Exception as exc:  # pragma: no cover - error path tested separately
            if self.event_bus:
                self.event_bus.publish(
                    "encoder.failed",
                    {
                        "name": self.name,
                        "class": self.__class__.__name__,
                        "error": str(exc),
                    },
                )
            raise
        else:
            if self.event_bus and _feature_enabled(
                self.__class__.__name__, "emit_events", True
            ):
                self.event_bus.publish(
                    "encoder.processed",
                    {"name": self.name, "class": self.__class__.__name__},
                )
        finally:
            if self.event_bus:
                self.event_bus.publish(
                    "encoder.finished",
                    EncoderFinishedEvent(name=self.name, cls=self.__class__.__name__),
                )
        return result


if _encoder_enabled("RSIPhaseEncoder"):
    register_encoder("RSIPhaseEncoder", RSIPhaseEncoder)
else:
    unregister_encoder("RSIPhaseEncoder")

if _encoder_enabled("VolumeRatioAmplitudeEncoder"):
    register_encoder("VolumeRatioAmplitudeEncoder", VolumeRatioAmplitudeEncoder)
else:
    unregister_encoder("VolumeRatioAmplitudeEncoder")


__all__ = ["RSIPhaseEncoder", "VolumeRatioAmplitudeEncoder"]
