# Compression Operator

O operador de *Compression* aplica técnicas quânticas para reduzir a
dimensão e a entropia de vetores de entrada. Ele utiliza PCA e
transformadas wavelet como aproximações clássicas, selecionando o
resultado que produz menor entropia.

Principais características
-------------------------
- Seleção automática entre PCA e wavelet com base na entropia resultante.
- Configuração de dimensão alvo e níveis da wavelet.
- Implementação assíncrona compatível com os demais operadores do sistema.
