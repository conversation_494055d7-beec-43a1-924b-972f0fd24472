# Estratégia No-Op

O utilitário `load_strategy_class` em `qualia.strategies.strategy_utils` cria uma
classe temporária quando o alias solicitado não é encontrado. Essa `_TemporaryStrategy`
agora funciona como uma estratégia **no-op**: durante a inicialização, um aviso
é registrado e todo dado de mercado recebido é apenas armazenado em
`received_market_data`.

Os métodos `analyze_market` e `backtest` retornam resultados estruturados sem
executar negociações, permitindo testes ou execuções degradadas quando uma
estratégia real não está disponível.
