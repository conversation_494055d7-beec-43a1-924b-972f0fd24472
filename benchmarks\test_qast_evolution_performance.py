import random
import pytest

pytest.importorskip("pytest_benchmark")


class DummyEvolutionEngine:
    def __init__(self, population_size: int = 8):
        self.population = [0.0 for _ in range(population_size)]
        self.generation = 0

    def evolve_once(self) -> None:
        self.population = [val + random.random() * 0.01 for val in self.population]
        self.generation += 1


def test_qast_evolution_generation_time(benchmark):
    engine = DummyEvolutionEngine(population_size=8)
    benchmark(engine.evolve_once)
    assert engine.generation >= 1
