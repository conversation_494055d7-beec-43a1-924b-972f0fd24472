#!/usr/bin/env python3
"""
Test Real QUALIA Components Integration - P-02.3 Phase 2
Validates that real QUALIA components are properly integrated and functional
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add src to path for QUALIA imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_real_components_integration():
    """Test real QUALIA components integration"""
    logger.info("🧪 Starting Real QUALIA Components Integration Test...")
    
    try:
        # Import the pilot system
        from qualia_pilot_trading_system import QUALIAPilotTradingSystem
        
        # Initialize pilot system
        pilot = QUALIAPilotTradingSystem("config/pilot_config.yaml")
        
        # Test 1: Configuration Loading
        logger.info("📋 Test 1: Configuration Loading...")
        config_loaded = await pilot.load_configuration()
        if config_loaded:
            logger.info("✅ Configuration loaded successfully")
        else:
            logger.error("❌ Configuration loading failed")
            return False
        
        # Test 2: Component Initialization
        logger.info("🧠 Test 2: QUALIA Components Initialization...")
        components_initialized = await pilot.initialize_qualia_components()
        if components_initialized:
            logger.info("✅ QUALIA components initialized successfully")
        else:
            logger.error("❌ QUALIA components initialization failed")
            return False
        
        # Test 3: Check Component Types
        logger.info("🔍 Test 3: Component Type Validation...")
        
        # Check if real components are loaded
        if hasattr(pilot.enhanced_data_collector, 'collect_enhanced_market_data'):
            logger.info("✅ Real EnhancedDataCollector detected")
        else:
            logger.info("ℹ️ Mock EnhancedDataCollector in use")
        
        if hasattr(pilot.oracle_decision_engine, 'generate_decisions'):
            logger.info("✅ Real QASTOracleDecisionEngine detected")
        else:
            logger.info("ℹ️ Mock QASTOracleDecisionEngine in use")
        
        # Test 4: Data Collection Test
        logger.info("📊 Test 4: Data Collection Test...")
        try:
            if hasattr(pilot.enhanced_data_collector, 'collect_enhanced_market_data'):
                # Test real data collection
                enhanced_data = await pilot.enhanced_data_collector.collect_enhanced_market_data()
                if enhanced_data:
                    logger.info(f"✅ Real data collection successful - {len(enhanced_data)} data points")
                    
                    # Log first data point details
                    first_data = enhanced_data[0]
                    logger.info(f"📈 Sample data: Symbol={first_data.symbol}, Price=${first_data.price:.2f}")
                else:
                    logger.warning("⚠️ Real data collection returned empty results")
            else:
                # Test mock data collection
                market_data = await pilot.enhanced_data_collector.collect_market_data()
                if market_data:
                    logger.info("✅ Mock data collection successful")
                else:
                    logger.warning("⚠️ Mock data collection failed")
        except Exception as e:
            logger.error(f"❌ Data collection test failed: {e}")
        
        # Test 5: Oracle Decision Test
        logger.info("🔮 Test 5: Oracle Decision Test...")
        try:
            if hasattr(pilot.oracle_decision_engine, 'generate_decisions'):
                # Test real oracle decisions
                enhanced_data = await pilot.enhanced_data_collector.collect_enhanced_market_data()
                if enhanced_data:
                    decisions = await pilot.oracle_decision_engine.generate_decisions(enhanced_data)
                    if decisions:
                        logger.info(f"✅ Real oracle decisions generated - {len(decisions)} decisions")
                        
                        # Log first decision details
                        first_decision = decisions[0]
                        logger.info(f"🧠 Sample decision: Confidence={getattr(first_decision, 'confidence', 'N/A')}")
                    else:
                        logger.warning("⚠️ Real oracle decisions returned empty results")
                else:
                    logger.warning("⚠️ No enhanced data available for oracle test")
            else:
                # Test mock oracle analysis
                mock_data = {'symbol': 'BTC/USDT', 'price': 45000.0}
                analysis = await pilot.oracle_decision_engine.analyze_market_state(mock_data)
                if analysis:
                    logger.info("✅ Mock oracle analysis successful")
                else:
                    logger.warning("⚠️ Mock oracle analysis failed")
        except Exception as e:
            logger.error(f"❌ Oracle decision test failed: {e}")
        
        # Test 6: Complete Trading Cycle Test
        logger.info("🔄 Test 6: Complete Trading Cycle Test...")
        try:
            cycle_success = await pilot.run_qualia_trading_cycle()
            if cycle_success:
                logger.info("✅ Complete trading cycle executed successfully")
            else:
                logger.warning("⚠️ Trading cycle completed with warnings")
        except Exception as e:
            logger.error(f"❌ Trading cycle test failed: {e}")
        
        # Test Summary
        logger.info("📊 Test Summary:")
        logger.info(f"   - Configuration: {'✅' if config_loaded else '❌'}")
        logger.info(f"   - Components: {'✅' if components_initialized else '❌'}")
        logger.info(f"   - Real Components: {'✅' if hasattr(pilot.enhanced_data_collector, 'collect_enhanced_market_data') else 'Mock'}")
        
        logger.info("🎉 Real QUALIA Components Integration Test Completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        return False

async def main():
    """Main test execution"""
    success = await test_real_components_integration()
    if success:
        logger.info("🎯 All tests completed successfully!")
        return 0
    else:
        logger.error("💥 Some tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
