#!/usr/bin/env python3
"""
Simple test for structured logging system.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_basic_redaction():
    """Test basic sensitive data redaction"""
    print("Testing basic redaction...")
    
    from src.qualia.utils.structured_logging import SensitiveDataRedactor
    
    redactor = SensitiveDataRedactor()
    
    # Test API key redaction
    test_text = 'api_key: "sk-1234567890abcdef"'
    result = redactor.redact(test_text)
    print(f"Input: {test_text}")
    print(f"Output: {result}")
    
    if "sk-1234567890abcdef" not in result:
        print("✅ API key redacted successfully")
        return True
    else:
        print("❌ API key not redacted")
        return False

def test_basic_logging():
    """Test basic structured logging"""
    print("\nTesting basic logging...")
    
    from src.qualia.utils.structured_logging import configure_structured_logging, get_qualia_logger
    
    # Configure logging to console only
    configure_structured_logging(
        level="INFO",
        console_enabled=True,
        file_enabled=False,
        structured_format=False,  # Use simple format for this test
        redact_sensitive=True
    )
    
    # Get logger and test
    logger = get_qualia_logger("test.basic")
    
    print("Logging test messages...")
    logger.info("This is a test message")
    logger.info("API key test: %s", "sk-secret-key-123")
    logger.warning("Warning with sensitive data", extra={'password': 'secret123'})
    
    print("✅ Basic logging test completed")
    return True

def main():
    """Run simple tests"""
    print("🧪 Simple Structured Logging Test")
    print("=" * 50)
    
    try:
        # Test redaction
        redaction_ok = test_basic_redaction()
        
        # Test logging
        logging_ok = test_basic_logging()
        
        if redaction_ok and logging_ok:
            print("\n✅ All basic tests passed!")
            return True
        else:
            print("\n❌ Some tests failed!")
            return False
            
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
