#!/usr/bin/env python3
"""
Script de debug para verificar problema de reamostragem.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Adiciona o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy
from qualia.strategies.strategy_interface import TradingContext

# Configura logging para DEBUG
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def create_test_data(periods=200):
    """Cria dados de teste com timestamps realistas."""
    end_time = datetime.now()
    start_time = end_time - timedelta(minutes=periods)
    
    # Cria índice de tempo com frequência de 1 minuto
    index = pd.date_range(start=start_time, end=end_time, freq='1min')[:periods]
    
    # Gera dados OHLCV realistas
    base_price = 45000.0
    prices = []
    
    for i in range(periods):
        # Simula movimento de preço com tendência e volatilidade
        trend = 0.001 * np.sin(i / 50)  # Tendência suave
        noise = np.random.normal(0, 0.005)  # Ruído
        price_change = trend + noise
        
        if i == 0:
            prices.append(base_price)
        else:
            new_price = prices[-1] * (1 + price_change)
            prices.append(max(new_price, 1000))  # Evita preços negativos
    
    # Cria OHLCV
    data = []
    for i, price in enumerate(prices):
        volatility = np.random.uniform(0.001, 0.01)
        high = price * (1 + volatility)
        low = price * (1 - volatility)
        open_price = prices[i-1] if i > 0 else price
        close_price = price
        volume = np.random.uniform(100, 1000)
        
        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(data, index=index)
    return df

def main():
    print("=== Debug de Reamostragem FWH ===")
    
    # Cria dados de teste
    print("Criando dados de teste...")
    market_data = create_test_data(200)
    print(f"Dados criados: {len(market_data)} períodos")
    print(f"Range temporal: {market_data.index.min()} até {market_data.index.max()}")
    print(f"Span total: {(market_data.index.max() - market_data.index.min()).total_seconds() / 60:.1f} minutos")
    
    # Inicializa estratégia
    print("\nInicializando estratégia FWH...")
    parameters = {
        "fib_lookback": 20,
        "hype_threshold": 0.15,
        "wave_min_strength": 0.3,
        "supported_timeframes": ["1m", "5m", "15m", "1h"]
    }
    
    strategy = FibonacciWaveHypeStrategy(
        symbol="BTC/USDT",
        timeframe="1m",
        parameters=parameters
    )
    
    # Cria contexto de trading
    context = TradingContext(
        symbol="BTC/USDT",
        timeframe="1m",
        current_price=market_data['close'].iloc[-1],
        ohlcv=market_data,
        timestamp=pd.Timestamp.now(),
        wallet_state={"BTC": 0.0, "USDT": 10000.0}
    )
    
    # Testa geração de sinal
    print("\nTestando geração de sinal multi-timeframe...")
    try:
        signal_df = strategy.generate_signal(context)
        print(f"Sinal gerado com sucesso: {not signal_df.empty}")
        
        if not signal_df.empty:
            signal_info = signal_df.iloc[0]
            print(f"Tipo: {signal_info.get('signal', 'N/A')}")
            print(f"Confiança: {signal_info.get('confidence', 0):.3f}")
        
    except Exception as e:
        print(f"Erro na geração de sinal: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n=== Teste concluído ===")

if __name__ == "__main__":
    main()