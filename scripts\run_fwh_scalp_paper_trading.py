#!/usr/bin/env python3
"""
Sistema de Paper Trading para Scalp Trading com Estratégia FWH

Este script implementa um sistema completo de paper trading otimizado para scalping
usando a estratégia Fibonacci Wave Hype (FWH) integrada à infraestrutura QUALIA.

Características:
- Paper trading seguro (sem risco real)
- Otimizado para scalping de alta frequência
- Integração completa com a estratégia FWH
- Monitoramento em tempo real
- Métricas de performance detalhadas
- Gestão de risco automática

Autor: QUALIA System
Data: 2024
"""

import asyncio
import logging
import sys
import os
import yaml
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import signal
import time
from collections import deque
import numpy as np

# Adicionar o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))
sys.path.insert(0, str(Path(__file__).parent))

# Imports do QUALIA
try:
    from start_real_trading import QUALIATradingSystem
    from qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy
    from qualia.strategies.strategy_interface import TradingContext
    from qualia.monitoring.performance_metrics import PerformanceMetricsCollector
    from qualia.exchanges.binance_client import BinanceClient
    from qualia.market.market_data_client import MarketDataClient
    from qualia.market.paper_broker import PaperBroker
    from qualia.common.specs import MarketSpec
    from dataclasses import dataclass
    from typing import Optional
    import pandas as pd
    
    # Sistema de logging de métricas em tempo real
    from qualia.utils.real_time_metrics_logger import (
        get_metrics_logger,
        log_trading_decision,
        TradingDecisionMetrics,
        TimeframeMetrics,
        ConsolidatedMetrics,
        create_timeframe_metrics,
        create_consolidated_metrics
    )

    # YAA-OTOC: Imports do sistema OTOC integrado
    from qualia.utils.otoc_calculator import calculate_otoc, calculate_adaptive_threshold
    from qualia.utils.otoc_metrics import (
        OTOCMetricsCollector,
        log_trading_decision as log_otoc_trading_decision,
        get_otoc_metrics_collector
    )
    from qualia.strategies.fibonacci_wave_hype.multi_timeframe_consolidator import (
        MultiTimeframeSignalConsolidator,
        TimeframeSignal
    )
except ImportError as e:
    print(f"Erro ao importar módulos QUALIA: {e}")
    print("Verifique se o PYTHONPATH está configurado corretamente")
    
    # Fallback para sistema de métricas
    def get_metrics_logger():
        return None
    def log_trading_decision(*args, **kwargs):
        pass
    TradingDecisionMetrics = None
    TimeframeMetrics = None
    ConsolidatedMetrics = None
    def create_timeframe_metrics(*args, **kwargs):
        return None
    def create_consolidated_metrics(*args, **kwargs):
        return None
    
    sys.exit(1)


@dataclass
class Position:
    """Representa uma posição de trading aberta"""
    id: str
    symbol: str
    side: str  # 'buy' or 'sell'
    quantity: float
    entry_price: float
    entry_time: datetime
    entry_fee: float
    current_price: float = 0.0
    unrealized_pnl: float = 0.0
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    confidence: float = 0.0

    def update_current_price(self, price: float):
        """Atualiza preço atual e PnL não realizado"""
        self.current_price = price
        if self.side == 'buy':
            # Long position: profit when price goes up
            self.unrealized_pnl = (price - self.entry_price) * self.quantity - self.entry_fee
        else:
            # Short position: profit when price goes down
            self.unrealized_pnl = (self.entry_price - price) * self.quantity - self.entry_fee


class FWHScalpPaperTradingSystem:
    """
    Sistema de Paper Trading para Scalping com Estratégia FWH
    
    Gerencia todo o ciclo de vida do sistema de trading:
    - Inicialização e configuração
    - Execução de trading em tempo real
    - Monitoramento de performance
    - Gestão de risco
    - Relatórios e métricas
    """
    
    def __init__(self, config_path: str = "config/fwh_scalp_config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.running = False
        self.start_time = None
        
        # Componentes do sistema
        self.trading_system = None
        self.strategy = None
        self.performance_metrics = None
        self.binance_client = None
        self.market_data_client = None
        self.paper_broker = None
        self.binance_fees = None

        # Position management
        self.open_positions = {}  # {symbol: Position}
        self.closed_positions = []
        self.position_id_counter = 0

        # Enhanced metrics tracking
        self.positions_opened = 0
        self.positions_closed = 0
        self.realized_pnl = 0.0
        self.unrealized_pnl = 0.0
        self.gross_profit = 0.0
        self.gross_loss = 0.0
        self.total_fees_paid = 0.0
        self.daily_returns = []
        self.trade_durations = []
        self.winning_amounts = []
        self.losing_amounts = []
        self.equity_curve = []
        self.portfolio_values = []
        self.start_time = None

        # Trading controls for realistic strategy (mais rigorosos)
        self.last_trade_time = {}  # {symbol: datetime} - cooldown tracking
        self.max_concurrent_positions = 2  # Limit simultaneous positions (mais conservador)
        self.min_confidence_threshold = 0.3  # Moderado - Minimum confidence for trades
        self.cooldown_minutes = 2  # Moderado - Minimum time between trades on same symbol

        # Controles moderados para permitir trades
        self.daily_trade_limit = 50  # Moderado - Máximo de trades por dia
        self.daily_trades_count = 0
        self.last_reset_date = datetime.now().date()
        self.min_profit_target = 0.005  # 0.5% - profit mínimo esperado razoável
        self.max_risk_per_trade = 0.02  # 2% - risco máximo por trade razoável
        self.consecutive_losses = 0  # Contador de perdas consecutivas
        self.max_consecutive_losses = 10  # Moderado - Máximo de perdas consecutivas

        # Backtest mode
        self.backtest_mode = False
        self.backtest_results = []
        self.backtest_start_date = None
        self.backtest_end_date = None
        self.backtest_data = {}
        
        # Métricas de trading
        self.trades_executed = 0
        self.total_pnl = 0.0
        self.winning_trades = 0
        self.losing_trades = 0
        self.max_drawdown = 0.0
        self.current_drawdown = 0.0
        
        # Sistema de saúde e circuit breakers
        self.system_health = {
            'status': 'healthy',
            'last_check': None,
            'api_errors': 0,
            'consecutive_failures': 0,
            'last_successful_trade': None
        }
        
        self.circuit_breakers = {
            'api_errors': {'count': 0, 'threshold': 5, 'reset_time': None},
            'consecutive_losses': {'count': 0, 'threshold': 3, 'reset_time': None},
            'drawdown': {'active': False, 'threshold': 0.03}  # 3% drawdown
        }
        
        # Setup logging
        self._setup_logging()
        
        # Inicializar sistema de logging de métricas em tempo real
        self.metrics_logger = get_metrics_logger()
        if self.metrics_logger:
            self.logger.info("Sistema de logging de métricas em tempo real inicializado")
        else:
            self.logger.warning("Sistema de logging de métricas não disponível - continuando sem logging de métricas")
        
        # Setup signal handlers para shutdown graceful
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        # YAA-OTOC: Inicializar sistema OTOC
        # Debug da configuração OTOC
        fwh_config = self.config.get('fibonacci_wave_hype_config', {})
        params_config = fwh_config.get('params', {})
        mtf_config = params_config.get('multi_timeframe_config', {})
        otoc_config = mtf_config.get('otoc_config', {})

        self.logger.info(f" DEBUG OTOC Config: fwh_keys={list(fwh_config.keys())}")
        self.logger.info(f" DEBUG OTOC Config: params_keys={list(params_config.keys())}")
        self.logger.info(f" DEBUG OTOC Config: mtf_keys={list(mtf_config.keys())}")
        self.logger.info(f" DEBUG OTOC Config: otoc_config={otoc_config}")

        self.otoc_enabled = otoc_config.get('enabled', False)
        self.logger.info(f" DEBUG OTOC: otoc_enabled={self.otoc_enabled}")

        if self.otoc_enabled:
            self.logger.info(" Sistema OTOC habilitado")

            # Configurar consolidador multi-timeframe com OTOC
            mtf_config = self.config.get('fibonacci_wave_hype_config', {}).get(
                'params', {}
            ).get('multi_timeframe_config', {})
            self.mtf_consolidator = MultiTimeframeSignalConsolidator(mtf_config)

            # Coletor de métricas OTOC
            self.otoc_metrics_collector = get_otoc_metrics_collector()

            self.logger.info("Sistema OTOC inicializado com sucesso")
        else:
            self.logger.info("Sistema OTOC desabilitado na configuração")
            self.mtf_consolidator = None
            self.otoc_metrics_collector = None

        self.logger.info("FWH Scalp Paper Trading System inicializado")
    
    def _load_config(self) -> Dict[str, Any]:
        """Carrega a configuração do arquivo YAML"""
        try:
            print(f"Carregando configuração de: {self.config_path}")
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            print(f"Configuração carregada com sucesso. Chaves principais: {list(config.keys()) if config else 'None'}")
            return config
        except Exception as e:
            print(f"Erro ao carregar configuração: {e}")
            sys.exit(1)
    
    def _setup_logging(self):
        """Configura o sistema de logging"""
        # Verificar se config foi carregado corretamente
        if self.config is None:
            print("ERRO: Configuração não foi carregada corretamente")
            sys.exit(1)
            
        log_config = self.config.get('logging', {})
        
        # Criar diretório de logs se não existir
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Configurar logger principal
        logging.basicConfig(
            level=getattr(logging, log_config.get('level', 'INFO')),
            format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            handlers=[
                logging.FileHandler(log_config.get('files', {}).get('main', 'logs/fwh_scalp_trading.log')),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger('FWHScalpTrading')
        
        # Sistema de cache inteligente otimizado para scalping - YAA-FIX: TTL aumentado
        self.ohlcv_cache = {}
        self.cache_stats = {'hits': 0, 'misses': 0, 'evictions': 0}
        self.cache_ttl = {
            '1m': 60,   # 60s cache para 1m (aumentado para reduzir chamadas API)
            '5m': 120,  # 120s cache para 5m (aumentado)
            '15m': 300, # 300s cache para 15m (aumentado)
            '1h': 600   # 600s cache para 1h (aumentado)
        }
        self.max_cache_size = 1000  # Limite de entradas no cache
        
        # Sistema de priorização dinâmica de símbolos (12 ativos otimizados)
        self.symbol_priority_cycle = 0
        self.symbol_performance_scores = {}  # Score baseado em volume, volatilidade e sucesso
        # TIER 1: Máxima liquidez e volume (Obrigatórios)
        self.high_priority_symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'SOL/USDT']
        # TIER 2: Alta qualidade para scalping
        self.medium_priority_symbols = ['XRP/USDT', 'LINK/USDT', 'AVAX/USDT', 'ADA/USDT']
        # TIER 3: Diversificação estratégica
        self.low_priority_symbols = ['TON/USDT', 'DOGE/USDT', 'AAVE/USDT', 'ARB/USDT']
        self.last_priority_update = None
        
        # Rate limiting inteligente para Binance
        self.api_call_history = deque(maxlen=1200)  # Histórico de chamadas
        self.api_call_weights = {
            'klines': 1,
            'ticker': 1,
            'depth': 5,
            'trades': 1
        }
        self.last_api_call = 0
        self.min_api_interval = 0.5  # YAA-FIX: Reduzido para 0.5s - permite operação normal multi-timeframe
        self.api_call_semaphore = asyncio.Semaphore(3)  # Máximo 3 chamadas simultâneas
        self.api_error_count = 0
        self.max_api_errors = 10
        
        # YAA-FIX: Backoff exponencial para rate limiting
        self.rate_limit_backoff = {}
        self.max_backoff_time = 60.0  # Máximo 60 segundos de backoff
        self.rate_limit_failures = {}  # Contador de falhas consecutivas por tipo
    
    def _signal_handler(self, signum, frame):
        """Handler para sinais de sistema (Ctrl+C, etc.)"""
        self.logger.info(f"Sinal {signum} recebido. Iniciando shutdown graceful...")
        self.running = False
    
    def _check_cache_health(self):
        """Verifica e mantém a saúde do cache"""
        current_time = time.time()
        
        # Limpar entradas expiradas
        expired_keys = []
        for key, (data, timestamp, ttl) in self.ohlcv_cache.items():
            if current_time - timestamp > ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.ohlcv_cache[key]
            self.cache_stats['evictions'] += 1
        
        # Limitar tamanho do cache
        if len(self.ohlcv_cache) > self.max_cache_size:
            # Remove as entradas mais antigas
            sorted_items = sorted(self.ohlcv_cache.items(), key=lambda x: x[1][1])
            items_to_remove = len(self.ohlcv_cache) - self.max_cache_size
            for i in range(items_to_remove):
                key = sorted_items[i][0]
                del self.ohlcv_cache[key]
                self.cache_stats['evictions'] += 1
    
    def _get_cache_key(self, symbol: str, timeframe: str, limit: int = 100) -> str:
        """Gera chave única para cache"""
        return f"{symbol}_{timeframe}_{limit}"
    
    def _get_cached_data(self, symbol: str, timeframe: str, limit: int = 100):
        """Recupera dados do cache se válidos"""
        cache_key = self._get_cache_key(symbol, timeframe, limit)
        
        if cache_key in self.ohlcv_cache:
            data, timestamp, ttl = self.ohlcv_cache[cache_key]
            if time.time() - timestamp < ttl:
                self.cache_stats['hits'] += 1
                return data
            else:
                # Cache expirado
                del self.ohlcv_cache[cache_key]
                self.cache_stats['evictions'] += 1
        
        self.cache_stats['misses'] += 1
        return None
    
    def _cache_data(self, symbol: str, timeframe: str, data, limit: int = 100):
        """Armazena dados no cache"""
        cache_key = self._get_cache_key(symbol, timeframe, limit)
        ttl = self.cache_ttl.get(timeframe, 60)
        self.ohlcv_cache[cache_key] = (data, time.time(), ttl)
        
        # Verificar saúde do cache periodicamente
        if len(self.ohlcv_cache) % 100 == 0:
            self._check_cache_health()
    
    async def _check_rate_limit(self, call_type: str = 'klines') -> bool:
        """Verifica se pode fazer chamada à API respeitando rate limit com backoff exponencial"""
        current_time = time.time()
        
        # YAA-FIX: Verificar backoff exponencial primeiro
        if call_type in self.rate_limit_backoff:
            backoff_until, backoff_count = self.rate_limit_backoff[call_type]
            if current_time < backoff_until:
                remaining = backoff_until - current_time
                self.logger.debug(f"Backoff ativo para {call_type}: {remaining:.1f}s restantes")
                return False
            else:
                # Backoff expirou, reduzir contador
                if backoff_count > 0:
                    self.rate_limit_backoff[call_type] = (0, max(0, backoff_count - 1))
                else:
                    del self.rate_limit_backoff[call_type]
        
        # Limpar chamadas antigas (mais de 1 minuto)
        while self.api_call_history and current_time - self.api_call_history[0] > 60:
            self.api_call_history.popleft()
        
        # Verificar peso da chamada
        call_weight = self.api_call_weights.get(call_type, 1)
        
        # Verificar se excederia o limite
        current_weight = len(self.api_call_history) + call_weight
        if current_weight > 1100:  # YAA-FIX: Aumentado para 1100 (Binance permite 1200)
            self.logger.warning(f"YAA-FIX: Rate limit peso excedido ({current_weight} > 1100) para {call_type} - histórico: {len(self.api_call_history)}")
            self._apply_backoff(call_type)
            return False
        
        # YAA-FIX: Permitir primeira chamada (quando last_api_call == 0)
        # Verificar intervalo mínimo apenas após a primeira chamada
        time_since_last = current_time - self.last_api_call if self.last_api_call > 0 else float('inf')
        if self.last_api_call > 0 and time_since_last < self.min_api_interval:
            self.logger.debug(f"YAA-FIX: Rate limit intervalo insuficiente ({time_since_last:.2f}s < {self.min_api_interval}s) para {call_type}")
            self._apply_backoff(call_type)
            return False
        
        # YAA-FIX: Resetar contador de falhas em caso de sucesso
        if call_type in self.rate_limit_failures:
            self.rate_limit_failures[call_type] = 0
        
        self.logger.debug(f"YAA-FIX: Rate limit OK: {call_type}, peso={call_weight}, histórico={len(self.api_call_history)}/1100, intervalo={time_since_last:.2f}s")
        return True
    
    def _record_api_call(self, call_type: str = 'klines'):
        """Registra chamada à API"""
        current_time = time.time()
        call_weight = self.api_call_weights.get(call_type, 1)
        
        # Adicionar peso da chamada ao histórico
        for _ in range(call_weight):
            self.api_call_history.append(current_time)
        
        self.last_api_call = current_time
    
    def _apply_backoff(self, call_type: str):
        """Aplica backoff exponencial para rate limiting - YAA-FIX: Menos agressivo"""
        current_time = time.time()
        
        # Incrementar contador de falhas consecutivas
        if call_type not in self.rate_limit_failures:
            self.rate_limit_failures[call_type] = 0
        self.rate_limit_failures[call_type] += 1
        
        # YAA-FIX: Só aplicar backoff após 3 falhas consecutivas
        if self.rate_limit_failures[call_type] < 3:
            self.logger.debug(f"Rate limit {call_type}: falha {self.rate_limit_failures[call_type]}/3 - sem backoff")
            return
        
        if call_type in self.rate_limit_backoff:
            _, backoff_count = self.rate_limit_backoff[call_type]
            backoff_count += 1
        else:
            backoff_count = 1
        
        # Backoff exponencial: 2^count segundos, máximo 60s
        backoff_time = min(2 ** backoff_count, self.max_backoff_time)
        backoff_until = current_time + backoff_time
        
        self.rate_limit_backoff[call_type] = (backoff_until, backoff_count)
        self.logger.info(f"YAA-FIX: Aplicando backoff {backoff_time:.1f}s para {call_type} após {self.rate_limit_failures[call_type]} falhas")
    
    def _resample_ohlcv(self, data: pd.DataFrame, target_timeframe: str) -> pd.DataFrame:
        """Resample dados OHLCV para timeframe maior - YAA-FIX: Fallback strategy"""
        try:
            if data.empty:
                return pd.DataFrame()
            
            # Mapear timeframes para pandas frequency
            timeframe_map = {
                '1m': '1T',
                '5m': '5T', 
                '15m': '15T',
                '1h': '1H'
            }
            
            freq = timeframe_map.get(target_timeframe)
            if not freq:
                self.logger.warning(f"Timeframe {target_timeframe} não suportado para resample")
                return pd.DataFrame()
            
            # Garantir que o índice seja datetime
            if not isinstance(data.index, pd.DatetimeIndex):
                if 'timestamp' in data.columns:
                    data = data.set_index('timestamp')
                elif 'datetime' in data.columns:
                    data = data.set_index('datetime')
                else:
                    # Assumir que a primeira coluna é timestamp
                    data = data.set_index(data.columns[0])
            
            # Converter índice para datetime se necessário
            if not isinstance(data.index, pd.DatetimeIndex):
                data.index = pd.to_datetime(data.index)
            
            # Resample OHLCV
            resampled = data.resample(freq).agg({
                'open': 'first',
                'high': 'max', 
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()
            
            self.logger.debug(f"Resample: {len(data)} -> {len(resampled)} candles para {target_timeframe}")
            return resampled
            
        except Exception as e:
            self.logger.error(f"Erro no resample para {target_timeframe}: {e}")
            return pd.DataFrame()
    
    def _update_symbol_performance(self, symbol: str, success: bool, pnl: float = 0.0):
        """Atualiza score de performance do símbolo"""
        if symbol not in self.symbol_performance_scores:
            self.symbol_performance_scores[symbol] = {
                'trades': 0,
                'wins': 0,
                'total_pnl': 0.0,
                'score': 0.5  # Score inicial neutro
            }
        
        perf = self.symbol_performance_scores[symbol]
        perf['trades'] += 1
        if success:
            perf['wins'] += 1
        perf['total_pnl'] += pnl
        
        # Calcular novo score (0.0 a 1.0)
        win_rate = perf['wins'] / perf['trades'] if perf['trades'] > 0 else 0.5
        avg_pnl = perf['total_pnl'] / perf['trades'] if perf['trades'] > 0 else 0.0
        
        # Score combinado (60% win rate, 40% PnL médio)
        perf['score'] = (win_rate * 0.6) + (min(max(avg_pnl / 0.01, -1), 1) * 0.2 + 0.5) * 0.4
        perf['score'] = max(0.0, min(1.0, perf['score']))
    
    def _get_prioritized_symbols(self) -> List[str]:
        """Retorna TODOS os símbolos ordenados por prioridade dinâmica"""
        current_time = time.time()
        
        # Atualizar prioridades a cada 5 minutos
        if (self.last_priority_update is None or 
            current_time - self.last_priority_update > 300):
            
            # Ordenar símbolos por score de performance
            all_symbols = (self.high_priority_symbols + 
                          self.medium_priority_symbols + 
                          self.low_priority_symbols)
            
            scored_symbols = []
            for symbol in all_symbols:
                score = self.symbol_performance_scores.get(symbol, {}).get('score', 0.5)
                scored_symbols.append((symbol, score))
            
            # Ordenar por score (maior primeiro)
            scored_symbols.sort(key=lambda x: x[1], reverse=True)
            
            # Reorganizar listas de prioridade
            self.high_priority_symbols = [s[0] for s in scored_symbols[:4]]
            self.medium_priority_symbols = [s[0] for s in scored_symbols[4:8]]
            self.low_priority_symbols = [s[0] for s in scored_symbols[8:]]
            
            self.last_priority_update = current_time
        
        # YAA-FIX: Retornar TODOS os 12 símbolos em ordem de prioridade
        # Alta prioridade primeiro, depois média, depois baixa
        return (self.high_priority_symbols + 
                self.medium_priority_symbols + 
                self.low_priority_symbols)
    
    def _check_circuit_breakers(self) -> bool:
        """Verifica se algum circuit breaker foi ativado"""
        current_time = time.time()
        
        # Circuit breaker para erros de API
        if self.circuit_breakers['api_errors']['count'] >= self.circuit_breakers['api_errors']['threshold']:
            if (self.circuit_breakers['api_errors']['reset_time'] is None or
                current_time - self.circuit_breakers['api_errors']['reset_time'] < 300):  # 5 min
                self.logger.warning("Circuit breaker ativo: muitos erros de API")
                return True
            else:
                # Reset do circuit breaker
                self.circuit_breakers['api_errors']['count'] = 0
                self.circuit_breakers['api_errors']['reset_time'] = None
        
        # Circuit breaker para perdas consecutivas
        if self.consecutive_losses >= self.circuit_breakers['consecutive_losses']['threshold']:
            self.logger.warning(f"Circuit breaker ativo: {self.consecutive_losses} perdas consecutivas")
            return True
        
        # Circuit breaker para drawdown
        if self.current_drawdown >= self.circuit_breakers['drawdown']['threshold']:
            self.circuit_breakers['drawdown']['active'] = True
            self.logger.warning(f"Circuit breaker ativo: drawdown de {self.current_drawdown:.2%}")
            return True
        
        return False
    
    async def initialize_system(self):
        """Inicializa todos os componentes do sistema"""
        self.logger.info("Inicializando sistema de trading...")
        
        try:
            # 1. Inicializar configuração da estratégia FWH
            self.logger.info("Carregando configuração FWH...")
            fwh_config = self.config['fibonacci_wave_hype_config']
            self.logger.info("Configuração FWH carregada")
            
            # 2. Inicializar cliente Binance para dados reais
            self.logger.info("Inicializando cliente Binance...")
            binance_config = {
                "api_key": "",  # Não precisamos de credenciais para dados públicos
                "api_secret": "",
                "sandbox": False,
                "use_websocket": False
            }
            self.binance_client = BinanceClient(binance_config)
            await self.binance_client.initialize()
            self.market_data_client = MarketDataClient(self.binance_client.integration)

            # Buscar fees reais da Binance
            self.binance_fees = await self._fetch_binance_fees()

            # Inicializar paper broker com fees reais
            self.paper_broker = PaperBroker(fee_pct=self.binance_fees)

            self.logger.info(f"Cliente Binance inicializado - Fee: {self.binance_fees*100:.3f}%")

            # 3. Inicializar sistema QUALIA principal
            self.logger.info("Inicializando sistema QUALIA...")
            self.trading_system = QUALIATradingSystem(config=self.config)
            self.logger.info("Sistema QUALIA inicializado")
            
            # 3. Configurar estratégia FWH para múltiplos símbolos
            self.logger.info("Configurando estratégia FWH para múltiplos símbolos...")
            symbols = self.config['trading_system']['symbols']
            self.logger.info(f"Símbolos configurados: {symbols}")

            # Inicializar estratégia para múltiplos símbolos (símbolo será definido dinamicamente)
            # Criar ConfigManager com o arquivo de configuração correto
            from qualia.config.config_manager import ConfigManager
            config_manager = ConfigManager(config_path=self.config_path)

            self.strategy = FibonacciWaveHypeStrategy(
                params=fwh_config['params'],
                context={},  # Símbolo será passado dinamicamente no contexto de trading
                config_manager=config_manager
            )

            # 4.1. Inicializar estratégia FWH com dados reais
            self.logger.info("Inicializando estratégia FWH com dados reais...")
            initialization_context = {
                # Configurar cliente de dados reais
                "market_data_client": self.market_data_client,
                "use_real_data": True,
                "paper_trading": True
            }
            self.strategy.initialize(initialization_context)

            # Verificar se a estratégia foi inicializada corretamente
            if hasattr(self.strategy, 'initialized') and self.strategy.initialized:
                self.logger.info(" Estratégia FWH inicializada com sucesso")
            else:
                self.logger.error(" Falha na inicialização da estratégia FWH")
                raise Exception("Estratégia não foi inicializada corretamente")

            self.logger.info(f"Estratégia FWH configurada para {len(symbols)} símbolos: {', '.join(symbols)}")
            
            # 4. Inicializar métricas de performance
            self.logger.info("Inicializando métricas de performance...")
            self.performance_metrics = PerformanceMetricsCollector("fwh_scalp_trading")
            self.logger.info("Métricas de performance inicializadas")
            
            # 5. Inicializar métricas básicas
            self.logger.info("Carregando configuração de backtesting...")
            self.initial_capital = self.config['backtesting']['initial_capital']
            self.initial_balance = self.initial_capital  # Alias for consistency
            self.current_balance = self.initial_capital
            self.logger.info("Métricas básicas inicializadas")
            
            # 5. Configurar limites de risco
            self.logger.info("Configurando limites de risco...")
            self.risk_limits = {
                'max_position_size': self.config['trading_system']['limits']['max_position_size_usd'],
                'max_daily_loss': self.config['trading_system']['limits']['max_daily_loss'],
                'stop_loss_pct': self.config['trading_system']['risk_management']['stop_loss_pct'],
                'take_profit_pct': self.config['trading_system']['risk_management']['take_profit_pct']
            }
            self.logger.info("Limites de risco configurados")
            
            self.logger.info("Sistema inicializado com sucesso")
            
        except Exception as e:
            import traceback
            self.logger.error(f"Erro na inicialização: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            raise
    
    def _get_symbols_for_cycle(self) -> list:
        """YAA-FIX: Retorna símbolos priorizados para o ciclo atual"""
        # Ciclo 0: Alta prioridade
        # Ciclo 1: Média prioridade  
        # Ciclo 2: Baixa prioridade (dividida em 2 partes)
        # Ciclo 3: Baixa prioridade (segunda parte)
        
        if self.symbol_priority_cycle == 0:
            symbols = self.high_priority_symbols
        elif self.symbol_priority_cycle == 1:
            symbols = self.medium_priority_symbols
        elif self.symbol_priority_cycle == 2:
            # Primeira metade dos símbolos de baixa prioridade
            mid_point = len(self.low_priority_symbols) // 2
            symbols = self.low_priority_symbols[:mid_point]
        else:  # cycle == 3
            # Segunda metade dos símbolos de baixa prioridade
            mid_point = len(self.low_priority_symbols) // 2
            symbols = self.low_priority_symbols[mid_point:]
        
        # Avançar para o próximo ciclo
        self.symbol_priority_cycle = (self.symbol_priority_cycle + 1) % 4
        
        self.logger.info(f"Ciclo {(self.symbol_priority_cycle - 1) % 4}: processando {len(symbols)} símbolos")
        return symbols
    
    def _cleanup_cache(self):
        """YAA-FIX: Limpa cache expirado para evitar uso excessivo de memória"""
        current_time = time.time()
        expired_keys = []
        
        for cache_key, (cached_data, cache_time, ttl) in self.ohlcv_cache.items():
            if current_time - cache_time > ttl:
                expired_keys.append(cache_key)
        
        for key in expired_keys:
            del self.ohlcv_cache[key]
            self.cache_stats['evictions'] += 1
        
        if expired_keys:
            self.logger.debug(f"Cache limpo: {len(expired_keys)} entradas expiradas removidas")

    def _check_cache_health(self):
        """Verifica a saúde do sistema de cache"""
        try:
            total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
            
            if total_requests > 0:
                hit_rate = (self.cache_stats['hits'] / total_requests) * 100
                
                # Alertar se hit rate muito baixo
                if hit_rate < 30:
                    self.logger.warning(f" Cache hit rate baixo: {hit_rate:.1f}%")
                
                # Alertar se cache muito grande
                if len(self.ohlcv_cache) > 1000:
                    self.logger.warning(f" Cache muito grande: {len(self.ohlcv_cache)} entradas")
                    # Forçar limpeza mais agressiva
                    self._aggressive_cache_cleanup()
                    
        except Exception as e:
            self.logger.error(f"Erro ao verificar saúde do cache: {e}")

    def _aggressive_cache_cleanup(self):
        """Limpeza agressiva do cache quando necessário"""
        try:
            current_time = time.time()
            # Remover entradas mais antigas que 5 minutos (mais agressivo)
            aggressive_ttl = 300  # 5 minutos
            
            expired_keys = []
            for cache_key, (cached_data, cache_time, ttl) in self.ohlcv_cache.items():
                if current_time - cache_time > aggressive_ttl:
                    expired_keys.append(cache_key)
            
            for key in expired_keys:
                del self.ohlcv_cache[key]
                self.cache_stats['evictions'] += 1
                
            self.logger.info(f" Limpeza agressiva: {len(expired_keys)} entradas removidas")
            
        except Exception as e:
            self.logger.error(f"Erro na limpeza agressiva do cache: {e}")
    
    async def start_trading(self):
        """Inicia o loop principal de trading com otimizações de API"""
        self.logger.info("Iniciando trading com otimizações de API...")
        self.running = True
        self.start_time = datetime.now()
        
        symbols = self.config['trading_system']['symbols']
        timeframes = self.config['trading_system']['timeframes']
        update_interval = self.config['monitoring']['update_interval_seconds']
        
        try:
            cycle_count = 0
            while self.running:
                cycle_count += 1
                self.logger.info(f"Iniciando ciclo de trading #{cycle_count}")

                # Verificar condições de emergência
                self.logger.debug("Verificando condições de emergência...")
                if await self._check_emergency_conditions():
                    self.logger.warning("Condições de emergência detectadas. Parando trading.")
                    break

                # Health check e circuit breakers
                if self._check_circuit_breakers():
                    self.logger.error(" CIRCUIT BREAKER ATIVO - Pausando trading por 5 minutos")
                    await asyncio.sleep(300)  # 5 minutos
                    continue
                
                # Verificar condições de emergência
                if await self._check_emergency_conditions():
                    self.logger.error(" CONDIÇÕES DE EMERGÊNCIA DETECTADAS - Parando sistema")
                    self.running = False
                    break

                # Processar símbolos priorizados sequencialmente
                symbols_to_process = self._get_prioritized_symbols()
                self.logger.info(f"Processando {len(symbols_to_process)} símbolos priorizados sequencialmente...")
                
                # Processamento sequencial simples para evitar problemas de rate limiting
                for symbol_num, symbol in enumerate(symbols_to_process, 1):
                    if not self.running:
                        break
                    
                    self.logger.info(f"Processando símbolo {symbol_num}/{len(symbols_to_process)}: {symbol}")
                    
                    try:
                        # Processar símbolo individualmente
                        await self._process_symbol_multi_timeframe(symbol, timeframes)
                        
                        # Pausa entre símbolos para evitar rate limiting
                        if symbol_num < len(symbols_to_process):  # Não pausar após o último símbolo
                            await asyncio.sleep(3)  # 3 segundos entre símbolos
                            
                    except Exception as e:
                        self.logger.error(f"Erro ao processar símbolo {symbol}: {e}")
                        continue

                # Atualizar posições abertas com preços atuais
                await self._update_open_positions()

                # Manutenção periódica do sistema
                if cycle_count % 5 == 0:  # A cada 5 ciclos
                    await self._perform_system_maintenance()
                
                # Atualizar métricas e performance
                self.logger.debug("Atualizando métricas...")
                await self._update_metrics()
                
                # Atualizar performance de símbolos
                await self._update_symbol_performance_scores()

                # Log de status detalhado
                self.logger.debug("Logando status...")
                await self._log_status()

                # Aguardar próximo ciclo com ajuste dinâmico
                dynamic_interval = self._calculate_dynamic_interval(cycle_count)
                self.logger.info(f"Aguardando {dynamic_interval}s para próximo ciclo...")
                await asyncio.sleep(dynamic_interval)
                
        except Exception as e:
            self.logger.error(f"Erro no loop de trading: {e}")
        finally:
            await self._shutdown()

    async def _perform_system_maintenance(self):
        """Executa manutenção periódica do sistema"""
        try:
            self.logger.info(" Executando manutenção do sistema...")
            
            # Limpar cache expirado
            self._cleanup_cache()
            
            # Verificar saúde do cache
            self._check_cache_health()
            
            # Reset de circuit breakers se necessário
            self._reset_circuit_breakers_if_needed()
            
            # Limpeza de métricas antigas
            self._cleanup_old_metrics()
            
            # Log de estatísticas do sistema
            self._log_system_stats()

            # YAA-OTOC: Log de métricas OTOC
            await self._log_otoc_metrics()

        except Exception as e:
            self.logger.error(f"Erro na manutenção do sistema: {e}")

    def _calculate_dynamic_interval(self, cycle_count: int) -> int:
        """Calcula intervalo dinâmico baseado na performance e condições de mercado"""
        try:
            base_interval = 20  # 20 segundos base (YAA-OTIMIZAÇÃO: reduzido de 30s para 20s para melhor responsividade)
            
            # Ajustar baseado na performance recente
            if len(self.open_positions) > 0:
                # Mais posições abertas = monitoramento mais frequente
                position_factor = 0.8  # Reduzir intervalo em 20%
            else:
                position_factor = 1.2  # Aumentar intervalo em 20%
            
            # Ajustar baseado em erros de API
            if self.api_error_count > 5:
                error_factor = 1.5  # Aumentar intervalo para reduzir stress na API
            else:
                error_factor = 1.0
            
            # Ajustar baseado no horário (scalping é mais ativo em certas horas)
            current_hour = datetime.now().hour
            if 8 <= current_hour <= 22:  # Horário ativo
                time_factor = 0.9
            else:
                time_factor = 1.3  # Menos ativo durante madrugada
            
            # Calcular intervalo final
            dynamic_interval = int(base_interval * position_factor * error_factor * time_factor)
            
            # Limitar entre 5 e 60 segundos
            return max(5, min(60, dynamic_interval))
            
        except Exception as e:
            self.logger.error(f"Erro ao calcular intervalo dinâmico: {e}")
            return 20  # Fallback (YAA-OTIMIZAÇÃO: reduzido de 30s para 20s)

    async def _update_symbol_performance_scores(self):
        """Atualiza scores de performance dos símbolos"""
        try:
            for symbol in list(self.symbol_performance_scores.keys()):
                # Calcular score baseado em trades recentes
                recent_trades = [pos for pos in self.closed_positions 
                               if pos.symbol == symbol and 
                               (datetime.now() - pos.entry_time).days < 1]
                
                if recent_trades:
                    # Calcular win rate e PnL médio
                    winning_trades = [t for t in recent_trades if t.unrealized_pnl > 0]
                    win_rate = len(winning_trades) / len(recent_trades)
                    avg_pnl = sum(t.unrealized_pnl for t in recent_trades) / len(recent_trades)
                    
                    # Score combinado (0.0 a 1.0)
                    performance_score = (win_rate * 0.6) + (min(avg_pnl / 10, 0.4))  # Normalizar PnL
                    performance_score = max(0.1, min(1.0, performance_score))
                    
                    self.symbol_performance_scores[symbol] = performance_score
                    self.logger.debug(f"Score atualizado para {symbol}: {performance_score:.3f}")
                else:
                    # Decaimento gradual para símbolos sem trades recentes
                    current_score = self.symbol_performance_scores.get(symbol, 0.5)
                    self.symbol_performance_scores[symbol] = max(0.1, current_score * 0.95)
                    
        except Exception as e:
            self.logger.error(f"Erro ao atualizar performance de símbolos: {e}")

    def _reset_circuit_breakers_if_needed(self):
        """Reset circuit breakers se condições melhoraram"""
        try:
            current_time = time.time()
            
            for breaker_name, breaker in self.circuit_breakers.items():
                if breaker['active'] and breaker['last_trigger']:
                    # Reset após 10 minutos
                    if current_time - breaker['last_trigger'] > 600:
                        breaker['active'] = False
                        breaker['count'] = 0
                        self.logger.info(f"Circuit breaker {breaker_name} resetado")
                        
        except Exception as e:
            self.logger.error(f"Erro ao resetar circuit breakers: {e}")

    def _cleanup_old_metrics(self):
        """Limpa métricas antigas para evitar acúmulo de memória"""
        try:
            # Manter apenas últimas 1000 entradas do equity curve
            if len(self.equity_curve) > 1000:
                self.equity_curve = self.equity_curve[-1000:]
            
            # Manter apenas últimas 500 durações de trade
            if len(self.trade_durations) > 500:
                self.trade_durations = self.trade_durations[-500:]
            
            # Manter apenas últimos 200 valores de portfolio
            if len(self.portfolio_values) > 200:
                self.portfolio_values = self.portfolio_values[-200:]
                
        except Exception as e:
            self.logger.error(f"Erro ao limpar métricas antigas: {e}")

    def _log_system_stats(self):
        """Log estatísticas detalhadas do sistema"""
        try:
            cache_hit_rate = (self.cache_stats['hits'] /
                            max(self.cache_stats['hits'] + self.cache_stats['misses'], 1)) * 100

            self.logger.info(f"   STATS DO SISTEMA:")
            self.logger.info(f"   Cache: {cache_hit_rate:.1f}% hit rate, {len(self.ohlcv_cache)} entradas")
            self.logger.info(f"   API: {len(self.api_call_history)} calls recentes, {self.api_error_count} erros")
            self.logger.info(f"   Posições: {len(self.open_positions)} abertas, {self.positions_closed} fechadas")
            self.logger.info(f"   Performance: {self.winning_trades}W/{self.losing_trades}L, PnL: ${self.total_pnl:.2f}")
            self.logger.info(f"   Circuit Breakers: {sum(1 for cb in self.circuit_breakers.values() if cb['active'])} ativos")

        except Exception as e:
            self.logger.error(f"Erro ao logar estatísticas: {e}")

    async def _calculate_otoc_for_symbol(self, symbol: str, timeframe: str, ohlcv_data) -> float:
        """
        Calcula OTOC para um símbolo e timeframe específicos.

        YAA-OTOC: Implementação do cálculo OTOC integrado ao paper trading
        YAA-ROBUSTNESS: Tratamento robusto de tipos de dados (DataFrame ou dict)
        """
        try:
            if not self.otoc_enabled:
                return 0.0

            # YAA-TYPE-SAFETY: Verificar e converter tipo de dados
            if isinstance(ohlcv_data, dict):
                # Se é dicionário, verificar se tem dados
                if not ohlcv_data or 'close' not in ohlcv_data:
                    self.logger.debug(f"OTOC {symbol} ({timeframe}): Dados vazios ou sem 'close'")
                    return 0.0

                # Converter para DataFrame se necessário
                try:
                    if isinstance(ohlcv_data['close'], (list, np.ndarray)):
                        close_prices = np.array(ohlcv_data['close'])
                    else:
                        # Assumir que é um valor único
                        self.logger.debug(f"OTOC {symbol} ({timeframe}): Apenas um valor de preço")
                        return 0.0
                except (KeyError, TypeError) as e:
                    self.logger.debug(f"OTOC {symbol} ({timeframe}): Erro ao extrair preços do dict: {e}")
                    return 0.0

            elif hasattr(ohlcv_data, 'empty'):
                # É um DataFrame pandas
                if ohlcv_data.empty:
                    return 0.0
                close_prices = ohlcv_data['close'].values
            else:
                # Tipo não suportado
                self.logger.warning(f"OTOC {symbol} ({timeframe}): Tipo de dados não suportado: {type(ohlcv_data)}")
                return 0.0

            # Obter configuração OTOC
            otoc_config = self.config.get('fibonacci_wave_hype_config', {}).get(
                'params', {}
            ).get('multi_timeframe_config', {}).get('otoc_config', {})

            window = otoc_config.get('window', 100)
            method = otoc_config.get('method', 'correlation')

            if len(close_prices) < window * 2:
                self.logger.debug(f"OTOC {symbol} ({timeframe}): Dados insuficientes ({len(close_prices)} < {window*2})")
                return 0.0

            otoc_value = calculate_otoc(
                series=close_prices,
                window=window,
                method=method
            )

            if np.isnan(otoc_value):
                self.logger.debug(f"OTOC {symbol} ({timeframe}): Resultado NaN")
                return 0.0

            self.logger.debug(f"OTOC {symbol} ({timeframe}): {otoc_value:.4f}")
            return float(otoc_value)

        except Exception as e:
            self.logger.error(f"Erro ao calcular OTOC para {symbol} ({timeframe}): {e}")
            return 0.0

    async def _apply_otoc_filter(self, symbol: str, timeframe_signals: Dict[str, Dict], timeframe_data: Dict[str, pd.DataFrame]) -> Dict[str, Dict]:
        """
        Aplica filtro OTOC aos sinais de trading.

        YAA-OTOC: Filtra sinais em regimes caóticos
        """
        try:
            if not self.otoc_enabled or not self.mtf_consolidator:
                return timeframe_signals

            # Obter configuração OTOC
            otoc_config = self.config.get('fibonacci_wave_hype_config', {}).get(
                'params', {}
            ).get('multi_timeframe_config', {}).get('otoc_config', {})

            max_threshold = otoc_config.get('max_threshold', 0.35)
            adaptive_enabled = otoc_config.get('adaptive_threshold', {}).get('enabled', True)
            beta = otoc_config.get('adaptive_threshold', {}).get('beta', 1.0)
            vol_window = otoc_config.get('adaptive_threshold', {}).get('vol_window', 20)

            filtered_signals = {}

            for timeframe, signal_data in timeframe_signals.items():
                try:
                    # Calcular OTOC para este timeframe
                    ohlcv_data = timeframe_data.get(timeframe, {})
                    otoc_value = await self._calculate_otoc_for_symbol(symbol, timeframe, ohlcv_data)

                    # Calcular threshold adaptativo se habilitado
                    effective_threshold = max_threshold
                    volatility = 0.0

                    # YAA-TYPE-SAFETY: Verificar tipo de dados para threshold adaptativo
                    has_data = False
                    if isinstance(ohlcv_data, dict):
                        has_data = bool(ohlcv_data and 'close' in ohlcv_data)
                    elif hasattr(ohlcv_data, 'empty'):
                        has_data = not ohlcv_data.empty

                    if adaptive_enabled and has_data:
                        try:
                            # YAA-TYPE-SAFETY: Calcular volatilidade baseado no tipo de dados
                            if isinstance(ohlcv_data, dict):
                                close_data = ohlcv_data['close']
                                if isinstance(close_data, (list, np.ndarray)) and len(close_data) > vol_window:
                                    close_series = pd.Series(close_data)
                                    returns = close_series.pct_change()
                                    volatility = returns.rolling(vol_window).std().iloc[-1]
                                else:
                                    volatility = np.nan
                            else:
                                # DataFrame pandas
                                returns = ohlcv_data['close'].pct_change()
                                volatility = returns.rolling(vol_window).std().iloc[-1]

                            if not np.isnan(volatility):
                                effective_threshold = calculate_adaptive_threshold(
                                    base_threshold=max_threshold,
                                    volatility=volatility,
                                    beta=beta,
                                    vol_window=vol_window
                                )
                        except Exception as e:
                            self.logger.debug(f"Erro ao calcular volatilidade para {symbol} ({timeframe}): {e}")
                            volatility = 0.0

                    # Aplicar filtro OTOC
                    original_action = signal_data['action']
                    original_confidence = signal_data['confidence']

                    if otoc_value > effective_threshold:
                        # Regime caótico detectado - bloquear sinal
                        filtered_action = 'HOLD'
                        filtered_confidence = 0.0

                        self.logger.info(
                            f"OTOC FILTER: {symbol} ({timeframe}) - Regime caótico detectado! "
                            f"OTOC={otoc_value:.3f} > {effective_threshold:.3f}. "
                            f"Sinal {original_action} → HOLD"
                        )

                        # Registrar métricas OTOC
                        if self.otoc_metrics_collector:
                            self.otoc_metrics_collector.record_otoc_decision(
                                symbol=symbol,
                                timeframe=timeframe,
                                otoc_value=otoc_value,
                                threshold_used=effective_threshold,
                                threshold_base=max_threshold,
                                volatility=volatility if not np.isnan(volatility) else 0.0,
                                signal_original=original_action,
                                signal_filtered=filtered_action,
                                confidence_original=original_confidence,
                                confidence_filtered=filtered_confidence
                            )
                    else:
                        # Regime ordenado - manter sinal original
                        filtered_action = original_action
                        filtered_confidence = original_confidence

                        self.logger.debug(
                            f" OTOC OK: {symbol} ({timeframe}) - "
                            f"OTOC={otoc_value:.3f} <= {effective_threshold:.3f}"
                        )

                    # Atualizar sinal com informações OTOC
                    filtered_signals[timeframe] = {
                        'action': filtered_action,
                        'confidence': filtered_confidence,
                        'price': signal_data.get('price', 0.0),
                        'otoc_value': otoc_value,
                        'otoc_threshold': effective_threshold,
                        'chaos_detected': otoc_value > effective_threshold
                    }

                except Exception as e:
                    self.logger.error(f"Erro ao aplicar filtro OTOC para {symbol} ({timeframe}): {e}")
                    # Em caso de erro, manter sinal original (fail-safe)
                    filtered_signals[timeframe] = signal_data

            return filtered_signals

        except Exception as e:
            self.logger.error(f"Erro geral no filtro OTOC para {symbol}: {e}")
            return timeframe_signals

    async def _log_otoc_metrics(self):
        """
        Log periódico das métricas OTOC.

        YAA-OTOC: Observabilidade do sistema OTOC
        """
        try:
            if not self.otoc_enabled or not self.otoc_metrics_collector:
                return

            # Obter estatísticas OTOC
            stats = self.otoc_metrics_collector.get_otoc_statistics()
            chaos_rate = self.otoc_metrics_collector.get_chaos_rate()

            if stats:
                self.logger.info(" MÉTRICAS OTOC:")
                self.logger.info(f"   Taxa de caos (1h): {chaos_rate:.1%}")
                self.logger.info(f"   Total decisões: {stats.get('count', 0)}")
                self.logger.info(f"   OTOC médio: {stats.get('otoc_mean', 0):.3f}")
                self.logger.info(f"   Eventos de caos: {stats.get('chaos_events', 0)}")

            # Verificar alertas
            alerts = self.otoc_metrics_collector.generate_alert_conditions()
            for alert in alerts:
                self.logger.warning(f"🚨 OTOC ALERT [{alert['severity']}]: {alert['message']}")

        except Exception as e:
            self.logger.error(f"Erro ao logar métricas OTOC: {e}")
    
    async def _process_symbol_timeframe(self, symbol: str, timeframe: str):
        """Processa um símbolo específico em um timeframe específico"""
        try:
            self.logger.debug(f"Obtendo dados de mercado para {symbol} ({timeframe})...")
            # Obter dados de mercado reais
            market_data = await self._get_market_data(symbol, timeframe)

            if not market_data:
                self.logger.warning(f"Nenhum dado de mercado obtido para {symbol} ({timeframe})")
                return

            self.logger.debug(f"Dados obtidos para {symbol} ({timeframe}): ${market_data['price']:.2f}")

            # Obter dados OHLCV reais
            ohlcv_data = await self._get_real_ohlcv(symbol, timeframe)

            # Criar contexto de trading com dados reais
            context = TradingContext(
                symbol=symbol,
                timeframe=timeframe,
                current_price=market_data['price'],
                timestamp=pd.Timestamp.now(),
                ohlcv=ohlcv_data,
                wallet_state={'balance': self.current_balance, 'positions': {}}
            )

            # Gerar sinal da estratégia FWH
            self.logger.info(f"Gerando sinal FWH para {symbol} ({timeframe})...")
            signal_df = self.strategy.generate_signal(context)

            if signal_df is None:
                self.logger.info(f"{symbol} ({timeframe}): Nenhum sinal gerado (None)")
                return
            elif signal_df.empty:
                self.logger.info(f"{symbol} ({timeframe}): Nenhum sinal gerado (DataFrame vazio)")
                return
            elif signal_df is not None and not signal_df.empty:
                signal_action = signal_df.iloc[0].get('signal', 'HOLD')
                confidence = signal_df.iloc[0].get('confidence', 0.0)

                self.logger.info(f" {symbol}: Sinal={signal_action}, Confiança={confidence:.3f}")

                if signal_action != 'HOLD':
                    self.logger.info(f" {symbol} ({timeframe}): Sinal de {signal_action} detectado!")

                    # Validar qualidade do sinal ANTES da gestão de risco
                    if await self._validate_signal_quality(symbol, signal_action, confidence):
                        # Verificar gestão de risco básica
                        if self._validate_risk(signal_action, market_data['price']):
                            # Executar trade (paper trading) - apenas no timeframe principal
                            if timeframe == "1m":  # Executar trades apenas no timeframe principal
                                self.logger.info(f" {symbol} ({timeframe}): Executando trade {signal_action}")
                                await self._execute_trade(signal_action, confidence, context)
                            else:
                                self.logger.info(f" {symbol} ({timeframe}): Sinal confirmado mas não executado (timeframe secundário)")
                        else:
                            self.logger.info(f" {symbol} ({timeframe}): Trade rejeitado por gestão de risco")
                    else:
                        self.logger.info(f" {symbol} ({timeframe}): Sinal rejeitado - qualidade insuficiente")
                else:
                    self.logger.info(f"  {symbol} ({timeframe}): Sinal HOLD - sem ação")
            
        except Exception as e:
            self.logger.error(f"Erro ao processar {symbol} ({timeframe}): {e}")
    
    async def _get_market_data(self, symbol: str, timeframe: str) -> Optional[Dict]:
        """Obtém dados de mercado reais da Binance"""
        try:
            if not self.market_data_client:
                self.logger.error("Cliente de dados não inicializado")
                return None

            # Obter ticker atual da Binance
            ticker = await self.market_data_client.fetch_ticker(symbol)

            if ticker:
                return {
                    'symbol': symbol,
                    'price': float(ticker.get('last', 0)),
                    'volume': float(ticker.get('baseVolume', 0)),
                    'timestamp': datetime.now(),
                    'bid': float(ticker.get('bid', 0)),
                    'ask': float(ticker.get('ask', 0)),
                    'change_24h': float(ticker.get('percentage', 0))
                }
            else:
                self.logger.warning(f"Nenhum ticker obtido para {symbol}")
                return None

        except Exception as e:
            self.logger.error(f"Erro ao obter dados de mercado reais: {e}")
            return None

    async def _fetch_binance_fees(self) -> float:
        """Busca fees reais da Binance via API"""
        try:
            if not self.binance_client or not self.binance_client.integration:
                self.logger.warning("Cliente Binance não disponível, usando fee padrão")
                return 0.001  # 0.1% fee padrão da Binance

            # Tentar obter fees via CCXT
            exchange = self.binance_client.integration.exchange
            if hasattr(exchange, 'fetch_trading_fees'):
                fees_info = await exchange.fetch_trading_fees()
                if fees_info and 'trading' in fees_info:
                    taker_fee = fees_info['trading'].get('taker', 0.001)
                    self.logger.info(f"Fee Binance obtido via API: {taker_fee*100:.3f}%")
                    return taker_fee

            # Fallback: fee padrão da Binance
            self.logger.info("Usando fee padrão da Binance: 0.1%")
            return 0.001  # 0.1% fee padrão

        except Exception as e:
            self.logger.error(f"Erro ao buscar fees da Binance: {e}")
            return 0.001  # 0.1% fee padrão

    async def _get_real_ohlcv(self, symbol: str, timeframe: str = "1h", limit: int = None) -> pd.DataFrame:
        """Obtém dados OHLCV reais da Binance com cache inteligente e rate limiting otimizado"""
        try:
            # Verificar cache primeiro - RATE LIMIT FIX: Limites reduzidos
            if limit is None:
                # Limits otimizados para garantir dados suficientes para resample
                limit_map = {
                    "1m": 200,   # Para gerar dados suficientes para timeframes maiores
                    "5m": 100,   # Dados suficientes para análise
                    "15m": 150,  # Dados para ondas de hype
                    "1h": 200    # Tendência principal
                }
                limit = limit_map.get(timeframe, 100)  
            
            cached_data = self._get_cached_data(symbol, timeframe, limit)
            if cached_data is not None:
                self.logger.debug(f"Cache hit para {symbol} ({timeframe})")
                return cached_data
            
            # Verificar rate limit antes da chamada
            if not await self._check_rate_limit('klines'):
                self.logger.warning(f"Rate limit atingido para {symbol}")
                # YAA-FIX: Estratégia de fallback melhorada
                cache_key = self._get_cache_key(symbol, timeframe, limit)
                if cache_key in self.ohlcv_cache:
                    expired_data, cache_timestamp, _ = self.ohlcv_cache[cache_key]
                    cache_age = time.time() - cache_timestamp
                    # Aceitar dados até 30 minutos antigos como fallback
                    if cache_age < 1800:  # 30 minutos
                        self.logger.info(f"YAA-FIX: Usando dados em cache de {cache_age:.0f}s para {symbol} (fallback)")
                        return expired_data
                
                # Tentar buscar dados de timeframe menor para resample
                if timeframe != '1m':
                    fallback_timeframes = {'5m': '1m', '15m': '5m', '1h': '15m'}
                    fallback_tf = fallback_timeframes.get(timeframe)
                    if fallback_tf:
                        self.logger.info(f"YAA-FIX: Tentando fallback {fallback_tf} -> {timeframe} para {symbol}")
                        fallback_data = await self._get_real_ohlcv(symbol, fallback_tf, limit * 5)
                        if not fallback_data.empty:
                            # Resample para o timeframe desejado
                            try:
                                resampled_data = self._resample_ohlcv(fallback_data, timeframe)
                                if not resampled_data.empty:
                                    self.logger.info(f"YAA-FIX: Dados resampled {fallback_tf} -> {timeframe} para {symbol}")
                                    return resampled_data
                            except Exception as resample_error:
                                self.logger.warning(f"YAA-FIX: Erro no resample para {symbol}: {resample_error}")
                
                self.logger.error(f"Nenhum dado disponível para {symbol} - pulando análise")
                return pd.DataFrame()  # Retorna DataFrame vazio
            
            # Fazer chamada à API com semáforo
            async with self.api_call_semaphore:
                try:
                    # Registrar chamada à API
                    self._record_api_call('klines')
                    
                    # Ajustar limite baseado no timeframe - Valores otimizados para resample
                    if limit is None:
                        limit_map = {"1m": 200, "5m": 100, "15m": 150, "1h": 200}  # Dados suficientes para análise multi-timeframe
                        limit = limit_map.get(timeframe, 100)

                    if not self.market_data_client:
                        self.logger.error(f"Cliente de dados não disponível para {symbol} - pulando análise")
                        return pd.DataFrame()  # Retorna DataFrame vazio
                    # Criar especificação de mercado
                    market_spec = MarketSpec(symbol=symbol, timeframe=timeframe)

                    # Obter dados OHLCV reais
                    self.logger.debug(f"Fazendo chamada à API para {symbol} ({timeframe})")
                    ohlcv_df = await self.market_data_client.fetch_ohlcv(market_spec, limit=limit)

                    if not ohlcv_df.empty:
                        # Armazenar no cache
                        self._cache_data(symbol, timeframe, ohlcv_df.copy(), limit)
                        self.logger.debug(f"Dados OHLCV obtidos e cacheados para {symbol}: {len(ohlcv_df)} períodos")
                        return ohlcv_df
                    else:
                        self.logger.error(f"Dados OHLCV vazios para {symbol} - pulando análise")
                        return pd.DataFrame()  # Retorna DataFrame vazio
                        
                except Exception as api_error:
                    self.logger.error(f"Erro na chamada à API para {symbol}: {api_error}")
                    self.api_error_count += 1
                    self.circuit_breakers['api_errors']['count'] += 1
                    
                    # Retornar DataFrame vazio em caso de erro
                    self.logger.error(f"Falha na obtenção de dados para {symbol} - pulando análise")
                    return pd.DataFrame()  # Retorna DataFrame vazio

        except Exception as e:
            self.logger.error(f"Erro geral ao obter OHLCV para {symbol}: {e}")
            self.logger.error(f"Erro geral ao obter OHLCV para {symbol} - pulando análise")
            return pd.DataFrame()  # Retorna DataFrame vazio


    async def _process_symbol_multi_timeframe(self, symbol: str, timeframes: List[str]):
        """Processa um símbolo com análise multi-timeframe consolidada"""
        try:
            self.logger.info(f" Análise multi-timeframe para {symbol}")

            # Coletar dados de todos os timeframes
            timeframe_data = {}
            timeframe_signals = {}

            for timeframe in timeframes:
                try:
                    # Obter dados de mercado
                    market_data = await self._get_market_data(symbol, timeframe)
                    if not market_data:
                        continue

                    # Obter dados OHLCV
                    ohlcv_data = await self._get_real_ohlcv(symbol, timeframe)
                    if ohlcv_data is None or ohlcv_data.empty:
                        continue

                    timeframe_data[timeframe] = {
                        'market_data': market_data,
                        'ohlcv': ohlcv_data,
                        'price': market_data['price']
                    }

                    # Gerar sinal para este timeframe
                    context = TradingContext(
                        symbol=symbol,
                        timeframe=timeframe,
                        current_price=market_data['price'],
                        timestamp=pd.Timestamp.now(),
                        ohlcv=ohlcv_data,
                        wallet_state={'balance': self.current_balance, 'positions': {}}
                    )

                    signal_df = self.strategy.generate_signal(context)

                    if signal_df is not None and not signal_df.empty:
                        signal_action = signal_df.iloc[0].get('signal', 'HOLD')
                        confidence = signal_df.iloc[0].get('confidence', 0.0)

                        timeframe_signals[timeframe] = {
                            'action': signal_action,
                            'confidence': confidence
                        }

                        # Registrar sinal individual do timeframe para tracking
                        if self.metrics_logger:
                            from qualia.utils.real_time_metrics_logger import log_individual_timeframe_signal
                            
                            # Criar objeto de sinal para o timeframe
                            class TimeframeSignal:
                                def __init__(self, timeframe, signal, confidence, timestamp):
                                    self.timeframe = timeframe
                                    self.signal = signal
                                    self.confidence = confidence
                                    self.signal_strength = confidence  # Usar confidence como strength
                                    self.hype_momentum = 0.0  # Placeholder
                                    self.holographic_boost = 1.0  # Placeholder
                                    self.tsvf_validation = 0.5  # Placeholder
                                    self.timestamp = timestamp
                            
                            tf_signal = TimeframeSignal(
                                timeframe=timeframe,
                                signal=signal_action,
                                confidence=confidence,
                                timestamp=pd.Timestamp.now()
                            )
                            
                            decision_id = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
                            
                            log_individual_timeframe_signal(
                                decision_id=decision_id,
                                symbol=symbol,
                                current_price=market_data['price'],
                                timeframe_signal=tf_signal,
                                volume=market_data.get('volume'),
                                market_conditions={'timeframe': timeframe},
                                system_state={'balance': self.current_balance}
                            )

                        self.logger.info(f"   {timeframe}: {signal_action} (confiança: {confidence:.3f})")
                    else:
                        timeframe_signals[timeframe] = {'action': 'HOLD', 'confidence': 0.0}
                        
                        # Registrar sinal HOLD individual do timeframe para tracking
                        if self.metrics_logger:
                            from qualia.utils.real_time_metrics_logger import log_individual_timeframe_signal
                            
                            class TimeframeSignal:
                                def __init__(self, timeframe, signal, confidence, timestamp):
                                    self.timeframe = timeframe
                                    self.signal = signal
                                    self.confidence = confidence
                                    self.signal_strength = confidence
                                    self.hype_momentum = 0.0
                                    self.holographic_boost = 1.0
                                    self.tsvf_validation = 0.5
                                    self.timestamp = timestamp
                            
                            tf_signal = TimeframeSignal(
                                timeframe=timeframe,
                                signal='HOLD',
                                confidence=0.0,
                                timestamp=pd.Timestamp.now()
                            )
                            
                            decision_id = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
                            
                            log_individual_timeframe_signal(
                                decision_id=decision_id,
                                symbol=symbol,
                                current_price=market_data['price'],
                                timeframe_signal=tf_signal,
                                volume=market_data.get('volume'),
                                market_conditions={'timeframe': timeframe},
                                system_state={'balance': self.current_balance}
                            )
                        
                        self.logger.info(f"   {timeframe}: HOLD")

                except Exception as e:
                    self.logger.error(f"Erro ao processar {symbol} ({timeframe}): {e}")
                    continue

            # Consolidar sinais de todos os timeframes
            if timeframe_signals:
                consolidated_signal = await self._consolidate_multi_timeframe_signals(symbol, timeframe_signals, timeframe_data)

                if consolidated_signal['action'] != 'HOLD':
                    self.logger.info(f" {symbol}: Sinal consolidado = {consolidated_signal['action']} "
                                   f"(confiança: {consolidated_signal['confidence']:.3f})")

                    # Validar e executar trade consolidado
                    if await self._validate_signal_quality(symbol, consolidated_signal['action'], consolidated_signal['confidence']):
                        if self._validate_risk(consolidated_signal['action'], consolidated_signal['price']):
                            # Usar dados do timeframe principal (primeiro da lista)
                            main_timeframe = timeframes[0]
                            if main_timeframe in timeframe_data:
                                context = TradingContext(
                                    symbol=symbol,
                                    timeframe=main_timeframe,
                                    current_price=consolidated_signal['price'],
                                    timestamp=pd.Timestamp.now(),
                                    ohlcv=timeframe_data[main_timeframe]['ohlcv'],
                                    wallet_state={'balance': self.current_balance, 'positions': {}}
                                )

                                self.logger.info(f" {symbol}: Executando trade consolidado {consolidated_signal['action']}")
                                await self._execute_trade(consolidated_signal['action'], consolidated_signal['confidence'], context)
                        else:
                            self.logger.info(f" {symbol}: Trade rejeitado por gestão de risco")
                    else:
                        self.logger.info(f" {symbol}: Trade rejeitado por qualidade insuficiente")
                else:
                    self.logger.info(f" {symbol}: Nenhum sinal consolidado gerado")
            else:
                self.logger.info(f"  {symbol}: Nenhum dado válido obtido")

        except Exception as e:
            self.logger.error(f"Erro na análise multi-timeframe para {symbol}: {e}")

    async def _consolidate_multi_timeframe_signals(self, symbol: str, timeframe_signals: dict, timeframe_data: dict) -> dict:
        """
        Consolida sinais de múltiplos timeframes COM FILTRO OTOC integrado.

        YAA-OTOC: Versão atualizada com sistema OTOC
        """
        try:
            self.logger.info(f" DEBUG: _consolidate_multi_timeframe_signals chamado para {symbol}")
            self.logger.info(f" DEBUG: timeframe_signals={timeframe_signals}")
            self.logger.info(f" DEBUG: otoc_enabled={self.otoc_enabled}")
            # Gerar ID único para esta decisão
            decision_id = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"

            # YAA-OTOC: Aplicar filtro OTOC ANTES da consolidação
            if self.otoc_enabled:
                self.logger.debug(f" Aplicando filtro OTOC para {symbol}...")
                timeframe_signals = await self._apply_otoc_filter(symbol, timeframe_signals, timeframe_data)

            # NOVA HIERARQUIA PARA SCALPING: Timeframes menores lideram
            timeframe_hierarchy = ['1m', '5m', '15m', '1h']  # Prioridade INVERTIDA
            available_timeframes = [tf for tf in timeframe_hierarchy if tf in timeframe_signals]

            if not available_timeframes:
                return {'action': 'HOLD', 'confidence': 0.0, 'price': 0.0}

            # Logar métricas de cada timeframe
            if self.metrics_logger:
                for tf in available_timeframes:
                    if tf in timeframe_signals:
                        signal_data = {
                            'signal': timeframe_signals[tf]['action'],
                            'confidence': timeframe_signals[tf]['confidence'],
                            'price': timeframe_data[tf]['price'] if tf in timeframe_data else 0.0
                        }
                        tf_metrics = create_timeframe_metrics(tf, signal_data)
                        # RealTimeMetricsLogger não tem método log_timeframe_metrics
                        # As métricas de timeframe são registradas via log_trading_decision

            # NOVA LÓGICA: Timeframes menores detectam reversões, maiores filtram
            reversal_timeframes = [tf for tf in available_timeframes if tf in ['1m', '5m']]    # Detectores de reversão
            filter_timeframes = [tf for tf in available_timeframes if tf in ['15m', '1h']]     # Filtros de contexto

            # 1. Detectar reversões nos timeframes menores (PRIORIDADE MÁXIMA)
            reversal_signals = []
            for tf in reversal_timeframes:
                if timeframe_signals[tf]['action'] != 'HOLD':
                    reversal_signals.append({
                        'timeframe': tf,
                        'signal': timeframe_signals[tf],
                        'weight': 0.4 if tf == '1m' else 0.3  # 1m tem peso maior
                    })

            # 2. Verificar filtros de contexto nos timeframes maiores (CAMADA C: Slope + VWAP)
            filter_signals = []
            for tf in filter_timeframes:
                original_action = timeframe_signals[tf]['action']
                if original_action != 'HOLD':
                    # Aplicar filtros de Slope e VWAP
                    filtered_action = await self._apply_slope_vwap_filter(symbol, tf, original_action, timeframe_data.get(tf, {}))
                    
                    if filtered_action != 'HOLD':  # Só adicionar se passou nos filtros
                        filter_signals.append({
                            'timeframe': tf,
                            'signal': {
                                'action': filtered_action,
                                'confidence': timeframe_signals[tf]['confidence']
                            },
                            'weight': 0.2 if tf == '15m' else 0.1,  # 15m tem peso maior que 1h
                            'original_action': original_action,
                            'filtered': filtered_action != original_action
                        })

            # 3. NOVA LÓGICA DE CONSOLIDAÇÃO PARA SCALPING
            consolidated_action = 'HOLD'
            consolidated_confidence = 0.0
            reasoning = "No signals detected"

            # CENÁRIO 1: Reversão detectada em 1m + confirmação em 5m = ENTRADA IMEDIATA
            if len(reversal_signals) >= 2:
                signal_1m = next((s for s in reversal_signals if s['timeframe'] == '1m'), None)
                signal_5m = next((s for s in reversal_signals if s['timeframe'] == '5m'), None)
                
                if signal_1m and signal_5m and signal_1m['signal']['action'] == signal_5m['signal']['action']:
                    consolidated_action = signal_1m['signal']['action']
                    # Confiança base da reversão confirmada
                    base_confidence = (signal_1m['signal']['confidence'] * 0.4) + (signal_5m['signal']['confidence'] * 0.3)
                    
                    # Boost/penalização dos filtros
                    filter_adjustment = 0.0
                    for filter_sig in filter_signals:
                        if filter_sig['signal']['action'] == consolidated_action:
                            filter_adjustment += filter_sig['weight'] * 0.5  # Boost se alinhado
                        elif filter_sig['signal']['action'] != 'HOLD':
                            filter_adjustment -= filter_sig['weight'] * 0.2  # Penalização leve se contra
                    
                    consolidated_confidence = min(0.9, base_confidence + filter_adjustment)
                    reasoning = f"Reversão confirmada: 1m+5m concordam ({consolidated_action}), filtros: {filter_adjustment:+.2f}"

            # CENÁRIO 2: Sinal forte em 1m apenas (sem confirmação 5m)
            elif reversal_signals:
                strongest_reversal = max(reversal_signals, key=lambda x: x['signal']['confidence'])
                
                if strongest_reversal['signal']['confidence'] > 0.3:  # Threshold para sinal solo
                    consolidated_action = strongest_reversal['signal']['action']
                    base_confidence = strongest_reversal['signal']['confidence'] * strongest_reversal['weight']
                    
                    # Verificar se filtros estão neutros ou alinhados
                    filter_penalty = 0.0
                    opposing_filters = [f for f in filter_signals 
                                      if f['signal']['action'] != 'HOLD' and f['signal']['action'] != consolidated_action]
                    
                    if opposing_filters:
                        filter_penalty = sum(f['weight'] * 0.3 for f in opposing_filters)  # Penalização moderada
                    
                    consolidated_confidence = max(0.1, base_confidence - filter_penalty)
                    reasoning = f"Reversão solo em {strongest_reversal['timeframe']} ({consolidated_action}), penalização filtros: -{filter_penalty:.2f}"

            # CENÁRIO 3: Apenas sinais de filtro (sem reversão) - MUITO CONSERVADOR
            elif filter_signals:
                strongest_filter = max(filter_signals, key=lambda x: x['signal']['confidence'])
                
                if strongest_filter['signal']['confidence'] > 0.5:  # Threshold ALTO para filtros sozinhos
                    consolidated_action = strongest_filter['signal']['action']
                    consolidated_confidence = strongest_filter['signal']['confidence'] * 0.5  # Redução significativa
                    reasoning = f"Apenas filtro {strongest_filter['timeframe']} ({consolidated_action}), confiança reduzida"

            # CAMADA A + B: TREND SCORE - Detectar tendência e ajustar confiança
            trend_info = {'direction': 'neutral', 'strength': 0.0, 'aligned': False}
            if consolidated_action != 'HOLD':
                try:
                    trend_direction, trend_strength = await self._detect_trend(symbol, '1h')
                    
                    # Verificar alinhamento com a tendência
                    trend_ok = ((trend_direction == 'up' and consolidated_action == 'BUY') or 
                               (trend_direction == 'down' and consolidated_action == 'SELL'))
                    
                    # Calcular peso da tendência (0.3 a 0.7 baseado na força)
                    trend_weight = 0.3 + 0.4 * trend_strength
                    
                    # Só permitir scalps contra tendência se força < 0.25
                    if not trend_ok and trend_strength >= 0.25:
                        self.logger.debug(f"Bloqueando scalp contra tendência forte: {trend_direction} strength={trend_strength:.3f}")
                        consolidated_action = 'HOLD'
                        consolidated_confidence = 0.0
                        reasoning += f" | BLOQUEADO: contra tendência forte ({trend_direction}, {trend_strength:.3f})"
                    elif trend_ok:
                        # Alinhado com tendência - boost
                        old_confidence = consolidated_confidence
                        consolidated_confidence += trend_weight
                        self.logger.debug(f"Boost por alinhamento com tendência: +{trend_weight:.3f} ({old_confidence:.3f} -> {consolidated_confidence:.3f})")
                        reasoning += f" | BOOST: alinhado com tendência ({trend_direction}, +{trend_weight:.3f})"
                    else:
                        # Contra tendência fraca - penalidade menor
                        old_confidence = consolidated_confidence
                        penalty = trend_weight * 0.5
                        consolidated_confidence -= penalty
                        self.logger.debug(f"Penalidade por contra-tendência fraca: -{penalty:.3f} ({old_confidence:.3f} -> {consolidated_confidence:.3f})")
                        reasoning += f" | PENALTY: contra tendência fraca ({trend_direction}, -{penalty:.3f})"
                    
                    # Armazenar informações da tendência para uso posterior
                    trend_info = {
                        'direction': trend_direction,
                        'strength': trend_strength,
                        'aligned': trend_ok
                    }
                    
                except Exception as e:
                    self.logger.error(f"Erro ao calcular trend score: {e}")
                    trend_info = {'direction': 'neutral', 'strength': 0.0, 'aligned': False}

            # Usar preço do timeframe principal (1m para scalping)
            main_timeframe = '1m' if '1m' in available_timeframes else available_timeframes[0]
            price = timeframe_data[main_timeframe]['price'] if main_timeframe in timeframe_data else 0.0

            # Log da nova lógica de scalping
            self.logger.info(f"{symbol}: SCALP LOGIC - {reasoning} | Action: {consolidated_action} | Confidence: {consolidated_confidence:.3f} | Trend: {trend_info['direction']} ({trend_info['strength']:.3f})")

            # Logar métricas consolidadas
            if self.metrics_logger:
                # Criar objeto consolidado para as métricas
                class ConsolidatedSignal:
                    def __init__(self, signal, confidence, primary_timeframe, supporting_timeframes, convergence_score, reasoning):
                        self.signal = signal
                        self.confidence = confidence
                        self.primary_timeframe = primary_timeframe
                        self.supporting_timeframes = supporting_timeframes
                        self.convergence_score = convergence_score
                        self.reasoning = reasoning
                
                consolidated_signal = ConsolidatedSignal(
                    signal=consolidated_action,
                    confidence=consolidated_confidence,
                    primary_timeframe=main_timeframe,
                    supporting_timeframes=[tf for tf in available_timeframes if tf != main_timeframe],
                    convergence_score=consolidated_confidence,
                    reasoning=reasoning
                )
                
                consolidated_metrics = create_consolidated_metrics(consolidated_signal)
                # RealTimeMetricsLogger não tem método log_consolidated_metrics
                # As métricas consolidadas são registradas via log_trading_decision
                pass

            # YAA-OTOC: Adicionar informações OTOC ao resultado
            otoc_info = {}
            if self.otoc_enabled:
                chaos_detected = any(
                    sig.get('chaos_detected', False)
                    for sig in timeframe_signals.values()
                    if isinstance(sig, dict)
                )
                otoc_values = [
                    sig.get('otoc_value', 0.0)
                    for sig in timeframe_signals.values()
                    if isinstance(sig, dict) and 'otoc_value' in sig
                ]
                avg_otoc = np.mean(otoc_values) if otoc_values else 0.0

                otoc_info = {
                    'chaos_detected': chaos_detected,
                    'avg_otoc': avg_otoc,
                    'otoc_enabled': True
                }

                if chaos_detected:
                    reasoning += " [OTOC: Caos detectado em alguns timeframes]"

            # Log da decisão consolidada
            self.logger.info(f" {symbol}: Consolidação final = {consolidated_action} "
                           f"(confiança: {consolidated_confidence:.3f}) - {reasoning}")

            return {
                'action': consolidated_action,
                'confidence': consolidated_confidence,
                'price': price,
                'decision_id': decision_id,  # Incluir ID da decisão
                'analysis': {
                    'reversal_signals': len(reversal_signals),
                    'filter_signals': len(filter_signals),
                    'timeframes_analyzed': len(available_timeframes),
                    'reasoning': reasoning,
                    'scalp_optimized': True,
                    **otoc_info
                }
            }

        except Exception as e:
            self.logger.error(f"Erro na consolidação de sinais para {symbol}: {e}")
            return {'action': 'HOLD', 'confidence': 0.0, 'price': 0.0}

    def _validate_risk(self, signal_action: str, price: float) -> bool:
        """Validação básica de risco"""
        try:
            # Verificar se não excede posição máxima
            position_size = self.risk_limits['max_position_size']
            
            validation_result = True
            rejection_reason = None
            
            # Verificar se não excede perda diária
            if self.total_pnl < -self.risk_limits['max_daily_loss']:
                validation_result = False
                rejection_reason = f"Daily loss limit exceeded: {self.total_pnl} < -{self.risk_limits['max_daily_loss']}"
            
            # Verificar drawdown
            elif self.current_drawdown > self.risk_limits['max_daily_loss']:
                validation_result = False
                rejection_reason = f"Drawdown limit exceeded: {self.current_drawdown} > {self.risk_limits['max_daily_loss']}"
            
            # Logar validação de risco
            if self.metrics_logger:
                risk_validation_data = {
                    'timestamp': datetime.now().isoformat(),
                    'validation_type': 'RISK_VALIDATION',
                    'signal_action': signal_action,
                    'price': price,
                    'position_size': position_size,
                    'total_pnl': self.total_pnl,
                    'current_drawdown': self.current_drawdown,
                    'max_daily_loss': self.risk_limits['max_daily_loss'],
                    'validation_result': validation_result,
                    'rejection_reason': rejection_reason,
                    'open_positions_count': len(self.open_positions),
                    'current_balance': self.current_balance
                }
                
                # Log usando o logger normal
                import json
                self.logger.info(f"Risk validation data: {json.dumps(risk_validation_data)}")
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"Erro na validação de risco: {e}")
            return False

    async def _validate_signal_quality(self, symbol: str, signal_action: str, confidence: float, trend_info: dict = None) -> bool:
        """Valida se o sinal tem qualidade suficiente para ser executado (CONTROLES DINÂMICOS)"""
        try:
            # Reset contador diário se necessário
            current_date = datetime.now().date()
            if current_date != self.last_reset_date:
                self.daily_trades_count = 0
                self.last_reset_date = current_date
                self.logger.info(f"Novo dia: contador de trades resetado")

            validation_result = True
            rejection_reason = None
            validation_checks = {}
            
            # Obter informações de tendência se não fornecidas
            if trend_info is None:
                try:
                    trend_direction, trend_strength = await self._detect_trend(symbol, '1h')
                    trend_info = {'direction': trend_direction, 'strength': trend_strength}
                except:
                    trend_info = {'direction': 'neutral', 'strength': 0.0}

            # 1. Verificar limite diário de trades
            validation_checks['daily_trade_limit'] = {
                'current_count': self.daily_trades_count,
                'limit': self.daily_trade_limit,
                'passed': self.daily_trades_count < self.daily_trade_limit
            }
            if self.daily_trades_count >= self.daily_trade_limit:
                validation_result = False
                rejection_reason = f"Daily trade limit reached: {self.daily_trades_count}/{self.daily_trade_limit}"
                self.logger.info(f"{symbol}: REJEITADO - Limite diário atingido: {self.daily_trades_count}/{self.daily_trade_limit}")

            # 2. Verificar confiança mínima (MAIS RIGOROSO)
            validation_checks['min_confidence'] = {
                'confidence': confidence,
                'threshold': self.min_confidence_threshold,
                'passed': confidence >= self.min_confidence_threshold
            }
            if validation_result and confidence < self.min_confidence_threshold:
                validation_result = False
                rejection_reason = f"Confidence below threshold: {confidence:.3f} < {self.min_confidence_threshold}"
                self.logger.info(f"{symbol}: REJEITADO - Confiança baixa: {confidence:.3f} < {self.min_confidence_threshold}")

            # 3. Verificar cooldown entre trades (MAIS RIGOROSO)
            now = datetime.now()
            time_since_last = None
            if symbol in self.last_trade_time:
                time_since_last = (now - self.last_trade_time[symbol]).total_seconds() / 60
                validation_checks['cooldown'] = {
                    'time_since_last_min': time_since_last,
                    'cooldown_min': self.cooldown_minutes,
                    'passed': time_since_last >= self.cooldown_minutes
                }
                if validation_result and time_since_last < self.cooldown_minutes:
                    validation_result = False
                    rejection_reason = f"Cooldown period: {time_since_last:.1f}min < {self.cooldown_minutes}min"
                    self.logger.info(f"{symbol}: REJEITADO - Cooldown: {time_since_last:.1f}min < {self.cooldown_minutes}min")
            else:
                validation_checks['cooldown'] = {
                    'time_since_last_min': None,
                    'cooldown_min': self.cooldown_minutes,
                    'passed': True
                }

            # 4. Verificar limite de posições simultâneas (MAIS RIGOROSO)
            validation_checks['concurrent_positions'] = {
                'current_positions': len(self.open_positions),
                'max_positions': self.max_concurrent_positions,
                'passed': len(self.open_positions) < self.max_concurrent_positions
            }
            if validation_result and len(self.open_positions) >= self.max_concurrent_positions:
                validation_result = False
                rejection_reason = f"Max concurrent positions: {len(self.open_positions)}/{self.max_concurrent_positions}"
                self.logger.info(f"{symbol}: REJEITADO - Max posições: {len(self.open_positions)}/{self.max_concurrent_positions}")

            # 5. Evitar over-trading no mesmo símbolo
            validation_checks['existing_position'] = {
                'has_position': symbol in self.open_positions,
                'passed': symbol not in self.open_positions
            }
            if validation_result and symbol in self.open_positions:
                validation_result = False
                rejection_reason = f"Position already open for {symbol}"
                self.logger.info(f"{symbol}: REJEITADO - Posição já aberta")

            # 6. Threshold dinâmico ULTRA BAIXO (TEMPORÁRIO PARA TESTE)
            trend_strength = trend_info.get('strength', 0.0)
            
            # ULTRA LOW thresholds para garantir trades
            dynamic_threshold = 0.001  # ULTRA LOW - praticamente aceita tudo
            
            validation_checks['dynamic_confidence'] = {
                'confidence': confidence,
                'threshold': dynamic_threshold,
                'trend_strength': trend_strength,
                'passed': confidence >= dynamic_threshold
            }
            if validation_result and confidence < dynamic_threshold:
                validation_result = False
                rejection_reason = f"Confidence insufficient (dynamic): {confidence:.3f} < {dynamic_threshold:.3f} (trend_strength={trend_strength:.3f})"
                self.logger.info(f"{symbol}: REJEITADO - Confiança insuficiente (dinâmica): {confidence:.3f} < {dynamic_threshold:.3f} (trend={trend_strength:.3f})")

            # 7. Filtro ULTRA PERMISSIVO para sinais (TEMPORÁRIO PARA TESTE)
            quality_threshold = 0.001  # ULTRA LOW - praticamente aceita tudo
            
            validation_checks['quality_signal'] = {
                'confidence': confidence,
                'threshold': quality_threshold,
                'trend_strength': trend_strength,
                'passed': confidence >= quality_threshold
            }
            if validation_result and confidence < quality_threshold:
                validation_result = False
                rejection_reason = f"Signal quality insufficient (trend-adjusted): {confidence:.3f} < {quality_threshold:.3f} (trend_strength={trend_strength:.3f})"
                self.logger.info(f"{symbol}: REJEITADO - Qualidade insuficiente (ajustada): {confidence:.3f} < {quality_threshold:.3f} (trend={trend_strength:.3f})")

            # Logar validação de qualidade do sinal (sem JSON para evitar erros de serialização)
            if self.metrics_logger:
                self.logger.info(f"Signal validation for {symbol}: action={signal_action}, confidence={confidence:.3f}, result={validation_result}, reason={rejection_reason}")

            if validation_result:
                # Sinal aprovado - APENAS OS MELHORES
                self.logger.info(f"{symbol}: APROVADO - Confiança ALTA: {confidence:.3f}")
            
            return validation_result

        except Exception as e:
            self.logger.error(f"Erro na validação de qualidade do sinal para {symbol}: {e}")
            return False

    async def _execute_trade(self, signal_action: str, confidence: float, context):
        """Executa trading logic com position management adequado"""
        try:
            symbol = context.symbol
            current_price = context.current_price

            # Check if we have an open position for this symbol
            existing_position = self.open_positions.get(symbol)

            if existing_position:
                # Update current price and unrealized PnL
                existing_position.update_current_price(current_price)

                # Check if we should close the position
                should_close = self._should_close_position(existing_position, signal_action, confidence)

                if should_close:
                    await self._close_position(existing_position, current_price, "signal_change")
                else:
                    self.logger.info(f"Holding position: {symbol} | "
                                   f"Side: {existing_position.side} | "
                                   f"Entry: ${existing_position.entry_price:.2f} | "
                                   f"Current: ${current_price:.2f} | "
                                   f"Unrealized PnL: ${existing_position.unrealized_pnl:.4f}")
            else:
                # No existing position, open new one
                await self._open_position(signal_action, confidence, context)

        except Exception as e:
            self.logger.error(f"Erro ao executar trade: {e}")

    def _should_close_position(self, position: Position, new_signal: str, confidence: float) -> bool:
        """Determina se uma posição deve ser fechada com lógica aprimorada"""
        try:
            # 1. Verificar circuit breakers
            if self._check_circuit_breakers():
                self.logger.warning(f"Circuit breaker ativo - fechando posição {position.symbol}")
                return True

            # 2. Fechar se sinal oposto com confiança suficiente
            if position.side != new_signal and confidence > 0.15:  # Aumentado threshold
                self.logger.info(f"Fechando {position.symbol} por sinal oposto (conf: {confidence:.3f})")
                return True

            # 3. Stop loss adaptativo
            if position.stop_loss:
                if position.side == 'buy' and position.current_price <= position.stop_loss:
                    self.consecutive_losses += 1
                    self.circuit_breakers['consecutive_losses']['count'] += 1
                    self.logger.warning(f"Stop loss atingido para {position.symbol} - Perda #{self.consecutive_losses}")
                    return True
                elif position.side == 'sell' and position.current_price >= position.stop_loss:
                    self.consecutive_losses += 1
                    self.circuit_breakers['consecutive_losses']['count'] += 1
                    self.logger.warning(f"Stop loss atingido para {position.symbol} - Perda #{self.consecutive_losses}")
                    return True

            # 4. Take profit adaptativo
            if position.take_profit:
                if position.side == 'buy' and position.current_price >= position.take_profit:
                    self.consecutive_losses = 0  # Reset contador de perdas
                    self.logger.info(f"Take profit atingido para {position.symbol}")
                    return True
                elif position.side == 'sell' and position.current_price <= position.take_profit:
                    self.consecutive_losses = 0  # Reset contador de perdas
                    self.logger.info(f"Take profit atingido para {position.symbol}")
                    return True

            # 5. Proteção por tempo (scalping de reversão deve ser rápido)
            position_age_minutes = (datetime.now() - position.entry_time).total_seconds() / 60
            if position_age_minutes > 15:  # Máximo 15 minutos para scalping de reversão
                self.logger.info(f"Fechando {position.symbol} por tempo limite ({position_age_minutes:.1f}min)")
                return True

            # 6. Proteção por drawdown da posição
            if position.unrealized_pnl < -50:  # Perda máxima de $50 por posição
                self.logger.warning(f"Fechando {position.symbol} por drawdown excessivo (${position.unrealized_pnl:.2f})")
                return True

            return False
            
        except Exception as e:
            self.logger.error(f"Erro ao verificar fechamento de posição: {e}")
            return True  # Fechar por segurança

    async def _open_position(self, signal_action: str, confidence: float, context):
        """Abre uma nova posição com gestão de risco aprimorada"""
        try:
            symbol = context.symbol
            current_price = context.current_price
            
            # Calcular tamanho da posição baseado na confiança e risco
            base_size = self.config['trading_system']['limits']['min_position_size_usd']
            confidence_multiplier = min(confidence * 2, 1.5)  # Máximo 1.5x
            trade_size_usd = base_size * confidence_multiplier
            
            # Limitar tamanho máximo por trade
            max_trade_size = self.current_balance * 0.02  # Máximo 2% do saldo
            trade_size_usd = min(trade_size_usd, max_trade_size)

            # Calculate quantity
            quantity = trade_size_usd / current_price

            # Calculate entry fee
            entry_fee = self.paper_broker.calculate_fee(current_price, quantity) if self.paper_broker else 0

            # Calcular volatilidade para stops adaptativos
            volatility = await self._calculate_symbol_volatility(symbol)
            
            # Create position
            self.position_id_counter += 1
            position = Position(
                id=f"pos_{self.position_id_counter}",
                symbol=symbol,
                side=signal_action,
                quantity=quantity,
                entry_price=current_price,
                entry_time=datetime.now(),
                entry_fee=entry_fee,
                current_price=current_price,
                confidence=confidence
            )

            # Detectar tendência para ajuste dinâmico de TP/SL
            try:
                trend_direction, trend_strength = await self._detect_trend(symbol, '1h')
                trend_aligned = ((trend_direction == 'up' and signal_action == 'buy') or 
                               (trend_direction == 'down' and signal_action == 'sell'))
            except Exception as e:
                self.logger.error(f"Erro ao detectar tendência para {symbol}: {e}")
                trend_direction, trend_strength = 'neutral', 0.0
                trend_aligned = False
            
            # Stops adaptativos para SCALPING DE REVERSÕES (mais apertados)
            base_stop_pct = 0.003  # 0.3% base (reduzido para scalping)
            base_tp_pct = 0.006    # 0.6% base (reduzido para scalping rápido)
            
            # ADAPTAÇÃO DINÂMICA BASEADA NA TENDÊNCIA
            if trend_strength > 0.6 and trend_aligned:
                # Scalp de continuação em tendência forte - TP maior, SL menor
                trend_tp_multiplier = 1 + trend_strength  # Multiplica TP por 1.6-2.0
                trend_sl_multiplier = 0.8  # Reduz SL em 20%
                self.logger.info(f"{symbol}: Scalp de CONTINUAÇÃO em tendência forte ({trend_direction}, {trend_strength:.3f})")
            elif trend_strength > 0.4 and trend_aligned:
                # Scalp de continuação em tendência moderada
                trend_tp_multiplier = 1 + (trend_strength * 0.5)  # Multiplica TP por 1.2-1.3
                trend_sl_multiplier = 0.9  # Reduz SL em 10%
                self.logger.info(f"{symbol}: Scalp de continuação em tendência moderada ({trend_direction}, {trend_strength:.3f})")
            elif trend_strength < 0.25:
                # Scalp de reversão em consolidação - TP e SL padrão
                trend_tp_multiplier = 1.0
                trend_sl_multiplier = 1.0
                self.logger.info(f"{symbol}: Scalp de REVERSÃO em consolidação ({trend_direction}, {trend_strength:.3f})")
            else:
                # Scalp contra tendência moderada - TP menor, SL maior (mais conservador)
                trend_tp_multiplier = 0.8  # Reduz TP em 20%
                trend_sl_multiplier = 1.2  # Aumenta SL em 20%
                self.logger.info(f"{symbol}: Scalp CONTRA tendência moderada ({trend_direction}, {trend_strength:.3f}) - conservador")
            
            # Aplicar multiplicadores de tendência
            base_tp_pct *= trend_tp_multiplier
            base_stop_pct *= trend_sl_multiplier
            
            # Ajustar baseado na volatilidade (mais conservador)
            volatility_multiplier = max(0.7, min(1.5, volatility * 80))  # Entre 0.7x e 1.5x
            stop_pct = base_stop_pct * volatility_multiplier
            
            # Ajustar take profit baseado na confiança (mais agressivo para scalping)
            confidence_tp_multiplier = max(1.0, confidence * 2.5)  # Maior confiança = maior TP
            tp_pct = base_tp_pct * confidence_tp_multiplier
            
            # Limites para scalping (ajustados para permitir TPs maiores em tendências fortes)
            max_tp = 0.025 if trend_strength > 0.6 and trend_aligned else 0.015  # Até 2.5% para continuação forte
            stop_pct = max(0.002, min(0.008, stop_pct))  # Entre 0.2% e 0.8%
            tp_pct = max(0.004, min(max_tp, tp_pct))     # Entre 0.4% e 1.5%/2.5%
            
            # Aplicar stops adaptativos
            if signal_action == 'buy':
                position.stop_loss = current_price * (1 - stop_pct)
                position.take_profit = current_price * (1 + tp_pct)
            else:
                position.stop_loss = current_price * (1 + stop_pct)
                position.take_profit = current_price * (1 - tp_pct)
            
            # Log dos valores finais de TP/SL
            self.logger.info(f"{symbol}: TP/SL dinâmicos - SL: {stop_pct*100:.2f}% (${position.stop_loss:.4f}), "
                           f"TP: {tp_pct*100:.2f}% (${position.take_profit:.4f}) | "
                           f"Trend: {trend_direction} ({trend_strength:.3f}), Aligned: {trend_aligned}")

            # Store position
            self.open_positions[symbol] = position

            # Update last trade time for cooldown
            self.last_trade_time[symbol] = datetime.now()

            # Update daily trade counter
            self.daily_trades_count += 1

            # Update metrics for position opening
            self.positions_opened += 1
            self.trades_executed += 1  # Count position opening as a trade
            self.total_fees_paid += entry_fee

            # Update balance (subtract entry fee)
            self.current_balance -= entry_fee

            # Logar decisão de trading completa
            if self.metrics_logger:
                decision_id = getattr(context, 'decision_id', f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}")
                
                trading_decision = TradingDecisionMetrics(
                    decision_id=decision_id,
                    symbol=symbol,
                    action="OPEN_POSITION",
                    signal=signal_action,
                    confidence=confidence,
                    price=current_price,
                    quantity=quantity,
                    stop_loss=position.stop_loss,
                    take_profit=position.take_profit,
                    entry_fee=entry_fee,
                    volatility=volatility,
                    trade_size_usd=trade_size_usd,
                    balance_before=self.current_balance + entry_fee,
                    balance_after=self.current_balance,
                    position_count=len(self.open_positions),
                    daily_trades_count=self.daily_trades_count,
                    reasoning=f"Opened {signal_action} position with {confidence:.3f} confidence, volatility-adjusted stops"
                )
                
                self.metrics_logger.log_trading_decision(trading_decision)

            self.logger.info(f"Position opened: {signal_action} {symbol} | "
                           f"Price: ${current_price:.2f} | "
                           f"Size: ${trade_size_usd:.2f} | "
                           f"Quantity: {quantity:.6f} | "
                           f"Entry Fee: ${entry_fee:.4f} | "
                           f"Stop Loss: ${position.stop_loss:.2f} ({stop_pct*100:.2f}%) | "
                           f"Take Profit: ${position.take_profit:.2f} ({tp_pct*100:.2f}%) | "
                           f"Volatility: {volatility*100:.2f}% | "
                           f"Confidence: {confidence:.3f}")

        except Exception as e:
            self.logger.error(f"Erro ao abrir posição: {e}")

    async def _calculate_symbol_volatility(self, symbol: str) -> float:
        """Calcula volatilidade do símbolo para stops adaptativos"""
        try:
            # Tentar obter dados históricos para calcular volatilidade
            ohlcv_data = await self._get_real_ohlcv(symbol, '5m', 50)
            
            if ohlcv_data is not None and len(ohlcv_data) > 10:
                # Calcular retornos logarítmicos
                import numpy as np
                closes = ohlcv_data['close'].values
                returns = np.diff(np.log(closes))
                
                # Volatilidade como desvio padrão dos retornos
                volatility = np.std(returns)
                
                # Normalizar para range útil (0.001 a 0.05)
                volatility = max(0.001, min(0.05, volatility))
                
                self.logger.debug(f"Volatilidade calculada para {symbol}: {volatility*100:.3f}%")
                return volatility
            else:
                # Fallback: volatilidade padrão baseada no tipo de ativo
                if 'BTC' in symbol or 'ETH' in symbol:
                    return 0.015  # 1.5% para crypto majors
                elif 'USDT' in symbol:
                    return 0.008  # 0.8% para stablecoins
                else:
                    return 0.012  # 1.2% padrão
                    
        except Exception as e:
            self.logger.error(f"Erro ao calcular volatilidade para {symbol}: {e}")
            return 0.01  # 1% fallback

    async def _detect_trend(self, symbol: str, tf: str = '1h') -> tuple:
        """Detecta direção e força da tendência usando EMA e ADX"""
        try:
            # Cache key para evitar recálculos frequentes
            cache_key = f"trend_{symbol}_{tf}"
            current_time = datetime.now()
            
            # Verificar cache (60 segundos)
            if hasattr(self, '_trend_cache') and cache_key in self._trend_cache:
                cached_time, cached_result = self._trend_cache[cache_key]
                if (current_time - cached_time).total_seconds() < 60:
                    return cached_result
            
            # Obter dados históricos
            ohlcv_data = await self._get_real_ohlcv(symbol, tf, 200)  # Mais dados para ADX
            
            if ohlcv_data is None or len(ohlcv_data) < 150:
                return ('neutral', 0.0)
            
            import numpy as np
            import pandas as pd
            
            # Calcular EMAs
            close = ohlcv_data['close']
            high = ohlcv_data['high']
            low = ohlcv_data['low']
            
            ema_fast = close.ewm(span=34).mean()
            ema_slow = close.ewm(span=144).mean()
            
            # Calcular ADX (Average Directional Index)
            def calculate_adx(high, low, close, length=14):
                """Calcula ADX manualmente"""
                # True Range
                tr1 = high - low
                tr2 = abs(high - close.shift(1))
                tr3 = abs(low - close.shift(1))
                tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
                
                # Directional Movement
                dm_plus = np.where((high - high.shift(1)) > (low.shift(1) - low), 
                                 np.maximum(high - high.shift(1), 0), 0)
                dm_minus = np.where((low.shift(1) - low) > (high - high.shift(1)), 
                                  np.maximum(low.shift(1) - low, 0), 0)
                
                # Smoothed values
                tr_smooth = pd.Series(tr).rolling(window=length).mean()
                dm_plus_smooth = pd.Series(dm_plus).rolling(window=length).mean()
                dm_minus_smooth = pd.Series(dm_minus).rolling(window=length).mean()
                
                # Directional Indicators
                di_plus = 100 * (dm_plus_smooth / tr_smooth)
                di_minus = 100 * (dm_minus_smooth / tr_smooth)
                
                # ADX
                dx = 100 * abs(di_plus - di_minus) / (di_plus + di_minus)
                adx = dx.rolling(window=length).mean()
                
                return adx.fillna(0)
            
            adx = calculate_adx(high, low, close, length=14)
            
            # Determinar direção
            direction = 'up' if ema_fast.iloc[-1] > ema_slow.iloc[-1] else 'down'
            
            # Normalizar força (ADX de 0-40 para 0-1)
            strength = min(adx.iloc[-1] / 40.0, 1.0) if not pd.isna(adx.iloc[-1]) else 0.0
            
            # Inicializar cache se não existir
            if not hasattr(self, '_trend_cache'):
                self._trend_cache = {}
            
            # Armazenar no cache
            result = (direction, strength)
            self._trend_cache[cache_key] = (current_time, result)
            
            self.logger.debug(f"Tendência {symbol} ({tf}): {direction} | Força: {strength:.3f} | ADX: {adx.iloc[-1]:.1f}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Erro ao detectar tendência para {symbol}: {e}")
            return ('neutral', 0.0)

    async def _apply_slope_vwap_filter(self, symbol: str, tf: str, action: str, tf_data: dict) -> str:
        """Aplica filtros de Slope e VWAP para evitar entradas em consolidação ou preços distantes"""
        try:
            # Obter dados históricos para cálculos
            ohlcv_data = await self._get_real_ohlcv(symbol, tf, 50)
            
            if ohlcv_data is None or len(ohlcv_data) < 20:
                return action  # Se não há dados suficientes, manter ação original
            
            import numpy as np
            import pandas as pd
            
            close = ohlcv_data['close']
            high = ohlcv_data['high']
            low = ohlcv_data['low']
            volume = ohlcv_data['volume']
            
            # FILTRO 1: SLOPE - Evitar entradas em consolidação
            ema_fast = close.ewm(span=21).mean()  # EMA mais rápida para slope
            
            # Calcular slope dos últimos 6 períodos
            if len(ema_fast) >= 6:
                slope_radians = np.arctan((ema_fast.iloc[-1] - ema_fast.iloc[-6]) / 6)
                slope_degrees = np.degrees(slope_radians)
                
                # Se slope muito baixo (< 5°), considerar consolidação
                if abs(slope_degrees) < 5:
                    self.logger.debug(f"Filtro Slope: {symbol} {tf} em consolidação (slope={slope_degrees:.2f}°), convertendo {action} -> HOLD")
                    return 'HOLD'
            
            # FILTRO 2: VWAP - Evitar entradas muito distantes do VWAP
            # Calcular VWAP ancorado no início do período (últimas 24 horas de dados)
            typical_price = (high + low + close) / 3
            vwap = (typical_price * volume).cumsum() / volume.cumsum()
            current_price = close.iloc[-1]
            current_vwap = vwap.iloc[-1]
            
            # Calcular ATR para determinar distância aceitável
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = tr.rolling(window=14).mean().iloc[-1]
            
            # Verificar se preço está muito distante do VWAP (> 3 ATR)
            vwap_distance = abs(current_price - current_vwap)
            max_vwap_distance = 3 * atr
            
            if vwap_distance > max_vwap_distance:
                # Verificar se a entrada é na direção de retorno ao VWAP
                price_above_vwap = current_price > current_vwap
                
                if (price_above_vwap and action == 'BUY') or (not price_above_vwap and action == 'SELL'):
                    # Entrada se afastando ainda mais do VWAP - bloquear
                    self.logger.debug(f"Filtro VWAP: {symbol} {tf} muito distante do VWAP ({vwap_distance/atr:.1f} ATR), convertendo {action} -> HOLD")
                    return 'HOLD'
                else:
                    # Entrada na direção do VWAP - permitir mas com cautela
                    self.logger.debug(f"Filtro VWAP: {symbol} {tf} distante do VWAP mas na direção correta, mantendo {action}")
            
            # Se passou em todos os filtros, manter ação original
            self.logger.debug(f"Filtros Slope/VWAP: {symbol} {tf} passou em todos os filtros, mantendo {action}")
            return action
            
        except Exception as e:
            self.logger.error(f"Erro ao aplicar filtros Slope/VWAP para {symbol} {tf}: {e}")
            return action  # Em caso de erro, manter ação original

    async def _close_position(self, position: Position, exit_price: float, reason: str):
        """Fecha uma posição e realiza PnL"""
        try:
            symbol = position.symbol

            # Calculate exit fee
            exit_fee = self.paper_broker.calculate_fee(exit_price, position.quantity) if self.paper_broker else 0

            # Calculate realized PnL
            if position.side == 'buy':
                gross_pnl = (exit_price - position.entry_price) * position.quantity
            else:
                gross_pnl = (position.entry_price - exit_price) * position.quantity

            # Net PnL after fees
            net_pnl = gross_pnl - position.entry_fee - exit_fee

            # Calculate trade duration
            trade_duration = (datetime.now() - position.entry_time).total_seconds() / 60  # minutes

            # Update comprehensive metrics
            self.positions_closed += 1
            self.realized_pnl += net_pnl
            self.total_pnl += net_pnl
            self.current_balance += net_pnl
            self.total_fees_paid += exit_fee
            self.trade_durations.append(trade_duration)

            if net_pnl > 0:
                self.winning_trades += 1
                self.gross_profit += net_pnl
                self.winning_amounts.append(net_pnl)
                self.current_drawdown = max(0, self.current_drawdown - net_pnl)
            else:
                self.losing_trades += 1
                self.gross_loss += abs(net_pnl)
                self.losing_amounts.append(abs(net_pnl))
                self.current_drawdown += abs(net_pnl)
                self.max_drawdown = max(self.max_drawdown, self.current_drawdown)

            # Update equity curve
            current_portfolio_value = self.current_balance + self._calculate_unrealized_pnl()
            self.portfolio_values.append(current_portfolio_value)
            self.equity_curve.append({
                'timestamp': datetime.now(),
                'portfolio_value': current_portfolio_value,
                'realized_pnl': self.realized_pnl,
                'unrealized_pnl': self._calculate_unrealized_pnl()
            })

            # Logar decisão de fechamento de posição
            if self.metrics_logger:
                decision_id = f"{symbol}_close_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
                
                trading_decision = TradingDecisionMetrics(
                    decision_id=decision_id,
                    symbol=symbol,
                    action="CLOSE_POSITION",
                    signal=f"close_{position.side}",
                    confidence=position.confidence,
                    price=exit_price,
                    quantity=position.quantity,
                    stop_loss=position.stop_loss,
                    take_profit=position.take_profit,
                    entry_fee=position.entry_fee,
                    exit_fee=exit_fee,
                    gross_pnl=gross_pnl,
                    net_pnl=net_pnl,
                    trade_duration_minutes=trade_duration,
                    balance_before=self.current_balance - net_pnl,
                    balance_after=self.current_balance,
                    position_count=len(self.open_positions) - 1,  # Will be one less after removal
                    daily_trades_count=self.daily_trades_count,
                    reasoning=f"Closed {position.side} position due to {reason}, held for {trade_duration:.1f}min, PnL: ${net_pnl:.4f}"
                )
                
                self.metrics_logger.log_trading_decision(trading_decision)

            # Log the trade
            self.logger.info(f"Position closed: {position.side} {symbol} | "
                           f"Entry: ${position.entry_price:.2f} | "
                           f"Exit: ${exit_price:.2f} | "
                           f"Quantity: {position.quantity:.6f} | "
                           f"Gross PnL: ${gross_pnl:.4f} | "
                           f"Fees: ${position.entry_fee + exit_fee:.4f} | "
                           f"Net PnL: ${net_pnl:.4f} | "
                           f"Reason: {reason} | "
                           f"Balance: ${self.current_balance:.2f}")

            # Move to closed positions and remove from open
            position.unrealized_pnl = net_pnl
            self.closed_positions.append(position)
            del self.open_positions[symbol]

            # Save trade record
            await self._save_trade(f"close_{position.side}", position.confidence,
                                 type('Context', (), {'symbol': symbol, 'current_price': exit_price})(),
                                 net_pnl)

        except Exception as e:
            self.logger.error(f"Erro ao fechar posição: {e}")

    async def _update_open_positions(self):
        """Atualiza preços atuais de todas as posições abertas"""
        for symbol, position in list(self.open_positions.items()):
            try:
                # Get current market data
                market_data = await self._get_market_data(symbol, "1m")
                if market_data:
                    current_price = market_data['price']
                    position.update_current_price(current_price)

                    # Check if position should be closed due to stop loss/take profit
                    if self._should_close_position(position, position.side, 0.0):
                        reason = "stop_loss" if (
                            (position.side == 'buy' and current_price <= position.stop_loss) or
                            (position.side == 'sell' and current_price >= position.stop_loss)
                        ) else "take_profit"
                        await self._close_position(position, current_price, reason)

            except Exception as e:
                self.logger.error(f"Erro ao atualizar posição {symbol}: {e}")

    def _calculate_unrealized_pnl(self) -> float:
        """Calcula PnL não realizado de todas as posições abertas"""
        total_unrealized = 0.0
        for position in self.open_positions.values():
            total_unrealized += position.unrealized_pnl
        return total_unrealized

    def _calculate_portfolio_value(self) -> float:
        """Calcula valor total do portfólio (cash + posições abertas)"""
        return self.current_balance + self._calculate_unrealized_pnl()

    async def _save_trade(self, signal_action: str, confidence: float, context, pnl: float):
        """Salva informações do trade"""
        trade_data = {
            'timestamp': datetime.now().isoformat(),
            'symbol': context.symbol,
            'action': signal_action,
            'confidence': confidence,
            'pnl': pnl,
            'total_pnl': self.total_pnl,
            'current_balance': self.current_balance,
            'trade_number': self.trades_executed
        }
        
        # Criar diretório de logs se não existir
        Path('logs').mkdir(exist_ok=True)
        
        # Salvar em arquivo de trades
        trades_file = self.config['logging']['files'].get('trades', 'logs/fwh_scalp_trades.log')
        with open(trades_file, 'a') as f:
            f.write(json.dumps(trade_data) + '\n')
    
    async def _check_emergency_conditions(self) -> bool:
        """Verifica condições de parada de emergência"""
        emergency_config = self.config['security']['emergency_stop']
        
        # Verificar perda máxima diária
        if self.total_pnl < -emergency_config['max_daily_loss_pct'] * 10:  # Simplificado
            return True
        
        # Verificar perdas consecutivas
        if self.losing_trades >= emergency_config['max_consecutive_losses']:
            return True
        
        # Verificar drawdown máximo
        if self.current_drawdown > emergency_config['max_daily_loss_pct'] * 10:
            return True
        
        return False
    
    async def _update_metrics(self):
        """Atualiza métricas de performance avançadas"""
        # Update unrealized PnL
        self.unrealized_pnl = self._calculate_unrealized_pnl()

        # Calculate comprehensive metrics
        metrics = self._calculate_comprehensive_metrics()

        # Save metrics in real-time
        Path('logs').mkdir(exist_ok=True)
        metrics_file = self.config['monitoring']['metrics_file']
        with open(metrics_file, 'w') as f:
            json.dump(metrics, f, indent=2)

    def _calculate_comprehensive_metrics(self) -> dict:
        """Calcula métricas abrangentes de performance quantitativa"""
        import numpy as np
        from math import sqrt

        # Basic metrics
        total_positions = self.positions_opened
        open_positions_count = len(self.open_positions)
        closed_positions_count = self.positions_closed

        # Portfolio value
        current_portfolio_value = self._calculate_portfolio_value()
        initial_capital = self.initial_balance
        total_return = (current_portfolio_value - initial_capital) / initial_capital

        # Win rate and trade metrics
        win_rate = (self.winning_trades / max(closed_positions_count, 1)) * 100
        avg_win = np.mean(self.winning_amounts) if self.winning_amounts else 0.0
        avg_loss = np.mean(self.losing_amounts) if self.losing_amounts else 0.0
        win_loss_ratio = avg_win / max(avg_loss, 0.001)

        # Profit factor
        profit_factor = self.gross_profit / max(self.gross_loss, 0.001)

        # Average trade duration
        avg_trade_duration = np.mean(self.trade_durations) if self.trade_durations else 0.0

        # Risk metrics
        returns = self._calculate_returns()
        sharpe_ratio = self._calculate_sharpe_ratio(returns)
        sortino_ratio = self._calculate_sortino_ratio(returns)
        calmar_ratio = self._calculate_calmar_ratio(total_return)
        recovery_factor = self._calculate_recovery_factor()

        # Drawdown metrics
        max_drawdown_pct = (self.max_drawdown / initial_capital) * 100
        current_drawdown_pct = (self.current_drawdown / initial_capital) * 100

        return {
            'timestamp': datetime.now().isoformat(),
            'runtime_minutes': (datetime.now() - self.start_time).total_seconds() / 60 if self.start_time else 0,

            # Position metrics
            'total_positions_opened': total_positions,
            'open_positions': open_positions_count,
            'closed_positions': closed_positions_count,

            # PnL metrics
            'realized_pnl': self.realized_pnl,
            'unrealized_pnl': self.unrealized_pnl,
            'total_pnl': self.realized_pnl + self.unrealized_pnl,
            'gross_profit': self.gross_profit,
            'gross_loss': self.gross_loss,
            'total_fees_paid': self.total_fees_paid,

            # Portfolio metrics
            'initial_capital': initial_capital,
            'current_portfolio_value': current_portfolio_value,
            'total_return_pct': total_return * 100,
            'current_balance': self.current_balance,

            # Performance metrics
            'win_rate_pct': win_rate,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'win_loss_ratio': win_loss_ratio,
            'profit_factor': profit_factor,
            'avg_trade_duration_minutes': avg_trade_duration,

            # Risk metrics
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'calmar_ratio': calmar_ratio,
            'recovery_factor': recovery_factor,
            'max_drawdown_usd': self.max_drawdown,
            'max_drawdown_pct': max_drawdown_pct,
            'current_drawdown_usd': self.current_drawdown,
            'current_drawdown_pct': current_drawdown_pct,

            # Additional metrics
            'trades_per_hour': total_positions / max((datetime.now() - self.start_time).total_seconds() / 3600, 0.001) if self.start_time else 0,
            'portfolio_volatility': np.std(returns) * sqrt(252) if len(returns) > 1 else 0.0,
        }

    def _calculate_returns(self) -> list:
        """Calcula retornos baseados na curva de equity"""
        if len(self.portfolio_values) < 2:
            return []

        returns = []
        for i in range(1, len(self.portfolio_values)):
            ret = (self.portfolio_values[i] - self.portfolio_values[i-1]) / self.portfolio_values[i-1]
            returns.append(ret)
        return returns

    def _calculate_sharpe_ratio(self, returns: list) -> float:
        """Calcula Sharpe Ratio (assumindo risk-free rate = 0)"""
        if len(returns) < 2:
            return 0.0

        import numpy as np
        from math import sqrt

        mean_return = np.mean(returns)
        std_return = np.std(returns)

        if std_return == 0:
            return 0.0

        # Annualized Sharpe ratio (assuming daily returns)
        return (mean_return / std_return) * sqrt(252)

    def _calculate_sortino_ratio(self, returns: list) -> float:
        """Calcula Sortino Ratio (downside deviation)"""
        if len(returns) < 2:
            return 0.0

        import numpy as np
        from math import sqrt

        mean_return = np.mean(returns)
        negative_returns = [r for r in returns if r < 0]

        if not negative_returns:
            return float('inf') if mean_return > 0 else 0.0

        downside_deviation = np.std(negative_returns)

        if downside_deviation == 0:
            return 0.0

        # Annualized Sortino ratio
        return (mean_return / downside_deviation) * sqrt(252)

    def _calculate_calmar_ratio(self, total_return: float) -> float:
        """Calcula Calmar Ratio (Annual Return / Max Drawdown)"""
        if self.max_drawdown == 0:
            return float('inf') if total_return > 0 else 0.0

        # Annualize the return (assuming current period represents full year for simplicity)
        max_drawdown_pct = self.max_drawdown / self.initial_balance
        return total_return / max_drawdown_pct

    def _calculate_recovery_factor(self) -> float:
        """Calcula Recovery Factor (Net Profit / Max Drawdown)"""
        if self.max_drawdown == 0:
            return float('inf') if self.total_pnl > 0 else 0.0

        return self.total_pnl / self.max_drawdown
    
    def get_performance_metrics(self) -> dict:
        """Retorna métricas de performance para o calibrador"""
        return self._calculate_comprehensive_metrics()
    
    async def _log_status(self):
        """Log do status atual do sistema com métricas abrangentes"""
        # Calculate current metrics
        unrealized_pnl = self._calculate_unrealized_pnl()
        portfolio_value = self._calculate_portfolio_value()
        total_return_pct = ((portfolio_value - self.initial_balance) / self.initial_balance) * 100

        # Basic metrics
        open_positions = len(self.open_positions)
        closed_positions = self.positions_closed

        if self.positions_opened > 0:
            runtime = datetime.now() - self.start_time if self.start_time else timedelta(0)

            # Calculate win rate based on closed positions
            win_rate = (self.winning_trades / max(closed_positions, 1)) * 100

            # Calculate profit factor
            profit_factor = self.gross_profit / max(self.gross_loss, 0.001)

            self.logger.info(
                f" TRADING STATUS | "
                f"Runtime: {str(runtime).split('.')[0]} | "
                f"Positions: {self.positions_opened} opened, {open_positions} open, {closed_positions} closed"
            )

            self.logger.info(
                f" PERFORMANCE | "
                f"Portfolio: ${portfolio_value:.2f} ({total_return_pct:+.2f}%) | "
                f"Realized: ${self.realized_pnl:.2f} | "
                f"Unrealized: ${unrealized_pnl:.2f} | "
                f"Fees: ${self.total_fees_paid:.2f}"
            )

            if closed_positions > 0:
                self.logger.info(
                    f" TRADE METRICS | "
                    f"Win Rate: {win_rate:.1f}% ({self.winning_trades}W/{self.losing_trades}L) | "
                    f"Profit Factor: {profit_factor:.2f} | "
                    f"Max DD: ${self.max_drawdown:.2f}"
                )
        else:
            self.logger.info(f" STATUS | No positions opened yet | Portfolio: ${portfolio_value:.2f}")
    
    async def _shutdown(self):
        """Shutdown graceful do sistema"""
        self.logger.info("Iniciando shutdown do sistema...")
        
        # Salvar métricas finais
        await self._save_final_metrics()
        
        # Log de resumo final
        if self.trades_executed > 0:
            win_rate = (self.winning_trades / self.trades_executed) * 100
            self.logger.info(
                f"Resumo Final: {self.trades_executed} trades | "
                f"Win Rate: {win_rate:.1f}% | "
                f"PnL Total: ${self.total_pnl:.2f} | "
                f"Saldo Final: ${self.current_balance:.2f}"
            )
        
        self.logger.info("Sistema finalizado com sucesso")
    
    async def _save_final_metrics(self):
        """Salva métricas finais"""
        runtime = datetime.now() - self.start_time if self.start_time else timedelta(0)
        win_rate = (self.winning_trades / self.trades_executed * 100) if self.trades_executed > 0 else 0
        
        final_metrics = {
            'session_start': self.start_time.isoformat() if self.start_time else None,
            'session_end': datetime.now().isoformat(),
            'runtime_seconds': runtime.total_seconds(),
            'total_trades': self.trades_executed,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate_pct': win_rate,
            'total_pnl': self.total_pnl,
            'max_drawdown': self.max_drawdown,
            'avg_pnl_per_trade': self.total_pnl / self.trades_executed if self.trades_executed > 0 else 0,
            'trades_per_hour': self.trades_executed / (runtime.total_seconds() / 3600) if runtime.total_seconds() > 0 else 0
        }
        
        # Salvar métricas
        metrics_file = self.config['monitoring']['metrics_file']
        with open(metrics_file, 'w') as f:
            json.dump(final_metrics, f, indent=2)
        
        # Log das métricas finais
        self.logger.info("=== MÉTRICAS FINAIS ===")
        self.logger.info(f"Runtime: {runtime}")
        self.logger.info(f"Total de Trades: {self.trades_executed}")
        self.logger.info(f"Win Rate: {win_rate:.1f}%")
        self.logger.info(f"PnL Total: ${self.total_pnl:.2f}")
        self.logger.info(f"Max Drawdown: ${self.max_drawdown:.2f}")
        self.logger.info(f"Trades/Hora: {final_metrics['trades_per_hour']:.1f}")

async def main():
    """Função principal"""
    print("FWH Scalp Paper Trading System")
    print("=" * 50)
    print("Iniciando sistema de paper trading para scalping...")
    print("Pressione Ctrl+C para parar o sistema")
    print("=" * 50)
    
    # Caminho para a configuração
    config_path = Path(__file__).parent.parent / "config" / "fwh_scalp_config.yaml"
    
    if not config_path.exists():
        print(f"Erro: Arquivo de configuração não encontrado: {config_path}")
        sys.exit(1)
    
    try:
        # Inicializar sistema
        system = FWHScalpPaperTradingSystem(str(config_path))
        
        # Inicializar componentes
        await system.initialize_system()
        
        # Iniciar trading
        await system.start_trading()
        
    except KeyboardInterrupt:
        print("\nSistema interrompido pelo usuário")
    except Exception as e:
        print(f"Erro fatal: {e}")
        sys.exit(1)
    finally:
        print("Sistema finalizado")

if __name__ == "__main__":
    # Executar sistema
    asyncio.run(main())
