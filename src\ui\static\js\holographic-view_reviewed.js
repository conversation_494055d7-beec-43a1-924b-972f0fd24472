// Revisado em 2025-06-13 por Codex
/**
 * QUALIA: Visualização Holográfica em Tempo Real
 *
 * Implementação baseada no documento "Implementação de Interface de Visualização Quântica".
 * Utiliza Three.js para renderizar um campo quântico holográfico com otimizações
 * de LOD, frustum culling e controle de taxa de quadros.
 */

class PIDController {
  constructor(kp = 0.1, ki = 0.01, kd = 0.05) {
    this.kp = kp;
    this.ki = ki;
    this.kd = kd;
    this.integral = 0;
    this.prevError = 0;
  }

  update(error) {
    this.integral += error;
    const derivative = error - this.prevError;
    this.prevError = error;
    return this.kp * error + this.ki * this.integral + this.kd * derivative;
  }
}

const TARGET_FPS = 60;
const TARGET_LATENCY = 100;
const lodHistory = [];
let lodValue = 1;
const lodPID = new PIDController();

function computeLOD(avgFPS, latency, distance) {
  const baseLOD = distance < 5 ? 2 : distance < 10 ? 1 : 0;
  const error = TARGET_FPS - avgFPS + (latency - TARGET_LATENCY) / 20;
  lodValue += lodPID.update(error);
  lodValue = Math.max(0, Math.min(baseLOD, lodValue));
  const rounded = Math.round(lodValue);
  lodHistory.push({ t: performance.now(), v: rounded });
  return rounded;
}

document.addEventListener('DOMContentLoaded', () => {

let scene, camera, renderer, shaderMaterial, geometry, mesh;
let uniforms;
// --- SIM Graph ------------------------------------------------------------------
let simGraphData = { nodes: [], links: [] };
let simGraphStats = { cycles: [], features: {} };

function startSimGraphPolling() {
  fetchSimGraph();
  fetchSimGraphStats();
  setInterval(fetchSimGraph, 2000);
  setInterval(fetchSimGraphStats, 2000);
}

async function fetchSimGraph() {
  try {
    const res = await fetch('/api/sim_graph');
    if (!res.ok) return;
    const data = await res.json();
    simGraphData = data;
    updateSimGraphVisualization();
  } catch (err) {
    console.warn('Falha ao obter SIM graph', err);
  }
}

async function fetchSimGraphStats() {
  try {
    const res = await fetch('/api/sim_graph/stats');
    if (!res.ok) return;
    const data = await res.json();
    simGraphStats = data;
    updateSimGraphStats();
  } catch (err) {
    console.warn('Falha ao obter estatísticas do SIM graph', err);
  }
}

function updateSimGraphVisualization() {
  // Placeholder: por ora apenas imprime no console.
  // Futuro: integrar com force-graph ou Three.js nodes.
  console.debug('SIM graph snapshot', simGraphData);
}

function updateSimGraphStats() {
  console.debug('SIM graph stats', simGraphStats);
}
// -------------------------------------------------------------------------------

const container = document.getElementById('holographic-view');
if (!container) {
  console.error("Elemento holographic-view não encontrado");
  // O return aqui vai parar a execução do script se o container não for achado.
  // Isso é um comportamento "fail-fast" desejável.
} else {
    // Flag para controlar a transição da animação inicial para os dados ao vivo
    let hasReceivedData = false;

    const width = container.clientWidth;
    const height = container.clientHeight;

    // Cena básica Three.js
    scene = new THREE.Scene();
    camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
    camera.position.set(0, 0, 5);

    renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(width, height);
    container.appendChild(renderer.domElement);
    window.addEventListener("resize", onResize);

    // Inicialização dos uniforms com valores padrão. Essencial para a animação de loading.
    uniforms = {
      time: { value: 0.0 },
      coherence: { value: 0.2 },
      entanglement: { value: 0.1 },
      resonance: { value: 0.0 },
      fieldStrength: { value: 0.3 },
      consciousness: { value: 0.0 },
      liquidityBuckets: { value: new THREE.Vector3(0, 0, 0) },
      trendStrength: { value: 0.0 },
      deltaEntropy: { value: 0.0 },
      lodLevel: { value: 1 }
    };

    const socket = io('/ws/holographic', {
      path: '/socket.io',
      transports: ['websocket'],
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      reconnectionAttempts: Infinity,
    });

    socket.on('connect', () => {
      console.log('Conectado ao servidor WebSocket para visualização holográfica.');
      socket.emit('request_initial_data');
      // Inicia polling do grafo SIM a cada 2s
      startSimGraphPolling();
    });

    // Constantes físicas
    const PLANCK = 6.62607015e-34;
    const HBAR = PLANCK / (2 * Math.PI);
    const C = 299792458;
    console.debug("Constantes físicas carregadas", PLANCK, HBAR, C);

    // Estado quântico (mockado)
    const quantumState = {
      coherence: 0.5,
      entanglement: 0.5,
      metrics: {
        resonance: 0.5,
        fieldStrength: 0.5,
        consciousness: 0.5,
      },
    };

    // Controle de performance
    const fpsBuffer = [];
    const latencyBuffer = [];
    let lastFrameTime = performance.now();
    let targetFPS = 60;
    let serverFPS = null;
    let serverLatency = null;

    function onResize() {
      const { clientWidth, clientHeight } = container;
      camera.aspect = clientWidth / clientHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(clientWidth, clientHeight);
    }

    function updateFPS() {
      const now = performance.now();
      const delta = now - lastFrameTime;
      const fps = delta > 0 ? 1000 / delta : 0;
      fpsBuffer.push(fps);
      if (fpsBuffer.length > 60) fpsBuffer.shift();
      const avg = fpsBuffer.reduce((a, b) => a + b, 0) / fpsBuffer.length;
      targetFPS = avg < 30 ? 30 : avg < 45 ? 45 : 60;
      lastFrameTime = now;
    }

    function getTargetFPS() {
      return serverFPS ? Math.min(targetFPS, serverFPS) : targetFPS;
    }

    function getLODLevel(distance) {
      const fps = getTargetFPS();
      const latencyAvg =
        latencyBuffer.length > 0
          ? latencyBuffer.reduce((a, b) => a + b, 0) / latencyBuffer.length
          : 0;
      return computeLOD(fps, latencyAvg, distance);
    }

    let lastMetricsSent = performance.now();

    function sendMetrics() {
      if (!fpsBuffer.length) return;
      const avg = fpsBuffer.reduce((a, b) => a + b, 0) / fpsBuffer.length;
      const latencyAvg =
        latencyBuffer.length > 0
          ? latencyBuffer.reduce((a, b) => a + b, 0) / latencyBuffer.length
          : null;
      const payload = { avgFPS: avg };
      if (latencyAvg !== null) {
        payload.latency = latencyAvg;
      }
      fetch("/api/hud/metrics", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      }).catch((err) => console.warn("Falha ao enviar métricas", err));
    }

    const frustum = new THREE.Frustum();
    const projMatrix = new THREE.Matrix4();

    function isInFrustum(object, cam) {
      projMatrix.multiplyMatrices(cam.projectionMatrix, cam.matrixWorldInverse);
      frustum.setFromProjectionMatrix(projMatrix);
      return frustum.intersectsObject(object);
    }

    class Octree {
      constructor(boundBox, depth = 0, maxDepth = 8, maxObjects = 8) {
        this.boundBox = boundBox.clone();
        this.depth = depth;
        this.maxDepth = maxDepth;
        this.maxObjects = maxObjects;
        this.objects = [];
        this.children = [];
      }

      insert(object) {
        if (this.children.length > 0) {
          const idx = this._getIndex(object);
          if (idx !== -1) {
            this.children[idx].insert(object);
            return;
          }
        }
        this.objects.push(object);

        if (
          this.objects.length > this.maxObjects &&
          this.depth < this.maxDepth
        ) {
          if (this.children.length === 0) {
            this._split();
          }
          let i = 0;
          while (i < this.objects.length) {
            const obj = this.objects[i];
            const idx = this._getIndex(obj);
            if (idx !== -1) {
              this.children[idx].insert(obj);
              this.objects.splice(i, 1);
            } else {
              i += 1;
            }
          }
        }
      }

      retrieve(frustum, result = []) {
        if (!frustum.intersectsBox(this.boundBox)) {
          return result;
        }
        for (const obj of this.objects) {
          if (frustum.intersectsObject(obj)) {
            result.push(obj);
          }
        }
        for (const child of this.children) {
          child.retrieve(frustum, result);
        }
        return result;
      }

      _split() {
        const { min, max } = this.boundBox;
        const size = new THREE.Vector3()
          .subVectors(max, min)
          .multiplyScalar(0.5);
        const offsets = [
          [0, 0, 0],
          [1, 0, 0],
          [0, 1, 0],
          [1, 1, 0],
          [0, 0, 1],
          [1, 0, 1],
          [0, 1, 1],
          [1, 1, 1],
        ];
        for (let i = 0; i < 8; i++) {
          const off = new THREE.Vector3(
            offsets[i][0] * size.x,
            offsets[i][1] * size.y,
            offsets[i][2] * size.z
          );
          const boxMin = min.clone().add(off);
          const boxMax = boxMin.clone().add(size);
          const childBox = new THREE.Box3(boxMin, boxMax);
          this.children[i] = new Octree(
            childBox,
            this.depth + 1,
            this.maxDepth,
            this.maxObjects
          );
        }
      }

      _getIndex(object) {
        const objectBox = new THREE.Box3().setFromObject(object);
        for (let i = 0; i < this.children.length; i++) {
          if (this.children[i].boundBox.containsBox(objectBox)) {
            return i;
          }
        }
        return -1;
      }
    }

    // Animação de "despertar" para o estado inicial antes de receber dados.
    function updateInitialAnimation() {
        const elapsedTime = clock.getElapsedTime();
        // Cria uma pulsação lenta e suave, oscilando entre 0 e 1.
        const pulse = 0.5 * (1.0 + Math.sin(elapsedTime * 0.5));

        // Modula os uniforms para criar um efeito de "respiração".
        uniforms.coherence.value = 0.2 + pulse * 0.3;      // Coerência baixa, mas pulsante.
        uniforms.consciousness.value = pulse * 0.5;       // A cor da "consciência" pulsa suavemente.
        uniforms.fieldStrength.value = 0.3 + pulse * 0.4; // O brilho (glow) pulsa.
        uniforms.resonance.value = pulse * 0.2;           // Uma leve oscilação na forma.
    }

    function handleSnapshot(data) {
      // Na primeira mensagem recebida, desativa a animação inicial.
      if (!hasReceivedData) {
        console.log("Primeiro snapshot recebido. Transicionando para dados ao vivo.");
        hasReceivedData = true;
      }

      try {
        // Mapear os nomes das métricas do servidor para os nomes dos uniforms
        if (typeof data.coherence === "number") {
          uniforms.coherence.value = data.coherence;
        }
        
        // Usar valores padrão para métricas que não estão disponíveis no servidor
        uniforms.entanglement.value = typeof data.entanglement === "number" ? 
          data.entanglement : 0.5;
        
        // Mapear outras métricas do servidor
        if (typeof data.self_reflection_depth === "number") {
          uniforms.resonance.value = data.self_reflection_depth / 10.0; // Normalizar
        }
        
        if (typeof data.pattern_recognition_rate === "number") {
          uniforms.fieldStrength.value = data.pattern_recognition_rate;
        }
        
        if (typeof data.quantum_entropy === "number") {
          uniforms.deltaEntropy.value = data.quantum_entropy;
          // Também usar entropia quântica para consciência como fallback
          if (typeof data.consciousness !== "number") {
            uniforms.consciousness.value = 1.0 - data.quantum_entropy; // Inverso da entropia
          }
        }
        
        // Outros valores específicos
        if (typeof data.consciousness === "number") {
          uniforms.consciousness.value = data.consciousness;
        }
        
        // Métricas de desempenho
        if (typeof data.fps === "number") {
          serverFPS = data.fps;
        }
        if (typeof data.latency === "number") {
          serverLatency = data.latency;
        } else if (typeof data.timestamp === "number") {
          const serverTime = data.timestamp > 1e12 ? data.timestamp : data.timestamp * 1000;
          const latency = performance.now() - serverTime;
          latencyBuffer.push(latency);
          if (latencyBuffer.length > 60) latencyBuffer.shift();
          serverLatency = latency;
        }
        
        console.debug("Snapshot processado:", data);
        
      } catch (err) {
        console.error("Invalid SocketIO message", err, data);
      }
    }

    socket.on("snapshot", handleSnapshot);

    // Tratamento de desconexão
    socket.on("disconnect", () => {
      console.warn("Desconectado do servidor WebSocket");
      // Tenta reconectar automaticamente
      setTimeout(() => {
        socket.connect();
      }, 5000);
    });

    shaderMaterial = new THREE.ShaderMaterial({
      uniforms: uniforms,
      vertexShader: `
              uniform float time;
              uniform float coherence;
              uniform float entanglement;
              uniform float resonance;
              uniform vec3 liquidityBuckets;
              uniform float trendStrength;
              uniform float deltaEntropy;
              uniform int lodLevel;

              varying vec3 vPosition;
              varying vec3 vNormal;

              float psi(vec3 p, float t) {
                  float phase = dot(p, vec3(1.0)) * (10.0 / float(lodLevel + 1)) + t;
                  return cos(phase) * exp(-length(p) * (1.0 - coherence));
              }

              vec3 applyEntanglement(vec3 p) {
                  float factor = entanglement * sin(time * 0.5);
                  vec3 displacement = vec3(
                      sin(p.y * 5.0),
                      cos(p.z * 5.0),
                      sin(p.x * 5.0)
                  );
                  return p * (1.0 + 0.2 * factor * displacement);
              }

              void main() {
                  vPosition = position;
                  vNormal = normal;
                  vec3 pos = position;

                  if (lodLevel < 2) {
                      float wave = psi(pos, time);
                      pos += normal * wave * 0.2;
                      if (lodLevel < 1) {
                          pos = applyEntanglement(pos);
                      }
                  }

                  vec3 trendVec = normalize(liquidityBuckets * 2.0 - 1.0);
                  pos += trendVec * trendStrength * 0.5;
                  pos *= 1.0 + 0.1 * resonance * sin(time + deltaEntropy);
                  gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
              }
          `,
      fragmentShader: `
              uniform float time;
              uniform float coherence;
              uniform float fieldStrength;
              uniform float consciousness;
              uniform vec3 liquidityBuckets;
              uniform float deltaEntropy;
              uniform int lodLevel;

              varying vec3 vPosition;
              varying vec3 vNormal;

              void main() {
                  vec3 baseColor = vec3(0.1, 0.4, 0.8);
                  float interference = 0.0;

                  if (lodLevel < 2) {
                      interference = sin(vPosition.x * 20.0 + time) *
                                     cos(vPosition.y * 15.0 + time * 0.7) *
                                     coherence;
                  }

                  float glow = 0.5 + 0.5 * fieldStrength * (0.5 + 0.5 * sin(time * 0.5));

                  vec3 consciousColor = vec3(
                      0.5 + 0.5 * sin(consciousness * 6.28),
                      0.5 + 0.5 * sin(consciousness * 6.28 + 2.09),
                      0.5 + 0.5 * sin(consciousness * 6.28 + 4.18)
                  );

                  vec3 color = mix(baseColor, consciousColor, consciousness) * glow;

                  if (lodLevel < 2) {
                      color += vec3(0.1, 0.2, 0.4) * interference;
                  }

                  color += liquidityBuckets * 0.3;
                  color = mix(color, vec3(1.0, 0.0, 0.0), clamp(deltaEntropy, 0.0, 1.0));

                  gl_FragColor = vec4(color, 0.9);
              }
          `,
      transparent: true,
      side: THREE.DoubleSide,
    });

    geometry = new THREE.TorusKnotGeometry(1, 0.3, 128, 32, 2, 3);
    mesh = new THREE.Mesh(geometry, shaderMaterial);
    scene.add(mesh);

    const sceneBox = new THREE.Box3().setFromObject(mesh).expandByScalar(2);
    const octree = new Octree(sceneBox);
    octree.insert(mesh);

    function getVisibleObjects(cam) {
      projMatrix.multiplyMatrices(cam.projectionMatrix, cam.matrixWorldInverse);
      frustum.setFromProjectionMatrix(projMatrix);
      return octree.retrieve(frustum, []);
    }

    const clock = new THREE.Clock();
    let targetFrameTime = 1000 / 60;
    let lastRender = performance.now();
    const USE_OCTREE = true;
    let cullingTime = 0;
    let cullingCount = 0;

    function animate() {
      // Otimização: se o container não existir, não faz nada.
      if (!container) return;

      requestAnimationFrame(animate);

      const now = performance.now();
      targetFrameTime = 1000 / getTargetFPS();
      const delta = now - lastRender;
      if (delta < targetFrameTime) {
        return;
      }
      lastRender = now;

      // Se nenhum dado foi recebido ainda, executa a animação de "despertar".
      if (!hasReceivedData) {
        updateInitialAnimation();
      }

      updateFPS();
      if (now - lastMetricsSent > 5000) {
        sendMetrics();
        lastMetricsSent = now;
      }

      const cullStart = performance.now();
      const visible = USE_OCTREE
        ? getVisibleObjects(camera).includes(mesh)
        : isInFrustum(mesh, camera);
      cullingTime += performance.now() - cullStart;
      cullingCount += 1;
      if (!visible) {
        return;
      }

      const distance = camera.position.distanceTo(mesh.position);
      const lodLevel = getLODLevel(distance);
      uniforms.lodLevel.value = lodLevel;
      uniforms.time.value += clock.getDelta();
      renderer.render(scene, camera);

      if (now - lastMetricsSent > 5000 && cullingCount) {
        const avg = cullingTime / cullingCount;
        console.info(
          `Culling avg (${USE_OCTREE ? "octree" : "simple"}): ${avg.toFixed(3)} ms`
        );
      }
    }

    animate();
}

});

if (typeof module !== "undefined" && module.exports) {
  module.exports = { PIDController, computeLOD };
}

export { PIDController, computeLOD };
