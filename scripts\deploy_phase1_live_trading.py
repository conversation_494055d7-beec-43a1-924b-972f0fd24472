#!/usr/bin/env python3
"""
QUALIA P-02.3: Phase 1 Live Trading Deployment
Comprehensive deployment script for transitioning to live production trading

This script orchestrates the complete Phase 1 implementation:
1. Activate Real Credentials
2. Validate Live Trading Path  
3. Enable Enhanced Monitoring

DEPLOYMENT PROCESS:
- Secure credential setup with real KuCoin API keys
- Comprehensive validation of all QUALIA components
- Enhanced monitoring system activation
- Ultra-conservative safety mechanism verification
- Production readiness assessment
"""

import asyncio
import json
import logging
import os
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import yaml

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/pilot/phase1_deployment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Phase1LiveTradingDeployment:
    """Comprehensive Phase 1 deployment orchestrator"""
    
    def __init__(self):
        self.deployment_start_time = datetime.now()
        self.deployment_steps = []
        self.deployment_status = {}
        
        # Deployment tracking
        self.steps_completed = 0
        self.steps_failed = 0
        self.critical_failures = []
        
        logger.info("Phase 1 Live Trading Deployment initialized")
    
    def log_deployment_step(self, step_name: str, status: str, details: str = ""):
        """Log deployment step with status"""
        step_info = {
            "step": step_name,
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "details": details
        }
        
        self.deployment_steps.append(step_info)
        self.deployment_status[step_name] = status
        
        if status == "COMPLETED":
            self.steps_completed += 1
            logger.info(f"✅ {step_name}: {status}")
        elif status == "FAILED":
            self.steps_failed += 1
            self.critical_failures.append(step_name)
            logger.error(f"❌ {step_name}: {status} - {details}")
        else:
            logger.info(f"🔄 {step_name}: {status}")
    
    def run_script(self, script_path: str, description: str) -> bool:
        """Run a deployment script and capture results"""
        try:
            logger.info(f"Running: {description}")
            
            # Run the script
            result = subprocess.run(
                [sys.executable, script_path],
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode == 0:
                logger.info(f"✅ {description} completed successfully")
                return True
            else:
                logger.error(f"❌ {description} failed:")
                logger.error(f"STDOUT: {result.stdout}")
                logger.error(f"STDERR: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"❌ {description} timed out after 5 minutes")
            return False
        except Exception as e:
            logger.error(f"❌ {description} failed with exception: {e}")
            return False
    
    async def run_async_script(self, script_path: str, description: str) -> bool:
        """Run an async deployment script"""
        try:
            logger.info(f"Running: {description}")
            
            # Import and run the async script
            if "validate_live_trading" in script_path:
                from scripts.validate_live_trading import main as validate_main
                success = await validate_main()
            elif "enhanced_monitoring" in script_path:
                # For monitoring, we'll start it in background
                logger.info("Enhanced monitoring will be started in background")
                success = True
            else:
                success = False
            
            if success:
                logger.info(f"✅ {description} completed successfully")
                return True
            else:
                logger.error(f"❌ {description} failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ {description} failed with exception: {e}")
            return False
    
    def verify_prerequisites(self) -> bool:
        """Verify deployment prerequisites"""
        try:
            self.log_deployment_step("Prerequisites Check", "STARTED")
            
            # Check if pilot configuration exists
            pilot_config = Path("config/pilot_config.yaml")
            if not pilot_config.exists():
                self.log_deployment_step("Prerequisites Check", "FAILED", 
                                        "Pilot configuration not found")
                return False
            
            # Check if logs directory exists
            logs_dir = Path("logs/pilot")
            logs_dir.mkdir(parents=True, exist_ok=True)
            
            # Check if backup directory exists
            backup_dir = Path("backups/credentials")
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Verify Python dependencies
            required_modules = ['cryptography', 'yaml', 'psutil']
            for module in required_modules:
                try:
                    __import__(module)
                except ImportError:
                    self.log_deployment_step("Prerequisites Check", "FAILED", 
                                            f"Missing required module: {module}")
                    return False
            
            self.log_deployment_step("Prerequisites Check", "COMPLETED")
            return True
            
        except Exception as e:
            self.log_deployment_step("Prerequisites Check", "FAILED", str(e))
            return False
    
    def deploy_step_1_activate_credentials(self) -> bool:
        """Deploy Step 1: Activate Real Credentials"""
        try:
            self.log_deployment_step("Step 1: Activate Real Credentials", "STARTED")
            
            # Run credential setup script
            success = self.run_script(
                "scripts/setup_real_credentials.py",
                "Real KuCoin API credentials setup"
            )
            
            if success:
                # Verify credentials were set up correctly
                config_dir = Path("config")
                if (config_dir / ".pilot_credentials").exists() and (config_dir / ".pilot_master.key").exists():
                    self.log_deployment_step("Step 1: Activate Real Credentials", "COMPLETED")
                    return True
                else:
                    self.log_deployment_step("Step 1: Activate Real Credentials", "FAILED", 
                                            "Credential files not found after setup")
                    return False
            else:
                self.log_deployment_step("Step 1: Activate Real Credentials", "FAILED", 
                                        "Credential setup script failed")
                return False
                
        except Exception as e:
            self.log_deployment_step("Step 1: Activate Real Credentials", "FAILED", str(e))
            return False
    
    async def deploy_step_2_validate_trading(self) -> bool:
        """Deploy Step 2: Validate Live Trading Path"""
        try:
            self.log_deployment_step("Step 2: Validate Live Trading Path", "STARTED")
            
            # Run live trading validation
            success = await self.run_async_script(
                "scripts/validate_live_trading.py",
                "Live trading path validation"
            )
            
            if success:
                # Check validation report
                report_file = Path("logs/pilot/live_trading_validation_report.json")
                if report_file.exists():
                    with open(report_file, 'r') as f:
                        report = json.load(f)
                    
                    if report.get('production_ready', False):
                        self.log_deployment_step("Step 2: Validate Live Trading Path", "COMPLETED")
                        return True
                    else:
                        self.log_deployment_step("Step 2: Validate Live Trading Path", "FAILED", 
                                                "System not production ready")
                        return False
                else:
                    self.log_deployment_step("Step 2: Validate Live Trading Path", "FAILED", 
                                            "Validation report not found")
                    return False
            else:
                self.log_deployment_step("Step 2: Validate Live Trading Path", "FAILED", 
                                        "Validation script failed")
                return False
                
        except Exception as e:
            self.log_deployment_step("Step 2: Validate Live Trading Path", "FAILED", str(e))
            return False
    
    def deploy_step_3_enable_monitoring(self) -> bool:
        """Deploy Step 3: Enable Enhanced Monitoring"""
        try:
            self.log_deployment_step("Step 3: Enable Enhanced Monitoring", "STARTED")
            
            # Create monitoring configuration
            monitoring_config = {
                "enabled": True,
                "realtime": {
                    "quantum_metrics": True,
                    "performance_tracking": True,
                    "alert_thresholds": {
                        "pnl_loss_usd": 25.0,
                        "drawdown_pct": 2.0,
                        "position_loss_pct": 0.5
                    }
                },
                "logging": {
                    "level": "INFO",
                    "quantum_decisions": True,
                    "trading_signals": True,
                    "risk_events": True
                }
            }
            
            # Update pilot configuration with monitoring
            pilot_config_path = Path("config/pilot_config.yaml")
            with open(pilot_config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            config['monitoring'] = monitoring_config
            
            with open(pilot_config_path, 'w') as f:
                yaml.dump(config, f, default_flow_style=False, indent=2)
            
            self.log_deployment_step("Step 3: Enable Enhanced Monitoring", "COMPLETED")
            return True
            
        except Exception as e:
            self.log_deployment_step("Step 3: Enable Enhanced Monitoring", "FAILED", str(e))
            return False
    
    def generate_deployment_report(self) -> Dict[str, Any]:
        """Generate comprehensive deployment report"""
        try:
            end_time = datetime.now()
            duration = (end_time - self.deployment_start_time).total_seconds()
            
            total_steps = self.steps_completed + self.steps_failed
            success_rate = (self.steps_completed / total_steps * 100) if total_steps > 0 else 0
            
            report = {
                "deployment_timestamp": self.deployment_start_time.isoformat(),
                "completion_timestamp": end_time.isoformat(),
                "duration_seconds": duration,
                "steps_completed": self.steps_completed,
                "steps_failed": self.steps_failed,
                "success_rate_percent": success_rate,
                "critical_failures": self.critical_failures,
                "deployment_steps": self.deployment_steps,
                "deployment_successful": len(self.critical_failures) == 0 and success_rate == 100.0,
                "production_ready": len(self.critical_failures) == 0 and success_rate == 100.0
            }
            
            # Save report
            report_file = Path("logs/pilot/phase1_deployment_report.json")
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate deployment report: {e}")
            return {}
    
    async def run_full_deployment(self) -> bool:
        """Run complete Phase 1 deployment"""
        try:
            print("\n" + "="*70)
            print("🚀 QUALIA P-02.3: Phase 1 Live Trading Deployment")
            print("="*70)
            print("This will transition the system from demo to live production trading.")
            print("The deployment includes:")
            print("  1. ✅ Activate Real KuCoin API Credentials")
            print("  2. ✅ Validate Live Trading Path with QUALIA Components")
            print("  3. ✅ Enable Enhanced Monitoring with Quantum Metrics")
            print("="*70)
            
            # Confirm deployment
            confirm = input("\nProceed with Phase 1 live trading deployment? (yes/no): ").strip().lower()
            if confirm not in ['yes', 'y']:
                print("Deployment cancelled.")
                return False
            
            logger.info("🚀 Starting Phase 1 live trading deployment...")
            
            # Step 0: Verify prerequisites
            if not self.verify_prerequisites():
                logger.error("❌ Prerequisites check failed")
                return False
            
            # Step 1: Activate real credentials
            if not self.deploy_step_1_activate_credentials():
                logger.error("❌ Step 1 failed: Real credentials activation")
                return False
            
            # Step 2: Validate live trading path
            if not await self.deploy_step_2_validate_trading():
                logger.error("❌ Step 2 failed: Live trading validation")
                return False
            
            # Step 3: Enable enhanced monitoring
            if not self.deploy_step_3_enable_monitoring():
                logger.error("❌ Step 3 failed: Enhanced monitoring setup")
                return False
            
            # Generate deployment report
            report = self.generate_deployment_report()
            
            # Display results
            print("\n" + "="*70)
            print("📊 PHASE 1 DEPLOYMENT RESULTS")
            print("="*70)
            print(f"Duration: {report.get('duration_seconds', 0):.1f} seconds")
            print(f"Steps Completed: {self.steps_completed}")
            print(f"Steps Failed: {self.steps_failed}")
            print(f"Success Rate: {report.get('success_rate_percent', 0):.1f}%")
            print(f"Production Ready: {'✅ YES' if report.get('production_ready') else '❌ NO'}")
            
            if self.critical_failures:
                print(f"\nCritical Failures:")
                for failure in self.critical_failures:
                    print(f"  ❌ {failure}")
            
            if report.get('deployment_successful'):
                print("\n🎉 PHASE 1 DEPLOYMENT SUCCESSFUL!")
                print("✅ QUALIA P-02.3 is now ready for live production trading!")
                print("\nNEXT STEPS:")
                print("1. Start pilot trading: python scripts/start_pilot_trading.py")
                print("2. Monitor trading: python scripts/enhanced_monitoring_system.py")
                print("3. Review performance: Check logs/pilot/ directory")
            else:
                print("\n❌ PHASE 1 DEPLOYMENT FAILED!")
                print("Please address the critical failures before proceeding.")
            
            print("="*70)
            
            return report.get('deployment_successful', False)
            
        except Exception as e:
            logger.error(f"Phase 1 deployment failed: {e}")
            return False

async def main():
    """Main entry point"""
    try:
        deployment = Phase1LiveTradingDeployment()
        success = await deployment.run_full_deployment()
        
        if success:
            print("\n✅ Phase 1 deployment completed successfully!")
            return True
        else:
            print("\n❌ Phase 1 deployment failed!")
            return False
            
    except KeyboardInterrupt:
        print("\n\nDeployment cancelled by user.")
        return False
    except Exception as e:
        logger.error(f"Deployment failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
