# Sistema de Processamento de Notícias QUALIA

## Visão Geral

O sistema QUALIA possui um pipeline sofisticado para processar dados de notícias que vai muito além de análise simples de sentiment. Este documento detalha o fluxo completo desde a coleta RSS até a influência nas decisões de trading, incluindo encoding quântico e integração holográfica.

## 🔄 Arquitetura do Pipeline

```
RSS Feeds → RealDataCollector → RSSTextSentimentEncoder → NewsEvent + Quantum State → HolographicEvent → HolographicUniverse → Pattern Detection → Trading Signals → Decision Engine
```

## 📡 1. Coleta de Dados RSS

### Fontes Configuradas

O sistema coleta notícias de múltiplas fontes RSS especializadas em criptomoedas:

```python
self.news_feeds = [
    "https://cointelegraph.com/rss",
    "https://decrypt.co/feed", 
    "https://cryptonews.com/news/feed/",
    "https://www.coindesk.com/arc/outboundfeeds/rss/"
]
```

### Processo de Coleta

- **Frequência**: Busca periódica a cada 60 segundos
- **Parser**: Utiliza `feedparser` para processar XML/RSS
- **Extração**: Título, resumo, URL, timestamp
- **Rate Limiting**: 0.5s entre feeds para evitar sobrecarga
- **Filtragem**: Top 3 artigos por feed

### Implementação

```python
async def collect_news_events(self) -> List[NewsEvent]:
    news_events = []
    
    for feed_url in self.news_feeds:
        try:
            async with self.session.get(feed_url) as response:
                if response.status == 200:
                    content = await response.text()
                    feed = feedparser.parse(content)
                    
                    for entry in feed.entries[:3]:  # Top 3 por feed
                        # Processa cada entrada...
```

## 🧠 2. Análise de Sentiment Quântico

### RSSTextSentimentEncoder

O coração do sistema é o `RSSTextSentimentEncoder` que converte texto em **estados quânticos**:

```python
class RSSTextSentimentEncoder(QuantumEncoder):
    """Encoder de sentimento textual para um único qubit (Ry).
    
    Converte a polaridade no range [-1, 1] → [0, 1] e mapeia em θ = π/2 * x.
    """
    
    _POS_WORDS = {
        "good", "great", "positive", "up", "gain", 
        "improve", "growth", "success", "record"
    }
    _NEG_WORDS = {
        "bad", "negative", "down", "loss", "decline", 
        "fail", "crisis", "drop"
    }
```

### Processo de Encoding

1. **Análise Textual**: Identifica palavras-chave positivas/negativas
2. **Score de Sentiment**: Calcula polaridade de -1 (negativo) a +1 (positivo)
3. **Normalização**: Converte [-1,1] → [0,1]
4. **Encoding Quântico**: Aplica rotação Ry: `θ = norm * (π/2)`
5. **Estado Quântico**: Gera vetor 2D `[cos(θ), sin(θ)]`

```python
def _encode_single(self, snap: Dict[str, Any]) -> np.ndarray:
    s = self._get_sentiment(snap)
    # Normaliza [-1,1] → [0,1]
    norm = (s + 1.0) / 2.0
    theta = norm * (np.pi / 2.0)
    return np.array([np.cos(theta), np.sin(theta)], dtype=np.float32)
```

### Estrutura de Dados

```python
@dataclass
class NewsEvent:
    """Evento de notícia com encoding quântico."""
    title: str
    content: str
    timestamp: float
    sentiment_score: float
    quantum_sentiment_state: Optional[np.ndarray] = None  # Estado quântico
    source: str = ""
    url: str = ""
```

## 🌀 3. Conversão em Eventos Holográficos

### Transformação Quantum-Enhanced

As notícias são transformadas em `HolographicEvent` com dados quânticos:

```python
def convert_to_holographic_events(self, news_events: List[NewsEvent]) -> List[HolographicEvent]:
    events = []
    current_time = time.time()
    
    for news in news_events:
        # Mapeia para região de notícias (150, 150)
        x = 150 + int(np.random.normal(0, 10))
        y = 150 + int(np.random.normal(0, 10))
        
        # Amplitude enriquecida com dados quânticos
        base_amplitude = news.sentiment_score * 4.0 + 1.0
        
        # Se tem estado quântico, usa a magnitude do vetor
        if news.quantum_sentiment_state is not None:
            quantum_magnitude = np.linalg.norm(news.quantum_sentiment_state)
            base_amplitude *= quantum_magnitude
        
        events.append(HolographicEvent(
            position=(x, y),
            time=current_time,
            amplitude=base_amplitude,
            spatial_sigma=15.0,  # Maior dispersão para notícias
            temporal_sigma=8.0,  # Maior duração de impacto
            event_type="quantum_news_event",
            source_data={
                "title": news.title,
                "sentiment": news.sentiment_score,
                "quantum_encoded": news.quantum_sentiment_state is not None,
                "quantum_state": news.quantum_sentiment_state.tolist(),
                "source": news.source,
                "url": news.url
            },
            confidence=0.85  # Alta confiança por ser quantum-enhanced
        ))
```

### Características dos Eventos

- **Posição Espacial**: Região dedicada às notícias (150, 150) ± variação gaussiana
- **Amplitude**: Baseada em sentiment score amplificado + magnitude quântica
- **Dispersão Espacial**: 15.0 (maior que eventos de mercado)
- **Persistência Temporal**: 8.0 (impacto duradouro)
- **Confiança**: 0.85 (alta por ser quantum-enhanced)

## 🌊 4. Propagação no Universo Holográfico

### Dinâmica do Campo

Os eventos de notícias se propagam pelo campo holográfico 2D através de:

```python
async def step_evolution(self, current_time: float) -> None:
    # Calcula feedback total de todos os eventos ativos
    total_feedback = np.zeros(self.field_size)
    
    for event in self.events_queue:
        pulse = self.inject_pulse(
            event.position,
            event.time,
            current_time,
            event.spatial_sigma,
            event.temporal_sigma,
            event.amplitude
        )
        total_feedback += pulse
    
    # Evolui campo com difusão e feedback
    self.current_field = self.evolve_field(total_feedback)
```

### Características da Propagação

- **Difusão**: Informação se espalha usando equações de difusão
- **Interferência**: Notícias interagem com eventos de mercado
- **Feedback Não-Linear**: Campo influencia sua própria evolução
- **Persistência**: Impacto decai exponencialmente ao longo do tempo

### Equação de Evolução

```python
def evolve_field(self, feedback: np.ndarray) -> np.ndarray:
    # Difusão linear
    diffused = gaussian_filter(self.current_field, sigma=self.diffusion_rate)
    
    # Feedback não-linear
    nonlinear_term = self.feedback_strength * (self.current_field ** 3)
    
    # Evolução completa
    return diffused + feedback - nonlinear_term
```

## 🔍 5. Detecção de Padrões

### Análise Wavelet Multi-Escalar

O sistema usa análise wavelet para detectar padrões emergentes:

```python
def analyze_holographic_patterns(self) -> List[HolographicPattern]:
    patterns = []
    
    for point in self.analysis_points:
        # Extrai série temporal do ponto
        time_series = [field[point] for field in self.field_history[-20:]]
        
        if len(time_series) < 10:
            continue
            
        # Análise wavelet multi-escala
        coeffs, freqs = pywt.cwt(time_series, self.wavelet_scales, 'morl')
        
        # Calcula força do padrão
        pattern_strength = np.max(np.abs(coeffs))
        
        if pattern_strength > 0.3:  # Threshold de significância
            # Encontra frequência dominante
            max_coeff_idx = np.unravel_index(np.argmax(np.abs(coeffs)), coeffs.shape)
            dominant_scale = self.wavelet_scales[max_coeff_idx[0]]
            dominant_freq = 1.0 / dominant_scale
            
            # Classifica padrão
            pattern_type = self._classify_pattern(coeffs, time_series)
            
            pattern = HolographicPattern(
                position=point,
                strength=pattern_strength,
                dominant_frequency=dominant_freq,
                pattern_type=pattern_type,
                confidence=min(pattern_strength / 2.0, 1.0),
                timestamp=time.time()
            )
            
            patterns.append(pattern)
```

### Classificação de Padrões

```python
def _classify_pattern(self, coeffs: np.ndarray, time_series: np.ndarray) -> str:
    # Análise de tendência
    recent_trend = np.mean(time_series[-5:]) - np.mean(time_series[-10:-5])
    slope = np.polyfit(range(len(time_series)), time_series, 1)[0]
    volatility = np.std(time_series[-10:])
    
    # Classificação baseada em múltiplos fatores
    if slope > 0.1 or recent_trend > 0.2:
        return "bullish_momentum"
    elif slope < -0.1 or recent_trend < -0.2:
        return "bearish_momentum"
    elif volatility > 0.5:
        return "high_volatility_bullish" if slope >= 0 else "high_volatility_bearish"
    # ... mais classificações
```

## 🎯 6. Geração de Sinais de Trading

### Conversão de Padrões em Sinais

```python
def generate_trading_signals(self, patterns: List[HolographicPattern]) -> List[TradingSignal]:
    signals = []
    
    for pattern in patterns:
        if pattern.strength > 0.3 and pattern.confidence > 0.4:  # Thresholds reduzidos
            # Determina ação baseada no tipo de padrão
            if any(keyword in pattern.pattern_type for keyword in ["bullish", "positive"]):
                action = "BUY"
            elif any(keyword in pattern.pattern_type for keyword in ["bearish", "negative"]):
                action = "SELL"
            else:
                # Analisa padrões neutros pela força
                if pattern.strength > 0.6:
                    action = "BUY" if "momentum" in pattern.pattern_type else "HOLD"
                else:
                    continue
            
            signal = TradingSignal(
                symbol=self._infer_symbol_from_pattern(pattern),
                action=action,
                confidence=pattern.confidence,
                strength=pattern.strength,
                timeframe=self._scale_to_timeframe(1.0 / pattern.dominant_frequency),
                holographic_context={
                    "pattern_type": pattern.pattern_type,
                    "dominant_frequency": pattern.dominant_frequency,
                    "news_influenced": True,
                    "quantum_enhanced": True
                }
            )
            signals.append(signal)
```

## 📊 7. Integração com Decision Engine

### Combinação de Sinais

```python
async def _decision_cycle_loop(self):
    # Gera decisões do oracle
    oracle_decisions = await self.oracle_engine.generate_decisions()
    
    # Analisa padrões holográficos (incluindo notícias)
    patterns = self.holographic_universe.analyze_holographic_patterns()
    holographic_signals = self.holographic_universe.generate_trading_signals(patterns)
    
    # Combina decisões oracle + sinais holográficos
    combined_decisions = await self._combine_oracle_and_holographic_signals(
        oracle_decisions, holographic_signals
    )
```

### Enhanced Risk Assessment

```python
def _enhanced_risk_assessment(self, decision):
    risk_assessment = {'approved': True, 'reason': 'Standard validation'}
    
    # Considera contexto holográfico de notícias
    if hasattr(decision, 'holographic_context'):
        holographic_data = decision.holographic_context
        
        if holographic_data.get('news_influenced'):
            news_sentiment = holographic_data.get('sentiment_score', 0)
            
            # Ajusta risco baseado em sentiment extremo
            if abs(news_sentiment) > 0.8:
                risk_assessment['news_factor'] = news_sentiment
                risk_assessment['adjusted_confidence'] = decision.confidence * 1.1
            
        if holographic_data.get('quantum_enhanced'):
            risk_assessment['quantum_validated'] = True
            risk_assessment['adjusted_confidence'] = decision.confidence * 1.05
    
    return risk_assessment
```

## 🎯 Exemplo Prático de Fluxo

### Cenário: "Bitcoin ETF aprovado pela SEC"

1. **Coleta RSS**: Artigo detectado no feed da CoinTelegraph
2. **Análise de Sentiment**: 
   - Palavras positivas: "aprovado", "ETF", "success"
   - Score calculado: +0.8 (muito positivo)
3. **Encoding Quântico**:
   - Normalização: (0.8 + 1.0) / 2.0 = 0.9
   - Theta: 0.9 * (π/2) = 1.41 rad
   - Estado: `[cos(1.41), sin(1.41)] = [0.16, 0.99]`
4. **Evento Holográfico**:
   - Amplitude base: 0.8 * 4.0 + 1.0 = 4.2
   - Magnitude quântica: √(0.16² + 0.99²) = 1.01
   - Amplitude final: 4.2 * 1.01 = 4.24
5. **Propagação**: Evento se espalha pela região (150, 150)
6. **Detecção de Padrão**: "bullish_momentum" detectado com força 0.68
7. **Sinal Gerado**: BUY BTC com confiança 0.82
8. **Risk Assessment**: Aprovado com boost por quantum enhancement
9. **Execução**: Ordem enviada para exchange

## 🔧 Configuração e Parâmetros

### Parâmetros do Sistema

```yaml
# config/news_processing.yaml
rss_collection:
  interval_seconds: 60
  max_articles_per_feed: 3
  timeout_seconds: 10
  rate_limit_seconds: 0.5

quantum_encoding:
  sentiment_amplification: 4.0
  base_amplitude: 1.0
  confidence_threshold: 0.85

holographic_events:
  news_region_center: [150, 150]
  spatial_sigma: 15.0
  temporal_sigma: 8.0
  position_variance: 10

pattern_detection:
  strength_threshold: 0.3
  confidence_threshold: 0.4
  wavelet_scales: [1, 41]
  analysis_points: 100

signal_generation:
  min_pattern_strength: 0.3
  min_confidence: 0.4
  quantum_boost_factor: 1.05
  news_boost_factor: 1.1
```

## 🚀 Características Únicas

### Inovações Técnicas

1. **Encoding Quântico Real**: Sentiment convertido em estados quânticos matemáticos
2. **Propagação Holográfica**: Notícias influenciam campo 2D completo
3. **Interferência Multi-modal**: Notícias interagem com dados de mercado
4. **Análise Wavelet**: Detecção de padrões em múltiplas escalas temporais
5. **Persistência Temporal**: Impacto das notícias decai gradualmente
6. **Feedback Não-Linear**: Campo auto-organizante com emergência

### Vantagens do Sistema

- **Contextualização**: Notícias não são analisadas isoladamente
- **Persistência**: Impacto das notícias persiste no tempo
- **Interferência**: Múltiplas notícias podem amplificar ou cancelar efeitos
- **Multi-escalar**: Detecta padrões de curto e longo prazo
- **Quantum-Enhanced**: Estados quânticos capturam nuances do sentiment

## 📈 Métricas e Monitoramento

### Métricas Coletadas

```python
# Métricas de coleta RSS
- news_articles_collected
- rss_feeds_processed
- collection_latency
- parsing_errors

# Métricas de encoding quântico
- quantum_states_generated
- sentiment_scores_distribution
- encoding_latency

# Métricas holográficas
- holographic_events_created
- field_energy_levels
- pattern_detection_rate
- signal_generation_rate

# Métricas de trading
- news_influenced_signals
- quantum_enhanced_decisions
- execution_success_rate
```

### Logs de Debug

```python
logger.debug(f"🌀 RSS Quantum Sentiment: '{entry.title[:40]}...' "
           f"score={sentiment_score:.3f} "
           f"quantum_state=[{quantum_state[0]:.3f}, {quantum_state[1]:.3f}]")

logger.info(f"📰 RSS: Coletados {len(news_events)} eventos de notícias")
logger.info(f"🌀 Quantum States: {quantum_encoded_count}/{len(enhanced_data)} símbolos com encoding quântico")
logger.info(f"🎯 Padrões acima do threshold: {patterns_above_threshold}/{len(patterns)}")
```

## 🔬 Pesquisa e Desenvolvimento

### Direções Futuras

1. **Modelos de Linguagem**: Integração com LLMs para análise semântica avançada
2. **Redes Neurais Quânticas**: Encoding mais sofisticado usando QNNs
3. **Análise de Sentimento Multi-idioma**: Suporte para notícias em várias línguas
4. **Correlação Temporal**: Análise de lag entre notícias e movimentos de preço
5. **Detecção de Fake News**: Filtros para identificar informações não confiáveis

### Experimentos Atuais

- **Retrocausalidade**: Influência de eventos futuros no campo atual
- **Entrelaçamento Quântico**: Correlações não-locais entre eventos
- **Consciência Artificial**: Integração com QUALIAConsciousness

## 📚 Referências Técnicas

### Documentação Relacionada

- [HolographicUniverse API](./holographic_universe.md)
- [Quantum Encoders](./encoding_features.md)
- [Trading Decision Engine](./decision_engine.md)
- [Risk Management](./risk_management.md)

### Arquivos Principais

```
src/qualia/consciousness/real_data_collectors.py
src/qualia/core/rss_encoder.py
src/qualia/consciousness/holographic_universe.py
src/qualia/sources/rss_ingestor.py
scripts/start_real_trading.py
```

---

**Documento gerado por YAA (Yet Another Agent) - QUALIA Quantum Consciousness System**  
*Versão: 1.0 | Data: 2024 | Status: Ativo* 