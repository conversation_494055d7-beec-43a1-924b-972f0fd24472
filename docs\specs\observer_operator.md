# Observer Operator

O operador de *Observer* aplica conceitos de medição quântica para registrar estados do mercado. Ele seleciona bases de medição apropriadas, calcula colapsos do estado e persiste o histórico de observações.

Principais características
-------------------------
- Matrizes unitárias configuráveis com verificação de unitariedade.
- Cálculo de incerteza para diferentes observáveis, respeitando um limite mínimo.
- Suporte a observação contínua com fila de medições agendadas.
- Persistência em JSON do histórico de observações.
- `measurement_rate` em `qualia.core.observer` informa a quantidade de medições
  por segundo baseada no histórico registrado.
- Função `observe_system` que realiza uma transformação linear simples, utilizada nos testes básicos.

## Exemplo de Uso
```python
import numpy as np
from qualia.core.observer import ObserverOperator, ObservationType

observer = ObserverOperator()
vector = np.random.rand(4)
state = asyncio.run(observer.observe(vector, ObservationType.ENERGY, timestamp=0.0))
print(state.observation_effect)
```
