# Exemplos de Exploração Manual

Este diretório contém scripts destinados à investigação e testes manuais.
Eles utilizam integrações de mercado para verificar a funcionalidade de
`fetch_ticker` em diferentes exchanges.

Os arquivos podem ser executados individualmente e **não fazem parte** da
suíte automatizada de testes (`pytest tests/`).
O diretório utilizado para logs é definido em ``src.qualia.config.logging.log_dir``.
Caso precise de mais detalhes durante os testes, defina ``QUALIA_LOG_LEVEL=DEBUG`` antes de executar os scripts.
