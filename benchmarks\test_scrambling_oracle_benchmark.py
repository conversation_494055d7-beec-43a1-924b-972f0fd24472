import pytest

pytest.importorskip("pytest_benchmark")
from qiskit import QuantumCircuit

from qualia.core.scrambling_utils import _apply_random_scrambling_layer
from qualia.core.universe import (
    build_grover_operator,
    QUALIAQuantumUniverse,
)


@pytest.mark.parametrize("n_qubits", [4, 6, 8])
def test_scrambling_layer_runtime(benchmark, n_qubits):
    qc = QuantumCircuit(n_qubits)
    benchmark(_apply_random_scrambling_layer, qc, qc.qubits)
    assert qc.num_qubits == n_qubits


@pytest.mark.parametrize("n_qubits", [4, 6, 8])
def test_grover_operator_runtime(benchmark, n_qubits):
    oracle = QuantumCircuit(n_qubits)
    grover_matrix = benchmark(build_grover_operator, oracle, n_qubits)
    assert grover_matrix.shape[0] == 2**n_qubits


@pytest.mark.parametrize("n_qubits", [2, 4])
def test_run_with_qast_feedback_runtime(benchmark, n_qubits):
    universe = QUALIAQuantumUniverse(n_qubits=n_qubits, scr_depth=1)
    benchmark(
        lambda: universe.run_with_qast_feedback(
            max_steps=1, stable_cycles=1, thermal=False
        )
    )
    assert universe.n_qubits == n_qubits
