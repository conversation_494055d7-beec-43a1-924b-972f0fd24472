#!/usr/bin/env python3
"""
Validação rápida da configuração OTOC.
"""

import yaml
import os

def main():
    print("🔍 Validando configuração OTOC...")
    
    config_path = os.path.join('config', 'fwh_scalp_config.yaml')
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print(f"✅ Arquivo carregado: {config_path}")
        
        # Navegar pela estrutura
        fwh_config = config.get('fibonacci_wave_hype_config', {})
        print(f"📊 FWH config keys: {list(fwh_config.keys())}")

        params_config = fwh_config.get('params', {})
        print(f"📊 Params config keys: {list(params_config.keys())}")

        mtf_config = params_config.get('multi_timeframe_config', {})
        print(f"📊 MTF config keys: {list(mtf_config.keys())}")

        otoc_config = mtf_config.get('otoc_config', {})
        print(f"📊 OTOC config: {otoc_config}")
        
        if otoc_config:
            print("✅ Configuração OTOC encontrada!")
            print(f"   Enabled: {otoc_config.get('enabled')}")
            print(f"   Threshold: {otoc_config.get('max_threshold')}")
            print(f"   Window: {otoc_config.get('window')}")
            print(f"   Method: {otoc_config.get('method')}")
        else:
            print("❌ Configuração OTOC não encontrada!")
            
    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    main()
