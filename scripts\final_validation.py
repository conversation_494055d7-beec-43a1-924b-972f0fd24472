#!/usr/bin/env python3
"""
Validação final da estratégia Enhanced Quantum Momentum corrigida.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from datetime import datetime


def final_validation():
    """Validação final completa."""
    
    print("🎯 VALIDAÇÃO FINAL: ENHANCED QUANTUM MOMENTUM CORRIGIDA")
    print("=" * 60)
    print(f"🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. Verificar configuração
    print("\n📋 1. VERIFICANDO CONFIGURAÇÃO...")
    
    try:
        import yaml
        
        # Verificar strategy_parameters.yaml
        with open("config/strategy_parameters.yaml", 'r') as f:
            config = yaml.safe_load(f)
        
        strategy_name = config['strategy_config']['name']
        params = config['strategy_config']['params']
        
        print(f"   ✅ Arquivo de configuração carregado")
        print(f"   📊 Estratégia: {strategy_name}")
        
        if strategy_name == "EnhancedQuantumMomentumStrategy":
            print(f"   🎯 ✅ Estratégia CORRETA configurada!")
        else:
            print(f"   ❌ Estratégia INCORRETA: {strategy_name}")
            return False
        
        # Verificar parâmetros otimizados
        print(f"\n   🔍 VERIFICANDO PARÂMETROS OTIMIZADOS:")
        
        expected_params = {
            'rsi_overbought': 68.0,
            'rsi_oversold': 32.0,
            'signal_threshold': 0.60,
            'take_profit_r_multiple': 2.375,
            'stop_loss_r_multiple': 1.2
        }
        
        all_correct = True
        for param, expected in expected_params.items():
            actual = params.get(param, 'N/A')
            correct = actual == expected
            status = "✅" if correct else "❌"
            
            print(f"      {status} {param}: {actual} (esperado: {expected})")
            
            if not correct:
                all_correct = False
        
        if all_correct:
            print(f"   🎯 ✅ TODOS os parâmetros estão otimizados!")
        else:
            print(f"   ❌ Alguns parâmetros NÃO estão corretos!")
            return False
            
    except Exception as e:
        print(f"   ❌ Erro ao verificar configuração: {e}")
        return False
    
    # 2. Verificar estratégia
    print("\n📋 2. VERIFICANDO ESTRATÉGIA...")
    
    try:
        from qualia.strategies.enhanced_quantum_momentum import EnhancedQuantumMomentumStrategy
        
        # Criar instância de teste
        strategy = EnhancedQuantumMomentumStrategy(
            symbol="BTC/USDT",
            timeframe="1h"
        )
        
        print(f"   ✅ Estratégia criada: {type(strategy).__name__}")
        
        # Verificar parâmetros da instância
        print(f"\n   🔍 VERIFICANDO PARÂMETROS DA INSTÂNCIA:")
        
        instance_checks = [
            ("Signal threshold", strategy.signal_threshold, 0.60),
            ("RSI overbought", strategy.rsi_overbought, 68.0),
            ("RSI oversold", strategy.rsi_oversold, 32.0),
            ("Take profit R", strategy._take_profit_r_multiple_value, 2.375),
            ("Stop loss R", strategy._stop_loss_r_multiple_value, 1.2)
        ]
        
        all_instance_correct = True
        for name, actual, expected in instance_checks:
            correct = actual == expected
            status = "✅" if correct else "❌"
            
            print(f"      {status} {name}: {actual} (esperado: {expected})")
            
            if not correct:
                all_instance_correct = False
        
        if all_instance_correct:
            print(f"   🎯 ✅ Instância da estratégia está correta!")
        else:
            print(f"   ❌ Instância da estratégia tem problemas!")
            return False
            
    except Exception as e:
        print(f"   ❌ Erro ao verificar estratégia: {e}")
        return False
    
    # 3. Verificar factory
    print("\n📋 3. VERIFICANDO STRATEGY FACTORY...")
    
    try:
        from qualia.config.config_manager import ConfigManager
        from qualia.strategies.strategy_factory import StrategyFactory
        
        # Carregar configuração
        config_manager = ConfigManager("config/strategy_parameters.yaml")
        config_manager.load()
        
        # Criar estratégia via factory
        strategy_from_factory = StrategyFactory.create_strategy(
            alias="EnhancedQuantumMomentumStrategy",
            context={"symbol": "BTC/USDT", "timeframe": "1h"},
            config_manager=config_manager
        )
        
        print(f"   ✅ Estratégia criada via factory: {type(strategy_from_factory).__name__}")
        
        # Verificar se é a mesma classe
        if isinstance(strategy_from_factory, EnhancedQuantumMomentumStrategy):
            print(f"   ✅ Factory retorna a estratégia correta!")
        else:
            print(f"   ❌ Factory retorna estratégia incorreta!")
            return False
            
    except Exception as e:
        print(f"   ❌ Erro ao verificar factory: {e}")
        return False
    
    # 4. Teste de funcionalidade
    print("\n📋 4. TESTE DE FUNCIONALIDADE...")
    
    try:
        # Verificar métodos essenciais
        essential_methods = [
            'analyze_market',
            'get_parameters',
            'initialize'
        ]
        
        for method in essential_methods:
            if hasattr(strategy, method):
                print(f"   ✅ Método {method} disponível")
            else:
                print(f"   ❌ Método {method} NÃO disponível")
                return False
        
        # Testar get_parameters
        params = strategy.get_parameters()
        print(f"   ✅ get_parameters() retorna {len(params)} parâmetros")
        
        # Verificar parâmetros chave
        key_params = ['signal_threshold', 'rsi_overbought', 'rsi_oversold']
        for param in key_params:
            if param in params:
                print(f"      ✅ {param}: {params[param]}")
            else:
                print(f"      ❌ {param} não encontrado em get_parameters()")
                return False
        
    except Exception as e:
        print(f"   ❌ Erro no teste de funcionalidade: {e}")
        return False
    
    # 5. Resultado final
    print(f"\n🎉 VALIDAÇÃO FINAL CONCLUÍDA COM SUCESSO!")
    print(f"\n📊 RESUMO COMPLETO:")
    print(f"   ✅ Configuração correta (EnhancedQuantumMomentumStrategy)")
    print(f"   ✅ Parâmetros otimizados aplicados")
    print(f"   ✅ Instância da estratégia funcional")
    print(f"   ✅ Strategy Factory configurado")
    print(f"   ✅ Métodos essenciais disponíveis")
    print(f"   ✅ Sistema pronto para execução")
    
    print(f"\n🚀 CORREÇÕES IMPLEMENTADAS:")
    print(f"   🎯 Signal threshold: 0.72 → 0.60 (mais oportunidades)")
    print(f"   📊 RSI range: 35-65 → 32-68 (expandido)")
    print(f"   💰 Take profit: 2.0 → 2.375 (mais agressivo)")
    print(f"   🛡️ Stop loss: 1.0 → 1.2 (melhor proteção)")
    print(f"   🔧 Threshold adaptativo implementado")
    
    print(f"\n📋 PARA EXECUTAR O SISTEMA:")
    print(f"   python main.py")
    print(f"   (Sistema usará Enhanced Quantum Momentum corrigida)")
    
    return True


def main():
    """Função principal."""
    
    success = final_validation()
    
    if success:
        print(f"\n🏆 SUCESSO TOTAL!")
        print(f"Sistema QUALIA validado com Enhanced Quantum Momentum corrigida")
    else:
        print(f"\n❌ FALHA NA VALIDAÇÃO!")
        print(f"Revisar configuração e correções")


if __name__ == "__main__":
    main()
