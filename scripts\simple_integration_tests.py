#!/usr/bin/env python3
"""
QUALIA Simple Integration Tests
P-02.2: Testes de Integração Simplificados

Simplified integration testing for available QUALIA components
"""

import os
import sys
import json
import time
import asyncio
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import yaml
import psutil

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleIntegrationTestSuite:
    """Simplified integration test suite for QUALIA"""
    
    def __init__(self, config_path: str = "config/staging_config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.test_id = f"simple_integration_{int(time.time())}"
        
        logger.info(f"Initialized simple integration test suite: {self.test_id}")
    
    def _load_config(self) -> Dict[str, Any]:
        """Load staging configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            raise
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run simplified integration test suite"""
        logger.info("Starting simplified integration tests")
        
        test_report = {
            'test_id': self.test_id,
            'start_time': datetime.utcnow().isoformat(),
            'config_path': self.config_path,
            'environment': self.config.get('environment', 'unknown'),
            'tests': [],
            'summary': {},
            'status': 'running'
        }
        
        # Define simplified test suite
        test_suite = [
            ("System Health", self._test_system_health),
            ("Configuration Validation", self._test_configuration_validation),
            ("File System Integration", self._test_file_system_integration),
            ("Network Connectivity", self._test_network_connectivity),
            ("Module Imports", self._test_module_imports),
            ("Performance Baseline", self._test_performance_baseline),
            ("Security Files", self._test_security_files),
            ("Logging System", self._test_logging_system),
            ("Environment Setup", self._test_environment_setup)
        ]
        
        passed_tests = 0
        failed_tests = 0
        warning_tests = 0
        
        try:
            for test_name, test_func in test_suite:
                logger.info(f"Running test: {test_name}")
                
                test_start = time.time()
                try:
                    result = await test_func()
                    result['test_name'] = test_name
                    result['execution_time_ms'] = (time.time() - test_start) * 1000
                    result['timestamp'] = datetime.utcnow().isoformat()
                    
                    test_report['tests'].append(result)
                    
                    if result['status'] == 'PASS':
                        passed_tests += 1
                        logger.info(f"✓ {test_name} PASSED")
                    elif result['status'] == 'WARNING':
                        warning_tests += 1
                        logger.warning(f"⚠ {test_name} WARNING: {result['message']}")
                    else:
                        failed_tests += 1
                        logger.error(f"✗ {test_name} FAILED: {result['message']}")
                
                except Exception as e:
                    failed_tests += 1
                    result = {
                        'test_name': test_name,
                        'status': 'FAIL',
                        'message': f'Test failed with exception: {str(e)}',
                        'execution_time_ms': (time.time() - test_start) * 1000,
                        'timestamp': datetime.utcnow().isoformat(),
                        'error': str(e)
                    }
                    test_report['tests'].append(result)
                    logger.error(f"✗ {test_name} EXCEPTION: {e}")
            
            # Calculate summary
            total_tests = len(test_suite)
            test_report['summary'] = {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'warning_tests': warning_tests,
                'success_rate': (passed_tests / total_tests) * 100 if total_tests > 0 else 0
            }
            
            # Determine overall status
            if failed_tests == 0:
                test_report['status'] = 'PASS' if warning_tests == 0 else 'WARNING'
            else:
                test_report['status'] = 'FAIL'
            
            test_report['end_time'] = datetime.utcnow().isoformat()
            
            logger.info(f"Simple integration tests completed: {test_report['status']}")
            
        except Exception as e:
            test_report['status'] = 'FAIL'
            test_report['error'] = str(e)
            test_report['end_time'] = datetime.utcnow().isoformat()
            logger.error(f"Integration test suite failed: {e}")
        
        # Save test report
        await self._save_test_report(test_report)
        
        return test_report
    
    async def _test_system_health(self) -> Dict[str, Any]:
        """Test system health"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('.')
            
            issues = []
            if cpu_percent > 90:
                issues.append(f"High CPU: {cpu_percent}%")
            if memory.percent > 95:
                issues.append(f"Critical memory: {memory.percent}%")
            if disk.percent > 90:
                issues.append(f"High disk usage: {disk.percent}%")
            
            if issues:
                return {
                    'status': 'WARNING',
                    'message': f'System health issues: {"; ".join(issues)}',
                    'details': {
                        'cpu_percent': cpu_percent,
                        'memory_percent': memory.percent,
                        'disk_percent': disk.percent
                    }
                }
            else:
                return {
                    'status': 'PASS',
                    'message': 'System health is good',
                    'details': {
                        'cpu_percent': cpu_percent,
                        'memory_percent': memory.percent,
                        'disk_percent': disk.percent
                    }
                }
        
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'System health test failed: {e}',
                'error': str(e)
            }
    
    async def _test_configuration_validation(self) -> Dict[str, Any]:
        """Test configuration validation"""
        try:
            config = self.config
            
            required_sections = ['environment', 'trading', 'exchange']
            missing_sections = [s for s in required_sections if s not in config]
            
            if missing_sections:
                return {
                    'status': 'FAIL',
                    'message': f'Missing config sections: {missing_sections}',
                    'details': {'missing_sections': missing_sections}
                }
            
            if config.get('environment') != 'staging':
                return {
                    'status': 'WARNING',
                    'message': f'Unexpected environment: {config.get("environment")}',
                    'details': {'environment': config.get('environment')}
                }
            
            return {
                'status': 'PASS',
                'message': 'Configuration is valid',
                'details': {'environment': config.get('environment')}
            }
        
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Configuration validation failed: {e}',
                'error': str(e)
            }
    
    async def _test_file_system_integration(self) -> Dict[str, Any]:
        """Test file system integration"""
        try:
            required_dirs = ['logs', 'data', 'config', 'reports']
            staging_dirs = ['logs/staging', 'data/staging', 'backups/staging', 'reports/staging']
            
            missing_dirs = []
            for dir_path in required_dirs + staging_dirs:
                if not Path(dir_path).exists():
                    missing_dirs.append(dir_path)
            
            # Test file creation
            test_file = Path('tmp/test_integration.tmp')
            test_file.parent.mkdir(exist_ok=True)
            
            try:
                test_file.write_text('integration test')
                test_content = test_file.read_text()
                test_file.unlink()
                
                if test_content != 'integration test':
                    return {
                        'status': 'FAIL',
                        'message': 'File I/O test failed',
                        'details': {'expected': 'integration test', 'actual': test_content}
                    }
            except Exception as e:
                return {
                    'status': 'FAIL',
                    'message': f'File I/O test failed: {e}',
                    'error': str(e)
                }
            
            if missing_dirs:
                return {
                    'status': 'WARNING',
                    'message': f'Missing directories: {missing_dirs}',
                    'details': {'missing_dirs': missing_dirs}
                }
            
            return {
                'status': 'PASS',
                'message': 'File system integration is functional',
                'details': {'directories_checked': len(required_dirs + staging_dirs)}
            }
        
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'File system integration test failed: {e}',
                'error': str(e)
            }
    
    async def _test_network_connectivity(self) -> Dict[str, Any]:
        """Test network connectivity"""
        try:
            import requests
            
            # Test KuCoin API connectivity
            start_time = time.time()
            response = requests.get('https://api.kucoin.com/api/v1/timestamp', timeout=10)
            latency = (time.time() - start_time) * 1000
            
            if response.status_code != 200:
                return {
                    'status': 'FAIL',
                    'message': f'KuCoin API connectivity failed: {response.status_code}',
                    'details': {'status_code': response.status_code, 'latency_ms': latency}
                }
            
            if latency > 2000:
                return {
                    'status': 'WARNING',
                    'message': f'High network latency: {latency:.1f}ms',
                    'details': {'latency_ms': latency}
                }
            
            return {
                'status': 'PASS',
                'message': 'Network connectivity is good',
                'details': {'latency_ms': latency}
            }
        
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Network connectivity test failed: {e}',
                'error': str(e)
            }
    
    async def _test_module_imports(self) -> Dict[str, Any]:
        """Test module imports"""
        try:
            import_tests = [
                ('yaml', 'yaml'),
                ('psutil', 'psutil'),
                ('requests', 'requests'),
                ('aiofiles', 'aiofiles'),
                ('numpy', 'numpy'),
                ('pandas', 'pandas')
            ]
            
            successful_imports = []
            failed_imports = []
            
            for module_name, import_name in import_tests:
                try:
                    __import__(import_name)
                    successful_imports.append(module_name)
                except ImportError:
                    failed_imports.append(module_name)
            
            if failed_imports:
                return {
                    'status': 'WARNING',
                    'message': f'Some modules failed to import: {failed_imports}',
                    'details': {
                        'successful': successful_imports,
                        'failed': failed_imports
                    }
                }
            
            return {
                'status': 'PASS',
                'message': 'All modules imported successfully',
                'details': {'successful_imports': successful_imports}
            }
        
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Module import test failed: {e}',
                'error': str(e)
            }
    
    async def _test_performance_baseline(self) -> Dict[str, Any]:
        """Test performance baseline"""
        try:
            # CPU test
            start_time = time.time()
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_test_time = time.time() - start_time
            
            # Memory test
            memory = psutil.virtual_memory()
            
            # Simple computation test
            start_time = time.time()
            result = sum(i * i for i in range(10000))
            computation_time = (time.time() - start_time) * 1000
            
            issues = []
            if cpu_percent > 80:
                issues.append(f"High CPU: {cpu_percent}%")
            if memory.percent > 90:
                issues.append(f"High memory: {memory.percent}%")
            if computation_time > 100:
                issues.append(f"Slow computation: {computation_time:.1f}ms")
            
            if issues:
                return {
                    'status': 'WARNING',
                    'message': f'Performance issues: {"; ".join(issues)}',
                    'details': {
                        'cpu_percent': cpu_percent,
                        'memory_percent': memory.percent,
                        'computation_time_ms': computation_time
                    }
                }
            
            return {
                'status': 'PASS',
                'message': 'Performance baseline is acceptable',
                'details': {
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent,
                    'computation_time_ms': computation_time
                }
            }
        
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Performance baseline test failed: {e}',
                'error': str(e)
            }
    
    async def _test_security_files(self) -> Dict[str, Any]:
        """Test security files"""
        try:
            config_dir = Path('config')
            security_files = ['.credentials.enc', '.master.key', '.staging_credentials']
            
            existing_files = []
            missing_files = []
            
            for file_name in security_files:
                file_path = config_dir / file_name
                if file_path.exists():
                    existing_files.append(file_name)
                else:
                    missing_files.append(file_name)
            
            if missing_files:
                return {
                    'status': 'WARNING',
                    'message': f'Missing security files: {missing_files}',
                    'details': {
                        'existing': existing_files,
                        'missing': missing_files
                    }
                }
            
            return {
                'status': 'PASS',
                'message': 'Security files are present',
                'details': {'security_files': existing_files}
            }
        
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Security files test failed: {e}',
                'error': str(e)
            }
    
    async def _test_logging_system(self) -> Dict[str, Any]:
        """Test logging system"""
        try:
            # Test log directory creation
            log_dir = Path('logs/staging')
            log_dir.mkdir(parents=True, exist_ok=True)
            
            # Test log file creation with unique name
            import uuid
            test_log_file = log_dir / f'integration_test_{uuid.uuid4().hex[:8]}.log'

            # Create a test logger
            test_logger = logging.getLogger(f'integration_test_{uuid.uuid4().hex[:8]}')
            handler = logging.FileHandler(test_log_file)
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            test_logger.addHandler(handler)
            test_logger.setLevel(logging.INFO)

            # Test logging
            test_logger.info('Integration test log message')

            # Close handler to release file
            handler.close()
            test_logger.removeHandler(handler)

            # Verify log file
            if not test_log_file.exists():
                return {
                    'status': 'FAIL',
                    'message': 'Log file was not created',
                    'details': {'log_file': str(test_log_file)}
                }

            log_content = test_log_file.read_text()
            if 'Integration test log message' not in log_content:
                return {
                    'status': 'FAIL',
                    'message': 'Log message was not written correctly',
                    'details': {'log_content': log_content}
                }

            # Cleanup
            test_log_file.unlink()
            
            return {
                'status': 'PASS',
                'message': 'Logging system is functional',
                'details': {'log_dir': str(log_dir)}
            }
        
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Logging system test failed: {e}',
                'error': str(e)
            }
    
    async def _test_environment_setup(self) -> Dict[str, Any]:
        """Test environment setup"""
        try:
            required_env_vars = ['QUALIA_ENV', 'QUALIA_CONFIG']
            optional_env_vars = ['QUALIA_LOG_LEVEL']
            
            missing_required = [var for var in required_env_vars if var not in os.environ]
            missing_optional = [var for var in optional_env_vars if var not in os.environ]
            
            if missing_required:
                return {
                    'status': 'FAIL',
                    'message': f'Missing required environment variables: {missing_required}',
                    'details': {
                        'missing_required': missing_required,
                        'missing_optional': missing_optional
                    }
                }
            
            if missing_optional:
                return {
                    'status': 'WARNING',
                    'message': f'Missing optional environment variables: {missing_optional}',
                    'details': {
                        'missing_optional': missing_optional,
                        'qualia_env': os.environ.get('QUALIA_ENV')
                    }
                }
            
            return {
                'status': 'PASS',
                'message': 'Environment setup is complete',
                'details': {
                    'qualia_env': os.environ.get('QUALIA_ENV'),
                    'qualia_config': os.environ.get('QUALIA_CONFIG')
                }
            }
        
        except Exception as e:
            return {
                'status': 'FAIL',
                'message': f'Environment setup test failed: {e}',
                'error': str(e)
            }
    
    async def _save_test_report(self, report: Dict[str, Any]):
        """Save integration test report"""
        try:
            report_dir = Path("reports/staging")
            report_dir.mkdir(parents=True, exist_ok=True)
            
            report_file = report_dir / f"simple_integration_{self.test_id}.json"
            
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"Simple integration test report saved: {report_file}")
            
        except Exception as e:
            logger.error(f"Failed to save test report: {e}")

async def main():
    """Main test execution"""
    import argparse
    
    parser = argparse.ArgumentParser(description='QUALIA Simple Integration Tests')
    parser.add_argument('--config', default='config/staging_config.yaml',
                       help='Configuration file')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Setup environment
    os.environ['QUALIA_ENV'] = 'staging'
    os.environ['QUALIA_CONFIG'] = args.config
    os.environ['QUALIA_LOG_LEVEL'] = 'DEBUG' if args.verbose else 'INFO'
    
    # Execute integration tests
    test_suite = SimpleIntegrationTestSuite(args.config)
    report = await test_suite.run_all_tests()
    
    # Print summary
    print("\n" + "="*80)
    print("QUALIA SIMPLE INTEGRATION TEST RESULTS")
    print("="*80)
    print(f"Test ID: {report['test_id']}")
    print(f"Environment: {report['environment']}")
    print(f"Overall Status: {report['status']}")
    
    summary = report['summary']
    print(f"Total Tests: {summary['total_tests']}")
    print(f"Passed: {summary['passed_tests']}")
    print(f"Failed: {summary['failed_tests']}")
    print(f"Warnings: {summary['warning_tests']}")
    print(f"Success Rate: {summary['success_rate']:.1f}%")
    
    if report['status'] == 'PASS':
        print("🎉 ALL INTEGRATION TESTS PASSED!")
    elif report['status'] == 'WARNING':
        print("⚠️ INTEGRATION TESTS PASSED WITH WARNINGS")
    else:
        print("❌ INTEGRATION TESTS FAILED")
    
    # Show failed tests
    if summary['failed_tests'] > 0:
        print("\nFAILED TESTS:")
        for test in report['tests']:
            if test['status'] == 'FAIL':
                print(f"  ✗ {test['test_name']}: {test['message']}")
    
    # Show warning tests
    if summary['warning_tests'] > 0:
        print("\nWARNING TESTS:")
        for test in report['tests']:
            if test['status'] == 'WARNING':
                print(f"  ⚠ {test['test_name']}: {test['message']}")
    
    print("="*80)
    
    # Exit with appropriate code
    sys.exit(0 if report['status'] in ['PASS', 'WARNING'] else 1)

if __name__ == '__main__':
    asyncio.run(main())
