"""Adaptive timeout management for network operations.

This module provides sophisticated timeout management that adapts based on:
- Historical latency patterns
- Network conditions
- Exchange-specific performance characteristics
- Time-of-day patterns
- Market volatility correlation

The adaptive timeout system helps optimize the balance between responsiveness
and reliability in network operations.
"""

from __future__ import annotations

import asyncio
import time
import statistics
from typing import Dict, List, Optional, Tuple
from collections import deque, defaultdict
from dataclasses import dataclass
from enum import Enum

from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class NetworkCondition(Enum):
    """Network condition classifications."""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    CRITICAL = "critical"


@dataclass
class LatencyMeasurement:
    """Single latency measurement with context."""
    timestamp: float
    latency: float
    success: bool
    exchange: str
    operation: str
    symbol: Optional[str] = None


@dataclass
class TimeoutConfig:
    """Timeout configuration for specific context."""
    base_timeout: float
    min_timeout: float
    max_timeout: float
    multiplier: float
    confidence_level: float


class AdaptiveTimeoutManager:
    """Manages adaptive timeouts based on historical performance and network conditions.
    
    Features:
    - Per-exchange timeout adaptation
    - Per-operation timeout optimization
    - Network condition assessment
    - Time-of-day pattern recognition
    - Volatility-based adjustments
    - Confidence-based timeout selection
    """
    
    def __init__(
        self,
        default_timeout: float = 30.0,
        min_timeout: float = 5.0,
        max_timeout: float = 300.0,
        history_size: int = 1000,
        adaptation_rate: float = 0.1,
        confidence_threshold: float = 0.8,
    ):
        self.default_timeout = default_timeout
        self.min_timeout = min_timeout
        self.max_timeout = max_timeout
        self.history_size = history_size
        self.adaptation_rate = adaptation_rate
        self.confidence_threshold = confidence_threshold
        
        # Historical data storage
        self.latency_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=history_size))
        self.success_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=history_size))
        self.timeout_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=history_size))
        
        # Per-exchange configurations
        self.exchange_configs: Dict[str, TimeoutConfig] = {}
        
        # Network condition tracking
        self.network_conditions: deque = deque(maxlen=100)
        self.last_condition_update = 0
        
        # Performance statistics
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'timeout_adjustments': 0,
            'condition_changes': 0
        }

    def get_adaptive_timeout(
        self, 
        exchange: str, 
        operation: str = "default",
        symbol: Optional[str] = None,
        volatility_factor: float = 1.0
    ) -> float:
        """Calculate adaptive timeout for specific context.
        
        Parameters
        ----------
        exchange : str
            Exchange identifier
        operation : str
            Operation type (fetch_ticker, fetch_ohlcv, etc.)
        symbol : str, optional
            Trading symbol
        volatility_factor : float
            Market volatility multiplier (1.0 = normal, >1.0 = high volatility)
            
        Returns
        -------
        float
            Calculated adaptive timeout in seconds
        """
        context_key = self._get_context_key(exchange, operation, symbol)
        
        # Get base timeout from configuration or default
        config = self.exchange_configs.get(exchange)
        if config:
            base_timeout = config.base_timeout
            min_timeout = config.min_timeout
            max_timeout = config.max_timeout
        else:
            base_timeout = self.default_timeout
            min_timeout = self.min_timeout
            max_timeout = self.max_timeout
        
        # Calculate adaptive timeout based on historical data
        if context_key in self.latency_history and len(self.latency_history[context_key]) >= 5:
            adaptive_timeout = self._calculate_statistical_timeout(context_key)
        else:
            adaptive_timeout = base_timeout
        
        # Apply network condition adjustment
        condition_multiplier = self._get_condition_multiplier()
        adaptive_timeout *= condition_multiplier
        
        # Apply volatility adjustment
        adaptive_timeout *= volatility_factor
        
        # Apply time-of-day adjustment
        time_multiplier = self._get_time_multiplier()
        adaptive_timeout *= time_multiplier
        
        # Ensure bounds
        adaptive_timeout = max(min_timeout, min(adaptive_timeout, max_timeout))
        
        logger.debug(
            f"Adaptive timeout for {context_key}: {adaptive_timeout:.2f}s "
            f"(base: {base_timeout:.2f}s, condition: {condition_multiplier:.2f}x, "
            f"volatility: {volatility_factor:.2f}x, time: {time_multiplier:.2f}x)"
        )
        
        return adaptive_timeout

    def record_measurement(
        self,
        exchange: str,
        operation: str,
        latency: float,
        success: bool,
        timeout_used: float,
        symbol: Optional[str] = None
    ) -> None:
        """Record a latency measurement for adaptive learning.
        
        Parameters
        ----------
        exchange : str
            Exchange identifier
        operation : str
            Operation type
        latency : float
            Measured latency in seconds
        success : bool
            Whether the operation succeeded
        timeout_used : float
            Timeout value that was used
        symbol : str, optional
            Trading symbol
        """
        context_key = self._get_context_key(exchange, operation, symbol)
        
        # Store measurement
        measurement = LatencyMeasurement(
            timestamp=time.time(),
            latency=latency,
            success=success,
            exchange=exchange,
            operation=operation,
            symbol=symbol
        )
        
        self.latency_history[context_key].append(measurement)
        self.success_history[context_key].append(success)
        self.timeout_history[context_key].append(timeout_used)
        
        # Update statistics
        self.stats['total_requests'] += 1
        if success:
            self.stats['successful_requests'] += 1
        
        # Update network condition assessment
        self._update_network_condition(latency, success)
        
        # Adapt configuration if needed
        self._adapt_configuration(exchange, context_key)

    def _get_context_key(self, exchange: str, operation: str, symbol: Optional[str]) -> str:
        """Generate context key for historical data storage."""
        if symbol:
            return f"{exchange}:{operation}:{symbol}"
        return f"{exchange}:{operation}"

    def _calculate_statistical_timeout(self, context_key: str) -> float:
        """Calculate timeout based on statistical analysis of historical data."""
        measurements = list(self.latency_history[context_key])
        
        if not measurements:
            return self.default_timeout
        
        # Extract successful measurements for baseline
        successful_latencies = [m.latency for m in measurements if m.success]
        all_latencies = [m.latency for m in measurements]
        
        if not successful_latencies:
            # If no successful measurements, use all measurements with penalty
            latencies = all_latencies
            penalty_multiplier = 2.0
        else:
            latencies = successful_latencies
            penalty_multiplier = 1.0
        
        # Calculate statistical measures
        mean_latency = statistics.mean(latencies)
        median_latency = statistics.median(latencies)
        
        if len(latencies) > 1:
            std_dev = statistics.stdev(latencies)
            # Use 95th percentile approach: mean + 2*std_dev
            statistical_timeout = mean_latency + (2.0 * std_dev)
        else:
            statistical_timeout = mean_latency * 2.0
        
        # Apply penalty for failed requests
        statistical_timeout *= penalty_multiplier
        
        # Blend with median for stability
        blended_timeout = (statistical_timeout * 0.7) + (median_latency * 1.5 * 0.3)
        
        return blended_timeout

    def _get_condition_multiplier(self) -> float:
        """Get timeout multiplier based on current network conditions."""
        if not self.network_conditions:
            return 1.0
        
        current_condition = self.network_conditions[-1]
        
        multipliers = {
            NetworkCondition.EXCELLENT: 0.8,
            NetworkCondition.GOOD: 1.0,
            NetworkCondition.FAIR: 1.3,
            NetworkCondition.POOR: 1.8,
            NetworkCondition.CRITICAL: 2.5
        }
        
        return multipliers.get(current_condition, 1.0)

    def _get_time_multiplier(self) -> float:
        """Get timeout multiplier based on time of day patterns."""
        current_hour = time.localtime().tm_hour
        
        # Higher timeouts during peak trading hours (market open times)
        if 8 <= current_hour <= 10 or 14 <= current_hour <= 16:  # Market open times
            return 1.2
        elif 0 <= current_hour <= 6:  # Low activity hours
            return 0.9
        else:
            return 1.0

    def _update_network_condition(self, latency: float, success: bool) -> None:
        """Update network condition assessment based on recent measurements."""
        current_time = time.time()
        
        # Update condition every 30 seconds
        if current_time - self.last_condition_update < 30:
            return
        
        self.last_condition_update = current_time
        
        # Analyze recent performance
        recent_measurements = []
        cutoff_time = current_time - 300  # Last 5 minutes
        
        for context_measurements in self.latency_history.values():
            recent_measurements.extend([
                m for m in context_measurements 
                if m.timestamp > cutoff_time
            ])
        
        if not recent_measurements:
            return
        
        # Calculate condition metrics
        success_rate = sum(1 for m in recent_measurements if m.success) / len(recent_measurements)
        avg_latency = statistics.mean([m.latency for m in recent_measurements])
        
        # Determine condition
        if success_rate >= 0.95 and avg_latency <= 2.0:
            condition = NetworkCondition.EXCELLENT
        elif success_rate >= 0.90 and avg_latency <= 5.0:
            condition = NetworkCondition.GOOD
        elif success_rate >= 0.80 and avg_latency <= 15.0:
            condition = NetworkCondition.FAIR
        elif success_rate >= 0.60 and avg_latency <= 30.0:
            condition = NetworkCondition.POOR
        else:
            condition = NetworkCondition.CRITICAL
        
        # Update condition history
        if not self.network_conditions or self.network_conditions[-1] != condition:
            self.network_conditions.append(condition)
            self.stats['condition_changes'] += 1
            logger.info(f"Network condition changed to: {condition.value}")

    def _adapt_configuration(self, exchange: str, context_key: str) -> None:
        """Adapt timeout configuration based on performance patterns."""
        if context_key not in self.latency_history:
            return
        
        measurements = list(self.latency_history[context_key])
        if len(measurements) < 20:  # Need sufficient data
            return
        
        # Calculate recent performance
        recent_measurements = measurements[-20:]
        success_rate = sum(1 for m in recent_measurements if m.success) / len(recent_measurements)
        
        # Adapt if performance is consistently good or bad
        if success_rate >= 0.95:
            # Performance is excellent, can reduce timeout
            self._adjust_exchange_config(exchange, 0.95)
            self.stats['timeout_adjustments'] += 1
        elif success_rate <= 0.70:
            # Performance is poor, increase timeout
            self._adjust_exchange_config(exchange, 1.1)
            self.stats['timeout_adjustments'] += 1

    def _adjust_exchange_config(self, exchange: str, multiplier: float) -> None:
        """Adjust exchange-specific timeout configuration."""
        if exchange not in self.exchange_configs:
            self.exchange_configs[exchange] = TimeoutConfig(
                base_timeout=self.default_timeout,
                min_timeout=self.min_timeout,
                max_timeout=self.max_timeout,
                multiplier=1.0,
                confidence_level=0.5
            )
        
        config = self.exchange_configs[exchange]
        new_multiplier = config.multiplier * multiplier
        new_multiplier = max(0.5, min(new_multiplier, 3.0))  # Reasonable bounds
        
        config.multiplier = new_multiplier
        config.base_timeout = self.default_timeout * new_multiplier
        config.confidence_level = min(config.confidence_level + 0.1, 1.0)
        
        logger.debug(f"Adjusted {exchange} timeout config: multiplier={new_multiplier:.2f}")

    def get_stats(self) -> Dict:
        """Get comprehensive timeout manager statistics."""
        return {
            **self.stats,
            'success_rate': self.stats['successful_requests'] / max(1, self.stats['total_requests']),
            'current_condition': self.network_conditions[-1].value if self.network_conditions else 'unknown',
            'exchange_configs': {
                exchange: {
                    'base_timeout': config.base_timeout,
                    'multiplier': config.multiplier,
                    'confidence_level': config.confidence_level
                }
                for exchange, config in self.exchange_configs.items()
            },
            'context_data_points': {
                context: len(measurements)
                for context, measurements in self.latency_history.items()
            }
        }

    def reset_exchange_config(self, exchange: str) -> None:
        """Reset exchange configuration to defaults."""
        if exchange in self.exchange_configs:
            del self.exchange_configs[exchange]
            logger.info(f"Reset timeout configuration for {exchange}")
