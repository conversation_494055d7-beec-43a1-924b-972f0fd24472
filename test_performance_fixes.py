#!/usr/bin/env python3
"""
Script de teste para validar as correções de performance implementadas.
Testa:
1. TICKER_CACHE_TTL reduzido para 5s
2. Base interval reduzido para 20s
3. Limit de candles OHLCV reduzido para 30
4. Logs de debug do cache de ticker
"""

import os
import sys
import time
from pathlib import Path

# Adicionar src ao path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_env_production_config():
    """Testa se as configurações do .env.production foram atualizadas"""
    print("\n=== TESTE 1: Configurações .env.production ===")
    
    env_file = Path(".env.production")
    if not env_file.exists():
        print("❌ Arquivo .env.production não encontrado")
        return False
    
    content = env_file.read_text(encoding='utf-8')
    
    # Verificar TICKER_CACHE_TTL
    if "TICKER_CACHE_TTL=5.0" in content:
        print("✅ TICKER_CACHE_TTL configurado para 5.0s")
    else:
        print("❌ TICKER_CACHE_TTL não foi atualizado para 5.0s")
        return False
    
    print("✅ Configurações do .env.production validadas")
    return True

def test_script_optimizations():
    """Testa se as otimizações do script foram aplicadas"""
    print("\n=== TESTE 2: Otimizações do Script ===")
    
    script_file = Path("scripts/run_fwh_scalp_paper_trading.py")
    if not script_file.exists():
        print("❌ Script run_fwh_scalp_paper_trading.py não encontrado")
        return False
    
    content = script_file.read_text(encoding='utf-8')
    
    # Verificar base_interval
    if "base_interval = 20" in content:
        print("✅ base_interval reduzido para 20s")
    else:
        print("❌ base_interval não foi atualizado para 20s")
        return False
    
    # Verificar fallback
    if "return 20  # Fallback" in content:
        print("✅ Fallback do intervalo atualizado para 20s")
    else:
        print("❌ Fallback do intervalo não foi atualizado")
        return False
    
    # Verificar limit de candles
    if "limit = 30 if timeframe == \"1m\" else 21" in content:
        print("✅ Limit de candles OHLCV reduzido para 30 (1m)")
    else:
        print("❌ Limit de candles não foi atualizado")
        return False
    
    print("✅ Otimizações do script validadas")
    return True

def test_cache_debug_logs():
    """Testa se os logs de debug do cache foram adicionados"""
    print("\n=== TESTE 3: Logs de Debug do Cache ===")
    
    base_integration_file = Path("src/qualia/market/base_integration.py")
    if not base_integration_file.exists():
        print("❌ Arquivo base_integration.py não encontrado")
        return False
    
    content = base_integration_file.read_text(encoding='utf-8')
    
    # Verificar logs de debug
    debug_patterns = [
        "[CACHE] Ticker cache miss",
        "[CACHE] Ticker para",
        "[CACHE] Ticker cache hit",
        "[CACHE] Ticker expirado"
    ]
    
    for pattern in debug_patterns:
        if pattern in content:
            print(f"✅ Log de debug encontrado: {pattern}")
        else:
            print(f"❌ Log de debug não encontrado: {pattern}")
            return False
    
    print("✅ Logs de debug do cache validados")
    return True

def calculate_expected_improvements():
    """Calcula as melhorias esperadas com as otimizações"""
    print("\n=== ANÁLISE DE MELHORIAS ESPERADAS ===")
    
    print("📊 Melhorias implementadas:")
    print("   • TICKER_CACHE_TTL: 10s → 5s (50% redução)")
    print("   • Base interval: 30s → 20s (33% redução)")
    print("   • Limit candles: 42 → 30 (29% redução)")
    
    print("\n🎯 Impactos esperados:")
    print("   • Ticker cache mais responsivo (dados mais frescos)")
    print("   • Ciclos de trading ~33% mais rápidos (20-22s vs 30-32s)")
    print("   • Menos warnings de candles insuficientes da Binance")
    print("   • Melhor monitoramento do cache via logs de debug")
    
    # Calcular novo intervalo esperado
    base_interval = 20
    position_factor = 1.2  # Sem posições abertas
    error_factor = 1.0     # Sem erros de API
    time_factor = 0.9      # Horário ativo (8h-22h)
    
    new_interval = int(base_interval * position_factor * error_factor * time_factor)
    print(f"\n⏱️  Novo intervalo esperado: {new_interval}s (vs 32s anterior)")
    print(f"   Melhoria: {((32 - new_interval) / 32) * 100:.1f}% mais rápido")

def main():
    """Executa todos os testes de validação"""
    print("🔧 VALIDAÇÃO DAS CORREÇÕES DE PERFORMANCE")
    print("=" * 50)
    
    tests = [
        test_env_production_config,
        test_script_optimizations,
        test_cache_debug_logs
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Erro no teste {test.__name__}: {e}")
            results.append(False)
    
    # Análise de melhorias
    calculate_expected_improvements()
    
    # Resumo final
    print("\n" + "=" * 50)
    print("📋 RESUMO DOS TESTES")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ Todos os {total} testes passaram!")
        print("🚀 Correções implementadas com sucesso")
        print("\n📝 Próximos passos:")
        print("   1. Executar o sistema e monitorar os logs")
        print("   2. Verificar se o ticker cache não fica mais defasado")
        print("   3. Confirmar que os ciclos estão mais rápidos (~21s)")
        print("   4. Validar que não há mais warnings de candles insuficientes")
    else:
        print(f"❌ {total - passed}/{total} testes falharam")
        print("⚠️  Revisar as correções antes de executar o sistema")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)