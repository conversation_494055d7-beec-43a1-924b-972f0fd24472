from pathlib import Path
from datetime import datetime, timezone
import json

from qualia.analysis.threshold_optimizer import grid_search


def _write_hist(tmp_path: Path):
    p = tmp_path / "hist.jsonl"
    alerts = [
        {
            "type": "alert",
            "timestamp": datetime(2020, 1, 1, tzinfo=timezone.utc).timestamp(),
        },
    ]
    p.write_text("\n".join(json.dumps(a) for a in alerts))
    return p


def _write_ev(tmp_path: Path):
    p = tmp_path / "ev.json"
    p.write_text(json.dumps([{"name": "Test", "date": "2020-01-05"}]))
    return p


def test_grid_search(tmp_path):
    hist = _write_hist(tmp_path)
    evs = _write_ev(tmp_path)
    params, metrics = grid_search(
        hist, evs, alphas=[0.1], offsets=[0.05], window_days=7
    )
    assert params["alpha"] == 0.1 and metrics["recall"] >= 0.0
