# Funções críticas sem teste

Esta lista foi gerada para identificar partes do código que precisam de maior cobertura.
As funções abaixo localizadas em `src/qualia/core` e `src/qualia/utils` ainda não
possuem testes específicos.

## src/qualia/core
- `FoldingOperator.evaluate_reversibility` (casos não perfeitos)
- `FoldingOperator.get_state_dict` quando nenhum estado foi criado
- `RetrocausalityOperator._extract_future_indicators`
- `RetrocausalityOperator._create_retrocausal_event`
- `apply_retrocausality` parâmetros `shift` e `history`

## src/qualia/utils
- `_extract_timestamp_utc`
- `parse_complex_string`
- `bitstring_to_int` / `int_to_bitstring`
- `hamming_distance`
