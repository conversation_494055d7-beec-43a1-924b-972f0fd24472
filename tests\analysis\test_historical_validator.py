import json
from datetime import datetime, timezone
from pathlib import Path

from qualia.analysis.historical_validator import validate
from qualia.analysis.historical_validator import prf1


def _write_history(tmp_path: Path):
    path = tmp_path / "hist.jsonl"
    alerts = [
        {"type": "alert", "timestamp": datetime(2010, 12, 10, tzinfo=timezone.utc).timestamp()},
        {"type": "alert", "timestamp": datetime(2019, 12, 25, tzinfo=timezone.utc).timestamp()},
    ]
    with path.open("w", encoding="utf-8") as fh:
        for rec in alerts:
            fh.write(json.dumps(rec) + "\n")
    return path


def _write_events(tmp_path: Path):
    events = [
        {"name": "Arab Spring Start", "date": "2010-12-17"},
        {"name": "COVID-19 Pandemic", "date": "2019-12-31"},
    ]
    path = tmp_path / "ev.json"
    path.write_text(json.dumps(events), encoding="utf-8")
    return path


def test_validator_detects(tmp_path):
    hist = _write_history(tmp_path)
    evs = _write_events(tmp_path)
    res = validate(hist, evs, window_days=30)
    assert all(r["detected"] for r in res)
    assert res[0]["lead_days"] == 7  # 17th - 10th

    m = prf1(res)
    assert m["f1"] == 1.0 