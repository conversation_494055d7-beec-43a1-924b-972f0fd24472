{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Sweep de OTOC x Scrambling Depth\n", "Este notebook carrega dados simulados de OTOC gerados pelo script `generate_otoc_scrambling_data.py` e plota curvas de profundidade de scrambling versus OTOC para diferentes números de qubits e níveis de ruído."]}, {"cell_type": "code", "metadata": {}, "execution_count": null, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "metadata": {}, "execution_count": null, "outputs": [], "source": ["df = pd.read_csv('docs/data/otoc_scrambling_data.csv')\n", "df.head()"]}, {"cell_type": "code", "metadata": {}, "execution_count": null, "outputs": [], "source": ["for (nq, noise), group in df.groupby(['num_qubits', 'noise']):\n", "    group.plot(x='scr_depth', y='otoc', marker='o', label=f'noise={noise}')\n", "    plt.title(f'OTOC vs Scrambling Depth - {nq} qubits')\n", "    plt.ylabel('OTOC')\n", "    plt.xlabel('Scrambling Depth')\n", "    plt.legend()\n", "    plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "pygments_lexer": "ipython3"}}, "nbformat": 4, "nbformat_minor": 2}