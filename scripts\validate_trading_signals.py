#!/usr/bin/env python3
"""
QUALIA Enhanced Trading Signal Validation System
Valida o fluxo completo: Padrões → Sinais → Decisões → Posições → Performance
"""

import asyncio
import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import statistics

# QUALIA Core Imports
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from qualia.consciousness.real_data_collectors import RealDataCollector
from qualia.consciousness.holographic_universe import HolographicMarketUniverse
from qualia.risk_management.advanced_risk_manager import AdvancedRiskManager
from qualia.market.kucoin_integration import KucoinIntegration
from qualia.config.config_loader import ConfigLoader

@dataclass
class TradingSignal:
    """Estrutura de sinal de trading"""
    timestamp: datetime
    symbol: str
    timeframe: str
    signal_type: str  # 'BUY', 'SELL', 'HOLD'
    strength: float   # 0.0 - 1.0
    confidence: float # 0.0 - 1.0
    pattern_source: str
    entry_price: float
    stop_loss: float
    take_profit: float
    position_size: float
    metadata: Dict[str, Any]

@dataclass
class Position:
    """Estrutura de posição"""
    id: str
    signal: TradingSignal
    entry_time: datetime
    entry_price: float
    current_price: float
    quantity: float
    side: str  # 'long', 'short'
    status: str  # 'open', 'closed'
    pnl: float = 0.0
    pnl_percentage: float = 0.0
    exit_time: Optional[datetime] = None
    exit_price: Optional[float] = None
    exit_reason: str = ""

@dataclass
class PerformanceMetrics:
    """Métricas de performance"""
    total_signals: int = 0
    total_positions: int = 0
    winning_positions: int = 0
    losing_positions: int = 0
    total_pnl: float = 0.0
    total_pnl_percentage: float = 0.0
    win_rate: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    profit_factor: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    avg_trade_duration: float = 0.0

class TradingSignalValidator:
    """Sistema de validação de sinais de trading"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.signals: List[TradingSignal] = []
        self.positions: List[Position] = []
        self.performance = PerformanceMetrics()
        
        # Componentes QUALIA
        self.data_collector = None
        self.holographic_universe = None
        self.risk_manager = None
        self.exchange_client = None
        
        # Configurações
        self.config = ConfigLoader.load_config()
        self.symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT']
        self.timeframes = ['5m', '15m', '1h']
        self.initial_balance = 10000.0  # USD
        self.current_balance = self.initial_balance
        
        # Thresholds para forçar sinais
        self.force_signal_threshold = 0.2  # Reduzido para gerar mais sinais
        self.min_pattern_strength = 0.15   # Reduzido para capturar padrões fracos
        
    async def initialize_components(self):
        """Inicializa componentes QUALIA"""
        self.logger.info("🔧 Inicializando componentes para validação...")
        
        # Real Data Collector
        self.data_collector = RealDataCollector()
        
        # Holographic Universe
        self.holographic_universe = HolographicMarketUniverse()
        
        # Risk Manager
        risk_config = self.config.get('risk', {})
        self.risk_manager = AdvancedRiskManager(
            max_position_size=risk_config.get('max_position_size', 0.1),
            max_daily_loss=risk_config.get('max_daily_loss', 0.05),
            max_correlation=risk_config.get('max_correlation', 0.7)
        )
        
        # Exchange Client
        exchange_config = self.config.get('exchanges', {}).get('kucoin', {})
        if exchange_config:
            self.exchange_client = KucoinIntegration(exchange_config)
            # KucoinIntegration não tem initialize_connection, usa setup interno
            
        self.logger.info("✅ Componentes inicializados")
    
    async def collect_market_data(self) -> Dict[str, Any]:
        """Coleta dados de mercado para análise"""
        market_data = {}
        
        for symbol in self.symbols:
            for timeframe in self.timeframes:
                try:
                    # Coleta dados via data collector
                    data_points = await self.data_collector.collect_market_data()
                    
                    # Simula dados se não conseguir coletar
                    if not data_points:
                        data_points = await self._simulate_market_data(symbol, timeframe)
                    
                    market_data[f"{symbol}_{timeframe}"] = data_points
                    
                except Exception as e:
                    self.logger.error(f"Erro coletando dados {symbol}@{timeframe}: {e}")
                    # Fallback para dados simulados
                    market_data[f"{symbol}_{timeframe}"] = await self._simulate_market_data(symbol, timeframe)
        
        return market_data
    
    async def _simulate_market_data(self, symbol: str, timeframe: str) -> Dict[str, Any]:
        """Simula dados de mercado para teste"""
        import random
        
        base_price = {'BTCUSDT': 107000, 'ETHUSDT': 3500, 'ADAUSDT': 0.9, 'SOLUSDT': 200}[symbol]
        
        return {
            'symbol': symbol,
            'timeframe': timeframe,
            'price': base_price * (1 + random.uniform(-0.02, 0.02)),
            'volume': random.uniform(1000000, 10000000),
            'change_24h': random.uniform(-5, 5),
            'rsi': random.uniform(20, 80),
            'volume_ratio': random.uniform(0.5, 2.0),
            'volatility': random.uniform(0.01, 0.05)
        }
    
    async def analyze_patterns_and_generate_signals(self, market_data: Dict[str, Any]) -> List[TradingSignal]:
        """Analisa padrões holográficos e gera sinais de trading"""
        signals = []
        
        # Injeta eventos no universo holográfico
        await self._inject_holographic_events(market_data)
        
        # Analisa padrões
        patterns = self.holographic_universe.analyze_patterns()
        
        self.logger.info(f"🔍 Padrões detectados: {len(patterns)}")
        
        for pattern in patterns:
            # Força geração de sinais com threshold reduzido
            if pattern.get('strength', 0) >= self.min_pattern_strength:
                signal = await self._pattern_to_signal(pattern, market_data)
                if signal:
                    signals.append(signal)
                    self.logger.info(f"📊 Sinal gerado: {signal.symbol} {signal.signal_type} (força: {signal.strength:.3f})")
        
        # Se não gerou sinais suficientes, força alguns baseados em indicadores
        if len(signals) < 2:
            forced_signals = await self._force_signals_from_indicators(market_data)
            signals.extend(forced_signals)
        
        return signals
    
    async def _inject_holographic_events(self, market_data: Dict[str, Any]):
        """Injeta eventos no universo holográfico"""
        for key, data in market_data.items():
            if not data:
                continue
                
            symbol = data['symbol']
            
            # Calcula amplitude baseada em múltiplos fatores
            price_change = abs(data.get('change_24h', 0)) / 100
            volume_factor = min(data.get('volume_ratio', 1.0) / 2.0, 1.0)
            rsi_factor = abs(data.get('rsi', 50) - 50) / 50
            
            amplitude = (price_change + volume_factor + rsi_factor) / 3.0
            amplitude = max(amplitude, 0.1)  # Amplitude mínima
            
            # Injeta evento
            await self.holographic_universe.inject_event(
                event_type=f"enhanced_market_{symbol.lower()}",
                amplitude=amplitude,
                metadata=data
            )
    
    async def _pattern_to_signal(self, pattern: Dict[str, Any], market_data: Dict[str, Any]) -> Optional[TradingSignal]:
        """Converte padrão holográfico em sinal de trading"""
        try:
            # Determina símbolo baseado no padrão
            symbol = self._extract_symbol_from_pattern(pattern)
            if not symbol:
                return None
            
            # Busca dados do mercado para o símbolo
            symbol_data = None
            for key, data in market_data.items():
                if data and data.get('symbol') == symbol:
                    symbol_data = data
                    break
            
            if not symbol_data:
                return None
            
            # Determina tipo de sinal baseado no padrão
            signal_type = self._determine_signal_type(pattern, symbol_data)
            
            # Calcula força e confiança
            strength = min(pattern.get('strength', 0.3), 1.0)
            confidence = min(pattern.get('coherence', 0.5), 1.0)
            
            # Preços e gestão de risco
            entry_price = symbol_data['price']
            stop_loss, take_profit = self._calculate_risk_levels(entry_price, signal_type)
            position_size = self._calculate_position_size(symbol, entry_price, stop_loss)
            
            return TradingSignal(
                timestamp=datetime.now(),
                symbol=symbol,
                timeframe='15m',  # Default
                signal_type=signal_type,
                strength=strength,
                confidence=confidence,
                pattern_source=f"holographic_pattern_{pattern.get('id', 'unknown')}",
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                position_size=position_size,
                metadata={
                    'pattern': pattern,
                    'market_data': symbol_data,
                    'rsi': symbol_data.get('rsi'),
                    'volume_ratio': symbol_data.get('volume_ratio')
                }
            )
            
        except Exception as e:
            self.logger.error(f"Erro convertendo padrão em sinal: {e}")
            return None
    
    def _extract_symbol_from_pattern(self, pattern: Dict[str, Any]) -> Optional[str]:
        """Extrai símbolo do padrão"""
        # Busca por referências de símbolos no padrão
        pattern_str = str(pattern).upper()
        for symbol in self.symbols:
            if symbol in pattern_str:
                return symbol
        
        # Se não encontrar, retorna símbolo aleatório para teste
        import random
        return random.choice(self.symbols)
    
    def _determine_signal_type(self, pattern: Dict[str, Any], market_data: Dict[str, Any]) -> str:
        """Determina tipo de sinal baseado no padrão e dados"""
        # Análise baseada em RSI
        rsi = market_data.get('rsi', 50)
        strength = pattern.get('strength', 0.3)
        
        if rsi < 30 and strength > 0.3:
            return 'BUY'  # RSI oversold + padrão forte
        elif rsi > 70 and strength > 0.3:
            return 'SELL'  # RSI overbought + padrão forte
        elif strength > 0.5:
            # Padrão muito forte, determina direção por slope
            slope = pattern.get('slope', 0)
            return 'BUY' if slope > 0 else 'SELL'
        else:
            return 'HOLD'
    
    def _calculate_risk_levels(self, entry_price: float, signal_type: str) -> tuple:
        """Calcula níveis de stop loss e take profit"""
        if signal_type == 'BUY':
            stop_loss = entry_price * 0.98  # 2% stop loss
            take_profit = entry_price * 1.06  # 6% take profit
        elif signal_type == 'SELL':
            stop_loss = entry_price * 1.02  # 2% stop loss
            take_profit = entry_price * 0.94  # 6% take profit
        else:
            stop_loss = entry_price
            take_profit = entry_price
            
        return stop_loss, take_profit
    
    def _calculate_position_size(self, symbol: str, entry_price: float, stop_loss: float) -> float:
        """Calcula tamanho da posição baseado no risco"""
        risk_per_trade = self.current_balance * 0.02  # 2% do capital por trade
        price_risk = abs(entry_price - stop_loss)
        
        if price_risk > 0:
            position_size = risk_per_trade / price_risk
            # Limita a 10% do capital
            max_position = self.current_balance * 0.1 / entry_price
            return min(position_size, max_position)
        
        return self.current_balance * 0.05 / entry_price  # 5% default
    
    async def _force_signals_from_indicators(self, market_data: Dict[str, Any]) -> List[TradingSignal]:
        """Força sinais baseados em indicadores técnicos"""
        forced_signals = []
        
        for key, data in market_data.items():
            if not data:
                continue
                
            symbol = data['symbol']
            rsi = data.get('rsi', 50)
            volume_ratio = data.get('volume_ratio', 1.0)
            
            # Força sinal se RSI extremo + volume alto
            if (rsi < 25 or rsi > 75) and volume_ratio > 1.5:
                signal_type = 'BUY' if rsi < 25 else 'SELL'
                entry_price = data['price']
                stop_loss, take_profit = self._calculate_risk_levels(entry_price, signal_type)
                position_size = self._calculate_position_size(symbol, entry_price, stop_loss)
                
                signal = TradingSignal(
                    timestamp=datetime.now(),
                    symbol=symbol,
                    timeframe='15m',
                    signal_type=signal_type,
                    strength=0.7,
                    confidence=0.6,
                    pattern_source="forced_rsi_volume",
                    entry_price=entry_price,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    position_size=position_size,
                    metadata={
                        'rsi': rsi,
                        'volume_ratio': volume_ratio,
                        'forced': True
                    }
                )
                
                forced_signals.append(signal)
                self.logger.info(f"🎯 Sinal forçado: {symbol} {signal_type} (RSI: {rsi:.1f})")
        
        return forced_signals
    
    async def simulate_position_execution(self, signals: List[TradingSignal]) -> List[Position]:
        """Simula execução de posições baseadas nos sinais"""
        new_positions = []
        
        for signal in signals:
            if signal.signal_type == 'HOLD':
                continue
                
            # Valida risco antes de abrir posição
            risk_assessment = await self.risk_manager.assess_risk(
                symbol=signal.symbol,
                position_size=signal.position_size,
                current_price=signal.entry_price
            )
            
            if not risk_assessment.get('approved', False):
                self.logger.warning(f"❌ Posição rejeitada pelo risk manager: {signal.symbol}")
                continue
            
            # Cria posição
            position = Position(
                id=f"pos_{len(self.positions) + len(new_positions) + 1}",
                signal=signal,
                entry_time=signal.timestamp,
                entry_price=signal.entry_price,
                current_price=signal.entry_price,
                quantity=signal.position_size,
                side='long' if signal.signal_type == 'BUY' else 'short',
                status='open'
            )
            
            new_positions.append(position)
            self.logger.info(f"📈 Posição aberta: {position.id} {signal.symbol} {signal.signal_type}")
        
        return new_positions
    
    async def update_positions(self, market_data: Dict[str, Any]):
        """Atualiza posições abertas com preços atuais"""
        for position in self.positions:
            if position.status != 'open':
                continue
                
            # Busca preço atual
            current_price = None
            for key, data in market_data.items():
                if data and data.get('symbol') == position.signal.symbol:
                    current_price = data['price']
                    break
            
            if not current_price:
                continue
                
            position.current_price = current_price
            
            # Calcula PnL
            if position.side == 'long':
                position.pnl = (current_price - position.entry_price) * position.quantity
            else:
                position.pnl = (position.entry_price - current_price) * position.quantity
                
            position.pnl_percentage = (position.pnl / (position.entry_price * position.quantity)) * 100
            
            # Verifica condições de fechamento
            await self._check_exit_conditions(position)
    
    async def _check_exit_conditions(self, position: Position):
        """Verifica condições para fechar posição"""
        signal = position.signal
        current_price = position.current_price
        
        should_close = False
        exit_reason = ""
        
        # Stop Loss
        if position.side == 'long' and current_price <= signal.stop_loss:
            should_close = True
            exit_reason = "stop_loss"
        elif position.side == 'short' and current_price >= signal.stop_loss:
            should_close = True
            exit_reason = "stop_loss"
        
        # Take Profit
        elif position.side == 'long' and current_price >= signal.take_profit:
            should_close = True
            exit_reason = "take_profit"
        elif position.side == 'short' and current_price <= signal.take_profit:
            should_close = True
            exit_reason = "take_profit"
        
        # Tempo máximo (24h para teste)
        elif (datetime.now() - position.entry_time).total_seconds() > 86400:
            should_close = True
            exit_reason = "max_time"
        
        if should_close:
            await self._close_position(position, exit_reason)
    
    async def _close_position(self, position: Position, reason: str):
        """Fecha posição"""
        position.status = 'closed'
        position.exit_time = datetime.now()
        position.exit_price = position.current_price
        position.exit_reason = reason
        
        # Atualiza balance
        self.current_balance += position.pnl
        
        self.logger.info(f"📉 Posição fechada: {position.id} {reason} PnL: {position.pnl:.2f} ({position.pnl_percentage:.1f}%)")
    
    def calculate_performance_metrics(self):
        """Calcula métricas de performance"""
        closed_positions = [p for p in self.positions if p.status == 'closed']
        
        if not closed_positions:
            return
        
        # Básicas
        self.performance.total_signals = len(self.signals)
        self.performance.total_positions = len(closed_positions)
        self.performance.winning_positions = len([p for p in closed_positions if p.pnl > 0])
        self.performance.losing_positions = len([p for p in closed_positions if p.pnl < 0])
        
        # PnL
        self.performance.total_pnl = sum(p.pnl for p in closed_positions)
        self.performance.total_pnl_percentage = ((self.current_balance - self.initial_balance) / self.initial_balance) * 100
        
        # Win Rate
        if self.performance.total_positions > 0:
            self.performance.win_rate = (self.performance.winning_positions / self.performance.total_positions) * 100
        
        # Médias
        winning_trades = [p.pnl for p in closed_positions if p.pnl > 0]
        losing_trades = [p.pnl for p in closed_positions if p.pnl < 0]
        
        self.performance.avg_win = statistics.mean(winning_trades) if winning_trades else 0
        self.performance.avg_loss = statistics.mean(losing_trades) if losing_trades else 0
        
        # Profit Factor
        gross_profit = sum(winning_trades) if winning_trades else 0
        gross_loss = abs(sum(losing_trades)) if losing_trades else 1
        self.performance.profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0
        
        # Duração média
        durations = [(p.exit_time - p.entry_time).total_seconds() / 3600 for p in closed_positions if p.exit_time]
        self.performance.avg_trade_duration = statistics.mean(durations) if durations else 0
    
    def print_performance_report(self):
        """Imprime relatório de performance"""
        self.calculate_performance_metrics()
        
        print("\n" + "="*60)
        print("🎯 QUALIA TRADING SIGNAL VALIDATION REPORT")
        print("="*60)
        
        print("\n📊 SINAIS E POSIÇÕES:")
        print(f"   • Total de Sinais Gerados: {self.performance.total_signals}")
        print(f"   • Total de Posições: {self.performance.total_positions}")
        print(f"   • Posições Vencedoras: {self.performance.winning_positions}")
        print(f"   • Posições Perdedoras: {self.performance.losing_positions}")
        
        print("\n💰 PERFORMANCE FINANCEIRA:")
        print(f"   • Capital Inicial: ${self.initial_balance:,.2f}")
        print(f"   • Capital Final: ${self.current_balance:,.2f}")
        print(f"   • PnL Total: ${self.performance.total_pnl:,.2f}")
        print(f"   • Retorno Percentual: {self.performance.total_pnl_percentage:.2f}%")
        
        print("\n📈 MÉTRICAS DE TRADING:")
        print(f"   • Win Rate: {self.performance.win_rate:.1f}%")
        print(f"   • Ganho Médio: ${self.performance.avg_win:.2f}")
        print(f"   • Perda Média: ${self.performance.avg_loss:.2f}")
        print(f"   • Profit Factor: {self.performance.profit_factor:.2f}")
        print(f"   • Duração Média: {self.performance.avg_trade_duration:.1f}h")
        
        print("\n🎲 ÚLTIMAS POSIÇÕES:")
        recent_positions = sorted(self.positions, key=lambda p: p.entry_time, reverse=True)[:5]
        for pos in recent_positions:
            status_icon = "✅" if pos.pnl > 0 else "❌" if pos.pnl < 0 else "⏳"
            print(f"   {status_icon} {pos.signal.symbol} {pos.side} | PnL: ${pos.pnl:.2f} ({pos.pnl_percentage:.1f}%) | {pos.status}")
        
        print("\n" + "="*60)
    
    async def run_validation_cycle(self, duration_minutes: int = 5):
        """Executa ciclo completo de validação"""
        self.logger.info(f"🚀 Iniciando validação por {duration_minutes} minutos...")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        cycle = 0
        
        while datetime.now() < end_time:
            cycle += 1
            self.logger.info(f"\n🔄 Ciclo {cycle} - {datetime.now().strftime('%H:%M:%S')}")
            
            try:
                # 1. Coleta dados
                market_data = await self.collect_market_data()
                self.logger.info(f"📊 Dados coletados: {len(market_data)} símbolos")
                
                # 2. Analisa padrões e gera sinais
                new_signals = await self.analyze_patterns_and_generate_signals(market_data)
                self.signals.extend(new_signals)
                self.logger.info(f"📡 Novos sinais: {len(new_signals)}")
                
                # 3. Executa posições
                new_positions = await self.simulate_position_execution(new_signals)
                self.positions.extend(new_positions)
                
                # 4. Atualiza posições existentes
                await self.update_positions(market_data)
                
                # 5. Relatório rápido
                open_positions = len([p for p in self.positions if p.status == 'open'])
                closed_positions = len([p for p in self.positions if p.status == 'closed'])
                current_pnl = sum(p.pnl for p in self.positions if p.status == 'closed')
                
                self.logger.info(f"💼 Posições: {open_positions} abertas, {closed_positions} fechadas | PnL: ${current_pnl:.2f}")
                
                # Aguarda próximo ciclo
                await asyncio.sleep(30)  # 30 segundos entre ciclos
                
            except Exception as e:
                self.logger.error(f"❌ Erro no ciclo {cycle}: {e}")
                await asyncio.sleep(10)
        
        self.logger.info("✅ Validação concluída!")
        self.print_performance_report()

async def main():
    """Função principal"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    validator = TradingSignalValidator()
    
    try:
        await validator.initialize_components()
        await validator.run_validation_cycle(duration_minutes=10)  # 10 minutos de teste
        
    except KeyboardInterrupt:
        print("\n🛑 Validação interrompida pelo usuário")
        validator.print_performance_report()
    except Exception as e:
        print(f"\n❌ Erro durante validação: {e}")
        validator.print_performance_report()

if __name__ == "__main__":
    asyncio.run(main())
