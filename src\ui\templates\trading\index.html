<!-- Revisado em 2025-06-13 por Codex -->
{% extends 'base.html' %}

{% block title %}QUALIA Trading System | Integração Kraken{% endblock %}

{% block extra_head %}
    <link rel="stylesheet" href="{{ url_for('static', filename='css/trading-index.css') }}">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
{% endblock %}

{% block body %}
<div class="trading-app">
    {% block header %}
    <header class="trading-header">
        <div class="app-title">
            <div class="app-logo">🧠</div>
            <div class="app-name">QUALIA Trading System</div>
        </div>
        <div class="header-controls">
            <div class="system-status-indicator">
                <div class="indicator-led" id="system-status-led"></div>
                <div id="status-text">Sistema Parado</div>
            </div>
            <div id="update-timestamp" class="timestamp"></div>
        </div>
    </header>
    {% endblock %}

    <aside class="trading-sidebar">
        <div class="sidebar-section">
            <div class="sidebar-section-title">Credenciais API Kraken</div>
            <form id="api-credentials-form" class="api-credentials" method="post">
                {{ csrf_token() }}
                <div class="form-group">
                    <label for="api-key">API Key</label>
                    <input type="text" id="api-key" class="form-control" placeholder="Insira sua API Key">
                </div>
                <div class="form-group">
                    <label for="api-secret">API Secret</label>
                    <input type="password" id="api-secret" class="form-control" placeholder="Insira seu API Secret">
                </div>
                <button id="test-api-btn" class="quantum-button">Testar Conexão</button>
            </form>
        </div>
        <div class="sidebar-section">
            <div class="sidebar-section-title">Configurações do Sistema</div>
            <div class="form-group">
                <label for="initial-capital">Capital Inicial ($)</label>
                <input type="number" id="initial-capital" class="form-control" value="10000">
            </div>
            <div class="form-group">
                <label for="risk-profile">Perfil de Risco</label>
                <select id="risk-profile" class="form-control">
                    <option value="conservative">Conservador</option>
                    <option value="balanced" selected>Balanceado</option>
                    <option value="aggressive">Agressivo</option>
                </select>
            </div>
            <div class="toggle-container">
                <div class="toggle-label">Modo Trading:</div>
                <label class="toggle-switch">
                    <input type="checkbox" id="live-mode-toggle">
                    <span class="toggle-slider"></span>
                </label>
                <div class="toggle-text" id="mode-label">Simulação</div>
            </div>
        </div>
        <div class="sidebar-section">
            <div class="sidebar-section-title">Gerenciamento Avançado</div>
            <div class="form-group">
                <label for="drawdown-threshold">Threshold de Drawdown (%)</label>
                <input type="number" id="drawdown-threshold" class="form-control" value="10">
            </div>
            <div class="toggle-container">
                <div class="toggle-label">Retrocausalidade:</div>
                <label class="toggle-switch">
                    <input type="checkbox" id="retrocausality-toggle">
                    <span class="toggle-slider"></span>
                </label>
            </div>
            <div class="form-group">
                <label for="retrocausal-coefficient">Coeficiente Retrocausal</label>
                <input type="number" id="retrocausal-coefficient" class="form-control" step="0.1" value="0.5">
            </div>
            <button id="update-config-btn" class="quantum-button">Atualizar Config.</button>
        </div>
        <div class="sidebar-section">
            <div class="sidebar-section-title">Pares de Trading</div>
            <div id="trading-pairs-container"></div>
        </div>
        <div class="sidebar-section">
            <div class="sidebar-section-title">Controles do Sistema</div>
            <div class="controls-group">
                <button id="initialize-btn" class="quantum-button">Inicializar Sistema</button>
                <button id="start-btn" class="quantum-button" disabled>Iniciar Trading</button>
                <button id="stop-btn" class="quantum-button" disabled>Parar Trading</button>
            </div>
            <div class="status-indicators">
                <div class="status-indicator">
                    <div class="indicator-led" id="api-connection-led"></div>
                    <div class="indicator-label">API Kraken</div>
                </div>
                <div class="status-indicator">
                    <div class="indicator-led" id="trading-system-led"></div>
                    <div class="indicator-label">Sistema</div>
                </div>
                <div class="status-indicator">
                    <div class="indicator-led" id="live-mode-led"></div>
                    <div class="indicator-label">Modo Real</div>
                </div>
            </div>
        </div>
    </aside>

    <main class="trading-main">
        <div class="panel">
            <div class="panel-header">Dados de Mercado</div>
            <div class="panel-content">
                <div class="market-tabs">
                    <div class="tab-headers" id="market-tab-headers"></div>
                    <div class="tab-content" id="market-tab-content"></div>
                </div>
            </div>
        </div>
        <div class="main-panels">
            <div class="panel">
                <div class="panel-header">Métricas de Performance</div>
                <div class="panel-content">
                    <div class="grid-3-col">
                        <div class="metrics-display">
                            <div class="metric-label">P&L</div>
                            <div class="metric-value" id="pnl-value">$0.00</div>
                            <div class="metric-subvalue" id="pnl-percent">0.00%</div>
                        </div>
                        <div class="metrics-display">
                            <div class="metric-label">Taxa de Vitória</div>
                            <div class="metric-value" id="win-rate">0.0%</div>
                            <div class="metric-subvalue" id="trade-count">0 operações</div>
                        </div>
                        <div class="metrics-display">
                            <div class="metric-label">Drawdown Máximo</div>
                            <div class="metric-value" id="max-drawdown">0.00%</div>
                            <div class="metric-subvalue">Período Total</div>
                        </div>
                        <div class="metrics-display">
                            <div class="metric-label">Sharpe Ratio</div>
                            <div class="metric-value" id="sharpe-ratio">0.00</div>
                            <div class="metric-subvalue">Diário</div>
                        </div>
                        <div class="metrics-display">
                            <div class="metric-label">Métricas Quânticas</div>
                            <div id="quantum-metrics-line-chart" style="height: 100px;"></div>
                        </div>
                        <div class="metrics-display">
                            <div class="metric-label">Curva de Equity</div>
                            <div id="equity-curve" style="height: 100px;"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel">
                <div class="panel-header">Métricas Quânticas</div>
                <div class="panel-content">
                    <div class="grid-3-col">
                        <div class="metrics-display">
                            <div class="metric-label">Page Entropy</div>
                            <div class="metric-value" id="page-entropy-value">0.00</div>
                            <div class="metric-subvalue">Entropia de Page</div>
                        </div>
                        <div class="metrics-display">
                            <div class="metric-label">OTOC</div>
                            <div class="metric-value" id="otoc-value">0.00</div>
                            <div class="metric-subvalue">Out-of-Time-Order Correlator</div>
                        </div>
                        <div class="metrics-display">
                            <div class="metric-label">Coerência Quântica</div>
                            <div class="metric-value" id="quantum-coherence-value">0.00</div>
                            <div class="metric-subvalue">Nível de Coerência</div>
                        </div>
                        <div class="metrics-display">
                            <div class="metric-label">Eco de Loschmidt</div>
                            <div class="metric-value" id="loschmidt-echo-value">0.00</div>
                            <div class="metric-subvalue">Sensibilidade Quântica</div>
                        </div>
                        <div class="metrics-display">
                            <div class="metric-label">Reconhecimento</div>
                            <div class="metric-value" id="pattern-recognition-value">0.00</div>
                            <div class="metric-subvalue">Taxa de Reconhecimento</div>
                        </div>
                        <div class="metrics-display">
                            <div class="metric-label">Auto-Reflexão</div>
                            <div class="metric-value" id="self-reflection-value">0.00</div>
                            <div class="metric-subvalue">Profundidade</div>
                        </div>
                    </div>
                    <div class="chart-container" style="margin-top: 20px;">
                        <canvas id="quantum-metrics-chart" height="200"></canvas>
                    </div>
                    <div class="metric-explanation" style="margin-top: 20px; padding: 15px; background-color: rgba(72, 91, 196, 0.1); border-radius: 8px;">
                        <h4 style="margin-top: 0; color: var(--trading-accent);">Interpretação das Métricas Quânticas</h4>
                        <ul style="color: var(--trading-text-secondary); font-size: 14px; line-height: 1.5;">
                            <li><strong>Entropia de Page:</strong> Mede o nível de emaranhamento quântico no sistema. Valores altos indicam maior distribuição de probabilidade entre estados quânticos, sugerindo potencial para identificação de novas oportunidades de mercado.</li>
                            <li><strong>Coerência Quântica:</strong> Reflete a capacidade do sistema manter superposições quânticas estáveis. Valores altos sugerem maior confiabilidade nas previsões em mercados estáveis.</li>
                            <li><strong>Eco de Loschmidt:</strong> Mede a sensibilidade do sistema a pequenas perturbações. Valores baixos indicam maior sensibilidade, útil para detecção antecipada de mudanças de tendência.</li>
                            <li><strong>Taxa de Reconhecimento:</strong> Avalia a capacidade do sistema identificar padrões repetitivos em dados históricos. Correlacionado com precisão de previsões em mercados cíclicos.</li>
                        </ul>
                        <div style="font-style: italic; font-size: 13px; color: var(--trading-text-secondary); margin-top: 10px;">
                            Nota: Estas métricas são calculadas diretamente do hardware quântico QUALIA e não são simuladas. Os valores são atualizados em tempo real durante a execução do sistema.
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel" id="quantum-circuit-panel">
                <div class="panel-header">
                    <span>Circuito Quântico QUALIA</span>
                    <div>
                        <button class="quantum-button small" id="refresh-circuit-btn">Atualizar Circuito</button>
                        <button class="quantum-button small" id="export-qasm-btn">Exportar QASM</button>
                    </div>
                </div>
                <div class="panel-content">
                    <div class="circuit-visualization-container">
                        <div class="circuit-options">
                            <div class="form-group" style="max-width: 150px;">
                                <label for="circuit-steps">Passos de evolução:</label>
                                <input type="number" id="circuit-steps" class="form-control" value="5" min="1" max="20">
                            </div>
                            <div class="form-group" style="max-width: 150px;">
                                <label for="visualization-type">Tipo de visualização:</label>
                                <select id="visualization-type" class="form-control">
                                    <option value="mpl">Circuito Completo</option>
                                    <option value="bloch">Esferas de Bloch</option>
                                    <option value="text">Representação Textual</option>
                                </select>
                            </div>
                        </div>
                        <div id="circuit-visualization-area" class="circuit-visualization">
                            <div class="circuit-placeholder">
                                <p>Selecione as opções e clique em "Atualizar Circuito" para visualizar o circuito quântico atual.</p>
                            </div>
                        </div>
                        <div class="circuit-info" style="margin-top: 15px; font-size: 14px;">
                            <h4 style="margin-top: 0; color: var(--trading-accent);">Informações do Circuito</h4>
                            <div class="circuit-info-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                                <div class="circuit-info-item">
                                    <div class="item-label">Qubits</div>
                                    <div class="item-value" id="circuit-qubits">8</div>
                                </div>
                                <div class="circuit-info-item">
                                    <div class="item-label">Profundidade</div>
                                    <div class="item-value" id="circuit-depth">0</div>
                                </div>
                                <div class="circuit-info-item">
                                    <div class="item-label">Gates</div>
                                    <div class="item-value" id="circuit-gates">0</div>
                                </div>
                            </div>
                            <div id="circuit-compatibility-status" class="compatibility-status" style="margin-top: 15px; padding: 10px; border-radius: 4px; background-color: rgba(72, 91, 196, 0.1);">
                                <div style="font-weight: bold; margin-bottom: 5px;">Compatibilidade com Hardware:</div>
                                <div id="compatibility-message">Utilize "Atualizar Circuito" para verificar compatibilidade.</div>
                            </div>
                        </div>
                        <div class="metric-explanation" style="margin-top: 20px; padding: 15px; background-color: rgba(72, 91, 196, 0.1); border-radius: 8px;">
                            <h4 style="margin-top: 0; color: var(--trading-accent);">Visualização e Exportação de Circuitos Quânticos</h4>
                            <p style="color: var(--trading-text-secondary); font-size: 14px; line-height: 1.5;">
                                Este painel permite visualizar e exportar os circuitos quânticos gerados pelo sistema QUALIA. Estas funcionalidades são essenciais para auditar as decisões de trading baseadas em computação quântica e validar a conformidade da estratégia com requisitos regulatórios.
                            </p>
                            <ul style="color: var(--trading-text-secondary); font-size: 14px; line-height: 1.5;">
                                <li><strong>Visualização do Circuito:</strong> Permite examinar a estrutura exata do circuito quântico utilizado nas análises. Ajuda a entender como as portas quânticas são aplicadas durante o processamento.</li>
                                <li><strong>Exportação QASM 3.0:</strong> Possibilita exportar o circuito em formato OpenQASM, permitindo sua reprodução em outros ambientes ou sua execução em hardware quântico real.</li>
                                <li><strong>Validação de Compatibilidade:</strong> Verifica automaticamente se o circuito pode ser executado em dispositivos quânticos reais atuais, identificando limitações específicas.</li>
                            </ul>
                            <div style="font-style: italic; font-size: 13px; color: var(--trading-text-secondary); margin-top: 10px;">
                                Nota: Os circuitos são gerados diretamente do processador quântico QUALIA, representando exatamente as operações utilizadas para análise de mercado e tomada de decisão.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel">
                <div class="panel-header">Posições Abertas</div>
                <div class="panel-content">
                    <div class="table-responsive">
                        <table class="trading-table" id="positions-table">
                            <thead>
                                <tr>
                                    <th>Par</th>
                                    <th>Lado</th>
                                    <th>Quantidade</th>
                                    <th>Preço Entrada</th>
                                    <th>Preço Atual</th>
                                    <th>P&L</th>
                                    <th>Duração</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="empty-row">
                                    <td colspan="8">Sem posições abertas</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="panel">
                <div class="panel-header">Enviar Ordem Manual</div>
                <div class="panel-content">
                    <form id="manual-order-form" class="order-form" method="post">
                        {{ csrf_token() }}
                        <div class="form-group">
                            <label for="manual-symbol">Par</label>
                            <select id="manual-symbol" class="form-control">
                                <option value="BTC/USD">BTC/USD</option>
                                <option value="ETH/USD">ETH/USD</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="manual-side">Lado</label>
                            <select id="manual-side" class="form-control">
                                <option value="buy">Compra</option>
                                <option value="sell">Venda</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="manual-type">Tipo</label>
                            <select id="manual-type" class="form-control">
                                <option value="market">Market</option>
                                <option value="limit">Limit</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="manual-quantity">Quantidade</label>
                            <input type="number" id="manual-quantity" class="form-control" placeholder="Quantidade">
                        </div>
                        <div class="form-group" id="price-container">
                            <label for="manual-price">Preço ($)</label>
                            <input type="number" id="manual-price" class="form-control" placeholder="Preço">
                        </div>
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button id="manual-order-btn" class="quantum-button">Enviar Ordem</button>
                        </div>
                    </form>
                </div>
            </div>
            <div class="panel">
                <div class="panel-header">Histórico de Trades</div>
                <div class="panel-content">
                    <div class="table-responsive">
                        <table class="trading-table" id="history-table">
                            <thead>
                                <tr>
                                    <th>Data/Hora</th>
                                    <th>Par</th>
                                    <th>Lado</th>
                                    <th>Quantidade</th>
                                    <th>Preço</th>
                                    <th>P&L Realizado</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="empty-row">
                                    <td colspan="7">Histórico de operações vazio</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    {% block footer %}
    <footer class="trading-footer">
        <div class="footer-status">
            <div>QUALIA Trading System © 2025</div>
            <div id="footer-mode-label">Modo: Simulação</div>
        </div>
        <div class="footer-info">
            Powered by Quantum Computing & Kraken API
        </div>
    </footer>
    {% endblock %}
</div>

<div id="notifications-container"></div>
<div id="modal-overlay" class="modal-overlay">
    <div class="modal">
        <div class="modal-header">
            <div class="modal-title" id="modal-title">Título do Modal</div>
            <button id="modal-close" class="modal-close">&times;</button>
        </div>
        <div class="modal-body" id="modal-content">
            Conteúdo do modal...
        </div>
        <div class="modal-footer">
            <button id="modal-cancel" class="quantum-button">Cancelar</button>
            <button id="modal-confirm" class="quantum-button">Confirmar</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
    <script src="{{ url_for('static', filename='js/trading-interface.js') }}"></script>
{% endblock %}
