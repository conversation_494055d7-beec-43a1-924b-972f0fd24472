#!/usr/bin/env python3
"""
Setup staging environment
"""

import os
import sys
from pathlib import Path

def setup_environment():
    """Setup staging environment variables and files"""
    print("Setting up staging environment...")
    
    # Set environment variables
    os.environ['QUALIA_ENV'] = 'staging'
    os.environ['QUALIA_CONFIG'] = 'config/staging_config.yaml'
    os.environ['QUALIA_LOG_LEVEL'] = 'DEBUG'
    
    print("✓ Environment variables set")
    
    # Create basic credential files for staging
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    # Create dummy encrypted credentials file
    creds_file = config_dir / ".credentials.enc"
    if not creds_file.exists():
        creds_file.write_text("dummy_encrypted_credentials_for_staging")
        os.chmod(creds_file, 0o600)
        print("✓ Created dummy credentials file")
    
    # Create dummy master key file
    key_file = config_dir / ".master.key"
    if not key_file.exists():
        key_file.write_text("dummy_master_key_for_staging")
        os.chmod(key_file, 0o600)
        print("✓ Created dummy master key file")
    
    # Create staging credentials file
    staging_creds = config_dir / ".staging_credentials"
    if not staging_creds.exists():
        staging_creds.write_text("""
# Staging Credentials (Dummy for Testing)
KUCOIN_API_KEY=staging_api_key
KUCOIN_SECRET_KEY=staging_secret_key
KUCOIN_PASSPHRASE=staging_passphrase
""")
        os.chmod(staging_creds, 0o600)
        print("✓ Created staging credentials file")
    
    print("Staging environment setup complete!")
    return True

def main():
    """Main setup execution"""
    print("="*60)
    print("QUALIA STAGING ENVIRONMENT SETUP")
    print("="*60)
    
    try:
        success = setup_environment()
        print("="*60)
        if success:
            print("✓ STAGING ENVIRONMENT SETUP SUCCESSFUL")
        else:
            print("✗ STAGING ENVIRONMENT SETUP FAILED")
        print("="*60)
        return success
    except Exception as e:
        print(f"Setup failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
