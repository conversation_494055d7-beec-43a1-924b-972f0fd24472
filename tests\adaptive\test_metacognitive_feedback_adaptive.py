import logging
from unittest.mock import MagicMock

from qualia.adaptive_evolution import AdaptiveConsciousnessEvolution


def test_inconclusive_feedback_reduces_partially():
    ace = AdaptiveConsciousnessEvolution(qualia_universe=MagicMock())
    ace.adaptation_aggressiveness = 1.2
    ace.receive_metacognitive_feedback("sem_historico_relevante", 0.5, {})
    assert ace.adaptation_aggressiveness < 1.2 and ace.adaptation_aggressiveness > 1.0


def test_inconclusive_feedback_logs_memory(caplog):
    ace = AdaptiveConsciousnessEvolution(qualia_universe=MagicMock())
    caplog.set_level(logging.INFO)
    ace.receive_metacognitive_feedback(
        "sem_historico_relevante",
        0.5,
        {"memory_empty": True},
    )
    assert any("memória QPM vazia" in rec.getMessage() for rec in caplog.records)


def test_inconclusive_feedback_logs_threshold(caplog):
    ace = AdaptiveConsciousnessEvolution(qualia_universe=MagicMock())
    caplog.set_level(logging.INFO)
    ace.receive_metacognitive_feedback(
        "sem_historico_relevante",
        0.5,
        {"similarity_threshold_used": 0.95},
    )
    assert any("limiar de similaridade" in rec.getMessage() for rec in caplog.records)
