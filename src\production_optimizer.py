#!/usr/bin/env python3
"""
QUALIA Production Optimizer - Etapa D.1
YAA IMPLEMENTATION: Sistema de otimização para ambiente de produção.
"""

import sys
import asyncio
import json
import logging
import time
import psutil
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List
import signal

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

from src.bayesian_optimizer import BayesianOptimizer, OptimizationConfig

class ProductionOptimizer:
    """Otimizador para ambiente de produção com recursos avançados."""
    
    def __init__(self, config_file: str = "config/production_config.json"):
        self.config_file = config_file
        self.config = None
        self.optimizer = None
        self.is_running = False
        self.should_stop = False
        self.current_cycle = 0
        self.consecutive_failures = 0
        self.start_time = None
        self.last_save_time = None
        
        # Estatísticas
        self.stats = {
            'total_trials': 0,
            'successful_trials': 0,
            'failed_trials': 0,
            'best_pnl_ever': 0.0,
            'cycles_completed': 0,
            'total_runtime_hours': 0.0,
            'average_cycle_time': 0.0,
            'memory_usage_mb': 0.0,
            'cpu_usage_percent': 0.0,
            'last_update': datetime.now().isoformat()
        }
        
        # Setup logging
        self.setup_logging()
        
        # Carrega configuração
        self.load_config()
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def setup_logging(self):
        """Configura logging para produção."""
        
        # Cria diretórios
        Path("logs").mkdir(exist_ok=True)
        
        # Configuração de logging
        log_format = '%(asctime)s - PROD - %(levelname)s - %(message)s'
        
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler('logs/production_optimizer.log'),
                logging.StreamHandler()
            ]
        )
        
        # Logger específico para métricas
        self.metrics_logger = logging.getLogger('metrics')
        metrics_handler = logging.FileHandler('logs/production_metrics.log')
        metrics_handler.setFormatter(logging.Formatter(log_format))
        self.metrics_logger.addHandler(metrics_handler)
        self.metrics_logger.setLevel(logging.INFO)
    
    def load_config(self) -> Dict[str, Any]:
        """Carrega configuração de produção."""
        
        try:
            with open(self.config_file, 'r') as f:
                config_data = json.load(f)
            
            # Cria OptimizationConfig
            self.config = OptimizationConfig(
                study_name=config_data['study_name'],
                n_trials_per_cycle=config_data['n_trials_per_cycle'],
                optimization_cycles=config_data['optimization_cycles'],
                symbols=config_data['symbols'],
                base_params=config_data['base_params']
            )
            
            # Adiciona configurações específicas de produção
            self.config.worker_settings = config_data.get('worker_settings', {})
            self.config.symbol_weights = self.extract_symbol_weights(config_data)
            self.config.risk_management = config_data.get('risk_management', {})
            self.config.monitoring = config_data.get('monitoring', {})
            
            logging.info(f"Configuração carregada: {len(self.config.symbols)} símbolos, {self.config.n_trials_per_cycle} trials/ciclo")
            
            return config_data
            
        except Exception as e:
            logging.error(f"Erro ao carregar configuração: {e}")
            raise
    
    def extract_symbol_weights(self, config_data: Dict) -> Dict[str, float]:
        """Extrai pesos dos símbolos da configuração."""
        
        weights = {}
        symbol_settings = config_data.get('symbol_specific_settings', {})
        
        for symbol in self.config.symbols:
            weights[symbol] = symbol_settings.get(symbol, {}).get('weight', 1.0)
        
        return weights
    
    def signal_handler(self, signum, frame):
        """Handler para sinais do sistema."""
        
        logging.info(f"Sinal recebido: {signum}. Iniciando shutdown gracioso...")
        self.should_stop = True
    
    def check_system_resources(self) -> Dict[str, float]:
        """Verifica recursos do sistema."""
        
        try:
            # Memória
            memory = psutil.virtual_memory()
            memory_usage_mb = memory.used / (1024 * 1024)
            memory_percent = memory.percent
            
            # CPU
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Atualiza estatísticas
            self.stats['memory_usage_mb'] = memory_usage_mb
            self.stats['cpu_usage_percent'] = cpu_percent
            
            return {
                'memory_usage_mb': memory_usage_mb,
                'memory_percent': memory_percent,
                'cpu_percent': cpu_percent
            }
            
        except Exception as e:
            logging.error(f"Erro ao verificar recursos: {e}")
            return {}
    
    def check_resource_limits(self) -> bool:
        """Verifica se os recursos estão dentro dos limites."""
        
        resources = self.check_system_resources()
        worker_settings = self.config.worker_settings
        
        # Verifica limites
        memory_limit = worker_settings.get('memory_limit_mb', 2048)
        cpu_limit = worker_settings.get('cpu_limit_percent', 80)
        
        if resources.get('memory_usage_mb', 0) > memory_limit:
            logging.warning(f"Uso de memória acima do limite: {resources['memory_usage_mb']:.1f}MB > {memory_limit}MB")
            return False
        
        if resources.get('cpu_percent', 0) > cpu_limit:
            logging.warning(f"Uso de CPU acima do limite: {resources['cpu_percent']:.1f}% > {cpu_limit}%")
            return False
        
        return True
    
    def should_save_results(self) -> bool:
        """Verifica se deve salvar resultados."""
        
        if self.last_save_time is None:
            return True
        
        save_interval = self.config.worker_settings.get('save_interval_cycles', 10)
        cycles_since_save = self.current_cycle - (self.last_save_time or 0)
        
        return cycles_since_save >= save_interval
    
    def run_production_cycle(self) -> Dict[str, Any]:
        """Executa um ciclo de otimização em produção."""
        
        cycle_start = time.time()
        
        try:
            logging.info(f"🚀 Iniciando ciclo de produção {self.current_cycle + 1}")
            
            # Verifica recursos antes do ciclo
            if not self.check_resource_limits():
                logging.warning("Recursos insuficientes, pulando ciclo")
                return {'success': False, 'reason': 'insufficient_resources'}
            
            # Executa ciclo de otimização
            cycle_result = self.optimizer.run_optimization_cycle()
            
            # Atualiza estatísticas
            self.stats['cycles_completed'] += 1
            self.stats['total_trials'] += cycle_result.get('n_trials', 0)
            
            if cycle_result.get('best_pnl_24h', 0) > self.stats['best_pnl_ever']:
                self.stats['best_pnl_ever'] = cycle_result['best_pnl_24h']
                logging.info(f"🏆 Novo melhor PnL: {self.stats['best_pnl_ever']:.4f}%")
            
            # Reset contador de falhas
            self.consecutive_failures = 0
            
            cycle_time = time.time() - cycle_start
            self.stats['average_cycle_time'] = (
                (self.stats['average_cycle_time'] * (self.stats['cycles_completed'] - 1) + cycle_time) 
                / self.stats['cycles_completed']
            )
            
            logging.info(f"✅ Ciclo {self.current_cycle + 1} concluído em {cycle_time:.1f}s")
            
            return {
                'success': True,
                'cycle_time': cycle_time,
                'best_pnl': cycle_result.get('best_pnl_24h', 0),
                'trials': cycle_result.get('n_trials', 0)
            }
            
        except Exception as e:
            self.consecutive_failures += 1
            self.stats['failed_trials'] += 1
            
            logging.error(f"❌ Erro no ciclo {self.current_cycle + 1}: {e}")
            
            return {
                'success': False,
                'error': str(e),
                'consecutive_failures': self.consecutive_failures
            }
    
    def log_metrics(self):
        """Registra métricas de produção."""
        
        try:
            resources = self.check_system_resources()
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'cycle': self.current_cycle,
                'stats': self.stats.copy(),
                'resources': resources,
                'optimizer_status': {
                    'is_running': self.is_running,
                    'consecutive_failures': self.consecutive_failures,
                    'runtime_hours': (time.time() - self.start_time) / 3600 if self.start_time else 0
                }
            }
            
            self.metrics_logger.info(f"METRICS: {json.dumps(metrics)}")
            
        except Exception as e:
            logging.error(f"Erro ao registrar métricas: {e}")
    
    async def start_production_optimization(self):
        """Inicia otimização de produção."""
        
        logging.info("🏭 INICIANDO OTIMIZAÇÃO DE PRODUÇÃO")
        logging.info(f"📊 Configuração: {len(self.config.symbols)} símbolos, {self.config.n_trials_per_cycle} trials/ciclo")
        
        self.is_running = True
        self.start_time = time.time()
        
        # Inicializa otimizador
        self.optimizer = BayesianOptimizer(self.config)
        
        try:
            while not self.should_stop and self.current_cycle < self.config.optimization_cycles:
                
                # Executa ciclo
                cycle_result = self.run_production_cycle()
                
                # Verifica falhas consecutivas
                max_failures = self.config.worker_settings.get('max_consecutive_failures', 5)
                if self.consecutive_failures >= max_failures:
                    logging.error(f"❌ Muitas falhas consecutivas ({self.consecutive_failures}), parando")
                    break
                
                # Salva resultados se necessário
                if self.should_save_results():
                    self.optimizer.save_results()
                    self.last_save_time = self.current_cycle
                    logging.info("💾 Resultados salvos")
                
                # Log de métricas
                self.log_metrics()
                
                self.current_cycle += 1
                
                # Intervalo entre ciclos
                if not self.should_stop:
                    interval_minutes = self.config.worker_settings.get('cycle_interval_minutes', 15)
                    logging.info(f"⏳ Aguardando {interval_minutes} minutos para próximo ciclo...")
                    await asyncio.sleep(interval_minutes * 60)
        
        except Exception as e:
            logging.error(f"❌ Erro crítico na otimização: {e}")
        
        finally:
            self.is_running = False
            
            # Salva resultados finais
            if self.optimizer:
                self.optimizer.save_results()
            
            # Estatísticas finais
            total_time = time.time() - self.start_time
            self.stats['total_runtime_hours'] = total_time / 3600
            self.stats['last_update'] = datetime.now().isoformat()
            
            logging.info("🏁 OTIMIZAÇÃO DE PRODUÇÃO FINALIZADA")
            logging.info(f"📊 Estatísticas finais: {json.dumps(self.stats, indent=2)}")
    
    def get_status(self) -> Dict[str, Any]:
        """Retorna status atual do otimizador."""
        
        return {
            'is_running': self.is_running,
            'current_cycle': self.current_cycle,
            'consecutive_failures': self.consecutive_failures,
            'stats': self.stats.copy(),
            'config': {
                'symbols': self.config.symbols,
                'n_trials_per_cycle': self.config.n_trials_per_cycle,
                'optimization_cycles': self.config.optimization_cycles
            },
            'resources': self.check_system_resources()
        }


async def main():
    """Função principal do otimizador de produção."""
    
    print("🏭 QUALIA PRODUCTION OPTIMIZER - ETAPA D.1")
    print("=" * 60)
    
    try:
        optimizer = ProductionOptimizer()
        await optimizer.start_production_optimization()
        
    except KeyboardInterrupt:
        print("\n🛑 Interrompido pelo usuário")
    except Exception as e:
        print(f"❌ Erro: {e}")
        logging.error(f"Erro crítico: {e}")


if __name__ == "__main__":
    asyncio.run(main())
