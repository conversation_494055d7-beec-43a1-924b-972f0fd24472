#!/usr/bin/env python3
"""
Grid Search QUALIA Core - Sistema Real com Componentes Essenciais
YAA IMPLEMENTATION: Teste com componentes core do QUALIA e dados reais.
"""

import asyncio
import json
import sys
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import time
from typing import Dict, List, Any
import os

import pandas as pd
import numpy as np
import requests

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

# Imports essenciais do QUALIA
try:
    from src.qualia.config.hyperparams_loader import HyperParamsLoader
    from src.qualia.consciousness.amplification_calibrator import AmplificationCalibrator
    from src.qualia.utils.logger import get_logger
    from src.qualia.backtest.hyperparams_grid_search import HyperParamsGridSearch
except ImportError as e:
    print(f"❌ Erro de import: {e}")
    print("🔄 Tentando imports alternativos...")
    
    # Fallback para estrutura alternativa
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))
    
    try:
        from qualia.config.hyperparams_loader import HyperParamsLoader
        from qualia.consciousness.amplification_calibrator import AmplificationCalibrator
        from qualia.utils.logger import get_logger
        from qualia.backtest.hyperparams_grid_search import HyperParamsGridSearch
    except ImportError as e2:
        print(f"❌ Erro de import alternativo: {e2}")
        print("🚨 Usando implementação standalone...")
        
        # Implementação standalone mínima
        class MockHyperParamsLoader:
            def load(self):
                from types import SimpleNamespace
                return SimpleNamespace(
                    price_amplification=5.0,
                    news_amplification=4.0,
                    min_confidence=0.6,
                    tsvf_window=50,
                    tsvf_vector_size=64,
                    tsvf_alpha=0.8,
                    tsvf_gamma=0.6,
                    tsvf_cE=1.0,
                    tsvf_cH=0.5
                )
        
        class MockAmplificationCalibrator:
            def __init__(self, hyperparams=None):
                self.hyperparams = hyperparams
                self.price_amplification = hyperparams.price_amplification if hyperparams else 5.0
                self.news_amplification = hyperparams.news_amplification if hyperparams else 4.0
            
            def get_calibrated_amplification(self, signal_type):
                if signal_type == "price":
                    return self.price_amplification
                elif signal_type == "news":
                    return self.news_amplification
                else:
                    return (self.price_amplification + self.news_amplification) / 2
        
        def get_logger(name):
            import logging
            return logging.getLogger(name)
        
        HyperParamsLoader = MockHyperParamsLoader
        AmplificationCalibrator = MockAmplificationCalibrator

logger = get_logger(__name__)


class QualiaRealDataFetcher:
    """Fetcher de dados reais otimizado para QUALIA."""
    
    BASE_URL = "https://api.binance.com/api/v3"
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'QUALIA-CoreGridSearch/1.0'
        })
    
    def fetch_historical_data(self, symbol: str, interval: str = "1h", days: int = 90) -> pd.DataFrame:
        """Busca dados históricos formatados para QUALIA."""
        try:
            # Calcula timestamps
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            logger.info(f"🔄 Buscando dados reais para {symbol} ({days} dias)")
            
            # Parâmetros da API
            params = {
                'symbol': symbol.replace('/', ''),
                'interval': interval,
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            # Faz requisição
            response = self.session.get(f"{self.BASE_URL}/klines", params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                logger.warning(f"⚠️ Nenhum dado retornado para {symbol}")
                return pd.DataFrame()
            
            # Converte para DataFrame no formato QUALIA
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Processa dados para formato QUALIA
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']].copy()
            
            # Converte para float
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index()
            
            # Remove dados inválidos
            df = df.dropna()
            
            # Adiciona indicadores básicos para simulação QUALIA
            df['returns'] = df['close'].pct_change().fillna(0)
            df['sma_20'] = df['close'].rolling(20).mean()
            df['volatility'] = df['returns'].rolling(20).std()
            
            logger.info(f"✅ Dados obtidos para {symbol}: {len(df)} candles de {df.index[0]} a {df.index[-1]}")
            return df
            
        except Exception as e:
            logger.error(f"❌ Erro ao buscar dados para {symbol}: {e}")
            return pd.DataFrame()


def run_qualia_core_backtest(
    df: pd.DataFrame, 
    symbol: str,
    price_amp: float, 
    news_amp: float, 
    min_conf: float
) -> Dict[str, Any]:
    """Executa backtest usando componentes core do QUALIA."""
    
    if df.empty or len(df) < 100:
        return {
            'total_return_pct': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown_pct': 0.0,
            'win_rate': 0.0,
            'total_trades': 0,
            'volatility': 0.0,
            'error': 'Dados insuficientes'
        }
    
    try:
        # Carrega hiperparâmetros QUALIA
        hyperparams_loader = HyperParamsLoader()
        hyperparams = hyperparams_loader.load()
        
        # Atualiza parâmetros de amplificação
        hyperparams.price_amplification = price_amp
        hyperparams.news_amplification = news_amp
        hyperparams.min_confidence = min_conf
        
        # Cria calibrador de amplificação
        calibrator = AmplificationCalibrator(hyperparams=hyperparams)
        
        # Simula estratégia QUALIA core
        positions = []
        returns = []
        
        # Janela mínima para análise
        min_window = 50
        
        for i in range(min_window, len(df)):
            try:
                # Dados da janela atual
                window_data = df.iloc[max(0, i-min_window):i+1].copy()
                
                if len(window_data) < min_window:
                    positions.append(0.0)
                    returns.append(0.0)
                    continue
                
                # Simula análise QUALIA core
                current_price = window_data['close'].iloc[-1]
                sma_20 = window_data['sma_20'].iloc[-1]
                volatility = window_data['volatility'].iloc[-1]
                
                # Sinal de momentum (simulado)
                momentum_signal = 0.0
                if not pd.isna(sma_20):
                    momentum_signal = (current_price - sma_20) / sma_20
                
                # Sinal de volatilidade (simulado)
                vol_signal = 0.0
                if not pd.isna(volatility) and volatility > 0:
                    vol_signal = 1.0 / (1.0 + volatility * 100)  # Inverso da volatilidade
                
                # Confiança simulada baseada em volume e volatilidade
                volume_ratio = window_data['volume'].iloc[-1] / window_data['volume'].mean()
                confidence = np.clip(0.5 + 0.2 * vol_signal + 0.1 * np.log(volume_ratio), 0.2, 0.9)
                
                # Aplica filtro de confiança mínima
                if confidence < min_conf:
                    position = 0.0
                else:
                    # Combina sinais com amplificação QUALIA
                    price_weight = calibrator.get_calibrated_amplification("price") / 10.0
                    news_weight = calibrator.get_calibrated_amplification("news") / 10.0
                    
                    combined_signal = (
                        price_weight * momentum_signal + 
                        news_weight * vol_signal
                    )
                    
                    position = np.clip(combined_signal, -1.0, 1.0)
                
                positions.append(position)
                
                # Calcula retorno
                if i > 0:
                    market_return = (df['close'].iloc[i] / df['close'].iloc[i-1]) - 1
                    strategy_return = positions[-2] * market_return if len(positions) > 1 else 0.0
                    
                    # Aplica custos de transação
                    if len(positions) > 1:
                        position_change = abs(positions[-1] - positions[-2])
                        transaction_cost = position_change * 0.001  # 0.1%
                        strategy_return -= transaction_cost
                    
                    returns.append(strategy_return)
                else:
                    returns.append(0.0)
                    
            except Exception as e:
                logger.debug(f"Erro no ponto {i}: {e}")
                positions.append(0.0)
                returns.append(0.0)
        
        # Calcula métricas de performance
        if not returns or len(returns) == 0:
            return {
                'total_return_pct': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown_pct': 0.0,
                'win_rate': 0.0,
                'total_trades': 0,
                'volatility': 0.0,
                'error': 'Sem retornos calculados'
            }
        
        returns_series = pd.Series(returns)
        cumulative_returns = (1 + returns_series).cumprod()
        
        # Métricas
        total_return = cumulative_returns.iloc[-1] - 1 if len(cumulative_returns) > 0 else 0.0
        
        # Anualizadas (8760 horas por ano)
        periods_per_year = 8760
        mean_return = returns_series.mean() * periods_per_year
        volatility = returns_series.std() * np.sqrt(periods_per_year)
        sharpe_ratio = mean_return / volatility if volatility > 0 else 0.0
        
        # Max drawdown
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = abs(drawdown.min()) if len(drawdown) > 0 else 0.0
        
        # Trades
        position_changes = pd.Series(positions).diff().abs()
        total_trades = int(position_changes.sum())
        
        # Win rate
        winning_periods = (returns_series > 0).sum()
        win_rate = winning_periods / len(returns_series) if len(returns_series) > 0 else 0.0
        
        return {
            'total_return_pct': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'volatility': volatility,
            'periods_analyzed': len(returns),
            'avg_position': np.mean(np.abs(positions)) if positions else 0.0,
            'avg_confidence': confidence if 'confidence' in locals() else 0.5
        }
        
    except Exception as e:
        logger.error(f"❌ Erro no backtest QUALIA Core: {e}")
        return {
            'total_return_pct': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown_pct': 0.0,
            'win_rate': 0.0,
            'total_trades': 0,
            'volatility': 0.0,
            'error': str(e)
        }


def run_qualia_core_grid_search():
    """Executa grid search com componentes core do QUALIA e dados reais."""
    print("🚀 QUALIA CORE GRID SEARCH - COMPONENTES REAIS")
    print("=" * 60)
    
    # Inicializa fetcher
    fetcher = QualiaRealDataFetcher()
    
    # Configuração do grid search
    symbols = ["BTC/USDT", "ETH/USDT"]
    
    # Grid de parâmetros QUALIA
    price_amps = [2.0, 5.0, 8.0]      # Amplificação de preço
    news_amps = [1.5, 4.0, 7.0]       # Amplificação de notícias  
    min_confs = [0.4, 0.6, 0.8]       # Confiança mínima
    
    total_combinations = len(price_amps) * len(news_amps) * len(min_confs) * len(symbols)
    print(f"📊 Total de combinações: {total_combinations}")
    print(f"🧠 Usando componentes QUALIA: AmplificationCalibrator + HyperParamsLoader")
    
    results = []
    
    for symbol in symbols:
        print(f"\n📈 Processando {symbol} com QUALIA Core...")
        
        # Busca dados reais
        df = fetcher.fetch_historical_data(symbol, days=120)
        
        if df.empty:
            logger.error(f"❌ Falha ao obter dados para {symbol}")
            continue
        
        for price_amp in price_amps:
            for news_amp in news_amps:
                for min_conf in min_confs:
                    logger.info(f"🔄 Testando ({price_amp:.1f}, {news_amp:.1f}, {min_conf:.1f})")
                    
                    result = run_qualia_core_backtest(df, symbol, price_amp, news_amp, min_conf)
                    result.update({
                        'symbol': symbol,
                        'price_amplification': price_amp,
                        'news_amplification': news_amp,
                        'min_confidence': min_conf,
                        'data_points': len(df),
                        'strategy': 'QUALIA_Core'
                    })
                    results.append(result)
                    
                    if 'error' not in result:
                        print(f"   ✅ Return {result['total_return_pct']:.2%}, "
                              f"Sharpe {result['sharpe_ratio']:.3f}, "
                              f"Trades {result['total_trades']}")
                    else:
                        print(f"   ❌ Erro: {result['error']}")
    
    # Análise dos resultados
    if results:
        print(f"\n" + "="*60)
        print(f"📊 RESULTADOS QUALIA CORE GRID SEARCH")
        print(f"="*60)
        
        # Filtra resultados válidos
        valid_results = [r for r in results if 'error' not in r]
        
        if valid_results:
            df_results = pd.DataFrame(valid_results)
            
            print(f"\n📈 Estatísticas (QUALIA Core + Dados Reais):")
            print(f"   • Testes válidos: {len(valid_results)}/{len(results)}")
            print(f"   • Sharpe médio: {df_results['sharpe_ratio'].mean():.3f}")
            print(f"   • Return médio: {df_results['total_return_pct'].mean():.2%}")
            print(f"   • Drawdown médio: {df_results['max_drawdown_pct'].mean():.2%}")
            print(f"   • Win Rate médio: {df_results['win_rate'].mean():.2%}")
            print(f"   • Trades médios: {df_results['total_trades'].mean():.0f}")
            
            # Top 3 configurações
            top_configs = df_results.nlargest(3, 'sharpe_ratio')
            print(f"\n🏆 TOP 3 CONFIGURAÇÕES QUALIA CORE:")
            for i, (_, row) in enumerate(top_configs.iterrows(), 1):
                print(f"   {i}. {row['symbol']}: ({row['price_amplification']:.1f}, "
                      f"{row['news_amplification']:.1f}, {row['min_confidence']:.1f})")
                print(f"      Return: {row['total_return_pct']:.2%}, "
                      f"Sharpe: {row['sharpe_ratio']:.3f}, "
                      f"Drawdown: {row['max_drawdown_pct']:.2%}, "
                      f"Trades: {row['total_trades']}")
        
        # Salva resultados
        output_dir = Path("results/qualia_core_grid_search")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = int(time.time())
        output_file = output_dir / f"qualia_core_results_{timestamp}.json"
        
        # Salva como JSON
        with open(output_file, 'w') as f:
            json.dump({
                'metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'total_combinations': len(results),
                    'valid_results': len(valid_results) if valid_results else 0,
                    'symbols': symbols,
                    'strategy': 'QUALIA_Core',
                    'data_source': 'Binance API',
                    'system': 'QUALIA Core Components'
                },
                'results': results
            }, f, indent=2)
        
        print(f"\n💾 Resultados salvos em: {output_file}")
        
        # Salva CSV
        if valid_results:
            csv_file = output_dir / f"qualia_core_results_{timestamp}.csv"
            df_results.to_csv(csv_file, index=False)
            print(f"📊 CSV salvo em: {csv_file}")
        
        print(f"\n✅ QUALIA Core Grid Search concluído!")
        
    else:
        print("❌ Nenhum resultado obtido")


if __name__ == "__main__":
    run_qualia_core_grid_search()
