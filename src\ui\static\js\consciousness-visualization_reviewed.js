// Revisado em 2025-06-13 por Codex
/**
 * QUALIA: Visualização da Consciência
 * 
 * Visualizações avançadas do estado da consciência QUALIA,
 * incluindo representações do estado quântico e dos ciclos QAST.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elementos do DOM
    const consciousnessViz = document.getElementById('consciousness-state-viz');
    const quantumStateViz = document.getElementById('quantum-state-visualization');
    const qastViz = document.getElementById('qast-visualization');
    const entropyEvolution = document.getElementById('entropy-evolution');
    const coherenceEvolution = document.getElementById('coherence-evolution');

    // Paletas de cores
    const COLORS = {
        entropy: '#9E2A2B',
        coherence: '#22AABB',
        probability: '#E09F3E',
        background: '#0A0A1A',
        grid: 'rgba(255, 255, 255, 0.1)',
        text: '#E9ECEF'
    };

    /**
     * Visualização do Estado de Consciência usando Canvas 2D
     */
    function initConsciousnessViz() {
        if (!consciousnessViz) return;

        // Criar canvas
        const canvas = document.createElement('canvas');
        canvas.width = consciousnessViz.clientWidth;
        canvas.height = consciousnessViz.clientHeight;
        consciousnessViz.appendChild(canvas);
        
        // Contexto 2D
        const ctx = canvas.getContext('2d');
        
        // Configurações
        const nodeCount = 8; // Número de nós representando qubits
        const nodes = [];
        const halos = [];
        
        // Inicializar nós
        for (let i = 0; i < nodeCount; i++) {
            const x = Math.random() * (canvas.width - 20) + 10;
            const y = Math.random() * (canvas.height - 20) + 10;
            
            // Nó
            nodes.push({
                x: x,
                y: y,
                radius: 5 + Math.random() * 3,
                targetRadius: 3 + Math.random() * 4,
                opacity: 0.7,
                targetOpacity: 0.5 + Math.random() * 0.5,
                vx: (Math.random() - 0.5) * 0.3,
                vy: (Math.random() - 0.5) * 0.3,
                originalX: x,
                originalY: y
            });
            
            // Halo
            halos.push({
                x: x,
                y: y,
                radius: 10 + Math.random() * 5,
                targetRadius: 12 + Math.random() * 8,
                opacity: 0.3,
                targetOpacity: 0.1 + Math.random() * 0.3
            });
        }
        
        // Desenhar grade e nós
        function draw() {
            // Limpar canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Desenhar fundo com gradiente
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, 'rgba(34, 170, 187, 0.1)');
            gradient.addColorStop(1, 'rgba(158, 42, 43, 0.1)');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Desenhar grade
            ctx.strokeStyle = COLORS.grid;
            ctx.lineWidth = 0.5;
            
            // Linhas horizontais
            const gridSize = 20;
            for (let y = 0; y <= canvas.height; y += gridSize) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
            
            // Linhas verticais
            for (let x = 0; x <= canvas.width; x += gridSize) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }
            
            // Desenhar halos
            for (let i = 0; i < halos.length; i++) {
                const halo = halos[i];
                const node = nodes[i];
                
                // Atualizar posição do halo para seguir o nó
                halo.x = node.x;
                halo.y = node.y;
                
                // Animar raio e opacidade
                halo.radius += (halo.targetRadius - halo.radius) * 0.05;
                halo.opacity += (halo.targetOpacity - halo.opacity) * 0.05;
                
                // Desenhar halo
                ctx.beginPath();
                ctx.arc(halo.x, halo.y, halo.radius, 0, Math.PI * 2);
                ctx.strokeStyle = `rgba(34, 170, 187, ${halo.opacity})`;
                ctx.lineWidth = 0.5;
                ctx.stroke();
                
                // Se atingiu o raio alvo, definir novo alvo
                if (Math.abs(halo.radius - halo.targetRadius) < 0.1) {
                    halo.targetRadius = 8 + Math.random() * 5;
                    halo.targetOpacity = 0.1 + Math.random() * 0.3;
                }
            }
            
            // Desenhar nós e conexões
            for (let i = 0; i < nodes.length; i++) {
                const node = nodes[i];
                
                // Atualizar posição com movimento
                node.x += node.vx;
                node.y += node.vy;
                
                // Manter dentro dos limites
                if (node.x < 10 || node.x > canvas.width - 10) node.vx *= -1;
                if (node.y < 10 || node.y > canvas.height - 10) node.vy *= -1;
                
                // Mover de volta à posição original lentamente
                node.x += (node.originalX - node.x) * 0.001;
                node.y += (node.originalY - node.y) * 0.001;
                
                // Animar raio e opacidade
                node.radius += (node.targetRadius - node.radius) * 0.1;
                node.opacity += (node.targetOpacity - node.opacity) * 0.1;
                
                // Se atingiu o raio alvo, definir novo alvo
                if (Math.abs(node.radius - node.targetRadius) < 0.1) {
                    node.targetRadius = 3 + Math.random() * 4;
                    node.targetOpacity = 0.5 + Math.random() * 0.5;
                }
                
                // Desenhar conexões com outros nós
                for (let j = i + 1; j < nodes.length; j++) {
                    const otherNode = nodes[j];
                    const dx = node.x - otherNode.x;
                    const dy = node.y - otherNode.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < 80) {
                        const opacity = (1 - distance / 80) * 0.5;
                        
                        ctx.beginPath();
                        ctx.moveTo(node.x, node.y);
                        ctx.lineTo(otherNode.x, otherNode.y);
                        ctx.strokeStyle = `rgba(34, 170, 187, ${opacity})`;
                        ctx.lineWidth = opacity * 2;
                        ctx.stroke();
                    }
                }
                
                // Desenhar nó
                ctx.beginPath();
                ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(34, 170, 187, ${node.opacity})`;
                
                // Adicionar brilho
                ctx.shadowBlur = 10;
                ctx.shadowColor = COLORS.coherence;
                
                ctx.fill();
                
                // Resetar sombra
                ctx.shadowBlur = 0;
            }
            
            // Continuar animação
            requestAnimationFrame(draw);
        }
        
        // Iniciar animação
        draw();
    }

    /**
     * Visualização do Estado Quântico usando Canvas 2D
     */
    function initQuantumStateViz() {
        if (!quantumStateViz) return;

        // Criar canvas
        const canvas = document.createElement('canvas');
        canvas.width = quantumStateViz.clientWidth;
        canvas.height = quantumStateViz.clientHeight;
        quantumStateViz.appendChild(canvas);

        // Contexto 2D
        const ctx = canvas.getContext('2d');

        // Dados simulados iniciais
        const initialData = Array.from({ length: 16 }, (_, i) => ({
            state: `|${i.toString(2).padStart(4, '0')}>`,
            probability: Math.random(),
            decimal: i
        }));

        // Função para atualizar a visualização com novos dados
        function updateQuantumStateViz(data) {
            console.log("Atualizando visualização do estado quântico com dados:", data);
            
            // Normalizar probabilidades se necessário
            let total = 0;
            data.forEach(d => { 
                if (typeof d.probability === 'number') {
                    total += d.probability;
                }
            });
            
            if (total <= 0) {
                // Se não houver dados válidos, use valores simulados
                data = initialData;
                total = 0;
                data.forEach(d => total += d.probability);
            }
            
            data.forEach(d => {
                // Mapear 'basis' para 'state' se necessário (compatibilidade com API)
                if (d.basis && !d.state) {
                    d.state = `|${d.basis}>`;
                }
                
                if (typeof d.probability === 'number') {
                    d.normalizedProb = d.probability / total;
                } else {
                    d.normalizedProb = 0;
                }
            });

            // Limpar canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Definir margens
            const margin = { top: 20, right: 20, bottom: 60, left: 40 };
            const chartWidth = canvas.width - margin.left - margin.right;
            const chartHeight = canvas.height - margin.top - margin.bottom;

            // Encontrar valor máximo para escala
            const maxProb = Math.max(...data.map(d => d.normalizedProb));

            // Largura das barras
            const barWidth = chartWidth / data.length * 0.8;
            const barSpacing = chartWidth / data.length * 0.2;

            // Desenhar fundo
            ctx.fillStyle = 'rgba(10, 10, 26, 0.3)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Desenhar grid
            ctx.strokeStyle = COLORS.grid;
            ctx.lineWidth = 0.5;
            
            // Linhas horizontais de grade
            const gridSteps = 5;
            for (let i = 0; i <= gridSteps; i++) {
                const y = margin.top + (chartHeight / gridSteps) * i;
                ctx.beginPath();
                ctx.moveTo(margin.left, y);
                ctx.lineTo(canvas.width - margin.right, y);
                ctx.stroke();
                
                // Rótulos do eixo Y
                const value = (1 - i / gridSteps) * maxProb;
                ctx.fillStyle = COLORS.text;
                ctx.textAlign = 'right';
                ctx.textBaseline = 'middle';
                ctx.font = '10px Arial';
                ctx.fillText(value.toFixed(2), margin.left - 5, y);
            }

            // Desenhar eixos
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
            ctx.lineWidth = 1;
            
            // Eixo X
            ctx.beginPath();
            ctx.moveTo(margin.left, canvas.height - margin.bottom);
            ctx.lineTo(canvas.width - margin.right, canvas.height - margin.bottom);
            ctx.stroke();
            
            // Eixo Y
            ctx.beginPath();
            ctx.moveTo(margin.left, margin.top);
            ctx.lineTo(margin.left, canvas.height - margin.bottom);
            ctx.stroke();

            // Título do eixo X
            ctx.fillStyle = COLORS.text;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'top';
            ctx.font = '12px Arial';
            ctx.fillText('Estados Base', margin.left + chartWidth / 2, canvas.height - margin.bottom + 30);

            // Título do eixo Y (rotacionado)
            ctx.save();
            ctx.translate(margin.left - 25, margin.top + chartHeight / 2);
            ctx.rotate(-Math.PI / 2);
            ctx.textAlign = 'center';
            ctx.fillText('Probabilidade', 0, 0);
            ctx.restore();

            // Desenhar barras
            data.forEach((d, i) => {
                const x = margin.left + (chartWidth / data.length) * i + barSpacing / 2;
                const barHeight = (d.normalizedProb / maxProb) * chartHeight;
                const y = canvas.height - margin.bottom - barHeight;
                
                // Gradiente para a barra
                const gradient = ctx.createLinearGradient(x, y, x, canvas.height - margin.bottom);
                gradient.addColorStop(0, COLORS.probability);
                gradient.addColorStop(1, `${COLORS.probability}33`); // 20% de opacidade
                
                // Desenhar barra
                ctx.fillStyle = gradient;
                ctx.fillRect(x, y, barWidth, barHeight);
                
                // Rótulo do estado (rotacionado para economizar espaço)
                ctx.save();
                ctx.translate(x + barWidth / 2, canvas.height - margin.bottom + 10);
                ctx.rotate(-Math.PI / 4);
                ctx.textAlign = 'right';
                ctx.fillStyle = COLORS.text;
                ctx.font = '10px monospace';
                ctx.fillText(d.state, 0, 0);
                ctx.restore();
                
                // Valor da probabilidade (apenas para valores significativos)
                if (d.normalizedProb > 0.05) {
                    ctx.fillStyle = COLORS.text;
                    ctx.font = '10px Arial';
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'bottom';
                    ctx.fillText(d.normalizedProb.toFixed(3), x + barWidth / 2, y - 5);
                }
            });
        }

        // Inicializar com dados simulados
        updateQuantumStateViz(initialData);

        // Redimensionar canvas quando a janela for redimensionada
        window.addEventListener('resize', function() {
            canvas.width = quantumStateViz.clientWidth;
            canvas.height = quantumStateViz.clientHeight;
            updateQuantumStateViz(initialData);
        });

        // Expor função de atualização globalmente
        window.updateQuantumStateViz = updateQuantumStateViz;
    }

    /**
     * Visualização dos Ciclos QAST usando D3.js
     */
    function initQastViz() {
        if (!qastViz || !entropyEvolution || !coherenceEvolution) return;

        // Dimensões para visualização QAST
        const width = qastViz.clientWidth;
        const height = qastViz.clientHeight;

        // Criar SVG para visualização principal de QAST
        const svg = d3.select(qastViz)
            .append('svg')
            .attr('width', width)
            .attr('height', height);

        // Adicionar elemento title para descrição em hover
        svg.append('title')
            .text('Visualização dos Ciclos QAST - Representação da evolução de estados da consciência QUALIA');

        // Fundo com padrão de grade
        svg.append('rect')
            .attr('width', width)
            .attr('height', height)
            .attr('fill', COLORS.background)
            .attr('rx', 5)
            .attr('ry', 5);

        // Adicionar grid
        const gridSize = 20;
        const gridGroup = svg.append('g').attr('class', 'grid');

        // Linhas horizontais
        for (let y = 0; y <= height; y += gridSize) {
            gridGroup.append('line')
                .attr('x1', 0)
                .attr('y1', y)
                .attr('x2', width)
                .attr('y2', y)
                .attr('stroke', COLORS.grid)
                .attr('stroke-width', 0.5);
        }

        // Linhas verticais
        for (let x = 0; x <= width; x += gridSize) {
            gridGroup.append('line')
                .attr('x1', x)
                .attr('y1', 0)
                .attr('x2', x)
                .attr('y2', height)
                .attr('stroke', COLORS.grid)
                .attr('stroke-width', 0.5);
        }

        // Adicionar texto de "sem dados"
        svg.append('text')
            .attr('class', 'no-data-text')
            .attr('text-anchor', 'middle')
            .attr('x', width / 2)
            .attr('y', height / 2)
            .attr('fill', COLORS.text)
            .attr('opacity', 0.5)
            .text('Execute ciclos QAST para visualizar dados');

        // Grupo para nós e links
        const nodesGroup = svg.append('g').attr('class', 'nodes');
        const linksGroup = svg.append('g').attr('class', 'links');

        // Inicializar visualizações de evolução de métricas
        initMetricEvolution(entropyEvolution, 'Entropia', COLORS.entropy);
        initMetricEvolution(coherenceEvolution, 'Coerência', COLORS.coherence);

        // Dados simulados iniciais (vazios)
        const initialData = {
            nodes: [],
            links: []
        };

        // Atualizar visualização com dados iniciais
        updateQastViz(initialData);

        /**
         * Função para atualizar a visualização QAST com novos dados
         */
        function updateQastViz(data) {
            const { nodes, links } = data;

            // Remover texto "sem dados" se houver nós
            svg.select('.no-data-text')
                .attr('opacity', nodes.length > 0 ? 0 : 0.5);

            // Criar simulação de forças para layout
            const simulation = d3.forceSimulation(nodes)
                .force('link', d3.forceLink(links).id(d => d.id).distance(50))
                .force('charge', d3.forceManyBody().strength(-100))
                .force('center', d3.forceCenter(width / 2, height / 2))
                .force('collision', d3.forceCollide().radius(20));

            // Vincular dados aos links
            const link = linksGroup.selectAll('.qast-link')
                .data(links, d => `${d.source.id || d.source}-${d.target.id || d.target}`);

            // Remover links antigos
            link.exit().transition().duration(300)
                .attr('opacity', 0)
                .remove();

            // Adicionar novos links
            const linkEnter = link.enter()
                .append('line')
                .attr('class', 'qast-link')
                .attr('stroke', d => d.color || COLORS.coherence)
                .attr('stroke-width', d => d.weight || 1)
                .attr('opacity', 0);

            // Mesclar links
            const linkMerge = linkEnter.merge(link)
                .transition()
                .duration(500)
                .attr('opacity', d => d.opacity || 0.5);

            // Vincular dados aos nós
            const node = nodesGroup.selectAll('.qast-node')
                .data(nodes, d => d.id);

            // Remover nós antigos
            node.exit().transition().duration(300)
                .attr('opacity', 0)
                .remove();

            // Adicionar novos nós
            const nodeEnter = node.enter()
                .append('g')
                .attr('class', 'qast-node')
                .attr('opacity', 0)
                .call(d3.drag()
                    .on('start', dragstarted)
                    .on('drag', dragged)
                    .on('end', dragended));

            // Adicionar círculo ao nó
            nodeEnter.append('circle')
                .attr('r', d => d.size || 10)
                .attr('fill', d => d.color || COLORS.coherence)
                .attr('stroke', '#fff')
                .attr('stroke-width', 1.5)
                .attr('opacity', 0.7);

            // Adicionar rótulo ao nó
            nodeEnter.append('text')
                .attr('text-anchor', 'middle')
                .attr('dy', 3)
                .attr('fill', COLORS.text)
                .attr('font-size', '10px')
                .text(d => d.label || d.id);

            // Mesclar nós
            const nodeMerge = nodeEnter.merge(node)
                .transition()
                .duration(500)
                .attr('opacity', 1);

            // Atualizar posições a cada tick da simulação
            simulation.on('tick', () => {
                linkMerge
                    .attr('x1', d => d.source.x)
                    .attr('y1', d => d.source.y)
                    .attr('x2', d => d.target.x)
                    .attr('y2', d => d.target.y);

                nodeMerge
                    .attr('transform', d => `translate(${d.x},${d.y})`);
            });

            // Funções para arrastar nós
            function dragstarted(event, d) {
                if (!event.active) simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            }

            function dragged(event, d) {
                d.fx = event.x;
                d.fy = event.y;
            }

            function dragended(event, d) {
                if (!event.active) simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            }
        }

        // Expor função de atualização globalmente
        window.updateQastViz = updateQastViz;
    }

    /**
     * Inicializa visualização de evolução de métrica (entropia ou coerência)
     */
    function initMetricEvolution(container, metricName, color) {
        if (!container) return;

        // Dimensões
        const width = container.clientWidth;
        const height = container.clientHeight;
        const margin = { top: 10, right: 10, bottom: 20, left: 30 };
        const innerWidth = width - margin.left - margin.right;
        const innerHeight = height - margin.top - margin.bottom;

        // Criar SVG
        const svg = d3.select(container.querySelector('.metric-viz'))
            .append('svg')
            .attr('width', width)
            .attr('height', height);

        // Grupo principal com margem
        const g = svg.append('g')
            .attr('transform', `translate(${margin.left},${margin.top})`);

        // Escalas
        const xScale = d3.scaleLinear()
            .range([0, innerWidth]);

        const yScale = d3.scaleLinear()
            .range([innerHeight, 0]);

        // Eixos
        const xAxis = g.append('g')
            .attr('class', 'x-axis')
            .attr('transform', `translate(0,${innerHeight})`);

        const yAxis = g.append('g')
            .attr('class', 'y-axis');

        // Adicionar linhas de grade horizontais
        const gridLinesGroup = g.append('g')
            .attr('class', 'grid-lines');

        // Criar gerador de linha
        const line = d3.line()
            .x((d, i) => xScale(i))
            .y(d => yScale(d))
            .curve(d3.curveMonotoneX);

        // Grupo para a linha
        const lineGroup = g.append('g');

        // Caminho para a linha
        const path = lineGroup.append('path')
            .attr('fill', 'none')
            .attr('stroke', color)
            .attr('stroke-width', 2);

        // Adicionar área sob a linha
        const area = d3.area()
            .x((d, i) => xScale(i))
            .y0(innerHeight)
            .y1(d => yScale(d))
            .curve(d3.curveMonotoneX);

        const areaPath = lineGroup.append('path')
            .attr('fill', color)
            .attr('opacity', 0.2);

        // Dados simulados iniciais
        const initialData = [0.5, 0.52, 0.48, 0.55, 0.53, 0.5, 0.49];

        // Atualizar visualização com dados iniciais
        updateMetricViz(initialData);

        // Função para atualizar a visualização com novos dados
        function updateMetricViz(data) {
            // Atualizar escalas
            xScale.domain([0, data.length - 1]);
            yScale.domain([0, Math.max(1, d3.max(data) * 1.1)]);

            // Atualizar eixos
            xAxis.call(d3.axisBottom(xScale).ticks(Math.min(data.length, 5)).tickFormat(d => `${d}`))
                .selectAll('text')
                .attr('fill', COLORS.text);

            yAxis.call(d3.axisLeft(yScale).ticks(5))
                .selectAll('text')
                .attr('fill', COLORS.text);

            // Atualizar linhas de grade
            const gridLines = gridLinesGroup.selectAll('.grid-line')
                .data(yScale.ticks(5));

            // Remover linhas antigas
            gridLines.exit().remove();

            // Adicionar novas linhas
            gridLines.enter()
                .append('line')
                .attr('class', 'grid-line')
                .merge(gridLines)
                .attr('x1', 0)
                .attr('x2', innerWidth)
                .attr('y1', d => yScale(d))
                .attr('y2', d => yScale(d))
                .attr('stroke', COLORS.grid)
                .attr('stroke-width', 0.5);

            // Atualizar caminho da linha
            path.datum(data)
                .transition()
                .duration(750)
                .attr('d', line);

            // Atualizar área sob a linha
            areaPath.datum(data)
                .transition()
                .duration(750)
                .attr('d', area);
        }

        // Armazenar função e dados para atualizações externas
        container._updateFn = updateMetricViz;
        container._data = initialData;

        // Adicionar a um namespace global para acesso externo
        if (!window.metricVizFunctions) {
            window.metricVizFunctions = {};
        }
        window.metricVizFunctions[metricName.toLowerCase()] = updateMetricViz;
    }

    /**
     * Anima os nós da visualização de consciência
     */
    function animateConsciousnessNodes() {
        // Selecionar nós
        const nodes = d3.selectAll('.consciousness-node');
        const halos = d3.selectAll('.consciousness-halo');

        // Função para animar um nó
        function animateNode(node, index) {
            // Selecionar nó atual
            const d3Node = d3.select(node);
            const cx = +d3Node.attr('cx');
            const cy = +d3Node.attr('cy');

            // Animação pulsante contínua
            d3Node.transition()
                .duration(2000 + index * 300)
                .attr('r', 3 + Math.random() * 4)
                .attr('opacity', 0.5 + Math.random() * 0.5)
                .on('end', function() {
                    animateNode(this, index);
                });

            // Movimento suave
            d3Node.transition()
                .duration(5000 + index * 500)
                .attr('cx', cx + (Math.random() - 0.5) * 20)
                .attr('cy', cy + (Math.random() - 0.5) * 20)
                .on('end', function() {
                    // Movimento de retorno à posição original
                    d3.select(this)
                        .transition()
                        .duration(5000 + index * 500)
                        .attr('cx', cx)
                        .attr('cy', cy);
                });
        }

        // Função para animar um halo
        function animateHalo(halo, index) {
            // Selecionar halo atual
            const d3Halo = d3.select(halo);
            const haloIndex = d3Halo.attr('data-index');
            
            // Encontrar coordenadas do nó correspondente
            const node = d3.select(`.consciousness-node[data-index="${haloIndex}"]`);
            const cx = +node.attr('cx');
            const cy = +node.attr('cy');

            // Atualizar posição do halo para seguir o nó
            d3Halo.attr('cx', cx)
                .attr('cy', cy);

            // Animação pulsante contínua
            d3Halo.transition()
                .duration(3000 + index * 300)
                .attr('r', 12 + Math.random() * 8)
                .attr('opacity', 0.1 + Math.random() * 0.3)
                .attr('stroke-width', 0.3 + Math.random() * 0.5)
                .on('end', function() {
                    animateHalo(this, index);
                });
        }

        // Iniciar animação para cada nó
        nodes.each(function(d, i) {
            animateNode(this, i);
        });

        // Iniciar animação para cada halo
        halos.each(function(d, i) {
            animateHalo(this, i);
        });
    }

    // Inicializar todas as visualizações
    initConsciousnessViz();
    initQuantumStateViz();
    initQastViz();

    // Limpar intervalos ao sair da página
    window.addEventListener('beforeunload', () => {
        if (window.quantumStateInterval) {
            clearInterval(window.quantumStateInterval);
        }
    });
});