# Auditoria da Lógica de Geração de Sinais

Este documento analisa o código presente em `src/qualia/strategies` e módulos relacionados, avaliando a coerência da lógica de geração de sinais e potenciais riscos técnicos.

## Principais Problemas Identificados

1. **Arquitetura Inconsistente**
   - Diversas estratégias implementam `generate_signal(s)` com estruturas de retorno heterogêneas. Algumas retornam tuplas `(str, float)`, outras retornam `DataFrame`. Esta falta de padronização complica a integração com os motores de trading e dificulta testes automatizados.
2. **Métodos Extensos e Pouco Modulares**
   - Arquivos como `scalping_strategies.py` e `enhanced_quantum_momentum/core.py` possuem funções com centenas de linhas, concentrando cálculo de indicadores, avaliação de métricas quânticas e controle de risco em um único bloco. Isso reduz legibilidade e aumenta o risco de bugs.
3. **Redundância de Cálculos**
   - Em `EnhancedQuantumMomentumStrategy`, EMAs e MACD são recalculados mesmo quando valores já foram obtidos em `common_indicators`. A ausência de uma função específica para calcular o MACD a partir de EMAs leva a processamento desnecessário.
   - A partir da versão atual, os cálculos de EMAs, RSI e ATR foram consolidados
     no módulo `qualia.indicators`, e as estratégias reutilizam essas funções.
4. **Dependências Pesadas Sem Tratamento Adequado**
   - `QuantumTrendReversalStrategy` depende de `qiskit`. Embora exista fallback, a importação é feita no topo do módulo e pode causar falha de import em ambientes sem a biblioteca.
5. **Testes Insuficientes**
   - Há cobertura de testes para algumas funções de geração de sinal, porém não existem cenários abrangendo casos de erro, tamanhos mínimos de dados ou inconsistências em métricas quânticas.
6. **Backtest Ineficiente**
   - O backtest de `scalping/backtesting.py` executa `generate_signal` dentro de um laço para cada candle, recalculando indicadores a cada iteração. Essa abordagem pode ser otimizada com vetorização ou uso de janelas incrementais.
7. **Risco de Divisão por Zero e Falta de Verificações**
   - Vários cálculos assumem que indicadores possuem valores prévios suficientes. Em séries curtas, `rolling` ou `ewm` podem produzir `NaN`, e não há verificação uniforme antes da geração do sinal.

## Recomendações e Tasks

1. **Padronizar Interface de `generate_signals`**
   - Criar uma especificação única para o retorno das estratégias (por exemplo, sempre `DataFrame` com colunas `signal` e `confidence`).
   - Atualizar todas as implementações para seguir essa interface.
2. **Refatorar Arquivos Extensos**
   - Extrair funções auxiliares de cálculo e decisão em módulos específicos (`indicators.py`, `signal_logic.py`, `risk.py`).
   - Dividir `scalping_strategies.py` em submódulos semelhantes ao modelo usado em `scalping/`.
3. **Evitar Recalculo de Indicadores**
   - Implementar função `calculate_macd_from_emas` para reutilizar EMAs já computados no `EnhancedQuantumMomentumStrategy`.
   - Inserir cache ou passagem explícita de indicadores pré-calculados nas estratégias que compartilham dados.
4. **Gerenciar Dependências Opcionais**
   - Mover importações de bibliotecas pesadas (ex.: `qiskit`) para dentro das funções que as utilizam e documentar comportamento degradado.
5. **Ampliar Cobertura de Testes**
   - Criar testes unitários que verifiquem limites (`len(df) < window`), retorno de confiança entre 0 e 1 e tratamento de métricas quânticas vazias.
   - Adicionar testes de integração cobrindo `CompositeStrategy` e interação entre subestratégias.
6. **Otimizar Backtest**
   - Refatorar `backtesting.backtest` utilizando vetorização: pré-calcular todos os sinais em uma única passagem e aplicar lógica de posições de forma acumulada.
7. **Validações de Entrada**
   - Garantir que todas as estratégias verifiquem tamanho mínimo de dados antes de calcular indicadores, retornando `hold` quando apropriado.

Cada item acima deve ser registrado como tarefa no rastreador de issues do projeto para acompanhamento.
