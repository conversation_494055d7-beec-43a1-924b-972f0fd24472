#!/usr/bin/env python3
"""
YAA (YET ANOTHER AGENT) - Correção da Coleta de Dados QUALIA

Este script corrige o problema de dados insuficientes que impede a geração de sinais,
forçando a coleta de dados históricos adequados para análise técnica.
"""

import asyncio
import sys
import os
import json
import logging
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, Any, List
import pandas as pd

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

# Carregar variáveis de ambiente do .env
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # Fallback manual se python-dotenv não estiver disponível
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file) as f:
            for line in f:
                if line.strip() and not line.startswith("#"):
                    key, value = line.strip().split("=", 1)
                    os.environ[key] = value

from qualia.exchanges.kucoin_client import KuCoinClient
from qualia.consciousness.enhanced_data_collector import EnhancedDataCollector
from qualia.utils.logger import setup_logging
from qualia.config.config_loader import load_env_and_json
from qualia.market.base_integration import MarketSpec
import yaml

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class QUALIADataFixer:
    """Corretor de dados para o sistema QUALIA."""
    
    def __init__(self, config_path: str = None):
        self.config_path = config_path or "config/aggressive_test_config.yaml"
        self.config = self._load_config()
        self.kucoin_client = None
        self.data_collector = None
        
    def _load_config(self) -> Dict[str, Any]:
        """Carrega a configuração do sistema."""
        try:
            config_file = Path(self.config_path)
            if config_file.exists():
                with open(config_file, 'r') as f:
                    config = yaml.safe_load(f)
                logger.info(f"✅ Configuração carregada de: {config_file}")
                return config
            else:
                logger.error(f"❌ Arquivo de configuração não encontrado: {config_file}")
                return {}
        except Exception as e:
            logger.error(f"❌ Erro ao carregar configuração: {e}")
            return {}
    
    def _setup_kucoin_client(self):
        """Configura o cliente da KuCoin."""
        try:
            # Carregar credenciais do .env
            api_key = os.getenv("KUCOIN_API_KEY")
            api_secret = os.getenv("KUCOIN_SECRET_KEY") 
            passphrase = os.getenv("KUCOIN_PASSPHRASE")
            
            if not all([api_key, api_secret, passphrase]):
                raise ValueError("Credenciais da KuCoin não encontradas no .env")
            
            # Configurar cliente
            kucoin_config = {
                "api_key": api_key,
                "api_secret": api_secret,
                "password": passphrase,
                "sandbox": True,  # Usar sandbox para segurança
                "exchange_id": "kucoin"
            }
            
            self.kucoin_client = KuCoinClient(kucoin_config)
            logger.info("✅ Cliente KuCoin configurado com sucesso")
            
        except Exception as e:
            logger.error(f"❌ Erro ao configurar cliente KuCoin: {e}")
            raise
    
    async def collect_historical_data(self, symbol: str, timeframe: str = "5m", limit: int = 100):
        """Coleta dados históricos para um símbolo específico."""
        try:
            logger.info(f"📊 Coletando dados históricos para {symbol} ({timeframe})")

            # Inicializar cliente se necessário
            if not hasattr(self.kucoin_client, '_integration') or self.kucoin_client._integration is None:
                await self.kucoin_client.initialize()

            # Criar MarketSpec
            spec = MarketSpec(symbol=symbol, timeframe=timeframe)

            # Buscar dados via cliente KuCoin
            df = await self.kucoin_client.fetch_ohlcv(spec, limit=limit)

            if df is None or df.empty or len(df) < 20:
                logger.warning(f"⚠️ Dados insuficientes para {symbol}: {len(df) if df is not None else 0} candles")
                return None

            logger.info(f"✅ Coletados {len(df)} candles para {symbol}")
            return df

        except Exception as e:
            logger.error(f"❌ Erro ao coletar dados para {symbol}: {e}")
            return None
    
    def save_data_to_cache(self, symbol: str, timeframe: str, df: pd.DataFrame):
        """Salva os dados no cache do sistema."""
        try:
            cache_dir = Path("data/cache")
            cache_dir.mkdir(parents=True, exist_ok=True)
            
            # Converter símbolo para formato de arquivo
            file_symbol = symbol.replace("/", "")
            cache_file = cache_dir / f"{file_symbol}_{timeframe}.json"
            
            # Converter DataFrame para formato JSON esperado
            data = {
                "timestamp": df['timestamp'].dt.strftime('%Y-%m-%dT%H:%M:%S').to_dict(),
                "open": df['open'].to_dict(),
                "high": df['high'].to_dict(),
                "low": df['low'].to_dict(),
                "close": df['close'].to_dict(),
                "volume": df['volume'].to_dict()
            }
            
            # Salvar dados
            with open(cache_file, 'w') as f:
                json.dump(data, f)
            
            # Criar arquivo de metadados
            meta_file = cache_dir / f"{file_symbol}_{timeframe}.meta.json"
            meta_data = {
                "expiry": int((datetime.now(timezone.utc) + timedelta(minutes=5)).timestamp())
            }
            
            with open(meta_file, 'w') as f:
                json.dump(meta_data, f)
            
            logger.info(f"✅ Dados salvos no cache: {cache_file}")
            
        except Exception as e:
            logger.error(f"❌ Erro ao salvar dados no cache: {e}")
    
    async def fix_all_symbols(self):
        """Corrige os dados para todos os símbolos configurados."""
        try:
            # Obter símbolos da configuração
            symbols = self.config.get("symbols", ["BTC/USDT", "SOL/USDT", "ADA/USDT"])
            timeframes = ["5m", "15m"]
            
            logger.info(f"🔧 Corrigindo dados para {len(symbols)} símbolos")
            
            for symbol in symbols:
                for timeframe in timeframes:
                    df = await self.collect_historical_data(symbol, timeframe, limit=100)
                    if df is not None:
                        self.save_data_to_cache(symbol, timeframe, df)
                        
                        # Pequena pausa para não sobrecarregar a API
                        await asyncio.sleep(0.5)
            
            logger.info("🎉 Correção de dados concluída!")
            
        except Exception as e:
            logger.error(f"❌ Erro na correção de dados: {e}")
            raise

async def main():
    """Função principal."""
    logger.info("🌌 YAA - Correção da Coleta de Dados QUALIA")
    logger.info("=" * 60)
    
    try:
        # Verificar argumentos
        config_path = sys.argv[1] if len(sys.argv) > 1 else "config/aggressive_test_config.yaml"
        
        # Criar e executar corretor
        fixer = QUALIADataFixer(config_path)
        fixer._setup_kucoin_client()
        await fixer.fix_all_symbols()
        
        logger.info("✅ Correção concluída com sucesso!")
        
    except Exception as e:
        logger.error(f"❌ Erro na execução: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
