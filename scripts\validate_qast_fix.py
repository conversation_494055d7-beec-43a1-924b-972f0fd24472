#!/usr/bin/env python3
"""
Validation script for QASTCore market integration fix
Tests the specific fix: get_ticker() -> fetch_ticker()
"""
import asyncio
import sys
import os
import logging

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def validate_qast_core_fix():
    """Validate that QASTCore can now properly use market_integration with fetch_ticker()"""
    
    print("🔧 VALIDATING QASTCORE MARKET INTEGRATION FIX")
    print("=" * 60)
    
    try:
        # Import components
        print("📦 Importing QUALIA components...")
        from src.qualia.market.base_integration import CryptoDataFetcher
        from src.qualia.core.qast_core import TradingQASTCore
        print("✅ Components imported successfully")
        
        # Create market integration
        print("\n🌐 Creating market integration...")
        market_integration = CryptoDataFetcher('kucoin')
        
        # Load environment variables for KuCoin
        from dotenv import load_dotenv
        load_dotenv()
        
        await market_integration.initialize_connection()
        print("✅ Market integration initialized")
        
        # Test 1: Direct fetch_ticker call
        print("\n🧪 TEST 1: Direct fetch_ticker() method test")
        try:
            ticker_data = await market_integration.fetch_ticker('BTC-USDT')
            if ticker_data and 'last' in ticker_data:
                price = ticker_data['last']
                print(f"✅ fetch_ticker() works: BTC-USDT @ ${price}")
                test1_success = True
            else:
                print("❌ fetch_ticker() returned invalid data")
                test1_success = False
        except Exception as e:
            print(f"❌ fetch_ticker() failed: {e}")
            test1_success = False
        
        # Test 2: QASTCore with market_integration
        print("\n🧪 TEST 2: QASTCore market_integration parameter")
        try:
            config = {
                'consciousness_threshold': 0.5,
                'qualia': {},
                'symbols': ['BTC/USDT']
            }
            qast_core = TradingQASTCore(config, market_integration=market_integration)
            
            if hasattr(qast_core, 'market_integration') and qast_core.market_integration is not None:
                print("✅ QASTCore received market_integration parameter")
                test2_success = True
            else:
                print("❌ QASTCore did not receive market_integration parameter")
                test2_success = False
        except Exception as e:
            print(f"❌ QASTCore creation failed: {e}")
            test2_success = False
        
        # Test 3: QASTCore market data collection with fixed method
        print("\n🧪 TEST 3: QASTCore market data collection (THE FIX)")
        try:
            if test2_success:
                market_data = await qast_core._gather_market_data_from_integration()
                
                if market_data and len(market_data) > 0:
                    symbols = list(market_data.keys())
                    print(f"✅ QASTCore market data collection SUCCESS: {len(symbols)} symbols")
                    
                    for symbol in symbols:
                        data = market_data[symbol]
                        ticker = data.get('ticker', {})
                        price = ticker.get('last', 'N/A')
                        source = ticker.get('source', 'unknown')
                        print(f"   📈 {symbol}: ${price} (source: {source})")
                    
                    test3_success = True
                else:
                    print("❌ QASTCore market data collection returned empty/None")
                    test3_success = False
            else:
                print("❌ Skipping test 3 due to test 2 failure")
                test3_success = False
        except AttributeError as e:
            if "'kucoin' object has no attribute 'get_ticker'" in str(e):
                print(f"❌ THE OLD BUG IS STILL PRESENT: {e}")
                print("❌ The fix was not applied correctly!")
                test3_success = False
            else:
                print(f"❌ QASTCore market data collection failed: {e}")
                test3_success = False
        except Exception as e:
            print(f"❌ QASTCore market data collection failed: {e}")
            test3_success = False
        
        # Cleanup
        print("\n🧹 Cleaning up...")
        await market_integration.close()
        print("✅ Cleanup completed")
        
        # Results summary
        print("\n📊 VALIDATION RESULTS")
        print("=" * 60)
        print(f"TEST 1 - Direct fetch_ticker(): {'✅ PASS' if test1_success else '❌ FAIL'}")
        print(f"TEST 2 - QASTCore parameter: {'✅ PASS' if test2_success else '❌ FAIL'}")
        print(f"TEST 3 - QASTCore fix validation: {'✅ PASS' if test3_success else '❌ FAIL'}")
        
        overall_success = test1_success and test2_success and test3_success
        
        if overall_success:
            print("\n🎉 QASTCORE MARKET INTEGRATION FIX: SUCCESSFUL!")
            print("✅ The get_ticker() -> fetch_ticker() fix is working correctly")
            print("✅ Ready for full system validation")
        else:
            print("\n💥 QASTCORE MARKET INTEGRATION FIX: FAILED!")
            print("❌ The fix needs further investigation")
        
        return overall_success
        
    except Exception as e:
        print(f"\n💥 VALIDATION FAILED WITH CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(validate_qast_core_fix())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Validation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Validation crashed: {e}")
        sys.exit(1)
