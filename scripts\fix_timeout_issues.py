#!/usr/bin/env python3
"""
Script para corrigir problemas de timeout no QUALIA
Implementa fallbacks robustos e configurações otimizadas
"""

import os
import sys
import asyncio
import time
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, 'src')

def apply_timeout_fixes():
    """Aplica correções para problemas de timeout"""
    print("🔧 APLICANDO CORREÇÕES DE TIMEOUT")
    print("=" * 50)
    
    # Configurações ultra conservadoras
    fixes = {
        'TICKER_TIMEOUT': '60',  # 1 minuto
        'OHLCV_TIMEOUT': '180',  # 3 minutos  
        'RATE_LIMIT': '6.0',     # 6 segundos entre requisições
        'TICKER_CACHE_TTL': '60', # Cache de 1 minuto
        'OHLCV_CACHE_TTL': '600', # Cache de 10 minutos
        'API_FAIL_THRESHOLD': '1', # Circuit breaker após 1 falha
        'API_RECOVERY_TIMEOUT': '30', # 30s recovery
        'TICKER_RETRIES': '1',   # Apenas 1 tentativa
        'OHLCV_RETRIES': '1',    # Apenas 1 tentativa
        'MAX_CONCURRENT_REQUESTS': '1', # Apenas 1 requisição simultânea
    }
    
    for key, value in fixes.items():
        os.environ[key] = value
        print(f"✅ {key} = {value}")
    
    print("\n🎯 Configurações aplicadas:")
    print("   • Timeouts muito altos (60s ticker, 180s OHLCV)")
    print("   • Rate limit conservador (6s)")
    print("   • Cache agressivo (1-10 min)")
    print("   • Circuit breaker rápido (1 falha)")
    print("   • Sem retry (evitar acúmulo)")
    print("   • Requisições sequenciais")

async def test_robust_connectivity():
    """Testa conectividade com configurações robustas"""
    print("\n🔍 TESTE DE CONECTIVIDADE ROBUSTA")
    print("-" * 50)
    
    from qualia.market.kucoin_integration import KucoinIntegration
    from qualia.config.settings import get_env
    
    # Criar integração com configurações ultra conservadoras
    integration = KucoinIntegration(
        api_key=get_env("KUCOIN_API_KEY"),
        api_secret=get_env("KUCOIN_SECRET_KEY"),
        password=get_env("KUCOIN_PASSPHRASE"),
        ticker_timeout=60.0,
        ohlcv_timeout=180.0,
        fail_threshold=1,
        recovery_timeout=30.0,
        use_websocket=False  # Desabilitar WebSocket para simplificar
    )
    
    try:
        print("📡 Inicializando conexão...")
        start_time = time.time()
        await integration.initialize_connection()
        init_time = time.time() - start_time
        print(f"✅ Conexão inicializada em {init_time:.2f}s")
        
        # Teste 1: Ticker com timeout alto
        print("\n🎯 Teste 1: Ticker (timeout 60s)")
        start_time = time.time()
        try:
            ticker = await integration.fetch_ticker('BTC/USDT')
            duration = time.time() - start_time
            
            if ticker and ticker.get('last'):
                print(f"✅ Ticker obtido em {duration:.2f}s")
                print(f"   Preço: ${ticker['last']}")
                print(f"   Bid: ${ticker.get('bid', 'N/A')}")
                print(f"   Ask: ${ticker.get('ask', 'N/A')}")
                return True
            else:
                print(f"❌ Ticker vazio após {duration:.2f}s")
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            print(f"❌ Erro no ticker após {duration:.2f}s: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Erro na inicialização: {e}")
        return False
    finally:
        try:
            await integration.close()
            print("🔌 Conexão fechada")
        except:
            pass

def create_emergency_config():
    """Cria configuração de emergência para casos extremos"""
    print("\n🚨 CRIANDO CONFIGURAÇÃO DE EMERGÊNCIA")
    print("-" * 50)
    
    config_path = Path("config/emergency_settings.yaml")
    config_content = """
# Configuração de emergência para problemas de conectividade
emergency_mode:
  enabled: true
  
# Timeouts extremamente altos
timeouts:
  ticker: 120  # 2 minutos
  ohlcv: 300   # 5 minutos
  connection: 60  # 1 minuto
  
# Rate limiting ultra conservador
rate_limiting:
  base_interval: 10.0  # 10 segundos entre requisições
  backoff_multiplier: 2.0
  max_backoff: 60.0
  
# Cache muito agressivo
cache:
  ticker_ttl: 300  # 5 minutos
  ohlcv_ttl: 1800  # 30 minutos
  
# Circuit breaker imediato
circuit_breaker:
  fail_threshold: 1
  recovery_timeout: 60
  
# Fallbacks
fallbacks:
  use_cache_on_error: true
  skip_failed_symbols: true
  continue_on_api_error: true
  
# Logging detalhado
logging:
  level: DEBUG
  network_details: true
  timing_info: true
"""
    
    config_path.parent.mkdir(exist_ok=True)
    config_path.write_text(config_content)
    print(f"✅ Configuração salva em: {config_path}")
    print("   • Timeouts de até 5 minutos")
    print("   • Rate limit de 10 segundos")
    print("   • Cache de até 30 minutos")
    print("   • Fallbacks habilitados")

async def main():
    """Função principal"""
    print("🚨 CORREÇÃO DE PROBLEMAS DE TIMEOUT - QUALIA")
    print("=" * 60)
    
    # Aplicar correções
    apply_timeout_fixes()
    
    # Criar configuração de emergência
    create_emergency_config()
    
    # Testar conectividade
    success = await test_robust_connectivity()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 CORREÇÕES APLICADAS COM SUCESSO!")
        print("✅ Sistema pronto para execução com configurações robustas")
        print("\n📋 Para usar:")
        print("1. Execute: python scripts/run_qualia_optimized.py")
        print("2. O sistema usará as configurações robustas automaticamente")
        print("3. Monitorize logs para verificar performance")
    else:
        print("⚠️ AINDA HÁ PROBLEMAS DE CONECTIVIDADE")
        print("🔧 Recomendações:")
        print("1. Verifique conexão de internet")
        print("2. Verifique credenciais KuCoin")
        print("3. Considere usar VPN se houver bloqueios")
        print("4. Execute em horários de menor tráfego")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1) 