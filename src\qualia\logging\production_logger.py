"""
QUALIA Production Logging System
Structured JSON logging with correlation, rotation, and monitoring integration
"""

import os
import json
import logging
import logging.handlers
import uuid
import time
from datetime import datetime
from typing import Dict, Any, Optional, List
from contextlib import contextmanager
from dataclasses import dataclass, asdict
import threading
from pathlib import Path
import sys

# Thread-local storage for correlation context
_correlation_context = threading.local()

@dataclass
class LogContext:
    """Log context information"""
    correlation_id: str
    session_id: str
    user_id: Optional[str] = None
    component: Optional[str] = None
    operation: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class CorrelationFormatter(logging.Formatter):
    """Custom formatter that adds correlation context to log records"""
    
    def format(self, record):
        # Get correlation context
        correlation_id = getattr(_correlation_context, 'correlation_id', None)
        session_id = getattr(_correlation_context, 'session_id', None)
        component = getattr(_correlation_context, 'component', None)
        operation = getattr(_correlation_context, 'operation', None)
        metadata = getattr(_correlation_context, 'metadata', {})
        
        # Create structured log entry
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'thread_name': record.threadName,
            'process': record.process,
            'correlation_id': correlation_id,
            'session_id': session_id,
            'component': component,
            'operation': operation,
            'metadata': metadata
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__ if record.exc_info[0] else None,
                'message': str(record.exc_info[1]) if record.exc_info[1] else None,
                'traceback': self.formatException(record.exc_info)
            }
        
        # Add extra fields from record
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info']:
                log_entry['extra'] = log_entry.get('extra', {})
                log_entry['extra'][key] = value
        
        return json.dumps(log_entry, default=str, ensure_ascii=False)

class ProductionLogger:
    """
    Production logging system with structured JSON output,
    correlation tracking, and monitoring integration
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.loggers = {}
        self.session_id = str(uuid.uuid4())
        
        # Create log directories
        self._create_log_directories()
        
        # Setup loggers
        self._setup_loggers()
        
        # Setup correlation context
        self._setup_correlation_context()
    
    def _create_log_directories(self):
        """Create necessary log directories"""
        log_files = self.config.get('logging', {}).get('files', {})
        
        for log_file in log_files.values():
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
    
    def _setup_loggers(self):
        """Setup structured loggers for different components"""
        logging_config = self.config.get('logging', {})
        log_level = getattr(logging, logging_config.get('level', 'INFO'))
        
        # Main application logger
        self._setup_logger(
            'qualia.main',
            logging_config.get('files', {}).get('main', 'logs/qualia_production.log'),
            log_level
        )
        
        # Trading operations logger
        self._setup_logger(
            'qualia.trading',
            logging_config.get('files', {}).get('trading', 'logs/trading.log'),
            log_level
        )
        
        # Optimization logger
        self._setup_logger(
            'qualia.optimization',
            logging_config.get('files', {}).get('optimization', 'logs/optimization.log'),
            log_level
        )
        
        # Error logger (higher level only)
        self._setup_logger(
            'qualia.errors',
            logging_config.get('files', {}).get('errors', 'logs/errors.log'),
            logging.ERROR
        )
        
        # Performance logger
        self._setup_logger(
            'qualia.performance',
            'logs/performance.log',
            log_level
        )
        
        # Security/audit logger
        self._setup_logger(
            'qualia.security',
            'logs/security.log',
            log_level
        )
    
    def _setup_logger(self, name: str, filename: str, level: int):
        """Setup individual logger with rotation and formatting"""
        logger = logging.getLogger(name)
        logger.setLevel(level)
        
        # Remove existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # File handler with rotation
        rotation_config = self.config.get('logging', {}).get('rotation', {})
        
        file_handler = logging.handlers.RotatingFileHandler(
            filename=filename,
            maxBytes=self._parse_size(rotation_config.get('max_size', '100MB')),
            backupCount=rotation_config.get('backup_count', 10),
            encoding='utf-8'
        )
        
        # Set formatter
        formatter = CorrelationFormatter()
        file_handler.setFormatter(formatter)
        
        # Add handler
        logger.addHandler(file_handler)
        
        # Console handler for development (if enabled)
        if self.config.get('environment', {}).get('debug', False):
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        # Store logger reference
        self.loggers[name] = logger
        
        # Prevent propagation to root logger
        logger.propagate = False
    
    def _parse_size(self, size_str: str) -> int:
        """Parse size string like '100MB' to bytes"""
        size_str = size_str.upper()
        
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def _setup_correlation_context(self):
        """Setup initial correlation context"""
        _correlation_context.correlation_id = str(uuid.uuid4())
        _correlation_context.session_id = self.session_id
        _correlation_context.component = 'qualia.system'
        _correlation_context.operation = 'initialization'
        _correlation_context.metadata = {}
    
    @contextmanager
    def correlation_context(self, correlation_id: Optional[str] = None,
                          component: Optional[str] = None,
                          operation: Optional[str] = None,
                          metadata: Optional[Dict[str, Any]] = None):
        """Context manager for correlation tracking"""
        # Save current context
        old_correlation_id = getattr(_correlation_context, 'correlation_id', None)
        old_component = getattr(_correlation_context, 'component', None)
        old_operation = getattr(_correlation_context, 'operation', None)
        old_metadata = getattr(_correlation_context, 'metadata', {})
        
        try:
            # Set new context
            _correlation_context.correlation_id = correlation_id or str(uuid.uuid4())
            _correlation_context.component = component or old_component
            _correlation_context.operation = operation or old_operation
            _correlation_context.metadata = {**old_metadata, **(metadata or {})}
            
            yield _correlation_context.correlation_id
            
        finally:
            # Restore old context
            _correlation_context.correlation_id = old_correlation_id
            _correlation_context.component = old_component
            _correlation_context.operation = old_operation
            _correlation_context.metadata = old_metadata
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get logger by name"""
        return self.loggers.get(name, logging.getLogger(name))
    
    def log_trading_event(self, event_type: str, symbol: str, 
                         data: Dict[str, Any], level: str = 'INFO'):
        """Log trading-specific events"""
        logger = self.get_logger('qualia.trading')
        
        with self.correlation_context(
            component='trading',
            operation=event_type,
            metadata={'symbol': symbol, 'event_type': event_type}
        ):
            log_method = getattr(logger, level.lower())
            log_method(f"Trading event: {event_type}", extra={
                'symbol': symbol,
                'event_type': event_type,
                'trading_data': data
            })
    
    def log_optimization_event(self, trial_id: str, parameters: Dict[str, Any],
                             results: Dict[str, Any], level: str = 'INFO'):
        """Log optimization-specific events"""
        logger = self.get_logger('qualia.optimization')
        
        with self.correlation_context(
            component='optimization',
            operation='trial_complete',
            metadata={'trial_id': trial_id}
        ):
            log_method = getattr(logger, level.lower())
            log_method(f"Optimization trial completed: {trial_id}", extra={
                'trial_id': trial_id,
                'parameters': parameters,
                'results': results
            })
    
    def log_performance_metrics(self, metrics: Dict[str, Any]):
        """Log performance metrics"""
        logger = self.get_logger('qualia.performance')
        
        with self.correlation_context(
            component='monitoring',
            operation='metrics_collection'
        ):
            logger.info("Performance metrics collected", extra={
                'metrics': metrics,
                'timestamp': datetime.utcnow().isoformat()
            })
    
    def log_security_event(self, event_type: str, details: Dict[str, Any],
                          level: str = 'WARNING'):
        """Log security-related events"""
        logger = self.get_logger('qualia.security')
        
        with self.correlation_context(
            component='security',
            operation=event_type,
            metadata={'security_event': event_type}
        ):
            log_method = getattr(logger, level.lower())
            log_method(f"Security event: {event_type}", extra={
                'event_type': event_type,
                'security_details': details
            })
    
    def log_error(self, error: Exception, context: Dict[str, Any],
                  component: str = 'system'):
        """Log errors with full context"""
        logger = self.get_logger('qualia.errors')
        
        with self.correlation_context(
            component=component,
            operation='error_handling',
            metadata=context
        ):
            logger.error(f"Error in {component}: {str(error)}", 
                        exc_info=True, extra={
                'error_type': type(error).__name__,
                'error_message': str(error),
                'context': context
            })
    
    def log_system_startup(self, config: Dict[str, Any]):
        """Log system startup with configuration"""
        logger = self.get_logger('qualia.main')
        
        with self.correlation_context(
            component='system',
            operation='startup'
        ):
            logger.info("QUALIA system starting up", extra={
                'startup_config': {
                    'environment': config.get('environment', {}),
                    'trading_symbols': config.get('symbols', {}),
                    'risk_management': config.get('risk_management', {}),
                    'session_id': self.session_id
                }
            })
    
    def log_system_shutdown(self, reason: str = 'normal'):
        """Log system shutdown"""
        logger = self.get_logger('qualia.main')
        
        with self.correlation_context(
            component='system',
            operation='shutdown'
        ):
            logger.info(f"QUALIA system shutting down: {reason}", extra={
                'shutdown_reason': reason,
                'session_id': self.session_id,
                'uptime_seconds': time.time() - self._start_time if hasattr(self, '_start_time') else 0
            })
    
    def start_session(self):
        """Start logging session"""
        self._start_time = time.time()
        logger = self.get_logger('qualia.main')
        
        with self.correlation_context(
            component='system',
            operation='session_start'
        ):
            logger.info("Logging session started", extra={
                'session_id': self.session_id,
                'start_time': datetime.utcnow().isoformat()
            })

# Global production logger instance
_production_logger = None

def initialize_production_logging(config: Dict[str, Any]) -> ProductionLogger:
    """Initialize global production logger"""
    global _production_logger
    _production_logger = ProductionLogger(config)
    _production_logger.start_session()
    return _production_logger

def get_production_logger() -> Optional[ProductionLogger]:
    """Get global production logger instance"""
    return _production_logger

# Convenience functions
def log_trading_event(event_type: str, symbol: str, data: Dict[str, Any], level: str = 'INFO'):
    """Log trading event using global logger"""
    if _production_logger:
        _production_logger.log_trading_event(event_type, symbol, data, level)

def log_optimization_event(trial_id: str, parameters: Dict[str, Any], 
                          results: Dict[str, Any], level: str = 'INFO'):
    """Log optimization event using global logger"""
    if _production_logger:
        _production_logger.log_optimization_event(trial_id, parameters, results, level)

def log_performance_metrics(metrics: Dict[str, Any]):
    """Log performance metrics using global logger"""
    if _production_logger:
        _production_logger.log_performance_metrics(metrics)

def log_security_event(event_type: str, details: Dict[str, Any], level: str = 'WARNING'):
    """Log security event using global logger"""
    if _production_logger:
        _production_logger.log_security_event(event_type, details, level)

def log_error(error: Exception, context: Dict[str, Any], component: str = 'system'):
    """Log error using global logger"""
    if _production_logger:
        _production_logger.log_error(error, context, component)
