#!/usr/bin/env python3
"""
Test script for asynchronous initialization system.
"""

import sys
import asyncio
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class MockComponent:
    """Mock component for testing"""
    
    def __init__(self, name: str, init_delay: float = 0.1, fail: bool = False):
        self.name = name
        self.init_delay = init_delay
        self.fail = fail
        self.initialized = False
        self.initialization_time = 0.0
    
    async def initialize(self):
        """Initialize the component"""
        start_time = time.time()
        await asyncio.sleep(self.init_delay)
        
        if self.fail:
            raise Exception(f"Mock failure in {self.name}")
        
        self.initialized = True
        self.initialization_time = time.time() - start_time
        print(f"✅ {self.name} initialized in {self.initialization_time:.2f}s")


async def mock_component_factory(name: str, init_delay: float = 0.1, fail: bool = False):
    """Factory for mock components"""
    return MockComponent(name, init_delay, fail)


async def test_basic_initialization():
    """Test basic component initialization"""
    print("🧪 Testing Basic Component Initialization")
    print("=" * 50)
    
    try:
        from src.qualia.core.async_initialization_system import (
            AsyncInitializationSystem, ComponentDefinition, InitializationStrategy
        )
        
        # Create initialization system
        init_system = AsyncInitializationSystem()
        
        # Register simple components
        components = [
            ComponentDefinition(
                name="component_a",
                factory=lambda: MockComponent("ComponentA", 0.1),
                dependencies=[],
                strategy=InitializationStrategy.PARALLEL
            ),
            ComponentDefinition(
                name="component_b",
                factory=lambda: MockComponent("ComponentB", 0.2),
                dependencies=[],
                strategy=InitializationStrategy.PARALLEL
            ),
            ComponentDefinition(
                name="component_c",
                factory=lambda: MockComponent("ComponentC", 0.1),
                dependencies=["component_a"],
                strategy=InitializationStrategy.PARALLEL
            )
        ]
        
        init_system.register_components(components)
        
        # Initialize all components
        report = await init_system.initialize_all()
        
        # Verify results
        if report.successful == 3 and report.failed == 0:
            print("✅ All components initialized successfully")
        else:
            print(f"❌ Expected 3 successful, got {report.successful} successful, {report.failed} failed")
            return False
        
        # Verify dependency order
        component_c_result = report.results.get("component_c")
        if component_c_result and "component_a" in component_c_result.dependencies_resolved:
            print("✅ Dependencies resolved correctly")
        else:
            print("❌ Dependencies not resolved correctly")
            return False
        
        # Verify instances are available
        instance_a = init_system.get_component("component_a")
        if instance_a and hasattr(instance_a, 'initialized') and instance_a.initialized:
            print("✅ Component instances accessible")
        else:
            print("❌ Component instances not accessible")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in basic initialization test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_parallel_initialization():
    """Test parallel initialization performance"""
    print("\n🧪 Testing Parallel Initialization Performance")
    print("=" * 50)
    
    try:
        from src.qualia.core.async_initialization_system import (
            AsyncInitializationSystem, ComponentDefinition, InitializationStrategy
        )
        
        # Create initialization system
        init_system = AsyncInitializationSystem()
        
        # Register components that can be initialized in parallel
        components = []
        for i in range(5):
            components.append(ComponentDefinition(
                name=f"parallel_component_{i}",
                factory=lambda i=i: MockComponent(f"ParallelComponent{i}", 0.5),  # 0.5s each
                dependencies=[],
                strategy=InitializationStrategy.PARALLEL
            ))
        
        init_system.register_components(components)
        
        # Measure initialization time
        start_time = time.time()
        report = await init_system.initialize_all()
        total_time = time.time() - start_time
        
        # Verify parallel execution (should be ~0.5s, not 2.5s)
        if total_time < 1.0:  # Allow some overhead
            print(f"✅ Parallel initialization working: {total_time:.2f}s for 5 components")
        else:
            print(f"❌ Parallel initialization too slow: {total_time:.2f}s for 5 components")
            return False
        
        if report.successful == 5:
            print("✅ All parallel components initialized")
        else:
            print(f"❌ Expected 5 successful, got {report.successful}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in parallel initialization test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_dependency_resolution():
    """Test complex dependency resolution"""
    print("\n🧪 Testing Complex Dependency Resolution")
    print("=" * 50)
    
    try:
        from src.qualia.core.async_initialization_system import (
            AsyncInitializationSystem, ComponentDefinition, InitializationStrategy
        )
        
        # Create initialization system
        init_system = AsyncInitializationSystem()
        
        # Create complex dependency chain: A -> B -> C, A -> D, B -> E
        components = [
            ComponentDefinition(
                name="comp_a",
                factory=lambda: MockComponent("CompA", 0.1),
                dependencies=[],
                strategy=InitializationStrategy.PARALLEL
            ),
            ComponentDefinition(
                name="comp_b",
                factory=lambda: MockComponent("CompB", 0.1),
                dependencies=["comp_a"],
                strategy=InitializationStrategy.PARALLEL
            ),
            ComponentDefinition(
                name="comp_c",
                factory=lambda: MockComponent("CompC", 0.1),
                dependencies=["comp_b"],
                strategy=InitializationStrategy.PARALLEL
            ),
            ComponentDefinition(
                name="comp_d",
                factory=lambda: MockComponent("CompD", 0.1),
                dependencies=["comp_a"],
                strategy=InitializationStrategy.PARALLEL
            ),
            ComponentDefinition(
                name="comp_e",
                factory=lambda: MockComponent("CompE", 0.1),
                dependencies=["comp_b"],
                strategy=InitializationStrategy.PARALLEL
            )
        ]
        
        init_system.register_components(components)
        
        # Initialize all components
        report = await init_system.initialize_all()
        
        # Verify all components initialized
        if report.successful == 5:
            print("✅ All components with complex dependencies initialized")
        else:
            print(f"❌ Expected 5 successful, got {report.successful}")
            return False
        
        # Verify initialization order respects dependencies
        order = report.initialization_order
        a_index = order.index("comp_a")
        b_index = order.index("comp_b")
        c_index = order.index("comp_c")
        
        if a_index < b_index < c_index:
            print("✅ Dependency order respected")
        else:
            print(f"❌ Dependency order not respected: {order}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in dependency resolution test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_failure_handling():
    """Test failure handling and circuit breaker"""
    print("\n🧪 Testing Failure Handling and Circuit Breaker")
    print("=" * 50)
    
    try:
        from src.qualia.core.async_initialization_system import (
            AsyncInitializationSystem, ComponentDefinition, InitializationStrategy
        )
        
        # Create initialization system
        init_system = AsyncInitializationSystem()
        
        # Register components with one that fails
        components = [
            ComponentDefinition(
                name="good_component",
                factory=lambda: MockComponent("GoodComponent", 0.1, fail=False),
                dependencies=[],
                strategy=InitializationStrategy.PARALLEL,
                required=False
            ),
            ComponentDefinition(
                name="failing_component",
                factory=lambda: MockComponent("FailingComponent", 0.1, fail=True),
                dependencies=[],
                strategy=InitializationStrategy.PARALLEL,
                required=False,
                retry_count=2
            ),
            ComponentDefinition(
                name="dependent_component",
                factory=lambda: MockComponent("DependentComponent", 0.1, fail=False),
                dependencies=["failing_component"],
                strategy=InitializationStrategy.PARALLEL,
                required=False
            )
        ]
        
        init_system.register_components(components)
        
        # Initialize all components
        report = await init_system.initialize_all()
        
        # Verify failure handling
        if report.successful == 1 and report.failed == 1 and report.skipped == 1:
            print("✅ Failure handling working correctly")
            print(f"   Successful: {report.successful}, Failed: {report.failed}, Skipped: {report.skipped}")
        else:
            print(f"❌ Unexpected failure handling: successful={report.successful}, failed={report.failed}, skipped={report.skipped}")
            return False
        
        # Verify good component still initialized
        good_instance = init_system.get_component("good_component")
        if good_instance and good_instance.initialized:
            print("✅ Good component unaffected by failures")
        else:
            print("❌ Good component affected by failures")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in failure handling test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_initialization_report():
    """Test initialization reporting"""
    print("\n🧪 Testing Initialization Reporting")
    print("=" * 50)
    
    try:
        from src.qualia.core.async_initialization_system import (
            AsyncInitializationSystem, ComponentDefinition, InitializationStrategy
        )
        
        # Create initialization system
        init_system = AsyncInitializationSystem()
        
        # Register mixed components
        components = [
            ComponentDefinition(
                name="fast_component",
                factory=lambda: MockComponent("FastComponent", 0.05),
                dependencies=[],
                strategy=InitializationStrategy.PARALLEL
            ),
            ComponentDefinition(
                name="slow_component",
                factory=lambda: MockComponent("SlowComponent", 0.3),
                dependencies=[],
                strategy=InitializationStrategy.PARALLEL
            )
        ]
        
        init_system.register_components(components)
        
        # Initialize and get report
        report = await init_system.initialize_all()
        
        # Verify report structure
        if hasattr(report, 'total_components') and hasattr(report, 'success_rate'):
            print("✅ Report structure correct")
        else:
            print("❌ Report structure incorrect")
            return False
        
        # Verify timing information
        fast_result = report.results.get("fast_component")
        slow_result = report.results.get("slow_component")
        
        if (fast_result and slow_result and 
            fast_result.initialization_time < slow_result.initialization_time):
            print("✅ Timing information captured correctly")
        else:
            print("❌ Timing information incorrect")
            return False
        
        # Verify success rate calculation
        expected_rate = report.successful / report.total_components
        if abs(report.success_rate - expected_rate) < 0.01:
            print(f"✅ Success rate calculated correctly: {report.success_rate:.1%}")
        else:
            print(f"❌ Success rate calculation incorrect: {report.success_rate}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in reporting test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all tests"""
    print("🚀 Starting Async Initialization System Tests")
    print("=" * 80)
    
    tests = [
        test_basic_initialization,
        test_parallel_initialization,
        test_dependency_resolution,
        test_failure_handling,
        test_initialization_report
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if await test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ FAIL: {test.__name__} raised exception: {e}")
            failed += 1
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 TEST SUMMARY")
    print("=" * 80)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 All async initialization tests passed!")
        return True
    else:
        print(f"\n💥 {failed} test(s) failed!")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
