import os
import argparse
from qualia.utils.logger import get_logger
import json
from qualia.config import config
from qualia.utils.logging_config import init_logging
from qualia.utils.persistence import load_positions_json
from datetime import datetime


# Configurar logger
logger = get_logger("show_positions")


class DateTimeEncoder(json.JSONEncoder):
    """Classe para codificar objetos datetime para JSON."""

    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


def show_positions(base_dir=None, detail_level=1):
    """
    Mostra todas as posições abertas do sistema QUALIA.

    Args:
        base_dir: Diretório base do projeto QUALIA. Se None, usa o diretório atual.
        detail_level: Nível de detalhes (1-3, sendo 3 o mais detalhado)
    """
    if base_dir is None:
        base_dir = os.getcwd()

    # Caminho para o arquivo de cache de posições abertas
    positions_file = config.open_positions_file

    # Verificar se o arquivo existe
    if not os.path.exists(positions_file):
        logger.info(f"Arquivo de posições não encontrado: {positions_file}")
        logger.info("Não há posições abertas registradas.")
        return

    try:
        # Carregar posições abertas
        open_positions = load_positions_json(positions_file)

        if not open_positions:
            logger.info("Não há posições abertas registradas.")
            return

        # Mostrar posições abertas
        total_pos = sum(len(p) for p in open_positions.values())
        logger.info(f"Posições abertas encontradas: {total_pos}")

        # Formatar e mostrar cada posição com base no nível de detalhe
        for symbol, positions in open_positions.items():
            for position in positions:
                position_id = position.order_id
                position_data = {
                    "ID": position_id,
                    "Símbolo": position.symbol,
                    "Lado": position.side.upper(),
                    "Preço de Entrada": position.entry_price,
                    "Tamanho": position.size,
                    "Stop Loss": position.stop_loss,
                    "Take Profit": position.take_profit,
                    "Status": position.status,
                    "Timestamp": (
                        position.timestamp.isoformat()
                        if isinstance(position.timestamp, datetime)
                        else position.timestamp
                    ),
                    "P&L": f"{position.pnl:.2f} ({position.pnl_pct:.2%})",
                }

            # Adicionar detalhes extras para níveis mais altos
            if detail_level >= 2:
                # Calcular o potencial de ganho/perda (distance to TP/SL)
                if position.take_profit and position.stop_loss:
                    if position.side == "buy":
                        tp_distance = (
                            (position.take_profit - position.entry_price)
                            / position.entry_price
                            * 100
                        )
                        sl_distance = (
                            (position.entry_price - position.stop_loss)
                            / position.entry_price
                            * 100
                        )
                    else:  # sell
                        tp_distance = (
                            (position.entry_price - position.take_profit)
                            / position.entry_price
                            * 100
                        )
                        sl_distance = (
                            (position.stop_loss - position.entry_price)
                            / position.entry_price
                            * 100
                        )

                    position_data["TP Distance"] = f"{tp_distance:.2f}%"
                    position_data["SL Distance"] = f"{sl_distance:.2f}%"
                    position_data["Risk-Reward Ratio"] = (
                        f"{tp_distance/sl_distance:.2f}" if sl_distance > 0 else "∞"
                    )

            if detail_level >= 3:
                # Adicionar informações sobre a assinatura quântica
                if (
                    hasattr(position, "captured_quantum_signature_packet")
                    and position.captured_quantum_signature_packet
                ):
                    qsp = position.captured_quantum_signature_packet
                    position_data["Quantum Signature"] = {
                        "ID": getattr(qsp, "id", "N/A"),
                        "Vector Length": (
                            len(qsp.vector)
                            if hasattr(qsp, "vector") and qsp.vector is not None
                            else 0
                        ),
                        "Source": (
                            getattr(qsp, "source_details", {}).get("type", "N/A")
                            if hasattr(qsp, "source_details")
                            else "N/A"
                        ),
                    }

                # Adicionar detalhes de contexto de decisão
                if (
                    hasattr(position, "decision_context_details")
                    and position.decision_context_details
                ):
                    position_data["Decision Context"] = {
                        k: v
                        for k, v in position.decision_context_details.items()
                        if k in ["confidence", "reasons", "strategy_id"]
                    }

            # Imprimir formatado como JSON
            print(f"\n===== Posição {position_id} =====")
            print(json.dumps(position_data, indent=2, cls=DateTimeEncoder))

    except Exception as e:
        logger.error(f"Erro ao ler posições abertas: {e}", exc_info=True)


def main():
    """Função principal."""
    parser = argparse.ArgumentParser(
        description="Mostra posições abertas do sistema QUALIA."
    )
    parser.add_argument(
        "--base_dir", type=str, help="Diretório base do projeto QUALIA."
    )
    parser.add_argument(
        "--detail",
        type=int,
        default=1,
        choices=[1, 2, 3],
        help="Nível de detalhes (1-3, sendo 3 o mais detalhado)",
    )

    args = parser.parse_args()

    # Se init_logging já configurou o sistema, um aviso será registrado
    # via ``logger.warning``
    init_logging(
        log_level="INFO",
        enable_console=True,
        enable_structured=True,
    )

    show_positions(args.base_dir, args.detail)


if __name__ == "__main__":
    main()
