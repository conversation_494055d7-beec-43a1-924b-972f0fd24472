# Multimodal Encoders

Este documento descreve os encoders quânticos voltados a **áudio**, **imagem** e **vídeo** introduzidos em `src/qualia/core/multimodal_encoders.py`.

| Encoder | Snapshot esperado | Qubits | Operação quântica | Notas |
|---------|------------------|--------|-------------------|-------|
| `SpectrogramQuantumEncoder` | `{ "band_power": float, "timestamp": float }` | 1 | `Ry(θ)` onde `θ = band_power * π/2` | Potência de banda STFT normalizada (0-1). |
| `QuantumPatchEncoder` | `{ "patch": np.ndarray shape (64,), "position": (int,int) }` | 6 | `initialize(|ψ⟩)` | Patch 8×8 em escala 0-1, amplitude-encoding. |
| `TemporalDifferenceEncoder` | `{ "diff_value": float, "frame_idx": int }` | 1 | `Rz(φ)` onde `φ = diff_value * π` | Diferença normalizada frame-to-frame. |

## Exemplo mínimo (NumPy)
```python
from qualia.core.multimodal_encoders import SpectrogramQuantumEncoder
import numpy as np

snap = {"band_power": 0.42, "timestamp": 0.0}
enc = SpectrogramQuantumEncoder()
vec = enc.encode(snap)  # np.ndarray [cos θ, sin θ]
print(vec)
```

## Dependências opcionais para o demo
```
pip install librosa opencv-python qiskit-aer tqdm
```

## Executando o demo
```
python scripts/demo_synesthetic_fusion.py samples/audio.wav samples/video.mp4
```
Gera `fusion_brightness.npy` contendo série temporal de coerência (0-1). Mapear este vetor em cores produz visualização sinestésica.

## Testes unitários
Há um teste simples em `tests/test_multimodal_encoders.py`:
```
pytest -q tests/test_multimodal_encoders.py
```
Ele verifica: shape de saída, normalização e robustez do fallback.
