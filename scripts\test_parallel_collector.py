#!/usr/bin/env python3
"""
YAA: Script para testar melhorias de paralelização do EnhancedDataCollector.

Demonstra:
- Paralelização com asyncio.gather()
- Rate limiting com semáforo
- Batch size otimizado (8 requisições)
- Pausa reduzida (0.5s)
- Métricas de performance

Uso:
    python scripts/test_parallel_collector.py --symbols BTC/USDT,ETH/USDT --benchmark
    python scripts/test_parallel_collector.py --compare-sequential --iterations 3
"""

import argparse
import asyncio
import sys
import time
from pathlib import Path

# Adiciona o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.consciousness.enhanced_data_collector import EnhancedDataCollector
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class MockDataFetcher:
    """Mock do data fetcher para testes."""
    
    def __init__(self, latency: float = 0.1):
        self.latency = latency
        self.call_count = 0
        
    async def fetch_ohlcv(self, spec, **kwargs):
        """Simula fetch de dados OHLCV."""
        self.call_count += 1
        await asyncio.sleep(self.latency)  # Simula latência de API
        
        import pandas as pd
        import numpy as np
        
        # Retorna DataFrame simulado
        rows = kwargs.get('limit', 100)
        return pd.DataFrame({
            'timestamp': pd.date_range('2024-01-01', periods=rows, freq='1min'),
            'open': np.random.uniform(40000, 50000, rows),
            'high': np.random.uniform(50000, 55000, rows),
            'low': np.random.uniform(35000, 40000, rows),
            'close': np.random.uniform(40000, 50000, rows),
            'volume': np.random.uniform(100, 1000, rows),
        })


async def test_parallel_collection(symbols: list, timeframes: list, iterations: int = 1):
    """Testa coleta paralela com métricas."""
    print("\n" + "="*60)
    print("🚀 TESTE DE COLETA PARALELA")
    print("="*60)
    
    # Configura collector com mock
    collector = EnhancedDataCollector(
        symbols=symbols,
        timeframes=timeframes,
        gather_timeout=30.0
    )
    
    # Injeta mock data fetcher
    mock_fetcher = MockDataFetcher(latency=0.2)  # 200ms de latência simulada
    collector.data_fetcher = mock_fetcher
    
    total_tasks = len(symbols) * len(timeframes)
    print(f"Símbolos: {symbols}")
    print(f"Timeframes: {timeframes}")
    print(f"Total de tarefas: {total_tasks}")
    
    # Executa testes
    times = []
    success_rates = []
    
    for i in range(iterations):
        print(f"\n--- Iteração {i+1}/{iterations} ---")
        
        start_time = time.time()
        results = await collector.collect_enhanced_market_data()
        end_time = time.time()
        
        duration = end_time - start_time
        success_count = len([r for r in results if r is not None])
        success_rate = success_count / total_tasks if total_tasks > 0 else 0.0
        
        times.append(duration)
        success_rates.append(success_rate)
        
        print(f"Duração: {duration:.2f}s")
        print(f"Sucessos: {success_count}/{total_tasks} ({success_rate:.1%})")
        print(f"Chamadas API: {mock_fetcher.call_count}")
        
        # Reset contador para próxima iteração
        mock_fetcher.call_count = 0
    
    # Estatísticas finais
    avg_time = sum(times) / len(times)
    avg_success_rate = sum(success_rates) / len(success_rates)
    
    print(f"\n📊 ESTATÍSTICAS FINAIS:")
    print(f"Tempo médio: {avg_time:.2f}s")
    print(f"Taxa de sucesso média: {avg_success_rate:.1%}")
    print(f"Throughput: {total_tasks/avg_time:.1f} tarefas/s")
    
    return avg_time, avg_success_rate


async def test_sequential_vs_parallel(symbols: list, timeframes: list):
    """Compara coleta sequencial vs paralela."""
    print("\n" + "="*60)
    print("⚡ COMPARAÇÃO SEQUENCIAL vs PARALELA")
    print("="*60)
    
    total_tasks = len(symbols) * len(timeframes)
    
    # Teste sequencial simulado
    print("\n🐌 Teste Sequencial (simulado):")
    mock_fetcher = MockDataFetcher(latency=0.2)
    
    start_time = time.time()
    for symbol in symbols:
        for timeframe in timeframes:
            await mock_fetcher.fetch_ohlcv(None, limit=100)
    sequential_time = time.time() - start_time
    
    print(f"Tempo sequencial: {sequential_time:.2f}s")
    print(f"Throughput: {total_tasks/sequential_time:.1f} tarefas/s")
    
    # Teste paralelo
    print(f"\n🚀 Teste Paralelo (batch_size=8):")
    parallel_time, success_rate = await test_parallel_collection(symbols, timeframes, 1)
    
    # Comparação
    speedup = sequential_time / parallel_time
    print(f"\n📈 RESULTADOS:")
    print(f"Speedup: {speedup:.1f}x mais rápido")
    print(f"Eficiência: {speedup/8:.1%} (ideal: 100% para 8 threads)")
    print(f"Taxa de sucesso: {success_rate:.1%}")


async def benchmark_batch_sizes():
    """Testa diferentes tamanhos de batch."""
    print("\n" + "="*60)
    print("📊 BENCHMARK DE BATCH SIZES")
    print("="*60)
    
    symbols = ["BTC/USDT", "ETH/USDT", "ADA/USDT", "DOT/USDT"]
    timeframes = ["1m", "5m", "15m"]
    batch_sizes = [1, 2, 4, 8, 16]
    
    results = {}
    
    for batch_size in batch_sizes:
        print(f"\n--- Testando batch_size = {batch_size} ---")
        
        # Cria collector com batch_size específico
        collector = EnhancedDataCollector(
            symbols=symbols,
            timeframes=timeframes,
            gather_timeout=30.0
        )
        
        # Modifica batch_size internamente (hack para teste)
        original_method = collector.collect_enhanced_market_data
        
        async def patched_method():
            # Simula diferentes batch sizes alterando o semáforo
            import aiohttp
            
            tasks_info = [
                (symbol, timeframe)
                for symbol in collector.symbols
                for timeframe in collector.timeframes
            ]
            
            async with aiohttp.ClientSession() as session:
                semaphore = asyncio.Semaphore(batch_size)
                
                async def rate_limited_task(symbol: str, timeframe: str):
                    async with semaphore:
                        # Simula processamento
                        await asyncio.sleep(0.2)
                        return f"{symbol}@{timeframe}"
                
                start_time = time.time()
                gather_tasks = [
                    rate_limited_task(sym, tf) for sym, tf in tasks_info
                ]
                
                await asyncio.gather(*gather_tasks)
                return time.time() - start_time
        
        duration = await patched_method()
        throughput = len(symbols) * len(timeframes) / duration
        
        results[batch_size] = {
            'duration': duration,
            'throughput': throughput
        }
        
        print(f"Duração: {duration:.2f}s")
        print(f"Throughput: {throughput:.1f} tarefas/s")
    
    # Mostra comparação
    print(f"\n📈 COMPARAÇÃO DE BATCH SIZES:")
    print(f"{'Batch':>5} | {'Tempo':>8} | {'Throughput':>12} | {'Speedup':>8}")
    print("-" * 40)
    
    baseline = results[1]['duration']
    for batch_size, data in results.items():
        speedup = baseline / data['duration']
        print(f"{batch_size:>5} | {data['duration']:>6.2f}s | {data['throughput']:>9.1f}/s | {speedup:>6.1f}x")


async def main():
    parser = argparse.ArgumentParser(description="Teste de paralelização do EnhancedDataCollector")
    parser.add_argument(
        "--symbols",
        type=str,
        default="BTC/USDT,ETH/USDT,ADA/USDT",
        help="Símbolos separados por vírgula"
    )
    parser.add_argument(
        "--timeframes",
        type=str,
        default="1m,5m,15m",
        help="Timeframes separados por vírgula"
    )
    parser.add_argument(
        "--iterations",
        type=int,
        default=3,
        help="Número de iterações para teste"
    )
    parser.add_argument(
        "--compare-sequential",
        action="store_true",
        help="Compara com versão sequencial"
    )
    parser.add_argument(
        "--benchmark",
        action="store_true",
        help="Executa benchmark de batch sizes"
    )
    
    args = parser.parse_args()
    
    symbols = [s.strip() for s in args.symbols.split(",")]
    timeframes = [tf.strip() for tf in args.timeframes.split(",")]
    
    print("🧪 TESTE DE PARALELIZAÇÃO DO ENHANCED DATA COLLECTOR")
    print(f"Configuração: {len(symbols)} símbolos × {len(timeframes)} timeframes = {len(symbols) * len(timeframes)} tarefas")
    
    if args.compare_sequential:
        await test_sequential_vs_parallel(symbols, timeframes)
    elif args.benchmark:
        await benchmark_batch_sizes()
    else:
        await test_parallel_collection(symbols, timeframes, args.iterations)
    
    print("\n✅ Testes concluídos!")


if __name__ == "__main__":
    asyncio.run(main())
