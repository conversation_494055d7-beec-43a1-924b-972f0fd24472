#!/usr/bin/env python3
"""
YAA (YET ANOTHER AGENT) - Correção Simples de Dados QUALIA

Abordagem direta usando CCXT para coletar dados históricos suficientes.
"""

import asyncio
import sys
import os
import json
import logging
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, Any, List
import pandas as pd

# Carregar variáveis de ambiente do .env
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # Fallback manual se python-dotenv não estiver disponível
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file) as f:
            for line in f:
                if line.strip() and not line.startswith("#"):
                    key, value = line.strip().split("=", 1)
                    os.environ[key] = value

import ccxt.async_support as ccxt

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleDataFixer:
    """Corretor simples de dados usando CCXT diretamente."""
    
    def __init__(self):
        self.exchange = None
        
    async def setup_exchange(self):
        """Configura a exchange KuCoin via CCXT."""
        try:
            # Carregar credenciais do .env
            api_key = os.getenv("KUCOIN_API_KEY")
            api_secret = os.getenv("KUCOIN_SECRET_KEY") 
            passphrase = os.getenv("KUCOIN_PASSPHRASE")
            
            if not all([api_key, api_secret, passphrase]):
                logger.warning("⚠️ Credenciais não encontradas, usando modo público")
                api_key = api_secret = passphrase = None
            
            # Configurar exchange
            self.exchange = ccxt.kucoin({
                'apiKey': api_key,
                'secret': api_secret,
                'password': passphrase,
                'sandbox': False,  # Usar produção
                'timeout': 60000,  # 60 segundos
                'enableRateLimit': True,
            })
            
            await self.exchange.load_markets()
            logger.info("✅ Exchange KuCoin configurada com sucesso")
            
        except Exception as e:
            logger.error(f"❌ Erro ao configurar exchange: {e}")
            raise
    
    async def collect_historical_data(self, symbol: str, timeframe: str = "5m", limit: int = 100):
        """Coleta dados históricos para um símbolo específico."""
        try:
            logger.info(f"📊 Coletando dados históricos para {symbol} ({timeframe})")
            
            # Buscar dados OHLCV
            ohlcv_data = await self.exchange.fetch_ohlcv(
                symbol=symbol,
                timeframe=timeframe,
                limit=limit
            )
            
            if not ohlcv_data or len(ohlcv_data) < 20:
                logger.warning(f"⚠️ Dados insuficientes para {symbol}: {len(ohlcv_data) if ohlcv_data else 0} candles")
                return None
            
            # Converter para DataFrame
            df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            logger.info(f"✅ Coletados {len(df)} candles para {symbol}")
            return df
            
        except Exception as e:
            logger.error(f"❌ Erro ao coletar dados para {symbol}: {e}")
            return None
    
    def save_data_to_cache(self, symbol: str, timeframe: str, df: pd.DataFrame):
        """Salva os dados no cache do sistema."""
        try:
            cache_dir = Path("data/cache")
            cache_dir.mkdir(parents=True, exist_ok=True)
            
            # Converter símbolo para formato de arquivo (BTC/USDT -> BTCUSDT)
            file_symbol = symbol.replace("/", "")
            cache_file = cache_dir / f"{file_symbol}_{timeframe}.json"
            
            # Converter DataFrame para formato JSON esperado pelo sistema
            data = {
                "timestamp": df['timestamp'].dt.strftime('%Y-%m-%dT%H:%M:%S').to_dict(),
                "open": df['open'].to_dict(),
                "high": df['high'].to_dict(),
                "low": df['low'].to_dict(),
                "close": df['close'].to_dict(),
                "volume": df['volume'].to_dict()
            }
            
            # Salvar dados
            with open(cache_file, 'w') as f:
                json.dump(data, f)
            
            # Criar arquivo de metadados
            meta_file = cache_dir / f"{file_symbol}_{timeframe}.meta.json"
            meta_data = {
                "expiry": int((datetime.now(timezone.utc) + timedelta(minutes=5)).timestamp())
            }
            
            with open(meta_file, 'w') as f:
                json.dump(meta_data, f)
            
            logger.info(f"✅ Dados salvos no cache: {cache_file}")
            
        except Exception as e:
            logger.error(f"❌ Erro ao salvar dados no cache: {e}")
    
    async def fix_all_symbols(self):
        """Corrige os dados para todos os símbolos principais."""
        try:
            # Símbolos principais do sistema
            symbols = ["BTC/USDT", "SOL/USDT", "ADA/USDT"]
            timeframes = ["5m", "15m"]
            
            logger.info(f"🔧 Corrigindo dados para {len(symbols)} símbolos")
            
            for symbol in symbols:
                for timeframe in timeframes:
                    df = await self.collect_historical_data(symbol, timeframe, limit=100)
                    if df is not None:
                        self.save_data_to_cache(symbol, timeframe, df)
                        
                        # Pequena pausa para não sobrecarregar a API
                        await asyncio.sleep(1.0)
            
            logger.info("🎉 Correção de dados concluída!")
            
        except Exception as e:
            logger.error(f"❌ Erro na correção de dados: {e}")
            raise
    
    async def close(self):
        """Fecha a conexão com a exchange."""
        if self.exchange:
            await self.exchange.close()

async def main():
    """Função principal."""
    logger.info("🌌 YAA - Correção Simples de Dados QUALIA")
    logger.info("=" * 60)
    
    fixer = SimpleDataFixer()
    
    try:
        await fixer.setup_exchange()
        await fixer.fix_all_symbols()
        
        logger.info("✅ Correção concluída com sucesso!")
        
    except Exception as e:
        logger.error(f"❌ Erro na execução: {e}")
        sys.exit(1)
    finally:
        await fixer.close()

if __name__ == "__main__":
    asyncio.run(main())
