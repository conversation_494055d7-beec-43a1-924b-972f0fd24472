#!/usr/bin/env python3
"""
Comparação Simplificada de Estratégias QUALIA com Dados Reais
Teste direto para provar qual estratégia tem melhor performance.
"""

import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any

import pandas as pd
import numpy as np
import requests


class RealDataFetcher:
    """Fetcher simplificado de dados reais."""
    
    def __init__(self):
        self.session = requests.Session()
    
    def fetch_data(self, symbol: str, days: int = 90) -> pd.DataFrame:
        """Busca dados históricos da Binance."""
        try:
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': symbol.replace('/', ''),
                'interval': '1h',
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index().dropna()
            
            # Adiciona indicadores básicos
            df['returns'] = df['close'].pct_change()
            df['sma_20'] = df['close'].rolling(20).mean()
            df['sma_50'] = df['close'].rolling(50).mean()
            df['rsi'] = self._calculate_rsi(df['close'], 14)
            df['volatility'] = df['returns'].rolling(20).std()
            
            print(f"✅ Dados obtidos para {symbol}: {len(df)} candles")
            return df
            
        except Exception as e:
            print(f"❌ Erro ao buscar dados para {symbol}: {e}")
            return pd.DataFrame()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calcula RSI."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))


class StrategySimulator:
    """Simulador simplificado de estratégias."""
    
    def __init__(self, initial_capital: float = 10000):
        self.initial_capital = initial_capital
    
    def qualia_tsvf_strategy(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Estratégia TSVF simplificada - baseada em momentum quântico."""
        signals = []
        
        for i in range(50, len(df)):
            # Simula análise quântica com múltiplos timeframes
            short_momentum = df['close'].iloc[i-10:i].pct_change().mean()
            long_momentum = df['close'].iloc[i-30:i].pct_change().mean()
            
            # Volatility clustering (efeito quântico)
            vol_ratio = df['volatility'].iloc[i] / df['volatility'].iloc[i-20:i].mean()
            
            # RSI divergence
            rsi_signal = (df['rsi'].iloc[i] - 50) / 50  # Normalizado
            
            # Combinação quântica (TSVF-inspired)
            quantum_signal = (
                short_momentum * 0.4 +
                long_momentum * 0.3 +
                (1 - vol_ratio) * 0.2 +  # Baixa volatilidade = sinal forte
                rsi_signal * 0.1
            )
            
            # Threshold de confiança
            if abs(quantum_signal) > 0.02:  # 2% threshold
                signals.append(np.clip(quantum_signal * 10, -1, 1))
            else:
                signals.append(0)
        
        return self._calculate_performance(df.iloc[50:], signals, "QUALIA_TSVF")
    
    def enhanced_quantum_momentum(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Estratégia Quantum Momentum OTIMIZADA com correções aplicadas."""
        signals = []

        for i in range(50, len(df)):
            # 🔧 OTIMIZAÇÃO 1: Filtros de qualidade

            # Filtro de volatilidade (evita mercados choppy)
            vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-20:i].quantile(0.7)

            # Filtro de trend (só opera com trend claro)
            trend_filter = abs(df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i] > 0.02

            # 🚀 FILTRO RSI OTIMIZADO (evita extremos)
            rsi_filter = 32 < df['rsi'].iloc[i] < 68  # OTIMIZADO: Expandido de 35-65 para 32-68

            if not (vol_filter and trend_filter and rsi_filter):
                signals.append(0)
                continue

            # 🔧 OTIMIZAÇÃO 2: Sinais mais robustos
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50

            # Momentum de longo prazo (NOVO)
            long_momentum = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]

            # Combinação quantum otimizada
            signal = (
                price_momentum * 0.4 +
                vol_momentum * 0.2 +
                rsi_momentum * 0.2 +
                long_momentum * 0.2  # NOVO componente
            )

            # 🚀 THRESHOLD OTIMIZADO (2.7% vs 3.0%)
            if abs(signal) > 0.027:  # OTIMIZADO: Reduzido de 0.03 para 0.027
                signals.append(np.clip(signal * 6, -1, 1))  # Multiplicador mantido
            else:
                signals.append(0)

        return self._calculate_performance_with_risk_mgmt(df.iloc[50:], signals, "QUANTUM_MOMENTUM_OPTIMIZED")
    
    def quantum_trend_reversal(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Estratégia Trend Reversal simplificada."""
        signals = []
        
        for i in range(50, len(df)):
            # Detecção de reversão
            price_vs_sma = (df['close'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            
            # RSI extremos
            rsi_extreme = 0
            if df['rsi'].iloc[i] > 70:
                rsi_extreme = -1  # Overbought -> sell
            elif df['rsi'].iloc[i] < 30:
                rsi_extreme = 1   # Oversold -> buy
            
            # Volatility spike (reversão)
            vol_spike = df['volatility'].iloc[i] / df['volatility'].iloc[i-10:i].mean()
            
            # Sinal de reversão
            reversal_signal = (
                -price_vs_sma * 0.4 +  # Contrário à tendência
                rsi_extreme * 0.4 +
                (vol_spike - 1) * 0.2
            )
            
            if abs(reversal_signal) > 0.02:
                signals.append(np.clip(reversal_signal * 6, -1, 1))
            else:
                signals.append(0)
        
        return self._calculate_performance(df.iloc[50:], signals, "TREND_REVERSAL")
    
    def composite_strategy(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Estratégia ROBUSTA com NORMALIZAÇÃO POR ATIVO - Zero Divergência."""
        signals = []

        # NORMALIZAÇÃO POR ATIVO - Calcula estatísticas históricas
        lookback = 200
        start_idx = max(50, lookback)

        # Estatísticas de normalização (últimos 200 períodos)
        trend_history = []
        mean_rev_history = []
        momentum_history = []
        rsi_history = []

        # Coleta histórico para normalização
        for j in range(start_idx-lookback, start_idx):
            if j >= 50:
                trend_raw = (df['sma_20'].iloc[j] - df['sma_50'].iloc[j]) / df['sma_50'].iloc[j]
                mean_rev_raw = -(df['close'].iloc[j] - df['sma_20'].iloc[j]) / df['sma_20'].iloc[j]
                momentum_raw = df['close'].iloc[j-10:j].pct_change().mean()
                rsi_raw = (df['rsi'].iloc[j] - 50) / 50

                trend_history.append(trend_raw)
                mean_rev_history.append(mean_rev_raw)
                momentum_history.append(momentum_raw)
                rsi_history.append(rsi_raw)

        # Estatísticas para normalização Z-score
        trend_mean, trend_std = np.mean(trend_history), np.std(trend_history)
        mean_rev_mean, mean_rev_std = np.mean(mean_rev_history), np.std(mean_rev_history)
        momentum_mean, momentum_std = np.mean(momentum_history), np.std(momentum_history)
        rsi_mean, rsi_std = np.mean(rsi_history), np.std(rsi_history)

        for i in range(start_idx, len(df)):
            # Componentes RAW
            trend_raw = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            mean_rev_raw = -(df['close'].iloc[i] - df['sma_20'].iloc[i]) / df['sma_20'].iloc[i]
            momentum_raw = df['close'].iloc[i-10:i].pct_change().mean()
            rsi_raw = (df['rsi'].iloc[i] - 50) / 50

            # NORMALIZAÇÃO Z-SCORE (remove viés específico do ativo)
            trend_signal = (trend_raw - trend_mean) / (trend_std + 1e-8)
            mean_rev_signal = (mean_rev_raw - mean_rev_mean) / (mean_rev_std + 1e-8)
            momentum_signal = (momentum_raw - momentum_mean) / (momentum_std + 1e-8)
            rsi_signal = (rsi_raw - rsi_mean) / (rsi_std + 1e-8)

            # Clipping para evitar outliers
            trend_signal = np.clip(trend_signal, -3, 3)
            mean_rev_signal = np.clip(mean_rev_signal, -3, 3)
            momentum_signal = np.clip(momentum_signal, -3, 3)
            rsi_signal = np.clip(rsi_signal, -3, 3)

            # Combinação NORMALIZADA (pesos iguais para consistência)
            composite_signal = (
                trend_signal * 0.25 +
                mean_rev_signal * 0.25 +
                momentum_signal * 0.25 +
                rsi_signal * 0.25
            )

            # Threshold baseado em desvios padrão (consistente entre ativos)
            if abs(composite_signal) > 0.5:  # 0.5 desvios padrão
                signals.append(np.clip(composite_signal * 0.3, -1, 1))  # Sinal suave
            else:
                signals.append(0)

        # Preenche início com zeros
        full_signals = [0] * start_idx + signals

        return self._calculate_performance(df.iloc[start_idx:], signals, "ROBUST_NORMALIZED")
    
    def _calculate_performance(self, df: pd.DataFrame, signals: List[float], strategy_name: str) -> Dict[str, Any]:
        """Calcula métricas de performance."""
        if len(signals) != len(df):
            return {'error': 'Mismatch de dados'}
        
        # Simula trading
        positions = signals
        returns = []
        trades = 0
        
        for i in range(1, len(df)):
            if abs(positions[i-1]) > 0.1:  # Posição significativa
                price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
                position_return = positions[i-1] * price_return
                returns.append(position_return)
                trades += 1
            else:
                returns.append(0)
        
        if not returns:
            return {'error': 'Nenhum trade executado'}
        
        returns_series = pd.Series(returns)
        
        # Métricas
        total_return = returns_series.sum()
        volatility = returns_series.std() * np.sqrt(252)
        sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
        
        # Drawdown
        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        # Win rate
        winning_trades = (returns_series > 0).sum()
        win_rate = winning_trades / len(returns_series) if len(returns_series) > 0 else 0
        
        return {
            'strategy': strategy_name,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown * 100,
            'win_rate': win_rate * 100,
            'total_trades': trades,
            'volatility': volatility * 100,
            'avg_position': np.mean(np.abs(positions))
        }

    def _calculate_performance_with_risk_mgmt(self, df: pd.DataFrame, signals: List[float], strategy_name: str) -> Dict[str, Any]:
        """Calcula performance com gestão de risco melhorada (stop-loss e take-profit)."""
        if len(signals) != len(df):
            return {'error': 'Mismatch de dados'}

        positions = signals
        returns = []
        trades = 0
        winning_trades = 0
        losing_trades = 0
        total_wins = 0
        total_losses = 0

        for i in range(1, len(df)):
            if abs(positions[i-1]) > 0.1:  # Posição significativa
                price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]

                # 🔧 GESTÃO DE RISCO: Stop-loss e take-profit
                raw_return = positions[i-1] * price_return

                # 🚀 STOP-LOSS OTIMIZADO: máximo -0.48%
                if raw_return < -0.0048:  # OTIMIZADO: Mais generoso de -0.005 para -0.0048
                    final_return = -0.0048
                # 🚀 TAKE-PROFIT OTIMIZADO: mínimo +0.95%
                elif raw_return > 0.0095:  # OTIMIZADO: Mais agressivo de 0.008 para 0.0095
                    final_return = 0.0095
                else:
                    final_return = raw_return

                returns.append(final_return)
                trades += 1

                if final_return > 0:
                    winning_trades += 1
                    total_wins += final_return
                else:
                    losing_trades += 1
                    total_losses += abs(final_return)
            else:
                returns.append(0)

        if not returns:
            return {'error': 'Nenhum trade executado'}

        returns_series = pd.Series(returns)

        # Métricas básicas
        total_return = returns_series.sum()
        volatility = returns_series.std() * np.sqrt(252)
        sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0

        # Drawdown
        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())

        # Métricas de trading
        win_rate = winning_trades / trades if trades > 0 else 0
        avg_win = total_wins / winning_trades if winning_trades > 0 else 0
        avg_loss = total_losses / losing_trades if losing_trades > 0 else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else 0

        return {
            'strategy': strategy_name,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown * 100,
            'win_rate': win_rate * 100,
            'total_trades': trades,
            'volatility': volatility * 100,
            'avg_position': np.mean(np.abs(positions)),
            'avg_win_pct': avg_win * 100,
            'avg_loss_pct': avg_loss * 100,
            'profit_factor': profit_factor,
            'win_loss_ratio': avg_win / avg_loss if avg_loss > 0 else 0
        }

    def _calculate_performance_with_risk_mgmt(self, df: pd.DataFrame, signals: List[float], strategy_name: str) -> Dict[str, Any]:
        """Performance com gestão de risco melhorada (stop-loss e take-profit)."""
        if len(signals) != len(df):
            return {'error': 'Mismatch de dados'}

        positions = signals
        returns = []
        trades = 0
        winning_trades = 0
        losing_trades = 0
        total_wins = 0
        total_losses = 0

        for i in range(1, len(df)):
            if abs(positions[i-1]) > 0.1:
                price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]

                # 🔧 OTIMIZAÇÃO 4: Stop-loss e take-profit
                raw_return = positions[i-1] * price_return

                # Stop-loss: máximo -0.5%
                if raw_return < -0.005:
                    final_return = -0.005
                # Take-profit: mínimo +0.8%
                elif raw_return > 0.008:
                    final_return = 0.008
                else:
                    final_return = raw_return

                returns.append(final_return)
                trades += 1

                if final_return > 0:
                    winning_trades += 1
                    total_wins += final_return
                else:
                    losing_trades += 1
                    total_losses += abs(final_return)
            else:
                returns.append(0)

        if not returns:
            return {'error': 'Nenhum trade executado'}

        returns_series = pd.Series(returns)

        total_return = returns_series.sum()
        volatility = returns_series.std() * np.sqrt(252)
        sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0

        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())

        win_rate = winning_trades / trades if trades > 0 else 0
        avg_win = total_wins / winning_trades if winning_trades > 0 else 0
        avg_loss = total_losses / losing_trades if losing_trades > 0 else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else 0

        return {
            'strategy': strategy_name,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown * 100,
            'win_rate': win_rate * 100,
            'total_trades': trades,
            'volatility': volatility * 100,
            'avg_position': np.mean(np.abs(positions)),
            'avg_win_pct': avg_win * 100,
            'avg_loss_pct': avg_loss * 100,
            'profit_factor': profit_factor,
            'win_loss_ratio': avg_win / avg_loss if avg_loss > 0 else 0
        }


def run_comparison():
    """Executa comparação simplificada."""
    print("🚀 COMPARAÇÃO SIMPLIFICADA DE ESTRATÉGIAS QUALIA")
    print("=" * 60)
    
    fetcher = RealDataFetcher()
    simulator = StrategySimulator()
    
    symbols = ["BTCUSDT", "ETHUSDT"]
    all_results = []
    
    for symbol in symbols:
        print(f"\n📈 Testando {symbol}...")
        
        df = fetcher.fetch_data(symbol, days=90)
        if df.empty or len(df) < 100:
            print(f"❌ Dados insuficientes para {symbol}")
            continue
        
        # Testa cada estratégia
        strategies = [
            ('QUALIA TSVF', simulator.qualia_tsvf_strategy),
            ('Quantum Momentum OTIMIZADA 🚀', simulator.enhanced_quantum_momentum),
            ('Trend Reversal', simulator.quantum_trend_reversal),
            ('Robust Normalized (Zero Divergência)', simulator.composite_strategy)
        ]
        
        for name, strategy_func in strategies:
            print(f"   🧠 {name}...")
            result = strategy_func(df)
            
            if 'error' not in result:
                result['symbol'] = symbol
                all_results.append(result)
                print(f"      ✅ Return: {result['total_return_pct']:.2f}%, "
                      f"Sharpe: {result['sharpe_ratio']:.3f}, "
                      f"Trades: {result['total_trades']}")
            else:
                print(f"      ❌ {result['error']}")
    
    # Análise final
    if all_results:
        print(f"\n" + "="*60)
        print(f"🏆 RESULTADOS FINAIS")
        print(f"="*60)
        
        df_results = pd.DataFrame(all_results)
        
        # Rankings
        print(f"\n📊 RANKING POR SHARPE RATIO:")
        sharpe_ranking = df_results.groupby('strategy')['sharpe_ratio'].mean().sort_values(ascending=False)
        for i, (strategy, sharpe) in enumerate(sharpe_ranking.items(), 1):
            print(f"   {i}. {strategy}: {sharpe:.3f}")
        
        print(f"\n💰 RANKING POR RETORNO:")
        return_ranking = df_results.groupby('strategy')['total_return_pct'].mean().sort_values(ascending=False)
        for i, (strategy, ret) in enumerate(return_ranking.items(), 1):
            print(f"   {i}. {strategy}: {ret:.2f}%")
        
        print(f"\n🎯 RANKING POR WIN RATE:")
        winrate_ranking = df_results.groupby('strategy')['win_rate'].mean().sort_values(ascending=False)
        for i, (strategy, wr) in enumerate(winrate_ranking.items(), 1):
            print(f"   {i}. {strategy}: {wr:.1f}%")
        
        # Vencedor
        best_strategy = sharpe_ranking.index[0]
        print(f"\n🥇 MELHOR ESTRATÉGIA: {best_strategy}")
        best_metrics = df_results[df_results['strategy'] == best_strategy].mean()
        print(f"   Sharpe Ratio: {best_metrics['sharpe_ratio']:.3f}")
        print(f"   Retorno Médio: {best_metrics['total_return_pct']:.2f}%")
        print(f"   Win Rate: {best_metrics['win_rate']:.1f}%")
        print(f"   Max Drawdown: {best_metrics['max_drawdown_pct']:.2f}%")
        
        # Salva resultados
        output_dir = Path("results/simple_comparison")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = int(time.time())
        with open(output_dir / f"simple_comparison_{timestamp}.json", 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'best_strategy': best_strategy,
                'results': all_results
            }, f, indent=2)
        
        print(f"\n💾 Resultados salvos em results/simple_comparison/")
    
    else:
        print("❌ Nenhum resultado válido")


if __name__ == "__main__":
    run_comparison()
