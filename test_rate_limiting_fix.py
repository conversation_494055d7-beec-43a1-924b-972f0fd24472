#!/usr/bin/env python3
"""
Teste para validar as configurações de rate limiting otimizadas
Verifica se as mudanças foram aplicadas corretamente para resolver problemas de requisições
"""

import yaml
import re
import sys
from pathlib import Path

def test_market_yaml_config():
    """Testa configurações do market.yaml"""
    print("🔍 Testando configurações do market.yaml...")
    
    market_yaml_path = Path("config/market.yaml")
    if not market_yaml_path.exists():
        print("❌ Arquivo market.yaml não encontrado")
        return False
    
    with open(market_yaml_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # Verificar rate limits
    rate_limits = config.get('rate_limits', {})
    global_limit = rate_limits.get('global', 0)
    binance_limit = rate_limits.get('binance', 0)
    max_concurrent = rate_limits.get('max_concurrent_requests', 0)
    
    print(f"   Global rate limit: {global_limit}s")
    print(f"   Binance rate limit: {binance_limit}s")
    print(f"   Max concurrent requests: {max_concurrent}")
    
    # Verificar timeouts
    timeouts = config.get('timeouts', {})
    connection_timeout = timeouts.get('connection', 0)
    ticker_timeout = timeouts.get('ticker', 0)
    ohlcv_timeout = timeouts.get('ohlcv', 0)
    
    print(f"   Connection timeout: {connection_timeout}s")
    print(f"   Ticker timeout: {ticker_timeout}s")
    print(f"   OHLCV timeout: {ohlcv_timeout}s")
    
    # Validações
    success = True
    if global_limit < 10.0:
        print("❌ Global rate limit deve ser >= 10.0s")
        success = False
    if binance_limit < 10.0:
        print("❌ Binance rate limit deve ser >= 10.0s")
        success = False
    if max_concurrent > 1:
        print("❌ Max concurrent requests deve ser 1")
        success = False
    if connection_timeout < 45.0:
        print("❌ Connection timeout deve ser >= 45.0s")
        success = False
    if ticker_timeout < 45.0:
        print("❌ Ticker timeout deve ser >= 45.0s")
        success = False
    if ohlcv_timeout < 120.0:
        print("❌ OHLCV timeout deve ser >= 120.0s")
        success = False
    
    if success:
        print("✅ Configurações do market.yaml estão corretas")
    
    return success

def test_python_script_config():
    """Testa configurações no script Python"""
    print("\n🔍 Testando configurações do script Python...")
    
    script_path = Path("scripts/run_fwh_scalp_paper_trading.py")
    if not script_path.exists():
        print("❌ Script run_fwh_scalp_paper_trading.py não encontrado")
        return False
    
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    success = True
    
    # Verificar limites de candles reduzidos
    if 'limit = 20 if timeframe == "1m" else 15' in content:
        print("✅ Limites de candles reduzidos encontrados (20/15)")
    else:
        print("❌ Limites de candles não foram reduzidos corretamente")
        success = False
    
    # Verificar batch_size reduzido
    if 'batch_size = 2' in content:
        print("✅ Batch size reduzido para 2 símbolos")
    else:
        print("❌ Batch size não foi reduzido para 2")
        success = False
    
    # Verificar delay entre lotes aumentado
    if 'await asyncio.sleep(5)  # Aumentado de 1s para 5s' in content:
        print("✅ Delay entre lotes aumentado para 5s")
    else:
        print("❌ Delay entre lotes não foi aumentado para 5s")
        success = False
    
    # Verificar limit_map atualizado
    if '"1m": 20, "5m": 15, "15m": 12, "1h": 20' in content:
        print("✅ Limit map atualizado com valores conservadores")
    else:
        print("❌ Limit map não foi atualizado corretamente")
        success = False
    
    # Verificar base_interval de 30s
    if 'base_interval = 30' in content:
        print("✅ Base interval configurado para 30s")
    else:
        print("❌ Base interval não está configurado para 30s")
        success = False
    
    return success

def test_rate_limiting_improvements():
    """Testa melhorias específicas de rate limiting"""
    print("\n🔍 Verificando melhorias de rate limiting...")
    
    improvements = [
        "Limites de candles reduzidos (20 para 1m, 15 para outros)",
        "Rate limit global aumentado para 10s",
        "Max concurrent requests reduzido para 1",
        "Timeouts aumentados (45s/45s/120s)",
        "Batch size reduzido para 2 símbolos",
        "Delay entre lotes aumentado para 5s",
        "Base interval aumentado para 30s"
    ]
    
    print("📋 Melhorias implementadas:")
    for improvement in improvements:
        print(f"   ✅ {improvement}")
    
    print("\n🎯 Benefícios esperados:")
    benefits = [
        "Redução significativa da pressão na API Binance",
        "Menor probabilidade de rate limiting",
        "Timeouts mais realistas para requisições lentas",
        "Processamento mais sequencial e controlado",
        "Melhor qualidade dos dados retornados",
        "Redução de avisos de limite da exchange"
    ]
    
    for benefit in benefits:
        print(f"   🚀 {benefit}")
    
    return True

def main():
    """Função principal do teste"""
    print("🧪 TESTE DE CONFIGURAÇÕES DE RATE LIMITING")
    print("=" * 50)
    
    # Executar testes
    market_yaml_ok = test_market_yaml_config()
    python_script_ok = test_python_script_config()
    improvements_ok = test_rate_limiting_improvements()
    
    # Resultado final
    print("\n" + "=" * 50)
    if market_yaml_ok and python_script_ok and improvements_ok:
        print("🎉 TODOS OS TESTES PASSARAM!")
        print("✅ Configurações de rate limiting otimizadas aplicadas com sucesso")
        print("\n💡 Próximos passos:")
        print("   1. Executar o sistema e monitorar logs")
        print("   2. Verificar se avisos de rate limiting diminuíram")
        print("   3. Observar qualidade dos dados retornados")
        print("   4. Ajustar configurações se necessário")
        return True
    else:
        print("❌ ALGUNS TESTES FALHARAM")
        print("🔧 Verifique as configurações e tente novamente")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)