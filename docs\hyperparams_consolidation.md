# QUALIA Hyperparameters Consolidation

## Visão Geral

YAA REFINEMENT: Sistema consolidado de hiperparâmetros que elimina duplicação e inconsistências entre configurações QUALIA. Implementa fonte única de verdade para parâmetros críticos como `price_amplification`, `news_amplification`, `min_confidence` e `pattern_threshold`.

## Arquitetura

### Antes da Consolidação
```
❌ PROBLEMA: Parâmetros espalhados e duplicados
├── config/holographic_enhanced_config.yaml (price_amplification: 5.0)
├── config/strategy_parameters.yaml (min_confidence: 0.6)
├── config/metacognition_defaults.yaml (metacognition_min_confidence: 0.65)
├── src/qualia/consciousness/amplification_calibrator.py (hardcoded: 5.0, 4.0)
└── src/qualia/consciousness/enhanced_data_collector.py (hardcoded: 3.0, 2.0)
```

### Após a Consolidação
```
✅ SOLUÇÃO: Fonte única de verdade com precedência
├── qualia/config/hyperparams.yaml (FONTE ÚNICA)
├── src/qualia/config/hyperparams_loader.py (LOADER COM PRECEDÊNCIA)
└── Precedência: base < env vars < CLI args < API override
```

## Uso

### 1. Carregamento Básico
```python
from qualia.config.hyperparams_loader import load_hyperparams

# Carrega hiperparâmetros com precedência completa
hyperparams = load_hyperparams()

print(f"Price Amplification: {hyperparams.price_amplification}")
print(f"News Amplification: {hyperparams.news_amplification}")
print(f"Min Confidence: {hyperparams.min_confidence}")
print(f"Pattern Threshold: {hyperparams.pattern_threshold}")
```

### 2. Override por Variáveis de Ambiente
```bash
# Define variáveis de ambiente
export QUALIA_PRICE_AMP=7.5
export QUALIA_NEWS_AMP=6.5
export QUALIA_MIN_CONFIDENCE=0.75

# Executa aplicação - valores serão sobrescritos
python main.py
```

### 3. Override por Argumentos CLI
```python
import argparse
from qualia.config.hyperparams_loader import load_hyperparams

parser = argparse.ArgumentParser()
parser.add_argument('--price-amp', type=float, help='Price amplification')
parser.add_argument('--news-amp', type=float, help='News amplification')
parser.add_argument('--min-confidence', type=float, help='Minimum confidence')
parser.add_argument('--risk-profile', choices=['conservative', 'moderate', 'aggressive'])

args = parser.parse_args()
hyperparams = load_hyperparams(args)
```

### 4. Override Dinâmico via API
```python
from qualia.config.hyperparams_loader import get_global_hyperparams_loader

loader = get_global_hyperparams_loader()

# Define override dinâmico (maior precedência)
loader.set_dynamic_override("price_amplification", 8.5)
loader.set_dynamic_override("min_confidence", 0.8)

# Carrega com overrides aplicados
hyperparams = loader.load()

# Limpa overrides quando necessário
loader.clear_dynamic_overrides()
```

### 5. Integração com AmplificationCalibrator
```python
from qualia.consciousness.amplification_calibrator import AmplificationCalibrator
from qualia.config.hyperparams_loader import load_hyperparams

# Método recomendado - usa hiperparâmetros consolidados
hyperparams = load_hyperparams()
calibrator = AmplificationCalibrator(hyperparams=hyperparams)

# Método legado - ainda suportado para compatibilidade
calibrator_legacy = AmplificationCalibrator(
    initial_price_amp=5.0,
    initial_news_amp=4.0
)
```

## Precedência de Configuração

A precedência é aplicada na seguinte ordem (menor para maior):

1. **Valores Base** (`hyperparams.yaml`)
2. **Variáveis de Ambiente** (`QUALIA_*`)
3. **Argumentos CLI** (`--price-amp`, etc.)
4. **Override Dinâmico** (via API)

### Exemplo de Precedência
```yaml
# hyperparams.yaml
amplification:
  initial:
    price_amplification: 5.0  # Base
```

```bash
# Variável de ambiente
export QUALIA_PRICE_AMP=6.0  # Sobrescreve base
```

```bash
# Argumento CLI
python main.py --price-amp 7.0  # Sobrescreve env var
```

```python
# Override dinâmico
loader.set_dynamic_override("price_amplification", 8.0)  # Sobrescreve CLI
```

**Resultado final**: `price_amplification = 8.0`

## Perfis de Risco

O sistema suporta perfis de risco pré-definidos:

```yaml
risk_profiles:
  conservative:
    price_amplification: 2.0
    news_amplification: 1.5
    min_confidence: 0.7
    pattern_threshold: 0.4
    
  moderate:
    price_amplification: 5.0
    news_amplification: 4.0
    min_confidence: 0.6
    pattern_threshold: 0.3
    
  aggressive:
    price_amplification: 8.0
    news_amplification: 7.0
    min_confidence: 0.4
    pattern_threshold: 0.2
```

### Uso de Perfis
```bash
# Via CLI
python main.py --risk-profile aggressive

# Via env var
export QUALIA_RISK_PROFILE=conservative
```

## Validação

O sistema inclui validação automática:

```python
# Validação automática ao carregar
hyperparams = load_hyperparams()  # Lança exceção se inválido

# Validação manual
try:
    hyperparams.validate()
    print("✅ Hiperparâmetros válidos")
except ValueError as e:
    print(f"❌ Erro de validação: {e}")

# Relatório de validação
loader = get_global_hyperparams_loader()
report = loader.get_validation_report()
print(f"Status: {report['status']}")
print(f"Source: {report['source']}")
```

### Regras de Validação
- `price_amplification`: 1.0 ≤ valor ≤ 10.0
- `news_amplification`: 1.0 ≤ valor ≤ 10.0
- `min_confidence`: 0.0 ≤ valor ≤ 1.0
- `pattern_threshold`: 0.0 ≤ valor ≤ 1.0
- Consistência lógica: `price_amplification >= news_amplification * 0.5`

## Migração de Código Legado

### Antes (Código Legado)
```python
# ❌ Valores hardcoded
calibrator = AmplificationCalibrator(
    initial_price_amp=5.0,
    initial_news_amp=4.0,
    learning_rate=0.1
)

# ❌ Configuração duplicada em YAML
# config/my_config.yaml
amplification:
  price_amplification: 5.0
  news_amplification: 4.0
```

### Depois (Consolidado)
```python
# ✅ Usa hiperparâmetros consolidados
calibrator = AmplificationCalibrator()

# ✅ Configuração centralizada
# qualia/config/hyperparams.yaml (fonte única)
```

## Arquivos Afetados pela Consolidação

### Arquivos Limpos (duplicatas removidas)
- `config/holographic_enhanced_config.yaml`
- `config/strategy_parameters.yaml`
- `config/strategy_parameters.json`
- `config/holographic_universe.yaml`
- `config/permissive_config.yaml`
- `config/metacognition_defaults.yaml`

### Arquivos Atualizados (integração)
- `src/qualia/consciousness/amplification_calibrator.py`
- `src/qualia/consciousness/enhanced_data_collector.py`
- `src/qualia/consciousness/real_data_collectors.py`

### Arquivos Criados
- `qualia/config/hyperparams.yaml` (fonte única)
- `src/qualia/config/hyperparams_loader.py` (loader)
- `scripts/cleanup_duplicate_configs.py` (utilitário)

## Benefícios

1. **Eliminação de Duplicação**: Parâmetros centralizados em um único arquivo
2. **Consistência**: Impossível ter valores conflitantes entre arquivos
3. **Flexibilidade**: Múltiplas formas de override (env, CLI, API)
4. **Validação**: Verificação automática de limites e consistência
5. **Auditoria**: Rastreamento da fonte de cada parâmetro
6. **Manutenibilidade**: Mudanças em um local se propagam para todo o sistema

## Próximos Passos

Esta consolidação é a base para as próximas etapas do roadmap:
- **Etapa B**: Telemetry & Metrics (logging detalhado)
- **Etapa C**: Benchmark offline (otimização baseada em dados)
- **Etapa D**: Bayesian Optimizer online (ajuste automático)
- **Etapa E**: Fail-safe bounds (limites de segurança)
- **Etapa F**: Regime-aware presets (adaptação por regime de mercado)
