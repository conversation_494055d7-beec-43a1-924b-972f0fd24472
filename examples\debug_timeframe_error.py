#!/usr/bin/env python3
"""Script para debug do UnboundLocalError do timeframe."""

import traceback
import sys
import os
import asyncio

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from qualia.market.base_integration import CryptoDataFetcher


async def test_fetch_with_debug():
    """Testa fetch_historical_data com debug detalhado."""
    print("=== INICIANDO DEBUG DO TIMEFRAME ERROR ===")

    try:
        print("1. Criando CryptoDataFetcher...")
        config = {"market": {"kucoin_batch_size": 1200}}
        fetcher = CryptoDataFetcher(config=config)
        print("   ✅ CryptoDataFetcher criado")

        print("2. Testando fetch_historical_data...")
        print("   Symbol: BTCUSDT")
        print("   Timeframe: 5m")

        # Teste direto do método problemático
        result = await fetcher.fetch_historical_data("BTCUSDT", "5m")
        print(f"   ✅ SUCCESS: {len(result)} candles coletados")

    except Exception as e:
        print(f"   ❌ ERRO: {e}")
        print(f"   Tipo: {type(e).__name__}")
        print("\n=== STACK TRACE COMPLETO ===")
        traceback.print_exc()
        print("=== FIM DO STACK TRACE ===")


if __name__ == "__main__":
    asyncio.run(test_fetch_with_debug())
