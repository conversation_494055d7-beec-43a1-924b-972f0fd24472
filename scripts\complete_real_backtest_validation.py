#!/usr/bin/env python3
"""
Complete Real Backtest Validation - Sistema completo de validação com dados reais
para eliminar over-fitting artificial no sistema OTOC+FWH.

YAA-COMPLETE-VALIDATION: Implementação completa conforme especificação detalhada.
"""

import sys
import os
import asyncio
import yaml
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from qualia.data.historical_data_manager import download_historical_data
    from real_backtest_integration import run_real_backtest
    from overfitting_detection import OverfittingDetector, validate_optimization_results
    from synthetic_vs_real_analysis import SyntheticVsRealAnalyzer
    from otoc_parameter_optimization import OTOCParameterOptimizer
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ Required modules not available: {e}")
    MODULES_AVAILABLE = False
    sys.exit(1)

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CompleteRealBacktestValidator:
    """
    Validador completo com dados reais para eliminar over-fitting artificial.
    
    YAA-COMPLETE: Implementação de todas as etapas especificadas
    """
    
    def __init__(self, config_path: str = "config/fwh_scalp_config_optimized.yaml"):
        """
        Inicializa validador completo.
        
        Parameters
        ----------
        config_path : str
            Caminho para configuração base
        """
        self.config_path = config_path
        self.config = self._load_config()
        
        # Componentes do sistema
        self.optimizer = OTOCParameterOptimizer(config_path)
        self.overfitting_detector = OverfittingDetector(
            overfitting_threshold=0.2,  # Rigoroso: < 20%
            stability_threshold=0.8     # Rigoroso: > 80%
        )
        self.comparative_analyzer = SyntheticVsRealAnalyzer()
        
        # Configurações de validação
        self.symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'AVAX/USDT', 'LINK/USDT']
        self.timeframes = ['1m', '5m', '15m', '1h']
        self.validation_months = 6  # Mínimo 6 meses conforme especificação
        
        # Critérios de validação final (conforme especificação)
        self.final_criteria = {
            'min_real_sharpe': 1.2,
            'max_real_drawdown': 0.04,  # 4%
            'min_consistency_folds': 4,  # 4 de 5 folds
            'max_overfitting_rate': 0.2,  # 20%
            'min_otoc_effectiveness': 0.1  # 10% redução de drawdown
        }
        
        logger.info("🚀 Complete Real Backtest Validator initialized")
        logger.info(f"   Symbols: {self.symbols}")
        logger.info(f"   Validation period: {self.validation_months} months")
        logger.info(f"   Final criteria: {self.final_criteria}")
    
    def _load_config(self) -> Dict[str, Any]:
        """Carrega configuração base."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {self.config_path}")
            return {}
    
    async def step1_download_historical_data(self) -> Dict[str, Dict[str, Any]]:
        """
        ETAPA 1: Integração de dados históricos reais.
        
        YAA-STEP1: Download automático, cache local, validação de qualidade
        """
        logger.info("📥 ETAPA 1: Downloading historical data...")
        
        try:
            # Download dados históricos (6 meses mínimo)
            historical_data = await download_historical_data(
                symbols=self.symbols,
                timeframes=self.timeframes,
                months_back=self.validation_months,
                cache_dir="data/cache"
            )
            
            # Validar qualidade dos dados
            data_quality = {}
            total_periods = 0
            valid_symbols = []
            
            for symbol in self.symbols:
                if symbol in historical_data:
                    symbol_periods = 0
                    symbol_valid = True
                    
                    for timeframe in self.timeframes:
                        if timeframe in historical_data[symbol]:
                            df = historical_data[symbol][timeframe]
                            periods = len(df)
                            symbol_periods += periods
                            total_periods += periods
                            
                            # Validação básica
                            if periods < 1000:  # Mínimo 1000 períodos
                                logger.warning(f"⚠️ {symbol} {timeframe}: Only {periods} periods")
                                symbol_valid = False
                    
                    data_quality[symbol] = {
                        'total_periods': symbol_periods,
                        'is_valid': symbol_valid
                    }
                    
                    if symbol_valid:
                        valid_symbols.append(symbol)
            
            logger.info(f"✅ ETAPA 1 completed:")
            logger.info(f"   Total periods downloaded: {total_periods:,}")
            logger.info(f"   Valid symbols: {len(valid_symbols)}/{len(self.symbols)}")
            logger.info(f"   Data quality: {data_quality}")
            
            # Atualizar lista de símbolos válidos
            self.symbols = valid_symbols
            
            return {
                'historical_data': historical_data,
                'data_quality': data_quality,
                'total_periods': total_periods,
                'valid_symbols': valid_symbols
            }
            
        except Exception as e:
            logger.error(f"❌ ETAPA 1 failed: {e}")
            raise
    
    async def step2_optimize_parameters(self, n_samples: int = 50) -> List[Dict[str, Any]]:
        """
        ETAPA 2: Otimização de parâmetros com motor real.
        
        YAA-STEP2: Otimização usando backtest real, não sintético
        """
        logger.info(f"🎯 ETAPA 2: Parameter optimization ({n_samples} samples)...")
        
        try:
            # Executar otimização (agora usa motor real automaticamente)
            optimization_results = self.optimizer.grid_search_optimization(n_samples)
            
            # Extrair top 20 para validação rigorosa
            top_20 = optimization_results[:20]
            
            logger.info(f"✅ ETAPA 2 completed:")
            logger.info(f"   Total configurations tested: {len(optimization_results)}")
            logger.info(f"   Top 20 selected for validation")
            logger.info(f"   Best score: {top_20[0].score:.4f}")
            
            # Salvar resultados
            self.optimizer.save_results("logs/real_optimization_results.json")
            
            return [
                {
                    'parameters': result.parameters,
                    'score': result.score,
                    'metrics': result.metrics
                }
                for result in top_20
            ]
            
        except Exception as e:
            logger.error(f"❌ ETAPA 2 failed: {e}")
            raise
    
    async def step3_cross_validation(self, top_configurations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        ETAPA 3: Validação cruzada temporal rigorosa.
        
        YAA-STEP3: Walk-forward analysis com splits de 3 meses
        """
        logger.info(f"🔍 ETAPA 3: Rigorous cross-validation ({len(top_configurations)} configs)...")
        
        try:
            # Definir período de validação
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.validation_months * 30)
            
            # Função de backtest para validação
            async def backtest_function(params, start, end):
                config = self.optimizer.create_config_variant(params)
                result = await run_real_backtest(
                    config=config,
                    start_date=start,
                    end_date=end,
                    symbols=self.symbols,
                    engine_type="real"
                )
                
                return {
                    'metrics': {
                        'sharpe_ratio': result.sharpe_ratio,
                        'max_drawdown': result.max_drawdown,
                        'win_rate': result.win_rate,
                        'profit_factor': result.profit_factor,
                        'total_trades': result.total_trades
                    }
                }
            
            # Executar validação cruzada para cada configuração
            validated_configs = []
            
            for i, config_data in enumerate(top_configurations):
                logger.info(f"   Validating configuration {i+1}/{len(top_configurations)}")
                
                try:
                    # Validação cruzada temporal
                    validation_result = await self.overfitting_detector.cross_validate_parameters(
                        parameters=config_data['parameters'],
                        backtest_function=backtest_function,
                        start_date=start_date,
                        end_date=end_date,
                        n_splits=5  # 5 folds de 3 meses cada
                    )
                    
                    # Walk-forward analysis
                    walkforward_result = await self.overfitting_detector.walk_forward_analysis(
                        parameters=config_data['parameters'],
                        backtest_function=backtest_function,
                        start_date=start_date,
                        end_date=end_date,
                        window_months=6,  # Janela de 6 meses
                        step_months=1     # Passo de 1 mês
                    )
                    
                    # Aplicar critérios rigorosos
                    is_valid = (
                        not validation_result.is_overfitted and
                        validation_result.stability_score >= 0.8 and
                        walkforward_result.get('is_robust', False)
                    )
                    
                    if is_valid:
                        validated_configs.append({
                            **config_data,
                            'validation_result': validation_result,
                            'walkforward_result': walkforward_result,
                            'is_validated': True
                        })
                        
                        logger.info(f"      ✅ VALIDATED: Stability {validation_result.stability_score:.3f}")
                    else:
                        logger.info(f"      ❌ REJECTED: Overfitting or instability detected")
                
                except Exception as e:
                    logger.error(f"      ❌ Validation failed: {e}")
            
            logger.info(f"✅ ETAPA 3 completed:")
            logger.info(f"   Configurations validated: {len(validated_configs)}/{len(top_configurations)}")
            
            # Salvar resultados de validação
            self.overfitting_detector.save_validation_results("logs/cross_validation_results.json")
            
            return validated_configs
            
        except Exception as e:
            logger.error(f"❌ ETAPA 3 failed: {e}")
            raise
    
    async def step4_comparative_analysis(self, validated_configs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        ETAPA 4: Análise comparativa sintético vs real.
        
        YAA-STEP4: Detectar discrepâncias > 30% (over-fitting severo)
        """
        logger.info(f"🔬 ETAPA 4: Synthetic vs Real analysis ({len(validated_configs)} configs)...")
        
        try:
            # Período para comparação
            end_date = datetime.now()
            start_date = end_date - timedelta(days=90)  # 3 meses
            
            # Executar análise comparativa
            comparison_results = await self.comparative_analyzer.compare_engines(
                parameters_list=[config['parameters'] for config in validated_configs],
                start_date=start_date,
                end_date=end_date,
                symbols=self.symbols[:3],  # Usar apenas 3 símbolos para comparação
                max_concurrent=2
            )
            
            # Filtrar configurações robustas (divergência < 15%)
            robust_configs = []
            
            for i, comparison in enumerate(comparison_results):
                config_data = validated_configs[i]
                
                if comparison.is_robust and not comparison.has_severe_overfitting:
                    robust_configs.append({
                        **config_data,
                        'comparison_result': comparison,
                        'is_robust': True,
                        'divergence': comparison.overall_divergence
                    })
                    
                    logger.info(f"   ✅ ROBUST: Config {i+1} - Divergence {comparison.overall_divergence:.1%}")
                else:
                    status = "SEVERE OVERFITTING" if comparison.has_severe_overfitting else "MODERATE"
                    logger.info(f"   ❌ {status}: Config {i+1} - Divergence {comparison.overall_divergence:.1%}")
            
            logger.info(f"✅ ETAPA 4 completed:")
            logger.info(f"   Robust configurations: {len(robust_configs)}/{len(validated_configs)}")
            
            # Gerar relatório comparativo
            report = self.comparative_analyzer.generate_comparison_report()
            logger.info(f"\n{report}")
            
            # Salvar resultados
            self.comparative_analyzer.save_comparison_results("logs/comparative_analysis_results.json")
            
            return robust_configs
            
        except Exception as e:
            logger.error(f"❌ ETAPA 4 failed: {e}")
            raise
    
    async def step5_final_validation(self, robust_configs: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        ETAPA 5: Validação final com critérios rigorosos.
        
        YAA-STEP5: Aplicar critérios finais conforme especificação
        """
        logger.info(f"🏆 ETAPA 5: Final validation ({len(robust_configs)} configs)...")
        
        try:
            final_candidates = []
            
            for i, config_data in enumerate(robust_configs):
                logger.info(f"   Evaluating final candidate {i+1}/{len(robust_configs)}")
                
                # Executar backtest final com período completo
                end_date = datetime.now()
                start_date = end_date - timedelta(days=self.validation_months * 30)
                
                config = self.optimizer.create_config_variant(config_data['parameters'])
                final_result = await run_real_backtest(
                    config=config,
                    start_date=start_date,
                    end_date=end_date,
                    symbols=self.symbols,
                    engine_type="real"
                )
                
                # Aplicar critérios finais
                meets_criteria = (
                    final_result.sharpe_ratio >= self.final_criteria['min_real_sharpe'] and
                    abs(final_result.max_drawdown) <= self.final_criteria['max_real_drawdown'] and
                    config_data['validation_result'].stability_score >= 0.8 and
                    config_data['validation_result'].overfitting_ratio <= self.final_criteria['max_overfitting_rate'] and
                    final_result.otoc_effectiveness_ratio >= self.final_criteria['min_otoc_effectiveness']
                )
                
                if meets_criteria:
                    final_candidates.append({
                        **config_data,
                        'final_result': final_result,
                        'meets_all_criteria': True,
                        'final_score': self._calculate_final_score(final_result, config_data)
                    })
                    
                    logger.info(f"      ✅ MEETS ALL CRITERIA:")
                    logger.info(f"         Sharpe: {final_result.sharpe_ratio:.3f} >= {self.final_criteria['min_real_sharpe']}")
                    logger.info(f"         Drawdown: {abs(final_result.max_drawdown):.1%} <= {self.final_criteria['max_real_drawdown']:.1%}")
                    logger.info(f"         OTOC Effectiveness: {final_result.otoc_effectiveness_ratio:.1%}")
                else:
                    logger.info(f"      ❌ FAILED FINAL CRITERIA")
            
            if not final_candidates:
                logger.error("❌ No configurations passed final validation!")
                return None
            
            # Selecionar melhor configuração
            best_config = max(final_candidates, key=lambda x: x['final_score'])
            
            logger.info(f"✅ ETAPA 5 completed:")
            logger.info(f"   Final candidates: {len(final_candidates)}")
            logger.info(f"   Best configuration selected with score: {best_config['final_score']:.4f}")
            
            # Salvar configuração final
            await self._save_final_configuration(best_config)
            
            return best_config
            
        except Exception as e:
            logger.error(f"❌ ETAPA 5 failed: {e}")
            raise
    
    def _calculate_final_score(self, final_result, config_data) -> float:
        """Calcula score final combinando múltiplos fatores."""
        return (
            0.4 * final_result.sharpe_ratio +
            0.3 * (1 - abs(final_result.max_drawdown)) +
            0.2 * final_result.win_rate +
            0.1 * final_result.otoc_effectiveness_ratio
        )
    
    async def _save_final_configuration(self, best_config: Dict[str, Any]):
        """Salva configuração final otimizada."""
        final_config = self.optimizer.create_config_variant(best_config['parameters'])
        
        # Adicionar metadados de validação
        final_config['validation_metadata'] = {
            'validation_date': datetime.now().isoformat(),
            'validation_method': 'complete_real_backtest',
            'final_score': best_config['final_score'],
            'sharpe_ratio': best_config['final_result'].sharpe_ratio,
            'max_drawdown': best_config['final_result'].max_drawdown,
            'win_rate': best_config['final_result'].win_rate,
            'otoc_effectiveness': best_config['final_result'].otoc_effectiveness_ratio,
            'overfitting_ratio': best_config['validation_result'].overfitting_ratio,
            'stability_score': best_config['validation_result'].stability_score,
            'divergence': best_config['divergence'],
            'symbols_tested': self.symbols,
            'validation_months': self.validation_months
        }
        
        # Salvar configuração
        output_path = "config/fwh_scalp_config_real_validated.yaml"
        with open(output_path, 'w') as f:
            yaml.dump(final_config, f, default_flow_style=False, indent=2)
        
        logger.info(f"💾 Final validated configuration saved: {output_path}")
    
    async def run_complete_validation(self, n_optimization_samples: int = 50) -> Optional[Dict[str, Any]]:
        """
        Executa validação completa em 5 etapas.
        
        YAA-COMPLETE: Pipeline completo conforme especificação
        """
        logger.info("🚀 STARTING COMPLETE REAL BACKTEST VALIDATION")
        logger.info("=" * 60)
        
        try:
            # ETAPA 1: Download de dados históricos
            data_result = await self.step1_download_historical_data()
            
            # ETAPA 2: Otimização de parâmetros
            top_configs = await self.step2_optimize_parameters(n_optimization_samples)
            
            # ETAPA 3: Validação cruzada rigorosa
            validated_configs = await self.step3_cross_validation(top_configs)
            
            if not validated_configs:
                logger.error("❌ No configurations passed cross-validation!")
                return None
            
            # ETAPA 4: Análise comparativa
            robust_configs = await self.step4_comparative_analysis(validated_configs)
            
            if not robust_configs:
                logger.error("❌ No configurations passed comparative analysis!")
                return None
            
            # ETAPA 5: Validação final
            final_config = await self.step5_final_validation(robust_configs)
            
            if final_config:
                logger.info("🎉 COMPLETE VALIDATION SUCCESSFUL!")
                logger.info(f"   Final Sharpe Ratio: {final_config['final_result'].sharpe_ratio:.3f}")
                logger.info(f"   Final Max Drawdown: {abs(final_config['final_result'].max_drawdown):.1%}")
                logger.info(f"   OTOC Effectiveness: {final_config['final_result'].otoc_effectiveness_ratio:.1%}")
                logger.info(f"   Configuration saved: config/fwh_scalp_config_real_validated.yaml")
            else:
                logger.error("❌ COMPLETE VALIDATION FAILED!")
            
            return final_config
            
        except Exception as e:
            logger.error(f"❌ Complete validation failed: {e}")
            raise


async def main():
    """Executa validação completa."""
    print("🚀 COMPLETE REAL BACKTEST VALIDATION")
    print("=" * 60)
    
    # Inicializar validador
    validator = CompleteRealBacktestValidator()
    
    # Executar validação completa
    try:
        result = await validator.run_complete_validation(n_optimization_samples=30)
        
        if result:
            print("\n🎉 SUCCESS: Real backtest validation completed!")
            print("   Over-fitting artificial eliminated ✅")
            print("   Configuration ready for production ✅")
        else:
            print("\n❌ FAILED: No configuration passed all validation criteria")
            
    except Exception as e:
        print(f"\n💥 CRITICAL ERROR: {e}")
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
