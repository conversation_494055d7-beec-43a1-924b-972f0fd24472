"""
QUALIA D-05: Hot-reload & Rollback - Teste Simples
==================================================

Teste básico para validar funcionamento do sistema D-05.
"""

import asyncio
import json
import tempfile
from pathlib import Path
import shutil
import sys

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import yaml
from qualia.config.hot_reload import ConfigurationHotReloader, ReloadStatus

async def test_basic_functionality():
    """Teste básico do hot-reload."""
    print("🧪 Teste Básico D-05: Hot-reload & Rollback")
    print("=" * 50)
    
    # Criar diretório temporário
    temp_dir = Path(tempfile.mkdtemp())
    config_file = temp_dir / "test_config.yaml"
    backup_dir = temp_dir / "backups"
    
    try:
        # Configuração inicial
        initial_config = {
            "optimization": {
                "n_trials_per_cycle": 25,
                "lookback_hours": 24
            },
            "pruning": {
                "strategy": "ADAPTIVE"
            }
        }
        
        # Salvar configuração inicial
        with open(config_file, 'w') as f:
            yaml.dump(initial_config, f)
            
        print(f"📁 Arquivo de teste criado: {config_file}")
        
        # Criar hot-reloader
        hot_reloader = ConfigurationHotReloader(
            watched_paths=[str(config_file)],
            backup_dir=str(backup_dir),
            max_versions=5
        )
        
        print("🔄 Hot-reloader criado")
        
        # Teste 1: Criar versão inicial
        hot_reloader._create_initial_versions()
        versions = hot_reloader.get_version_history(str(config_file))
        assert len(versions) == 1, f"Esperado 1 versão, encontrado {len(versions)}"
        print("✅ Teste 1: Versão inicial criada")
        
        # Teste 2: Validação de configuração válida
        result = await hot_reloader._validate_config(str(config_file), initial_config)
        assert result.is_valid, f"Configuração deveria ser válida: {result.error_message}"
        print("✅ Teste 2: Validação bem-sucedida")
        
        # Teste 3: Reload de configuração
        new_config = initial_config.copy()
        new_config["optimization"]["n_trials_per_cycle"] = 50
        
        with open(config_file, 'w') as f:
            yaml.dump(new_config, f)
            
        event = await hot_reloader.reload_config(str(config_file))
        assert event.status == ReloadStatus.SUCCESS, f"Reload falhou: {event.error_message}"
        print("✅ Teste 3: Reload bem-sucedido")
        
        # Teste 4: Verificar nova versão
        versions = hot_reloader.get_version_history(str(config_file))
        assert len(versions) == 2, f"Esperado 2 versões, encontrado {len(versions)}"
        assert versions[-1].config_data["optimization"]["n_trials_per_cycle"] == 50
        print("✅ Teste 4: Nova versão criada")
        
        # Teste 5: Rollback manual
        first_version = versions[0]
        rollback_event = await hot_reloader.manual_rollback(str(config_file), first_version.version_id)
        assert rollback_event.status == ReloadStatus.ROLLBACK, f"Rollback falhou: {rollback_event.error_message}"
        print("✅ Teste 5: Rollback bem-sucedido")
        
        # Teste 6: Verificar arquivo foi restaurado
        with open(config_file, 'r') as f:
            restored_config = yaml.safe_load(f)
        assert restored_config["optimization"]["n_trials_per_cycle"] == 25
        print("✅ Teste 6: Arquivo restaurado corretamente")
        
        print("\n🎉 TODOS OS TESTES BÁSICOS PASSARAM!")
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
        print(f"🧹 Cleanup concluído")

if __name__ == "__main__":
    success = asyncio.run(test_basic_functionality())
    exit(0 if success else 1)
