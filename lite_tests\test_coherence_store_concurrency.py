import json
import threading
import sys
import types

event_bus_stub = types.ModuleType("event_bus_stub")


class SimpleEventBus:
    def __init__(self) -> None:
        self.subs = {}

    def subscribe(self, name, handler):
        self.subs.setdefault(name, []).append(handler)

    def publish(self, name, payload):
        for h in self.subs.get(name, []):
            h(payload)


event_bus_stub.SimpleEventBus = SimpleEventBus
sys.modules.setdefault("qualia.memory.event_bus", event_bus_stub)

from qualia.persistence.coherence_store import CoherenceStore


class DummyBus:
    def __init__(self) -> None:
        self.subs = {}

    def subscribe(self, name, handler):
        self.subs.setdefault(name, []).append(handler)

    def publish(self, name, payload):
        for h in self.subs.get(name, []):
            h(payload)


def test_coherence_store_concurrent_writes(tmp_path):
    bus = DummyBus()
    path = tmp_path / "hist.jsonl"
    CoherenceStore(bus, path=str(path))

    def writer(i: int) -> None:
        bus.publish("nexus.cross_modal_coherence", {"coherence": i})

    threads = [threading.Thread(target=writer, args=(i,)) for i in range(50)]
    for t in threads:
        t.start()
    for t in threads:
        t.join()

    lines = path.read_text(encoding="utf-8").splitlines()
    assert len(lines) == 50
    vals = {json.loads(line)["coherence"] for line in lines}
    assert vals == set(range(50))
