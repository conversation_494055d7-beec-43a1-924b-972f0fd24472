"""Connection pool with load balancing for exchange integrations.

This module provides sophisticated connection pooling and load balancing
capabilities for managing multiple connections to exchanges and external APIs.

Features:
- Multiple connection pool strategies (round-robin, least-connections, weighted)
- Health monitoring and automatic failover
- Connection lifecycle management
- Load balancing with performance-based routing
- Connection warming and preemptive scaling
- Comprehensive metrics and monitoring
"""

from __future__ import annotations

import asyncio
import time
import random
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from collections import deque, defaultdict
import weakref

from qualia.utils.logger import get_logger
from qualia.utils.network_resilience import EnhancedCircuitBreaker

logger = get_logger(__name__)


class LoadBalancingStrategy(Enum):
    """Load balancing strategies for connection selection."""
    ROUND_ROBIN = "round_robin"
    LEAST_CONNECTIONS = "least_connections"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    PERFORMANCE_BASED = "performance_based"
    RANDOM = "random"


class ConnectionState(Enum):
    """Connection states for lifecycle management."""
    INITIALIZING = "initializing"
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    CLOSED = "closed"


@dataclass
class ConnectionMetrics:
    """Metrics for a single connection."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    active_requests: int = 0
    average_response_time: float = 0.0
    last_used: float = 0.0
    created_at: float = field(default_factory=time.time)
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests
    
    @property
    def load_score(self) -> float:
        """Calculate load score for load balancing."""
        # Lower is better: combines active requests and response time
        base_score = self.active_requests * 10
        response_penalty = min(self.average_response_time * 5, 50)
        success_bonus = (1.0 - self.success_rate) * 100
        
        return base_score + response_penalty + success_bonus


@dataclass
class PooledConnection:
    """Wrapper for a pooled connection with metadata."""
    connection: Any
    connection_id: str
    state: ConnectionState
    metrics: ConnectionMetrics
    weight: float = 1.0
    max_concurrent: int = 10
    created_at: float = field(default_factory=time.time)
    last_health_check: float = 0.0
    
    def __post_init__(self):
        self.circuit_breaker = EnhancedCircuitBreaker(
            name=f"conn_{self.connection_id}",
            fail_threshold=5,
            recovery_timeout=30.0,
            adaptive_recovery=True
        )
    
    @property
    def is_available(self) -> bool:
        """Check if connection is available for use."""
        return (
            self.state in (ConnectionState.HEALTHY, ConnectionState.DEGRADED) and
            self.metrics.active_requests < self.max_concurrent and
            self.circuit_breaker.allow_request()
        )
    
    @property
    def utilization(self) -> float:
        """Calculate connection utilization percentage."""
        return (self.metrics.active_requests / self.max_concurrent) * 100


class ConnectionPool:
    """Advanced connection pool with load balancing and health monitoring.
    
    This pool provides:
    - Multiple load balancing strategies
    - Automatic health monitoring and failover
    - Connection lifecycle management
    - Performance-based routing
    - Preemptive scaling and connection warming
    """
    
    def __init__(
        self,
        pool_name: str,
        connection_factory: Callable[[], Any],
        min_connections: int = 2,
        max_connections: int = 10,
        load_balancing_strategy: LoadBalancingStrategy = LoadBalancingStrategy.PERFORMANCE_BASED,
        health_check_interval: float = 30.0,
        connection_timeout: float = 30.0,
        max_idle_time: float = 300.0,
        enable_preemptive_scaling: bool = True,
        target_utilization: float = 0.7,
    ):
        self.pool_name = pool_name
        self.connection_factory = connection_factory
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.load_balancing_strategy = load_balancing_strategy
        self.health_check_interval = health_check_interval
        self.connection_timeout = connection_timeout
        self.max_idle_time = max_idle_time
        self.enable_preemptive_scaling = enable_preemptive_scaling
        self.target_utilization = target_utilization
        
        # Connection storage
        self.connections: Dict[str, PooledConnection] = {}
        self.connection_order: deque = deque()  # For round-robin
        self.last_round_robin_index = 0
        
        # Pool state
        self.is_initialized = False
        self.is_closed = False
        self._lock = asyncio.Lock()
        
        # Background tasks
        self.health_check_task: Optional[asyncio.Task] = None
        self.scaling_task: Optional[asyncio.Task] = None
        
        # Statistics
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'connections_created': 0,
            'connections_destroyed': 0,
            'load_balancing_decisions': 0,
            'health_checks_performed': 0,
            'scaling_events': 0
        }
        
        # Performance tracking
        self.request_history: deque = deque(maxlen=1000)
        self.utilization_history: deque = deque(maxlen=100)

    async def initialize(self) -> None:
        """Initialize the connection pool."""
        if self.is_initialized:
            return
        
        async with self._lock:
            if self.is_initialized:
                return
            
            logger.info(f"Initializing connection pool '{self.pool_name}'")
            
            # Create initial connections
            for i in range(self.min_connections):
                await self._create_connection()
            
            # Start background tasks
            self.health_check_task = asyncio.create_task(self._health_check_loop())
            
            if self.enable_preemptive_scaling:
                self.scaling_task = asyncio.create_task(self._scaling_loop())
            
            self.is_initialized = True
            logger.info(f"Connection pool '{self.pool_name}' initialized with {len(self.connections)} connections")

    async def get_connection(self, timeout: Optional[float] = None) -> PooledConnection:
        """Get a connection from the pool using the configured load balancing strategy.
        
        Parameters
        ----------
        timeout : float, optional
            Timeout for acquiring a connection
            
        Returns
        -------
        PooledConnection
            Available connection from the pool
            
        Raises
        ------
        asyncio.TimeoutError
            If no connection becomes available within timeout
        RuntimeError
            If pool is closed or no healthy connections available
        """
        if not self.is_initialized:
            await self.initialize()
        
        if self.is_closed:
            raise RuntimeError(f"Connection pool '{self.pool_name}' is closed")
        
        timeout = timeout or self.connection_timeout
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            async with self._lock:
                connection = await self._select_connection()
                if connection:
                    # Mark connection as in use
                    connection.metrics.active_requests += 1
                    connection.metrics.last_used = time.time()
                    
                    self.stats['total_requests'] += 1
                    self.stats['load_balancing_decisions'] += 1
                    
                    logger.debug(
                        f"Acquired connection {connection.connection_id} from pool '{self.pool_name}' "
                        f"(active: {connection.metrics.active_requests}/{connection.max_concurrent})"
                    )
                    
                    return connection
            
            # No connection available, wait a bit and try again
            await asyncio.sleep(0.1)
        
        raise asyncio.TimeoutError(f"Failed to acquire connection from pool '{self.pool_name}' within {timeout}s")

    async def return_connection(
        self, 
        connection: PooledConnection, 
        success: bool = True,
        response_time: Optional[float] = None
    ) -> None:
        """Return a connection to the pool and update its metrics.
        
        Parameters
        ----------
        connection : PooledConnection
            Connection to return to the pool
        success : bool
            Whether the operation using this connection was successful
        response_time : float, optional
            Response time for the operation
        """
        async with self._lock:
            if connection.connection_id not in self.connections:
                logger.warning(f"Attempted to return unknown connection {connection.connection_id}")
                return
            
            # Update connection metrics
            connection.metrics.active_requests = max(0, connection.metrics.active_requests - 1)
            connection.metrics.total_requests += 1
            
            if success:
                connection.metrics.successful_requests += 1
                connection.circuit_breaker.record_success()
                self.stats['successful_requests'] += 1
            else:
                connection.metrics.failed_requests += 1
                connection.circuit_breaker.record_failure()
                self.stats['failed_requests'] += 1
            
            # Update average response time
            if response_time is not None:
                current_avg = connection.metrics.average_response_time
                total_requests = connection.metrics.total_requests
                
                # Exponential moving average
                if total_requests == 1:
                    connection.metrics.average_response_time = response_time
                else:
                    alpha = 0.1  # Smoothing factor
                    connection.metrics.average_response_time = (
                        alpha * response_time + (1 - alpha) * current_avg
                    )
            
            # Record request in history
            self.request_history.append({
                'timestamp': time.time(),
                'connection_id': connection.connection_id,
                'success': success,
                'response_time': response_time,
                'active_requests': connection.metrics.active_requests
            })
            
            logger.debug(
                f"Returned connection {connection.connection_id} to pool '{self.pool_name}' "
                f"(success: {success}, active: {connection.metrics.active_requests})"
            )

    async def _select_connection(self) -> Optional[PooledConnection]:
        """Select a connection based on the configured load balancing strategy."""
        available_connections = [
            conn for conn in self.connections.values() 
            if conn.is_available
        ]
        
        if not available_connections:
            # Try to create a new connection if under limit
            if len(self.connections) < self.max_connections:
                new_conn = await self._create_connection()
                if new_conn and new_conn.is_available:
                    available_connections = [new_conn]
            
            if not available_connections:
                return None
        
        if self.load_balancing_strategy == LoadBalancingStrategy.ROUND_ROBIN:
            return self._round_robin_selection(available_connections)
        elif self.load_balancing_strategy == LoadBalancingStrategy.LEAST_CONNECTIONS:
            return self._least_connections_selection(available_connections)
        elif self.load_balancing_strategy == LoadBalancingStrategy.WEIGHTED_ROUND_ROBIN:
            return self._weighted_round_robin_selection(available_connections)
        elif self.load_balancing_strategy == LoadBalancingStrategy.PERFORMANCE_BASED:
            return self._performance_based_selection(available_connections)
        elif self.load_balancing_strategy == LoadBalancingStrategy.RANDOM:
            return random.choice(available_connections)
        else:
            return available_connections[0]

    def _round_robin_selection(self, connections: List[PooledConnection]) -> PooledConnection:
        """Select connection using round-robin strategy."""
        if not connections:
            return None
        
        self.last_round_robin_index = (self.last_round_robin_index + 1) % len(connections)
        return connections[self.last_round_robin_index]

    def _least_connections_selection(self, connections: List[PooledConnection]) -> PooledConnection:
        """Select connection with least active connections."""
        return min(connections, key=lambda c: c.metrics.active_requests)

    def _weighted_round_robin_selection(self, connections: List[PooledConnection]) -> PooledConnection:
        """Select connection using weighted round-robin based on connection weights."""
        total_weight = sum(c.weight for c in connections)
        if total_weight == 0:
            return random.choice(connections)
        
        # Weighted random selection
        r = random.uniform(0, total_weight)
        cumulative_weight = 0
        
        for connection in connections:
            cumulative_weight += connection.weight
            if r <= cumulative_weight:
                return connection
        
        return connections[-1]  # Fallback

    def _performance_based_selection(self, connections: List[PooledConnection]) -> PooledConnection:
        """Select connection based on performance metrics (load score)."""
        return min(connections, key=lambda c: c.metrics.load_score)

    async def _create_connection(self) -> Optional[PooledConnection]:
        """Create a new connection and add it to the pool."""
        if len(self.connections) >= self.max_connections:
            return None
        
        try:
            connection_id = f"{self.pool_name}_{len(self.connections)}_{int(time.time())}"
            
            logger.debug(f"Creating new connection {connection_id}")
            raw_connection = await asyncio.wait_for(
                self.connection_factory(), 
                timeout=self.connection_timeout
            )
            
            pooled_connection = PooledConnection(
                connection=raw_connection,
                connection_id=connection_id,
                state=ConnectionState.HEALTHY,
                metrics=ConnectionMetrics()
            )
            
            self.connections[connection_id] = pooled_connection
            self.connection_order.append(connection_id)
            
            self.stats['connections_created'] += 1
            
            logger.info(f"Created connection {connection_id} for pool '{self.pool_name}'")
            return pooled_connection
            
        except Exception as e:
            logger.error(f"Failed to create connection for pool '{self.pool_name}': {e}")
            return None

    async def _health_check_loop(self) -> None:
        """Background task for periodic health checks."""
        while not self.is_closed:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_checks()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health check loop for pool '{self.pool_name}': {e}")

    async def _perform_health_checks(self) -> None:
        """Perform health checks on all connections."""
        current_time = time.time()
        
        async with self._lock:
            connections_to_remove = []
            
            for conn_id, connection in self.connections.items():
                # Skip if recently checked
                if current_time - connection.last_health_check < self.health_check_interval:
                    continue
                
                connection.last_health_check = current_time
                
                # Check if connection is idle for too long
                if (current_time - connection.metrics.last_used > self.max_idle_time and
                    len(self.connections) > self.min_connections):
                    connections_to_remove.append(conn_id)
                    continue
                
                # Check circuit breaker state
                if not connection.circuit_breaker.allow_request():
                    connection.state = ConnectionState.UNHEALTHY
                    continue
                
                # Update connection state based on metrics
                success_rate = connection.metrics.success_rate
                if success_rate >= 0.9:
                    connection.state = ConnectionState.HEALTHY
                elif success_rate >= 0.7:
                    connection.state = ConnectionState.DEGRADED
                else:
                    connection.state = ConnectionState.UNHEALTHY
                
                self.stats['health_checks_performed'] += 1
            
            # Remove idle connections
            for conn_id in connections_to_remove:
                await self._remove_connection(conn_id)

    async def _scaling_loop(self) -> None:
        """Background task for preemptive scaling."""
        while not self.is_closed:
            try:
                await asyncio.sleep(10.0)  # Check every 10 seconds
                await self._check_scaling_needs()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in scaling loop for pool '{self.pool_name}': {e}")

    async def _check_scaling_needs(self) -> None:
        """Check if pool needs to scale up or down."""
        async with self._lock:
            current_utilization = self._calculate_pool_utilization()
            self.utilization_history.append(current_utilization)
            
            # Scale up if utilization is consistently high
            if (current_utilization > self.target_utilization and
                len(self.connections) < self.max_connections):
                
                # Check if utilization has been high for the last few measurements
                recent_utilization = list(self.utilization_history)[-5:]
                if len(recent_utilization) >= 3 and all(u > self.target_utilization for u in recent_utilization):
                    await self._create_connection()
                    self.stats['scaling_events'] += 1
                    logger.info(f"Scaled up pool '{self.pool_name}' due to high utilization ({current_utilization:.1%})")
            
            # Scale down if utilization is consistently low
            elif (current_utilization < self.target_utilization * 0.5 and
                  len(self.connections) > self.min_connections):
                
                recent_utilization = list(self.utilization_history)[-5:]
                if len(recent_utilization) >= 3 and all(u < self.target_utilization * 0.5 for u in recent_utilization):
                    # Find least used connection to remove
                    idle_connections = [
                        conn for conn in self.connections.values()
                        if conn.metrics.active_requests == 0
                    ]
                    
                    if idle_connections:
                        least_used = min(idle_connections, key=lambda c: c.metrics.last_used)
                        await self._remove_connection(least_used.connection_id)
                        self.stats['scaling_events'] += 1
                        logger.info(f"Scaled down pool '{self.pool_name}' due to low utilization ({current_utilization:.1%})")

    def _calculate_pool_utilization(self) -> float:
        """Calculate overall pool utilization."""
        if not self.connections:
            return 0.0
        
        total_capacity = sum(conn.max_concurrent for conn in self.connections.values())
        total_active = sum(conn.metrics.active_requests for conn in self.connections.values())
        
        return total_active / max(1, total_capacity)

    async def _remove_connection(self, connection_id: str) -> None:
        """Remove a connection from the pool."""
        if connection_id not in self.connections:
            return
        
        connection = self.connections[connection_id]
        
        # Wait for active requests to complete (with timeout)
        timeout = 30.0
        start_time = time.time()
        
        while connection.metrics.active_requests > 0 and time.time() - start_time < timeout:
            await asyncio.sleep(0.1)
        
        # Close the connection
        try:
            if hasattr(connection.connection, 'close'):
                await connection.connection.close()
        except Exception as e:
            logger.warning(f"Error closing connection {connection_id}: {e}")
        
        # Remove from pool
        del self.connections[connection_id]
        if connection_id in self.connection_order:
            self.connection_order.remove(connection_id)
        
        self.stats['connections_destroyed'] += 1
        logger.info(f"Removed connection {connection_id} from pool '{self.pool_name}'")

    async def close(self) -> None:
        """Close the connection pool and all connections."""
        if self.is_closed:
            return
        
        logger.info(f"Closing connection pool '{self.pool_name}'")
        self.is_closed = True
        
        # Cancel background tasks
        if self.health_check_task:
            self.health_check_task.cancel()
        if self.scaling_task:
            self.scaling_task.cancel()
        
        # Close all connections
        async with self._lock:
            connection_ids = list(self.connections.keys())
            for conn_id in connection_ids:
                await self._remove_connection(conn_id)
        
        logger.info(f"Connection pool '{self.pool_name}' closed")

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive pool statistics."""
        pool_utilization = self._calculate_pool_utilization()
        
        connection_stats = {}
        for conn_id, conn in self.connections.items():
            connection_stats[conn_id] = {
                'state': conn.state.value,
                'active_requests': conn.metrics.active_requests,
                'total_requests': conn.metrics.total_requests,
                'success_rate': conn.metrics.success_rate,
                'average_response_time': conn.metrics.average_response_time,
                'utilization': conn.utilization,
                'load_score': conn.metrics.load_score,
                'circuit_breaker_state': conn.circuit_breaker.state
            }
        
        return {
            'pool_name': self.pool_name,
            'total_connections': len(self.connections),
            'healthy_connections': len([c for c in self.connections.values() if c.state == ConnectionState.HEALTHY]),
            'pool_utilization': pool_utilization,
            'load_balancing_strategy': self.load_balancing_strategy.value,
            'stats': self.stats,
            'connection_details': connection_stats,
            'recent_utilization': list(self.utilization_history)[-10:] if self.utilization_history else []
        }
