#!/usr/bin/env python3
"""
Teste de integração do BayesianOptimizer com simulação realista.
Valida que a integração D-01 está funcionando corretamente.
"""

import sys
import os
import asyncio
from datetime import datetime

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from qualia.optimization.bayesian_optimizer import BayesianOptimizer, OptimizationConfig
from qualia.utils.logger import setup_logging, get_logger

logger = get_logger(__name__)


async def test_bayesian_with_realistic_evaluation():
    """Testa BayesianOptimizer com avaliação realista habilitada."""
    logger.info("🧪 Teste: BayesianOptimizer com avaliação realista")
    
    # Configuração para teste rápido
    config = OptimizationConfig(
        n_trials_per_cycle=3,  # Poucos trials para teste rápido
        optimization_interval_cycles=1,
        price_amp_range=(0.5, 2.0),
        news_amp_range=(5.0, 15.0),
        min_conf_range=(0.1, 0.5)
    )
    
    # Inicializar com avaliação realista
    optimizer = BayesianOptimizer(config, use_realistic_evaluation=True)
    
    # Executar otimização para um símbolo
    result = await optimizer.optimize_symbol("BTCUSDT")
    
    # Validações
    assert result is not None, "Deve retornar resultado de otimização"
    assert result.symbol == "BTCUSDT", "Símbolo deve estar correto"
    assert result.parameters is not None, "Deve ter melhores parâmetros"
    assert result.objective_value > 0, "Objetivo deve ser positivo"

    logger.info(f"✅ Otimização realista concluída:")
    logger.info(f"   📊 Melhor objetivo: {result.objective_value:.4f}")
    logger.info(f"   🎯 Melhores parâmetros: {result.parameters}")
    logger.info(f"   ⏱️  Tempo: {result.duration_seconds:.2f}s")
    logger.info(f"   🔬 Trial: {result.trial_number}")
    
    return result


async def test_bayesian_with_fallback_evaluation():
    """Testa BayesianOptimizer com avaliação de fallback."""
    logger.info("🧪 Teste: BayesianOptimizer com avaliação de fallback")
    
    # Configuração para teste rápido
    config = OptimizationConfig(
        n_trials_per_cycle=3,
        optimization_interval_cycles=1
    )
    
    # Inicializar sem avaliação realista (fallback)
    optimizer = BayesianOptimizer(config, use_realistic_evaluation=False)
    
    # Executar otimização
    result = await optimizer.optimize_symbol("ETHUSDT")
    
    # Validações
    assert result is not None, "Deve retornar resultado de otimização"
    assert result.symbol == "ETHUSDT", "Símbolo deve estar correto"
    
    logger.info(f"✅ Otimização fallback concluída:")
    logger.info(f"   📊 Melhor objetivo: {result.objective_value:.4f}")
    logger.info(f"   🎯 Melhores parâmetros: {result.parameters}")
    
    return result


def test_parameter_evaluation_sync():
    """Testa avaliação síncrona de parâmetros."""
    logger.info("🧪 Teste: Avaliação síncrona de parâmetros")
    
    config = OptimizationConfig()
    optimizer = BayesianOptimizer(config, use_realistic_evaluation=True)
    
    # Testar avaliação síncrona
    result = optimizer._evaluate_parameters_sync(
        symbol="BTCUSDT",
        price_amp=1.5,
        news_amp=8.0,
        min_conf=0.3
    )
    
    # Validações
    assert isinstance(result, dict), "Deve retornar dicionário"
    assert 'sharpe_ratio' in result, "Deve conter Sharpe ratio"
    assert 'pnl_24h' in result, "Deve conter PnL 24h"
    assert result['sharpe_ratio'] >= 0, "Sharpe deve ser não-negativo"
    
    logger.info(f"✅ Avaliação síncrona:")
    logger.info(f"   📊 Sharpe: {result['sharpe_ratio']:.2f}")
    logger.info(f"   💰 PnL 24h: {result['pnl_24h']:.0f}")
    logger.info(f"   🎯 Realista: {result.get('realistic_evaluation', False)}")
    
    return result


def test_fallback_evaluation():
    """Testa avaliação de fallback."""
    logger.info("🧪 Teste: Avaliação de fallback")
    
    config = OptimizationConfig()
    optimizer = BayesianOptimizer(config, use_realistic_evaluation=False)
    
    # Testar fallback
    result = optimizer._evaluate_parameters_fallback(
        price_amp=2.0,
        news_amp=12.0,
        min_conf=0.4
    )
    
    # Validações
    assert isinstance(result, dict), "Deve retornar dicionário"
    assert 'sharpe_ratio' in result, "Deve conter Sharpe ratio"
    assert 'realistic_evaluation' in result, "Deve indicar tipo de avaliação"
    assert result['realistic_evaluation'] == False, "Deve ser marcado como não-realista"
    
    logger.info(f"✅ Avaliação fallback:")
    logger.info(f"   📊 Sharpe: {result['sharpe_ratio']:.2f}")
    logger.info(f"   💰 PnL 24h: {result['pnl_24h']:.0f}")
    logger.info(f"   🎯 Realista: {result['realistic_evaluation']}")
    
    return result


async def test_comparison_realistic_vs_fallback():
    """Compara avaliação realista vs fallback."""
    logger.info("🧪 Teste: Comparação realista vs fallback")
    
    config = OptimizationConfig(n_trials_per_cycle=2)
    
    # Teste com avaliação realista
    optimizer_realistic = BayesianOptimizer(config, use_realistic_evaluation=True)
    result_realistic = await optimizer_realistic.optimize_symbol("ADAUSDT")
    
    # Teste com fallback
    optimizer_fallback = BayesianOptimizer(config, use_realistic_evaluation=False)
    result_fallback = await optimizer_fallback.optimize_symbol("ADAUSDT")
    
    logger.info(f"✅ Comparação:")
    logger.info(f"   🎯 Realista - Objetivo: {result_realistic.objective_value:.4f}")
    logger.info(f"   📊 Fallback - Objetivo: {result_fallback.objective_value:.4f}")
    logger.info(f"   ⏱️  Realista - Tempo: {result_realistic.duration_seconds:.2f}s")
    logger.info(f"   ⏱️  Fallback - Tempo: {result_fallback.duration_seconds:.2f}s")

    # Ambos devem funcionar
    assert result_realistic.objective_value > 0, "Avaliação realista deve funcionar"
    assert result_fallback.objective_value > 0, "Avaliação fallback deve funcionar"
    
    return result_realistic, result_fallback


async def run_all_integration_tests():
    """Executa todos os testes de integração."""
    logger.info("🚀 Iniciando testes de integração BayesianOptimizer + Simulação Realista")
    
    try:
        # Teste 1: Avaliação realista
        result_realistic = await test_bayesian_with_realistic_evaluation()
        
        # Teste 2: Avaliação fallback
        result_fallback = await test_bayesian_with_fallback_evaluation()
        
        # Teste 3: Avaliação síncrona
        sync_result = test_parameter_evaluation_sync()
        
        # Teste 4: Fallback direto
        fallback_result = test_fallback_evaluation()
        
        # Teste 5: Comparação
        realistic_comp, fallback_comp = await test_comparison_realistic_vs_fallback()
        
        # Resumo final
        logger.info("🎉 Todos os testes de integração concluídos com sucesso!")
        logger.info("📊 Resumo dos resultados:")
        logger.info(f"   • Otimização realista: {result_realistic.objective_value:.4f}")
        logger.info(f"   • Otimização fallback: {result_fallback.objective_value:.4f}")
        logger.info(f"   • Avaliação síncrona: Sharpe {sync_result['sharpe_ratio']:.2f}")
        logger.info(f"   • Fallback direto: Sharpe {fallback_result['sharpe_ratio']:.2f}")
        logger.info(f"   • Comparação realista: {realistic_comp.objective_value:.4f}")
        logger.info(f"   • Comparação fallback: {fallback_comp.objective_value:.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro nos testes de integração: {e}")
        return False


if __name__ == "__main__":
    # Configurar logging
    setup_logging()
    
    # Executar testes
    success = asyncio.run(run_all_integration_tests())
    
    if success:
        print("\n✅ Todos os testes de integração passaram!")
        print("🎯 D-01 integração BayesianOptimizer + Simulação Realista validada!")
        sys.exit(0)
    else:
        print("\n❌ Alguns testes de integração falharam")
        sys.exit(1)
