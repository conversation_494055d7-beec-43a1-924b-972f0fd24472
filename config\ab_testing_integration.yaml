# QUALIA A/B Testing Framework - D-07.6 System Integration Configuration
# Configuração completa para integração do framework A/B Testing com sistema QUALIA

# =============================================================================
# CONFIGURAÇÃO PRINCIPAL DO SISTEMA A/B TESTING
# =============================================================================

ab_testing:
  # Configurações gerais do framework
  framework:
    enabled: true
    version: "1.0.0"
    environment: "production"  # development, staging, production
    
  # Configurações de integração com sistema QUALIA
  integration:
    # Componentes integrados
    components:
      live_feed: true          # D-03.2 Live Feed Integration
      bayesian_optimizer: true # D-04 BayesianOptimizer
      parameter_tuner: true    # ParameterTuner worker
      hot_reload: true         # D-05 Hot-reload & Rollback
      regime_detection: true   # D-06 Market Regime Detection
      
    # Configurações de inicialização
    initialization:
      timeout_seconds: 30
      retry_attempts: 3
      health_check_interval: 10
      
  # Configurações de testes A/B
  testing:
    # Duração padrão dos testes
    default_duration_hours: 24
    
    # Símbolos para teste
    symbols:
      - "BTC/USDT"
      - "ETH/USDT"
      - "POL/USDT"
      - "ADA/USDT"
      
    # Configurações estatísticas
    statistics:
      confidence_level: 0.95
      min_sample_size: 100
      significance_threshold: 0.05
      
    # Métricas de comparação
    metrics:
      primary: "sharpe_ratio"
      secondary: ["total_pnl", "max_drawdown", "win_rate"]
      
  # Configurações de monitoramento
  monitoring:
    # Intervalos de coleta
    intervals:
      health_check: 10      # segundos
      metrics_update: 60    # segundos
      report_generation: 300 # segundos
      
    # Alertas
    alerts:
      enabled: true
      channels: ["log", "file"]  # log, file, slack, email
      thresholds:
        error_rate: 0.05
        latency_ms: 1000
        memory_usage_mb: 512
        
  # Configurações de logging
  logging:
    level: "INFO"  # TRACE, DEBUG, INFO, WARNING, ERROR
    structured: true
    correlation_enabled: true
    
    # Rotação de logs
    rotation:
      max_file_size_mb: 100
      backup_count: 5
      
    # Diretórios
    directories:
      logs: "logs/ab_testing"
      reports: "reports/ab_testing"
      temp: "temp/ab_testing"

# =============================================================================
# CONFIGURAÇÕES DE REGIME DE MERCADO
# =============================================================================

market_regime:
  # Detector de regime
  detector:
    enabled: true
    update_interval_minutes: 15
    
    # Parâmetros de detecção
    parameters:
      volatility_window: 20
      momentum_window: 14
      trend_window: 50
      
    # Thresholds para classificação
    thresholds:
      bull_momentum: 0.6
      bear_momentum: -0.4
      high_volatility: 0.02
      low_volatility: 0.005
      
  # Presets por regime
  presets:
    bull:
      news_amplification: 8.5
      price_amplification: 1.2
      min_confidence: 0.35
      
    bear:
      news_amplification: 12.0
      price_amplification: 0.8
      min_confidence: 0.45
      
    sideways:
      news_amplification: 11.3
      price_amplification: 1.0
      min_confidence: 0.37

# =============================================================================
# CONFIGURAÇÕES DE PERFORMANCE E RECURSOS
# =============================================================================

performance:
  # Configurações de threading
  threading:
    max_workers: 4
    thread_pool_size: 8
    
  # Configurações de memória
  memory:
    max_cache_size_mb: 256
    gc_interval_seconds: 300
    
  # Configurações de rede
  network:
    timeout_seconds: 30
    max_retries: 3
    backoff_factor: 2.0
    
  # Configurações de I/O
  io:
    buffer_size: 8192
    async_enabled: true
    batch_size: 100

# =============================================================================
# CONFIGURAÇÕES DE SEGURANÇA E VALIDAÇÃO
# =============================================================================

security:
  # Validação de dados
  validation:
    enabled: true
    strict_mode: true
    
  # Limites de segurança
  limits:
    max_test_duration_hours: 168  # 7 dias
    max_concurrent_tests: 5
    max_symbols_per_test: 10
    
  # Fail-safe bounds
  bounds:
    news_amplification:
      min: 1.0
      max: 20.0
    price_amplification:
      min: 0.1
      max: 5.0
    min_confidence:
      min: 0.1
      max: 0.9

# =============================================================================
# CONFIGURAÇÕES DE INTEGRAÇÃO COM COMPONENTES EXTERNOS
# =============================================================================

external_integrations:
  # KuCoin Live Feed (D-03.2)
  kucoin_feed:
    enabled: true
    config_path: "config/kucoin_config.yaml"
    
  # Bayesian Optimizer (D-04)
  bayesian_optimizer:
    enabled: true
    study_name: "qualia_ab_testing"
    storage_url: "sqlite:///optuna_ab_testing.db"
    
  # Grafana Dashboard (D-05)
  grafana:
    enabled: false  # Configurar se disponível
    dashboard_url: "http://localhost:3000"
    
  # Alerting Systems
  alerting:
    slack:
      enabled: false
      webhook_url: ""
    email:
      enabled: false
      smtp_server: ""

# =============================================================================
# CONFIGURAÇÕES DE DESENVOLVIMENTO E DEBUG
# =============================================================================

development:
  # Modo debug
  debug:
    enabled: false
    verbose_logging: false
    save_intermediate_results: true
    
  # Configurações de teste
  testing:
    mock_data: false
    fast_mode: false
    skip_validation: false
    
  # Profiling
  profiling:
    enabled: false
    output_dir: "profiling/ab_testing"
