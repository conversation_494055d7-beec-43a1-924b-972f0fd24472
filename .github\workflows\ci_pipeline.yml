name: QUALIA CI Pipeline

on:
  push:
    branches:
      - main
      - develop
      - 'ci/**'
  pull_request:
    branches:
      - main
      - develop
  workflow_dispatch:
    inputs:
      run_benchmarks:
        description: 'Run benchmark pipeline'
        required: false
        default: 'false'

jobs:
  test-and-lint:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"] # Conforme especificação NFR

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        ./scripts/install_test_deps.sh
        pip install -e .[dev]

    - name: Verify dependencies
      run: |
        python - <<'EOF'
        import pkg_resources, sys

        required = {
            "flask": "3.0.3",
            "flask-socketio": "5.3.6",
        }

        for package, expected_version in required.items():
            distribution = pkg_resources.get_distribution(package)
            if distribution.version != expected_version:
                print(
                    f"{package}=={expected_version} é necessário, mas {distribution.version} foi encontrado",
                )
                sys.exit(1)
        print("Dependências verificadas com sucesso")
        EOF

    - name: Run pre-commit checks
      run: |
        pre-commit install # Garante que os hooks estão instalados para a execução manual abaixo
        pre-commit run --all-files

    - name: Run tests with pytest and coverage
      env:
        PYTHONIOENCODING: "utf-8"
        LC_ALL: C.UTF-8
      run: |
        pytest -vv --cov=src/qualia tests/ --cov-report=xml --cov-fail-under=80 \
          | tee result_testes.txt

    - name: Validate risk configuration
      run: |
        python - <<'EOF'
        from qualia.config.risk_validator import (
            RiskParameterValidator,
            validate_and_fix_risk_config,
        )

        result = validate_and_fix_risk_config(
            "config/strategy_parameters.json", auto_fix=False
        )
        summary = RiskParameterValidator().get_validation_summary(result)
        print(summary)
        with open("risk_validation_summary.txt", "w", encoding="utf-8") as f:
            f.write(summary)
        EOF

    - name: Upload risk validation summary
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: risk-validation-summary
        path: risk_validation_summary.txt

    # Futuramente, adicionar etapa para upload de coverage report (ex: Codecov)
    # - name: Upload coverage reports to Codecov
    #   uses: codecov/codecov-action@v4.0.1
    #   with:
    # token: ${{ secrets.CODECOV_TOKEN }} # Adicionar CODECOV_TOKEN aos segredos do repo
    #   env:
    #     CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

  benchmark-encoders:
    needs: test-and-lint # Rodar somente se testes e lint passarem
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"]
        
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          ./scripts/install_test_deps.sh
          pip install -e .[dev]
          pip install numpy
          # Se houver outras dependências específicas do benchmark, adicionar aqui

      - name: Run encoder benchmark script
        run: python benchmarks/encoders/run_encoder_bench.py --output-file results/encoder_benchmark_results.json
        # O script run_encoder_bench.py sairá com sys.exit(1) se os guardrails forem violados,
        # o que fará esta etapa e o job falharem automaticamente.
      
      - name: Create results directory for benchmark encoders (if not exists)
        run: mkdir -p results
        if: always() # Garante que o diretório seja criado para o artefato mesmo se o benchmark falhar

      - name: Upload encoder benchmark results
        uses: actions/upload-artifact@v4
        if: always() # Sempre faz upload, mesmo que o benchmark falhe (para análise)
        with:
          name: encoder-benchmark-results
          path: results/encoder_benchmark_results.json

  benchmark-cortex:
    needs: [test-and-lint, benchmark-encoders] # Rodar somente se testes, lint e benchmark de encoders passarem
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"]
        
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          ./scripts/install_test_deps.sh
          pip install -e .[dev]
          pip install numpy scikit-learn  # Dependências para consciousness.py e o benchmark

      - name: Create results directory for cortex benchmark (if not exists)
        run: mkdir -p results

      - name: Run cortex benchmark script
        run: python benchmarks/cortex/run_cortex_bench.py --output-file results/cortex_benchmark_results.json
        # O script run_cortex_bench.py sairá com sys.exit(1) se os guardrails forem violados.

      - name: Upload cortex benchmark results
        uses: actions/upload-artifact@v4
        if: always() # Sempre faz upload, mesmo que o benchmark falhe (para análise)
        with:
          name: cortex-benchmark-results
          path: results/cortex_benchmark_results.json

  benchmark-qast:
    needs: [test-and-lint, benchmark-encoders, benchmark-cortex]
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          ./scripts/install_test_deps.sh
          pip install -e .[dev]
          pip install pytest-benchmark

      - name: Run QAST evolution benchmark
        run: pytest benchmarks/test_qast_evolution_performance.py --benchmark-json results/qast_benchmark_results.json

      - name: Upload QAST benchmark results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: qast-benchmark-results
          path: results/qast_benchmark_results.json

  benchmark-pipeline:
    if: github.event_name == 'workflow_dispatch' && inputs.run_benchmarks == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          ./scripts/install_test_deps.sh
          pip install -e .[dev]
          pip install pytest-benchmark

      - name: Run QUALIA benchmark pipeline
        run: python benchmarks/run_benchmark_pipeline.py --compare

      - name: Upload pipeline benchmark results
        uses: actions/upload-artifact@v4
        with:
          name: pipeline-benchmark-results
          path: benchmarks/results/

  # Job de Benchmark (a ser completamente definido depois)
  # benchmark-encoders:
  #   needs: test-and-lint # Rodar somente se testes e lint passarem
  #   runs-on: ubuntu-latest
  #   steps:
  #     - name: Checkout repository
  #       uses: actions/checkout@v2
  #     - name: Set up Python
  #       uses: actions/setup-python@v2
  #       with:
  #         python-version: '3.11'
  #     - name: Install dependencies
  #       run: |
  #         pip install -r requirements.txt
  #         pip install numpy # ou outras deps específicas do benchmark
  #     - name: Create benchmark directory if not exists
  #       run: mkdir -p benchmarks/encoders
  #     - name: Run benchmark script
  #       run: python benchmarks/encoders/run_encoder_bench.py # Este script precisa ser criado
  #     - name: Check benchmark results (placeholder)
  #       run: |
  #         echo "Verificando resultados do benchmark... (lógica a ser implementada)"
  #         # Aqui, o script leria o JSON output do benchmark e compararia com os SLAs
  #         # Exemplo: exit 1 se a latência > 5ms 