import importlib
import yaml
from tests.stub_utils import install_stubs

install_stubs()

from qualia.config import encoder_registry
from src.ai import encoders_plugin as plugin


def _reload_plugin(tmp_path, flags):
    config_file = tmp_path / "encoders.yaml"
    with open(config_file, "w", encoding="utf-8") as fh:
        yaml.safe_dump(flags, fh)
    importlib.reload(encoder_registry)
    return importlib.reload(plugin), config_file


def test_hardware_acceleration_enabled(monkeypatch, tmp_path):
    mod, config = _reload_plugin(
        tmp_path,
        {"RSIPhaseEncoder": {"enabled": True, "use_hardware_acceleration": True}},
    )
    monkeypatch.setenv("QUALIA_ENCODERS_CONFIG", str(config))
    mod = importlib.reload(mod)
    monkeypatch.setattr(
        "src.ai.encoders_plugin.check_hardware_status",
        lambda: {"gpu": True, "qpu": False},
    )
    enc = encoder_registry.create_encoder("RSIPhaseEncoder", name="rsi")
    assert enc.use_hardware_acceleration is True


def test_hardware_acceleration_disabled_no_hw(monkeypatch, tmp_path):
    mod, config = _reload_plugin(
        tmp_path,
        {"RSIPhaseEncoder": {"enabled": True, "use_hardware_acceleration": True}},
    )
    monkeypatch.setenv("QUALIA_ENCODERS_CONFIG", str(config))
    mod = importlib.reload(mod)
    monkeypatch.setattr(
        "src.ai.encoders_plugin.check_hardware_status",
        lambda: {"gpu": False, "qpu": False},
    )
    enc = encoder_registry.create_encoder("RSIPhaseEncoder", name="rsi")
    assert enc.use_hardware_acceleration is False
