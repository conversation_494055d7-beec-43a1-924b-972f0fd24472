# Serviço de Metacognição

Este documento descreve a API mínima exposta pelo módulo
`src.qualia.metacognition.service`.

O serviço mantém um pequeno registro em memória das métricas enviadas
durante os ciclos do QUALIA. A partir dessas métricas calcula-se um
valor de "foco" que representa a atenção média do sistema.

## Funções Principais

### `push_metric(name, value, timestamp=None)`
Registra uma métrica com rótulo e valor numérico. O timestamp é
opcional e, quando omitido, a hora atual em UTC é utilizada.

### `get_focus(window=10)`
Retorna o nível de foco calculado pela média dos últimos `window`
valores armazenados. O resultado varia de `0` a `1`.

### `snapshot()`
Devolve um dicionário contendo o foco atual e todas as métricas
armazenadas.

## Exemplo Rápido

```python
from src.qualia.metacognition.service import push_metric, get_focus, snapshot

push_metric("coherence", 0.8)
push_metric("risk_awareness", 0.6)

print(get_focus())
print(snapshot())
```
