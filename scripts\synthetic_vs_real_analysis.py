#!/usr/bin/env python3
"""
Synthetic vs Real Analysis - Análise comparativa entre motores sintético e real
para detectar over-fitting artificial.

YAA-COMPARATIVE: Sistema rigoroso para identificar discrepâncias entre
simulação sintética e backtest real, conforme especificação.
"""

import sys
import os
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import json
import logging

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from real_backtest_integration import run_real_backtest
    from otoc_parameter_optimization import OTOCParameterOptimizer
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Modules not available: {e}")
    MODULES_AVAILABLE = False

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ComparisonResult:
    """Resultado da comparação sintético vs real."""
    parameters: Dict[str, Any]
    
    # Métricas sintéticas
    synthetic_sharpe: float
    synthetic_drawdown: float
    synthetic_win_rate: float
    synthetic_trades: int
    synthetic_score: float
    
    # Métricas reais
    real_sharpe: float
    real_drawdown: float
    real_win_rate: float
    real_trades: int
    real_score: float
    
    # Divergências (conforme especificação)
    sharpe_divergence: float
    drawdown_divergence: float
    win_rate_divergence: float
    trades_divergence: float
    overall_divergence: float
    
    # Classificação de confiabilidade
    is_robust: bool  # divergência < 15%
    has_severe_overfitting: bool  # divergência > 30%
    
    # Metadados
    comparison_date: datetime
    backtest_period: str


class SyntheticVsRealAnalyzer:
    """
    Analisador comparativo entre motores sintético e real.
    
    YAA-ANALYZER: Implementação rigorosa conforme especificação
    """
    
    def __init__(self):
        """Inicializa analisador comparativo."""
        self.comparison_results: List[ComparisonResult] = []
        
        # Thresholds conforme especificação
        self.robust_threshold = 0.15      # 15% - configurações robustas
        self.severe_overfitting_threshold = 0.30  # 30% - over-fitting severo
        
        print("🔬 Synthetic vs Real Analyzer initialized")
        print(f"   Robust threshold: {self.robust_threshold:.1%}")
        print(f"   Severe overfitting threshold: {self.severe_overfitting_threshold:.1%}")
    
    async def compare_engines(
        self,
        parameters_list: List[Dict[str, Any]],
        start_date: datetime,
        end_date: datetime,
        symbols: List[str] = None,
        max_concurrent: int = 3
    ) -> List[ComparisonResult]:
        """
        Executa comparação paralela entre motores sintético e real.
        
        YAA-PARALLEL-COMPARISON: Mesmo período, mesmos parâmetros, motores diferentes
        
        Parameters
        ----------
        parameters_list : List[Dict[str, Any]]
            Lista de configurações de parâmetros para testar
        start_date : datetime
            Data inicial do backtest
        end_date : datetime
            Data final do backtest
        symbols : List[str], optional
            Símbolos para teste (padrão: BTC/USDT, ETH/USDT, SOL/USDT)
        max_concurrent : int
            Máximo de comparações simultâneas
            
        Returns
        -------
        List[ComparisonResult]
            Resultados das comparações
        """
        if symbols is None:
            symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']
        
        print(f"🔄 Comparing {len(parameters_list)} configurations")
        print(f"   Period: {start_date.date()} - {end_date.date()}")
        print(f"   Symbols: {symbols}")
        
        # Criar semáforo para controlar concorrência
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def compare_single(params: Dict[str, Any], index: int) -> ComparisonResult:
            async with semaphore:
                return await self._compare_single_configuration(
                    params, start_date, end_date, symbols, index
                )
        
        # Executar comparações
        tasks = [
            compare_single(params, i) 
            for i, params in enumerate(parameters_list)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filtrar resultados válidos
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Comparison {i} failed: {result}")
            else:
                valid_results.append(result)
                self.comparison_results.append(result)
        
        print(f"✅ Completed {len(valid_results)}/{len(parameters_list)} comparisons")
        
        return valid_results
    
    async def _compare_single_configuration(
        self,
        parameters: Dict[str, Any],
        start_date: datetime,
        end_date: datetime,
        symbols: List[str],
        index: int
    ) -> ComparisonResult:
        """Compara uma configuração específica entre motores."""
        print(f"🔍 Comparing configuration #{index + 1}")
        
        try:
            # Criar configuração base
            base_config = self._create_base_config(parameters)
            
            # 1. Executar backtest sintético
            print(f"   Running synthetic backtest...")
            synthetic_result = await run_real_backtest(
                config=base_config,
                start_date=start_date,
                end_date=end_date,
                symbols=symbols,
                engine_type="synthetic"
            )
            
            # 2. Executar backtest real
            print(f"   Running real backtest...")
            real_result = await run_real_backtest(
                config=base_config,
                start_date=start_date,
                end_date=end_date,
                symbols=symbols,
                engine_type="real"
            )
            
            # 3. Calcular divergências
            comparison = self._calculate_divergences(
                parameters, synthetic_result, real_result, start_date, end_date
            )
            
            # Log resultado
            status = "🟢 ROBUST" if comparison.is_robust else "🟡 MODERATE"
            if comparison.has_severe_overfitting:
                status = "🔴 SEVERE OVERFITTING"
            
            print(f"   {status}: Overall divergence {comparison.overall_divergence:.1%}")
            
            return comparison
            
        except Exception as e:
            logger.error(f"Failed to compare configuration #{index + 1}: {e}")
            # Retornar resultado de erro
            return ComparisonResult(
                parameters=parameters,
                synthetic_sharpe=0, synthetic_drawdown=0, synthetic_win_rate=0,
                synthetic_trades=0, synthetic_score=0,
                real_sharpe=0, real_drawdown=0, real_win_rate=0,
                real_trades=0, real_score=0,
                sharpe_divergence=1.0, drawdown_divergence=1.0,
                win_rate_divergence=1.0, trades_divergence=1.0,
                overall_divergence=1.0,
                is_robust=False, has_severe_overfitting=True,
                comparison_date=datetime.now(),
                backtest_period=f"{start_date.date()}-{end_date.date()}"
            )
    
    def _create_base_config(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Cria configuração base a partir dos parâmetros."""
        # Carregar configuração template
        try:
            import yaml
            with open('config/fwh_scalp_config_optimized.yaml', 'r') as f:
                base_config = yaml.safe_load(f)
        except:
            # Configuração mínima se arquivo não existir
            base_config = {
                'fibonacci_wave_hype_config': {
                    'params': {
                        'multi_timeframe_config': {
                            'otoc_config': {
                                'enabled': True,
                                'max_threshold': 0.35,
                                'window': 100,
                                'method': 'correlation'
                            },
                            'timeframe_weights': {
                                '1m': 0.25, '5m': 0.35, '15m': 0.65, '1h': 0.85
                            }
                        }
                    }
                },
                'trading_system': {
                    'risk_management': {
                        'stop_loss_pct': 0.55,
                        'take_profit_pct': 1.1
                    }
                },
                'backtesting': {
                    'initial_capital': 10000.0,
                    'commission': 0.001,
                    'slippage': 0.0005
                }
            }
        
        # Aplicar parâmetros específicos
        if 'otoc_max_threshold' in parameters:
            base_config['fibonacci_wave_hype_config']['params']['multi_timeframe_config']['otoc_config']['max_threshold'] = parameters['otoc_max_threshold']
        
        if 'weight_15m' in parameters:
            base_config['fibonacci_wave_hype_config']['params']['multi_timeframe_config']['timeframe_weights']['15m'] = parameters['weight_15m']
        
        # Aplicar outros parâmetros conforme necessário
        for param_name, param_value in parameters.items():
            if param_name.startswith('weight_'):
                timeframe = param_name.replace('weight_', '') + 'm'
                if timeframe in base_config['fibonacci_wave_hype_config']['params']['multi_timeframe_config']['timeframe_weights']:
                    base_config['fibonacci_wave_hype_config']['params']['multi_timeframe_config']['timeframe_weights'][timeframe] = param_value
        
        return base_config
    
    def _calculate_divergences(
        self,
        parameters: Dict[str, Any],
        synthetic_result,
        real_result,
        start_date: datetime,
        end_date: datetime
    ) -> ComparisonResult:
        """
        Calcula divergências entre resultados sintético e real.
        
        YAA-DIVERGENCE: Implementação conforme especificação
        """
        # Extrair métricas
        syn_sharpe = synthetic_result.sharpe_ratio
        syn_drawdown = abs(synthetic_result.max_drawdown)
        syn_win_rate = synthetic_result.win_rate
        syn_trades = synthetic_result.total_trades
        
        real_sharpe = real_result.sharpe_ratio
        real_drawdown = abs(real_result.max_drawdown)
        real_win_rate = real_result.win_rate
        real_trades = real_result.total_trades
        
        # Calcular scores compostos
        syn_score = self._calculate_composite_score({
            'sharpe_ratio': syn_sharpe,
            'max_drawdown': syn_drawdown,
            'win_rate': syn_win_rate,
            'profit_factor': getattr(synthetic_result, 'profit_factor', 1.0)
        })
        
        real_score = self._calculate_composite_score({
            'sharpe_ratio': real_sharpe,
            'max_drawdown': real_drawdown,
            'win_rate': real_win_rate,
            'profit_factor': getattr(real_result, 'profit_factor', 1.0)
        })
        
        # Calcular divergências (conforme especificação)
        def safe_divergence(real_val, syn_val):
            if syn_val == 0:
                return 1.0 if real_val != 0 else 0.0
            return abs(real_val - syn_val) / abs(syn_val)
        
        sharpe_div = safe_divergence(real_sharpe, syn_sharpe)
        drawdown_div = safe_divergence(real_drawdown, syn_drawdown)
        win_rate_div = safe_divergence(real_win_rate, syn_win_rate)
        trades_div = safe_divergence(real_trades, syn_trades)
        
        # Divergência geral (média ponderada)
        overall_div = (
            0.4 * sharpe_div +      # Sharpe é mais importante
            0.3 * drawdown_div +    # Drawdown crítico
            0.2 * win_rate_div +    # Win rate importante
            0.1 * trades_div        # Trades menos crítico
        )
        
        # Classificação
        is_robust = overall_div < self.robust_threshold
        has_severe_overfitting = overall_div > self.severe_overfitting_threshold
        
        return ComparisonResult(
            parameters=parameters,
            synthetic_sharpe=syn_sharpe,
            synthetic_drawdown=syn_drawdown,
            synthetic_win_rate=syn_win_rate,
            synthetic_trades=syn_trades,
            synthetic_score=syn_score,
            real_sharpe=real_sharpe,
            real_drawdown=real_drawdown,
            real_win_rate=real_win_rate,
            real_trades=real_trades,
            real_score=real_score,
            sharpe_divergence=sharpe_div,
            drawdown_divergence=drawdown_div,
            win_rate_divergence=win_rate_div,
            trades_divergence=trades_div,
            overall_divergence=overall_div,
            is_robust=is_robust,
            has_severe_overfitting=has_severe_overfitting,
            comparison_date=datetime.now(),
            backtest_period=f"{start_date.date()}-{end_date.date()}"
        )
    
    def _calculate_composite_score(self, metrics: Dict[str, float]) -> float:
        """Calcula score composto (mesmo do otimizador)."""
        sharpe_norm = np.clip(metrics.get('sharpe_ratio', 0) / 3.0, 0, 1)
        drawdown_norm = np.clip(1 - abs(metrics.get('max_drawdown', 0.1)) / 0.1, 0, 1)
        win_rate_norm = metrics.get('win_rate', 0.5)
        profit_factor_norm = np.clip(metrics.get('profit_factor', 1) / 3.0, 0, 1)
        
        return (
            0.35 * sharpe_norm +
            0.25 * drawdown_norm +
            0.20 * win_rate_norm +
            0.20 * profit_factor_norm
        )
    
    def generate_comparison_report(self) -> str:
        """Gera relatório detalhado das comparações."""
        if not self.comparison_results:
            return "📊 Nenhuma comparação disponível."
        
        total_comparisons = len(self.comparison_results)
        robust_count = sum(1 for r in self.comparison_results if r.is_robust)
        severe_count = sum(1 for r in self.comparison_results if r.has_severe_overfitting)
        moderate_count = total_comparisons - robust_count - severe_count
        
        # Estatísticas de divergência
        divergences = [r.overall_divergence for r in self.comparison_results]
        avg_divergence = np.mean(divergences)
        max_divergence = np.max(divergences)
        min_divergence = np.min(divergences)
        
        report = f"""
🔬 RELATÓRIO COMPARATIVO: SINTÉTICO vs REAL
{'='*60}

📊 ESTATÍSTICAS GERAIS:
   Total de Comparações: {total_comparisons}
   Configurações Robustas: {robust_count} ({robust_count/total_comparisons:.1%})
   Over-fitting Moderado: {moderate_count} ({moderate_count/total_comparisons:.1%})
   Over-fitting Severo: {severe_count} ({severe_count/total_comparisons:.1%})

📈 DIVERGÊNCIAS:
   Divergência Média: {avg_divergence:.1%}
   Divergência Mínima: {min_divergence:.1%}
   Divergência Máxima: {max_divergence:.1%}

🎯 ANÁLISE POR MÉTRICA:
"""
        
        # Análise por métrica
        sharpe_divs = [r.sharpe_divergence for r in self.comparison_results]
        drawdown_divs = [r.drawdown_divergence for r in self.comparison_results]
        win_rate_divs = [r.win_rate_divergence for r in self.comparison_results]
        
        report += f"""   Sharpe Ratio - Média: {np.mean(sharpe_divs):.1%}, Max: {np.max(sharpe_divs):.1%}
   Max Drawdown - Média: {np.mean(drawdown_divs):.1%}, Max: {np.max(drawdown_divs):.1%}
   Win Rate - Média: {np.mean(win_rate_divs):.1%}, Max: {np.max(win_rate_divs):.1%}

🚨 ALERTAS AUTOMÁTICOS:
"""
        
        # Gerar alertas
        if severe_count > total_comparisons * 0.3:
            report += f"   🔴 CRÍTICO: {severe_count/total_comparisons:.1%} das configurações têm over-fitting severo!\n"
        
        if robust_count < total_comparisons * 0.5:
            report += f"   ⚠️ ATENÇÃO: Apenas {robust_count/total_comparisons:.1%} das configurações são robustas.\n"
        
        if avg_divergence > 0.25:
            report += f"   🟡 MODERADO: Divergência média alta ({avg_divergence:.1%}).\n"
        
        if robust_count > total_comparisons * 0.8:
            report += f"   ✅ EXCELENTE: {robust_count/total_comparisons:.1%} das configurações são robustas!\n"
        
        return report
    
    def identify_problematic_parameters(self) -> Dict[str, Any]:
        """Identifica parâmetros com maior discrepância entre motores."""
        if not self.comparison_results:
            return {}
        
        # Agrupar por parâmetros
        parameter_analysis = {}
        
        # Analisar cada parâmetro
        all_param_names = set()
        for result in self.comparison_results:
            all_param_names.update(result.parameters.keys())
        
        for param_name in all_param_names:
            param_values = []
            param_divergences = []
            
            for result in self.comparison_results:
                if param_name in result.parameters:
                    param_values.append(result.parameters[param_name])
                    param_divergences.append(result.overall_divergence)
            
            if len(param_values) > 1:
                # Correlação entre valor do parâmetro e divergência
                correlation = np.corrcoef(param_values, param_divergences)[0, 1]
                
                parameter_analysis[param_name] = {
                    'correlation_with_divergence': correlation,
                    'avg_divergence': np.mean(param_divergences),
                    'problematic': abs(correlation) > 0.3  # Correlação forte
                }
        
        return parameter_analysis
    
    def save_comparison_results(self, filepath: str = "logs/synthetic_vs_real_comparison.json"):
        """Salva resultados das comparações."""
        results_data = []
        
        for result in self.comparison_results:
            results_data.append({
                'parameters': result.parameters,
                'synthetic_metrics': {
                    'sharpe_ratio': result.synthetic_sharpe,
                    'max_drawdown': result.synthetic_drawdown,
                    'win_rate': result.synthetic_win_rate,
                    'total_trades': result.synthetic_trades,
                    'score': result.synthetic_score
                },
                'real_metrics': {
                    'sharpe_ratio': result.real_sharpe,
                    'max_drawdown': result.real_drawdown,
                    'win_rate': result.real_win_rate,
                    'total_trades': result.real_trades,
                    'score': result.real_score
                },
                'divergences': {
                    'sharpe_divergence': result.sharpe_divergence,
                    'drawdown_divergence': result.drawdown_divergence,
                    'win_rate_divergence': result.win_rate_divergence,
                    'trades_divergence': result.trades_divergence,
                    'overall_divergence': result.overall_divergence
                },
                'classification': {
                    'is_robust': result.is_robust,
                    'has_severe_overfitting': result.has_severe_overfitting
                },
                'metadata': {
                    'comparison_date': result.comparison_date.isoformat(),
                    'backtest_period': result.backtest_period
                }
            })
        
        with open(filepath, 'w') as f:
            json.dump(results_data, f, indent=2)
        
        print(f"💾 Comparison results saved: {filepath}")


async def main():
    """Demonstração do sistema de análise comparativa."""
    print("🔬 SYNTHETIC vs REAL ANALYSIS DEMO")
    print("=" * 50)
    
    # Configurações de teste
    test_parameters = [
        {'otoc_max_threshold': 0.35, 'weight_15m': 0.65},
        {'otoc_max_threshold': 0.42, 'weight_15m': 0.70},
        {'otoc_max_threshold': 0.28, 'weight_15m': 0.60}
    ]
    
    # Período de teste
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 3, 31)
    
    # Executar análise
    analyzer = SyntheticVsRealAnalyzer()
    
    try:
        results = await analyzer.compare_engines(
            test_parameters, start_date, end_date
        )
        
        # Gerar relatório
        print(analyzer.generate_comparison_report())
        
        # Analisar parâmetros problemáticos
        problematic = analyzer.identify_problematic_parameters()
        if problematic:
            print("\n🎯 PARÂMETROS PROBLEMÁTICOS:")
            for param, analysis in problematic.items():
                if analysis['problematic']:
                    print(f"   {param}: correlação {analysis['correlation_with_divergence']:.3f}")
        
        # Salvar resultados
        analyzer.save_comparison_results()
        
    except Exception as e:
        print(f"❌ Error in analysis: {e}")


if __name__ == "__main__":
    asyncio.run(main())
