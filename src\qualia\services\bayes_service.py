"""
BayesOpt Microservice - D-02 Implementation
FastAPI service with Optuna Study + SQLite for distributed optimization.
"""

import os
import time
import asyncio
import sqlite3
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from contextlib import asynccontextmanager

import optuna
import psutil
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from optuna.samplers import TPESampler, RandomSampler, CmaEsSampler
from optuna.pruners import MedianPruner, NopPruner

from .models import (
    SuggestRequest, SuggestResponse, ReportRequest, ReportResponse,
    StudyInfo, StudyListResponse, StudyStatsResponse, HealthResponse,
    ErrorResponse, ServiceConfig
)
from ..utils.logger import get_logger

logger = get_logger(__name__)


class BayesOptService:
    """Core service for Bayesian Optimization with Optuna + SQLite."""
    
    def __init__(self, config: ServiceConfig):
        self.config = config
        self.studies: Dict[str, optuna.Study] = {}
        self.study_metadata: Dict[str, Dict[str, Any]] = {}
        self.start_time = time.time()
        self.lock = threading.RLock()
        
        # Initialize database
        self._init_database()
        
        # Start background tasks
        self._start_background_tasks()
        
        logger.info("🎯 BayesOptService inicializado")
        logger.info(f"   📊 Database: {config.database_url}")
        logger.info(f"   🔧 Max studies: {config.max_concurrent_studies}")
        logger.info(f"   ⏱️  Study timeout: {config.study_timeout_hours}h")
    
    def _init_database(self):
        """Initialize SQLite database for Optuna studies."""
        try:
            # Ensure data directory exists
            db_path = self.config.database_url.replace("sqlite:///", "")
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            
            # Test connection
            with sqlite3.connect(db_path) as conn:
                conn.execute("SELECT 1")
            
            logger.info(f"✅ Database inicializado: {db_path}")
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar database: {e}")
            raise
    
    def _start_background_tasks(self):
        """Start background maintenance tasks."""
        if self.config.auto_cleanup_enabled:
            # Start cleanup task in background thread
            cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
            cleanup_thread.start()
            logger.info("🧹 Background cleanup iniciado")
    
    def _cleanup_loop(self):
        """Background loop for cleaning up inactive studies."""
        while True:
            try:
                self._cleanup_inactive_studies()
                time.sleep(self.config.health_check_interval)
            except Exception as e:
                logger.error(f"❌ Erro no cleanup: {e}")
                time.sleep(60)  # Wait longer on error
    
    def _cleanup_inactive_studies(self):
        """Remove inactive studies from memory."""
        with self.lock:
            current_time = datetime.now()
            timeout_delta = timedelta(hours=self.config.study_timeout_hours)
            
            studies_to_remove = []
            for study_name, metadata in self.study_metadata.items():
                last_activity = metadata.get("last_activity", current_time)
                if current_time - last_activity > timeout_delta:
                    studies_to_remove.append(study_name)
            
            for study_name in studies_to_remove:
                if study_name in self.studies:
                    del self.studies[study_name]
                if study_name in self.study_metadata:
                    del self.study_metadata[study_name]
                logger.info(f"🧹 Study removido por inatividade: {study_name}")
    
    def get_or_create_study(self, request: SuggestRequest) -> optuna.Study:
        """Get existing study or create new one."""
        with self.lock:
            study_key = f"{request.study_name}_{request.symbol}"
            
            if study_key in self.studies:
                # Update last activity
                self.study_metadata[study_key]["last_activity"] = datetime.now()
                return self.studies[study_key]
            
            # Check concurrent studies limit
            if len(self.studies) >= self.config.max_concurrent_studies:
                raise HTTPException(
                    status_code=429,
                    detail=f"Maximum concurrent studies limit reached: {self.config.max_concurrent_studies}"
                )
            
            # Create new study
            study = self._create_optuna_study(request, study_key)
            self.studies[study_key] = study
            self.study_metadata[study_key] = {
                "created_at": datetime.now(),
                "last_activity": datetime.now(),
                "symbol": request.symbol,
                "study_name": request.study_name,
                "sampler_type": request.sampler_type,
                "pruning_enabled": request.pruning_enabled
            }
            
            logger.info(f"📊 Novo study criado: {study_key}")
            return study
    
    def _create_optuna_study(self, request: SuggestRequest, study_key: str) -> optuna.Study:
        """Create Optuna study with proper configuration."""
        # Configure sampler
        if request.sampler_type == "TPE":
            sampler = TPESampler(seed=42, n_startup_trials=request.n_startup_trials)
        elif request.sampler_type == "Random":
            sampler = RandomSampler(seed=42)
        elif request.sampler_type == "CmaEs":
            sampler = CmaEsSampler(seed=42)
        else:
            sampler = TPESampler(seed=42, n_startup_trials=request.n_startup_trials)
        
        # Configure pruner
        pruner = MedianPruner(n_startup_trials=5) if request.pruning_enabled else NopPruner()
        
        # Create study
        study = optuna.create_study(
            study_name=study_key,
            direction=request.direction,
            sampler=sampler,
            pruner=pruner,
            storage=self.config.database_url,
            load_if_exists=request.load_if_exists
        )
        
        return study
    
    def suggest_parameters(self, request: SuggestRequest) -> SuggestResponse:
        """Suggest new parameters for optimization."""
        try:
            study = self.get_or_create_study(request)
            
            # Create trial and suggest parameters
            trial = study.ask()
            
            # Suggest parameters within bounds
            parameters = {
                "price_amplification": trial.suggest_float("price_amplification", *request.price_amp_range),
                "news_amplification": trial.suggest_float("news_amplification", *request.news_amp_range),
                "min_confidence": trial.suggest_float("min_confidence", *request.min_conf_range)
            }
            
            # Create response
            response = SuggestResponse(
                study_name=request.study_name,
                symbol=request.symbol,
                trial_number=trial.number,
                parameters=parameters,
                timestamp=datetime.now(),
                trial_id=str(trial.number),  # Use trial number as ID
                sampler_info={"sampler": request.sampler_type}
            )
            
            logger.info(f"💡 Parâmetros sugeridos para {request.symbol}: {parameters}")
            return response
            
        except Exception as e:
            logger.error(f"❌ Erro ao sugerir parâmetros: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    def report_result(self, request: ReportRequest) -> ReportResponse:
        """Report trial result back to study."""
        try:
            study_key = f"{request.study_name}_{request.symbol}"
            
            if study_key not in self.studies:
                raise HTTPException(status_code=404, detail=f"Study not found: {study_key}")
            
            study = self.studies[study_key]
            
            # Find trial by number (trial_id is actually trial number)
            trial_number = int(request.trial_id)

            # Get trial by number
            if trial_number >= len(study.trials):
                raise HTTPException(status_code=404, detail=f"Trial number not found: {trial_number}")

            trial = study.trials[trial_number]

            # Report result using study.tell with trial number
            if request.success:
                study.tell(trial_number, request.objective_value)
                logger.info(f"📈 Resultado reportado para {request.symbol}: {request.objective_value:.4f}")
            else:
                # Report failed trial
                study.tell(trial_number, float('nan'))
                logger.warning(f"⚠️  Trial falhou para {request.symbol}: {request.error_message}")
            
            # Update metadata
            self.study_metadata[study_key]["last_activity"] = datetime.now()
            
            # Create response
            response = ReportResponse(
                study_name=request.study_name,
                symbol=request.symbol,
                trial_id=request.trial_id,
                trial_number=trial.number,
                n_trials=len(study.trials),
                best_value=study.best_value if study.best_trial else None,
                best_parameters=study.best_params if study.best_trial else None,
                timestamp=datetime.now()
            )
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Erro ao reportar resultado: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    def get_study_info(self, study_name: str, symbol: str) -> StudyInfo:
        """Get information about a specific study."""
        study_key = f"{study_name}_{symbol}"
        
        if study_key not in self.studies:
            raise HTTPException(status_code=404, detail=f"Study not found: {study_key}")
        
        study = self.studies[study_key]
        metadata = self.study_metadata[study_key]
        
        # Calculate stats
        successful_trials = len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE])
        failed_trials = len([t for t in study.trials if t.state == optuna.trial.TrialState.FAIL])
        pruned_trials = len([t for t in study.trials if t.state == optuna.trial.TrialState.PRUNED])
        
        durations = [t.duration.total_seconds() for t in study.trials if t.duration]
        avg_duration = sum(durations) / len(durations) if durations else 0.0
        
        return StudyInfo(
            study_name=study_name,
            symbol=symbol,
            direction=study.direction.name,
            n_trials=len(study.trials),
            best_value=study.best_value if study.best_trial else None,
            best_parameters=study.best_params if study.best_trial else None,
            created_at=metadata["created_at"],
            last_updated=metadata["last_activity"],
            sampler_type=metadata["sampler_type"],
            pruning_enabled=metadata["pruning_enabled"],
            successful_trials=successful_trials,
            failed_trials=failed_trials,
            pruned_trials=pruned_trials,
            average_duration=avg_duration
        )
    
    def list_studies(self) -> StudyListResponse:
        """List all active studies."""
        studies = []
        for study_key in self.studies.keys():
            study_name, symbol = study_key.rsplit("_", 1)
            try:
                study_info = self.get_study_info(study_name, symbol)
                studies.append(study_info)
            except Exception as e:
                logger.warning(f"⚠️  Erro ao obter info do study {study_key}: {e}")
        
        return StudyListResponse(
            studies=studies,
            total_count=len(studies),
            timestamp=datetime.now()
        )
    
    def get_health_status(self) -> HealthResponse:
        """Get service health status."""
        # Calculate stats
        uptime = time.time() - self.start_time
        active_studies = len(self.studies)
        total_trials = sum(len(study.trials) for study in self.studies.values())
        
        # System stats
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        cpu_percent = process.cpu_percent()
        
        # Database size
        db_path = self.config.database_url.replace("sqlite:///", "")
        db_size_mb = 0.0
        try:
            if os.path.exists(db_path):
                db_size_mb = os.path.getsize(db_path) / 1024 / 1024
        except:
            pass
        
        return HealthResponse(
            status="healthy",
            timestamp=datetime.now(),
            uptime_seconds=uptime,
            active_studies=active_studies,
            total_trials=total_trials,
            database_status="connected",
            database_size_mb=db_size_mb,
            memory_usage_mb=memory_mb,
            cpu_usage_percent=cpu_percent
        )


# Global service instance
service: Optional[BayesOptService] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI lifespan manager."""
    global service
    
    # Startup
    config = ServiceConfig()
    service = BayesOptService(config)
    logger.info("🚀 BayesOpt Microservice iniciado")
    
    yield
    
    # Shutdown
    logger.info("🛑 BayesOpt Microservice finalizando")


# Create FastAPI app
app = FastAPI(
    title="QUALIA BayesOpt Microservice",
    description="Distributed Bayesian Optimization with Optuna + SQLite",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def get_service() -> BayesOptService:
    """Dependency to get service instance."""
    global service
    if service is None:
        # Create service instance if not exists (for testing)
        config = ServiceConfig()
        service = BayesOptService(config)
        logger.info("🚀 BayesOpt Service instance criada para teste")
    return service


# REST Endpoints

@app.post("/suggest", response_model=SuggestResponse)
async def suggest_parameters(
    request: SuggestRequest,
    svc: BayesOptService = Depends(get_service)
) -> SuggestResponse:
    """Suggest new parameters for optimization."""
    return svc.suggest_parameters(request)


@app.post("/report", response_model=ReportResponse)
async def report_result(
    request: ReportRequest,
    svc: BayesOptService = Depends(get_service)
) -> ReportResponse:
    """Report trial result back to study."""
    return svc.report_result(request)


@app.get("/studies", response_model=StudyListResponse)
async def list_studies(
    svc: BayesOptService = Depends(get_service)
) -> StudyListResponse:
    """List all active studies."""
    return svc.list_studies()


@app.get("/studies/{study_name}/{symbol}", response_model=StudyInfo)
async def get_study_info(
    study_name: str,
    symbol: str,
    svc: BayesOptService = Depends(get_service)
) -> StudyInfo:
    """Get detailed information about a specific study."""
    return svc.get_study_info(study_name, symbol)


@app.get("/health", response_model=HealthResponse)
async def health_check(
    svc: BayesOptService = Depends(get_service)
) -> HealthResponse:
    """Health check endpoint."""
    return svc.get_health_status()


@app.get("/")
async def root():
    """Root endpoint with service info."""
    return {
        "service": "QUALIA BayesOpt Microservice",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "suggest": "POST /suggest - Request parameter suggestions",
            "report": "POST /report - Report trial results",
            "studies": "GET /studies - List all studies",
            "study_info": "GET /studies/{study_name}/{symbol} - Get study details",
            "health": "GET /health - Health check"
        }
    }
