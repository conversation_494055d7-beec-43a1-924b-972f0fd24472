// Revisado em 2025-06-13 por Codex
/**
 * QUALIA: Interface Principal
 * 
 * Gerencia a interação do usuário com o sistema QUALIA,
 * estabelecendo conexões com a API e atualizando visualizações.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Flag global de depuração
    window.DEBUG = window.DEBUG || false;
    // Elementos da UI
    const processCycleBtn = document.getElementById('process-cycle-btn');
    const processSymbolsBtn = document.getElementById('process-symbols-btn');
    const triggerReflectionBtn = document.getElementById('trigger-reflection-btn');
    const lastUpdateElem = document.getElementById('last-update');
    const symbolsInput = document.getElementById('symbolic-input');
    const reflectionTopic = document.getElementById('reflection-topic');
    const statusIndicators = document.querySelectorAll('.indicator-led');

    // Elementos de resultado
    const symbolResults = document.getElementById('symbolic-results');
    const patternsContainer = document.getElementById('patterns-container');
    const reflectionLog = document.getElementById('reflection-log');
    
    // Elementos de medição
    const entropyGauge = document.getElementById('entropy-gauge');
    const complexityGauge = document.getElementById('complexity-gauge');
    const diversityGauge = document.getElementById('diversity-gauge');
    
    // Valores de parâmetros 
    const paramQubits = document.getElementById('param-qubits');
    const paramDepth = document.getElementById('param-depth');
    const paramThermal = document.getElementById('param-thermal');
    const paramEntropy = document.getElementById('param-entropy');

    // Controle de estado
    let stateLoaded = false;
    let systemRunning = false;

    // Configurações de animação
    const ANIMATION_DURATION = 1000;
    
    // Cores
    const COLORS = {
        active: '#22AABB',
        error: '#9E2A2B',
        warning: '#E09F3E',
        inactive: '#4A4A6A'
    };

    /**
     * Inicializa a interface e carrega o estado inicial
     */
    function initInterface() {
        // Configurar event listeners
        if (processCycleBtn) {
            processCycleBtn.addEventListener('click', processCycle);
        }
        
        if (processSymbolsBtn) {
            processSymbolsBtn.addEventListener('click', processSymbols);
        }
        
        if (triggerReflectionBtn) {
            triggerReflectionBtn.addEventListener('click', triggerReflection);
        }
        
        // Ativar indicadores de status
        activateStatusIndicators(true);
        
        // Carregar estado inicial do sistema
        fetchConsciousnessState();
        
        // Configurar atualizações periódicas
        setInterval(fetchConsciousnessState, 30000); // Atualizar a cada 30 segundos
    }

    /**
     * Busca o estado atual da consciência QUALIA da API
     */
    function fetchConsciousnessState() {
        showSystemActivity();
        
        if (window.DEBUG) {
            console.log("Buscando estado da consciência...");
        }
        fetch('/api/consciousness/state')
            .then(response => {
                if (window.DEBUG) {
                    console.log("Resposta recebida:", response.status, response.statusText);
                }
                if (!response.ok) {
                    throw new Error(`Erro ao buscar estado (${response.status}): ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (window.DEBUG) {
                    console.log("Dados da consciência recebidos:", data);
                }
                updateConsciousnessState(data);
                stateLoaded = true;
                systemRunning = true;
                hideSystemActivity();
            })
            .catch(error => {
                console.error('Erro ao carregar estado da consciência:', error);
                showError('Não foi possível carregar o estado da consciência QUALIA.');
                systemRunning = false;
                hideSystemActivity();
            });
    }

    /**
     * Atualiza a interface com os dados do estado da consciência
     */
    function updateConsciousnessState(data) {
        // Atualizar parâmetros
        if (data.parameters) {
            if (paramQubits) paramQubits.textContent = data.parameters.n_qubits || '8';
            if (paramDepth) paramDepth.textContent = data.parameters.perception_depth || '3';
            if (paramThermal) paramThermal.textContent = data.parameters.thermal_coefficient || '0.1';
            if (paramEntropy) paramEntropy.textContent = data.parameters.entropy_sensitivity || '0.02';
        }
        
        // Atualizar timestamp
        if (lastUpdateElem) {
            const timestamp = document.querySelector('.timestamp');
            if (timestamp) {
                const date = new Date(data.timestamp);
                timestamp.textContent = formatDateTime(date);
            }
        }
        
        // Atualizar visualização de estado quântico
        if (window.updateQuantumStateViz && data.quantum_state) {
            window.updateQuantumStateViz(data.quantum_state);
        }
        
        // Atualizar visualização QAST se houver histórico
        if (window.updateQastViz && data.qast_cycles && data.qast_cycles.length > 0) {
            // Transformar dados para o formato da visualização QAST
            const qastData = transformQastCyclesToGraph(data.qast_cycles);
            window.updateQastViz(qastData);
            
            // Atualizar gráficos de evolução de métricas
            if (window.metricVizFunctions) {
                if (window.metricVizFunctions.entropia) {
                    const entropyValues = data.qast_cycles.map(cycle => cycle.entropy);
                    window.metricVizFunctions.entropia(entropyValues);
                }
                
                if (window.metricVizFunctions.coerência) {
                    const coherenceValues = data.qast_cycles.map(cycle => cycle.coherence);
                    window.metricVizFunctions.coerência(coherenceValues);
                }
            }
        }
    }

    /**
     * Transforma dados de ciclos QAST em formato adequado para visualização de grafo
     */
    function transformQastCyclesToGraph(qastCycles) {
        const nodes = [];
        const links = [];
        
        // Criar nós para cada ciclo
        qastCycles.forEach((cycle, index) => {
            // Calcular cor baseada na entropia e coerência
            const entropyNorm = Math.min(1, Math.max(0, cycle.entropy));
            const coherenceNorm = Math.min(1, Math.max(0, cycle.coherence));
            
            // Interpolação de cor entre vermelho (alta entropia) e ciano (alta coerência)
            let r = Math.floor(158 + entropyNorm * 97);  // 158-255 (vermelho)
            let g = Math.floor(42 + coherenceNorm * 128); // 42-170 (verde)
            let b = Math.floor(43 + coherenceNorm * 144); // 43-187 (azul)
            
            // Ajustar para coerência dominante
            if (coherenceNorm > entropyNorm) {
                r = Math.floor(34 + (1 - coherenceNorm) * 124);  // 34-158
                g = Math.floor(170 - (1 - coherenceNorm) * 128); // 42-170
                b = Math.floor(187 - (1 - coherenceNorm) * 144); // 43-187
            }
            
            const color = `rgb(${r},${g},${b})`;
            
            // Adicionar nó
            nodes.push({
                id: `cycle-${index}`,
                label: `${index}`,
                size: 8 + Math.min(cycle.patterns_detected || 0, 5) * 2,
                color: color,
                entropy: cycle.entropy,
                coherence: cycle.coherence,
                patterns: cycle.patterns_detected || 0,
                timestamp: cycle.timestamp
            });
            
            // Conectar com o ciclo anterior
            if (index > 0) {
                links.push({
                    source: `cycle-${index-1}`,
                    target: `cycle-${index}`,
                    color: color,
                    opacity: 0.6,
                    weight: 1 + Math.min(cycle.patterns_detected || 0, 5) / 5
                });
            }
        });
        
        return { nodes, links };
    }

    /**
     * Processa um ciclo QAST
     */
    function processCycle() {
        if (!systemRunning) {
            showError('O sistema QUALIA não está respondendo. Tente novamente mais tarde.');
            return;
        }
        
        showSystemActivity();
        animateButton(processCycleBtn);
        
        fetch('/api/consciousness/process', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Erro ao processar ciclo (${response.status}): ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showSuccess('Ciclo QAST processado com sucesso');
                // Atualizar estado após processamento
                fetchConsciousnessState();
            } else {
                showWarning(data.message || 'Processamento concluído com avisos');
            }
            hideSystemActivity();
        })
        .catch(error => {
            console.error('Erro ao processar ciclo QAST:', error);
            showError('Não foi possível processar o ciclo QAST.');
            hideSystemActivity();
        });
    }

    /**
     * Processa dados simbólicos
     */
    function processSymbols() {
        if (!systemRunning) {
            showError('O sistema QUALIA não está respondendo. Tente novamente mais tarde.');
            return;
        }
        
        const symbols = symbolsInput.value.trim();
        if (!symbols) {
            showWarning('Insira algum texto para análise simbólica.');
            return;
        }
        
        showSystemActivity();
        animateButton(processSymbolsBtn);
        
        fetch('/api/symbolic/process', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ symbols })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Erro ao processar símbolos (${response.status}): ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                updateSymbolicResults(data);
                showSuccess('Análise simbólica concluída');
            } else {
                showWarning(data.message || 'Processamento concluído com avisos');
            }
            hideSystemActivity();
        })
        .catch(error => {
            console.error('Erro ao processar dados simbólicos:', error);
            showError('Não foi possível processar a análise simbólica.');
            hideSystemActivity();
        });
    }

    /**
     * Atualiza resultados da análise simbólica
     */
    function updateSymbolicResults(data) {
        // Atualizar indicadores de entropia
        if (data.entropic_analysis) {
            const analysis = data.entropic_analysis;
            
            if (entropyGauge) {
                const entropyValue = entropyGauge.querySelector('.gauge-value');
                if (entropyValue) {
                    entropyValue.textContent = analysis.entropy.toFixed(4);
                    animateValue(entropyValue);
                }
            }
            
            if (complexityGauge) {
                const complexityValue = complexityGauge.querySelector('.gauge-value');
                if (complexityValue) {
                    complexityValue.textContent = analysis.complexity.toFixed(4);
                    animateValue(complexityValue);
                }
            }
            
            if (diversityGauge) {
                const diversityValue = diversityGauge.querySelector('.gauge-value');
                if (diversityValue) {
                    diversityValue.textContent = analysis.diversity.toFixed(4);
                    animateValue(diversityValue);
                }
            }
        }
        
        // Atualizar padrões detectados
        if (patternsContainer) {
            if (!data.patterns || data.patterns.length === 0) {
                patternsContainer.innerHTML = DOMPurify.sanitize(
                    '<div class="empty-message">Nenhum padrão recorrente detectado</div>'
                );
            } else {
                patternsContainer.textContent = '';
                
                // Criar elementos para cada padrão
                data.patterns.forEach(pattern => {
                    const patternElement = document.createElement('div');
                    patternElement.className = 'pattern-item';
                    
                    const patternContent = `
                        <div class="pattern-sequence">${pattern.sequence}</div>
                        <div class="pattern-info">
                            <span class="pattern-freq">Frequência: ${pattern.frequency}</span>
                            <span class="pattern-significance">Significância: ${pattern.significance.toFixed(2)}</span>
                        </div>
                    `;
                    
                    patternElement.innerHTML = DOMPurify.sanitize(patternContent);
                    patternsContainer.appendChild(patternElement);
                    
                    // Animar entrada
                    setTimeout(() => {
                        patternElement.style.opacity = '1';
                        patternElement.style.transform = 'translateY(0)';
                    }, 100);
                });
            }
        }
    }

    /**
     * Dispara o processo de auto-reflexão
     */
    function triggerReflection() {
        if (!systemRunning) {
            showError('O sistema QUALIA não está respondendo. Tente novamente mais tarde.');
            return;
        }
        
        const topic = reflectionTopic.value.trim();
        if (!topic) {
            showWarning('Insira um tópico para auto-reflexão.');
            return;
        }
        
        showSystemActivity();
        animateButton(triggerReflectionBtn);
        
        fetch('/api/reflection/execute', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ topic })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Erro ao executar auto-reflexão (${response.status}): ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                updateReflectionResults(data);
                showSuccess('Auto-reflexão concluída');
            } else {
                showWarning(data.message || 'Processamento concluído com avisos');
            }
            hideSystemActivity();
        })
        .catch(error => {
            console.error('Erro ao executar auto-reflexão:', error);
            showError('Não foi possível processar a auto-reflexão.');
            hideSystemActivity();
        });
    }

    /**
     * Atualiza resultados da auto-reflexão
     */
    function updateReflectionResults(data) {
        if (!reflectionLog) return;
        
        if (!data.reflection_log || data.reflection_log.length === 0) {
            reflectionLog.innerHTML = DOMPurify.sanitize(
                '<div class="empty-message">Nenhum registro de auto-reflexão</div>'
            );
            return;
        }

        reflectionLog.textContent = '';
        
        // Criar elementos para cada entrada do log
        data.reflection_log.forEach((entry, index) => {
            const entryElement = document.createElement('div');
            entryElement.className = 'reflection-entry';
            
            // Formatar data
            let formattedTime = '';
            if (entry.timestamp) {
                const date = new Date(entry.timestamp);
                formattedTime = formatDateTime(date);
            }
            
            const entryContent = `
                <div class="reflection-time">${formattedTime}</div>
                <div class="reflection-content">${entry.insight}</div>
            `;
            
            entryElement.innerHTML = DOMPurify.sanitize(entryContent);
            reflectionLog.appendChild(entryElement);
            
            // Animar entrada
            setTimeout(() => {
                entryElement.style.opacity = '1';
                entryElement.style.transform = 'translateY(0)';
            }, 100 * index);
        });
    }

    /**
     * Mostra indicação de atividade do sistema
     */
    function showSystemActivity() {
        // Ativar indicadores de status
        activateStatusIndicators();
        
        // Adicionar classe à body
        document.body.classList.add('system-active');
    }

    /**
     * Esconde indicação de atividade do sistema
     */
    function hideSystemActivity() {
        // Normalizar indicadores após delay
        setTimeout(() => {
            activateStatusIndicators(false);
            document.body.classList.remove('system-active');
        }, 500);
    }

    /**
     * Anima um botão durante processamento
     */
    function animateButton(button) {
        if (!button) return;
        
        // Adicionar classe de animação
        button.classList.add('processing');
        
        // Armazenar texto original
        const originalText = button.textContent;
        button.textContent = 'Processando...';
        
        // Restaurar após animação
        setTimeout(() => {
            button.classList.remove('processing');
            button.textContent = originalText;
        }, ANIMATION_DURATION);
    }

    /**
     * Anima um valor durante atualização
     */
    function animateValue(element) {
        if (!element) return;
        
        // Adicionar classe de animação
        element.classList.add('updating');
        
        // Restaurar após animação
        setTimeout(() => {
            element.classList.remove('updating');
        }, ANIMATION_DURATION);
    }

    /**
     * Ativa/desativa indicadores de status
     */
    function activateStatusIndicators(active = true) {
        if (!statusIndicators || statusIndicators.length === 0) return;
        
        statusIndicators.forEach(indicator => {
            if (active) {
                // Efeito de pulsação rápida
                indicator.style.animation = 'pulse 0.8s infinite alternate';
                indicator.style.boxShadow = '0 0 10px var(--coherence-cyan)';
            } else {
                // Efeito normal
                indicator.style.animation = 'pulse 2s infinite alternate';
                indicator.style.boxShadow = '0 0 5px var(--coherence-cyan)';
            }
        });
    }

    /**
     * Mostra mensagem de sucesso temporária
     */
    function showSuccess(message) {
        showNotification(message, 'success');
    }

    /**
     * Mostra mensagem de erro temporária
     */
    function showError(message) {
        showNotification(message, 'error');
    }

    /**
     * Mostra mensagem de aviso temporária
     */
    function showWarning(message) {
        showNotification(message, 'warning');
    }

    /**
     * Mostra notificação temporária
     */
    function showNotification(message, type = 'info') {
        // Verificar se container de notificações existe
        let notificationContainer = document.querySelector('.notification-container');
        
        // Criar container se não existir
        if (!notificationContainer) {
            notificationContainer = document.createElement('div');
            notificationContainer.className = 'notification-container';
            document.body.appendChild(notificationContainer);
        }
        
        // Definir cor baseada no tipo
        let color;
        switch (type) {
            case 'success':
                color = COLORS.active;
                break;
            case 'error':
                color = COLORS.error;
                break;
            case 'warning':
                color = COLORS.warning;
                break;
            default:
                color = COLORS.active;
        }
        
        // Criar elemento de notificação
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = DOMPurify.sanitize(
            `<div class="notification-message">${message}</div>`
        );
        notification.style.borderColor = color;
        notification.style.boxShadow = `0 0 10px ${color}`;
        
        // Adicionar ao container
        notificationContainer.appendChild(notification);
        
        // Animar entrada
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateY(0)';
        }, 10);
        
        // Remover após timeout
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
            
            // Remover do DOM após fade out
            setTimeout(() => {
                notification.remove();
                
                // Remover container se vazio
                if (notificationContainer.children.length === 0) {
                    notificationContainer.remove();
                }
            }, 500);
        }, 3000);
    }

    /**
     * Formata data/hora para exibição
     */
    function formatDateTime(date) {
        if (!(date instanceof Date)) return '';
        
        const options = { 
            year: 'numeric', 
            month: 'numeric', 
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        };
        
        return date.toLocaleString('pt-BR', options);
    }

    // Adicionar CSS para notificações
    function addNotificationStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .notification-container {
                position: fixed;
                top: 20px;
                right: 20px;
                max-width: 300px;
                z-index: 9999;
            }
            
            .notification {
                background: rgba(10, 10, 26, 0.9);
                border-left: 3px solid var(--coherence-cyan);
                border-radius: 4px;
                padding: 12px 15px;
                margin-bottom: 10px;
                box-shadow: 0 0 10px var(--coherence-cyan);
                color: var(--consciousness-white);
                font-size: 14px;
                opacity: 0;
                transform: translateY(-20px);
                transition: all 0.3s ease;
                backdrop-filter: blur(5px);
                -webkit-backdrop-filter: blur(5px);
            }
            
            .notification-message {
                display: flex;
                align-items: center;
                line-height: 1.4;
            }
            
            .system-active {
                position: relative;
            }
            
            .system-active::after {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(90deg, transparent, var(--coherence-cyan), transparent);
                animation: scan 1.5s linear infinite;
                opacity: 0.7;
                z-index: 9998;
            }
            
            @keyframes scan {
                0% {
                    transform: translateY(-2px);
                }
                100% {
                    transform: translateY(100vh);
                }
            }
            
            .quantum-button.processing {
                background: linear-gradient(90deg, var(--probability-purple), var(--quantum-blue));
                opacity: 0.8;
                pointer-events: none;
            }
            
            .updating {
                animation: flash 1s;
            }
            
            @keyframes flash {
                0%, 100% {
                    color: var(--consciousness-white);
                }
                50% {
                    color: var(--coherence-cyan);
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Adicionar estilos e inicializar interface
    addNotificationStyles();
    initInterface();
});