# Folding Operator

O operador de *Folding* aplica uma redução dimensional inspirada em mecânica quântica para simplificar séries temporais sem perder a estrutura essencial. Ele utiliza uma matriz unitária derivada de um Hamiltoniano e pode reverter a transformação via `unfold`.

Principais características
-------------------------
- Escalonamento por proporções da razão áurea para preservar correlações.
- Evolução unitária pré-computada com `expm` para desempenho.
- Cálculo de coerência entre componentes dobrados e faseados.
- Histórico de estados para análise posterior.
- Métrica `evaluate_information_loss` em `qualia.core.folding` calcula a perda de
  informação normalizada após um ciclo de `fold` e `unfold`.

A função utilitária `apply_folding` oferece uma transformação leve usada em testes automatizados.

## Exemplo de Uso
```python
import numpy as np
from qualia.core.folding import FoldingOperator

config = {"dimensions": 4}
operator = FoldingOperator(config)

# Simular dados de mercado
arr = np.random.randn(10, 4)
state = asyncio.run(operator.fold(arr, timestamp=0.0))
print(state.coherence)
``` 
