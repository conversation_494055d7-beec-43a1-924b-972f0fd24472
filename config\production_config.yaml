# QUALIA Production Configuration
# Optimized parameters for live trading with real capital
# Generated: 2025-01-07
# Environment: PRODUCTION

# =============================================================================
# PRODUCTION ENVIRONMENT SETTINGS
# =============================================================================
environment:
  name: "production"
  mode: "live_trading"
  debug: false
  verbose_logging: false
  log_level: "INFO"
  
# =============================================================================
# CAPITAL AND RISK MANAGEMENT
# =============================================================================
capital:
  initial_capital: 1000.0  # Starting with $1K for pilot
  max_capital: 100000.0    # Maximum capital limit
  currency: "USDT"
  
risk_management:
  max_position_size: 0.02          # 2% of capital per position
  max_daily_drawdown: 0.05         # 5% daily drawdown limit
  max_total_drawdown: 0.15         # 15% total drawdown limit
  max_correlation: 0.6             # 60% maximum correlation
  min_liquidity: 100000            # $100K minimum liquidity
  emergency_stop: true             # Emergency stop enabled
  stop_loss_percentage: 0.08       # 8% stop loss
  take_profit_percentage: 0.16     # 16% take profit (2:1 ratio)

# =============================================================================
# OPTIMIZED TRADING PARAMETERS (From Bayesian Optimization)
# =============================================================================
trading:
  # Optimized parameters from D-04 Bayesian Optimization
  news_amplification: 11.3         # Optimal news amplification
  price_amplification: 1.0         # Optimal price amplification  
  min_confidence: 0.37             # Optimal minimum confidence
  
  # Trading behavior
  max_positions: 3                 # Maximum concurrent positions
  position_timeout: 14400          # 4 hours position timeout
  rebalance_frequency: 3600        # 1 hour rebalance frequency
  
  # Execution settings
  execution_mode: "market"         # Market orders for speed
  slippage_tolerance: 0.001        # 0.1% slippage tolerance
  fill_timeout: 30                 # 30 seconds fill timeout

# =============================================================================
# SYMBOLS AND MARKETS
# =============================================================================
symbols:
  primary: "BTC/USDT"              # Primary trading pair for pilot
  secondary: []                    # Additional pairs (empty for pilot)
  
  # Symbol-specific settings
  BTC/USDT:
    min_trade_size: 0.001
    max_trade_size: 0.1
    tick_size: 0.01
    lot_size: 0.000001

# =============================================================================
# EXCHANGE CONFIGURATION
# =============================================================================
exchange:
  name: "kucoin"
  environment: "production"        # Production environment
  
  # API settings
  api:
    rate_limit: 10                 # 10 requests per second
    timeout: 30                    # 30 seconds timeout
    retry_attempts: 3              # 3 retry attempts
    retry_delay: 1                 # 1 second retry delay
  
  # Connection settings
  websocket:
    ping_interval: 20              # 20 seconds ping interval
    ping_timeout: 10               # 10 seconds ping timeout
    reconnect_attempts: 5          # 5 reconnection attempts
    reconnect_delay: 5             # 5 seconds reconnection delay

# =============================================================================
# BAYESIAN OPTIMIZATION SETTINGS
# =============================================================================
optimization:
  enabled: true                    # Enable continuous optimization
  frequency: 86400                 # 24 hours optimization cycle
  trials_per_cycle: 25             # 25 trials per optimization cycle
  
  # Parameter bounds (safety limits)
  bounds:
    news_amplification: [1.0, 20.0]
    price_amplification: [0.1, 5.0]
    min_confidence: [0.1, 0.8]
  
  # Optimization targets
  objectives:
    primary: "sharpe_ratio"        # Primary optimization target
    secondary: "total_return"      # Secondary target
    constraint: "max_drawdown"     # Constraint to respect

# =============================================================================
# MONITORING AND ALERTING
# =============================================================================
monitoring:
  enabled: true
  frequency: 60                    # 1 minute monitoring frequency
  
  # Performance thresholds
  thresholds:
    min_sharpe_ratio: 2.0          # Minimum acceptable Sharpe ratio
    max_drawdown_alert: 0.08       # 8% drawdown triggers alert
    min_win_rate: 0.55             # 55% minimum win rate
    max_consecutive_losses: 5      # 5 consecutive losses trigger alert
  
  # Alert channels
  alerts:
    email:
      enabled: true
      smtp_server: "smtp.gmail.com"
      smtp_port: 587
      recipients: ["<EMAIL>"]
    
    slack:
      enabled: false               # Disabled for pilot
      webhook_url: ""
    
    sms:
      enabled: false               # Disabled for pilot
      provider: ""

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
logging:
  level: "INFO"                    # Production log level
  format: "json"                   # Structured JSON logging
  
  # Log files
  files:
    main: "logs/qualia_production.log"
    trading: "logs/trading.log"
    optimization: "logs/optimization.log"
    errors: "logs/errors.log"
  
  # Log rotation
  rotation:
    max_size: "100MB"              # 100MB max file size
    backup_count: 10               # Keep 10 backup files
    when: "midnight"               # Rotate at midnight
  
  # Log correlation
  correlation:
    enabled: true                  # Enable correlation IDs
    header: "X-Correlation-ID"

# =============================================================================
# BACKUP AND RECOVERY
# =============================================================================
backup:
  enabled: true
  frequency: 3600                  # 1 hour backup frequency
  
  # Backup targets
  targets:
    - "data/positions.json"
    - "data/trades.json"
    - "data/optimization_results.json"
    - "config/production_config.yaml"
  
  # Backup storage
  storage:
    local:
      enabled: true
      path: "backups/"
      retention_days: 30
    
    cloud:
      enabled: false               # Disabled for pilot
      provider: ""
      bucket: ""

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
security:
  # API key management
  credentials:
    encryption: true               # Encrypt stored credentials
    rotation_days: 30              # Rotate keys every 30 days
    validation_frequency: 3600     # Validate keys every hour
  
  # Access control
  access:
    ip_whitelist: []               # Empty for now
    rate_limiting: true            # Enable rate limiting
    audit_logging: true            # Enable audit logs

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================
performance:
  # Memory management
  memory:
    max_usage: "2GB"               # 2GB memory limit
    gc_frequency: 300              # 5 minutes garbage collection
  
  # CPU management
  cpu:
    max_usage: 0.8                 # 80% CPU usage limit
    thread_pool_size: 4            # 4 worker threads
  
  # Data management
  data:
    cache_size: 1000               # 1000 items cache
    history_retention: 86400       # 24 hours data retention

# =============================================================================
# FEATURE FLAGS
# =============================================================================
features:
  hot_reload: true                 # Enable hot configuration reload
  a_b_testing: true                # Enable A/B testing framework
  regime_detection: true           # Enable market regime detection
  news_sentiment: true             # Enable news sentiment analysis
  technical_indicators: true       # Enable technical indicators
  
# =============================================================================
# PRODUCTION VALIDATION
# =============================================================================
validation:
  # Pre-trading checks
  pre_trading:
    - "validate_credentials"
    - "check_connectivity"
    - "verify_balance"
    - "test_order_placement"
  
  # Runtime checks
  runtime:
    - "monitor_performance"
    - "check_risk_limits"
    - "validate_positions"
    - "monitor_drawdown"
  
  # Post-trading checks
  post_trading:
    - "reconcile_positions"
    - "backup_data"
    - "generate_reports"
    - "update_metrics"

# =============================================================================
# METADATA
# =============================================================================
metadata:
  version: "1.0.0"
  created: "2025-01-07T00:00:00Z"
  updated: "2025-01-07T00:00:00Z"
  author: "YAA (Yet Another Agent)"
  description: "QUALIA Production Configuration - Optimized for live trading"
  
  # Configuration hash for integrity
  checksum: "sha256:production_config_v1.0.0"
  
  # Environment info
  environment_info:
    python_version: "3.11+"
    required_packages:
      - "ccxt>=4.0.0"
      - "optuna>=3.0.0"
      - "prometheus_client>=0.15.0"
      - "pyyaml>=6.0"
      - "asyncio"
      - "aiohttp"
