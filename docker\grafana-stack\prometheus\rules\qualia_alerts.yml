groups:
  - name: qualia_trading_alerts
    rules:
      # CRITICAL: Drawdown excessivo
      - alert: QualiaExcessiveDrawdown
        expr: qualia_hyperparams_max_drawdown_pct > 25
        for: 2m
        labels:
          severity: critical
          component: trading
          alert_type: drawdown
        annotations:
          summary: "QUALIA: Drawdown excessivo detectado"
          description: "Drawdown atual de {{ $value }}% excede o limite de 25%. Sistema pode estar em risco."
          runbook_url: "https://docs.qualia.ai/alerts/drawdown"

      # CRITICAL: Perda de capital significativa
      - alert: QualiaSignificantLoss
        expr: qualia_hyperparams_total_return_pct < -15
        for: 5m
        labels:
          severity: critical
          component: trading
          alert_type: loss
        annotations:
          summary: "QUALIA: Perda significativa de capital"
          description: "Retorno total de {{ $value }}% indica perda significativa. Revisão urgente necessária."

      # WARNING: Sharpe ratio baixo
      - alert: QualiaLowSharpeRatio
        expr: qualia_hyperparams_sharpe_ratio < 0.5
        for: 10m
        labels:
          severity: warning
          component: trading
          alert_type: performance
        annotations:
          summary: "QUALIA: Sharpe ratio baixo"
          description: "Sharpe ratio de {{ $value }} está abaixo do esperado (>0.5). Performance pode estar degradando."

      # WARNING: Taxa de decisão muito baixa
      - alert: QualiaLowDecisionRate
        expr: qualia_hyperparams_decision_rate < 2
        for: 15m
        labels:
          severity: warning
          component: trading
          alert_type: activity
        annotations:
          summary: "QUALIA: Taxa de decisão muito baixa"
          description: "Taxa de decisão de {{ $value }} decisões/hora está muito baixa. Sistema pode estar inativo."

      # WARNING: Confiança média baixa
      - alert: QualiaLowConfidence
        expr: avg_over_time(qualia_hyperparams_final_confidence[1h]) < 0.4
        for: 20m
        labels:
          severity: warning
          component: calibration
          alert_type: confidence
        annotations:
          summary: "QUALIA: Confiança média baixa"
          description: "Confiança média de {{ $value }} na última hora está baixa. Calibração pode ser necessária."

      # INFO: Sistema inativo
      - alert: QualiaSystemInactive
        expr: increase(qualia_hyperparams_decisions_total[30m]) == 0
        for: 30m
        labels:
          severity: info
          component: system
          alert_type: activity
        annotations:
          summary: "QUALIA: Sistema inativo"
          description: "Nenhuma decisão tomada nos últimos 30 minutos. Verificar se sistema está funcionando."

  - name: qualia_system_alerts
    rules:
      # CRITICAL: Sistema offline
      - alert: QualiaSystemDown
        expr: up{job="qualia-trading"} == 0
        for: 1m
        labels:
          severity: critical
          component: system
          alert_type: availability
        annotations:
          summary: "QUALIA: Sistema offline"
          description: "Sistema QUALIA não está respondendo. Intervenção imediata necessária."

      # WARNING: Alta latência
      - alert: QualiaHighLatency
        expr: qualia_hyperparams_processing_latency_ms > 1000
        for: 5m
        labels:
          severity: warning
          component: performance
          alert_type: latency
        annotations:
          summary: "QUALIA: Alta latência de processamento"
          description: "Latência de {{ $value }}ms está acima do normal. Performance pode estar degradada."

      # WARNING: Uso alto de CPU
      - alert: QualiaHighCPU
        expr: rate(process_cpu_seconds_total{job="qualia-trading"}[5m]) * 100 > 80
        for: 10m
        labels:
          severity: warning
          component: system
          alert_type: resource
        annotations:
          summary: "QUALIA: Alto uso de CPU"
          description: "Uso de CPU de {{ $value }}% está alto. Monitorar performance do sistema."

  - name: qualia_optimization_alerts
    rules:
      # WARNING: Otimização Bayesiana com muitas falhas
      - alert: QualiaOptimizationFailures
        expr: rate(qualia_bayesian_optimizer_failed_trials_total[1h]) > 0.1
        for: 15m
        labels:
          severity: warning
          component: optimization
          alert_type: failure
        annotations:
          summary: "QUALIA: Muitas falhas na otimização"
          description: "Taxa de falhas de {{ $value }} trials/hora na otimização Bayesiana está alta."

      # INFO: Novo melhor resultado encontrado
      - alert: QualiaNewBestResult
        expr: increase(qualia_bayesian_optimizer_best_score_updates_total[1h]) > 0
        for: 0s
        labels:
          severity: info
          component: optimization
          alert_type: improvement
        annotations:
          summary: "QUALIA: Novo melhor resultado encontrado"
          description: "Otimização Bayesiana encontrou nova configuração com melhor performance."
