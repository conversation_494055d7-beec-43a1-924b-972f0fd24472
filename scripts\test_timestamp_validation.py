#!/usr/bin/env python3
"""
Script de teste para validar a correção do timestamp futuro inválido.
Testa se a validação detecta e corrige timestamps como 1753403940001.
"""

import time
import sys
import os
from datetime import datetime, timezone

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def test_timestamp_validation():
    """Testa a validação de timestamp futuro"""
    print("🔍 TESTE DE VALIDAÇÃO DE TIMESTAMP FUTURO")
    print("=" * 50)
    
    # Timestamp problemático dos logs
    problematic_timestamp = 1753403940001
    current_timestamp = int(time.time() * 1000)
    
    print(f"📅 Timestamp atual: {current_timestamp}")
    print(f"📅 Timestamp problemático: {problematic_timestamp}")
    
    # Converter para datas legíveis
    current_date = datetime.fromtimestamp(current_timestamp / 1000, tz=timezone.utc)
    problematic_date = datetime.fromtimestamp(problematic_timestamp / 1000, tz=timezone.utc)
    
    print(f"📅 Data atual: {current_date}")
    print(f"📅 Data problemática: {problematic_date}")
    
    # Calcular diferença
    diff_ms = problematic_timestamp - current_timestamp
    diff_hours = diff_ms / (60 * 60 * 1000)
    diff_days = diff_hours / 24
    diff_years = diff_days / 365
    
    print(f"⏰ Diferença: {diff_ms:,} ms")
    print(f"⏰ Diferença: {diff_hours:,.1f} horas")
    print(f"⏰ Diferença: {diff_days:,.1f} dias")
    print(f"⏰ Diferença: {diff_years:,.1f} anos")
    
    # Testar validação
    max_future_ms = current_timestamp + (60 * 60 * 1000)  # 1 hora no futuro
    is_future = problematic_timestamp > max_future_ms
    
    print(f"\n🔍 VALIDAÇÃO:")
    print(f"   Limite máximo futuro: {max_future_ms}")
    print(f"   Timestamp é futuro inválido: {is_future}")
    
    if is_future:
        print(f"   ✅ CORREÇÃO: Timestamp seria detectado e resetado")
        print(f"   ⚠️ Cache seria limpo para evitar repetição")
    else:
        print(f"   ❌ ERRO: Timestamp não seria detectado como inválido")
    
    return is_future

def test_edge_cases():
    """Testa casos extremos da validação"""
    print(f"\n🔍 TESTE DE CASOS EXTREMOS")
    print("=" * 30)
    
    current_timestamp = int(time.time() * 1000)
    max_future_ms = current_timestamp + (60 * 60 * 1000)  # 1 hora no futuro
    
    test_cases = [
        ("Timestamp atual", current_timestamp),
        ("30 min no futuro", current_timestamp + (30 * 60 * 1000)),
        ("1 hora no futuro (limite)", max_future_ms),
        ("2 horas no futuro", current_timestamp + (2 * 60 * 60 * 1000)),
        ("1 dia no futuro", current_timestamp + (24 * 60 * 60 * 1000)),
        ("Timestamp problemático", 1753403940001),
    ]
    
    for name, timestamp in test_cases:
        is_future = timestamp > max_future_ms
        status = "❌ INVÁLIDO" if is_future else "✅ VÁLIDO"
        print(f"   {name}: {status}")
    
    return True

def test_correction_logic():
    """Testa a lógica de correção implementada"""
    print(f"\n🔍 TESTE DA LÓGICA DE CORREÇÃO")
    print("=" * 35)
    
    # Simular a lógica implementada
    request_since = 1753403940001  # Timestamp problemático
    current_time_ms = int(time.time() * 1000)
    max_future_ms = current_time_ms + (60 * 60 * 1000)  # 1 hora no futuro
    
    print(f"📅 request_since original: {request_since}")
    print(f"📅 current_time_ms: {current_time_ms}")
    print(f"📅 max_future_ms: {max_future_ms}")
    
    if request_since > max_future_ms:
        hours_future = (request_since - current_time_ms) / (60 * 60 * 1000)
        print(f"⚠️ DETECTADO: Timestamp está {hours_future:.1f} horas no futuro")
        print(f"🔧 CORREÇÃO: request_since seria resetado para None")
        print(f"🧹 LIMPEZA: Cache seria limpo")
        request_since = None
        corrected = True
    else:
        print(f"✅ OK: Timestamp está dentro do limite")
        corrected = False
    
    print(f"📅 request_since corrigido: {request_since}")
    print(f"🎯 Correção aplicada: {corrected}")
    
    return corrected

def main():
    """Executa todos os testes"""
    print("🧪 TESTE DE VALIDAÇÃO DE TIMESTAMP FUTURO INVÁLIDO")
    print("=" * 60)
    
    try:
        # Teste principal
        result1 = test_timestamp_validation()
        
        # Casos extremos
        result2 = test_edge_cases()
        
        # Lógica de correção
        result3 = test_correction_logic()
        
        print(f"\n📊 RESUMO DOS TESTES:")
        print(f"   ✅ Detecção de timestamp futuro: {result1}")
        print(f"   ✅ Casos extremos: {result2}")
        print(f"   ✅ Lógica de correção: {result3}")
        
        if all([result1, result2, result3]):
            print(f"\n🎉 TODOS OS TESTES PASSARAM!")
            print(f"   A correção deve resolver o problema de candles insuficientes")
            print(f"   causado pelo timestamp futuro inválido 1753403940001")
        else:
            print(f"\n❌ ALGUNS TESTES FALHARAM")
        
    except Exception as e:
        print(f"❌ Erro durante os testes: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()