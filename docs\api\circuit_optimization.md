# Otimização de Circuitos

O módulo `circuit_optimization` oferece a função `optimize_circuit` para reduzir profundidade e contagem de portas de circuitos quânticos utilizados no QUALIA.

## Processo
1. O circuito é transpilado com `optimization_level=3`.
2. Passes adicionais de cancelamento removem resets e portas redundantes.
3. O resultado é um `QuantumCircuit` otimizado.

## Uso
```python
from qualia.core.quantum_circuit_builder import create_complex_circuit
from qualia.core.circuit_optimization import optimize_circuit

circuit = create_complex_circuit()
optimized = optimize_circuit(circuit)
```
