"""
QAST Core - Main quantum-aware trading engine
Integrates all QUALIA operators for unified trading intelligence
"""

import asyncio
import logging
import os
import traceback
from typing import Any, Deque, Dict, List, Optional

import numpy as np

try:
    from datadog import DogStatsd
except ImportError:
    DogStatsd = object  # Define como um placeholder se não estiver disponível
from dataclasses import dataclass
import time
from collections import deque
import uuid
from pathlib import Path
from qiskit import QuantumCircuit
import json

from . import metrics_helpers
from .metrics_controller import PIDController, update_metrics
from .operator_manager import OperatorManager
from ..exceptions import CriticalLoopError

# Utilitários para importação dinâmica de estratégias
import importlib
import pkgutil

from .observer import ObservationType

from ..exchanges.base_exchange import BaseExchange
from ..exchanges.kraken_client import KrakenClient
from ..exchanges.kucoin_client import KuCoinClient
from ..exchanges.binance_client import BinanceClient
from .multi_exchange_manager import MultiExchangeManager
from ..temporal.pattern_detector import TemporalPatternDetector
from ..memory.legacy_memory_system import LegacyMemorySystem
from ..memory import get_qpm_instance
from ..metacognition.metacognitive_engine import MetacognitiveEngine
from ..risk.manager import (
    QUALIARiskManager as RiskManager,
    create_risk_manager,
    QUALIARiskManagerBase,
)
from ..signals.generator import SignalGenerator
from ..common.specs import MarketSpec
from ..strategies.strategy_factory import StrategyFactory
from ..monitoring.metrics import MetricsCollector
from ..config.consciousness_defaults import load_consciousness_defaults

logger = logging.getLogger(__name__)


@dataclass
class HyperParams:
    """Parameters for symbolic entropy and PID control."""

    max_degree: int = 1
    alpha_coeffs: tuple[float, float, float] = (0.01, 0.05, 0.02)
    mass_min: float = 0.1
    H_target: float = 0.5
    Kp: float = 0.5
    Ki: float = 0.1
    i_max: float = 1.0
    num_dct_features: int = 2
    stop_window: int = 5
    H_var_threshold: float = 0.01


@dataclass
class MassManager:
    """Track and update informational mass."""

    alpha_coeffs: tuple[float, float, float]
    mass_min: float
    mass: float = 1.0

    def update(self, H_symb: float, coherence: float | None) -> float:
        """Update mass based on entropy and coherence."""
        if coherence is None:
            coherence = 1.0
        delta = (
            self.alpha_coeffs[0]
            + self.alpha_coeffs[1] * H_symb
            + self.alpha_coeffs[2] * H_symb**2
        ) * H_symb + self.alpha_coeffs[1] * (1 - coherence)
        self.mass = max(self.mass - delta, self.mass_min)
        return self.mass


class SymbolicEntropy:
    """Compute normalized entropy from polynomial coefficients."""

    @staticmethod
    def compute(coeffs: np.ndarray) -> float:
        if coeffs.size == 0 or np.allclose(coeffs, 0):
            return 0.0
        abs_coeffs = np.abs(coeffs)
        total = np.sum(abs_coeffs)
        if total == 0:
            return 0.0
        probs = abs_coeffs / total
        probs_nz = probs[probs > 0]
        if probs_nz.size <= 1:
            return 0.0
        H = -np.sum(probs_nz * np.log2(probs_nz))
        return float(H / np.log2(len(probs)))


def extract_signal_features(
    signal: np.ndarray,
    num_features: int = 2,
    complex_mode: str = "stack",
) -> np.ndarray:
    """Return a fixed number of DCT features from ``signal``."""

    from scipy.fft import dct

    if complex_mode == "real":
        vec = signal.real
    elif complex_mode == "imag":
        vec = signal.imag
    else:
        vec = np.hstack([signal.real, signal.imag])

    features = dct(vec, norm="ortho")
    if features.size < num_features:
        features = np.pad(features, (0, num_features - features.size))
    return features[:num_features]


@dataclass
class QualiaState:
    """Unified QUALIA system state"""

    folding_signature: np.ndarray
    resonance_strength: float
    emergence_complexity: float
    retrocausal_field: float
    observer_effect: float
    coherence_level: float
    quantum_entanglement: float
    information_reduction: float
    integration_level: float
    timestamp: float


class QASTCore:
    """Minimal QAST core used in unit tests."""

    def __init__(self, universe: Any, params: HyperParams) -> None:
        self.universe = universe
        self.params = params
        self.pid = PIDController(params.Kp, params.Ki, params.i_max)
        self.mass_mgr = MassManager(params.alpha_coeffs, params.mass_min)
        self.H_history: Deque[float] = deque(maxlen=params.stop_window)

    def generate_poly_coeffs(self, features: np.ndarray, cycle: int) -> np.ndarray:
        if features.size == 0:
            base = 1.0
        else:
            base = abs(float(np.mean(features[: self.params.num_dct_features]))) + 1.0
        coeffs = np.array([base])
        if self.params.max_degree > 1:
            coeffs = np.pad(coeffs, (0, self.params.max_degree - coeffs.size))
        return np.append(coeffs, cycle)

    def run_cycle(self, snapshot: Dict[str, Any], *, cycle: int) -> Dict[str, Any]:
        trace_id = str(uuid.uuid4())
        encoded = self.universe.encode_snapshot(snapshot)
        new_state = self.universe.update(encoded)
        metrics = metrics_helpers.compute_quantum_metrics(
            self.universe, new_state["vector"]
        )
        features = extract_signal_features(
            np.asarray(new_state["vector"]),
            num_features=self.params.num_dct_features,
        )
        coeffs = self.generate_poly_coeffs(features, cycle)
        H_symb = SymbolicEntropy.compute(coeffs)
        self.H_history.append(H_symb)
        error = self.params.H_target - H_symb
        ctrl = self.pid.update(error)
        mass = self.mass_mgr.update(H_symb, metrics.get("coherence", 1.0))
        self.universe.scr_depth = max(1, int(self.universe.scr_depth * (1 + ctrl)))
        self.universe.temperature = max(
            0.1, float(self.universe.temperature * (1 + error))
        )
        log = {
            "quantum_entropy": metrics.get("entropy"),
            "coherence": metrics.get("coherence"),
            "symbolic_entropy": H_symb,
            "mass_info": mass,
            "control_signal": ctrl,
            "terminated": self.should_terminate(),
            "trace_id": trace_id,
            "timestamp": time.time(),
        }
        return {
            "new_state": new_state,
            "metrics": metrics,
            "symbolic_entropy": H_symb,
            "control_signal": ctrl,
            "mass_info": mass,
            "log": log,
        }

    def should_terminate(self) -> bool:
        if self.mass_mgr.mass <= self.params.mass_min:
            return True
        if len(self.H_history) >= self.params.stop_window:
            if np.var(self.H_history) < self.params.H_var_threshold:
                return True
        return False


class TradingQASTCore:
    """
    QUALIA Quantum-Aware Algorithmic Trading System Core Engine

    Integrates all quantum operators and subsystems for unified
    market analysis and trading decisions.
    """

    def __init__(
        self,
        config: Dict[str, Any],
        metrics_client: Optional[DogStatsd] = None,
        quantum_universe: Optional[Any] = None,
        market_integration: Optional[Any] = None,
    ):
        self.config = config
        defaults = load_consciousness_defaults()
        qualia_defaults = defaults.get("qualia", {})
        self.config.setdefault("qualia", {})
        self.config["qualia"].setdefault(
            "intent_engine", qualia_defaults.get("intent_engine", {})
        )
        self.config["qualia"].setdefault(
            "symbolic_intention", qualia_defaults.get("symbolic_intention", {})
        )
        self.metrics_client = metrics_client or DogStatsd()
        self.running = False

        # Store market_integration for use when no exchanges are configured
        self.market_integration = market_integration
        if market_integration:
            logger.info("✅ Market integration received by QAST Core")

        # YAA T10: Usar universo injetado ou criar um novo
        if quantum_universe:
            self.quantum_universe = quantum_universe
            logger.info("✅ Quantum universe injetado no QAST Core.")
        else:
            self._initialize_quantum_universe()

        # Initialize quantum operators
        self._initialize_quantum_operators()

        # Initialize trading subsystems
        self._initialize_trading_subsystems()

        # State tracking
        self.current_qualia_state: Optional[QualiaState] = None
        self.state_history: List[QualiaState] = []
        self.state_history_max = config.get("qast", {}).get("state_history_max", 10000)
        self.last_valid_market_data: Optional[Dict[str, Any]] = None
        self.last_valid_market_data_timestamp: float = 0.0

        # Auxiliary metrics
        self.tsvf_output = None
        self.otoc_value = None

        # Main processing loop
        self.main_task: Optional[asyncio.Task] = None
        self.processing_interval = 1.0  # 1 second processing cycle
        self.operator_timeout = config.get("qast", {}).get("operator_timeout", 5.0)

        logger.info("QAST Core initialized successfully")

    def _initialize_quantum_universe(self):
        """Initialize the QUALIA quantum universe for metacognition"""
        try:
            from ..core.universe import QUALIAQuantumUniverse

            # Use quantum_universe_config if available, otherwise fall back to universe_config
            universe_config = self.config.get("quantum_universe_config", {})
            if not universe_config:
                universe_config = self.config.get("universe_config", {})

            # Initialize quantum universe with configuration
            self.quantum_universe = QUALIAQuantumUniverse(
                n_qubits=universe_config.get("n_qubits", 8),
                scr_depth=universe_config.get("scr_depth", 10),
                base_lambda=universe_config.get("base_lambda", 0.5),
                alpha=universe_config.get("alpha", 0.1),
                retro_strength=universe_config.get("retro_strength", 0.1),
                num_ctc_qubits=universe_config.get("num_ctc_qubits", 0),
                measure_frequency=universe_config.get("measure_frequency", 1),
                thermal_coefficient=universe_config.get("thermal_coefficient", 0.01),
                hawking_factor=universe_config.get("hawking_factor", 0.01),
                backend_name=universe_config.get(
                    "backend_name", "aer_simulator_statevector"
                ),
                qast_feedback_enabled=universe_config.get(
                    "qast_feedback_enabled", False
                ),
                lambda_factor_multiplier=universe_config.get(
                    "lambda_factor_multiplier", 1.0
                ),
                enrichment_cycles=universe_config.get("enrichment_cycles", 1),
                adaptive_threshold=universe_config.get("adaptive_threshold", None),
                entanglement_style=universe_config.get("entanglement_style", "linear"),
                max_history_size=universe_config.get("max_history_size", 100),
                initial_state_type=universe_config.get("initial_state_type", "qft"),
                shots=universe_config.get("shots", 2048),
                qpu_steps=universe_config.get("qpu_steps", 6),
                eqci_config=universe_config.get(
                    "eqci_config",
                    {
                        "critical_window": 20,
                        "secondary_window": 20,
                        "vol_cap": 3.0,
                    },
                ),
                qpm_config=self.config.get("qpm_config", {}),
                qpm_instance=getattr(self, "qpm_memory", None),
            )

            logger.info(
                f"✅ Quantum universe inicializado com {self.quantum_universe.n_qubits} qubits para metacognição"
            )

        except Exception as e:
            logger.warning(
                f"⚠️ Não foi possível inicializar universo quântico para metacognição: {e}"
            )
            self.quantum_universe = None

    def _initialize_quantum_operators(self):
        """Initialize QUALIA quantum and intention operators."""
        try:
            self.operators = OperatorManager(self.config)
            self.retro_enabled = self.operators.retro_enabled
            self.folding = self.operators.folding
            self.resonance = self.operators.resonance
            self.emergence = self.operators.emergence
            self.retrocausality = self.operators.retrocausality
            self.observer = self.operators.observer
            self.reduction = self.operators.reduction
            self.integration = self.operators.integration
            self.intent_engine = self.operators.intent_engine
            self.symbolic_intention = self.operators.symbolic_intention
            self.operator_sim = self.operators.operator_sim
            self.tokenizer = self.operators.quantum_tokenizer

            logger.info("Quantum operators initialized")

        except Exception as e:
            logger.error(f"Failed to initialize quantum operators: {e}")
            raise

    def _initialize_trading_subsystems(self):
        """Initialize trading-related subsystems"""
        try:
            # Exchange clients
            self.exchanges: Dict[str, BaseExchange] = {}
            self._initialize_exchanges()

            # Multi-Exchange Dimensional Manager
            self.multi_exchange_manager: Optional[MultiExchangeManager] = None
            if self.config.get("dimensional", {}).get("enabled", False):
                self.multi_exchange_manager = MultiExchangeManager(self.config)

            # Core subsystems
            self.temporal_detector = TemporalPatternDetector(
                self.config.get("temporal", {})
            )
            self.memory_system = LegacyMemorySystem(self.config.get("memory", {}))
            self.metacognition = MetacognitiveEngine(
                self.config.get("metacognition", {})
            )
            self.risk_manager = RiskManager(self.config.get("risk", {}))
            self.signal_generator = SignalGenerator(self.config)

            # Determinar símbolo/timeframe padrão
            default_symbols = self.config.get("symbols", ["BTC/USDT"])
            symbol = (
                default_symbols[0]
                if isinstance(default_symbols, list)
                else default_symbols
            )
            timeframe = self.config.get("timeframe", "1h")

            # Configuração da estratégia definida pelo usuário (strategy_parameters.yaml)
            strat_cfg: Dict[str, Any] = self.config.get("strategy_config", {})
            strat_alias: str = strat_cfg.get("name", "NovaEstrategiaQUALIA")
            strat_params: Dict[str, Any] = strat_cfg.get("params", {})

            # Instanciar dinamicamente via StrategyFactory
            strategy_instance = StrategyFactory.create_strategy(
                strat_alias,
                params=strat_params,
                context={
                    "symbol": symbol,
                    "timeframe": timeframe,
                    "qpm_instance": get_qpm_instance(),
                },
            )

            # Registrar no dicionário de estratégias
            self.strategies = {strat_alias: strategy_instance}

            # Metrics collection
            self.metrics = MetricsCollector()

            # Configure per-symbol risk managers
            self._setup_risk_managers()

            logger.info("Trading subsystems initialized")

        except Exception as e:
            logger.error(f"Failed to initialize trading subsystems: {e}")
            raise

    def _initialize_exchanges(self):
        """Initialize exchange connections"""
        exchange_config = self.config.get("exchanges") or {}

        # Validate exchange configuration first
        from ..config.exchange_validator import ExchangeConfigValidator

        validator = ExchangeConfigValidator()
        validation_result = validator.validate_config(self.config)

        if not validation_result.is_valid:
            logger.error(
                f"[EXCHANGE] QAST Core configuration validation failed: {validation_result.error_message}"
            )
            logger.warning(f"[EXCHANGE] QAST Core running in simulation mode")
            return

        # DEBUG: Log the full config structure to understand what's being received
        logger.info(
            f"[DEBUG] QAST Core received config keys: {list(self.config.keys())}"
        )
        logger.info(f"[DEBUG] Exchange config found: {exchange_config}")

        # Kraken
        if exchange_config.get("kraken", {}).get("enabled", False):
            try:
                self.exchanges["kraken"] = KrakenClient(exchange_config["kraken"])
                logger.info("✅ Kraken exchange initialized successfully")
            except (OSError, ValueError) as e:
                logger.warning("❌ Failed to initialize Kraken: %s", e)

        # KuCoin
        kucoin_config = exchange_config.get("kucoin", {})
        logger.info(f"[DEBUG] KuCoin config: {kucoin_config}")

        if kucoin_config.get("enabled", False):
            try:
                # Resolve environment variables for credentials
                def resolve_env_var(value):
                    if (
                        isinstance(value, str)
                        and value.startswith("${")
                        and value.endswith("}")
                    ):
                        import os

                        env_var = value[2:-1]  # Remove ${ and }
                        return os.getenv(env_var)
                    return value

                api_key = resolve_env_var(kucoin_config.get("api_key"))
                api_secret = resolve_env_var(kucoin_config.get("api_secret"))
                password = resolve_env_var(
                    kucoin_config.get("passphrase")
                )  # Note: using 'passphrase' key

                logger.info(
                    f"[DEBUG] KuCoin credentials check - API Key: {'✓' if api_key else '✗'}, Secret: {'✓' if api_secret else '✗'}, Passphrase: {'✓' if password else '✗'}"
                )

                if not all([api_key, api_secret, password]):
                    logger.error(
                        f"[ERROR] Missing KuCoin credentials after environment resolution"
                    )
                    logger.error(
                        f"[ERROR] Check environment variables: KUCOIN_API_KEY, KUCOIN_SECRET_KEY, KUCOIN_PASSPHRASE"
                    )
                    raise ValueError("Missing required KuCoin credentials")

                # Create resolved config for KuCoinClient
                resolved_config = kucoin_config.copy()
                resolved_config.update(
                    {"api_key": api_key, "api_secret": api_secret, "password": password}
                )

                self.exchanges["kucoin"] = KuCoinClient(resolved_config)
                logger.info("✅ KuCoin exchange initialized successfully")
            except (OSError, ValueError) as e:
                logger.warning(f"❌ Failed to initialize KuCoin: {e}")
        else:
            logger.warning(
                f"[DEBUG] KuCoin not enabled or config missing. Enabled: {kucoin_config.get('enabled', False)}"
            )

        # Binance
        binance_config = exchange_config.get("binance", {})
        logger.info(f"[DEBUG] Binance config: {binance_config}")

        if binance_config.get("enabled", False):
            try:

                def _resolve_env(value: Any) -> Any:
                    if (
                        isinstance(value, str)
                        and value.startswith("${")
                        and value.endswith("}")
                    ):
                        import os

                        return os.getenv(value[2:-1])
                    return value

                api_key = _resolve_env(binance_config.get("api_key"))
                api_secret = _resolve_env(binance_config.get("api_secret"))

                logger.info(
                    f"[DEBUG] Binance credentials check - API Key: {'✓' if api_key else '✗'}, Secret: {'✓' if api_secret else '✗'}"
                )

                if not all([api_key, api_secret]):
                    logger.error(
                        "[ERROR] Missing Binance credentials after environment resolution"
                    )
                    logger.error(
                        "[ERROR] Check environment variables: BINANCE_API_KEY, BINANCE_API_SECRET"
                    )
                    raise ValueError("Missing required Binance credentials")

                resolved_config = binance_config.copy()
                resolved_config.update({"api_key": api_key, "api_secret": api_secret})

                self.exchanges["binance"] = BinanceClient(resolved_config)
                logger.info("✅ Binance exchange initialized successfully")
            except (OSError, ValueError) as e:
                logger.warning(f"❌ Failed to initialize Binance: {e}")
        else:
            logger.warning(
                f"[DEBUG] Binance not enabled or config missing. Enabled: {binance_config.get('enabled', False)}"
            )

        # Final status check
        if not self.exchanges:
            logger.warning("⚠️  No exchanges initialized - running in simulation mode")
            logger.info(
                "💡 To enable live trading, ensure exchange credentials are properly configured"
            )
        else:
            initialized_exchanges = list(self.exchanges.keys())
            logger.info(
                f"✅ Exchanges initialized successfully: {', '.join(initialized_exchanges)}"
            )
            logger.info("🚀 QAST Core ready for live trading")

    def _setup_risk_managers(self) -> None:
        """Initialize unified risk managers for each configured symbol."""
        from ..risk.risk_manager_factory import create_unified_risk_manager

        symbols = self.config.get("symbols", [])
        risk_profile = self.config.get("risk_profile", "moderate")
        profile_cfg = self.config.get("risk_profile_settings", {})
        initial_capital = self.config.get("capital", 0.0)

        self.risk_managers: Dict[str, QUALIARiskManagerBase] = {}

        logger.info(f"🛡️  Setting up unified risk managers for {len(symbols)} symbols")
        logger.info(f"   Profile: {risk_profile}")
        logger.info(f"   Capital: ${initial_capital:,.2f}")

        for sym in symbols:
            try:
                profile_params = profile_cfg.get(risk_profile, {}).get(sym, {})

                # Use unified risk manager factory
                self.risk_managers[sym] = create_unified_risk_manager(
                    initial_capital=initial_capital,
                    risk_profile=risk_profile,
                    symbol=sym,
                    exchange_id="qast_core",
                    profile_specific_config=profile_params,
                )

                logger.info(f"   ✅ Risk manager created for {sym}")

            except Exception as e:
                logger.error(f"   ❌ Failed to create risk manager for {sym}: {e}")
                raise

    async def initialize(self):
        """Initialize all async components"""
        try:
            # Initialize exchanges
            for name, exchange in self.exchanges.items():
                await exchange.initialize()
                logger.info(f"Exchange {name} connected")

            # Initialize Multi-Exchange Dimensional Manager
            if self.multi_exchange_manager:
                await self.multi_exchange_manager.initialize()
                logger.info("🌌 Multi-Exchange Dimensional Manager initialized")

            # Initialize memory system
            await self.memory_system.initialize()

            # Initialize metacognitive engine
            await self.metacognition.initialize()

            # Start observer
            await self.observer.start_observation()

            logger.info("QAST Core initialization complete")

        except Exception as e:
            logger.error(f"Failed to initialize QAST Core: {e}")
            raise

    async def run(self):
        """Main trading loop"""
        self.running = True
        logger.info("QAST Core starting main trading loop")

        try:
            while self.running:
                start_time = time.time()

                # Main processing cycle
                await self._process_market_cycle()

                # Sleep to maintain processing interval
                processing_time = time.time() - start_time
                sleep_time = max(0, self.processing_interval - processing_time)

                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                else:
                    logger.warning(
                        f"Processing cycle took {processing_time:.2f}s, "
                        f"exceeding target interval of {self.processing_interval}s"
                    )

        except asyncio.CancelledError:
            logger.info("QAST Core main loop cancelled")
        except Exception as e:
            logger.critical(
                "Falha crítica no loop principal do QAST Core: %s", e, exc_info=True
            )
            self.running = False
            await self.shutdown()
            raise CriticalLoopError(str(e)) from e

    async def run_cycle(self, trace_id: Optional[str] = None) -> Dict[str, Any]:
        """Public method to process a single market cycle."""
        return await self._process_market_cycle(trace_id=trace_id)

    async def _process_market_cycle(self, trace_id: Optional[str] = None):
        """Process one complete market analysis and trading cycle.

        After gathering market data, the intention engine generates symbolic
        tokens which are applied to the quantum universe before the main
        quantum analysis steps.
        """
        try:
            timestamp = time.time()

            if self.config is None:
                logger.error("Trading configuration is None")
                return {}

            # Etapa 1: Coletar dados de mercado com retry e fallback inteligente
            market_data = await self._gather_market_data_with_retry_and_fallback()

            if not market_data:
                logger.critical(
                    "CRITICAL: Market data is None even after fallback. Skipping cycle."
                )
                await asyncio.sleep(self.config.get("market_data_retry_wait", 1.0))
                return {}

            # Atualiza o último dado válido se a coleta foi bem-sucedida (não é do fallback)
            if not market_data.get("is_fallback", False):
                self.last_valid_market_data = market_data
                self.last_valid_market_data_timestamp = timestamp

            # Etapa 2: Gerar tokens de intenção a partir dos dados de mercado
            intention_tokens = await self.intent_engine.generate_tokens(market_data)

            # Etapa 3: Aplicar análise quântica
            qualia_state = await self._apply_quantum_analysis(market_data, timestamp)

            if not qualia_state:
                logger.error("Quantum analysis returned no state. Skipping cycle.")
                return {}

            # Step 4: Temporal pattern detection
            data_arrays = self._prepare_data_arrays(market_data)
            prices_series = data_arrays.get("prices", np.array([]))
            temporal_patterns = await self.temporal_detector.detect_patterns(
                prices_series,
                {"timestamp": timestamp},
            )

            # Step 5: Generate trading signals using consolidated input
            analysis_input = {
                "market_data": market_data,
                "quantum_results": qualia_state,
                "temporal_results": temporal_patterns,
            }
            signals = await self.signal_generator.generate_signals(
                "GLOBAL_PORTFOLIO", analysis_input
            )

            # Step 6: Risk assessment
            risk_assessment = await self.risk_manager.assess_portfolio_risk(
                market_data,
                signals,
                timestamp,
                tsvf_channel=self.tsvf_output,
                otoc_value=self.otoc_value,
            )

            # Step 7: Execute approved trades
            if risk_assessment.approved_signals:
                await self._execute_trades(
                    risk_assessment.approved_signals, trace_id=trace_id
                )

            # Step 8: Update memory and learning
            await self._update_memory_and_learning(
                market_data, qualia_state, signals, risk_assessment, timestamp
            )

            # Step 9: Metacognitive adaptation
            await self.metacognition.process_feedback(
                qualia_state, signals, risk_assessment, timestamp
            )

            # Step 10: Update metrics
            await self._update_metrics(
                qualia_state, signals, risk_assessment, timestamp
            )

            # Persist current state
            self.current_qualia_state = qualia_state
            self.state_history.append(qualia_state)

            return {
                "signals": signals,
                "risk_assessment": risk_assessment,
                "qualia_state": qualia_state,
                "timestamp": timestamp,
            }

        except Exception as e:
            logger.error(
                "Error in market processing cycle: %s\n%s",
                e,
                traceback.format_exc(),
            )
            return {}

    async def _gather_market_data_with_retry_and_fallback(
        self,
    ) -> Optional[Dict[str, Any]]:
        """
        Tenta coletar dados de mercado com múltiplas tentativas e backoff exponencial.
        Se todas as tentativas falharem, usa um fallback inteligente para dados recentes.
        """
        max_attempts = self.config.get("market_data_retries", 3)
        base_wait_seconds = self.config.get("market_data_retry_wait", 1.5)
        market_data = None

        for attempt in range(1, max_attempts + 1):
            market_data = await self._gather_market_data()
            if market_data:
                return market_data

            wait_time = base_wait_seconds * (1.5 ** (attempt - 1))
            logger.warning(
                "Market data is None, retrying after reconnection (%s/%s). Waiting %.2f seconds.",
                attempt,
                max_attempts,
                wait_time,
            )
            await self._reconnect_exchanges()
            await asyncio.sleep(wait_time)

        if market_data is None:
            logger.error(
                "All attempts to fetch live market data failed. Using fallback."
            )
            market_data = self._generate_fallback_market_data()

        return market_data

    def _generate_fallback_market_data(self) -> Optional[Dict[str, Any]]:
        """Gera dados de fallback usando a última informação válida, se disponível e recente."""
        logger.warning("Entering intelligent fallback data generation mode.")
        # Limite de 5 minutos para considerar os dados obsoletos como válidos
        if (
            self.last_valid_market_data
            and (time.time() - self.last_valid_market_data_timestamp) < 300
        ):
            logger.warning(
                "Using last valid market data (stale for %.2f seconds).",
                time.time() - self.last_valid_market_data_timestamp,
            )
            stale_data = self.last_valid_market_data.copy()
            stale_data["is_fallback"] = True
            return stale_data

        logger.critical("No recent valid market data available for fallback.")
        return None

    async def _gather_market_data(self) -> Optional[Dict[str, Any]]:
        """
        Coleta dados de mercado de todas as exchanges configuradas, utilizando MarketSpec
        e mantendo os dados como DataFrames. Se nenhuma exchange estiver configurada,
        usa market_integration como fallback.
        """
        market_data: Dict[str, Any] = {}

        # Se não há exchanges configuradas, tenta usar market_integration
        if not self.exchanges:
            if self.market_integration:
                logger.info(
                    "No exchanges configured - using market_integration for data collection"
                )
                return await self._gather_market_data_from_integration()
            else:
                logger.warning(
                    "Nenhuma exchange configurada para coletar dados de mercado."
                )
                return None

        symbols = self.config.get("symbols", [])
        timeframes = self.config.get("timeframes", [])

        for exchange_name, exchange_client in self.exchanges.items():
            exchange_market_data: Dict[str, Any] = {}
            for symbol in symbols:
                symbol_data: Dict[str, Any] = {"ohlcv": {}}

                # Fetch OHLCV data for all configured timeframes
                for timeframe in timeframes:
                    try:
                        spec = MarketSpec(symbol=symbol, timeframe=timeframe)
                        ohlcv_df = await exchange_client.fetch_ohlcv(spec)
                        if ohlcv_df is not None and not ohlcv_df.empty:
                            symbol_data["ohlcv"][timeframe] = ohlcv_df
                        else:
                            logger.warning(
                                f"Nenhum dado OHLCV para {symbol}@{timeframe} da {exchange_name}."
                            )
                    except Exception as e:
                        logger.error(
                            f"Erro ao buscar OHLCV para {symbol}@{timeframe} da {exchange_name}: {e}"
                        )

                # Fetch ticker data
                try:
                    ticker_data = await exchange_client.fetch_ticker(symbol)
                    if ticker_data:
                        symbol_data["ticker"] = ticker_data
                    else:
                        logger.warning(
                            f"Nenhum dado de ticker para {symbol} da {exchange_name}."
                        )
                except Exception as e:
                    logger.error(
                        f"Erro ao buscar ticker para {symbol} da {exchange_name}: {e}"
                    )

                if symbol_data.get("ohlcv") or symbol_data.get("ticker"):
                    exchange_market_data[symbol] = symbol_data

            if exchange_market_data:
                market_data[exchange_name] = exchange_market_data

        if not market_data:
            logger.warning("Nenhum dado de mercado coletado de nenhuma exchange.")
            return None

        return market_data

    async def _gather_market_data_from_integration(self) -> Optional[Dict[str, Any]]:
        """
        Coleta dados de mercado usando market_integration quando nenhuma exchange está configurada.
        """
        try:
            if not self.market_integration:
                logger.error("Market integration is None")
                return None

            symbols = self.config.get("symbols", ["BTC/USDT"])
            timeframes = self.config.get("timeframes", ["1min"])

            # Estrutura de dados similar ao formato esperado
            market_data = {}

            for symbol in symbols:
                # Converte símbolo para formato KuCoin se necessário (BTC/USDT -> BTC-USDT)
                kucoin_symbol = symbol.replace("/", "-")

                try:
                    # Busca dados de ticker usando market_integration
                    ticker_data = await self.market_integration.fetch_ticker(
                        kucoin_symbol
                    )

                    if ticker_data:
                        # Cria estrutura de dados compatível
                        symbol_data = {
                            "ticker": {
                                "symbol": symbol,
                                "last": ticker_data.get("last", 0),
                                "bid": ticker_data.get("bid", 0),
                                "ask": ticker_data.get("ask", 0),
                                "volume": ticker_data.get("vol", 0),
                                "timestamp": ticker_data.get("time", 0),
                                "source": "market_integration",
                            },
                            "ohlcv": {},  # Placeholder para compatibilidade
                        }

                        # Armazena dados válidos para fallback futuro
                        self.last_valid_market_data = {symbol: symbol_data}
                        self.last_valid_market_data_timestamp = time.time()

                        market_data[symbol] = symbol_data
                        logger.info(
                            f"[OK] Market data collected via integration for {symbol}: ${ticker_data.get('last', 0)}"
                        )

                    else:
                        logger.warning(
                            f"No ticker data received for {symbol} from market_integration"
                        )

                except Exception as e:
                    logger.error(
                        f"Error fetching data for {symbol} from market_integration: {e}"
                    )

            return market_data if market_data else None

        except Exception as e:
            logger.error(f"Error in _gather_market_data_from_integration: {e}")
            return None

    async def _reconnect_exchanges(self) -> None:
        """Attempt to reinitialize all exchange connections."""
        for name, exchange in getattr(self, "exchanges", {}).items():
            try:
                await exchange.initialize()
                logger.info("Reconnected exchange %s", name)
            except Exception as exc:  # pragma: no cover - log only
                logger.warning("Failed to reconnect %s: %s", name, exc)

    async def _apply_quantum_analysis(
        self, market_data: Dict[str, Any], timestamp: float
    ) -> QualiaState:
        """Apply all quantum operators to market data"""
        try:
            # Convert market data to numpy arrays for analysis
            data_arrays = self._prepare_data_arrays(market_data)

            # Apply folding operator
            try:
                folding_state = await asyncio.wait_for(
                    self.folding.fold(data_arrays["prices"], timestamp),
                    timeout=self.operator_timeout,
                )
            except Exception as exc:
                logger.exception("Folding operator failed: %s", exc)
                return self._create_default_qualia_state(timestamp)

            # Apply resonance operator
            try:
                resonance_state = await asyncio.wait_for(
                    self.resonance.analyze_resonance(data_arrays["volumes"], timestamp),
                    timeout=self.operator_timeout,
                )
            except Exception as exc:
                logger.exception("Resonance operator failed: %s", exc)
                return self._create_default_qualia_state(timestamp)

            # Apply emergence operator
            try:
                emergence_state = await asyncio.wait_for(
                    self.emergence.detect_emergence(data_arrays["features"], timestamp),
                    timeout=self.operator_timeout,
                )
            except Exception as exc:
                logger.exception("Emergence operator failed: %s", exc)
                return self._create_default_qualia_state(timestamp)

            # Apply retrocausality operator if enabled
            future_prediction = self._generate_future_prediction(data_arrays)
            if self.retro_enabled:
                try:
                    retrocausality_state = await asyncio.wait_for(
                        self.retrocausality.analyze_retrocausality(
                            data_arrays["prices"], future_prediction, timestamp
                        ),
                        timeout=self.operator_timeout,
                    )
                    self.tsvf_output = self.retrocausality.last_tsvf_output
                except Exception as exc:
                    logger.exception("Retrocausality operator failed: %s", exc)
                    return self._create_default_qualia_state(timestamp)
            else:
                retrocausality_state = self.retrocausality._create_initial_state(
                    timestamp
                )
                self.tsvf_output = None

            # Calculate OTOC value when quantum universe is available
            self.otoc_value = None
            if hasattr(self, "quantum_universe") and self.quantum_universe:
                try:
                    from ..metacognition.metalayer_analysis import MetalayerAnalysis

                    analyzer = MetalayerAnalysis(self.quantum_universe)
                    self.otoc_value = analyzer.analyze_otoc()
                except Exception as exc:  # pragma: no cover - optional metric
                    logger.debug("OTOC calculation failed: %s", exc)

            # Apply observer operator
            quantum_state = self._create_quantum_state(data_arrays)
            try:
                observer_state = await asyncio.wait_for(
                    self.observer.observe_system(
                        quantum_state, ObservationType.POSITION, timestamp
                    ),
                    timeout=self.operator_timeout,
                )
            except Exception as exc:
                logger.exception("Observer operator failed: %s", exc)
                return self._create_default_qualia_state(timestamp)

            # Apply reduction operator
            try:
                # Convert state vector to density matrix before reduction
                density_matrix = self._create_density_matrix(quantum_state)
                reduction_state = await asyncio.wait_for(
                    self.reduction.reduce_information(density_matrix, timestamp),
                    timeout=self.operator_timeout,
                )
            except Exception as exc:
                logger.exception("Reduction operator failed: %s", exc)
                return self._create_default_qualia_state(timestamp)

            # Apply integration operator
            try:
                integration_state = await asyncio.wait_for(
                    self.integration.integrate_information(
                        reduction_state.reduced_density, timestamp
                    ),
                    timeout=self.operator_timeout,
                )
            except Exception as exc:
                logger.exception("Integration operator failed: %s", exc)
                return self._create_default_qualia_state(timestamp)

            # Calculate derived measures
            coherence_level = self._calculate_coherence_level(
                folding_state, resonance_state, emergence_state
            )

            quantum_entanglement = self._calculate_entanglement_measure(
                observer_state, emergence_state
            )

            # Create unified QUALIA state
            qualia_state = QualiaState(
                folding_signature=folding_state.folded_data,
                resonance_strength=resonance_state.resonance_strength,
                emergence_complexity=emergence_state.complexity_measure,
                retrocausal_field=retrocausality_state.temporal_field_strength,
                observer_effect=observer_state.observation_effect,
                coherence_level=coherence_level,
                quantum_entanglement=quantum_entanglement,
                information_reduction=reduction_state.collapse_rate,
                integration_level=integration_state.experience_intensity,
                timestamp=timestamp,
            )

            self.current_qualia_state = qualia_state
            self.state_history.append(qualia_state)

            # Limit history size
            if len(self.state_history) > self.state_history_max:
                self.state_history.pop(0)

            return qualia_state

        except Exception as e:
            logger.error(f"Error in quantum analysis: {e}")
            return self._create_default_qualia_state(timestamp)

    def _prepare_data_arrays(
        self, market_data: Dict[str, Any]
    ) -> Dict[str, np.ndarray]:
        """Convert market data to numpy arrays for analysis"""
        try:
            prices = []
            volumes = []
            features = []

            for exchange_name, exchange_data in market_data.items():
                if not isinstance(exchange_data, dict):
                    logger.warning(
                        "Market data for %s is invalid type %s; skipping",
                        exchange_name,
                        type(exchange_data).__name__,
                    )
                    continue
                for symbol, ticker in exchange_data.items():
                    if not isinstance(ticker, dict):
                        logger.warning(
                            "Ticker for %s on %s is None or invalid; skipping",
                            symbol,
                            exchange_name,
                        )
                        continue
                    # Extract price data
                    prices.extend(
                        [
                            ticker.get("bid", 0),
                            ticker.get("ask", 0),
                            ticker.get("last", 0),
                        ]
                    )

                    # Extract volume data
                    volumes.append(ticker.get("baseVolume", 0))

                    # Extract additional features
                    features.extend(
                        [
                            ticker.get("percentage", 0),
                            ticker.get("change", 0),
                            ticker.get("high", 0) - ticker.get("low", 1),  # Range
                            ticker.get("vwap", ticker.get("last", 0)),
                        ]
                    )

            return {
                "prices": np.array(prices),
                "volumes": np.array(volumes),
                "features": np.array(features),
            }

        except Exception as e:
            logger.warning(f"Data array preparation failed: {e}")
            return {
                "prices": np.array([1.0]),
                "volumes": np.array([1.0]),
                "features": np.array([1.0]),
            }

    def _generate_future_prediction(
        self, data_arrays: Dict[str, np.ndarray]
    ) -> np.ndarray:
        """Generate future prediction using :class:`RetrocausalityOperator`."""
        try:
            prices = data_arrays["prices"]
            return self.retrocausality.forecast_future_state(prices)
        except Exception as exc:
            logger.exception("Failed to generate future prediction: %s", exc)
            return np.array([1.0])

    def _create_quantum_state(self, data_arrays: Dict[str, np.ndarray]) -> np.ndarray:
        """Create quantum state representation from market data"""
        try:
            # Normalize price data to create quantum state
            prices = data_arrays["prices"]
            if len(prices) > 0:
                normalized = prices / (np.linalg.norm(prices) + 1e-8)
                # Ensure we have at least 2 components for quantum state
                if len(normalized) < 2:
                    quantum_state = np.array([normalized[0], 0.0])
                else:
                    quantum_state = normalized[:2]
                return quantum_state
            else:
                return np.array([1.0, 0.0])
        except Exception as exc:
            logger.exception("Failed to create quantum state: %s", exc)
            return np.array([1.0, 0.0])

    def _create_density_matrix(self, state_vector: np.ndarray) -> np.ndarray:
        """Return density matrix ρ = |ψ⟩⟨ψ| from a state vector.

        The reduction operator requires a square 2-D numpy array (density matrix).
        If ``state_vector`` is already a square matrix, it is validated and returned.
        Otherwise, the outer product is computed. As a final fallback, a 2×2
        identity matrix scaled by ε is returned to avoid operator failure.
        """
        try:
            if state_vector.ndim == 1:
                return np.outer(state_vector, np.conjugate(state_vector))
            if (
                state_vector.ndim == 2
                and state_vector.shape[0] == state_vector.shape[1]
            ):
                return state_vector
        except Exception:
            pass  # fall through to fallback
        return np.eye(2) * 1e-8

    def _calculate_coherence_level(
        self, folding_state, resonance_state, emergence_state
    ) -> float:
        """Calculate overall system coherence level"""
        try:
            coherence_components = [
                folding_state.coherence,
                resonance_state.resonance_strength,
                emergence_state.complexity_measure,
            ]

            tsvf_coherence = 0.0
            if self.tsvf_output is not None:
                tsvf_coherence = float(getattr(self.tsvf_output, "coherence", 0.0))

            otoc_val = self.otoc_value if self.otoc_value is not None else 0.5

            coherence_components.extend([tsvf_coherence, otoc_val])

            # Weighted average with emphasis on folding coherence
            weights = [0.4, 0.25, 0.15, 0.1, 0.1]
            coherence = sum(c * w for c, w in zip(coherence_components, weights))

            return np.clip(coherence, 0.0, 1.0)

        except Exception as exc:
            logger.exception("Failed to calculate coherence level: %s", exc)
            return 0.5

    def _calculate_entanglement_measure(self, observer_state, emergence_state) -> float:
        """Calculate quantum entanglement measure"""
        try:
            # Use observer effect and emergence complexity to estimate entanglement
            observer_effect = observer_state.observation_effect
            emergence_complexity = emergence_state.complexity_measure

            tsvf_signal = 0.0
            if self.tsvf_output is not None:
                tsvf_signal = float(getattr(self.tsvf_output, "signal", 0.0))

            otoc_val = self.otoc_value if self.otoc_value is not None else 0.5

            # Incorporate TSVF signal and OTOC as weighting factors
            scaling = 0.6 + 0.2 * tsvf_signal + 0.2 * otoc_val
            entanglement = observer_effect * emergence_complexity * scaling

            return np.clip(entanglement, 0.0, 1.0)

        except Exception as exc:
            logger.exception("Failed to calculate entanglement measure: %s", exc)
            return 0.0

    def _create_default_qualia_state(self, timestamp: float) -> QualiaState:
        """Create default QUALIA state for error cases"""
        return QualiaState(
            folding_signature=np.array([0.0]),
            resonance_strength=0.0,
            emergence_complexity=0.0,
            retrocausal_field=0.0,
            observer_effect=0.0,
            coherence_level=0.5,
            quantum_entanglement=0.0,
            information_reduction=0.0,
            integration_level=0.0,
            timestamp=timestamp,
        )

    async def _execute_trades(
        self, approved_signals: List[Any], trace_id: Optional[str] = None
    ) -> None:
        """Execute approved trading signals"""
        start_time = time.perf_counter()
        try:
            for signal in approved_signals:
                # Select appropriate exchange
                exchange_name = signal.exchange or list(self.exchanges.keys())[0]

                if exchange_name in self.exchanges:
                    exchange = self.exchanges[exchange_name]

                    try:
                        # Execute the trade
                        order_result = await exchange.place_order(
                            symbol=signal.symbol,
                            order_type=signal.order_type,
                            side=signal.side,
                            amount=signal.amount,
                            price=signal.price,
                        )

                        if order_result:
                            logger.info(
                                f"Trade executed: {signal.symbol} {signal.side} "
                                f"{signal.amount} @ {signal.price}"
                            )

                    except Exception as e:
                        logger.error(f"Failed to execute trade {signal.symbol}: {e}")

        except Exception as e:
            logger.error(f"Error executing trades: {e}")
        finally:
            if self.metrics_client:
                elapsed_ms = (time.perf_counter() - start_time) * 1000
                tags = [f"trace_id:{trace_id}"] if trace_id else None
                self.metrics_client.timing(
                    "trading.execution_ms", elapsed_ms, tags=tags
                )
                self.metrics_client.increment("trading.execution_count", tags=tags)

    async def _update_memory_and_learning(
        self, market_data, qualia_state, signals, risk_assessment, timestamp
    ):
        """Update memory system and learning components"""
        try:
            # Store experience in memory
            experience = {
                "market_data": market_data,
                "qualia_state": qualia_state,
                "signals": signals,
                "risk_assessment": risk_assessment,
                "timestamp": timestamp,
            }

            await self.memory_system.store_experience(experience)

        except Exception as e:
            logger.warning(f"Failed to update memory: {e}")

    async def _update_metrics(self, qualia_state, signals, risk_assessment, timestamp):
        """Delegate metrics update to :mod:`metrics_controller`."""
        await update_metrics(
            metrics=self.metrics,
            retro_operator=self.retrocausality,
            quantum_universe=getattr(self, "quantum_universe", None),
            qualia_state=qualia_state,
            signals=signals,
            risk_assessment=risk_assessment,
            timestamp=timestamp,
        )

    async def shutdown(self):
        """Graceful shutdown of all components"""
        logger.info("Shutting down QAST Core...")
        self.running = False

        try:
            # Stop observer
            await self.observer.stop_observation()

            # Shutdown Multi-Exchange Dimensional Manager
            if self.multi_exchange_manager:
                await self.multi_exchange_manager.shutdown()
                logger.info("🛑 Multi-Exchange Dimensional Manager shutdown")

            # Shutdown exchanges
            for name, exchange in self.exchanges.items():
                await exchange.shutdown()
                logger.info(f"Exchange {name} disconnected")

            # Shutdown subsystems
            await self.memory_system.shutdown()
            await self.metacognition.shutdown()

            logger.info("QAST Core shutdown complete")

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status"""
        try:
            return {
                "running": self.running,
                "exchanges_connected": len(self.exchanges),
                "current_coherence": (
                    float(self.current_qualia_state.coherence_level)
                    if self.current_qualia_state
                    else 0.0
                ),
                "quantum_entanglement": (
                    float(self.current_qualia_state.quantum_entanglement)
                    if self.current_qualia_state
                    else 0.0
                ),
                "observer_active": self.observer.is_observing,
                "memory_size": (
                    len(self.memory_system.memories)
                    if hasattr(self.memory_system, "memories")
                    else 0
                ),
                "state_history_length": len(self.state_history),
                "last_update": (
                    self.current_qualia_state.timestamp
                    if self.current_qualia_state
                    else 0
                ),
            }
        except Exception as e:
            logger.warning(f"Failed to get system status: {e}")
            return {"running": False, "error": str(e)}

    async def generate_quantum_signature(
        self,
        universe: Any,
        **kwargs: Any,
    ) -> Optional[Any]:
        """Wrapper to generate quantum signature from the universe."""
        return await universe.generate_quantum_signature(**kwargs)

    def collect_quantum_metrics(self, universe: Any) -> Dict[str, Any]:
        """Return metrics from the provided universe if available."""
        try:
            if (
                hasattr(universe, "metrics")
                and universe.metrics is not None
                and hasattr(universe.metrics, "get_metrics_dict")
                and callable(getattr(universe.metrics, "get_metrics_dict"))
            ):
                return universe.metrics.get_metrics_dict()
        except Exception as exc:  # pragma: no cover - best effort
            logger.debug("collect_quantum_metrics failed: %s", exc)
        return {}

    def create_circuit_from_market_features(
        self, market_features: Dict[str, Any], n_qubits: int
    ) -> Optional[QuantumCircuit]:
        """Create a quantum circuit from market features."""
        try:
            if not market_features or not isinstance(market_features, dict):
                logger.warning(
                    "Market features inválidas ou vazias para criação de circuito"
                )
                return None

            if n_qubits < 2:
                logger.warning("Número de qubits insuficiente: %s", n_qubits)
                return None

            qc = QuantumCircuit(n_qubits)

            price_change = market_features.get("price_change_5m", 0.0)
            volatility = market_features.get("volatility_5m", 0.0)
            volume_change = market_features.get("volume_change_5m", 0.0)

            price_change_norm = min(max(price_change * 10, -np.pi), np.pi) + np.pi
            volatility_norm = min(volatility * 20, 2 * np.pi)
            volume_change_norm = min(max(volume_change * 5, -np.pi), np.pi) + np.pi

            qc.rx(price_change_norm, 0)
            qc.ry(volatility_norm, 1)
            if n_qubits > 2 and "volume_change_5m" in market_features:
                qc.rz(volume_change_norm, 2)

            if n_qubits > 3:
                for i in range(n_qubits - 1):
                    qc.cx(i, i + 1)

            rsi = market_features.get(f"{next(iter(market_features), 'default')}_rsi")
            if rsi is not None and n_qubits > 3:
                rsi_norm = (rsi / 100) * 2 * np.pi
                qc.ry(rsi_norm, 3)

            qc.barrier()

            logger.debug(
                "Circuito criado com %s qubits e profundidade %s",
                n_qubits,
                qc.depth(),
            )
            return qc
        except Exception as exc:  # noqa: BLE001 - log and return None
            logger.exception(
                "Erro ao criar circuito quântico a partir de market_features: %s",
                exc,
            )
            return None

    async def run_qast_evolution(
        self,
        qast_engines: Dict[str, Any],
        market_data: Dict[str, Any],
        primary_timeframe: str,
        results_dir: Path,
    ) -> None:
        """Execute QAST evolution for all engines and persist best DNA."""
        for symbol, engine in qast_engines.items():
            if not engine:
                continue
            market_df = market_data.get(symbol, {}).get(primary_timeframe)
            if market_df is None or (hasattr(market_df, "empty") and market_df.empty):
                continue

            cycles = getattr(engine, "generations_per_call", 1)
            best = engine.evolve_strategies(market_df, cycles=cycles)
            if best is None:
                continue

            dna_dir = Path(results_dir) / "qast"
            dna_dir.mkdir(parents=True, exist_ok=True)
            dna_file = dna_dir / f"{symbol.replace('/', '_')}_best_dna.json"

            if isinstance(best, dict):
                dna_data = best
            elif hasattr(best, "get_parameters"):
                dna_data = {
                    "id": getattr(best, "id", None),
                    "parameters": best.get_parameters(),
                }
            else:
                dna_data = {
                    "id": getattr(best, "id", None),
                    "parameters": getattr(best, "params", {}),
                }

            with open(dna_file, "w", encoding="utf-8") as f:
                json.dump(
                    {"generation": engine.generation, "dna": dna_data}, f, indent=2
                )
