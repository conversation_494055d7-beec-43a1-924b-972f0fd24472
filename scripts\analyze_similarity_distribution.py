#!/usr/bin/env python
"""Utility to analyze similarity distribution of stored QPM patterns.

This script loads the Quantum Pattern Memory persistence file and computes
pairwise cosine similarities between all stored pattern vectors. It outputs
basic statistics and a suggested similarity threshold based on the 95th
percentile of observed similarities. If the persistence file is missing,
synthetic patterns are generated for demonstration purposes.
"""

from __future__ import annotations

import argparse
from pathlib import Path

import numpy as np

from qualia.memory.qpm_utils import (
    compute_similarity_distribution,
    load_qpm_vectors,
)

DEFAULT_PERSISTENCE_PATH = Path("data") / "cache" / "qpm_memory.json"


def main() -> None:
    parser = argparse.ArgumentParser(description="Analyze QPM similarity distribution")
    parser.add_argument(
        "--path", default=DEFAULT_PERSISTENCE_PATH, help="Path to QPM persistence JSON"
    )
    args = parser.parse_args()

    vectors = load_qpm_vectors(args.path)
    if len(vectors) < 2:
        print("Not enough vectors to compute similarities.")
        return

    sims = compute_similarity_distribution(vectors)
    if sims.size == 0:
        print("No similarities calculated.")
        return

    mean = float(np.mean(sims))
    std = float(np.std(sims))
    median = float(np.median(sims))
    pct95 = float(np.percentile(sims, 95))

    print(
        f"count={sims.size} mean={mean:.4f} std={std:.4f} median={median:.4f} pct95={pct95:.4f}"
    )
    print(f"Suggested similarity_threshold={pct95:.2f}")


if __name__ == "__main__":
    main()
