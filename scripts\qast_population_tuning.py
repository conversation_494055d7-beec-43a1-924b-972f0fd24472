#!/usr/bin/env python
"""Evaluate QAST population sizes.

This script runs a small evolutionary loop for different population sizes
(32, 48, 64) using a dummy trading strategy. For each size, five generations
are executed and the mean fitness and Shannon diversity of the resulting
population are recorded. A comparative table is printed for use with
<PERSON> followed by Nemenyi post-hoc tests.
"""

from __future__ import annotations

import logging

from qualia.utils.logger import get_logger
import math
from typing import Dict, List, Sequence, Tuple

import numpy as np
import pandas as pd

from qualia.market.qast_evolutionary_strategy import QASTEvolutionaryStrategy
from qualia.strategies.strategy_interface import OrderDecision, TradingStrategy


logging.basicConfig(level=logging.INFO)
logger = get_logger(__name__)


class DummyStrategy(TradingStrategy):
    """Minimal strategy used for population tuning."""

    strategy_name = "Dummy"

    def _initialize_specific_parameters(self) -> None:  # pragma: no cover - simplicity
        pass

    def analyze_market(
        self,
        market_data: pd.DataFrame,
        trading_context,
        quantum_metrics=None,
        similar_past_patterns=None,
    ) -> OrderDecision:
        return OrderDecision(signal="HOLD", confidence=0.0)

    def backtest(
        self,
        market_data: pd.DataFrame,
        initial_capital: float = 10000.0,
        risk_per_trade_pct: float = 0.01,
        **_: Dict,
    ) -> Dict[str, float]:
        pnl = float(np.random.normal())
        return {
            "final_capital": initial_capital + pnl,
            "trades_details": [{"pnl": pnl, "fee": 0.0}],
            "total_fees": 0.0,
        }


def _shannon_diversity(population: Sequence[TradingStrategy]) -> float:
    """Compute Shannon diversity for a population of strategies."""

    counts: Dict[Tuple[Tuple[str, float], ...], int] = {}
    for strat in population:
        params: Dict = getattr(strat, "params", getattr(strat, "parameters", {}))
        key = tuple(sorted(params.items()))
        counts[key] = counts.get(key, 0) + 1

    total = sum(counts.values())
    if total == 0:
        return 0.0
    ent = -sum((c / total) * math.log(c / total) for c in counts.values())
    return float(ent)


def _run_for_size(pop_size: int) -> Tuple[float, float]:
    """Run five generations with the given population size."""

    template = DummyStrategy(params={"x": 1})
    qast = QASTEvolutionaryStrategy(
        strategy_template=template,
        qualia_consciousness=None,
        population_size=pop_size,
    )
    qast.initialize_population()

    fitness_samples: List[float] = []
    diversity_samples: List[float] = []

    for _ in range(5):
        for strat in qast.population:
            strat.fitness = np.random.rand()
        qast.population.sort(key=lambda s: s.fitness, reverse=True)
        qast.best_strategy = qast.population[0]
        fitness_samples.append(float(np.mean([s.fitness for s in qast.population])))
        diversity_samples.append(_shannon_diversity(qast.population))
        qast.generation += 1

    avg_fitness = float(np.mean(fitness_samples))
    avg_diversity = float(np.mean(diversity_samples))

    logger.info(
        "pop_size=%d avg_fitness=%.4f shannon_diversity=%.4f",
        pop_size,
        avg_fitness,
        avg_diversity,
    )
    return avg_fitness, avg_diversity


def main() -> None:
    sizes = [32, 48, 64]
    records = []
    for size in sizes:
        avg_fit, div = _run_for_size(size)
        records.append(
            {"population_size": size, "avg_fitness": avg_fit, "shannon_diversity": div}
        )

    df = pd.DataFrame(records)
    logger.info("\n%s", df.to_markdown(index=False))


if __name__ == "__main__":
    main()
