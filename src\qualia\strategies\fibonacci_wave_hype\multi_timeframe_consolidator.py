"""Consolidador de Sinais Multi-Timeframe para Estratégia FWH.

Este módulo implementa a lógica de consolidação de sinais entre múltiplos
timeframes, otimizando a geração de sinais da estratégia Fibonacci Wave Hype.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import hashlib

try:
    from ....utils.logger import get_logger
    from ...utils.otoc_calculator import calculate_otoc, calculate_adaptive_threshold
except ImportError:
    # Fallback para execução direta
    import logging
    def get_logger(name):
        return logging.getLogger(name)

    # Mock functions for fallback
    def calculate_otoc(*args, **kwargs):
        return 0.0
    def calculate_adaptive_threshold(*args, **kwargs):
        return 0.35

logger = get_logger(__name__)

@dataclass
class TimeframeSignal:
    """Representa um sinal de um timeframe específico."""
    timeframe: str
    signal: str  # 'buy', 'sell', 'hold'
    confidence: float
    signal_strength: float
    hype_momentum: float
    holographic_boost: float
    tsvf_validation: float
    timestamp: datetime
    # YAA-BACKWARDS-COMPAT: Default value prevents TypeError in legacy code
    otoc_value: float = 0.0

@dataclass
class ConsolidatedSignal:
    """Representa um sinal consolidado de múltiplos timeframes."""
    signal: str
    confidence: float
    primary_timeframe: str
    supporting_timeframes: List[str]
    convergence_score: float
    individual_signals: List[TimeframeSignal]
    reasoning: str

class MultiTimeframeSignalConsolidator:
    """Consolidador de sinais multi-timeframe para estratégia FWH."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Inicializa o consolidador.

        Args:
            config: Configuração do consolidador
        """
        self.config = config or {}

        # Pesos por timeframe - DEVE ser fornecido via configuração
        self.timeframe_weights = self.config.get("timeframe_weights", {})

        # Validação: timeframe_weights é obrigatório
        if not self.timeframe_weights:
            raise ValueError(
                "timeframe_weights é obrigatório na configuração do consolidador. "
                "Defina os pesos para cada timeframe no arquivo de configuração YAML."
            )

        # Validação: verificar se todos os timeframes necessários têm pesos
        required_timeframes = ["1m", "5m", "15m", "1h"]
        missing_timeframes = [tf for tf in required_timeframes if tf not in self.timeframe_weights]
        if missing_timeframes:
            raise ValueError(
                f"Pesos faltando para timeframes: {missing_timeframes}. "
                f"Defina pesos para todos os timeframes: {required_timeframes}"
            )

        # Validação: verificar se os pesos são numéricos e positivos
        for tf, weight in self.timeframe_weights.items():
            if not isinstance(weight, (int, float)) or weight <= 0:
                raise ValueError(
                    f"Peso inválido para timeframe '{tf}': {weight}. "
                    "Pesos devem ser números positivos."
                )
        
        # Thresholds de consolidação
        self.min_confidence_threshold = self.config.get("min_confidence_threshold", 0.12)
        self.convergence_threshold = self.config.get("convergence_threshold", 0.7)
        self.divergence_penalty = self.config.get("divergence_penalty", 0.5)
        
        # Configurações de qualidade
        self.require_primary_signal = self.config.get("require_primary_signal", True)

        # YAA-OTOC: Configuração do filtro OTOC anti-caos
        otoc_config = self.config.get("otoc_config", {})
        self.otoc_enabled = bool(otoc_config) and otoc_config.get("enabled", True)
        self.otoc_max_threshold = otoc_config.get("max_threshold", 0.35)
        self.otoc_window = otoc_config.get("window", 100)
        self.otoc_delta = otoc_config.get("delta", 1)
        self.otoc_method = otoc_config.get("method", "correlation")

        # Threshold adaptativo
        adaptive_config = otoc_config.get("adaptive_threshold", {})
        self.otoc_adaptive_enabled = adaptive_config.get("enabled", True)
        self.otoc_beta = adaptive_config.get("beta", 1.0)
        self.otoc_vol_window = adaptive_config.get("vol_window", 20)
        self.max_timeframe_age_minutes = self.config.get("max_timeframe_age_minutes", 5)
        
        # Sistema de cache para dados reamostrados
        self.cache_enabled = self.config.get("cache_enabled", True)
        self.cache_ttl_minutes = self.config.get("cache_ttl_minutes", 2)
        self.resample_cache: Dict[str, Dict] = {}
        
        logger.info(f"[MTF-Consolidator] Inicializado com pesos da configuração: {self.timeframe_weights}")
        logger.info(f"[MTF-Consolidator] Cache habilitado: {self.cache_enabled} (TTL: {self.cache_ttl_minutes}m)")
        logger.info(f"[MTF-Consolidator] Thresholds: confiança={self.min_confidence_threshold}, convergência={self.convergence_threshold}")
        logger.info(f"[MTF-Consolidator] Configuração carregada do arquivo YAML - sem valores hardcoded")
    
    def _generate_cache_key(self, data_1m: pd.DataFrame, target_timeframe: str) -> str:
        """Gera chave única para cache baseada nos dados e timeframe."""
        # Usa hash dos últimos timestamps e valores para criar chave única
        last_rows = data_1m.tail(5)
        data_str = f"{target_timeframe}_{last_rows.index[-1]}_{last_rows['close'].iloc[-1]}"
        return hashlib.md5(data_str.encode()).hexdigest()[:16]
    
    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """Verifica se entrada do cache ainda é válida."""
        if not cache_entry:
            return False
        
        cache_time = cache_entry.get('timestamp')
        if not cache_time:
            return False
        
        age_minutes = (datetime.now() - cache_time).total_seconds() / 60
        return age_minutes < self.cache_ttl_minutes
    
    def _diagnose_data_issues(self, data_1m: pd.DataFrame, target_timeframe: str) -> Dict[str, Any]:
        """Diagnostica problemas com dados de entrada para melhor debugging."""
        diagnosis = {
            'total_periods': len(data_1m),
            'has_data': not data_1m.empty,
            'time_span_minutes': 0.0,
            'has_valid_timestamps': False,
            'duplicate_timestamps': 0,
            'issues': []
        }
        
        if len(data_1m) == 0:
            diagnosis['issues'].append('Dados vazios')
            return diagnosis
        
        # Verifica timestamps
        if isinstance(data_1m.index, pd.DatetimeIndex):
            diagnosis['has_valid_timestamps'] = True
            if len(data_1m) > 1:
                diagnosis['time_span_minutes'] = (data_1m.index.max() - data_1m.index.min()).total_seconds() / 60
                diagnosis['duplicate_timestamps'] = data_1m.index.duplicated().sum()
                
                if diagnosis['time_span_minutes'] < 0.5:
                    diagnosis['issues'].append(f'Span temporal muito pequeno: {diagnosis["time_span_minutes"]:.1f}min')
                
                if diagnosis['duplicate_timestamps'] > 0:
                    diagnosis['issues'].append(f'{diagnosis["duplicate_timestamps"]} timestamps duplicados')
        else:
            diagnosis['issues'].append('Índice não é DatetimeIndex')
        
        # Verifica se há dados suficientes para o timeframe
        min_for_timeframe = {'5m': 3, '15m': 5, '1h': 10}.get(target_timeframe, 3)
        if len(data_1m) < min_for_timeframe:
            diagnosis['issues'].append(f'Poucos períodos para {target_timeframe}: {len(data_1m)} < {min_for_timeframe}')
        
        return diagnosis
    
    def resample_data(self, data_1m: pd.DataFrame, target_timeframe: str) -> pd.DataFrame:
        """Reamostra dados de 1m para timeframe maior com cache otimizado.
        
        Args:
            data_1m: Dados OHLCV de 1 minuto
            target_timeframe: Timeframe alvo (ex: '5m', '15m')
            
        Returns:
            Dados reamostrados para o timeframe alvo
        """
        try:
            # Diagnóstico inicial dos dados
            diagnosis = self._diagnose_data_issues(data_1m, target_timeframe)
            
            if diagnosis['issues']:
                logger.debug(f"[MTF-Consolidator] Problemas detectados para {target_timeframe}: {'; '.join(diagnosis['issues'])}")
                # Se há problemas críticos, retorna vazio
                if 'Dados vazios' in diagnosis['issues'] or 'Índice não é DatetimeIndex' in diagnosis['issues']:
                    return pd.DataFrame()
            
            # Verifica cache se habilitado
            if self.cache_enabled:
                cache_key = self._generate_cache_key(data_1m, target_timeframe)
                cache_entry = self.resample_cache.get(cache_key)
                
                if cache_entry and self._is_cache_valid(cache_entry):
                    logger.debug(f"[MTF-Consolidator] Cache hit para {target_timeframe}")
                    return cache_entry['data']
            # Mapeia timeframes para frequências pandas
            freq_map = {
                '1m': '1min', '5m': '5min', '15m': '15min', 
                '30m': '30min', '1h': '1h', '4h': '4h'
            }
            
            if target_timeframe not in freq_map:
                raise ValueError(f"Timeframe não suportado: {target_timeframe}")
            
            freq = freq_map[target_timeframe]
            
            # Validação de dados mínimos antes do resample
            # Ajustado para ser mais flexível com dados limitados (paper trading)
            min_periods_required = {
                '5m': 3,     # Reduzido: mínimo 3 períodos de 1m
                '15m': 5,    # Reduzido: mínimo 5 períodos de 1m  
                '1h': 10     # Reduzido: mínimo 10 períodos de 1m
            }
            
            min_required = min_periods_required.get(target_timeframe, 3)
            if len(data_1m) < min_required:
                logger.debug(f"[MTF-Consolidator] Dados insuficientes para {target_timeframe}: {len(data_1m)} < {min_required}")
                # Modo fallback: retorna dados originais se timeframe for próximo
                if target_timeframe in ['5m'] and len(data_1m) >= 2:
                    logger.info(f"[MTF-Consolidator] Usando fallback para {target_timeframe} com {len(data_1m)} períodos")
                else:
                    return pd.DataFrame()
            
            # Verifica span temporal dos dados para detectar problemas
            if len(data_1m) > 1:
                time_span_minutes = (data_1m.index.max() - data_1m.index.min()).total_seconds() / 60
                if time_span_minutes < 1.0:  # Menos de 1 minuto de dados
                    logger.warning(f"[MTF-Consolidator] Span temporal muito pequeno para {target_timeframe}: {time_span_minutes:.1f} minutos")
                    # Para timeframes pequenos, ainda tenta processar
                    if target_timeframe not in ['5m'] or time_span_minutes < 0.1:
                        return pd.DataFrame()
            
            # Validação mais flexível: aceita pelo menos 1 período após resample
            expected_periods_after_resample = {
                '5m': max(1, len(data_1m) // 5),
                '15m': max(1, len(data_1m) // 15),
                '1h': max(1, len(data_1m) // 60)
            }
            
            expected_periods = expected_periods_after_resample.get(target_timeframe, len(data_1m))
            if expected_periods < 1:  # Reduzido: aceita pelo menos 1 período
                logger.debug(f"[MTF-Consolidator] Nenhum período esperado após resample para {target_timeframe}")
                return pd.DataFrame()
            
            # Garante que o índice é datetime
            if not isinstance(data_1m.index, pd.DatetimeIndex):
                data_1m = data_1m.copy()
                data_1m.index = pd.to_datetime(data_1m.index)
            
            # Log detalhado para debug
            logger.debug(f"[MTF-Consolidator] Dados antes resample {target_timeframe}: {len(data_1m)} períodos")
            logger.debug(f"[MTF-Consolidator] Índice range: {data_1m.index.min()} até {data_1m.index.max()}")
            logger.debug(f"[MTF-Consolidator] Frequência: {freq}")
            
            # Reamostra os dados
            resampled = data_1m.resample(freq).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()
            
            logger.debug(f"[MTF-Consolidator] Reamostrado {len(data_1m)} -> {len(resampled)} para {target_timeframe}")
            
            # Tratamento inteligente para resultados pequenos
            if len(resampled) <= 2:
                logger.debug(f"[MTF-Consolidator] Resultado pequeno para {target_timeframe}: {len(resampled)} períodos")
                time_span = (data_1m.index.max() - data_1m.index.min()).total_seconds() / 60 if len(data_1m) > 1 else 0.0
                logger.debug(f"[MTF-Consolidator] Dados originais span: {time_span:.1f} minutos")
                
                # Modo fallback: para timeframes pequenos com poucos dados, usa dados originais
                if target_timeframe == '5m' and len(resampled) == 1 and len(data_1m) >= 2:
                    logger.info(f"[MTF-Consolidator] Aplicando fallback: usando dados 1m como proxy para {target_timeframe}")
                    # Retorna últimos períodos dos dados originais como proxy
                    fallback_data = data_1m.tail(min(5, len(data_1m))).copy()
                    return fallback_data
                elif len(resampled) == 0:
                    logger.warning(f"[MTF-Consolidator] Resample resultou em dados vazios para {target_timeframe}")
                    return pd.DataFrame()
            
            # Armazena no cache se habilitado
            if self.cache_enabled:
                cache_key = self._generate_cache_key(data_1m, target_timeframe)
                self.resample_cache[cache_key] = {
                    'data': resampled.copy(),
                    'timestamp': datetime.now()
                }
                
                # Limpa cache antigo (mantém apenas últimas 10 entradas)
                if len(self.resample_cache) > 10:
                    oldest_key = min(self.resample_cache.keys(), 
                                    key=lambda k: self.resample_cache[k]['timestamp'])
                    del self.resample_cache[oldest_key]
            
            return resampled
            
        except Exception as e:
            logger.error(f"[MTF-Consolidator] Erro no resample para {target_timeframe}: {e}")
            return pd.DataFrame()
    
    def calculate_convergence_score(self, signals: List[TimeframeSignal]) -> float:
        """Calcula score de convergência entre sinais.
        
        Args:
            signals: Lista de sinais de diferentes timeframes
            
        Returns:
            Score de convergência (0.0 a 1.0)
        """
        if len(signals) < 2:
            return 1.0
        
        # Conta sinais por direção
        buy_signals = sum(1 for s in signals if s.signal == 'buy')
        sell_signals = sum(1 for s in signals if s.signal == 'sell')
        hold_signals = sum(1 for s in signals if s.signal == 'hold')
        
        total_signals = len(signals)
        
        # Calcula convergência baseada na direção dominante
        max_direction = max(buy_signals, sell_signals, hold_signals)
        convergence = max_direction / total_signals
        
        # Penaliza se há muitos sinais de hold
        if hold_signals > total_signals * 0.5:
            convergence *= 0.7
        
        # Bonus para convergência forte entre buy/sell
        if buy_signals > 0 and sell_signals == 0:
            convergence *= 1.2
        elif sell_signals > 0 and buy_signals == 0:
            convergence *= 1.2
        
        return min(convergence, 1.0)
    
    def apply_trend_filter(self, signals: List[TimeframeSignal]) -> List[TimeframeSignal]:
        """Aplica filtro de tendência principal baseado no timeframe de 1h.
        
        Args:
            signals: Lista de sinais de diferentes timeframes
            
        Returns:
            Lista de sinais filtrados pela tendência principal
        """
        # Encontra sinal do timeframe de 1h (tendência principal)
        trend_signal = None
        for signal in signals:
            if signal.timeframe == '1h':
                trend_signal = signal
                break
        
        # Se não há sinal de 1h ou é hold, retorna sinais originais
        if not trend_signal or trend_signal.signal == 'hold':
            return signals
        
        # Filtra sinais que estão alinhados com a tendência principal
        filtered_signals = []
        for signal in signals:
            # Timeframe de 1h sempre passa
            if signal.timeframe == '1h':
                filtered_signals.append(signal)
            # Outros timeframes devem estar alinhados ou ser neutros
            elif signal.signal == trend_signal.signal or signal.signal == 'hold':
                filtered_signals.append(signal)
            # Sinais contrários à tendência são penalizados
            else:
                # Reduz confiança de sinais contrários
                penalized_signal = TimeframeSignal(
                    timeframe=signal.timeframe,
                    signal=signal.signal,
                    confidence=signal.confidence * 0.3,  # Penalidade de 70%
                    signal_strength=signal.signal_strength,
                    hype_momentum=signal.hype_momentum,
                    holographic_boost=signal.holographic_boost,
                    tsvf_validation=signal.tsvf_validation,
                    timestamp=signal.timestamp
                )
                filtered_signals.append(penalized_signal)
        
        return filtered_signals

    def apply_otoc_filter(
        self,
        signals: List[TimeframeSignal],
        market_data: Optional[Dict[str, pd.DataFrame]] = None
    ) -> List[TimeframeSignal]:
        """
        Aplica filtro OTOC para bloquear sinais em regimes caóticos.

        YAA-CHAOS-FILTER: Implementa a lógica central do roadmap ICTR+OTOC

        Args:
            signals: Lista de sinais de diferentes timeframes
            market_data: Dados de mercado por timeframe para cálculo OTOC

        Returns:
            Lista de sinais filtrados pelo OTOC
        """
        if not self.otoc_enabled or not signals:
            return signals

        filtered_signals = []

        for signal in signals:
            try:
                # YAA-NAN-HANDLING: Se OTOC é NaN (warm-up), manter sinal original
                if np.isnan(signal.otoc_value):
                    logger.debug(f"OTOC NaN para {signal.timeframe}, mantendo sinal original")
                    filtered_signals.append(signal)
                    continue

                # Calcular threshold adaptativo se habilitado
                effective_threshold = self.otoc_max_threshold

                if self.otoc_adaptive_enabled and market_data:
                    timeframe_data = market_data.get(signal.timeframe)
                    if timeframe_data is not None and len(timeframe_data) > self.otoc_vol_window:
                        # Calcular volatilidade recente
                        returns = timeframe_data["close"].pct_change()
                        volatility = returns.rolling(self.otoc_vol_window).std().iloc[-1]

                        if not np.isnan(volatility):
                            effective_threshold = calculate_adaptive_threshold(
                                base_threshold=self.otoc_max_threshold,
                                volatility=volatility,
                                beta=self.otoc_beta,
                                vol_window=self.otoc_vol_window
                            )

                            logger.debug(
                                f"OTOC threshold adaptativo para {signal.timeframe}: "
                                f"{effective_threshold:.3f} (vol={volatility:.4f})"
                            )

                # YAA-CHAOS-DETECTION: Aplicar filtro OTOC
                if signal.otoc_value > effective_threshold:
                    # Mercado caótico detectado - converter para HOLD
                    chaos_filtered_signal = TimeframeSignal(
                        timeframe=signal.timeframe,
                        signal="hold",  # YAA-ANTI-CHAOS: Bloquear operação
                        confidence=0.0,  # Zerar confiança
                        signal_strength=0.0,
                        hype_momentum=signal.hype_momentum,
                        holographic_boost=signal.holographic_boost,
                        tsvf_validation=signal.tsvf_validation,
                        timestamp=signal.timestamp,
                        otoc_value=signal.otoc_value
                    )

                    logger.info(
                        f"🌀 OTOC FILTER: Regime caótico detectado em {signal.timeframe} "
                        f"(OTOC={signal.otoc_value:.3f} > {effective_threshold:.3f}). "
                        f"Sinal {signal.signal.upper()} → HOLD"
                    )

                    filtered_signals.append(chaos_filtered_signal)
                else:
                    # Regime ordenado - manter sinal original
                    logger.debug(
                        f"OTOC OK para {signal.timeframe}: "
                        f"{signal.otoc_value:.3f} <= {effective_threshold:.3f}"
                    )
                    filtered_signals.append(signal)

            except Exception as e:
                logger.error(f"Erro no filtro OTOC para {signal.timeframe}: {e}")
                # Em caso de erro, manter sinal original (fail-safe)
                filtered_signals.append(signal)

        return filtered_signals

    def consolidate_signals(self, signals: List[TimeframeSignal]) -> ConsolidatedSignal:
        """Consolida sinais de múltiplos timeframes.
        
        Args:
            signals: Lista de sinais de diferentes timeframes
            
        Returns:
            Sinal consolidado
        """
        try:
            if not signals:
                return ConsolidatedSignal(
                    signal='hold',
                    confidence=0.0,
                    primary_timeframe='none',
                    supporting_timeframes=[],
                    convergence_score=0.0,
                    individual_signals=[],
                    reasoning="Nenhum sinal fornecido"
                )
            
            # Aplica filtro de tendência principal
            trend_filtered_signals = self.apply_trend_filter(signals)

            # YAA-OTOC-INTEGRATION: Aplica filtro OTOC anti-caos
            otoc_filtered_signals = self.apply_otoc_filter(trend_filtered_signals)

            # Filtra sinais válidos
            valid_signals = [s for s in otoc_filtered_signals if s.confidence > 0.05]
            
            if not valid_signals:
                return ConsolidatedSignal(
                    signal='hold',
                    confidence=0.0,
                    primary_timeframe='none',
                    supporting_timeframes=[],
                    convergence_score=0.0,
                    individual_signals=signals,
                    reasoning="Nenhum sinal com confiança suficiente"
                )
            
            # Calcula convergência
            convergence_score = self.calculate_convergence_score(valid_signals)
            
            # Calcula confiança ponderada por timeframe
            weighted_buy_confidence = 0.0
            weighted_sell_confidence = 0.0
            total_weight = 0.0
            
            for signal in valid_signals:
                weight = self.timeframe_weights.get(signal.timeframe, 0.5)
                total_weight += weight
                
                if signal.signal == 'buy':
                    weighted_buy_confidence += signal.confidence * weight
                elif signal.signal == 'sell':
                    weighted_sell_confidence += signal.confidence * weight
            
            # Normaliza pelas pesos
            if total_weight > 0:
                weighted_buy_confidence /= total_weight
                weighted_sell_confidence /= total_weight
            
            # Aplica fator de convergência
            weighted_buy_confidence *= convergence_score
            weighted_sell_confidence *= convergence_score
            
            # Determina sinal final
            if weighted_buy_confidence > weighted_sell_confidence and weighted_buy_confidence > self.min_confidence_threshold:
                final_signal = 'buy'
                final_confidence = weighted_buy_confidence
            elif weighted_sell_confidence > weighted_buy_confidence and weighted_sell_confidence > self.min_confidence_threshold:
                final_signal = 'sell'
                final_confidence = weighted_sell_confidence
            else:
                final_signal = 'hold'
                final_confidence = 0.0
            
            # Identifica timeframe primário (maior peso com sinal válido)
            primary_timeframe = 'none'
            max_weighted_confidence = 0.0
            
            for signal in valid_signals:
                if signal.signal == final_signal:
                    weight = self.timeframe_weights.get(signal.timeframe, 0.5)
                    weighted_conf = signal.confidence * weight
                    
                    if weighted_conf > max_weighted_confidence:
                        max_weighted_confidence = weighted_conf
                        primary_timeframe = signal.timeframe
            
            # Identifica timeframes de suporte
            supporting_timeframes = [
                s.timeframe for s in valid_signals 
                if s.signal == final_signal and s.timeframe != primary_timeframe
            ]
            
            # Gera reasoning
            reasoning = self._generate_reasoning(
                final_signal, final_confidence, convergence_score, 
                valid_signals, primary_timeframe, supporting_timeframes
            )
            
            logger.info(f"[MTF-Consolidator] Sinal consolidado: {final_signal} (conf: {final_confidence:.3f}, conv: {convergence_score:.3f})")
            
            return ConsolidatedSignal(
                signal=final_signal,
                confidence=final_confidence,
                primary_timeframe=primary_timeframe,
                supporting_timeframes=supporting_timeframes,
                convergence_score=convergence_score,
                individual_signals=signals,
                reasoning=reasoning
            )
            
        except Exception as e:
            logger.error(f"[MTF-Consolidator] Erro na consolidação: {e}")
            return ConsolidatedSignal(
                signal='hold',
                confidence=0.0,
                primary_timeframe='error',
                supporting_timeframes=[],
                convergence_score=0.0,
                individual_signals=signals,
                reasoning=f"Erro na consolidação: {str(e)}"
            )
    
    def _generate_reasoning(self, signal: str, confidence: float, convergence: float,
                          signals: List[TimeframeSignal], primary_tf: str, 
                          supporting_tfs: List[str]) -> str:
        """Gera explicação para o sinal consolidado."""
        
        if signal == 'hold':
            if convergence < self.convergence_threshold:
                return f"Sinais divergentes entre timeframes (convergência: {convergence:.2f})"
            else:
                return f"Confiança insuficiente (máx: {confidence:.3f} < {self.min_confidence_threshold})"
        
        reasoning_parts = [
            f"Sinal {signal.upper()} consolidado",
            f"Timeframe primário: {primary_tf}"
        ]
        
        if supporting_tfs:
            reasoning_parts.append(f"Suporte: {', '.join(supporting_tfs)}")
        
        reasoning_parts.extend([
            f"Confiança: {confidence:.3f}",
            f"Convergência: {convergence:.2f}"
        ])
        
        return " | ".join(reasoning_parts)