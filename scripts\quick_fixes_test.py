#!/usr/bin/env python3
"""
Quick validation test for QUALIA P-02.3 fixes
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_unicode_fixes():
    """Test Unicode character fixes"""
    print("[TEST] Testing Unicode fixes...")
    
    # Import the SafeUnicodeFormatter
    from scripts.qualia_pilot_trading_system import SafeUnicodeFormatter
    import logging
    
    # Create formatter
    formatter = SafeUnicodeFormatter()
    
    # Create test record with Unicode characters
    record = logging.LogRecord(
        name="test",
        level=logging.INFO,
        pathname="test.py",
        lineno=1,
        msg="Test message with Unicode: ✅ ❌ ⚠️ 🔄 🚀 🧠",
        args=(),
        exc_info=None
    )
    
    # Test formatting
    try:
        formatted = formatter.format(record)
        print(f"[SUCCESS] Unicode formatting works: {formatted}")
        
        # Check that problematic Unicode characters are replaced
        if "✅" not in formatted and "[OK]" in formatted:
            print("[SUCCESS] Unicode characters properly replaced with ASCII")
            return True
        else:
            print("[WARNING] Unicode characters may not be fully replaced")
            return False
            
    except Exception as e:
        print(f"[ERROR] Unicode formatting failed: {e}")
        return False

def test_environment_loading():
    """Test environment variable loading"""
    print("[TEST] Testing environment variable loading...")
    
    from dotenv import load_dotenv
    
    try:
        # Load environment variables
        env_path = project_root / '.env'
        if env_path.exists():
            load_dotenv(env_path)
            print(f"[SUCCESS] Environment loaded from: {env_path}")
            
            # Check KuCoin credentials
            api_key = os.getenv('KUCOIN_API_KEY')
            api_secret = os.getenv('KUCOIN_API_SECRET') or os.getenv('KUCOIN_SECRET_KEY')
            passphrase = os.getenv('KUCOIN_PASSPHRASE')
            
            if api_key and api_secret and passphrase:
                print("[SUCCESS] All KuCoin credentials loaded")
                return True
            else:
                print("[WARNING] Some KuCoin credentials missing")
                return False
        else:
            print("[WARNING] .env file not found")
            return False
            
    except Exception as e:
        print(f"[ERROR] Environment loading failed: {e}")
        return False

def test_imports():
    """Test critical imports"""
    print("[TEST] Testing critical imports...")
    
    try:
        # Test QUALIA imports
        from qualia.core.qast_oracle_decision_engine import QASTOracleDecisionEngine
        from qualia.core.unified_qualia_consciousness import UnifiedQUALIAConsciousness
        from qualia.strategies.strategy_factory import StrategyFactory
        from qualia.market.kucoin_integration import KucoinIntegration
        from qualia.utils.network_resilience import CircuitBreaker
        
        print("[SUCCESS] All critical QUALIA components imported")
        return True
        
    except Exception as e:
        print(f"[ERROR] Import failed: {e}")
        return False

def main():
    """Main test function"""
    print("QUALIA P-02.3 Quick Fixes Validation Test")
    print("=" * 50)
    
    tests = [
        ("Unicode Fixes", test_unicode_fixes),
        ("Environment Loading", test_environment_loading),
        ("Critical Imports", test_imports)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[RUNNING] {test_name}")
        try:
            if test_func():
                print(f"[PASSED] {test_name}")
                passed += 1
            else:
                print(f"[FAILED] {test_name}")
        except Exception as e:
            print(f"[ERROR] {test_name} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"[RESULTS] {passed}/{total} tests passed")
    
    if passed == total:
        print("[SUCCESS] All fixes validation tests PASSED")
        return True
    else:
        print("[WARNING] Some tests failed - system may have issues")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"[FATAL] Test crashed: {e}")
        sys.exit(1)
