#!/usr/bin/env python3
"""
Teste de conexão com Binance API usando credenciais do .env
"""

import sys
import os
import asyncio
from dotenv import load_dotenv

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Carregar variáveis de ambiente
load_dotenv(os.path.join(os.path.dirname(__file__), '..', '.env'))

async def test_binance_connection():
    """Testa conexão básica com Binance."""
    print("🔗 TESTE DE CONEXÃO BINANCE")
    print("=" * 50)
    
    try:
        # Verificar credenciais
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')
        
        print(f"📋 Verificando credenciais...")
        print(f"   API Key: {'✅ SET' if api_key else '❌ NOT SET'}")
        print(f"   API Secret: {'✅ SET' if api_secret else '❌ NOT SET'}")
        
        if not api_key or not api_secret:
            print("❌ Credenciais não encontradas no .env")
            return False
        
        print(f"   API Key: {api_key[:8]}...{api_key[-4:]}")
        
        # Importar e testar Binance
        from qualia.market.binance_integration import BinanceIntegration
        
        print(f"\n🚀 Inicializando conexão Binance...")
        binance = BinanceIntegration(
            api_key=api_key,
            api_secret=api_secret
        )
        
        print(f"   Conectando...")
        await binance.initialize_connection()
        print(f"   ✅ Conexão estabelecida!")
        
        # Testar ticker
        print(f"\n📊 Testando ticker BTC/USDT...")
        ticker = await binance.fetch_ticker("BTC/USDT")
        if ticker:
            print(f"   ✅ Ticker recebido:")
            print(f"   Preço: ${ticker.get('last', 'N/A')}")
            print(f"   Volume: {ticker.get('baseVolume', 'N/A')}")
        else:
            print(f"   ❌ Ticker não recebido")
        
        # Testar OHLCV
        print(f"\n📈 Testando OHLCV 1h (últimas 5 velas)...")
        from qualia.common.specs import MarketSpec
        
        spec = MarketSpec(symbol="BTC/USDT", timeframe="1h")
        ohlcv = await binance.fetch_ohlcv(spec, limit=5)
        
        if not ohlcv.empty:
            print(f"   ✅ OHLCV recebido: {len(ohlcv)} velas")
            print(f"   Última vela:")
            last_candle = ohlcv.iloc[-1]
            print(f"   Open: ${last_candle['open']:.2f}")
            print(f"   High: ${last_candle['high']:.2f}")
            print(f"   Low: ${last_candle['low']:.2f}")
            print(f"   Close: ${last_candle['close']:.2f}")
            print(f"   Volume: {last_candle['volume']:.2f}")
        else:
            print(f"   ❌ OHLCV vazio")
        
        # Testar múltiplos timeframes
        print(f"\n⏰ Testando múltiplos timeframes...")
        timeframes = ['1m', '5m', '15m', '1h']
        
        for tf in timeframes:
            try:
                spec = MarketSpec(symbol="BTC/USDT", timeframe=tf)
                df = await binance.fetch_ohlcv(spec, limit=3)
                
                if not df.empty:
                    print(f"   ✅ {tf}: {len(df)} velas")
                else:
                    print(f"   ❌ {tf}: Vazio")
                    
            except Exception as e:
                print(f"   ❌ {tf}: Erro - {e}")
        
        # Fechar conexão
        await binance.close()
        print(f"\n✅ Teste de conexão concluído com sucesso!")
        return True
        
    except Exception as e:
        print(f"\n❌ Erro no teste de conexão: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_historical_data():
    """Testa busca de dados históricos."""
    print(f"\n📚 TESTE DE DADOS HISTÓRICOS")
    print("=" * 50)
    
    try:
        from qualia.market.binance_integration import BinanceIntegration
        from qualia.common.specs import MarketSpec
        from datetime import datetime, timedelta
        
        # Credenciais
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')
        
        # Conectar
        binance = BinanceIntegration(api_key=api_key, api_secret=api_secret)
        await binance.initialize_connection()
        
        # Período de teste (últimos 2 dias)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=2)
        
        print(f"📅 Período: {start_date.strftime('%Y-%m-%d %H:%M')} a {end_date.strftime('%Y-%m-%d %H:%M')}")
        
        # Testar dados históricos
        timeframes = ['1h', '15m']  # Começar com timeframes menos intensivos
        
        for tf in timeframes:
            try:
                print(f"\n🔄 Buscando dados históricos {tf}...")
                
                spec = MarketSpec(symbol="BTC/USDT", timeframe=tf)
                df = await binance.fetch_historical_data(
                    spec=spec,
                    start_date=start_date,
                    end_date=end_date,
                    use_cache=True
                )
                
                if not df.empty:
                    print(f"   ✅ {tf}: {len(df)} velas históricas")
                    print(f"   Período coberto: {df['timestamp'].min()} a {df['timestamp'].max()}")
                    
                    # Mostrar algumas estatísticas
                    print(f"   Preço médio: ${df['close'].mean():.2f}")
                    print(f"   Volume médio: {df['volume'].mean():.2f}")
                else:
                    print(f"   ❌ {tf}: Nenhum dado histórico")
                    
            except Exception as e:
                print(f"   ❌ {tf}: Erro - {e}")
        
        await binance.close()
        print(f"\n✅ Teste de dados históricos concluído!")
        return True
        
    except Exception as e:
        print(f"\n❌ Erro no teste de dados históricos: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Executa todos os testes."""
    print("🧪 TESTES DE INTEGRAÇÃO BINANCE")
    print("=" * 60)
    
    # Teste 1: Conexão básica
    connection_ok = await test_binance_connection()
    
    if connection_ok:
        # Teste 2: Dados históricos
        historical_ok = await test_historical_data()
        
        if historical_ok:
            print(f"\n🎉 TODOS OS TESTES PASSARAM!")
            print(f"✅ Conexão Binance funcionando")
            print(f"✅ Dados em tempo real funcionando")
            print(f"✅ Dados históricos funcionando")
            print(f"\n🚀 Pronto para executar otimização real!")
            return True
    
    print(f"\n❌ ALGUNS TESTES FALHARAM")
    print(f"🔧 Verifique as credenciais e conexão de rede")
    return False

if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print(f"\n✅ Sistema pronto para otimização FWH real!")
    else:
        print(f"\n❌ Corrija os problemas antes de prosseguir")
