"""Quick standalone preview server for the QUALIA SIM token-transition graph.

Execute:
    python scripts/run_sim_graph_preview.py

It will spin up a FastAPI app on http://127.0.0.1:8000, serve the preview
HTML (based on three-forcegraph) and expose ``/api/sim_graph`` returning a
randomly generated graph so that you can test the visualisation without
running the full QUALIA back-end.

If the QUALIA back-end is already running with the real endpoint, simply open
``examples/sim_graph_preview.html`` in your browser instead – this script is
only for quick local testing.
"""
from __future__ import annotations

import random
import threading
import webbrowser
from pathlib import Path
from typing import List

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from starlette.responses import JSONResponse, RedirectResponse

# ---- Graph generator ---------------------------------------------------------

def random_graph(num_nodes: int = 20, max_edges: int = 40):
    nodes = [f"S{random.randrange(0xFFF):03X}" for _ in range(num_nodes)]
    edges = set()
    for _ in range(max_edges):
        u, v = random.sample(nodes, 2)
        edges.add((u, v))
    links = [
        {"source": u, "target": v, "weight": random.randint(1, 5)}
        for u, v in edges
    ]
    return {"nodes": [{"id": n, "weight": random.randint(1, 5)} for n in nodes], "links": links}


# ---- FastAPI app -------------------------------------------------------------

app = FastAPI(title="QUALIA SIM Graph Preview", docs_url=None, redoc_url=None)

BASE_DIR = Path(__file__).resolve().parents[1]
STATIC_DIR = BASE_DIR / "examples"

app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")


@app.get("/api/sim_graph")
async def sim_graph() -> JSONResponse:  # noqa: D401 simple
    """Return a synthetic token graph (random) for preview purposes."""
    return JSONResponse(random_graph())


@app.get("/preview")
async def preview() -> RedirectResponse:  # noqa: D401 simple
    return RedirectResponse(url="/static/sim_graph_preview.html")

@app.get("/")
async def root() -> RedirectResponse:  # noqa: D401 simple
    return RedirectResponse(url="/preview")


# ---- main --------------------------------------------------------------------

def _open_browser():
    webbrowser.open("http://127.0.0.1:8000/preview")


def main():
    import uvicorn

    threading.Timer(1.0, _open_browser).start()
    uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")


if __name__ == "__main__":
    main()
