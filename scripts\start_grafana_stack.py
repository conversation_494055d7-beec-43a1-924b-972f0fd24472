#!/usr/bin/env python3
"""
Script para inicializar o stack completo de monitoramento QUALIA.

YAA REFINEMENT: Inicia Grafana + Prometheus + AlertManager + QUALIA Prometheus Exporter
para observabilidade completa do sistema de trading.
"""

import os
import sys
import time
import subprocess
import signal
import logging
from pathlib import Path
from typing import Optional

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.monitoring.prometheus_exporter import get_prometheus_exporter
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class GrafanaStackManager:
    """Gerenciador do stack completo de monitoramento."""
    
    def __init__(self, docker_compose_path: Optional[str] = None):
        self.docker_compose_path = docker_compose_path or self._find_docker_compose()
        self.prometheus_exporter = None
        self._running = False
        
        # Configurar handler para shutdown graceful
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _find_docker_compose(self) -> str:
        """Encontra o arquivo docker-compose.yml do Grafana stack."""
        possible_paths = [
            "docker/grafana-stack/docker-compose.yml",
            "../docker/grafana-stack/docker-compose.yml",
            "../../docker/grafana-stack/docker-compose.yml"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return os.path.abspath(path)
        
        raise FileNotFoundError(
            "Arquivo docker-compose.yml não encontrado. "
            "Certifique-se de que está no diretório correto."
        )
    
    def _signal_handler(self, signum, frame):
        """Handler para shutdown graceful."""
        logger.info(f"📡 Recebido sinal {signum}, parando stack...")
        self.stop()
        sys.exit(0)
    
    def start_docker_stack(self):
        """Inicia o stack Docker (Grafana + Prometheus + AlertManager)."""
        logger.info("🐳 Iniciando stack Docker...")
        
        compose_dir = os.path.dirname(self.docker_compose_path)
        
        try:
            # Parar containers existentes
            subprocess.run([
                "docker-compose", "-f", self.docker_compose_path, "down"
            ], cwd=compose_dir, check=False, capture_output=True)
            
            # Iniciar stack
            result = subprocess.run([
                "docker-compose", "-f", self.docker_compose_path, "up", "-d"
            ], cwd=compose_dir, check=True, capture_output=True, text=True)
            
            logger.info("✅ Stack Docker iniciado com sucesso")
            
            # Aguardar serviços ficarem prontos
            self._wait_for_services()
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Erro iniciando stack Docker: {e}")
            logger.error(f"Stdout: {e.stdout}")
            logger.error(f"Stderr: {e.stderr}")
            raise
    
    def _wait_for_services(self):
        """Aguarda serviços ficarem prontos."""
        services = {
            "Prometheus": "http://localhost:9090/-/ready",
            "Grafana": "http://localhost:3000/api/health",
            "AlertManager": "http://localhost:9093/-/ready"
        }
        
        logger.info("⏳ Aguardando serviços ficarem prontos...")
        
        for service_name, health_url in services.items():
            max_attempts = 30
            for attempt in range(max_attempts):
                try:
                    import requests
                    response = requests.get(health_url, timeout=5)
                    if response.status_code == 200:
                        logger.info(f"✅ {service_name} está pronto")
                        break
                except Exception:
                    if attempt == max_attempts - 1:
                        logger.warning(f"⚠️ {service_name} pode não estar pronto")
                    else:
                        time.sleep(2)
    
    def start_prometheus_exporter(self, port: int = 8080):
        """Inicia o exportador Prometheus do QUALIA."""
        logger.info("📊 Iniciando exportador Prometheus QUALIA...")
        
        try:
            self.prometheus_exporter = get_prometheus_exporter(port=port)
            self.prometheus_exporter.start()
            
            logger.info(f"✅ Exportador Prometheus iniciado em http://localhost:{port}/metrics")
            
        except Exception as e:
            logger.error(f"❌ Erro iniciando exportador Prometheus: {e}")
            raise
    
    def start(self, prometheus_port: int = 8080):
        """Inicia o stack completo de monitoramento."""
        logger.info("🚀 Iniciando stack completo de monitoramento QUALIA...")
        
        try:
            # 1. Iniciar stack Docker
            self.start_docker_stack()
            
            # 2. Iniciar exportador Prometheus
            self.start_prometheus_exporter(prometheus_port)
            
            self._running = True
            
            # 3. Mostrar URLs de acesso
            self._show_access_urls(prometheus_port)
            
            logger.info("✅ Stack de monitoramento iniciado com sucesso!")
            
        except Exception as e:
            logger.error(f"❌ Erro iniciando stack: {e}")
            self.stop()
            raise
    
    def _show_access_urls(self, prometheus_port: int):
        """Mostra URLs de acesso aos serviços."""
        urls = {
            "🎨 Grafana Dashboard": "http://localhost:3000 (admin/qualia2024)",
            "📊 Prometheus": "http://localhost:9090",
            "🚨 AlertManager": "http://localhost:9093",
            "📈 QUALIA Metrics": f"http://localhost:{prometheus_port}/metrics",
            "💚 Health Check": f"http://localhost:{prometheus_port}/health"
        }
        
        print("\n" + "="*60)
        print("🚀 QUALIA MONITORING STACK - URLS DE ACESSO")
        print("="*60)
        
        for name, url in urls.items():
            print(f"{name}: {url}")
        
        print("="*60)
        print("💡 Dica: Importe o dashboard 'qualia-main-dashboard.json' no Grafana")
        print("📚 Documentação: docs/monitoring.md")
        print("="*60 + "\n")
    
    def stop(self):
        """Para o stack completo de monitoramento."""
        if not self._running:
            return
        
        logger.info("🛑 Parando stack de monitoramento...")
        
        # Parar exportador Prometheus
        if self.prometheus_exporter:
            self.prometheus_exporter.stop()
        
        # Parar stack Docker
        try:
            compose_dir = os.path.dirname(self.docker_compose_path)
            subprocess.run([
                "docker-compose", "-f", self.docker_compose_path, "down"
            ], cwd=compose_dir, check=False, capture_output=True)
            
            logger.info("✅ Stack Docker parado")
            
        except Exception as e:
            logger.error(f"❌ Erro parando stack Docker: {e}")
        
        self._running = False
        logger.info("✅ Stack de monitoramento parado")
    
    def status(self):
        """Mostra status dos serviços."""
        logger.info("📊 Status dos serviços:")
        
        # Status Docker
        try:
            compose_dir = os.path.dirname(self.docker_compose_path)
            result = subprocess.run([
                "docker-compose", "-f", self.docker_compose_path, "ps"
            ], cwd=compose_dir, capture_output=True, text=True)
            
            print("\n🐳 Docker Services:")
            print(result.stdout)
            
        except Exception as e:
            logger.error(f"❌ Erro verificando status Docker: {e}")
        
        # Status exportador
        if self.prometheus_exporter and self.prometheus_exporter.is_running():
            logger.info("✅ Exportador Prometheus: Rodando")
        else:
            logger.info("❌ Exportador Prometheus: Parado")
    
    def run_forever(self, prometheus_port: int = 8080):
        """Executa o stack e mantém rodando."""
        self.start(prometheus_port)
        
        try:
            logger.info("🔄 Stack rodando... Pressione Ctrl+C para parar")
            while self._running:
                time.sleep(1)
                
                # Atualizar métricas periodicamente
                if self.prometheus_exporter:
                    self.prometheus_exporter.update_metrics()
                
        except KeyboardInterrupt:
            logger.info("📡 Interrupção recebida, parando...")
        finally:
            self.stop()


def main():
    """Função principal."""
    import argparse
    
    parser = argparse.ArgumentParser(description="QUALIA Grafana Stack Manager")
    parser.add_argument("--port", type=int, default=8080, 
                       help="Porta do exportador Prometheus (default: 8080)")
    parser.add_argument("--docker-compose", type=str,
                       help="Caminho para docker-compose.yml")
    parser.add_argument("--action", choices=["start", "stop", "status", "run"], 
                       default="run", help="Ação a executar")
    
    args = parser.parse_args()
    
    try:
        manager = GrafanaStackManager(args.docker_compose)
        
        if args.action == "start":
            manager.start(args.port)
        elif args.action == "stop":
            manager.stop()
        elif args.action == "status":
            manager.status()
        elif args.action == "run":
            manager.run_forever(args.port)
            
    except Exception as e:
        logger.error(f"❌ Erro: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
