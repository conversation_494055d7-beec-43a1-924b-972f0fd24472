#!/usr/bin/env python3
"""
Diagnóstico Final - Por que TODAS as estratégias falham?
YAA IMPLEMENTATION: Compara com buy-and-hold e identifica problema raiz.
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import numpy as np
import requests
import json

def fetch_btc_data():
    """Busca dados do BTC."""
    try:
        session = requests.Session()
        session.headers.update({'User-Agent': 'QUALIA-FinalDiagnosis/1.0'})
        
        end_time = int(datetime.now().timestamp() * 1000)
        start_time = int((datetime.now() - timedelta(days=60)).timestamp() * 1000)
        
        params = {
            'symbol': 'BTCUSDT',
            'interval': '1h',
            'startTime': start_time,
            'endTime': end_time,
            'limit': 1000
        }
        
        response = session.get("https://api.binance.com/api/v3/klines", params=params)
        response.raise_for_status()
        data = response.json()
        
        df = pd.DataFrame(data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])
        
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']].copy()
        
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df.set_index('timestamp', inplace=True)
        df = df.sort_index().dropna()
        
        return df
        
    except Exception as e:
        print(f"❌ Erro ao buscar dados: {e}")
        return pd.DataFrame()

def add_simple_indicators(df):
    """Adiciona indicadores básicos."""
    
    # Retornos
    df['returns'] = df['close'].pct_change().fillna(0)
    
    # Médias móveis simples
    df['sma_20'] = df['close'].rolling(20).mean()
    df['sma_50'] = df['close'].rolling(50).mean()
    
    # RSI simples
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    return df

def analyze_market_performance(df):
    """Analisa performance do mercado (buy-and-hold)."""
    
    if df.empty or len(df) < 50:
        return {'error': 'Dados insuficientes'}
    
    # Buy and Hold
    start_price = df['close'].iloc[30]  # Mesmo ponto de início da estratégia
    end_price = df['close'].iloc[-1]
    buy_hold_return = (end_price / start_price) - 1
    
    # Retornos diários
    daily_returns = df['returns'].iloc[30:]
    
    # Métricas do mercado
    market_volatility = daily_returns.std() * np.sqrt(8760)  # Anualizada
    market_sharpe = (daily_returns.mean() * 8760) / market_volatility if market_volatility > 0 else 0
    
    # Drawdown do mercado
    cumulative_market = (1 + daily_returns).cumprod()
    running_max = cumulative_market.expanding().max()
    market_drawdown = ((cumulative_market - running_max) / running_max).min()
    
    return {
        'buy_hold_return_pct': buy_hold_return * 100,
        'market_sharpe': market_sharpe,
        'market_volatility': market_volatility,
        'market_max_drawdown_pct': abs(market_drawdown) * 100,
        'start_price': start_price,
        'end_price': end_price,
        'periods': len(daily_returns)
    }

def test_signal_correlation(df):
    """Testa correlação dos sinais com retornos futuros."""
    
    if df.empty or len(df) < 100:
        return {'error': 'Dados insuficientes'}
    
    signals = []
    future_returns_1h = []
    future_returns_4h = []
    future_returns_24h = []
    
    for i in range(50, len(df) - 24):  # Deixa espaço para retornos futuros
        current_data = df.iloc[i]
        
        # Gera sinal simples (RSI + SMA)
        rsi = current_data['rsi']
        sma_20 = current_data['sma_20']
        sma_50 = current_data['sma_50']
        current_price = current_data['close']
        
        # Sinal RSI
        if pd.isna(rsi):
            rsi_signal = 0.0
        elif rsi > 70:
            rsi_signal = -1.0
        elif rsi < 30:
            rsi_signal = 1.0
        else:
            rsi_signal = (50 - rsi) / 20
        
        # Sinal Trend
        if pd.isna(sma_20) or pd.isna(sma_50):
            trend_signal = 0.0
        else:
            trend_signal = 1.0 if sma_20 > sma_50 else -1.0
        
        # Sinal combinado
        combined_signal = (rsi_signal + trend_signal) / 2.0
        signals.append(combined_signal)
        
        # Retornos futuros
        future_1h = (df['close'].iloc[i+1] / current_price) - 1
        future_4h = (df['close'].iloc[i+4] / current_price) - 1
        future_24h = (df['close'].iloc[i+24] / current_price) - 1
        
        future_returns_1h.append(future_1h)
        future_returns_4h.append(future_4h)
        future_returns_24h.append(future_24h)
    
    # Calcula correlações
    signals_series = pd.Series(signals)
    
    corr_1h = signals_series.corr(pd.Series(future_returns_1h))
    corr_4h = signals_series.corr(pd.Series(future_returns_4h))
    corr_24h = signals_series.corr(pd.Series(future_returns_24h))
    
    return {
        'signal_stats': {
            'mean': signals_series.mean(),
            'std': signals_series.std(),
            'positive_pct': (signals_series > 0).mean() * 100,
            'negative_pct': (signals_series < 0).mean() * 100,
            'neutral_pct': (signals_series == 0).mean() * 100
        },
        'correlations': {
            '1h': corr_1h,
            '4h': corr_4h,
            '24h': corr_24h
        },
        'future_returns_stats': {
            '1h': {'mean': np.mean(future_returns_1h) * 100, 'std': np.std(future_returns_1h) * 100},
            '4h': {'mean': np.mean(future_returns_4h) * 100, 'std': np.std(future_returns_4h) * 100},
            '24h': {'mean': np.mean(future_returns_24h) * 100, 'std': np.std(future_returns_24h) * 100}
        }
    }

def test_simple_strategies(df):
    """Testa estratégias ultra-simples."""
    
    if df.empty or len(df) < 100:
        return {'error': 'Dados insuficientes'}
    
    strategies = {}
    
    # 1. Buy and Hold
    start_price = df['close'].iloc[30]
    end_price = df['close'].iloc[-1]
    buy_hold_return = (end_price / start_price) - 1
    strategies['buy_hold'] = {
        'return_pct': buy_hold_return * 100,
        'trades': 1,
        'description': 'Compra e segura'
    }
    
    # 2. Trend Following Ultra-Simples (SMA 20 vs 50)
    positions = []
    for i in range(50, len(df)):
        sma_20 = df['sma_20'].iloc[i]
        sma_50 = df['sma_50'].iloc[i]
        
        if pd.isna(sma_20) or pd.isna(sma_50):
            pos = 0
        elif sma_20 > sma_50:
            pos = 1.0  # Long
        else:
            pos = 0.0  # Neutro (não short)
        
        positions.append(pos)
    
    # Calcula retornos
    returns = []
    for i in range(1, len(positions)):
        if i < len(df) - 50:
            market_ret = (df['close'].iloc[50+i] / df['close'].iloc[50+i-1]) - 1
            strategy_ret = positions[i-1] * market_ret
            returns.append(strategy_ret)
    
    if returns:
        total_return = np.prod([1 + r for r in returns]) - 1
        trades = sum(1 for i in range(1, len(positions)) if positions[i] != positions[i-1])
        strategies['trend_simple'] = {
            'return_pct': total_return * 100,
            'trades': trades,
            'description': 'Trend following simples (SMA 20>50)'
        }
    
    # 3. RSI Mean Reversion Ultra-Conservador
    positions = []
    for i in range(50, len(df)):
        rsi = df['rsi'].iloc[i]
        
        if pd.isna(rsi):
            pos = 0
        elif rsi > 80:  # Muito overbought
            pos = -0.5  # Short pequeno
        elif rsi < 20:  # Muito oversold
            pos = 0.5   # Long pequeno
        else:
            pos = 0
        
        positions.append(pos)
    
    # Calcula retornos
    returns = []
    for i in range(1, len(positions)):
        if i < len(df) - 50:
            market_ret = (df['close'].iloc[50+i] / df['close'].iloc[50+i-1]) - 1
            strategy_ret = positions[i-1] * market_ret
            returns.append(strategy_ret)
    
    if returns:
        total_return = np.prod([1 + r for r in returns]) - 1
        trades = sum(1 for i in range(1, len(positions)) if positions[i] != positions[i-1])
        strategies['rsi_conservative'] = {
            'return_pct': total_return * 100,
            'trades': trades,
            'description': 'RSI conservador (80/20)'
        }
    
    return strategies

def main():
    """Executa diagnóstico final completo."""
    print("🔬 DIAGNÓSTICO FINAL - POR QUE TODAS AS ESTRATÉGIAS FALHAM?")
    print("=" * 70)
    
    # Busca dados
    print(f"📈 Buscando dados BTC/USDT...")
    df = fetch_btc_data()
    
    if df.empty:
        print("❌ Falha ao obter dados")
        return
    
    print(f"✅ Dados obtidos: {len(df)} candles")
    print(f"📅 Período: {df.index[0]} a {df.index[-1]}")
    
    # Adiciona indicadores
    df = add_simple_indicators(df)
    
    # 1. Análise do mercado
    print(f"\n🏪 ANÁLISE DO MERCADO (BUY-AND-HOLD):")
    market_analysis = analyze_market_performance(df)
    
    if 'error' not in market_analysis:
        print(f"   💰 Buy & Hold Return: {market_analysis['buy_hold_return_pct']:.2f}%")
        print(f"   📊 Market Sharpe: {market_analysis['market_sharpe']:.3f}")
        print(f"   📉 Max Drawdown: {market_analysis['market_max_drawdown_pct']:.2f}%")
        print(f"   💲 Preço inicial: ${market_analysis['start_price']:.2f}")
        print(f"   💲 Preço final: ${market_analysis['end_price']:.2f}")
    
    # 2. Análise de correlação dos sinais
    print(f"\n🎯 ANÁLISE DE CORRELAÇÃO DOS SINAIS:")
    signal_analysis = test_signal_correlation(df)
    
    if 'error' not in signal_analysis:
        print(f"   📊 Estatísticas dos Sinais:")
        print(f"      • Média: {signal_analysis['signal_stats']['mean']:.3f}")
        print(f"      • Positivos: {signal_analysis['signal_stats']['positive_pct']:.1f}%")
        print(f"      • Negativos: {signal_analysis['signal_stats']['negative_pct']:.1f}%")
        print(f"      • Neutros: {signal_analysis['signal_stats']['neutral_pct']:.1f}%")
        
        print(f"   🔗 Correlações com Retornos Futuros:")
        print(f"      • 1h: {signal_analysis['correlations']['1h']:.4f}")
        print(f"      • 4h: {signal_analysis['correlations']['4h']:.4f}")
        print(f"      • 24h: {signal_analysis['correlations']['24h']:.4f}")
        
        # Diagnóstico das correlações
        corr_1h = signal_analysis['correlations']['1h']
        if corr_1h < -0.1:
            print(f"   ❌ PROBLEMA: Correlação negativa! Sinais estão INVERTIDOS!")
        elif corr_1h > 0.1:
            print(f"   ✅ Correlação positiva - sinais corretos")
        else:
            print(f"   ⚠️ Correlação próxima de zero - sinais aleatórios")
    
    # 3. Teste de estratégias simples
    print(f"\n🧪 TESTE DE ESTRATÉGIAS ULTRA-SIMPLES:")
    simple_strategies = test_simple_strategies(df)
    
    if 'error' not in simple_strategies:
        for name, data in simple_strategies.items():
            print(f"   • {data['description']}: {data['return_pct']:.2f}% ({data['trades']} trades)")
    
    # 4. Diagnóstico final
    print(f"\n" + "="*70)
    print(f"🔍 DIAGNÓSTICO FINAL")
    print(f"="*70)
    
    if 'error' not in market_analysis:
        market_return = market_analysis['buy_hold_return_pct']
        
        if market_return < -5:
            print(f"❌ PROBLEMA IDENTIFICADO: Mercado em queda ({market_return:.2f}%)")
            print(f"💡 SOLUÇÃO: Estratégias long-only não funcionam em bear market")
            print(f"🎯 RECOMENDAÇÃO: Implementar proteção contra quedas ou focar em short")
        
        elif market_return > 5:
            print(f"✅ Mercado em alta ({market_return:.2f}%)")
            print(f"❌ PROBLEMA: Nossas estratégias estão perdendo em mercado em alta!")
            print(f"💡 CAUSA PROVÁVEL: Overtrading ou sinais invertidos")
        
        else:
            print(f"📊 Mercado lateral ({market_return:.2f}%)")
            print(f"💡 ESTRATÉGIAS: Mean reversion pode funcionar melhor")
    
    if 'error' not in signal_analysis:
        corr_1h = signal_analysis['correlations']['1h']
        
        if corr_1h < -0.1:
            print(f"\n🚨 SOLUÇÃO ENCONTRADA: INVERTER TODOS OS SINAIS!")
            print(f"   • Correlação negativa ({corr_1h:.4f}) indica sinais invertidos")
            print(f"   • Multiplicar todos os sinais por -1")
        
        elif abs(corr_1h) < 0.05:
            print(f"\n⚠️ PROBLEMA: Sinais aleatórios ({corr_1h:.4f})")
            print(f"   • Indicadores não têm poder preditivo")
            print(f"   • Usar estratégia mais simples (buy-and-hold)")
    
    # Salva diagnóstico
    output_dir = Path("results/final_diagnosis")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    diagnosis = {
        'market_analysis': market_analysis,
        'signal_analysis': signal_analysis,
        'simple_strategies': simple_strategies,
        'timestamp': datetime.now().isoformat()
    }
    
    with open(output_dir / "final_diagnosis.json", 'w') as f:
        json.dump(diagnosis, f, indent=2, default=str)
    
    print(f"\n💾 Diagnóstico salvo em: {output_dir / 'final_diagnosis.json'}")
    
    return diagnosis

if __name__ == "__main__":
    main()
