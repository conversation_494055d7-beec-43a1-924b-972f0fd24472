# SimulationQASTCore Benchmark

Este benchmark registra o tempo e uso de memória de um ciclo de simulação com duas personas.

Parâmetros de teste:
- `simulation_cycles`: 3
- `simulation_steps_per_cycle`: 1
- `future_scenario_clusters`: 2

Após a execução de `pytest benchmarks/test_simulation_qast_core_metrics.py`, os resultados são gravados em `benchmarks/results/simulation_core_metrics.json`.

Limites aceitáveis observados:
- `simulation.cycle_duration_ms` deve ficar abaixo de **200 ms**.
- `simulation.cycle_memory_mb` não deve exceder **150 MB**.

Ajuste `simulation_cycles` ou `simulation_steps_per_cycle` para manter o consumo dentro desses valores em ambientes restritos.
