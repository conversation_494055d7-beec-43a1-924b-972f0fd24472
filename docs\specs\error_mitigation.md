# Error Mitigation

O módulo `qualia.core.error_mitigation` oferece três rotinas simples
para reduzir o impacto de ruído em circuitos quânticos executados nos
testes do QUALIA.

Principais características
-------------------------
- **Extrapolação de Ruído Zero (ZNE)** via `apply_zne`.
- **Mitigação Probabilística (PEM)** via `apply_pem`.
- **Mitigação com Aprendizado de Máquina** via `apply_ml_mitigation`.

## Exemplo de Uso
```python
from qiskit_aer import AerSimulator
from qiskit import QuantumCircuit
from qualia.core.error_mitigation import apply_zne

backend = AerSimulator()
qc = QuantumCircuit(1, 1)
qc.h(0)
qc.measure(0, 0)
counts = apply_zne(qc, backend)
print(counts)
```
