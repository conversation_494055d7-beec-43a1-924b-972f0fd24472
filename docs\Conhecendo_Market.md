# Conhecendo o Módulo de Mercado do QUALIA

O diretório `market` implementa a integração com exchanges, coleta de dados e execução das ordens geradas pelo sistema. É o ponto de contato do QUALIA com o ambiente real de negociação.

## Principais serviços

- **TradingEngine** – gerencia execução de ordens e atualiza posições.
- **SelfEvolvingTradingSystem** – executa estratégias com feedback do QAST e aplica otimizações pendentes.
- **DynamicRiskController** – ajusta tamanhos de posição conforme métricas de risco.
- **KucoinIntegration/KrakenIntegration** – wrappers para APIs dessas exchanges via `ccxt`.
- **SignalArbiter** – organiza os sinais produzidos pelas estratégias e decide a ação final.
- **PaperBroker** – simula negociações para testes offline.

## Pontos de Atenção

Os módulos de mercado dependem de credenciais definidas em variáveis de ambiente e utilizam a biblioteca `ccxt`. Para ambientes de teste pode-se usar o `PaperBroker` com dados históricos em `data/`.

O `DynamicRiskController` passa a considerar volatilidade calma quando o
indicador estiver abaixo de `0.08` e volátil acima de `0.25`. Essa nova
faixa de atuação tende a tornar os ajustes de stop-loss e take-profit mais
sensíveis a movimentos abruptos. É recomendável comparar os resultados desse
mecanismo com um stop-loss estático para validar se a mudança traz
benefícios consistentes ao seu perfil de operação.

## Ajustes de Latência do Ticker

Os parâmetros `ticker_timeout` e `ticker_retries` controlam quanto tempo o
sistema espera por respostas das exchanges e quantas tentativas serão feitas em
caso de falhas. Para conexões instáveis recomenda-se definir `TICKER_TIMEOUT`
entre `20` e `30` segundos e `TICKER_RETRY_ATTEMPTS` igual ou superior a `3`.
Em ambientes locais ou de backtest, valores menores (por exemplo `5` segundos e
apenas uma tentativa) reduzem o tempo total de execução.

## Próximos Passos

- **Novas integrações** com outras exchanges e provedores de preço.
- **Ferramentas de monitoramento** que exponham métricas em dashboards.
- **Testes de carga** para validar a robustez do motor em alta frequência.

## Métricas StatsD do DynamicRiskController

O `DynamicRiskController` emite métricas via StatsD para acompanhar sua
atuação em tempo real. As principais métricas são:

- `dynamic_risk.volatility_threshold_breach` – incrementada sempre que a
  volatilidade ultrapassa os limites configurados.
- `dynamic_risk.recalibration` – marca os momentos de recalibração dos
  parâmetros de risco.

Esses dados alimentam um painel no Grafana. Os limites de volatilidade
são mostrados em gráfico de linha, destacando em vermelho valores
acima de `0.25` e em verde quando estão abaixo de `0.08`. Cada evento de
`recalibration` surge como anotação vertical, facilitando a correlação
entre ajustes de risco e oscilações de mercado.

## Eventos do DynamicRiskController

Quando o controlador finaliza uma calibração ele publica o evento
`risk.recalibrated` pelo `SimpleEventBus`, caso este tenha sido fornecido.
O payload contém todos os campos de `RiskCalibrationResult` para que
outros módulos possam reagir aos novos níveis calculados.

### Obtendo histórico de OHLCV

Use `CryptoDataFetcher.fetch_historical_data` para baixar candles em intervalos longos. O método divide o período em lotes de até 30 dias e faz múltiplas chamadas sequenciais à exchange, unindo todos os resultados em um único `DataFrame`.

```python
from datetime import datetime
from src.qualia.market.base_integration import CryptoDataFetcher

fetcher = CryptoDataFetcher(api_key="...", api_secret="...")
start = datetime(2021, 1, 1)
end = datetime(2021, 1, 10)

ohlcv = await fetcher.fetch_historical_data("BTC/USD", "1h", start, end)
```

Esse processo trata automaticamente a paginação avançando o parâmetro `since` a cada requisição.

### Gerenciamento de Timeouts

O ``CryptoDataFetcher`` monitora falhas consecutivas em ``fetch_ohlcv``. Quando dois
ou mais ``TimeoutError`` ocorrem em sequência, o ``ohlcv_timeout`` é ampliado
gradualmente (até três vezes o valor inicial) para acomodar períodos de latência
elevada. Assim que uma chamada obtém sucesso, o timeout volta ao valor original.

### Circuit Breakers por Símbolo

Cada combinação de símbolo e timeframe possui seu próprio ``CircuitBreaker``.
Use os parâmetros ``symbol_fail_threshold`` e ``symbol_recovery_timeout`` ao
instanciar ``CryptoDataFetcher`` para ajustar a tolerância a falhas. Os valores
padrão são ``3`` falhas antes de abrir e ``600`` segundos de espera para
recuperação.

## Configuração Centralizada (``market.yaml``)

Parâmetros de rate limit, timeouts e toggles do módulo de mercado agora ficam
reunidos em ``config/market.yaml``. O loader ``load_market_defaults`` presente em
``src.qualia.config`` lê esse arquivo durante a inicialização do
``CryptoDataFetcher`` e das integrações. Variáveis de ambiente continuam
sobrescrevendo os valores do YAML para compatibilidade com scripts existentes.

Exemplo simplificado de estrutura:

```yaml
rate_limits:
  global: 4.0
  max_concurrent_requests: 3
timeouts:
  connection: 30.0
  ticker: 30.0
  ohlcv: 90.0
feature_toggles:
  market_module: false
```

## Política de Logs

Mensagens relacionadas à paginação (`Paginacao OHLCV`) e à contagem de candles
agora são emitidas em nível `DEBUG`. Dessa forma, o log padrão se mantém mais
enxuto e foca em avisos realmente importantes. Para visualizá-las, execute o
QUALIA com `--log_level DEBUG` ou defina `QUALIA_MODULE_LEVELS` adequadamente.

O logger principal utiliza `RotatingFileHandler` com limite de 10 MB e cinco
arquivos de backup, mantendo os registros em `data/logs/qualia.log`. Isso evita
que os arquivos cresçam indefinidamente e facilita a rotação automática.

## Instrumentação com OpenTelemetry

Os métodos `fetch_ticker`, `fetch_ohlcv`, `calibrate_risk_levels` e `trade_decision` incluem spans
de tracing quando o módulo `opentelemetry` está instalado. Para habilitar,
execute `configure_tracing` durante a inicialização, indicando o exportador desejado
ou defina a variável `QUALIA_TRACING_EXPORTER`:

```python
from src.qualia.utils.tracing import configure_tracing

configure_tracing(service_name="qualia-market", exporter="otlp")
```

O arquivo `config/logging.yaml` possui um exemplo de seção `tracing` que define
o `service_name` e o tipo de exportador utilizado. O exportador pode ser
``"console"`` ou ``"otlp"``.
\n## Eventos de Padr\u00e3o de Mercado
O `MarketTemporalPatternDetector` publica o evento `market.pattern_detected` sempre que identifica um novo padrão. O payload contém o vetor analisado e os metadados do padrão.

Para persistir automaticamente essas informações, conecte o `SimpleEventBus` ao `MemoryService`:

```python
from src.qualia.memory.service import MemoryService
from src.qualia.market.temporal_pattern_detector import MarketTemporalPatternDetector
from src.qualia.memory.event_bus import SimpleEventBus
from src.qualia.config.temporal_detector_config import TemporalPatternConfig

bus = SimpleEventBus()
memory = MemoryService(event_bus=bus)
detector = MarketTemporalPatternDetector(
    config=TemporalPatternConfig(use_quantum_transform=False),
    event_bus=bus,
)

detector.detect_patterns(decision_history)
```


## Parâmetros das Novas Exchanges

As integrações com **Binance** e **Coinbase** utilizam as mesmas variáveis de ambiente do padrão do `CryptoDataFetcher`. Defina as credenciais antes de iniciar o sistema:

- `BINANCE_API_KEY` e `BINANCE_API_SECRET`
- `COINBASE_API_KEY` e `COINBASE_API_SECRET`

Os valores de timeout e tentativas seguem `config/market.yaml`, podendo ser sobrescritos por:

- `TICKER_TIMEOUT`
- `TICKER_RETRY_ATTEMPTS`
- `EXCHANGE_CONN_TIMEOUT`
- `EXCHANGE_CONN_RETRIES`

Quando `market_metrics_enabled` estiver habilitado, latências e erros dessas integrações serão enviados ao StatsD configurado e poderão ser visualizados no Grafana.
