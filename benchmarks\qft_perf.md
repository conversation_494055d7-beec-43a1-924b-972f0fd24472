# Benchmark de Performance: Criação do Estado Inicial QFT

Data da Execução: 2025-07-01

## Configuração do Ambiente de Benchmark

- **Máquina**: (Seria ideal adicionar detalhes da CPU aqui, mas `pytest-benchmark` os inclui na sua própria saída, então a tabela abaixo é a referência principal)
- **Python**: 3.11.1
- **pytest**: 8.3.5
- **pytest-benchmark**: 5.1.0
- **QUALIA Version**: (branch `feature/qft-fixes` ou commit hash relevante)

## Teste Realizado

O benchmark mediu o tempo necessário para instanciar `QUALIAQuantumUniverse` com `initial_state_type="qft"` para diferentes números de qubits (`n_qubits`).
Os parâmetros adicionais de `QUALIAQuantumUniverse` foram mantidos constantes com valores padrão:
- `scr_depth`: 1
- `base_lambda`: 0.5
- `alpha`: 0.1
- `retro_strength`: 0.0
- `num_ctc_qubits`: 0
- `allow_qft_fallback`: False

## Resultados

A tabela abaixo mostra os resultados detalhados da execução do `pytest-benchmark`:

```
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
Name (time in ms)                                          Min                   Max                  Mean              StdDev                Median                 IQR            Outliers       OPS            Rounds  Iterations
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
test_qft_initial_state_creation_performance[8]          6.2407 (1.0)          8.1278 (1.0)          7.0145 (1.0)        0.4201 (1.0)          6.9773 (1.0)        0.6093 (1.0)           3;0  142.5610 (1.0)        20           1
test_qft_initial_state_creation_performance[10]         9.2475 (1.48)        23.2416 (2.86)        12.7421 (1.82)       3.4961 (8.32)        10.7200 (1.54)       5.8566 (9.61)         20;0   78.4799 (0.55)        98           1
test_qft_initial_state_creation_performance[12]        18.6800 (2.99)        24.4454 (3.01)        20.7925 (2.96)       1.3524 (3.22)        20.6078 (2.95)       1.9691 (3.23)         13;0   48.0943 (0.34)        42           1
test_qft_initial_state_creation_performance[14]        40.8247 (6.54)        51.9169 (6.39)        46.7730 (6.67)       2.8558 (6.80)        47.6826 (6.83)       2.4238 (3.98)          5;4   21.3798 (0.15)        19           1
test_qft_initial_state_creation_performance[16]         1.2200 (0.20)         1.5345 (0.19)         1.3110 (0.19)       0.0983 (0.23)         1.3024 (0.19)       0.0721 (0.12)        15;0  762.4538 (7.52)       50           1
test_qft_initial_state_creation_performance[18]         1.2457 (0.20)         1.6682 (0.21)         1.3648 (0.19)       0.1312 (0.31)         1.3477 (0.19)       0.0894 (0.15)        14;0  732.7983 (7.23)       50           1
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
```

**Legenda:**
- **Min, Max, Mean, StdDev, Median, IQR**: Métricas estatísticas padrão para o tempo de execução em milissegundos.
- **Outliers**: Número de execuções consideradas outliers.
- **OPS**: Operações Por Segundo (calculado como 1 / Mean).
- **Rounds**: Número de loops de benchmark.
- **Iterations**: Número de vezes que a função foi chamada dentro de cada round.

## Análise Preliminar

Com a lógica de fallback habilitada, apenas as contagens até o limite configurado apresentam custo mais elevado.
- Para **8 a 14 qubits**, o comportamento permanece semelhante ao benchmark anterior, com tempos abaixo de 50 ms.
- Para **16 qubits** ou mais, o circuito QFT é substituído por um circuito vazio e o estado aleatório é recuperado do cache, resultando em tempos ao redor de **1.3 ms**.

Esses dados são cruciais para entender os limites práticos da simulação de universos quânticos maiores diretamente via construção de estado QFT completo e podem orientar otimizações futuras ou a exploração de métodos de inicialização alternativos para sistemas muito grandes. 