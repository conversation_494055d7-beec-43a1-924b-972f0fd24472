#!/usr/bin/env python3
"""
Simple staging test to verify components
"""

import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test basic imports"""
    print("Testing imports...")
    
    try:
        import yaml
        print("✓ yaml imported successfully")
    except Exception as e:
        print(f"✗ yaml import failed: {e}")
        return False
    
    try:
        import psutil
        print("✓ psutil imported successfully")
    except Exception as e:
        print(f"✗ psutil import failed: {e}")
        return False
    
    try:
        from src.qualia.validation.production_validator import ProductionValidator
        print("✓ ProductionValidator imported successfully")
    except Exception as e:
        print(f"✗ ProductionValidator import failed: {e}")
        return False
    
    return True

def test_config():
    """Test config loading"""
    print("\nTesting config loading...")
    
    try:
        import yaml
        with open("config/staging_config.yaml", 'r') as f:
            config = yaml.safe_load(f)
        print("✓ Staging config loaded successfully")
        print(f"  Environment: {config.get('environment', 'unknown')}")
        return True
    except Exception as e:
        print(f"✗ Config loading failed: {e}")
        return False

def test_validation():
    """Test validation system"""
    print("\nTesting validation system...")
    
    try:
        import yaml
        from src.qualia.validation.production_validator import ProductionValidator
        
        with open("config/staging_config.yaml", 'r') as f:
            config = yaml.safe_load(f)
        
        validator = ProductionValidator(config)
        print("✓ Validator created successfully")
        return True
    except Exception as e:
        print(f"✗ Validation test failed: {e}")
        return False

def main():
    """Main test execution"""
    print("="*60)
    print("QUALIA STAGING COMPONENT TEST")
    print("="*60)
    
    tests = [
        ("Import Test", test_imports),
        ("Config Test", test_config),
        ("Validation Test", test_validation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{test_name}]")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} FAILED with exception: {e}")
    
    print("\n" + "="*60)
    print(f"RESULTS: {passed}/{total} tests passed")
    print("="*60)
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
