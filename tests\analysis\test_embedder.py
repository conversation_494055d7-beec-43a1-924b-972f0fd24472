import numpy as np
import pytest

from qualia.farsight.embedder import Embedder


@pytest.mark.unit
def test_encode_uses_bow_fallback(monkeypatch):
    monkeypatch.setattr("qualia.farsight.embedder._SBERT_AVAILABLE", False)
    monkeypatch.setattr("qualia.farsight.embedder.SentenceTransformer", None)

    sentinel = np.array([[1.0]], dtype="float32")

    def fake_bow(self, documents):
        return sentinel

    monkeypatch.setattr(Embedder, "_bow_fallback", fake_bow)

    embedder = Embedder()
    result = embedder.encode(["text"])

    assert result is sentinel
