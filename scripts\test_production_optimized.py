#!/usr/bin/env python3
# Teste do sistema com configurações otimizadas para produção

import asyncio
import sys
import os
from datetime import datetime

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Aplicar configurações de produção via variáveis de ambiente
os.environ.update({
    'RATE_LIMIT': '2.0',
    'TICKER_TIMEOUT': '20.0',
    'OHLCV_TIMEOUT': '60.0',
    'TICKER_CACHE_TTL': '10.0',
    'OHLCV_CACHE_TTL': '120.0',
    'TICKER_RETRIES': '2',
    'OHLCV_RETRIES': '2',
    'API_FAIL_THRESHOLD': '3',
    'API_RECOVERY_TIMEOUT': '30.0',
    'MAX_CONCURRENT_REQUESTS': '3',
})


async def test_production_optimized():
    print("🚀 TESTE DE PRODUÇÃO OTIMIZADO")
    print("=" * 60)
    
    try:
        from qualia.market.kucoin_integration import KucoinIntegration
        print("✅ KucoinIntegration importado")
        
        # Criar integração com configurações otimizadas
        kucoin = KucoinIntegration(
            api_key=None,
            api_secret=None,
            password=None,
            use_websocket=False,
            ticker_timeout=20.0,
            ohlcv_timeout=60.0,
            ticker_retries=2,
            ticker_backoff_base=2.0,
            fail_threshold=3,
            recovery_timeout=30.0,
        )
        print("✅ Integração criada com configurações otimizadas")
        
        # Inicializar conexão
        await kucoin.initialize_connection()
        print("✅ Conexão inicializada")
        
        # Teste conservador: uma requisição por vez com intervalo
        print("\n📊 TESTE CONSERVADOR")
        success_count = 0
        
        for i in range(3):
            try:
                print(f"🔄 Requisição {i+1}/3...")
                ticker = await kucoin.fetch_ticker("BTC/USDT")
                
                if ticker and 'last' in ticker:
                    success_count += 1
                    print(f"✅ Sucesso - ${ticker['last']:,.2f}")
                else:
                    print("❌ Ticker vazio")
                
                if i < 2:
                    await asyncio.sleep(5.0)
                
            except Exception as e:
                print(f"❌ Erro: {e}")
        
        # Fechar conexão
        await kucoin.close()
        print("✅ Conexão fechada")
        
        # Resultado final
        print(f"\n🏁 Sucessos: {success_count}/3")
        
        if success_count >= 2:
            print("🎉 SISTEMA OTIMIZADO FUNCIONANDO!")
            return True
        else:
            print("⚠️ Sistema ainda precisa de ajustes")
            return False
        
    except Exception as e:
        print(f"❌ Erro geral: {e}")
        return False


if __name__ == "__main__":
    result = asyncio.run(test_production_optimized())
    
    if result:
        print("🚀 PRONTO PARA PRODUÇÃO!")
    else:
        print("🔧 PRECISA DE MAIS AJUSTES!")
