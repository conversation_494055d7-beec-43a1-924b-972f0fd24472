#!/usr/bin/env python3
"""
Test QUALIA Imports - Simple import test
"""

import sys
import os

# Add src to path for QUALIA imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

print("🧪 Testing QUALIA imports...")

try:
    print("📦 Testing basic imports...")
    import asyncio
    import logging
    print("✅ Basic imports successful")
    
    print("📦 Testing QUALIA component imports...")
    
    try:
        from qualia.consciousness.enhanced_data_collector import EnhancedDataCollector
        print("✅ EnhancedDataCollector import successful")
    except ImportError as e:
        print(f"❌ EnhancedDataCollector import failed: {e}")
    
    try:
        from qualia.core.qast_oracle_decision_engine import QASTOracleDecisionEngine
        print("✅ QASTOracleDecisionEngine import successful")
    except ImportError as e:
        print(f"❌ QASTOracleDecisionEngine import failed: {e}")
    
    try:
        from qualia.strategies.strategy_factory import StrategyFactory
        print("✅ StrategyFactory import successful")
    except ImportError as e:
        print(f"❌ StrategyFactory import failed: {e}")
    
    try:
        from qualia.signals.generator import SignalGenerator
        print("✅ SignalGenerator import successful")
    except ImportError as e:
        print(f"❌ SignalGenerator import failed: {e}")
    
    try:
        from qualia.market.kucoin_integration import KucoinIntegration
        print("✅ KucoinIntegration import successful")
    except ImportError as e:
        print(f"❌ KucoinIntegration import failed: {e}")
    
    print("🎉 Import test completed!")
    
except Exception as e:
    print(f"💥 Import test failed: {e}")
    sys.exit(1)
