const THREE = require('three');
const { performance } = require('perf_hooks');

class Octree {
  constructor(boundBox, depth = 0, maxDepth = 8, maxObjects = 8) {
    this.boundBox = boundBox.clone();
    this.depth = depth;
    this.maxDepth = maxDepth;
    this.maxObjects = maxObjects;
    this.objects = [];
    this.children = [];
  }
  insert(object) {
    if (this.children.length > 0) {
      const idx = this._getIndex(object);
      if (idx !== -1) {
        this.children[idx].insert(object);
        return;
      }
    }
    this.objects.push(object);
    if (this.objects.length > this.maxObjects && this.depth < this.maxDepth) {
      if (this.children.length === 0) {
        this._split();
      }
      let i = 0;
      while (i < this.objects.length) {
        const obj = this.objects[i];
        const idx = this._getIndex(obj);
        if (idx !== -1) {
          this.children[idx].insert(obj);
          this.objects.splice(i, 1);
        } else {
          i += 1;
        }
      }
    }
  }
  retrieve(frustum, result = []) {
    if (!frustum.intersectsBox(this.boundBox)) {
      return result;
    }
    for (const obj of this.objects) {
      if (frustum.intersectsObject(obj)) {
        result.push(obj);
      }
    }
    for (const child of this.children) {
      child.retrieve(frustum, result);
    }
    return result;
  }
  _split() {
    const { min, max } = this.boundBox;
    const size = new THREE.Vector3().subVectors(max, min).multiplyScalar(0.5);
    const offsets = [
      [0, 0, 0],
      [1, 0, 0],
      [0, 1, 0],
      [1, 1, 0],
      [0, 0, 1],
      [1, 0, 1],
      [0, 1, 1],
      [1, 1, 1],
    ];
    for (let i = 0; i < 8; i++) {
      const off = new THREE.Vector3(
        offsets[i][0] * size.x,
        offsets[i][1] * size.y,
        offsets[i][2] * size.z,
      );
      const boxMin = min.clone().add(off);
      const boxMax = boxMin.clone().add(size);
      const childBox = new THREE.Box3(boxMin, boxMax);
      this.children[i] = new Octree(childBox, this.depth + 1, this.maxDepth, this.maxObjects);
    }
  }
  _getIndex(object) {
    const objectBox = new THREE.Box3().setFromObject(object);
    for (let i = 0; i < this.children.length; i++) {
      if (this.children[i].boundBox.containsBox(objectBox)) {
        return i;
      }
    }
    return -1;
  }
}

function createMesh() {
  const geometry = new THREE.BoxGeometry(1, 1, 1);
  const material = new THREE.MeshBasicMaterial();
  const mesh = new THREE.Mesh(geometry, material);
  mesh.position.set(
    (Math.random() - 0.5) * 20,
    (Math.random() - 0.5) * 20,
    (Math.random() - 0.5) * 20,
  );
  mesh.updateMatrixWorld();
  return mesh;
}

const objects = Array.from({ length: 1000 }, () => createMesh());
const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
camera.position.set(0, 0, 5);
camera.updateMatrixWorld();
camera.updateProjectionMatrix();

const frustum = new THREE.Frustum();
const projMatrix = new THREE.Matrix4();

function simpleCulling() {
  projMatrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
  frustum.setFromProjectionMatrix(projMatrix);
  const visible = [];
  for (const obj of objects) {
    if (frustum.intersectsObject(obj)) {
      visible.push(obj);
    }
  }
  return visible.length;
}

const rootBox = new THREE.Box3();
for (const obj of objects) {
  rootBox.expandByObject(obj);
}
const octree = new Octree(rootBox);
for (const obj of objects) {
  octree.insert(obj);
}

function octreeCulling() {
  projMatrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
  frustum.setFromProjectionMatrix(projMatrix);
  return octree.retrieve(frustum, []).length;
}

function benchmark(fn) {
  const iterations = 100;
  const start = performance.now();
  for (let i = 0; i < iterations; i++) {
    fn();
  }
  const end = performance.now();
  return (end - start) / iterations;
}

const simpleTime = benchmark(simpleCulling);
const octreeTime = benchmark(octreeCulling);

console.log(`Simple culling avg: ${simpleTime.toFixed(3)} ms`);
console.log(`Octree culling avg: ${octreeTime.toFixed(3)} ms`);
