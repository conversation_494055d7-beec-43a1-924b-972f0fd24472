import argparse
import logging
import pytest

import src.backtest.backtest_harness as backtest_harness


@pytest.mark.asyncio
async def test_run_backtest_requires_strategy_config(monkeypatch, caplog):
    monkeypatch.setenv(
        "QUALIA_SECRET_KEY",
        "MDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDA=",
    )

    def fail_if_called(*_, **__):
        raise AssertionError("load_strategy_parameters should not be called")

    monkeypatch.setattr(backtest_harness, "load_strategy_parameters", fail_if_called)

    args = argparse.Namespace(
        risk_profile="conservative",
        risk_per_trade_pct=1.0,
        stop_loss_pct=0.02,
        take_profit_pct=0.04,
        initial_capital=10000.0,
        strategy_config_json=None,
        max_position_capital_pct=None,
        output_report_path=None,
        historical_data_path="dummy.csv",
    )

    caplog.set_level(logging.ERROR)
    await backtest_harness.run_backtest(args)
    assert "strategy_config_json" in caplog.text
