#!/usr/bin/env python3
"""
QUALIA D-07.1 Validation Script

Validação rápida dos componentes implementados.
"""

import sys
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def main():
    print("🚀 Validando D-07.1 A/B Testing Framework...")
    
    tests_passed = 0
    total_tests = 0
    
    # Teste 1: Import ABTestConfig
    total_tests += 1
    try:
        from qualia.ab_testing import ABTestConfig
        config = ABTestConfig(
            test_name="Validation Test",
            duration_hours=1.0,
            symbols=["BTC/USDT"],
            initial_capital=10000.0
        )
        assert config.test_id is not None
        print("✅ ABTestConfig: OK")
        tests_passed += 1
    except Exception as e:
        print(f"❌ ABTestConfig: {e}")
    
    # Teste 2: Import ABTestFramework
    total_tests += 1
    try:
        from qualia.ab_testing import ABTestFramework
        framework = ABTestFramework(config)
        assert framework.status.value == "pending"
        print("✅ ABTestFramework: OK")
        tests_passed += 1
    except Exception as e:
        print(f"❌ ABTestFramework: {e}")
    
    # Teste 3: Import PerformanceComparator
    total_tests += 1
    try:
        from qualia.ab_testing import PerformanceComparator
        comparator = PerformanceComparator()
        metrics = comparator.initialize_session("test", "simulator")
        assert metrics.session_type == "simulator"
        print("✅ PerformanceComparator: OK")
        tests_passed += 1
    except Exception as e:
        print(f"❌ PerformanceComparator: {e}")
    
    # Teste 4: Import DataQualityValidator
    total_tests += 1
    try:
        from qualia.ab_testing import DataQualityValidator
        validator = DataQualityValidator()
        metrics = validator.initialize_validation("test")
        assert metrics.session_id == "test"
        print("✅ DataQualityValidator: OK")
        tests_passed += 1
    except Exception as e:
        print(f"❌ DataQualityValidator: {e}")
    
    # Teste 5: Import StatisticalAnalyzer
    total_tests += 1
    try:
        from qualia.ab_testing import StatisticalAnalyzer
        analyzer = StatisticalAnalyzer()
        ci = analyzer.calculate_confidence_interval([1, 2, 3, 4, 5])
        assert len(ci) == 2
        print("✅ StatisticalAnalyzer: OK")
        tests_passed += 1
    except Exception as e:
        print(f"❌ StatisticalAnalyzer: {e}")
    
    # Teste 6: Import TestConfigManager
    total_tests += 1
    try:
        from qualia.ab_testing import TestConfigManager
        manager = TestConfigManager()
        print("✅ TestConfigManager: OK")
        tests_passed += 1
    except Exception as e:
        print(f"❌ TestConfigManager: {e}")
    
    # Teste 7: Estruturas de dados
    total_tests += 1
    try:
        from qualia.ab_testing.data_quality_validator import PriceFeedComparison, ExecutionComparison
        from qualia.ab_testing.performance_comparator import TradeResult
        from datetime import datetime
        
        # PriceFeedComparison
        price_comp = PriceFeedComparison(
            symbol="BTC/USDT",
            timestamp=datetime.now(),
            simulator_price=50000.0,
            live_price=50005.0,
            price_difference=5.0,
            price_difference_pct=0.01,
            simulator_timestamp=datetime.now(),
            live_timestamp=datetime.now(),
            timestamp_lag_ms=100.0
        )
        
        # ExecutionComparison
        exec_comp = ExecutionComparison(
            order_id="test",
            symbol="BTC/USDT",
            side="buy",
            quantity=0.1,
            expected_price=50000.0,
            order_timestamp=datetime.now()
        )
        
        # TradeResult
        trade = TradeResult(
            timestamp=datetime.now(),
            symbol="BTC/USDT",
            side="buy",
            entry_price=50000.0,
            exit_price=51000.0,
            quantity=0.1,
            pnl=100.0,
            pnl_pct=2.0,
            duration_seconds=3600
        )
        
        print("✅ Data Structures: OK")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Data Structures: {e}")
    
    # Resumo
    print("\n" + "="*50)
    print(f"📋 RESUMO: {tests_passed}/{total_tests} testes passaram")
    print(f"Taxa de Sucesso: {tests_passed/total_tests*100:.1f}%")
    
    if tests_passed == total_tests:
        print("🎉 D-07.1 A/B Testing Framework implementado com sucesso!")
        return True
    else:
        print(f"❌ {total_tests-tests_passed} testes falharam.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
