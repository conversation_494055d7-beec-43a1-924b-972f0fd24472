// QUALIA Farsight front-end script
// Fetches insights and renders simple cards.

async function fetchInsights(params = {}) {
  try {
    const qs = new URLSearchParams(params).toString();
    const url = qs ? `/api/farsight/insights?${qs}` : '/api/farsight/insights';
    const res = await fetch(url);
    const data = await res.json();
    if (!data.success) throw new Error(data.message || 'Erro desconhecido');
    return data.insights;
  } catch (err) {
    console.error('Falha ao obter insights', err);
    return [];
  }
}

let radarChart = null;

function renderInsights(insights) {
  const container = document.getElementById('insights-container');
  container.innerHTML = '';
  if (!insights.length) {
    container.textContent = 'Nenhum insight disponível.';
    return;
  }
  insights.forEach((insight) => {
    const card = document.createElement('div');
    card.className = 'insight-card';

    const title = document.createElement('h3');
    title.textContent = insight.topic;
    card.appendChild(title);

    const metrics = document.createElement('p');
    metrics.textContent = `Curvatura: ${insight.curvature} | Velocidade: ${insight.velocity}`;
    card.appendChild(metrics);

    const list = document.createElement('ul');
    insight.sources.forEach((src) => {
      const li = document.createElement('li');
      const a = document.createElement('a');
      a.href = src.link;
      a.textContent = src.title;
      a.target = '_blank';
      li.appendChild(a);
      list.appendChild(li);
    });
    card.appendChild(list);

  container.appendChild(card);
  });
  renderRadar(insights);
}

function renderRadar(insights) {
  const ctx = document.getElementById('farsight-radar').getContext('2d');
  const labels = insights.map((i) => i.topic);
  const data = insights.map((i) => i.curvature);
  if (radarChart) {
    radarChart.destroy();
  }
  radarChart = new Chart(ctx, {
    type: 'radar',
    data: {
      labels,
      datasets: [
        {
          label: 'Curvatura',
          data,
          backgroundColor: 'rgba(34, 170, 187, 0.2)',
          borderColor: 'rgba(34, 170, 187, 1)',
          borderWidth: 2,
        },
      ],
    },
    options: {
      scales: {
        r: {
          beginAtZero: true,
          ticks: { maxTicksLimit: 5 },
        },
      },
    },
  });
}

document.addEventListener('DOMContentLoaded', () => {
  const form = document.getElementById('farsight-form');
  form.addEventListener('submit', async (e) => {
    e.preventDefault();
    const daysBack = document.getElementById('days-back').value;
    const maxResults = document.getElementById('max-results').value;
    // mostra mensagem de carregamento
    renderInsights([]);
    const loading = document.createElement('div');
    loading.className = 'loading-message';
    loading.textContent = 'Coletando o futuro...';
    document.getElementById('insights-container').appendChild(loading);
    const insights = await fetchInsights({ days_back: daysBack, max_results: maxResults });
    renderInsights(insights);
  });

  (async () => {
    const insights = await fetchInsights({ days_back: 7, max_results: 40 });
    renderInsights(insights);
  })();
});
