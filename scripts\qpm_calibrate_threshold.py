#!/usr/bin/env python
"""Calibrate Quantum Pattern Memory similarity threshold.

This offline job computes the distribution of pairwise cosine similarities
between patterns stored in the Quantum Pattern Memory and suggests an optimal
similarity threshold. The recommended value is chosen by searching for the
lowest threshold where the mean similarity of pairs above that value differs
significantly from the mean below it (two-sample t-test, ``p-value < 0.05``).

Run this script weekly via cron to keep the threshold aligned with the current
pattern database.
"""

from __future__ import annotations

import argparse
import json
from pathlib import Path
from typing import List, Tuple

import numpy as np
from scipy import stats

from qualia.memory.qpm_utils import (
    compute_similarity_distribution,
    load_qpm_vectors,
)
from qualia.memory.quantum_pattern_memory import QuantumPatternMemory
from qualia.common_types import QuantumSignaturePacket


DEFAULT_PERSISTENCE_PATH = Path("data") / "cache" / "qpm_memory.json"


def _find_optimal_threshold(sims: np.ndarray, alpha: float) -> Tuple[float, float]:
    """Search for the threshold that best separates high and low similarities.

    Parameters
    ----------
    sims : ndarray
        Pairwise similarity values.
    alpha : float
        Significance level for the t-test used during the search.

    Returns
    -------
    tuple of float
        Selected threshold and the associated p-value.
    """

    best_p = 1.0
    best_t = float(np.percentile(sims, 95))
    for threshold in np.arange(0.1, 0.95, 0.01):
        high = sims[sims >= threshold]
        low = sims[sims < threshold]
        if len(high) < 2 or len(low) < 2:
            continue
        _, p_val = stats.ttest_ind(high, low, equal_var=False)
        if p_val < best_p:
            best_p = p_val
            best_t = threshold
    if best_p >= alpha:
        print(
            f"No threshold achieved p<{alpha}; using 95th percentile value {best_t:.2f}."
        )
    return best_t, best_p


def verify_threshold(
    vectors: List[np.ndarray], threshold: float, sample: int = 5
) -> bool:
    """Check if retrieval with the given threshold returns scores above it.

    Parameters
    ----------
    vectors : list of ndarray
        Stored pattern vectors.
    threshold : float
        Threshold to verify.
    sample : int, optional
        Number of vectors to use as queries during verification.

    Returns
    -------
    bool
        ``True`` if all sampled queries retrieve at least one pattern with
        ``similarity_score`` above ``threshold``.
    """

    qpm = QuantumPatternMemory(similarity_threshold=threshold, enable_warmstart=False)
    for vec in vectors:
        qpm.store_pattern(
            QuantumSignaturePacket(vector=vec.tolist(), metrics={}),
            market_snapshot={},
            outcome={},
        )
    for vec in vectors[:sample]:
        query = QuantumSignaturePacket(vector=vec.tolist(), metrics={})
        result = qpm.retrieve_similar_patterns(
            query,
            top_n=1,
            similarity_threshold=threshold,
        )
        if not result or result[0]["similarity_score"] < threshold:
            return False
    return True


def main() -> None:
    import collections

    parser = argparse.ArgumentParser(
        description="Calibrate QPM similarity threshold using stored patterns"
    )
    parser.add_argument(
        "--path", default=DEFAULT_PERSISTENCE_PATH, help="Path to QPM persistence JSON"
    )
    parser.add_argument(
        "--alpha", type=float, default=0.05, help="Significance level for t-test"
    )
    parser.add_argument(
        "--verify",
        action="store_true",
        help="Verify retrieval using the suggested threshold",
    )
    args = parser.parse_args()

    vectors = load_qpm_vectors(args.path)
    if len(vectors) < 2:
        print("Not enough vectors to compute similarities.")
        return

    # --- Validação de shapes ---
    shapes = [tuple(v.shape) for v in vectors]
    shape_counts = collections.Counter(shapes)
    if not shape_counts:
        print("Nenhum vetor encontrado.")
        return
    shape_majoritario, n_majoritario = shape_counts.most_common(1)[0]
    if len(shape_counts) > 1:
        print(
            f"[QUALIA AUDITORIA] Shapes encontrados: {dict(shape_counts)}. Shape majoritário: {shape_majoritario} ({n_majoritario} vetores). Vetores divergentes serão descartados."
        )
    else:
        print(f"[QUALIA AUDITORIA] Todos os vetores têm shape {shape_majoritario}.")
    vetores_validos = [v for v in vectors if tuple(v.shape) == shape_majoritario]
    n_descartados = len(vectors) - len(vetores_validos)
    if n_descartados > 0:
        print(
            f"[QUALIA AUDITORIA] {n_descartados} vetores descartados por shape inconsistente."
        )
        # Exportar shapes e índices dos vetores descartados para auditoria
        discarded_info = [
            {
                "index": i,
                "shape": tuple(v.shape),
                "preview": v[:10].tolist() if hasattr(v, "tolist") else str(v)[:100],
            }
            for i, v in enumerate(vectors)
            if tuple(v.shape) != shape_majoritario
        ]
        with open("discarded_shapes_audit.json", "w", encoding="utf-8") as f:
            import json

            json.dump(discarded_info, f, indent=2, ensure_ascii=False)
        print(
            "[QUALIA AUDITORIA] Relatório de vetores descartados salvo em discarded_shapes_audit.json."
        )
    if len(vetores_validos) < 2:
        print("Não há vetores suficientes com shape consistente para calibrar.")
        return

    sims = compute_similarity_distribution(vetores_validos)
    if sims.size == 0:
        print("No similarities calculated.")
        return

    threshold, p_val = _find_optimal_threshold(sims, args.alpha)
    print(f"optimal_threshold={threshold:.2f} p_value={p_val:.5f}")

    if args.verify:
        if verify_threshold(vetores_validos, threshold):
            print("[QUALIA AUDITORIA] Verification succeeded.")
        else:
            print("[QUALIA AUDITORIA] Verification failed.")

    # Atualização automática do threshold no arquivo de parâmetros
    config_path = Path(__file__).resolve().parents[1] / "config" / "strategy_parameters.json"
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            config_data = json.load(f)
        if "qpm_config" not in config_data:
            config_data["qpm_config"] = {}
        old_value = config_data["qpm_config"].get("similarity_threshold", None)
        config_data["qpm_config"]["similarity_threshold"] = float(f"{threshold:.4f}")
        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(config_data, f, indent=4, ensure_ascii=False)
        print(
            f"[QUALIA AUDITORIA] Threshold do QPM atualizado em '{config_path}': {old_value} -> {threshold:.4f}"
        )
    except Exception as e:
        print(
            f"[QUALIA AUDITORIA] Falha ao atualizar threshold em '{config_path}': {e}"
        )


if __name__ == "__main__":
    main()
