"""
QUALIA Multi-Fidelity Optimization System - D-04 Implementation

Sistema de otimização multi-fidelidade que permite avaliar parâmetros
com diferentes níveis de recursos computacionais (tempo de backtesting).

Funcionalidades:
- Níveis de fidelidade: 1h, 6h, 24h de backtesting
- Alocação inteligente de recursos
- Promoção progressiva de trials promissores
- Integração com sistema de pruning
"""

from __future__ import annotations

import time
import asyncio
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum
import numpy as np

import optuna
from optuna.trial import Trial

from ..utils.logger import get_logger
from .advanced_pruning import TradingMetrics, MarketRegime

logger = get_logger(__name__)


class FidelityLevel(Enum):
    """Níveis de fidelidade disponíveis."""
    LOW = 1      # 1 hora de backtesting
    MEDIUM = 6   # 6 horas de backtesting  
    HIGH = 24    # 24 horas de backtesting
    ULTRA = 72   # 72 horas de backtesting (para casos especiais)


@dataclass
class FidelityConfig:
    """Configuração do sistema multi-fidelidade."""
    
    # Níveis de fidelidade e orçamento
    fidelity_levels: List[FidelityLevel] = field(default_factory=lambda: [
        FidelityLevel.LOW, FidelityLevel.MEDIUM, FidelityLevel.HIGH
    ])
    
    # Distribuição de orçamento por nível (deve somar 1.0)
    budget_allocation: Dict[FidelityLevel, float] = field(default_factory=lambda: {
        FidelityLevel.LOW: 0.6,     # 60% dos trials em baixa fidelidade
        FidelityLevel.MEDIUM: 0.3,  # 30% em média fidelidade
        FidelityLevel.HIGH: 0.1     # 10% em alta fidelidade
    })
    
    # Critérios de promoção entre níveis
    promotion_thresholds: Dict[FidelityLevel, Dict[str, float]] = field(default_factory=lambda: {
        FidelityLevel.LOW: {
            "min_sharpe": 0.8,
            "max_drawdown": 0.12,
            "min_trades": 5,
            "percentile_rank": 0.7  # Top 30%
        },
        FidelityLevel.MEDIUM: {
            "min_sharpe": 1.2,
            "max_drawdown": 0.10,
            "min_trades": 15,
            "percentile_rank": 0.8  # Top 20%
        }
    })
    
    # Configurações de execução
    max_concurrent_evaluations: int = 4
    evaluation_timeout_multiplier: float = 1.5  # Timeout = fidelity_hours * multiplier
    
    # Early stopping
    early_stopping_enabled: bool = True
    early_stopping_patience: int = 3  # Níveis de fidelidade sem melhoria
    
    # Regime-specific adjustments
    regime_adjustments: Dict[MarketRegime, Dict[str, float]] = field(default_factory=lambda: {
        MarketRegime.VOLATILE: {"budget_low": 0.7, "budget_medium": 0.25, "budget_high": 0.05},
        MarketRegime.STABLE: {"budget_low": 0.5, "budget_medium": 0.3, "budget_high": 0.2},
    })


@dataclass
class FidelityResult:
    """Resultado de avaliação em um nível de fidelidade."""
    
    trial_number: int
    fidelity_level: FidelityLevel
    parameters: Dict[str, Any]
    metrics: TradingMetrics
    evaluation_time: float
    timestamp: float
    promoted: bool = False
    promotion_reason: Optional[str] = None


class MultiFidelityEvaluator:
    """
    Avaliador multi-fidelidade para otimização Bayesiana.
    
    Gerencia a execução de trials em diferentes níveis de fidelidade,
    promovendo trials promissores para níveis mais altos.
    """
    
    def __init__(self, 
                 config: FidelityConfig = None,
                 evaluation_function: Optional[Callable] = None):
        self.config = config or FidelityConfig()
        self.evaluation_function = evaluation_function
        self.current_regime = MarketRegime.SIDEWAYS
        
        # Estado do avaliador
        self.results_history: List[FidelityResult] = []
        self.active_evaluations: Dict[int, FidelityLevel] = {}  # trial_number -> fidelity
        self.promotion_queue: List[Tuple[int, FidelityLevel]] = []  # (trial_number, target_fidelity)
        
        # Estatísticas
        self.stats = {
            "total_evaluations": 0,
            "evaluations_by_fidelity": {level: 0 for level in FidelityLevel},
            "promotions": 0,
            "early_stops": 0,
            "average_evaluation_time": {}
        }
        
        logger.info(f"[TARGET] MultiFidelityEvaluator inicializado")
        logger.info(f"   Níveis: {[level.value for level in self.config.fidelity_levels]}")
        logger.info(f"   Orçamento: {self.config.budget_allocation}")
    
    def set_evaluation_function(self, func: Callable):
        """Define a função de avaliação de parâmetros."""
        self.evaluation_function = func
    
    async def evaluate_trial(self, 
                           trial: Trial, 
                           symbol: str,
                           initial_fidelity: Optional[FidelityLevel] = None) -> TradingMetrics:
        """
        Avalia um trial usando multi-fidelidade.
        
        Args:
            trial: Trial do Optuna
            symbol: Símbolo para avaliação
            initial_fidelity: Nível inicial (se None, usa LOW)
            
        Returns:
            Métricas finais do trial
        """
        
        if not self.evaluation_function:
            raise ValueError("Função de avaliação não definida")
        
        # Determinar fidelidade inicial
        current_fidelity = initial_fidelity or self._get_initial_fidelity()
        
        # Registrar avaliação ativa
        self.active_evaluations[trial.number] = current_fidelity
        
        try:
            # Avaliar no nível atual
            result = await self._evaluate_at_fidelity(trial, symbol, current_fidelity)
            
            # Verificar se deve promover
            while self._should_promote(result):
                next_fidelity = self._get_next_fidelity(current_fidelity)
                if not next_fidelity:
                    break
                
                logger.info(f"🚀 Promovendo trial {trial.number}: {current_fidelity.value}h → {next_fidelity.value}h")
                
                # Avaliar no próximo nível
                promoted_result = await self._evaluate_at_fidelity(trial, symbol, next_fidelity)
                promoted_result.promoted = True
                promoted_result.promotion_reason = f"Promoted from {current_fidelity.value}h"
                
                # Atualizar resultado
                result = promoted_result
                current_fidelity = next_fidelity
                self.stats["promotions"] += 1
            
            # Salvar resultado final
            self.results_history.append(result)
            self._update_stats(result)
            
            return result.metrics
            
        finally:
            # Remover da lista de avaliações ativas
            self.active_evaluations.pop(trial.number, None)
    
    async def _evaluate_at_fidelity(self, 
                                  trial: Trial, 
                                  symbol: str, 
                                  fidelity: FidelityLevel) -> FidelityResult:
        """Avalia parâmetros em um nível específico de fidelidade."""
        
        start_time = time.time()
        
        # Extrair parâmetros do trial
        params = {
            "price_amplification": trial.params.get("price_amplification", 1.0),
            "news_amplification": trial.params.get("news_amplification", 1.0),
            "min_confidence": trial.params.get("min_confidence", 0.5)
        }
        
        # Configurar timeout baseado na fidelidade
        timeout = fidelity.value * self.config.evaluation_timeout_multiplier * 3600  # horas para segundos
        
        try:
            # Executar avaliação com timeout
            metrics = await asyncio.wait_for(
                self.evaluation_function(symbol, params, fidelity.value),
                timeout=timeout
            )
            
            evaluation_time = time.time() - start_time
            
            # Criar resultado
            result = FidelityResult(
                trial_number=trial.number,
                fidelity_level=fidelity,
                parameters=params,
                metrics=metrics,
                evaluation_time=evaluation_time,
                timestamp=time.time()
            )
            
            logger.info(f"[PASS] Trial {trial.number} avaliado em {fidelity.value}h: "
                       f"Sharpe={metrics.sharpe_ratio:.3f}, DD={metrics.max_drawdown:.3f}")
            
            return result
            
        except asyncio.TimeoutError:
            logger.warning(f"⏰ Timeout na avaliação do trial {trial.number} em {fidelity.value}h")
            
            # Retornar métricas ruins para indicar falha
            metrics = TradingMetrics()
            metrics.sharpe_ratio = -999.0
            metrics.max_drawdown = 1.0
            metrics.trade_count = 0
            
            return FidelityResult(
                trial_number=trial.number,
                fidelity_level=fidelity,
                parameters=params,
                metrics=metrics,
                evaluation_time=time.time() - start_time,
                timestamp=time.time()
            )
    
    def _get_initial_fidelity(self) -> FidelityLevel:
        """Determina o nível inicial de fidelidade baseado no orçamento."""
        
        # Usar distribuição probabilística baseada no orçamento
        levels = list(self.config.budget_allocation.keys())
        weights = list(self.config.budget_allocation.values())
        
        # Ajustar baseado no regime de mercado
        if self.current_regime in self.config.regime_adjustments:
            adjustments = self.config.regime_adjustments[self.current_regime]
            # Aplicar ajustes (implementação simplificada)
            pass
        
        # Por simplicidade, sempre começar com LOW
        return FidelityLevel.LOW
    
    def _should_promote(self, result: FidelityResult) -> bool:
        """Determina se um resultado deve ser promovido para o próximo nível."""
        
        current_level = result.fidelity_level
        
        # Verificar se há próximo nível
        if not self._get_next_fidelity(current_level):
            return False
        
        # Verificar critérios de promoção
        if current_level not in self.config.promotion_thresholds:
            return False
        
        thresholds = self.config.promotion_thresholds[current_level]
        metrics = result.metrics
        
        # Verificar métricas mínimas
        if not metrics.is_valid():
            return False
        
        if metrics.sharpe_ratio < thresholds.get("min_sharpe", 0.0):
            return False
        
        if metrics.max_drawdown > thresholds.get("max_drawdown", 1.0):
            return False
        
        if metrics.trade_count < thresholds.get("min_trades", 0):
            return False
        
        # Verificar ranking percentil (se temos histórico suficiente)
        percentile_threshold = thresholds.get("percentile_rank", 0.5)
        if self._get_percentile_rank(result) < percentile_threshold:
            return False
        
        return True
    
    def _get_next_fidelity(self, current: FidelityLevel) -> Optional[FidelityLevel]:
        """Retorna o próximo nível de fidelidade."""
        
        current_index = self.config.fidelity_levels.index(current)
        if current_index + 1 < len(self.config.fidelity_levels):
            return self.config.fidelity_levels[current_index + 1]
        return None
    
    def _get_percentile_rank(self, result: FidelityResult) -> float:
        """Calcula o ranking percentil do resultado."""
        
        # Filtrar resultados do mesmo nível de fidelidade
        same_level_results = [
            r for r in self.results_history 
            if r.fidelity_level == result.fidelity_level and r.metrics.is_valid()
        ]
        
        if len(same_level_results) < 5:  # Não há dados suficientes
            return 0.5  # Assumir mediano
        
        # Calcular percentil baseado no Sharpe ratio
        sharpe_values = [r.metrics.sharpe_ratio for r in same_level_results]
        current_sharpe = result.metrics.sharpe_ratio
        
        better_count = sum(1 for s in sharpe_values if s < current_sharpe)
        percentile = better_count / len(sharpe_values)
        
        return percentile
    
    def _update_stats(self, result: FidelityResult):
        """Atualiza estatísticas do avaliador."""
        
        self.stats["total_evaluations"] += 1
        self.stats["evaluations_by_fidelity"][result.fidelity_level] += 1
        
        # Atualizar tempo médio de avaliação
        level_key = result.fidelity_level.value
        if level_key not in self.stats["average_evaluation_time"]:
            self.stats["average_evaluation_time"][level_key] = []
        
        self.stats["average_evaluation_time"][level_key].append(result.evaluation_time)
        
        # Manter apenas os últimos 100 tempos
        if len(self.stats["average_evaluation_time"][level_key]) > 100:
            self.stats["average_evaluation_time"][level_key] = \
                self.stats["average_evaluation_time"][level_key][-100:]
    
    def update_market_regime(self, regime: MarketRegime):
        """Atualiza o regime de mercado."""
        if regime != self.current_regime:
            logger.info(f"[STATS] Regime multi-fidelidade atualizado: {self.current_regime.value} -> {regime.value}")
            self.current_regime = regime
    
    def get_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do avaliador."""
        
        # Calcular tempos médios
        avg_times = {}
        for level, times in self.stats["average_evaluation_time"].items():
            if times:
                avg_times[f"{level}h"] = f"{np.mean(times):.1f}s"
        
        return {
            **self.stats,
            "average_evaluation_time": avg_times,
            "active_evaluations": len(self.active_evaluations),
            "current_regime": self.current_regime.value,
            "recent_results": [
                {
                    "trial": r.trial_number,
                    "fidelity": f"{r.fidelity_level.value}h",
                    "sharpe": f"{r.metrics.sharpe_ratio:.3f}" if r.metrics.sharpe_ratio else "N/A",
                    "promoted": r.promoted
                }
                for r in self.results_history[-10:]
            ]
        }
