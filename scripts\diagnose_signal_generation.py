#!/usr/bin/env python3
"""
YAA (YET ANOTHER AGENT) - Diagnóstico de Geração de Sinais QUALIA

Este script diagnostica por que o sistema QUALIA não está gerando sinais de trading
após longas execuções, investigando cada etapa da cadeia de decisão.
"""

import asyncio
import sys
import os
import json
import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from qualia.signals.generator import SignalGenerator, SignalConfig
from qualia.signals.technical_analyzer import TechnicalSignalAnalyzer
from qualia.signals.quantum_analyzer import QuantumSignalAnalyzer
from qualia.signals.sentiment_analyzer import SentimentSignalAnalyzer
from qualia.core.qast_core import TradingQASTCore
from qualia.consciousness.enhanced_data_collector import EnhancedDataCollector
from qualia.config.config_loader import load_env_and_json
from qualia.utils.logger import setup_logging
import yaml
import numpy as np

# Configurar logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class QUALIASignalDiagnostic:
    """Diagnóstico completo do sistema de geração de sinais QUALIA."""
    
    def __init__(self, config_path: str = None):
        self.config_path = config_path or "config/aggressive_test_config.yaml"
        self.config = self._load_config()
        self.issues_found = []
        self.recommendations = []
        
    def _load_config(self) -> Dict[str, Any]:
        """Carrega a configuração do sistema."""
        try:
            config_file = Path(self.config_path)
            if config_file.exists():
                with open(config_file, 'r') as f:
                    config = yaml.safe_load(f)
                logger.info(f"✅ Configuração carregada de: {config_file}")
                return config
            else:
                logger.error(f"❌ Arquivo de configuração não encontrado: {config_file}")
                return {}
        except Exception as e:
            logger.error(f"❌ Erro ao carregar configuração: {e}")
            return {}
    
    def diagnose_configuration(self):
        """Diagnostica as configurações de geração de sinais."""
        logger.info("🔍 DIAGNÓSTICO 1: Configurações de Geração de Sinais")
        
        # Verificar configuração min_confidence
        signal_approval = self.config.get("signal_approval", {})
        min_confidence = signal_approval.get("min_confidence", 0.6)
        
        logger.info(f"📊 min_confidence configurado: {min_confidence}")
        
        if min_confidence > 0.1:
            self.issues_found.append(f"min_confidence muito alto: {min_confidence}")
            self.recommendations.append("Reduzir min_confidence para 0.01-0.05 para testes")
        
        # Verificar outras configurações críticas
        holographic_config = self.config.get("holographic_trading", {})
        holo_min_confidence = holographic_config.get("min_confidence", 0.6)
        
        logger.info(f"📊 holographic min_confidence: {holo_min_confidence}")
        
        if holo_min_confidence > 0.1:
            self.issues_found.append(f"holographic min_confidence muito alto: {holo_min_confidence}")
            self.recommendations.append("Reduzir holographic min_confidence para 0.01-0.05")
        
        # Verificar configurações do universo holográfico
        holo_universe = self.config.get("holographic_universe", {})
        pattern_detection = holo_universe.get("pattern_detection", {})
        confidence_threshold = pattern_detection.get("confidence_threshold", 0.6)
        
        logger.info(f"📊 pattern confidence_threshold: {confidence_threshold}")
        
        if confidence_threshold > 0.3:
            self.issues_found.append(f"pattern confidence_threshold muito alto: {confidence_threshold}")
            self.recommendations.append("Reduzir pattern confidence_threshold para 0.1-0.2")
    
    async def diagnose_signal_generator(self):
        """Diagnostica o SignalGenerator diretamente."""
        logger.info("🔍 DIAGNÓSTICO 2: SignalGenerator")
        
        # Criar SignalGenerator com configuração atual
        signal_config = {
            "min_confidence": self.config.get("signal_approval", {}).get("min_confidence", 0.6),
            "max_signals_per_symbol": 3,
            "quantum_weight": 0.4,
            "technical_weight": 0.3,
            "sentiment_weight": 0.3
        }
        
        generator = SignalGenerator(signal_config)
        
        logger.info(f"📊 SignalGenerator min_confidence: {generator.signal_config.min_confidence}")
        
        # Testar com dados simulados
        mock_market_data = self._create_mock_market_data()
        mock_analysis_input = {
            "market_data": mock_market_data,
            "quantum_results": {"entropy": 0.5, "coherence": 0.7},
            "temporal_results": {"patterns": []}
        }
        
        try:
            signals = await generator.generate_signals("BTC/USDT", mock_analysis_input)
            logger.info(f"📊 Sinais gerados com dados simulados: {len(signals)}")
            
            if len(signals) == 0:
                self.issues_found.append("SignalGenerator não gera sinais mesmo com dados simulados")
                self.recommendations.append("Verificar implementação dos analisadores técnicos")
            else:
                for signal in signals:
                    logger.info(f"✅ Sinal: {signal['action']} conf={signal['confidence']:.3f}")
                    
        except Exception as e:
            logger.error(f"❌ Erro no SignalGenerator: {e}")
            self.issues_found.append(f"Erro no SignalGenerator: {str(e)}")
    
    def _create_mock_market_data(self) -> Dict[str, Any]:
        """Cria dados de mercado simulados para teste."""
        # Simular 100 pontos de preço com tendência de alta
        base_price = 65000
        prices = []
        volumes = []
        
        for i in range(100):
            # Tendência de alta com ruído
            price = base_price + (i * 10) + np.random.normal(0, 50)
            volume = 1000 + np.random.normal(0, 100)
            prices.append(price)
            volumes.append(max(volume, 100))  # Volume mínimo
        
        return {
            "prices": prices,
            "volumes": volumes,
            "timestamps": [datetime.now(timezone.utc).timestamp() + i for i in range(100)],
            "symbol": "BTC/USDT"
        }
    
    async def diagnose_technical_analyzer(self):
        """Diagnostica o TechnicalSignalAnalyzer."""
        logger.info("🔍 DIAGNÓSTICO 3: TechnicalSignalAnalyzer")
        
        analyzer = TechnicalSignalAnalyzer()
        mock_data = self._create_mock_market_data()
        
        try:
            analysis = await analyzer.analyze(mock_data)
            logger.info(f"📊 Análises técnicas geradas: {len(analysis)}")
            
            for i, result in enumerate(analysis):
                logger.info(f"  Análise {i+1}: {result.get('action', 'N/A')} conf={result.get('confidence', 0):.3f}")
                
            if len(analysis) == 0:
                self.issues_found.append("TechnicalSignalAnalyzer não gera análises")
                self.recommendations.append("Verificar se os dados têm formato correto para análise técnica")
                
        except Exception as e:
            logger.error(f"❌ Erro no TechnicalSignalAnalyzer: {e}")
            self.issues_found.append(f"Erro no TechnicalSignalAnalyzer: {str(e)}")
    
    async def diagnose_data_structure(self):
        """Diagnostica a estrutura dos dados de mercado."""
        logger.info("🔍 DIAGNÓSTICO 4: Estrutura dos Dados de Mercado")
        
        mock_data = self._create_mock_market_data()
        
        # Verificar estrutura esperada
        required_fields = ["prices", "volumes"]
        for field in required_fields:
            if field not in mock_data:
                self.issues_found.append(f"Campo obrigatório ausente: {field}")
            else:
                data_len = len(mock_data[field])
                logger.info(f"📊 {field}: {data_len} elementos")
                
                if data_len < 20:
                    self.issues_found.append(f"Dados insuficientes em {field}: {data_len} < 20")
                    self.recommendations.append(f"Garantir pelo menos 20 elementos em {field}")
    
    async def diagnose_real_data_flow(self):
        """Diagnostica o fluxo de dados reais do sistema."""
        logger.info("🔍 DIAGNÓSTICO 5: Fluxo de Dados Reais")

        # Verificar se há dados reais salvos
        data_dir = Path("data")
        if data_dir.exists():
            logger.info(f"📁 Diretório de dados encontrado: {data_dir}")

            # Procurar por arquivos de dados recentes
            for file_path in data_dir.rglob("*.json"):
                if file_path.stat().st_size > 0:
                    logger.info(f"📄 Arquivo de dados: {file_path} ({file_path.stat().st_size} bytes)")

                    # Tentar ler e analisar o conteúdo
                    try:
                        with open(file_path, 'r') as f:
                            data = json.load(f)
                            if isinstance(data, dict):
                                logger.info(f"  📊 Chaves: {list(data.keys())}")

                                # Verificar se há dados de mercado
                                if "market_data" in data:
                                    market_data = data["market_data"]
                                    if isinstance(market_data, dict):
                                        for symbol, symbol_data in market_data.items():
                                            if isinstance(symbol_data, dict):
                                                for timeframe, tf_data in symbol_data.items():
                                                    if isinstance(tf_data, dict) and "close" in tf_data:
                                                        close_len = len(tf_data["close"])
                                                        logger.info(f"    📈 {symbol} {timeframe}: {close_len} candles")

                                                        if close_len < 20:
                                                            self.issues_found.append(f"Dados insuficientes para {symbol} {timeframe}: {close_len} < 20")
                                                            self.recommendations.append(f"Coletar mais dados históricos para {symbol}")
                    except Exception as e:
                        logger.warning(f"  ⚠️ Erro ao ler {file_path}: {e}")
        else:
            self.issues_found.append("Diretório de dados não encontrado")
            self.recommendations.append("Verificar se o sistema está coletando dados corretamente")

    async def diagnose_live_data_collection(self):
        """Diagnostica a coleta de dados em tempo real."""
        logger.info("🔍 DIAGNÓSTICO 6: Coleta de Dados em Tempo Real")

        try:
            # Tentar criar um EnhancedDataCollector
            from qualia.exchanges.kucoin_client import KuCoinClient

            # Verificar se as credenciais estão disponíveis
            api_key = os.getenv("KUCOIN_API_KEY")
            api_secret = os.getenv("KUCOIN_SECRET_KEY")
            passphrase = os.getenv("KUCOIN_PASSPHRASE")

            if not all([api_key, api_secret, passphrase]):
                self.issues_found.append("Credenciais da KuCoin não configuradas")
                self.recommendations.append("Configurar KUCOIN_API_KEY, KUCOIN_SECRET_KEY e KUCOIN_PASSPHRASE no .env")
                return

            # Tentar conectar com a exchange
            kucoin_config = {
                "api_key": api_key,
                "api_secret": api_secret,
                "password": passphrase,
                "sandbox": True,  # Usar sandbox para teste
                "exchange_id": "kucoin"
            }

            client = KuCoinClient(kucoin_config)

            # Testar conectividade
            try:
                # Tentar buscar dados de mercado
                symbols = ["BTC-USDT"]  # Formato KuCoin
                timeframe = "1min"

                logger.info(f"🔌 Testando conectividade com KuCoin...")

                # Simular coleta de dados
                for symbol in symbols:
                    try:
                        # Aqui normalmente chamaríamos client.get_ohlcv()
                        # Mas vamos apenas verificar se o cliente foi criado
                        logger.info(f"📊 Cliente KuCoin criado para {symbol}")

                    except Exception as e:
                        logger.error(f"❌ Erro ao coletar dados para {symbol}: {e}")
                        self.issues_found.append(f"Erro na coleta de dados: {str(e)}")

            except Exception as e:
                logger.error(f"❌ Erro de conectividade: {e}")
                self.issues_found.append(f"Erro de conectividade com exchange: {str(e)}")
                self.recommendations.append("Verificar conexão de internet e credenciais da exchange")

        except Exception as e:
            logger.error(f"❌ Erro ao inicializar cliente da exchange: {e}")
            self.issues_found.append(f"Erro ao inicializar exchange: {str(e)}")

    def print_summary(self):
        """Imprime o resumo do diagnóstico."""
        logger.info("=" * 80)
        logger.info("📋 RESUMO DO DIAGNÓSTICO QUALIA")
        logger.info("=" * 80)

        if not self.issues_found:
            logger.info("✅ Nenhum problema encontrado!")
        else:
            logger.info(f"❌ {len(self.issues_found)} problemas encontrados:")
            for i, issue in enumerate(self.issues_found, 1):
                logger.info(f"  {i}. {issue}")

        if self.recommendations:
            logger.info(f"\n💡 {len(self.recommendations)} recomendações:")
            for i, rec in enumerate(self.recommendations, 1):
                logger.info(f"  {i}. {rec}")

        logger.info("=" * 80)

async def main():
    """Função principal do diagnóstico."""
    logger.info("🌌 YAA - Diagnóstico de Geração de Sinais QUALIA")
    logger.info("=" * 80)
    
    # Verificar argumentos
    config_path = sys.argv[1] if len(sys.argv) > 1 else "config/aggressive_test_config.yaml"
    
    diagnostic = QUALIASignalDiagnostic(config_path)
    
    # Executar diagnósticos
    diagnostic.diagnose_configuration()
    await diagnostic.diagnose_signal_generator()
    await diagnostic.diagnose_technical_analyzer()
    await diagnostic.diagnose_data_structure()
    await diagnostic.diagnose_real_data_flow()
    await diagnostic.diagnose_live_data_collection()

    # Imprimir resumo
    diagnostic.print_summary()

if __name__ == "__main__":
    asyncio.run(main())
