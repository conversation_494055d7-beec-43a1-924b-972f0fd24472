from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Tuple

from datadog import DogStatsd
from datetime import datetime
import time
import numpy as np
import pandas as pd
from ..config.settings import market_metrics_enabled
from ..utils.logger import get_logger
from ..signals.signal_type import SignalType
from .risk_utils import (
    calculate_atr_levels,
    DynamicRiskParameters,
)
import traceback
from ..config.decision_defaults import load_decision_defaults

# YAA: Cores para o console (adicionadas para QPM Test Mode log)
BOLD = "\033[1m"
YELLOW = "\033[93m"
ENDC = "\033[0m"

logger = get_logger(__name__)

_DECISION_DEFAULTS = load_decision_defaults()

# Mapeamento de funções extensas:
# - _compute_decision utiliza _integrate_qpm_insights e _apply_rsi_filter


@dataclass
class TradeContext:
    """Contexto para decisões de trading."""

    symbol: str
    timeframe: str
    ohlcv: pd.DataFrame
    current_price: float
    timestamp: datetime
    wallet_state: Dict[str, float]
    liquidity: float
    volatility: float
    strategy_metrics: Dict[str, Any]
    quantum_metrics: Dict[str, Any]
    market_state: str  # 'trend', 'range', 'volatile', 'uncertain'
    risk_mode: str  # 'normal', 'conservative', 'aggressive'
    current_bid: Optional[float] = None
    current_ask: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class ScalpingDecision:
    """
    Implementação da classe de decisão de scalping conforme princípios quânticos.
    Avalia múltiplos cenários simultaneamente (superposição conceitual),
    conecta padrões não óbvios (entrelançamento tecnológico) e
    simplifica sem remover essência (redução complexa objetiva).
    """

    def __init__(
        self,
        symbol: str,
        timeframe: str,
        ohlcv_data: pd.DataFrame,
        current_price: float,
        # YAA: Mantido para acesso a wallet, etc.
        trade_context: Optional[TradeContext] = None,
        # YAA: Parâmetros da estratégia
        strategy_params: Optional[Dict[str, Any]] = None,
        # YAA: Parâmetro que pode vir do construtor se necessário no
        # futuro
        risk_profile_override: Optional[str] = None,
        # YAA: Novas configs de risco
        risk_profile_settings: Optional[Dict[str, Any]] = None,
        # YAA: Novo parâmetro para QPM insights
        qpm_insights: Optional[Dict[str, Any]] = None,
        # YAA: Novos parâmetros para SL Adaptativo por ATR
        atr_value: Optional[float] = None,
        atr_sl_multiplier_k: Optional[float] = None,
        *,
        statsd_client: Optional[DogStatsd] = None,
    ):
        self.symbol = symbol
        self.timeframe = timeframe
        self.ohlcv_data = ohlcv_data
        self.current_price = current_price
        self.trade_context = trade_context

        # Optional metrics client
        if statsd_client is not None or market_metrics_enabled:
            self.statsd: Optional[DogStatsd] = statsd_client or DogStatsd()
        else:
            self.statsd = None

        # YAA: Usar risk_profile do TradeContext se disponível, senão o passado
        # diretamente, senão default.
        # O risk_profile é fundamental para o risk_adjustment.
        if (
            trade_context
            and hasattr(trade_context, "risk_mode")
            and trade_context.risk_mode
        ):
            self.risk_profile = trade_context.risk_mode
        elif (
            risk_profile_override
        ):  # Parâmetro que pode vir do construtor se necessário no futuro
            self.risk_profile = risk_profile_override
        else:  # Fallback para um default, ou poderia vir de self.params
            self.risk_profile = "balanced"

        # YAA: Inicializar self.risk_adjustment
        if self.risk_profile == "conservative":
            self.risk_adjustment = 0.2
        elif self.risk_profile == "aggressive":
            self.risk_adjustment = 0.8
        else:  # balanced ou default
            self.risk_adjustment = 0.5

        # YAA: Sobrescrever risk_adjustment se fornecido via
        # risk_profile_settings
        self.risk_profile_settings = (
            risk_profile_settings if risk_profile_settings is not None else {}
        )
        current_profile_settings = self.risk_profile_settings.get(self.risk_profile, {})
        self.risk_adjustment = current_profile_settings.get(
            "risk_adjustment_value", self.risk_adjustment
        )

        # YAA: Armazenar fatores de SL/TP das configurações ou usar defaults
        self.sl_adjustment_factor = current_profile_settings.get(
            "sl_factor", 1.0
        )  # Default para balanced
        self.tp_adjustment_factor = current_profile_settings.get(
            "tp_factor", 1.0
        )  # Default para balanced
        # Ajustar defaults se o perfil não for balanced e não estiver nas
        # configs
        if (
            not current_profile_settings
        ):  # Se o perfil específico não foi encontrado nas configs
            if self.risk_profile == "conservative":
                self.sl_adjustment_factor = 1.5
                self.tp_adjustment_factor = 0.5
            elif self.risk_profile == "aggressive":
                self.sl_adjustment_factor = 0.75
                self.tp_adjustment_factor = 1.5

        self.params = strategy_params if strategy_params is not None else {}
        self.qpm_insights = qpm_insights  # YAA: Armazenar QPM insights
        # YAA: Armazenar parâmetros ATR
        self.atr_value = atr_value
        self.atr_sl_multiplier_k = atr_sl_multiplier_k

        # Inicializar os atributos da decisão com valores padrão
        self.signal: SignalType = SignalType.HOLD
        self.confidence: float = 0.0
        self.strength: float = 0.0
        self.reasons: List[str] = []
        self.metadata: Dict[str, Any] = {}
        self.stop_loss: Optional[float] = None
        self.take_profit: Optional[float] = None
        self.stop_loss_pct = self.params.get("stop_loss_pct", 0.01)  # Default 1%
        self.take_profit_pct = self.params.get("profit_target_pct", 0.02)  # Default 2%

        # Aplicar ajustes de SL/TP baseados no perfil de risco
        # stop_loss_pct_effective e take_profit_pct_effective são usados para
        # calcular os preços finais de SL/TP
        stop_loss_pct_effective = self.stop_loss_pct * self.sl_adjustment_factor
        take_profit_pct_effective = self.take_profit_pct * self.tp_adjustment_factor

        logger.debug(
            f"SL/TP Pcts para {self.symbol}: Base SL={self.stop_loss_pct:.4f}, "
            f"Base TP={self.take_profit_pct:.4f}. "
            f"Effective SL={stop_loss_pct_effective:.4f}, "
            f"Effective TP={take_profit_pct_effective:.4f} "
            f"(Fatores SL/TP: {self.sl_adjustment_factor:.2f}/"
            f"{self.tp_adjustment_factor:.2f} para perfil {self.risk_profile})"
        )

        # Definir stop loss e take profit somente se houver um sinal de BUY ou SELL
        if self.signal == SignalType.BUY:
            # YAA: Lógica para SL Adaptativo por ATR
            if (
                self.atr_sl_multiplier_k is not None
                and self.atr_sl_multiplier_k > 0
                and self.atr_value is not None
                and self.atr_value > 0
            ):
                stop_loss_offset = self.atr_value * self.atr_sl_multiplier_k
                self.stop_loss = self.current_price - stop_loss_offset
                self.reasons.append(
                    f"SL by ATR (k={self.atr_sl_multiplier_k:.2f}, "
                    f"atr={self.atr_value:.4f}, offset={stop_loss_offset:.4f})"
                )
                logger.debug(
                    f"{self.symbol}: SL definido por ATR para BUY: "
                    f"{self.stop_loss:.4f} "
                    f"(Preço: {self.current_price:.4f} - "
                    f"Offset: {stop_loss_offset:.4f})"
                )
            else:
                self.stop_loss = self.current_price * (1 - stop_loss_pct_effective)
                self.reasons.append(f"SL by % ({stop_loss_pct_effective*100:.2f}%)")
                logger.debug(
                    f"{self.symbol}: SL definido por % para BUY: {self.stop_loss:.4f} "
                    f"(Preço: {self.current_price:.4f}, "
                    f"Pct: {stop_loss_pct_effective:.4f})"
                )

            self.take_profit = self.current_price * (1 + take_profit_pct_effective)
            self.reasons.append(f"TP by % ({take_profit_pct_effective*100:.2f}%)")

        elif self.signal == SignalType.SELL:
            # YAA: Lógica para SL Adaptativo por ATR
            if (
                self.atr_sl_multiplier_k is not None
                and self.atr_sl_multiplier_k > 0
                and self.atr_value is not None
                and self.atr_value > 0
            ):
                stop_loss_offset = self.atr_value * self.atr_sl_multiplier_k
                self.stop_loss = self.current_price + stop_loss_offset
                self.reasons.append(
                    f"SL by ATR (k={self.atr_sl_multiplier_k:.2f}, "
                    f"atr={self.atr_value:.4f}, offset={stop_loss_offset:.4f})"
                )
                logger.debug(
                    f"{self.symbol}: SL definido por ATR para SELL: "
                    f"{self.stop_loss:.4f} "
                    f"(Preço: {self.current_price:.4f} + "
                    f"Offset: {stop_loss_offset:.4f})"
                )
            else:
                self.stop_loss = self.current_price * (1 + stop_loss_pct_effective)
                self.reasons.append(f"SL by % ({stop_loss_pct_effective*100:.2f}%)")
                logger.debug(
                    f"{self.symbol}: SL definido por % para SELL: {self.stop_loss:.4f} "
                    f"(Preço: {self.current_price:.4f}, "
                    f"Pct: {stop_loss_pct_effective:.4f})"
                )

            self.take_profit = self.current_price * (1 - take_profit_pct_effective)
            self.reasons.append(f"TP by % ({take_profit_pct_effective*100:.2f}%)")
        else:  # HOLD ou outro sinal
            self.stop_loss = None
            self.take_profit = None

        # Chamar os métodos de cálculo imediatamente após a inicialização
        self._compute_decision()  # Este agora usará self.params
        self._compute_order_parameters()  # Este também pode usar self.params no futuro

    def _measure_microvolatility(self, context: TradeContext) -> float:
        df = context.ohlcv
        if len(df) < 20:
            return 0.5
        recent_df = df.iloc[-20:]
        high_low = recent_df["high"] - recent_df["low"]
        high_close = np.abs(recent_df["high"] - recent_df["close"].shift())
        low_close = np.abs(recent_df["low"] - recent_df["close"].shift())
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        atr = tr.mean()
        norm_atr = atr / context.current_price
        upper_wicks = (
            recent_df["high"] - recent_df[["open", "close"]].max(axis=1)
        ) / tr
        lower_wicks = (recent_df[["open", "close"]].min(axis=1) - recent_df["low"]) / tr
        micro_moves = recent_df["high"].diff().abs() + recent_df["low"].diff().abs()
        micro_energy = micro_moves.mean() / atr
        price_velocity = recent_df["close"].diff().abs().mean() / atr
        if "volume" in recent_df.columns:
            vol_std = recent_df["volume"].std() / recent_df["volume"].mean()
            vol_pressure = vol_std * 0.3
        else:
            vol_pressure = 0.2
        micro_vol = (
            norm_atr * 2.5
            + upper_wicks.mean() * 0.7
            + lower_wicks.mean() * 0.7
            + micro_energy * 1.2
            + price_velocity * 1.5
            + vol_pressure
        ) / 6.6
        return min(1.0, max(0.0, micro_vol))

    def _check_liquidity(self, context: TradeContext) -> float:
        if hasattr(context, "liquidity") and context.liquidity is not None:
            return context.liquidity
        df = context.ohlcv
        if len(df) < 10 or "volume" not in df.columns:
            return 0.5
        recent_volume = df["volume"].iloc[-10:].mean()
        if len(df) >= 50:
            historical_volume = df["volume"].iloc[-50:-10].mean()
            volume_ratio = (
                recent_volume / historical_volume if historical_volume > 0 else 1.0
            )
        else:
            volume_ratio = 1.0
        if "BTC/USD" in context.symbol or "ETH/USD" in context.symbol:
            spread_factor = 0.9
        elif "BTC" in context.symbol or "ETH" in context.symbol:
            spread_factor = 0.8
        else:
            spread_factor = 0.6
        metadata_liquidity = context.metadata.get("liquidity_estimate", None)
        if metadata_liquidity is not None:
            return metadata_liquidity
        liquidity = volume_ratio * 0.7 + spread_factor * 0.3
        return min(1.0, max(0.0, liquidity))

    def _dynamic_risk(self, risk_profile: str, wallet_state: Dict[str, Any]) -> float:
        if risk_profile == "conservative":
            base_risk = 0.3
        elif risk_profile == "aggressive":
            base_risk = 0.7
        else:
            base_risk = 0.5
        # Ajuste dinâmico pode ser expandido conforme necessário
        return base_risk

    def _predict_edge(self, context: TradeContext) -> float:
        """Estima o edge esperado usando a diferença entre SMAs."""
        df = context.ohlcv
        if df is None or "close" not in df.columns or len(df) < 20:
            return 0.0

        short_sma = df["close"].rolling(window=5).mean().iloc[-1]
        long_sma = df["close"].rolling(window=20).mean().iloc[-1]
        if pd.isna(short_sma) or pd.isna(long_sma) or context.current_price == 0:
            return 0.0

        edge = (short_sma - long_sma) / context.current_price
        return float(max(min(edge, 1.0), -1.0))

    def _determine_market_condition(self, context: TradeContext) -> str:
        """Classifica a condição de mercado com base em tendência e volatilidade."""
        df = context.ohlcv
        if df is None or "close" not in df.columns or len(df) < 20:
            return "uncertain"

        recent = df["close"].iloc[-20:]
        trend = recent.iloc[-1] - recent.iloc[0]
        volatility = recent.pct_change().abs().mean()

        if volatility > 0.05:
            return "volatile"
        if abs(trend) > recent.std():
            return "trend"
        return "range"

    def _extract_quantum_confidence(self, context: TradeContext) -> float:
        """Calcula a confiança com base nas métricas quânticas recebidas."""
        qm = context.quantum_metrics or {}
        entropy = qm.get("entropy")
        coherence = qm.get("coherence")
        if entropy is None or coherence is None or entropy == 0:
            return 0.5

        score = coherence / entropy
        return float(max(min(score, 1.0), 0.0))

    def _integrate_qpm_insights(self, qpm_applied_signal_change: bool) -> bool:
        """Modula o sinal com base em insights do Quantum Pattern Matcher.

        Parameters
        ----------
        qpm_applied_signal_change
            Indica se o sinal já foi alterado pelo QPM anteriormente.

        Returns
        -------
        bool
            ``True`` se o sinal foi modificado pelo QPM, caso contrário o
            valor original.
        """
        if not self.qpm_insights:
            return qpm_applied_signal_change

        logger.debug("QPM Insights para %s: %s", self.symbol, self.qpm_insights)
        qpm_dominant_direction = self.qpm_insights.get(
            "dominant_direction", SignalType.HOLD
        )
        qpm_direction_confidence = self.qpm_insights.get("direction_confidence", 0.0)
        qpm_patterns_count = self.qpm_insights.get("similar_patterns_count", 0)

        if qpm_patterns_count <= 0:
            return qpm_applied_signal_change

        qpm_reason = (
            f"QPM({qpm_patterns_count} pats): Dir={qpm_dominant_direction}, "
            f"Conf={qpm_direction_confidence:.2f}"
        )

        if self.signal == qpm_dominant_direction and self.signal != SignalType.HOLD:
            self.confidence = min(
                1.0, self.confidence * (1 + (qpm_direction_confidence * 0.5))
            )
            qpm_reason += (
                f" (Confirmação QPM: Confiança aumentada para {self.confidence:.2f})"
            )
        elif (
            qpm_dominant_direction != SignalType.HOLD
            and self.signal != SignalType.HOLD
            and self.signal != qpm_dominant_direction
        ):
            self.confidence *= 1 - (qpm_direction_confidence * 0.75)
            qpm_reason += (
                f" (Contradição QPM: Confiança reduzida para {self.confidence:.2f})"
            )

        qpm_high_confidence_threshold = 0.70
        technical_signal_weak_threshold = 0.60

        if (
            qpm_direction_confidence >= qpm_high_confidence_threshold
            and qpm_dominant_direction != SignalType.HOLD
            and (
                self.signal == SignalType.HOLD
                or self.confidence < technical_signal_weak_threshold
            )
        ):
            if self.signal != qpm_dominant_direction:
                logger.info(
                    "Sinal para %s alterado pela QPM de %s (conf: %.2f) para %s (QPM conf: %.2f)",
                    self.symbol,
                    self.signal,
                    self.confidence,
                    qpm_dominant_direction,
                    qpm_direction_confidence,
                )
                self.signal = qpm_dominant_direction
                qpm_applied_signal_change = True
                self.confidence = qpm_direction_confidence * 0.9
                qpm_reason += f" (Sinal alterado para {self.signal} pela QPM com nova conf {self.confidence:.2f})"
            elif self.signal == qpm_dominant_direction:
                self.confidence = max(self.confidence, qpm_direction_confidence * 0.8)
                qpm_reason += f" (Sinal {self.signal} fraco/HOLD confirmado e reforçado pela QPM para conf {self.confidence:.2f})"

        self.reasons.append(qpm_reason)
        return qpm_applied_signal_change

    def _apply_rsi_filter(
        self,
        use_rsi_filter: bool,
        rsi_period: int,
        rsi_oversold_threshold: int,
        rsi_overbought_threshold: int,
        rsi_confirmation_factor: float,
        rsi_contradiction_factor: float,
    ) -> None:
        """Ajusta a confiança utilizando o RSI como filtro adicional."""
        ohlcv_len = len(self.ohlcv_data) if self.ohlcv_data is not None else "None"
        logger.debug(
            "Pre-RSI check: use_rsi_filter=%s, self.signal=%s, len_ohlcv=%s, rsi_period=%s",
            use_rsi_filter,
            self.signal,
            ohlcv_len,
            rsi_period,
        )

        if (
            not use_rsi_filter
            or self.signal == SignalType.HOLD
            or len(self.ohlcv_data) < rsi_period
        ):
            return

        delta = self.ohlcv_data["close"].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        current_rsi = rsi.iloc[-1]

        if pd.isna(current_rsi):
            logger.warning(
                "RSI(%s) não pôde ser calculado para %s.", rsi_period, self.symbol
            )
            return

        logger.debug("RSI(%s) para %s: %.2f", rsi_period, self.symbol, current_rsi)
        rsi_reason = f"RSI({rsi_period}): {current_rsi:.2f}"

        if self.signal == SignalType.BUY:
            if current_rsi > rsi_overbought_threshold:
                self.confidence *= rsi_contradiction_factor
                rsi_reason += f" (Sobrecomprado > {rsi_overbought_threshold}, conf reduzida para {self.confidence:.2f})"
            elif current_rsi > 50:
                self.confidence = min(1.0, self.confidence * rsi_confirmation_factor)
                rsi_reason += f" (Acima de 50, conf confirmada/aumentada para {self.confidence:.2f})"
        elif self.signal == SignalType.SELL:
            if current_rsi < rsi_oversold_threshold:
                self.confidence *= rsi_contradiction_factor
                rsi_reason += f" (Sobrevendido < {rsi_oversold_threshold}, conf reduzida para {self.confidence:.2f})"
            elif current_rsi < 50:
                self.confidence = min(1.0, self.confidence * rsi_confirmation_factor)
                rsi_reason += f" (Abaixo de 50, conf confirmada/aumentada para {self.confidence:.2f})"

        self.reasons.append(rsi_reason)

    def _calculate_sma_signal(
        self, short_period: int, long_period: int
    ) -> tuple[SignalType, float, float, float, str]:
        """Return signal based on SMA crossover."""
        sma_short = self.ohlcv_data["close"].rolling(window=short_period).mean()
        sma_long = self.ohlcv_data["close"].rolling(window=long_period).mean()

        sma_short_last = sma_short.iloc[-1]
        sma_long_last = sma_long.iloc[-1]
        sma_short_prev = sma_short.iloc[-2] if len(sma_short) >= 2 else sma_short_last
        sma_long_prev = sma_long.iloc[-2] if len(sma_long) >= 2 else sma_long_last

        signal = SignalType.HOLD
        confidence = 0.5
        reason = "Sem cruzamento de SMA recente"

        if sma_short_last > sma_long_last and sma_short_prev <= sma_long_prev:
            signal = SignalType.BUY
            confidence = 0.75
            reason = (
                f"Cruzamento de SMA: Curta ({short_period}p) "
                f"acima da Longa ({long_period}p)"
            )
        elif sma_short_last < sma_long_last and sma_short_prev >= sma_long_prev:
            signal = SignalType.SELL
            confidence = 0.75
            reason = (
                f"Cruzamento de SMA: Curta ({short_period}p) "
                f"abaixo da Longa ({long_period}p)"
            )

        return signal, confidence, sma_short_last, sma_long_last, reason

    def _compute_decision(self) -> None:
        """
        Calcula o sinal de trading (BUY, SELL, HOLD) e a confiança.
        Esta é a lógica principal da estratégia de scalping.
        YAA: Agora usa self.params para períodos de SMA e modo de teste QPM.
        YAA: Modificado para incluir filtro RSI.
        """
        logger.debug(
            f"ScalpingDecision._compute_decision para {self.symbol} @ {self.timeframe}"
        )
        start_time = time.perf_counter()
        qpm_applied_signal_change = (
            False  # YAA: Flag para rastrear se QPM mudou o sinal
        )

        # Parâmetros de SMA dos self.params, com defaults
        sma_short_period = self.params.get("sma_short_period", 5)
        sma_long_period = self.params.get("sma_long_period", 10)

        # Parâmetros de RSI dos self.params, com defaults
        use_rsi_filter = self.params.get("use_rsi_filter", False)
        rsi_period = self.params.get("rsi_period", 14)
        rsi_oversold_threshold = self.params.get("rsi_oversold_threshold", 30)
        rsi_overbought_threshold = self.params.get("rsi_overbought_threshold", 70)
        # Fatores de modulação de confiança pelo RSI
        rsi_confirmation_factor = self.params.get(
            "rsi_confirmation_factor", 1.1
        )  # Aumenta confiança se RSI confirmar
        rsi_contradiction_factor = self.params.get(
            "rsi_contradiction_factor", 0.5
        )  # Diminui confiança se RSI contradisser

        qpm_test_override = self.params.get("qpm_test_mode_sma_override", False)
        forced_signal = SignalType(
            self.params.get("force_qpm_test_signal_type", SignalType.HOLD)
        )

        if qpm_test_override:
            logger.info(
                f"{BOLD}{YELLOW}[QPM TEST MODE ACTIVE]{ENDC} Forçando sinal "
                f"'{forced_signal}' para {self.symbol} para popular QPM."
            )
            self.signal = forced_signal
            self.confidence = (
                0.99  # Confiança alta para garantir que o trade seja processado
            )
            self.reasons.append(f"QPM Test Mode: Sinal forçado para {forced_signal}")
            # Não calcular SMAs se estivermos forçando o sinal
            strength_value = 0.0
            if self.signal == SignalType.BUY:
                strength_value = 0.99
            elif self.signal == SignalType.SELL:
                strength_value = -0.99
            self.strength = strength_value
            return  # YAA: Sair cedo se o sinal for forçado pelo QPM Test Mode

        if (
            self.ohlcv_data is None
            or self.ohlcv_data.empty
            or len(self.ohlcv_data) < sma_long_period
        ):
            self.signal = SignalType.HOLD
            self.confidence = 0.0
            self.strength = 0.0
            self.reasons.append(
                "Dados insuficientes para calcular SMAs ("
                f"{len(self.ohlcv_data)} < {sma_long_period})"
            )
            logger.warning(
                f"ScalpingDecision: Dados insuficientes para {self.symbol} "
                f"({len(self.ohlcv_data)} candles) para SMAs "
                f"{sma_short_period}/{sma_long_period}."
            )
            return

        try:
            (
                self.signal,
                base_confidence,
                sma_short_last,
                sma_long_last,
                sma_reason,
            ) = self._calculate_sma_signal(sma_short_period, sma_long_period)

            self.confidence = base_confidence
            self.reasons.append(sma_reason)

            qpm_applied_signal_change = self._integrate_qpm_insights(
                qpm_applied_signal_change
            )
            # YAA: Fim da integração QPM Insights

            self._apply_rsi_filter(
                use_rsi_filter,
                rsi_period,
                rsi_oversold_threshold,
                rsi_overbought_threshold,
                rsi_confirmation_factor,
                rsi_contradiction_factor,
            )

            # Calcular força do sinal (exemplo simples)
            if (
                not qpm_applied_signal_change
            ):  # Só calcular força SMA se QPM não mudou o sinal fundamentalmente
                self.strength = (
                    (sma_short_last - sma_long_last) / sma_long_last
                    if sma_long_last != 0
                    else 0.0
                )
                self.strength = round(self.strength * 100, 2)  # Como porcentagem
            else:  # Se QPM mudou o sinal, a força original da SMA não é mais representativa
                # Usar a confiança da QPM como base para a força
                self.strength = self.confidence * 100

            logger.debug(
                f"ScalpingDecision._compute_decision: Sinal={self.signal}, Confiança Final={self.confidence:.2f}, Força={self.strength:.2f} (SMA{sma_short_period}_last={sma_short_last:.2f}, SMA{sma_long_period}_last={sma_long_last:.2f})"
            )
            logger.info(
                f"Decisão para {self.symbol}@{self.timeframe}: {self.signal} com Confiança {self.confidence:.2f}. Razões: {'; '.join(self.reasons)}"
            )

            if self.statsd:
                self.statsd.gauge("scalping_decision.confidence", self.confidence)
                self.statsd.timing(
                    "scalping_decision.compute_ms",
                    (time.perf_counter() - start_time) * 1000,
                )
                if qpm_applied_signal_change:
                    self.statsd.increment("scalping_decision.qpm_override")

        except Exception as e:
            logger.error(
                f"Erro em ScalpingDecision._compute_decision para {self.symbol}: {e}"
            )
            logger.debug(traceback.format_exc())
            self.signal = SignalType.HOLD
            self.confidence = 0.0
            self.strength = 0.0
            self.reasons.append(f"Erro ao calcular decisão: {e}")
            if self.statsd:
                self.statsd.increment("scalping_decision.error")
                self.statsd.gauge("scalping_decision.confidence", self.confidence)
                self.statsd.timing(
                    "scalping_decision.compute_ms",
                    (time.perf_counter() - start_time) * 1000,
                )

    def _compute_order_parameters(self) -> None:
        """
        Calcula stop_loss e take_profit baseados no sinal e risco.
        Define self.stop_loss e self.take_profit.
        YAA: Modificado para usar ATR se disponível e controle de risco dinâmico.
        """
        self.stop_loss = None
        self.take_profit = None

        # YAA: Verificar se há níveis de risco dinâmico disponíveis
        dynamic_risk_levels = None
        if hasattr(self, "dynamic_risk_controller") and self.dynamic_risk_controller:
            try:
                # Tentar obter níveis calibrados dinamicamente
                current_levels = self.dynamic_risk_controller.get_current_risk_levels(
                    self.symbol
                )
                if current_levels:
                    dynamic_risk_levels = current_levels
                    logger.info(
                        f"Usando níveis de risco dinâmicos para {self.symbol}: Regime={current_levels.market_regime}"
                    )
            except Exception as e:
                logger.warning(
                    f"Erro ao obter níveis de risco dinâmico para {self.symbol}: {e}"
                )

        # Se temos níveis dinâmicos, usar eles; caso contrário, usar lógica tradicional
        if dynamic_risk_levels:
            try:
                # Usar níveis calibrados dinamicamente

                if self.signal == SignalType.BUY:
                    self.stop_loss = dynamic_risk_levels.stop_loss_price
                    self.take_profit = dynamic_risk_levels.take_profit_price
                elif self.signal == SignalType.SELL:
                    self.stop_loss = dynamic_risk_levels.stop_loss_price
                    self.take_profit = dynamic_risk_levels.take_profit_price

                self.reasons.append(
                    f"SL/TP dinâmico: {dynamic_risk_levels.adjustment_reason}"
                )

                logger.info(
                    f"Aplicados níveis dinâmicos para {self.symbol}: "
                    f"SL={self.stop_loss:.4f}, TP={self.take_profit:.4f}, "
                    f"ATR={dynamic_risk_levels.atr_value:.4f}"
                )
                return  # Sair da função se conseguimos usar níveis dinâmicos

            except Exception as e:
                logger.error(
                    f"Erro ao aplicar níveis de risco dinâmico para {self.symbol}: {e}"
                )
                # Continuar com lógica tradicional como fallback

        # Lógica tradicional (fallback)
        defaults = _DECISION_DEFAULTS
        atr_period = self.params.get("atr_period", defaults.get("atr_period", 14))
        sl_atr_multiplier = self.params.get(
            "stop_loss_atr_multiplier",
            defaults.get("stop_loss_atr_multiplier", 1.5),
        )
        tp_atr_multiplier = self.params.get(
            "take_profit_atr_multiplier",
            defaults.get("take_profit_atr_multiplier", 2.0),
        )

        current_atr_value = None
        if (
            self.ohlcv_data is not None
            and not self.ohlcv_data.empty
            and len(self.ohlcv_data) >= atr_period
        ):
            try:
                atr_value, sl_dist, tp_dist = calculate_atr_levels(
                    self.ohlcv_data["high"].values,
                    self.ohlcv_data["low"].values,
                    self.ohlcv_data["close"].values,
                    self.current_price,
                    DynamicRiskParameters(
                        atr_period=atr_period,
                        atr_multiplier_base=sl_atr_multiplier,
                        take_profit_base_ratio=tp_atr_multiplier,
                    ),
                )
                current_atr_value = atr_value
                if self.signal == SignalType.BUY:
                    self.stop_loss = self.current_price - sl_dist
                    self.take_profit = self.current_price + tp_dist
                elif self.signal == SignalType.SELL:
                    self.stop_loss = self.current_price + sl_dist
                    self.take_profit = self.current_price - tp_dist
                self.reasons.append(
                    f"SL/TP calculado com ATR({atr_period}):{current_atr_value:.4f}"
                )
            except Exception as e_atr:
                logger.error(f"Erro ao calcular ATR para {self.symbol}: {e_atr}")
                logger.debug(traceback.format_exc())
        else:
            logger.warning(
                f"Dados insuficientes para calcular ATR({atr_period}) para {self.symbol}. Candles: {len(self.ohlcv_data) if self.ohlcv_data is not None else 0}."
            )

        if self.signal != SignalType.HOLD and self.current_price:
            if current_atr_value is not None and current_atr_value > 0:
                # Usar ATR para SL/TP
                self.reasons.append(
                    f"SL/TP calculado com ATR({atr_period}):{current_atr_value:.4f}"
                )
                if self.signal == SignalType.BUY:
                    self.stop_loss = self.current_price - (
                        current_atr_value
                        * sl_atr_multiplier
                        * self.sl_adjustment_factor
                    )
                    self.take_profit = self.current_price + (
                        current_atr_value
                        * tp_atr_multiplier
                        * self.tp_adjustment_factor
                    )
                elif self.signal == SignalType.SELL:
                    self.stop_loss = self.current_price + (
                        current_atr_value
                        * sl_atr_multiplier
                        * self.sl_adjustment_factor
                    )
                    self.take_profit = self.current_price - (
                        current_atr_value
                        * tp_atr_multiplier
                        * self.tp_adjustment_factor
                    )
            else:
                # Fallback para porcentagens fixas se ATR não disponível
                self.reasons.append("SL/TP calculado com % fixas (fallback ATR)")
                base_sl_pct = defaults.get("fallback_stop_pct", 0.005)
                base_tp_pct = defaults.get("fallback_take_profit_pct", 0.01)
                if self.signal == SignalType.BUY:
                    self.stop_loss = self.current_price * (
                        1 - base_sl_pct * self.sl_adjustment_factor
                    )
                    self.take_profit = self.current_price * (
                        1 + base_tp_pct * self.tp_adjustment_factor
                    )
                elif self.signal == SignalType.SELL:
                    self.stop_loss = self.current_price * (
                        1 + base_sl_pct * self.sl_adjustment_factor
                    )
                    self.take_profit = self.current_price * (
                        1 - base_tp_pct * self.tp_adjustment_factor
                    )

            # Evitar SL/TP praticamente iguais ao preço de entrada
            min_offset = self.current_price * 0.0001
            if (
                self.stop_loss is not None
                and abs(self.stop_loss - self.current_price) < min_offset
            ):
                if self.signal == SignalType.BUY:
                    self.stop_loss = self.current_price - min_offset
                elif self.signal == SignalType.SELL:
                    self.stop_loss = self.current_price + min_offset

            if (
                self.take_profit is not None
                and abs(self.take_profit - self.current_price) < min_offset
            ):
                if self.signal == SignalType.BUY:
                    self.take_profit = self.current_price + min_offset
                elif self.signal == SignalType.SELL:
                    self.take_profit = self.current_price - min_offset

        logger.debug(
            f"ScalpingDecision._compute_order_parameters: SL={self.stop_loss}, TP={self.take_profit} para sinal {self.signal} com risk_profile '{self.risk_profile}' (atr_val: {current_atr_value}, adj_val: {self.risk_adjustment}, sl_f: {self.sl_adjustment_factor}, tp_f: {self.tp_adjustment_factor})"
        )

    def to_dict(self) -> Dict[str, Any]:
        """
        Converte os atributos da decisão para um dicionário.
        """
        signal_to_return = str(self.signal)
        # Alinhar com o que o sistema de trading espera ('HOLD')
        if self.signal == "neutral":
            signal_to_return = SignalType.HOLD

        return {
            "signal": signal_to_return,
            "confidence": self.confidence,
            "stop_loss": self.stop_loss,
            "take_profit": self.take_profit,
            # Adicionado para consistência, embora possa ser o current_price
            "entry_price": self.current_price,
            "reasons": self.reasons,
        }

    def compute_signal_details(self, signal: str, confidence: float) -> None:
        """
        Calcula os detalhes do sinal (stop_loss, take_profit) com base em um sinal e confiança fornecidos externamente.
        YAA: Método adicionado para permitir que a estratégia QUALIAEnhanced forneça um sinal calculado com métricas quânticas.
        """
        self.signal = SignalType(signal)
        self.confidence = confidence

        # Armazenar o sinal inicial
        if not hasattr(self, "reasons"):
            self.reasons = []

        self.reasons.append(
            f"Sinal fornecido externamente: {signal} com confiança {confidence:.2f}"
        )

        # Calcular stop_loss e take_profit
        self._compute_order_parameters()

    # Os demais métodos auxiliares (_predict_edge,
    # _determine_market_condition, _extract_quantum_confidence,
    # _compute_decision, _compute_order_parameters) devem ser copiados do
    # original conforme necessário para funcionamento completo.
