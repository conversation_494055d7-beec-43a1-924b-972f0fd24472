import numpy as np
import pytest

pytest.importorskip("pytest_benchmark")

from qualia.core.retrocausality import RetrocausalityOperator


def _extract_past_changes_naive(buffer):
    if len(buffer) < 2:
        return []
    past_data = list(buffer)
    changes = []
    for i in range(1, len(past_data)):
        prev_data, prev_time = past_data[i - 1]
        curr_data, curr_time = past_data[i]
        change = curr_data - prev_data
        change_magnitude = np.linalg.norm(change)
        if change_magnitude > np.std([np.linalg.norm(d[0]) for d in past_data]) * 0.5:
            changes.append((change, curr_time))
    return changes


def _find_correlations_naive(op, past_changes, future_indicators, timestamp):
    correlations = []
    change_magnitudes = [np.linalg.norm(c) for c, _ in past_changes]
    change_times = [t for _, t in past_changes]
    for i, f in enumerate(future_indicators):
        for j, (m, t) in enumerate(zip(change_magnitudes, change_times)):
            time_weight = np.exp(-(timestamp - t) / op.temporal_window)
            strength = abs(f) * m * time_weight
            if strength > op.influence_threshold:
                correlations.append(
                    {
                        "future_indicator_idx": i,
                        "past_change_idx": j,
                        "strength": strength,
                        "future_time": timestamp + op.temporal_window,
                        "past_time": t,
                        "indicator_value": f,
                        "change_magnitude": m,
                    }
                )
    correlations.sort(key=lambda x: x["strength"], reverse=True)
    return correlations[:20]


@pytest.mark.benchmark(group="retrocausal")
def test_extract_past_changes_vectorized(benchmark):
    op = RetrocausalityOperator({})
    for i in range(200):
        op.past_buffer.append((np.random.randn(8), float(i)))
    benchmark(op._extract_past_changes)


@pytest.mark.benchmark(group="retrocausal")
def test_extract_past_changes_naive(benchmark):
    buf = [(np.random.randn(8), float(i)) for i in range(200)]
    benchmark(_extract_past_changes_naive, buf)


@pytest.mark.benchmark(group="retrocausal")
def test_find_correlations_vectorized(benchmark):
    op = RetrocausalityOperator({})
    past_changes = [(np.random.randn(8), float(i)) for i in range(100)]
    future_indicators = np.random.randn(20)
    benchmark(op._find_retrocausal_correlations, past_changes, future_indicators, 0.0)


@pytest.mark.benchmark(group="retrocausal")
def test_find_correlations_naive(benchmark):
    op = RetrocausalityOperator({})
    past_changes = [(np.random.randn(8), float(i)) for i in range(100)]
    future_indicators = np.random.randn(20)
    benchmark(_find_correlations_naive, op, past_changes, future_indicators, 0.0)
