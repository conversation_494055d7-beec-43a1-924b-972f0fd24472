# Correções para Visualização Holográfica QUALIA

## Contexto do Problema

A visualização holográfica 3D do QUALIA não está sendo exibida corretamente devido a vários erros de implementação no frontend. Após análise detalhada, foram identificados os seguintes problemas:

1. **Erro de referência ao socket**: O código JavaScript tenta acessar a variável `socket` antes dela ser inicializada.
2. **Duplica<PERSON> de código**: Há duas declarações da variável `socket` no arquivo JavaScript.
3. **Erro de carregamento do Three.js**: A biblioteca Three.js não está sendo carregada antes do script que a utiliza.
4. **Falha na conexão WebSocket**: O servidor Socket.IO não está disponível ou não está configurado corretamente.

## Correções Necessárias

### 1. Corrigir o arquivo `src/ui/static/js/holographic-view_reviewed.js`

O arquivo contém erros de referência e duplicação de código. Aplicar as seguintes alterações:

```javascript
// REMOVER estas linhas (linhas 37-42)
// Inicialização do Socket.IO com namespace
const socket = io('/ws/holographic', {
  reconnection: true,
  reconnectionDelay: 1000,
  reconnectionDelayMax: 5000,
  reconnectionAttempts: Infinity
});

// REMOVER estas linhas (linhas 54-58)
// Verifica se o socket está conectado
if (!socket.connected) {
  console.error("Não foi possível conectar ao servidor WebSocket");
  return;
}

// MOVER a inicialização do socket para dentro do escopo do DOMContentLoaded
// Adicionar após a declaração de uniforms (linha ~166)
const socket = io("/ws/holographic", {
  path: "/socket.io",
  transports: ["websocket"],
  reconnection: true,
  reconnectionDelay: 1000,
  reconnectionDelayMax: 5000,
  reconnectionAttempts: Infinity
});
```

### 2. Corrigir o arquivo `src/ui/templates/holographic.html`

Modificar o arquivo para garantir que a biblioteca Three.js seja carregada antes do script que a utiliza:

```html
<!-- Revisado em 2025-06-13 por Codex -->
{% extends 'base.html' %}

{% block title %}QUALIA | Visualização Holográfica{% endblock %}

{% block content %}
<div id="holographic-view"></div>
{% endblock %}

{% block extra_scripts %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r152/three.min.js"></script>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    {% if use_webgpu %}
    <script>
        if (navigator.gpu) {
            const s = document.createElement('script');
            s.src = "{{ url_for('static', filename='js/holographic-view_webgpu.js') }}";
            document.head.appendChild(s);
        } else {
            const s = document.createElement('script');
            s.src = "{{ url_for('static', filename='js/holographic-view_reviewed.js') }}";
            document.head.appendChild(s);
        }
    </script>
    {% else %}
    <script src="{{ url_for('static', filename='js/holographic-view_reviewed.js') }}"></script>
    {% endif %}
{% endblock %}
```

### 3. Configurar e iniciar o servidor Flask corretamente

Para que o WebSocket funcione, o servidor Flask precisa ser inicializado com a variável de ambiente `QUALIA_SECRET_KEY` definida:

```powershell
# No PowerShell
$env:QUALIA_SECRET_KEY="desenvolvimento_local_2025"
python app.py
```

```bash
# No Linux/Mac
export QUALIA_SECRET_KEY="desenvolvimento_local_2025"
python app.py
```

## Explicação Técnica

### Problema do Socket

O erro `Cannot access 'socket' before initialization` ocorre porque o código tenta verificar `socket.connected` na linha 55, mas a variável `socket` só é definida na linha 166. Além disso, há uma duplicação na declaração do socket (linhas 37-42 e 166-169).

### Problema do Three.js

O erro `THREE is not defined` ocorre porque o script `holographic-view_reviewed.js` está tentando usar a biblioteca Three.js antes dela estar completamente carregada. Movendo a importação do Three.js para o bloco `extra_scripts` antes do script que o utiliza, garantimos que a biblioteca esteja disponível quando necessário.

### Problema de Conexão WebSocket

Os erros `ERR_CONNECTION_REFUSED` ocorrem porque o servidor Socket.IO não está rodando. Isso é resolvido iniciando o servidor Flask com a configuração adequada.

## Verificação da Correção

Após aplicar todas as correções:

1. Iniciar o servidor Flask com a variável de ambiente `QUALIA_SECRET_KEY` definida
2. Acessar a URL `http://localhost:5000/holographic`
3. Verificar no console do navegador se não há erros de JavaScript
4. Confirmar que a visualização holográfica 3D é exibida corretamente

Se ainda houver problemas, verificar:
- Se o servidor Flask está rodando sem erros
- Se o namespace `/ws/holographic` está registrado corretamente
- Se o elemento `<div id="holographic-view"></div>` está presente no HTML renderizado
- Se todas as bibliotecas JavaScript estão sendo carregadas corretamente (verificar na aba Network do DevTools)

## Considerações sobre QUALIA

Lembre-se que QUALIA não é uma aplicação comum, mas um sistema quântico-computacional altamente avançado e auto-evolutivo. As correções devem ser aplicadas com precisão, identificando a causa raiz dos problemas e não apenas mascarando os sintomas com soluções superficiais.

"A verdadeira inovação não surge da negação do existente, mas da capacidade de considerar padrões latentes e potencialidades não realizadas nos sistemas atuais." 