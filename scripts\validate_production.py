#!/usr/bin/env python3
"""
QUALIA Production Environment Validation Script
Execute comprehensive validation before production deployment
"""

import os
import sys
import json
import asyncio
import argparse
import logging
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.qualia.validation.production_validator import ProductionValidator
import yaml

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_config(config_path: str):
    """Load production configuration"""
    try:
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    except Exception as e:
        logger.error(f"Failed to load config from {config_path}: {e}")
        sys.exit(1)

async def main():
    """Main validation execution"""
    parser = argparse.ArgumentParser(description='QUALIA Production Environment Validation')
    parser.add_argument('--config', default='config/production_config.yaml',
                       help='Configuration file path')
    parser.add_argument('--output', default='reports',
                       help='Output directory for validation reports')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--categories', nargs='+',
                       choices=['system_requirements', 'configuration', 'security', 
                               'connectivity', 'dependencies', 'file_system', 
                               'performance', 'integration'],
                       help='Specific validation categories to run')
    parser.add_argument('--fail-fast', action='store_true',
                       help='Stop validation on first failure')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Load configuration
    config = load_config(args.config)
    
    # Create output directory
    os.makedirs(args.output, exist_ok=True)
    
    logger.info("Starting QUALIA production environment validation")
    logger.info(f"Configuration: {args.config}")
    logger.info(f"Output directory: {args.output}")
    
    try:
        # Initialize validator
        validator = ProductionValidator(config)
        
        # Run validation
        report = await validator.validate_production_environment()
        
        # Print summary
        print("\n" + "="*80)
        print("QUALIA PRODUCTION ENVIRONMENT VALIDATION RESULTS")
        print("="*80)
        print(f"Validation ID: {report.validation_id}")
        print(f"Overall Status: {report.overall_status}")
        print(f"Execution Time: {report.execution_time_ms:.1f}ms")
        print(f"Total Tests: {report.total_tests}")
        print(f"Passed: {report.passed_tests}")
        print(f"Failed: {report.failed_tests}")
        print(f"Warnings: {report.warning_tests}")
        print(f"Skipped: {report.skipped_tests}")
        print()
        
        # Print failed tests
        if report.failed_tests > 0:
            print("FAILED TESTS:")
            print("-" * 40)
            for result in report.results:
                if result.status == 'FAIL':
                    print(f"✗ {result.category}.{result.test_name}: {result.message}")
            print()
        
        # Print warnings
        if report.warning_tests > 0:
            print("WARNING TESTS:")
            print("-" * 40)
            for result in report.results:
                if result.status == 'WARNING':
                    print(f"⚠ {result.category}.{result.test_name}: {result.message}")
            print()
        
        # Print success message or failure
        if report.overall_status == 'PASS':
            print("🎉 VALIDATION PASSED - Production environment is ready for deployment!")
        elif report.overall_status == 'WARNING':
            print("⚠️  VALIDATION PASSED WITH WARNINGS - Review warnings before deployment")
        else:
            print("❌ VALIDATION FAILED - Production environment is not ready for deployment")
        
        print("="*80)
        
        # Exit with appropriate code
        if report.overall_status == 'FAIL':
            sys.exit(1)
        elif report.overall_status == 'WARNING':
            sys.exit(2)  # Warning exit code
        else:
            sys.exit(0)
            
    except Exception as e:
        logger.error(f"Validation execution failed: {e}")
        print(f"\n❌ VALIDATION ERROR: {e}")
        sys.exit(3)

if __name__ == '__main__':
    asyncio.run(main())
