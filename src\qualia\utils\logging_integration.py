"""
Logging Integration for QUALIA System
Integrates structured logging with existing QUALIA components.
"""

import logging
import os
from pathlib import Path
from typing import Dict, Any, Optional

from .structured_logging import (
    configure_structured_logging,
    get_qualia_logger,
    LogContext
)


def setup_qualia_logging_from_config(config: Dict[str, Any]) -> None:
    """
    Setup QUALIA logging from configuration dictionary
    
    Args:
        config: Configuration dictionary with logging settings
    """
    logging_config = config.get('logging', {})
    
    # Extract configuration values
    level = logging_config.get('level', 'INFO')
    structured = logging_config.get('structured', True)
    redact_sensitive = logging_config.get('redact_sensitive', True)
    
    # Console configuration
    console_config = logging_config.get('console', {})
    console_enabled = console_config.get('enabled', True)
    console_format = console_config.get('format', 'structured')
    compact_console = console_config.get('compact', False)
    
    # File configuration
    file_config = logging_config.get('file', {})
    file_enabled = file_config.get('enabled', True)
    file_path = file_config.get('path', 'logs/qualia_structured.log')
    max_file_size = file_config.get('max_size_mb', 100) * 1024 * 1024  # Convert to bytes
    backup_count = file_config.get('backup_count', 10)
    
    # Configure structured logging
    configure_structured_logging(
        level=level,
        console_enabled=console_enabled,
        file_enabled=file_enabled,
        file_path=Path(file_path),
        structured_format=(console_format == 'structured') if console_enabled else True,
        redact_sensitive=redact_sensitive,
        compact_console=compact_console,
        max_file_size=max_file_size,
        backup_count=backup_count
    )
    
    # Log configuration summary
    logger = get_qualia_logger('logging_integration')
    logger.info("🔧 QUALIA structured logging configured")
    logger.info(f"   Level: {level}")
    logger.info(f"   Console: {'enabled' if console_enabled else 'disabled'}")
    logger.info(f"   File: {'enabled' if file_enabled else 'disabled'}")
    logger.info(f"   Sensitive data redaction: {'enabled' if redact_sensitive else 'disabled'}")
    
    if file_enabled:
        logger.info(f"   Log file: {file_path}")
        logger.info(f"   Max size: {max_file_size // (1024*1024)}MB")
        logger.info(f"   Backup count: {backup_count}")


def get_component_logger(component_name: str, 
                        symbol: Optional[str] = None,
                        exchange: Optional[str] = None,
                        operation: Optional[str] = None) -> 'QualiaLogger':
    """
    Get a logger for a specific QUALIA component with context
    
    Args:
        component_name: Name of the component (e.g., 'oracle_engine', 'risk_manager')
        symbol: Trading symbol (optional)
        exchange: Exchange name (optional)
        operation: Current operation (optional)
        
    Returns:
        QualiaLogger instance with context
    """
    context = LogContext(
        component=component_name,
        symbol=symbol,
        exchange=exchange,
        operation=operation
    )
    
    return get_qualia_logger(f"qualia.{component_name}", context)


def get_trading_logger(symbol: str, 
                      exchange: str = "kucoin",
                      operation: Optional[str] = None) -> 'QualiaLogger':
    """
    Get a logger specifically for trading operations
    
    Args:
        symbol: Trading symbol
        exchange: Exchange name
        operation: Trading operation (optional)
        
    Returns:
        QualiaLogger instance with trading context
    """
    context = LogContext(
        component="trading",
        symbol=symbol,
        exchange=exchange,
        operation=operation
    )
    
    return get_qualia_logger("qualia.trading", context)


def get_oracle_logger(symbol: Optional[str] = None,
                     operation: Optional[str] = None) -> 'QualiaLogger':
    """
    Get a logger for Oracle Decision Engine
    
    Args:
        symbol: Trading symbol (optional)
        operation: Current operation (optional)
        
    Returns:
        QualiaLogger instance with oracle context
    """
    context = LogContext(
        component="oracle_engine",
        symbol=symbol,
        operation=operation
    )
    
    return get_qualia_logger("qualia.oracle", context)


def get_risk_logger(symbol: Optional[str] = None,
                   operation: Optional[str] = None) -> 'QualiaLogger':
    """
    Get a logger for Risk Manager
    
    Args:
        symbol: Trading symbol (optional)
        operation: Current operation (optional)
        
    Returns:
        QualiaLogger instance with risk management context
    """
    context = LogContext(
        component="risk_manager",
        symbol=symbol,
        operation=operation
    )
    
    return get_qualia_logger("qualia.risk", context)


def get_data_logger(symbol: Optional[str] = None,
                   exchange: Optional[str] = None,
                   operation: Optional[str] = None) -> 'QualiaLogger':
    """
    Get a logger for Data Collection
    
    Args:
        symbol: Trading symbol (optional)
        exchange: Exchange name (optional)
        operation: Current operation (optional)
        
    Returns:
        QualiaLogger instance with data collection context
    """
    context = LogContext(
        component="data_collector",
        symbol=symbol,
        exchange=exchange,
        operation=operation
    )
    
    return get_qualia_logger("qualia.data", context)


def log_system_startup(config: Dict[str, Any]) -> None:
    """
    Log system startup information
    
    Args:
        config: System configuration
    """
    logger = get_component_logger("system")
    
    logger.info("🚀 QUALIA System Starting")
    logger.info("=" * 50)
    
    # Log key configuration
    if 'environment' in config:
        logger.info(f"Environment: {config['environment']}")
    
    if 'version' in config:
        logger.info(f"Version: {config['version']}")
    
    # Log trading configuration
    trading_config = config.get('trading', {})
    if 'symbols' in trading_config:
        logger.info(f"Trading symbols: {trading_config['symbols']}")
    
    # Log capital information
    capital_config = config.get('capital', {})
    if 'total_capital_usd' in capital_config:
        logger.info(f"Total capital: ${capital_config['total_capital_usd']:,.2f}")
    
    # Log risk management
    risk_config = config.get('risk_management', {})
    if 'risk_profile' in risk_config:
        logger.info(f"Risk profile: {risk_config['risk_profile']}")
    
    logger.info("=" * 50)


def log_system_shutdown(reason: str = "Normal shutdown") -> None:
    """
    Log system shutdown information
    
    Args:
        reason: Reason for shutdown
    """
    logger = get_component_logger("system")
    
    logger.info("🛑 QUALIA System Shutting Down")
    logger.info(f"Reason: {reason}")
    logger.info("=" * 50)


def log_trading_decision(symbol: str,
                        action: str,
                        confidence: float,
                        reasoning: str,
                        additional_data: Optional[Dict[str, Any]] = None) -> None:
    """
    Log trading decision with structured data
    
    Args:
        symbol: Trading symbol
        action: Trading action (BUY, SELL, HOLD)
        confidence: Decision confidence (0.0 to 1.0)
        reasoning: Decision reasoning
        additional_data: Additional structured data
    """
    logger = get_trading_logger(symbol, operation="decision")
    
    log_data = {
        'action': action,
        'confidence': confidence,
        'reasoning': reasoning
    }
    
    if additional_data:
        log_data.update(additional_data)
    
    logger.info(
        f"🎯 TRADING DECISION | {action} | confidence={confidence:.3f} | {reasoning}",
        extra=log_data
    )


def log_risk_assessment(symbol: str,
                       risk_score: float,
                       position_size: float,
                       risk_factors: Dict[str, Any]) -> None:
    """
    Log risk assessment with structured data
    
    Args:
        symbol: Trading symbol
        risk_score: Risk score (0.0 to 1.0)
        position_size: Calculated position size
        risk_factors: Risk factors dictionary
    """
    logger = get_risk_logger(symbol, operation="assessment")
    
    log_data = {
        'risk_score': risk_score,
        'position_size': position_size,
        'risk_factors': risk_factors
    }
    
    logger.info(
        f"🛡️  RISK ASSESSMENT | score={risk_score:.3f} | size={position_size:.4f}",
        extra=log_data
    )


def log_performance_metrics(metrics: Dict[str, Any]) -> None:
    """
    Log performance metrics
    
    Args:
        metrics: Performance metrics dictionary
    """
    logger = get_component_logger("performance")
    
    logger.info("📊 PERFORMANCE METRICS", extra={'metrics': metrics})
    
    # Log key metrics individually for readability
    if 'sharpe_ratio' in metrics:
        logger.info(f"   Sharpe Ratio: {metrics['sharpe_ratio']:.3f}")
    
    if 'total_return' in metrics:
        logger.info(f"   Total Return: {metrics['total_return']:.2%}")
    
    if 'max_drawdown' in metrics:
        logger.info(f"   Max Drawdown: {metrics['max_drawdown']:.2%}")


# Backward compatibility functions
def setup_logging(config: Optional[Dict[str, Any]] = None) -> None:
    """Backward compatibility function for existing code"""
    if config is None:
        config = {}
    
    # Convert old format to new format if needed
    if 'logging' not in config:
        config['logging'] = {
            'level': config.get('level', 'INFO'),
            'structured': config.get('structured', True),
            'console': {'enabled': config.get('console', True)},
            'file': {
                'enabled': config.get('file_enabled', True),
                'path': config.get('file', 'logs/qualia_structured.log')
            }
        }
    
    setup_qualia_logging_from_config(config)


def get_logger(name: str) -> 'QualiaLogger':
    """Backward compatibility function for existing code"""
    return get_qualia_logger(name)
