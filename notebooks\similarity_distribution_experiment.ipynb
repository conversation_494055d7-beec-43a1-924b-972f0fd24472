{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Similarity Distribution Experiment\n", "Demonstrates similarity scores in QuantumPatternMemory for simple inputs."]}, {"cell_type": "code", "metadata": {}, "execution_count": null, "outputs": [], "source": ["from src.qualia.memory.quantum_pattern_memory import QuantumPatternMemory\n", "from src.qualia.common_types import QuantumSignaturePacket\n", "import numpy as np"]}, {"cell_type": "code", "metadata": {}, "execution_count": null, "outputs": [], "source": ["qpm = QuantumPatternMemory(max_memory_size_per_dimension=10, enable_warmstart=False)\n", "vectors = [\n", "    [1.0, 0.0],\n", "    [0.9, 0.1],\n", "    [0.7, 0.3],\n", "    [-1.0, 0.0],\n", "    [0.0, -1.0],\n", "]\n", "for vec in vectors:\n", "    pkt = QuantumSignaturePacket(vector=vec, metrics={})\n", "    qpm.store_pattern(pkt, market_snapshot={}, outcome={})"]}, {"cell_type": "code", "metadata": {}, "execution_count": null, "outputs": [], "source": ["query = QuantumSignaturePacket(vector=[1.0, 0.0], metrics={})\n", "results = qpm.retrieve_similar_patterns(query, top_n=5)\n", "[(r['id'], r['similarity_score']) for r in results]"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}