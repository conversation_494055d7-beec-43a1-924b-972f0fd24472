import argparse
import json
import time
import tracemalloc
import numpy as np
import uuid
import cProfile
import pstats
import io
from typing import Optional

from qualia.core.consciousness import QUALIAConsciousness, UniverseState
from qualia.config.settings import settings

# --- Guardrails de Performance --- (Para CTX-1: _detect_patterns)
DETECT_PATTERNS_LATENCY_P95_MS_GUARDRAIL = 150.0  # ms
DETECT_PATTERNS_HEAP_USAGE_MB_GUARDRAIL = 5.0  # MB


def generate_mock_universe_state(
    dimension: int, trace_id: Optional[str] = None
) -> UniverseState:
    vector = np.random.rand(dimension)
    # Garantir que o vetor não seja nulo para evitar problemas com PCA/SVD se todos os vetores forem iguais
    vector = vector + 1e-9 * np.random.rand(dimension)
    vector = vector / np.linalg.norm(vector)  # Normalizar
    return UniverseState(
        vector=vector.real,
        page_entropy=np.random.uniform(
            0.5, 5.0
        ),  # Entropia aleatória para simular variações
        timestamp=time.time() * 1000,  # epoch ms
        trace_id=trace_id or str(uuid.uuid4()),
    )


def run_benchmark(
    num_iterations: int,
    history_size: int,
    vector_dimension: int,
    output_file: Optional[str] = None,
    profile_output: Optional[str] = None,
):
    print(
        f"Iniciando benchmark para _detect_patterns com {num_iterations} iterações..."
    )
    print(
        f"Tamanho do histórico: {history_size}, Dimensão do vetor: {vector_dimension}"
    )

    # Garantir que vector_dimension seja potência de 2 para n_qubits
    n_qubits_approx = 0
    if vector_dimension > 0:
        if vector_dimension & (vector_dimension - 1) == 0:
            n_qubits_approx = int(np.log2(vector_dimension))
        else:
            n_qubits_approx = int(np.log2(vector_dimension))  # Arredonda para baixo
            actual_dim_for_qualia = 2**n_qubits_approx
            print(
                (
                    f"AVISO: vector_dimension ({vector_dimension}) não é uma potência de 2. "
                    f"QUALIAConsciousness será inicializada com n_qubits={n_qubits_approx} "
                    f"(dimensão teórica {actual_dim_for_qualia}). "
                    f"Os vetores de mock ainda usarão {vector_dimension}D, e PCA em QUALIA os tratará."
                )
            )
    else:
        n_qubits_approx = 1  # Default mínimo
        print(
            f"AVISO: vector_dimension inválida ({vector_dimension}). Usando n_qubits={n_qubits_approx}"
        )

    consciousness = QUALIAConsciousness(
        n_qubits=n_qubits_approx,
        history_maxlen=history_size,
        entropy_sensitivity=0.0000001,  # Valor MUITO baixo para garantir que o trigger ocorra com frequência
        pca_n_components_variance=settings.consciousness_pca_n_components_variance,
        pca_max_components=settings.consciousness_pca_max_components,
        kmeans_k_range=(
            settings.consciousness_kmeans_k_min,
            min(
                settings.consciousness_kmeans_k_max,
                history_size - 1 if history_size > 2 else 2,
            ),
        ),
    )

    latencies = []
    heap_usages = []  # Em MB

    initial_fill_count = max(
        history_size, getattr(consciousness, "kmeans_k_range", (2, 2))[0] + 5
    )
    print(f"Preenchendo histórico inicial com {initial_fill_count} estados...")
    for i in range(initial_fill_count):
        mock_state_trace_id = f"initial_fill_{uuid.uuid4().hex}"
        mock_state = generate_mock_universe_state(vector_dimension, mock_state_trace_id)
        consciousness.current_qast_state["trace_id"] = mock_state_trace_id
        consciousness.record_universe_state(mock_state)
        # Para os primeiros estados, o std_dev da entropia pode ser 0 ou pequeno.
        # O observe_entropy_delta lida com isso internamente para decidir o trigger.
        # A chamada a record_universe_state já aciona o observe_entropy_delta via
        # _detect_patterns (se o trigger interno do _detect_patterns for chamado)
        # No entanto, _detect_patterns é chamado depois. Para o PREENCHIMENTO, precisamos popular o histórico.
        # A lógica de trigger real para _detect_patterns ocorrerá nas iterações de benchmark.

    profiler = None
    if profile_output:
        print(f"Perfilamento habilitado. Saída será salva em: {profile_output}")
        profiler = cProfile.Profile()
        profiler.enable()

    tracemalloc.start()
    actual_runs = 0
    for i in range(num_iterations):
        new_mock_state_trace_id = f"iter_{i}_{uuid.uuid4().hex}"
        new_mock_state = generate_mock_universe_state(
            vector_dimension, new_mock_state_trace_id
        )
        consciousness.current_qast_state["trace_id"] = new_mock_state_trace_id
        consciousness.record_universe_state(new_mock_state)

        start_time = time.perf_counter()
        patterns = consciousness._detect_patterns()
        end_time = time.perf_counter()

        # Heurística para verificar se _detect_patterns realmente executou sua lógica principal:
        # Se retornou patterns (mesmo que vazios, mas não None), ou se o log interno de trigger foi ativado.
        # O trigger está em _detect_patterns. Se ele não dispara, retorna [].
        # Se dispara e não acha clusters, também retorna [].
        # Por segurança, só contamos se `patterns` não for None (nunca deve ser None).
        # O ideal seria _detect_patterns retornar um status, mas para já contamos todas as chamadas.
        if (
            patterns is not None
        ):  # _detect_patterns sempre retorna lista, então isso é mais para controle de fluxo
            latencies.append((end_time - start_time) * 1000)  # ms
            actual_runs += 1
        else:
            # Isso não deve acontecer, _detect_patterns retorna [] se não há trigger ou padrões.
            print(f"AVISO: _detect_patterns retornou None na iteração {i+1}")

        current_heap, peak_heap = tracemalloc.get_traced_memory()
        heap_usages.append(peak_heap / (1024 * 1024))  # MB

        if (i + 1) % 100 == 0 or (
            num_iterations < 100 and (i + 1) % (max(1, num_iterations // 10))
        ) == 0:
            print(
                f"  Iteração {i + 1}/{num_iterations} completa. (Corridas de _detect_patterns: {actual_runs})"
            )

    tracemalloc.stop()

    if profiler:
        profiler.disable()
        print(f"Salvando dados do perfil em {profile_output}...")
        try:
            profiler.dump_stats(profile_output)
            print(f"Dados do perfil salvos em {profile_output}.")
            s = io.StringIO()
            ps = pstats.Stats(profiler, stream=s).sort_stats(pstats.SortKey.CUMULATIVE)
            ps.print_stats(30)
            print("\n--- Top 30 Funções Mais Custosas (cProfile - Cumulativo) ---")
            print(s.getvalue())
        except Exception as e:
            print(f"Erro ao salvar ou processar dados do perfil: {e}")

    if not latencies:
        print(
            "AVISO: Nenhuma latência registrada. _detect_patterns pode não ter "
            "sido disparado ou executado. Verifique a lógica de trigger e "
            "entropy_sensitivity."
        )
        avg_latency, p95_latency = 0, 0
    else:
        avg_latency = np.mean(latencies)
        p95_latency = np.percentile(latencies, 95)

    avg_heap = np.mean(heap_usages) if heap_usages else 0
    peak_heap = np.max(heap_usages) if heap_usages else 0

    print(
        f"\nExecução do benchmark concluída. Total de {actual_runs} chamadas a "
        f"_detect_patterns em {num_iterations} iterações."
    )
    print("\n--- Resultados do Benchmark para _detect_patterns ---")
    print(f"Latência Média: {avg_latency:.3f} ms")
    print(
        f"Latência P95:   {p95_latency:.3f} ms (Guardrail: < {DETECT_PATTERNS_LATENCY_P95_MS_GUARDRAIL} ms)"
    )
    print(f"Uso Médio de Heap (pico por iteração): {avg_heap:.3f} MB")
    print(
        f"Pico de Uso de Heap (total rastreado): {peak_heap:.3f} MB (Guardrail: "
        f"< {DETECT_PATTERNS_HEAP_USAGE_MB_GUARDRAIL} MB)"
    )

    violations = []
    if latencies and p95_latency > DETECT_PATTERNS_LATENCY_P95_MS_GUARDRAIL:
        violations.append(
            f"- Latência P95 ({p95_latency:.3f} ms) excedeu o guardrail ({DETECT_PATTERNS_LATENCY_P95_MS_GUARDRAIL} ms)"
        )
    if heap_usages and peak_heap > DETECT_PATTERNS_HEAP_USAGE_MB_GUARDRAIL:
        violations.append(
            f"- Pico de Uso de Heap ({peak_heap:.3f} MB) excedeu o guardrail ("
            f"{DETECT_PATTERNS_HEAP_USAGE_MB_GUARDRAIL} MB)"
        )

    if violations:
        print("\n--- VIOLAÇÕES DE GUARDRAIL DETECTADAS! ---")
        for v in violations:
            print(v)
    elif actual_runs > 0:
        print("\n--- Todos os guardrails foram atendidos. ---")
    else:
        print(
            "\n--- Benchmark concluído, mas _detect_patterns pode não ter sido executado "
            "(0 corridas efetivas). Guardrails não avaliados. ---"
        )

    results_data = {
        "iterations": num_iterations,
        "actual_detect_pattern_runs": actual_runs,
        "history_size": history_size,
        "vector_dimension": vector_dimension,
        "avg_latency_ms": avg_latency,
        "p95_latency_ms": p95_latency,
        "avg_peak_heap_iter_mb": avg_heap,
        "max_peak_heap_total_mb": peak_heap,
        "guardrails": {
            "detect_patterns_latency_p95_ms": DETECT_PATTERNS_LATENCY_P95_MS_GUARDRAIL,
            "heap_usage_mb": DETECT_PATTERNS_HEAP_USAGE_MB_GUARDRAIL,
        },
    }

    if output_file:
        print(f"Salvando resultados em {output_file}")
        with open(output_file, "w") as f:
            json.dump(results_data, f, indent=4)

    if violations:
        print("\nBenchmark concluído com violações de guardrail.")
    elif actual_runs > 0:
        print("\nBenchmark concluído sem violações de guardrail.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Benchmark para a função _detect_patterns de QUALIAConsciousness."
    )
    parser.add_argument(
        "-n",
        "--num-iterations",
        type=int,
        default=100,
        help="Número de iterações. Para perfilamento, 10-100 é recomendado.",
    )
    parser.add_argument(
        "--history-size",
        type=int,
        default=256,
        help="Tamanho do histórico (maxlen) para QUALIAConsciousness.",
    )
    parser.add_argument(
        "--vector-dimension",
        type=int,
        default=256,
        help="Dimensão dos vetores (idealmente potência de 2).",
    )
    parser.add_argument(
        "-o",
        "--output-file",
        type=str,
        help="Arquivo JSON para salvar os resultados do benchmark.",
    )
    parser.add_argument(
        "--profile-output",
        type=str,
        help="Arquivo para salvar dados cProfile (ex: profile.prof).",
    )

    args = parser.parse_args()

    if args.history_size < 20:
        print("AVISO: history-size < 20. Ajustando para 20.")
        args.history_size = 20
    if args.vector_dimension < 2:
        print("AVISO: vector-dimension < 2. Ajustando para 2.")
        args.vector_dimension = 2

    run_benchmark(
        num_iterations=args.num_iterations,
        history_size=args.history_size,
        vector_dimension=args.vector_dimension,
        output_file=args.output_file,
        profile_output=args.profile_output,
    )
