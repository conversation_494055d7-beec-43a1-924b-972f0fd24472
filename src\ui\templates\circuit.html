<!-- Revisado em 2025-06-13 por Codex -->
{% extends 'base.html' %}

{% block title %}QUALIA | Visualização de Circuito Quântico{% endblock %}

{% block extra_head %}
    <link rel='stylesheet' href='{{ url_for('static', filename='css/circuit.css') }}'>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/7.0.0/d3.min.js"></script>
{% endblock %}

{% block overlay %}
    <div id="quantum-field" class="quantum-field"></div>
{% endblock %}

{% block content %}
    <a href="/" class="quantum-button back-button">← Voltar ao Painel Principal</a>

    <div class="panel">
        <h2>Visualização de Circuito Quântico</h2>
        <div class="panel-content">
            <div class="circuit-container">
                <div class="circuit-controls">
                    <div class="control-group">
                        <label for="visualization-type">Tipo de Visualização:</label>
                        <select id="visualization-type" class="quantum-select">
                            <option value="text">Texto</option>
                            <option value="latex">LaTeX</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label for="circuit-steps">Passos do Circuito:</label>
                        <input type="number" id="circuit-steps" class="quantum-input" value="3" min="1" max="10">
                    </div>

                    <div class="real-circuit-checkbox">
                        <input type="checkbox" id="use-real-quantum-circuit" checked>
                        <label for="use-real-quantum-circuit">Usar Circuito Real (executa trading)</label>
                    </div>

                    <button id="refresh-circuit-btn" class="quantum-button">Visualizar Circuito</button>

                    <button id="export-qasm-btn" class="quantum-button">Exportar QASM</button>
                </div>

                <div id="circuit-visualization-area" class="circuit-visualization-area">
                    <div class="loading-indicator">Selecione as opções e clique em 'Visualizar Circuito'</div>
                </div>

                <div id="circuit-info" class="circuit-info">
                    <h3>Dados do Circuito Quântico</h3>
                    <p>Este painel mostra o circuito quântico real gerado pelo sistema QUALIA durante o processamento de dados de mercado.</p>
                    <p>A visualização do circuito permite auditar as decisões quânticas do sistema e validar sua conformidade com requisitos de compliance.</p>
                    <ul>
                        <li><strong>Circuito Real:</strong> Execute o trading para gerar um circuito autêntico baseado em dados reais de mercado</li>
                        <li><strong>Exportação QASM:</strong> Permite exportar o circuito em formato OpenQASM 3.0 para integração com hardware quântico real</li>
                        <li><strong>Visualização em Texto/LaTeX:</strong> Diferentes formatos para análise e documentação</li>
                    </ul>
                    <p>Os circuitos quânticos são a base das métricas de entropia, coerência e outras medidas quânticas utilizadas nas decisões de trading.</p>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_scripts %}
    <script src="{{ url_for('static', filename='js/quantum-field.js') }}"></script>
    <script src="{{ url_for('static', filename='js/trading-interface.js') }}"></script>
    <script src="{{ url_for('static', filename='js/circuit-interface.js') }}"></script>
{% endblock %}
