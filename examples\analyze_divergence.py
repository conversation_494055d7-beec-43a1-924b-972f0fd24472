#!/usr/bin/env python3
"""
Análise de Divergência entre BTC e ETH na Estratégia COMPOSITE
Investigação das causas da diferença de performance.
"""

import pandas as pd
import numpy as np
import requests
from datetime import datetime, timedelta
import matplotlib.pyplot as plt


class DivergenceAnalyzer:
    """Analisador de divergência entre ativos."""
    
    def __init__(self):
        self.session = requests.Session()
    
    def fetch_data(self, symbol: str, days: int = 90) -> pd.DataFrame:
        """Busca dados históricos."""
        try:
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': symbol.replace('/', ''),
                'interval': '1h',
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index().dropna()
            
            # Adiciona indicadores
            df['returns'] = df['close'].pct_change()
            df['sma_20'] = df['close'].rolling(20).mean()
            df['sma_50'] = df['close'].rolling(50).mean()
            df['rsi'] = self._calculate_rsi(df['close'], 14)
            df['volatility'] = df['returns'].rolling(20).std()
            
            return df
            
        except Exception as e:
            print(f"❌ Erro ao buscar dados para {symbol}: {e}")
            return pd.DataFrame()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calcula RSI."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def analyze_composite_signals(self, df: pd.DataFrame, symbol: str):
        """Analisa os sinais da estratégia COMPOSITE."""
        signals = []
        components = {
            'trend_signals': [],
            'mean_rev_signals': [],
            'momentum_signals': [],
            'rsi_signals': [],
            'vol_adjustments': [],
            'final_signals': []
        }
        
        for i in range(50, len(df)):
            # Componentes da estratégia
            trend_signal = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            mean_rev = -(df['close'].iloc[i] - df['sma_20'].iloc[i]) / df['sma_20'].iloc[i]
            momentum = df['close'].iloc[i-10:i].pct_change().mean()
            rsi_signal = (df['rsi'].iloc[i] - 50) / 50
            vol_adj = 1 / (1 + df['volatility'].iloc[i] * 20)
            
            # Sinal final
            composite_signal = (
                trend_signal * 0.25 +
                mean_rev * 0.25 +
                momentum * 0.35 +
                rsi_signal * 0.15
            ) * vol_adj
            
            # Armazena componentes
            components['trend_signals'].append(trend_signal)
            components['mean_rev_signals'].append(mean_rev)
            components['momentum_signals'].append(momentum)
            components['rsi_signals'].append(rsi_signal)
            components['vol_adjustments'].append(vol_adj)
            components['final_signals'].append(composite_signal)
            
            # Threshold
            if abs(composite_signal) > 0.003:
                signals.append(np.clip(composite_signal * 25, -1, 1))
            else:
                signals.append(0)
        
        return signals, components
    
    def compare_market_characteristics(self, btc_df: pd.DataFrame, eth_df: pd.DataFrame):
        """Compara características dos mercados."""
        print("📊 COMPARAÇÃO DE CARACTERÍSTICAS DOS MERCADOS")
        print("=" * 60)
        
        # Estatísticas básicas
        btc_stats = {
            'volatility_mean': btc_df['volatility'].mean(),
            'volatility_std': btc_df['volatility'].std(),
            'returns_mean': btc_df['returns'].mean(),
            'returns_std': btc_df['returns'].std(),
            'rsi_mean': btc_df['rsi'].mean(),
            'rsi_std': btc_df['rsi'].std(),
            'volume_mean': btc_df['volume'].mean(),
            'price_trend': (btc_df['close'].iloc[-1] - btc_df['close'].iloc[0]) / btc_df['close'].iloc[0]
        }
        
        eth_stats = {
            'volatility_mean': eth_df['volatility'].mean(),
            'volatility_std': eth_df['volatility'].std(),
            'returns_mean': eth_df['returns'].mean(),
            'returns_std': eth_df['returns'].std(),
            'rsi_mean': eth_df['rsi'].mean(),
            'rsi_std': eth_df['rsi'].std(),
            'volume_mean': eth_df['volume'].mean(),
            'price_trend': (eth_df['close'].iloc[-1] - eth_df['close'].iloc[0]) / eth_df['close'].iloc[0]
        }
        
        print(f"\n🟠 BTC Características:")
        print(f"   Volatilidade Média: {btc_stats['volatility_mean']:.4f}")
        print(f"   Retorno Médio: {btc_stats['returns_mean']:.4f}")
        print(f"   RSI Médio: {btc_stats['rsi_mean']:.1f}")
        print(f"   Tendência do Período: {btc_stats['price_trend']:.2%}")
        
        print(f"\n🔵 ETH Características:")
        print(f"   Volatilidade Média: {eth_stats['volatility_mean']:.4f}")
        print(f"   Retorno Médio: {eth_stats['returns_mean']:.4f}")
        print(f"   RSI Médio: {eth_stats['rsi_mean']:.1f}")
        print(f"   Tendência do Período: {eth_stats['price_trend']:.2%}")
        
        print(f"\n🔍 DIFERENÇAS:")
        print(f"   Volatilidade: {(eth_stats['volatility_mean'] - btc_stats['volatility_mean']):.4f}")
        print(f"   Retorno: {(eth_stats['returns_mean'] - btc_stats['returns_mean']):.4f}")
        print(f"   RSI: {(eth_stats['rsi_mean'] - btc_stats['rsi_mean']):.1f}")
        print(f"   Tendência: {(eth_stats['price_trend'] - btc_stats['price_trend']):.2%}")
        
        return btc_stats, eth_stats
    
    def analyze_signal_components(self, btc_components, eth_components):
        """Analisa os componentes dos sinais."""
        print(f"\n🔬 ANÁLISE DOS COMPONENTES DOS SINAIS")
        print("=" * 60)
        
        components_names = ['trend_signals', 'mean_rev_signals', 'momentum_signals', 'rsi_signals', 'vol_adjustments']
        
        for comp_name in components_names:
            btc_mean = np.mean(btc_components[comp_name])
            eth_mean = np.mean(eth_components[comp_name])
            difference = eth_mean - btc_mean
            
            print(f"\n📈 {comp_name.replace('_', ' ').title()}:")
            print(f"   BTC Média: {btc_mean:.4f}")
            print(f"   ETH Média: {eth_mean:.4f}")
            print(f"   Diferença: {difference:.4f} ({'ETH melhor' if difference > 0 else 'BTC melhor'})")
        
        # Análise de sinais ativos
        btc_active = sum(1 for s in btc_components['final_signals'] if abs(s) > 0.003)
        eth_active = sum(1 for s in eth_components['final_signals'] if abs(s) > 0.003)
        
        print(f"\n🎯 SINAIS ATIVOS:")
        print(f"   BTC: {btc_active} sinais ativos")
        print(f"   ETH: {eth_active} sinais ativos")
        print(f"   Diferença: {eth_active - btc_active} sinais")


def run_divergence_analysis():
    """Executa análise completa de divergência."""
    print("🔍 ANÁLISE DE DIVERGÊNCIA BTC vs ETH - ESTRATÉGIA COMPOSITE")
    print("=" * 70)
    
    analyzer = DivergenceAnalyzer()
    
    # Busca dados
    print("📥 Buscando dados...")
    btc_df = analyzer.fetch_data("BTCUSDT", days=90)
    eth_df = analyzer.fetch_data("ETHUSDT", days=90)
    
    if btc_df.empty or eth_df.empty:
        print("❌ Erro ao obter dados")
        return
    
    # Compara características dos mercados
    btc_stats, eth_stats = analyzer.compare_market_characteristics(btc_df, eth_df)
    
    # Analisa sinais da estratégia
    print(f"\n🧠 Analisando sinais da estratégia COMPOSITE...")
    btc_signals, btc_components = analyzer.analyze_composite_signals(btc_df, "BTC")
    eth_signals, eth_components = analyzer.analyze_composite_signals(eth_df, "ETH")
    
    # Analisa componentes
    analyzer.analyze_signal_components(btc_components, eth_components)
    
    # Conclusões
    print(f"\n💡 POSSÍVEIS CAUSAS DA DIVERGÊNCIA:")
    print("=" * 60)
    
    vol_diff = eth_stats['volatility_mean'] - btc_stats['volatility_mean']
    trend_diff = eth_stats['price_trend'] - btc_stats['price_trend']
    
    if abs(vol_diff) > 0.001:
        print(f"   🔸 Volatilidade: ETH {'mais' if vol_diff > 0 else 'menos'} volátil que BTC")
    
    if abs(trend_diff) > 0.05:
        print(f"   🔸 Tendência: ETH teve {'melhor' if trend_diff > 0 else 'pior'} performance no período")
    
    print(f"   🔸 Ajuste de volatilidade pode estar penalizando diferentemente")
    print(f"   🔸 Componentes de momentum podem reagir diferente a cada ativo")
    print(f"   🔸 Estratégia pode ter viés para características específicas do ETH")


if __name__ == "__main__":
    run_divergence_analysis()
