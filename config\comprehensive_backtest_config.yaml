# Configuração Abrangente para Backtest da Estratégia FWH
# Este arquivo define todos os parâmetros para execução de backtest completo

backtest:
  # Período de análise
  start_date: "2024-01-01"
  end_date: "2024-12-31"
  
  # Ativos para teste (símbolos principais)
  symbols:
    - "BTC/USDT"
    - "ETH/USDT"
    - "BNB/USDT"
    - "SOL/USDT"
    - "ADA/USDT"
    - "XRP/USDT"
    - "AVAX/USDT"
    - "LINK/USDT"
  
  # Timeframes para análise multi-temporal
  timeframes:
    - "1m"    # Scalping principal
    - "5m"    # Confirmação
    - "15m"   # Tendência de curto prazo
    - "1h"    # Contexto macro
  
  # Capital e gestão de risco
  capital:
    initial_capital: 10000.0
    max_position_size_pct: 10.0      # Máximo 10% por posição
    max_concurrent_positions: 5       # Máximo 5 posições simultâneas
    risk_per_trade_pct: 2.0          # Máximo 2% de risco por trade
  
  # Custos de transação realistas
  transaction_costs:
    trading_fee_pct: 0.001           # 0.1% fee da Binance
    slippage_bps: 2.0                # 2 basis points de slippage
    latency_ms: 50                   # 50ms de latência
  
  # Configuração de otimização
  optimization:
    enable: true
    method: "bayesian"               # bayesian, grid, random
    trials: 100                      # Número de tentativas
    timeout_hours: 3.0               # Timeout em horas
    parallel_jobs: 4                 # Jobs paralelos
    
    # Espaço de busca para parâmetros FWH (por timeframe)
    parameter_space:
      # Parâmetros globais
      fib_lookback:
        min: 10
        max: 30
        type: "int"

      # Parâmetros específicos por timeframe - 1m
      "1m_hype_threshold":
        min: 0.35
        max: 0.50
        type: "float"
      "1m_wave_min_strength":
        min: 0.25
        max: 0.40
        type: "float"
      "1m_quantum_boost_factor":
        min: 1.0
        max: 1.1
        type: "float"
      "1m_holographic_weight":
        min: 0.3
        max: 0.5
        type: "float"
      "1m_tsvf_validation_threshold":
        min: 0.55
        max: 0.75
        type: "float"

      # Parâmetros específicos por timeframe - 5m
      "5m_hype_threshold":
        min: 0.25
        max: 0.45
        type: "float"
      "5m_wave_min_strength":
        min: 0.20
        max: 0.35
        type: "float"
      "5m_quantum_boost_factor":
        min: 1.0
        max: 1.15
        type: "float"
      "5m_holographic_weight":
        min: 0.4
        max: 0.6
        type: "float"
      "5m_tsvf_validation_threshold":
        min: 0.45
        max: 0.65
        type: "float"

      # Parâmetros específicos por timeframe - 15m
      "15m_hype_threshold":
        min: 0.20
        max: 0.35
        type: "float"
      "15m_wave_min_strength":
        min: 0.15
        max: 0.30
        type: "float"
      "15m_quantum_boost_factor":
        min: 1.05
        max: 1.20
        type: "float"
      "15m_holographic_weight":
        min: 0.5
        max: 0.7
        type: "float"
      "15m_tsvf_validation_threshold":
        min: 0.35
        max: 0.55
        type: "float"

      # Parâmetros específicos por timeframe - 1h
      "1h_hype_threshold":
        min: 0.15
        max: 0.30
        type: "float"
      "1h_wave_min_strength":
        min: 0.10
        max: 0.25
        type: "float"
      "1h_quantum_boost_factor":
        min: 1.08
        max: 1.25
        type: "float"
      "1h_holographic_weight":
        min: 0.6
        max: 0.8
        type: "float"
      "1h_tsvf_validation_threshold":
        min: 0.25
        max: 0.45
        type: "float"
  
  # Validação e robustez
  validation:
    train_test_split: 0.7            # 70% treino, 30% teste
    enable_walk_forward: true        # Walk-forward analysis
    walk_forward_window_days: 30     # Janela de 30 dias
    cross_validation_folds: 5        # 5-fold cross validation
    monte_carlo_runs: 1000           # Simulações Monte Carlo
  
  # Métricas de performance
  metrics:
    primary_metric: "sharpe_ratio"   # Métrica principal para otimização
    
    # Métricas calculadas
    calculate:
      - "sharpe_ratio"
      - "sortino_ratio"
      - "calmar_ratio"
      - "profit_factor"
      - "max_drawdown"
      - "var_95"                     # Value at Risk 95%
      - "cvar_95"                    # Conditional VaR 95%
      - "omega_ratio"
      - "sterling_ratio"
      - "burke_ratio"
  
  # Análise de risco
  risk_analysis:
    enable_stress_testing: true
    stress_scenarios:
      - name: "market_crash"
        description: "Simulação de crash de mercado"
        price_shock_pct: -30
      
      - name: "high_volatility"
        description: "Período de alta volatilidade"
        volatility_multiplier: 3.0
      
      - name: "low_liquidity"
        description: "Baixa liquidez"
        slippage_multiplier: 5.0
    
    # Análise de correlação
    correlation_analysis:
      enable: true
      rolling_window_days: 30
      correlation_threshold: 0.7     # Alerta se correlação > 70%
  
  # Relatórios e visualização
  reporting:
    generate_plots: true
    plot_types:
      - "equity_curve"
      - "drawdown_chart"
      - "returns_distribution"
      - "correlation_matrix"
      - "parameter_sensitivity"
      - "performance_heatmap"
    
    save_detailed_trades: true
    export_formats:
      - "json"
      - "csv"
      - "html"
    
    output_directory: "backtest_results"
    
    # Relatórios automáticos
    auto_reports:
      - "executive_summary"
      - "detailed_analysis"
      - "risk_assessment"
      - "optimization_report"
      - "validation_report"

# Configuração específica da estratégia FWH para backtest
strategy_config:
  # Parâmetros base (serão otimizados)
  base_params:
    fib_lookback: 20
    hype_threshold: 0.15
    wave_min_strength: 0.3
    quantum_boost_factor: 1.1
    holographic_weight: 0.5
    tsvf_validation_threshold: 0.4
    sentiment_cache_ttl: 60
  
  # Configuração de clusters para análise holográfica
  cluster_config:
    enable_cluster_analysis: true
    cluster_relevance_map:
      RetailCluster: ["BTC", "ETH", "DOGE", "PEPE", "TON", "TRX"]
      InstitutionalCluster: ["BTC", "ETH", "SOL", "AVAX", "LINK", "AAVE"]
      MomentumQuant: ["BTC", "ETH", "BNB", "SOL", "XRP", "ADA"]
      DeFiCluster: ["LINK", "AAVE", "ARB", "OP", "SUI", "ENA"]
      LayerOneCluster: ["BTC", "ETH", "SOL", "ADA", "AVAX", "POL"]
      AltcoinCluster: ["XLM", "HBAR", "ONDO", "PEPE", "DOGE", "TRX"]

# Configuração de ambiente
environment:
  # Dados históricos
  data_source: "binance"
  data_quality_checks: true
  min_data_points: 1000            # Mínimo de pontos para backtest válido
  
  # Performance
  enable_caching: true
  cache_directory: "cache/backtest"
  parallel_processing: true
  max_memory_gb: 8.0               # Limite de memória
  
  # Logging
  log_level: "INFO"
  log_file: "logs/comprehensive_backtest.log"
  enable_progress_bar: true
  
  # Notificações
  notifications:
    enable: false
    email: "<EMAIL>"
    webhook_url: null

# Configuração de benchmark
benchmark:
  enable_benchmark: true
  benchmark_symbols:
    - "BTC/USDT"                   # Buy and hold BTC
    - "ETH/USDT"                   # Buy and hold ETH
  
  benchmark_strategies:
    - "buy_and_hold"
    - "simple_moving_average"
    - "rsi_strategy"
  
  comparison_metrics:
    - "sharpe_ratio"
    - "total_return"
    - "max_drawdown"
    - "volatility"
