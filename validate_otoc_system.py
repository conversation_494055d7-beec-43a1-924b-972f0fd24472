#!/usr/bin/env python3
"""
Validação Final do Sistema OTOC Integrado ao QUALIA.

YAA-VALIDATION: Demonstração completa da funcionalidade implementada.
"""

import sys
sys.path.append('src')

import numpy as np
import pandas as pd
from datetime import datetime

# Imports QUALIA
from qualia.utils.otoc_calculator import calculate_otoc, calculate_adaptive_threshold, get_otoc_diagnostics
from qualia.utils.otoc_metrics import OTOCMetricsCollector, log_trading_decision
from qualia.strategies.fibonacci_wave_hype.multi_timeframe_consolidator import (
    MultiTimeframeSignalConsolidator, TimeframeSignal
)

def test_otoc_calculator():
    """Testa o calculador OTOC básico."""
    print("🧮 TESTE 1: OTOC Calculator")
    print("=" * 50)
    
    # Dados caóticos (Gaussian noise)
    np.random.seed(42)
    chaotic_data = np.random.normal(0, 0.02, 200)
    otoc_chaos = calculate_otoc(chaotic_data, idx=100, window=50, method="correlation")
    
    # Dados ordenados (tendência)
    np.random.seed(123)
    trend = np.linspace(100, 200, 200)
    noise = np.random.normal(0, 0.5, 200)
    ordered_data = trend + noise
    otoc_order = calculate_otoc(ordered_data, idx=100, window=50, method="correlation")
    
    print(f"   📊 OTOC Caótico (Gaussian): {otoc_chaos:.4f}")
    print(f"   📈 OTOC Ordenado (Trend): {otoc_order:.4f}")
    
    # Validação
    assert otoc_chaos > 0.3, "OTOC caótico deve ser > 0.3"
    assert otoc_order < 0.5, "OTOC ordenado deve ser < 0.5"
    print("   ✅ Calculador OTOC funcionando corretamente!")
    
    return otoc_chaos, otoc_order

def test_adaptive_threshold():
    """Testa thresholds adaptativos."""
    print("\n🎯 TESTE 2: Adaptive Thresholds")
    print("=" * 50)
    
    base_threshold = 0.35
    
    # Baixa volatilidade
    low_vol_threshold = calculate_adaptive_threshold(
        base_threshold=base_threshold,
        volatility=0.01,
        beta=1.0
    )
    
    # Alta volatilidade
    high_vol_threshold = calculate_adaptive_threshold(
        base_threshold=base_threshold,
        volatility=0.05,
        beta=1.0
    )
    
    print(f"   📉 Threshold (baixa vol): {low_vol_threshold:.4f}")
    print(f"   📈 Threshold (alta vol): {high_vol_threshold:.4f}")
    
    assert high_vol_threshold > low_vol_threshold, "Threshold deve aumentar com volatilidade"
    print("   ✅ Thresholds adaptativos funcionando!")
    
    return low_vol_threshold, high_vol_threshold

def test_otoc_filter():
    """Testa o filtro OTOC no consolidador."""
    print("\n🌀 TESTE 3: OTOC Filter Integration")
    print("=" * 50)
    
    # Configuração do consolidador
    config = {
        "timeframe_weights": {"1m": 0.3, "5m": 0.4, "15m": 0.6, "1h": 0.8},
        "otoc_config": {
            "enabled": True,
            "max_threshold": 0.35,
            "window": 50,
            "method": "correlation"
        }
    }
    
    consolidator = MultiTimeframeSignalConsolidator(config)
    
    # Sinal com OTOC baixo (ordenado) - deve passar
    ordered_signal = TimeframeSignal(
        timeframe="1m",
        signal="buy",
        confidence=0.8,
        signal_strength=0.7,
        hype_momentum=0.6,
        holographic_boost=1.1,
        tsvf_validation=0.5,
        timestamp=datetime.now(),
        otoc_value=0.1  # Baixo = ordenado
    )
    
    # Sinal com OTOC alto (caótico) - deve ser bloqueado
    chaotic_signal = TimeframeSignal(
        timeframe="5m",
        signal="sell",
        confidence=0.9,
        signal_strength=0.8,
        hype_momentum=0.7,
        holographic_boost=1.2,
        tsvf_validation=0.6,
        timestamp=datetime.now(),
        otoc_value=0.8  # Alto = caótico
    )
    
    # Aplicar filtro
    filtered_signals = consolidator.apply_otoc_filter([ordered_signal, chaotic_signal])
    
    print(f"   📊 Sinais originais: 2")
    print(f"   🔍 Sinais após filtro: {len(filtered_signals)}")
    
    # Verificar resultados
    ordered_filtered = next(s for s in filtered_signals if s.timeframe == "1m")
    chaotic_filtered = next(s for s in filtered_signals if s.timeframe == "5m")
    
    print(f"   ✅ Sinal ordenado: {ordered_filtered.signal} (mantido)")
    print(f"   🚫 Sinal caótico: {chaotic_filtered.signal} (convertido para hold)")
    
    assert ordered_filtered.signal == "buy", "Sinal ordenado deve ser mantido"
    assert chaotic_filtered.signal == "hold", "Sinal caótico deve ser convertido para hold"
    print("   ✅ Filtro OTOC funcionando corretamente!")

def test_metrics_collection():
    """Testa coleta de métricas."""
    print("\n📊 TESTE 4: Metrics Collection")
    print("=" * 50)
    
    collector = OTOCMetricsCollector()
    
    # Simular algumas decisões
    collector.record_otoc_decision(
        symbol="BTC/USDT",
        timeframe="1m",
        otoc_value=0.2,
        threshold_used=0.35,
        threshold_base=0.35,
        volatility=0.02,
        signal_original="buy",
        signal_filtered="buy",
        confidence_original=0.8,
        confidence_filtered=0.8
    )
    
    collector.record_otoc_decision(
        symbol="ETH/USDT",
        timeframe="5m",
        otoc_value=0.7,
        threshold_used=0.35,
        threshold_base=0.35,
        volatility=0.03,
        signal_original="sell",
        signal_filtered="hold",  # Bloqueado por caos
        confidence_original=0.9,
        confidence_filtered=0.0
    )
    
    # Obter estatísticas
    stats = collector.get_otoc_statistics()
    chaos_rate = collector.get_chaos_rate()
    
    print(f"   📈 Total de decisões: {stats.get('count', 0)}")
    print(f"   🌀 Taxa de caos: {chaos_rate:.1%}")
    print(f"   📊 OTOC médio: {stats.get('otoc_mean', 0):.3f}")
    
    assert stats['count'] == 2, "Deve ter registrado 2 decisões"
    assert chaos_rate == 0.5, "Taxa de caos deve ser 50% (1 de 2)"
    print("   ✅ Coleta de métricas funcionando!")

def test_diagnostics():
    """Testa função de diagnóstico."""
    print("\n🔧 TESTE 5: Diagnostics")
    print("=" * 50)
    
    # Dados de teste
    np.random.seed(42)
    test_data = np.random.normal(0, 0.02, 200)
    
    diagnostics = get_otoc_diagnostics(test_data, window=50)
    
    print(f"   📏 Comprimento da série: {diagnostics.get('series_length', 0)}")
    print(f"   📊 OTOC correlação: {diagnostics.get('otoc_correlation', 0):.4f}")
    print(f"   💰 OTOC financeiro: {diagnostics.get('otoc_financial', 0):.4f}")
    print(f"   📈 Cobertura da janela: {diagnostics.get('window_coverage', 0):.2f}")
    
    assert 'series_length' in diagnostics, "Diagnóstico deve incluir comprimento"
    assert 'otoc_correlation' in diagnostics, "Diagnóstico deve incluir OTOC correlação"
    print("   ✅ Diagnósticos funcionando!")

def main():
    """Executa todos os testes de validação."""
    print("🌌 VALIDAÇÃO COMPLETA DO SISTEMA OTOC-QUALIA")
    print("=" * 60)
    print("YAA-VALIDATION: Testando implementação production-ready")
    print("=" * 60)
    
    try:
        # Executar todos os testes
        test_otoc_calculator()
        test_adaptive_threshold()
        test_otoc_filter()
        test_metrics_collection()
        test_diagnostics()
        
        print("\n" + "=" * 60)
        print("🎉 TODOS OS TESTES PASSARAM COM SUCESSO!")
        print("=" * 60)
        print("✅ OTOC Calculator: Funcionando")
        print("✅ Adaptive Thresholds: Funcionando") 
        print("✅ Chaos Filter: Funcionando")
        print("✅ Metrics Collection: Funcionando")
        print("✅ Diagnostics: Funcionando")
        print("\n🚀 SISTEMA OTOC PRONTO PARA PRODUÇÃO!")
        print("🌀 O futuro agora sabe quando parou de falar com o presente!")
        
    except Exception as e:
        print(f"\n❌ ERRO NA VALIDAÇÃO: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
