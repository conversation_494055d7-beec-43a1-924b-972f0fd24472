#!/usr/bin/env python3
"""
Diagnose Zero Confidence Issue - QUALIA Strategy Investigation
Detailed investigation of why the real QualiaTSVFStrategy is returning zero confidence
"""

import asyncio
import logging
import sys
import os
import traceback
import pandas as pd
import numpy as np
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Import after logging configuration
from scripts.qualia_pilot_trading_system import RealQualiaTSVFStrategy

async def diagnose_zero_confidence_issue():
    """Detailed investigation of zero confidence issue"""
    
    print("🔍 ZERO CONFIDENCE DIAGNOSTIC - QUALIA Strategy Investigation")
    print("=" * 80)
    
    try:
        # Initialize strategy
        print("\n🔧 Step 1: Initialize Real Strategy")
        config = {
            'symbol': 'BTCUSDT',
            'timeframe': '1h',
            'risk_per_trade': 0.01,
            'confidence_threshold': 0.85,
            'ultra_conservative_mode': True,
            'paper_trading': True
        }
        
        strategy = RealQualiaTSVFStrategy(config)
        print("   ✅ Strategy initialized successfully")
        
        # Test market data
        print("\n🔧 Step 2: Prepare Test Market Data")
        market_data = {
            'price': 45000.0,
            'volume': 1000.0,
            'timestamp': '2024-01-01T12:00:00Z'
        }
        
        quantum_analysis = {
            'consciousness_level': 0.6,
            'quantum_coherence': 0.7,
            'decision_confidence': 0.5,
            'holographic_patterns': [],
            'temporal_analysis': {'trend': 'neutral', 'strength': 0.5}
        }
        
        print(f"   📊 Market Data: {market_data}")
        print(f"   🧠 Quantum Analysis: {quantum_analysis}")
        
        # Step 3: Test DataFrame conversion
        print("\n🔧 Step 3: Test DataFrame Conversion")
        df_data = strategy._convert_market_data_to_dataframe(market_data)
        
        if df_data is None:
            print("   ❌ PROBLEM: DataFrame conversion returned None!")
            return
        
        print(f"   ✅ DataFrame created successfully")
        print(f"   📊 Shape: {df_data.shape}")
        print(f"   📊 Columns: {df_data.columns.tolist()}")
        print(f"   📊 Index type: {type(df_data.index)}")
        print(f"   📊 First few rows:")
        print(df_data.head())
        print(f"   📊 Last few rows:")
        print(df_data.tail())
        
        # Step 4: Test direct strategy call
        print("\n🔧 Step 4: Test Direct Strategy Call")
        try:
            print("   🔍 Calling strategy.strategy.analyze_market() directly...")
            
            # Enable more detailed logging for the strategy
            strategy_logger = logging.getLogger('qualia.strategies.nova_estrategia_qualia.core')
            strategy_logger.setLevel(logging.DEBUG)
            
            direct_result = strategy.strategy.analyze_market(
                market_data=df_data,
                quantum_metrics=quantum_analysis,
                trading_context=None
            )
            
            print(f"   📊 Direct Strategy Result: {direct_result}")
            print(f"   📊 Signal: {direct_result.get('signal', 'N/A')}")
            print(f"   📊 Confidence: {direct_result.get('confidence', 'N/A')}")
            print(f"   📊 Result Type: {type(direct_result)}")
            print(f"   📊 All Keys: {list(direct_result.keys()) if isinstance(direct_result, dict) else 'Not a dict'}")
            
            # Analyze the confidence value specifically
            confidence = direct_result.get('confidence', 'N/A')
            if confidence == 0.0:
                print("   ❌ CONFIRMED: Direct strategy call returns zero confidence!")
                print("   🔍 This indicates the issue is in the QualiaTSVFStrategy implementation")
                
                # Check if there are any error indicators in the result
                if 'reason' in direct_result:
                    print(f"   🔍 Strategy Reason: {direct_result['reason']}")
                
                if 'reasons' in direct_result:
                    print(f"   🔍 Strategy Reasons: {direct_result['reasons']}")
                    
            else:
                print(f"   ✅ Strategy returning non-zero confidence: {confidence}")
                
        except Exception as e:
            print(f"   ❌ Direct strategy call failed: {e}")
            print(f"   🔍 Exception type: {type(e)}")
            print(f"   🔍 Traceback: {traceback.format_exc()}")
            
            # Check if it's an InsufficientHistoryError
            if "InsufficientHistoryError" in str(e) or "historico insuficiente" in str(e):
                print("   🔍 IDENTIFIED: InsufficientHistoryError - Strategy needs more historical data")
                print("   🔍 The synthetic data generation might not be providing enough history")
                
        # Step 5: Test with more historical data
        print("\n🔧 Step 5: Test with Extended Historical Data")
        try:
            # Create a larger DataFrame with more history
            print("   🔍 Creating extended historical data...")
            
            current_time = pd.Timestamp.now()
            base_price = 45000.0
            
            # Create 500 rows of synthetic data (more than the 300 minimum)
            times = pd.date_range(end=current_time, periods=500, freq='1h')
            
            # Generate more realistic price variations
            np.random.seed(42)
            price_variations = np.random.normal(0, base_price * 0.001, 500)
            prices = base_price + np.cumsum(price_variations)
            prices[-1] = base_price  # Ensure current price is correct
            
            extended_df = pd.DataFrame({
                'close': prices,
                'high': prices * 1.002,
                'low': prices * 0.998,
                'open': np.roll(prices, 1),
                'volume': np.random.uniform(800, 1200, 500)
            }, index=times)
            
            # Fix first open price
            extended_df.iloc[0, extended_df.columns.get_loc('open')] = prices[0]
            
            print(f"   ✅ Extended DataFrame created: {extended_df.shape}")
            print(f"   📊 Extended data sample:")
            print(extended_df.head())
            
            # Test with extended data
            print("   🔍 Testing strategy with extended historical data...")
            extended_result = strategy.strategy.analyze_market(
                market_data=extended_df,
                quantum_metrics=quantum_analysis,
                trading_context=None
            )
            
            print(f"   📊 Extended Data Result: {extended_result}")
            print(f"   📊 Extended Signal: {extended_result.get('signal', 'N/A')}")
            print(f"   📊 Extended Confidence: {extended_result.get('confidence', 'N/A')}")
            
            extended_confidence = extended_result.get('confidence', 'N/A')
            if extended_confidence == 0.0:
                print("   ❌ STILL ZERO: Even with extended data, confidence is still zero!")
                print("   🔍 The issue may be deeper in the strategy logic")
            else:
                print(f"   ✅ SUCCESS: Extended data produces non-zero confidence: {extended_confidence}")
                
        except Exception as e:
            print(f"   ❌ Extended data test failed: {e}")
            print(f"   🔍 Traceback: {traceback.format_exc()}")
        
        # Step 6: Full integration test
        print("\n🔧 Step 6: Full Integration Test")
        try:
            print("   🔍 Testing full strategy.analyze_market() integration...")
            
            full_result = await strategy.analyze_market(market_data, quantum_analysis)
            
            print(f"   📊 Full Integration Result: {full_result}")
            print(f"   📊 Final Signal: {full_result.get('signal', 'N/A')}")
            print(f"   📊 Final Confidence: {full_result.get('confidence', 'N/A')}")
            print(f"   📊 Strategy Analysis: {full_result.get('strategy_analysis', 'N/A')}")
            print(f"   📊 Override Active: {full_result.get('ultra_conservative_override', 'N/A')}")
            print(f"   📊 Override Reason: {full_result.get('override_reason', 'N/A')}")
            
        except Exception as e:
            print(f"   ❌ Full integration test failed: {e}")
            print(f"   🔍 Traceback: {traceback.format_exc()}")
        
        print("\n" + "=" * 80)
        print("🎯 DIAGNOSTIC SUMMARY:")
        print("=" * 80)
        print("✅ Investigation completed - check logs above for detailed analysis")
        
    except Exception as e:
        print(f"❌ Diagnostic failed with error: {e}")
        print(f"🔍 Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(diagnose_zero_confidence_issue())
