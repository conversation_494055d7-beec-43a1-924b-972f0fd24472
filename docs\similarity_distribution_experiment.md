# Similarity Distribution Experiment

This notebook (`notebooks/similarity_distribution_experiment.ipynb`) inserts a
few simple vectors into the `QuantumPatternMemory` and retrieves patterns using
one of them as query. The logged similarity distribution illustrates how scores
are concentrated when vectors are very similar.

Run the notebook to observe that vectors such as `[1.0, 0.0]` and `[0.9, 0.1]`
obtain similarity values greater than `0.5`, confirming the updated
normalization strategy.
