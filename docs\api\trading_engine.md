# TradingEngine

O `TradingEngine` é o motor central que orquestra a execução de estratégias, coleta de dados de mercado e gerenciamento de risco. Ele integra as camadas quânticas do QUALIA e provê métodos para iniciar, monitorar e encerrar o ciclo de trading.

## Principais Funções
- `initialize()` – carrega dados iniciais e prepara as dependências sem iniciar as threads.
- `start()` – inicia as threads de coleta, análise e execução de ordens.
- `stop()` – encerra o motor e aguarda a finalização das threads.
- `get_status()` – retorna um dicionário resumindo o estado atual do sistema.
- `get_market_data()` – expõe os dados de mercado coletados.
- `get_positions()` – lista de posições abertas no momento.
- `get_trade_history()` – histórico de negociações realizadas.

## Exemplo de Uso
```python
from qualia.market.trading_engine import TradingEngine
import time

engine = TradingEngine(
    api_key="SUA_API_KEY",
    api_secret="SEU_SECRET",
    symbols=["BTC/USD"],
    initial_capital=5000.0,
    risk_profile="balanced",
    live_mode=False,
)

if engine.initialize():
    engine.start()
    time.sleep(60)  # executa um minuto de ciclos de trading
    engine.stop()
status = engine.get_status()
print(status["performance_metrics"])
```

Este exemplo executa o motor em modo de simulação por um minuto. Para operações reais, defina `live_mode=True` e configure as credenciais da exchange.
