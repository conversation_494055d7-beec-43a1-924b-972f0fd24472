"""KuCoin exchange client wrapper."""

from __future__ import annotations

from typing import Any, Dict, Optional

from .ccxt_client import CCXTExchangeClient

# Removida importação circular
# from ..market.kucoin_integration import KucoinIntegration


class KuCoinClient(CCXTExchangeClient):
    """Concrete client for the KuCoin exchange."""

    integration_cls = None

    def __init__(self, config: Dict[str, Any]) -> None:
        # Garantir que o exchange_id esteja definido
        config["exchange_id"] = "kucoin"
        super().__init__(config)

        # A integração será importada e configurada quando necessária
        self._integration = None

    @property
    def integration(self):
        """Lazy loading da integração para evitar importação circular."""
        if self._integration is None:
            if self.integration_cls is None:
                from ..market.kucoin_integration import KucoinIntegration

                self.integration_cls = KucoinIntegration

            self._integration = self.integration_cls(
                api_key=self.api_key,
                api_secret=self.api_secret,
                password=self.password,
                config=self.config,
            )
        return self._integration

    async def get_order_book(
        self, symbol: str, limit: int = 100
    ) -> Optional[Dict[str, Any]]:
        """Delegates to :class:`CCXTExchangeClient`."""

        return await super().get_order_book(symbol, limit)
