import logging
import pytest
from tests.stub_utils import install_stubs

install_stubs()

from qualia.adaptive_evolution import AdaptiveConsciousnessEvolution


class DummyUniverse:
    def __init__(self, n_qubits: int = 4) -> None:
        self.n_qubits = n_qubits

    def reconfigure(self, **kwargs):
        self.n_qubits = kwargs.get("n_qubits", self.n_qubits)


def test_skip_circuit_mutation_prevents_reconfigure(caplog):
    uni = DummyUniverse()
    ace = AdaptiveConsciousnessEvolution(qualia_universe=uni)
    directives = {
        "skip_circuit_mutation": True,
        "override_on_metacognition": True,
        "qubit_increase": 2,
    }
    with caplog.at_level(logging.INFO):
        assert not ace.assess_and_adapt(
            {"volatility": 0.5, "non_linearity": 0.5}, directives
        )
    assert uni.n_qubits == 4
    assert "skip_circuit_mutation" in caplog.text
