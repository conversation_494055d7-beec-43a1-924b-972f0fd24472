import argparse
import json
import time
import tracemalloc
import numpy as np
import sys

from qualia.core.consciousness import QUALIAConsciousness
from qiskit.quantum_info import Statevector  # YAA: Adicionado para simular entrada

# --- Guardrails de Performance --- (Para CTX-12: C<PERSON>lo <PERSON>mpleto do Córtex)
# Estes são guardrails iniciais e podem precisar de ajuste após as primeiras execuções.
CORTEX_FULL_CYCLE_LATENCY_P95_MS_GUARDRAIL = (
    300.0  # ms (Ex: _detect_patterns < 150ms + _execute_self_reflection < 150ms)
)
CORTEX_FULL_CYCLE_HEAP_USAGE_MB_GUARDRAIL = (
    10.0  # MB (Ex: _detect_patterns < 5MB + _execute_self_reflection < 5MB)
)

# --- Configurações do Benchmark ---
# Configurações do Benchmark
N_QUBITS_BENCH = 8
DIM_BENCH = 2**N_QUBITS_BENCH
HISTORY_SIZE_BENCH = 256  # Tamanho do universe_state_history
N_CYCLES_BENCH = 50  # Número de ciclos de process_qast_cycle a serem medidos
# (menor que N_ITERATIONS_BENCH para _detect_patterns para manter o tempo de
# execução razoável)
N_INITIAL_HISTORY_POPULATION = (
    HISTORY_SIZE_BENCH  # Número de estados para popular o histórico inicialmente
)


def make_fake_qiskit_statevector(dimension: int) -> Statevector:
    """Cria um Statevector qiskit fake para entrada do ciclo QAST."""
    vec = np.random.rand(dimension) + 1j * np.random.rand(dimension)
    vec /= np.linalg.norm(vec)
    return Statevector(vec)


def run_full_cycle_benchmark(output_file: str = None):
    print(
        f"Iniciando benchmark do Ciclo Completo do Córtex (CTX-12) com {N_CYCLES_BENCH} ciclos..."
    )
    print(
        f"Configurações: {N_QUBITS_BENCH} qubits, Dimensão Vetor: {DIM_BENCH}, Histórico: {HISTORY_SIZE_BENCH}"
    )

    consciousness = QUALIAConsciousness(
        n_qubits=N_QUBITS_BENCH,
        history_maxlen=HISTORY_SIZE_BENCH,
        entropy_sensitivity=0.01,  # Sensibilidade baixa para facilitar trigger de _detect_patterns
        self_reflection_enabled=True,  # Garantir que _execute_self_reflection seja chamado
    )
    # consciousness.current_qast_state já é inicializado no __init__ de QUALIAConsciousness
    # e o trace_id é gerado por ciclo em process_qast_cycle

    # 1. Popular o histórico inicial para que _detect_patterns tenha dados suficientes
    #    e para que a entropia possa ser calculada e variar.
    #    A função process_qast_cycle irá adicionar ao history via record_universe_state
    #    então não precisamos popular diretamente o consciousness.universe_state_history aqui.
    #    Em vez disso, vamos rodar alguns ciclos "preparatórios".
    print(
        f"Executando {N_INITIAL_HISTORY_POPULATION} ciclos preparatórios para popular o histórico..."
    )
    for i in range(N_INITIAL_HISTORY_POPULATION):
        sv_initial = make_fake_qiskit_statevector(DIM_BENCH)
        # Para os ciclos preparatórios, não queremos que a entropia seja completamente aleatória,
        # mas que tenha alguma variação para que o std_dev não seja zero.
        # O cálculo da entropia é feito dentro de process_qast_cycle a partir do statevector.
        # A lógica de forçar trigger no script original era para _detect_patterns isolado.
        # Aqui, a dinâmica natural do process_qast_cycle deve ser observada.
        # Para garantir que _detect_patterns seja chamado, precisamos de variação de entropia.
        # O Statevector em si já tem aleatoriedade que levará a diferentes entropias.
        _ = consciousness.process_qast_cycle(statevector_from_universe=sv_initial)
        if (i + 1) % (
            N_INITIAL_HISTORY_POPULATION // 10
            if N_INITIAL_HISTORY_POPULATION >= 10
            else 1
        ) == 0:
            print(
                f"  Ciclo preparatório {i+1}/{N_INITIAL_HISTORY_POPULATION} completo."
            )
    print(
        f"Histórico inicial populado com {len(consciousness.universe_state_history)} estados."
    )

    latencies_ns = []
    heap_usages_bytes = []

    # Medição principal
    print(f"Executando e medindo {N_CYCLES_BENCH} ciclos de process_qast_cycle...")
    for i in range(N_CYCLES_BENCH):
        sv = make_fake_qiskit_statevector(DIM_BENCH)

        # Para garantir que _detect_patterns seja mais provável de ser acionado e, portanto,
        # _execute_self_reflection também, podemos introduzir um "choque" de entropia
        # artificialmente nos dados do statevector ANTES de passá-lo,
        # ou confiar na aleatoriedade dos statevectors gerados.
        # Por ora, vamos confiar na aleatoriedade e na sensibilidade configurada.
        # Se _detect_patterns não for acionado consistentemente, esta parte pode ser revisada.

        tracemalloc.start()
        start_time_ns = time.perf_counter_ns()

        # Executa a função alvo: o ciclo QAST completo
        cycle_result = consciousness.process_qast_cycle(statevector_from_universe=sv)

        end_time_ns = time.perf_counter_ns()
        current_heap, peak_heap = tracemalloc.get_traced_memory()
        tracemalloc.stop()

        latencies_ns.append(end_time_ns - start_time_ns)
        heap_usages_bytes.append(peak_heap)

        detected_patterns_in_cycle = cycle_result.get("patterns_detected", [])
        self_reflection_output = cycle_result.get("self_reflection_output", {})
        num_added_rules = len(self_reflection_output.get("added_rules", []))
        num_updated_rules = len(self_reflection_output.get("updated_rules", []))

        if (i + 1) % (N_CYCLES_BENCH // 10 if N_CYCLES_BENCH >= 10 else 1) == 0:
            print(
                f"  Ciclo medido {i+1}/{N_CYCLES_BENCH} completo. Padrões:"
                f" {len(detected_patterns_in_cycle)}, Regras Add: {num_added_rules},"
                f" Upd: {num_updated_rules}"
            )

    print("Execução do benchmark do ciclo completo concluída.")

    # Calcular métricas
    latencies_ms = [ns / 1_000_000 for ns in latencies_ns]
    avg_latency_ms = np.mean(latencies_ms) if latencies_ms else 0
    p95_latency_ms = np.percentile(latencies_ms, 95) if latencies_ms else 0

    heap_usages_mb = [b / (1024 * 1024) for b in heap_usages_bytes]
    avg_heap_mb = np.mean(heap_usages_mb) if heap_usages_mb else 0
    peak_heap_mb = np.max(heap_usages_mb) if heap_usages_mb else 0

    results = {
        "benchmark_target": "QUALIAConsciousness.process_qast_cycle",
        "cycles_measured": N_CYCLES_BENCH,
        "initial_history_population_cycles": N_INITIAL_HISTORY_POPULATION,
        "history_maxlen_config": HISTORY_SIZE_BENCH,
        "n_qubits_config": N_QUBITS_BENCH,
        "vector_dimension": DIM_BENCH,
        "avg_latency_ms": avg_latency_ms,
        "p95_latency_ms": p95_latency_ms,
        "avg_heap_mb": avg_heap_mb,
        "peak_heap_mb": peak_heap_mb,
        "guardrails": {
            "cortex_full_cycle_latency_p95_ms": CORTEX_FULL_CYCLE_LATENCY_P95_MS_GUARDRAIL,
            "cortex_full_cycle_heap_usage_mb": CORTEX_FULL_CYCLE_HEAP_USAGE_MB_GUARDRAIL,
        },
    }

    print(
        "\n--- Resultados do Benchmark para o Ciclo Completo do Córtex (process_qast_cycle) ---"
    )
    print(f"Latência Média: {avg_latency_ms:.3f} ms")
    print(
        f"Latência P95:   {p95_latency_ms:.3f} ms (Guardrail: < {CORTEX_FULL_CYCLE_LATENCY_P95_MS_GUARDRAIL} ms)"
    )
    print(f"Uso Médio de Heap: {avg_heap_mb:.3f} MB")
    print(
        f"Pico de Uso de Heap: {peak_heap_mb:.3f} MB (Guardrail: < {CORTEX_FULL_CYCLE_HEAP_USAGE_MB_GUARDRAIL} MB)"
    )

    violations = []
    if p95_latency_ms > CORTEX_FULL_CYCLE_LATENCY_P95_MS_GUARDRAIL:
        violations.append(
            f"Latência P95 do ciclo completo ({p95_latency_ms:.3f} ms) excedeu o "
            f"guardrail ({CORTEX_FULL_CYCLE_LATENCY_P95_MS_GUARDRAIL} ms)"
        )
    if peak_heap_mb > CORTEX_FULL_CYCLE_HEAP_USAGE_MB_GUARDRAIL:
        violations.append(
            f"Pico de Heap do ciclo completo ({peak_heap_mb:.3f} MB) excedeu o "
            f"guardrail ({CORTEX_FULL_CYCLE_HEAP_USAGE_MB_GUARDRAIL} MB)"
        )

    if violations:
        print("\n--- VIOLAÇÕES DE GUARDRAIL DETECTADAS! ---")
        for v in violations:
            print(f"- {v}")
        exit_code = 1
    else:
        print(
            "\n--- Todos os guardrails de performance para o ciclo completo foram atendidos. ---"
        )
        exit_code = 0

    if output_file:
        print(f"Salvando resultados em {output_file}")
        with open(output_file, "w") as f:
            json.dump(results, f, indent=4)

    return exit_code


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Executa o benchmark para o ciclo completo de QUALIAConsciousness.process_qast_cycle."
    )
    parser.add_argument(
        "--output-file",
        type=str,
        help="Caminho para o arquivo JSON onde os resultados do benchmark serão salvos.",
        default="cortex_full_cycle_benchmark_results.json",  # Default output file
    )
    args = parser.parse_args()

    sys.exit(run_full_cycle_benchmark(output_file=args.output_file))
