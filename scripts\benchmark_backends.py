import csv
import os
import time
from pathlib import Path
from typing import Dict, List

from qiskit import QuantumCircuit, transpile
from qiskit_aer import AerSimulator
from qiskit_aer.noise import NoiseModel, depolarizing_error

from qualia.metrics import quantum_fidelity
from qualia.utils.backend import safe_get_backend
from qualia.core.io_utils import run_on_hardware


def create_complex_circuit() -> QuantumCircuit:
    """Return a 4-qubit circuit combining entanglement and rotations."""
    qc = QuantumCircuit(4, 4)
    qc.h(range(4))
    qc.cx(0, 1)
    qc.cx(1, 2)
    qc.cx(2, 3)
    for idx in range(4):
        qc.rz(0.2 * (idx + 1), idx)
        qc.ry(0.1 * (idx + 1), idx)
    qc.cx(0, 2)
    qc.cx(1, 3)
    qc.measure(range(4), range(4))
    return qc


def _classical_fidelity(
    counts_exp: Dict[str, int], counts_ref: Dict[str, int]
) -> float:
    total_exp = sum(counts_exp.values()) or 1
    total_ref = sum(counts_ref.values()) or 1
    prob_fid = 0.0
    outcomes = set(counts_exp) | set(counts_ref)
    for out in outcomes:
        p = counts_exp.get(out, 0) / total_exp
        q = counts_ref.get(out, 0) / total_ref
        prob_fid += (p**0.5) * (q**0.5)
    return prob_fid**2


def _run_simulator_statevector(circuit: QuantumCircuit) -> Dict[str, float]:
    backend = safe_get_backend("aer_simulator_statevector")
    start = time.perf_counter()
    transpiled = transpile(
        circuit.remove_final_measurements(inplace=False),
        backend=backend,
        optimization_level=3,
    )
    result = backend.run(transpiled).result()
    state = result.get_statevector(transpiled)
    run_time = time.perf_counter() - start
    fidelity = quantum_fidelity(state, state)
    return {
        "backend": "aer_statevector",
        "fidelity": fidelity,
        "run_time": run_time,
        "final_depth": transpiled.depth(),
    }


def _run_simulator_noisy(
    circuit: QuantumCircuit, ideal_counts: Dict[str, int]
) -> Dict[str, float]:
    backend = AerSimulator()
    noise_model = NoiseModel()
    error = depolarizing_error(0.01, 1)
    noise_model.add_all_qubit_quantum_error(error, ["u1", "u2", "u3", "cx"])
    start = time.perf_counter()
    transpiled = transpile(circuit, backend=backend, optimization_level=3)
    result = backend.run(transpiled, noise_model=noise_model, shots=1024).result()
    counts = result.get_counts(transpiled)
    run_time = time.perf_counter() - start
    fidelity = _classical_fidelity(counts, ideal_counts)
    return {
        "backend": "aer_noisy",
        "fidelity": fidelity,
        "run_time": run_time,
        "final_depth": transpiled.depth(),
    }


def _run_hardware(
    back_name: str, circuit: QuantumCircuit, ideal_counts: Dict[str, int]
) -> Dict[str, float]:
    backend = safe_get_backend(back_name)
    if backend is None:
        return {}
    start = time.perf_counter()
    counts = run_on_hardware(circuit, backend, shots=1024)
    transpiled = transpile(circuit, backend=backend, optimization_level=3)
    run_time = time.perf_counter() - start
    fidelity = _classical_fidelity(counts, ideal_counts)
    return {
        "backend": back_name,
        "fidelity": fidelity,
        "run_time": run_time,
        "final_depth": transpiled.depth(),
    }


def main(output: Path) -> None:
    circuit = create_complex_circuit()
    ideal_backend = safe_get_backend("aer_simulator")
    ideal_result = ideal_backend.run(
        transpile(circuit, backend=ideal_backend, optimization_level=3), shots=1024
    ).result()
    ideal_counts = ideal_result.get_counts()

    rows: List[Dict[str, float]] = []
    rows.append(_run_simulator_statevector(circuit))
    rows.append(_run_simulator_noisy(circuit, ideal_counts))

    backends_env = os.getenv("IO_BACKENDS")
    if backends_env:
        for name in backends_env.split(","):
            name = name.strip()
            if not name:
                continue
            result = _run_hardware(name, circuit, ideal_counts)
            if result:
                rows.append(result)

    output.parent.mkdir(parents=True, exist_ok=True)
    with output.open("w", newline="") as fh:
        writer = csv.DictWriter(
            fh, fieldnames=["backend", "fidelity", "run_time", "final_depth"]
        )
        writer.writeheader()
        for row in rows:
            writer.writerow(row)


if __name__ == "__main__":
    import sys

    path = Path("data/benchmarks/benchmark_backends.csv")
    if len(sys.argv) > 1:
        path = Path(sys.argv[1])
    main(path)
