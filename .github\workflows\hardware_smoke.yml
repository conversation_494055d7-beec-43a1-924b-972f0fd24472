name: Hardware Smoke Test

on:
  workflow_dispatch:
  pull_request:
    branches: [ main, develop ]

jobs:
  hardware-smoke:
    runs-on: ubuntu-latest
    if: env.IBMQ_TOKEN != ''
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install dependencies
      run: |
        ./scripts/install_test_deps.sh
        pip install -e .[dev]
        pip install qiskit qiskit-ibm-provider

      - name: Run hardware smoke test
        env:
          IBMQ_TOKEN: ${{ secrets.IBMQ_TOKEN }}
        run: |
          python scripts/reproduce_high_diversity.py --fixed-seed --shots 32 --backend-name ibm_oslo
