#!/usr/bin/env python3
"""
QUALIA A/B Testing Framework - D-07.6 Integration Test Suite

Suite completa de testes de integração que demonstra o workflow completo
do framework A/B Testing integrado com todos os componentes do QUALIA.

Executa:
1. Testes de integração de componentes
2. Testes end-to-end completos
3. Testes de performance e estabilidade
4. Validação de produção
"""

import asyncio
import sys
import logging
from pathlib import Path
from datetime import datetime
import json
import tempfile

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.ab_testing.system_integration import QualiaABTestingIntegration, SystemIntegrationConfig
from qualia.ab_testing.end_to_end_testing import EndToEndTestFramework
from qualia.ab_testing.production_logging import ProductionLogger, ProductionErrorHandler, ErrorSeverity

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)


class D07_6_IntegrationTester:
    """Tester principal para D-07.6 System Integration & Testing."""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp(prefix="d07_6_integration_")
        self.production_logger = ProductionLogger(
            log_dir=f"{self.temp_dir}/logs",
            enable_console=True,
            log_level=logging.INFO
        )
        self.error_handler = ProductionErrorHandler(self.production_logger)
        self.test_results = []
        
        logger.info(f"🧪 D07_6_IntegrationTester inicializado (temp: {self.temp_dir})")
    
    async def run_complete_integration_tests(self) -> dict:
        """Executa suite completa de testes de integração."""
        logger.info("🚀 Iniciando testes completos de integração D-07.6...")
        
        start_time = datetime.now()
        
        # Lista de testes de integração
        integration_tests = [
            ("Component Integration", self._test_component_integration),
            ("End-to-End Workflow", self._test_end_to_end_workflow),
            ("Production Logging", self._test_production_logging),
            ("Error Handling", self._test_error_handling),
            ("Performance & Stability", self._test_performance_stability),
            ("Production Readiness", self._test_production_readiness)
        ]
        
        passed_tests = 0
        failed_tests = 0
        
        for test_name, test_func in integration_tests:
            try:
                logger.info(f"🧪 Executando: {test_name}")
                result = await test_func()
                
                if result["success"]:
                    logger.info(f"✅ {test_name}: PASSOU - {result['details']}")
                    passed_tests += 1
                else:
                    logger.error(f"❌ {test_name}: FALHOU - {result['details']}")
                    failed_tests += 1
                
                self.test_results.append({
                    "test_name": test_name,
                    "success": result["success"],
                    "details": result["details"],
                    "metrics": result.get("metrics", {}),
                    "duration_seconds": result.get("duration_seconds", 0),
                    "timestamp": datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"💥 {test_name}: ERRO CRÍTICO - {e}")
                failed_tests += 1
                self.test_results.append({
                    "test_name": test_name,
                    "success": False,
                    "details": f"Erro crítico: {e}",
                    "metrics": {},
                    "duration_seconds": 0,
                    "timestamp": datetime.now().isoformat()
                })
        
        total_duration = (datetime.now() - start_time).total_seconds()
        success_rate = (passed_tests / len(integration_tests)) * 100
        
        # Gerar relatório final
        final_report = {
            "test_suite": "D-07.6 System Integration & Testing",
            "total_tests": len(integration_tests),
            "passed": passed_tests,
            "failed": failed_tests,
            "success_rate": success_rate,
            "total_duration_seconds": total_duration,
            "timestamp": datetime.now().isoformat(),
            "test_results": self.test_results,
            "production_ready": success_rate >= 80.0,  # 80% mínimo para produção
            "temp_directory": self.temp_dir
        }
        
        logger.info(f"📊 Testes de integração finalizados: {passed_tests}/{len(integration_tests)} passaram ({success_rate:.1f}%)")
        
        return final_report
    
    async def _test_component_integration(self) -> dict:
        """Testa integração entre componentes principais."""
        start_time = datetime.now()
        
        try:
            # Configurar integração
            config = SystemIntegrationConfig(
                ab_test_duration_hours=0.1,  # 6 minutos para teste
                ab_test_symbols=["BTC/USDT"],
                monitoring_interval_seconds=5,
                report_storage_path=f"{self.temp_dir}/reports"
            )
            
            integration = QualiaABTestingIntegration(config)
            
            # Testar inicialização
            init_success = await integration.initialize()
            
            if not init_success:
                return {
                    "success": False,
                    "details": "Falha na inicialização da integração",
                    "metrics": {},
                    "duration_seconds": (datetime.now() - start_time).total_seconds()
                }
            
            # Verificar status dos componentes
            status = integration.get_integration_status()
            
            components_ok = all([
                status["is_initialized"],
                status["components"]["ab_testing"]
            ])
            
            # Parar integração
            await integration.stop_integration()
            
            return {
                "success": components_ok,
                "details": f"Componentes integrados: {status['components']}",
                "metrics": {
                    "components_status": status["components"],
                    "initialization_time": (datetime.now() - start_time).total_seconds()
                },
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
            
        except Exception as e:
            return {
                "success": False,
                "details": f"Erro na integração de componentes: {e}",
                "metrics": {},
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
    
    async def _test_end_to_end_workflow(self) -> dict:
        """Testa workflow completo end-to-end."""
        start_time = datetime.now()
        
        try:
            # Criar framework de teste E2E
            e2e_framework = EndToEndTestFramework()
            
            # Executar suite completa
            e2e_results = await e2e_framework.run_complete_test_suite()
            
            success = e2e_results["success_rate"] >= 70.0  # 70% mínimo para E2E
            
            return {
                "success": success,
                "details": f"E2E Suite: {e2e_results['passed']}/{e2e_results['total_tests']} passaram ({e2e_results['success_rate']:.1f}%)",
                "metrics": {
                    "e2e_success_rate": e2e_results["success_rate"],
                    "e2e_total_tests": e2e_results["total_tests"],
                    "e2e_passed": e2e_results["passed"],
                    "e2e_failed": e2e_results["failed"],
                    "e2e_duration": e2e_results["total_duration_seconds"]
                },
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
            
        except Exception as e:
            return {
                "success": False,
                "details": f"Erro no workflow E2E: {e}",
                "metrics": {},
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
    
    async def _test_production_logging(self) -> dict:
        """Testa sistema de logging de produção."""
        start_time = datetime.now()
        
        try:
            # Testar diferentes níveis de log
            self.production_logger.trace("Teste de trace log", {"test": "trace"})
            self.production_logger.debug("Teste de debug log", {"test": "debug"})
            self.production_logger.info("Teste de info log", {"test": "info"})
            self.production_logger.warning("Teste de warning log", {"test": "warning"})
            
            # Testar contexto de correlação
            self.production_logger.set_correlation_context(test_id="integration_test_123")
            self.production_logger.info("Log com contexto de correlação")
            self.production_logger.clear_correlation_context()
            
            # Verificar se arquivos de log foram criados
            log_dir = Path(f"{self.temp_dir}/logs")
            log_files = list(log_dir.glob("*.log"))
            
            success = len(log_files) >= 2  # Pelo menos log principal e de erros
            
            return {
                "success": success,
                "details": f"Arquivos de log criados: {len(log_files)}",
                "metrics": {
                    "log_files_created": len(log_files),
                    "log_directory": str(log_dir),
                    "log_files": [f.name for f in log_files]
                },
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
            
        except Exception as e:
            return {
                "success": False,
                "details": f"Erro no sistema de logging: {e}",
                "metrics": {},
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
    
    async def _test_error_handling(self) -> dict:
        """Testa sistema de tratamento de erros."""
        start_time = datetime.now()
        
        try:
            # Registrar estratégia de recovery mock
            async def mock_recovery_strategy(error_context):
                return True  # Simula recovery bem-sucedido
            
            self.error_handler.register_recovery_strategy("ValueError", mock_recovery_strategy)
            
            # Simular diferentes tipos de erro
            test_errors = [
                (ValueError("Erro de teste 1"), ErrorSeverity.LOW),
                (RuntimeError("Erro de teste 2"), ErrorSeverity.MEDIUM),
                (ConnectionError("Erro de teste 3"), ErrorSeverity.HIGH)
            ]
            
            error_contexts = []
            for error, severity in test_errors:
                context = await self.error_handler.handle_error(
                    component="test_component",
                    operation="test_operation",
                    error=error,
                    severity=severity
                )
                error_contexts.append(context)
            
            # Verificar estatísticas
            stats = self.error_handler.get_error_statistics()
            
            success = (
                stats["total_errors"] == len(test_errors) and
                stats["recovery_success_rate"] > 0  # Pelo menos um recovery
            )
            
            return {
                "success": success,
                "details": f"Erros processados: {stats['total_errors']}, Recovery rate: {stats['recovery_success_rate']:.1f}%",
                "metrics": {
                    "total_errors_processed": stats["total_errors"],
                    "recovery_success_rate": stats["recovery_success_rate"],
                    "circuit_breakers": stats["circuit_breakers"],
                    "most_common_errors": stats["most_common_errors"]
                },
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
            
        except Exception as e:
            return {
                "success": False,
                "details": f"Erro no sistema de error handling: {e}",
                "metrics": {},
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
    
    async def _test_performance_stability(self) -> dict:
        """Testa performance e estabilidade do sistema."""
        start_time = datetime.now()
        
        try:
            # Teste de carga básico
            tasks = []
            for i in range(10):  # 10 operações concorrentes
                task = asyncio.create_task(self._simulate_ab_test_operation(i))
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Analisar resultados
            successful_operations = sum(1 for r in results if not isinstance(r, Exception))
            failed_operations = len(results) - successful_operations
            
            success_rate = (successful_operations / len(results)) * 100
            success = success_rate >= 80.0  # 80% mínimo
            
            return {
                "success": success,
                "details": f"Operações concorrentes: {successful_operations}/{len(results)} bem-sucedidas ({success_rate:.1f}%)",
                "metrics": {
                    "concurrent_operations": len(results),
                    "successful_operations": successful_operations,
                    "failed_operations": failed_operations,
                    "success_rate": success_rate,
                    "total_test_duration": (datetime.now() - start_time).total_seconds()
                },
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
            
        except Exception as e:
            return {
                "success": False,
                "details": f"Erro no teste de performance: {e}",
                "metrics": {},
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
    
    async def _simulate_ab_test_operation(self, operation_id: int) -> bool:
        """Simula operação de A/B testing para teste de carga."""
        try:
            # Simular operação que demora um tempo variável
            await asyncio.sleep(0.1 + (operation_id % 3) * 0.05)
            
            # Simular algum processamento
            self.production_logger.info(f"Operação {operation_id} executada com sucesso")
            
            return True
            
        except Exception as e:
            self.production_logger.error(f"Erro na operação {operation_id}: {e}")
            return False
    
    async def _test_production_readiness(self) -> dict:
        """Testa prontidão para produção."""
        start_time = datetime.now()
        
        try:
            readiness_checks = {
                "logging_system": self._check_logging_system(),
                "error_handling": self._check_error_handling_system(),
                "configuration_management": self._check_configuration_management(),
                "monitoring_capabilities": self._check_monitoring_capabilities(),
                "documentation": self._check_documentation()
            }
            
            passed_checks = sum(1 for check in readiness_checks.values() if check)
            total_checks = len(readiness_checks)
            readiness_score = (passed_checks / total_checks) * 100
            
            success = readiness_score >= 80.0  # 80% mínimo para produção
            
            return {
                "success": success,
                "details": f"Prontidão para produção: {passed_checks}/{total_checks} checks passaram ({readiness_score:.1f}%)",
                "metrics": {
                    "readiness_score": readiness_score,
                    "passed_checks": passed_checks,
                    "total_checks": total_checks,
                    "readiness_details": readiness_checks
                },
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
            
        except Exception as e:
            return {
                "success": False,
                "details": f"Erro na verificação de prontidão: {e}",
                "metrics": {},
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
    
    def _check_logging_system(self) -> bool:
        """Verifica sistema de logging."""
        try:
            log_dir = Path(f"{self.temp_dir}/logs")
            return log_dir.exists() and len(list(log_dir.glob("*.log"))) > 0
        except:
            return False
    
    def _check_error_handling_system(self) -> bool:
        """Verifica sistema de error handling."""
        return (
            self.error_handler is not None and
            len(self.error_handler.recovery_strategies) > 0
        )
    
    def _check_configuration_management(self) -> bool:
        """Verifica gerenciamento de configuração."""
        # Verificar se classes de configuração existem
        try:
            from qualia.ab_testing.system_integration import SystemIntegrationConfig
            config = SystemIntegrationConfig()
            return True
        except:
            return False
    
    def _check_monitoring_capabilities(self) -> bool:
        """Verifica capacidades de monitoramento."""
        # Verificar se sistema de métricas está funcionando
        return hasattr(self.error_handler, 'get_error_statistics')
    
    def _check_documentation(self) -> bool:
        """Verifica documentação."""
        # Verificar se arquivos principais existem
        src_dir = Path(__file__).parent.parent / "src" / "qualia" / "ab_testing"
        return src_dir.exists() and len(list(src_dir.glob("*.py"))) > 5


async def main():
    """Função principal."""
    print("🚀 QUALIA A/B TESTING FRAMEWORK - D-07.6 INTEGRATION TESTS")
    print("=" * 70)
    
    tester = D07_6_IntegrationTester()
    
    try:
        # Executar testes completos
        results = await tester.run_complete_integration_tests()
        
        # Exibir resultados
        print("\n" + "=" * 70)
        print("📋 RESUMO DOS TESTES DE INTEGRAÇÃO D-07.6")
        print("=" * 70)
        
        for result in results["test_results"]:
            status = "✅ PASSOU" if result["success"] else "❌ FALHOU"
            print(f"{status}: {result['test_name']}")
            print(f"   {result['details']}")
        
        print(f"\nTaxa de Sucesso: {results['passed']}/{results['total_tests']} ({results['success_rate']:.1f}%)")
        
        if results["production_ready"]:
            print("🎉 D-07.6 System Integration & Testing está PRONTO PARA PRODUÇÃO!")
        else:
            print("⚠️ D-07.6 precisa de ajustes antes da produção")
        
        print(f"📁 Arquivos temporários: {results['temp_directory']}")
        
        # Salvar relatório detalhado
        report_file = Path(results['temp_directory']) / "integration_test_report.json"
        with open(report_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"📊 Relatório detalhado salvo em: {report_file}")
        
    except Exception as e:
        print(f"💥 Erro crítico nos testes: {e}")
        return False
    
    return results["production_ready"]


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
