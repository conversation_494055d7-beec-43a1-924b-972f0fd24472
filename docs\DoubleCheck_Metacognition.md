# Double-Check de Integração do Módulo Metacognition

Este documento resume riscos e oportunidades observados na integração do módulo `src/qualia/metacognition` ao ecossistema QUALIA.

## Riscos e Problemas Encontrados

| Gravidade | Esforço | Descrição |
|-----------|----------|----------------------------------------------------------------|
| Alta | Alto | Ausência de publicação/consumo de eventos no **Qualia Event Bus**. |
| Alta | Médio | Módulo dependia de configurações JSON e não havia suporte a feature flags. |
| Média | Médio | Falta de instrumentação via OpenTelemetry, dificultando tracing fim a fim. |
| Média | Baixo | Benchmarks ausentes para medir latência de avaliação quântica. |
| Baixa | Baixo | Documentação de integração não cobre detalhes de rollback ou cleanup. |

## Quick Wins \u26a1\ufe0f


- [x] #23  Adicionar hooks de logging de eventos para o **Qualia Event Bus**.
- [x] #23  Converter arquivos de configura\u00e7\u00e3o para YAML e permitir override via vari\u00e1veis de ambiente.
- [x] #23  Instrumentar m\u00f3dulo com OpenTelemetry e propagar `trace_id` at\u00e9 os dashboards.
- [x] #23  Incluir testes de benchmark utilizando `pytest-benchmark` para medir p95 de lat\u00eancia.

## Features de Valor

*Como usuário do QUALIA eu quero...*

1. **Memória de Padrões Expandida** - ...poder acessar relatórios detalhados de como o `QuantumPatternMemory` influencia a pontuação metacognitiva (estimativa: 5 dias).
2. **Controle de Risco Dinâmico** - ...definir faixas de risco via `DynamicRiskController` que ajustam o peso dos métodos de avaliação (estimativa: 8 dias).
3. **Off-loading para GPU** - ...processar vetores quânticos maiores em GPUs, reduzindo o tempo total em 30\% (estimativa: 10 dias).


