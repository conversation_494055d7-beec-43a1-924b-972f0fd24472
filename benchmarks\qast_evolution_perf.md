# QAST Evolution Benchmark

Data da Execucao: 2025-05-26

## Configuracao

- **Python**: 3.12.10
- **pytest-benchmark**: 5.1.0
- **Populacao**: 8 individuos

## Resultado Baseline

```
Name (time in us)                          Min      Max    Mean  StdDev  Median     IQR  Outliers  OPS (Kops/s)  Rounds  Iterations
test_qast_evolution_generation_time     1.0590  75.4200  1.4135  3.2624  1.1620  0.0570    12;116      707.4725    2064           1
```

Este benchmark mede o tempo necessario para evoluir uma geracao de uma engine de evolucao simples. Valores maiores indicam regressao de desempenho.
