#!/usr/bin/env python3
"""
Teste de conexão com Binance API usando credenciais do .env (versão corrigida)

Este script testa se as credenciais da Binance estão funcionando corretamente.
"""

import os
import sys
from pathlib import Path

# Carregar variáveis de ambiente do .env
try:
    from dotenv import load_dotenv
    env_path = Path(__file__).parent.parent / ".env"
    load_dotenv(env_path)
    print(f"✅ Arquivo .env carregado: {env_path}")
except ImportError:
    print("⚠️ python-dotenv não instalado, usando variáveis de ambiente do sistema")
except Exception as e:
    print(f"⚠️ Erro ao carregar .env: {e}")

try:
    from binance.client import Client
    from binance.exceptions import BinanceAPIException
except ImportError:
    print("❌ Biblioteca python-binance não instalada")
    print("💡 Execute: pip install python-binance")
    sys.exit(1)

def test_binance_connection():
    """Testa conexão com a Binance API."""
    
    print("🔍 Testando conexão com Binance API...")
    print("=" * 50)
    
    # Verificar se as credenciais estão disponíveis
    api_key = os.environ.get('BINANCE_API_KEY')
    api_secret = os.environ.get('BINANCE_API_SECRET')
    
    if not api_key or not api_secret:
        print("❌ Credenciais não encontradas no .env")
        print("💡 Verifique se BINANCE_API_KEY e BINANCE_API_SECRET estão configurados")
        return False
    
    print(f"✅ API Key encontrada: {api_key[:8]}...{api_key[-8:]}")
    print(f"✅ API Secret encontrada: {api_secret[:8]}...{api_secret[-8:]}")
    
    try:
        # Inicializar cliente
        client = Client(api_key, api_secret)
        print("✅ Cliente Binance inicializado")
        
        # Testar conexão com servidor
        server_time = client.get_server_time()
        print(f"✅ Conexão com servidor: {server_time['serverTime']}")
        
        # Testar informações da conta (requer permissões de leitura)
        try:
            account_info = client.get_account()
            print(f"✅ Informações da conta obtidas")
            print(f"📊 Número de balances: {len(account_info['balances'])}")
        except BinanceAPIException as e:
            if e.code == -2014:
                print("⚠️ Chave API inválida ou sem permissões")
            else:
                print(f"⚠️ Erro da API: {e.message} (código: {e.code})")
            return False
        
        # Testar dados de mercado (público)
        try:
            ticker = client.get_symbol_ticker(symbol="BTCUSDT")
            print(f"✅ Ticker BTCUSDT: ${float(ticker['price']):,.2f}")
        except Exception as e:
            print(f"⚠️ Erro ao obter ticker: {e}")
        
        # Testar orderbook
        try:
            orderbook = client.get_order_book(symbol="BTCUSDT", limit=5)
            best_bid = float(orderbook['bids'][0][0])
            best_ask = float(orderbook['asks'][0][0])
            spread = (best_ask - best_bid) / best_bid * 100
            print(f"✅ Orderbook BTCUSDT - Spread: {spread:.4f}%")
        except Exception as e:
            print(f"⚠️ Erro ao obter orderbook: {e}")
        
        print("\n🎉 CONEXÃO COM BINANCE FUNCIONANDO PERFEITAMENTE!")
        return True
        
    except BinanceAPIException as e:
        print(f"❌ Erro da API Binance: {e.message} (código: {e.code})")
        if e.code == -1021:
            print("💡 Erro de timestamp - verifique o horário do sistema")
        elif e.code == -2014:
            print("💡 Chave API inválida")
        return False
    except Exception as e:
        print(f"❌ Erro de conexão: {e}")
        return False

if __name__ == "__main__":
    success = test_binance_connection()
    
    if success:
        print("\n✅ Teste concluído com sucesso!")
        print("🚀 Agora você pode executar o calibrador com dados reais:")
        print("   python fwh_scalp_calibrator.py --mode fast")
    else:
        print("\n❌ Teste falhou!")
        print("🔧 Verifique suas credenciais no arquivo .env")
        sys.exit(1)