#!/usr/bin/env python3
"""
Teste para verificar se o filtro de posições HOLD está funcionando corretamente.
"""

import sys
import os
import tempfile
import json
from datetime import datetime, timezone
from pathlib import Path

# Adiciona o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qualia.utils.real_time_metrics_logger import (
    RealTimeMetricsLogger,
    TradingDecisionMetrics,
    TimeframeMetrics,
    ConsolidatedMetrics
)

def create_test_metrics(signal_type: str) -> TradingDecisionMetrics:
    """Cria métricas de teste com o tipo de sinal especificado."""
    
    timeframe_metrics = [
        TimeframeMetrics(
            timeframe="1m",
            signal=signal_type,
            confidence=0.5,
            signal_strength=0.3,
            hype_momentum=0.2,
            holographic_boost=0.1,
            tsvf_validation=0.4,
            data_periods=100,
            timestamp=datetime.now(timezone.utc).isoformat()
        )
    ]
    
    consolidated_metrics = ConsolidatedMetrics(
        final_signal=signal_type,
        final_confidence=0.5,
        convergence_score=0.8,
        primary_timeframe="1m",
        supporting_timeframes=["5m"],
        reasoning=f"Sinal {signal_type} detectado",
        timestamp=datetime.now(timezone.utc).isoformat()
    )
    
    return TradingDecisionMetrics(
        decision_id=f"test_{signal_type.lower()}_{int(datetime.now().timestamp())}",
        symbol="BTC/USDT",
        timestamp=datetime.now(timezone.utc).isoformat(),
        log_type="TRADE_EXECUTION",
        current_price=50000.0,
        volume=1000.0,
        timeframe_metrics=timeframe_metrics,
        consolidated_metrics=consolidated_metrics,
        trade_executed=(signal_type != "HOLD"),
        execution_reason=f"Sinal {signal_type}",
        rejection_reason=None if signal_type != "HOLD" else None,
        stop_loss=49000.0 if signal_type != "HOLD" else None,
        take_profit=51000.0 if signal_type != "HOLD" else None,
        position_size=0.1 if signal_type != "HOLD" else None,
        risk_percentage=2.0 if signal_type != "HOLD" else None,
        processing_time_ms=50.0,
        data_quality_score=0.95,
        market_conditions={"volatility": "medium"},
        system_state={"memory_usage": "normal", "cpu_load": "low"}
    )

def test_hold_filter():
    """Testa o filtro de posições HOLD."""
    
    print("🧪 Testando filtro de posições HOLD...")
    
    # Cria diretório temporário para logs
    with tempfile.TemporaryDirectory() as temp_dir:
        
        # Teste 1: Logger com filtro habilitado
        print("\n📋 Teste 1: Logger com filtro HOLD habilitado")
        logger_with_filter = RealTimeMetricsLogger(
            log_directory=os.path.join(temp_dir, "with_filter"),
            filter_hold_positions=True
        )
        logger_with_filter.start()
        
        # Testa diferentes tipos de sinais
        signals_to_test = ["BUY", "SELL", "HOLD"]
        
        for signal in signals_to_test:
            metrics = create_test_metrics(signal)
            logger_with_filter.log_trading_decision(metrics)
            print(f"   ✅ Enviado sinal {signal}")
        
        # Aguarda processamento
        import time
        time.sleep(2)
        logger_with_filter.stop()
        
        # Verifica arquivos gerados
        log_files = list(Path(os.path.join(temp_dir, "with_filter")).glob("*.jsonl"))
        if log_files:
            with open(log_files[0], 'r') as f:
                lines = f.readlines()
            
            print(f"   📊 Logs salvos: {len(lines)} (esperado: 2 - BUY e SELL apenas)")
            
            # Verifica se não há logs HOLD
            hold_logs = [line for line in lines if "HOLD" in line]
            if not hold_logs:
                print("   ✅ Filtro funcionando: Nenhum log HOLD encontrado")
            else:
                print(f"   ❌ Filtro falhou: {len(hold_logs)} logs HOLD encontrados")
        else:
            print("   ⚠️  Nenhum arquivo de log encontrado")
        
        # Teste 2: Logger com filtro desabilitado
        print("\n📋 Teste 2: Logger com filtro HOLD desabilitado")
        logger_without_filter = RealTimeMetricsLogger(
            log_directory=os.path.join(temp_dir, "without_filter"),
            filter_hold_positions=False
        )
        logger_without_filter.start()
        
        for signal in signals_to_test:
            metrics = create_test_metrics(signal)
            logger_without_filter.log_trading_decision(metrics)
            print(f"   ✅ Enviado sinal {signal}")
        
        # Aguarda processamento
        time.sleep(2)
        logger_without_filter.stop()
        
        # Verifica arquivos gerados
        log_files = list(Path(os.path.join(temp_dir, "without_filter")).glob("*.jsonl"))
        if log_files:
            with open(log_files[0], 'r') as f:
                lines = f.readlines()
            
            print(f"   📊 Logs salvos: {len(lines)} (esperado: 3 - BUY, SELL e HOLD)")
            
            # Verifica se há logs HOLD
            hold_logs = [line for line in lines if "HOLD" in line]
            if hold_logs:
                print(f"   ✅ Sem filtro funcionando: {len(hold_logs)} logs HOLD encontrados")
            else:
                print("   ❌ Sem filtro falhou: Nenhum log HOLD encontrado")
        else:
            print("   ⚠️  Nenhum arquivo de log encontrado")

def test_hold_detection():
    """Testa a detecção de posições HOLD."""
    
    print("\n🔍 Testando detecção de posições HOLD...")
    
    logger = RealTimeMetricsLogger(filter_hold_positions=True)
    
    # Teste diferentes cenários de HOLD
    test_cases = [
        ("HOLD explícito", create_test_metrics("HOLD")),
        ("BUY normal", create_test_metrics("BUY")),
        ("SELL normal", create_test_metrics("SELL")),
    ]
    
    for case_name, metrics in test_cases:
        is_hold = logger._is_hold_decision(metrics)
        expected_hold = "HOLD" in case_name
        
        if is_hold == expected_hold:
            print(f"   ✅ {case_name}: {'HOLD' if is_hold else 'NÃO HOLD'} (correto)")
        else:
            print(f"   ❌ {case_name}: {'HOLD' if is_hold else 'NÃO HOLD'} (esperado: {'HOLD' if expected_hold else 'NÃO HOLD'})")

if __name__ == "__main__":
    print("🚀 Iniciando testes do filtro de posições HOLD")
    
    try:
        test_hold_detection()
        test_hold_filter()
        print("\n✅ Todos os testes concluídos!")
        
    except Exception as e:
        print(f"\n❌ Erro durante os testes: {e}")
        import traceback
        traceback.print_exc()
