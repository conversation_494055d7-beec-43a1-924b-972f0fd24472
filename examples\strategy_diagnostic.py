#!/usr/bin/env python3
"""
Diagnóstico da Estratégia QUALIA - Análise de Problemas
YAA IMPLEMENTATION: Identifica por que a estratégia está gerando performance negativa.
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import numpy as np
import requests
import matplotlib.pyplot as plt
import seaborn as sns

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

class StrategyDiagnostic:
    """Diagnóstico completo da estratégia QUALIA."""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'QUALIA-Diagnostic/1.0'})
    
    def fetch_data(self, symbol: str, days: int = 120) -> pd.DataFrame:
        """Busca dados para diagnóstico."""
        try:
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            params = {
                'symbol': symbol.replace('/', ''),
                'interval': '1h',
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            response = self.session.get("https://api.binance.com/api/v3/klines", params=params)
            response.raise_for_status()
            data = response.json()
            
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']].copy()
            
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index().dropna()
            
            # Adiciona indicadores
            df['returns'] = df['close'].pct_change().fillna(0)
            df['sma_20'] = df['close'].rolling(20).mean()
            df['volatility'] = df['returns'].rolling(20).std()
            
            return df
            
        except Exception as e:
            print(f"❌ Erro ao buscar dados: {e}")
            return pd.DataFrame()
    
    def analyze_current_strategy(self, df: pd.DataFrame, price_amp: float = 5.0, news_amp: float = 4.0) -> dict:
        """Analisa a estratégia atual e identifica problemas."""
        
        if df.empty or len(df) < 100:
            return {'error': 'Dados insuficientes'}
        
        signals = []
        positions = []
        momentum_signals = []
        vol_signals = []
        confidences = []
        
        min_window = 50
        
        for i in range(min_window, len(df)):
            window_data = df.iloc[max(0, i-min_window):i+1].copy()
            
            if len(window_data) < min_window:
                signals.append(0.0)
                positions.append(0.0)
                momentum_signals.append(0.0)
                vol_signals.append(0.0)
                confidences.append(0.0)
                continue
            
            # Reproduz lógica atual
            current_price = window_data['close'].iloc[-1]
            sma_20 = window_data['sma_20'].iloc[-1]
            volatility = window_data['volatility'].iloc[-1]
            
            # Sinal de momentum
            momentum_signal = 0.0
            if not pd.isna(sma_20):
                momentum_signal = (current_price - sma_20) / sma_20
            
            # Sinal de volatilidade (PROBLEMA: sempre positivo)
            vol_signal = 0.0
            if not pd.isna(volatility) and volatility > 0:
                vol_signal = 1.0 / (1.0 + volatility * 100)
            
            # Confiança
            volume_ratio = window_data['volume'].iloc[-1] / window_data['volume'].mean()
            confidence = np.clip(0.5 + 0.2 * vol_signal + 0.1 * np.log(volume_ratio), 0.2, 0.9)
            
            # Combina sinais
            price_weight = price_amp / 10.0
            news_weight = news_amp / 10.0
            
            combined_signal = (
                price_weight * momentum_signal + 
                news_weight * vol_signal
            )
            
            position = np.clip(combined_signal, -1.0, 1.0)
            
            signals.append(combined_signal)
            positions.append(position)
            momentum_signals.append(momentum_signal)
            vol_signals.append(vol_signal)
            confidences.append(confidence)
        
        # Análise dos sinais
        analysis = {
            'total_signals': len(signals),
            'momentum_stats': {
                'mean': np.mean(momentum_signals),
                'std': np.std(momentum_signals),
                'min': np.min(momentum_signals),
                'max': np.max(momentum_signals),
                'positive_pct': np.mean(np.array(momentum_signals) > 0) * 100
            },
            'vol_signal_stats': {
                'mean': np.mean(vol_signals),
                'std': np.std(vol_signals),
                'min': np.min(vol_signals),
                'max': np.max(vol_signals),
                'always_positive': np.all(np.array(vol_signals) >= 0)
            },
            'combined_signal_stats': {
                'mean': np.mean(signals),
                'std': np.std(signals),
                'min': np.min(signals),
                'max': np.max(signals),
                'positive_pct': np.mean(np.array(signals) > 0) * 100
            },
            'position_stats': {
                'mean': np.mean(positions),
                'std': np.std(positions),
                'long_pct': np.mean(np.array(positions) > 0) * 100,
                'short_pct': np.mean(np.array(positions) < 0) * 100,
                'neutral_pct': np.mean(np.array(positions) == 0) * 100
            },
            'confidence_stats': {
                'mean': np.mean(confidences),
                'std': np.std(confidences),
                'min': np.min(confidences),
                'max': np.max(confidences)
            }
        }
        
        return analysis
    
    def test_signal_correlation(self, df: pd.DataFrame) -> dict:
        """Testa correlação entre sinais e retornos futuros."""
        
        if df.empty or len(df) < 100:
            return {'error': 'Dados insuficientes'}
        
        # Calcula sinais
        df['momentum_signal'] = (df['close'] - df['sma_20']) / df['sma_20']
        df['vol_signal'] = 1.0 / (1.0 + df['volatility'] * 100)
        
        # Retornos futuros (1h, 4h, 24h)
        df['future_return_1h'] = df['close'].shift(-1) / df['close'] - 1
        df['future_return_4h'] = df['close'].shift(-4) / df['close'] - 1
        df['future_return_24h'] = df['close'].shift(-24) / df['close'] - 1
        
        # Correlações
        correlations = {}
        for signal in ['momentum_signal', 'vol_signal']:
            correlations[signal] = {
                '1h': df[signal].corr(df['future_return_1h']),
                '4h': df[signal].corr(df['future_return_4h']),
                '24h': df[signal].corr(df['future_return_24h'])
            }
        
        return correlations
    
    def run_full_diagnostic(self, symbol: str = "BTCUSDT") -> dict:
        """Executa diagnóstico completo."""
        
        print(f"🔍 DIAGNÓSTICO DA ESTRATÉGIA QUALIA - {symbol}")
        print("=" * 60)
        
        # Busca dados
        df = self.fetch_data(symbol)
        if df.empty:
            return {'error': 'Falha ao obter dados'}
        
        print(f"📊 Dados obtidos: {len(df)} candles de {df.index[0]} a {df.index[-1]}")
        
        # Análise da estratégia atual
        strategy_analysis = self.analyze_current_strategy(df)
        
        # Análise de correlação
        correlation_analysis = self.test_signal_correlation(df)
        
        # Relatório
        print(f"\n🔍 ANÁLISE DOS SINAIS:")
        print(f"   • Momentum Signal - Média: {strategy_analysis['momentum_stats']['mean']:.4f}")
        print(f"   • Momentum Signal - % Positivos: {strategy_analysis['momentum_stats']['positive_pct']:.1f}%")
        print(f"   • Vol Signal - Sempre Positivo: {strategy_analysis['vol_signal_stats']['always_positive']}")
        print(f"   • Vol Signal - Média: {strategy_analysis['vol_signal_stats']['mean']:.4f}")
        
        print(f"\n📈 ANÁLISE DAS POSIÇÕES:")
        print(f"   • Posições Long: {strategy_analysis['position_stats']['long_pct']:.1f}%")
        print(f"   • Posições Short: {strategy_analysis['position_stats']['short_pct']:.1f}%")
        print(f"   • Posições Neutras: {strategy_analysis['position_stats']['neutral_pct']:.1f}%")
        print(f"   • Posição Média: {strategy_analysis['position_stats']['mean']:.4f}")
        
        print(f"\n🔗 CORRELAÇÕES COM RETORNOS FUTUROS:")
        for signal, corrs in correlation_analysis.items():
            print(f"   • {signal}:")
            for period, corr in corrs.items():
                print(f"     - {period}: {corr:.4f}")
        
        # Identifica problemas
        problems = []
        
        if strategy_analysis['vol_signal_stats']['always_positive']:
            problems.append("❌ PROBLEMA CRÍTICO: Vol Signal sempre positivo - nunca gera sinais de venda")
        
        if strategy_analysis['position_stats']['long_pct'] > 80:
            problems.append("❌ PROBLEMA: Estratégia muito enviesada para long")
        
        if abs(correlation_analysis['momentum_signal']['1h']) < 0.05:
            problems.append("❌ PROBLEMA: Momentum signal não correlacionado com retornos futuros")
        
        if abs(correlation_analysis['vol_signal']['1h']) < 0.05:
            problems.append("❌ PROBLEMA: Vol signal não correlacionado com retornos futuros")
        
        print(f"\n🚨 PROBLEMAS IDENTIFICADOS:")
        for problem in problems:
            print(f"   {problem}")
        
        return {
            'symbol': symbol,
            'data_points': len(df),
            'strategy_analysis': strategy_analysis,
            'correlation_analysis': correlation_analysis,
            'problems': problems
        }


if __name__ == "__main__":
    diagnostic = StrategyDiagnostic()
    
    # Testa BTC e ETH
    for symbol in ["BTCUSDT", "ETHUSDT"]:
        result = diagnostic.run_full_diagnostic(symbol)
        print(f"\n" + "="*60)
        
    print(f"\n✅ Diagnóstico concluído!")
