"""Performance metrics collection and analysis for QUALIA system.

This module provides comprehensive performance monitoring capabilities including:
- Real-time metrics collection
- Performance trend analysis
- Bottleneck detection
- Resource utilization tracking
- Performance alerting
- Automated optimization suggestions
"""

from __future__ import annotations

import asyncio
import time
import psutil
import statistics
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from collections import deque, defaultdict
import threading

from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class MetricType(Enum):
    """Types of performance metrics."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


class PerformanceLevel(Enum):
    """Performance level classifications."""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    CRITICAL = "critical"


@dataclass
class PerformanceMetric:
    """Individual performance metric."""
    name: str
    value: float
    metric_type: MetricType
    unit: str
    timestamp: float
    tags: Dict[str, str] = field(default_factory=dict)
    
    def __post_init__(self):
        if self.timestamp == 0:
            self.timestamp = time.time()


@dataclass
class PerformanceSnapshot:
    """Snapshot of system performance at a point in time."""
    timestamp: float
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, float]
    custom_metrics: Dict[str, float]
    
    @property
    def overall_score(self) -> float:
        """Calculate overall performance score (0-1)."""
        # Weight different metrics
        cpu_score = max(0, 1.0 - (self.cpu_usage / 100.0))
        memory_score = max(0, 1.0 - (self.memory_usage / 100.0))
        disk_score = max(0, 1.0 - (self.disk_usage / 100.0))
        
        # Weighted average
        return (cpu_score * 0.4 + memory_score * 0.4 + disk_score * 0.2)


class PerformanceMetricsCollector:
    """Advanced performance metrics collector with real-time analysis.
    
    This collector provides:
    - System resource monitoring
    - Custom application metrics
    - Performance trend analysis
    - Bottleneck detection
    - Automated optimization suggestions
    """
    
    def __init__(
        self,
        name: str = "qualia_performance_metrics",
        collection_interval: float = 10.0,
        history_size: int = 1000,
        enable_system_metrics: bool = True,
        enable_trend_analysis: bool = True,
    ):
        self.name = name
        self.collection_interval = collection_interval
        self.history_size = history_size
        self.enable_system_metrics = enable_system_metrics
        self.enable_trend_analysis = enable_trend_analysis
        
        # Metrics storage
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=history_size))
        self.performance_history: deque = deque(maxlen=history_size)
        self.custom_metrics: Dict[str, PerformanceMetric] = {}
        
        # System monitoring
        self.system_snapshot_history: deque = deque(maxlen=history_size)
        
        # Performance analysis
        self.bottlenecks: Dict[str, Dict] = {}
        self.performance_trends: Dict[str, Dict] = {}
        self.optimization_suggestions: List[str] = []
        
        # Background collection
        self.collection_task: Optional[asyncio.Task] = None
        self.is_collecting = False
        
        # Callbacks
        self.metric_callbacks: List[Callable] = []
        self.bottleneck_callbacks: List[Callable] = []
        
        # Statistics
        self.stats = {
            'metrics_collected': 0,
            'snapshots_taken': 0,
            'bottlenecks_detected': 0,
            'trends_analyzed': 0,
            'suggestions_generated': 0
        }
        
        # Thread-safe metric updates
        self._metric_lock = threading.Lock()

    async def start_collection(self) -> None:
        """Start performance metrics collection."""
        if self.is_collecting:
            return
        
        self.is_collecting = True
        self.collection_task = asyncio.create_task(self._collection_loop())
        
        logger.info(f"Performance metrics collector '{self.name}' started")

    async def stop_collection(self) -> None:
        """Stop performance metrics collection."""
        if not self.is_collecting:
            return
        
        self.is_collecting = False
        
        if self.collection_task and not self.collection_task.done():
            self.collection_task.cancel()
            try:
                await self.collection_task
            except asyncio.CancelledError:
                pass
        
        logger.info(f"Performance metrics collector '{self.name}' stopped")

    def record_metric(
        self,
        name: str,
        value: float,
        metric_type: MetricType = MetricType.GAUGE,
        unit: str = "",
        tags: Optional[Dict[str, str]] = None
    ) -> None:
        """Record a custom performance metric.
        
        Parameters
        ----------
        name : str
            Metric name
        value : float
            Metric value
        metric_type : MetricType
            Type of metric
        unit : str
            Unit of measurement
        tags : Dict, optional
            Additional tags for the metric
        """
        with self._metric_lock:
            metric = PerformanceMetric(
                name=name,
                value=value,
                metric_type=metric_type,
                unit=unit,
                timestamp=time.time(),
                tags=tags or {}
            )
            
            self.custom_metrics[name] = metric
            self.metrics[name].append({
                'timestamp': metric.timestamp,
                'value': value,
                'type': metric_type.value,
                'unit': unit,
                'tags': tags or {}
            })
            
            self.stats['metrics_collected'] += 1
            
            # Trigger callbacks
            for callback in self.metric_callbacks:
                try:
                    callback(metric)
                except Exception as e:
                    logger.warning(f"Metric callback failed: {e}")

    def record_timer(self, name: str, duration: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record a timing metric.
        
        Parameters
        ----------
        name : str
            Timer name
        duration : float
            Duration in seconds
        tags : Dict, optional
            Additional tags
        """
        self.record_metric(name, duration, MetricType.TIMER, "seconds", tags)

    def record_counter(self, name: str, increment: float = 1.0, tags: Optional[Dict[str, str]] = None) -> None:
        """Record a counter metric.
        
        Parameters
        ----------
        name : str
            Counter name
        increment : float
            Increment value
        tags : Dict, optional
            Additional tags
        """
        # For counters, we accumulate the value
        current_value = 0.0
        if name in self.custom_metrics:
            current_value = self.custom_metrics[name].value
        
        self.record_metric(name, current_value + increment, MetricType.COUNTER, "count", tags)

    async def _collection_loop(self) -> None:
        """Main collection loop."""
        while self.is_collecting:
            try:
                # Collect system metrics
                if self.enable_system_metrics:
                    await self._collect_system_metrics()
                
                # Analyze trends
                if self.enable_trend_analysis:
                    await self._analyze_trends()
                
                # Detect bottlenecks
                await self._detect_bottlenecks()
                
                # Generate optimization suggestions
                await self._generate_optimization_suggestions()
                
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in performance collection loop: {e}")
                await asyncio.sleep(self.collection_interval)

    async def _collect_system_metrics(self) -> None:
        """Collect system performance metrics."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # Network I/O
            network = psutil.net_io_counters()
            network_io = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
            
            # Create snapshot
            snapshot = PerformanceSnapshot(
                timestamp=time.time(),
                cpu_usage=cpu_percent,
                memory_usage=memory_percent,
                disk_usage=disk_percent,
                network_io=network_io,
                custom_metrics={name: metric.value for name, metric in self.custom_metrics.items()}
            )
            
            self.system_snapshot_history.append(snapshot)
            self.stats['snapshots_taken'] += 1
            
            # Record individual metrics
            self.record_metric("system.cpu_usage", cpu_percent, MetricType.GAUGE, "%")
            self.record_metric("system.memory_usage", memory_percent, MetricType.GAUGE, "%")
            self.record_metric("system.disk_usage", disk_percent, MetricType.GAUGE, "%")
            self.record_metric("system.network_bytes_sent", network_io['bytes_sent'], MetricType.COUNTER, "bytes")
            self.record_metric("system.network_bytes_recv", network_io['bytes_recv'], MetricType.COUNTER, "bytes")
            
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")

    async def _analyze_trends(self) -> None:
        """Analyze performance trends."""
        if len(self.system_snapshot_history) < 10:
            return
        
        try:
            recent_snapshots = list(self.system_snapshot_history)[-20:]  # Last 20 snapshots
            
            # Analyze CPU trend
            cpu_values = [s.cpu_usage for s in recent_snapshots]
            cpu_trend = self._calculate_trend(cpu_values)
            
            # Analyze memory trend
            memory_values = [s.memory_usage for s in recent_snapshots]
            memory_trend = self._calculate_trend(memory_values)
            
            # Analyze overall performance trend
            performance_scores = [s.overall_score for s in recent_snapshots]
            performance_trend = self._calculate_trend(performance_scores)
            
            self.performance_trends = {
                'cpu': {
                    'current': cpu_values[-1],
                    'trend': cpu_trend,
                    'average': statistics.mean(cpu_values),
                    'volatility': statistics.stdev(cpu_values) if len(cpu_values) > 1 else 0.0
                },
                'memory': {
                    'current': memory_values[-1],
                    'trend': memory_trend,
                    'average': statistics.mean(memory_values),
                    'volatility': statistics.stdev(memory_values) if len(memory_values) > 1 else 0.0
                },
                'performance': {
                    'current': performance_scores[-1],
                    'trend': performance_trend,
                    'average': statistics.mean(performance_scores),
                    'volatility': statistics.stdev(performance_scores) if len(performance_scores) > 1 else 0.0
                }
            }
            
            self.stats['trends_analyzed'] += 1
            
        except Exception as e:
            logger.error(f"Failed to analyze trends: {e}")

    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate trend slope for a series of values."""
        if len(values) < 2:
            return 0.0
        
        n = len(values)
        x_mean = (n - 1) / 2
        y_mean = sum(values) / n
        
        numerator = sum((i - x_mean) * (values[i] - y_mean) for i in range(n))
        denominator = sum((i - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return 0.0
        
        return numerator / denominator

    async def _detect_bottlenecks(self) -> None:
        """Detect performance bottlenecks."""
        if not self.system_snapshot_history:
            return
        
        try:
            current_snapshot = self.system_snapshot_history[-1]
            bottlenecks_detected = {}
            
            # CPU bottleneck
            if current_snapshot.cpu_usage > 80:
                bottlenecks_detected['cpu'] = {
                    'severity': 'high' if current_snapshot.cpu_usage > 90 else 'medium',
                    'value': current_snapshot.cpu_usage,
                    'threshold': 80,
                    'description': f"High CPU usage: {current_snapshot.cpu_usage:.1f}%"
                }
            
            # Memory bottleneck
            if current_snapshot.memory_usage > 85:
                bottlenecks_detected['memory'] = {
                    'severity': 'high' if current_snapshot.memory_usage > 95 else 'medium',
                    'value': current_snapshot.memory_usage,
                    'threshold': 85,
                    'description': f"High memory usage: {current_snapshot.memory_usage:.1f}%"
                }
            
            # Disk bottleneck
            if current_snapshot.disk_usage > 90:
                bottlenecks_detected['disk'] = {
                    'severity': 'high' if current_snapshot.disk_usage > 95 else 'medium',
                    'value': current_snapshot.disk_usage,
                    'threshold': 90,
                    'description': f"High disk usage: {current_snapshot.disk_usage:.1f}%"
                }
            
            # Check custom metrics for bottlenecks
            for metric_name, metric in self.custom_metrics.items():
                if 'latency' in metric_name.lower() and metric.value > 1.0:  # > 1 second
                    bottlenecks_detected[f'latency_{metric_name}'] = {
                        'severity': 'high' if metric.value > 5.0 else 'medium',
                        'value': metric.value,
                        'threshold': 1.0,
                        'description': f"High latency in {metric_name}: {metric.value:.2f}s"
                    }
            
            # Update bottlenecks
            if bottlenecks_detected:
                self.bottlenecks.update(bottlenecks_detected)
                self.stats['bottlenecks_detected'] += len(bottlenecks_detected)
                
                # Trigger callbacks
                for callback in self.bottleneck_callbacks:
                    try:
                        callback(bottlenecks_detected)
                    except Exception as e:
                        logger.warning(f"Bottleneck callback failed: {e}")
                
                logger.warning(f"Performance bottlenecks detected: {list(bottlenecks_detected.keys())}")
            
        except Exception as e:
            logger.error(f"Failed to detect bottlenecks: {e}")

    async def _generate_optimization_suggestions(self) -> None:
        """Generate optimization suggestions based on performance data."""
        suggestions = []
        
        try:
            # Analyze trends for suggestions
            if 'cpu' in self.performance_trends:
                cpu_trend = self.performance_trends['cpu']
                if cpu_trend['trend'] > 5.0:  # Increasing CPU usage
                    suggestions.append("CPU usage is trending upward. Consider optimizing algorithms or scaling horizontally.")
                if cpu_trend['volatility'] > 20.0:  # High CPU volatility
                    suggestions.append("CPU usage is highly volatile. Check for inefficient loops or resource contention.")
            
            if 'memory' in self.performance_trends:
                memory_trend = self.performance_trends['memory']
                if memory_trend['trend'] > 2.0:  # Increasing memory usage
                    suggestions.append("Memory usage is trending upward. Check for memory leaks or optimize data structures.")
                if memory_trend['current'] > 80:
                    suggestions.append("High memory usage detected. Consider implementing caching strategies or increasing memory allocation.")
            
            # Bottleneck-based suggestions
            for bottleneck_name, bottleneck_info in self.bottlenecks.items():
                if bottleneck_name == 'cpu':
                    suggestions.append("CPU bottleneck detected. Consider profiling code for optimization opportunities.")
                elif bottleneck_name == 'memory':
                    suggestions.append("Memory bottleneck detected. Review memory allocation patterns and implement garbage collection tuning.")
                elif bottleneck_name == 'disk':
                    suggestions.append("Disk bottleneck detected. Consider using faster storage or implementing data compression.")
                elif 'latency' in bottleneck_name:
                    suggestions.append(f"High latency detected in {bottleneck_name}. Review network connections and optimize data processing.")
            
            # Custom metric analysis
            for metric_name, metric in self.custom_metrics.items():
                if 'error' in metric_name.lower() and metric.value > 0:
                    suggestions.append(f"Errors detected in {metric_name}. Review error handling and system stability.")
                if 'queue' in metric_name.lower() and metric.value > 100:
                    suggestions.append(f"Queue backlog detected in {metric_name}. Consider increasing processing capacity.")
            
            # Update suggestions (keep only unique ones)
            new_suggestions = [s for s in suggestions if s not in self.optimization_suggestions]
            self.optimization_suggestions.extend(new_suggestions)
            
            # Keep only recent suggestions (last 10)
            self.optimization_suggestions = self.optimization_suggestions[-10:]
            
            if new_suggestions:
                self.stats['suggestions_generated'] += len(new_suggestions)
                logger.info(f"Generated {len(new_suggestions)} new optimization suggestions")
            
        except Exception as e:
            logger.error(f"Failed to generate optimization suggestions: {e}")

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        current_snapshot = self.system_snapshot_history[-1] if self.system_snapshot_history else None
        
        return {
            'name': self.name,
            'is_collecting': self.is_collecting,
            'current_performance': {
                'cpu_usage': current_snapshot.cpu_usage if current_snapshot else 0.0,
                'memory_usage': current_snapshot.memory_usage if current_snapshot else 0.0,
                'disk_usage': current_snapshot.disk_usage if current_snapshot else 0.0,
                'overall_score': current_snapshot.overall_score if current_snapshot else 0.0,
                'performance_level': self._get_performance_level(current_snapshot.overall_score if current_snapshot else 0.0)
            },
            'trends': self.performance_trends,
            'bottlenecks': self.bottlenecks,
            'optimization_suggestions': self.optimization_suggestions,
            'custom_metrics': {
                name: {
                    'value': metric.value,
                    'type': metric.metric_type.value,
                    'unit': metric.unit,
                    'timestamp': metric.timestamp
                }
                for name, metric in self.custom_metrics.items()
            },
            'statistics': self.stats,
            'timestamp': time.time()
        }

    def _get_performance_level(self, score: float) -> str:
        """Determine performance level from score."""
        if score >= 0.9:
            return PerformanceLevel.EXCELLENT.value
        elif score >= 0.7:
            return PerformanceLevel.GOOD.value
        elif score >= 0.5:
            return PerformanceLevel.ACCEPTABLE.value
        elif score >= 0.3:
            return PerformanceLevel.POOR.value
        else:
            return PerformanceLevel.CRITICAL.value

    def get_metric_history(self, metric_name: str, hours: float = 1.0) -> List[Dict]:
        """Get historical data for a specific metric."""
        if metric_name not in self.metrics:
            return []
        
        cutoff_time = time.time() - (hours * 3600)
        return [
            entry for entry in self.metrics[metric_name]
            if entry['timestamp'] > cutoff_time
        ]

    def add_metric_callback(self, callback: Callable) -> None:
        """Add callback for metric updates."""
        self.metric_callbacks.append(callback)

    def add_bottleneck_callback(self, callback: Callable) -> None:
        """Add callback for bottleneck detection."""
        self.bottleneck_callbacks.append(callback)

    def reset_statistics(self) -> None:
        """Reset all statistics and history."""
        with self._metric_lock:
            self.metrics.clear()
            self.performance_history.clear()
            self.custom_metrics.clear()
            self.system_snapshot_history.clear()
            self.bottlenecks.clear()
            self.performance_trends.clear()
            self.optimization_suggestions.clear()
            
            self.stats = {
                'metrics_collected': 0,
                'snapshots_taken': 0,
                'bottlenecks_detected': 0,
                'trends_analyzed': 0,
                'suggestions_generated': 0
            }
        
        logger.info(f"Performance metrics collector '{self.name}' statistics reset")

    async def integrate_with_qualia_system(self) -> None:
        """Integrate performance metrics with QUALIA system components."""
        # Register QUALIA-specific metrics
        await self._register_qualia_metrics()

        # Setup QUALIA component monitoring
        await self._setup_qualia_monitoring()

        logger.info("Performance metrics integrated with QUALIA system")

    async def _register_qualia_metrics(self) -> None:
        """Register QUALIA-specific performance metrics."""
        # Trading performance metrics
        self.record_metric("qualia.trading.signals_generated", 0.0, MetricType.COUNTER, "count")
        self.record_metric("qualia.trading.signals_executed", 0.0, MetricType.COUNTER, "count")
        self.record_metric("qualia.trading.pnl_realized", 0.0, MetricType.GAUGE, "currency")
        self.record_metric("qualia.trading.sharpe_ratio", 0.0, MetricType.GAUGE, "ratio")

        # Quantum computation metrics
        self.record_metric("qualia.quantum.consciousness_level", 0.0, MetricType.GAUGE, "level")
        self.record_metric("qualia.quantum.coherence_score", 0.0, MetricType.GAUGE, "score")
        self.record_metric("qualia.quantum.entanglement_strength", 0.0, MetricType.GAUGE, "strength")

        # Decision engine metrics
        self.record_metric("qualia.decision.confidence_avg", 0.0, MetricType.GAUGE, "confidence")
        self.record_metric("qualia.decision.processing_time", 0.0, MetricType.TIMER, "seconds")
        self.record_metric("qualia.decision.accuracy_rate", 0.0, MetricType.GAUGE, "percentage")

        # Data pipeline metrics
        self.record_metric("qualia.data.market_data_latency", 0.0, MetricType.TIMER, "milliseconds")
        self.record_metric("qualia.data.cache_hit_rate", 0.0, MetricType.GAUGE, "percentage")
        self.record_metric("qualia.data.quality_score", 0.0, MetricType.GAUGE, "score")

        # Network resilience metrics
        self.record_metric("qualia.network.circuit_breaker_trips", 0.0, MetricType.COUNTER, "count")
        self.record_metric("qualia.network.failover_events", 0.0, MetricType.COUNTER, "count")
        self.record_metric("qualia.network.recovery_time", 0.0, MetricType.TIMER, "seconds")

    async def _setup_qualia_monitoring(self) -> None:
        """Setup monitoring for QUALIA system components."""
        # This would integrate with actual QUALIA components
        # For now, we'll setup the framework

        # Add callbacks for QUALIA-specific events
        self.add_metric_callback(self._handle_qualia_metric_update)
        self.add_bottleneck_callback(self._handle_qualia_bottleneck)

        logger.info("QUALIA component monitoring setup completed")

    def _handle_qualia_metric_update(self, metric: PerformanceMetric) -> None:
        """Handle QUALIA-specific metric updates."""
        # Special handling for QUALIA metrics
        if metric.name.startswith("qualia."):
            # Log important QUALIA metrics
            if "consciousness_level" in metric.name:
                logger.info(f"Consciousness level updated: {metric.value:.3f}")
            elif "sharpe_ratio" in metric.name:
                logger.info(f"Sharpe ratio updated: {metric.value:.3f}")
            elif "pnl_realized" in metric.name:
                logger.info(f"Realized PnL updated: {metric.value:.2f}")

    def _handle_qualia_bottleneck(self, bottlenecks: Dict[str, Dict]) -> None:
        """Handle QUALIA-specific bottlenecks."""
        for bottleneck_name, bottleneck_info in bottlenecks.items():
            if "qualia" in bottleneck_name.lower():
                logger.warning(f"QUALIA bottleneck detected: {bottleneck_info['description']}")

                # Trigger QUALIA-specific optimization
                self._trigger_qualia_optimization(bottleneck_name, bottleneck_info)

    def _trigger_qualia_optimization(self, bottleneck_name: str, bottleneck_info: Dict) -> None:
        """Trigger QUALIA-specific optimization for bottlenecks."""
        # This would trigger actual QUALIA optimization procedures
        logger.info(f"Triggering QUALIA optimization for {bottleneck_name}")

        # Record optimization trigger
        self.record_metric("qualia.optimization.triggers", 1.0, MetricType.COUNTER, "count")

    def get_qualia_performance_summary(self) -> Dict[str, Any]:
        """Get QUALIA-specific performance summary."""
        qualia_metrics = {
            name: metric for name, metric in self.custom_metrics.items()
            if name.startswith("qualia.")
        }

        return {
            'qualia_metrics': {
                name: {
                    'value': metric.value,
                    'type': metric.metric_type.value,
                    'unit': metric.unit,
                    'timestamp': metric.timestamp
                }
                for name, metric in qualia_metrics.items()
            },
            'trading_performance': {
                'signals_generated': qualia_metrics.get('qualia.trading.signals_generated', PerformanceMetric('', 0.0, MetricType.GAUGE, '', 0.0)).value,
                'signals_executed': qualia_metrics.get('qualia.trading.signals_executed', PerformanceMetric('', 0.0, MetricType.GAUGE, '', 0.0)).value,
                'pnl_realized': qualia_metrics.get('qualia.trading.pnl_realized', PerformanceMetric('', 0.0, MetricType.GAUGE, '', 0.0)).value,
                'sharpe_ratio': qualia_metrics.get('qualia.trading.sharpe_ratio', PerformanceMetric('', 0.0, MetricType.GAUGE, '', 0.0)).value
            },
            'quantum_performance': {
                'consciousness_level': qualia_metrics.get('qualia.quantum.consciousness_level', PerformanceMetric('', 0.0, MetricType.GAUGE, '', 0.0)).value,
                'coherence_score': qualia_metrics.get('qualia.quantum.coherence_score', PerformanceMetric('', 0.0, MetricType.GAUGE, '', 0.0)).value,
                'entanglement_strength': qualia_metrics.get('qualia.quantum.entanglement_strength', PerformanceMetric('', 0.0, MetricType.GAUGE, '', 0.0)).value
            },
            'system_health': {
                'decision_confidence': qualia_metrics.get('qualia.decision.confidence_avg', PerformanceMetric('', 0.0, MetricType.GAUGE, '', 0.0)).value,
                'data_quality': qualia_metrics.get('qualia.data.quality_score', PerformanceMetric('', 0.0, MetricType.GAUGE, '', 0.0)).value,
                'cache_efficiency': qualia_metrics.get('qualia.data.cache_hit_rate', PerformanceMetric('', 0.0, MetricType.GAUGE, '', 0.0)).value
            }
        }


class PerformanceTimer:
    """Context manager for timing operations."""
    
    def __init__(self, collector: PerformanceMetricsCollector, name: str, tags: Optional[Dict[str, str]] = None):
        self.collector = collector
        self.name = name
        self.tags = tags
        self.start_time = 0.0
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        self.collector.record_timer(self.name, duration, self.tags)
