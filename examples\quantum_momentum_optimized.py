#!/usr/bin/env python3
"""
Quantum Momentum OTIMIZADA - Aplicando as correções identificadas
Testando se conseguimos superar a Composite Strategy.
"""

import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any

import pandas as pd
import numpy as np
import requests


class OptimizedQuantumMomentum:
    """Versão otimizada da Quantum Momentum com correções aplicadas."""
    
    def __init__(self):
        self.session = requests.Session()
    
    def fetch_data(self, symbol: str, days: int = 90) -> pd.DataFrame:
        """Busca dados históricos."""
        try:
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': symbol.replace('/', ''),
                'interval': '1h',
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index().dropna()
            
            # Indicadores
            df['returns'] = df['close'].pct_change()
            df['sma_20'] = df['close'].rolling(20).mean()
            df['sma_50'] = df['close'].rolling(50).mean()
            df['rsi'] = self._calculate_rsi(df['close'], 14)
            df['volatility'] = df['returns'].rolling(20).std()
            df['atr'] = self._calculate_atr(df, 14)
            
            return df
            
        except Exception as e:
            print(f"❌ Erro ao buscar dados para {symbol}: {e}")
            return pd.DataFrame()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calcula RSI."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calcula Average True Range."""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        return true_range.rolling(period).mean()
    
    def original_quantum_momentum(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Versão ORIGINAL (com problemas)."""
        signals = []
        
        for i in range(50, len(df)):
            # Momentum clássico
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
            
            signal = (
                price_momentum * 0.5 +
                vol_momentum * 0.3 +
                rsi_momentum * 0.2
            )
            
            # PROBLEMA: Threshold muito baixo (1.5%)
            if abs(signal) > 0.015:
                signals.append(np.clip(signal * 8, -1, 1))
            else:
                signals.append(0)
        
        return self._calculate_performance(df.iloc[50:], signals, "ORIGINAL_QM")
    
    def optimized_quantum_momentum(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Versão OTIMIZADA (com correções)."""
        signals = []
        
        for i in range(50, len(df)):
            # 1. MELHORIA: Filtros de qualidade
            
            # Filtro de volatilidade (evita mercados choppy)
            vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-20:i].quantile(0.7)
            
            # Filtro de trend (só opera com trend claro)
            trend_filter = abs(df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i] > 0.02
            
            # Filtro RSI (evita extremos)
            rsi_filter = 35 < df['rsi'].iloc[i] < 65
            
            if not (vol_filter and trend_filter and rsi_filter):
                signals.append(0)
                continue
            
            # 2. MELHORIA: Sinais mais robustos
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
            
            # Momentum de longo prazo
            long_momentum = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            
            signal = (
                price_momentum * 0.4 +
                vol_momentum * 0.2 +
                rsi_momentum * 0.2 +
                long_momentum * 0.2  # NOVO: momentum de longo prazo
            )
            
            # 3. MELHORIA: Threshold mais alto (3% vs 1.5%)
            if abs(signal) > 0.03:  # DOBROU o threshold
                signals.append(np.clip(signal * 6, -1, 1))  # Reduzido multiplicador
            else:
                signals.append(0)
        
        return self._calculate_performance_with_risk_mgmt(df.iloc[50:], signals, "OPTIMIZED_QM")
    
    def _calculate_performance(self, df: pd.DataFrame, signals: List[float], strategy_name: str) -> Dict[str, Any]:
        """Performance básica (versão original)."""
        positions = signals
        returns = []
        trades = 0
        
        for i in range(1, len(df)):
            if abs(positions[i-1]) > 0.1:
                price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
                position_return = positions[i-1] * price_return
                returns.append(position_return)
                trades += 1
            else:
                returns.append(0)
        
        if not returns:
            return {'error': 'Nenhum trade'}
        
        returns_series = pd.Series(returns)
        
        total_return = returns_series.sum()
        volatility = returns_series.std() * np.sqrt(252)
        sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
        
        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        winning_trades = (returns_series > 0).sum()
        win_rate = winning_trades / len(returns_series) if len(returns_series) > 0 else 0
        
        return {
            'strategy': strategy_name,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown * 100,
            'win_rate': win_rate * 100,
            'total_trades': trades,
            'volatility': volatility * 100,
            'avg_position': np.mean(np.abs(positions))
        }
    
    def _calculate_performance_with_risk_mgmt(self, df: pd.DataFrame, signals: List[float], strategy_name: str) -> Dict[str, Any]:
        """Performance com gestão de risco melhorada."""
        positions = signals
        returns = []
        trades = 0
        winning_trades = 0
        losing_trades = 0
        total_wins = 0
        total_losses = 0
        
        for i in range(1, len(df)):
            if abs(positions[i-1]) > 0.1:
                price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
                
                # 4. MELHORIA: Stop-loss e take-profit
                raw_return = positions[i-1] * price_return
                
                # Stop-loss: máximo -0.5%
                if raw_return < -0.005:
                    final_return = -0.005
                # Take-profit: mínimo +0.8%
                elif raw_return > 0.008:
                    final_return = 0.008
                else:
                    final_return = raw_return
                
                returns.append(final_return)
                trades += 1
                
                if final_return > 0:
                    winning_trades += 1
                    total_wins += final_return
                else:
                    losing_trades += 1
                    total_losses += abs(final_return)
            else:
                returns.append(0)
        
        if not returns:
            return {'error': 'Nenhum trade'}
        
        returns_series = pd.Series(returns)
        
        total_return = returns_series.sum()
        volatility = returns_series.std() * np.sqrt(252)
        sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
        
        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        win_rate = winning_trades / trades if trades > 0 else 0
        avg_win = total_wins / winning_trades if winning_trades > 0 else 0
        avg_loss = total_losses / losing_trades if losing_trades > 0 else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        return {
            'strategy': strategy_name,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown * 100,
            'win_rate': win_rate * 100,
            'total_trades': trades,
            'volatility': volatility * 100,
            'avg_position': np.mean(np.abs(positions)),
            'avg_win_pct': avg_win * 100,
            'avg_loss_pct': avg_loss * 100,
            'profit_factor': profit_factor,
            'win_loss_ratio': avg_win / avg_loss if avg_loss > 0 else 0
        }
    
    def composite_strategy(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Estratégia Composite para comparação."""
        signals = []
        
        for i in range(50, len(df)):
            trend_signal = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            mean_rev = -(df['close'].iloc[i] - df['sma_20'].iloc[i]) / df['sma_20'].iloc[i]
            momentum = df['close'].iloc[i-5:i].pct_change().mean()
            vol_adj = 1 / (1 + df['volatility'].iloc[i] * 100)
            
            composite_signal = (
                trend_signal * 0.3 +
                mean_rev * 0.3 +
                momentum * 0.4
            ) * vol_adj
            
            if abs(composite_signal) > 0.01:
                signals.append(np.clip(composite_signal * 12, -1, 1))
            else:
                signals.append(0)
        
        return self._calculate_performance(df.iloc[50:], signals, "COMPOSITE")


def run_optimization_test():
    """Testa a versão otimizada vs original."""
    print("🚀 TESTE: QUANTUM MOMENTUM OTIMIZADA vs ORIGINAL")
    print("=" * 60)
    
    optimizer = OptimizedQuantumMomentum()
    
    symbols = ["BTCUSDT", "ETHUSDT"]
    all_results = []
    
    for symbol in symbols:
        print(f"\n📈 Testando {symbol}...")
        
        df = optimizer.fetch_data(symbol, days=90)
        if df.empty or len(df) < 100:
            print(f"❌ Dados insuficientes para {symbol}")
            continue
        
        # Testa todas as versões
        original = optimizer.original_quantum_momentum(df)
        optimized = optimizer.optimized_quantum_momentum(df)
        composite = optimizer.composite_strategy(df)
        
        results = [original, optimized, composite]
        
        for result in results:
            if 'error' not in result:
                result['symbol'] = symbol
                all_results.append(result)
                print(f"   🧠 {result['strategy']}: "
                      f"Return {result['total_return_pct']:.2f}%, "
                      f"Sharpe {result['sharpe_ratio']:.3f}, "
                      f"Trades {result['total_trades']}")
    
    # Análise comparativa
    if all_results:
        print(f"\n" + "="*60)
        print(f"🏆 RESULTADOS DA OTIMIZAÇÃO")
        print(f"="*60)
        
        df_results = pd.DataFrame(all_results)
        
        # Comparação por estratégia
        strategy_comparison = df_results.groupby('strategy').agg({
            'total_return_pct': 'mean',
            'sharpe_ratio': 'mean',
            'win_rate': 'mean',
            'total_trades': 'mean',
            'max_drawdown_pct': 'mean'
        }).round(3)
        
        print(f"\n📊 COMPARAÇÃO MÉDIA:")
        for strategy in strategy_comparison.index:
            row = strategy_comparison.loc[strategy]
            print(f"\n🧠 {strategy}:")
            print(f"   Return: {row['total_return_pct']:.2f}%")
            print(f"   Sharpe: {row['sharpe_ratio']:.3f}")
            print(f"   Win Rate: {row['win_rate']:.1f}%")
            print(f"   Trades: {row['total_trades']:.0f}")
            print(f"   Max DD: {row['max_drawdown_pct']:.2f}%")
        
        # Verifica se otimização funcionou
        original_sharpe = strategy_comparison.loc['ORIGINAL_QM', 'sharpe_ratio']
        optimized_sharpe = strategy_comparison.loc['OPTIMIZED_QM', 'sharpe_ratio']
        composite_sharpe = strategy_comparison.loc['COMPOSITE', 'sharpe_ratio']
        
        print(f"\n🎯 RESULTADO DA OTIMIZAÇÃO:")
        improvement = optimized_sharpe - original_sharpe
        print(f"   Melhoria no Sharpe: {improvement:.3f}")
        
        if optimized_sharpe > original_sharpe:
            print(f"   ✅ OTIMIZAÇÃO FUNCIONOU!")
        else:
            print(f"   ❌ Otimização não melhorou")
        
        if optimized_sharpe > composite_sharpe:
            print(f"   🏆 QUANTUM MOMENTUM OTIMIZADA VENCEU A COMPOSITE!")
        else:
            print(f"   🥈 Composite ainda é superior")
        
        # Salva resultados
        output_dir = Path("results/quantum_optimization")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = int(time.time())
        with open(output_dir / f"optimization_results_{timestamp}.json", 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'improvement': float(improvement),
                'optimized_beats_original': bool(optimized_sharpe > original_sharpe),
                'optimized_beats_composite': bool(optimized_sharpe > composite_sharpe),
                'results': all_results
            }, f, indent=2)
        
        print(f"\n💾 Resultados salvos em results/quantum_optimization/")


if __name__ == "__main__":
    run_optimization_test()
