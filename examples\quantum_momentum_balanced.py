#!/usr/bin/env python3
"""
QUANTUM MOMENTUM BALANCED - Melhorias Incrementais e Equilibradas
Foco: Melhorar win rate e returns sem over-optimization
"""

import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any

import pandas as pd
import numpy as np
import requests


class QuantumMomentumBalanced:
    """Versão equilibrada com melhorias incrementais."""
    
    def __init__(self):
        self.session = requests.Session()
    
    def fetch_data(self, symbol: str, days: int = 90) -> pd.DataFrame:
        """Busca dados históricos."""
        try:
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': symbol.replace('/', ''),
                'interval': '1h',
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index().dropna()
            
            # Indicadores básicos
            df['returns'] = df['close'].pct_change()
            df['sma_20'] = df['close'].rolling(20).mean()
            df['sma_50'] = df['close'].rolling(50).mean()
            df['rsi'] = self._calculate_rsi(df['close'], 14)
            df['volatility'] = df['returns'].rolling(20).std()
            df['atr'] = self._calculate_atr(df, 14)
            
            # Indicadores adicionais (mínimos)
            df['volume_sma'] = df['volume'].rolling(20).mean()
            df['trend_strength'] = abs(df['sma_20'] - df['sma_50']) / df['sma_50']
            
            return df
            
        except Exception as e:
            print(f"❌ Erro ao buscar dados para {symbol}: {e}")
            return pd.DataFrame()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calcula RSI."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calcula Average True Range."""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        return true_range.rolling(period).mean()
    
    def quantum_momentum_current(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Versão ATUAL (baseline)."""
        signals = []
        
        for i in range(50, len(df)):
            # Filtros atuais
            vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-20:i].quantile(0.7)
            trend_filter = abs(df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i] > 0.02
            rsi_filter = 35 < df['rsi'].iloc[i] < 65
            
            if not (vol_filter and trend_filter and rsi_filter):
                signals.append(0)
                continue
            
            # Sinais atuais
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
            long_momentum = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            
            signal = (
                price_momentum * 0.4 +
                vol_momentum * 0.2 +
                rsi_momentum * 0.2 +
                long_momentum * 0.2
            )
            
            if abs(signal) > 0.03:
                signals.append(np.clip(signal * 6, -1, 1))
            else:
                signals.append(0)
        
        return self._calculate_performance_current(df.iloc[50:], signals, "CURRENT")
    
    def quantum_momentum_balanced(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Versão BALANCED com melhorias incrementais."""
        signals = []
        
        for i in range(50, len(df)):
            # 🔧 MELHORIA 1: Filtros ligeiramente mais seletivos (mas não restritivos)
            
            # Volatilidade: um pouco mais restritivo
            vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-20:i].quantile(0.65)  # 0.7 → 0.65
            
            # Trend: mantém o mesmo
            trend_filter = df['trend_strength'].iloc[i] > 0.02
            
            # RSI: ligeiramente mais conservador
            rsi_filter = 38 < df['rsi'].iloc[i] < 62  # 35-65 → 38-62
            
            # 🔧 MELHORIA 2: Adiciona apenas UM filtro extra (volume)
            volume_filter = df['volume'].iloc[i] > df['volume_sma'].iloc[i] * 0.7  # Volume mínimo
            
            if not (vol_filter and trend_filter and rsi_filter and volume_filter):
                signals.append(0)
                continue
            
            # 🔧 MELHORIA 3: Sinais ligeiramente melhorados
            
            price_momentum = (df['close'].iloc[i] / df['sma_20'].iloc[i] - 1)
            
            # Volume momentum com peso ligeiramente maior se volume for forte
            vol_momentum = (df['volume'].iloc[i] / df['volume'].iloc[i-20:i].mean() - 1)
            vol_strength = df['volume'].iloc[i] / df['volume_sma'].iloc[i]
            vol_weight = 0.2 if vol_strength < 1.2 else 0.25  # Peso adaptativo mínimo
            
            rsi_momentum = (df['rsi'].iloc[i] - df['rsi'].iloc[i-5:i].mean()) / 50
            long_momentum = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            
            # Combinação com pesos ligeiramente ajustados
            signal = (
                price_momentum * 0.38 +  # Ligeiramente reduzido
                vol_momentum * vol_weight +  # Adaptativo
                rsi_momentum * 0.17 +  # Ligeiramente reduzido
                long_momentum * 0.25  # Ligeiramente aumentado
            )
            
            # 🔧 MELHORIA 4: Threshold ligeiramente mais baixo para mais oportunidades
            if abs(signal) > 0.028:  # 0.03 → 0.028
                signals.append(np.clip(signal * 6, -1, 1))
            else:
                signals.append(0)
        
        return self._calculate_performance_balanced(df.iloc[50:], signals, "BALANCED")
    
    def _calculate_performance_current(self, df: pd.DataFrame, signals: List[float], strategy_name: str) -> Dict[str, Any]:
        """Performance atual (baseline)."""
        returns = []
        trades = 0
        winning_trades = 0
        total_wins = 0
        total_losses = 0
        
        for i in range(1, len(signals)):
            if abs(signals[i-1]) > 0.1:
                price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
                raw_return = signals[i-1] * price_return
                
                # Gestão de risco atual
                if raw_return < -0.005:
                    final_return = -0.005
                elif raw_return > 0.008:
                    final_return = 0.008
                else:
                    final_return = raw_return
                
                returns.append(final_return)
                trades += 1
                
                if final_return > 0:
                    winning_trades += 1
                    total_wins += final_return
                else:
                    total_losses += abs(final_return)
        
        if not returns:
            return {'error': 'Nenhum trade'}
        
        returns_series = pd.Series(returns)
        
        total_return = returns_series.sum()
        volatility = returns_series.std() * np.sqrt(252)
        sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
        
        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        win_rate = winning_trades / trades if trades > 0 else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        return {
            'strategy': strategy_name,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown * 100,
            'win_rate': win_rate * 100,
            'total_trades': trades,
            'volatility': volatility * 100,
            'profit_factor': profit_factor
        }
    
    def _calculate_performance_balanced(self, df: pd.DataFrame, signals: List[float], strategy_name: str) -> Dict[str, Any]:
        """Performance com melhorias equilibradas."""
        returns = []
        trades = 0
        winning_trades = 0
        total_wins = 0
        total_losses = 0
        
        for i in range(1, len(signals)):
            if abs(signals[i-1]) > 0.1:
                price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
                raw_return = signals[i-1] * price_return
                
                # 🔧 MELHORIA 5: Gestão de risco ligeiramente melhorada
                
                # Stop-loss ligeiramente mais generoso para melhorar win rate
                stop_loss = -0.0045  # -0.005 → -0.0045 (10% mais generoso)
                
                # Take-profit ligeiramente mais agressivo para melhorar returns
                take_profit = 0.010  # 0.008 → 0.010 (25% mais agressivo)
                
                if raw_return < stop_loss:
                    final_return = stop_loss
                elif raw_return > take_profit:
                    final_return = take_profit
                else:
                    final_return = raw_return
                
                returns.append(final_return)
                trades += 1
                
                if final_return > 0:
                    winning_trades += 1
                    total_wins += final_return
                else:
                    total_losses += abs(final_return)
        
        if not returns:
            return {'error': 'Nenhum trade'}
        
        returns_series = pd.Series(returns)
        
        total_return = returns_series.sum()
        volatility = returns_series.std() * np.sqrt(252)
        sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
        
        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        win_rate = winning_trades / trades if trades > 0 else 0
        avg_win = total_wins / winning_trades if winning_trades > 0 else 0
        avg_loss = total_losses / (trades - winning_trades) if (trades - winning_trades) > 0 else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        return {
            'strategy': strategy_name,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown * 100,
            'win_rate': win_rate * 100,
            'total_trades': trades,
            'volatility': volatility * 100,
            'profit_factor': profit_factor,
            'avg_win_pct': avg_win * 100,
            'avg_loss_pct': avg_loss * 100,
            'win_loss_ratio': avg_win / avg_loss if avg_loss > 0 else 0
        }


def run_balanced_test():
    """Testa versão equilibrada."""
    print("🔧 QUANTUM MOMENTUM BALANCED - Melhorias Incrementais")
    print("=" * 60)
    print("🎯 Objetivo: Win Rate >60%, Sharpe >0.5, Return >3%, Max DD <2%")
    print("=" * 60)
    
    balancer = QuantumMomentumBalanced()
    
    symbols = ["BTCUSDT", "ETHUSDT"]
    all_results = []
    
    for symbol in symbols:
        print(f"\n📈 Testando {symbol}...")
        
        df = balancer.fetch_data(symbol, days=90)
        if df.empty or len(df) < 100:
            print(f"❌ Dados insuficientes para {symbol}")
            continue
        
        # Testa ambas as versões
        current = balancer.quantum_momentum_current(df)
        balanced = balancer.quantum_momentum_balanced(df)
        
        if 'error' not in current:
            current['symbol'] = symbol
            all_results.append(current)
            
            print(f"\n📊 VERSÃO ATUAL:")
            print(f"   Return: {current['total_return_pct']:.2f}%")
            print(f"   Sharpe: {current['sharpe_ratio']:.3f}")
            print(f"   Win Rate: {current['win_rate']:.1f}%")
            print(f"   Trades: {current['total_trades']}")
            print(f"   Max DD: {current['max_drawdown_pct']:.2f}%")
            print(f"   Profit Factor: {current['profit_factor']:.2f}")
        
        if 'error' not in balanced:
            balanced['symbol'] = symbol
            all_results.append(balanced)
            
            print(f"\n🔧 VERSÃO BALANCED:")
            print(f"   Return: {balanced['total_return_pct']:.2f}%")
            print(f"   Sharpe: {balanced['sharpe_ratio']:.3f}")
            print(f"   Win Rate: {balanced['win_rate']:.1f}%")
            print(f"   Trades: {balanced['total_trades']}")
            print(f"   Max DD: {balanced['max_drawdown_pct']:.2f}%")
            print(f"   Profit Factor: {balanced['profit_factor']:.2f}")
            if 'win_loss_ratio' in balanced:
                print(f"   Win/Loss Ratio: {balanced['win_loss_ratio']:.2f}")
        
        # Comparação
        if 'error' not in current and 'error' not in balanced:
            print(f"\n📈 MELHORIAS:")
            print(f"   Win Rate: {balanced['win_rate'] - current['win_rate']:+.1f}% pontos")
            print(f"   Sharpe: {balanced['sharpe_ratio'] - current['sharpe_ratio']:+.3f}")
            print(f"   Return: {balanced['total_return_pct'] - current['total_return_pct']:+.2f}%")
            
            # Status vs objetivos
            print(f"\n🎯 STATUS VS OBJETIVOS (Balanced):")
            print(f"   Win Rate: {balanced['win_rate']:.1f}% {'✅' if balanced['win_rate'] >= 60 else '❌'} (>60%)")
            print(f"   Sharpe: {balanced['sharpe_ratio']:.3f} {'✅' if balanced['sharpe_ratio'] >= 0.5 else '❌'} (>0.5)")
            print(f"   Return: {balanced['total_return_pct']:.2f}% {'✅' if balanced['total_return_pct'] >= 3 else '❌'} (>3%)")
            print(f"   Max DD: {balanced['max_drawdown_pct']:.2f}% {'✅' if balanced['max_drawdown_pct'] <= 2 else '❌'} (<2%)")
    
    # Análise final
    if all_results:
        current_results = [r for r in all_results if r['strategy'] == 'CURRENT']
        balanced_results = [r for r in all_results if r['strategy'] == 'BALANCED']
        
        if current_results and balanced_results:
            print(f"\n" + "="*60)
            print(f"📊 RESULTADO FINAL")
            print(f"="*60)
            
            # Médias
            balanced_avg = {
                'win_rate': np.mean([r['win_rate'] for r in balanced_results]),
                'sharpe_ratio': np.mean([r['sharpe_ratio'] for r in balanced_results]),
                'total_return_pct': np.mean([r['total_return_pct'] for r in balanced_results]),
                'max_drawdown_pct': np.mean([r['max_drawdown_pct'] for r in balanced_results])
            }
            
            print(f"\n🎯 PERFORMANCE MÉDIA (Balanced):")
            print(f"   Win Rate: {balanced_avg['win_rate']:.1f}%")
            print(f"   Sharpe: {balanced_avg['sharpe_ratio']:.3f}")
            print(f"   Return: {balanced_avg['total_return_pct']:.2f}%")
            print(f"   Max DD: {balanced_avg['max_drawdown_pct']:.2f}%")
            
            # Verifica objetivos
            objectives_met = (
                balanced_avg['win_rate'] >= 60 and
                balanced_avg['sharpe_ratio'] >= 0.5 and
                balanced_avg['total_return_pct'] >= 3 and
                balanced_avg['max_drawdown_pct'] <= 2
            )
            
            if objectives_met:
                print(f"\n✅ TODOS OS OBJETIVOS ATINGIDOS!")
                print(f"🏆 Quantum Momentum Balanced está pronta!")
            else:
                print(f"\n🔧 Progresso feito, mas ainda precisamos de mais ajustes")
                
                # Próximas melhorias sugeridas
                print(f"\n💡 PRÓXIMAS MELHORIAS SUGERIDAS:")
                if balanced_avg['win_rate'] < 60:
                    print(f"   • Win Rate: Ajustar stop-loss para {-0.004:.3f}")
                if balanced_avg['total_return_pct'] < 3:
                    print(f"   • Return: Aumentar take-profit para {0.012:.3f}")
            
            # Salva resultados
            output_dir = Path("results/quantum_momentum_balanced")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = int(time.time())
            with open(output_dir / f"balanced_results_{timestamp}.json", 'w') as f:
                json.dump({
                    'timestamp': datetime.now().isoformat(),
                    'objectives_met': objectives_met,
                    'balanced_avg': balanced_avg,
                    'all_results': all_results
                }, f, indent=2, default=str)
            
            print(f"\n💾 Resultados salvos em results/quantum_momentum_balanced/")


if __name__ == "__main__":
    run_balanced_test()
