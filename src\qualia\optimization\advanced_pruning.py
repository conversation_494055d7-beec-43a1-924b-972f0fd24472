"""
QUALIA Advanced Pruning System - D-04 Implementation

Sistema avançado de pruning para otimização Bayesiana, incluindo:
- MedianPruner adaptativo com configuração dinâmica
- SuccessiveHalving para alocação progressiva de recursos
- Multi-fidelity optimization com diferentes níveis de tempo
- Pruning baseado em métricas de trading (Sharpe, drawdown, PnL)
"""

from __future__ import annotations

import time
import logging
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
import numpy as np

import optuna
from optuna.pruners import MedianPruner, SuccessiveHalvingPruner, NopPruner
from optuna.trial import TrialState

from ..utils.logger import get_logger

logger = get_logger(__name__)


class PruningStrategy(Enum):
    """Estratégias de pruning disponíveis."""

    NONE = "none"
    MEDIAN = "median"
    SUCCESSIVE_HALVING = "successive_halving"
    ADAPTIVE = "adaptive"
    TRADING_AWARE = "trading_aware"
    MULTI_FIDELITY = "multi_fidelity"


class MarketRegime(Enum):
    """Regimes de mercado para ajuste de pruning."""

    BULL = "bull"
    BEAR = "bear"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    STABLE = "stable"


@dataclass
class PruningConfig:
    """Configuração do sistema de pruning."""

    # Estratégia principal
    strategy: PruningStrategy = PruningStrategy.ADAPTIVE

    # MedianPruner settings
    median_n_startup_trials: int = 10
    median_n_warmup_steps: int = 5
    median_interval_steps: int = 1
    median_percentile: float = 50.0  # Percentil para pruning (50% = mediana)

    # SuccessiveHalving settings
    sh_min_resource: int = 1  # Mínimo de recursos (horas de backtesting)
    sh_reduction_factor: int = 3  # Fator de redução
    sh_min_early_stopping_rate: int = 0

    # Multi-fidelity settings
    fidelity_levels: List[int] = field(default_factory=lambda: [1, 6, 24])  # Horas
    fidelity_budget_ratios: List[float] = field(default_factory=lambda: [0.5, 0.3, 0.2])

    # Trading-aware pruning
    min_sharpe_threshold: float = 0.5  # Sharpe mínimo para continuar
    max_drawdown_threshold: float = 0.15  # Drawdown máximo (15%)
    min_trades_threshold: int = 10  # Mínimo de trades para avaliar

    # Adaptive settings
    adaptive_percentile_range: Tuple[float, float] = (25.0, 75.0)
    adaptive_adjustment_factor: float = 0.1

    # Market regime adjustments
    regime_adjustments: Dict[MarketRegime, Dict[str, float]] = field(
        default_factory=lambda: {
            MarketRegime.BULL: {"percentile_adj": -10.0, "sharpe_adj": -0.2},
            MarketRegime.BEAR: {"percentile_adj": +15.0, "sharpe_adj": +0.3},
            MarketRegime.VOLATILE: {"percentile_adj": +5.0, "drawdown_adj": -0.05},
            MarketRegime.STABLE: {"percentile_adj": -5.0, "drawdown_adj": +0.05},
        }
    )


class TradingMetrics:
    """Métricas de trading para decisões de pruning."""

    def __init__(self):
        self.sharpe_ratio: Optional[float] = None
        self.max_drawdown: Optional[float] = None
        self.total_pnl: Optional[float] = None
        self.trade_count: int = 0
        self.win_rate: Optional[float] = None
        self.profit_factor: Optional[float] = None
        self.calmar_ratio: Optional[float] = None

    def is_valid(self) -> bool:
        """Verifica se as métricas são válidas para pruning."""
        return (
            self.sharpe_ratio is not None
            and self.max_drawdown is not None
            and self.trade_count >= 5
        )

    def should_prune(self, config: PruningConfig) -> bool:
        """Determina se deve fazer pruning baseado nas métricas."""
        if not self.is_valid():
            return False

        # Verificar thresholds críticos
        if self.sharpe_ratio < config.min_sharpe_threshold:
            logger.debug(
                f"Pruning: Sharpe {self.sharpe_ratio:.3f} < {config.min_sharpe_threshold}"
            )
            return True

        if self.max_drawdown > config.max_drawdown_threshold:
            logger.debug(
                f"Pruning: Drawdown {self.max_drawdown:.3f} > {config.max_drawdown_threshold}"
            )
            return True

        if self.trade_count < config.min_trades_threshold:
            logger.debug(
                f"Pruning: Trades {self.trade_count} < {config.min_trades_threshold}"
            )
            return True

        return False


class AdvancedPruner:
    """
    Sistema avançado de pruning para otimização Bayesiana.

    Combina múltiplas estratégias de pruning com awareness de métricas de trading
    e adaptação a regimes de mercado.
    """

    def __init__(self, config: PruningConfig = None):
        self.config = config or PruningConfig()
        self.current_regime = MarketRegime.SIDEWAYS
        self.pruning_history: List[Dict[str, Any]] = []
        self.performance_buffer: Dict[str, List[float]] = {}

        # Criar pruners específicos
        self._create_pruners()

        logger.info(
            f"[CONFIG] AdvancedPruner inicializado com estratégia: {self.config.strategy.value}"
        )

    def _create_pruners(self):
        """Cria os pruners específicos baseados na configuração."""

        # MedianPruner adaptativo
        self.median_pruner = MedianPruner(
            n_startup_trials=self.config.median_n_startup_trials,
            n_warmup_steps=self.config.median_n_warmup_steps,
            interval_steps=self.config.median_interval_steps,
        )

        # SuccessiveHalving adaptativo
        self.sh_pruner = self._create_adaptive_successive_halving()

        # NopPruner para casos sem pruning
        self.nop_pruner = NopPruner()

    def get_pruner(self, study_name: str = None) -> optuna.pruners.BasePruner:
        """
        Retorna o pruner apropriado baseado na estratégia configurada.

        Args:
            study_name: Nome do estudo para personalização

        Returns:
            Pruner configurado
        """

        if self.config.strategy == PruningStrategy.NONE:
            return self.nop_pruner

        elif self.config.strategy == PruningStrategy.MEDIAN:
            return self._get_adaptive_median_pruner()

        elif self.config.strategy == PruningStrategy.SUCCESSIVE_HALVING:
            return self.sh_pruner

        elif self.config.strategy == PruningStrategy.ADAPTIVE:
            return self._get_adaptive_pruner(study_name)

        elif self.config.strategy == PruningStrategy.TRADING_AWARE:
            return self._get_trading_aware_pruner(study_name)

        elif self.config.strategy == PruningStrategy.MULTI_FIDELITY:
            return self._get_multi_fidelity_pruner()

        else:
            logger.warning(
                f"Estratégia desconhecida: {self.config.strategy}, usando MedianPruner"
            )
            return self.median_pruner

    def _get_adaptive_median_pruner(self) -> MedianPruner:
        """Retorna MedianPruner com configuração adaptativa baseada no regime de mercado."""

        # Ajustar parâmetros baseado no regime de mercado
        n_startup_trials = self.config.median_n_startup_trials
        n_warmup_steps = self.config.median_n_warmup_steps

        if self.current_regime in self.config.regime_adjustments:
            regime_adj = self.config.regime_adjustments[self.current_regime]

            # Ajustar startup trials baseado no regime
            if self.current_regime == MarketRegime.VOLATILE:
                n_startup_trials = max(
                    5, int(n_startup_trials * 1.5)
                )  # Mais trials em mercado volátil
            elif self.current_regime == MarketRegime.STABLE:
                n_startup_trials = max(
                    3, int(n_startup_trials * 0.7)
                )  # Menos trials em mercado estável

            # Ajustar warmup steps
            if self.current_regime == MarketRegime.BEAR:
                n_warmup_steps = max(
                    3, int(n_warmup_steps * 1.3)
                )  # Mais warmup em bear market

        percentile = (
            self.config.median_percentile + regime_adj.get("percentile_adj", 0.0)
            if self.current_regime in self.config.regime_adjustments
            else self.config.median_percentile
        )

        logger.info(
            f"📊 MedianPruner adaptativo: regime={self.current_regime.value}, "
            f"startup_trials={n_startup_trials}, warmup_steps={n_warmup_steps}"
        )

        return MedianPruner(
            n_startup_trials=n_startup_trials,
            n_warmup_steps=n_warmup_steps,
            interval_steps=self.config.median_interval_steps,
            percentile=percentile,
        )

    def _create_adaptive_successive_halving(self) -> SuccessiveHalvingPruner:
        """Cria SuccessiveHalving adaptativo baseado no regime de mercado."""

        min_resource = self.config.sh_min_resource
        reduction_factor = self.config.sh_reduction_factor

        # Ajustar parâmetros baseado no regime
        if self.current_regime == MarketRegime.VOLATILE:
            # Em mercado volátil, dar mais recursos iniciais
            min_resource = max(2, int(min_resource * 1.5))
            reduction_factor = max(2, reduction_factor - 1)  # Redução mais suave
        elif self.current_regime == MarketRegime.STABLE:
            # Em mercado estável, ser mais agressivo no pruning
            reduction_factor = min(5, reduction_factor + 1)  # Redução mais agressiva
        elif self.current_regime == MarketRegime.BEAR:
            # Em bear market, ser mais conservador
            min_resource = max(3, int(min_resource * 2))

        logger.info(
            f"📊 SuccessiveHalving adaptativo: regime={self.current_regime.value}, "
            f"min_resource={min_resource}, reduction_factor={reduction_factor}"
        )

        return SuccessiveHalvingPruner(
            min_resource=min_resource,
            reduction_factor=reduction_factor,
            min_early_stopping_rate=self.config.sh_min_early_stopping_rate,
        )

    def record_trial_result(self, study_name: str, value: float) -> None:
        """Armazena desempenho de trials para ajustes adaptativos."""

        history = self.performance_buffer.setdefault(study_name, [])
        history.append(float(value))
        if len(history) > 50:
            del history[:-50]

    def _get_adaptive_pruner(self, study_name: str | None = None) -> MedianPruner:
        """Retorna pruner adaptativo baseado em performance recente e regime."""

        percentile = self.config.median_percentile

        # Ajuste por regime de mercado
        regime_adj = self.config.regime_adjustments.get(self.current_regime, {})
        percentile += regime_adj.get("percentile_adj", 0.0)

        # Ajuste dinâmico conforme histórico de performance
        history = self.performance_buffer.get(study_name or "default", [])
        if len(history) >= 2:
            recent_improvement = history[-1] - np.mean(history[:-1])
            delta = (
                np.sign(recent_improvement)
                * self.config.adaptive_adjustment_factor
                * 100
            )
            percentile -= delta

        low, high = self.config.adaptive_percentile_range
        percentile = float(np.clip(percentile, low, high))

        logger.debug(
            "[ADAPTIVE] Percentile %.1f%% (regime=%s, history=%d)",
            percentile,
            self.current_regime.value,
            len(history),
        )

        return MedianPruner(
            n_startup_trials=self.config.median_n_startup_trials,
            n_warmup_steps=self.config.median_n_warmup_steps,
            interval_steps=self.config.median_interval_steps,
            percentile=percentile,
        )

    def _get_trading_aware_pruner(self, study_name: str | None = None) -> MedianPruner:
        """Retorna pruner que considera métricas de trading."""

        return self._get_adaptive_pruner(study_name)

    def _get_multi_fidelity_pruner(self) -> SuccessiveHalvingPruner:
        """Retorna pruner para otimização multi-fidelidade."""

        return SuccessiveHalvingPruner(
            min_resource=min(self.config.fidelity_levels),
            reduction_factor=self.config.sh_reduction_factor,
            min_early_stopping_rate=self.config.sh_min_early_stopping_rate,
        )

    def should_prune_trial(
        self, trial: optuna.Trial, metrics: TradingMetrics = None
    ) -> bool:
        """
        Determina se um trial deve ser podado baseado em métricas customizadas.

        Args:
            trial: Trial do Optuna
            metrics: Métricas de trading

        Returns:
            True se deve podar, False caso contrário
        """

        # Se temos métricas de trading, usar lógica trading-aware
        if metrics and self.config.strategy in [
            PruningStrategy.TRADING_AWARE,
            PruningStrategy.ADAPTIVE,
        ]:
            if metrics.should_prune(self.config):
                self._record_pruning_decision(trial, "trading_metrics", metrics)
                return True

        # Usar pruner padrão do Optuna
        pruner = self.get_pruner()
        should_prune = pruner.prune(trial)

        if should_prune:
            self._record_pruning_decision(trial, "optuna_pruner", metrics)

        return should_prune

    def update_market_regime(self, new_regime: MarketRegime) -> None:
        """
        Atualiza o regime de mercado atual e reconfigura pruners se necessário.

        Args:
            new_regime: Novo regime de mercado detectado
        """
        if new_regime != self.current_regime:
            old_regime = self.current_regime
            self.current_regime = new_regime

            # Recriar pruners com nova configuração
            self._create_pruners()

            logger.info(
                f"🔄 Regime de mercado atualizado: {old_regime.value} → {new_regime.value}"
            )
            logger.info(f"📊 Pruners reconfigurados para regime: {new_regime.value}")

    def _record_pruning_decision(
        self, trial: optuna.Trial, reason: str, metrics: TradingMetrics = None
    ):
        """Registra decisão de pruning para análise."""

        record = {
            "trial_number": trial.number,
            "trial_params": trial.params,
            "reason": reason,
            "timestamp": time.time(),
            "regime": self.current_regime.value,
        }

        if metrics:
            record.update(
                {
                    "sharpe_ratio": metrics.sharpe_ratio,
                    "max_drawdown": metrics.max_drawdown,
                    "trade_count": metrics.trade_count,
                    "total_pnl": metrics.total_pnl,
                }
            )

        self.pruning_history.append(record)

        logger.info(f"🔪 Trial {trial.number} podado: {reason}")

    def update_market_regime(self, regime: MarketRegime):
        """Atualiza o regime de mercado atual."""
        if regime != self.current_regime:
            logger.info(
                f"[STATS] Regime de mercado atualizado: {self.current_regime.value} -> {regime.value}"
            )
            self.current_regime = regime

            # Recriar pruners com nova configuração
            self._create_pruners()

    def get_pruning_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas de pruning."""

        if not self.pruning_history:
            return {"total_pruned": 0, "pruning_reasons": {}}

        total_pruned = len(self.pruning_history)
        reasons = {}

        for record in self.pruning_history:
            reason = record["reason"]
            reasons[reason] = reasons.get(reason, 0) + 1

        return {
            "total_pruned": total_pruned,
            "pruning_reasons": reasons,
            "current_regime": self.current_regime.value,
            "recent_prunings": (
                self.pruning_history[-5:]
                if len(self.pruning_history) >= 5
                else self.pruning_history
            ),
        }


@dataclass
class MultiFidelityConfig:
    """Configuração para otimização multi-fidelidade."""

    # Níveis de fidelidade (em horas de backtesting)
    fidelity_levels: List[int] = field(default_factory=lambda: [1, 6, 24])

    # Orçamento de trials por nível
    budget_ratios: List[float] = field(default_factory=lambda: [0.5, 0.3, 0.2])

    # Critérios de promoção entre níveis
    promotion_percentile: float = 0.3  # Top 30% promovidos
    min_trials_for_promotion: int = 5

    # Métricas por nível de fidelidade
    metrics_weights: Dict[str, List[float]] = field(
        default_factory=lambda: {
            "sharpe_ratio": [0.4, 0.5, 0.6],  # Mais peso em fidelidade alta
            "pnl": [0.3, 0.3, 0.3],
            "drawdown": [0.3, 0.2, 0.1],  # Menos peso em fidelidade alta
        }
    )


class MultiFidelityOptimizer:
    """
    Sistema de otimização multi-fidelidade para QUALIA.

    Implementa alocação progressiva de recursos onde trials promissores
    recebem mais tempo de backtesting para avaliação mais precisa.
    """

    def __init__(self, config: MultiFidelityConfig = None):
        self.config = config or MultiFidelityConfig()
        self.trial_fidelity_map: Dict[int, int] = {}  # trial_id -> fidelity_level
        self.trial_results: Dict[int, Dict[str, Any]] = {}
        self.promotion_history: List[Dict[str, Any]] = []

        logger.info(
            f"🎯 MultiFidelityOptimizer inicializado com níveis: {self.config.fidelity_levels}"
        )

    def get_fidelity_for_trial(
        self, trial_number: int, study_trials: List[optuna.Trial]
    ) -> int:
        """
        Determina o nível de fidelidade para um trial.

        Args:
            trial_number: Número do trial
            study_trials: Lista de trials do estudo

        Returns:
            Nível de fidelidade (horas de backtesting)
        """

        # Novos trials começam no nível mais baixo
        if trial_number not in self.trial_fidelity_map:
            self.trial_fidelity_map[trial_number] = self.config.fidelity_levels[0]
            logger.debug(
                f"🎯 Trial {trial_number} iniciado no nível {self.config.fidelity_levels[0]}h"
            )

        return self.trial_fidelity_map[trial_number]

    def should_promote_trial(self, trial: optuna.Trial, current_fidelity: int) -> bool:
        """
        Determina se um trial deve ser promovido para maior fidelidade.

        Args:
            trial: Trial do Optuna
            current_fidelity: Nível atual de fidelidade

        Returns:
            True se deve ser promovido
        """

        # Verificar se há nível superior disponível
        try:
            current_idx = self.config.fidelity_levels.index(current_fidelity)
        except ValueError:
            return False

        if current_idx >= len(self.config.fidelity_levels) - 1:
            return False  # Já no nível máximo

        # Verificar se tem trials suficientes para comparação
        completed_trials = [
            t for t in trial.study.trials if t.state == TrialState.COMPLETE
        ]
        if len(completed_trials) < self.config.min_trials_for_promotion:
            return False

        # Calcular percentil do trial atual
        trial_value = trial.value if trial.value is not None else float("-inf")
        values = [t.value for t in completed_trials if t.value is not None]

        if not values:
            return False

        percentile_rank = sum(1 for v in values if v < trial_value) / len(values)

        # Promover se estiver no top percentil
        should_promote = percentile_rank >= (1.0 - self.config.promotion_percentile)

        if should_promote:
            next_fidelity = self.config.fidelity_levels[current_idx + 1]
            self.trial_fidelity_map[trial.number] = next_fidelity

            self.promotion_history.append(
                {
                    "trial_number": trial.number,
                    "from_fidelity": current_fidelity,
                    "to_fidelity": next_fidelity,
                    "percentile_rank": percentile_rank,
                    "timestamp": time.time(),
                }
            )

            logger.info(
                f"🚀 Trial {trial.number} promovido: {current_fidelity}h → {next_fidelity}h "
                f"(percentil: {percentile_rank:.2%})"
            )

        return should_promote

    def calculate_multi_fidelity_score(
        self, metrics: Dict[str, float], fidelity_level: int
    ) -> float:
        """
        Calcula score ajustado para o nível de fidelidade.

        Args:
            metrics: Métricas do trial
            fidelity_level: Nível de fidelidade usado

        Returns:
            Score ajustado
        """

        try:
            fidelity_idx = self.config.fidelity_levels.index(fidelity_level)
        except ValueError:
            fidelity_idx = 0

        total_score = 0.0

        for metric_name, weights in self.config.metrics_weights.items():
            if metric_name in metrics:
                weight = (
                    weights[fidelity_idx]
                    if fidelity_idx < len(weights)
                    else weights[-1]
                )
                total_score += metrics[metric_name] * weight

        # Ajuste de confiança baseado na fidelidade
        confidence_multiplier = (
            0.5 + (fidelity_idx / len(self.config.fidelity_levels)) * 0.5
        )

        return total_score * confidence_multiplier

    def get_statistics(self) -> Dict[str, Any]:
        """Retorna estatísticas do sistema multi-fidelidade."""

        fidelity_counts = {}
        for fidelity in self.trial_fidelity_map.values():
            fidelity_counts[fidelity] = fidelity_counts.get(fidelity, 0) + 1

        return {
            "total_trials": len(self.trial_fidelity_map),
            "fidelity_distribution": fidelity_counts,
            "promotions": len(self.promotion_history),
            "promotion_rate": len(self.promotion_history)
            / max(1, len(self.trial_fidelity_map)),
            "recent_promotions": (
                self.promotion_history[-5:] if self.promotion_history else []
            ),
        }
