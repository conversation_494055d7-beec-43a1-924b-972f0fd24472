"""Exemplo manual que consulta ``fetch_ticker`` em múltiplas exchanges."""

import asyncio
import time
import ccxt.async_support as ccxt_async

EXCHANGES = [
    "kraken",
    "binance",
    "coinbase",
    "bitfinex",
    "kucoin",
]
PAIRS = [
    "BTC/USD",
    "BTC/USDT",
    "ETH/USD",
    "ETH/USDT",
]


async def fetch_ticker(exchange_id, pair):
    try:
        exchange_class = getattr(ccxt_async, exchange_id)
        exchange = exchange_class()
        await exchange.load_markets()
        # Normalizar símbolo para o formato aceito
        symbol = pair
        if pair not in exchange.markets:
            # Tentar encontrar símbolo equivalente (ex: market['id'])
            for m in exchange.markets.values():
                if m["base"] in pair and m["quote"] in pair:
                    symbol = m["id"]
                    break
        start = time.time()
        ticker = await exchange.fetch_ticker(symbol)
        elapsed = time.time() - start
        await exchange.close()
        return {
            "exchange": exchange_id,
            "pair": pair,
            "symbol_used": symbol,
            "success": True,
            "elapsed": elapsed,
            "ticker": ticker,
        }
    except Exception as e:
        return {
            "exchange": exchange_id,
            "pair": pair,
            "symbol_used": symbol if "symbol" in locals() else pair,
            "success": False,
            "elapsed": None,
            "error": str(e),
        }


async def main():
    results = []
    for exchange_id in EXCHANGES:
        for pair in PAIRS:
            print(f"Testando {exchange_id} - {pair}...")
            res = await fetch_ticker(exchange_id, pair)
            results.append(res)
    print("\n--- Relatório ---")
    for r in results:
        if r["success"]:
            print(
                f"{r['exchange']} | {r['pair']} | símbolo usado: {r['symbol_used']} | OK | tempo: {r['elapsed']:.2f}s"
            )
        else:
            print(
                f"{r['exchange']} | {r['pair']} | símbolo usado: {r['symbol_used']} | ERRO: {r['error']}"
            )


if __name__ == "__main__":
    asyncio.run(main())
