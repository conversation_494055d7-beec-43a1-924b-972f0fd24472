#!/usr/bin/env python3
"""
Simple Signal Generator Test
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_signal_generator():
    print("🧪 Testing Signal Generator Integration...")
    
    try:
        # Test import
        print("1. Testing import...")
        from qualia_pilot_trading_system import RealSignalGenerator
        print("✅ Import successful")
        
        # Test initialization
        print("2. Testing initialization...")
        config = {'symbol': 'BTCUSDT', 'min_confidence': 0.8}
        signal_gen = RealSignalGenerator(config)
        print("✅ Initialization successful")
        
        # Test attributes
        print("3. Testing attributes...")
        assert hasattr(signal_gen, 'ultra_conservative_min_confidence')
        assert signal_gen.ultra_conservative_min_confidence == 0.85
        print("✅ Attributes validated")
        
        print("\n🎉 All tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_signal_generator()
    sys.exit(0 if success else 1)
