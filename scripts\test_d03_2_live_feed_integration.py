#!/usr/bin/env python3
"""
YAA: D-03.2 - Test Live Feed Integration with QUALIATradingSystem

Este script testa se a integração do live feed (validado no D-03.1) está
funcionando corretamente com o QUALIATradingSystem.

Testa:
1. Inicialização da LiveFeedIntegration no QUALIATradingSystem
2. Carregamento correto da configuração
3. Conexão com componentes validados (KuCoinFeed, DataNormalizer, etc.)
4. Status da integração e funcionamento
5. Dados sendo processados corretamente
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

# Adicionar o diretório raiz ao path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configurar PYTHONPATH para importações
import os
os.environ['PYTHONPATH'] = str(Path(__file__).parent.parent)

try:
    from src.qualia.qualia_trading_system import QUALIARealTimeTrader
except ImportError as e:
    logger.error(f"Erro ao importar QUALIARealTimeTrader: {e}")
    sys.exit(1)

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class D032LiveFeedIntegrationTester:
    """Testa a integração do live feed com o sistema de trading."""
    
    def __init__(self):
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "test_name": "D-03.2 Live Feed Integration Test",
            "components_tested": [],
            "test_results": [],
            "overall_status": "PENDING"
        }
        
    async def test_trading_system_initialization(self) -> bool:
        """Testa se o QUALIATradingSystem inicializa com live feed."""
        try:
            logger.info("🧪 Testando inicialização do QUALIATradingSystem com Live Feed...")
            
            # Configurar trader com live feed habilitado
            trader = QUALIARealTimeTrader(
                symbols=["BTC/USDT", "ETH/USDT"],
                timeframes=["5m", "1h"],
                capital=10000.0,
                risk_profile="moderate",
                mode="paper_trading",
                data_source="kucoin",
                duration_seconds=30,  # Teste curto
                config={
                    "live_feed": {
                        "enabled": True,
                        "mode": "hybrid"
                    }
                }
            )
            
            # Inicializar sistema
            await trader.initialize()
            
            # Verificar se LiveFeedIntegration foi criada
            has_live_feed = trader.live_feed_integration is not None
            
            self.test_results["components_tested"].append("QUALIATradingSystem.live_feed_integration")
            self.test_results["test_results"].append({
                "test": "trading_system_initialization",
                "status": "PASS" if has_live_feed else "FAIL",
                "details": f"LiveFeedIntegration criada: {has_live_feed}"
            })
            
            await trader.close_exchange()
            return has_live_feed
            
        except Exception as e:
            logger.error(f"❌ Erro no teste de inicialização: {e}")
            self.test_results["test_results"].append({
                "test": "trading_system_initialization",
                "status": "ERROR",
                "error": str(e)
            })
            return False
    
    async def test_live_feed_configuration(self) -> bool:
        """Testa se a configuração do live feed está sendo carregada corretamente."""
        try:
            logger.info("🧪 Testando carregamento da configuração do Live Feed...")
            
            # Carregar configuração
            config_path = Path("config/holographic_trading_config.yaml")
            if not config_path.exists():
                raise FileNotFoundError(f"Arquivo de configuração não encontrado: {config_path}")
            
            import yaml
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # Verificar seções necessárias
            live_feed_config = config_data.get("live_feed", {})
            required_keys = ["enabled", "mode", "optimized_params"]
            
            config_valid = all(key in live_feed_config for key in required_keys)
            enabled = live_feed_config.get("enabled", False)
            
            self.test_results["components_tested"].append("holographic_trading_config.yaml")
            self.test_results["test_results"].append({
                "test": "live_feed_configuration",
                "status": "PASS" if config_valid and enabled else "FAIL",
                "details": {
                    "config_valid": config_valid,
                    "enabled": enabled,
                    "mode": live_feed_config.get("mode"),
                    "optimized_params": live_feed_config.get("optimized_params", {})
                }
            })
            
            return config_valid and enabled
            
        except Exception as e:
            logger.error(f"❌ Erro no teste de configuração: {e}")
            self.test_results["test_results"].append({
                "test": "live_feed_configuration",
                "status": "ERROR",
                "error": str(e)
            })
            return False
    
    async def test_live_feed_components_availability(self) -> bool:
        """Testa se os componentes do live feed estão disponíveis."""
        try:
            logger.info("🧪 Testando disponibilidade dos componentes do Live Feed...")
            
            # Testar importações
            components_to_test = [
                ("LiveFeedIntegration", "src.qualia.consciousness.live_feed_integration"),
                ("LiveFeedDataCollector", "src.qualia.consciousness.live_feed_data_collector"),
                ("KuCoinFeed", "src.qualia.consciousness.feeds.kucoin_feed"),
                ("DataNormalizer", "src.qualia.consciousness.data_normalizer"),
                ("FeedManager", "src.qualia.consciousness.feed_manager"),
                ("FeedAggregator", "src.qualia.consciousness.feed_aggregator")
            ]
            
            import_results = []
            for component_name, module_path in components_to_test:
                try:
                    module = __import__(module_path, fromlist=[component_name])
                    component_class = getattr(module, component_name)
                    import_results.append({
                        "component": component_name,
                        "status": "AVAILABLE",
                        "module": module_path
                    })
                except Exception as e:
                    import_results.append({
                        "component": component_name,
                        "status": "ERROR",
                        "error": str(e)
                    })
            
            all_available = all(r["status"] == "AVAILABLE" for r in import_results)
            
            self.test_results["components_tested"].extend([r["component"] for r in import_results])
            self.test_results["test_results"].append({
                "test": "live_feed_components_availability",
                "status": "PASS" if all_available else "FAIL",
                "details": import_results
            })
            
            return all_available
            
        except Exception as e:
            logger.error(f"❌ Erro no teste de componentes: {e}")
            self.test_results["test_results"].append({
                "test": "live_feed_components_availability",
                "status": "ERROR",
                "error": str(e)
            })
            return False
    
    async def run_all_tests(self) -> dict:
        """Executa todos os testes da integração."""
        logger.info("🚀 Iniciando testes D-03.2: Live Feed Integration")
        
        # Lista de testes
        tests = [
            ("Configuration Loading", self.test_live_feed_configuration),
            ("Components Availability", self.test_live_feed_components_availability),
            ("Trading System Integration", self.test_trading_system_initialization),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"🧪 Executando: {test_name}")
            try:
                result = await test_func()
                if result:
                    logger.info(f"✅ {test_name}: PASSOU")
                    passed_tests += 1
                else:
                    logger.warning(f"❌ {test_name}: FALHOU")
            except Exception as e:
                logger.error(f"💥 {test_name}: ERRO - {e}")
        
        # Calcular resultado geral
        success_rate = (passed_tests / total_tests) * 100
        overall_status = "PASS" if success_rate == 100 else "PARTIAL" if success_rate > 0 else "FAIL"
        
        self.test_results.update({
            "tests_passed": passed_tests,
            "tests_total": total_tests,
            "success_rate": success_rate,
            "overall_status": overall_status
        })
        
        # Salvar resultados
        results_file = f"data/d03_2_integration_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs("data", exist_ok=True)
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📊 Resultados salvos em: {results_file}")
        logger.info(f"🎯 Taxa de Sucesso: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        
        return self.test_results

async def main():
    """Função principal do teste."""
    tester = D032LiveFeedIntegrationTester()
    results = await tester.run_all_tests()
    
    print("\n" + "="*60)
    print("📋 RESUMO DOS TESTES D-03.2")
    print("="*60)
    print(f"Status Geral: {results['overall_status']}")
    print(f"Taxa de Sucesso: {results['success_rate']:.1f}%")
    print(f"Testes Passaram: {results['tests_passed']}/{results['tests_total']}")
    print(f"Componentes Testados: {len(results['components_tested'])}")
    
    if results['overall_status'] == 'PASS':
        print("\n✅ D-03.2: Live Feed Integration está funcionando corretamente!")
        print("🚀 Sistema pronto para usar dados em tempo real do KuCoin")
    else:
        print(f"\n⚠️ D-03.2: Alguns testes falharam ({results['success_rate']:.1f}% sucesso)")
        print("🔧 Verifique os logs para detalhes dos problemas")
    
    return results['overall_status'] == 'PASS'

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
