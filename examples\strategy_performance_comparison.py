#!/usr/bin/env python3
"""
Comparação de Performance entre Estratégias QUALIA com Dados Reais
YAA IMPLEMENTATION: Teste comparativo de todas as estratégias disponíveis.
"""

import asyncio
import json
import sys
from datetime import datetime, timedelta
from pathlib import Path
import time
from typing import Dict, List, Any, Optional

import pandas as pd
import numpy as np
import requests

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))
sys.path.append(str(Path(__file__).parent.parent / "src"))

try:
    from qualia.strategies.strategy_factory import StrategyFactory
    from qualia.strategies.strategy_interface import TradingContext
    from qualia.strategies.params import QualiaTSVFParams
    from qualia.consciousness.amplification_calibrator import AmplificationCalibrator
    from qualia.config.hyperparams_loader import HyperParamsLoader
    from qualia.utils.logger import get_logger
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    print("🔄 Tentando importação alternativa...")
    # Fallback para teste simples sem dependências complexas
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

logger = get_logger(__name__)


class RealDataFetcher:
    """Fetcher de dados reais para comparação de estratégias."""
    
    BASE_URL = "https://api.binance.com/api/v3"
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'QUALIA-StrategyComparison/1.0'
        })
    
    def fetch_historical_data(self, symbol: str, interval: str = "1h", days: int = 90) -> pd.DataFrame:
        """Busca dados históricos formatados para QUALIA."""
        try:
            # Calcula timestamps
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            logger.info(f"🔄 Buscando dados reais para {symbol} ({days} dias)")
            
            # Parâmetros da API
            params = {
                'symbol': symbol.replace('/', ''),
                'interval': interval,
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            # Faz a requisição
            response = self.session.get(f"{self.BASE_URL}/klines", params=params)
            response.raise_for_status()
            
            data = response.json()
            
            # Converte para DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Converte tipos
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index()
            
            # Remove dados inválidos
            df = df.dropna()
            
            logger.info(f"✅ Dados obtidos para {symbol}: {len(df)} candles de {df.index[0]} a {df.index[-1]}")
            return df
            
        except Exception as e:
            logger.error(f"❌ Erro ao buscar dados para {symbol}: {e}")
            return pd.DataFrame()


def test_strategy_performance(
    strategy_name: str,
    df: pd.DataFrame, 
    symbol: str,
    context: Optional[Dict] = None
) -> Dict[str, Any]:
    """Testa performance de uma estratégia específica."""
    
    if df.empty or len(df) < 100:
        return {
            'strategy': strategy_name,
            'symbol': symbol,
            'total_return_pct': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown_pct': 0.0,
            'win_rate': 0.0,
            'total_trades': 0,
            'volatility': 0.0,
            'error': 'Dados insuficientes'
        }
    
    try:
        logger.info(f"🧠 Testando estratégia: {strategy_name}")
        
        # Cria contexto de trading
        trading_context = TradingContext(
            symbol=symbol,
            timeframe="1h",
            initial_capital=10000.0,
            **(context or {})
        )
        
        # Cria estratégia usando factory
        try:
            strategy = StrategyFactory.create_strategy(
                alias=strategy_name,
                context=trading_context
            )
        except Exception as e:
            logger.warning(f"⚠️ Erro ao criar estratégia {strategy_name}: {e}")
            return {
                'strategy': strategy_name,
                'symbol': symbol,
                'error': f'Erro na criação: {str(e)}'
            }
        
        # Simula trading
        positions = []
        returns = []
        trades = 0
        capital = trading_context.initial_capital
        
        for i in range(100, len(df)):
            try:
                # Dados históricos para análise
                historical_data = df.iloc[max(0, i-100):i].copy()
                current_price = df.iloc[i]['close']
                
                # Gera sinal
                signal_result = strategy.generate_signals(historical_data)
                
                if signal_result and 'position' in signal_result:
                    position = signal_result['position']
                    confidence = signal_result.get('confidence', 0.5)
                    
                    # Aplica filtro de confiança
                    if confidence > 0.6:  # Threshold mínimo
                        positions.append(position)
                        
                        # Calcula retorno
                        if len(positions) > 1:
                            prev_price = df.iloc[i-1]['close']
                            price_change = (current_price - prev_price) / prev_price
                            position_return = positions[-2] * price_change
                            returns.append(position_return)
                            
                            if abs(positions[-2]) > 0.1:  # Trade significativo
                                trades += 1
                    else:
                        positions.append(0.0)  # Sem posição
                else:
                    positions.append(0.0)
                    
            except Exception as e:
                logger.debug(f"Erro no passo {i}: {e}")
                positions.append(0.0)
        
        # Calcula métricas
        if not returns:
            return {
                'strategy': strategy_name,
                'symbol': symbol,
                'total_return_pct': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown_pct': 0.0,
                'win_rate': 0.0,
                'total_trades': 0,
                'volatility': 0.0,
                'error': 'Nenhum retorno gerado'
            }
        
        returns_series = pd.Series(returns)
        
        # Métricas básicas
        total_return = returns_series.sum()
        volatility = returns_series.std() * np.sqrt(252)  # Anualizado
        sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0.0
        
        # Drawdown
        cumulative_returns = (1 + returns_series).cumprod()
        rolling_max = cumulative_returns.expanding().max()
        drawdowns = (cumulative_returns - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        # Win rate
        winning_periods = (returns_series > 0).sum()
        win_rate = winning_periods / len(returns_series) if len(returns_series) > 0 else 0.0
        
        result = {
            'strategy': strategy_name,
            'symbol': symbol,
            'total_return_pct': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown,
            'win_rate': win_rate,
            'total_trades': trades,
            'volatility': volatility,
            'periods_analyzed': len(returns),
            'avg_position': np.mean(np.abs(positions)) if positions else 0.0
        }
        
        logger.info(f"✅ {strategy_name}: Return {total_return:.2%}, Sharpe {sharpe_ratio:.3f}, Trades {trades}")
        return result
        
    except Exception as e:
        logger.error(f"❌ Erro no teste da estratégia {strategy_name}: {e}")
        return {
            'strategy': strategy_name,
            'symbol': symbol,
            'total_return_pct': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown_pct': 0.0,
            'win_rate': 0.0,
            'total_trades': 0,
            'volatility': 0.0,
            'error': str(e)
        }


def run_strategy_comparison():
    """Executa comparação completa entre todas as estratégias."""
    print("🚀 COMPARAÇÃO DE PERFORMANCE - ESTRATÉGIAS QUALIA")
    print("=" * 60)
    
    # Inicializa fetcher
    fetcher = RealDataFetcher()
    
    # Estratégias para testar
    strategies_to_test = [
        "NovaEstrategiaQUALIA",  # QualiaTSVFStrategy
        "CompositeStrategy",
        "quantum_momentum_v2",   # EnhancedQuantumMomentumStrategy
        "q_reversal",           # QuantumTrendReversalStrategy
    ]
    
    # Símbolos para testar
    symbols = ["BTC/USDT", "ETH/USDT"]
    
    print(f"📊 Estratégias: {len(strategies_to_test)}")
    print(f"📈 Símbolos: {len(symbols)}")
    print(f"🧠 Total de testes: {len(strategies_to_test) * len(symbols)}")
    
    all_results = []
    
    for symbol in symbols:
        print(f"\n📈 Processando {symbol}...")
        
        # Busca dados reais
        df = fetcher.fetch_historical_data(symbol, days=90)
        
        if df.empty:
            print(f"❌ Falha ao obter dados para {symbol}")
            continue
        
        for strategy_name in strategies_to_test:
            print(f"   🧠 Testando {strategy_name}...")
            
            result = test_strategy_performance(strategy_name, df, symbol)
            all_results.append(result)
            
            if 'error' not in result:
                print(f"      ✅ Return {result['total_return_pct']:.2%}, "
                      f"Sharpe {result['sharpe_ratio']:.3f}, "
                      f"Trades {result['total_trades']}")
            else:
                print(f"      ❌ Erro: {result['error']}")
    
    # Análise dos resultados
    if all_results:
        print(f"\n" + "="*60)
        print(f"📊 RESULTADOS DA COMPARAÇÃO")
        print(f"="*60)
        
        # Filtra resultados válidos
        valid_results = [r for r in all_results if 'error' not in r and r['total_trades'] > 0]
        
        if valid_results:
            df_results = pd.DataFrame(valid_results)
            
            print(f"\n📈 Estatísticas Gerais:")
            print(f"   • Testes válidos: {len(valid_results)}/{len(all_results)}")
            
            # Ranking por Sharpe Ratio
            print(f"\n🏆 RANKING POR SHARPE RATIO:")
            sharpe_ranking = df_results.groupby('strategy')['sharpe_ratio'].mean().sort_values(ascending=False)
            for i, (strategy, sharpe) in enumerate(sharpe_ranking.items(), 1):
                print(f"   {i}. {strategy}: {sharpe:.3f}")
            
            # Ranking por Return
            print(f"\n💰 RANKING POR RETORNO TOTAL:")
            return_ranking = df_results.groupby('strategy')['total_return_pct'].mean().sort_values(ascending=False)
            for i, (strategy, ret) in enumerate(return_ranking.items(), 1):
                print(f"   {i}. {strategy}: {ret:.2%}")
            
            # Ranking por Win Rate
            print(f"\n🎯 RANKING POR WIN RATE:")
            winrate_ranking = df_results.groupby('strategy')['win_rate'].mean().sort_values(ascending=False)
            for i, (strategy, wr) in enumerate(winrate_ranking.items(), 1):
                print(f"   {i}. {strategy}: {wr:.2%}")
            
            # Melhor estratégia geral
            print(f"\n🥇 MELHOR ESTRATÉGIA GERAL:")
            best_strategy = sharpe_ranking.index[0]
            best_metrics = df_results[df_results['strategy'] == best_strategy].mean()
            print(f"   Estratégia: {best_strategy}")
            print(f"   Sharpe Ratio: {best_metrics['sharpe_ratio']:.3f}")
            print(f"   Retorno Total: {best_metrics['total_return_pct']:.2%}")
            print(f"   Win Rate: {best_metrics['win_rate']:.2%}")
            print(f"   Max Drawdown: {best_metrics['max_drawdown_pct']:.2%}")
            print(f"   Trades Médios: {best_metrics['total_trades']:.0f}")
        
        # Salva resultados
        output_dir = Path("results/strategy_comparison")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = int(time.time())
        output_file = output_dir / f"strategy_comparison_{timestamp}.json"
        
        with open(output_file, 'w') as f:
            json.dump({
                'metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'total_tests': len(all_results),
                    'valid_results': len(valid_results) if valid_results else 0,
                    'strategies_tested': strategies_to_test,
                    'symbols': symbols,
                    'data_source': 'Binance API'
                },
                'results': all_results
            }, f, indent=2)
        
        print(f"\n💾 Resultados salvos em: {output_file}")
    
    else:
        print("❌ Nenhum resultado válido obtido")


if __name__ == "__main__":
    run_strategy_comparison()
