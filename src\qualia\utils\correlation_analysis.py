from __future__ import annotations

"""Utilities for correlation analysis persisted in ``QuantumPatternMemory``."""

from typing import Iterable, Optional, List
import numpy as np
from scipy.stats import pearsonr

from ..memory.qpm_loader import get_qpm_instance
from ..common_types import QuantumSignaturePacket
from ..utils.logger import get_logger

logger = get_logger(__name__)


def evaluate_and_store_correlation(
    signal_series: Iterable[float],
    metric_series: Iterable[float],
    *,
    metadata: Optional[dict] = None,
) -> float:
    """Calculate Pearson correlation and store it in ``QuantumPatternMemory``."""

    sig = np.asarray(list(signal_series), dtype=float)
    met = np.asarray(list(metric_series), dtype=float)

    if sig.size == 0 or met.size != sig.size:
        logger.debug("Series incompatíveis para correlação")
        corr = 0.0
    else:
        corr_val, _ = pearsonr(sig, met)
        corr = float(corr_val if not np.isnan(corr_val) else 0.0)

    try:
        qpm = get_qpm_instance({"enable_warmstart": False})
        packet = QuantumSignaturePacket(
            vector=[corr], metrics={"type": "correlation", **(metadata or {})}
        )
        qpm.store_pattern(packet, market_snapshot={}, outcome={})
    except Exception as exc:  # pragma: no cover - defensive
        logger.debug("Falha ao armazenar correlação: %s", exc)

    return corr
