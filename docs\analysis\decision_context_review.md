# Revisão Holística do módulo `src/qualia/market/decision_context.py`

Este documento sumariza a análise do arquivo responsável por gerar sinais de
trading (classe `ScalpingDecision`) e definir parâmetros de ordens a partir de
um `TradeContext`.

## Papel na Pipeline QUALIA

- **Feature engineering**: consome OHLCV, volatilidade e indicadores locais.
- **Quantum layer**: utiliza `quantum_metrics` e insights do QPM para modular a
  confiança dos sinais.
- **Estratégia**: principal produtor de `BUY`/`SELL`/`HOLD` enviados ao motor de
  execução.
- **Risk**: aplica perfis de risco e integra-se opcionalmente ao
  `DynamicRiskController`.

## Dependências Principais

- `pandas`, `numpy` para cálculo de indicadores.
- `dataclasses` para representar `TradeContext`.
- `src.qualia.utils.logger` para logging.
- `DynamicRiskController` e utilitários de volatilidade/ATR.

## Pontos de Acoplamento

- Métodos invocados diretamente por estratégias
  (`QUALIAEnhanced`, `qast_evolution` etc.).
- Atributos lidos pelo módulo de execução e pela coleta de métricas.
- Lógica de risco dependente de `DynamicRiskController` e configurações externas.

## Débitos Técnicos Observados

- Arquivo extenso (mais de 800 linhas) centralizando múltiplas
  responsabilidades.
- Uso de strings literais para os sinais (`"BUY"`, `"SELL"`, `"HOLD"`).
- Tratamentos de exceção genéricos (`except Exception`) em trechos críticos,
  dificultando depuração.
- Parte da configuração ainda "hard-coded" (cores ANSI e valores padrão para
  SL/TP).
- Falta de métricas de tempo de cálculo ou número de decisões produzidas.

## Recomendações de Melhoria

1. **Refatoração Modular**: extrair o cálculo de ATR e a lógica de perfis de
   risco para funções auxiliares ou classes dedicadas, reduzindo o tamanho de
   `_compute_order_parameters`.
2. **Enumerações**: definir um `Enum` para os tipos de sinal, evitando erros de
   digitação e facilitando validação.
3. **Configuração Centralizada**: mover os valores padrão (porcentagens,
   multiplicadores, códigos de cor) para arquivos YAML em `config/`.
4. **Observabilidade**: instrumentar com `statsd` a duração de `_compute_decision`
   e quantidade de reversões pelo QPM.
5. **Cobertura de Testes**: criar testes unitários para cenários de ATR presente
   vs. ausente e para integração com o `DynamicRiskController` usando mocks.

## Atualizações Implementadas

- Introdução de ``SignalType`` (:class:`StrEnum`) padronizando os sinais
  ``BUY``, ``SELL`` e ``HOLD``.
- Métricas ``DogStatsd`` adicionadas à execução de ``_compute_decision`` para
  medir o tempo de cálculo e número de ajustes vindos do QPM.

Estas ações aumentariam a legibilidade, favoreceriam testes específicos e
permitiriam monitorar a performance das decisões, alinhando o módulo com os
objetivos definidos no roadmap **QUALIA 2025-H2**.
