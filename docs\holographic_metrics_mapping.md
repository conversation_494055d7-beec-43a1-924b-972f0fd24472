# Mapeamento das Métricas para Uniforms de Shader

Este documento descreve como cada métrica calculada no backend do QUALIA
alimenta os **uniforms** usados pelos shaders na visualização holográfica.
Manter essa correspondência atualizada é fundamental para que ajustes nas
métricas se reflitam corretamente na interface.

## Tabela de Correspondência

| Campo no Backend         | Uniform do Shader | Efeito Principal |
|-------------------------|-------------------|-----------------|
| `coherence`             | `coherence`       | Intensidade das ondulações e padrões de interferência. |
| `entanglement`          | `entanglement`    | Deslocamento adicional dos vértices via `applyEntanglement`. |
| `resonance`             | `resonance`       | Escala global e vibração do objeto. |
| `fieldStrength`         | `fieldStrength`   | Força do brilho ao redor da geometria. |
| `consciousness`         | `consciousness`   | Mistura das cores base com a paleta consciente. |
| `liquidity_buckets`     | `liquidityBuckets`| Vetor `vec3` que direciona a distorção e tinge o fragmento. |
| `trend_strength`        | `trendStrength`   | Amplitude do deslocamento causado pela tendência. |
| `delta_entropy`         | `deltaEntropy`    | Modula cores extras e a intensidade do movimento. |

Valores como `fps` e `latency` são enviados para fins de diagnóstico e não se
conectam diretamente a uniforms, enquanto `time` é atualizado continuamente no
frontend.

## Exemplo de Payload

```python
snapshot = {
    "coherence": 0.9,
    "entanglement": 0.2,
    "resonance": 0.3,
    "fieldStrength": 0.4,
    "consciousness": 0.1,
    "liquidity_buckets": [0.2, 0.3, 0.5],
    "trend_strength": 0.8,
    "delta_entropy": 0.05,
}
publish_snapshot(snapshot)
```

No JavaScript, `handleSnapshot` atribui cada valor ao uniform correspondente,
permitindo que os shaders reajam imediatamente às novas métricas.

## Métricas de Subcampos

O TSVF agora expõe um campo `subfields`, que é uma lista onde cada elemento
representa um subconjunto do vetor psi final. Para cada subcampo são calculados
`strength`, `E` e `H`. Esses valores podem ser usados para modular partes
específicas da visualização holográfica, permitindo destacar zonas com maior
coerência ou entropia local. A ordem da lista corresponde à divisão sequencial
do vetor e deve ser interpretada conforme o shader associe cada segmento da
geometria a um subcampo.
