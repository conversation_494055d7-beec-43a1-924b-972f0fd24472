# Conhecendo o QUALIA Farsight – Radar de Inovações Latentes

O **Farsight** é o subsistema responsável por detectar sinais de tecnologia emergente em fontes abertas
(arXiv, GitHub, X/Twitter e futuramente patentes). Ele funciona como um radar capaz de
mapear ideias embrionárias antes que se tornem tendência.

## Visão Geral do Pipeline

1. **Coleta** – `FarsightEngine` consulta papers do arXiv e, na versão 2 (`FAR_V2=1`),
   também repositórios do GitHub e tweets recentes.
2. **Pré-processamento** – na versão clássica os títulos são vetorizados via **TF-IDF**.
3. **Agrupamento** – k-means (v1) ou **HDBSCAN** com embeddings semânticos (v2).
4. **Métrica de Curvatura** – distância média dos artigos ao centróide → mede quão “embrionário” é o
   tema (maior curvatura = ideia mais crua / menos consolidada).
5. **Velocidade** – fração de artigos do cluster sobre o total recente → proxy de tração.
6. **Publicação** – resultado é exposto na rota `/api/farsight/insights` e renderizado em
   `/farsight` pelo front-end D3/vanilla JS.

```
┌──────────────────────────┐       ┌───────────────┐       ┌───────────────┐
│ arXiv/GitHub/Twitter     │ ───▶ │ Embeddings    │ ───▶ │ HDBSCAN       │
└──────────────────────────┘       └───────────────┘       └───────────────┘
        ▲                             │                     │
        │                             ▼                     ▼
   Config YAML                  Curvatura &           API JSON +
   days_back etc.               Velocidade            Front-end
```

## Componentes Principais

| Componente | Local | Papel |
|------------|-------|-------|
| `FarsightEngine` | `src/qualia/analysis/farsight_engine.py` | Implementa pipeline end-to-end, retornando lista de insights ready-to-JSON. |
| Blueprint `farsight_bp` | `src/qualia/ui/blueprints/farsight.py` | Registra página HTML e endpoint `/api/farsight/insights`. Usa cache LRU para evitar requisições repetidas. |
| Front-end (`farsight.html`, `static/js/farsight.js`) | `src/ui/templates` & `static/js` | Busca dados da API e exibe cartões de tópico com métrica & links. |

## Como Executar

```powershell
# Dentro do projeto (venv ativo)
$Env:QUALIA_SECRET_KEY = "algo_secreto"
python -m pip install -r requirements.txt  # garanta arxiv>=1.4.8 instalado
python -m src.ui.app                      # inicia Flask/SocketIO
```
Acesse `http://localhost:5000/farsight`.

## Configurações Rápidas

| Parâmetro | Onde alterar | Descrição |
|-----------|--------------|-----------|
| `days_back` | Linha `engine = FarsightEngine(... )` em `blueprints/farsight.py` | Janela (dias) para buscar papers. |
| `max_results` | Idem | Limite de downloads. |
| `categories` | Parâmetro de `FarsightEngine` (ou variável `FAR_CATEGORIES`) | Categorias do arXiv, p.ex. `"cs.AI OR cs.LG"`. |

### Alternando entre versões

Defina a variável de ambiente `FAR_V2=1` para ativar o pipeline baseado em
embeddings (v2). Sem essa flag o Farsight utiliza o fluxo clássico com TF-IDF.

## Roadmap Futuro

- **Múltiplas fontes**: GitHub e X/Twitter já suportados; extensível a bases de patentes.
- **Embeddings + HDBSCAN**: implementado quando `FAR_V2=1`.
- **Persistência incremental**: histórico salvo via `FarsightHistory`.
- **Dashboard interativo**: visualização radial (“radar”) com filtros de tempo.

## Pontos de Atenção

- Dependência de rede para arXiv (`https://export.arxiv.org`).
- O código lida com datetimes *naive* vs UTC (bug já corrigido em `2025-06-19`).
- Caso nenhum paper seja encontrado → API responde lista vazia; front-end exibe “Nenhum insight disponível”.

## TL;DR
Farsight monitora a fronteira do conhecimento. 4 passos para usar:
1. Instale dependências (incluindo `arxiv`).
2. Exporte `QUALIA_SECRET_KEY`.
3. `python -m src.ui.app`.
4. Abra `/farsight` e explore os clusters surgindo em tempo real.
