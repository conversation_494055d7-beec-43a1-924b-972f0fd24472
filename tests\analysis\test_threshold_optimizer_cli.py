import os
import subprocess
import sys
from pathlib import Path

import yaml

# Helper functions reused from test_threshold_optimizer
from tests.analysis.test_threshold_optimizer import _write_hist, _write_ev


def test_cli_runs(tmp_path: Path) -> None:
    hist = _write_hist(tmp_path)
    events = _write_ev(tmp_path)
    out_cfg = tmp_path / "cfg.yaml"
    repo_root = Path(__file__).resolve().parents[2]
    env = {**os.environ, "PYTHONPATH": str(repo_root / "src")}
    subprocess.run(
        [
            sys.executable,
            "-m",
            "qualia.analysis.threshold_optimizer",
            "--history",
            str(hist),
            "--events",
            str(events),
            "--output",
            str(out_cfg),
        ],
        check=True,
        capture_output=True,
        text=True,
        env=env,
    )
    assert out_cfg.exists()
    params = yaml.safe_load(out_cfg.read_text())
    assert "alpha" in params and "offset" in params
