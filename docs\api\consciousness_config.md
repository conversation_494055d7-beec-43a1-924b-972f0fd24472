# Configuração da QUALIAConsciousness

O arquivo `config/consciousness_defaults.yaml` centraliza os parâmetros utilizados na inicialização da `QUALIAConsciousness`. Defina a variável de ambiente `QUALIA_CONSCIOUSNESS_DEFAULTS` para apontar para outro caminho caso deseje personalizar os valores.
Para maiores detalhes sobre o fluxo simbólico, consulte [Camada Ô_SIM](../specs/QUALIA_TS_SIM.md).
Consulte também o [exemplo de configuração](#exemplo-habilitando-a-camada-Ô_SIM) para habilitar o operador.

## Esquema YAML

```yaml
n_qubits: 8
n_steps: 10
thermal_coefficient: 0.1
retro_mode: none
perception_depth: 3
self_reflection_enabled: true
entropy_sensitivity: 0.02
history_maxlen: 256
pca_n_components_variance: 0.95
pca_max_components: 16
kmeans_k_range: [2, 10]
init_symbolic_processor: false
qualia:
  intent_engine:
    wavelet: haar
    level: 3
  symbolic_intention:
    amp_factor: 1.1
    attenuation_factor: 0.9
    depth_delta: 1
```

Todos os campos são opcionais e podem ser sobrepostos via código ou variáveis de ambiente.
Utilize `load_consciousness_defaults()` para carregar o YAML e utilize `settings` para acessar os valores durante a inicialização.
Para ativar a camada Ô_SIM defina `init_symbolic_processor: true` nesse arquivo ou forneça um YAML alternativo via `QUALIA_CONSCIOUSNESS_DEFAULTS`.

### Seção `qualia.intent_engine`

Esta seção define os parâmetros do `IntentioWaveletEngine` utilizado na captura de padrões de intenção.

- `wavelet`: nome da wavelet empregada. Padrão `"haar"`.
- `level`: nível máximo de decomposição. Padrão `3`.

### Seção `qualia.symbolic_intention`

Configurações do `SymbolicIntentionOperator`, responsável por traduzir tokens em ajustes do universo quântico.

- `amp_factor`: fator multiplicador para o parâmetro `lambda_factor_multiplier`. Padrão `1.1`.
- `attenuation_factor`: fator de redução aplicado quando tokens pedem atenuação. Padrão `0.9`.
- `depth_delta`: incremento (ou decremento) da profundidade de SCR. Padrão `1`.

### Seção `qualia.symbolic_modulation`

Configura a *Symbolic Modulation Pipeline* (Ô\_SIM‐07+).

| Campo | Tipo | Intervalo | Default | Descrição |
|-------|------|-----------|---------|-----------|
| `enabled` | bool | — | `false` | Ativa a detecção/tokenização/modulação simbólica. |
| `zscore_threshold` | float | 0.5–10 | `2.5` | Limite de Z-score para considerar um hotspot. |

Exemplo mínimo habilitando a feature:

```yaml
qualia:
  symbolic_modulation:
    enabled: true
    zscore_threshold: 3.0
```

### Utilitário `Base64PatternEncoder`

Use `Base64PatternEncoder` para converter sequências numéricas em tokens ASCII no formato Base64. Essa classe está disponível em `qualia.core.intention` e pode ser empregada para persistir ou transmitir padrões detectados.

### Exemplo habilitando a camada Ô_SIM

Adicione as seções abaixo ao arquivo `config/consciousness_defaults.yaml` e defina `init_symbolic_processor: true`:

```yaml
init_symbolic_processor: true
qualia:
  intent_engine:
    wavelet: haar
    level: 3
  symbolic_intention:
    amp_factor: 1.1
    attenuation_factor: 0.9
    depth_delta: 1
```
