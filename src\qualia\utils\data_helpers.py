"""Generic helpers for safely handling data objects."""

from __future__ import annotations

import pandas as pd
import numpy as np

__all__ = ["is_data_empty"]


def is_data_empty(data) -> bool:
    """Check whether ``data`` is empty in a type-safe manner.

    Parameters
    ----------
    data
        Dataset to verify. Supports pandas objects, numpy arrays, lists and tuples.

    Returns
    -------
    bool
        ``True`` if ``data`` contains no entries, ``False`` otherwise.
    """
    if data is None:
        return True

    if isinstance(data, pd.DataFrame):
        return data.empty
    if isinstance(data, np.ndarray):
        return data.size == 0
    if isinstance(data, (list, tuple)):
        return len(data) == 0
    if hasattr(data, "__len__"):
        try:
            return len(data) == 0
        except Exception:  # pragma: no cover - defensive
            return True
    if hasattr(data, "empty"):
        try:
            return data.empty
        except Exception:  # pragma: no cover - defensive
            return True
    return False
