#!/usr/bin/env python3
"""
YAA: Script para testar melhorias do sistema de cache QUALIA.

Demonstra:
- <PERSON><PERSON> vs CSV
- Controle write-once por candle-set
- Eliminação de redundância
- Estatísticas de performance

Uso:
    python scripts/test_cache_improvements.py --format parquet --test-write-once
    python scripts/test_cache_improvements.py --benchmark --iterations 100
"""

import argparse
import os
import sys
import time
import tempfile
from pathlib import Path

# Adiciona o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import pandas as pd
from qualia.utils.cache import (
    cached_dataframe, 
    clear_dirty_flags, 
    get_cache_stats,
    PARQUET_AVAILABLE
)
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


def create_sample_dataframe(rows: int = 1000) -> pd.DataFrame:
    """Cria DataFrame de exemplo para testes."""
    import numpy as np
    
    return pd.DataFrame({
        'timestamp': pd.date_range('2024-01-01', periods=rows, freq='1min'),
        'symbol': ['BTC/USDT'] * rows,
        'open': np.random.uniform(40000, 50000, rows),
        'high': np.random.uniform(50000, 55000, rows),
        'low': np.random.uniform(35000, 40000, rows),
        'close': np.random.uniform(40000, 50000, rows),
        'volume': np.random.uniform(100, 1000, rows),
    })


@cached_dataframe(expiry_seconds=3600, cache_file_path="data/test_cache")
def fetch_market_data(symbol: str, timeframe: str, timestamp: str) -> pd.DataFrame:
    """Simula fetch de dados de mercado."""
    logger.info(f"Fetching data for {symbol}:{timeframe}:{timestamp}")
    time.sleep(0.1)  # Simula latência de API
    return create_sample_dataframe()


def test_write_once_behavior():
    """Testa comportamento write-once por candle-set."""
    print("\n" + "="*60)
    print("🔒 TESTE WRITE-ONCE POR CANDLE-SET")
    print("="*60)
    
    symbol = "BTC/USDT"
    timeframe = "1h"
    
    # Primeira chamada - deve executar e salvar
    start_time = time.time()
    df1 = fetch_market_data(symbol, timeframe, "2024-01-01T00:00:00")
    first_call_time = time.time() - start_time
    
    # Segunda chamada com mesmo candle-set - deve usar cache
    start_time = time.time()
    df2 = fetch_market_data(symbol, timeframe, "2024-01-01T01:00:00")
    second_call_time = time.time() - start_time
    
    # Terceira chamada com timestamp diferente - deve usar cache
    start_time = time.time()
    df3 = fetch_market_data(symbol, timeframe, "2024-01-01T02:00:00")
    third_call_time = time.time() - start_time
    
    print(f"1ª chamada (cache miss): {first_call_time:.3f}s")
    print(f"2ª chamada (cache hit):  {second_call_time:.3f}s")
    print(f"3ª chamada (cache hit):  {third_call_time:.3f}s")
    
    speedup = first_call_time / max(second_call_time, 0.001)
    print(f"Speedup: {speedup:.1f}x")
    
    # Verifica se os DataFrames são iguais (mesmo candle-set)
    assert df1.equals(df2), "DataFrames deveriam ser iguais (mesmo candle-set)"
    assert df2.equals(df3), "DataFrames deveriam ser iguais (mesmo candle-set)"
    print("✅ Write-once funcionando corretamente")


def test_format_comparison():
    """Compara performance entre CSV e Parquet."""
    print("\n" + "="*60)
    print("📊 COMPARAÇÃO CSV vs PARQUET")
    print("="*60)
    
    if not PARQUET_AVAILABLE:
        print("❌ PyArrow não disponível - instale com: pip install pyarrow")
        return
        
    # Cria DataFrame grande para teste
    df = create_sample_dataframe(10000)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        csv_path = os.path.join(temp_dir, "test.csv")
        parquet_path = os.path.join(temp_dir, "test.parquet")
        
        # Teste CSV
        start_time = time.time()
        df.to_csv(csv_path, index=False)
        csv_write_time = time.time() - start_time
        
        start_time = time.time()
        df_csv = pd.read_csv(csv_path)
        csv_read_time = time.time() - start_time
        
        csv_size = os.path.getsize(csv_path)
        
        # Teste Parquet
        start_time = time.time()
        df.to_parquet(parquet_path, index=False, compression='snappy')
        parquet_write_time = time.time() - start_time
        
        start_time = time.time()
        df_parquet = pd.read_parquet(parquet_path)
        parquet_read_time = time.time() - start_time
        
        parquet_size = os.path.getsize(parquet_path)
        
        print(f"Tamanho do dataset: {len(df):,} linhas")
        print(f"\nCSV:")
        print(f"  Escrita: {csv_write_time:.3f}s")
        print(f"  Leitura: {csv_read_time:.3f}s")
        print(f"  Tamanho: {csv_size:,} bytes ({csv_size/1024/1024:.1f} MB)")
        
        print(f"\nParquet:")
        print(f"  Escrita: {parquet_write_time:.3f}s")
        print(f"  Leitura: {parquet_read_time:.3f}s")
        print(f"  Tamanho: {parquet_size:,} bytes ({parquet_size/1024/1024:.1f} MB)")
        
        print(f"\nMelhorias:")
        print(f"  Compressão: {csv_size/parquet_size:.1f}x menor")
        print(f"  Leitura: {csv_read_time/parquet_read_time:.1f}x mais rápida")
        print(f"  Escrita: {csv_write_time/parquet_write_time:.1f}x mais rápida")


def benchmark_cache_performance(iterations: int = 50):
    """Benchmark de performance do cache."""
    print("\n" + "="*60)
    print(f"⚡ BENCHMARK DE PERFORMANCE ({iterations} iterações)")
    print("="*60)
    
    symbols = ["BTC/USDT", "ETH/USDT", "ADA/USDT"]
    timeframes = ["1m", "5m", "1h"]
    
    # Aquece o cache
    for symbol in symbols:
        for timeframe in timeframes:
            fetch_market_data(symbol, timeframe, "2024-01-01T00:00:00")
    
    # Benchmark cache hits
    start_time = time.time()
    for i in range(iterations):
        symbol = symbols[i % len(symbols)]
        timeframe = timeframes[i % len(timeframes)]
        df = fetch_market_data(symbol, timeframe, f"2024-01-01T{i:02d}:00:00")
        
    total_time = time.time() - start_time
    avg_time = total_time / iterations
    
    print(f"Total: {total_time:.3f}s")
    print(f"Média por chamada: {avg_time*1000:.1f}ms")
    print(f"Throughput: {iterations/total_time:.1f} chamadas/s")


def main():
    parser = argparse.ArgumentParser(description="Teste de melhorias do cache QUALIA")
    parser.add_argument(
        "--test-write-once",
        action="store_true",
        help="Testa comportamento write-once"
    )
    parser.add_argument(
        "--format-comparison",
        action="store_true",
        help="Compara CSV vs Parquet"
    )
    parser.add_argument(
        "--benchmark",
        action="store_true",
        help="Executa benchmark de performance"
    )
    parser.add_argument(
        "--iterations",
        type=int,
        default=50,
        help="Número de iterações para benchmark"
    )
    parser.add_argument(
        "--stats",
        action="store_true",
        help="Mostra estatísticas do cache"
    )
    parser.add_argument(
        "--clean",
        action="store_true",
        help="Limpa flags dirty antigas"
    )
    
    args = parser.parse_args()
    
    print("🧪 TESTE DE MELHORIAS DO CACHE QUALIA")
    print(f"PyArrow disponível: {'✅' if PARQUET_AVAILABLE else '❌'}")
    
    if args.test_write_once:
        test_write_once_behavior()
        
    if args.format_comparison:
        test_format_comparison()
        
    if args.benchmark:
        benchmark_cache_performance(args.iterations)
        
    if args.stats:
        print("\n" + "="*60)
        print("📈 ESTATÍSTICAS DO CACHE")
        print("="*60)
        stats = get_cache_stats()
        
        mem_stats = stats["memory_cache"]
        print(f"Itens em memória: {mem_stats['items']}")
        print(f"Itens expirados: {mem_stats['expired_items']}")
        print(f"Uso de memória: {mem_stats['total_size_mb']:.1f} MB")
        print(f"Candle-sets ativos: {stats['dirty_flags']['active_candle_sets']}")
        
        if stats["metadata"]:
            print(f"\nEstatísticas por função:")
            for func, meta in stats["metadata"].items():
                print(f"  {func}:")
                print(f"    Hits: {meta.get('hits', 0)}")
                print(f"    Misses: {meta.get('misses', 0)}")
                print(f"    Itens: {meta.get('items', 0)}")
                
    if args.clean:
        removed = clear_dirty_flags()
        print(f"\n🧹 Limpeza: {removed} flags dirty removidas")
        
    if not any([args.test_write_once, args.format_comparison, args.benchmark, args.stats, args.clean]):
        # Executa todos os testes por padrão
        test_write_once_behavior()
        test_format_comparison()
        benchmark_cache_performance(25)
        
        stats = get_cache_stats()
        print(f"\n📊 Cache final: {stats['memory_cache']['items']} itens, "
              f"{stats['dirty_flags']['active_candle_sets']} candle-sets")


if __name__ == "__main__":
    main()
