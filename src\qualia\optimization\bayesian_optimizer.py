"""
QUALIA Bayesian Optimizer Online
Implementa otimização Bayesiana em tempo real usando Optuna
Baseado nos resultados do benchmark offline da Etapa C
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict, field

import optuna
import numpy as np
import pandas as pd
from optuna.samplers import TPESampler
from optuna.pruners import MedianPruner

from ..utils.logger import get_logger
from ..config.config_loader import ConfigLoader
from .realistic_evaluator import RealisticParameterEvaluator
from ..services.bayes_client import BayesOptClient, BayesClientConfig
from .advanced_pruning import (
    AdvancedPruner,
    PruningConfig,
    PruningStrategy,
    TradingMetrics,
    MarketRegime,
    MultiFidelityOptimizer,
    MultiFidelityConfig,
)
from .multi_fidelity import MultiFidelityEvaluator, FidelityConfig, FidelityLevel

logger = get_logger(__name__)


@dataclass
class OptimizationResult:
    """Resultado de uma otimização."""

    timestamp: datetime
    symbol: str
    trial_number: int
    parameters: Dict[str, float]
    objective_value: float
    metrics: Dict[str, float]
    duration_seconds: float


@dataclass
class OptimizationConfig:
    """Configuração do otimizador Bayesiano."""

    n_trials_per_cycle: int = 25
    optimization_interval_cycles: int = 500
    lookback_hours: int = 24
    objective_metric: str = (
        "sharpe_pnl_combined"  # sharpe_ratio, pnl_24h, sharpe_pnl_combined
    )
    pruning_enabled: bool = True
    sampler_type: str = "TPE"  # TPE, Random, CmaEs

    # Search space bounds (baseado no benchmark offline)
    price_amp_range: Tuple[float, float] = (1.0, 10.0)
    news_amp_range: Tuple[float, float] = (
        1.0,
        15.0,
    )  # Expandido baseado em descobertas
    min_conf_range: Tuple[float, float] = (0.20, 0.80)

    # Multi-objective weights
    sharpe_weight: float = 0.6
    pnl_weight: float = 0.4

    # D-04: Advanced Pruning & Multi-fidelity
    pruning_strategy: PruningStrategy = PruningStrategy.ADAPTIVE
    enable_multi_fidelity: bool = True
    multi_fidelity_levels: List[int] = field(
        default_factory=lambda: [1, 6, 24]
    )  # horas

    # D-02: Distributed optimization settings
    use_distributed_optimization: bool = False
    bayes_service_url: str = "http://localhost:8080"
    distributed_fallback_enabled: bool = True


class BayesianOptimizer:
    """
    Otimizador Bayesiano online para parâmetros do QUALIA.

    Usa Optuna para otimização inteligente baseada em:
    - Performance histórica de 24h
    - Resultados do benchmark offline
    - Métricas de risco ajustadas
    """

    def __init__(
        self, config: OptimizationConfig = None, use_realistic_evaluation: bool = True
    ):
        self.config = config or OptimizationConfig()
        self.config_loader = ConfigLoader()
        self.use_realistic_evaluation = use_realistic_evaluation

        # Estado do otimizador
        self.studies: Dict[str, optuna.Study] = {}
        self.optimization_history: List[OptimizationResult] = []
        self.current_parameters: Dict[str, Dict[str, float]] = {}
        self.cycle_counter = 0
        self.last_optimization_time = None

        # Métricas de performance
        self.performance_buffer: Dict[str, List[Dict]] = {}
        self.optimization_stats = {
            "total_optimizations": 0,
            "successful_optimizations": 0,
            "average_improvement": 0.0,
            "best_parameters_per_symbol": {},
        }

        # D-04: Advanced Pruning & Multi-fidelity
        self.advanced_pruner = AdvancedPruner(
            PruningConfig(
                strategy=self.config.pruning_strategy,
                min_sharpe_threshold=0.3,
                max_drawdown_threshold=0.20,
            )
        )

        # D-04: Multi-fidelity Optimizer (novo sistema)
        if self.config.enable_multi_fidelity:
            mf_config = MultiFidelityConfig(
                fidelity_levels=self.config.multi_fidelity_levels,
                promotion_percentile=0.3,
                min_trials_for_promotion=5,
            )
            self.multi_fidelity_optimizer = MultiFidelityOptimizer(mf_config)
            logger.info(
                f"🎯 MultiFidelityOptimizer habilitado com níveis: {self.config.multi_fidelity_levels}"
            )
        else:
            self.multi_fidelity_optimizer = None

        self.multi_fidelity_evaluator = None
        if self.config.enable_multi_fidelity:
            self.multi_fidelity_evaluator = MultiFidelityEvaluator(
                FidelityConfig(
                    fidelity_levels=[
                        FidelityLevel(h) for h in self.config.multi_fidelity_levels
                    ]
                )
            )

        self.current_market_regime = MarketRegime.SIDEWAYS

        # D-02: Distributed optimization client
        self.distributed_client: Optional[BayesOptClient] = None
        if self.config.use_distributed_optimization:
            client_config = BayesClientConfig(
                service_url=self.config.bayes_service_url,
                fallback_to_local=self.config.distributed_fallback_enabled,
            )
            self.distributed_client = BayesOptClient(client_config)
            logger.info(
                f"🌐 Modo distribuído habilitado: {self.config.bayes_service_url}"
            )

        # Inicializar avaliador realista se habilitado
        if self.use_realistic_evaluation:
            self.realistic_evaluator = RealisticParameterEvaluator(
                evaluation_window_hours=72, min_trades_required=5
            )
            logger.info(
                "[TARGET] Avaliação realista habilitada (com custos de transação)"
            )
        else:
            self.realistic_evaluator = None
            logger.info("[STATS] Usando avaliação simulada (sem custos)")

        # Carregar configurações iniciais do benchmark
        self._load_benchmark_insights()

        logger.info("[BRAIN] BayesianOptimizer inicializado")
        logger.info(
            f"[STATS] Configuração: {self.config.n_trials_per_cycle} trials a cada {self.config.optimization_interval_cycles} ciclos"
        )

    def _load_benchmark_insights(self):
        """Carrega insights do benchmark offline para inicialização inteligente."""
        try:
            benchmark_file = Path(
                "data/benchmark_results_20250706_141236_best_configs.json"
            )
            if benchmark_file.exists():
                with open(benchmark_file, "r", encoding="utf-8") as f:
                    benchmark_data = json.load(f)

                # Extrair melhores configurações por símbolo
                best_configs = benchmark_data.get("best_configurations", [])
                for config in best_configs[:5]:  # Top 5 configurações
                    symbol = config["symbol"]
                    params = config["parameters"]

                    if symbol not in self.current_parameters:
                        self.current_parameters[symbol] = {
                            "price_amplification": params["price_amplification"],
                            "news_amplification": params["news_amplification"],
                            "min_confidence": params["min_confidence"],
                        }

                        self.optimization_stats["best_parameters_per_symbol"][
                            symbol
                        ] = {
                            "parameters": params,
                            "benchmark_sharpe": config["metrics"]["sharpe_ratio"],
                            "benchmark_return": config["metrics"]["total_return"],
                        }

                logger.info(
                    f"[CHART] Carregados insights do benchmark para {len(self.current_parameters)} símbolos"
                )

        except Exception as e:
            logger.warning(
                f"[WARNING] Não foi possível carregar insights do benchmark: {e}"
            )
            # Usar configurações padrão baseadas nos melhores resultados conhecidos
            self._set_default_parameters()

    def _set_default_parameters(self):
        """Define parâmetros padrão baseados em descobertas anteriores."""
        default_params = {
            "price_amplification": 1.0,  # Descoberta: price_amp baixo funciona melhor
            "news_amplification": 11.3,  # Descoberta: news_amp alto é efetivo
            "min_confidence": 0.37,  # Descoberta: confiança moderada
        }

        # Aplicar para símbolos comuns
        common_symbols = [
            "BTCUSDT",
            "ETHUSDT",
            "BNBUSDT",
            "ADAUSDT",
            "SOLUSDT",
            "DOTUSDT",
            "LINKUSDT",
            "POLUSDT",
        ]
        for symbol in common_symbols:
            self.current_parameters[symbol] = default_params.copy()

        logger.info(
            "[TARGET] Parâmetros padrão definidos baseados em descobertas anteriores"
        )

    def create_study(self, symbol: str) -> optuna.Study:
        """Cria um estudo Optuna para um símbolo específico."""
        study_name = f"qualia_optimization_{symbol}_{int(time.time())}"

        # Configurar sampler
        if self.config.sampler_type == "TPE":
            sampler = TPESampler(seed=42, n_startup_trials=10)
        else:
            sampler = optuna.samplers.RandomSampler(seed=42)

        # D-04: Configurar pruner avançado
        if self.config.pruning_enabled:
            pruner = self.advanced_pruner.get_pruner(study_name)
            logger.info(
                f"🔧 Usando pruner avançado: {self.config.pruning_strategy.value}"
            )
        else:
            pruner = optuna.pruners.NopPruner()

        study = optuna.create_study(
            study_name=study_name, direction="maximize", sampler=sampler, pruner=pruner
        )

        # Adicionar trials iniciais baseados no benchmark
        if symbol in self.current_parameters:
            initial_params = self.current_parameters[symbol]
            study.enqueue_trial(initial_params)
            logger.info(
                f"[STATS] Estudo criado para {symbol} com parâmetros iniciais do benchmark"
            )

        return study

    def objective_function(self, trial: optuna.Trial, symbol: str) -> float:
        """
        Função objetivo para otimização.
        Combina Sharpe ratio e PnL 24h com pesos configuráveis.
        D-04: Integrado com multi-fidelidade e pruning avançado.
        """
        # Sugerir parâmetros
        price_amp = trial.suggest_float(
            "price_amplification", *self.config.price_amp_range
        )
        news_amp = trial.suggest_float(
            "news_amplification", *self.config.news_amp_range
        )
        min_conf = trial.suggest_float("min_confidence", *self.config.min_conf_range)

        # D-04: Usar multi-fidelidade se habilitado
        if self.config.enable_multi_fidelity and self.multi_fidelity_evaluator:
            # Executar avaliação multi-fidelidade (síncrona para compatibilidade com Optuna)
            import asyncio

            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            trading_metrics = loop.run_until_complete(
                self.multi_fidelity_evaluator.evaluate_trial(trial, symbol)
            )
        else:
            # Avaliação tradicional
            performance_metrics = self._evaluate_parameters_sync(
                symbol, price_amp, news_amp, min_conf
            )
            trading_metrics = self._convert_to_trading_metrics(performance_metrics)

        # D-04: Verificar pruning avançado
        if self.config.pruning_enabled:
            should_prune = self.advanced_pruner.should_prune_trial(
                trial, trading_metrics
            )
            if should_prune:
                logger.debug(f"Trial {trial.number} podado pelo sistema avançado")
                raise optuna.TrialPruned()

        # D-04: Verificar promoção multi-fidelidade
        if self.multi_fidelity_optimizer and trial.value is not None:
            current_fidelity = self.multi_fidelity_optimizer.get_fidelity_for_trial(
                trial.number, trial.study.trials
            )
            should_promote = self.multi_fidelity_optimizer.should_promote_trial(
                trial, current_fidelity
            )
            if should_promote:
                logger.info(f"🚀 Trial {trial.number} promovido para maior fidelidade")
                # Re-avaliar com maior fidelidade seria feito em próxima iteração

        # Calcular objetivo baseado na configuração
        if self.config.objective_metric == "sharpe_ratio":
            if self.config.enable_multi_fidelity:
                objective = trading_metrics.sharpe_ratio or -999.0
            else:
                objective = performance_metrics["sharpe_ratio"]
        elif self.config.objective_metric == "pnl_24h":
            if self.config.enable_multi_fidelity:
                objective = trading_metrics.total_pnl or -999.0
            else:
                objective = performance_metrics["pnl_24h"]
        else:  # sharpe_pnl_combined
            if self.config.enable_multi_fidelity:
                sharpe = trading_metrics.sharpe_ratio or 0.0
                pnl = trading_metrics.total_pnl or 0.0
                pnl_normalized = pnl / 1000.0  # Normalizar PnL
                objective = (
                    self.config.sharpe_weight * sharpe
                    + self.config.pnl_weight * pnl_normalized
                )
            else:
                sharpe_normalized = min(performance_metrics["sharpe_ratio"] / 10.0, 1.0)
                pnl_normalized = min(performance_metrics["pnl_24h"] / 10000.0, 1.0)
                objective = (
                    self.config.sharpe_weight * sharpe_normalized
                    + self.config.pnl_weight * pnl_normalized
                )

        # Log do trial
        logger.debug(
            f"🔬 Trial {trial.number} para {symbol}: "
            f"price_amp={price_amp:.2f}, news_amp={news_amp:.2f}, min_conf={min_conf:.2f} "
            f"→ objetivo={objective:.4f}"
        )

        # Registrar desempenho do trial para ajustes de pruning
        self.advanced_pruner.record_trial_result(trial.study.study_name, objective)

        return objective

    def _convert_to_trading_metrics(
        self, performance_metrics: Dict[str, float]
    ) -> TradingMetrics:
        """Converte métricas de performance para TradingMetrics."""
        metrics = TradingMetrics()
        metrics.sharpe_ratio = performance_metrics.get("sharpe_ratio")
        metrics.max_drawdown = performance_metrics.get("max_drawdown")
        metrics.total_pnl = performance_metrics.get(
            "total_pnl"
        ) or performance_metrics.get("pnl_24h")
        metrics.trade_count = performance_metrics.get("trade_count", 0)
        metrics.win_rate = performance_metrics.get("win_rate")
        metrics.profit_factor = performance_metrics.get("profit_factor")
        metrics.calmar_ratio = performance_metrics.get("calmar_ratio")
        return metrics

    def update_market_regime(self, regime: MarketRegime):
        """Atualiza o regime de mercado para todos os componentes."""
        if regime != self.current_market_regime:
            logger.info(
                f"[STATS] Regime de mercado atualizado: {self.current_market_regime.value} -> {regime.value}"
            )
            self.current_market_regime = regime

            # Atualizar componentes
            self.advanced_pruner.update_market_regime(regime)
            if self.multi_fidelity_evaluator:
                self.multi_fidelity_evaluator.update_market_regime(regime)

    def _create_multi_fidelity_evaluation_function(self, symbol: str):
        """Cria função de avaliação para multi-fidelidade."""

        async def evaluate_with_fidelity(
            symbol: str, params: Dict[str, float], fidelity_hours: int
        ) -> TradingMetrics:
            """
            Avalia parâmetros com nível específico de fidelidade.

            Args:
                symbol: Símbolo para avaliação
                params: Parâmetros a avaliar
                fidelity_hours: Horas de backtesting

            Returns:
                TradingMetrics com resultados
            """

            # Configurar parâmetros de avaliação baseados na fidelidade
            evaluation_config = {
                "lookback_hours": fidelity_hours,
                "price_amplification": params["price_amplification"],
                "news_amplification": params["news_amplification"],
                "min_confidence": params["min_confidence"],
            }

            # Executar avaliação (usar realistic evaluator se disponível)
            if self.use_realistic_evaluation and self.realistic_evaluator:
                # Usar avaliação realística
                performance_metrics = await asyncio.get_event_loop().run_in_executor(
                    None,
                    self.realistic_evaluator.evaluate_parameters,
                    symbol,
                    evaluation_config["price_amplification"],
                    evaluation_config["news_amplification"],
                    evaluation_config["min_confidence"],
                    fidelity_hours,
                )
            else:
                # Usar avaliação simulada
                performance_metrics = self._simulate_performance_metrics(
                    symbol,
                    evaluation_config["price_amplification"],
                    evaluation_config["news_amplification"],
                    evaluation_config["min_confidence"],
                )

            # Converter para TradingMetrics
            return self._convert_to_trading_metrics(performance_metrics)

        return evaluate_with_fidelity

    def _evaluate_parameters_sync(
        self, symbol: str, price_amp: float, news_amp: float, min_conf: float
    ) -> Dict[str, float]:
        """
        Versão síncrona da avaliação de parâmetros para compatibilidade com Optuna.
        """
        if self.use_realistic_evaluation and self.realistic_evaluator:
            try:
                # Verificar se já existe um loop rodando
                try:
                    current_loop = asyncio.get_running_loop()
                    # Se chegou aqui, há um loop rodando - usar fallback
                    logger.debug(
                        f"🔄 Loop assíncrono detectado, usando fallback para {symbol}"
                    )
                    return self._evaluate_parameters_fallback(
                        price_amp, news_amp, min_conf
                    )
                except RuntimeError:
                    # Não há loop rodando - pode criar um novo
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        result = loop.run_until_complete(
                            self.realistic_evaluator.evaluate_parameters_realistic(
                                symbol=symbol,
                                price_amp=price_amp,
                                news_amp=news_amp,
                                min_conf=min_conf,
                            )
                        )
                        logger.debug(
                            f"[TARGET] Avaliação realista concluída para {symbol}"
                        )
                        return result
                    finally:
                        loop.close()

            except Exception as e:
                logger.warning(
                    f"[WARNING] Erro na avaliação realista para {symbol}: {e}"
                )
                logger.info("[STATS] Usando fallback para avaliação simulada")

        # Fallback: simulação baseada nos padrões do benchmark
        return self._evaluate_parameters_fallback(price_amp, news_amp, min_conf)

    def _evaluate_parameters_fallback(
        self, price_amp: float, news_amp: float, min_conf: float
    ) -> Dict[str, float]:
        """
        Avaliação de fallback baseada nos padrões do benchmark.
        """
        sharpe_base = 15.0  # Média do benchmark

        # Ajustes baseados em descobertas
        if price_amp <= 2.0:
            sharpe_base += 2.0  # Price amp baixo é melhor
        if news_amp >= 10.0:
            sharpe_base += 3.0  # News amp alto é melhor
        if 0.3 <= min_conf <= 0.4:
            sharpe_base += 1.0  # Confiança moderada é melhor

        # Adicionar ruído realista
        sharpe_ratio = max(0, sharpe_base + np.random.normal(0, 2.0))

        # Simular PnL 24h correlacionado com Sharpe
        pnl_24h = sharpe_ratio * 500 + np.random.normal(0, 1000)

        return {
            "sharpe_ratio": sharpe_ratio,
            "pnl_24h": pnl_24h,
            "volatility": 0.15 + np.random.uniform(-0.05, 0.05),
            "max_drawdown": max(0, np.random.uniform(0, 0.1)),
            "win_rate": min(1.0, 0.6 + np.random.uniform(-0.2, 0.3)),
            "realistic_evaluation": False,
        }

    async def _evaluate_parameters(
        self, symbol: str, price_amp: float, news_amp: float, min_conf: float
    ) -> Dict[str, float]:
        """
        Avalia parâmetros usando simulação realista ou fallback simulado.
        """
        if self.use_realistic_evaluation and self.realistic_evaluator:
            try:
                # Usar avaliação realista com custos de transação
                result = await self.realistic_evaluator.evaluate_parameters_realistic(
                    symbol=symbol,
                    price_amp=price_amp,
                    news_amp=news_amp,
                    min_conf=min_conf,
                )

                logger.debug(
                    f"[TARGET] Avaliação realista {symbol}: Sharpe={result['sharpe_ratio']:.2f}, "
                    f"PnL={result['pnl_24h']:.0f}, Custos={result.get('cost_ratio_pct', 0):.1f}%"
                )

                return result

            except Exception as e:
                logger.warning(
                    f"[WARNING] Erro na avaliação realista para {symbol}: {e}"
                )
                logger.info("[STATS] Usando fallback para avaliação simulada")

        # Fallback: simulação baseada nos padrões do benchmark
        sharpe_base = 15.0  # Média do benchmark

        # Ajustes baseados em descobertas
        if price_amp <= 2.0:
            sharpe_base += 2.0  # Price amp baixo é melhor
        if news_amp >= 10.0:
            sharpe_base += 3.0  # News amp alto é melhor
        if 0.3 <= min_conf <= 0.4:
            sharpe_base += 1.0  # Confiança moderada é melhor

        # Adicionar ruído realista
        sharpe_ratio = max(0, sharpe_base + np.random.normal(0, 2.0))

        # Simular PnL 24h correlacionado com Sharpe
        pnl_24h = sharpe_ratio * 500 + np.random.normal(0, 1000)

        return {
            "sharpe_ratio": sharpe_ratio,
            "pnl_24h": pnl_24h,
            "volatility": 0.15 + np.random.uniform(-0.05, 0.05),
            "max_drawdown": max(0, np.random.uniform(0, 0.1)),
            "win_rate": min(1.0, 0.6 + np.random.uniform(-0.2, 0.3)),
            "realistic_evaluation": False,
        }

    async def optimize_symbol(self, symbol: str) -> Optional[OptimizationResult]:
        """
        Executa otimização Bayesiana para um símbolo específico.
        Suporta modo distribuído (D-02) e modo local.
        D-04: Integrado com multi-fidelidade e pruning avançado.
        """
        start_time = time.time()

        logger.info(f"[TARGET] Iniciando otimização para {symbol}")

        # D-04: Configurar função de avaliação para multi-fidelidade
        if self.config.enable_multi_fidelity and self.multi_fidelity_evaluator:
            self.multi_fidelity_evaluator.set_evaluation_function(
                self._create_multi_fidelity_evaluation_function(symbol)
            )

        try:
            # D-02: Use distributed optimization if enabled
            if self.config.use_distributed_optimization and self.distributed_client:
                return await self._optimize_symbol_distributed(symbol, start_time)
            else:
                return await self._optimize_symbol_local(symbol, start_time)

        except Exception as e:
            logger.error(f"❌ Erro na otimização de {symbol}: {e}")
            return None

    async def _optimize_symbol_local(
        self, symbol: str, start_time: float
    ) -> Optional[OptimizationResult]:
        """Otimização local usando Optuna diretamente."""
        # Criar ou recuperar estudo
        if symbol not in self.studies:
            self.studies[symbol] = self.create_study(symbol)

        study = self.studies[symbol]

        logger.info(
            f"🔍 Otimização local para {symbol} ({self.config.n_trials_per_cycle} trials)"
        )

        # Executar otimização
        study.optimize(
            lambda trial: self.objective_function(trial, symbol),
            n_trials=self.config.n_trials_per_cycle,
            timeout=300,  # 5 minutos máximo
        )

        # Obter melhor resultado
        best_trial = study.best_trial
        best_params = best_trial.params
        best_value = best_trial.value

        # Atualizar parâmetros atuais
        self.current_parameters[symbol] = best_params

        # Criar resultado
        result = OptimizationResult(
            timestamp=datetime.now(),
            symbol=symbol,
            trial_number=best_trial.number,
            parameters=best_params,
            objective_value=best_value,
            metrics=await self._evaluate_parameters(
                symbol,
                best_params["price_amplification"],
                best_params["news_amplification"],
                best_params["min_confidence"],
            ),
            duration_seconds=time.time() - start_time,
        )

        # Adicionar ao histórico
        self.optimization_history.append(result)

        # Atualizar estatísticas
        self.optimization_stats["total_optimizations"] += 1
        self.optimization_stats["successful_optimizations"] += 1
        self.optimization_stats["best_parameters_per_symbol"][symbol] = best_params

        logger.info(f"[PASS] Otimização local concluída para {symbol}:")
        logger.info(f"   [TARGET] Melhor objetivo: {best_value:.4f}")
        logger.info(f"   [STATS] Parâmetros: {best_params}")
        logger.info(f"   [TIME] Duração: {result.duration_seconds:.1f}s")

        return result

    async def _optimize_symbol_distributed(
        self, symbol: str, start_time: float
    ) -> Optional[OptimizationResult]:
        """Otimização distribuída usando BayesOpt Microservice."""
        logger.info(
            f"🌐 Otimização distribuída para {symbol} ({self.config.n_trials_per_cycle} trials)"
        )

        study_name = f"qualia_optimization_{int(time.time())}"
        best_result = None

        try:
            # Execute multiple trials
            for trial_idx in range(self.config.n_trials_per_cycle):
                trial_start = time.time()

                # Get parameter suggestion from service
                suggestion = await self.distributed_client.suggest_parameters(
                    study_name=study_name,
                    symbol=symbol,
                    price_amp_range=self.config.price_amp_range,
                    news_amp_range=self.config.news_amp_range,
                    min_conf_range=self.config.min_conf_range,
                    sampler_type=self.config.sampler_type,
                    pruning_enabled=self.config.pruning_enabled,
                )

                params = suggestion["parameters"]
                trial_id = suggestion["trial_id"]

                # Evaluate parameters
                metrics = await self._evaluate_parameters(
                    symbol,
                    params["price_amplification"],
                    params["news_amplification"],
                    params["min_confidence"],
                )

                # Calculate objective value
                objective_value = self._calculate_objective_value(metrics)
                trial_duration = time.time() - trial_start

                # Report result back to service
                await self.distributed_client.report_result(
                    study_name=study_name,
                    symbol=symbol,
                    trial_id=trial_id,
                    objective_value=objective_value,
                    metrics=metrics,
                    duration_seconds=trial_duration,
                    success=True,
                    trial_metadata=suggestion,
                )

                # Track best result
                if (
                    best_result is None
                    or objective_value > best_result["objective_value"]
                ):
                    best_result = {
                        "trial_number": suggestion["trial_number"],
                        "parameters": params,
                        "objective_value": objective_value,
                        "metrics": metrics,
                    }

                logger.info(
                    f"   Trial {trial_idx+1}/{self.config.n_trials_per_cycle}: objetivo={objective_value:.4f}"
                )

            # Update current parameters with best result
            if best_result:
                self.current_parameters[symbol] = best_result["parameters"]

                # Create optimization result
                result = OptimizationResult(
                    timestamp=datetime.now(),
                    symbol=symbol,
                    trial_number=best_result["trial_number"],
                    parameters=best_result["parameters"],
                    objective_value=best_result["objective_value"],
                    metrics=best_result["metrics"],
                    duration_seconds=time.time() - start_time,
                )

                # Adicionar ao histórico
                self.optimization_history.append(result)

                # Atualizar estatísticas
                self.optimization_stats["total_optimizations"] += 1
                self.optimization_stats["successful_optimizations"] += 1
                self.optimization_stats["best_parameters_per_symbol"][symbol] = (
                    best_result["parameters"]
                )

                logger.info(f"[PASS] Otimização distribuída concluída para {symbol}:")
                logger.info(
                    f"   [TARGET] Melhor objetivo: {best_result['objective_value']:.4f}"
                )
                logger.info(f"   [STATS] Parâmetros: {best_result['parameters']}")
                logger.info(f"   [TIME] Duração: {result.duration_seconds:.1f}s")

                return result

        except Exception as e:
            logger.error(f"[FAIL] Erro na otimização distribuída de {symbol}: {e}")
            # Try fallback to local if distributed fails
            if self.config.distributed_fallback_enabled:
                logger.info(f"[RETRY] Tentando fallback local para {symbol}")
                return await self._optimize_symbol_local(symbol, start_time)

        return None

    async def optimize_all_symbols(
        self, symbols: List[str]
    ) -> Dict[str, OptimizationResult]:
        """
        Executa otimização para múltiplos símbolos em paralelo.
        """
        logger.info(f"[ROCKET] Iniciando otimização para {len(symbols)} símbolos")

        # Executar otimizações em paralelo
        tasks = [self.optimize_symbol(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Processar resultados
        optimization_results = {}
        successful_count = 0

        for symbol, result in zip(symbols, results):
            if isinstance(result, OptimizationResult):
                optimization_results[symbol] = result
                successful_count += 1
            elif isinstance(result, Exception):
                logger.error(f"[FAIL] Falha na otimização de {symbol}: {result}")

        logger.info(
            f"[PASS] Otimização concluída: {successful_count}/{len(symbols)} símbolos otimizados"
        )

        return optimization_results

    def should_optimize(self) -> bool:
        """
        Verifica se deve executar otimização baseado no contador de ciclos.
        """
        self.cycle_counter += 1

        if self.cycle_counter >= self.config.optimization_interval_cycles:
            self.cycle_counter = 0
            return True

        return False

    def get_current_parameters(self, symbol: str) -> Dict[str, float]:
        """
        Retorna os parâmetros atuais para um símbolo.
        """
        return self.current_parameters.get(
            symbol,
            {
                "price_amplification": 1.0,
                "news_amplification": 11.3,
                "min_confidence": 0.37,
            },
        )

    def get_optimization_stats(self) -> Dict[str, Any]:
        """
        Retorna estatísticas de otimização.
        D-04: Inclui estatísticas de pruning e multi-fidelidade.
        """
        stats = self.optimization_stats.copy()
        stats.update(
            {
                "cycle_counter": self.cycle_counter,
                "last_optimization": (
                    self.last_optimization_time.isoformat()
                    if self.last_optimization_time
                    else None
                ),
                "total_symbols": len(self.current_parameters),
                "optimization_history_size": len(self.optimization_history),
                "current_market_regime": self.current_market_regime.value,
            }
        )

        # D-04: Adicionar estatísticas de pruning
        if self.advanced_pruner:
            stats["pruning_stats"] = self.advanced_pruner.get_pruning_stats()

        # D-04: Adicionar estatísticas de multi-fidelidade (sistema legado)
        if self.multi_fidelity_evaluator:
            stats["multi_fidelity_stats"] = self.multi_fidelity_evaluator.get_stats()

        # D-04: Adicionar estatísticas do novo multi-fidelity optimizer
        if self.multi_fidelity_optimizer:
            stats["multi_fidelity_optimizer_stats"] = (
                self.multi_fidelity_optimizer.get_statistics()
            )

        return stats

    def save_optimization_state(
        self, filepath: str = "data/bayesian_optimizer_state.json"
    ):
        """
        Salva o estado atual do otimizador.
        """
        try:
            state = {
                "timestamp": datetime.now().isoformat(),
                "config": asdict(self.config),
                "current_parameters": self.current_parameters,
                "optimization_stats": self.optimization_stats,
                "cycle_counter": self.cycle_counter,
                "optimization_history": [
                    asdict(result) for result in self.optimization_history[-100:]
                ],  # Últimos 100
            }

            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(state, f, indent=2, ensure_ascii=False, default=str)

            logger.info(f"💾 Estado do otimizador salvo em: {filepath}")

        except Exception as e:
            logger.error(f"❌ Erro ao salvar estado: {e}")

    def load_optimization_state(
        self, filepath: str = "data/bayesian_optimizer_state.json"
    ):
        """
        Carrega o estado do otimizador.
        """
        try:
            if not Path(filepath).exists():
                logger.info(
                    "📂 Arquivo de estado não encontrado, iniciando com estado limpo"
                )
                return

            with open(filepath, "r", encoding="utf-8") as f:
                state = json.load(f)

            # Restaurar estado
            self.current_parameters = state.get("current_parameters", {})
            self.optimization_stats = state.get("optimization_stats", {})
            self.cycle_counter = state.get("cycle_counter", 0)

            # Restaurar histórico
            history_data = state.get("optimization_history", [])
            self.optimization_history = []
            for item in history_data:
                if isinstance(item["timestamp"], str):
                    item["timestamp"] = datetime.fromisoformat(item["timestamp"])
                self.optimization_history.append(OptimizationResult(**item))

            logger.info(
                f"📂 Estado do otimizador carregado: {len(self.current_parameters)} símbolos"
            )

        except Exception as e:
            logger.error(f"❌ Erro ao carregar estado: {e}")

    async def cleanup(self):
        """
        Limpeza e salvamento final.
        """
        logger.info("🧹 Executando limpeza do BayesianOptimizer")
        self.save_optimization_state()

        # Limpar estudos
        self.studies.clear()

        logger.info("[PASS] Limpeza concluída")
