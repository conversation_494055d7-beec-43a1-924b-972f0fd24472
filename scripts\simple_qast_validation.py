#!/usr/bin/env python3
"""
Simple validation for QASTCore market integration fix
Tests only the specific method name correction
"""
import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def validate_qast_fix():
    """Validate that the QASTCore fix was applied correctly"""
    
    print("🔧 SIMPLE QASTCORE FIX VALIDATION")
    print("=" * 50)
    
    try:
        # Read the QASTCore file
        qast_file = os.path.join(project_root, 'src', 'qualia', 'core', 'qast_core.py')
        
        if not os.path.exists(qast_file):
            print(f"❌ QASTCore file not found: {qast_file}")
            return False
        
        with open(qast_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("📁 QASTCore file loaded successfully")
        
        # Test 1: Check if fetch_ticker is present
        print("\n🧪 TEST 1: Check for correct method name")
        if 'fetch_ticker(kucoin_symbol)' in content:
            print("✅ fetch_ticker() method found - CORRECT")
            test1_pass = True
        else:
            print("❌ fetch_ticker() method not found - INCORRECT")
            test1_pass = False
        
        # Test 2: Check if get_ticker is absent
        print("\n🧪 TEST 2: Check for incorrect method name")
        if 'get_ticker(kucoin_symbol)' in content:
            print("❌ get_ticker() method still present - INCORRECT")
            test2_pass = False
        else:
            print("✅ get_ticker() method not found - CORRECT")
            test2_pass = True
        
        # Test 3: Check specific line
        print("\n🧪 TEST 3: Check specific implementation line")
        lines = content.split('\n')
        found_line = False
        for i, line in enumerate(lines, 1):
            if 'ticker_data = await self.market_integration.fetch_ticker(kucoin_symbol)' in line:
                print(f"✅ Line {i}: {line.strip()}")
                print("✅ Correct implementation found")
                found_line = True
                break
        
        if not found_line:
            print("❌ Correct implementation line not found")
        
        test3_pass = found_line
        
        # Test 4: Check method context
        print("\n🧪 TEST 4: Check method context")
        if '_gather_market_data_from_integration' in content:
            print("✅ Method context _gather_market_data_from_integration found")
            test4_pass = True
        else:
            print("❌ Method context not found")
            test4_pass = False
        
        # Results summary
        print("\n📊 VALIDATION RESULTS")
        print("=" * 50)
        print(f"TEST 1 - fetch_ticker() present: {'✅ PASS' if test1_pass else '❌ FAIL'}")
        print(f"TEST 2 - get_ticker() absent: {'✅ PASS' if test2_pass else '❌ FAIL'}")
        print(f"TEST 3 - Implementation line: {'✅ PASS' if test3_pass else '❌ FAIL'}")
        print(f"TEST 4 - Method context: {'✅ PASS' if test4_pass else '❌ FAIL'}")
        
        overall_success = test1_pass and test2_pass and test3_pass and test4_pass
        
        if overall_success:
            print("\n🎉 QASTCORE FIX VALIDATION: SUCCESS!")
            print("✅ The get_ticker() -> fetch_ticker() fix has been applied correctly")
            print("✅ QASTCore should now work with market_integration")
        else:
            print("\n💥 QASTCORE FIX VALIDATION: FAILED!")
            print("❌ The fix was not applied correctly")
        
        return overall_success
        
    except Exception as e:
        print(f"\n💥 VALIDATION FAILED WITH ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = validate_qast_fix()
        
        if success:
            print("\n🚀 NEXT STEP: Ready for full system validation")
            print("   The QASTCore market integration fix is complete")
            print("   System should now operate without 'get_ticker' errors")
        else:
            print("\n🔧 NEXT STEP: Fix needs to be reapplied")
            print("   The correction was not properly implemented")
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⚠️ Validation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Validation crashed: {e}")
        sys.exit(1)
