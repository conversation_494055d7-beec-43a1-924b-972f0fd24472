"""Health monitoring dashboard for QUALIA system.

This module provides real-time health monitoring and alerting capabilities
for the QUALIA trading system, including:
- System component health tracking
- Performance metrics monitoring
- Proactive alerting and notifications
- Health trend analysis
- Automated recovery suggestions
"""

from __future__ import annotations

import asyncio
import time
import statistics
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from collections import deque, defaultdict
import json

from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class HealthStatus(Enum):
    """System health status levels."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"
    DEGRADED = "degraded"


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


@dataclass
class HealthMetric:
    """Individual health metric."""
    name: str
    value: float
    status: HealthStatus
    threshold_warning: float
    threshold_critical: float
    unit: str = ""
    description: str = ""
    timestamp: float = field(default_factory=time.time)
    
    @property
    def is_healthy(self) -> bool:
        """Check if metric is in healthy range."""
        return self.status == HealthStatus.HEALTHY


@dataclass
class ComponentHealth:
    """Health status for a system component."""
    component_name: str
    overall_status: HealthStatus
    metrics: Dict[str, HealthMetric]
    last_update: float
    uptime: float
    error_count: int = 0
    warning_count: int = 0
    
    @property
    def health_score(self) -> float:
        """Calculate overall health score (0-1)."""
        if not self.metrics:
            return 0.5
        
        healthy_metrics = sum(1 for m in self.metrics.values() if m.is_healthy)
        return healthy_metrics / len(self.metrics)


@dataclass
class HealthAlert:
    """Health monitoring alert."""
    alert_id: str
    component: str
    metric_name: str
    severity: AlertSeverity
    message: str
    value: float
    threshold: float
    timestamp: float
    acknowledged: bool = False
    resolved: bool = False


class HealthMonitoringDashboard:
    """Comprehensive health monitoring dashboard for QUALIA system.
    
    This dashboard provides:
    - Real-time component health tracking
    - Performance metrics monitoring
    - Proactive alerting system
    - Health trend analysis
    - Automated recovery recommendations
    """
    
    def __init__(
        self,
        name: str = "qualia_health_dashboard",
        update_interval: float = 30.0,
        history_retention: int = 1000,
        enable_auto_alerts: bool = True,
        alert_cooldown: float = 300.0,
    ):
        self.name = name
        self.update_interval = update_interval
        self.history_retention = history_retention
        self.enable_auto_alerts = enable_auto_alerts
        self.alert_cooldown = alert_cooldown
        
        # Component tracking
        self.components: Dict[str, ComponentHealth] = {}
        self.component_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=history_retention))
        
        # Alert management
        self.active_alerts: Dict[str, HealthAlert] = {}
        self.alert_history: deque = deque(maxlen=history_retention)
        self.alert_callbacks: List[Callable] = []
        self.last_alert_time: Dict[str, float] = {}
        
        # System-wide metrics
        self.system_metrics = {
            'overall_health_score': 0.0,
            'total_components': 0,
            'healthy_components': 0,
            'warning_components': 0,
            'critical_components': 0,
            'active_alerts': 0,
            'uptime': 0.0
        }
        
        # Background monitoring
        self.monitoring_task: Optional[asyncio.Task] = None
        self.is_monitoring = False
        self.start_time = time.time()
        
        # Health check functions
        self.health_checkers: Dict[str, Callable] = {}
        
        # Recovery suggestions
        self.recovery_suggestions: Dict[str, List[str]] = {
            'network_connectivity': [
                'Check network connection',
                'Restart network interfaces',
                'Verify DNS resolution',
                'Check firewall settings'
            ],
            'memory_usage': [
                'Clear cache and temporary data',
                'Restart memory-intensive components',
                'Check for memory leaks',
                'Increase memory allocation'
            ],
            'cpu_usage': [
                'Identify CPU-intensive processes',
                'Optimize algorithm parameters',
                'Scale horizontally if possible',
                'Check for infinite loops'
            ],
            'disk_space': [
                'Clean up log files',
                'Archive old data',
                'Check for large temporary files',
                'Increase disk capacity'
            ],
            'response_time': [
                'Check network latency',
                'Optimize database queries',
                'Review algorithm complexity',
                'Scale infrastructure'
            ]
        }

    async def start_monitoring(self) -> None:
        """Start health monitoring."""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        logger.info(f"Health monitoring dashboard '{self.name}' started")

    async def stop_monitoring(self) -> None:
        """Stop health monitoring."""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        
        if self.monitoring_task and not self.monitoring_task.done():
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info(f"Health monitoring dashboard '{self.name}' stopped")

    def register_component(
        self,
        component_name: str,
        health_checker: Optional[Callable] = None,
        metric_definitions: Optional[Dict[str, Dict]] = None
    ) -> None:
        """Register a component for health monitoring.
        
        Parameters
        ----------
        component_name : str
            Name of the component
        health_checker : Callable, optional
            Function to check component health
        metric_definitions : Dict, optional
            Metric definitions with thresholds
        """
        if health_checker:
            self.health_checkers[component_name] = health_checker
        
        # Initialize component health
        self.components[component_name] = ComponentHealth(
            component_name=component_name,
            overall_status=HealthStatus.UNKNOWN,
            metrics={},
            last_update=time.time(),
            uptime=0.0
        )
        
        # Initialize metrics if provided
        if metric_definitions:
            for metric_name, definition in metric_definitions.items():
                self.update_metric(
                    component_name,
                    metric_name,
                    0.0,  # Initial value
                    definition.get('threshold_warning', 0.8),
                    definition.get('threshold_critical', 0.9),
                    definition.get('unit', ''),
                    definition.get('description', '')
                )
        
        logger.info(f"Registered component '{component_name}' for health monitoring")

    def update_metric(
        self,
        component_name: str,
        metric_name: str,
        value: float,
        threshold_warning: float,
        threshold_critical: float,
        unit: str = "",
        description: str = ""
    ) -> None:
        """Update a health metric for a component.
        
        Parameters
        ----------
        component_name : str
            Name of the component
        metric_name : str
            Name of the metric
        value : float
            Current metric value
        threshold_warning : float
            Warning threshold
        threshold_critical : float
            Critical threshold
        unit : str
            Unit of measurement
        description : str
            Metric description
        """
        if component_name not in self.components:
            self.register_component(component_name)
        
        # Determine status based on thresholds
        if value >= threshold_critical:
            status = HealthStatus.CRITICAL
        elif value >= threshold_warning:
            status = HealthStatus.WARNING
        else:
            status = HealthStatus.HEALTHY
        
        # Create metric
        metric = HealthMetric(
            name=metric_name,
            value=value,
            status=status,
            threshold_warning=threshold_warning,
            threshold_critical=threshold_critical,
            unit=unit,
            description=description
        )
        
        # Update component
        component = self.components[component_name]
        component.metrics[metric_name] = metric
        component.last_update = time.time()
        
        # Update component overall status
        self._update_component_status(component)
        
        # Store in history
        self.component_history[component_name].append({
            'timestamp': time.time(),
            'metric_name': metric_name,
            'value': value,
            'status': status.value,
            'health_score': component.health_score
        })
        
        # Check for alerts
        if self.enable_auto_alerts:
            self._check_metric_alerts(component_name, metric)
        
        logger.debug(f"Updated metric {component_name}.{metric_name}: {value} ({status.value})")

    def _update_component_status(self, component: ComponentHealth) -> None:
        """Update overall component status based on metrics."""
        if not component.metrics:
            component.overall_status = HealthStatus.UNKNOWN
            return
        
        # Count status types
        critical_count = sum(1 for m in component.metrics.values() if m.status == HealthStatus.CRITICAL)
        warning_count = sum(1 for m in component.metrics.values() if m.status == HealthStatus.WARNING)
        
        # Update counters
        component.error_count = critical_count
        component.warning_count = warning_count
        
        # Determine overall status
        if critical_count > 0:
            component.overall_status = HealthStatus.CRITICAL
        elif warning_count > 0:
            component.overall_status = HealthStatus.WARNING
        else:
            component.overall_status = HealthStatus.HEALTHY
        
        # Update uptime
        component.uptime = time.time() - self.start_time

    def _check_metric_alerts(self, component_name: str, metric: HealthMetric) -> None:
        """Check if metric should trigger an alert."""
        alert_key = f"{component_name}.{metric.name}"
        
        # Check cooldown
        if alert_key in self.last_alert_time:
            if time.time() - self.last_alert_time[alert_key] < self.alert_cooldown:
                return
        
        # Determine if alert is needed
        should_alert = False
        severity = AlertSeverity.INFO
        
        if metric.status == HealthStatus.CRITICAL:
            should_alert = True
            severity = AlertSeverity.CRITICAL
        elif metric.status == HealthStatus.WARNING:
            should_alert = True
            severity = AlertSeverity.WARNING
        
        if should_alert:
            self._create_alert(component_name, metric, severity)

    def _create_alert(self, component_name: str, metric: HealthMetric, severity: AlertSeverity) -> None:
        """Create a new health alert."""
        alert_id = f"{component_name}_{metric.name}_{int(time.time())}"
        
        # Determine threshold that was exceeded
        threshold = metric.threshold_critical if severity == AlertSeverity.CRITICAL else metric.threshold_warning
        
        alert = HealthAlert(
            alert_id=alert_id,
            component=component_name,
            metric_name=metric.name,
            severity=severity,
            message=f"{component_name}.{metric.name} is {severity.value}: {metric.value}{metric.unit} (threshold: {threshold}{metric.unit})",
            value=metric.value,
            threshold=threshold,
            timestamp=time.time()
        )
        
        # Store alert
        self.active_alerts[alert_id] = alert
        self.alert_history.append(alert)
        self.last_alert_time[f"{component_name}.{metric.name}"] = time.time()
        
        # Trigger callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.warning(f"Alert callback failed: {e}")
        
        logger.warning(f"Health alert created: {alert.message}")

    async def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        while self.is_monitoring:
            try:
                await self._perform_health_checks()
                await self._update_system_metrics()
                await asyncio.sleep(self.update_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.update_interval)

    async def _perform_health_checks(self) -> None:
        """Perform health checks for all registered components."""
        for component_name, health_checker in self.health_checkers.items():
            try:
                if asyncio.iscoroutinefunction(health_checker):
                    health_data = await health_checker()
                else:
                    health_data = health_checker()
                
                # Update metrics from health check
                if isinstance(health_data, dict):
                    for metric_name, metric_info in health_data.items():
                        if isinstance(metric_info, dict):
                            self.update_metric(
                                component_name,
                                metric_name,
                                metric_info.get('value', 0.0),
                                metric_info.get('threshold_warning', 0.8),
                                metric_info.get('threshold_critical', 0.9),
                                metric_info.get('unit', ''),
                                metric_info.get('description', '')
                            )
                        else:
                            # Simple value
                            self.update_metric(
                                component_name,
                                metric_name,
                                float(metric_info),
                                0.8,  # Default thresholds
                                0.9
                            )
                
            except Exception as e:
                logger.error(f"Health check failed for {component_name}: {e}")
                # Mark component as unknown status
                if component_name in self.components:
                    self.components[component_name].overall_status = HealthStatus.UNKNOWN

    async def _update_system_metrics(self) -> None:
        """Update system-wide metrics."""
        total_components = len(self.components)
        healthy_count = sum(1 for c in self.components.values() if c.overall_status == HealthStatus.HEALTHY)
        warning_count = sum(1 for c in self.components.values() if c.overall_status == HealthStatus.WARNING)
        critical_count = sum(1 for c in self.components.values() if c.overall_status == HealthStatus.CRITICAL)
        
        # Calculate overall health score
        if total_components > 0:
            overall_health = sum(c.health_score for c in self.components.values()) / total_components
        else:
            overall_health = 0.0
        
        self.system_metrics.update({
            'overall_health_score': overall_health,
            'total_components': total_components,
            'healthy_components': healthy_count,
            'warning_components': warning_count,
            'critical_components': critical_count,
            'active_alerts': len(self.active_alerts),
            'uptime': time.time() - self.start_time
        })

    def get_dashboard_status(self) -> Dict[str, Any]:
        """Get comprehensive dashboard status."""
        return {
            'name': self.name,
            'is_monitoring': self.is_monitoring,
            'system_metrics': self.system_metrics.copy(),
            'components': {
                name: {
                    'status': component.overall_status.value,
                    'health_score': component.health_score,
                    'uptime': component.uptime,
                    'error_count': component.error_count,
                    'warning_count': component.warning_count,
                    'last_update': component.last_update,
                    'metrics': {
                        metric_name: {
                            'value': metric.value,
                            'status': metric.status.value,
                            'unit': metric.unit,
                            'threshold_warning': metric.threshold_warning,
                            'threshold_critical': metric.threshold_critical
                        }
                        for metric_name, metric in component.metrics.items()
                    }
                }
                for name, component in self.components.items()
            },
            'active_alerts': [
                {
                    'alert_id': alert.alert_id,
                    'component': alert.component,
                    'metric': alert.metric_name,
                    'severity': alert.severity.value,
                    'message': alert.message,
                    'timestamp': alert.timestamp,
                    'acknowledged': alert.acknowledged
                }
                for alert in self.active_alerts.values()
            ],
            'timestamp': time.time()
        }

    def get_component_health(self, component_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed health information for a specific component."""
        if component_name not in self.components:
            return None
        
        component = self.components[component_name]
        history = list(self.component_history[component_name])[-50:]  # Last 50 entries
        
        return {
            'component_name': component_name,
            'overall_status': component.overall_status.value,
            'health_score': component.health_score,
            'uptime': component.uptime,
            'last_update': component.last_update,
            'metrics': {
                name: {
                    'value': metric.value,
                    'status': metric.status.value,
                    'threshold_warning': metric.threshold_warning,
                    'threshold_critical': metric.threshold_critical,
                    'unit': metric.unit,
                    'description': metric.description,
                    'timestamp': metric.timestamp
                }
                for name, metric in component.metrics.items()
            },
            'recent_history': history,
            'recovery_suggestions': self._get_recovery_suggestions(component)
        }

    def _get_recovery_suggestions(self, component: ComponentHealth) -> List[str]:
        """Get recovery suggestions for a component."""
        suggestions = []
        
        for metric_name, metric in component.metrics.items():
            if metric.status in [HealthStatus.WARNING, HealthStatus.CRITICAL]:
                # Map metric names to suggestion categories
                suggestion_key = None
                if 'network' in metric_name.lower() or 'connectivity' in metric_name.lower():
                    suggestion_key = 'network_connectivity'
                elif 'memory' in metric_name.lower():
                    suggestion_key = 'memory_usage'
                elif 'cpu' in metric_name.lower():
                    suggestion_key = 'cpu_usage'
                elif 'disk' in metric_name.lower() or 'storage' in metric_name.lower():
                    suggestion_key = 'disk_space'
                elif 'response' in metric_name.lower() or 'latency' in metric_name.lower():
                    suggestion_key = 'response_time'
                
                if suggestion_key and suggestion_key in self.recovery_suggestions:
                    suggestions.extend(self.recovery_suggestions[suggestion_key])
        
        return list(set(suggestions))  # Remove duplicates

    def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge an active alert."""
        if alert_id in self.active_alerts:
            self.active_alerts[alert_id].acknowledged = True
            logger.info(f"Alert {alert_id} acknowledged")
            return True
        return False

    def resolve_alert(self, alert_id: str) -> bool:
        """Resolve an active alert."""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.resolved = True
            del self.active_alerts[alert_id]
            logger.info(f"Alert {alert_id} resolved")
            return True
        return False

    def add_alert_callback(self, callback: Callable) -> None:
        """Add callback for alert notifications."""
        self.alert_callbacks.append(callback)

    def get_health_trends(self, component_name: str, hours: float = 24.0) -> Dict[str, Any]:
        """Get health trends for a component over time."""
        if component_name not in self.component_history:
            return {}
        
        cutoff_time = time.time() - (hours * 3600)
        history = [
            h for h in self.component_history[component_name]
            if h['timestamp'] > cutoff_time
        ]
        
        if not history:
            return {}
        
        # Calculate trends
        health_scores = [h['health_score'] for h in history]
        timestamps = [h['timestamp'] for h in history]
        
        # Simple trend calculation
        if len(health_scores) > 1:
            trend = (health_scores[-1] - health_scores[0]) / len(health_scores)
        else:
            trend = 0.0
        
        return {
            'component_name': component_name,
            'time_window_hours': hours,
            'data_points': len(history),
            'average_health_score': statistics.mean(health_scores),
            'min_health_score': min(health_scores),
            'max_health_score': max(health_scores),
            'health_trend': trend,
            'improving': trend > 0.01,
            'degrading': trend < -0.01,
            'stable': abs(trend) <= 0.01,
            'history': history[-100:]  # Last 100 points for visualization
        }
