# Revisão Holística do módulo `src/qualia/market/kraken_integration.py`

Este documento resume a análise realizada no arquivo que implementa o `CryptoDataFetcher`,
um wrapper assíncrono para interagir com exchanges via **ccxt**. A classe é utilizada
como base para integrações específicas, como `KrakenIntegration` e `KucoinIntegration`.

## Papel na Pipeline QUALIA

- **Data-fetch**: coleta de OHLCV, tickers e execução de ordens.
- **Feature engineering**: fornece dados para cálculo de indicadores e métricas.
- **Quantum layer**: disponibiliza históricos para geração de features quânticas.
- **Estratégia e risk**: expõe métodos assíncronos que as estratégias e o controle
  de risco utilizam para tomada de decisão.
- **Execution**: executa ordens via `create_order` e afins.

## Dependências Principais

- `ccxt`/`ccxt.pro` para REST/WebSocket.
- `websockets` para fallback de conexão direta.
- `pandas`, `numpy` para manipulação de dados.
- Utilidades do projeto: `get_logger`, `normalize_symbol`, `CircuitBreaker`.

## Pontos de Acoplamento

- Dependência de `src.qualia.config` para diretórios de cache.
- Uso de `symbol_utils` para normalização de pares.
- `CircuitBreaker` compartilhado com outros módulos de rede.
- Métodos são chamados em estratégias e testes de integração.

## Débitos Técnicos Observados

- Funções extensas (`fetch_historical_data`, `_fetch_historical_chunk`) mesclam
  várias responsabilidades.
- Exceções genéricas (`except Exception`) dificultam testes específicos.
- Ausência de coleta de métricas (ex.: duração de chamadas REST/WebSocket).
- Algumas leituras de variáveis de ambiente repetidas poderiam ser centralizadas
  em `src.qualia.config`.
- Lógica de cache em arquivo poderia ser extraída para utilitário dedicado.

## Recomendações de Melhoria

1. **Refatoração Modular**: dividir a manipulação de histórico em funções menores
   e mover lógica de cache para módulo próprio.
2. **Observabilidade**: instrumentar com `statsd` ou similar, registrando tempo
   de resposta e taxas de erro dos métodos principais.
3. **Tipagem Estrita**: revisar `mypy` para remover tipos genéricos (`Any`) e
   documentar valores retornados em detalhes.
4. **Testabilidade**: criar mocks para chamadas WebSocket e extrair estratégia de
   retry/backoff em função isolada, permitindo testes unitários.
5. **Configuração**: mapear todos os parâmetros sensíveis para `config/*.yaml` ou
   variáveis de ambiente documentadas, evitando valores "hard-coded".

Estas melhorias alinham o módulo aos objetivos de performance e manutenibilidade
previstos no roadmap **QUALIA 2025-H2**.
