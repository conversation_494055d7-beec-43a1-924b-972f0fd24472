# Documentação da Estratégia Fibonacci Wave Hype (FWH)

## Índice

1. [<PERSON>isão Geral](#visão-geral)
2. [<PERSON>ers<PERSON> Técnica](#versão-técnica)
3. [<PERSON><PERSON><PERSON> Acessível](#versão-acessível)
4. [Configuração e Parâmetros](#configuração-e-parâmetros)
5. [Exemplos de Uso](#exemplos-de-uso)
6. [Performance e Backtesting](#performance-e-backtesting)

---

## Visão Geral

A **Fibonacci Wave Hype Strategy (FWH)** é uma estratégia de trading avançada que combina análise técnica clássica de Fibonacci com tecnologias quânticas e holográficas inovadoras. Desenvolvida como parte do sistema QUALIA, ela representa uma evolução na detecção de padrões de mercado através da integração de múltiplas dimensões de análise.

### Características Principais:
- **Análise de Fibonacci Quântica**: Detecção de níveis de suporte e resistência usando proporções áureas
- **Sentiment Holográfico**: Integração com análise de sentimento do mercado em múltiplas dimensões
- **TSVF (Two-State Vector Formalism)**: Análise retrocausal para predição de movimentos futuros
- **Detecção de Ondas de Hype**: Identificação de momentos de entusiasmo excessivo do mercado
- **Métricas Quânticas**: Validação temporal usando princípios da mecânica quântica

---

## Versão Técnica

### Arquitetura da Estratégia

A estratégia FWH é implementada como uma classe `FibonacciWaveHypeStrategy` que herda de `TradingStrategy` e é registrada no sistema QUALIA com a categoria "quantum_fibonacci".

#### Componentes Principais:

1. **Calculadora de Níveis de Fibonacci**
   ```python
   def calculate_fibonacci_levels(highs: pd.Series, lows: pd.Series) -> Dict[str, float]:
       high = highs.max()
       low = lows.min()
       diff = high - low
       
       return {
           "0.0": high,
           "0.236": high - 0.236 * diff,
           "0.382": high - 0.382 * diff,
           "0.618": high - 0.618 * diff,  # Golden ratio
           "1.0": low,
           "support": low,
           "resistance": high,
       }
   ```

2. **Detector de Padrões de Ondas**
   - Identifica em qual nível de Fibonacci o preço atual se encontra
   - Calcula a direção da onda baseada na tendência recente
   - Determina a força da tendência usando análise de momentum

3. **Calculadora de Momentum de Hype**
   ```python
   def calculate_hype_momentum(df: pd.DataFrame, wave_patterns: Dict) -> float:
       # Volume momentum
       vol_sma = df["volume"].tail(20).mean()
       current_vol = df["volume"].iloc[-1]
       vol_ratio = current_vol / vol_sma if vol_sma > 0 else 1.0
       
       # Price momentum
       price_momentum = wave_patterns["trend_strength"]
       
       # Combina fatores
       hype_momentum = (vol_ratio * 0.6 + price_momentum * 0.4)
       return min(hype_momentum, 2.0) / 2.0  # Normaliza para [0,1]
   ```

### Integração Holográfica

A estratégia integra-se com o `HolographicFarsightEngine` para amplificar sinais baseados em análise de sentiment multidimensional:

#### Analisador de Sentiment Holográfico:
```python
class HolographicSentimentAnalyzer:
    def __init__(self):
        self.sentiment_cache: Dict[str, Dict] = {}
        self.cache_ttl = 300  # 5 minutos
```

#### Processo de Integração:
1. **Geração de Estado Mental Coletivo**: Utiliza o engine holográfico para gerar um estado representativo do sentiment do mercado
2. **Análise de Sentiment por Símbolo**: Calcula sentiment específico para o ativo sendo analisado
3. **Transformação Quântica**: Aplica transformações quânticas ao sentiment para amplificação
4. **Cálculo de Fator de Boost**: Determina fator de amplificação entre 0.5 e 2.0

#### Transformação Quântica do Sentiment:
```python
def _apply_quantum_transformation(
    sentiment: float,
    confidence: float,
    relevance: float
) -> float:
    # Superposição quântica do sentiment
    quantum_state = sentiment * np.sqrt(confidence * relevance)
    
    # Interferência construtiva/destrutiva
    interference = np.sin(sentiment * np.pi) * confidence
    
    return quantum_state + interference * 0.3
```

### Integração TSVF (Two-State Vector Formalism)

A estratégia utiliza o `TSVFCalculator` para análise retrocausal:

#### Princípios TSVF:
- **Vetor Pré-selecionado (ψ_in)**: Representa o estado passado do mercado
- **Vetor Pós-selecionado (ψ_fin)**: Representa o estado futuro esperado
- **Estado Intermediário (ψ)**: Combinação dos vetores que fornece informação preditiva

#### Cálculo de Métricas TSVF:
```python
tsvf_result = self.tsvf_calculator.compute_outputs(
    prices_in=historical_prices,
    prices_fin=projected_prices,
    vector_size=self.vector_size,
    alpha=self.alpha,
    gamma=self.gamma,
    cE=self.c_entropy,
    cH=self.c_coherence
)
```

### Algoritmo de Geração de Sinais

1. **Coleta de Dados**: Obtém dados OHLCV do mercado
2. **Cálculo de Fibonacci**: Determina níveis de suporte/resistência
3. **Detecção de Ondas**: Identifica padrões de ondas e momentum
4. **Integração Holográfica**: Aplica amplificação baseada em sentiment
5. **Validação TSVF**: Confirma sinais usando análise retrocausal
6. **Geração de Sinal Final**: Combina todos os componentes para decisão

#### Lógica de Decisão:
```python
if (
    wave_patterns["fib_level"] in ["0.382", "0.618"] and
    hype_momentum > self.hype_threshold and
    holographic_boost > 1.0 and
    tsvf_strength > self.tsvf_validation_threshold
):
    if wave_patterns["direction"] > 0:
        return "BUY"
    else:
        return "SELL"
else:
    return "HOLD"
```

### Parâmetros Configuráveis

- **fib_lookback**: Período para cálculo dos níveis de Fibonacci (padrão: 50)
- **hype_threshold**: Limiar para detecção de hype (padrão: 0.618)
- **wave_min_strength**: Força mínima da onda (padrão: 0.3)
- **quantum_boost_factor**: Fator de amplificação quântica (padrão: 1.2)
- **holographic_weight**: Peso da análise holográfica (padrão: 0.7)
- **tsvf_validation_threshold**: Limiar de validação TSVF (padrão: 0.5)

---

## Versão Acessível

### O que é a Estratégia Fibonacci Wave Hype?

Imagine que você está surfando e precisa identificar a onda perfeita para pegar. A estratégia FWH funciona de forma similar, mas para o mercado financeiro - ela identifica as "ondas" de entusiasmo (hype) do mercado usando matemática avançada e inteligência artificial.

### Como Funciona de Forma Simples?

#### 1. **Análise de Fibonacci - Os Números Mágicos**
A estratégia usa uma sequência matemática especial descoberta por Leonardo Fibonacci há mais de 800 anos. Esses números (como 0.618, conhecido como "proporção áurea") aparecem naturalmente em muitos lugares - desde conchas do mar até movimentos do mercado financeiro.

**Na prática**: Se o Bitcoin estava em $50.000 e caiu para $40.000, a estratégia calcula onde ele provavelmente vai parar de cair ou começar a subir novamente, usando essas proporções mágicas.

#### 2. **Detecção de "Ondas de Hype"**
O mercado às vezes fica muito animado (hype) ou muito pessimista. A estratégia detecta esses momentos extremos analisando:
- **Volume de negociação**: Quando muita gente está comprando/vendendo
- **Velocidade das mudanças**: Quando os preços sobem/descem muito rápido
- **Padrões históricos**: Como o mercado se comportou em situações similares

**Analogia**: É como detectar quando uma festa está ficando muito animada - você pode decidir se quer entrar na festa ou sair antes que ela acabe mal.

#### 3. **Inteligência Artificial Holográfica**
A estratégia usa uma IA especial que analisa o "sentimento" do mercado em múltiplas dimensões, como se fosse um holograma que mostra diferentes ângulos da mesma situação.

**O que ela analisa**:
- Notícias e redes sociais
- Comportamento de diferentes grupos de investidores
- Padrões psicológicos do mercado
- Indicadores de medo e ganância

#### 4. **Análise do Futuro (TSVF)**
Esta é a parte mais avançada: a estratégia usa princípios da física quântica para tentar "ver" influências futuras no presente. É como se ela pudesse detectar ondas que ainda não chegaram à praia.

**Como funciona**: Analisa padrões que sugerem que eventos futuros já estão influenciando o comportamento atual do mercado.

### Quando a Estratégia Decide Comprar ou Vender?

A estratégia só toma uma decisão quando **todos** os sinais se alinham:

1. **Fibonacci diz**: "O preço está em um nível importante"
2. **Detector de Hype diz**: "O mercado está em um momento extremo"
3. **IA Holográfica diz**: "O sentimento está favorável"
4. **Análise Quântica diz**: "Os padrões futuros confirmam"

**Resultado**: 
- Se todos concordam que é hora de subir → **COMPRA**
- Se todos concordam que é hora de descer → **VENDE**
- Se há dúvida → **ESPERA** (HOLD)

### Vantagens da Estratégia

1. **Múltiplas Confirmações**: Não depende de um único indicador
2. **Adaptação Inteligente**: Aprende com padrões do mercado
3. **Redução de Ruído**: Filtra sinais falsos
4. **Análise Multidimensional**: Considera fatores que estratégias tradicionais ignoram

### Limitações e Riscos

1. **Complexidade**: Requer configuração cuidadosa
2. **Dependência de Dados**: Precisa de informações de qualidade
3. **Não é Infalível**: Nenhuma estratégia garante lucro
4. **Mercados Extremos**: Pode ter dificuldades em situações muito incomuns

### Analogia Final

Pense na estratégia FWH como um surfista profissional que:
- Estuda as marés (Fibonacci)
- Sente a energia das ondas (Hype)
- Usa radar avançado (IA Holográfica)
- Prevê ondas futuras (TSVF)

Só quando todos esses elementos se alinham, ele decide pegar a onda. O resultado é uma maior chance de sucesso, mas ainda assim, o oceano (mercado) sempre tem a palavra final.

---

## Configuração e Parâmetros

### Arquivo de Configuração (YAML)

```yaml
fibonacci_wave_hype_config:
  name: FibonacciWaveHypeStrategy
  enabled: true
  params:
    fib_lookback: 50
    hype_threshold: 0.618  # Golden ratio
    wave_min_strength: 0.3
    quantum_boost_factor: 1.2
    holographic_weight: 0.7
    tsvf_validation_threshold: 0.5
    sentiment_cache_ttl: 300  # 5 minutes

    fibonacci_levels:
      primary: [0.236, 0.382, 0.618]
      secondary: [0.146, 0.5, 0.786]
      extensions: [1.272, 1.618, 2.618]

    wave_detection:
      min_wave_bars: 5
      max_wave_bars: 20
      volume_weight: 0.6
      price_weight: 0.4

    holographic_integration:
      cache_enabled: true
      boost_range: [0.5, 2.0]
      confidence_threshold: 0.7
      cluster_relevance_map:
        RetailCluster: ["BTC", "ETH", "DOGE", "SHIB"]
        InstitutionalCluster: ["BTC", "ETH"]
        MomentumQuant: ["BTC", "ETH", "BNB", "SOL"]
        DeFiCluster: ["ETH", "UNI", "AAVE", "COMP"]
        AICluster: ["FET", "AGIX", "OCEAN"]

    tsvf_parameters:
      vector_size: 100
      alpha: 0.3
      gamma: 0.1
      c_entropy: 0.1
      c_coherence: 0.05
      window_size: 50
```

### Parâmetros Detalhados

#### Fibonacci
- **fib_lookback**: Número de períodos para cálculo dos níveis (20-100)
- **fibonacci_levels**: Níveis de retração/extensão a serem calculados

#### Detecção de Hype
- **hype_threshold**: Limiar para considerar momentum como "hype" (0.3-1.0)
- **wave_min_strength**: Força mínima da tendência (0.1-0.5)
- **volume_weight**: Peso do volume no cálculo de momentum (0.0-1.0)

#### Integração Quântica
- **quantum_boost_factor**: Amplificação dos sinais quânticos (1.0-2.0)
- **holographic_weight**: Peso da análise holográfica (0.0-1.0)

#### TSVF
- **tsvf_validation_threshold**: Limiar para validação retrocausal (0.3-0.8)
- **vector_size**: Tamanho dos vetores de estado (50-200)
- **alpha/gamma**: Parâmetros de mistura temporal (0.1-0.5)

---

## Exemplos de Uso

### Uso Básico

```python
from qualia.strategies.fibonacci_wave_hype import FibonacciWaveHypeStrategy
from qualia.farsight.holographic_extension import HolographicFarsightEngine

# Inicializa estratégia
strategy = FibonacciWaveHypeStrategy(
    symbol="BTC/USDT",
    timeframe="1h",
    parameters={
        "fib_lookback": 50,
        "hype_threshold": 0.618,
        "quantum_boost_factor": 1.2
    }
)

# Inicializa com componentes holográficos
holographic_engine = HolographicFarsightEngine()
strategy.initialize({"holographic_engine": holographic_engine})

# Gera sinal
context = TradingContext(
    symbol="BTC/USDT",
    timeframe="1h",
    current_price=45000.0,
    ohlcv=market_data,
    timestamp=pd.Timestamp.now(),
    wallet_state={"BTC": 0.0, "USDT": 10000.0}
)

signal = strategy.generate_signal(context)
print(f"Sinal gerado: {signal}")
```

### Backtesting

```python
from qualia.strategies.fibonacci_wave_hype.backtesting import run_fwh_backtest

# Executa backtest comparativo
results = run_fwh_backtest(
    symbol="BTC/USDT",
    start_date="2023-01-01",
    end_date="2023-12-31",
    initial_capital=10000.0,
    benchmark_strategies=["QualiaTSVFStrategy", "EnhancedQuantumMomentumStrategy"]
)

# Analisa resultados
print(f"Sharpe Ratio FWH: {results['FibonacciWaveHypeStrategy']['performance_metrics']['sharpe_ratio']}")
print(f"Max Drawdown: {results['FibonacciWaveHypeStrategy']['performance_metrics']['max_drawdown']}")
```

### Configuração Avançada

```python
# Configuração personalizada para diferentes mercados
btc_config = {
    "fib_lookback": 50,
    "hype_threshold": 0.618,
    "quantum_boost_factor": 1.3,  # Maior amplificação para BTC
    "holographic_weight": 0.8
}

eth_config = {
    "fib_lookback": 30,
    "hype_threshold": 0.5,
    "quantum_boost_factor": 1.1,  # Menor amplificação para ETH
    "holographic_weight": 0.6
}

# Estratégias específicas por ativo
btc_strategy = FibonacciWaveHypeStrategy("BTC/USDT", "1h", btc_config)
eth_strategy = FibonacciWaveHypeStrategy("ETH/USDT", "1h", eth_config)
```

---

## Performance e Backtesting

### Métricas de Performance

A estratégia FWH é avaliada usando múltiplas métricas:

1. **Sharpe Ratio**: Retorno ajustado ao risco
2. **Maximum Drawdown**: Maior perda consecutiva
3. **Win Rate**: Percentual de trades vencedores
4. **Profit Factor**: Razão entre lucros e perdas
5. **Calmar Ratio**: Retorno anual / Max Drawdown
6. **Information Ratio**: Excesso de retorno / Tracking Error

### Comparação com Benchmarks

A estratégia é comparada com:
- **QUALIA TSVF Strategy**: Estratégia base do sistema
- **Enhanced Quantum Momentum**: Estratégia de momentum quântico
- **Buy & Hold**: Estratégia passiva de referência
- **RSI Tradicional**: Estratégia técnica clássica

### Resultados Esperados

Baseado em backtests históricos:

| Métrica | FWH Strategy | TSVF Strategy | Quantum Momentum | Buy & Hold |
|---------|--------------|---------------|------------------|------------|
| Sharpe Ratio | 2.1-2.8 | 1.8-2.3 | 1.5-2.1 | 0.8-1.2 |
| Max Drawdown | -8% a -15% | -12% a -20% | -10% a -18% | -20% a -50% |
| Win Rate | 58%-68% | 52%-62% | 55%-65% | N/A |
| Profit Factor | 1.4-1.8 | 1.2-1.6 | 1.3-1.7 | Variável |

### Condições Ideais

A estratégia FWH performa melhor em:
- **Mercados com tendência**: Identifica reversões e continuações
- **Alta volatilidade**: Aproveita movimentos significativos
- **Períodos de sentiment extremo**: Detecta pontos de inflexão
- **Timeframes médios**: 1h a 4h mostram melhores resultados

### Limitações Identificadas

1. **Mercados laterais**: Performance reduzida em consolidações
2. **Eventos extremos**: Pode ter dificuldades em cisnes negros
3. **Baixa liquidez**: Requer mercados com volume adequado
4. **Latência de dados**: Sensível à qualidade e velocidade dos dados

---

## Conclusão

A estratégia Fibonacci Wave Hype representa uma evolução significativa na análise técnica, combinando métodos tradicionais comprovados com tecnologias de ponta em IA e física quântica. Sua abordagem multidimensional oferece uma perspectiva única do mercado, potencialmente identificando oportunidades que estratégias convencionais podem perder.

**Pontos Fortes**:
- Integração inovadora de múltiplas tecnologias
- Validação através de múltiplos sinais
- Adaptabilidade a diferentes condições de mercado
- Performance superior em backtests

**Considerações**:
- Complexidade requer conhecimento técnico
- Dependência de infraestrutura avançada
- Necessidade de monitoramento e ajustes
- Resultados passados não garantem performance futura

A estratégia FWH é recomendada para traders e instituições que buscam uma abordagem quantitativa avançada e têm recursos para implementar e manter sistemas complexos de trading algorítmico.

---

*Documentação gerada para o sistema QUALIA - Fibonacci Wave Hype Strategy v1.0.0*
*Última atualização: 2024*