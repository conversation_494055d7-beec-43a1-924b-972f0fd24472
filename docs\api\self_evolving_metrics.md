# Métricas do SelfEvolvingTradingSystem

Quando inicializado com `statsd_client`, o `SelfEvolvingTradingSystem` emite métricas StatsD relacionadas ao ciclo de aprendizado.

## Métricas

- `self_evolving.iteration_time_ms` – tempo gasto em cada iteração do ciclo, em milissegundos.
- `self_evolving.optimizations_applied` – contador de otimizações aplicadas ao sistema.
- `self_evolving.cycle_duration_ms` – duração total do ciclo de aprendizado, em milissegundos.

Todas as métricas aceitam a tag opcional `trace_id` para correlação com outros módulos.

```python
from datadog import DogStatsd

statsd = DogStatsd()
system = SelfEvolvingTradingSystem(engine, universe, statsd_client=statsd)
system.initialize_learning_cycle()
system.execute_learning_iteration({}, {}, trace_id="abc123")
```
