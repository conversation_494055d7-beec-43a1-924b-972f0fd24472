#!/usr/bin/env python3
"""
Debug da configuração OTOC.
"""

import yaml
import os

def main():
    print("🔍 DEBUG: Verificando configuração OTOC...")
    
    try:
        # Carregar configuração
        config_path = 'config/fwh_scalp_config.yaml'
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("✅ Configuração carregada com sucesso")
        
        # Navegar pela estrutura
        fwh_config = config.get('fibonacci_wave_hype_config', {})
        print(f"📊 FWH config keys: {list(fwh_config.keys())}")
        
        params_config = fwh_config.get('params', {})
        print(f"📊 Params config keys: {list(params_config.keys())}")
        
        mtf_config = params_config.get('multi_timeframe_config', {})
        print(f"📊 MTF config keys: {list(mtf_config.keys())}")
        
        otoc_config = mtf_config.get('otoc_config', {})
        print(f"📊 OTOC config: {otoc_config}")
        
        otoc_enabled = otoc_config.get('enabled', False)
        print(f"🌀 OTOC enabled: {otoc_enabled}")
        
        if otoc_enabled:
            print("✅ OTOC está habilitado na configuração!")
        else:
            print("❌ OTOC está desabilitado na configuração!")
            
        # Verificar se o caminho está correto
        test_path = config.get('fibonacci_wave_hype_config', {}).get(
            'params', {}
        ).get('multi_timeframe_config', {}).get('otoc_config', {}).get('enabled', False)
        
        print(f"🔍 Teste do caminho usado no código: {test_path}")
        
    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    main()
