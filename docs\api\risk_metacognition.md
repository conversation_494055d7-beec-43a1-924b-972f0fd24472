# Engines de Metacognição de Risco

## Visão Geral

O sistema de gerenciamento de risco do QUALIA possui dois componentes principais:

- **RiskAwarenessEngine** – monitora a eficácia das calibrações de risco e produz métricas
  que refletem o nível de consciência do sistema sobre sua própria performance.
- **RiskMetacognitionEngine** – utiliza as métricas geradas pelo RiskAwarenessEngine para
  executar ciclos periódicos de introspecção e gerar *insights* de otimização.

Estes módulos se integram para formar um *feedback loop* que permite ao QUALIA
adaptar de forma autônoma seus parâmetros de risco.

Observação: os identificadores e comentários nos arquivos do engine estão em inglês, mantendo esta documentação em português.

## Configuração do RiskAwarenessEngine

A função `create_risk_awareness_engine(config)` recebe um dicionário com as
seguintes opções:

- `observation_window_hours` – tamanho da janela usada para avaliar a performance
  (padrão: `24`).
- `min_samples` – quantidade mínima de snapshots necessários para calcular novas
  métricas (padrão: `50`).
- `learning_rate` – velocidade de adaptação do sistema de risco.
- `confidence_threshold` – nível de confiança para considerar que as métricas estão
  consolidadas.
- `baseline_drawdown` – valor de drawdown usado como referência para
  calcular redução de risco (padrão: `0.05`).
- `baseline_profit_factor` – profit factor histórico de referência
  (padrão: `1.2`).

```python
from src.qualia.metacognition.risk_awareness import create_risk_awareness_engine

config = {
    "observation_window_hours": 24,
    "min_samples": 50,
    "learning_rate": 0.1,
    "confidence_threshold": 0.7,
    "baseline_drawdown": 0.05,
    "baseline_profit_factor": 1.2,
}
engine = create_risk_awareness_engine(config)
```

O objeto retornado expõe métodos como `record_performance_snapshot` e
`get_risk_consciousness_level` para acompanhar a evolução das métricas.

## Configuração do RiskMetacognitionEngine

Já o `create_risk_metacognition_engine(config)` combina o engine anterior com
opções metacognitivas adicionais:

- `risk_awareness` – subconfiguração encaminhada para o RiskAwarenessEngine.
- `introspection_frequency_hours` – intervalo máximo entre sessões automáticas de
  introspecção (padrão: `6`).
- `insight_confidence_threshold` – apenas insights acima deste valor são
  registrados (padrão: `0.7`).
- `max_active_insights` – limite de insights simultâneos em aplicação
  (padrão: `5`).

```python
from src.qualia.metacognition.risk_metacognition_integration import (
    create_risk_metacognition_engine,
)

config = {
    "risk_awareness": {
        "observation_window_hours": 24,
        "min_samples": 50,
        "learning_rate": 0.1,
        "baseline_drawdown": 0.05,
        "baseline_profit_factor": 1.2,
    },
    "introspection_frequency_hours": 6,
    "insight_confidence_threshold": 0.7,
    "max_active_insights": 5,
}
metacognition_engine = create_risk_metacognition_engine(config)
```

Este engine possui métodos assíncronos como `process_market_observation` e
`force_introspection`, gerando objetos `MetacognitiveRiskInsight` quando encontra
padões ou oportunidades de melhoria.

## Uso Típico

1. Configure e crie o `RiskMetacognitionEngine` passando os parâmetros de risco.
2. Alimente o engine com dados de mercado via `process_market_observation`.
3. Consulte `get_metacognitive_state` para obter o nível de consciência e os
   insights gerados.
4. Aplique ou registre as ações sugeridas conforme necessário.

Estes componentes podem ser integrados ao `DynamicRiskController` e a outros
módulos para formar um ciclo completo de aprendizado de risco.
