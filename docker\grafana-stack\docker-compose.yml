version: '3.8'

services:
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: qualia-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./prometheus/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - qualia-monitoring
    restart: unless-stopped

  grafana:
    image: grafana/grafana:10.0.0
    container_name: qualia-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=qualia2024
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
      - GF_ALERTING_ENABLED=true
      - GF_UNIFIED_ALERTING_ENABLED=true
    networks:
      - qualia-monitoring
    restart: unless-stopped
    depends_on:
      - prometheus

  alertmanager:
    image: prom/alertmanager:v0.25.0
    container_name: qualia-alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    networks:
      - qualia-monitoring
    restart: unless-stopped

  node-exporter:
    image: prom/node-exporter:v1.6.0
    container_name: qualia-node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - qualia-monitoring
    restart: unless-stopped

  # StatsD Exporter para converter métricas StatsD para Prometheus
  statsd-exporter:
    image: prom/statsd-exporter:v0.22.7
    container_name: qualia-statsd-exporter
    ports:
      - "9102:9102"  # Prometheus metrics
      - "9125:9125/udp"  # StatsD UDP
      - "9125:9125/tcp"  # StatsD TCP
    volumes:
      - ./statsd/statsd_mapping.yml:/tmp/statsd_mapping.yml
    command:
      - '--statsd.mapping-config=/tmp/statsd_mapping.yml'
      - '--statsd.listen-udp=:9125'
      - '--statsd.listen-tcp=:9125'
      - '--web.listen-address=:9102'
    networks:
      - qualia-monitoring
    restart: unless-stopped

volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  alertmanager_data:
    driver: local

networks:
  qualia-monitoring:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
