#!/usr/bin/env python
"""Calibrate ACE complexity thresholds based on historical volatility.

This script downloads 90 days of hourly candles for a given symbol using
``ccxt`` and computes a realized volatility metric. The 20th and 80th
percentiles of the normalized volatility distribution are stored in a JSON file
for use by :class:`src.qualia.adaptive_evolution.AdaptiveConsciousnessEvolution`.
"""

from __future__ import annotations

import argparse
import json
import os
import time
from typing import List

import ccxt
import numpy as np
import pandas as pd

DEFAULT_OUTPUT_PATH = os.path.join("data", "cache", "ace_calibrated_thresholds.json")


def _fetch_candles(
    exchange: ccxt.Exchange, symbol: str, days: int = 90
) -> List[List[float]]:
    """Download OHLCV candles spanning the desired number of days."""
    timeframe = "1h"
    millis = exchange.milliseconds()
    since = millis - days * 24 * 60 * 60 * 1000
    all_candles: List[List[float]] = []
    increment = exchange.parse_timeframe(timeframe) * 1000

    while since < millis:
        candles = exchange.fetch_ohlcv(
            symbol, timeframe=timeframe, since=since, limit=1000
        )
        if not candles:
            break
        all_candles.extend(candles)
        since = candles[-1][0] + increment
        time.sleep(exchange.rateLimit / 1000)
        if len(all_candles) >= days * 24:
            break
    return all_candles[: days * 24]


def _calculate_thresholds(candles: List[List[float]]) -> dict:
    df = pd.DataFrame(
        candles, columns=["timestamp", "open", "high", "low", "close", "volume"]
    )
    df["return"] = np.log(df["close"]).diff()
    vol = df["return"].rolling(24).std() * np.sqrt(24)
    vol = vol.dropna()
    if vol.empty:
        raise ValueError("Not enough data to compute volatility")
    norm = (vol - vol.min()) / (vol.max() - vol.min())
    p20 = float(np.percentile(norm, 20))
    p80 = float(np.percentile(norm, 80))
    return {
        "complexity_threshold_calm": round(p20, 4),
        "complexity_threshold_volatile": round(p80, 4),
    }


def main() -> None:
    parser = argparse.ArgumentParser(description="Calibrate ACE complexity thresholds")
    parser.add_argument("--symbol", default="BTC/USDT", help="Trading pair symbol")
    parser.add_argument("--exchange", default="binance", help="Exchange id for ccxt")
    parser.add_argument(
        "--output", default=DEFAULT_OUTPUT_PATH, help="Path to save JSON"
    )
    args = parser.parse_args()

    exchange_class = getattr(ccxt, args.exchange)
    exchange = exchange_class()
    candles = _fetch_candles(exchange, args.symbol)
    thresholds = _calculate_thresholds(candles)
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    with open(args.output, "w", encoding="utf-8") as fh:
        json.dump(thresholds, fh, indent=2, ensure_ascii=False)
    print(json.dumps(thresholds, indent=2))


if __name__ == "__main__":
    main()
