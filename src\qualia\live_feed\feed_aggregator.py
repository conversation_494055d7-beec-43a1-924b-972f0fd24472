"""
QUALIA Feed Aggregator - Agregação inteligente de múltiplos feeds.

Este módulo agrega dados de múltiplos feeds, remove duplicatas, calcula médias
ponderadas e fornece dados consolidados para o sistema QUALIA.
"""

from __future__ import annotations

import asyncio
import time
from typing import Dict, Any, Optional, List, Callable, Set
from collections import defaultdict, deque
from dataclasses import dataclass
import statistics
import numpy as np

from ..utils.logger import get_logger
from .data_normalizer import NormalizedTicker, NormalizedOrderBook, NormalizedTrade

logger = get_logger(__name__)


@dataclass
class AggregatedTicker:
    """Ticker agregado de múltiplas fontes."""
    
    symbol: str
    price: float
    price_weighted: float  # Preço ponderado por volume
    bid: float
    ask: float
    spread: float
    volume_24h: float
    change_24h: float
    change_24h_percent: float
    high_24h: float
    low_24h: float
    timestamp: float
    sources: List[str]
    source_count: int
    price_variance: float  # Variância entre fontes
    confidence_score: float  # Score de confiança (0-1)
    raw_tickers: List[NormalizedTicker]


@dataclass
class AggregatedOrderBook:
    """Order book agregado de múltiplas fontes."""
    
    symbol: str
    bids: List[List[float]]  # Agregado e ordenado
    asks: List[List[float]]  # Agregado e ordenado
    best_bid: float
    best_ask: float
    spread: float
    timestamp: float
    sources: List[str]
    source_count: int
    depth_score: float  # Score de profundidade (0-1)


class FeedAggregator:
    """Agregador inteligente de múltiplos feeds."""
    
    def __init__(
        self,
        max_age_seconds: float = 10.0,
        min_sources: int = 1,
        price_variance_threshold: float = 0.05,  # 5%
        volume_weight_factor: float = 0.7,
        history_size: int = 100,
    ):
        """
        Inicializa o agregador.
        
        Args:
            max_age_seconds: Idade máxima dos dados para agregação
            min_sources: Mínimo de fontes para considerar dados válidos
            price_variance_threshold: Threshold de variância de preço entre fontes
            volume_weight_factor: Fator de peso do volume na agregação
            history_size: Tamanho do histórico mantido por símbolo
        """
        self.max_age_seconds = max_age_seconds
        self.min_sources = min_sources
        self.price_variance_threshold = price_variance_threshold
        self.volume_weight_factor = volume_weight_factor
        self.history_size = history_size
        
        # Cache de dados por símbolo
        self.ticker_data: Dict[str, Dict[str, NormalizedTicker]] = defaultdict(dict)
        self.orderbook_data: Dict[str, Dict[str, NormalizedOrderBook]] = defaultdict(dict)
        self.trade_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=history_size))
        
        # Cache de dados agregados
        self.aggregated_tickers: Dict[str, AggregatedTicker] = {}
        self.aggregated_orderbooks: Dict[str, AggregatedOrderBook] = {}
        
        # Callbacks
        self.on_aggregated_ticker_callback: Optional[Callable[[AggregatedTicker], None]] = None
        self.on_aggregated_orderbook_callback: Optional[Callable[[AggregatedOrderBook], None]] = None
        self.on_alert_callback: Optional[Callable[[str, Dict[str, Any]], None]] = None
        
        # Estatísticas
        self.stats = {
            'tickers_processed': 0,
            'orderbooks_processed': 0,
            'trades_processed': 0,
            'aggregations_created': 0,
            'alerts_triggered': 0,
        }
    
    def add_ticker(self, ticker: NormalizedTicker):
        """Adiciona ticker de uma fonte."""
        try:
            current_time = time.time()
            
            # Validar idade dos dados
            if current_time - ticker.timestamp > self.max_age_seconds:
                logger.debug(f"Ticker {ticker.symbol} muito antigo: {current_time - ticker.timestamp:.1f}s")
                return
            
            # Armazenar ticker por fonte
            self.ticker_data[ticker.symbol][ticker.source] = ticker
            self.stats['tickers_processed'] += 1
            
            # Limpar dados antigos
            self._cleanup_old_tickers(ticker.symbol, current_time)
            
            # Agregar dados se temos fontes suficientes
            if len(self.ticker_data[ticker.symbol]) >= self.min_sources:
                aggregated = self._aggregate_tickers(ticker.symbol)
                if aggregated:
                    self.aggregated_tickers[ticker.symbol] = aggregated
                    self.stats['aggregations_created'] += 1
                    
                    if self.on_aggregated_ticker_callback:
                        self.on_aggregated_ticker_callback(aggregated)
                    
                    # Verificar alertas
                    self._check_price_alerts(aggregated)
            
        except Exception as e:
            logger.error(f"Erro ao adicionar ticker: {e}")
    
    def add_orderbook(self, orderbook: NormalizedOrderBook):
        """Adiciona order book de uma fonte."""
        try:
            current_time = time.time()
            
            # Validar idade dos dados
            if current_time - orderbook.timestamp > self.max_age_seconds:
                logger.debug(f"OrderBook {orderbook.symbol} muito antigo")
                return
            
            # Armazenar orderbook por fonte
            self.orderbook_data[orderbook.symbol][orderbook.source] = orderbook
            self.stats['orderbooks_processed'] += 1
            
            # Limpar dados antigos
            self._cleanup_old_orderbooks(orderbook.symbol, current_time)
            
            # Agregar order books
            if len(self.orderbook_data[orderbook.symbol]) >= self.min_sources:
                aggregated = self._aggregate_orderbooks(orderbook.symbol)
                if aggregated:
                    self.aggregated_orderbooks[orderbook.symbol] = aggregated
                    
                    if self.on_aggregated_orderbook_callback:
                        self.on_aggregated_orderbook_callback(aggregated)
            
        except Exception as e:
            logger.error(f"Erro ao adicionar orderbook: {e}")
    
    def add_trade(self, trade: NormalizedTrade):
        """Adiciona trade ao histórico."""
        try:
            self.trade_history[trade.symbol].append(trade)
            self.stats['trades_processed'] += 1
        except Exception as e:
            logger.error(f"Erro ao adicionar trade: {e}")
    
    def _aggregate_tickers(self, symbol: str) -> Optional[AggregatedTicker]:
        """Agrega tickers de múltiplas fontes."""
        try:
            tickers = list(self.ticker_data[symbol].values())
            if not tickers:
                return None
            
            # Calcular estatísticas
            prices = [t.price for t in tickers]
            volumes = [t.volume_24h for t in tickers]
            
            # Preço médio simples
            avg_price = statistics.mean(prices)
            
            # Preço ponderado por volume
            total_volume = sum(volumes)
            if total_volume > 0:
                weighted_price = sum(p * v for p, v in zip(prices, volumes)) / total_volume
            else:
                weighted_price = avg_price
            
            # Variância de preços
            price_variance = statistics.variance(prices) if len(prices) > 1 else 0.0
            price_variance_pct = (price_variance ** 0.5) / avg_price if avg_price > 0 else 0.0
            
            # Score de confiança baseado em variância e número de fontes
            confidence_score = min(1.0, (1.0 - price_variance_pct) * (len(tickers) / 3.0))
            
            # Agregações simples
            avg_bid = statistics.mean([t.bid for t in tickers if t.bid > 0])
            avg_ask = statistics.mean([t.ask for t in tickers if t.ask > 0])
            total_volume_24h = sum(volumes)
            avg_change_24h = statistics.mean([t.change_24h for t in tickers])
            avg_change_24h_pct = statistics.mean([t.change_24h_percent for t in tickers])
            max_high_24h = max([t.high_24h for t in tickers])
            min_low_24h = min([t.low_24h for t in tickers if t.low_24h > 0])
            
            return AggregatedTicker(
                symbol=symbol,
                price=avg_price,
                price_weighted=weighted_price,
                bid=avg_bid,
                ask=avg_ask,
                spread=avg_ask - avg_bid if avg_ask > avg_bid else 0.0,
                volume_24h=total_volume_24h,
                change_24h=avg_change_24h,
                change_24h_percent=avg_change_24h_pct,
                high_24h=max_high_24h,
                low_24h=min_low_24h,
                timestamp=max([t.timestamp for t in tickers]),
                sources=[t.source for t in tickers],
                source_count=len(tickers),
                price_variance=price_variance_pct,
                confidence_score=confidence_score,
                raw_tickers=tickers
            )
            
        except Exception as e:
            logger.error(f"Erro ao agregar tickers para {symbol}: {e}")
            return None
    
    def _aggregate_orderbooks(self, symbol: str) -> Optional[AggregatedOrderBook]:
        """Agrega order books de múltiplas fontes."""
        try:
            orderbooks = list(self.orderbook_data[symbol].values())
            if not orderbooks:
                return None
            
            # Agregar bids e asks
            all_bids = []
            all_asks = []
            
            for ob in orderbooks:
                all_bids.extend(ob.bids)
                all_asks.extend(ob.asks)
            
            # Ordenar e consolidar
            all_bids.sort(key=lambda x: x[0], reverse=True)  # Maior preço primeiro
            all_asks.sort(key=lambda x: x[0])  # Menor preço primeiro
            
            # Pegar melhores preços
            best_bid = all_bids[0][0] if all_bids else 0.0
            best_ask = all_asks[0][0] if all_asks else 0.0
            
            # Score de profundidade
            total_bid_volume = sum([bid[1] for bid in all_bids[:10]])  # Top 10
            total_ask_volume = sum([ask[1] for ask in all_asks[:10]])  # Top 10
            depth_score = min(1.0, (total_bid_volume + total_ask_volume) / 1000.0)
            
            return AggregatedOrderBook(
                symbol=symbol,
                bids=all_bids[:20],  # Top 20 bids
                asks=all_asks[:20],  # Top 20 asks
                best_bid=best_bid,
                best_ask=best_ask,
                spread=best_ask - best_bid if best_ask > best_bid else 0.0,
                timestamp=max([ob.timestamp for ob in orderbooks]),
                sources=[ob.source for ob in orderbooks],
                source_count=len(orderbooks),
                depth_score=depth_score
            )
            
        except Exception as e:
            logger.error(f"Erro ao agregar orderbooks para {symbol}: {e}")
            return None
    
    def _cleanup_old_tickers(self, symbol: str, current_time: float):
        """Remove tickers antigos."""
        to_remove = []
        for source, ticker in self.ticker_data[symbol].items():
            if current_time - ticker.timestamp > self.max_age_seconds:
                to_remove.append(source)
        
        for source in to_remove:
            del self.ticker_data[symbol][source]
    
    def _cleanup_old_orderbooks(self, symbol: str, current_time: float):
        """Remove orderbooks antigos."""
        to_remove = []
        for source, orderbook in self.orderbook_data[symbol].items():
            if current_time - orderbook.timestamp > self.max_age_seconds:
                to_remove.append(source)
        
        for source in to_remove:
            del self.orderbook_data[symbol][source]
    
    def _check_price_alerts(self, ticker: AggregatedTicker):
        """Verifica alertas de preço."""
        try:
            # Alerta de alta variância entre fontes
            if ticker.price_variance > self.price_variance_threshold:
                alert = {
                    'type': 'high_price_variance',
                    'symbol': ticker.symbol,
                    'variance': ticker.price_variance,
                    'threshold': self.price_variance_threshold,
                    'sources': ticker.sources,
                    'prices': [t.price for t in ticker.raw_tickers]
                }
                
                if self.on_alert_callback:
                    self.on_alert_callback('high_price_variance', alert)
                
                self.stats['alerts_triggered'] += 1
                logger.warning(f"Alta variância de preço para {ticker.symbol}: {ticker.price_variance:.3f}")
            
        except Exception as e:
            logger.error(f"Erro ao verificar alertas: {e}")
    
    def get_aggregated_ticker(self, symbol: str) -> Optional[AggregatedTicker]:
        """Retorna ticker agregado para um símbolo."""
        return self.aggregated_tickers.get(symbol)
    
    def get_aggregated_orderbook(self, symbol: str) -> Optional[AggregatedOrderBook]:
        """Retorna orderbook agregado para um símbolo."""
        return self.aggregated_orderbooks.get(symbol)
    
    def get_trade_history(self, symbol: str, limit: int = 50) -> List[NormalizedTrade]:
        """Retorna histórico de trades para um símbolo."""
        trades = list(self.trade_history.get(symbol, []))
        return trades[-limit:] if trades else []
    
    def get_statistics(self) -> Dict[str, Any]:
        """Retorna estatísticas do agregador."""
        return {
            **self.stats,
            'symbols_tracked': len(self.aggregated_tickers),
            'active_sources': len(set(
                source for symbol_data in self.ticker_data.values()
                for source in symbol_data.keys()
            )),
        }
    
    def set_aggregated_ticker_callback(self, callback: Callable[[AggregatedTicker], None]):
        """Define callback para tickers agregados."""
        self.on_aggregated_ticker_callback = callback
    
    def set_aggregated_orderbook_callback(self, callback: Callable[[AggregatedOrderBook], None]):
        """Define callback para orderbooks agregados."""
        self.on_aggregated_orderbook_callback = callback
    
    def set_alert_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """Define callback para alertas."""
        self.on_alert_callback = callback
