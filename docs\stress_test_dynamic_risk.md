# Stress Test do DynamicRiskController

Este documento descreve o procedimento de stress testing do módulo
`DynamicRiskController` utilizando um executor <PERSON> simplificado.

O script [`scripts/stress_test_dynamic_risk.py`](../scripts/stress_test_dynamic_risk.py)
reproduz a lógica principal da classe `MonteCarloStressTester` encontrada em
`tests/market/test_dynamic_risk_regime_validation.py`. A versão incluída aqui
usa apenas dependências leves e grava os resultados em
`docs/data/stress_test_results.json`.

## Como executar

1. Certifique-se de que as dependências básicas estejam instaladas
   (`numpy`, `pandas`, `scikit-learn`, `pydantic`, `scipy`, `pyyaml`).
2. Execute o script diretamente:

```bash
python scripts/stress_test_dynamic_risk.py
```

O script irá simular dez cenários para cada tipo de evento extremo
(flash crash, gap, spike de volatilidade e mercado morto) e salvar as
estatísticas agregadas no arquivo JSON citado.

## Resultados Esperados

Para cada categoria de teste são calculadas taxas de sucesso e o número de
execuções realizadas. Um exemplo de saída agregada é:

```json
{
  "flash_crash": {"success_rate": 1.0, "total_tests": 10},
  "gap": {"success_rate": 1.0, "total_tests": 10},
  "volatility_spike": {"success_rate": 1.0, "total_tests": 10},
  "dead_market": {"success_rate": 1.0, "total_tests": 10},
  "overall_mean_success": 1.0
}
```

Se as dependências de visualização (como `qiskit`) estiverem ausentes, a
execução do controlador completo pode falhar. Nesse caso, instale os
requisitos listados em `requirements.txt` e `requirements-test.txt` antes de executar.
