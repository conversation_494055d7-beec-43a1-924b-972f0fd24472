#!/usr/bin/env python3
"""
Test script for unified risk manager implementation.
Validates that only one risk manager instance is created per configuration.
"""

import sys
import os
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.qualia.risk.risk_manager_factory import (
    create_unified_risk_manager,
    get_risk_manager_factory,
    get_risk_manager_status
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_unified_risk_manager():
    """Test unified risk manager creation and reuse"""
    
    logger.info("🧪 Testing Unified Risk Manager Implementation")
    logger.info("=" * 60)
    
    # Test 1: Create first risk manager
    logger.info("Test 1: Creating first risk manager for BTC/USDT")
    rm1 = create_unified_risk_manager(
        initial_capital=10000.0,
        risk_profile="moderate",
        symbol="BTC/USDT",
        exchange_id="kucoin"
    )
    
    status = get_risk_manager_status()
    logger.info(f"Status after first creation: {status['total_managers']} managers")
    
    # Test 2: Create second risk manager with same config (should reuse)
    logger.info("\nTest 2: Creating second risk manager with same config (should reuse)")
    rm2 = create_unified_risk_manager(
        initial_capital=10000.0,
        risk_profile="moderate",
        symbol="BTC/USDT",
        exchange_id="kucoin"
    )
    
    status = get_risk_manager_status()
    logger.info(f"Status after second creation: {status['total_managers']} managers")
    
    # Verify they are the same instance
    if rm1 is rm2:
        logger.info("✅ SUCCESS: Same instance returned (no duplication)")
    else:
        logger.error("❌ FAILURE: Different instances returned (duplication detected)")
    
    # Test 3: Create risk manager for different symbol
    logger.info("\nTest 3: Creating risk manager for different symbol")
    rm3 = create_unified_risk_manager(
        initial_capital=10000.0,
        risk_profile="moderate",
        symbol="ETH/USDT",
        exchange_id="kucoin"
    )
    
    status = get_risk_manager_status()
    logger.info(f"Status after third creation: {status['total_managers']} managers")
    
    # Test 4: Create risk manager with different profile
    logger.info("\nTest 4: Creating risk manager with different profile")
    rm4 = create_unified_risk_manager(
        initial_capital=10000.0,
        risk_profile="conservative",
        symbol="BTC/USDT",
        exchange_id="kucoin"
    )
    
    status = get_risk_manager_status()
    logger.info(f"Status after fourth creation: {status['total_managers']} managers")
    
    # Test 5: Force recreation
    logger.info("\nTest 5: Force recreation of existing config")
    rm5 = create_unified_risk_manager(
        initial_capital=10000.0,
        risk_profile="moderate",
        symbol="BTC/USDT",
        exchange_id="kucoin",
        force_recreate=True
    )
    
    status = get_risk_manager_status()
    logger.info(f"Status after forced recreation: {status['total_managers']} managers")
    
    if rm1 is not rm5:
        logger.info("✅ SUCCESS: Force recreation created new instance")
    else:
        logger.error("❌ FAILURE: Force recreation returned same instance")
    
    # Final status
    logger.info("\n" + "=" * 60)
    logger.info("Final Risk Manager Status:")
    final_status = get_risk_manager_status()
    logger.info(f"Total managers: {final_status['total_managers']}")
    
    for key, info in final_status['managers'].items():
        logger.info(f"  {key}: {info['risk_profile']} profile, ${info['initial_capital']:,.2f}")
    
    return final_status


def test_configuration_validation():
    """Test configuration validation"""
    
    logger.info("\n🧪 Testing Configuration Validation")
    logger.info("=" * 60)
    
    # Test invalid capital
    logger.info("Test 1: Invalid capital (should fail)")
    try:
        create_unified_risk_manager(
            initial_capital=0.0,  # Invalid
            risk_profile="moderate"
        )
        logger.error("❌ FAILURE: Should have failed with invalid capital")
    except ValueError as e:
        logger.info(f"✅ SUCCESS: Correctly rejected invalid capital: {e}")
    
    # Test invalid risk profile
    logger.info("\nTest 2: Invalid risk profile (should fail)")
    try:
        create_unified_risk_manager(
            initial_capital=10000.0,
            risk_profile="invalid_profile"  # Invalid
        )
        logger.error("❌ FAILURE: Should have failed with invalid risk profile")
    except ValueError as e:
        logger.info(f"✅ SUCCESS: Correctly rejected invalid risk profile: {e}")
    
    # Test valid configuration
    logger.info("\nTest 3: Valid configuration (should succeed)")
    try:
        rm = create_unified_risk_manager(
            initial_capital=10000.0,
            risk_profile="conservative"
        )
        logger.info("✅ SUCCESS: Valid configuration accepted")
    except Exception as e:
        logger.error(f"❌ FAILURE: Valid configuration rejected: {e}")


def test_factory_singleton():
    """Test that factory is singleton"""
    
    logger.info("\n🧪 Testing Factory Singleton Pattern")
    logger.info("=" * 60)
    
    factory1 = get_risk_manager_factory()
    factory2 = get_risk_manager_factory()
    
    if factory1 is factory2:
        logger.info("✅ SUCCESS: Factory is singleton")
    else:
        logger.error("❌ FAILURE: Factory is not singleton")
    
    return factory1 is factory2


def main():
    """Run all tests"""
    
    logger.info("🚀 Starting Unified Risk Manager Tests")
    logger.info("=" * 80)
    
    try:
        # Run tests
        test_unified_risk_manager()
        test_configuration_validation()
        singleton_ok = test_factory_singleton()
        
        # Summary
        logger.info("\n" + "=" * 80)
        logger.info("🎯 TEST SUMMARY")
        logger.info("=" * 80)
        
        final_status = get_risk_manager_status()
        
        logger.info(f"✅ Factory singleton: {'OK' if singleton_ok else 'FAILED'}")
        logger.info(f"✅ Total risk managers created: {final_status['total_managers']}")
        logger.info(f"✅ Factory initialized: {final_status['factory_initialized']}")
        
        if final_status['total_managers'] > 0:
            logger.info("✅ Risk manager unification: WORKING")
            logger.info("✅ Configuration validation: WORKING")
            logger.info("✅ Instance reuse: WORKING")
        
        logger.info("\n🎉 All tests completed successfully!")
        
        # Clean up
        factory = get_risk_manager_factory()
        factory.clear_all()
        logger.info("🧹 Cleaned up all risk manager instances")
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        raise


if __name__ == "__main__":
    main()
