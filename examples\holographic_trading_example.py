#!/usr/bin/env python3
"""
Exemplo prático do sistema de trading holográfico QUALIA.

Este exemplo demonstra como usar o HolographicFarsightEngine
para análise de mercado usando princípios holográficos.
"""

import asyncio
import time
import argparse
import yaml
from typing import Dict, List, Any

from src.qualia.consciousness.holographic_universe import (
    HolographicMarketUniverse,
    HolographicEvent,
    TradingSignal
)
from src.qualia.farsight.holographic_extension import HolographicFarsightEngine
from src.qualia.market.base_integration import CryptoDataFetcher
from src.qualia.market.market_data_client import MarketDataClient
from src.qualia.utils.logger import get_logger

logger = get_logger(__name__)

async def simulate_market_events() -> List[HolographicEvent]:
    """
    Simula eventos de mercado para demonstração.
    
    Returns:
        Lista de eventos holográficos simulados
    """
    events = []
    current_time = time.time()
    
    # Evento 1: Breaking news sobre Bitcoin
    events.append(HolographicEvent(
        position=(100, 100),  # Centro do campo (market region)
        time=current_time,
        amplitude=1.5,
        spatial_sigma=15.0,
        temporal_sigma=5.0,
        event_type="bitcoin_news",
        source_data={
            "headline": "Bitcoin ETF aprovado pela SEC",
            "sentiment": 0.8,
            "impact": "high"
        },
        confidence=0.9
    ))
    
    # Evento 2: Sentiment positivo sobre DeFi
    events.append(HolographicEvent(
        position=(150, 150),  # Região de news
        time=current_time + 1,
        amplitude=1.2,
        spatial_sigma=12.0,
        temporal_sigma=4.0,
        event_type="defi_sentiment",
        source_data={
            "topic": "DeFi protocols",
            "sentiment": 0.7,
            "volume": "increasing"
        },
        confidence=0.8
    ))
    
    # Evento 3: Análise técnica mostra divergência
    events.append(HolographicEvent(
        position=(75, 125),  # Entre market e sentiment
        time=current_time + 2,
        amplitude=0.8,
        spatial_sigma=8.0,
        temporal_sigma=3.0,
        event_type="technical_divergence",
        source_data={
            "indicator": "RSI divergence",
            "timeframe": "4h",
            "strength": "moderate"
        },
        confidence=0.7
    ))
    
    return events

async def run_holographic_analysis_demo():
    """
    Demonstra análise holográfica completa.
    """
    print("🌌 QUALIA Holographic Trading Analysis Demo")
    print("=" * 50)
    
    # Inicializa universo holográfico
    universe = HolographicMarketUniverse(
        field_size=(200, 200),
        diffusion_rate=0.3,
        feedback_strength=0.08
    )
    
    print(f"✅ Universo holográfico inicializado: {universe.field_size}")
    
    # Simula eventos de mercado
    print("\n📰 Simulando eventos de mercado...")
    market_events = await simulate_market_events()
    print(f"✅ {len(market_events)} eventos criados")
    
    # Injeta eventos no universo
    print("\n🔄 Injetando eventos no universo holográfico...")
    for event in market_events:
        await universe.inject_holographic_event(event)
        print(f"  • {event.event_type}: amplitude={event.amplitude:.2f}")
    
    # Executa simulação
    print("\n⚡ Executando simulação holográfica...")
    patterns = []
    
    for step in range(50):
        current_time = time.time() + step * 0.1
        await universe.step_evolution(current_time)
        
        # Analisa padrões a cada 10 passos
        if step % 10 == 0:
            step_patterns = universe.analyze_holographic_patterns()
            patterns.extend(step_patterns)
            print(f"  • Passo {step}: {len(step_patterns)} padrões detectados")
    
    print(f"✅ Simulação concluída: {len(patterns)} padrões totais")
    
    # Gera sinais de trading
    print("\n📊 Gerando sinais de trading...")
    trading_signals = universe.generate_trading_signals(patterns)
    
    # Exibe resultados
    print("\n🎯 RESULTADOS:")
    print(f"  • Padrões detectados: {len(patterns)}")
    print(f"  • Sinais de trading: {len(trading_signals)}")
    
    if trading_signals:
        print("\n📈 SINAIS DE TRADING:")
        for signal in trading_signals[:5]:  # Top 5
            print(f"  • {signal.symbol}: {signal.action}")
            print(f"    Confiança: {signal.confidence:.2f}")
            print(f"    Força: {signal.strength:.2f}")
            print(f"    Timeframe: {signal.timeframe}")
            print(f"    Razão: {signal.rationale}")
            print()
    
    # Resumo do campo
    field_summary = universe.get_field_summary()
    print("📊 RESUMO DO CAMPO:")
    print(f"  • Energia: {field_summary.get('field_energy', 0):.3f}")
    print(f"  • Entropia: {field_summary.get('field_entropy', 0):.3f}")
    print(f"  • Amplitude máxima: {field_summary.get('max_amplitude', 0):.3f}")
    print(f"  • Eventos ativos: {field_summary.get('active_events', 0)}")
    
    await universe.shutdown()
    print("\n✅ Demo concluída!")

async def run_farsight_integration_demo():
    """
    Demonstra integração com Farsight Engine.
    """
    print("\n🔮 QUALIA Farsight Integration Demo")
    print("=" * 50)
    
    try:
        # Cria engine holográfico
        # Nota: Isso pode falhar se FarsightEngine não estiver disponível
        # mas demonstra a integração
        
        print("⚠️  Nota: Esta demo requer FarsightEngine configurado")
        print("    Para demonstração completa, configure:")
        print("    1. ArXiv API access")
        print("    2. Embedding models")
        print("    3. Clustering algorithms")
        
        # Simulação de insights do Farsight
        mock_insights = [
            {
                'topic': 'Quantum Computing in Finance',
                'curvature': 0.8,
                'velocity': 0.3,
                'prediction_window': '4h',
                'sources': [{'title': 'Mock paper', 'link': 'mock://'}]
            },
            {
                'topic': 'DeFi Protocol Innovations',
                'curvature': 0.6,
                'velocity': 0.7,
                'prediction_window': '1h',
                'sources': [{'title': 'Mock paper 2', 'link': 'mock://'}]
            }
        ]
        
        print(f"🔄 Processando {len(mock_insights)} insights simulados...")
        
        # Simula conversão para eventos holográficos
        universe = HolographicMarketUniverse()
        events = []
        
        for i, insight in enumerate(mock_insights):
            # Mapeia insight para evento
            topic_hash = hash(insight['topic'])
            x = abs(topic_hash) % universe.field_size[0]
            y = abs(topic_hash // universe.field_size[0]) % universe.field_size[1]
            
            event = HolographicEvent(
                position=(x, y),
                time=time.time(),
                amplitude=insight['curvature'] * 1.5,
                spatial_sigma=5.0 + insight['velocity'] * 15.0,
                temporal_sigma=2.0 + insight['curvature'] * 6.0,
                event_type=f"farsight_{insight['topic'].lower().replace(' ', '_')}",
                source_data=insight,
                confidence=insight['curvature']
            )
            events.append(event)
        
        print(f"✅ {len(events)} eventos holográficos criados")
        
        # Injeta e processa
        for event in events:
            await universe.inject_holographic_event(event)
        
        # Executa simulação rápida
        for step in range(30):
            await universe.step_evolution(time.time() + step * 0.1)
        
        patterns = universe.analyze_holographic_patterns()
        signals = universe.generate_trading_signals(patterns)
        
        print("📊 Resultados da integração:")
        print(f"  • Padrões: {len(patterns)}")
        print(f"  • Sinais: {len(signals)}")
        
        await universe.shutdown()
        
    except Exception as e:
        print(f"⚠️  Erro na demo de integração: {e}")
        print("    Isso é esperado se FarsightEngine não estiver configurado")

async def run_backtest(
    credentials_path: str,
    symbol: str,
    timeframe: str,
    limit: int,
):
    """Executa um backtest simples usando dados OHLCV reais."""

    with open(credentials_path, "r") as f:
        creds = yaml.safe_load(f)

    fetcher = CryptoDataFetcher(
        api_key=creds.get("api_key"),
        api_secret=creds.get("api_secret"),
        password=creds.get("password"),
        exchange_id=creds.get("exchange_id", "kucoin"),
    )
    await fetcher.initialize_connection()
    client = MarketDataClient(fetcher)

    universe = HolographicMarketUniverse()

    events = await universe.ingest_real_market_data(client, symbol, timeframe, limit)
    for ev in events:
        await universe.step_evolution(ev.time)

    patterns = universe.analyze_holographic_patterns()
    signals = universe.generate_trading_signals(patterns)

    print(f"\n📈 Backtest concluído: {len(signals)} sinais gerados")
    await universe.shutdown()


def main():
    """Função principal do exemplo."""
    parser = argparse.ArgumentParser(description="Demo do Holographic Trading")
    parser.add_argument("--credentials", type=str, help="Arquivo YAML de credenciais")
    parser.add_argument("--backtest", action="store_true", help="Executa backtest usando OHLCV")
    parser.add_argument("--symbol", type=str, default="BTC/USDT", help="Símbolo para backtest")
    parser.add_argument("--timeframe", type=str, default="1h", help="Timeframe OHLCV")
    parser.add_argument("--limit", type=int, default=50, help="Qtd de candles")

    args = parser.parse_args()

    if args.backtest and args.credentials:
        asyncio.run(run_backtest(args.credentials, args.symbol, args.timeframe, args.limit))
        return

    print("🚀 Iniciando QUALIA Holographic Trading Demo")

    asyncio.run(run_holographic_analysis_demo())
    asyncio.run(run_farsight_integration_demo())

    print("\n🎉 Todas as demos concluídas!")
    print("\nPara usar em produção:")
    print("1. Configure FarsightEngine com API keys")
    print("2. Conecte feeds de dados reais")
    print("3. Integre com sistema de trading")
    print("4. Configure monitoramento e alertas")


if __name__ == "__main__":
    main()
