#!/usr/bin/env python3
"""
ANÁLISE DETALHADA: ROBUST_NORMALIZED Strategy Performance Anomaly
Investigando por que 11.6% win rate gera o melhor Sharpe ratio (0.962).
"""

import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Tuple

import pandas as pd
import numpy as np
import requests
import matplotlib.pyplot as plt


class RobustNormalizedAnalyzer:
    """Analisador detalhado da estratégia ROBUST_NORMALIZED."""
    
    def __init__(self):
        self.session = requests.Session()
    
    def fetch_data(self, symbol: str, days: int = 90) -> pd.DataFrame:
        """Busca dados históricos."""
        try:
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': symbol.replace('/', ''),
                'interval': '1h',
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index().dropna()
            
            # Indicadores necessários
            df['returns'] = df['close'].pct_change()
            df['sma_20'] = df['close'].rolling(20).mean()
            df['sma_50'] = df['close'].rolling(50).mean()
            df['rsi'] = self._calculate_rsi(df['close'], 14)
            df['volatility'] = df['returns'].rolling(20).std()
            
            return df
            
        except Exception as e:
            print(f"❌ Erro ao buscar dados para {symbol}: {e}")
            return pd.DataFrame()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calcula RSI."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def analyze_robust_normalized_detailed(self, df: pd.DataFrame, symbol: str) -> Dict[str, Any]:
        """Análise detalhada da estratégia ROBUST_NORMALIZED."""
        
        print(f"🔍 Analisando ROBUST_NORMALIZED para {symbol}...")
        
        # Implementação EXATA da estratégia original
        start_idx = 100
        
        # Calcula histórico para normalização
        trend_history = []
        mean_rev_history = []
        momentum_history = []
        rsi_history = []
        
        for i in range(50, start_idx):
            trend_raw = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            mean_rev_raw = -(df['close'].iloc[i] - df['sma_20'].iloc[i]) / df['sma_20'].iloc[i]
            momentum_raw = df['close'].iloc[i-10:i].pct_change().mean()
            rsi_raw = (df['rsi'].iloc[i] - 50) / 50
            
            trend_history.append(trend_raw)
            mean_rev_history.append(mean_rev_raw)
            momentum_history.append(momentum_raw)
            rsi_history.append(rsi_raw)
        
        # Estatísticas para normalização
        trend_mean, trend_std = np.mean(trend_history), np.std(trend_history)
        mean_rev_mean, mean_rev_std = np.mean(mean_rev_history), np.std(mean_rev_history)
        momentum_mean, momentum_std = np.mean(momentum_history), np.std(momentum_history)
        rsi_mean, rsi_std = np.mean(rsi_history), np.std(rsi_history)
        
        signals = []
        trade_details = []
        
        for i in range(start_idx, len(df)):
            # Componentes RAW
            trend_raw = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            mean_rev_raw = -(df['close'].iloc[i] - df['sma_20'].iloc[i]) / df['sma_20'].iloc[i]
            momentum_raw = df['close'].iloc[i-10:i].pct_change().mean()
            rsi_raw = (df['rsi'].iloc[i] - 50) / 50
            
            # NORMALIZAÇÃO Z-SCORE
            trend_signal = (trend_raw - trend_mean) / (trend_std + 1e-8)
            mean_rev_signal = (mean_rev_raw - mean_rev_mean) / (mean_rev_std + 1e-8)
            momentum_signal = (momentum_raw - momentum_mean) / (momentum_std + 1e-8)
            rsi_signal = (rsi_raw - rsi_mean) / (rsi_std + 1e-8)
            
            # Clipping
            trend_signal = np.clip(trend_signal, -3, 3)
            mean_rev_signal = np.clip(mean_rev_signal, -3, 3)
            momentum_signal = np.clip(momentum_signal, -3, 3)
            rsi_signal = np.clip(rsi_signal, -3, 3)
            
            # Combinação
            composite_signal = (
                trend_signal * 0.25 +
                mean_rev_signal * 0.25 +
                momentum_signal * 0.25 +
                rsi_signal * 0.25
            )
            
            # Threshold
            if abs(composite_signal) > 0.5:
                final_signal = np.clip(composite_signal * 0.3, -1, 1)
                signals.append(final_signal)
                
                # Registra detalhes do trade
                trade_details.append({
                    'timestamp': df.index[i],
                    'price': df['close'].iloc[i],
                    'signal': final_signal,
                    'composite_signal': composite_signal,
                    'trend_signal': trend_signal,
                    'mean_rev_signal': mean_rev_signal,
                    'momentum_signal': momentum_signal,
                    'rsi_signal': rsi_signal,
                    'rsi': df['rsi'].iloc[i],
                    'volatility': df['volatility'].iloc[i]
                })
            else:
                signals.append(0)
        
        # Calcula retornos detalhados
        returns = []
        winning_trades = []
        losing_trades = []
        
        for i in range(1, len(signals)):
            if abs(signals[i-1]) > 0.1:
                price_return = (df['close'].iloc[i+start_idx] - df['close'].iloc[i+start_idx-1]) / df['close'].iloc[i+start_idx-1]
                position_return = signals[i-1] * price_return
                returns.append(position_return)
                
                trade_info = {
                    'return': position_return,
                    'position': signals[i-1],
                    'price_change': price_return,
                    'timestamp': df.index[i+start_idx-1],
                    'signal_strength': abs(signals[i-1])
                }
                
                if position_return > 0:
                    winning_trades.append(trade_info)
                else:
                    losing_trades.append(trade_info)
        
        # Análise estatística detalhada
        returns_series = pd.Series(returns)
        
        if len(returns) == 0:
            return {'error': 'Nenhum trade executado'}
        
        # Métricas básicas
        total_return = returns_series.sum()
        win_rate = len(winning_trades) / len(returns) if returns else 0
        
        # Análise de ganhos vs perdas
        avg_win = np.mean([t['return'] for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t['return'] for t in losing_trades]) if losing_trades else 0
        
        # Profit factor e win/loss ratio
        total_wins = sum([t['return'] for t in winning_trades])
        total_losses = abs(sum([t['return'] for t in losing_trades]))
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        win_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0
        
        # Análise de extremos
        largest_win = max([t['return'] for t in winning_trades]) if winning_trades else 0
        largest_loss = min([t['return'] for t in losing_trades]) if losing_trades else 0
        
        # Métricas de risco
        volatility = returns_series.std()
        sharpe = (returns_series.mean() * 252) / (volatility * np.sqrt(252)) if volatility > 0 else 0
        
        # Drawdown
        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        # Análise de distribuição de trades
        win_sizes = [t['return'] for t in winning_trades]
        loss_sizes = [abs(t['return']) for t in losing_trades]
        
        return {
            'symbol': symbol,
            'total_return_pct': total_return * 100,
            'win_rate_pct': win_rate * 100,
            'total_trades': len(returns),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            
            # Análise de ganhos/perdas
            'avg_win_pct': avg_win * 100,
            'avg_loss_pct': avg_loss * 100,
            'win_loss_ratio': win_loss_ratio,
            'profit_factor': profit_factor,
            
            # Extremos
            'largest_win_pct': largest_win * 100,
            'largest_loss_pct': largest_loss * 100,
            'win_loss_extreme_ratio': abs(largest_win / largest_loss) if largest_loss != 0 else 0,
            
            # Métricas de risco
            'sharpe_ratio': sharpe,
            'volatility': volatility,
            'max_drawdown_pct': max_drawdown * 100,
            
            # Distribuições
            'win_sizes_distribution': {
                'mean': np.mean(win_sizes) * 100 if win_sizes else 0,
                'std': np.std(win_sizes) * 100 if win_sizes else 0,
                'median': np.median(win_sizes) * 100 if win_sizes else 0,
                'max': max(win_sizes) * 100 if win_sizes else 0,
                'min': min(win_sizes) * 100 if win_sizes else 0
            },
            'loss_sizes_distribution': {
                'mean': np.mean(loss_sizes) * 100 if loss_sizes else 0,
                'std': np.std(loss_sizes) * 100 if loss_sizes else 0,
                'median': np.median(loss_sizes) * 100 if loss_sizes else 0,
                'max': max(loss_sizes) * 100 if loss_sizes else 0,
                'min': min(loss_sizes) * 100 if loss_sizes else 0
            },
            
            # Amostras para análise
            'top_5_wins': sorted(winning_trades, key=lambda x: x['return'], reverse=True)[:5],
            'top_5_losses': sorted(losing_trades, key=lambda x: x['return'])[:5],
            'trade_details_sample': trade_details[:10]
        }
    
    def create_optimized_version(self, df: pd.DataFrame, symbol: str) -> Dict[str, Any]:
        """Cria versão otimizada da ROBUST_NORMALIZED."""
        
        print(f"🔧 Criando versão OTIMIZADA para {symbol}...")
        
        start_idx = 100
        
        # Mesmo processo de normalização
        trend_history = []
        mean_rev_history = []
        momentum_history = []
        rsi_history = []
        
        for i in range(50, start_idx):
            trend_raw = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            mean_rev_raw = -(df['close'].iloc[i] - df['sma_20'].iloc[i]) / df['sma_20'].iloc[i]
            momentum_raw = df['close'].iloc[i-10:i].pct_change().mean()
            rsi_raw = (df['rsi'].iloc[i] - 50) / 50
            
            trend_history.append(trend_raw)
            mean_rev_history.append(mean_rev_raw)
            momentum_history.append(momentum_raw)
            rsi_history.append(rsi_raw)
        
        trend_mean, trend_std = np.mean(trend_history), np.std(trend_history)
        mean_rev_mean, mean_rev_std = np.mean(mean_rev_history), np.std(mean_rev_history)
        momentum_mean, momentum_std = np.mean(momentum_history), np.std(momentum_history)
        rsi_mean, rsi_std = np.mean(rsi_history), np.std(rsi_history)
        
        signals = []
        
        for i in range(start_idx, len(df)):
            # 🔧 OTIMIZAÇÃO 1: Filtros de qualidade
            
            # Filtro de volatilidade
            vol_filter = df['volatility'].iloc[i] < df['volatility'].iloc[i-20:i].quantile(0.8)
            
            # Filtro de trend strength
            trend_strength = abs(df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            trend_filter = trend_strength > 0.01  # Trend mínimo
            
            # Filtro RSI (evita extremos)
            rsi_filter = 25 < df['rsi'].iloc[i] < 75
            
            if not (vol_filter and trend_filter and rsi_filter):
                signals.append(0)
                continue
            
            # Componentes RAW
            trend_raw = (df['sma_20'].iloc[i] - df['sma_50'].iloc[i]) / df['sma_50'].iloc[i]
            mean_rev_raw = -(df['close'].iloc[i] - df['sma_20'].iloc[i]) / df['sma_20'].iloc[i]
            momentum_raw = df['close'].iloc[i-10:i].pct_change().mean()
            rsi_raw = (df['rsi'].iloc[i] - 50) / 50
            
            # NORMALIZAÇÃO Z-SCORE
            trend_signal = (trend_raw - trend_mean) / (trend_std + 1e-8)
            mean_rev_signal = (mean_rev_raw - mean_rev_mean) / (mean_rev_std + 1e-8)
            momentum_signal = (momentum_raw - momentum_mean) / (momentum_std + 1e-8)
            rsi_signal = (rsi_raw - rsi_mean) / (rsi_std + 1e-8)
            
            # Clipping mais conservador
            trend_signal = np.clip(trend_signal, -2, 2)
            mean_rev_signal = np.clip(mean_rev_signal, -2, 2)
            momentum_signal = np.clip(momentum_signal, -2, 2)
            rsi_signal = np.clip(rsi_signal, -2, 2)
            
            # 🔧 OTIMIZAÇÃO 2: Pesos adaptativos baseados em volatilidade
            vol_adj = 1 / (1 + df['volatility'].iloc[i] * 50)  # Reduz sinal em alta volatilidade
            
            # Combinação com pesos adaptativos
            composite_signal = (
                trend_signal * 0.3 +      # Mais peso no trend
                momentum_signal * 0.3 +   # Mais peso no momentum
                mean_rev_signal * 0.2 +   # Menos peso na reversão
                rsi_signal * 0.2          # Menos peso no RSI
            ) * vol_adj
            
            # 🔧 OTIMIZAÇÃO 3: Threshold mais alto (0.8 vs 0.5)
            if abs(composite_signal) > 0.8:  # Mais seletivo
                final_signal = np.clip(composite_signal * 0.4, -1, 1)  # Sinal mais forte
                signals.append(final_signal)
            else:
                signals.append(0)
        
        # Calcula performance com gestão de risco
        return self._calculate_performance_with_risk_mgmt(df.iloc[start_idx:], signals, "ROBUST_OPTIMIZED")
    
    def _calculate_performance_with_risk_mgmt(self, df: pd.DataFrame, signals: List[float], strategy_name: str) -> Dict[str, Any]:
        """Performance com gestão de risco."""
        returns = []
        trades = 0
        winning_trades = 0
        losing_trades = 0
        total_wins = 0
        total_losses = 0
        
        for i in range(1, len(signals)):
            if abs(signals[i-1]) > 0.1:
                price_return = (df['close'].iloc[i] - df['close'].iloc[i-1]) / df['close'].iloc[i-1]
                raw_return = signals[i-1] * price_return
                
                # 🔧 GESTÃO DE RISCO: Stop-loss e take-profit
                if raw_return < -0.01:  # Stop-loss -1%
                    final_return = -0.01
                elif raw_return > 0.02:  # Take-profit +2%
                    final_return = 0.02
                else:
                    final_return = raw_return
                
                returns.append(final_return)
                trades += 1
                
                if final_return > 0:
                    winning_trades += 1
                    total_wins += final_return
                else:
                    losing_trades += 1
                    total_losses += abs(final_return)
        
        if not returns:
            return {'error': 'Nenhum trade'}
        
        returns_series = pd.Series(returns)
        
        total_return = returns_series.sum()
        volatility = returns_series.std() * np.sqrt(252)
        sharpe_ratio = (returns_series.mean() * 252) / volatility if volatility > 0 else 0
        
        cumulative = (1 + returns_series).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min())
        
        win_rate = winning_trades / trades if trades > 0 else 0
        avg_win = total_wins / winning_trades if winning_trades > 0 else 0
        avg_loss = total_losses / losing_trades if losing_trades > 0 else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        return {
            'strategy': strategy_name,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown * 100,
            'win_rate': win_rate * 100,
            'total_trades': trades,
            'volatility': volatility * 100,
            'avg_win_pct': avg_win * 100,
            'avg_loss_pct': avg_loss * 100,
            'profit_factor': profit_factor,
            'win_loss_ratio': avg_win / avg_loss if avg_loss > 0 else 0
        }


def run_robust_normalized_investigation():
    """Executa investigação completa da anomalia ROBUST_NORMALIZED."""
    print("🔍 INVESTIGAÇÃO: ROBUST_NORMALIZED Performance Anomaly")
    print("=" * 70)
    print("📊 Analisando por que 11.6% win rate gera melhor Sharpe (0.962)")
    print("=" * 70)

    analyzer = RobustNormalizedAnalyzer()

    symbols = ["BTCUSDT", "ETHUSDT"]
    all_analyses = []

    for symbol in symbols:
        print(f"\n📈 Investigando {symbol}...")

        df = analyzer.fetch_data(symbol, days=90)
        if df.empty or len(df) < 200:
            print(f"❌ Dados insuficientes para {symbol}")
            continue

        # Análise detalhada da versão original
        original_analysis = analyzer.analyze_robust_normalized_detailed(df, symbol)

        # Versão otimizada
        optimized_analysis = analyzer.create_optimized_version(df, symbol)

        if 'error' not in original_analysis:
            all_analyses.append(original_analysis)

            print(f"\n🔍 ANÁLISE DETALHADA - {symbol}:")
            print(f"   📊 Total Return: {original_analysis['total_return_pct']:.2f}%")
            print(f"   🎯 Win Rate: {original_analysis['win_rate_pct']:.1f}%")
            print(f"   📈 Total Trades: {original_analysis['total_trades']}")
            print(f"   💚 Avg Win: {original_analysis['avg_win_pct']:.3f}%")
            print(f"   💔 Avg Loss: {original_analysis['avg_loss_pct']:.3f}%")
            print(f"   ⚖️  Win/Loss Ratio: {original_analysis['win_loss_ratio']:.2f}")
            print(f"   📉 Profit Factor: {original_analysis['profit_factor']:.2f}")
            print(f"   🔥 Largest Win: {original_analysis['largest_win_pct']:.2f}%")
            print(f"   💥 Largest Loss: {original_analysis['largest_loss_pct']:.2f}%")
            print(f"   📊 Sharpe: {original_analysis['sharpe_ratio']:.3f}")

            # Diagnóstico da anomalia
            print(f"\n🔍 DIAGNÓSTICO DA ANOMALIA:")

            if original_analysis['win_rate_pct'] < 20 and original_analysis['sharpe_ratio'] > 0.5:
                print(f"   ⚠️  ANOMALIA CONFIRMADA: Win rate baixo ({original_analysis['win_rate_pct']:.1f}%) mas Sharpe alto ({original_analysis['sharpe_ratio']:.3f})")

                if original_analysis['win_loss_ratio'] > 3.0:
                    print(f"   💡 CAUSA: Ganhos médios MUITO MAIORES que perdas médias")
                    print(f"      • Ganho médio: {original_analysis['avg_win_pct']:.3f}%")
                    print(f"      • Perda média: {original_analysis['avg_loss_pct']:.3f}%")
                    print(f"      • Ratio: {original_analysis['win_loss_ratio']:.2f}x")

                if original_analysis['profit_factor'] > 2.0:
                    print(f"   💡 Profit Factor alto ({original_analysis['profit_factor']:.2f}): Poucos ganhos grandes compensam muitas perdas pequenas")

                # Análise de distribuição
                win_dist = original_analysis['win_sizes_distribution']
                loss_dist = original_analysis['loss_sizes_distribution']

                print(f"   📊 DISTRIBUIÇÃO DE GANHOS:")
                print(f"      • Média: {win_dist['mean']:.3f}%")
                print(f"      • Máximo: {win_dist['max']:.3f}%")
                print(f"      • Desvio: {win_dist['std']:.3f}%")

                print(f"   📊 DISTRIBUIÇÃO DE PERDAS:")
                print(f"      • Média: {loss_dist['mean']:.3f}%")
                print(f"      • Máximo: {loss_dist['max']:.3f}%")
                print(f"      • Desvio: {loss_dist['std']:.3f}%")

        # Mostra otimização
        if 'error' not in optimized_analysis:
            print(f"\n🔧 VERSÃO OTIMIZADA - {symbol}:")
            print(f"   📊 Total Return: {optimized_analysis['total_return_pct']:.2f}%")
            print(f"   🎯 Win Rate: {optimized_analysis['win_rate']:.1f}%")
            print(f"   📈 Total Trades: {optimized_analysis['total_trades']}")
            print(f"   📊 Sharpe: {optimized_analysis['sharpe_ratio']:.3f}")
            print(f"   ⚖️  Win/Loss Ratio: {optimized_analysis['win_loss_ratio']:.2f}")
            print(f"   📉 Profit Factor: {optimized_analysis['profit_factor']:.2f}")

    # Análise consolidada
    if all_analyses:
        print(f"\n" + "="*70)
        print(f"📊 ANÁLISE CONSOLIDADA DA ANOMALIA")
        print(f"="*70)

        avg_win_rate = np.mean([a['win_rate_pct'] for a in all_analyses])
        avg_sharpe = np.mean([a['sharpe_ratio'] for a in all_analyses])
        avg_win_loss_ratio = np.mean([a['win_loss_ratio'] for a in all_analyses])
        avg_profit_factor = np.mean([a['profit_factor'] for a in all_analyses])

        print(f"\n🎯 MÉTRICAS MÉDIAS:")
        print(f"   Win Rate: {avg_win_rate:.1f}%")
        print(f"   Sharpe Ratio: {avg_sharpe:.3f}")
        print(f"   Win/Loss Ratio: {avg_win_loss_ratio:.2f}")
        print(f"   Profit Factor: {avg_profit_factor:.2f}")

        print(f"\n💡 EXPLICAÇÃO MATEMÁTICA DA ANOMALIA:")
        print(f"   1. Win Rate baixo ({avg_win_rate:.1f}%) = Perde na maioria das vezes")
        print(f"   2. Win/Loss Ratio alto ({avg_win_loss_ratio:.2f}) = Quando ganha, ganha MUITO")
        print(f"   3. Profit Factor alto ({avg_profit_factor:.2f}) = Ganhos totais > Perdas totais")
        print(f"   4. Resultado: Sharpe alto ({avg_sharpe:.3f}) apesar do win rate baixo")

        print(f"\n🎯 RECOMENDAÇÕES DE OTIMIZAÇÃO:")
        print(f"   1. ✅ Aumentar threshold de 0.5 para 0.8 (mais seletivo)")
        print(f"   2. ✅ Adicionar filtros de qualidade (volatilidade, trend, RSI)")
        print(f"   3. ✅ Implementar stop-loss (-1%) e take-profit (+2%)")
        print(f"   4. ✅ Pesos adaptativos baseados em volatilidade")
        print(f"   5. ✅ Clipping mais conservador (-2 a +2 vs -3 a +3)")

        print(f"\n🏆 CONCLUSÃO:")
        if avg_win_loss_ratio > 3.0:
            print(f"   ✅ ROBUST_NORMALIZED é uma estratégia 'Home Run' válida")
            print(f"   ✅ Poucos ganhos grandes compensam muitas perdas pequenas")
            print(f"   ✅ Matematicamente sólida apesar do win rate baixo")
            print(f"   🎯 Foco: Melhorar seletividade para aumentar win rate")
        else:
            print(f"   ⚠️  Estratégia precisa de revisão mais profunda")

        # Salva análise
        output_dir = Path("results/robust_normalized_investigation")
        output_dir.mkdir(parents=True, exist_ok=True)

        timestamp = int(time.time())
        with open(output_dir / f"investigation_results_{timestamp}.json", 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'anomaly_confirmed': avg_win_rate < 20 and avg_sharpe > 0.5,
                'avg_metrics': {
                    'win_rate': avg_win_rate,
                    'sharpe_ratio': avg_sharpe,
                    'win_loss_ratio': avg_win_loss_ratio,
                    'profit_factor': avg_profit_factor
                },
                'detailed_analyses': all_analyses
            }, f, indent=2, default=str)

        print(f"\n💾 Análise detalhada salva em results/robust_normalized_investigation/")


if __name__ == "__main__":
    run_robust_normalized_investigation()
