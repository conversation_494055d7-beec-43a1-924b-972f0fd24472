# Persistência em JSON

A `QuantumPatternMemory` e o `QualiaSelfObserver` salvam seu estado em arquivos JSON localizados no diretório configurado para persistência (`QUALIA_CACHE_DIR` por padrão `data/cache`). O `QualiaSelfObserver` também consulta a chave `observer_persistence_dir` de `src.qualia.config.settings` quando nenhum diretório é informado explicitamente.

O arquivo da QPM é chamado `memory.json` e contém uma estrutura:

```json
{
  "memory": {"8": [...]},
  "max_memory_size_per_dimension": 1000,
  "similarity_threshold": 0.9
}
```

O observador grava `qualia_self_observer_memory.json` com as chaves `observed_patterns` e `entropy_deltas`.

Defina `QUALIA_CACHE_DIR` para escolher outro diretório ou forneça caminhos explicitamente aos construtores.
