"""
OTOC Metrics and Observability Module.

YAA-OBSERVABILITY: Métricas avançadas para debugging e monitoramento
do sistema OTOC em produção.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import json
from dataclasses import dataclass, asdict

from .logger import get_logger
from .otoc_calculator import calculate_otoc, get_otoc_diagnostics

logger = get_logger(__name__)


@dataclass
class OTOCMetrics:
    """Métricas OTOC para observabilidade."""
    timestamp: datetime
    symbol: str
    timeframe: str
    otoc_value: float
    threshold_used: float
    threshold_base: float
    volatility: float
    signal_original: str
    signal_filtered: str
    confidence_original: float
    confidence_filtered: float
    chaos_detected: bool
    adaptive_factor: float = 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte para dicionário para logging."""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result


class OTOCMetricsCollector:
    """Coletor de métricas OTOC para observabilidade."""
    
    def __init__(self, max_history: int = 1000):
        """
        Inicializa o coletor de métricas.
        
        Parameters
        ----------
        max_history : int
            Número máximo de métricas a manter em memória
        """
        self.max_history = max_history
        self.metrics_history: List[OTOCMetrics] = []
        self._chaos_events_count = 0
        self._total_signals_count = 0
    
    def record_otoc_decision(
        self,
        symbol: str,
        timeframe: str,
        otoc_value: float,
        threshold_used: float,
        threshold_base: float,
        volatility: float,
        signal_original: str,
        signal_filtered: str,
        confidence_original: float,
        confidence_filtered: float,
        adaptive_factor: float = 1.0
    ) -> None:
        """
        Registra uma decisão do filtro OTOC.
        
        YAA-METRICS: Captura todos os dados necessários para análise
        """
        chaos_detected = signal_original != signal_filtered
        
        metrics = OTOCMetrics(
            timestamp=datetime.now(),
            symbol=symbol,
            timeframe=timeframe,
            otoc_value=otoc_value,
            threshold_used=threshold_used,
            threshold_base=threshold_base,
            volatility=volatility,
            signal_original=signal_original,
            signal_filtered=signal_filtered,
            confidence_original=confidence_original,
            confidence_filtered=confidence_filtered,
            chaos_detected=chaos_detected,
            adaptive_factor=adaptive_factor
        )
        
        self.metrics_history.append(metrics)
        
        # Manter histórico limitado
        if len(self.metrics_history) > self.max_history:
            self.metrics_history.pop(0)
        
        # Atualizar contadores
        self._total_signals_count += 1
        if chaos_detected:
            self._chaos_events_count += 1
        
        # Log estruturado para Grafana/ELK
        logger.info(
            "OTOC_DECISION",
            extra={
                "otoc_metrics": metrics.to_dict(),
                "chaos_rate": self.get_chaos_rate(),
                "event_type": "otoc_filter"
            }
        )
    
    def get_chaos_rate(self, window_minutes: int = 60) -> float:
        """
        Calcula taxa de eventos de caos na janela especificada.
        
        Returns
        -------
        float
            Taxa de caos (0.0 a 1.0)
        """
        if not self.metrics_history:
            return 0.0
        
        cutoff_time = datetime.now() - timedelta(minutes=window_minutes)
        recent_metrics = [
            m for m in self.metrics_history 
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return 0.0
        
        chaos_count = sum(1 for m in recent_metrics if m.chaos_detected)
        return chaos_count / len(recent_metrics)
    
    def get_otoc_statistics(self, timeframe: Optional[str] = None) -> Dict[str, float]:
        """
        Calcula estatísticas OTOC para análise.
        
        Parameters
        ----------
        timeframe : str, optional
            Filtrar por timeframe específico
            
        Returns
        -------
        Dict[str, float]
            Estatísticas OTOC
        """
        if not self.metrics_history:
            return {}
        
        # Filtrar por timeframe se especificado
        metrics = self.metrics_history
        if timeframe:
            metrics = [m for m in metrics if m.timeframe == timeframe]
        
        if not metrics:
            return {}
        
        otoc_values = [m.otoc_value for m in metrics if not np.isnan(m.otoc_value)]
        thresholds = [m.threshold_used for m in metrics]
        volatilities = [m.volatility for m in metrics if not np.isnan(m.volatility)]
        
        stats = {
            "count": len(metrics),
            "chaos_events": sum(1 for m in metrics if m.chaos_detected),
            "chaos_rate": sum(1 for m in metrics if m.chaos_detected) / len(metrics),
        }
        
        if otoc_values:
            stats.update({
                "otoc_mean": np.mean(otoc_values),
                "otoc_std": np.std(otoc_values),
                "otoc_min": np.min(otoc_values),
                "otoc_max": np.max(otoc_values),
                "otoc_p50": np.percentile(otoc_values, 50),
                "otoc_p95": np.percentile(otoc_values, 95),
            })
        
        if thresholds:
            stats.update({
                "threshold_mean": np.mean(thresholds),
                "threshold_std": np.std(thresholds),
            })
        
        if volatilities:
            stats.update({
                "volatility_mean": np.mean(volatilities),
                "volatility_std": np.std(volatilities),
            })
        
        return stats
    
    def export_metrics_for_dashboard(
        self, 
        window_minutes: int = 60
    ) -> Dict[str, Any]:
        """
        Exporta métricas formatadas para dashboard (Grafana).
        
        YAA-DASHBOARD: Formato otimizado para visualização
        """
        cutoff_time = datetime.now() - timedelta(minutes=window_minutes)
        recent_metrics = [
            m for m in self.metrics_history 
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {
                "timestamp": datetime.now().isoformat(),
                "window_minutes": window_minutes,
                "total_signals": 0,
                "chaos_events": 0,
                "chaos_rate": 0.0,
                "timeframes": {}
            }
        
        # Métricas por timeframe
        timeframe_stats = {}
        for tf in ["1m", "5m", "15m", "1h"]:
            tf_metrics = [m for m in recent_metrics if m.timeframe == tf]
            if tf_metrics:
                timeframe_stats[tf] = {
                    "count": len(tf_metrics),
                    "chaos_events": sum(1 for m in tf_metrics if m.chaos_detected),
                    "chaos_rate": sum(1 for m in tf_metrics if m.chaos_detected) / len(tf_metrics),
                    "avg_otoc": np.mean([m.otoc_value for m in tf_metrics if not np.isnan(m.otoc_value)]),
                    "avg_threshold": np.mean([m.threshold_used for m in tf_metrics]),
                }
        
        return {
            "timestamp": datetime.now().isoformat(),
            "window_minutes": window_minutes,
            "total_signals": len(recent_metrics),
            "chaos_events": sum(1 for m in recent_metrics if m.chaos_detected),
            "chaos_rate": sum(1 for m in recent_metrics if m.chaos_detected) / len(recent_metrics),
            "avg_otoc": np.mean([m.otoc_value for m in recent_metrics if not np.isnan(m.otoc_value)]),
            "avg_threshold": np.mean([m.threshold_used for m in recent_metrics]),
            "timeframes": timeframe_stats
        }
    
    def generate_alert_conditions(self) -> List[Dict[str, Any]]:
        """
        Gera condições de alerta baseadas nas métricas OTOC.
        
        YAA-ALERTS: Sistema de alertas inteligente
        """
        alerts = []
        
        # Taxa de caos muito alta
        chaos_rate_1h = self.get_chaos_rate(window_minutes=60)
        if chaos_rate_1h > 0.8:
            alerts.append({
                "type": "HIGH_CHAOS_RATE",
                "severity": "WARNING",
                "message": f"Taxa de caos muito alta: {chaos_rate_1h:.1%} na última hora",
                "chaos_rate": chaos_rate_1h,
                "threshold": 0.8
            })
        
        # OTOC médio muito alto (mercado instável)
        recent_stats = self.get_otoc_statistics()
        if recent_stats and recent_stats.get("otoc_mean", 0) > 0.7:
            alerts.append({
                "type": "HIGH_AVERAGE_OTOC",
                "severity": "INFO",
                "message": f"OTOC médio alto: {recent_stats['otoc_mean']:.3f} (mercado instável)",
                "otoc_mean": recent_stats["otoc_mean"],
                "threshold": 0.7
            })
        
        # Poucos sinais recentes (possível problema)
        if recent_stats and recent_stats.get("count", 0) < 5:
            alerts.append({
                "type": "LOW_SIGNAL_COUNT",
                "severity": "WARNING",
                "message": f"Poucos sinais na última hora: {recent_stats['count']}",
                "signal_count": recent_stats["count"],
                "expected_minimum": 5
            })
        
        return alerts


# YAA-SINGLETON: Instância global para coleta de métricas
_global_otoc_metrics = OTOCMetricsCollector()


def get_otoc_metrics_collector() -> OTOCMetricsCollector:
    """Retorna a instância global do coletor de métricas."""
    return _global_otoc_metrics


def log_trading_decision(
    symbol: str,
    timeframe: str,
    signal_original: str,
    signal_filtered: str,
    confidence_original: float,
    confidence_filtered: float,
    extras: Optional[Dict[str, Any]] = None
) -> None:
    """
    Função de conveniência para logging de decisões de trading.
    
    YAA-INTEGRATION: Interface simples para uso na estratégia FWH
    """
    extras = extras or {}
    
    # Extrair métricas OTOC dos extras
    otoc_value = extras.get("otoc", 0.0)
    threshold = extras.get("otoc_thr", 0.35)
    volatility = extras.get("volatility", 0.0)
    
    # Registrar métricas se OTOC disponível
    if "otoc" in extras:
        collector = get_otoc_metrics_collector()
        collector.record_otoc_decision(
            symbol=symbol,
            timeframe=timeframe,
            otoc_value=otoc_value,
            threshold_used=threshold,
            threshold_base=extras.get("otoc_base_thr", threshold),
            volatility=volatility,
            signal_original=signal_original,
            signal_filtered=signal_filtered,
            confidence_original=confidence_original,
            confidence_filtered=confidence_filtered,
            adaptive_factor=extras.get("adaptive_factor", 1.0)
        )
    
    # Log estruturado adicional
    logger.info(
        f"TRADING_DECISION: {symbol} {timeframe} {signal_original}→{signal_filtered}",
        extra={
            "symbol": symbol,
            "timeframe": timeframe,
            "signal_original": signal_original,
            "signal_filtered": signal_filtered,
            "confidence_original": confidence_original,
            "confidence_filtered": confidence_filtered,
            **extras
        }
    )
