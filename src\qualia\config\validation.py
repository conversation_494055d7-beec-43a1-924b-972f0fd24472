"""Validation helpers for strategy configuration and environment settings."""

from __future__ import annotations

from typing import Mapping, Any

from .settings import require_env
from ..utils.validation import validate_non_negative_int
from ..utils.logger import get_logger

logger = get_logger(__name__)

MANDATORY_ENV_VARS = ["KRAKEN_API_KEY", "KRAKEN_API_SECRET"]


def validate_strategy_config(config: Mapping[str, Any]) -> None:
    """Validate minimal strategy configuration structure.

    Parameters
    ----------
    config
        Mapping with the strategy configuration loaded from file.

    Raises
    ------
    ValueError
        If required sections or values are missing or invalid.
    """

    if not isinstance(config, Mapping):
        raise ValueError("config must be a mapping")

    strat_cfg = config.get("strategy_config")
    if not isinstance(strat_cfg, Mapping):
        raise ValueError("strategy_config missing or invalid")

    name = strat_cfg.get("name")
    if not isinstance(name, str) or not name.strip():
        raise ValueError("strategy_config.name is required")

    params = strat_cfg.get("params")
    if not isinstance(params, Mapping):
        raise ValueError("strategy_config.params missing or invalid")

    for key in ("preload_candles_1h", "preload_candles_5m"):
        if key in strat_cfg:
            validate_non_negative_int(strat_cfg[key], f"strategy_config.{key}")


def validate_env_settings() -> None:
    """Ensure mandatory environment variables are defined.

    Raises
    ------
    EnvironmentError
        If any required variable is missing.
    """

    for var in MANDATORY_ENV_VARS:
        require_env(var)

