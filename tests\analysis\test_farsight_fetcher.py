import types
from datetime import datetime, timedelta, timezone

import pytest

from qualia.farsight.fetcher import PaperFetcher


class DummyResult:
    def __init__(self, published):
        self.published = published


class DummySearch:
    def __init__(self, results):
        self._results = results

    def results(self):
        return iter(self._results)


def _patch_arxiv(monkeypatch, results):
    dummy_module = types.SimpleNamespace(
        Search=lambda query, max_results, sort_by: DummySearch(results),
        SortCriterion=types.SimpleNamespace(SubmittedDate=""),
    )
    monkeypatch.setattr("qualia.farsight.fetcher.arxiv", dummy_module)


@pytest.mark.unit
def test_fetcher_handles_naive_and_aware_dates(monkeypatch):
    now = datetime.now(timezone.utc)
    results = [
        DummyResult((now - timedelta(hours=1)).replace(tzinfo=None)),
        DummyResult(now - timedelta(hours=2)),
        DummyR<PERSON>ult((now - timedelta(days=3)).replace(tzinfo=None)),
    ]
    _patch_arxiv(monkeypatch, results)
    fetcher = PaperFetcher(days_back=2)
    papers = fetcher.run()
    assert papers == results[:2]


@pytest.mark.unit
def test_fetcher_handles_search_errors(monkeypatch):
    class ErrorSearch:
        def results(self):
            raise RuntimeError("boom")

    dummy_module = types.SimpleNamespace(
        Search=lambda query, max_results, sort_by: ErrorSearch(),
        SortCriterion=types.SimpleNamespace(SubmittedDate=""),
    )
    monkeypatch.setattr("qualia.farsight.fetcher.arxiv", dummy_module)

    fetcher = PaperFetcher(days_back=1)
    papers = fetcher.run()
    assert papers == []


@pytest.mark.unit
def test_fetcher_arxiv_missing(monkeypatch, caplog):
    monkeypatch.setattr("qualia.farsight.fetcher.arxiv", None)
    fetcher = PaperFetcher(days_back=1)
    with caplog.at_level("WARNING"):
        papers = fetcher.run()
    assert papers == []
    assert "arxiv package missing" in caplog.text


@pytest.mark.unit
def test_fetcher_uses_custom_categories(monkeypatch):
    captured = {}

    def search(query, max_results, sort_by):
        captured["query"] = query
        return DummySearch([])

    dummy_module = types.SimpleNamespace(
        Search=search,
        SortCriterion=types.SimpleNamespace(SubmittedDate=""),
    )
    monkeypatch.setattr("qualia.farsight.fetcher.arxiv", dummy_module)

    fetcher = PaperFetcher(days_back=1, categories="stat.ML")
    fetcher.run()

    assert captured["query"] == "cat:stat.ML"


@pytest.mark.unit
def test_fetcher_env_variable(monkeypatch):
    captured = {}

    def search(query, max_results, sort_by):
        captured["query"] = query
        return DummySearch([])

    dummy_module = types.SimpleNamespace(
        Search=search,
        SortCriterion=types.SimpleNamespace(SubmittedDate=""),
    )
    monkeypatch.setattr("qualia.farsight.fetcher.arxiv", dummy_module)
    monkeypatch.setenv("FAR_CATEGORIES", "quant-ph")

    fetcher = PaperFetcher(days_back=1)
    fetcher.run()

    assert captured["query"] == "cat:quant-ph"
