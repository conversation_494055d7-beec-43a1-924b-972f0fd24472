import numpy as np
import pytest

pytest.importorskip("pytest_benchmark")

from qualia.strategies.quantum_trend_reversal import indicators
from qualia.core.resonance import ResonanceOperator
import pandas as pd
from qualia.strategies.adaptive_liquidity_manager import (
    AdaptiveLiquidityManager,
    LiquidityParameters,
)
from qualia.metrics.quantum_metrics import (
    _renyi_from_eigvals,
    _renyi_from_eigvals_naive,
)
from qualia.risk.manager import QUALIARiskManager


def _sma_naive(data: np.ndarray, period: int) -> np.ndarray:
    result = np.zeros_like(data, dtype=float)
    result[:period] = np.nan
    for i in range(period, len(data) + 1):
        result[i - 1] = np.mean(data[i - period : i])
    return result


def _harmonic_strength_naive(amplitudes: np.ndarray) -> float:
    if len(amplitudes) < 2:
        return 0.0
    golden_ratio = 1.************
    strength = 0.0
    count = 0
    for i in range(len(amplitudes) - 1):
        for j in range(i + 1, len(amplitudes)):
            if amplitudes[i] > 0 and amplitudes[j] > 0:
                ratio = amplitudes[j] / amplitudes[i]
                if abs(ratio - golden_ratio) < 0.1:
                    strength += 1.0
                elif abs(ratio - 1 / golden_ratio) < 0.1:
                    strength += 1.0
                elif abs(ratio - 2.0) < 0.1:
                    strength += 0.8
                elif abs(ratio - 0.5) < 0.1:
                    strength += 0.8
                count += 1
    return strength / max(count, 1)


@pytest.mark.benchmark(group="critical-loops")
def test_sma_vectorized(benchmark):
    data = np.random.rand(10000)
    benchmark(indicators.calculate_sma, data, 30)


@pytest.mark.benchmark(group="critical-loops")
def test_sma_naive(benchmark):
    data = np.random.rand(10000)
    benchmark(_sma_naive, data, 30)


@pytest.mark.benchmark(group="critical-loops")
def test_harmonic_strength_vectorized(benchmark):
    amps = np.abs(np.random.randn(512))
    operator = ResonanceOperator({})
    benchmark(operator._calculate_harmonic_strength, amps)


@pytest.mark.benchmark(group="critical-loops")
def test_harmonic_strength_naive(benchmark):
    amps = np.abs(np.random.randn(512))
    benchmark(_harmonic_strength_naive, amps)


@pytest.mark.benchmark(group="critical-loops")
def test_liquidity_backtest_vectorized(benchmark):
    params = LiquidityParameters(base_order_pct=0.2)
    manager = AdaptiveLiquidityManager(params=params)
    data = np.random.rand(2000)
    df = pd.DataFrame({"price": data + 100.0, "depth": 1.0, "slippage": 0.1})
    benchmark(manager.backtest, df, 1000.0)


@pytest.mark.benchmark(group="critical-loops")
def test_liquidity_backtest_loop(benchmark):
    params = LiquidityParameters(base_order_pct=0.2)
    manager = AdaptiveLiquidityManager(params=params)
    data = np.random.rand(2000)
    df = pd.DataFrame({"price": data + 100.0, "depth": 1.0, "slippage": 0.1})
    benchmark(manager._backtest_loop, df, 1000.0)


def _renyi_naive(eigvals: np.ndarray, alpha: float) -> float:
    total = 0.0
    for v in eigvals:
        total += v**alpha
    return float(1.0 / (1.0 - alpha) * np.log(total))


@pytest.mark.benchmark(group="critical-loops")
def test_renyi_vectorized(benchmark):
    eigvals = np.abs(np.random.rand(512))
    eigvals /= eigvals.sum()
    benchmark(_renyi_from_eigvals, eigvals, 2.0)


@pytest.mark.benchmark(group="critical-loops")
def test_renyi_naive(benchmark):
    eigvals = np.abs(np.random.rand(512))
    eigvals /= eigvals.sum()
    benchmark(_renyi_from_eigvals_naive, eigvals, 2.0)


@pytest.mark.benchmark(group="critical-loops")
def test_position_size_batch_vectorized(benchmark):
    manager = QUALIARiskManager(initial_capital=10000.0)
    points = np.abs(np.random.rand(1000)) + 0.01
    benchmark(
        manager.compute_position_size_batch,
        10000.0,
        1.0,
        points,
        1.0,
    )


@pytest.mark.benchmark(group="critical-loops")
def test_position_size_batch_loop(benchmark):
    manager = QUALIARiskManager(initial_capital=10000.0)
    points = np.abs(np.random.rand(1000)) + 0.01
    benchmark(
        manager.compute_position_size_batch_loop,
        10000.0,
        1.0,
        points,
        1.0,
    )
