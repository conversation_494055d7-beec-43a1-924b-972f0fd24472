"""Filesystem helpers used across QUALIA."""

from pathlib import Path
from typing import Optional, Union

PathLike = Union[str, Path]


def ensure_parent_dirs(
    log_file: Optional[PathLike], report_file: Optional[PathLike]
) -> None:
    """Create parent directories for log and report files if needed.

    Parameters
    ----------
    log_file : str or Path or None
        Path to the log file. When ``None``, no directory is created.
    report_file : str or Path or None
        Path to the report file. When ``None``, no directory is created.
    """

    if log_file:
        Path(log_file).resolve().parent.mkdir(parents=True, exist_ok=True)
    if report_file:
        Path(report_file).resolve().parent.mkdir(parents=True, exist_ok=True)
