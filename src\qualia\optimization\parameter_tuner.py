"""
QUALIA Parameter Tuner Worker
Worker que executa otimização Bayesiana a cada 500 ciclos
Integra com o sistema de trading para ajustes automáticos de parâmetros
"""

import asyncio
import json
import signal
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Set
from dataclasses import dataclass

from ..utils.logger import get_logger
from ..config.config_loader import ConfigLoader
from .bayesian_optimizer import BayesianOptimizer, OptimizationConfig
from .optimization_grpc_service import OptimizationGRPCService

logger = get_logger(__name__)

@dataclass
class TunerConfig:
    """Configuração do Parameter Tuner."""
    # Símbolos para otimização
    symbols: List[str] = None
    
    # Configuração de otimização
    optimization_interval_cycles: int = 500
    n_trials_per_cycle: int = 25
    max_concurrent_optimizations: int = 4
    
    # Configuração de performance
    performance_lookback_hours: int = 24
    min_trades_for_optimization: int = 10
    
    # Configuração gRPC
    grpc_port: int = 50051
    grpc_enabled: bool = True
    
    # Configuração de persistência
    state_save_interval_minutes: int = 30
    optimization_log_file: str = "data/optimization_log.jsonl"
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "SOLUSDT", "DOTUSDT", "LINKUSDT", "POLUSDT"]

class ParameterTuner:
    """
    Worker principal para otimização automática de parâmetros.
    
    Funcionalidades:
    - Monitora ciclos do sistema de trading
    - Executa otimização Bayesiana a cada N ciclos
    - Atualiza parâmetros em tempo real via gRPC
    - Persiste estado e histórico de otimizações
    """
    
    def __init__(self, config: TunerConfig = None):
        self.config = config or TunerConfig()
        self.config_loader = ConfigLoader()
        
        # Componentes principais
        self.optimizer = BayesianOptimizer(
            OptimizationConfig(
                n_trials_per_cycle=self.config.n_trials_per_cycle,
                optimization_interval_cycles=self.config.optimization_interval_cycles,
                lookback_hours=self.config.performance_lookback_hours
            )
        )
        
        self.grpc_service = None
        if self.config.grpc_enabled:
            self.grpc_service = OptimizationGRPCService(port=self.config.grpc_port)
        
        # Estado do worker
        self.is_running = False
        self.cycle_count = 0
        self.last_optimization_time = None
        self.last_state_save_time = datetime.now()
        
        # Controle de símbolos ativos
        self.active_symbols: Set[str] = set(self.config.symbols)
        self.symbol_performance: Dict[str, Dict] = {}
        
        # Configurar handlers de sinal
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("🔧 ParameterTuner inicializado")
        logger.info(f"📊 Símbolos: {len(self.active_symbols)}")
        logger.info(f"⚙️ Otimização a cada {self.config.optimization_interval_cycles} ciclos")
        logger.info(f"🔬 {self.config.n_trials_per_cycle} trials por ciclo")
    
    async def start(self):
        """Inicia o worker de otimização."""
        logger.info("🚀 Iniciando ParameterTuner...")
        
        self.is_running = True
        
        # Carregar estado anterior
        self.optimizer.load_optimization_state()
        
        # Iniciar serviço gRPC se habilitado
        if self.grpc_service:
            await self.grpc_service.start()
            logger.info(f"🌐 Serviço gRPC iniciado na porta {self.config.grpc_port}")
        
        # Loop principal
        try:
            await self._main_loop()
        except Exception as e:
            logger.error(f"❌ Erro no loop principal: {e}")
        finally:
            await self.stop()
    
    async def _main_loop(self):
        """Loop principal do worker."""
        logger.info("🔄 Iniciando loop principal do ParameterTuner")
        
        while self.is_running:
            try:
                # Incrementar contador de ciclos
                self.cycle_count += 1
                
                # Verificar se deve otimizar
                if self.optimizer.should_optimize():
                    await self._execute_optimization_cycle()
                
                # Salvar estado periodicamente
                await self._periodic_state_save()
                
                # Atualizar métricas de performance
                await self._update_performance_metrics()
                
                # Aguardar próximo ciclo (simular ciclo de trading)
                await asyncio.sleep(1.0)  # 1 segundo por ciclo
                
                # Log de progresso a cada 100 ciclos
                if self.cycle_count % 100 == 0:
                    logger.info(f"📈 Ciclo {self.cycle_count} - Próxima otimização em {self.config.optimization_interval_cycles - self.optimizer.cycle_counter} ciclos")
                
            except asyncio.CancelledError:
                logger.info("🛑 Loop principal cancelado")
                break
            except Exception as e:
                logger.error(f"❌ Erro no ciclo {self.cycle_count}: {e}")
                await asyncio.sleep(5.0)  # Pausa em caso de erro
    
    async def _execute_optimization_cycle(self):
        """Executa um ciclo completo de otimização."""
        start_time = datetime.now()
        logger.info(f"🔍 Iniciando ciclo de otimização {self.cycle_count}")
        
        try:
            # Filtrar símbolos elegíveis para otimização
            eligible_symbols = self._get_eligible_symbols()
            
            if not eligible_symbols:
                logger.warning("⚠️ Nenhum símbolo elegível para otimização")
                return
            
            # Executar otimização em lotes para controlar concorrência
            batch_size = self.config.max_concurrent_optimizations
            results = {}
            
            for i in range(0, len(eligible_symbols), batch_size):
                batch = eligible_symbols[i:i + batch_size]
                logger.info(f"🔬 Otimizando lote {i//batch_size + 1}: {batch}")
                
                batch_results = await self.optimizer.optimize_all_symbols(batch)
                results.update(batch_results)
                
                # Pausa entre lotes
                if i + batch_size < len(eligible_symbols):
                    await asyncio.sleep(2.0)
            
            # Processar resultados
            await self._process_optimization_results(results)
            
            # Atualizar timestamp
            self.last_optimization_time = start_time
            
            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"✅ Ciclo de otimização concluído em {duration:.1f}s")
            logger.info(f"📊 Símbolos otimizados: {len(results)}/{len(eligible_symbols)}")
            
        except Exception as e:
            logger.error(f"❌ Erro no ciclo de otimização: {e}")
    
    def _get_eligible_symbols(self) -> List[str]:
        """Retorna símbolos elegíveis para otimização."""
        eligible = []
        
        for symbol in self.active_symbols:
            # Verificar se tem dados suficientes
            perf_data = self.symbol_performance.get(symbol, {})
            trade_count = perf_data.get("trade_count", 0)
            
            if trade_count >= self.config.min_trades_for_optimization:
                eligible.append(symbol)
            else:
                logger.debug(f"📊 {symbol} não elegível: {trade_count} trades (mín: {self.config.min_trades_for_optimization})")
        
        return eligible
    
    async def _process_optimization_results(self, results: Dict[str, any]):
        """Processa resultados da otimização."""
        if not results:
            return
        
        # Log dos resultados
        for symbol, result in results.items():
            if result:
                logger.info(f"🎯 {symbol}: objetivo={result.objective_value:.4f}, "
                           f"params={result.parameters}")
                
                # Salvar no log de otimização
                await self._log_optimization_result(result)
                
                # Notificar via gRPC se disponível
                if self.grpc_service:
                    await self.grpc_service.notify_parameter_update(symbol, result.parameters)
        
        # Salvar estado atualizado
        self.optimizer.save_optimization_state()
    
    async def _log_optimization_result(self, result):
        """Salva resultado de otimização no log."""
        try:
            log_entry = {
                "timestamp": result.timestamp.isoformat(),
                "symbol": result.symbol,
                "trial_number": result.trial_number,
                "parameters": result.parameters,
                "objective_value": result.objective_value,
                "metrics": result.metrics,
                "duration_seconds": result.duration_seconds
            }
            
            # Criar diretório se não existir
            log_file = Path(self.config.optimization_log_file)
            log_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Append ao arquivo JSONL
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
                
        except Exception as e:
            logger.error(f"❌ Erro ao salvar log de otimização: {e}")
    
    async def _periodic_state_save(self):
        """Salva estado periodicamente."""
        now = datetime.now()
        if (now - self.last_state_save_time).total_seconds() >= self.config.state_save_interval_minutes * 60:
            self.optimizer.save_optimization_state()
            self.last_state_save_time = now
            logger.debug("💾 Estado salvo periodicamente")
    
    async def _update_performance_metrics(self):
        """Atualiza métricas de performance dos símbolos."""
        # Simular atualização de métricas
        # Em produção, isso consultaria métricas reais do sistema de trading
        for symbol in self.active_symbols:
            if symbol not in self.symbol_performance:
                self.symbol_performance[symbol] = {
                    "trade_count": 0,
                    "pnl_24h": 0.0,
                    "sharpe_ratio": 0.0,
                    "last_update": datetime.now()
                }
            
            # Simular incremento de trades
            self.symbol_performance[symbol]["trade_count"] += 1
            self.symbol_performance[symbol]["last_update"] = datetime.now()
    
    def get_current_parameters(self, symbol: str) -> Dict[str, float]:
        """Retorna parâmetros atuais para um símbolo."""
        return self.optimizer.get_current_parameters(symbol)
    
    def get_status(self) -> Dict:
        """Retorna status atual do tuner."""
        return {
            "is_running": self.is_running,
            "cycle_count": self.cycle_count,
            "last_optimization": self.last_optimization_time.isoformat() if self.last_optimization_time else None,
            "active_symbols": list(self.active_symbols),
            "optimization_stats": self.optimizer.get_optimization_stats(),
            "grpc_enabled": self.config.grpc_enabled,
            "grpc_port": self.config.grpc_port if self.config.grpc_enabled else None
        }
    
    def _signal_handler(self, signum, frame):
        """Handler para sinais de sistema."""
        logger.info(f"📡 Sinal {signum} recebido, iniciando shutdown graceful...")
        self.is_running = False
    
    async def stop(self):
        """Para o worker gracefully."""
        logger.info("🛑 Parando ParameterTuner...")
        
        self.is_running = False
        
        # Parar serviço gRPC
        if self.grpc_service:
            await self.grpc_service.stop()
        
        # Salvar estado final
        self.optimizer.save_optimization_state()
        
        # Cleanup do otimizador
        await self.optimizer.cleanup()
        
        logger.info("✅ ParameterTuner parado com sucesso")

# Função principal para execução standalone
async def main():
    """Função principal para execução do worker."""
    logger.info("🚀 Iniciando QUALIA Parameter Tuner Worker")
    
    # Carregar configuração
    config = TunerConfig()
    
    # Criar e iniciar tuner
    tuner = ParameterTuner(config)
    
    try:
        await tuner.start()
    except KeyboardInterrupt:
        logger.info("🛑 Interrompido pelo usuário")
    except Exception as e:
        logger.error(f"❌ Erro fatal: {e}")
    finally:
        await tuner.stop()

if __name__ == "__main__":
    asyncio.run(main())
