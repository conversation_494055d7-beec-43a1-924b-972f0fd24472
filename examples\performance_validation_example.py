"""
Example: Real-Time Performance Validation Integration with QUALIA Trading System.

This example demonstrates how to integrate the Real-Time Performance Validation
system with the existing QUALIA trading infrastructure.
"""

import asyncio
import json
from datetime import datetime, timedelta
from pathlib import Path

# QUALIA imports
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from qualia.monitoring.realtime_performance_validator import create_performance_validator
from qualia.monitoring.performance_validation_integration import create_performance_integration
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


class PerformanceValidationExample:
    """
    Example implementation of performance validation integration.
    """
    
    def __init__(self):
        self.validator = None
        self.integration = None
        self.is_running = False
    
    async def setup_performance_validation(self):
        """Setup performance validation system."""
        logger.info("🚀 Setting up Real-Time Performance Validation System...")
        
        # Create performance validator with optimized thresholds
        self.validator = create_performance_validator(
            min_sharpe_ratio=3.0,  # Target from optimization: 5.340
            max_drawdown_pct=15.0,  # Conservative threshold
            min_win_rate=45.0,  # Minimum acceptable win rate
            performance_window_hours=24  # 24-hour rolling window
        )
        
        # Create integration layer
        config_path = "config/performance_validation_config.json"
        self.integration = create_performance_integration(config_path=config_path)
        
        logger.info("✅ Performance validation system initialized")
    
    async def simulate_trading_session(self):
        """Simulate a trading session with performance validation."""
        logger.info("📊 Starting simulated trading session with performance validation...")
        
        # Simulate trading data
        trades = self._generate_sample_trades()
        
        # Process trades with performance validation
        for i, trade in enumerate(trades):
            logger.info(f"🔄 Processing trade {i+1}/{len(trades)}: {trade['symbol']} "
                       f"PnL: {trade['realized_pnl']:+.2f}")
            
            # Update validator with trade data
            self.validator.update_trade(trade)
            
            # Get current performance
            performance = self.validator.get_current_performance()
            
            # Log performance metrics
            if i % 5 == 0:  # Log every 5 trades
                logger.info(f"📈 Performance Update:")
                logger.info(f"   Sharpe Ratio: {performance.sharpe_ratio:.3f}")
                logger.info(f"   Total Return: {performance.total_return_pct:.2f}%")
                logger.info(f"   Win Rate: {performance.win_rate:.1f}%")
                logger.info(f"   Max Drawdown: {performance.max_drawdown_pct:.2f}%")
                logger.info(f"   Total Trades: {performance.total_trades}")
            
            # Simulate real-time delay
            await asyncio.sleep(0.1)
        
        logger.info("✅ Trading session simulation completed")
    
    def _generate_sample_trades(self):
        """Generate sample trading data for demonstration."""
        base_time = datetime.now()
        trades = []
        
        # Generate realistic trading scenario
        symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'SOL/USDT']
        
        for i in range(50):
            # Simulate varying performance (some good, some bad trades)
            if i < 20:
                # Initial good performance
                pnl_base = 100 if i % 3 != 0 else -30
            elif i < 35:
                # Performance degradation
                pnl_base = 50 if i % 2 == 0 else -80
            else:
                # Recovery phase
                pnl_base = 120 if i % 4 != 0 else -40
            
            # Add some randomness
            import random
            pnl_variation = random.uniform(-20, 20)
            realized_pnl = pnl_base + pnl_variation
            
            trade = {
                'symbol': symbols[i % len(symbols)],
                'side': 'buy' if realized_pnl > 0 else 'sell',
                'quantity': round(random.uniform(0.1, 1.0), 3),
                'entry_price': random.uniform(30000, 60000),
                'exit_price': 0,  # Will be calculated
                'realized_pnl': realized_pnl,
                'unrealized_pnl': random.uniform(-10, 10),
                'entry_time': base_time - timedelta(hours=2, minutes=i*2),
                'exit_time': base_time - timedelta(hours=1, minutes=i*2),
                'timestamp': base_time - timedelta(minutes=i*2)
            }
            
            # Calculate exit price based on PnL
            if trade['quantity'] > 0:
                trade['exit_price'] = trade['entry_price'] + (realized_pnl / trade['quantity'])
            
            trades.append(trade)
        
        return trades
    
    async def demonstrate_alert_system(self):
        """Demonstrate the alert system with poor performance scenario."""
        logger.info("🚨 Demonstrating alert system with poor performance scenario...")
        
        # Setup alert callback
        def alert_handler(alert):
            logger.warning(f"🚨 PERFORMANCE ALERT: {alert.severity} - {alert.message}")
            logger.info(f"💡 Suggested Action: {alert.suggested_action}")
        
        self.validator.add_alert_callback(alert_handler)
        
        # Generate poor performance trades
        poor_trades = []
        base_time = datetime.now()
        
        for i in range(10):
            trade = {
                'symbol': 'BTC/USDT',
                'realized_pnl': -150 - (i * 10),  # Increasing losses
                'timestamp': base_time - timedelta(minutes=i*5)
            }
            poor_trades.append(trade)
        
        # Process poor trades
        for trade in poor_trades:
            self.validator.update_trade(trade)
            
            # Force performance check
            performance = self.validator.get_current_performance(force_refresh=True)
            await self.validator._check_performance_alerts(performance)
            
            await asyncio.sleep(0.1)
        
        logger.info("✅ Alert system demonstration completed")
    
    async def demonstrate_backtest_comparison(self):
        """Demonstrate backtest vs live performance comparison."""
        logger.info("📊 Demonstrating backtest comparison...")
        
        # Get current performance
        performance = self.validator.get_current_performance()
        
        # Update backtest comparison
        self.validator._update_backtest_comparison(performance)
        
        comparison = self.validator.backtest_comparison
        
        logger.info("📈 Backtest vs Live Performance Comparison:")
        logger.info(f"   Target Sharpe (Backtest): {comparison.backtest_sharpe:.3f}")
        logger.info(f"   Live Sharpe: {comparison.live_sharpe:.3f}")
        logger.info(f"   Performance Deviation: {comparison.performance_deviation_pct:.1f}%")
        logger.info(f"   Is Underperforming: {comparison.is_underperforming}")
        
        if comparison.is_underperforming:
            logger.warning("⚠️  Live performance is significantly below backtest expectations")
        else:
            logger.info("✅ Live performance is within acceptable range of backtest")
    
    async def generate_performance_report(self):
        """Generate comprehensive performance report."""
        logger.info("📋 Generating comprehensive performance report...")
        
        summary = self.validator.get_performance_summary()
        
        logger.info("=" * 60)
        logger.info("📊 QUALIA PERFORMANCE VALIDATION REPORT")
        logger.info("=" * 60)
        
        # Current Performance
        perf = summary['current_performance']
        logger.info("🎯 Current Performance:")
        logger.info(f"   Sharpe Ratio: {perf['sharpe_ratio']:.3f}")
        logger.info(f"   Total Return: {perf['total_return_pct']:.2f}%")
        logger.info(f"   Max Drawdown: {perf['max_drawdown_pct']:.2f}%")
        logger.info(f"   Win Rate: {perf['win_rate']:.1f}%")
        logger.info(f"   Profit Factor: {perf['profit_factor']:.2f}")
        logger.info(f"   Total Trades: {perf['total_trades']}")
        logger.info(f"   Consecutive Losses: {perf['consecutive_losses']}")
        logger.info(f"   Current Capital: ${perf['current_capital']:,.2f}")
        
        # Backtest Comparison
        backtest = summary['backtest_comparison']
        logger.info("\n🎯 Backtest Comparison:")
        logger.info(f"   Target Sharpe: {backtest['target_sharpe']:.3f}")
        logger.info(f"   Live Sharpe: {backtest['live_sharpe']:.3f}")
        logger.info(f"   Deviation: {backtest['deviation_pct']:.1f}%")
        logger.info(f"   Underperforming: {backtest['is_underperforming']}")
        
        # Alerts Summary
        alerts = summary['alerts']
        logger.info(f"\n🚨 Alerts Summary:")
        logger.info(f"   Active Alerts: {alerts['active_alerts']}")
        logger.info(f"   Total Alerts: {alerts['total_alerts']}")
        
        if alerts['recent_alerts']:
            logger.info("   Recent Alerts:")
            for alert in alerts['recent_alerts']:
                logger.info(f"     - {alert['severity']}: {alert['type']} ({alert['timestamp']})")
        
        # Monitoring Status
        status = summary['monitoring_status']
        logger.info(f"\n⚙️  Monitoring Status:")
        logger.info(f"   Is Monitoring: {status['is_monitoring']}")
        logger.info(f"   Duration: {status['monitoring_duration_hours']:.1f} hours")
        logger.info(f"   Trades Tracked: {status['total_trades_tracked']}")
        logger.info(f"   Performance Snapshots: {status['performance_snapshots']}")
        
        logger.info("=" * 60)
    
    async def export_performance_data(self):
        """Export performance data for analysis."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        export_path = f"performance_validation_example_{timestamp}.json"
        
        self.validator.export_performance_data(export_path)
        logger.info(f"📁 Performance data exported to: {export_path}")
    
    async def run_complete_example(self):
        """Run the complete performance validation example."""
        try:
            # Setup
            await self.setup_performance_validation()
            
            # Start monitoring (in background)
            monitoring_task = asyncio.create_task(
                self.validator.start_monitoring(monitoring_interval=5.0)
            )
            
            # Wait a moment for monitoring to start
            await asyncio.sleep(1.0)
            
            # Run demonstrations
            await self.simulate_trading_session()
            await self.demonstrate_backtest_comparison()
            await self.demonstrate_alert_system()
            await self.generate_performance_report()
            await self.export_performance_data()
            
            # Stop monitoring
            self.validator.stop_monitoring()
            
            # Wait for monitoring task to complete
            try:
                await asyncio.wait_for(monitoring_task, timeout=2.0)
            except asyncio.TimeoutError:
                monitoring_task.cancel()
            
            logger.info("🎉 Performance validation example completed successfully!")
            
        except Exception as e:
            logger.error(f"❌ Error in performance validation example: {e}")
            raise


async def main():
    """Main function to run the performance validation example."""
    logger.info("🚀 Starting QUALIA Performance Validation Example")
    
    example = PerformanceValidationExample()
    await example.run_complete_example()


if __name__ == "__main__":
    asyncio.run(main())
