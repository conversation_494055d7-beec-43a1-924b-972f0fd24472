#!/usr/bin/env python3
"""
Teste simples para o BayesOpt Microservice - D-02 Implementation.
Testa funcionalidades básicas sem dependências complexas.
"""

import os
import sys
import tempfile
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from fastapi.testclient import TestClient
    from qualia.services.bayes_service import app
    from qualia.services.models import ServiceConfig
    from qualia.utils.logger import get_logger
    
    logger = get_logger(__name__)
    
    def test_basic_functionality():
        """Test basic microservice functionality."""
        print("🧪 TESTE SIMPLES BAYESOPT MICROSERVICE - D-02")
        print("=" * 60)
        
        # Create test client
        client = TestClient(app)
        
        # Test 1: Health endpoint
        print("\n📊 Testando health endpoint...")
        response = client.get("/health")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Status: {data.get('status', 'N/A')}")
            print(f"   Uptime: {data.get('uptime_seconds', 'N/A')}s")
            print("   ✅ Health endpoint funcionando")
        else:
            print(f"   ❌ Health endpoint falhou: {response.text}")
            return False
        
        # Test 2: Root endpoint
        print("\n📊 Testando root endpoint...")
        response = client.get("/")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Service: {data.get('service', 'N/A')}")
            print(f"   Version: {data.get('version', 'N/A')}")
            print("   ✅ Root endpoint funcionando")
        else:
            print(f"   ❌ Root endpoint falhou: {response.text}")
            return False
        
        # Test 3: Parameter suggestion
        print("\n📊 Testando suggest endpoint...")
        request_data = {
            "study_name": "test_study",
            "symbol": "BTCUSDT",
            "price_amp_range": [1.0, 10.0],
            "news_amp_range": [1.0, 15.0],
            "min_conf_range": [0.2, 0.8],
            "sampler_type": "TPE",
            "pruning_enabled": True,
            "n_startup_trials": 5
        }
        
        response = client.post("/suggest", json=request_data)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Study: {data.get('study_name', 'N/A')}")
            print(f"   Symbol: {data.get('symbol', 'N/A')}")
            print(f"   Trial ID: {data.get('trial_id', 'N/A')}")
            params = data.get('parameters', {})
            print(f"   Price Amp: {params.get('price_amplification', 'N/A'):.3f}")
            print(f"   News Amp: {params.get('news_amplification', 'N/A'):.3f}")
            print(f"   Min Conf: {params.get('min_confidence', 'N/A'):.3f}")
            print("   ✅ Suggest endpoint funcionando")
            
            # Test 4: Result reporting
            print("\n📊 Testando report endpoint...")
            report_data = {
                "study_name": "test_study",
                "symbol": "BTCUSDT",
                "trial_id": data["trial_id"],
                "objective_value": 1.5,
                "metrics": {
                    "sharpe_ratio": 2.0,
                    "pnl_24h": 1000.0,
                    "cost_ratio_pct": 50.0
                },
                "duration_seconds": 10.5,
                "success": True,
                "evaluation_type": "realistic"
            }
            
            response = client.post("/report", json=report_data)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                report_result = response.json()
                print(f"   Study: {report_result.get('study_name', 'N/A')}")
                print(f"   Trials: {report_result.get('n_trials', 'N/A')}")
                print(f"   Best Value: {report_result.get('best_value', 'N/A')}")
                print("   ✅ Report endpoint funcionando")
            else:
                print(f"   ❌ Report endpoint falhou: {response.text}")
                return False
                
        else:
            print(f"   ❌ Suggest endpoint falhou: {response.text}")
            return False
        
        # Test 5: Studies listing
        print("\n📊 Testando studies endpoint...")
        response = client.get("/studies")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Total Studies: {data.get('total_count', 'N/A')}")
            studies = data.get('studies', [])
            if studies:
                study = studies[0]
                print(f"   First Study: {study.get('study_name', 'N/A')}")
                print(f"   Trials: {study.get('n_trials', 'N/A')}")
            print("   ✅ Studies endpoint funcionando")
        else:
            print(f"   ❌ Studies endpoint falhou: {response.text}")
            return False
        
        print("\n🎉 TODOS OS TESTES BÁSICOS PASSARAM!")
        print("   • Health check: ✅")
        print("   • Root endpoint: ✅")
        print("   • Parameter suggestion: ✅")
        print("   • Result reporting: ✅")
        print("   • Studies listing: ✅")
        print("\n🚀 D-02 BayesOpt Microservice está funcionando!")
        
        return True
    
    if __name__ == "__main__":
        success = test_basic_functionality()
        sys.exit(0 if success else 1)
        
except Exception as e:
    print(f"❌ Erro crítico: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
