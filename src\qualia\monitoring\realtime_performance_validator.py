"""
Real-Time Performance Validation System for QUALIA.

This module provides comprehensive real-time performance monitoring and validation
to ensure optimized parameters (Sharpe 5.340) maintain their effectiveness in production.
"""

import asyncio
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from pathlib import Path
from collections import deque
import numpy as np
import pandas as pd

from ..utils.logger import get_logger
from ..metrics.performance_metrics import calculate_sharpe_ratio, calculate_max_drawdown, aggregate_trade_performance
from ..monitoring.performance_metrics import PerformanceMetricsCollector, MetricType

logger = get_logger(__name__)


@dataclass
class PerformanceThresholds:
    """Performance thresholds for validation."""
    min_sharpe_ratio: float = 3.0  # Minimum acceptable Sharpe ratio
    max_drawdown_pct: float = 15.0  # Maximum acceptable drawdown
    min_win_rate: float = 45.0  # Minimum win rate percentage
    min_profit_factor: float = 1.2  # Minimum profit factor
    max_consecutive_losses: int = 5  # Maximum consecutive losses
    performance_window_hours: int = 24  # Performance evaluation window
    alert_cooldown_minutes: int = 15  # Cooldown between alerts


@dataclass
class PerformanceSnapshot:
    """Snapshot of current performance metrics."""
    timestamp: datetime
    sharpe_ratio: float
    total_return_pct: float
    max_drawdown_pct: float
    win_rate: float
    profit_factor: float
    total_trades: int
    consecutive_losses: int
    avg_trade_duration_minutes: float
    current_capital: float
    unrealized_pnl: float
    realized_pnl: float


@dataclass
class PerformanceAlert:
    """Performance degradation alert."""
    alert_id: str
    timestamp: datetime
    alert_type: str
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL
    message: str
    current_value: float
    threshold_value: float
    suggested_action: str
    performance_snapshot: PerformanceSnapshot


@dataclass
class BacktestComparison:
    """Comparison between live performance and backtest results."""
    backtest_sharpe: float = 5.340  # Target Sharpe from optimization
    backtest_max_drawdown: float = 8.5  # Target max drawdown
    backtest_win_rate: float = 62.3  # Target win rate
    live_sharpe: float = 0.0
    live_max_drawdown: float = 0.0
    live_win_rate: float = 0.0
    performance_deviation_pct: float = 0.0
    is_underperforming: bool = False


class RealTimePerformanceValidator:
    """
    Real-time performance validation system for QUALIA trading.
    
    Features:
    - Continuous performance monitoring
    - Backtest vs live performance comparison
    - Automated alerts for performance degradation
    - Parameter adjustment recommendations
    - Performance trend analysis
    """
    
    def __init__(self, 
                 thresholds: Optional[PerformanceThresholds] = None,
                 metrics_collector: Optional[PerformanceMetricsCollector] = None):
        self.thresholds = thresholds or PerformanceThresholds()
        self.metrics_collector = metrics_collector or PerformanceMetricsCollector()
        
        # Performance tracking
        self.performance_history: deque = deque(maxlen=1000)
        self.trade_history: List[Dict[str, Any]] = []
        self.alert_history: List[PerformanceAlert] = []
        self.active_alerts: Dict[str, PerformanceAlert] = {}
        
        # Backtest comparison
        self.backtest_comparison = BacktestComparison()
        
        # State management
        self.is_monitoring = False
        self.last_alert_times: Dict[str, datetime] = {}
        self.monitoring_start_time = datetime.now()
        self.initial_capital = 10000.0
        self.current_capital = 10000.0
        
        # Callbacks
        self.alert_callbacks: List[Callable[[PerformanceAlert], None]] = []
        self.performance_callbacks: List[Callable[[PerformanceSnapshot], None]] = []
        
        # Performance cache
        self._last_performance_snapshot: Optional[PerformanceSnapshot] = None
        self._performance_cache_time = 0.0
        self._cache_duration = 30.0  # Cache for 30 seconds
        
        logger.info("[VALIDATOR] Real-Time Performance Validator initialized")
    
    async def start_monitoring(self, monitoring_interval: float = 60.0):
        """Start real-time performance monitoring."""
        self.is_monitoring = True
        self.monitoring_start_time = datetime.now()
        
        logger.info(f"[VALIDATOR] Starting real-time performance monitoring (interval: {monitoring_interval}s)")
        
        # Start monitoring loop
        monitoring_task = asyncio.create_task(self._monitoring_loop(monitoring_interval))
        
        # Start metrics collection
        metrics_task = asyncio.create_task(self._metrics_collection_loop())
        
        try:
            await asyncio.gather(monitoring_task, metrics_task)
        except asyncio.CancelledError:
            logger.info("[VALIDATOR] Performance monitoring stopped")
        except Exception as e:
            logger.error(f"[VALIDATOR] Error in monitoring: {e}")
            raise
    
    def stop_monitoring(self):
        """Stop performance monitoring."""
        self.is_monitoring = False
        logger.info("[VALIDATOR] Performance monitoring stopped")
    
    def update_trade(self, trade_data: Dict[str, Any]):
        """Update with new trade data."""
        # Add timestamp if not present
        if 'timestamp' not in trade_data:
            trade_data['timestamp'] = datetime.now()
        
        # Add to trade history
        self.trade_history.append(trade_data)
        
        # Update current capital
        if 'realized_pnl' in trade_data:
            self.current_capital += trade_data['realized_pnl']
        
        # Trigger immediate performance check for significant trades
        if abs(trade_data.get('realized_pnl', 0)) > self.current_capital * 0.02:  # 2% of capital
            asyncio.create_task(self._check_performance_immediately())
        
        logger.debug(f"[VALIDATOR] Trade updated: {trade_data.get('symbol', 'UNKNOWN')} "
                    f"PnL: {trade_data.get('realized_pnl', 0):.2f}")
    
    def update_position(self, position_data: Dict[str, Any]):
        """Update with current position data."""
        # Update unrealized PnL tracking
        unrealized_pnl = position_data.get('unrealized_pnl', 0)
        
        # Record unrealized PnL metric
        self.metrics_collector.record_metric(
            "trading.unrealized_pnl",
            unrealized_pnl,
            MetricType.GAUGE,
            "currency"
        )
    
    def get_current_performance(self, force_refresh: bool = False) -> PerformanceSnapshot:
        """Get current performance snapshot."""
        current_time = time.time()
        
        # Use cache if available and not expired
        if (not force_refresh and 
            self._last_performance_snapshot and 
            current_time - self._performance_cache_time < self._cache_duration):
            return self._last_performance_snapshot
        
        # Calculate fresh performance metrics
        snapshot = self._calculate_performance_snapshot()
        
        # Update cache
        self._last_performance_snapshot = snapshot
        self._performance_cache_time = current_time
        
        return snapshot
    
    def _calculate_performance_snapshot(self) -> PerformanceSnapshot:
        """Calculate current performance snapshot."""
        current_time = datetime.now()
        
        # Get recent trades for performance calculation
        recent_trades = self._get_recent_trades(hours=self.thresholds.performance_window_hours)
        
        if not recent_trades:
            return PerformanceSnapshot(
                timestamp=current_time,
                sharpe_ratio=0.0,
                total_return_pct=0.0,
                max_drawdown_pct=0.0,
                win_rate=0.0,
                profit_factor=0.0,
                total_trades=0,
                consecutive_losses=0,
                avg_trade_duration_minutes=0.0,
                current_capital=self.current_capital,
                unrealized_pnl=0.0,
                realized_pnl=0.0
            )
        
        # Calculate performance metrics
        performance_metrics = aggregate_trade_performance(recent_trades, self.initial_capital)
        
        # Calculate consecutive losses
        consecutive_losses = self._calculate_consecutive_losses(recent_trades)
        
        # Calculate average trade duration
        avg_duration = self._calculate_avg_trade_duration(recent_trades)
        
        # Get unrealized PnL from latest position data
        unrealized_pnl = sum(trade.get('unrealized_pnl', 0) for trade in recent_trades[-10:])
        
        return PerformanceSnapshot(
            timestamp=current_time,
            sharpe_ratio=performance_metrics.get('sharpe_ratio', 0.0),
            total_return_pct=performance_metrics.get('total_pnl_pct', 0.0),
            max_drawdown_pct=performance_metrics.get('max_drawdown_pct', 0.0),
            win_rate=performance_metrics.get('win_rate', 0.0),
            profit_factor=performance_metrics.get('profit_factor', 0.0),
            total_trades=performance_metrics.get('total_trades', 0),
            consecutive_losses=consecutive_losses,
            avg_trade_duration_minutes=avg_duration,
            current_capital=self.current_capital,
            unrealized_pnl=unrealized_pnl,
            realized_pnl=performance_metrics.get('total_pnl', 0.0)
        )
    
    def _get_recent_trades(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get trades from the last N hours."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        return [
            trade for trade in self.trade_history
            if trade.get('timestamp', datetime.min) >= cutoff_time
        ]
    
    def _calculate_consecutive_losses(self, trades: List[Dict[str, Any]]) -> int:
        """Calculate current consecutive losses."""
        consecutive = 0
        
        # Check from most recent trade backwards
        for trade in reversed(trades):
            pnl = trade.get('realized_pnl', 0)
            if pnl < 0:
                consecutive += 1
            else:
                break
        
        return consecutive
    
    def _calculate_avg_trade_duration(self, trades: List[Dict[str, Any]]) -> float:
        """Calculate average trade duration in minutes."""
        durations = []
        
        for trade in trades:
            entry_time = trade.get('entry_time')
            exit_time = trade.get('exit_time')
            
            if entry_time and exit_time:
                if isinstance(entry_time, str):
                    entry_time = datetime.fromisoformat(entry_time)
                if isinstance(exit_time, str):
                    exit_time = datetime.fromisoformat(exit_time)
                
                duration = (exit_time - entry_time).total_seconds() / 60.0
                durations.append(duration)
        
        return np.mean(durations) if durations else 0.0
    
    async def _monitoring_loop(self, interval: float):
        """Main monitoring loop."""
        while self.is_monitoring:
            try:
                # Get current performance
                performance = self.get_current_performance(force_refresh=True)
                
                # Add to history
                self.performance_history.append(performance)
                
                # Check for alerts
                await self._check_performance_alerts(performance)
                
                # Update backtest comparison
                self._update_backtest_comparison(performance)
                
                # Trigger performance callbacks
                for callback in self.performance_callbacks:
                    try:
                        callback(performance)
                    except Exception as e:
                        logger.warning(f"[VALIDATOR] Performance callback failed: {e}")
                
                # Record metrics
                self._record_performance_metrics(performance)
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"[VALIDATOR] Error in monitoring loop: {e}")
                await asyncio.sleep(interval)
    
    async def _metrics_collection_loop(self):
        """Metrics collection loop."""
        while self.is_monitoring:
            try:
                # Collect system metrics
                await self.metrics_collector.collect_system_metrics()
                
                # Record custom QUALIA metrics
                self.metrics_collector.record_metric(
                    "qualia.validator.active_alerts",
                    len(self.active_alerts),
                    MetricType.GAUGE,
                    "count"
                )
                
                self.metrics_collector.record_metric(
                    "qualia.validator.total_trades",
                    len(self.trade_history),
                    MetricType.COUNTER,
                    "count"
                )
                
                await asyncio.sleep(30.0)  # Collect metrics every 30 seconds

            except Exception as e:
                logger.error(f"[VALIDATOR] Error in metrics collection: {e}")
                await asyncio.sleep(30.0)

    async def _check_performance_alerts(self, performance: PerformanceSnapshot):
        """Check for performance alert conditions."""
        alerts_to_create = []
        current_time = datetime.now()

        # Check Sharpe ratio
        if performance.sharpe_ratio < self.thresholds.min_sharpe_ratio:
            if self._should_create_alert("sharpe_ratio_low", current_time):
                alerts_to_create.append(self._create_alert(
                    "sharpe_ratio_low",
                    "HIGH",
                    f"Sharpe ratio {performance.sharpe_ratio:.3f} below threshold {self.thresholds.min_sharpe_ratio}",
                    performance.sharpe_ratio,
                    self.thresholds.min_sharpe_ratio,
                    "Consider adjusting risk parameters or strategy weights",
                    performance
                ))

        # Check drawdown
        if performance.max_drawdown_pct > self.thresholds.max_drawdown_pct:
            if self._should_create_alert("drawdown_high", current_time):
                alerts_to_create.append(self._create_alert(
                    "drawdown_high",
                    "CRITICAL",
                    f"Max drawdown {performance.max_drawdown_pct:.2f}% exceeds threshold {self.thresholds.max_drawdown_pct}%",
                    performance.max_drawdown_pct,
                    self.thresholds.max_drawdown_pct,
                    "Reduce position sizes and implement stricter stop-losses",
                    performance
                ))

        # Check win rate
        if performance.win_rate < self.thresholds.min_win_rate and performance.total_trades >= 10:
            if self._should_create_alert("win_rate_low", current_time):
                alerts_to_create.append(self._create_alert(
                    "win_rate_low",
                    "MEDIUM",
                    f"Win rate {performance.win_rate:.1f}% below threshold {self.thresholds.min_win_rate}%",
                    performance.win_rate,
                    self.thresholds.min_win_rate,
                    "Review signal generation parameters and entry criteria",
                    performance
                ))

        # Check consecutive losses
        if performance.consecutive_losses >= self.thresholds.max_consecutive_losses:
            if self._should_create_alert("consecutive_losses", current_time):
                alerts_to_create.append(self._create_alert(
                    "consecutive_losses",
                    "HIGH",
                    f"Consecutive losses {performance.consecutive_losses} exceeds threshold {self.thresholds.max_consecutive_losses}",
                    performance.consecutive_losses,
                    self.thresholds.max_consecutive_losses,
                    "Consider pausing trading or reducing position sizes",
                    performance
                ))

        # Check profit factor
        if performance.profit_factor < self.thresholds.min_profit_factor and performance.total_trades >= 5:
            if self._should_create_alert("profit_factor_low", current_time):
                alerts_to_create.append(self._create_alert(
                    "profit_factor_low",
                    "MEDIUM",
                    f"Profit factor {performance.profit_factor:.2f} below threshold {self.thresholds.min_profit_factor}",
                    performance.profit_factor,
                    self.thresholds.min_profit_factor,
                    "Optimize take-profit and stop-loss levels",
                    performance
                ))

        # Create and process alerts
        for alert in alerts_to_create:
            await self._process_alert(alert)

    def _should_create_alert(self, alert_type: str, current_time: datetime) -> bool:
        """Check if alert should be created based on cooldown."""
        last_alert_time = self.last_alert_times.get(alert_type)

        if last_alert_time is None:
            return True

        time_since_last = current_time - last_alert_time
        cooldown = timedelta(minutes=self.thresholds.alert_cooldown_minutes)

        return time_since_last >= cooldown

    def _create_alert(self, alert_type: str, severity: str, message: str,
                     current_value: float, threshold_value: float,
                     suggested_action: str, performance: PerformanceSnapshot) -> PerformanceAlert:
        """Create a performance alert."""
        alert_id = f"{alert_type}_{int(time.time())}"

        return PerformanceAlert(
            alert_id=alert_id,
            timestamp=datetime.now(),
            alert_type=alert_type,
            severity=severity,
            message=message,
            current_value=current_value,
            threshold_value=threshold_value,
            suggested_action=suggested_action,
            performance_snapshot=performance
        )

    async def _process_alert(self, alert: PerformanceAlert):
        """Process and handle a performance alert."""
        # Add to active alerts
        self.active_alerts[alert.alert_id] = alert

        # Add to history
        self.alert_history.append(alert)

        # Update last alert time
        self.last_alert_times[alert.alert_type] = alert.timestamp

        # Log alert
        logger.warning(f"[VALIDATOR] ALERT [{alert.severity}] {alert.alert_type}: {alert.message}")
        logger.info(f"[VALIDATOR] Suggested action: {alert.suggested_action}")

        # Trigger alert callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.warning(f"[VALIDATOR] Alert callback failed: {e}")

        # Record alert metric
        self.metrics_collector.record_metric(
            f"qualia.validator.alert.{alert.alert_type}",
            1.0,
            MetricType.COUNTER,
            "count",
            tags={"severity": alert.severity}
        )

    def _update_backtest_comparison(self, performance: PerformanceSnapshot):
        """Update comparison with backtest results."""
        self.backtest_comparison.live_sharpe = performance.sharpe_ratio
        self.backtest_comparison.live_max_drawdown = performance.max_drawdown_pct
        self.backtest_comparison.live_win_rate = performance.win_rate

        # Calculate performance deviation
        sharpe_deviation = abs(performance.sharpe_ratio - self.backtest_comparison.backtest_sharpe)
        sharpe_deviation_pct = (sharpe_deviation / self.backtest_comparison.backtest_sharpe) * 100

        self.backtest_comparison.performance_deviation_pct = sharpe_deviation_pct

        # Check if underperforming (more than 30% deviation from backtest)
        self.backtest_comparison.is_underperforming = sharpe_deviation_pct > 30.0

        # Record comparison metrics
        self.metrics_collector.record_metric(
            "qualia.validator.backtest_deviation_pct",
            sharpe_deviation_pct,
            MetricType.GAUGE,
            "percent"
        )

    def _record_performance_metrics(self, performance: PerformanceSnapshot):
        """Record performance metrics."""
        # Core performance metrics
        self.metrics_collector.record_metric(
            "qualia.trading.sharpe_ratio",
            performance.sharpe_ratio,
            MetricType.GAUGE,
            "ratio"
        )

        self.metrics_collector.record_metric(
            "qualia.trading.total_return_pct",
            performance.total_return_pct,
            MetricType.GAUGE,
            "percent"
        )

        self.metrics_collector.record_metric(
            "qualia.trading.max_drawdown_pct",
            performance.max_drawdown_pct,
            MetricType.GAUGE,
            "percent"
        )

        self.metrics_collector.record_metric(
            "qualia.trading.win_rate",
            performance.win_rate,
            MetricType.GAUGE,
            "percent"
        )

        self.metrics_collector.record_metric(
            "qualia.trading.profit_factor",
            performance.profit_factor,
            MetricType.GAUGE,
            "ratio"
        )

        self.metrics_collector.record_metric(
            "qualia.trading.consecutive_losses",
            performance.consecutive_losses,
            MetricType.GAUGE,
            "count"
        )

        self.metrics_collector.record_metric(
            "qualia.trading.current_capital",
            performance.current_capital,
            MetricType.GAUGE,
            "currency"
        )

    async def _check_performance_immediately(self):
        """Immediate performance check for significant events."""
        try:
            performance = self.get_current_performance(force_refresh=True)
            await self._check_performance_alerts(performance)
        except Exception as e:
            logger.error(f"[VALIDATOR] Error in immediate performance check: {e}")

    def add_alert_callback(self, callback: Callable[[PerformanceAlert], None]):
        """Add callback for performance alerts."""
        self.alert_callbacks.append(callback)

    def add_performance_callback(self, callback: Callable[[PerformanceSnapshot], None]):
        """Add callback for performance updates."""
        self.performance_callbacks.append(callback)

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        current_performance = self.get_current_performance()

        return {
            "current_performance": {
                "sharpe_ratio": current_performance.sharpe_ratio,
                "total_return_pct": current_performance.total_return_pct,
                "max_drawdown_pct": current_performance.max_drawdown_pct,
                "win_rate": current_performance.win_rate,
                "profit_factor": current_performance.profit_factor,
                "total_trades": current_performance.total_trades,
                "consecutive_losses": current_performance.consecutive_losses,
                "current_capital": current_performance.current_capital
            },
            "backtest_comparison": {
                "target_sharpe": self.backtest_comparison.backtest_sharpe,
                "live_sharpe": self.backtest_comparison.live_sharpe,
                "deviation_pct": self.backtest_comparison.performance_deviation_pct,
                "is_underperforming": self.backtest_comparison.is_underperforming
            },
            "alerts": {
                "active_alerts": len(self.active_alerts),
                "total_alerts": len(self.alert_history),
                "recent_alerts": [
                    {
                        "type": alert.alert_type,
                        "severity": alert.severity,
                        "message": alert.message,
                        "timestamp": alert.timestamp.isoformat()
                    }
                    for alert in self.alert_history[-5:]  # Last 5 alerts
                ]
            },
            "monitoring_status": {
                "is_monitoring": self.is_monitoring,
                "monitoring_duration_hours": (datetime.now() - self.monitoring_start_time).total_seconds() / 3600,
                "total_trades_tracked": len(self.trade_history),
                "performance_snapshots": len(self.performance_history)
            }
        }

    def export_performance_data(self, filepath: str):
        """Export performance data to JSON file."""
        data = {
            "performance_history": [
                {
                    "timestamp": p.timestamp.isoformat(),
                    "sharpe_ratio": p.sharpe_ratio,
                    "total_return_pct": p.total_return_pct,
                    "max_drawdown_pct": p.max_drawdown_pct,
                    "win_rate": p.win_rate,
                    "profit_factor": p.profit_factor,
                    "total_trades": p.total_trades,
                    "consecutive_losses": p.consecutive_losses,
                    "current_capital": p.current_capital
                }
                for p in self.performance_history
            ],
            "alert_history": [
                {
                    "alert_id": alert.alert_id,
                    "timestamp": alert.timestamp.isoformat(),
                    "alert_type": alert.alert_type,
                    "severity": alert.severity,
                    "message": alert.message,
                    "current_value": alert.current_value,
                    "threshold_value": alert.threshold_value,
                    "suggested_action": alert.suggested_action
                }
                for alert in self.alert_history
            ],
            "summary": self.get_performance_summary()
        }

        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)

        logger.info(f"[VALIDATOR] Performance data exported to {filepath}")


# Factory function for easy integration
def create_performance_validator(
    min_sharpe_ratio: float = 3.0,
    max_drawdown_pct: float = 15.0,
    min_win_rate: float = 45.0,
    performance_window_hours: int = 24
) -> RealTimePerformanceValidator:
    """Create a configured performance validator."""
    thresholds = PerformanceThresholds(
        min_sharpe_ratio=min_sharpe_ratio,
        max_drawdown_pct=max_drawdown_pct,
        min_win_rate=min_win_rate,
        performance_window_hours=performance_window_hours
    )

    return RealTimePerformanceValidator(thresholds=thresholds)
