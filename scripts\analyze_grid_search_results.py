#!/usr/bin/env python3
"""
Análise de Resultados do Grid Search - Etapa C

YAA IMPLEMENTATION: Script para análise detalhada dos resultados do grid search
de hiperparâmetros, incluindo visualizações, correlações e recomendações.

Funcionalidades:
- Carregamento e análise de resultados JSON/CSV
- Visualizações interativas (heatmaps, scatter plots, box plots)
- Análise de correlações entre parâmetros e métricas
- Identificação de padrões e insights
- Recomendações de configurações ótimas
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

# Adiciona o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.utils.logger import get_logger

logger = get_logger(__name__)

# Configurações de visualização
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


class GridSearchAnalyzer:
    """Analisador de resultados do grid search."""
    
    def __init__(self, results_path: str):
        """Inicializa o analisador."""
        self.results_path = Path(results_path)
        self.results_data = None
        self.df = None
        
        self._load_results()
    
    def _load_results(self):
        """Carrega resultados do arquivo."""
        if not self.results_path.exists():
            raise FileNotFoundError(f"Arquivo de resultados não encontrado: {self.results_path}")
        
        if self.results_path.suffix == '.json':
            with open(self.results_path, 'r', encoding='utf-8') as f:
                self.results_data = json.load(f)
            
            # Converte para DataFrame
            self.df = pd.DataFrame(self.results_data['all_results'])
            
        elif self.results_path.suffix == '.csv':
            self.df = pd.read_csv(self.results_path)
            
        else:
            raise ValueError(f"Formato de arquivo não suportado: {self.results_path.suffix}")
        
        logger.info(f"Carregados {len(self.df)} resultados de {self.results_path}")
    
    def print_summary(self):
        """Imprime resumo dos resultados."""
        print("📊 RESUMO DOS RESULTADOS DO GRID SEARCH")
        print("=" * 60)
        
        if self.results_data:
            summary = self.results_data['summary']
            print(f"Total de combinações: {summary['total_combinations']}")
            print(f"Backtests bem-sucedidos: {summary['successful_backtests']}")
            print(f"Backtests falharam: {summary['failed_backtests']}")
            print(f"Tempo total: {summary.get('total_execution_time', 0):.1f}s")
            print()
        
        print("📈 ESTATÍSTICAS DAS MÉTRICAS:")
        print("-" * 40)
        
        metrics = ['sharpe_ratio', 'total_return_pct', 'max_drawdown_pct', 'win_rate', 'total_trades']
        
        for metric in metrics:
            if metric in self.df.columns:
                values = self.df[metric]
                print(f"{metric.replace('_', ' ').title()}:")
                print(f"  Média: {values.mean():.3f}")
                print(f"  Mediana: {values.median():.3f}")
                print(f"  Desvio: {values.std():.3f}")
                print(f"  Min: {values.min():.3f}")
                print(f"  Max: {values.max():.3f}")
                print()
    
    def analyze_correlations(self) -> pd.DataFrame:
        """Analisa correlações entre parâmetros e métricas."""
        print("🔗 ANÁLISE DE CORRELAÇÕES")
        print("=" * 40)
        
        # Seleciona colunas numéricas
        numeric_cols = self.df.select_dtypes(include=[np.number]).columns
        
        # Calcula matriz de correlação
        corr_matrix = self.df[numeric_cols].corr()
        
        # Foca nas correlações dos hiperparâmetros com métricas
        hyperparams = ['price_amplification', 'news_amplification', 'min_confidence']
        metrics = ['sharpe_ratio', 'total_return_pct', 'max_drawdown_pct', 'win_rate']
        
        print("Correlações dos Hiperparâmetros com Métricas:")
        print("-" * 50)
        
        for param in hyperparams:
            if param in corr_matrix.columns:
                print(f"\n{param.replace('_', ' ').title()}:")
                for metric in metrics:
                    if metric in corr_matrix.columns:
                        corr = corr_matrix.loc[param, metric]
                        strength = self._correlation_strength(abs(corr))
                        direction = "positiva" if corr > 0 else "negativa"
                        print(f"  {metric}: {corr:.3f} ({strength}, {direction})")
        
        return corr_matrix
    
    def _correlation_strength(self, corr: float) -> str:
        """Classifica força da correlação."""
        if corr >= 0.7:
            return "muito forte"
        elif corr >= 0.5:
            return "forte"
        elif corr >= 0.3:
            return "moderada"
        elif corr >= 0.1:
            return "fraca"
        else:
            return "muito fraca"
    
    def find_best_configurations(self, top_n: int = 5) -> Dict[str, List[Dict]]:
        """Encontra as melhores configurações por diferentes critérios."""
        print(f"🏆 TOP {top_n} CONFIGURAÇÕES")
        print("=" * 40)
        
        best_configs = {}
        
        # Melhores por Sharpe Ratio
        best_sharpe = self.df.nlargest(top_n, 'sharpe_ratio')
        best_configs['sharpe_ratio'] = best_sharpe.to_dict('records')
        
        print("🎯 Melhores por Sharpe Ratio:")
        for i, row in best_sharpe.iterrows():
            print(f"  {row.name + 1}. Sharpe: {row['sharpe_ratio']:.3f} | "
                  f"Price: {row['price_amplification']:.1f} | "
                  f"News: {row['news_amplification']:.1f} | "
                  f"Conf: {row['min_confidence']:.2f} | "
                  f"Return: {row['total_return_pct']:.2%}")
        
        # Melhores por Retorno
        best_return = self.df.nlargest(top_n, 'total_return_pct')
        best_configs['total_return'] = best_return.to_dict('records')
        
        print(f"\n💰 Melhores por Retorno:")
        for i, row in best_return.iterrows():
            print(f"  {row.name + 1}. Return: {row['total_return_pct']:.2%} | "
                  f"Price: {row['price_amplification']:.1f} | "
                  f"News: {row['news_amplification']:.1f} | "
                  f"Conf: {row['min_confidence']:.2f} | "
                  f"Sharpe: {row['sharpe_ratio']:.3f}")
        
        # Melhores por menor Drawdown
        best_drawdown = self.df.nsmallest(top_n, 'max_drawdown_pct')
        best_configs['min_drawdown'] = best_drawdown.to_dict('records')
        
        print(f"\n🛡️  Melhores por Menor Drawdown:")
        for i, row in best_drawdown.iterrows():
            print(f"  {row.name + 1}. Drawdown: {row['max_drawdown_pct']:.2%} | "
                  f"Price: {row['price_amplification']:.1f} | "
                  f"News: {row['news_amplification']:.1f} | "
                  f"Conf: {row['min_confidence']:.2f} | "
                  f"Return: {row['total_return_pct']:.2%}")
        
        return best_configs
    
    def create_visualizations(self, output_dir: str = "results/analysis"):
        """Cria visualizações dos resultados."""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print(f"📊 Gerando visualizações em {output_path}")
        
        # 1. Heatmap: Sharpe Ratio por Price vs News Amplification
        self._create_heatmap(output_path)
        
        # 2. Scatter: Return vs Drawdown colorido por Confidence
        self._create_scatter_plot(output_path)
        
        # 3. Box plots: Distribuição de métricas por Min Confidence
        self._create_box_plots(output_path)
        
        # 4. Matriz de correlação
        self._create_correlation_matrix(output_path)
        
        # 5. Distribuições das métricas
        self._create_distributions(output_path)
        
        print("✅ Visualizações criadas com sucesso!")
    
    def _create_heatmap(self, output_path: Path):
        """Cria heatmap de Sharpe Ratio."""
        plt.figure(figsize=(12, 8))
        
        # Cria pivot table
        pivot = self.df.pivot_table(
            values='sharpe_ratio',
            index='news_amplification',
            columns='price_amplification',
            aggfunc='mean'
        )
        
        # Cria heatmap
        sns.heatmap(
            pivot,
            annot=True,
            fmt='.2f',
            cmap='RdYlGn',
            center=0,
            cbar_kws={'label': 'Sharpe Ratio'}
        )
        
        plt.title('Sharpe Ratio por Amplificação de Preço vs Notícias')
        plt.xlabel('Price Amplification')
        plt.ylabel('News Amplification')
        plt.tight_layout()
        
        plt.savefig(output_path / 'sharpe_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_scatter_plot(self, output_path: Path):
        """Cria scatter plot Return vs Drawdown."""
        plt.figure(figsize=(12, 8))
        
        scatter = plt.scatter(
            self.df['total_return_pct'],
            self.df['max_drawdown_pct'],
            c=self.df['min_confidence'],
            cmap='viridis',
            alpha=0.7,
            s=60
        )
        
        plt.colorbar(scatter, label='Min Confidence')
        plt.xlabel('Total Return (%)')
        plt.ylabel('Max Drawdown (%)')
        plt.title('Retorno vs Drawdown (colorido por Min Confidence)')
        
        # Adiciona linha de referência (Calmar Ratio = 1)
        x_range = plt.xlim()
        plt.plot(x_range, x_range, 'r--', alpha=0.5, label='Calmar Ratio = 1')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig(output_path / 'return_vs_drawdown.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_box_plots(self, output_path: Path):
        """Cria box plots por Min Confidence."""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        metrics = ['sharpe_ratio', 'total_return_pct', 'max_drawdown_pct', 'win_rate']
        titles = ['Sharpe Ratio', 'Total Return (%)', 'Max Drawdown (%)', 'Win Rate']
        
        for i, (metric, title) in enumerate(zip(metrics, titles)):
            ax = axes[i // 2, i % 2]
            
            # Agrupa por min_confidence (arredondado para facilitar visualização)
            self.df['conf_rounded'] = self.df['min_confidence'].round(2)
            
            sns.boxplot(
                data=self.df,
                x='conf_rounded',
                y=metric,
                ax=ax
            )
            
            ax.set_title(f'Distribuição de {title} por Min Confidence')
            ax.set_xlabel('Min Confidence')
            ax.set_ylabel(title)
            ax.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(output_path / 'distributions_by_confidence.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_correlation_matrix(self, output_path: Path):
        """Cria matriz de correlação."""
        plt.figure(figsize=(12, 10))
        
        # Seleciona colunas numéricas relevantes
        relevant_cols = [
            'price_amplification', 'news_amplification', 'min_confidence',
            'sharpe_ratio', 'total_return_pct', 'max_drawdown_pct', 'win_rate',
            'total_trades', 'profit_factor', 'volatility'
        ]
        
        available_cols = [col for col in relevant_cols if col in self.df.columns]
        corr_matrix = self.df[available_cols].corr()
        
        # Cria heatmap
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        sns.heatmap(
            corr_matrix,
            mask=mask,
            annot=True,
            fmt='.2f',
            cmap='RdBu_r',
            center=0,
            square=True,
            cbar_kws={'label': 'Correlação'}
        )
        
        plt.title('Matriz de Correlação - Hiperparâmetros vs Métricas')
        plt.tight_layout()
        
        plt.savefig(output_path / 'correlation_matrix.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_distributions(self, output_path: Path):
        """Cria histogramas das principais métricas."""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        metrics = ['sharpe_ratio', 'total_return_pct', 'max_drawdown_pct', 'win_rate']
        titles = ['Sharpe Ratio', 'Total Return (%)', 'Max Drawdown (%)', 'Win Rate']
        
        for i, (metric, title) in enumerate(zip(metrics, titles)):
            ax = axes[i // 2, i % 2]
            
            # Histograma
            self.df[metric].hist(bins=30, alpha=0.7, ax=ax)
            
            # Linha da média
            mean_val = self.df[metric].mean()
            ax.axvline(mean_val, color='red', linestyle='--', label=f'Média: {mean_val:.3f}')
            
            ax.set_title(f'Distribuição de {title}')
            ax.set_xlabel(title)
            ax.set_ylabel('Frequência')
            ax.legend()
        
        plt.tight_layout()
        plt.savefig(output_path / 'metric_distributions.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_recommendations(self) -> Dict[str, Dict]:
        """Gera recomendações baseadas na análise."""
        print("💡 RECOMENDAÇÕES")
        print("=" * 40)
        
        recommendations = {}
        
        # Análise por quartis
        sharpe_q75 = self.df['sharpe_ratio'].quantile(0.75)
        return_q75 = self.df['total_return_pct'].quantile(0.75)
        drawdown_q25 = self.df['max_drawdown_pct'].quantile(0.25)
        
        # Configurações no top quartil
        top_performers = self.df[
            (self.df['sharpe_ratio'] >= sharpe_q75) &
            (self.df['total_return_pct'] >= return_q75) &
            (self.df['max_drawdown_pct'] <= drawdown_q25)
        ]
        
        if len(top_performers) > 0:
            # Recomendação conservadora (mediana dos top performers)
            conservative = {
                'price_amplification': top_performers['price_amplification'].median(),
                'news_amplification': top_performers['news_amplification'].median(),
                'min_confidence': top_performers['min_confidence'].median(),
                'expected_sharpe': top_performers['sharpe_ratio'].median(),
                'expected_return': top_performers['total_return_pct'].median(),
                'expected_drawdown': top_performers['max_drawdown_pct'].median()
            }
            
            recommendations['conservative'] = conservative
            
            print("🛡️  Configuração Conservadora (mediana dos top performers):")
            print(f"   Price Amplification: {conservative['price_amplification']:.1f}")
            print(f"   News Amplification: {conservative['news_amplification']:.1f}")
            print(f"   Min Confidence: {conservative['min_confidence']:.2f}")
            print(f"   Sharpe esperado: {conservative['expected_sharpe']:.3f}")
            print(f"   Retorno esperado: {conservative['expected_return']:.2%}")
            print(f"   Drawdown esperado: {conservative['expected_drawdown']:.2%}")
            print()
        
        # Melhor configuração geral (maior Sharpe)
        best_overall = self.df.loc[self.df['sharpe_ratio'].idxmax()]
        
        aggressive = {
            'price_amplification': best_overall['price_amplification'],
            'news_amplification': best_overall['news_amplification'],
            'min_confidence': best_overall['min_confidence'],
            'expected_sharpe': best_overall['sharpe_ratio'],
            'expected_return': best_overall['total_return_pct'],
            'expected_drawdown': best_overall['max_drawdown_pct']
        }
        
        recommendations['aggressive'] = aggressive
        
        print("🚀 Configuração Agressiva (melhor Sharpe):")
        print(f"   Price Amplification: {aggressive['price_amplification']:.1f}")
        print(f"   News Amplification: {aggressive['news_amplification']:.1f}")
        print(f"   Min Confidence: {aggressive['min_confidence']:.2f}")
        print(f"   Sharpe: {aggressive['expected_sharpe']:.3f}")
        print(f"   Retorno: {aggressive['expected_return']:.2%}")
        print(f"   Drawdown: {aggressive['expected_drawdown']:.2%}")
        
        return recommendations


def main():
    """Função principal."""
    if len(sys.argv) < 2:
        print("Uso: python analyze_grid_search_results.py <caminho_para_resultados>")
        print("Exemplo: python analyze_grid_search_results.py results/grid_search_results_20241205_143022.json")
        return
    
    results_path = sys.argv[1]
    
    try:
        # Inicializa analisador
        analyzer = GridSearchAnalyzer(results_path)
        
        # Executa análises
        analyzer.print_summary()
        print("\n" + "="*80 + "\n")
        
        analyzer.analyze_correlations()
        print("\n" + "="*80 + "\n")
        
        analyzer.find_best_configurations()
        print("\n" + "="*80 + "\n")
        
        analyzer.generate_recommendations()
        print("\n" + "="*80 + "\n")
        
        # Gera visualizações
        output_dir = f"results/analysis_{Path(results_path).stem}"
        analyzer.create_visualizations(output_dir)
        
        print(f"✅ Análise completa! Visualizações salvas em: {output_dir}")
        
    except Exception as e:
        logger.error(f"Erro na análise: {e}")
        print(f"❌ Erro: {e}")


if __name__ == "__main__":
    main()
