# Double-Check de Integração do módulo `src/qualia/risk_management`

Este documento avalia como o pacote `risk_management` se integra ao ecossistema QUALIA e identifica melhorias de curto e longo prazo.

## Integração Sistêmica
- **Event-driven**: os risk managers agora emitem `risk.manager_created` e `risk.recalibrated` pelo `Qualia Event Bus`, facilitando a reação de componentes externos.
- **Configuração**: `SimpleRiskManager` e `AdvancedRiskManager` suportam ajuste via env var `QUALIA_RISK_HISTORY_LIMIT`, mas não há YAML dedicado nem feature flags para rollout progressivo.
- **Observabilidade**: uso de `get_logger` garante logs estruturados; contudo não há tracing OpenTelemetry nem métricas expostas.
- **Segurança**: o módulo não manipula chaves de API diretamente. Faltam verificações de rate limit e políticas de retry quando acoplado às exchanges.

## Performance & Escalabilidade
- **Benchmark**: não existem testes de `pytest-benchmark` para os métodos de sizing e atualização de capital.
- **Paralelismo**: implementação inteiramente síncrona e CPU‑bound. Não há uso de `asyncio` ou vetorização para cálculos.
- **GPU/QPU Hooks**: ausência de interfaces para off‑load de cálculos pesados.

## Riscos Identificados
| Gravidade | Esforço | Descrição |
|-----------|--------|-----------|
| Alta | Médio | Falta de integração com o Event Bus reduz observabilidade e reatividade. |
| Média | Baixo | Ausência de feature flags dificulta rollout controlado de novas políticas. |
| Média | Médio | Sem benchmarks, regressões de latência podem passar despercebidas. |
| Baixa | Baixo | Metrificação limitada impede análise aprofundada de drawdown e PnL. |

## Quick Wins ⚡
- [x] #20 Publicar evento `risk.update` a cada chamada de `update_capital`.
- [x] #20 Adicionar flag `qualia.config.feature_toggle("risk_mgmt_v2")` para novas políticas.
- [x] #20 Criar benchmark simples para `calculate_position_size` usando `pytest-benchmark`.

## Features de Valor
1. **Integração com DynamicRiskController expandida**
   - *User Story*: Como operador, desejo receber ajustes de risco via eventos para responder dinamicamente ao mercado.
   - *Estimativa*: 4 dias.
2. **Tracing OpenTelemetry**
   - *User Story*: Como analista, quero rastrear o fluxo completo de ordens para depurar problemas de latência.
   - *Estimativa*: 2 dias.
   - *Atualização*: o `DynamicRiskController` agora gera um `trace_id` na inicialização quando o tracing está habilitado.
3. **Hooks para GPU/QPU**
   - *User Story*: Como desenvolvedor, quero opcionalmente utilizar hardware acelerado para cálculos de volatilidade quando disponível.
   - *Estimativa*: 6 dias.

Todos os riscos de Alta Gravidade devem gerar tickets com responsável e prazo definido.
