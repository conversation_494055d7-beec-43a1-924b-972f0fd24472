import pandas as pd
from unittest.mock import MagicMock

from qualia.market.temporal_pattern_detector import MarketTemporalPatternDetector
from qualia.config.temporal_detector_config import TemporalPatternConfig
from qualia.market.event_bus import (
    MARKET_PATTERN_EVENT,
    MarketPatternDetected,
    register_pattern_memory_handler,
)
from qualia.memory.quantum_pattern_memory import QuantumPatternMemory
from qualia.memory.event_bus import SimpleEventBus
from tests.memory.conftest import mock_risk_manager  # type: ignore
import numpy as np


def _simple_history(n: int = 5):
    base = pd.Timestamp("2020-01-01")
    history = []
    for i in range(n):
        history.append(
            {
                "decision": {
                    "timestamp": (base + pd.Timedelta(hours=i)).isoformat(),
                    "symbol": "TEST",
                    "action": "buy",
                },
                "performance": {"pnl": 1.0, "pnl_pct": 0.1, "duration": 1},
                "outcome": "ok",
            }
        )
    return history


def test_event_persists_in_qpm(monkeypatch, mock_risk_manager):
    monkeypatch.setattr(
        SimpleEventBus, "subscribe_async", SimpleEventBus.subscribe, raising=False
    )
    monkeypatch.setattr(
        "qualia.market.temporal_pattern_detector.HAS_PYWT", True, raising=False
    )

    class DummyStatsd:
        def gauge(self, *a, **k):
            pass

        def timing(self, *a, **k):
            pass

    monkeypatch.setattr(
        "qualia.market.temporal_pattern_detector.DogStatsd", DummyStatsd
    )
    bus = SimpleEventBus()
    qpm = QuantumPatternMemory(enable_warmstart=False, risk_manager=mock_risk_manager)
    qpm._vectorized_similarity = lambda *a, **k: np.array([1.0])
    register_pattern_memory_handler(qpm, bus)

    detector = MarketTemporalPatternDetector(
        config=TemporalPatternConfig(use_quantum_transform=False), event_bus=bus
    )
    fake_pattern = {"id": "p1", "type": "dummy", "confidence": 1.0}
    monkeypatch.setattr(
        detector, "_classical_wavelet_analysis", MagicMock(return_value=[fake_pattern])
    )

    detector.detect_patterns(_simple_history())

    assert qpm.get_memory_stats()["total_patterns"] >= 1
