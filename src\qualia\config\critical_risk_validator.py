"""
YAA TASK 3: Validador Crítico de Configuração de Risco

Este módulo implementa validação estrita de configurações de risco para prevenir
operação do sistema QUALIA em estado inseguro. Deve ser executado na inicialização
antes de qualquer operação de trading.
"""

from __future__ import annotations

import sys
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from ..utils.logging_config import get_qualia_logger
from .risk_validator import RiskParameterValidator, RiskValidationResult
from .risk_profiles import _FALLBACK_RISK_PROFILE_SETTINGS

logger = get_qualia_logger("config.critical_risk_validator")


@dataclass
class CriticalRiskValidationResult:
    """Resultado da validação crítica de risco."""
    
    is_safe_to_operate: bool
    critical_errors: List[str]
    warnings: List[str]
    missing_configurations: List[str]
    applied_fallbacks: List[str]
    validated_profiles: Dict[str, Any]
    risk_level_assessment: str  # "SAFE", "WARNING", "CRITICAL", "UNSAFE"


class CriticalRiskValidator:
    """
    Validador crítico de configurações de risco.
    
    Implementa validação estrita para garantir que o sistema não opere
    sem configurações adequadas de gerenciamento de risco.
    """
    
    # Configurações críticas que DEVEM estar presentes
    CRITICAL_RISK_PARAMETERS = [
        "max_position_percentage",
        "stop_loss_percentage", 
        "max_open_positions",
        "position_sizing_mode"
    ]
    
    # Perfis de risco obrigatórios
    REQUIRED_RISK_PROFILES = ["conservative", "moderate", "aggressive"]
    
    # Limites de segurança absolutos
    SAFETY_LIMITS = {
        "max_position_percentage": 0.20,  # Máximo 20% por posição
        "stop_loss_percentage": 0.10,     # Máximo 10% de stop loss
        "max_open_positions": 10,         # Máximo 10 posições abertas
        "max_daily_loss_pct": 15.0,       # Máximo 15% de perda diária
    }
    
    def __init__(self):
        """Inicializa o validador crítico."""
        self.validator = RiskParameterValidator()
        
    def validate_system_risk_configuration(
        self, 
        config_path: Optional[Path] = None,
        strategy_configs: Optional[Dict[str, Any]] = None
    ) -> CriticalRiskValidationResult:
        """
        Executa validação crítica completa do sistema de risco.
        
        Args:
            config_path: Caminho para arquivo de configuração principal
            strategy_configs: Configurações de estratégias carregadas
            
        Returns:
            CriticalRiskValidationResult com avaliação completa
        """
        logger.info("🔒 INICIANDO VALIDAÇÃO CRÍTICA DE RISCO")
        
        critical_errors = []
        warnings = []
        missing_configurations = []
        applied_fallbacks = []
        validated_profiles = {}
        
        # 1. Validar arquivo de configuração principal
        if config_path and config_path.exists():
            file_result = self.validator.validate_config_file(config_path)
            if not file_result.is_valid:
                critical_errors.extend(file_result.errors)
                applied_fallbacks.extend(file_result.applied_fallbacks)
            
            warnings.extend(file_result.warnings)
            validated_profiles.update(file_result.corrected_config)
        else:
            critical_errors.append("Arquivo de configuração de risco não encontrado")
            applied_fallbacks.append("Usando configurações de fallback")
            validated_profiles = self._get_emergency_fallback_profiles()
        
        # 2. Validar configurações de estratégias
        if strategy_configs:
            strategy_result = self._validate_strategy_risk_configs(strategy_configs)
            critical_errors.extend(strategy_result[0])
            warnings.extend(strategy_result[1])
            missing_configurations.extend(strategy_result[2])
        
        # 3. Verificar limites de segurança absolutos
        safety_result = self._validate_safety_limits(validated_profiles)
        critical_errors.extend(safety_result[0])
        warnings.extend(safety_result[1])
        
        # 4. Avaliar nível de risco geral
        risk_level = self._assess_overall_risk_level(
            critical_errors, warnings, missing_configurations
        )
        
        # 5. Determinar se é seguro operar
        is_safe = len(critical_errors) == 0 and risk_level in ["SAFE", "WARNING"]
        
        result = CriticalRiskValidationResult(
            is_safe_to_operate=is_safe,
            critical_errors=critical_errors,
            warnings=warnings,
            missing_configurations=missing_configurations,
            applied_fallbacks=applied_fallbacks,
            validated_profiles=validated_profiles,
            risk_level_assessment=risk_level
        )
        
        self._log_validation_result(result)
        return result
    
    def _validate_strategy_risk_configs(
        self, strategy_configs: Dict[str, Any]
    ) -> Tuple[List[str], List[str], List[str]]:
        """Valida configurações de risco das estratégias."""
        errors = []
        warnings = []
        missing = []
        
        for strategy_name, config in strategy_configs.items():
            risk_config = config.get("risk_management", {})
            
            if not risk_config:
                errors.append(f"Estratégia '{strategy_name}' sem configuração de risco")
                continue
            
            # Verificar parâmetros críticos
            for param in self.CRITICAL_RISK_PARAMETERS:
                if param not in risk_config:
                    missing.append(f"Estratégia '{strategy_name}': parâmetro '{param}' ausente")
                    
            # Verificar se há perfil de risco definido
            if "risk_profile" not in config:
                warnings.append(f"Estratégia '{strategy_name}' sem perfil de risco definido")
        
        return errors, warnings, missing
    
    def _validate_safety_limits(
        self, profiles: Dict[str, Any]
    ) -> Tuple[List[str], List[str]]:
        """Valida se as configurações respeitam limites de segurança absolutos."""
        errors = []
        warnings = []
        
        for profile_name, profile_config in profiles.items():
            for param, limit in self.SAFETY_LIMITS.items():
                if param in profile_config:
                    value = profile_config[param]
                    
                    if isinstance(value, (int, float)) and value > limit:
                        errors.append(
                            f"Perfil '{profile_name}': {param}={value} excede limite de segurança ({limit})"
                        )
        
        return errors, warnings
    
    def _assess_overall_risk_level(
        self, 
        critical_errors: List[str], 
        warnings: List[str], 
        missing: List[str]
    ) -> str:
        """Avalia o nível de risco geral do sistema."""
        if len(critical_errors) > 0:
            return "UNSAFE"
        elif len(missing) > 5:
            return "CRITICAL"
        elif len(warnings) > 3:
            return "WARNING"
        else:
            return "SAFE"
    
    def _get_emergency_fallback_profiles(self) -> Dict[str, Any]:
        """Retorna perfis de emergência ultra-conservadores."""
        emergency_profiles = {}
        
        for profile_name in self.REQUIRED_RISK_PROFILES:
            if profile_name in _FALLBACK_RISK_PROFILE_SETTINGS:
                profile = _FALLBACK_RISK_PROFILE_SETTINGS[profile_name]
                
                # Aplicar configurações ultra-conservadoras
                emergency_config = {
                    "max_position_percentage": min(0.02, profile.max_position_percentage),
                    "stop_loss_percentage": min(0.015, profile.stop_loss_percentage),
                    "max_open_positions": min(2, profile.max_open_positions),
                    "position_sizing_mode": "fixed_percentage",
                    "enable_trailing_stop": False,
                    "enable_dynamic_position_sizing": False,
                    "quantum_sensitivity_boost": 0.5,  # Reduzido para máxima segurança
                }
                
                emergency_profiles[profile_name] = emergency_config
        
        return emergency_profiles
    
    def _log_validation_result(self, result: CriticalRiskValidationResult) -> None:
        """Registra o resultado da validação nos logs."""
        if result.is_safe_to_operate:
            logger.info(
                f"✅ VALIDAÇÃO DE RISCO APROVADA - Nível: {result.risk_level_assessment}"
            )
            if result.warnings:
                logger.warning(f"⚠️ Avisos encontrados: {len(result.warnings)}")
                for warning in result.warnings:
                    logger.warning(f"  - {warning}")
        else:
            logger.error(
                f"❌ VALIDAÇÃO DE RISCO FALHOU - Nível: {result.risk_level_assessment}"
            )
            logger.error("🚨 SISTEMA NÃO SEGURO PARA OPERAÇÃO")
            
            for error in result.critical_errors:
                logger.error(f"  ERRO CRÍTICO: {error}")
            
            if result.missing_configurations:
                logger.error("  CONFIGURAÇÕES AUSENTES:")
                for missing in result.missing_configurations:
                    logger.error(f"    - {missing}")


def validate_system_before_trading(
    config_path: Optional[Path] = None,
    strategy_configs: Optional[Dict[str, Any]] = None,
    fail_on_unsafe: bool = True
) -> CriticalRiskValidationResult:
    """
    Função de conveniência para validação crítica antes do trading.
    
    Args:
        config_path: Caminho para configuração de risco
        strategy_configs: Configurações de estratégias
        fail_on_unsafe: Se deve falhar (sys.exit) em caso de configuração insegura
        
    Returns:
        CriticalRiskValidationResult
        
    Raises:
        SystemExit: Se fail_on_unsafe=True e sistema for considerado inseguro
    """
    validator = CriticalRiskValidator()
    result = validator.validate_system_risk_configuration(config_path, strategy_configs)
    
    if not result.is_safe_to_operate and fail_on_unsafe:
        logger.critical("🚨 SISTEMA INTERROMPIDO: Configuração de risco insegura")
        logger.critical("Corrija os erros críticos antes de prosseguir")
        sys.exit(1)
    
    return result


__all__ = [
    "CriticalRiskValidator",
    "CriticalRiskValidationResult", 
    "validate_system_before_trading"
]
