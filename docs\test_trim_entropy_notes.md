# test_trim_entropy Attempt

Foi solicitada a criação de um teste `test_trim_entropy.py` em `tests/` que gerasse um circuito profundo e verificasse `counts_diversity_ratio >= 0.73` após `enforce_circuit_limits`. Na primeira tentativa nenhuma configuração alcançou diversidade e profundidade dentro dos limites, logo o teste não foi adicionado.

Realizou-se posteriormente um sweep fino nos valores de `scr_depth` (2–4) utilizando `scripts/sweep_scr_depth.py`. Com `n_qubits=5`, `qpu_steps=1`, `steps=2` e `shots=128`, os resultados foram:

```
scr_depth=2 -> depth≈71 diversity≈0.78
scr_depth=3 -> depth≈80 diversity≈0.75
scr_depth=4 -> depth≈71 diversity≈0.77
```

Assim, `scr_depth=2` entregou diversidade acima de `0.73` mantendo profundidade abaixo de `80`. Tentou-se também carregar um noise model real (``FakeBelem``), porém a versão do ``qiskit`` utilizada não oferece esses backends fictícios e a instalação do pacote ``qiskit-ibm-provider`` gerou erros de importação de ``ProviderV1``. Portanto a simulação foi mantida sem ruído.
