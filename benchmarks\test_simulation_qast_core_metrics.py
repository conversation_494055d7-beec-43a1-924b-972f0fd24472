import json
import sys
import types
from dataclasses import dataclass
from pathlib import Path

import pytest

pytest.importorskip("pytest_benchmark")

# Criar stub de ``FarsightEngine`` para satisfazer import de ``HolographicFarsightEngine``
module = types.ModuleType("qualia.farsight.analyzer")


class FarsightEngine:
    pass


module.FarsightEngine = FarsightEngine
sys.modules.setdefault("qualia.farsight.analyzer", module)

# Stub para ``qualia.custom_types`` com estruturas mínimas.
ct_mod = types.ModuleType("qualia.custom_types")


@dataclass
class FutureScenario:
    scenario_id: str
    probability: float
    description: str
    dominant_patterns: list


@dataclass
class FutureProbabilityMap:
    timestamp: float
    scenarios: list


@dataclass
class CollectiveMindState:
    timestamp: float
    dominant_narrative: str
    persona_impact: dict


ct_mod.FutureScenario = FutureScenario
ct_mod.FutureProbabilityMap = FutureProbabilityMap
ct_mod.CollectiveMindState = CollectiveMindState
sys.modules.setdefault("qualia.custom_types", ct_mod)

from qualia.core.simulation_qast_core import SimulationQASTCore
from qualia.personas.retail_cluster import RetailCluster
from qualia.personas.momentum_quant import MomentumQuant
from qualia.consciousness.social_simulation_universe import SocialSimulationUniverse
from qualia.custom_types import CollectiveMindState


class DummyFarsightEngine:
    """Engine simplificado para gerar ``CollectiveMindState``."""

    def generate_collective_mind_state(self) -> CollectiveMindState:
        return CollectiveMindState(
            timestamp=0.0,
            dominant_narrative="TEST",
            persona_impact={},
        )


from qualia.monitoring.metrics import get_collector


def _build_core() -> SimulationQASTCore:
    personas = [RetailCluster("p1", {}), MomentumQuant("p2", {})]
    farsight = DummyFarsightEngine()
    universe = SocialSimulationUniverse()
    config = {
        "simulation_cycles": 3,
        "simulation_steps_per_cycle": 1,
        "future_scenario_clusters": 2,
    }
    return SimulationQASTCore(personas, farsight, universe, config)


def _run_cycle() -> None:
    core = _build_core()
    market_data = {"fear_greed_index": 50, "volatility": 0.05, "price_change_pct": 0.02}
    core.run_simulation_cycle(market_data)


def test_simulation_core_metrics(benchmark):
    benchmark(_run_cycle)
    summary = get_collector().get_summary()
    out_path = Path("benchmarks/results/simulation_core_metrics.json")
    out_path.parent.mkdir(parents=True, exist_ok=True)
    with out_path.open("w", encoding="utf-8") as fh:
        json.dump(summary, fh, indent=2)
    assert "simulation.cycle_duration_ms" in summary
