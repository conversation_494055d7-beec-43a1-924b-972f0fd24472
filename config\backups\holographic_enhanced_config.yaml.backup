# QUALIA Holographic Enhanced Configuration
# Configuração avançada para o sistema holográfico com quantum encoding

holographic:
  # Configuração do campo holográfico
  field_size: [200, 200]
  time_steps: 100
  diffusion_rate: 0.35
  feedback_strength: 0.08
  
  # Parâmetros de evolução
  evolution:
    step_interval: 15.0  # segundos entre evoluções
    min_field_energy: 0.01
    max_field_energy: 100.0
    entropy_threshold: 0.8
    
  # Detecção de padrões
  pattern_detection:
    min_strength: 0.3
    min_confidence: 0.6
    wavelet_scales: [2, 4, 8, 16]
    coherence_threshold: 0.7
    
  # Geração de sinais
  signal_generation:
    min_pattern_strength: 0.4
    min_pattern_confidence: 0.6
    signal_confidence_boost: 0.1
    max_signals_per_cycle: 10

# Amplificação adaptativa
amplification:
  # Valores iniciais
  initial:
    price_amplification: 5.0
    news_amplification: 4.0
    pattern_threshold: 0.3
    
  # Limites de segurança
  limits:
    min_amplification: 1.0
    max_amplification: 10.0
    min_pattern_threshold: 0.2
    max_pattern_threshold: 0.8
    
  # Parâmetros de aprendizado
  learning:
    rate: 0.1
    history_size: 100
    calibration_interval: 300  # segundos
    min_samples_for_calibration: 10

# Integração com quantum encoders
quantum_encoding:
  # RSS/News encoding
  rss_sentiment:
    enabled: true
    confidence_boost: 0.15
    quantum_weight: 1.2
    
  # Market data encoding
  market_indicators:
    rsi_encoding: true
    volume_encoding: true
    volatility_encoding: false
    
# Trading adapter
holographic_adapter:
  min_confidence: 0.6
  max_concurrent_positions: 3
  enable_holographic_risk_override: true
  
  # Risk overrides específicos
  risk_overrides:
    max_rsi_buy: 75
    min_rsi_sell: 25
    max_volatility: 8.0
    min_volume_ratio: 0.5
    min_pattern_strength: 0.4

# Data collection
data_collection:
  # Intervalos de coleta (segundos)
  market_data_interval: 30
  news_feed_interval: 60
  fear_greed_interval: 300
  
  # Fontes RSS
  rss_feeds:
    - "https://cointelegraph.com/rss"
    - "https://decrypt.co/feed"
    - "https://cryptonews.com/news/feed/"
    - "https://www.coindesk.com/arc/outboundfeeds/rss/"
    
  # Símbolos para monitorar
  symbols:
    - "BTCUSDT"
    - "ETHUSDT"
    - "ADAUSDT"
    - "SOLUSDT"
    - "MATICUSDT"
    - "DOTUSDT"
    - "LINKUSDT"
    - "UNIUSDT"

# Monitoramento e logging
monitoring:
  # Métricas holográficas
  log_field_summary: true
  log_pattern_detection: true
  log_signal_generation: true
  log_calibration_updates: true
  
  # Intervalos de log (segundos)
  field_summary_interval: 60
  performance_report_interval: 300
  
  # Alertas
  alerts:
    high_field_energy: 80.0
    low_pattern_detection: 0.05
    high_false_positive_rate: 0.3
    
# Performance tuning
performance:
  # Paralelização
  parallel_pattern_detection: true
  max_workers: 4
  
  # Cache
  cache_market_data: true
  cache_ttl: 300  # segundos
  
  # Batch processing
  batch_size: 50
  max_batch_wait: 5.0  # segundos 