"""
Módulo central para importações seguras com fallbacks para QUALIA.
"""

from ..utils.logger import get_logger
import warnings

logger = get_logger(__name__)

# --- qiskit.quantum_info ---
try:
    from qiskit.quantum_info import Statevector, partial_trace, entropy

    logger.info(
        "qiskit.quantum_info (Statevector, partial_trace, entropy) importado com sucesso."
    )
except ImportError:
    logger.warning(
        "qiskit.quantum_info não encontrado. Usando mocks para Statevector, partial_trace, entropy."
    )
    warnings.warn(
        "qiskit.quantum_info ausente. Funções quânticas funcionarão em modo demonstrativo",
        ImportWarning,
    )

    class MockStatevector:  # type: ignore
        """Mock para qiskit.quantum_info.Statevector."""

        def __init__(self, data, dims=None):
            self.data = data
            self.dims = dims
            logger.debug("MockStatevector instanciado.")

        @classmethod
        def from_instruction(cls, circuit):
            logger.debug(
                f"MockStatevector.from_instruction chamado com circuito: {circuit.name if hasattr(circuit, 'name') else 'Unnamed Circuit'}"
            )
            # Retorna uma instância mock com um estado base, por exemplo, |0...0>
            # O tamanho do data array deve ser 2**num_qubits.
            num_qubits = (
                circuit.num_qubits if hasattr(circuit, "num_qubits") else 1
            )  # Default para 1 qubit se não especificado
            mock_data = [0.0] * (2**num_qubits)
            if mock_data:
                mock_data[0] = 1.0  # Estado |0...0>
            return cls(mock_data)

        def is_valid(self, tol=1e-12):
            logger.debug("MockStatevector.is_valid chamado.")
            return True  # Sempre válido para o mock

        @property
        def num_qubits(self):
            if self.dims:
                # Estimativa grosseira
                return int(sum(d for d in self.dims) / 2)
            if self.data:
                num_q = (len(self.data) - 1).bit_length()  # log2(len(data))
                # Retorna None se não for potência de 2
                return num_q if 2**num_q == len(self.data) else None
            return None

        def expectation_value(self, oper, qargs=None):
            logger.debug(
                f"MockStatevector.expectation_value chamado para oper: {type(oper)}"
            )
            try:
                import numpy as np

                vector = np.asarray(self.data, dtype=complex)
                matrix = getattr(oper, "data", oper)
                matrix = np.asarray(matrix, dtype=complex)
                if matrix.shape != (vector.size, vector.size):
                    return 0.0
                expectation = np.vdot(vector, matrix @ vector)
                return float(expectation.real)
            except Exception as exc:  # pragma: no cover - fallback seguro
                logger.debug("MockStatevector.expectation_value erro: %s", exc)
                return 0.0

        def copy(self):
            logger.debug("MockStatevector.copy chamado.")
            return MockStatevector(
                self.data.copy() if self.data is not None else None, self.dims
            )

    Statevector = MockStatevector  # type: ignore

    def mock_partial_trace(state, qargs):
        logger.debug(
            f"mock_partial_trace chamado para estado: {type(state)} e qargs: {qargs}"
        )
        # Retorna um MockStatevector representando um estado traçado (dimensão reduzida)
        # Esta é uma simplificação extrema. Um mock real precisaria de mais lógica.
        # Por agora, apenas retorna um estado de 1 qubit no estado |0>.
        return MockStatevector([1.0, 0.0])

    partial_trace = mock_partial_trace  # type: ignore

    def mock_entropy(statevector, base=2):
        logger.debug(
            f"mock_entropy chamado para statevector: {type(statevector)} com base: {base}"
        )
        return 0.0  # Mock: entropia zero

    entropy = mock_entropy  # type: ignore

# --- qiskit_aer.noise.thermal_relaxation_error ---
try:
    from qiskit_aer.noise import thermal_relaxation_error

    logger.info("qiskit_aer.noise.thermal_relaxation_error importado com sucesso.")
except ImportError:
    try:
        from qiskit.providers.aer.noise import thermal_relaxation_error

        logger.info(
            "qiskit.providers.aer.noise.thermal_relaxation_error importado com sucesso."
        )
    except ImportError:
        thermal_relaxation_error = None  # Define como None se não encontrado
        logger.warning(
            "thermal_relaxation_error não encontrado em qiskit_aer.noise nem em qiskit.providers.aer.noise. Funcionalidade de ruído térmico pode ser limitada."
        )
        warnings.warn(
            "qiskit_aer ausente. Erros de relaxação térmica serão simulados",
            ImportWarning,
        )

# --- qualia.metrics.quantum_metrics ---
# Primeiro tenta importar do caminho do repositório
try:
    from ..metrics.quantum_metrics import renyi_entropy, mutual_information

    logger.info(
        "qualia.metrics.quantum_metrics (renyi_entropy, mutual_information) importado com sucesso."
    )
except ImportError:

    try:
        from ..metrics.quantum_metrics import (
            renyi_entropy,
            mutual_information,
        )

        logger.info(
            "qualia.metrics.quantum_metrics (renyi_entropy, mutual_information) importado com sucesso."
        )
    except ImportError:
        logger.warning(
            "Módulo de métricas quânticas (renyi_entropy, mutual_information) não encontrado. Usando funções mock."
        )
        warnings.warn(
            "Biblioteca de métricas quânticas ausente. Resultados retornam valores fictícios",
            ImportWarning,
        )

        def mock_renyi_entropy(state, alpha=2):
            logger.debug(
                f"mock_renyi_entropy chamado para estado: {type(state)} e alpha: {alpha}"
            )
            return 0.0  # Mock: entropia Renyi zero

        renyi_entropy = mock_renyi_entropy  # type: ignore

        def mock_mutual_information(state, idx1, idx2):
            logger.debug(
                f"mock_mutual_information chamado para estado: {type(state)}, idx1: {idx1}, idx2: {idx2}"
            )
            return 0.0  # Mock: informação mútua zero

        mutual_information = mock_mutual_information  # type: ignore

# --- qualia.utils.backend ---
try:
    from ..utils.backend import safe_get_backend

    logger.info("qualia.utils.backend.safe_get_backend importado com sucesso.")
except ImportError:
    logger.error(
        "Falha ao importar safe_get_backend de qualia.utils.backend. Esta é uma dependência crítica."
    )
    warnings.warn(
        "safe_get_backend não encontrado. Algumas funções podem falhar",
        ImportWarning,
    )
    # Definir um fallback MUITO básico para safe_get_backend pode ser perigoso,
    # pois o sistema depende dele para obter backends funcionais.
    # No entanto, para evitar que a importação quebre tudo, definimos um que
    # sempre falha ao ser chamado.

    def critical_safe_get_backend_fallback(
        backend_name: str, місце_виклику: str = "unknown"
    ):
        logger.critical(
            f"CRITICAL FALLBACK: safe_get_backend não pôde ser importado. Tentativa de obter backend '{backend_name}' falhará."
        )
        raise ImportError(
            f"safe_get_backend não está disponível. Backend '{backend_name}' não pode ser carregado."
        )

    safe_get_backend = critical_safe_get_backend_fallback  # type: ignore


# Placeholder para outros imports seguros que serão adicionados depois
# Ex: thermal_relaxation_error, renyi_entropy, mutual_information,
# safe_get_backend

if __name__ == "__main__":
    # Testes simples para os mocks, se executado diretamente
    logger.info("Testando importações seguras...")

    # Teste Statevector
    logger.info("Statevector type: %s", type(Statevector))
    if Statevector.__name__ == "MockStatevector":
        # Simular um circuito para from_instruction
        class MockCircuit:
            def __init__(self, num_qubits, name="test_circuit"):
                self.num_qubits = num_qubits
                self.name = name

        mock_qc = MockCircuit(2)
        sv_mock = Statevector.from_instruction(mock_qc)
        logger.info("MockStatevector.from_instruction data: %s", sv_mock.data)
        logger.info("MockStatevector.num_qubits: %s", sv_mock.num_qubits)
        sv_direct = Statevector([1, 0, 0, 0])
        logger.info("MockStatevector direct data: %s", sv_direct.data)
        logger.info("MockStatevector.is_valid(): %s", sv_direct.is_valid())
        logger.info(
            "MockStatevector.expectation_value(None): %s",
            sv_direct.expectation_value(None),
        )

    # Teste partial_trace
    logger.info("partial_trace type: %s", type(partial_trace))
    if partial_trace.__name__ == "mock_partial_trace":
        # Argumentos não importam muito para o mock
        traced_state = partial_trace(None, [0])
        logger.info(
            "mock_partial_trace result data: %s",
            traced_state.data if hasattr(traced_state, "data") else "N/A",
        )

    # Teste entropy
    logger.info("entropy type: %s", type(entropy))
    if entropy.__name__ == "mock_entropy":
        ent_val = entropy(None)
        logger.info("mock_entropy result: %s", ent_val)

    # Teste thermal_relaxation_error
    logger.info("thermal_relaxation_error type: %s", type(thermal_relaxation_error))
    if thermal_relaxation_error is None:
        logger.info(
            "thermal_relaxation_error não está disponível (conforme esperado no mock)."
        )

    # Teste renyi_entropy
    logger.info("renyi_entropy type: %s", type(renyi_entropy))
    if (
        hasattr(renyi_entropy, "__name__")
        and renyi_entropy.__name__ == "mock_renyi_entropy"
    ):
        renyi_val = renyi_entropy(None)
        logger.info("mock_renyi_entropy result: %s", renyi_val)

    # Teste mutual_information
    logger.info("mutual_information type: %s", type(mutual_information))
    if (
        hasattr(mutual_information, "__name__")
        and mutual_information.__name__ == "mock_mutual_information"
    ):
        mi_val = mutual_information(None, [0], [1])
        logger.info("mock_mutual_information result: %s", mi_val)

    # Teste safe_get_backend
    logger.info("safe_get_backend type: %s", type(safe_get_backend))
    if safe_get_backend.__name__ == "critical_safe_get_backend_fallback":
        try:
            safe_get_backend("test_backend")
        except ImportError as e:
            logger.info(
                "critical_safe_get_backend_fallback pegou erro (esperado): %s", e
            )
