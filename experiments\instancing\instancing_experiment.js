const express = require('express');
const path = require('path');
const puppeteer = require('puppeteer');
const fs = require('fs');

const app = express();
const PORT = 3005;

const threePath = path.join(__dirname, 'node_modules/three');
app.use('/three', express.static(path.join(threePath, 'build')));
app.use('/static', express.static(path.join(__dirname, '../../src/ui/static/js')));

app.get('/', (req, res) => {
  const mode = req.query.mode || 'basic';
  const script = mode === 'instanced' ? 'instanced-cubes.js' : 'basic-cubes.js';
  res.send(`<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<title>Instancing Experiment</title>
</head>
<body>
<div id="instancing-view" style="width:400px;height:300px;"></div>
<script src="/three/three.min.js"></script>
<script src="/static/${script}"></script>
</body>
</html>`);
});

const server = app.listen(PORT, async () => {
  console.log(`Server running on http://localhost:${PORT}`);
});

async function runTest(mode) {
  const browser = await puppeteer.launch({
    headless: "new",
    args: ["--no-sandbox"],
  });
  const page = await browser.newPage();
  await page.goto(`http://localhost:${PORT}/?mode=${mode}`);
  await page.waitForFunction(() => window.performanceData && window.performanceData.fps);
  const fps = await page.evaluate(() => window.performanceData.fps);
  await browser.close();
  return fps;
}

(async () => {
  const basicFps = await runTest('basic');
  const instancedFps = await runTest('instanced');
  console.log(`Basic FPS: ${basicFps.toFixed(2)}`);
  console.log(`Instanced FPS: ${instancedFps.toFixed(2)}`);
  const gain = ((instancedFps - basicFps) / basicFps) * 100;
  console.log(`Ganho: ${gain.toFixed(1)}%`);
  server.close();
})();
