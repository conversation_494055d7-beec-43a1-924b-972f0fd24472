#!/usr/bin/env python3
"""
Script simples para validar a configuração por timeframe
Testa apenas a estrutura dos arquivos de configuração
"""

import yaml
import json
import logging
from pathlib import Path

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def validate_yaml_config():
    """Valida o arquivo YAML de configuração"""
    logger.info("🔍 Validando config/fwh_scalp_config.yaml...")
    
    try:
        with open('config/fwh_scalp_config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Verificar estrutura básica
        assert 'fibonacci_wave_hype_config' in config
        assert 'params' in config['fibonacci_wave_hype_config']
        
        params = config['fibonacci_wave_hype_config']['params']
        
        # Verificar se timeframe_specific existe
        assert 'timeframe_specific' in params
        
        timeframe_specific = params['timeframe_specific']
        
        # Verificar timeframes
        required_timeframes = ["1m", "5m", "15m", "1h"]
        for tf in required_timeframes:
            assert tf in timeframe_specific, f"Timeframe {tf} não encontrado"
        
        # Verificar parâmetros em cada timeframe
        required_params = [
            "hype_threshold",
            "wave_min_strength", 
            "quantum_boost_factor",
            "holographic_weight",
            "tsvf_validation_threshold"
        ]
        
        for tf in required_timeframes:
            tf_params = timeframe_specific[tf]
            for param in required_params:
                assert param in tf_params, f"Parâmetro {param} não encontrado em {tf}"
                assert isinstance(tf_params[param], (int, float)), f"Parâmetro {param} em {tf} deve ser numérico"
        
        logger.info("✅ config/fwh_scalp_config.yaml está válido")
        
        # Mostrar valores por timeframe
        for tf in required_timeframes:
            tf_params = timeframe_specific[tf]
            logger.info(f"📊 {tf}: hype_threshold={tf_params['hype_threshold']}, "
                       f"wave_min_strength={tf_params['wave_min_strength']}, "
                       f"quantum_boost_factor={tf_params['quantum_boost_factor']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro no arquivo YAML: {e}")
        return False


def validate_yaml_strategy_params():
    """Valida o arquivo strategy_parameters.yaml"""
    logger.info("🔍 Validando config/strategy_parameters.yaml...")
    
    try:
        with open('config/strategy_parameters.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Verificar estrutura
        assert 'fibonacci_wave_hype_config' in config
        params = config['fibonacci_wave_hype_config']['params']
        assert 'timeframe_specific' in params
        
        # Verificar timeframes
        timeframe_specific = params['timeframe_specific']
        required_timeframes = ["1m", "5m", "15m", "1h"]
        
        for tf in required_timeframes:
            assert tf in timeframe_specific, f"Timeframe {tf} não encontrado"
        
        logger.info("✅ config/strategy_parameters.yaml está válido")
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro no arquivo strategy_parameters.yaml: {e}")
        return False


def validate_json_strategy_params():
    """Valida o arquivo strategy_parameters.json"""
    logger.info("🔍 Validando config/strategy_parameters.json...")
    
    try:
        with open('config/strategy_parameters.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Verificar estrutura
        assert 'fibonacci_wave_hype_config' in config
        params = config['fibonacci_wave_hype_config']['params']
        assert 'timeframe_specific' in params
        
        # Verificar timeframes
        timeframe_specific = params['timeframe_specific']
        required_timeframes = ["1m", "5m", "15m", "1h"]
        
        for tf in required_timeframes:
            assert tf in timeframe_specific, f"Timeframe {tf} não encontrado"
        
        logger.info("✅ config/strategy_parameters.json está válido")
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro no arquivo strategy_parameters.json: {e}")
        return False


def compare_configurations():
    """Compara se as configurações estão consistentes entre arquivos"""
    logger.info("🔄 Comparando consistência entre arquivos...")
    
    try:
        # Carregar arquivos
        with open('config/fwh_scalp_config.yaml', 'r', encoding='utf-8') as f:
            yaml_config = yaml.safe_load(f)
        
        with open('config/strategy_parameters.yaml', 'r', encoding='utf-8') as f:
            yaml_strategy = yaml.safe_load(f)
        
        with open('config/strategy_parameters.json', 'r', encoding='utf-8') as f:
            json_strategy = json.load(f)
        
        # Extrair parâmetros timeframe_specific
        yaml_tf = yaml_config['fibonacci_wave_hype_config']['params']['timeframe_specific']
        yaml_strat_tf = yaml_strategy['fibonacci_wave_hype_config']['params']['timeframe_specific']
        json_strat_tf = json_strategy['fibonacci_wave_hype_config']['params']['timeframe_specific']
        
        # Comparar estruturas
        timeframes = ["1m", "5m", "15m", "1h"]
        params = ["hype_threshold", "wave_min_strength", "quantum_boost_factor", 
                 "holographic_weight", "tsvf_validation_threshold"]
        
        inconsistencies = []
        
        for tf in timeframes:
            for param in params:
                yaml_val = yaml_tf[tf][param]
                yaml_strat_val = yaml_strat_tf[tf][param]
                json_strat_val = json_strat_tf[tf][param]
                
                if not (yaml_val == yaml_strat_val == json_strat_val):
                    inconsistencies.append(f"{tf}.{param}: YAML={yaml_val}, YAML_STRAT={yaml_strat_val}, JSON={json_strat_val}")
        
        if inconsistencies:
            logger.warning("⚠️ Inconsistências encontradas:")
            for inc in inconsistencies:
                logger.warning(f"  {inc}")
            return False
        else:
            logger.info("✅ Todos os arquivos estão consistentes")
            return True
            
    except Exception as e:
        logger.error(f"❌ Erro na comparação: {e}")
        return False


def main():
    """Função principal"""
    logger.info("🚀 Iniciando validação de configuração por timeframe...")
    
    results = []
    
    # Teste 1: Validar YAML principal
    results.append(validate_yaml_config())
    
    # Teste 2: Validar YAML de estratégia
    results.append(validate_yaml_strategy_params())
    
    # Teste 3: Validar JSON de estratégia
    results.append(validate_json_strategy_params())
    
    # Teste 4: Comparar consistência
    results.append(compare_configurations())
    
    # Resultado final
    if all(results):
        logger.info("🎉 TODAS AS VALIDAÇÕES PASSARAM!")
        logger.info("✅ Configuração por timeframe implementada com sucesso")
        return True
    else:
        logger.error("💥 ALGUMAS VALIDAÇÕES FALHARAM!")
        return False


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
