# Guardrails de Performance

Métrica

Alvo

Latência Média (encode() por amostra)

< 5 ms (5,000,000 ns)

Latência p95 (encode() por amostra)

< 8 ms (8,000,000 ns)

Heap Memory incremental

Mínima alocação; (Benchmark usa < 1MB por batch de 10k itens como referência)

Script de Benchmark

/benchmarks/encoders/run_encoder_bench.py

Qualquer PR que altere código de encoder deve anexar output do script acima demonstrando conformidade com estes SLAs.

**Resultados de Benchmark (Versão YYYY-MM-DD)**

*Executado com N_ITEMS_PER_BATCH = 10000, N_REPETITIONS = 5*

| Encoder                     | Mean Latency/Item (ns) | P95 Latency/Item (ns) | Peak Heap Incremental (bytes/batch) | Passa Guardrail Média (<5M ns)? | Passa Guardrail P95 (<8M ns)? | Passa Guardrail Heap (<1MB)? |
| :-------------------------- | :---------------------: | :--------------------: | :---------------------------------: | :-----------------------------: | :---------------------------: | :--------------------------: |
| OBIEncoder                  | 150,000                 | 250,000                | 150,000                             | Sim                             | Sim                           | Sim                          |
| LiquidityVelocityEncoder    | 80,000                  | 120,000                | 80,000                              | Sim                             | Sim                           | Sim                          |
| FundingRateDeviationEncoder | 90,000                  | 140,000                | 90,000                              | Sim                             | Sim                           | Sim                          |
| PriceMomentumEncoder        | 70,000                  | 110,000                | 75,000                              | Sim                             | Sim                           | Sim                          |
| VolatilityEncoder           | 75,000                  | 115,000                | 78,000                              | Sim                             | Sim                           | Sim                          |
*Valores ilustrativos. Substitua por resultados reais obtidos nos benchmarks executados pelo CI.*

1 — Visão Geral

A iniciativa SEN‑EncodersV2 introduz três novos encoders quânticos ‑ OBI, Liquidity Velocity e Funding‑Rate Deviation ‑ capazes de transformar sinais de microestrutura de mercado em rotações quânticas de alta fidelidade. O objetivo é ampliar o "ouvido" de QUALIA, oferecendo funcionalidades de ordem‑book, dinâmica de liquidez e comportamento de funding em tempo quase real.

2 — Escopo

**Dentro do escopo**

- OBIEncoder
- LiquidityVelocityEncoder
- FundingRateDeviationEncoder
- Benchmarks, testes (> 90 % cobertura)
- Documentação e exemplos de uso

**Fora do escopo**

- Alterações na QuantumEncodingInterface (mantém‑se estável)
- Otimizações em hardware especializado (tratar em épico futuro)

3 — Requisitos Funcionais

ID

Descrição

Critério de Aceite

 RF‑01

Cada encoder implementa encode(input) -> np.ndarray

Chamada retorna vetor numpy normalizado (‖v‖=1)

 RF‑02

Suporte a batch (encode(batch_inputs))

Tempo médio por item ainda nos guardrails

 RF‑03

Fallback seguro em caso de dados faltantes

Retorna vetor nulo + log de aviso

4 — Requisitos Não Funcionais

Seguir guardrails de performance (tabela acima).

Cobertura de testes unitários ≥ 90 % linhas + ramos.

Código mypy‑clean, black format, flake8 sem erros.

Compatível com Python 3.11.

5 — Modelo de Dados

class OrderBookSnapshot(TypedDict):
    bids: list[tuple[float, float]]  # [(price, size), ...]
    asks: list[tuple[float, float]]
    timestamp: float  # epoch ms

(Estruturas completas detalhadas por encoder nos anexos)

6 — Algoritmos Propostos

6.1 OBIEncoder

Agrupar níveis de preço em raio N ticks ao redor do mid‑price.

Somar volumes bid e ask.

Calcular obi = (bid_vol - ask_vol) / (bid_vol + ask_vol).

Mapear obi ∈ [‑1,1] → rotação θ = obi * π/2.

Retornar vetor [cos θ, sin θ].

(Detalhes matemáticos e pseudocódigo para LV e FRD nos subseções 6.2 e 6.3.)

7 — API Pública

Os encoders individuais herdam de `QuantumEncoder[InputSnapshotType]` e implementam `_encode_single(self, snapshot: InputSnapshotType) -> NDArrayFloat`. O método `encode(self, snapshot_or_batch)` é fornecido pela classe base.

Exemplo para `OBIEncoder`:

```python
from src.qualia.core.encoders import QuantumEncoder, OrderBookSnapshot, NDArrayFloat
import numpy as np
from src.qualia.utils.logger import get_logger

encoder_logger = get_logger(__name__)

class OBIEncoder(QuantumEncoder[OrderBookSnapshot]):
    def __init__(self, name: str = "OBIEncoder"):
        super().__init__(name)

    def _calculate_obi_from_snapshot(self, snapshot: OrderBookSnapshot) -> float:
        bids = snapshot.get('bids', [])
        asks = snapshot.get('asks', [])
        total_bid_volume = sum(size for price, size in bids)
        total_ask_volume = sum(size for price, size in asks)
        if total_bid_volume + total_ask_volume == 0:
            return 0.0
        obi_value = (total_bid_volume - total_ask_volume) / (total_bid_volume + total_ask_volume)
        return np.clip(obi_value, -1.0, 1.0)

    def _encode_single(self, snapshot: OrderBookSnapshot) -> NDArrayFloat:
        try:
            if not isinstance(snapshot, dict) or \
               not all(k in snapshot for k in ['bids', 'asks', 'timestamp']) or \
               not isinstance(snapshot['bids'], list) or \
               not isinstance(snapshot['asks'], list):
                encoder_logger.warning(f"Encoder '{self.name}' recebeu snapshot malformado.")
                return np.array([1.0, 0.0])
            
            order_book_imbalance = self._calculate_obi_from_snapshot(snapshot)
            theta = order_book_imbalance * (np.pi / 2.0)
            vector = np.array([np.cos(theta), np.sin(theta)])
            return vector
        except Exception as e:
            encoder_logger.exception(f"Encoder {self.name} falhou.")
            return np.array([1.0, 0.0])

# A QuantumEncodingInterface (se usada) é uma classe separada para gerenciar instâncias de encoders.
# class QuantumEncodingInterface:
#     def __init__(self, encoders: List[QuantumEncoder] = None):
#         ...
#     def add_encoder(self, encoder: QuantumEncoder):
#         ...
#     def get_encoder(self, name: str) -> Optional[QuantumEncoder]:
#         ...

8 — Plano de Testes

unit: verificação de cálculos individuais.

property‑based: invariantes (‖v‖ ≈ 1, simetria, limites).

benchmark: script calibra SLAs e falha no CI se violado.

9 — Riscos & Mitigações

Risco

Prob.

Impacto

Mitigação

Latência > guardrail em bursts

M

M

Profiling + otimização NumPy/Cython

Heap usage cresce com batches grandes

M

M

Pré‑alocação de buffers

10 — Histórico de Revisões

Versão

Data

Autor

Alteração

0.1‑draft

2025‑05‑16

YAA

Skeleton criado 