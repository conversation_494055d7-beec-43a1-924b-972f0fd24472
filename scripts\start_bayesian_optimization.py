#!/usr/bin/env python3
"""
QUALIA Bayesian Optimization Starter
Script principal para iniciar o sistema de otimização Bayesiana online
Integra com o sistema de trading QUALIA existente
"""

import asyncio
import json
import logging
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.utils.logger import get_logger, setup_logging
from qualia.config.config_loader import ConfigLoader
from qualia.optimization.parameter_tuner import ParameterTuner, TunerConfig
from qualia.optimization.bayesian_optimizer import OptimizationConfig

logger = get_logger(__name__)

class BayesianOptimizationManager:
    """
    Gerenciador principal do sistema de otimização Bayesiana.
    Coordena a integração com o sistema QUALIA existente.
    """
    
    def __init__(self):
        self.config_loader = ConfigLoader()
        self.parameter_tuner = None
        self.is_running = False
        
        # Carregar configuração
        self._load_configuration()
        
        logger.info("🧠 BayesianOptimizationManager inicializado")
    
    def _load_configuration(self):
        """Carrega configuração do sistema."""
        try:
            # Carregar configuração base
            base_config = self.config_loader.load()
            
            # Extrair símbolos do sistema
            symbols = []
            if "symbols" in base_config:
                symbols = list(base_config["symbols"].keys())
            else:
                # Usar símbolos padrão se não especificados
                symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "SOLUSDT", "DOTUSDT", "LINKUSDT", "POLUSDT"]
            
            # Configurar otimização baseada no benchmark
            optimization_config = OptimizationConfig(
                n_trials_per_cycle=25,  # Baseado no benchmark bem-sucedido
                optimization_interval_cycles=500,
                lookback_hours=24,
                objective_metric="sharpe_pnl_combined",
                
                # Ranges baseados nos resultados do benchmark
                price_amp_range=(1.0, 10.0),    # Benchmark mostrou que 10.0 é ótimo
                news_amp_range=(1.0, 15.0),     # Expandido para incluir descoberta de 11.3
                min_conf_range=(0.20, 0.80),    # Range do benchmark
                
                # Pesos otimizados
                sharpe_weight=0.6,
                pnl_weight=0.4
            )
            
            # Configurar tuner
            tuner_config = TunerConfig(
                symbols=symbols,
                optimization_interval_cycles=500,
                n_trials_per_cycle=25,
                max_concurrent_optimizations=4,
                performance_lookback_hours=24,
                min_trades_for_optimization=10,
                grpc_port=50051,
                grpc_enabled=True,
                state_save_interval_minutes=30
            )
            
            # Criar parameter tuner
            self.parameter_tuner = ParameterTuner(tuner_config)
            
            logger.info(f"⚙️ Configuração carregada: {len(symbols)} símbolos")
            logger.info(f"🔬 Otimização: {optimization_config.n_trials_per_cycle} trials a cada {optimization_config.optimization_interval_cycles} ciclos")
            
        except Exception as e:
            logger.error(f"❌ Erro ao carregar configuração: {e}")
            raise
    
    async def start(self):
        """Inicia o sistema de otimização Bayesiana."""
        logger.info("🚀 Iniciando sistema de otimização Bayesiana QUALIA")
        
        try:
            self.is_running = True
            
            # Verificar se há resultados de benchmark para usar como base
            await self._validate_benchmark_data()
            
            # Iniciar parameter tuner
            logger.info("🔧 Iniciando Parameter Tuner...")
            await self.parameter_tuner.start()
            
        except KeyboardInterrupt:
            logger.info("🛑 Interrompido pelo usuário")
        except Exception as e:
            logger.error(f"❌ Erro ao iniciar sistema: {e}")
            raise
        finally:
            await self.stop()
    
    async def _validate_benchmark_data(self):
        """Valida se os dados do benchmark estão disponíveis."""
        benchmark_file = Path("data/benchmark_results_20250706_141236_best_configs.json")
        
        if benchmark_file.exists():
            logger.info("✅ Dados do benchmark offline encontrados")
            
            # Carregar e validar dados
            try:
                with open(benchmark_file, 'r', encoding='utf-8') as f:
                    benchmark_data = json.load(f)
                
                best_configs = benchmark_data.get("best_configurations", [])
                if best_configs:
                    best_config = best_configs[0]
                    logger.info(f"🏆 Melhor configuração do benchmark:")
                    logger.info(f"   📊 Símbolo: {best_config['symbol']}")
                    logger.info(f"   🎯 Sharpe: {best_config['metrics']['sharpe_ratio']:.2f}")
                    logger.info(f"   💰 Return: {best_config['metrics']['total_return']:,.0f}")
                    logger.info(f"   📈 Parâmetros: {best_config['parameters']}")
                else:
                    logger.warning("⚠️ Nenhuma configuração encontrada no benchmark")
                    
            except Exception as e:
                logger.error(f"❌ Erro ao validar dados do benchmark: {e}")
        else:
            logger.warning("⚠️ Dados do benchmark não encontrados")
            logger.info("💡 Execute primeiro: python scripts/benchmark_offline.py")
    
    async def stop(self):
        """Para o sistema gracefully."""
        logger.info("🛑 Parando sistema de otimização Bayesiana...")
        
        self.is_running = False
        
        if self.parameter_tuner:
            await self.parameter_tuner.stop()
        
        logger.info("✅ Sistema parado com sucesso")
    
    def get_status(self) -> Dict:
        """Retorna status atual do sistema."""
        status = {
            "is_running": self.is_running,
            "timestamp": datetime.now().isoformat()
        }
        
        if self.parameter_tuner:
            status.update(self.parameter_tuner.get_status())
        
        return status
    
    def get_current_parameters(self, symbol: str = None) -> Dict:
        """Retorna parâmetros atuais."""
        if not self.parameter_tuner:
            return {}
        
        if symbol:
            return {symbol: self.parameter_tuner.get_current_parameters(symbol)}
        else:
            # Retornar todos os parâmetros
            all_params = {}
            for sym in self.parameter_tuner.active_symbols:
                all_params[sym] = self.parameter_tuner.get_current_parameters(sym)
            return all_params

async def main():
    """Função principal."""
    # Configurar logging
    setup_logging(level=logging.INFO)
    
    logger.info("=" * 60)
    logger.info("🧠 QUALIA BAYESIAN OPTIMIZATION SYSTEM")
    logger.info("=" * 60)
    logger.info("🎯 Etapa D: Bayesian Optimizer Online")
    logger.info("📊 Baseado nos resultados do benchmark offline")
    logger.info("🔬 Otimização inteligente com Optuna")
    logger.info("🌐 Comunicação via gRPC")
    logger.info("=" * 60)
    
    # Criar e iniciar manager
    manager = BayesianOptimizationManager()
    
    try:
        await manager.start()
    except Exception as e:
        logger.error(f"❌ Erro fatal: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
