# Revisado em 2025-06-13 por Codex
"""Flask application entry point for QUALIA."""

from __future__ import annotations

from flask import Flask, current_app, g
from flask_wtf.csrf import CSRFProtect
from socketio import WSGIApp

from qualia.config.settings import require_env
from ui.sockets import init_socketio

from qualia.utils.logger import get_logger
from qualia.utils.logging_initializer import initialize_logging
from qualia.extensions import QualiaStateExtension
from qualia.ui.blueprints import (
    api_bp,
    trading_bp,
    utils_bp,
    holographic_bp,
    farsight_bp,
)

logger = get_logger(__name__)
csrf = CSRFProtect()


def create_app() -> Flask:
    """Application factory used by tests and production."""
    app = Flask(__name__, static_folder="static", template_folder="templates")
    try:
        app.config["SECRET_KEY"] = require_env("QUALIA_SECRET_KEY")
    except EnvironmentError as exc:
        logger.error("Environment error: %s", str(exc))
        raise
    initialize_logging()
    csrf.init_app(app)

    app.register_blueprint(trading_bp)
    app.register_blueprint(api_bp)
    app.register_blueprint(utils_bp)
    app.register_blueprint(holographic_bp)
    app.register_blueprint(farsight_bp)

    csrf.exempt(api_bp)

    QualiaStateExtension(app)

    @app.before_request
    def _attach_state() -> None:
        g.qualia_state = current_app.extensions["qualia_state"]

    return app


app = create_app()
socketio = init_socketio(app)


def create_socketio_app() -> "WSGIApp":
    """Create ``WSGIApp`` for running with Gunicorn or eventlet."""

    from socketio import WSGIApp

    state = app.extensions["qualia_state"]
    from qualia.ui.initialize import initialize_qualia_system

    initialize_qualia_system(state)
    return WSGIApp(socketio, app)


socketio_app = create_socketio_app()


def run_dashboard() -> None:  # pragma: no cover - manual run
    """Execute o dashboard conforme configurado."""

    from qualia.utils.config import load_config

    config = load_config()
    web_cfg = config.get("web", {})
    host = web_cfg.get("host", "0.0.0.0")
    port = web_cfg.get("port", 5000)
    debug = web_cfg.get("debug", False)
    production = web_cfg.get("production_server", False)

    if production:
        import eventlet
        from eventlet import wsgi

        wsgi.server(eventlet.listen((host, port)), socketio_app)
    else:
        socketio.run(app, host=host, port=port, debug=debug)


if __name__ == "__main__":  # pragma: no cover - manual run
    run_dashboard()
