alerts:
  channels:
  - console
  - file
  - email
  critical:
  - emergency_stop_triggered
  - circuit_breaker_activated
  - api_connection_lost
  - position_stop_loss_hit
  - daily_loss_limit_reached
  enabled: true
  warning:
  - high_drawdown
  - consecutive_losses
  - low_sharpe_ratio
  - high_system_resource_usage
backup:
  config:
    enabled: true
    path: backups/pilot/config
  data:
    enabled: true
    path: backups/pilot
    retention_days: 30
  enabled: true
  interval_minutes: 15
bayesian_optimizer:
  conservative_mode: true
  enabled: true
  optimization:
    cycle_interval_hours: 6
    min_data_points: 100
    n_trials_per_cycle: 10
  parameter_bounds:
    min_confidence:
    - 0.3
    - 0.5
    news_amplification:
    - 8.0
    - 15.0
    price_amplification:
    - 0.5
    - 2.0
capital:
  emergency_stop_loss_pct: 5.0
  max_daily_risk_pct: 1.0
  max_position_size_pct: 2.0
  max_total_exposure_pct: 10.0
  total_capital_usd: 1000.0
circuit_breakers:
  emergency_shutdown_enabled: true
  enabled: true
  hourly_loss_threshold_usd: 20.0
  max_trades_per_hour: 5
  rapid_loss_threshold_pct: 2.0
  total_loss_threshold_usd: 50.0
deployment_date: '2025-07-07'
emergency:
  auto_shutdown_enabled: true
  recovery:
    admin_approval_required: true
    auto_restart_enabled: false
    notification_required: true
  shutdown_triggers:
  - total_loss_exceeds_50_usd
  - drawdown_exceeds_3_percent
  - api_errors_exceed_threshold
  - system_health_critical
environment: pilot
exchange:
  api_config:
    max_retries: 3
    rate_limit_buffer: 0.1
    timeout: 30
  api_url: https://api.kucoin.com
  environment: production
  name: kucoin
  order_config:
    default_order_type: limit
    max_order_size_usd: 20.0
    min_order_size_usd: 5.0
    price_precision: 8
    quantity_precision: 8
  ws_url: wss://ws-api.kucoin.com/endpoint

# Exchange configuration for QAST Core
exchanges:
  kucoin:
    enabled: true
    api_key: "${KUCOIN_API_KEY}"
    api_secret: "${KUCOIN_SECRET_KEY}"
    passphrase: "${KUCOIN_PASSPHRASE}"
    sandbox: false
    timeout: 30.0
    rate_limit: 4.0
    retry_attempts: 3
    retry_delay: 1.0
# Structured logging configuration
logging:
  # Basic settings
  level: INFO
  structured: true
  redact_sensitive: true

  # Console output
  console:
    enabled: true
    format: structured  # structured or simple
    compact: false
    use_colors: true

  # File output
  file:
    enabled: true
    path: logs/pilot/qualia_structured.log
    max_size_mb: 100
    backup_count: 10
    format: structured  # always structured for files

  # Specialized log files
  trades:
    enabled: true
    path: logs/pilot/trades.log
    include_decision_factors: true
    include_market_data: true
    redact_sensitive: true

  # Security settings
  security:
    redact_api_keys: true
    redact_secrets: true
    redact_passwords: true
    redact_tokens: true
    redact_env_vars: true
monitoring:
  enabled: true
  level: INTENSIVE
  performance:
    min_sharpe_threshold: 0.5
    track_max_drawdown: true
    track_profit_factor: true
    track_sharpe_ratio: true
    track_win_rate: true
  realtime:
    alert_thresholds:
      drawdown_pct: 2.0
      pnl_loss_usd: 25.0
      position_loss_pct: 0.5
    enabled: true
    update_interval_seconds: 10
  system_health:
    cpu_threshold_pct: 80
    disk_threshold_pct: 90
    memory_threshold_pct: 85
    network_latency_threshold_ms: 1000
performance_targets:
  daily_return_target_pct: 0.5
  max_acceptable_drawdown_pct: 5.0
  min_sharpe_ratio: 1.0
  min_win_rate_pct: 55.0
  monthly_return_target_pct: 10.0
qualia_integration:
  data_collector:
    cache_ttl_minutes: 1
    enabled: true
    symbols:
    - BTC/USDT
    timeframes:
    - 1m
    - 5m
  decision_engine:
    consciousness_level_threshold: 0.6
    enabled: true
    holographic_analysis: true
    quantum_processing: true
  enabled: true
  mode: live
  risk_manager:
    dynamic_adjustment: true
    enabled: true
    mode: ultra_conservative
  signals:
    enabled: true
    min_confidence: 0.37
    quantum_signals: true
    sentiment_signals: false
    technical_signals: true
  strategy:
    enabled: true
    mode: ultra_conservative
    name: QualiaTSVFStrategy
risk_management:
  # Unified risk profile configuration
  risk_profile: "moderate"
  initial_capital: 1000.0

  # Risk limits
  cooling_period_hours: 24
  daily_loss_limit_usd: 50.0
  max_consecutive_losses: 3
  max_drawdown_pct: 3.0
  position_stop_loss_pct: 1.0

  # Profile-specific settings
  risk_profile_settings:
    moderate:
      max_drawdown_pct: 12.0
      risk_per_trade_pct: 1.0
      max_position_size_pct: 15.0
      max_daily_loss_pct: 6.0
      max_open_positions: 2
      cooling_period_minutes: 30
      stop_loss_adjustment: 0.8
      min_lot_size: 0.0001
      position_sizing_mode: "volatility_adjusted"
      max_position_percentage: 0.05
      stop_loss_percentage: 0.025
      take_profit_percentage: 0.04
      enable_trailing_stop: true
      enable_dynamic_position_sizing: true
      quantum_sensitivity_boost: 1.2
security:
  api_key_rotation_days: 30
  credentials_file: config/.pilot_credentials
  encryption_enabled: true
  ip_whitelist:
    enabled: false
trading:
  amplification:
    min_confidence: 0.37
    news_amplification: 11.3
    price_amplification: 1.0
  limits:
    max_daily_trades: 5
    max_position_size_usd: 10.0
    max_positions: 1
    min_position_size_usd: 5.0
    min_trade_interval_minutes: 60
    position_timeout_hours: 24
  symbols:
  - BTC/USDT
  - ETH/USDT
  timeframes:
    primary: 1h
    secondary: 4h
validation:
  balance_validation: true
  position_size_validation: true
  pre_trade_validation: true
  production_mode: true
  real_credentials: true
  risk_limit_validation: true
  thresholds:
    max_daily_volume_usd: 200.0
    max_position_value_usd: 20.0
    min_account_balance_usd: 950.0
version: P-02.3
