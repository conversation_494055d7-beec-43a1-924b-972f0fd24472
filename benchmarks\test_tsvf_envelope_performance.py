import numpy as np
import pytest

pytest.importorskip("pytest_benchmark")

from qualia.intentions import IntentionEnvelope
from qualia.retroselector import RetroSelector
from qualia.strategies.nova_estrategia_qualia.tsvf import (
    TSVFCalculator,
)

prices_in = np.array([1.0, 2.0, 3.0], dtype=float)
prices_fin = np.array([2.0, 3.0, 4.0], dtype=float)
mc_paths = np.array(
    [
        [1.0, 2.0, 3.0],
        [2.0, 3.0, 4.0],
        [10.0, 10.0, 10.0],
    ],
    dtype=float,
)

envelope = IntentionEnvelope(profit_target=2.0, max_drawdown=1.0, horizon_hours=1)
selector = RetroSelector()

calculator = TSVFCalculator()


def _calc_no_env() -> dict:
    return calculator.compute_outputs(
        prices_in,
        prices_fin,
        vector_size=3,
        alpha=0.3,
        gamma=0.1,
        cE=0.1,
        cH=0.05,
    )


def _calc_with_env() -> dict:
    return calculator.compute_outputs(
        prices_in,
        prices_fin,
        vector_size=3,
        alpha=0.3,
        gamma=0.1,
        cE=0.1,
        cH=0.05,
        envelope=envelope,
        mc_paths=mc_paths,
        retro_selector=selector,
    )


@pytest.mark.benchmark(group="tsvf-envelope")
def test_tsvf_no_envelope_benchmark(benchmark):
    result = benchmark(_calc_no_env)
    assert "strength" in result


@pytest.mark.benchmark(group="tsvf-envelope")
def test_tsvf_with_envelope_benchmark(benchmark):
    result = benchmark(_calc_with_env)
    assert "strength" in result


def test_tsvf_results_similarity():
    no_env = _calc_no_env()
    with_env = _calc_with_env()
    assert set(no_env) == set(with_env)
