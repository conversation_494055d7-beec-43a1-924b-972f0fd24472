"""Utilitários de mitigação de erro quântico.

Este módulo implementa técnicas simples de mitigação usadas nos testes
do QUALIA. Inclui extrapolação de ruído zero (ZNE), mitigação
probabilística (PEM) e uma interface para mitigação baseada em modelos
de aprendizado de máquina.
"""

from __future__ import annotations

from typing import Iterable, Mapping, Any

import numpy as np
from numpy.typing import NDArray
from qiskit import QuantumCircuit
from sklearn.linear_model import LinearRegression

__all__ = ["apply_zne", "apply_pem", "apply_ml_mitigation"]


def _linear_extrapolation(x: Iterable[float], y: Iterable[float]) -> float:
    """Extrapola "y" para ``x=0`` usando regressão linear."""
    x_arr = np.asarray(list(x), dtype=float).reshape(-1, 1)
    y_arr = np.asarray(list(y), dtype=float)
    model = LinearRegression().fit(x_arr, y_arr)
    return float(model.predict(np.array([[0.0]]))[0])


def apply_zne(
    circuit: QuantumCircuit,
    backend: Any,
    shots: int = 1024,
    scale_factors: Iterable[int] | None = None,
) -> Mapping[str, int]:
    """Executa ``circuit`` aplicando extrapolação de ruído zero.

    Parâmetros
    ----------
    circuit
        Circuito a ser executado.
    backend
        Backend de execução.
    shots
        Número de amostras por escala de ruído.
    scale_factors
        Fatores de escala do ruído. Por padrão ``[1, 2, 3]``.

    Retorna
    -------
    dict
        Contagens mitigadas por extrapolação.
    """
    if scale_factors is None:
        scale_factors = [1, 2, 3]

    counts_per_scale: list[Mapping[str, int]] = []
    for factor in scale_factors:
        scaled = circuit.copy()
        for _ in range(int(factor) - 1):
            scaled.compose(circuit, inplace=True)
        result = backend.run(scaled, shots=shots).result()
        counts_per_scale.append(result.get_counts())

    all_keys = set().union(*[c.keys() for c in counts_per_scale])
    mitigated: dict[str, int] = {}
    for key in all_keys:
        probs = [c.get(key, 0) / shots for c in counts_per_scale]
        p0 = max(_linear_extrapolation(scale_factors, probs), 0.0)
        mitigated[key] = int(round(p0 * shots))
    return mitigated


def apply_pem(
    circuit: QuantumCircuit,
    backend: Any,
    shots: int = 1024,
    bit_flip_prob: float = 0.01,
) -> Mapping[str, int]:
    """Aplica mitigação probabilística baseada em bit-flip.

    Parâmetros
    ----------
    circuit
        Circuito a ser executado.
    backend
        Backend de execução.
    shots
        Número de amostras.
    bit_flip_prob
        Probabilidade de erro de leitura em cada bit clássico.

    Retorna
    -------
    dict
        Contagens mitigadas.
    """
    result = backend.run(circuit, shots=shots).result()
    raw_counts = result.get_counts()

    # Use the number of classical bits to match measurement outcome length
    # Qiskit's ``get_counts`` returns bitstrings sized ``circuit.num_clbits``
    # irrespective of the number of qubits in the circuit. Using ``num_qubits``
    # here would create an incorrectly sized confusion matrix for circuits with
    # partial measurements.
    num_bits = circuit.num_clbits or circuit.num_qubits
    states = [format(i, f"0{num_bits}b") for i in range(2**num_bits)]

    confusion_matrix = np.zeros((2**num_bits, 2**num_bits))
    for i, ideal_state in enumerate(states):
        for j, measured_state in enumerate(states):
            distance = sum(a != b for a, b in zip(ideal_state, measured_state))
            confusion_matrix[j, i] = (bit_flip_prob**distance) * (
                (1 - bit_flip_prob) ** (num_bits - distance)
            )

    try:
        confusion_inv = np.linalg.inv(confusion_matrix)
    except np.linalg.LinAlgError:  # pragma: no cover - singular matrix
        confusion_inv = np.linalg.pinv(confusion_matrix)

    counts_vec = np.array([raw_counts.get(s, 0) for s in states], dtype=float)
    corrected_counts: NDArray[np.float_] = confusion_inv @ counts_vec
    mitigated = {s: int(round(max(v, 0.0))) for s, v in zip(states, corrected_counts)}
    return mitigated


def apply_ml_mitigation(
    circuit: QuantumCircuit,
    backend: Any,
    shots: int = 1024,
    model: Any | None = None,
) -> Mapping[str, int]:
    """Utiliza modelo de ML simples para mitigar contagens.

    Se ``model`` for ``None``, um :class:`LinearRegression` trivial é
    treinado nas próprias contagens para ilustrar a interface.

    Parâmetros
    ----------
    circuit
        Circuito a ser executado.
    backend
        Backend de execução.
    shots
        Número de amostras.
    model
        Regressor do scikit-learn que recebe vetor de contagens e
        retorna contagens mitigadas.

    Retorna
    -------
    dict
        Contagens mitigadas pelo modelo de ML.
    """
    result = backend.run(circuit, shots=shots).result()
    raw_counts = result.get_counts()

    keys = list(raw_counts.keys())
    x = np.array(list(raw_counts.values()), dtype=float).reshape(1, -1)

    if model is None:
        model = LinearRegression().fit(x, x)

    y = model.predict(x).clip(min=0.0)[0]
    mitigated = {k: int(round(v)) for k, v in zip(keys, y)}
    return mitigated
