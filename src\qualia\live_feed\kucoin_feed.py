"""
QUALIA KuCoin Feed - Feed de dados em tempo real do <PERSON>Coin.

Este módulo implementa um feed robusto de dados do KuCoin usando REST + WebSocket,
com reconexão automática, cache local e normalização de dados.
"""

from __future__ import annotations

import asyncio
import json
import time
from typing import Dict, Any, Optional, Callable, List, Set
from datetime import datetime
import aiohttp

from ..market.kucoin_integration import KucoinIntegration
from ..utils.logger import get_logger
from .data_normalizer import DataNormalizer, NormalizedTicker, NormalizedOrderBook, NormalizedTrade

logger = get_logger(__name__)


class KuCoinFeed:
    """Feed de dados em tempo real do KuCoin."""
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        api_secret: Optional[str] = None,
        password: Optional[str] = None,
        symbols: Optional[List[str]] = None,
        enable_websocket: bool = True,
        enable_rest_fallback: bool = True,
        rest_update_interval: float = 5.0,
        max_reconnect_attempts: int = 5,
        reconnect_delay: float = 2.0,
        timeout: Optional[float] = None,
        sandbox: Optional[bool] = None,
        rate_limit: Optional[float] = None,
    ):
        """
        Inicializa o feed do KuCoin.

        Args:
            api_key: Chave da API KuCoin
            api_secret: Secret da API KuCoin
            password: Passphrase da API KuCoin
            symbols: Lista de símbolos para monitorar
            enable_websocket: Habilitar WebSocket
            enable_rest_fallback: Habilitar fallback REST
            rest_update_interval: Intervalo de atualização REST (segundos)
            max_reconnect_attempts: Máximo de tentativas de reconexão
            reconnect_delay: Delay entre tentativas de reconexão
            timeout: Timeout para requisições (segundos)
            sandbox: Usar ambiente sandbox (se disponível)
            rate_limit: Rate limit para requisições (segundos)
        """
        # Configurar parâmetros de timeout e rate limit
        kucoin_config = {}
        if timeout is not None:
            kucoin_config['conn_timeout'] = timeout
            kucoin_config['ticker_timeout'] = timeout
            kucoin_config['ohlcv_timeout'] = timeout

        self.kucoin = KucoinIntegration(
            api_key=api_key,
            api_secret=api_secret,
            password=password,
            use_websocket=enable_websocket,
            **kucoin_config
        )
        
        self.normalizer = DataNormalizer()
        self.symbols = symbols or ['BTC-USDT', 'ETH-USDT', 'ADA-USDT', 'SOL-USDT']
        self.enable_websocket = enable_websocket
        self.enable_rest_fallback = enable_rest_fallback
        self.rest_update_interval = rest_update_interval
        self.max_reconnect_attempts = max_reconnect_attempts
        self.reconnect_delay = reconnect_delay
        
        # Estado interno
        self.is_running = False
        self.is_connected = False
        self.reconnect_count = 0
        self.last_data_time = 0.0
        
        # Cache de dados
        self.ticker_cache: Dict[str, NormalizedTicker] = {}
        self.orderbook_cache: Dict[str, NormalizedOrderBook] = {}
        self.trade_cache: Dict[str, List[NormalizedTrade]] = {}
        
        # Callbacks
        self.on_ticker_callback: Optional[Callable[[NormalizedTicker], None]] = None
        self.on_orderbook_callback: Optional[Callable[[NormalizedOrderBook], None]] = None
        self.on_trade_callback: Optional[Callable[[NormalizedTrade], None]] = None
        self.on_error_callback: Optional[Callable[[Exception], None]] = None
        
        # Tasks
        self.websocket_task: Optional[asyncio.Task] = None
        self.rest_task: Optional[asyncio.Task] = None
        self.heartbeat_task: Optional[asyncio.Task] = None
    
    async def initialize(self) -> bool:
        """Inicializa a conexão com KuCoin."""
        try:
            await self.kucoin.initialize_connection()
            self.is_connected = True
            logger.info("✅ KuCoin Feed inicializado com sucesso")
            return True
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar KuCoin Feed: {e}")
            if self.on_error_callback:
                self.on_error_callback(e)
            return False
    
    async def start(self) -> bool:
        """Inicia o feed de dados."""
        if self.is_running:
            logger.warning("Feed já está rodando")
            return True
        
        if not self.is_connected:
            if not await self.initialize():
                return False
        
        self.is_running = True
        logger.info("🚀 Iniciando KuCoin Feed...")
        
        # Iniciar WebSocket se habilitado
        if self.enable_websocket:
            self.websocket_task = asyncio.create_task(self._websocket_loop())
        
        # Iniciar REST fallback se habilitado
        if self.enable_rest_fallback:
            self.rest_task = asyncio.create_task(self._rest_loop())
        
        # Iniciar heartbeat
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        
        logger.info("✅ KuCoin Feed iniciado")
        return True
    
    async def stop(self):
        """Para o feed de dados."""
        if not self.is_running:
            return
        
        logger.info("🛑 Parando KuCoin Feed...")
        self.is_running = False
        
        # Cancelar tasks
        for task in [self.websocket_task, self.rest_task, self.heartbeat_task]:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        # Fechar conexões
        await self.kucoin.close()
        self.is_connected = False
        
        logger.info("✅ KuCoin Feed parado")
    
    async def _websocket_loop(self):
        """Loop principal do WebSocket."""
        while self.is_running:
            try:
                # Subscrever tickers para todos os símbolos
                for symbol in self.symbols:
                    await self._subscribe_ticker(symbol)
                
                # Manter conexão ativa
                while self.is_running and self.is_connected:
                    await asyncio.sleep(1.0)
                    
            except Exception as e:
                logger.error(f"Erro no WebSocket loop: {e}")
                if self.on_error_callback:
                    self.on_error_callback(e)
                
                # Tentar reconectar
                if self.reconnect_count < self.max_reconnect_attempts:
                    self.reconnect_count += 1
                    logger.info(f"Tentativa de reconexão {self.reconnect_count}/{self.max_reconnect_attempts}")
                    await asyncio.sleep(self.reconnect_delay)
                    
                    try:
                        await self.kucoin.close_websocket()
                        await self.initialize()
                    except Exception as reconnect_error:
                        logger.error(f"Erro na reconexão: {reconnect_error}")
                else:
                    logger.error("Máximo de tentativas de reconexão atingido")
                    break
    
    async def _subscribe_ticker(self, symbol: str):
        """Subscreve ticker via WebSocket com retry robusto."""
        max_retries = 3
        retry_delay = 5.0

        for attempt in range(max_retries):
            try:
                # NETWORK FIX: Timeout específico para subscrição WebSocket aumentado
                ticker_data = await asyncio.wait_for(
                    self.kucoin.watch_ticker(symbol),
                    timeout=60.0  # NETWORK FIX: Timeout aumentado para 60s para subscrição
                )

                if ticker_data:
                    normalized_ticker = self.normalizer.normalize_kucoin_ticker(ticker_data)
                    self.ticker_cache[symbol] = normalized_ticker
                    self.last_data_time = time.time()

                    if self.on_ticker_callback:
                        self.on_ticker_callback(normalized_ticker)

                    logger.debug(f"[NETWORK FIX] Ticker WebSocket subscrito com sucesso: {symbol}")
                    return  # Sucesso, sair do loop de retry

            except asyncio.TimeoutError as e:
                logger.warning(f"[NETWORK FIX] Timeout na subscrição WebSocket {symbol} (tentativa {attempt+1}/{max_retries}): {e}")
            except asyncio.CancelledError as e:
                logger.warning(f"[NETWORK FIX] Subscrição WebSocket cancelada {symbol} (tentativa {attempt+1}/{max_retries}): {e}")
            except Exception as e:
                logger.error(f"[NETWORK FIX] Erro ao subscrever ticker WebSocket {symbol} (tentativa {attempt+1}/{max_retries}): {e}")

            # NETWORK FIX: Aguardar antes de retry se não for a última tentativa
            if attempt < max_retries - 1:
                await asyncio.sleep(retry_delay * (attempt + 1))  # Backoff exponencial

        logger.error(f"[NETWORK FIX] Falha ao subscrever ticker WebSocket {symbol} após {max_retries} tentativas")
    
    async def _rest_loop(self):
        """Loop de fallback via REST API com melhor tratamento de erros."""
        while self.is_running:
            try:
                # NETWORK FIX: Atualizar tickers via REST com timeout e retry
                for symbol in self.symbols:
                    try:
                        # NETWORK FIX: Timeout específico para REST fallback aumentado
                        ticker_data = await asyncio.wait_for(
                            self.kucoin.fetch_ticker(symbol),
                            timeout=120.0  # NETWORK FIX: Timeout aumentado para 120s para REST
                        )

                        if ticker_data:
                            normalized_ticker = self.normalizer.normalize_kucoin_ticker(ticker_data)

                            # NETWORK FIX: Cache staleness reduzido de 10s para 5s
                            if (symbol not in self.ticker_cache or
                                time.time() - self.ticker_cache[symbol].timestamp > 5.0):

                                self.ticker_cache[symbol] = normalized_ticker
                                self.last_data_time = time.time()

                                if self.on_ticker_callback:
                                    self.on_ticker_callback(normalized_ticker)

                                logger.debug(f"[NETWORK FIX] Ticker REST atualizado: {symbol}")

                    except asyncio.TimeoutError as e:
                        logger.warning(f"[NETWORK FIX] Timeout REST ticker {symbol}: {e}")
                    except asyncio.CancelledError as e:
                        logger.warning(f"[NETWORK FIX] REST ticker cancelado {symbol}: {e}")
                    except Exception as e:
                        logger.debug(f"[NETWORK FIX] Erro ao buscar ticker REST {symbol}: {e}")
                
                await asyncio.sleep(self.rest_update_interval)
                
            except Exception as e:
                logger.error(f"Erro no REST loop: {e}")
                await asyncio.sleep(self.rest_update_interval)
    
    async def _heartbeat_loop(self):
        """Loop de heartbeat para monitorar saúde da conexão."""
        while self.is_running:
            try:
                current_time = time.time()
                
                # Verificar se estamos recebendo dados
                if self.last_data_time > 0 and current_time - self.last_data_time > 30.0:
                    logger.warning("Sem dados há mais de 30 segundos, verificando conexão...")
                    
                    # Tentar buscar um ticker para testar conexão
                    try:
                        test_ticker = await self.kucoin.fetch_ticker(self.symbols[0])
                        if test_ticker:
                            logger.info("Conexão OK - ticker de teste recebido")
                            self.last_data_time = current_time
                        else:
                            logger.warning("Conexão pode estar com problemas")
                    except Exception as e:
                        logger.error(f"Teste de conexão falhou: {e}")
                
                await asyncio.sleep(10.0)  # Heartbeat a cada 10 segundos
                
            except Exception as e:
                logger.error(f"Erro no heartbeat: {e}")
                await asyncio.sleep(10.0)
    
    def set_ticker_callback(self, callback: Callable[[NormalizedTicker], None]):
        """Define callback para tickers."""
        self.on_ticker_callback = callback
    
    def set_orderbook_callback(self, callback: Callable[[NormalizedOrderBook], None]):
        """Define callback para order books."""
        self.on_orderbook_callback = callback
    
    def set_trade_callback(self, callback: Callable[[NormalizedTrade], None]):
        """Define callback para trades."""
        self.on_trade_callback = callback
    
    def set_error_callback(self, callback: Callable[[Exception], None]):
        """Define callback para erros."""
        self.on_error_callback = callback
    
    def get_latest_ticker(self, symbol: str) -> Optional[NormalizedTicker]:
        """Retorna o último ticker para um símbolo."""
        return self.ticker_cache.get(symbol)
    
    def get_all_tickers(self) -> Dict[str, NormalizedTicker]:
        """Retorna todos os tickers em cache."""
        return self.ticker_cache.copy()
    
    def get_feed_status(self) -> Dict[str, Any]:
        """Retorna status do feed."""
        return {
            'is_running': self.is_running,
            'is_connected': self.is_connected,
            'symbols_count': len(self.symbols),
            'cached_tickers': len(self.ticker_cache),
            'last_data_time': self.last_data_time,
            'reconnect_count': self.reconnect_count,
            'websocket_enabled': self.enable_websocket,
            'rest_fallback_enabled': self.enable_rest_fallback,
        }
