# Sistema de Logging de Métricas em Tempo Real

Este documento descreve o sistema de logging de métricas em tempo real implementado para capturar todas as decisões de trading e análises post-mortem.

## Visão Geral

O sistema de logging de métricas em tempo real foi desenvolvido para:

- **Capturar em tempo real** todas as métricas disponíveis para decisões que resultam em sinais consolidados
- **Evitar perda de dados** em caso de fechamento inesperado do sistema
- **Facilitar análise post-mortem** e calibração do sistema
- **Fornecer dados estruturados** em formato JSON para análise

## Componentes Principais

### 1. RealTimeMetricsLogger

Classe principal que gerencia o logging assíncrono com:
- Rotação automática de arquivos
- Compressão de logs antigos
- Buffer assíncrono para performance
- Tratamento de erros robusto

### 2. Estruturas de Dados

#### TimeframeMetrics
```python
@dataclass
class TimeframeMetrics:
    timeframe: str          # Ex: "1h", "15m", "5m", "1m"
    signal: str            # "BUY", "SELL", "HOLD"
    confidence: float      # 0.0 a 1.0
    price: float          # Preço atual
    reasoning: str        # Raciocínio da decisão
```

#### ConsolidatedMetrics
```python
@dataclass
class ConsolidatedMetrics:
    signal: str                    # Sinal consolidado final
    confidence: float             # Confiança consolidada
    primary_timeframe: str        # Timeframe principal
    supporting_timeframes: List[str]  # Timeframes de suporte
    convergence_score: float      # Score de convergência
    reasoning: str               # Raciocínio da consolidação
```

#### TradingDecisionMetrics
```python
@dataclass
class TradingDecisionMetrics:
    decision_id: str              # ID único da decisão
    symbol: str                  # Par de trading
    action: str                  # "OPEN_POSITION", "CLOSE_POSITION"
    signal: str                  # Sinal de trading
    confidence: float            # Confiança da decisão
    price: float                 # Preço de execução
    quantity: float              # Quantidade
    stop_loss: Optional[float]   # Stop loss
    take_profit: Optional[float] # Take profit
    # ... outros campos
```

## Pontos de Logging

### 1. Estratégia FWH (core.py)

- **Métricas por Timeframe**: Captura análise de cada timeframe individual
- **Métricas Consolidadas**: Captura resultado da consolidação multi-timeframe
- **Decisão Final**: Captura a decisão final de trading com todos os parâmetros

### 2. Sistema de Paper Trading (run_fwh_scalp_paper_trading.py)

- **Consolidação Multi-Timeframe**: Logs detalhados da consolidação de sinais
- **Validação de Risco**: Logs de todas as validações de risco
- **Validação de Qualidade**: Logs detalhados das validações de qualidade de sinal
- **Abertura de Posição**: Logs completos quando uma posição é aberta
- **Fechamento de Posição**: Logs completos quando uma posição é fechada

## Estrutura dos Arquivos de Log

### Localização
```
logs/
├── real_time_metrics/
│   ├── metrics_YYYYMMDD.jsonl     # Logs do dia atual
│   ├── metrics_YYYYMMDD.jsonl.gz  # Logs comprimidos
│   └── ...
```

### Formato dos Logs

Todos os logs são salvos em formato JSON Lines (.jsonl), onde cada linha é um objeto JSON válido:

```json
{
  "timestamp": "2024-01-15T10:30:45.123456",
  "log_type": "TIMEFRAME_METRICS",
  "decision_id": "BTCUSDT_20240115_103045_123456",
  "data": {
    "timeframe": "1h",
    "signal": "BUY",
    "confidence": 0.75,
    "price": 42500.0,
    "reasoning": "Strong bullish divergence on 1h timeframe"
  }
}
```

## Tipos de Logs Capturados

### 1. TIMEFRAME_METRICS
Métricas de análise por timeframe individual

### 2. CONSOLIDATED_METRICS
Métricas de consolidação multi-timeframe

### 3. TRADING_DECISION
Decisões completas de trading (abertura/fechamento)

### 4. RISK_VALIDATION
Resultados de validações de risco

### 5. SIGNAL_QUALITY_VALIDATION
Resultados de validações de qualidade de sinal

## Configuração

### Variáveis de Ambiente

```bash
# Diretório de logs (opcional)
QUALIA_METRICS_LOG_DIR=/path/to/logs/real_time_metrics

# Nível de log (opcional)
QUALIA_METRICS_LOG_LEVEL=INFO

# Tamanho máximo do arquivo antes da rotação (opcional)
QUALIA_METRICS_MAX_FILE_SIZE=100MB

# Número de arquivos de backup (opcional)
QUALIA_METRICS_BACKUP_COUNT=30
```

### Inicialização

```python
from qualia.utils.real_time_metrics_logger import get_metrics_logger

# Obter logger (singleton)
metrics_logger = get_metrics_logger()

# Verificar se está disponível
if metrics_logger:
    print("Sistema de métricas inicializado com sucesso")
else:
    print("Sistema de métricas não disponível")
```

## Análise dos Logs

### Leitura dos Logs

```python
import json
import gzip
from pathlib import Path

def read_metrics_logs(log_file_path):
    """Lê logs de métricas (suporta .jsonl e .jsonl.gz)"""
    metrics = []
    
    if log_file_path.endswith('.gz'):
        with gzip.open(log_file_path, 'rt') as f:
            for line in f:
                metrics.append(json.loads(line.strip()))
    else:
        with open(log_file_path, 'r') as f:
            for line in f:
                metrics.append(json.loads(line.strip()))
    
    return metrics

# Exemplo de uso
metrics = read_metrics_logs('logs/real_time_metrics/metrics_20240115.jsonl')
print(f"Total de métricas: {len(metrics)}")
```

### Análise de Performance

```python
import pandas as pd

def analyze_trading_decisions(metrics):
    """Analisa decisões de trading"""
    trading_decisions = [
        m for m in metrics 
        if m.get('log_type') == 'TRADING_DECISION'
    ]
    
    df = pd.DataFrame([d['data'] for d in trading_decisions])
    
    # Análises
    print(f"Total de decisões: {len(df)}")
    print(f"Confiança média: {df['confidence'].mean():.3f}")
    print(f"PnL total: {df['net_pnl'].sum():.2f}")
    
    return df
```

### Análise de Validações

```python
def analyze_validations(metrics):
    """Analisa validações rejeitadas"""
    validations = [
        m for m in metrics 
        if m.get('log_type') in ['RISK_VALIDATION', 'SIGNAL_QUALITY_VALIDATION']
    ]
    
    rejected = [v for v in validations if not v['data']['validation_result']]
    
    print(f"Total de validações: {len(validations)}")
    print(f"Rejeitadas: {len(rejected)} ({len(rejected)/len(validations)*100:.1f}%)")
    
    # Razões de rejeição
    rejection_reasons = {}
    for v in rejected:
        reason = v['data']['rejection_reason']
        rejection_reasons[reason] = rejection_reasons.get(reason, 0) + 1
    
    print("\nRazões de rejeição:")
    for reason, count in sorted(rejection_reasons.items(), key=lambda x: x[1], reverse=True):
        print(f"  {reason}: {count}")
```

## Monitoramento em Tempo Real

### Tail dos Logs

```bash
# Monitorar logs em tempo real
tail -f logs/real_time_metrics/metrics_$(date +%Y%m%d).jsonl | jq .

# Filtrar apenas decisões de trading
tail -f logs/real_time_metrics/metrics_$(date +%Y%m%d).jsonl | \
  jq 'select(.log_type == "TRADING_DECISION")'
```

### Dashboard Simples

```python
import time
import json
from datetime import datetime

def monitor_metrics(log_file_path, interval=5):
    """Monitor métricas em tempo real"""
    last_position = 0
    
    while True:
        try:
            with open(log_file_path, 'r') as f:
                f.seek(last_position)
                new_lines = f.readlines()
                last_position = f.tell()
                
                for line in new_lines:
                    metric = json.loads(line.strip())
                    
                    if metric.get('log_type') == 'TRADING_DECISION':
                        data = metric['data']
                        print(f"[{datetime.now()}] {data['action']} {data['symbol']} "
                              f"@ ${data['price']:.2f} (conf: {data['confidence']:.3f})")
                
        except FileNotFoundError:
            print(f"Aguardando arquivo de log: {log_file_path}")
        except Exception as e:
            print(f"Erro: {e}")
        
        time.sleep(interval)
```

## Melhores Práticas

### 1. Backup e Retenção
- Logs são automaticamente comprimidos após rotação
- Manter pelo menos 30 dias de logs para análise
- Fazer backup regular dos logs para armazenamento de longo prazo

### 2. Performance
- O sistema usa logging assíncrono para não impactar a performance
- Logs são bufferizados e escritos em lotes
- Rotação automática previne arquivos muito grandes

### 3. Análise
- Usar pandas para análise estruturada dos dados
- Implementar alertas para padrões anômalos
- Criar dashboards para monitoramento visual

### 4. Debugging
- Cada decisão tem um `decision_id` único para rastreamento
- Logs incluem contexto completo para debugging
- Timestamps precisos para análise temporal

## Troubleshooting

### Logs Não Sendo Gerados
1. Verificar se o diretório de logs existe e tem permissões de escrita
2. Verificar se o sistema de métricas foi inicializado corretamente
3. Verificar logs de erro do sistema principal

### Performance Degradada
1. Verificar tamanho dos arquivos de log
2. Ajustar configurações de rotação se necessário
3. Monitorar uso de disco

### Análise de Dados
1. Usar ferramentas como jq para análise rápida de JSON
2. Implementar scripts de análise automatizada
3. Considerar integração com ferramentas de BI para análise avançada

## Conclusão

O sistema de logging de métricas em tempo real fornece visibilidade completa sobre todas as decisões do sistema de trading, permitindo:

- **Análise post-mortem** detalhada de trades
- **Calibração** precisa do sistema baseada em dados reais
- **Debugging** eficiente de problemas
- **Otimização** contínua da estratégia
- **Compliance** e auditoria completa

Todos os dados são capturados em tempo real e persistidos de forma segura, garantindo que nenhuma informação importante seja perdida.