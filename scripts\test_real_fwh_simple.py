#!/usr/bin/env python3
"""
Teste simples da estratégia FWH real com dados da API Binance.
"""

import sys
import os
import asyncio
import pandas as pd
from dotenv import load_dotenv
from datetime import datetime, timedelta

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Carregar variáveis de ambiente
load_dotenv(os.path.join(os.path.dirname(__file__), '..', '.env'))

async def test_fwh_with_real_data():
    """Testa FWH com dados reais da API."""
    print("🎯 TESTE FWH COM DADOS REAIS DA API BINANCE")
    print("=" * 60)
    
    try:
        # Verificar credenciais
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')
        
        if not api_key or not api_secret:
            print("❌ Credenciais Binance não encontradas")
            return False
        
        print(f"🔑 API Key: {api_key[:8]}...")
        
        # 1. OBTER DADOS REAIS
        print(f"\n📊 Obtendo dados históricos reais...")
        
        from qualia.market.binance_integration import BinanceIntegration
        from qualia.common.specs import MarketSpec
        
        binance = BinanceIntegration(api_key=api_key, api_secret=api_secret)
        await binance.initialize_connection()
        
        # Período de teste (últimos 3 dias para ser mais rápido)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=3)
        
        print(f"📅 Período: {start_date.strftime('%Y-%m-%d')} a {end_date.strftime('%Y-%m-%d')}")
        
        # Obter dados 1h (menos intensivo)
        print(f"   🔄 Buscando dados 1h...")
        
        spec = MarketSpec(symbol="BTC/USDT", timeframe="1h")
        df = await binance.fetch_historical_data(
            spec=spec,
            start_date=start_date,
            end_date=end_date,
            use_cache=True
        )
        
        await binance.close()
        
        if df.empty:
            print("❌ Nenhum dado histórico obtido")
            return False
        
        print(f"✅ Dados obtidos: {len(df)} velas 1h")
        print(f"   Preço médio: ${df['close'].mean():.2f}")
        print(f"   Volume médio: {df['volume'].mean():.2f}")
        
        # 2. TESTAR ESTRATÉGIA FWH
        print(f"\n🧠 Testando estratégia FWH...")
        
        from qualia.strategies.fibonacci_wave_hype import FibonacciWaveHypeStrategy
        from qualia.strategies.strategy_interface import TradingContext
        
        # OTIMIZAÇÃO CIENTÍFICA: Testar múltiplas configurações
        print(f"\n🔬 INICIANDO OTIMIZAÇÃO CIENTÍFICA...")

        # Espaço de parâmetros para otimização real
        param_space = {
            'fib_lookback': [10, 15, 20, 25, 30],
            'hype_threshold': [0.01, 0.02, 0.03, 0.05, 0.08, 0.10, 0.15],
            'wave_min_strength': [0.005, 0.01, 0.02, 0.03, 0.05, 0.08],
            'quantum_boost_factor': [1.01, 1.02, 1.03, 1.05, 1.08, 1.10, 1.15]
        }

        print(f"📊 Espaço de parâmetros:")
        for param, values in param_space.items():
            print(f"   {param}: {len(values)} valores")

        total_combinations = 1
        for values in param_space.values():
            total_combinations *= len(values)
        print(f"🎯 Total de combinações: {total_combinations:,}")

        # Testar configuração base primeiro
        base_config = {
            'fib_lookback': 20,
            'hype_threshold': 0.05,
            'wave_min_strength': 0.01,
            'quantum_boost_factor': 1.08,
        }

        print(f"\n🧪 Testando configuração base primeiro...")
        strategy = FibonacciWaveHypeStrategy(
            symbol="BTC/USDT",
            timeframe="1h",
            parameters=base_config
        )
        
        print(f"✅ Estratégia FWH inicializada")
        
        # 3. TESTAR GERAÇÃO DE SINAIS
        print(f"\n📡 Testando geração de sinais...")

        def test_strategy_config(params, data, name="Config"):
            """Testa uma configuração específica."""
            try:
                strategy = FibonacciWaveHypeStrategy(
                    symbol="BTC/USDT",
                    timeframe="1h",
                    parameters=params
                )

                signals_generated = 0
                buy_signals = 0
                sell_signals = 0
                confidences = []

                # Testar com últimas 40 velas
                test_data = data.tail(40).copy()
                lookback = params['fib_lookback']

                for i in range(lookback, len(test_data)):
                    try:
                        current_data = test_data.iloc[:i+1]
                        context = TradingContext(
                            symbol="BTC/USDT",
                            timeframe="1h",
                            ohlcv=current_data,
                            current_price=float(current_data['close'].iloc[-1]),
                            timestamp=pd.Timestamp.now(),
                            wallet_state={"BTC": 0.0, "USDT": 10000.0},
                            liquidity=0.5,
                            volatility=0.02,
                            strategy_metrics={},
                            quantum_metrics={},
                            market_state="trend",
                            risk_mode="normal"
                        )

                        signal_df = strategy.generate_signal(context)

                        if not signal_df.empty:
                            signals_generated += 1
                            signal = signal_df.iloc[0]['signal']
                            confidence = signal_df.iloc[0]['confidence']
                            confidences.append(confidence)

                            if signal == 'buy':
                                buy_signals += 1
                            elif signal == 'sell':
                                sell_signals += 1

                    except Exception as e:
                        continue

                return {
                    'name': name,
                    'params': params,
                    'signals_generated': signals_generated,
                    'buy_signals': buy_signals,
                    'sell_signals': sell_signals,
                    'avg_confidence': sum(confidences) / len(confidences) if confidences else 0,
                    'max_confidence': max(confidences) if confidences else 0,
                    'min_confidence': min(confidences) if confidences else 0
                }

            except Exception as e:
                return {
                    'name': name,
                    'params': params,
                    'error': str(e),
                    'signals_generated': 0
                }

        # Testar configuração base
        base_result = test_strategy_config(base_config, df, "Base")
        print(f"\n📊 Resultado Base:")
        print(f"   Sinais: {base_result['signals_generated']}")
        print(f"   BUY: {base_result['buy_signals']}, SELL: {base_result['sell_signals']}")
        print(f"   Confidence: avg={base_result.get('avg_confidence', 0):.3f}, max={base_result.get('max_confidence', 0):.3f}")

        # OTIMIZAÇÃO CIENTÍFICA: Testar configurações extremas
        print(f"\n🔬 OTIMIZAÇÃO CIENTÍFICA - Testando configurações extremas...")

        extreme_configs = [
            # Configuração muito sensível
            {
                'name': 'Ultra Sensível',
                'fib_lookback': 10,
                'hype_threshold': 0.01,
                'wave_min_strength': 0.005,
                'quantum_boost_factor': 1.15,
            },
            # Configuração muito conservadora
            {
                'name': 'Ultra Conservadora',
                'fib_lookback': 30,
                'hype_threshold': 0.15,
                'wave_min_strength': 0.08,
                'quantum_boost_factor': 1.01,
            },
            # Configuração balanceada
            {
                'name': 'Balanceada',
                'fib_lookback': 20,
                'hype_threshold': 0.08,
                'wave_min_strength': 0.03,
                'quantum_boost_factor': 1.05,
            },
            # Configuração de alta frequência
            {
                'name': 'Alta Frequência',
                'fib_lookback': 15,
                'hype_threshold': 0.02,
                'wave_min_strength': 0.01,
                'quantum_boost_factor': 1.10,
            }
        ]

        results = []
        for config in extreme_configs:
            name = config.pop('name')
            result = test_strategy_config(config, df, name)
            results.append(result)

            print(f"\n📊 {name}:")
            print(f"   Parâmetros: {config}")
            print(f"   Sinais: {result['signals_generated']}")
            print(f"   BUY: {result['buy_signals']}, SELL: {result['sell_signals']}")
            if 'avg_confidence' in result:
                print(f"   Confidence: avg={result['avg_confidence']:.3f}, max={result['max_confidence']:.3f}")
            if 'error' in result:
                print(f"   ❌ Erro: {result['error']}")

        # Encontrar melhor configuração por atividade
        best_by_signals = max([r for r in results if 'signals_generated' in r],
                             key=lambda x: x['signals_generated'], default=base_result)

        print(f"\n🏆 MELHOR CONFIGURAÇÃO POR ATIVIDADE:")
        print(f"   Nome: {best_by_signals['name']}")
        print(f"   Sinais: {best_by_signals['signals_generated']}")
        print(f"   Parâmetros: {best_by_signals['params']}")

        signals_generated = best_by_signals['signals_generated']
        buy_signals = best_by_signals['buy_signals']
        sell_signals = best_by_signals['sell_signals']
        
        # 4. GRID SEARCH INTELIGENTE
        print(f"\n🧬 INICIANDO GRID SEARCH INTELIGENTE...")

        # Selecionar os melhores valores baseados nos testes extremos
        if signals_generated > 0:
            print(f"✅ Sinais sendo gerados - Iniciando busca otimizada")

            # Grid search focado nos melhores ranges
            best_params = best_by_signals['params']

            # Criar grid em torno dos melhores parâmetros
            grid_search_space = {
                'fib_lookback': [
                    max(10, best_params['fib_lookback'] - 5),
                    best_params['fib_lookback'],
                    min(30, best_params['fib_lookback'] + 5)
                ],
                'hype_threshold': [
                    max(0.01, best_params['hype_threshold'] * 0.5),
                    best_params['hype_threshold'],
                    min(0.15, best_params['hype_threshold'] * 1.5)
                ],
                'wave_min_strength': [
                    max(0.005, best_params['wave_min_strength'] * 0.5),
                    best_params['wave_min_strength'],
                    min(0.08, best_params['wave_min_strength'] * 1.5)
                ],
                'quantum_boost_factor': [
                    max(1.01, best_params['quantum_boost_factor'] - 0.02),
                    best_params['quantum_boost_factor'],
                    min(1.15, best_params['quantum_boost_factor'] + 0.02)
                ]
            }

            print(f"🎯 Grid Search Space:")
            for param, values in grid_search_space.items():
                print(f"   {param}: {values}")

            # Testar todas as combinações do grid
            from itertools import product

            grid_combinations = list(product(*grid_search_space.values()))
            print(f"🔬 Testando {len(grid_combinations)} combinações do grid...")

            grid_results = []
            for i, combination in enumerate(grid_combinations[:27]):  # Limitar a 27 para ser rápido
                params = dict(zip(grid_search_space.keys(), combination))
                result = test_strategy_config(params, df, f"Grid_{i+1}")
                grid_results.append(result)

                if i < 5:  # Mostrar primeiros 5
                    print(f"   Grid {i+1}: {result['signals_generated']} sinais")

            # Encontrar melhor resultado do grid
            valid_grid_results = [r for r in grid_results if 'signals_generated' in r and r['signals_generated'] > 0]

            if valid_grid_results:
                # Ordenar por número de sinais (atividade)
                best_grid = max(valid_grid_results, key=lambda x: x['signals_generated'])

                print(f"\n🏆 MELHOR RESULTADO DO GRID SEARCH:")
                print(f"   Sinais: {best_grid['signals_generated']}")
                print(f"   BUY: {best_grid['buy_signals']}, SELL: {best_grid['sell_signals']}")
                print(f"   Confidence: avg={best_grid.get('avg_confidence', 0):.3f}")
                print(f"   Parâmetros: {best_grid['params']}")

                # Usar os melhores parâmetros encontrados
                signals_generated = best_grid['signals_generated']
                buy_signals = best_grid['buy_signals']
                sell_signals = best_grid['sell_signals']

                print(f"\n🎯 PARÂMETROS OTIMIZADOS ENCONTRADOS:")
                for param, value in best_grid['params'].items():
                    print(f"   {param}: {value}")

            else:
                print(f"❌ Nenhuma configuração do grid gerou sinais")

        # 5. RESULTADOS FINAIS
        print(f"\n📊 RESULTADOS DA OTIMIZAÇÃO CIENTÍFICA:")
        print(f"=" * 50)
        print(f"🔢 Configurações testadas: {len(extreme_configs) + len(grid_combinations[:27])}")
        print(f"📡 Melhor resultado - Sinais: {signals_generated}")
        print(f"📈 Sinais BUY: {buy_signals}")
        print(f"📉 Sinais SELL: {sell_signals}")

        # Avaliar sucesso
        is_working = signals_generated > 5  # Pelo menos 5 sinais

        if is_working:
            print(f"\n🎉 OTIMIZAÇÃO CIENTÍFICA CONCLUÍDA COM SUCESSO!")
            print(f"✅ Parâmetros otimizados encontrados")
            print(f"✅ Estratégia gerando sinais ativos")
            print(f"🚀 Pronta para backtest com parâmetros otimizados")
        else:
            print(f"\n⚠️ Otimização não encontrou configuração ativa suficiente")
            print(f"🔧 Necessário expandir espaço de busca")

        return is_working
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Executa teste."""
    success = await test_fwh_with_real_data()
    
    if success:
        print(f"\n✅ TESTE CONCLUÍDO COM SUCESSO!")
        print(f"🚀 FWH funcionando com dados reais da API")
    else:
        print(f"\n🔧 AJUSTES NECESSÁRIOS")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print(f"\n🎉 FWH + API BINANCE FUNCIONANDO!")
    else:
        print(f"\n🔧 REVISAR CONFIGURAÇÃO")
