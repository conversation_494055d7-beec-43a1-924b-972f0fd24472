#!/usr/bin/env python3
"""
Análise Profunda da Estratégia QUALIA - Diagnóstico de Sharpe Negativo
YAA IMPLEMENTATION: Identifica por que o Sharpe médio ainda é negativo.
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import numpy as np
import requests
import json

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

class DeepStrategyAnalysis:
    """Análise profunda dos problemas da estratégia."""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'QUALIA-DeepAnalysis/1.0'})
    
    def fetch_data(self, symbol: str, days: int = 120) -> pd.DataFrame:
        """Busca dados para análise."""
        try:
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            params = {
                'symbol': symbol.replace('/', ''),
                'interval': '1h',
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            response = self.session.get("https://api.binance.com/api/v3/klines", params=params)
            response.raise_for_status()
            data = response.json()
            
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']].copy()
            
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df.set_index('timestamp', inplace=True)
            df = df.sort_index().dropna()
            
            # Adiciona indicadores
            df = self.add_indicators(df)
            
            return df
            
        except Exception as e:
            print(f"❌ Erro ao buscar dados: {e}")
            return pd.DataFrame()
    
    def add_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Adiciona indicadores técnicos."""
        
        # Retornos
        df['returns'] = df['close'].pct_change().fillna(0)
        
        # Médias móveis
        df['sma_10'] = df['close'].rolling(10).mean()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['ema_12'] = df['close'].ewm(span=12).mean()
        df['ema_26'] = df['close'].ewm(span=26).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        df['macd'] = df['ema_12'] - df['ema_26']
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(20).mean()
        bb_std = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # Volatilidade
        df['volatility'] = df['returns'].rolling(20).std()
        df['volume_sma'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        return df
    
    def analyze_signal_timing(self, df: pd.DataFrame) -> dict:
        """Analisa timing dos sinais vs retornos futuros."""
        
        if df.empty or len(df) < 100:
            return {'error': 'Dados insuficientes'}
        
        # Calcula sinais da estratégia atual
        signals = []
        
        for i in range(50, len(df)):
            current_data = df.iloc[i]
            
            # RSI Signal
            rsi = current_data['rsi']
            if pd.isna(rsi):
                rsi_signal = 0.0
            elif rsi > 70:
                rsi_signal = -1.0
            elif rsi < 30:
                rsi_signal = 1.0
            else:
                rsi_signal = (50 - rsi) / 20
            
            # MACD Signal
            macd = current_data['macd']
            macd_signal = current_data['macd_signal']
            if pd.isna(macd) or pd.isna(macd_signal):
                macd_momentum = 0.0
            else:
                macd_momentum = np.tanh((macd - macd_signal) * 1000)
            
            # Combined signal
            combined = (rsi_signal + macd_momentum) / 2.0
            signals.append(combined)
        
        # Retornos futuros
        future_returns_1h = []
        future_returns_4h = []
        future_returns_24h = []
        
        for i in range(50, len(df)):
            if i + 1 < len(df):
                ret_1h = (df['close'].iloc[i+1] / df['close'].iloc[i]) - 1
            else:
                ret_1h = 0
            
            if i + 4 < len(df):
                ret_4h = (df['close'].iloc[i+4] / df['close'].iloc[i]) - 1
            else:
                ret_4h = 0
                
            if i + 24 < len(df):
                ret_24h = (df['close'].iloc[i+24] / df['close'].iloc[i]) - 1
            else:
                ret_24h = 0
            
            future_returns_1h.append(ret_1h)
            future_returns_4h.append(ret_4h)
            future_returns_24h.append(ret_24h)
        
        # Correlações
        signals_series = pd.Series(signals)
        
        corr_1h = signals_series.corr(pd.Series(future_returns_1h))
        corr_4h = signals_series.corr(pd.Series(future_returns_4h))
        corr_24h = signals_series.corr(pd.Series(future_returns_24h))
        
        return {
            'signal_stats': {
                'mean': signals_series.mean(),
                'std': signals_series.std(),
                'positive_pct': (signals_series > 0).mean() * 100
            },
            'correlations': {
                '1h': corr_1h,
                '4h': corr_4h,
                '24h': corr_24h
            },
            'future_returns_stats': {
                '1h': {'mean': np.mean(future_returns_1h), 'std': np.std(future_returns_1h)},
                '4h': {'mean': np.mean(future_returns_4h), 'std': np.std(future_returns_4h)},
                '24h': {'mean': np.mean(future_returns_24h), 'std': np.std(future_returns_24h)}
            }
        }
    
    def analyze_transaction_costs(self, df: pd.DataFrame, cost_pct: float = 0.0002) -> dict:
        """Analisa impacto dos custos de transação."""
        
        if df.empty or len(df) < 100:
            return {'error': 'Dados insuficientes'}
        
        # Simula estratégia simples buy-and-hold
        buy_hold_return = (df['close'].iloc[-1] / df['close'].iloc[50]) - 1
        
        # Simula estratégia com diferentes frequências de trading
        results = {}
        
        for trade_freq in [0.1, 0.2, 0.5, 1.0, 2.0]:  # trades por período
            total_trades = len(df) * trade_freq / 100
            total_costs = total_trades * cost_pct
            
            # Retorno necessário para cobrir custos
            breakeven_return = total_costs
            
            results[f'freq_{trade_freq}'] = {
                'total_trades': total_trades,
                'total_costs_pct': total_costs * 100,
                'breakeven_return_pct': breakeven_return * 100
            }
        
        return {
            'buy_hold_return_pct': buy_hold_return * 100,
            'cost_analysis': results,
            'current_cost_pct': cost_pct * 100
        }
    
    def test_simple_strategies(self, df: pd.DataFrame) -> dict:
        """Testa estratégias simples para benchmark."""
        
        if df.empty or len(df) < 100:
            return {'error': 'Dados insuficientes'}
        
        strategies = {}
        
        # 1. Buy and Hold
        buy_hold_return = (df['close'].iloc[-1] / df['close'].iloc[50]) - 1
        strategies['buy_hold'] = {
            'return_pct': buy_hold_return * 100,
            'description': 'Compra e segura'
        }
        
        # 2. RSI Mean Reversion (simples)
        positions = []
        for i in range(50, len(df)):
            rsi = df['rsi'].iloc[i]
            if pd.isna(rsi):
                pos = 0
            elif rsi > 70:
                pos = -0.5  # Short
            elif rsi < 30:
                pos = 0.5   # Long
            else:
                pos = 0
            positions.append(pos)
        
        returns = []
        for i in range(1, len(positions)):
            if i < len(df) - 50:
                market_ret = (df['close'].iloc[50+i] / df['close'].iloc[50+i-1]) - 1
                strategy_ret = positions[i-1] * market_ret
                returns.append(strategy_ret)
        
        if returns:
            total_return = np.prod([1 + r for r in returns]) - 1
            strategies['rsi_simple'] = {
                'return_pct': total_return * 100,
                'description': 'RSI simples (70/30)'
            }
        
        # 3. Trend Following (simples)
        positions = []
        for i in range(50, len(df)):
            sma_10 = df['sma_10'].iloc[i]
            sma_20 = df['sma_20'].iloc[i]
            if pd.isna(sma_10) or pd.isna(sma_20):
                pos = 0
            elif sma_10 > sma_20:
                pos = 0.5  # Long
            else:
                pos = -0.5  # Short
            positions.append(pos)
        
        returns = []
        for i in range(1, len(positions)):
            if i < len(df) - 50:
                market_ret = (df['close'].iloc[50+i] / df['close'].iloc[50+i-1]) - 1
                strategy_ret = positions[i-1] * market_ret
                returns.append(strategy_ret)
        
        if returns:
            total_return = np.prod([1 + r for r in returns]) - 1
            strategies['trend_simple'] = {
                'return_pct': total_return * 100,
                'description': 'Trend following simples'
            }
        
        return strategies
    
    def run_deep_analysis(self, symbols: list = ["BTCUSDT", "ETHUSDT"]) -> dict:
        """Executa análise profunda completa."""
        
        print("🔍 ANÁLISE PROFUNDA DA ESTRATÉGIA QUALIA")
        print("=" * 60)
        
        results = {}
        
        for symbol in symbols:
            print(f"\n📊 Analisando {symbol}...")
            
            df = self.fetch_data(symbol)
            if df.empty:
                continue
            
            # Análise de timing
            timing_analysis = self.analyze_signal_timing(df)
            
            # Análise de custos
            cost_analysis = self.analyze_transaction_costs(df)
            
            # Estratégias benchmark
            benchmark_strategies = self.test_simple_strategies(df)
            
            results[symbol] = {
                'data_points': len(df),
                'period': f"{df.index[0]} to {df.index[-1]}",
                'timing_analysis': timing_analysis,
                'cost_analysis': cost_analysis,
                'benchmark_strategies': benchmark_strategies
            }
            
            # Relatório
            print(f"   📈 Período: {df.index[0].strftime('%Y-%m-%d')} a {df.index[-1].strftime('%Y-%m-%d')}")
            print(f"   📊 Dados: {len(df)} candles")
            
            if 'error' not in timing_analysis:
                print(f"   🎯 Correlação Sinais:")
                print(f"      • 1h: {timing_analysis['correlations']['1h']:.4f}")
                print(f"      • 4h: {timing_analysis['correlations']['4h']:.4f}")
                print(f"      • 24h: {timing_analysis['correlations']['24h']:.4f}")
            
            print(f"   💰 Benchmarks:")
            for name, data in benchmark_strategies.items():
                print(f"      • {data['description']}: {data['return_pct']:.2f}%")
            
            print(f"   💸 Análise de Custos:")
            print(f"      • Buy & Hold: {cost_analysis['buy_hold_return_pct']:.2f}%")
            print(f"      • Custo atual: {cost_analysis['current_cost_pct']:.3f}%")
        
        return results


if __name__ == "__main__":
    analyzer = DeepStrategyAnalysis()
    results = analyzer.run_deep_analysis()
    
    # Salva resultados
    output_file = Path("results/deep_strategy_analysis.json")
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Análise salva em: {output_file}")
    print(f"\n✅ Análise profunda concluída!")
