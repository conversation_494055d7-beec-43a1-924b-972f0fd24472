# TemporalPatternConfig


A classe `TemporalPatternConfig` centraliza os parâmetros do `TemporalPatternDetector`.
Ela lê valores padrão das variáveis de ambiente `QUALIA_*`, mas também pode ser
instanciada diretamente.

## Parâmetros

- `wavelet_depth` – profundidade máxima da análise wavelet.
- `use_quantum_transform` – habilita transformações quânticas quando `True`.
- `min_confidence` – limiar de confiança para aceitar padrões.
- `quantum_wavelet_shots` – número de execuções da transformada wavelet quântica.
- `qft_shots` – número de execuções da QFT.

## Exemplo de uso

```python
from src.qualia.config.temporal_detector_config import TemporalPatternConfig
from src.qualia.market.temporal_pattern_detector import MarketTemporalPatternDetector

cfg = TemporalPatternConfig(
    wavelet_depth=4,
    use_quantum_transform=True,
    min_confidence=0.7,
)

detector = MarketTemporalPatternDetector(config=cfg)
```

Um arquivo YAML com esses parâmetros pode ser salvo em `config/temporal_detector.yaml`
para documentar ajustes personalizados ou replicar configurações.

`TemporalPatternConfig` centraliza os parâmetros utilizados pelo `TemporalPatternDetector`. A configuração pode ser definida via variáveis de ambiente ou instanciada diretamente em código.

## Uso básico

```python
from src.qualia.config.temporal_detector_config import TemporalPatternConfig

config = TemporalPatternConfig(
    wavelet_depth=4,
    use_quantum_transform=True,
)
```

## Parâmetros

- `wavelet_depth`: profundidade máxima da decomposição wavelet
- `use_quantum_transform`: habilita análise via transformadas quânticas
- `min_confidence`: limiar mínimo para reportar padrões
- `quantum_wavelet_shots`: quantidade de *shots* ao simular a wavelet quântica
- `qft_shots`: quantidade de *shots* ao simular a QFT

Esses valores podem ser declarados em um arquivo YAML de referência:

```yaml
wavelet_depth: 3
use_quantum_transform: false
min_confidence: 0.65
quantum_wavelet_shots: 2048
qft_shots: 4096
```

Salve o arquivo como `config/temporal_detector.yaml` para consulta ou ajuste personalizado.

