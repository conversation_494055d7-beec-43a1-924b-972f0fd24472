#!/usr/bin/env python3
"""
QUALIA D-07.1 A/B Testing Framework - Teste Simplificado

Teste básico para validar componentes principais do framework A/B.
"""

import sys
import asyncio
import logging
from pathlib import Path
from datetime import datetime

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.ab_testing import (
    ABTestFramework, 
    ABTestConfig, 
    PerformanceComparator,
    DataQualityValidator,
    StatisticalAnalyzer,
    TestConfigManager
)
from qualia.utils.logging_config import get_qualia_logger

logger = get_qualia_logger(__name__)


async def test_basic_components():
    """Teste básico dos componentes principais."""
    logger.info("🧪 Testando componentes básicos...")
    
    results = []
    
    # 1. Teste ABTestConfig
    try:
        config = ABTestConfig(
            test_name="Basic Test",
            duration_hours=1.0,
            symbols=["BTC/USDT"],
            initial_capital=10000.0
        )
        assert config.test_id is not None
        assert config.test_name == "Basic Test"
        results.append(("ABTestConfig", True))
        logger.info("✅ ABTestConfig: OK")
    except Exception as e:
        results.append(("ABTestConfig", False))
        logger.error(f"❌ ABTestConfig: {e}")
    
    # 2. Teste PerformanceComparator
    try:
        comparator = PerformanceComparator()
        sim_metrics = comparator.initialize_session("test_sim", "simulator")
        live_metrics = comparator.initialize_session("test_live", "live")
        
        assert sim_metrics.session_type == "simulator"
        assert live_metrics.session_type == "live"
        results.append(("PerformanceComparator", True))
        logger.info("✅ PerformanceComparator: OK")
    except Exception as e:
        results.append(("PerformanceComparator", False))
        logger.error(f"❌ PerformanceComparator: {e}")
    
    # 3. Teste DataQualityValidator
    try:
        validator = DataQualityValidator()
        metrics = validator.initialize_validation("test_session")
        
        assert metrics.session_id == "test_session"
        assert metrics.total_price_comparisons == 0
        results.append(("DataQualityValidator", True))
        logger.info("✅ DataQualityValidator: OK")
    except Exception as e:
        results.append(("DataQualityValidator", False))
        logger.error(f"❌ DataQualityValidator: {e}")
    
    # 4. Teste StatisticalAnalyzer
    try:
        analyzer = StatisticalAnalyzer()
        
        # Teste básico de intervalo de confiança
        data = [1.0, 2.0, 3.0, 4.0, 5.0]
        ci = analyzer.calculate_confidence_interval(data, 0.95)
        
        assert len(ci) == 2
        assert ci[0] < ci[1]
        results.append(("StatisticalAnalyzer", True))
        logger.info("✅ StatisticalAnalyzer: OK")
    except Exception as e:
        results.append(("StatisticalAnalyzer", False))
        logger.error(f"❌ StatisticalAnalyzer: {e}")
    
    # 5. Teste TestConfigManager
    try:
        manager = TestConfigManager()
        
        # Criar configuração simples
        config = await manager.create_test_configuration(
            symbols=["BTC/USDT"],
            strategy_config={"name": "test", "type": "momentum"},
            risk_params={"initial_capital": 10000.0},
            config_name="Test Config"
        )
        
        assert config is not None
        assert config.symbols == ["BTC/USDT"]
        results.append(("TestConfigManager", True))
        logger.info("✅ TestConfigManager: OK")
    except Exception as e:
        results.append(("TestConfigManager", False))
        logger.error(f"❌ TestConfigManager: {e}")
    
    # 6. Teste ABTestFramework (inicialização apenas)
    try:
        config = ABTestConfig(
            test_name="Framework Test",
            duration_hours=0.1,
            symbols=["BTC/USDT"],
            initial_capital=10000.0
        )
        
        framework = ABTestFramework(config)
        
        assert framework.config.test_id is not None
        assert framework.status.value == "pending"
        results.append(("ABTestFramework", True))
        logger.info("✅ ABTestFramework: OK")
    except Exception as e:
        results.append(("ABTestFramework", False))
        logger.error(f"❌ ABTestFramework: {e}")
    
    return results


async def test_data_structures():
    """Teste das estruturas de dados."""
    logger.info("🧪 Testando estruturas de dados...")
    
    results = []
    
    # Teste PriceFeedComparison
    try:
        from qualia.ab_testing.data_quality_validator import PriceFeedComparison
        
        comparison = PriceFeedComparison(
            symbol="BTC/USDT",
            timestamp=datetime.now(),
            simulator_price=50000.0,
            live_price=50005.0,
            price_difference=5.0,
            price_difference_pct=0.01,
            simulator_timestamp=datetime.now(),
            live_timestamp=datetime.now(),
            timestamp_lag_ms=100.0
        )
        
        assert comparison.symbol == "BTC/USDT"
        assert comparison.price_difference == 5.0
        results.append(("PriceFeedComparison", True))
        logger.info("✅ PriceFeedComparison: OK")
    except Exception as e:
        results.append(("PriceFeedComparison", False))
        logger.error(f"❌ PriceFeedComparison: {e}")
    
    # Teste ExecutionComparison
    try:
        from qualia.ab_testing.data_quality_validator import ExecutionComparison
        
        execution = ExecutionComparison(
            order_id="test_order",
            symbol="BTC/USDT",
            side="buy",
            quantity=0.1,
            expected_price=50000.0,
            order_timestamp=datetime.now()
        )
        
        assert execution.order_id == "test_order"
        assert execution.quantity == 0.1
        results.append(("ExecutionComparison", True))
        logger.info("✅ ExecutionComparison: OK")
    except Exception as e:
        results.append(("ExecutionComparison", False))
        logger.error(f"❌ ExecutionComparison: {e}")
    
    # Teste TradeResult
    try:
        from qualia.ab_testing.performance_comparator import TradeResult
        
        trade = TradeResult(
            timestamp=datetime.now(),
            symbol="BTC/USDT",
            side="buy",
            entry_price=50000.0,
            exit_price=51000.0,
            quantity=0.1,
            pnl=100.0,
            pnl_pct=2.0,
            duration_seconds=3600
        )
        
        assert trade.symbol == "BTC/USDT"
        assert trade.pnl == 100.0
        results.append(("TradeResult", True))
        logger.info("✅ TradeResult: OK")
    except Exception as e:
        results.append(("TradeResult", False))
        logger.error(f"❌ TradeResult: {e}")
    
    return results


async def main():
    """Função principal de teste."""
    logger.info("🚀 Iniciando testes simplificados do D-07.1")
    
    all_results = []
    
    # Teste componentes básicos
    basic_results = await test_basic_components()
    all_results.extend(basic_results)
    
    # Teste estruturas de dados
    data_results = await test_data_structures()
    all_results.extend(data_results)
    
    # Resumo
    logger.info("\n" + "="*50)
    logger.info("📋 RESUMO DOS TESTES D-07.1")
    logger.info("="*50)
    
    passed = sum(1 for _, result in all_results if result)
    total = len(all_results)
    
    for test_name, result in all_results:
        status = "✅ PASSOU" if result else "❌ FALHOU"
        logger.info(f"{status}: {test_name}")
    
    logger.info(f"\nTaxa de Sucesso: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        logger.info("🎉 Todos os testes passaram! D-07.1 implementado com sucesso!")
        return True
    else:
        logger.error(f"❌ {total-passed} testes falharam.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
