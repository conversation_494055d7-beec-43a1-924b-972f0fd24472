# QUALIA Live Feed System

Sistema robusto de coleta de dados de mercado em tempo real para o QUALIA, com foco inicial no KuCoin.

## 🚀 Características

- **Multi-Exchange Support**: Arquitetura extensível para múltiplas exchanges
- **Real-time Data**: WebSocket + REST API fallback
- **Data Aggregation**: Agregação inteligente de múltiplas fontes
- **Auto-Reconnection**: Reconexão automática com circuit breaker
- **Data Normalization**: Formato padronizado para todos os dados
- **Health Monitoring**: Monitoramento contínuo de saúde das conexões
- **Alert System**: Sistema de alertas para anomalias

## 📁 Estrutura

```
live_feed/
├── __init__.py              # Exports principais
├── feed_manager.py          # Coordenador principal
├── kucoin_feed.py          # Feed específico do KuCoin
├── feed_aggregator.py      # Agregação de múltiplos feeds
├── data_normalizer.py      # Normalização de dados
├── example_usage.py        # Exemplo de uso
└── README.md               # Esta documentação
```

## 🔧 Componentes

### FeedManager
Coordenador principal que gerencia múltiplos feeds, monitora saúde e fornece interface unificada.

### KuCoinFeed
Feed específico do KuCoin com suporte a:
- WebSocket para dados em tempo real
- REST API como fallback
- Reconexão automática
- Cache local de dados

### FeedAggregator
Agrega dados de múltiplas fontes com:
- Remoção de duplicatas
- Cálculo de médias ponderadas
- Detecção de anomalias
- Score de confiança

### DataNormalizer
Normaliza dados de diferentes exchanges para formato padrão QUALIA.

## 🚀 Uso Básico

```python
import asyncio
from qualia.live_feed import FeedManager

async def main():
    # Configuração
    config = {
        'exchanges': {
            'kucoin': {
                'api_key': 'your_api_key',
                'api_secret': 'your_api_secret', 
                'password': 'your_passphrase',
            }
        }
    }
    
    # Símbolos para monitorar
    symbols = ['BTC-USDT', 'ETH-USDT', 'ADA-USDT']
    
    # Criar feed manager
    feed_manager = FeedManager(
        config=config,
        symbols=symbols,
        enable_kucoin=True,
        aggregation_enabled=True
    )
    
    # Configurar callbacks
    def on_ticker(ticker):
        print(f"{ticker.symbol}: ${ticker.price:.4f}")
    
    feed_manager.set_ticker_callback(on_ticker)
    
    # Iniciar
    if await feed_manager.start():
        print("Feed iniciado com sucesso!")
        
        # Executar por 60 segundos
        await asyncio.sleep(60)
        
        # Parar
        await feed_manager.stop()

# Executar
asyncio.run(main())
```

## 🔑 Configuração

### Variáveis de Ambiente

```bash
# KuCoin API Credentials
export KUCOIN_API_KEY="your_api_key"
export KUCOIN_API_SECRET="your_api_secret"  
export KUCOIN_PASSPHRASE="your_passphrase"

# Timeouts (opcional)
export OHLCV_TIMEOUT="45.0"
export TICKER_TIMEOUT="25.0"
```

### Configuração via Código

```python
config = {
    'exchanges': {
        'kucoin': {
            'api_key': 'your_api_key',
            'api_secret': 'your_api_secret',
            'password': 'your_passphrase',
            'sandbox': False,
            'timeout': 30.0,
            'rate_limit': 4.0,
        }
    }
}
```

## 📊 Dados Disponíveis

### Ticker Agregado
```python
@dataclass
class AggregatedTicker:
    symbol: str
    price: float
    price_weighted: float      # Preço ponderado por volume
    bid: float
    ask: float
    spread: float
    volume_24h: float
    change_24h: float
    change_24h_percent: float
    high_24h: float
    low_24h: float
    timestamp: float
    sources: List[str]         # Fontes dos dados
    source_count: int
    price_variance: float      # Variância entre fontes
    confidence_score: float    # Score de confiança (0-1)
```

### Order Book Agregado
```python
@dataclass
class AggregatedOrderBook:
    symbol: str
    bids: List[List[float]]    # [[price, size], ...]
    asks: List[List[float]]    # [[price, size], ...]
    best_bid: float
    best_ask: float
    spread: float
    timestamp: float
    sources: List[str]
    source_count: int
    depth_score: float         # Score de profundidade (0-1)
```

## 🔍 Monitoramento

### Status do Sistema
```python
status = feed_manager.get_system_status()
print(f"Feeds ativos: {status.active_feeds}")
print(f"Símbolos agregados: {status.aggregated_symbols}")
print(f"Total de dados: {status.total_data_points}")
```

### Estatísticas
```python
stats = feed_manager.get_statistics()
print(f"Tickers processados: {stats['tickers_processed']}")
print(f"Erros: {stats['errors']}")
print(f"Alertas: {stats['alerts']}")
```

## 🚨 Sistema de Alertas

O sistema monitora automaticamente:

- **High Price Variance**: Variância alta entre fontes
- **Feed Stale**: Feed sem dados por muito tempo
- **Feed Error**: Erros de conexão ou dados
- **Connection Issues**: Problemas de conectividade

```python
def on_alert(alert_type, alert_data):
    if alert_type == 'high_price_variance':
        print(f"⚠️ Alta variância para {alert_data['symbol']}")
    elif alert_type == 'feed_stale':
        print(f"⚠️ Feed {alert_data['feed']} sem dados")

feed_manager.set_system_alert_callback(on_alert)
```

## 🧪 Testes

Execute o exemplo para testar o sistema:

```bash
cd src/qualia/live_feed
python example_usage.py
```

## 🔧 Extensibilidade

### Adicionar Nova Exchange

1. Criar classe feed específica (ex: `binance_feed.py`)
2. Implementar interface padrão com callbacks
3. Adicionar normalização de dados
4. Registrar no FeedManager

### Personalizar Agregação

```python
aggregator = FeedAggregator(
    max_age_seconds=5.0,           # Dados mais frescos
    min_sources=2,                 # Mínimo 2 fontes
    price_variance_threshold=0.02, # 2% de variância
    volume_weight_factor=0.8,      # Mais peso no volume
)
```

## 📈 Performance

- **Latência**: < 100ms para dados WebSocket
- **Throughput**: > 1000 updates/segundo
- **Memory**: ~50MB para 10 símbolos
- **CPU**: < 5% em operação normal

## 🔒 Segurança

- Credenciais via variáveis de ambiente
- Rate limiting automático
- Circuit breaker para proteção
- Logs sanitizados (sem credenciais)

## 🐛 Troubleshooting

### Problemas Comuns

1. **Sem dados recebidos**
   - Verificar credenciais da API
   - Verificar conectividade de rede
   - Verificar logs para erros

2. **Alta latência**
   - Verificar qualidade da conexão
   - Ajustar timeouts
   - Considerar usar REST fallback

3. **Reconexões frequentes**
   - Verificar estabilidade da rede
   - Ajustar parâmetros de reconexão
   - Verificar rate limits da exchange

### Logs Úteis

```python
import logging
logging.getLogger('qualia.live_feed').setLevel(logging.DEBUG)
```

## 🚀 Próximos Passos

- [ ] Suporte a Binance
- [ ] Suporte a Coinbase Pro  
- [ ] Métricas avançadas
- [ ] Dashboard web
- [ ] Persistência de dados
- [ ] Backtesting com dados live
