# Relatórios de Análise

Este diretório reúne auditorias técnicas e revisões de integração do QUALIA. As anotações em cada documento seguem o mesmo padrão de tarefas com checkboxes, vinculadas às issues descritas em [../ISSUE_TRACKER.md](../ISSUE_TRACKER.md).
Todos os módulos consideram símbolos no formato ``BASE/QUOTE`` (ex.: ``BTC/USD``). Dados históricos podem estar em arquivos como ``BTCUSDT_5m.csv``. Apesar de não incluírem a barra, no código os pares devem sempre ser informados dessa forma.

## Ordem sugerida de leitura
1. [integration_overview.md](integration_overview.md) – visão geral das recomendações e do status de cada integração.
2. Relatórios `integration_*_double_check.md` – detalhes de cada módulo com suas respectivas tarefas.
3. [decision_context_review.md](decision_context_review.md) – justificativas das decisões técnicas tomadas.
4. [strategies_audit.md](strategies_audit.md) – avaliação das estratégias implementadas.
5. [stability_analysis_report.md](stability_analysis_report.md) – análise de estabilidade da massa informacional.

## Sistema de acompanhamento
Os itens marcados com `- [ ]` ou `- [x]` nos relatórios representam tarefas a serem acompanhadas no rastreador de issues. Cada checklist possui referências às issues correspondentes, permitindo monitorar o avanço de forma centralizada. Quando uma tarefa é concluída, o checkbox é marcado e o status é atualizado no arquivo de issues.

## Formato de Quick Wins e Features de Valor

Todos os relatórios de auditoria devem conter duas seções padronizadas:

1. **Quick Wins ⚡** – lista em formato de checkbox com referência ao número da issue. Exemplo:
   ```markdown
   - [ ] #18 Descrição objetiva do ganho rápido.
   - [x] #21 Tarefa já finalizada.
   ```
2. **Features de Valor** – itens numerados descrevendo a funcionalidade, com *User Story* e *Estimativa* de esforço.
   ```markdown
   1. **Nome da Feature**
      - *User Story*: descrição do benefício ao usuário.
      - *Estimativa*: X dias.
   ```
