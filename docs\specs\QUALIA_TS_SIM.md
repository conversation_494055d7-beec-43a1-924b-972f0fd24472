# Camada Ô_SIM

A camada **Ô_SIM** (Observação–Simbolização–Antecipação–Injeção de Mercado) funciona como o operador `OperatorSIM`, conectando intenções simbólicas ao universo quântico do QUALIA. Ela sintetiza sinais observados em tokens e injeta ajustes conforme a interpretação desses símbolos.

## Esquema de fluxo

```mermaid
flowchart LR
    A[observe] --> B[simbolize]
    B --> C[antecipar]
    C --> D[injetar]
```

1. **observe** – captura de padrões pelo `IntentioWaveletEngine`.
2. **simbolize** – geração de tokens ASCII representando intenções.
3. **antecipar** – leitura dos tokens pelo `SymbolicIntentionOperator`.
4. **injetar** – modulação dos parâmetros do `QUALIAQuantumUniverse`.

### Amplificação Criativa Ativa

O operador `apply_emergence` executa a *amplificação criativa ativa* logo antes
de `forecast_future_state`, potencializando padrões emergentes. Consulte
[`emergence_operator.md`](emergence_operator.md) e
[`retrocausality_operator.md`](retrocausality_operator.md) para mais detalhes.

## Módulos relacionados

 - [`intention.py`](../../src/qualia/core/intention.py) – implementa o `IntentioWaveletEngine`, o `HotspotDetector` e o `Base64PatternEncoder` para análise wavelet e geração de tokens.
- [`symbolic_intention_operator.py`](../../src/qualia/core/symbolic_intention_operator.py) – interpreta tokens e ajusta o universo quântico.

- [`operator_sim.py`](../../src/qualia/core/operator_sim.py) – implementa `OperatorSIM` e `QuantumSymbolicTokenizer` (importável via `qualia.core.operator_sim.QuantumSymbolicTokenizer`).
### Exemplo conceitual

```python
from qualia.core.intention import IntentioWaveletEngine, HotspotDetector
from qualia.core.operator_sim import QuantumSymbolicTokenizer, OperatorSIM
from qualia.core.symbolic_intention_operator import SymbolicIntentionOperator
from qualia.core.universe import QUALIAQuantumUniverse

engine = IntentioWaveletEngine()
detector = HotspotDetector()
tokenizer = QuantumSymbolicTokenizer()
operator = SymbolicIntentionOperator()
o_sim = OperatorSIM(operator)

field = ...  # matriz de intenções observadas
energies = engine.analyze_field(field)        # observe
hotspots = detector.detect(energies)
tokens = tokenizer.tokenize(hotspots)         # simbolize
for t in tokens:
    o_sim.listen(t)                           # antecipar

universe = QUALIAQuantumUniverse()
o_sim.modulate_field(universe)                # injetar
```

A inicialização da camada depende do parâmetro `init_symbolic_processor` em `config/consciousness_defaults.yaml`. Quando definido como `true`, o `QUALIAConsciousness` ativará automaticamente o processamento simbólico.
Consulte também a [configuração da Ô_SIM](../api/consciousness_config.md#exemplo-habilitando-a-camada-Ô_SIM) para habilitar a camada automaticamente.
