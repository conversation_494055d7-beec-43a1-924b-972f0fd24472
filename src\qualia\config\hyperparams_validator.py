"""
Hyperparameters Validation and Calibration System for QUALIA
Provides comprehensive validation, calibration, and monitoring of hyperparameters.
"""

import logging
import json
import time
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import yaml

from src.qualia.utils.logging_integration import get_component_logger

logger = get_component_logger("hyperparams_validator")


@dataclass
class ValidationRule:
    """Represents a validation rule for hyperparameters"""
    name: str
    condition: str
    error_message: str
    warning_threshold: Optional[float] = None
    critical: bool = False


@dataclass
class ValidationResult:
    """Result of hyperparameter validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    critical_violations: List[str]
    recommendations: List[str]


@dataclass
class CalibrationMetrics:
    """Metrics for hyperparameter calibration"""
    timestamp: float
    performance_score: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    avg_confidence: float
    false_positive_rate: float
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class HyperparametersValidator:
    """Validates and calibrates QUALIA hyperparameters"""
    
    def __init__(self, config_path: Optional[Path] = None):
        self.config_path = config_path or Path("qualia/config/hyperparams.yaml")
        self.validation_rules = []
        self.calibration_history = []
        self.performance_thresholds = {
            'min_sharpe_ratio': 0.5,
            'max_drawdown': 0.15,
            'min_win_rate': 0.45,
            'min_performance_score': 0.3
        }
        self._load_validation_rules()
    
    def _load_validation_rules(self):
        """Load validation rules from configuration"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                validation_config = config.get('validation', {})
                rules_config = validation_config.get('rules', [])
                
                for rule_config in rules_config:
                    rule = ValidationRule(
                        name=rule_config['name'],
                        condition=rule_config['condition'],
                        error_message=rule_config['error'],
                        warning_threshold=rule_config.get('warning_threshold'),
                        critical=rule_config.get('critical', False)
                    )
                    self.validation_rules.append(rule)
                
                logger.info(f"✅ Loaded {len(self.validation_rules)} validation rules")
            else:
                logger.warning(f"⚠️ Config file not found: {self.config_path}")
                self._create_default_rules()
                
        except Exception as e:
            logger.error(f"❌ Error loading validation rules: {e}")
            self._create_default_rules()
    
    def _create_default_rules(self):
        """Create default validation rules"""
        self.validation_rules = [
            ValidationRule(
                name="price_amplification_bounds",
                condition="1.0 <= price_amplification <= 10.0",
                error_message="price_amplification must be between 1.0 and 10.0",
                critical=True
            ),
            ValidationRule(
                name="news_amplification_bounds",
                condition="1.0 <= news_amplification <= 15.0",
                error_message="news_amplification must be between 1.0 and 15.0",
                critical=True
            ),
            ValidationRule(
                name="min_confidence_bounds",
                condition="0.1 <= min_confidence <= 0.9",
                error_message="min_confidence must be between 0.1 and 0.9",
                critical=True
            ),
            ValidationRule(
                name="pattern_threshold_bounds",
                condition="0.1 <= pattern_threshold <= 0.8",
                error_message="pattern_threshold must be between 0.1 and 0.8",
                critical=True
            ),
            ValidationRule(
                name="amplification_balance_warning",
                condition="price_amplification >= news_amplification * 0.1",
                error_message="price_amplification is extremely low compared to news_amplification",
                warning_threshold=0.2,
                critical=False
            ),
            ValidationRule(
                name="high_risk_price_amplification",
                condition="price_amplification <= 8.0",
                error_message="price_amplification is very high (>8.0) - high risk",
                critical=False
            ),
            ValidationRule(
                name="high_risk_news_amplification",
                condition="news_amplification <= 12.0",
                error_message="news_amplification is very high (>12.0) - high risk",
                critical=False
            )
        ]
        logger.info("📋 Created default validation rules")
    
    def validate_hyperparameters(self, hyperparams: Dict[str, Any]) -> ValidationResult:
        """
        Validate hyperparameters against defined rules
        
        Args:
            hyperparams: Dictionary of hyperparameters to validate
            
        Returns:
            ValidationResult with validation status and details
        """
        errors = []
        warnings = []
        critical_violations = []
        recommendations = []
        
        logger.info("🔍 Validating hyperparameters...")
        
        for rule in self.validation_rules:
            try:
                # Evaluate condition
                condition_result = self._evaluate_condition(rule.condition, hyperparams)
                
                if not condition_result:
                    if rule.critical:
                        critical_violations.append(rule.error_message)
                        errors.append(f"CRITICAL: {rule.error_message}")
                    else:
                        warnings.append(rule.error_message)
                    
                    logger.warning(f"⚠️ Rule violation: {rule.name} - {rule.error_message}")
                else:
                    logger.debug(f"✅ Rule passed: {rule.name}")
                    
            except Exception as e:
                error_msg = f"Error evaluating rule {rule.name}: {e}"
                errors.append(error_msg)
                logger.error(f"❌ {error_msg}")
        
        # Generate recommendations
        recommendations.extend(self._generate_recommendations(hyperparams))
        
        # Check for dangerous combinations
        dangerous_combinations = self._check_dangerous_combinations(hyperparams)
        if dangerous_combinations:
            warnings.extend(dangerous_combinations)
        
        is_valid = len(critical_violations) == 0
        
        result = ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            critical_violations=critical_violations,
            recommendations=recommendations
        )
        
        # Log summary
        if is_valid:
            logger.info(f"✅ Hyperparameters validation passed ({len(warnings)} warnings)")
        else:
            logger.error(f"❌ Hyperparameters validation failed ({len(critical_violations)} critical violations)")
        
        return result
    
    def _evaluate_condition(self, condition: str, hyperparams: Dict[str, Any]) -> bool:
        """Safely evaluate a condition string with hyperparameters"""
        try:
            # Create safe namespace with hyperparameters
            namespace = {
                'price_amplification': hyperparams.get('price_amplification', 0),
                'news_amplification': hyperparams.get('news_amplification', 0),
                'min_confidence': hyperparams.get('min_confidence', 0),
                'pattern_threshold': hyperparams.get('pattern_threshold', 0),
                'learning_rate': hyperparams.get('learning_rate', 0),
            }
            
            # Evaluate condition
            return eval(condition, {"__builtins__": {}}, namespace)
            
        except Exception as e:
            logger.error(f"Error evaluating condition '{condition}': {e}")
            return False
    
    def _generate_recommendations(self, hyperparams: Dict[str, Any]) -> List[str]:
        """Generate recommendations for hyperparameter optimization"""
        recommendations = []
        
        price_amp = hyperparams.get('price_amplification', 0)
        news_amp = hyperparams.get('news_amplification', 0)
        min_conf = hyperparams.get('min_confidence', 0)
        
        # Check for extreme imbalances
        if news_amp > price_amp * 5:
            recommendations.append(
                f"Consider increasing price_amplification ({price_amp}) or decreasing "
                f"news_amplification ({news_amp}) for better balance"
            )
        
        # Check for overly conservative settings
        if min_conf > 0.8:
            recommendations.append(
                f"min_confidence ({min_conf}) is very high - may miss trading opportunities"
            )
        
        # Check for overly aggressive settings
        if min_conf < 0.3:
            recommendations.append(
                f"min_confidence ({min_conf}) is very low - may generate false signals"
            )
        
        # Check for production-optimized values
        if abs(price_amp - 1.0) < 0.1 and abs(news_amp - 10.0) < 0.5:
            recommendations.append(
                "Current settings match production-optimized values from Bayesian optimization"
            )
        
        return recommendations
    
    def _check_dangerous_combinations(self, hyperparams: Dict[str, Any]) -> List[str]:
        """Check for dangerous parameter combinations"""
        warnings = []
        
        price_amp = hyperparams.get('price_amplification', 0)
        news_amp = hyperparams.get('news_amplification', 0)
        min_conf = hyperparams.get('min_confidence', 0)
        
        # Very high amplification with low confidence
        if (price_amp > 7.0 or news_amp > 10.0) and min_conf < 0.4:
            warnings.append(
                "High amplification with low confidence threshold - high risk of false signals"
            )
        
        # Very low amplification with high confidence
        if (price_amp < 2.0 and news_amp < 2.0) and min_conf > 0.7:
            warnings.append(
                "Low amplification with high confidence threshold - may miss opportunities"
            )
        
        return warnings
    
    def calibrate_hyperparameters(self, 
                                 current_params: Dict[str, Any],
                                 performance_metrics: CalibrationMetrics) -> Dict[str, Any]:
        """
        Calibrate hyperparameters based on performance metrics
        
        Args:
            current_params: Current hyperparameter values
            performance_metrics: Performance metrics for calibration
            
        Returns:
            Calibrated hyperparameters
        """
        logger.info("🎯 Starting hyperparameter calibration...")
        
        # Store metrics in history
        self.calibration_history.append(performance_metrics)
        
        # Keep only recent history (last 100 entries)
        if len(self.calibration_history) > 100:
            self.calibration_history = self.calibration_history[-100:]
        
        calibrated_params = current_params.copy()
        
        # Analyze performance trends
        if len(self.calibration_history) >= 5:
            recent_metrics = self.calibration_history[-5:]
            
            # Calculate trends
            performance_trend = self._calculate_trend([m.performance_score for m in recent_metrics])
            sharpe_trend = self._calculate_trend([m.sharpe_ratio for m in recent_metrics])
            drawdown_trend = self._calculate_trend([m.max_drawdown for m in recent_metrics])
            
            # Calibrate based on trends
            calibrated_params = self._apply_calibration_adjustments(
                calibrated_params, performance_trend, sharpe_trend, drawdown_trend, performance_metrics
            )
        
        # Validate calibrated parameters
        validation_result = self.validate_hyperparameters(calibrated_params)
        
        if not validation_result.is_valid:
            logger.warning("⚠️ Calibrated parameters failed validation, reverting to original")
            return current_params
        
        # Log calibration results
        changes = []
        for key, value in calibrated_params.items():
            if key in current_params and current_params[key] != value:
                changes.append(f"{key}: {current_params[key]:.3f} -> {value:.3f}")
        
        if changes:
            logger.info(f"🔧 Hyperparameter calibration applied: {', '.join(changes)}")
        else:
            logger.info("📊 No calibration adjustments needed")
        
        return calibrated_params
    
    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate trend direction (-1 to 1)"""
        if len(values) < 2:
            return 0.0
        
        # Simple linear trend calculation
        x = np.arange(len(values))
        y = np.array(values)
        
        try:
            slope = np.polyfit(x, y, 1)[0]
            # Normalize slope to -1 to 1 range
            return np.clip(slope * len(values), -1.0, 1.0)
        except:
            return 0.0
    
    def _apply_calibration_adjustments(self, 
                                     params: Dict[str, Any],
                                     performance_trend: float,
                                     sharpe_trend: float,
                                     drawdown_trend: float,
                                     current_metrics: CalibrationMetrics) -> Dict[str, Any]:
        """Apply calibration adjustments based on trends"""
        
        adjusted_params = params.copy()
        
        # Adjustment factors (small, conservative changes)
        adjustment_factor = 0.05  # 5% maximum adjustment
        
        # If performance is declining, be more conservative
        if performance_trend < -0.3:
            # Increase confidence threshold slightly
            current_conf = adjusted_params.get('min_confidence', 0.6)
            adjusted_params['min_confidence'] = min(current_conf + adjustment_factor, 0.8)
            
            # Reduce amplification slightly
            current_price_amp = adjusted_params.get('price_amplification', 1.0)
            adjusted_params['price_amplification'] = max(current_price_amp - adjustment_factor, 1.0)
            
            logger.info("📉 Performance declining - applying conservative adjustments")
        
        # If performance is improving, allow slight optimization
        elif performance_trend > 0.3 and current_metrics.performance_score > 0.5:
            # Slightly decrease confidence threshold if performance is good
            current_conf = adjusted_params.get('min_confidence', 0.6)
            adjusted_params['min_confidence'] = max(current_conf - adjustment_factor/2, 0.3)
            
            logger.info("📈 Performance improving - applying optimization adjustments")
        
        # If drawdown is increasing, be more conservative
        if drawdown_trend > 0.2 and current_metrics.max_drawdown > 0.1:
            current_conf = adjusted_params.get('min_confidence', 0.6)
            adjusted_params['min_confidence'] = min(current_conf + adjustment_factor, 0.8)
            
            logger.info("⚠️ Drawdown increasing - applying risk reduction")
        
        return adjusted_params
    
    def get_calibration_summary(self) -> Dict[str, Any]:
        """Get summary of calibration history and current status"""
        if not self.calibration_history:
            return {"status": "no_data", "message": "No calibration data available"}
        
        recent_metrics = self.calibration_history[-10:] if len(self.calibration_history) >= 10 else self.calibration_history
        
        summary = {
            "status": "active",
            "total_calibrations": len(self.calibration_history),
            "recent_performance": {
                "avg_performance_score": np.mean([m.performance_score for m in recent_metrics]),
                "avg_sharpe_ratio": np.mean([m.sharpe_ratio for m in recent_metrics]),
                "avg_max_drawdown": np.mean([m.max_drawdown for m in recent_metrics]),
                "avg_win_rate": np.mean([m.win_rate for m in recent_metrics]),
            },
            "performance_thresholds": self.performance_thresholds,
            "last_calibration": self.calibration_history[-1].to_dict() if self.calibration_history else None
        }
        
        return summary


class HyperparametersMonitor:
    """Monitors hyperparameters and triggers automatic calibration"""

    def __init__(self, validator: HyperparametersValidator,
                 calibration_interval: int = 300,  # 5 minutes
                 min_samples_for_calibration: int = 10):
        self.validator = validator
        self.calibration_interval = calibration_interval
        self.min_samples_for_calibration = min_samples_for_calibration
        self.last_calibration_time = 0
        self.performance_samples = []
        self.current_hyperparams = {}
        self.logger = get_component_logger("hyperparams_monitor")

    def update_performance(self, metrics: CalibrationMetrics):
        """Update performance metrics for monitoring"""
        self.performance_samples.append(metrics)

        # Keep only recent samples
        if len(self.performance_samples) > 100:
            self.performance_samples = self.performance_samples[-100:]

        # Check if calibration is needed
        if self._should_calibrate():
            self._trigger_calibration()

    def _should_calibrate(self) -> bool:
        """Determine if calibration should be triggered"""
        current_time = time.time()

        # Check time interval
        if current_time - self.last_calibration_time < self.calibration_interval:
            return False

        # Check minimum samples
        if len(self.performance_samples) < self.min_samples_for_calibration:
            return False

        # Check performance degradation
        if len(self.performance_samples) >= 5:
            recent_performance = [m.performance_score for m in self.performance_samples[-5:]]
            avg_recent = np.mean(recent_performance)

            if avg_recent < 0.3:  # Performance threshold
                self.logger.warning("⚠️ Performance degradation detected, triggering calibration")
                return True

        return True

    def _trigger_calibration(self):
        """Trigger automatic calibration"""
        if not self.current_hyperparams:
            self.logger.warning("⚠️ No current hyperparameters set, skipping calibration")
            return

        self.logger.info("🎯 Triggering automatic hyperparameter calibration")

        # Use latest metrics for calibration
        latest_metrics = self.performance_samples[-1]

        # Calibrate hyperparameters
        calibrated_params = self.validator.calibrate_hyperparameters(
            self.current_hyperparams, latest_metrics
        )

        # Update current hyperparameters
        self.current_hyperparams = calibrated_params
        self.last_calibration_time = time.time()

        self.logger.info("✅ Automatic calibration completed")

    def set_current_hyperparams(self, hyperparams: Dict[str, Any]):
        """Set current hyperparameters for monitoring"""
        self.current_hyperparams = hyperparams.copy()

        # Validate new hyperparameters
        validation_result = self.validator.validate_hyperparameters(hyperparams)

        if not validation_result.is_valid:
            self.logger.error("❌ Invalid hyperparameters set for monitoring")
            for error in validation_result.critical_violations:
                self.logger.error(f"   CRITICAL: {error}")
        else:
            self.logger.info("✅ Hyperparameters set for monitoring")

    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        return {
            "samples_collected": len(self.performance_samples),
            "last_calibration": self.last_calibration_time,
            "time_since_calibration": time.time() - self.last_calibration_time,
            "next_calibration_in": max(0, self.calibration_interval - (time.time() - self.last_calibration_time)),
            "current_hyperparams": self.current_hyperparams.copy(),
            "calibration_interval": self.calibration_interval,
            "min_samples_required": self.min_samples_for_calibration
        }


def validate_hyperparameters_from_config(config_path: Path, hyperparams: Dict[str, Any]) -> ValidationResult:
    """Convenience function to validate hyperparameters from config file"""
    validator = HyperparametersValidator(config_path)
    return validator.validate_hyperparameters(hyperparams)


def create_hyperparams_monitor(config_path: Optional[Path] = None) -> HyperparametersMonitor:
    """Create a hyperparameters monitor with validator"""
    validator = HyperparametersValidator(config_path)
    return HyperparametersMonitor(validator)
