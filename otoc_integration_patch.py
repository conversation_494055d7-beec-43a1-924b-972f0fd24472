#!/usr/bin/env python3
"""
OTOC Integration Patch for Paper Trading Script.

YAA-INTEGRATION: Patch completo para integrar sistema OTOC no paper trading.
"""

# ============================================================================
# 1. IMPORTS NECESSÁRIOS (adicionar após linha 60)
# ============================================================================

OTOC_IMPORTS = '''
    # YAA-OTOC: Imports do sistema OTOC integrado
    from qualia.utils.otoc_calculator import calculate_otoc, calculate_adaptive_threshold
    from qualia.utils.otoc_metrics import (
        OTOCMetricsCollector, 
        log_trading_decision,
        get_otoc_metrics_collector
    )
    from qualia.strategies.fibonacci_wave_hype.multi_timeframe_consolidator import (
        MultiTimeframeSignalConsolidator,
        TimeframeSignal
    )
'''

# ============================================================================
# 2. INICIALIZAÇÃO OTOC (adicionar no __init__ após linha 215)
# ============================================================================

OTOC_INITIALIZATION = '''
        # YAA-OTOC: Inicializar sistema OTOC
        self.otoc_enabled = self.config.get('fibonacci_wave_hype_config', {}).get(
            'multi_timeframe_config', {}
        ).get('otoc_config', {}).get('enabled', False)
        
        if self.otoc_enabled:
            self.logger.info("🌀 Sistema OTOC habilitado")
            
            # Configurar consolidador multi-timeframe com OTOC
            mtf_config = self.config.get('fibonacci_wave_hype_config', {}).get(
                'multi_timeframe_config', {}
            )
            self.mtf_consolidator = MultiTimeframeSignalConsolidator(mtf_config)
            
            # Coletor de métricas OTOC
            self.otoc_metrics_collector = get_otoc_metrics_collector()
            
            self.logger.info("✅ Sistema OTOC inicializado com sucesso")
        else:
            self.logger.info("⚠️ Sistema OTOC desabilitado na configuração")
            self.mtf_consolidator = None
            self.otoc_metrics_collector = None
'''

# ============================================================================
# 3. MÉTODO PARA CALCULAR OTOC (adicionar como novo método)
# ============================================================================

CALCULATE_OTOC_METHOD = '''
    async def _calculate_otoc_for_symbol(self, symbol: str, timeframe: str, ohlcv_data: pd.DataFrame) -> float:
        """
        Calcula OTOC para um símbolo e timeframe específicos.
        
        YAA-OTOC: Implementação do cálculo OTOC integrado ao paper trading
        """
        try:
            if not self.otoc_enabled or ohlcv_data.empty:
                return 0.0
            
            # Obter configuração OTOC
            otoc_config = self.config.get('fibonacci_wave_hype_config', {}).get(
                'multi_timeframe_config', {}
            ).get('otoc_config', {})
            
            window = otoc_config.get('window', 100)
            method = otoc_config.get('method', 'correlation')
            
            # Calcular OTOC usando preços de fechamento
            close_prices = ohlcv_data['close'].values
            
            if len(close_prices) < window * 2:
                self.logger.debug(f"OTOC {symbol} ({timeframe}): Dados insuficientes ({len(close_prices)} < {window*2})")
                return 0.0
            
            otoc_value = calculate_otoc(
                series=close_prices,
                window=window,
                method=method
            )
            
            if np.isnan(otoc_value):
                self.logger.debug(f"OTOC {symbol} ({timeframe}): Resultado NaN")
                return 0.0
            
            self.logger.debug(f"OTOC {symbol} ({timeframe}): {otoc_value:.4f}")
            return float(otoc_value)
            
        except Exception as e:
            self.logger.error(f"Erro ao calcular OTOC para {symbol} ({timeframe}): {e}")
            return 0.0
'''

# ============================================================================
# 4. MÉTODO PARA APLICAR FILTRO OTOC (adicionar como novo método)
# ============================================================================

APPLY_OTOC_FILTER_METHOD = '''
    async def _apply_otoc_filter(self, symbol: str, timeframe_signals: Dict[str, Dict], timeframe_data: Dict[str, pd.DataFrame]) -> Dict[str, Dict]:
        """
        Aplica filtro OTOC aos sinais de trading.
        
        YAA-OTOC: Filtra sinais em regimes caóticos
        """
        try:
            if not self.otoc_enabled or not self.mtf_consolidator:
                return timeframe_signals
            
            # Obter configuração OTOC
            otoc_config = self.config.get('fibonacci_wave_hype_config', {}).get(
                'multi_timeframe_config', {}
            ).get('otoc_config', {})
            
            max_threshold = otoc_config.get('max_threshold', 0.35)
            adaptive_enabled = otoc_config.get('adaptive_threshold', {}).get('enabled', True)
            beta = otoc_config.get('adaptive_threshold', {}).get('beta', 1.0)
            vol_window = otoc_config.get('adaptive_threshold', {}).get('vol_window', 20)
            
            filtered_signals = {}
            
            for timeframe, signal_data in timeframe_signals.items():
                try:
                    # Calcular OTOC para este timeframe
                    ohlcv_data = timeframe_data.get(timeframe, pd.DataFrame())
                    otoc_value = await self._calculate_otoc_for_symbol(symbol, timeframe, ohlcv_data)
                    
                    # Calcular threshold adaptativo se habilitado
                    effective_threshold = max_threshold
                    if adaptive_enabled and not ohlcv_data.empty:
                        returns = ohlcv_data['close'].pct_change()
                        volatility = returns.rolling(vol_window).std().iloc[-1]
                        
                        if not np.isnan(volatility):
                            effective_threshold = calculate_adaptive_threshold(
                                base_threshold=max_threshold,
                                volatility=volatility,
                                beta=beta,
                                vol_window=vol_window
                            )
                    
                    # Aplicar filtro OTOC
                    original_action = signal_data['action']
                    original_confidence = signal_data['confidence']
                    
                    if otoc_value > effective_threshold:
                        # Regime caótico detectado - bloquear sinal
                        filtered_action = 'HOLD'
                        filtered_confidence = 0.0
                        
                        self.logger.info(
                            f"🌀 OTOC FILTER: {symbol} ({timeframe}) - Regime caótico detectado! "
                            f"OTOC={otoc_value:.3f} > {effective_threshold:.3f}. "
                            f"Sinal {original_action} → HOLD"
                        )
                        
                        # Registrar métricas OTOC
                        if self.otoc_metrics_collector:
                            self.otoc_metrics_collector.record_otoc_decision(
                                symbol=symbol,
                                timeframe=timeframe,
                                otoc_value=otoc_value,
                                threshold_used=effective_threshold,
                                threshold_base=max_threshold,
                                volatility=volatility if not np.isnan(volatility) else 0.0,
                                signal_original=original_action,
                                signal_filtered=filtered_action,
                                confidence_original=original_confidence,
                                confidence_filtered=filtered_confidence
                            )
                    else:
                        # Regime ordenado - manter sinal original
                        filtered_action = original_action
                        filtered_confidence = original_confidence
                        
                        self.logger.debug(
                            f"✅ OTOC OK: {symbol} ({timeframe}) - "
                            f"OTOC={otoc_value:.3f} <= {effective_threshold:.3f}"
                        )
                    
                    # Atualizar sinal com informações OTOC
                    filtered_signals[timeframe] = {
                        'action': filtered_action,
                        'confidence': filtered_confidence,
                        'price': signal_data.get('price', 0.0),
                        'otoc_value': otoc_value,
                        'otoc_threshold': effective_threshold,
                        'chaos_detected': otoc_value > effective_threshold
                    }
                    
                except Exception as e:
                    self.logger.error(f"Erro ao aplicar filtro OTOC para {symbol} ({timeframe}): {e}")
                    # Em caso de erro, manter sinal original (fail-safe)
                    filtered_signals[timeframe] = signal_data
            
            return filtered_signals
            
        except Exception as e:
            self.logger.error(f"Erro geral no filtro OTOC para {symbol}: {e}")
            return timeframe_signals
'''

# ============================================================================
# 5. MODIFICAÇÃO NO MÉTODO DE CONSOLIDAÇÃO (substituir método existente)
# ============================================================================

MODIFIED_CONSOLIDATION_METHOD = '''
    async def _consolidate_multi_timeframe_signals(self, symbol: str, timeframe_signals: dict, timeframe_data: dict) -> dict:
        """
        Consolida sinais de múltiplos timeframes COM FILTRO OTOC integrado.
        
        YAA-OTOC: Versão atualizada com sistema OTOC
        """
        try:
            # Gerar ID único para esta decisão
            decision_id = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            
            # YAA-OTOC: Aplicar filtro OTOC ANTES da consolidação
            if self.otoc_enabled:
                self.logger.debug(f"🌀 Aplicando filtro OTOC para {symbol}...")
                timeframe_signals = await self._apply_otoc_filter(symbol, timeframe_signals, timeframe_data)
            
            # Continuar com lógica de consolidação existente...
            timeframe_hierarchy = ['1m', '5m', '15m', '1h']
            available_timeframes = [tf for tf in timeframe_hierarchy if tf in timeframe_signals]

            if not available_timeframes:
                return {'action': 'HOLD', 'confidence': 0.0, 'price': 0.0}

            # Detectar reversões nos timeframes menores
            reversal_signals = []
            for tf in ['1m', '5m']:
                if tf in timeframe_signals and timeframe_signals[tf]['action'] != 'HOLD':
                    reversal_signals.append({
                        'timeframe': tf,
                        'signal': timeframe_signals[tf],
                        'weight': 0.4 if tf == '1m' else 0.3
                    })

            # Consolidar sinais (lógica existente mantida)
            consolidated_action = 'HOLD'
            consolidated_confidence = 0.0
            reasoning = "No signals detected"

            if len(reversal_signals) >= 2:
                signal_1m = next((s for s in reversal_signals if s['timeframe'] == '1m'), None)
                signal_5m = next((s for s in reversal_signals if s['timeframe'] == '5m'), None)
                
                if signal_1m and signal_5m and signal_1m['signal']['action'] == signal_5m['signal']['action']:
                    consolidated_action = signal_1m['signal']['action']
                    consolidated_confidence = (signal_1m['signal']['confidence'] * 0.4) + (signal_5m['signal']['confidence'] * 0.3)
                    reasoning = f"Reversão confirmada: 1m+5m concordam ({consolidated_action})"

            elif reversal_signals:
                strongest_reversal = max(reversal_signals, key=lambda x: x['signal']['confidence'])
                if strongest_reversal['signal']['confidence'] > 0.3:
                    consolidated_action = strongest_reversal['signal']['action']
                    consolidated_confidence = strongest_reversal['signal']['confidence'] * 0.8
                    reasoning = f"Sinal forte em {strongest_reversal['timeframe']}"

            # YAA-OTOC: Adicionar informações OTOC ao resultado
            otoc_info = {}
            if self.otoc_enabled:
                chaos_detected = any(
                    sig.get('chaos_detected', False) 
                    for sig in timeframe_signals.values()
                )
                avg_otoc = np.mean([
                    sig.get('otoc_value', 0.0) 
                    for sig in timeframe_signals.values() 
                    if 'otoc_value' in sig
                ])
                
                otoc_info = {
                    'chaos_detected': chaos_detected,
                    'avg_otoc': avg_otoc,
                    'otoc_enabled': True
                }
                
                if chaos_detected:
                    reasoning += " [OTOC: Caos detectado em alguns timeframes]"

            # Log da decisão consolidada
            self.logger.info(f"📊 {symbol}: Consolidação final = {consolidated_action} "
                           f"(confiança: {consolidated_confidence:.3f}) - {reasoning}")

            return {
                'action': consolidated_action,
                'confidence': consolidated_confidence,
                'price': next(iter(timeframe_signals.values())).get('price', 0.0),
                'reasoning': reasoning,
                'timeframe_count': len(available_timeframes),
                **otoc_info
            }
            
        except Exception as e:
            self.logger.error(f"Erro na consolidação multi-timeframe para {symbol}: {e}")
            return {'action': 'HOLD', 'confidence': 0.0, 'price': 0.0}
'''

# ============================================================================
# 6. MÉTODO PARA LOG DE MÉTRICAS OTOC (adicionar como novo método)
# ============================================================================

LOG_OTOC_METRICS_METHOD = '''
    async def _log_otoc_metrics(self):
        """
        Log periódico das métricas OTOC.
        
        YAA-OTOC: Observabilidade do sistema OTOC
        """
        try:
            if not self.otoc_enabled or not self.otoc_metrics_collector:
                return
            
            # Obter estatísticas OTOC
            stats = self.otoc_metrics_collector.get_otoc_statistics()
            chaos_rate = self.otoc_metrics_collector.get_chaos_rate()
            
            if stats:
                self.logger.info("🌀 MÉTRICAS OTOC:")
                self.logger.info(f"   Taxa de caos (1h): {chaos_rate:.1%}")
                self.logger.info(f"   Total decisões: {stats.get('count', 0)}")
                self.logger.info(f"   OTOC médio: {stats.get('otoc_mean', 0):.3f}")
                self.logger.info(f"   Eventos de caos: {stats.get('chaos_events', 0)}")
            
            # Verificar alertas
            alerts = self.otoc_metrics_collector.generate_alert_conditions()
            for alert in alerts:
                self.logger.warning(f"🚨 OTOC ALERT [{alert['severity']}]: {alert['message']}")
                
        except Exception as e:
            self.logger.error(f"Erro ao logar métricas OTOC: {e}")
'''

# ============================================================================
# 7. INSTRUÇÕES DE APLICAÇÃO
# ============================================================================

INTEGRATION_INSTRUCTIONS = '''
INSTRUÇÕES PARA APLICAR O PATCH OTOC:

1. ADICIONAR IMPORTS (após linha 60):
   - Adicionar OTOC_IMPORTS

2. MODIFICAR __init__ (após linha 215):
   - Adicionar OTOC_INITIALIZATION

3. ADICIONAR NOVOS MÉTODOS:
   - _calculate_otoc_for_symbol
   - _apply_otoc_filter  
   - _log_otoc_metrics

4. SUBSTITUIR MÉTODO EXISTENTE:
   - _consolidate_multi_timeframe_signals (linha ~1359)

5. MODIFICAR _perform_system_maintenance (adicionar):
   - await self._log_otoc_metrics()

6. VERIFICAR CONFIGURAÇÃO:
   - Garantir que config/fwh_scalp_config.yaml tem otoc_config

RESULTADO ESPERADO:
✅ Sistema OTOC totalmente integrado ao paper trading
✅ Filtros de caos funcionando em tempo real  
✅ Métricas OTOC sendo coletadas
✅ Alertas automáticos para regimes caóticos
✅ Observabilidade completa via logs
'''

if __name__ == "__main__":
    print("🌀 OTOC Integration Patch for Paper Trading")
    print("=" * 60)
    print(INTEGRATION_INSTRUCTIONS)
