// Autogenerated WebGPU version - Codex 2025-06-13
// QUALIA WebGPU holographic view

// Espera o DOM carregar e inicializa WebGPU se disponível

document.addEventListener('DOMContentLoaded', async () => {
  const container = document.getElementById('holographic-view');
  if (!container) return;

  const width = container.clientWidth;
  const height = container.clientHeight;

  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  container.appendChild(canvas);

  if (!navigator.gpu) {
    console.warn('WebGPU não disponível, abortando.');
    return;
  }

  const adapter = await navigator.gpu.requestAdapter();
  if (!adapter) {
    console.warn('Adapter WebGPU indisponível');
    return;
  }
  const device = await adapter.requestDevice();
  const context = canvas.getContext('webgpu');
  const format = navigator.gpu.getPreferredCanvasFormat();
  context.configure({ device, format, alphaMode: 'premultiplied' });

  const vertices = new Float32Array([
    0, 0.5, 0,
    -0.5, -0.5, 0,
    0.5, -0.5, 0,
  ]);

  const vertexBuffer = device.createBuffer({
    size: vertices.byteLength,
    usage: GPUBufferUsage.VERTEX,
    mappedAtCreation: true,
  });
  new Float32Array(vertexBuffer.getMappedRange()).set(vertices);
  vertexBuffer.unmap();

  const uniformData = new Float32Array(8);
  const uniformBuffer = device.createBuffer({
    size: uniformData.byteLength,
    usage: GPUBufferUsage.UNIFORM | GPUBufferUsage.COPY_DST,
  });

  const shaderModule = device.createShaderModule({
    code: `
struct Uniforms {
  coherence : f32,
  entanglement : f32,
  time : f32,
  padding : f32,
};
@group(0) @binding(0) var<uniform> uniforms : Uniforms;
struct VSOut {
  @builtin(position) pos : vec4<f32>;
};
@vertex
fn vs_main(@location(0) position : vec3<f32>) -> VSOut {
  var out : VSOut;
  let scale = 0.6 + 0.4 * uniforms.coherence;
  out.pos = vec4<f32>(position.xy * scale, 0.0, 1.0);
  return out;
}
@fragment
fn fs_main() -> @location(0) vec4<f32> {
  let r = 0.5 + 0.5 * uniforms.entanglement;
  let g = 0.5 + 0.5 * sin(uniforms.time);
  return vec4<f32>(r, g, 0.8, 1.0);
}
    `,
  });

  const pipeline = device.createRenderPipeline({
    layout: 'auto',
    vertex: {
      module: shaderModule,
      entryPoint: 'vs_main',
      buffers: [
        {
          arrayStride: 12,
          attributes: [
            { shaderLocation: 0, format: 'float32x3', offset: 0 },
          ],
        },
      ],
    },
    fragment: {
      module: shaderModule,
      entryPoint: 'fs_main',
      targets: [{ format }],
    },
    primitive: { topology: 'triangle-list' },
  });

  const bindGroup = device.createBindGroup({
    layout: pipeline.getBindGroupLayout(0),
    entries: [
      { binding: 0, resource: { buffer: uniformBuffer } },
    ],
  });

  window.holographicUniforms = {
    coherence: { value: 0.5 },
    entanglement: { value: 0.5 },
    resonance: { value: 0 },
    fieldStrength: { value: 0 },
    consciousness: { value: 0 },
    liquidityBuckets: { value: [0, 0, 0] },
    trendStrength: { value: 0 },
    deltaEntropy: { value: 0 },
  };

  const socket = io('/ws/holographic', { path: '/socket.io', transports: ['websocket'] });
  socket.on('snapshot', (data) => {
    if (typeof data.coherence === 'number') {
      window.holographicUniforms.coherence.value = data.coherence;
    }
    if (typeof data.entanglement === 'number') {
      window.holographicUniforms.entanglement.value = data.entanglement;
    }
  });

  let t = 0;
  function frame() {
    t += 0.016;
    uniformData[0] = window.holographicUniforms.coherence.value;
    uniformData[1] = window.holographicUniforms.entanglement.value;
    uniformData[2] = t;
    device.queue.writeBuffer(uniformBuffer, 0, uniformData);

    const commandEncoder = device.createCommandEncoder();
    const textureView = context.getCurrentTexture().createView();
    const renderPass = commandEncoder.beginRenderPass({
      colorAttachments: [
        {
          view: textureView,
          clearValue: { r: 0, g: 0, b: 0, a: 1 },
          loadOp: 'clear',
          storeOp: 'store',
        },
      ],
    });

    renderPass.setPipeline(pipeline);
    renderPass.setVertexBuffer(0, vertexBuffer);
    renderPass.setBindGroup(0, bindGroup);
    renderPass.draw(3);
    renderPass.end();
    device.queue.submit([commandEncoder.finish()]);
    requestAnimationFrame(frame);
  }

  frame();
});
