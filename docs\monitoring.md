# 📊 QUALIA Monitoring & Observability

Sistema completo de monitoramento e observabilidade para QUALIA com Grafana, Prometheus e AlertManager.

## 🎯 Visão Geral

O sistema de monitoramento QUALIA fornece:

- **📈 Dashboards Visuais**: Grafana com métricas em tempo real
- **🚨 Alertas Automáticos**: Notificações para drawdown excessivo e problemas
- **📊 Métricas Detalhadas**: Performance, hiperparâmetros, sistema
- **🔍 Observabilidade**: Logs, traces e métricas centralizados

## 🚀 Início Rápido

### 1. Iniciar Stack Completo

```bash
# Iniciar tudo (Grafana + Prometheus + AlertManager + Exporter)
python scripts/start_grafana_stack.py

# Ou especificar porta customizada
python scripts/start_grafana_stack.py --port 8081
```

### 2. Acessar Serviços

- **🎨 Grafana**: http://localhost:3000 (admin/qualia2024)
- **📊 Prometheus**: http://localhost:9090
- **🚨 AlertManager**: http://localhost:9093
- **📈 Métricas QUALIA**: http://localhost:8080/metrics

### 3. Importar Dashboard

1. Acesse Grafana (http://localhost:3000)
2. Login: `admin` / Senha: `qualia2024`
3. Vá em **Dashboards** → **Import**
4. Carregue: `docker/grafana-stack/grafana/dashboards/qualia-main-dashboard.json`

## 📊 Métricas Disponíveis

### Performance Trading
- `qualia_hyperparams_total_return_pct`: Retorno total percentual
- `qualia_hyperparams_sharpe_ratio`: Sharpe ratio
- `qualia_hyperparams_max_drawdown_pct`: Drawdown máximo
- `qualia_hyperparams_decision_rate`: Taxa de decisões (por hora)

### Hiperparâmetros
- `qualia_hyperparams_price_amplification`: Amplificação de preço
- `qualia_hyperparams_news_amplification`: Amplificação de notícias
- `qualia_hyperparams_min_confidence`: Confiança mínima
- `qualia_hyperparams_final_confidence`: Confiança final

### Sistema
- `qualia_hyperparams_processing_latency_ms`: Latência de processamento
- `qualia_system_uptime_seconds`: Uptime do sistema
- `qualia_hyperparams_decisions_total`: Total de decisões

## 🚨 Alertas Configurados

### Críticos (Notificação Imediata)
- **Drawdown Excessivo**: > 25% por 2 minutos
- **Perda Significativa**: < -15% por 5 minutos
- **Sistema Offline**: Sem resposta por 1 minuto

### Warnings
- **Sharpe Baixo**: < 0.5 por 10 minutos
- **Taxa Decisão Baixa**: < 2 decisões/hora por 15 minutos
- **Confiança Baixa**: < 0.4 média por 20 minutos

### Info
- **Sistema Inativo**: Sem decisões por 30 minutos
- **Novo Melhor Resultado**: Otimização encontrou melhoria

## ⚙️ Configuração Avançada

### Personalizar Alertas

Edite `docker/grafana-stack/prometheus/rules/qualia_alerts.yml`:

```yaml
- alert: CustomAlert
  expr: your_metric > threshold
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "Seu alerta customizado"
```

### Configurar Notificações

Edite `docker/grafana-stack/alertmanager/alertmanager.yml`:

```yaml
receivers:
  - name: 'slack-alerts'
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK'
        channel: '#trading-alerts'
```

### Adicionar Métricas Customizadas

No código QUALIA:

```python
from qualia.monitoring.prometheus_exporter import get_prometheus_exporter

exporter = get_prometheus_exporter()
# Suas métricas customizadas serão automaticamente exportadas
```

## 🐳 Docker Stack

### Serviços Incluídos

- **Prometheus**: Coleta e armazena métricas
- **Grafana**: Visualização e dashboards
- **AlertManager**: Gerenciamento de alertas
- **Node Exporter**: Métricas do sistema
- **StatsD Exporter**: Converte StatsD para Prometheus

### Comandos Docker

```bash
# Iniciar stack
docker-compose -f docker/grafana-stack/docker-compose.yml up -d

# Ver logs
docker-compose -f docker/grafana-stack/docker-compose.yml logs -f

# Parar stack
docker-compose -f docker/grafana-stack/docker-compose.yml down

# Rebuild
docker-compose -f docker/grafana-stack/docker-compose.yml up -d --build
```

## 🔧 Troubleshooting

### Problema: Métricas não aparecem

1. Verifique se o exportador está rodando:
   ```bash
   curl http://localhost:8080/health
   ```

2. Verifique se Prometheus está coletando:
   ```bash
   curl http://localhost:9090/api/v1/targets
   ```

3. Verifique logs do exportador:
   ```bash
   python scripts/start_grafana_stack.py --action status
   ```

### Problema: Alertas não funcionam

1. Verifique regras no Prometheus:
   http://localhost:9090/rules

2. Verifique AlertManager:
   http://localhost:9093/#/alerts

3. Teste webhook:
   ```bash
   curl -X POST http://localhost:9093/api/v1/alerts
   ```

### Problema: Dashboard vazio

1. Verifique se dados estão sendo coletados:
   http://localhost:9090/graph

2. Verifique query no Grafana:
   - Use `qualia_hyperparams_*` como prefixo
   - Verifique timerange

3. Force refresh das métricas:
   ```bash
   curl http://localhost:8080/metrics
   ```

## 📈 Dashboards Disponíveis

### Main Dashboard
- **Performance Overview**: Return, Drawdown, Sharpe
- **Trading Activity**: Decision rate, Confidence
- **System Health**: Uptime, Latency, Errors
- **Hyperparameters**: Amplification values, Thresholds

### Custom Dashboards

Crie dashboards customizados usando queries como:

```promql
# Taxa de decisões por hora
rate(qualia_hyperparams_decisions_total[1h]) * 3600

# Confiança média últimas 24h
avg_over_time(qualia_hyperparams_final_confidence[24h])

# Drawdown atual vs histórico
qualia_hyperparams_max_drawdown_pct

# Performance por símbolo
qualia_hyperparams_total_return_pct{symbol="BTC/USDT"}
```

## 🔐 Segurança

### Credenciais Padrão
- **Grafana**: admin / qualia2024
- **Prometheus**: Sem autenticação (localhost apenas)
- **AlertManager**: Sem autenticação (localhost apenas)

### Produção
Para produção, configure:
1. HTTPS com certificados
2. Autenticação robusta
3. Firewall/VPN para acesso
4. Backup dos dados

## 📚 Recursos Adicionais

- [Prometheus Query Language](https://prometheus.io/docs/prometheus/latest/querying/)
- [Grafana Documentation](https://grafana.com/docs/)
- [AlertManager Configuration](https://prometheus.io/docs/alerting/latest/configuration/)

## 🆘 Suporte

Para problemas ou dúvidas:
1. Verifique logs: `docker-compose logs`
2. Consulte documentação oficial
3. Abra issue no repositório QUALIA
