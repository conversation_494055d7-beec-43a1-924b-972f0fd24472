#!/usr/bin/env python3
"""
Demonstração de Health Check Integrado com QUALIA
Mostra como usar o SystemHealthChecker com componentes reais do sistema.
"""

import asyncio
import sys
import logging
from pathlib import Path

# Adicionar o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.monitoring.health_check import SystemHealthChecker, SystemReadiness
from qualia.core.system_manager import QUALIASystemManager

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def demo_real_system_health_check():
    """Demonstra health check com configuração real do QUALIA."""
    
    print("🏥 HEALTH CHECK INTEGRADO - SISTEMA QUALIA REAL")
    print("=" * 60)
    
    # Configuração básica do sistema
    config = {
        "data_collection": {
            "symbols": ["BTCUSDT", "ETHUSDT"],
            "timeframes": ["5m", "1h"],
            "update_interval": 30
        },
        "holographic_universe": {
            "pattern_detection": {
                "significance_threshold": 0.2,
                "confidence_threshold": 0.3,
                "min_history_length": 10
            }
        },
        "strategies": {
            "BTCUSDT": {
                "timeframe": "5m",
                "params": {
                    "s1_strength_threshold": 0.01,
                    "s3_strength_threshold": 0.005
                }
            },
            "ETHUSDT": {
                "timeframe": "5m", 
                "params": {
                    "s1_strength_threshold": 0.01,
                    "s3_strength_threshold": 0.005
                }
            }
        },
        "risk_management": {
            "risk_per_trade": 0.02,
            "max_drawdown": 0.15,
            "max_open_positions": 5
        },
        "initial_capital": 10000.0,
        "health_check_interval": 30,
        "auto_corrections_enabled": True,
        "require_all_healthy_for_trading": False
    }
    
    # Criar system manager
    system_manager = QUALIASystemManager(config)
    
    # Registrar callback para monitorar mudanças de saúde
    async def health_callback(report):
        if report.overall_status == SystemReadiness.CRITICAL_ERROR:
            print("🚨 ALERTA: Sistema em estado crítico!")
        elif len(report.critical_issues) > 0:
            print(f"⚠️ ATENÇÃO: {len(report.critical_issues)} problemas críticos detectados")
    
    system_manager.register_health_callback(health_callback)
    
    try:
        print("\n🚀 Inicializando sistema QUALIA...")
        
        # Tentar inicializar o sistema
        success = await system_manager.initialize_system()
        
        if success:
            print("✅ Sistema inicializado com sucesso!")
            
            # Obter relatório inicial
            initial_report = await system_manager.get_current_health_report()
            
            print("\n📊 RELATÓRIO INICIAL DE SAÚDE:")
            system_manager.health_checker.print_health_report(initial_report)
            
            # Verificar se está pronto para trading
            ready_for_trading = system_manager.is_ready_for_trading()
            print(f"\n🚀 Pronto para trading: {'SIM' if ready_for_trading else 'NÃO'}")
            
            if ready_for_trading:
                print("\n✅ Sistema operacional - pode iniciar trading!")
            else:
                print("\n⚠️ Sistema não está pronto para trading")
                print("Verifique os problemas reportados acima.")
            
            # Demonstrar monitoramento por alguns ciclos
            print("\n🔄 Monitorando sistema por 60 segundos...")
            print("(O monitoramento contínuo já está ativo em background)")
            
            for i in range(3):
                await asyncio.sleep(20)
                
                summary = system_manager.get_health_summary()
                print(f"\n--- Status após {(i+1)*20}s ---")
                print(f"Status: {summary.get('overall_status', 'unknown')}")
                print(f"Score: {summary.get('readiness_score', 0):.2%}")
                print(f"Componentes saudáveis: {summary.get('healthy_components', 0)}/{summary.get('total_components', 0)}")
                print(f"Problemas críticos: {summary.get('critical_issues_count', 0)}")
            
        else:
            print("❌ Falha na inicialização do sistema")
            
            # Ainda assim, mostrar o que foi possível verificar
            try:
                report = await system_manager.get_current_health_report()
                print("\n📊 RELATÓRIO DE FALHA:")
                system_manager.health_checker.print_health_report(report)
            except Exception as e:
                print(f"Não foi possível gerar relatório de falha: {e}")
        
    except Exception as e:
        logger.error(f"Erro durante demonstração: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        print("\n🛑 Encerrando sistema...")
        try:
            await system_manager.shutdown_system()
            print("✅ Sistema encerrado com sucesso")
        except Exception as e:
            print(f"⚠️ Erro durante shutdown: {e}")


async def demo_standalone_health_check():
    """Demonstra health check standalone sem sistema completo."""
    
    print("\n🔧 HEALTH CHECK STANDALONE")
    print("=" * 50)
    
    # Criar health checker standalone
    checker = SystemHealthChecker()
    
    # Simular alguns componentes básicos
    class SimpleDataCollector:
        def __init__(self):
            import time
            self.last_update_time = time.time()
            self.active_connections = 1
            
        def get_data_count(self):
            return 25
    
    class SimpleStrategy:
        def is_ready(self):
            return True
    
    # Registrar componentes
    checker.register_component("data_collector", SimpleDataCollector())
    checker.register_component("strategy_btc", SimpleStrategy())
    
    # Executar verificação
    report = await checker.get_readiness_report()
    
    print("📊 Relatório Standalone:")
    checker.print_health_report(report)
    
    # Mostrar resumo
    summary = checker.get_health_summary()
    print("\n📈 Resumo:")
    print(f"- Status: {summary.get('overall_status')}")
    print(f"- Score: {summary.get('readiness_score', 0):.2%}")
    print(f"- Componentes: {summary.get('healthy_components')}/{summary.get('total_components')}")


async def demo_health_check_with_errors():
    """Demonstra health check detectando erros intencionais."""
    
    print("\n🔴 DEMONSTRAÇÃO DE DETECÇÃO DE ERROS")
    print("=" * 50)
    
    class ProblematicDataCollector:
        def __init__(self):
            self.last_update_time = None  # Sem updates
            self.active_connections = 0   # Sem conexões
            
        def get_data_count(self):
            return 2  # Dados insuficientes
    
    class BrokenStrategy:
        def is_ready(self):
            raise Exception("Strategy crashed!")
    
    # Criar checker com componentes problemáticos
    checker = SystemHealthChecker()
    checker.register_component("data_collector", ProblematicDataCollector())
    checker.register_component("broken_strategy", BrokenStrategy())
    
    # Executar verificação
    report = await checker.get_readiness_report()
    
    print("📊 Relatório com Erros:")
    checker.print_health_report(report)
    
    print("\n🚨 Sistema detectou corretamente os problemas!")
    print(f"- Status: {report.overall_status.value}")
    print(f"- Pronto para trading: {'SIM' if report.is_ready_for_trading else 'NÃO'}")
    print(f"- Problemas críticos: {len(report.critical_issues)}")
    print(f"- Warnings: {len(report.warnings)}")


async def main():
    """Função principal de demonstração."""
    
    print("🏥 DEMONSTRAÇÃO COMPLETA DO HEALTH CHECK SYSTEM")
    print("=" * 70)
    print("Este script demonstra o health check integrado com QUALIA")
    print("=" * 70)
    
    try:
        # 1. Demonstrar health check standalone
        await demo_standalone_health_check()
        
        # 2. Demonstrar detecção de erros
        await demo_health_check_with_errors()
        
        # 3. Demonstrar sistema integrado (pode falhar se dependências não estiverem disponíveis)
        print("\n" + "="*70)
        print("🔄 Tentando demonstração com sistema real...")
        print("(Pode falhar se componentes QUALIA não estiverem disponíveis)")
        print("="*70)
        
        try:
            await demo_real_system_health_check()
        except ImportError as e:
            print(f"⚠️ Componentes QUALIA não disponíveis: {e}")
            print("Isso é esperado se nem todos os módulos estiverem implementados.")
        except Exception as e:
            print(f"⚠️ Erro na demonstração integrada: {e}")
            print("Continuando com outras demonstrações...")
        
        print("\n" + "="*70)
        print("✅ DEMONSTRAÇÃO CONCLUÍDA")
        print("="*70)
        print("O Sistema de Health Check está implementado e funcional!")
        print("Principais recursos:")
        print("• Verificação automática de todos os componentes")
        print("• Relatórios detalhados de saúde e readiness")
        print("• Monitoramento contínuo em background")
        print("• Detecção e correção automática de problemas")
        print("• Integração com o sistema de gerenciamento")
        print("• Callbacks customizáveis para eventos de saúde")
        
    except Exception as e:
        logger.error(f"Erro na demonstração principal: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main()) 
