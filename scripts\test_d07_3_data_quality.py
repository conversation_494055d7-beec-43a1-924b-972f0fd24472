#!/usr/bin/env python3
"""
QUALIA D-07.3 Data Quality Validation Module - Teste Específico

Teste detalhado do módulo de validação de qualidade de dados.
"""

import sys
from pathlib import Path
from datetime import datetime
import numpy as np

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.ab_testing.data_quality_validator import (
    DataQualityValidator,
    PriceFeedComparison,
    ExecutionComparison,
    DataQualityMetrics,
    DataQualityReport
)

def test_price_feed_validation():
    """Testa validação de feeds de preço."""
    print("🧪 Testando validação de feeds de preço...")
    
    validator = DataQualityValidator()
    metrics = validator.initialize_validation("price_test_session")
    
    # Simular comparações de preço
    comparisons = []
    for i in range(100):
        # Simular pequenas diferenças de preço
        base_price = 50000.0 + i * 10
        price_diff = np.random.normal(0, 2.5)  # Diferença média de ~0.005%
        
        comparison = PriceFeedComparison(
            symbol="BTC/USDT",
            timestamp=datetime.now(),
            simulator_price=base_price,
            live_price=base_price + price_diff,
            price_difference=price_diff,
            price_difference_pct=(price_diff / base_price) * 100,
            simulator_timestamp=datetime.now(),
            live_timestamp=datetime.now(),
            timestamp_lag_ms=np.random.uniform(50, 200)  # 50-200ms lag
        )
        
        validator.add_price_comparison(comparison)
        comparisons.append(comparison)
    
    # Verificar métricas
    assert validator.metrics.total_price_comparisons == 100
    assert validator.metrics.avg_price_difference_pct > 0
    assert validator.metrics.avg_timestamp_lag_ms > 0
    
    # Calcular scores
    scores = validator.calculate_quality_scores()
    assert "price_accuracy" in scores
    assert 0 <= scores["price_accuracy"] <= 1
    
    print(f"✅ Price Feed Validation: {validator.metrics.total_price_comparisons} comparações")
    print(f"   Diferença média: {validator.metrics.avg_price_difference_pct:.4f}%")
    print(f"   Lag médio: {validator.metrics.avg_timestamp_lag_ms:.1f}ms")
    print(f"   Score de precisão: {scores['price_accuracy']:.3f}")
    
    return True

def test_execution_validation():
    """Testa validação de execução de ordens."""
    print("🧪 Testando validação de execução...")
    
    validator = DataQualityValidator()
    validator.initialize_validation("execution_test_session")
    
    # Simular comparações de execução
    for i in range(50):
        # Simular execuções com diferentes características
        simulator_filled = np.random.random() > 0.05  # 95% fill rate
        live_filled = np.random.random() > 0.08  # 92% fill rate
        
        expected_price = 50000.0 + i * 5
        sim_slippage = np.random.uniform(0, 0.1) if simulator_filled else None
        live_slippage = np.random.uniform(0, 0.12) if live_filled else None
        
        execution = ExecutionComparison(
            order_id=f"order_{i}",
            symbol="BTC/USDT",
            side="buy" if i % 2 == 0 else "sell",
            quantity=0.1,
            expected_price=expected_price,
            order_timestamp=datetime.now(),
            simulator_filled=simulator_filled,
            live_filled=live_filled,
            simulator_slippage=sim_slippage,
            live_slippage=live_slippage,
            slippage_difference=(sim_slippage - live_slippage) if (sim_slippage and live_slippage) else None,
            simulator_latency_ms=np.random.uniform(20, 100),
            live_latency_ms=np.random.uniform(50, 150),
            latency_difference_ms=np.random.uniform(-50, 50)
        )
        
        validator.add_execution_comparison(execution)
    
    # Verificar métricas
    assert validator.metrics.total_executions == 50
    assert 0 <= validator.metrics.simulator_fill_rate <= 1
    assert 0 <= validator.metrics.live_fill_rate <= 1
    
    # Calcular scores
    scores = validator.calculate_quality_scores()
    assert "execution_accuracy" in scores
    
    print(f"✅ Execution Validation: {validator.metrics.total_executions} execuções")
    print(f"   Fill rate simulador: {validator.metrics.simulator_fill_rate:.1%}")
    print(f"   Fill rate live: {validator.metrics.live_fill_rate:.1%}")
    print(f"   Score de execução: {scores['execution_accuracy']:.3f}")
    
    return True

def test_quality_scoring():
    """Testa sistema de scoring de qualidade."""
    print("🧪 Testando sistema de scoring...")

    validator = DataQualityValidator()
    validator.initialize_validation("scoring_test_session")

    # Adicionar dados de alta qualidade com variação controlada
    for i in range(20):
        # Preços com pequenas diferenças controladas
        base_price = 50000.0
        price_diff = (i % 5) * 0.1  # Diferenças pequenas e controladas

        price_comparison = PriceFeedComparison(
            symbol="BTC/USDT",
            timestamp=datetime.now(),
            simulator_price=base_price,
            live_price=base_price + price_diff,
            price_difference=price_diff,
            price_difference_pct=(price_diff / base_price) * 100,
            simulator_timestamp=datetime.now(),
            live_timestamp=datetime.now(),
            timestamp_lag_ms=20.0 + (i % 3) * 10  # Lag baixo e controlado
        )
        validator.add_price_comparison(price_comparison)

        # Execuções com diferenças mínimas e controladas
        sim_slippage = 0.02 + (i % 3) * 0.001  # Variação mínima
        live_slippage = 0.021 + (i % 3) * 0.001  # Variação mínima

        execution = ExecutionComparison(
            order_id=f"quality_order_{i}",
            symbol="BTC/USDT",
            side="buy",
            quantity=0.1,
            expected_price=base_price,
            order_timestamp=datetime.now(),
            simulator_filled=True,
            live_filled=True,
            simulator_slippage=sim_slippage,
            live_slippage=live_slippage,
            slippage_difference=abs(sim_slippage - live_slippage),
            simulator_latency_ms=30.0 + (i % 2) * 5,
            live_latency_ms=32.0 + (i % 2) * 5,
            latency_difference_ms=2.0 + (i % 2)
        )
        validator.add_execution_comparison(execution)

    # Calcular scores
    try:
        scores = validator.calculate_quality_scores()

        # Verificar se scores são válidos
        assert "price_accuracy" in scores
        assert "execution_accuracy" in scores
        assert "timing_accuracy" in scores
        assert "overall_quality" in scores

        # Scores devem ser números válidos entre 0 e 1
        for score_name, score_value in scores.items():
            assert 0 <= score_value <= 1, f"Score {score_name} fora do range: {score_value}"
            assert not np.isnan(score_value), f"Score {score_name} é NaN"
            assert not np.isinf(score_value), f"Score {score_name} é infinito"

        # Para dados de alta qualidade, scores devem ser razoáveis
        assert scores["price_accuracy"] > 0.5
        assert scores["execution_accuracy"] > 0.5
        assert scores["timing_accuracy"] > 0.5
        assert scores["overall_quality"] > 0.5

        print(f"✅ Quality Scoring (dados de alta qualidade):")
        print(f"   Price accuracy: {scores['price_accuracy']:.3f}")
        print(f"   Execution accuracy: {scores['execution_accuracy']:.3f}")
        print(f"   Timing accuracy: {scores['timing_accuracy']:.3f}")
        print(f"   Overall quality: {scores['overall_quality']:.3f}")

        return True

    except Exception as e:
        print(f"❌ Erro no cálculo de scores: {e}")
        # Tentar obter informações de debug
        try:
            print(f"   Métricas: price_comparisons={validator.metrics.total_price_comparisons}")
            print(f"   Métricas: executions={validator.metrics.total_executions}")
            print(f"   Métricas: avg_price_diff={validator.metrics.avg_price_difference_pct}")
            print(f"   Métricas: avg_timestamp_lag={validator.metrics.avg_timestamp_lag_ms}")
        except:
            pass
        raise

def test_quality_report_generation():
    """Testa geração de relatório de qualidade."""
    print("🧪 Testando geração de relatório...")
    
    validator = DataQualityValidator()
    validator.initialize_validation("report_test_session")
    
    # Adicionar alguns dados
    for i in range(10):
        price_comparison = PriceFeedComparison(
            symbol="BTC/USDT",
            timestamp=datetime.now(),
            simulator_price=50000.0,
            live_price=50000.0 + i,
            price_difference=float(i),
            price_difference_pct=i / 50000.0 * 100,
            simulator_timestamp=datetime.now(),
            live_timestamp=datetime.now(),
            timestamp_lag_ms=100.0 + i * 10
        )
        validator.add_price_comparison(price_comparison)
    
    # Gerar relatório
    report = validator.generate_report()
    
    assert report is not None
    assert isinstance(report, DataQualityReport)
    assert report.metrics.session_id == "report_test_session"
    assert len(report.price_comparisons) == 10
    assert len(report.recommendations) >= 0
    assert report.metrics.overall_quality_score >= 0
    
    print(f"✅ Report Generation:")
    print(f"   Session ID: {report.metrics.session_id}")
    print(f"   Price comparisons: {len(report.price_comparisons)}")
    print(f"   Execution comparisons: {len(report.execution_comparisons)}")
    print(f"   Quality score: {report.metrics.overall_quality_score:.3f}")
    print(f"   Recommendations: {len(report.recommendations)}")
    
    return True

def test_quality_issues_detection():
    """Testa detecção de problemas de qualidade."""
    print("🧪 Testando detecção de problemas...")
    
    validator = DataQualityValidator()
    validator.initialize_validation("issues_test_session")
    
    # Adicionar dados com problemas intencionais
    
    # 1. Grande diferença de preço
    bad_price = PriceFeedComparison(
        symbol="BTC/USDT",
        timestamp=datetime.now(),
        simulator_price=50000.0,
        live_price=50100.0,  # Diferença de 0.2%
        price_difference=100.0,
        price_difference_pct=0.2,  # Acima do threshold de 0.1%
        simulator_timestamp=datetime.now(),
        live_timestamp=datetime.now(),
        timestamp_lag_ms=100.0
    )
    validator.add_price_comparison(bad_price)
    
    # 2. Alto lag de timestamp
    high_lag_price = PriceFeedComparison(
        symbol="BTC/USDT",
        timestamp=datetime.now(),
        simulator_price=50000.0,
        live_price=50000.0,
        price_difference=0.0,
        price_difference_pct=0.0,
        simulator_timestamp=datetime.now(),
        live_timestamp=datetime.now(),
        timestamp_lag_ms=2000.0  # 2 segundos - acima do threshold
    )
    validator.add_price_comparison(high_lag_price)
    
    # 3. Grande diferença de slippage
    bad_execution = ExecutionComparison(
        order_id="bad_execution",
        symbol="BTC/USDT",
        side="buy",
        quantity=0.1,
        expected_price=50000.0,
        order_timestamp=datetime.now(),
        simulator_filled=True,
        live_filled=True,
        simulator_slippage=0.02,
        live_slippage=0.1,  # Diferença de 0.08% - acima do threshold
        slippage_difference=0.08,
        simulator_latency_ms=50,
        live_latency_ms=600,  # 600ms - acima do threshold
        latency_difference_ms=550
    )
    validator.add_execution_comparison(bad_execution)
    
    # Verificar se problemas foram detectados
    assert len(validator.metrics.quality_issues) > 0
    assert len(validator.metrics.warnings) > 0
    
    # Gerar relatório
    report = validator.generate_report()
    assert len(report.recommendations) > 0
    
    print(f"✅ Issues Detection:")
    print(f"   Quality issues: {len(validator.metrics.quality_issues)}")
    print(f"   Warnings: {len(validator.metrics.warnings)}")
    print(f"   Recommendations: {len(report.recommendations)}")
    
    for issue in validator.metrics.quality_issues:
        print(f"   - Issue: {issue}")
    
    for warning in validator.metrics.warnings:
        print(f"   - Warning: {warning}")
    
    return True

def main():
    """Função principal de teste."""
    print("🚀 Testando D-07.3 Data Quality Validation Module")
    
    tests = [
        ("Price Feed Validation", test_price_feed_validation),
        ("Execution Validation", test_execution_validation),
        ("Quality Scoring", test_quality_scoring),
        ("Report Generation", test_quality_report_generation),
        ("Issues Detection", test_quality_issues_detection),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Executando: {test_name}")
            result = test_func()
            results.append((test_name, result, None))
            print(f"✅ {test_name}: PASSOU")
        except Exception as e:
            results.append((test_name, False, str(e)))
            print(f"❌ {test_name}: FALHOU - {e}")
    
    # Resumo
    print("\n" + "="*60)
    print("📋 RESUMO DOS TESTES D-07.3")
    print("="*60)
    
    passed = sum(1 for _, result, _ in results if result)
    total = len(results)
    
    for test_name, result, error in results:
        status = "✅ PASSOU" if result else "❌ FALHOU"
        print(f"{status}: {test_name}")
        if error:
            print(f"    Erro: {error}")
    
    print(f"\nTaxa de Sucesso: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 D-07.3 Data Quality Validation Module implementado com sucesso!")
        return True
    else:
        print(f"❌ {total-passed} testes falharam.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
