#!/usr/bin/env python3
"""
Teste Simples da Correção do OTOC
Testa diretamente a lógica corrigida com estados diversos e múltiplos qubits alvo.
"""

import numpy as np

def create_tensor_operator(single_op, target_qubit, total_qubits, I):
    """Cria operador tensorial correto."""
    operators = []
    for i in range(total_qubits):
        if i == target_qubit:
            operators.append(single_op)
        else:
            operators.append(I)
    
    result = operators[0]
    for op in operators[1:]:
        result = np.kron(result, op)
    return result

def get_diverse_states(n_qubits):
    """Cria uma variedade de estados quânticos para teste."""
    states = {}
    
    # Estado |0...0⟩
    zero_state = np.zeros(2**n_qubits, dtype=complex)
    zero_state[0] = 1
    states["|0...0⟩"] = zero_state
    
    # Estado de superposição |+...+⟩
    plus_state_single = (1/np.sqrt(2)) * np.array([1, 1], dtype=complex)
    plus_state = plus_state_single
    for _ in range(n_qubits - 1):
        plus_state = np.kron(plus_state, plus_state_single)
    states["|+..._⟩"] = plus_state
    
    # Estado GHZ
    ghz_state = np.zeros(2**n_qubits, dtype=complex)
    ghz_state[0] = 1/np.sqrt(2)
    ghz_state[-1] = 1/np.sqrt(2)
    states["GHZ"] = ghz_state
    
    # Estado W
    w_state = np.zeros(2**n_qubits, dtype=complex)
    for i in range(n_qubits):
        w_state[2**i] = 1/np.sqrt(n_qubits)
    states["W"] = w_state
    
    # Estado aleatório
    random_state = np.random.rand(2**n_qubits) + 1j * np.random.rand(2**n_qubits)
    random_state /= np.linalg.norm(random_state)
    states["Random"] = random_state
    
    return states

def test_otoc_diversity_advanced():
    """Testa a diversidade do OTOC com estados e alvos variados."""
    print("🧪 TESTE AVANÇADO: Diversidade do OTOC com Estados Complexos")
    print("=" * 65)
    
    n_qubits = 3
    
    # Operadores de Pauli
    X = np.array([[0, 1], [1, 0]], dtype=complex)
    Y = np.array([[0, -1j], [1j, 0]], dtype=complex)
    Z = np.array([[1, 0], [0, -1]], dtype=complex)
    I = np.eye(2, dtype=complex)
    ops = {"x": X, "y": Y, "z": Z}
    
    states = get_diverse_states(n_qubits)
    operator_pairs = [("x", "z"), ("y", "z"), ("x", "y"), ("z", "x")]
    target_qubits_list = [[0], [1], [2], [0, 1], [0, 2]]
    
    print(f"\nEstado{'':<10}Qubits\tOps\tOTOC")
    print("-" * 50)
    
    otoc_values = []
    
    for cycle in range(12):
        state_name = list(states.keys())[cycle % len(states)]
        state_data = states[state_name]
        
        target_qubits = target_qubits_list[cycle % len(target_qubits_list)]
        
        pair_index = cycle % len(operator_pairs)
        op_w_type, op_v_type = operator_pairs[pair_index]
        W_op, V_op = ops[op_w_type], ops[op_v_type]
        
        # Construir operadores
        W_ext = np.eye(2**n_qubits, dtype=complex)
        V_ext = np.eye(2**n_qubits, dtype=complex)
        
        for q_idx in target_qubits:
            W_q = create_tensor_operator(W_op, q_idx, n_qubits, I)
            V_q = create_tensor_operator(V_op, q_idx, n_qubits, I)
            W_ext = W_ext @ W_q
            V_ext = V_ext @ V_q
            
        # Evolução temporal
        phase = np.exp(1j * cycle * np.pi / 4)
        W_t_ext = phase * W_ext
        
        # OTOC
        commutator = W_t_ext @ V_ext - V_ext @ W_t_ext
        rho = np.outer(state_data, state_data.conj())
        otoc_matrix = commutator.conj().T @ commutator
        otoc_val = np.trace(rho @ otoc_matrix)
        otoc_normalized = float(np.abs(otoc_val) / (4.0 * len(target_qubits)))
        
        otoc_values.append(otoc_normalized)
        
        print(f"{state_name:<15}{str(target_qubits):<7}\t{op_w_type.upper()}/{op_v_type.upper()}\t{otoc_normalized:.6f}")

    print("\n📊 ANÁLISE FINAL:")
    unique_values = sorted(list(set(round(v, 6) for v in otoc_values)))
    print(f"Valores únicos: {len(unique_values)}")
    print(f"Variância: {np.var(otoc_values):.6f}")
    print(f"Amplitude (max-min): {max(otoc_values) - min(otoc_values):.6f}")
    
    if len(unique_values) > 1 and np.var(otoc_values) > 1e-6:
        print("✅ SUCESSO: O OTOC agora é uma métrica dinâmica e rica!")
        print(f"   Valores observados: {unique_values}")
    else:
        print("❌ FALHA: OTOC ainda mostra pouca ou nenhuma variação.")

if __name__ == "__main__":
    test_otoc_diversity_advanced() 