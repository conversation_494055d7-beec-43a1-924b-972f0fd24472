# Fluxo de Decisão End-to-End

Este documento apresenta uma visão consolidada do ciclo de decisão do QUALIA. O fluxo ocorre dentro do `_decision_cycle_loop` e descreve como os dados de mercado geram sinais que passam por validação de risco antes de serem executados.

## Etapas principais

1. **Coleta de dados**: o módulo de coleta publica `MarketDataUpdated` com preços e notícias.
2. **Consulta ao Oracle**: `QASTOracleDecisionEngine.consult_oracle` retorna `OracleDecision` preliminar.
3. **Análise holográfica**: `HolographicUniverse.analyze_holographic_patterns` gera sinais complementares.
4. **Combinação de sinais**: `_combine_oracle_and_holographic_signals` unifica as recomendações.
5. **Avaliação de risco**: `_enhanced_risk_assessment` filtra e ajusta as decisões.
6. **Publicação de sinal**: `trading.signal.generated` é emitido com as decisões aprovadas.
7. **Registro de métricas**: `system.decision_metrics` armazena contagem e taxa de rejeição de cada ciclo.

## Exemplo de logs

```text
📊 ESTATÍSTICAS DO CICLO:
   Oracle decisions: 3
   Holographic signals: 2
   Combined decisions: 3
   Validated decisions: 1
   Rejected decisions: 2
   Rejection rate: 66.7%
```

## Estrutura das métricas

Cada entrada de `decision_metrics` contém:

- `timestamp` – instante de geração do relatório;
- `oracle_decisions` – número de decisões produzidas pelo Oracle;
- `holographic_signals` – quantidade de padrões holográficos detectados;
- `combined_decisions` – total de sinais combinados pelo sistema;
- `validated_decisions` – número de decisões aprovadas pelo risco;
- `rejection_rate` – proporção de sinais descartados.

Esses dados permitem monitorar a eficiência do ciclo e alimentar painéis de desempenho.
