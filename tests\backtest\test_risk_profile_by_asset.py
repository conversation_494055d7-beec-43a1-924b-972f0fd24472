import logging
import pytest

from qualia.strategies.strategy_factory import StrategyFactory
from qualia.strategies.nova_estrategia_qualia import QualiaTSVFStrategy


@pytest.mark.unit
def test_per_asset_profile_overrides_global(caplog):
    context = {
        "risk_profile": "moderate",
        "risk_profile_by_symbol": {"BTC/USDT": "aggressive"},
        "qualia_config": {
            "ace_config": {
                "enable_dynamic_risk_control": True,
                "dynamic_risk_config": {"dynamic_risk_parameters": {"atr_period": 14}},
            }
        },
        "symbol": "BTC/USDT",
        "timeframe": "1h",
    }

    caplog.set_level(logging.ERROR)
    strategy = StrategyFactory.create_strategy(
        alias="QualiaTSVFStrategy",
        params={},
        context=context,
    )

    assert strategy.shared_context.get("risk_profile") == "aggressive"
