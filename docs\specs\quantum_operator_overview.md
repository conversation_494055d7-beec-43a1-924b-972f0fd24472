# Visão Geral dos Operadores Quânticos

Este documento consolida as definições matemáticas básicas dos operadores do QUALIA.
Ele serve como referência para futuras evoluções descritas no [roadmap](../roadmap.md).

## FoldingOperator
A compressão dimensional utiliza uma matriz unitária $U = \exp(-\alpha H)$ gerada a partir de um Hamiltoniano $H$.
O estado $X$ é transformado por
\[
X' = X U,
\]
e pode ser revertido por $U^{-1}$.

## ResonanceOperator
Análises de ressonância são realizadas por meio da transformada rápida de Fourier.
Harmônicos predefinidos $f_k$ são extraídos e avaliados em amplitude e fase.
A força geral de ressonância é obtida ponderando coerência de fase e relações entre amplitudes.

## EmergenceOperator
Padrões emergentes são detectados a partir de subsequências recorrentes e correlações
entre componentes. Métricas de complexidade e persistência ranqueiam os padrões e
determinam interações relevantes.

## RetrocausalityOperator
O operador retrocausal estima influências de estados futuros no presente. Utiliza
janelas temporais para prever valores futuros e calcula correlações retroativas.
O canal TSVF integra essas informações para ajustar a confiança das previsões.

## ObserverOperator
Medições quânticas aplicam uma matriz observadora unitária $U_{obs}$ sobre o
estado analisado. A incerteza é calculada por limites mínimos e a persistência
histórica permite reavaliações posteriores.
