from __future__ import annotations

from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Union, TYPE_CHECKING
from datetime import datetime

if TYPE_CHECKING:  # pragma: no cover - only for type hints
    import pandas as pd

# ============================================================================
# EVENTOS FUNDAMENTAIS DO SISTEMA
# ============================================================================


# A dataclass MarketSpec foi movida para qualia.common.specs para quebrar dependências circulares.


@dataclass
class SystemEvent:
    """Classe base para todos os eventos do sistema."""

    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


# ============================================================================
# EVENTOS DE COLETA DE DADOS (DATA COLLECTION LAYER)
# ============================================================================


@dataclass(kw_only=True)
class MarketDataUpdated(SystemEvent):
    """Evento disparado quando novos dados de mercado (OHLCV) são coletados."""

    symbol: str = ""
    timeframe: str = ""
    ohlcv_data: "pd.DataFrame | None" = None


@dataclass(kw_only=True)
class HolographicEventReceived(SystemEvent):
    """Evento para quando dados brutos (RSS, notícias) para o universo holográfico são recebidos."""

    source: str = ""
    content: Any = None
    symbol_relevance: Dict[str, float] = field(default_factory=dict)


# ============================================================================
# EVENTOS DE ANÁLISE E DECISÃO (ANALYSIS & DECISION LAYER)
# ============================================================================


@dataclass(kw_only=True)
class OracleDecisionEvent(SystemEvent):
    """Evento publicado pelo QASTOracle com uma decisão de trading."""

    symbol: str = ""
    timeframe: str = ""
    decision: str = ""  # e.g., 'BUY', 'SELL', 'HOLD'
    confidence: float = 0.0
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass(kw_only=True)
class HolographicSignalEvent(SystemEvent):
    """Evento publicado pelo HolographicMarketUniverse com um padrão detectado."""

    patterns: List[Dict[str, Any]] = field(default_factory=list)


@dataclass(kw_only=True)
class CombinedSignalEvent(SystemEvent):
    """Evento gerado após a fusão de sinais do Oracle e do Universo Holográfico."""

    symbol: str = ""
    final_decision: str = ""
    confidence: float = 0.0
    contributing_signals: List[Any] = field(default_factory=list)


# ============================================================================
# EVENTOS DE EXECUÇÃO E RISCO (EXECUTION & RISK LAYER)
# ============================================================================


@dataclass(kw_only=True)
class RiskAssessmentCompleted(SystemEvent):
    """Publicado após a avaliação de risco de uma decisão de trading."""

    decision_event: Optional[OracleDecisionEvent] = None
    is_approved: bool = False
    risk_metrics: Dict[str, Any] = field(default_factory=dict)


@dataclass(kw_only=True)
class ExecutionRequestEvent(SystemEvent):
    """Solicitação para executar uma ordem no mercado."""

    symbol: str = ""
    side: str = ""  # 'buy' ou 'sell'
    amount: float = 0.0
    order_type: str = "market"
    price: Optional[float] = None


@dataclass(kw_only=True)
class OrderStatusUpdated(SystemEvent):
    """Atualização sobre o status de uma ordem (criada, preenchida, falhou)."""

    order_id: str = ""
    symbol: str = ""
    status: str = ""  # e.g., 'open', 'filled', 'canceled', 'failed'
    details: Dict[str, Any] = field(default_factory=dict)


# ============================================================================
# EVENTOS DE MONITORAMENTO E ESTADO (MONITORING & STATE LAYER)
# ============================================================================


@dataclass(kw_only=True)
class SystemStatusUpdate(SystemEvent):
    """Evento periódico com o status geral e as métricas de performance do sistema."""

    pnl: float = 0.0
    open_positions: int = 0
    consciousness_level: float = 0.0
    metrics: Dict[str, Any] = field(default_factory=dict)


@dataclass(kw_only=True)
class SafetyCheckResult(SystemEvent):
    """Resultado de uma verificação de segurança (drawdown, perda diária)."""

    check_name: str = ""
    is_safe: bool = True
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass(kw_only=True)
class MetacognitionUpdate(SystemEvent):
    """Publicado pelo loop de metacognição com ajustes ou observações."""

    insights: List[str] = field(default_factory=list)
    suggested_parameter_adjustments: Dict[str, Any] = field(default_factory=dict)


@dataclass(kw_only=True)
class ParadoxEvent(SystemEvent):
    """Evento emitido quando um paradoxo é resolvido ou detectado."""

    symbol: str = ""
    coherence: float = 0.0
    details: Dict[str, Any] = field(default_factory=dict)


# ---------------------------------------------------------------------------
# NEW EVENTS FOR DECOUPLED ARCHITECTURE
# ---------------------------------------------------------------------------


@dataclass(kw_only=True)
class RiskUpdateEvent(SystemEvent):
    """Evento emitido quando parâmetros de risco ou capital são atualizados."""

    new_capital: float | None = None
    params: Dict[str, Any] = field(default_factory=dict)


@dataclass(kw_only=True)
class RiskStressMetrics(SystemEvent):
    """Métricas consolidadas de uma simulação de estresse de risco."""

    metrics: Dict[str, Any] = field(default_factory=dict)


@dataclass(kw_only=True)
class CrossModalCoherenceEvent(SystemEvent):
    """Coerência multimodal calculada em tempo real."""

    coherence: float = 0.0


@dataclass(kw_only=True)
class CoherenceAlertEvent(SystemEvent):
    """Alerta gerado quando a coerência ultrapassa o limiar configurado."""

    coherence: float = 0.0
    threshold: float = 0.0


@dataclass(kw_only=True)
class MetricRecordedEvent(SystemEvent):
    """Evento emitido quando uma métrica é registrada pelo monitoramento."""

    name: str = ""
    value: float = 0.0
    tags: Dict[str, Any] = field(default_factory=dict)


@dataclass(kw_only=True)
class PerformanceRecordedEvent(SystemEvent):
    """Dados de desempenho registrados pelo monitor.

    Esta mensagem informa as métricas de FPS médio e latência
    capturadas pelo ``PerformanceMonitor``. Pode ser utilizada por
    consumidores para ajustes de telemetria ou diagnósticos em tempo
    real.
    """

    avg_fps: float
    latency_ms: float | None = None


@dataclass(kw_only=True)
class MemoryLatencyAlert(SystemEvent):
    """Alerta emitido quando a latência de escrita excede o limite."""

    latency_ms: float
    threshold_ms: float = 30.0


@dataclass(kw_only=True)
class DiskUsageAlert(SystemEvent):
    """Alerta emitido quando o uso de disco ultrapassa o limite configurado."""

    usage_pct: float
    threshold_pct: float = 90.0


@dataclass(kw_only=True)
class RiskManagerCreatedEvent(SystemEvent):
    """Emitido quando um :class:`QUALIARiskManagerBase` é instanciado."""

    manager_class: str
    initial_capital: float
    risk_profile: str


if TYPE_CHECKING:  # pragma: no cover - hints only
    from .market.dynamic_risk_controller import RiskCalibrationResult


@dataclass(kw_only=True)
class RiskRecalibratedEvent(SystemEvent):
    """Resultado de recalibração de risco."""

    current_capital: float | None = None
    drawdown_pct: float | None = None
    symbol: str | None = None
    calibration: "RiskCalibrationResult | None" = None


@dataclass(kw_only=True)
class RiskAdjustedEvent(SystemEvent):
    """Valores ajustados de stop loss e take profit."""

    symbol: str
    stop_loss: float
    take_profit: float


@dataclass(kw_only=True)
class RiskAdjustmentRequestedEvent(SystemEvent):
    """Solicitação de ajuste de risco."""

    symbol: str
    stop_loss: float
    take_profit: float
    entropy: float
    otoc: float


@dataclass(kw_only=True)
class EvaluationWeightAdjustedEvent(SystemEvent):
    """Notifica mudança nos pesos de avaliação de risco."""

    metric: str
    old_weight: float
    new_weight: float


@dataclass(kw_only=True)
class DynamicRiskParameterUpdate(SystemEvent):
    """Ajuste assíncrono do :class:`DynamicRiskController`."""

    params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MarketDataUpdate:
    """Evento de atualização de dados de mercado (preço, volume, etc)."""

    event_type: str = "market_data_update"


@dataclass
class OrderUpdate:
    """Evento de atualização de uma ordem (executada, cancelada, etc)."""

    event_type: str = "order_update"


# ---------------------------------------------------------------------------
# Eventos adicionais para desacoplamento total
# ---------------------------------------------------------------------------


@dataclass(kw_only=True)
class FocusMetricEvent(SystemEvent):
    """Métrica de foco calculada pelo ``ContextRelevanceAgent``."""

    focus: float
    interval: float


@dataclass(kw_only=True)
class CoherenceGuardFreeze(SystemEvent):
    """Sinaliza congelamento do processamento devido à baixa coerência."""

    coherence: float
    timestamp: float | None = None


@dataclass(kw_only=True)
class CoherenceGuardUnfreeze(SystemEvent):
    """Sinaliza liberação do processamento após retorno da coerência."""

    coherence: float
    timestamp: float | None = None


@dataclass(kw_only=True)
class RiskSnapshotRecorded(SystemEvent):
    """Registro de snapshot de performance para consciência de risco."""

    symbol: str
    timestamp: str


@dataclass(kw_only=True)
class RiskAwarenessUpdated(SystemEvent):
    """Métricas consolidadas da consciência de risco."""

    metrics: Dict[str, Any]


@dataclass(kw_only=True)
class OrderJournalAppended(SystemEvent):
    """Entrada adicionada ao journal de ordens."""

    entry: Dict[str, Any] = field(default_factory=dict)
    path: str = ""


@dataclass(kw_only=True)
class OrderJournalLoaded(SystemEvent):
    """Journal de ordens carregado do disco."""

    path: str
    entries: int


@dataclass(kw_only=True)
class JsonStoreDataSaved(SystemEvent):
    """Dados persistidos pelo :class:`JsonStore`."""

    key: str
    category: str = "system"
    path: str = ""
    size_bytes: int | None = None


@dataclass(kw_only=True)
class JsonStoreDataLoaded(SystemEvent):
    """Dados recuperados do :class:`JsonStore`."""

    key: str
    category: str = "system"
    path: str = ""
    size_bytes: int | None = None


@dataclass(kw_only=True)
class GAPauseEvent(SystemEvent):
    """Execução pausada do algoritmo genético."""


@dataclass(kw_only=True)
class GAResumeEvent(SystemEvent):
    """Retomada do algoritmo genético."""


@dataclass(kw_only=True)
class GAGenerationEvent(SystemEvent):
    """Resumo da geração do algoritmo genético."""

    gen: int
    alpha: float
    offset: float
    f1: float


@dataclass(kw_only=True)
class ThresholdOverrideEvent(SystemEvent):
    """Parâmetros de threshold definidos manualmente."""

    alpha: float
    offset: float
    f1: float | None = None


@dataclass(kw_only=True)
class ThresholdUpdatedEvent(SystemEvent):
    """Threshold calculado pelo calibrador adaptativo."""

    threshold: float
    timestamp: float


@dataclass(kw_only=True)
class EncoderStartedEvent(SystemEvent):
    """Indica início do processamento de um encoder."""

    name: str
    cls: str


@dataclass(kw_only=True)
class EncoderFinishedEvent(SystemEvent):
    """Indica término do processamento de um encoder."""

    name: str
    cls: str


@dataclass(kw_only=True)
class CognitiveAccelerationEvent(SystemEvent):
    """Aceleração cognitiva detectada pelo FarsightEngine."""

    acceleration: float
    timestamp: datetime


@dataclass(kw_only=True)
class HolographicSaturationAlert(SystemEvent):
    """Alert when useful event ratio falls below threshold."""

    memory_size: int
    decay_rate: float
    useful_ratio: float
    threshold: float = 0.05
