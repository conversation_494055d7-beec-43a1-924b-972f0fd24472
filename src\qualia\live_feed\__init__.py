"""
QUALIA Live Feed System - Sistema de feeds de dados em tempo real.

Este módulo implementa um sistema robusto de coleta de dados de mercado em tempo real
através de múltiplas exchanges, com foco inicial no KuCoin.

Componentes principais:
- FeedManager: Coordenador principal dos feeds
- KuCoinFeed: Feed específico do KuCoin (REST + WebSocket)
- DataNormalizer: Normalização de dados para formato QUALIA
- FeedAggregator: Agregação de múltiplos feeds

Arquitetura:
- Feeds independentes por exchange
- Normalização automática de dados
- Agregação inteligente com deduplicação
- Resiliência com reconexão automática
- Cache local para performance
"""

from .feed_manager import FeedManager
from .kucoin_feed import KuCoinFeed
from .data_normalizer import DataNormalizer, NormalizedTicker, NormalizedOrderBook, NormalizedTrade
from .feed_aggregator import FeedAggregator

__all__ = [
    "FeedManager",
    "KuCoinFeed",
    "DataNormalizer",
    "NormalizedTicker",
    "NormalizedOrderBook",
    "NormalizedTrade",
    "FeedAggregator",
]
