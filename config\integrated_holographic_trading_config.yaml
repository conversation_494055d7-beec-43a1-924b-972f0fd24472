# Configuração do Sistema Integrado de Trading Holográfico QUALIA
# Combina Enhanced Data Collection + Holographic Universe + QUALIARealTimeTrader

system:
  name: "QUALIA Integrated Holographic Trading"
  version: "1.0.0"
  mode: "paper_trading"  # "paper_trading" ou "live"
  
# Configuração do Universo Holográfico
holographic_universe:
  field_size: [200, 200]
  diffusion_rate: 0.35
  feedback_strength: 0.08
  evolution_steps: 5
  pattern_detection:
    min_strength: 0.3
    min_confidence: 0.4
    entropy_threshold: 0.7
    field_energy_buy_threshold: 0.6
    field_energy_sell_threshold: -0.6

# Configuração do QUALIARealTimeTrader
trader:
  symbols:
    - "BTC/USDT"
    - "ETH/USDT" 
    - "BNB/USDT"
    - "ADA/USDT"
    - "SOL/USDT"
  
  timeframes:
    - "5m"
    - "15m"
    - "1h"
  
  capital: 10000.0
  risk_profile: "moderate"  # conservative, moderate, aggressive
  data_source: "binance"
  
  # Risk Management
  risk_per_trade_pct: 2.0
  max_drawdown_pct: 15.0
  trading_fee_pct: 0.001
  
  # Timeouts e Performance
  market_data_timeout: 30.0
  ticker_fetch_timeout: 20.0
  ohlcv_fetch_timeout: 30.0
  position_monitor_interval: 5.0
  
  # Configurações avançadas
  adaptive_poll: true
  default_poll_interval: 60.0
  max_concurrent_positions: 3
  enable_hud: true
  use_webgpu: false

# Configuração do Holographic Trading Adapter
adapter:
  min_confidence: 0.5
  max_concurrent_positions: 2
  enable_holographic_risk_override: true
  
  # Risk Assessment Holográfico
  risk_assessment:
    rsi_buy_max: 75.0
    rsi_sell_min: 25.0
    max_volatility_pct: 8.0
    min_volume_ratio: 0.5
    min_pattern_strength: 0.4
  
  # Stop Loss e Take Profit
  stop_loss:
    default_pct: 2.0
    volatility_multiplier: 1.5
    max_pct: 5.0
  
  take_profit:
    default_pct: 3.0
    high_confidence_pct: 5.0
    rsi_threshold: 40.0  # Para BUY
    rsi_threshold_sell: 60.0  # Para SELL

# Configuração do Enhanced Data Collector
data_collection:
  # Símbolos para coleta de dados
  symbols:
    - "BTC/USDT"
    - "ETH/USDT"
    - "BNB/USDT"
    - "ADA/USDT"
    - "SOL/USDT"
    - "MATIC/USDT"
    - "DOT/USDT"
    - "LINK/USDT"
  
  # Timeframes para OHLCV
  timeframes:
    - "5m"
    - "15m" 
    - "1h"
  
  # Configuração dos Quantum Encoders
  quantum_encoders:
    rsi_phase_encoder:
      enabled: true
      rsi_period: 14
      phase_scaling: 0.1
    
    volume_ratio_encoder:
      enabled: true
      lookback_period: 20
      amplitude_scaling: 0.05
    
    rss_sentiment_encoder:
      enabled: true
      sentiment_scaling: 0.2
  
  # Configuração de coleta de notícias
  news_collection:
    enabled: true
    sources:
      - "coindesk"
      - "cointelegraph"
      - "bitcoinmagazine"
    max_articles: 10
    sentiment_analysis: true
  
  # Fear & Greed Index
  fear_greed:
    enabled: true
    update_interval: 3600  # 1 hora

# Configuração de Execução
execution:
  max_cycles: 100
  cycle_interval: 60.0  # segundos
  
  # Logging
  log_level: "INFO"
  log_file: "logs/integrated_holographic_trading.log"
  
  # Métricas e Monitoramento
  metrics:
    enabled: true
    save_interval: 300  # 5 minutos
    metrics_file: "results/holographic_trading_metrics.json"
  
  # Performance Reports
  reports:
    enabled: true
    generate_interval: 1800  # 30 minutos
    report_dir: "results/reports"

# Configurações de Emergência
emergency:
  # Stop Loss global
  global_stop_loss_pct: 20.0
  
  # Condições de parada automática
  auto_stop_conditions:
    max_consecutive_losses: 5
    max_daily_loss_pct: 10.0
    min_available_capital_pct: 80.0
  
  # Ações de emergência
  emergency_actions:
    close_all_positions: true
    disable_new_trades: true
    send_notification: true

# Configurações específicas do ambiente
environment:
  # Desenvolvimento
  development:
    mode: "paper_trading"
    capital: 1000.0
    max_cycles: 10
    cycle_interval: 30.0
    log_level: "DEBUG"
  
  # Produção
  production:
    mode: "live"
    capital: 10000.0
    max_cycles: 1000
    cycle_interval: 60.0
    log_level: "INFO"
    
  # Teste
  testing:
    mode: "paper_trading"
    capital: 5000.0
    max_cycles: 50
    cycle_interval: 45.0
    log_level: "INFO"

# Configurações de Exchange (KuCoin)
exchange:
  kucoin:
    # Rate limiting
    rate_limit: 4.0  # segundos entre requests
    max_retries: 3
    retry_delay: 2.0
    
    # Configurações de ordem
    order_type: "market"  # market, limit
    time_in_force: "GTC"  # GTC, IOC, FOK
    
    # Tamanhos mínimos (serão obtidos da exchange)
    min_order_sizes:
      "BTC/USDT": 0.0001
      "ETH/USDT": 0.001
      "BNB/USDT": 0.01
    
    # Fees (serão obtidos da exchange)
    trading_fees:
      maker: 0.001
      taker: 0.001

# Configurações de Backup e Recovery
backup:
  enabled: true
  interval: 3600  # 1 hora
  backup_dir: "backups"
  max_backups: 24
  
  # Dados para backup
  backup_data:
    - "positions"
    - "trade_history"
    - "metrics"
    - "holographic_state"

# Notificações
notifications:
  enabled: false
  
  # Tipos de notificação
  events:
    trade_executed: true
    position_closed: true
    emergency_stop: true
    system_error: true
  
  # Canais (configurar conforme necessário)
  channels:
    email:
      enabled: false
      smtp_server: ""
      smtp_port: 587
      username: ""
      password: ""
      to_addresses: []
    
    webhook:
      enabled: false
      url: ""
      headers: {}

# Configurações de Desenvolvimento
development:
  # Debugging
  debug_holographic_field: false
  debug_quantum_encoders: false
  debug_trading_decisions: true
  
  # Simulação
  simulate_network_delays: false
  simulate_exchange_errors: false
  
  # Testes
  run_integration_tests: false
  test_data_path: "test_data/" 