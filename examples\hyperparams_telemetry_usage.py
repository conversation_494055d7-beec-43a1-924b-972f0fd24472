#!/usr/bin/env python3
"""
Exemplo de uso do sistema de telemetria de hiperparâmetros QUALIA.

YAA REFINEMENT: Demonstra como integrar telemetria detalhada para
observabilidade completa dos hiperparâmetros críticos.
"""

import asyncio
import time
import random
from typing import Dict, Any

from src.qualia.config.hyperparams_loader import load_hyperparams, get_global_hyperparams_loader
from src.qualia.consciousness.amplification_calibrator import AmplificationCalibrator
from src.qualia.market.temporal_pattern_detector import MarketTemporalPatternDetector
from src.qualia.metrics.hyperparams_telemetry import get_global_hyperparams_telemetry


def simulate_market_data() -> Dict[str, Any]:
    """Simula dados de mercado para demonstração."""
    return {
        "symbol": random.choice(["BTC/USDT", "ETH/USDT", "ADA/USDT"]),
        "price": random.uniform(20000, 70000),
        "volume": random.uniform(1000000, 10000000),
        "volatility": random.uniform(0.01, 0.05),
        "trend": random.choice(["bull", "bear", "sideways"])
    }


def simulate_trading_decision(calibrator: AmplificationCalibrator, market_data: Dict[str, Any]) -> Dict[str, Any]:
    """Simula uma decisão de trading com telemetria."""
    
    # Simula cálculo de score baseado nos dados de mercado
    base_score = random.uniform(-1.0, 1.0)
    
    # Aplica amplificação de preço
    price_factor = market_data["volatility"] * calibrator.price_amplification
    
    # Aplica amplificação de notícias (simulada)
    news_sentiment = random.uniform(-0.5, 0.5)
    news_factor = news_sentiment * calibrator.news_amplification
    
    # Score final
    raw_score = base_score + price_factor + news_factor
    
    # Aplica threshold de confiança
    confidence = abs(raw_score) / 10.0  # Normaliza para 0-1
    confidence = min(confidence, 1.0)
    
    # Toma decisão baseada na confiança mínima
    if confidence < calibrator.hyperparams.min_confidence:
        decision = "hold"
    elif raw_score > 0:
        decision = "buy"
    else:
        decision = "sell"
    
    # Registra decisão com telemetria
    calibrator.record_decision_with_telemetry(
        raw_score=raw_score,
        confidence=confidence,
        decision=decision,
        symbol=market_data["symbol"],
        market_volatility=market_data["volatility"],
        market_trend=market_data["trend"]
    )
    
    return {
        "raw_score": raw_score,
        "confidence": confidence,
        "decision": decision,
        "market_data": market_data
    }


def simulate_pattern_detection(detector: MarketTemporalPatternDetector, market_data: Dict[str, Any]) -> Dict[str, Any]:
    """Simula detecção de padrões temporais com telemetria."""
    
    # Simula histórico de decisões
    decision_history = []
    for i in range(20):
        decision_history.append({
            "timestamp": time.time() - (i * 300),  # 5 minutos entre decisões
            "decision": random.choice(["buy", "sell", "hold"]),
            "pnl_pct": random.uniform(-0.05, 0.05),
            "confidence": random.uniform(0.3, 0.9)
        })
    
    # Detecta padrões
    patterns = detector.detect_patterns(decision_history)
    
    # Simula score baseado nos padrões
    raw_score = len(patterns) * 0.1 + random.uniform(-0.2, 0.2)
    confidence = min(len(patterns) * 0.15, 1.0)
    
    # Toma decisão baseada nos padrões
    if confidence < detector.min_confidence:
        decision = "hold"
    elif raw_score > 0:
        decision = "buy"
    else:
        decision = "sell"
    
    # Registra decisão com telemetria
    detector.record_pattern_decision_with_telemetry(
        patterns=patterns,
        raw_score=raw_score,
        confidence=confidence,
        decision=decision,
        symbol=market_data["symbol"],
        market_volatility=market_data["volatility"],
        market_trend=market_data["trend"]
    )
    
    return {
        "patterns": patterns,
        "raw_score": raw_score,
        "confidence": confidence,
        "decision": decision
    }


def simulate_calibration_cycle(calibrator: AmplificationCalibrator):
    """Simula um ciclo de calibração com métricas."""
    
    # Simula detecção de padrões
    patterns_detected = random.randint(5, 20)
    signals_generated = random.randint(2, patterns_detected)
    signals_executed = random.randint(1, signals_generated)
    
    # Simula resultados de execução
    execution_results = [random.choice([True, False]) for _ in range(signals_executed)]
    
    # Registra no calibrador (que automaticamente emite telemetria)
    calibrator.record_pattern_detection(
        patterns_detected=patterns_detected,
        signals_generated=signals_generated,
        signals_executed=signals_executed,
        execution_results=execution_results
    )
    
    return {
        "patterns_detected": patterns_detected,
        "signals_generated": signals_generated,
        "signals_executed": signals_executed,
        "success_rate": sum(execution_results) / len(execution_results) if execution_results else 0
    }


async def main():
    """Demonstração principal do sistema de telemetria."""
    
    print("🚀 Iniciando demonstração de telemetria de hiperparâmetros QUALIA")
    print("=" * 70)
    
    # 1. Carrega hiperparâmetros com diferentes overrides
    print("\n📋 1. Carregando hiperparâmetros...")
    
    # Configuração base
    hyperparams = load_hyperparams()
    print(f"   Base: price_amp={hyperparams.price_amplification}, news_amp={hyperparams.news_amplification}")
    print(f"   Min confidence: {hyperparams.min_confidence}, Source: {hyperparams.source}")
    
    # Override dinâmico para demonstração
    loader = get_global_hyperparams_loader()
    loader.set_dynamic_override("price_amplification", 7.5)
    loader.set_dynamic_override("news_amplification", 6.0)
    
    hyperparams_override = loader.load()
    print(f"   Override: price_amp={hyperparams_override.price_amplification}, news_amp={hyperparams_override.news_amplification}")
    print(f"   Source: {hyperparams_override.source}")
    
    # 2. Inicializa componentes
    print("\n🔧 2. Inicializando componentes...")
    calibrator = AmplificationCalibrator(hyperparams=hyperparams_override)
    detector = MarketTemporalPatternDetector(hyperparams=hyperparams_override)
    telemetry = get_global_hyperparams_telemetry()
    
    print(f"   ✅ AmplificationCalibrator inicializado")
    print(f"   ✅ TemporalPatternDetector inicializado")
    print(f"   ✅ Sistema de telemetria ativo")
    
    # 3. Simula ciclo de trading com telemetria
    print("\n📊 3. Simulando ciclo de trading...")
    
    for cycle in range(10):
        print(f"\n   Ciclo {cycle + 1}/10:")
        
        # Simula dados de mercado
        market_data = simulate_market_data()
        print(f"      Mercado: {market_data['symbol']} - {market_data['trend']} (vol: {market_data['volatility']:.3f})")
        
        # Decisão via calibrador
        decision_result = simulate_trading_decision(calibrator, market_data)
        print(f"      Calibrador: {decision_result['decision']} (score: {decision_result['raw_score']:.3f}, conf: {decision_result['confidence']:.3f})")
        
        # Decisão via detector de padrões
        pattern_result = simulate_pattern_detection(detector, market_data)
        print(f"      Detector: {pattern_result['decision']} (patterns: {len(pattern_result['patterns'])}, conf: {pattern_result['confidence']:.3f})")
        
        # Simula calibração ocasional
        if cycle % 3 == 0:
            calibration_result = simulate_calibration_cycle(calibrator)
            print(f"      Calibração: {calibration_result['patterns_detected']} padrões, {calibration_result['success_rate']:.2%} sucesso")
        
        # Pequena pausa para simular tempo real
        await asyncio.sleep(0.1)
    
    # 4. Exibe estatísticas de telemetria
    print("\n📈 4. Estatísticas de telemetria:")
    stats = telemetry.get_summary_stats()
    
    print(f"   Total de eventos: {stats['total_events']}")
    print(f"   Eventos recentes: {stats['recent_events']}")
    print(f"   Decisões por tipo: {stats['decision_counts']}")
    print(f"   Eventos por componente: {stats['component_counts']}")
    
    if stats['hyperparams_stats']:
        hp_stats = stats['hyperparams_stats']
        print(f"   Price Amplification: {hp_stats['price_amplification']['min']:.1f} - {hp_stats['price_amplification']['max']:.1f} (avg: {hp_stats['price_amplification']['avg']:.1f})")
        print(f"   News Amplification: {hp_stats['news_amplification']['min']:.1f} - {hp_stats['news_amplification']['max']:.1f} (avg: {hp_stats['news_amplification']['avg']:.1f})")
        print(f"   Min Confidence: {hp_stats['min_confidence']['min']:.2f} - {hp_stats['min_confidence']['max']:.2f} (avg: {hp_stats['min_confidence']['avg']:.2f})")
    
    # 5. Demonstra context manager
    print("\n🎯 5. Demonstrando context manager:")
    
    with telemetry.decision_context(hyperparams_override, "DemoComponent", symbol="DEMO/USDT") as ctx:
        # Simula processamento
        await asyncio.sleep(0.05)
        
        # Define resultados no contexto
        ctx["raw_score"] = 0.75
        ctx["confidence"] = 0.85
        ctx["decision"] = "buy"
        
        print(f"   Decisão no contexto: {ctx['decision']} (score: {ctx['raw_score']}, conf: {ctx['confidence']})")
    
    print(f"   ✅ Telemetria capturada automaticamente via context manager")
    
    # 6. Limpa overrides
    print("\n🧹 6. Limpando overrides dinâmicos...")
    loader.clear_dynamic_overrides()
    
    final_hyperparams = loader.load()
    print(f"   Valores finais: price_amp={final_hyperparams.price_amplification}, news_amp={final_hyperparams.news_amplification}")
    print(f"   Source: {final_hyperparams.source}")
    
    print("\n✅ Demonstração concluída!")
    print("=" * 70)
    print("💡 Métricas foram enviadas para sistemas de monitoramento (StatsD/Prometheus)")
    print("💡 Use ferramentas como Grafana para visualizar as métricas em tempo real")


if __name__ == "__main__":
    asyncio.run(main())
