#!/usr/bin/env python3
"""
Debug da geração de sinais FWH - Encontrar thresholds que realmente funcionem.
"""

import requests
import pandas as pd
import numpy as np
import yaml
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_binance_data(symbol: str = "BTCUSDT", interval: str = "1m", limit: int = 200) -> pd.DataFrame:
    """Obtém dados reais da Binance."""
    try:
        url = "https://api.binance.com/api/v3/klines"
        params = {'symbol': symbol, 'interval': interval, 'limit': limit}
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        df = pd.DataFrame(data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])
        
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = df[col].astype(float)
        
        df.set_index('timestamp', inplace=True)
        return df[['open', 'high', 'low', 'close', 'volume']]
        
    except Exception as e:
        logger.error(f"Error fetching data: {e}")
        return pd.DataFrame()

def debug_signal_generation(data: pd.DataFrame, params: dict) -> dict:
    """Debug detalhado da geração de sinais."""
    
    hype_threshold = params.get('hype_threshold', 0.45)
    wave_min_strength = params.get('wave_min_strength', 0.30)
    quantum_boost = params.get('quantum_boost_factor', 1.03)
    fib_lookback = params.get('fib_lookback', 34)
    
    debug_info = []
    signals = []
    
    print(f"🔍 DEBUGGING SIGNAL GENERATION")
    print(f"   hype_threshold: {hype_threshold}")
    print(f"   wave_min_strength: {wave_min_strength}")
    print(f"   quantum_boost: {quantum_boost}")
    print(f"   fib_lookback: {fib_lookback}")
    print()
    
    # Analisar últimos 50 candles para debug
    start_idx = max(fib_lookback, len(data) - 50)
    
    for i in range(start_idx, len(data)):
        window_data = data.iloc[i-fib_lookback:i+1]
        
        # Fibonacci levels
        high = window_data['high'].max()
        low = window_data['low'].min()
        diff = high - low
        
        fib_levels = {
            "0.618": high - 0.618 * diff,
            "resistance": high,
            "support": low,
        }
        
        # Wave patterns
        current_price = window_data["close"].iloc[-1]
        recent_trend = window_data["close"].tail(10).pct_change().mean()
        direction = 1 if recent_trend > 0 else -1
        trend_strength = abs(recent_trend)
        
        # Hype momentum
        vol_sma = window_data["volume"].tail(20).mean()
        current_vol = window_data["volume"].iloc[-1]
        vol_ratio = current_vol / vol_sma if vol_sma > 0 else 1.0
        
        price_changes = window_data["close"].pct_change().tail(10)
        volatility = price_changes.std()
        volatility_boost = min(volatility * 100, 0.5)
        
        base_momentum = (vol_ratio * 0.4 + trend_strength * 0.4 + volatility_boost * 0.2)
        hype_momentum = max(0.1, min(base_momentum, 1.0))
        
        # Signal strength
        signal_strength = hype_momentum * quantum_boost
        
        # Debug info
        debug_entry = {
            'timestamp': window_data.index[-1],
            'price': current_price,
            'trend_strength': trend_strength,
            'vol_ratio': vol_ratio,
            'volatility_boost': volatility_boost,
            'hype_momentum': hype_momentum,
            'signal_strength': signal_strength,
            'meets_hype_threshold': signal_strength > hype_threshold,
            'meets_wave_threshold': trend_strength > wave_min_strength,
            'would_signal': signal_strength > hype_threshold and trend_strength > wave_min_strength
        }
        
        debug_info.append(debug_entry)
        
        # Gerar sinal se condições atendidas
        if signal_strength > hype_threshold and trend_strength > wave_min_strength:
            signal_type = 'BUY' if direction > 0 else 'SELL'
            signals.append({
                'timestamp': window_data.index[-1],
                'type': signal_type,
                'strength': signal_strength,
                'price': current_price
            })
    
    # Análise estatística
    signal_strengths = [d['signal_strength'] for d in debug_info]
    trend_strengths = [d['trend_strength'] for d in debug_info]
    
    print(f"📊 SIGNAL ANALYSIS:")
    print(f"   Signal Strength - Min: {min(signal_strengths):.4f}, Max: {max(signal_strengths):.4f}, Avg: {np.mean(signal_strengths):.4f}")
    print(f"   Trend Strength - Min: {min(trend_strengths):.4f}, Max: {max(trend_strengths):.4f}, Avg: {np.mean(trend_strengths):.4f}")
    print(f"   Hype Threshold: {hype_threshold} (exceeded {sum(1 for d in debug_info if d['meets_hype_threshold'])} times)")
    print(f"   Wave Threshold: {wave_min_strength} (exceeded {sum(1 for d in debug_info if d['meets_wave_threshold'])} times)")
    print(f"   Both Conditions: {sum(1 for d in debug_info if d['would_signal'])} times")
    print(f"   Signals Generated: {len(signals)}")
    print()
    
    # Mostrar alguns exemplos
    print(f"🔬 SAMPLE ANALYSIS (last 10 candles):")
    for debug_entry in debug_info[-10:]:
        status = "✅ SIGNAL" if debug_entry['would_signal'] else "❌ NO SIGNAL"
        print(f"   {debug_entry['timestamp'].strftime('%H:%M:%S')} | {status}")
        print(f"      Signal Strength: {debug_entry['signal_strength']:.4f} (threshold: {hype_threshold})")
        print(f"      Trend Strength: {debug_entry['trend_strength']:.4f} (threshold: {wave_min_strength})")
        print(f"      Vol Ratio: {debug_entry['vol_ratio']:.2f}, Hype: {debug_entry['hype_momentum']:.4f}")
        print()
    
    return {
        'signals': signals,
        'debug_info': debug_info,
        'stats': {
            'signal_strength_max': max(signal_strengths),
            'signal_strength_avg': np.mean(signal_strengths),
            'trend_strength_max': max(trend_strengths),
            'trend_strength_avg': np.mean(trend_strengths),
            'hype_threshold_hits': sum(1 for d in debug_info if d['meets_hype_threshold']),
            'wave_threshold_hits': sum(1 for d in debug_info if d['meets_wave_threshold']),
            'total_signals': len(signals)
        }
    }

def main():
    """Executa debug de sinais."""
    print("🎯 FWH SIGNAL DEBUG")
    print("=" * 50)
    
    # Obter dados
    data = get_binance_data("BTCUSDT", "1m", 200)
    if data.empty:
        print("❌ Failed to get data")
        return
    
    print(f"✅ Data loaded: {len(data)} candles")
    print()
    
    # Testar configuração atual
    current_params = {
        'hype_threshold': 0.45,
        'wave_min_strength': 0.30,
        'quantum_boost_factor': 1.03,
        'fib_lookback': 34
    }
    
    print("🧪 TESTING CURRENT CONFIG:")
    current_result = debug_signal_generation(data, current_params)
    
    # Se não gerou sinais, testar thresholds progressivamente menores
    if current_result['stats']['total_signals'] == 0:
        print("🔧 CURRENT CONFIG GENERATES NO SIGNALS - TESTING LOWER THRESHOLDS:")
        print()
        
        test_configs = [
            {'name': 'Very Low', 'hype_threshold': 0.20, 'wave_min_strength': 0.15},
            {'name': 'Ultra Low', 'hype_threshold': 0.10, 'wave_min_strength': 0.05},
            {'name': 'Extreme Low', 'hype_threshold': 0.05, 'wave_min_strength': 0.01},
        ]
        
        for config in test_configs:
            test_params = current_params.copy()
            test_params.update(config)
            
            print(f"🧪 TESTING {config['name'].upper()} THRESHOLDS:")
            result = debug_signal_generation(data, test_params)
            
            if result['stats']['total_signals'] > 0:
                print(f"🎉 SUCCESS! Generated {result['stats']['total_signals']} signals")
                print(f"💡 RECOMMENDED CONFIG:")
                print(f"   hype_threshold: {test_params['hype_threshold']}")
                print(f"   wave_min_strength: {test_params['wave_min_strength']}")
                print(f"   quantum_boost_factor: {test_params['quantum_boost_factor']}")
                break
            else:
                print(f"❌ Still no signals with {config['name']} thresholds")
            print()
    
    print("✅ DEBUG COMPLETED")

if __name__ == "__main__":
    main()
